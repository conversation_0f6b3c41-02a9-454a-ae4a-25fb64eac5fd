using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 报告统计表
    /// </summary>
    [SugarTable("crm_report_statistics")]
    public class Db_crm_report_statistics
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime StatisticDate { get; set; }

        /// <summary>
        /// 统计周数
        /// </summary>
        public int? StatisticWeek { get; set; }

        /// <summary>
        /// 统计月份
        /// </summary>
        public int? StatisticMonth { get; set; }

        /// <summary>
        /// 统计年份
        /// </summary>
        public int StatisticYear { get; set; }

        /// <summary>
        /// 统计类型：daily, weekly, monthly
        /// </summary>
        public string StatisticType { get; set; }

        /// <summary>
        /// 团队ID
        /// </summary>
        public string TeamId { get; set; }

        /// <summary>
        /// 团队名称
        /// </summary>
        public string TeamName { get; set; }

        /// <summary>
        /// 团队级别：1-战队，2-大队，3-中队
        /// </summary>
        public int? TeamLevel { get; set; }

        /// <summary>
        /// 父团队ID
        /// </summary>
        public string ParentTeamId { get; set; }

        /// <summary>
        /// 用户ID，团队统计时可为空
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 数据值
        /// </summary>
        public int DataValue { get; set; }

        /// <summary>
        /// 点赞数
        /// </summary>
        public int LikeCount { get; set; }

        /// <summary>
        /// 评论数
        /// </summary>
        public int CommentCount { get; set; }

        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateDate { get; set; }
    }
}
