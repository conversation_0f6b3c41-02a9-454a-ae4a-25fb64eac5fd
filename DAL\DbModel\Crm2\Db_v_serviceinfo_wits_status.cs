﻿using System;
using System.Linq;
using System.Text;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_serviceinfo_wits_status")]
    public class Db_v_serviceinfo_wits_status
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string Id {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public EnumContractServiceOpenState State {get;set;}

    }
}
