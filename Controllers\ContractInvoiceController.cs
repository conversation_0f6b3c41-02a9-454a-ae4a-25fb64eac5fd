﻿using CRM2_API.BLL;
using CRM2_API.Common.Utils;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.Diagnostics.Contracts;
using System.IO;
using System.Threading.Tasks;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_OriginalInvoice;

namespace CRM2_API.Controllers
{
    [Description("合同发票")]
    public class ContractInvoiceController : MyControllerBase
    {
        public ContractInvoiceController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        /// <summary>
        /// 添加合同发票申请
        /// </summary>
        [HttpPost]
        public void AddContractInvoiceAppl([FromForm] AddContractInvoiceAppl_In addContractInvoiceApplIn)
        {
            BLL_ContractInvoice.Instance.AddContractInvoiceAppl(addContractInvoiceApplIn);
        }

        /// <summary>
        /// 修改合同发票申请
        /// </summary>
        [HttpPost]
        public void UpdateContractInvoiceAppl([FromForm] UpdateContractInvoiceAppl_In updateContractInvoiceApplIn)
        {
            BLL_ContractInvoice.Instance.UpdateContractInvoiceAppl(updateContractInvoiceApplIn);
        }

        /// <summary>
        /// 审核合同发票申请
        /// </summary>
        [HttpPost]
        public void AuditContractInvoiceAppl([FromForm] AuditContractInvoiceAppl_In auditContractInvoiceApplIn)
        {
            BLL_ContractInvoice.Instance.AuditContractInvoiceAppl(auditContractInvoiceApplIn);
        }

        /// <summary>
        /// 根据发票申请Id获取合同基本信息和发票申请信息
        /// </summary>
        [HttpPost]
        public ContractInfoAndInvoiceAppl_Out GetContractInfoAndInvoiceApplByInvoiceApplId(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractInfoAndInvoiceApplByInvoiceApplId(id);
        }

        /// <summary>
        /// 根据查询条件获取合同发票申请列表
        /// </summary>
        /// <param name="searchContractInvoiceAuditListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractInvoiceAuditList_Out> SearchContractInvoiceAuditList(SearchContractInvoiceAuditList_In searchContractInvoiceAuditListIn)
        {
            return BLL_ContractInvoice.Instance.SearchContractInvoiceAuditList(searchContractInvoiceAuditListIn);
        }

        /// <summary>
        /// 根据查询条件获取合同发票申请列表统计
        /// </summary>
        /// <param name="searchContractInvoiceAuditListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public SearchContractInvoiceAuditList_Sta_Out SearchContractInvoiceAuditListSta(SearchContractInvoiceAuditList_In searchContractInvoiceAuditListIn)
        {
            return BLL_ContractInvoice.Instance.SearchContractInvoiceAuditListSta(searchContractInvoiceAuditListIn);
        }

        /// <summary>
        /// 根据合同Id获取合同信息和到账信息
        /// </summary>
        [HttpPost]
        public ContractInfoAndReceipt_Out GetContractAndReceiptByContractId(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractAndReceiptByContractId(id);
        }

        /// <summary>
        /// 根据合同发票申请Id获取合同发票信息和审核信息
        /// </summary>
        [HttpPost]
        public ContractInvoiceAndAudit_Out GetContractInvoiceAndAuditByContractInvoiceApplId(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractInvoiceAndAuditByContractInvoiceApplId(id);
        }

        /// <summary>
        /// 根据合同发票申请Id获取合同信息和发票信息、发票申请信息
        /// </summary>
        [HttpPost]
        public ContractInfoAndInvoiceInfo_Out GetContractInfoAndInvoiceInfoByInvoiceApplId(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractInfoAndInvoiceInfoByInvoiceApplId(id);
        }

        /// <summary>
        /// 发起退票
        /// </summary>
        /// <param name="contractRefundInvoice"></param>
        [HttpPost]
        public void InitiateContractRefundInvoice(ContractRefundInvoice_In contractRefundInvoice)
        {
            BLL_ContractInvoice.Instance.InitiateContractRefundInvoice(contractRefundInvoice);
        }

        /// <summary>
        /// 审核退票信息
        /// </summary>
        /// <param name="auditContractRefundInvoice"></param>
        [HttpPost]
        public void AuditContractRefundInvoice(AuditContractRefundInvoice_In auditContractRefundInvoice)
        {
            BLL_ContractInvoice.Instance.AuditContractRefundInvoice(auditContractRefundInvoice);
        }

        /// <summary>
        /// 根据查询条件获取合同发票申请列表
        /// </summary>
        [HttpPost]
        public ApiTableOut<SearchContractInvoiceApplList_Out> SearchContractInvoiceApplList(SearchContractInvoiceApplList_In searchContractInvoiceApplList_In)
        {
            return BLL_ContractInvoice.Instance.SearchContractInvoiceApplList(searchContractInvoiceApplList_In);
        }

        /// <summary>
        /// 根据合同发票申请Id获取合同发票申请信息
        /// </summary>
        [HttpPost]
        public GetContractInvoiceAppl_OUT GetContractInvoiceApplById(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractInvoiceApplById(id);
        }

        /// <summary>
        /// 操作退票 (红冲)上传退票信息
        /// </summary>
        [HttpPost]
        public void OperateContractRefundInvoice([FromForm] OperateContractRefundInvoice_IN operateContractRefundInvoice_IN)
        {
            BLL_ContractInvoice.Instance.OperateContractRefundInvoice(operateContractRefundInvoice_IN);
        }

        /// <summary>
        /// 根据退票id获取退票信息
        /// </summary>
        /// <param name="id"></param>
        [HttpPost]
        public ContractRefundInvoice_Out GetContractRefundInvoiceById(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractRefundInvoiceById(id);
        }

        /// <summary>
        /// 根据合同发票申请Id获取合同信息和发票信息、发票申请信息、退票信息
        /// </summary>
        [HttpPost]
        public ContractInfoAndInvoiceInfoAndRefundInvoiceInfo_Out GetContractInfoAndInvoiceInfoAndRefundInvoiceInfoByInvoiceApplId(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractInfoAndInvoiceInfoAndRefundInvoiceInfoByInvoiceApplId(id);
        }

        /// <summary>
        /// 根据合同发票申请Id获取发票信息、发票申请信息
        /// </summary>
        [HttpPost]
        public ContractInvoiceApplAndInvoiceInfo_Out GetContractInvoiceApplAndInvoiceInfoByInvoiceApplId(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractInvoiceApplAndInvoiceInfoByInvoiceApplId(id);
        }

        /// <summary>
        /// 根据发票申请id获取退票信息列表
        /// </summary>
        /// <param name="id"></param>
        [HttpPost]
        public List<ContractRefundInvoice_Out> GetContractRefundInvoiceListByInvoiceApplId(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractRefundInvoiceListByInvoiceApplId(id);
        }

        /// <summary>
        /// 作废合同发票申请信息，当开票状态为全部开票、部分开票时，可操作。
        /// </summary>
        [HttpPost]
        public void VoidContractInvoiceAppl(string id)
        {
            BLL_ContractInvoice.Instance.VoidContractInvoiceAppl(id);
        }

        /// <summary>
        /// 撤销合同发票申请信息
        /// </summary>
        [HttpPost]
        public void RevokeContractInvoiceAppl(string id)
        {
            BLL_ContractInvoice.Instance.RevokeContractInvoiceAppl(id, EnumOperate.Examine.ToInt());
        }

        /// <summary>
        /// 撤销合同发票提交申请信息
        /// </summary>
        [HttpPost]
        public void RevokeContractInvoiceApplSubmit(string id)
        {
            BLL_ContractInvoice.Instance.RevokeContractInvoiceAppl(id, EnumOperate.Appl.ToInt());
        }

        /// <summary>
        /// 根据合同id获取已开票金额
        /// </summary>
        [HttpPost]
        public decimal GetInvoicedAmountByContractId(string id)
        {
            return BLL_ContractInvoice.Instance.GetInvoicedAmountByContractId(id);
        }
        /// <summary>
        /// 删除合同发票申请信息，当开票状态为草稿、作废、拒绝时，可删除。
        /// </summary>
        [HttpPost]
        public void DeleteContractInvoiceAppl(string id)
        {
            BLL_ContractInvoice.Instance.DeleteContractInvoiceAppl(id);
        }

        /// <summary>
        /// 删除合同形式发票申请信息，当开票状态为草稿、作废、拒绝时，可删除。
        /// </summary>
        [HttpPost]
        public void DeleteContractProformaTicketInvoiceApplByAppId(string id)
        {
            BLL_ContractInvoice.Instance.DeleteContractProformaTicketInvoiceApplByAppId(id);
        }

        /// <summary>
        /// 催票，当开票状态为申请时，可操作。
        /// </summary>
        [HttpPost]
        public void ReminderContractInvoiceAppl(ReminderContractInvoiceAppl_IN reminderContractInvoiceAppl_IN)
        {
            BLL_ContractInvoice.Instance.ReminderContractInvoiceAppl(reminderContractInvoiceAppl_IN);
        }
        /// <summary>
        /// 确认签收，当开票状态为全部开票、部分开票时，可操作
        /// </summary>
        [HttpPost]
        public void SignReceivingContractInvoiceAppl(string id)
        {
            BLL_ContractInvoice.Instance.SignReceivingContractInvoiceAppl(id);
        }

        /// <summary>
        /// 根据合同发票申请Id获取发票信息、发票申请信息、退票信息
        /// </summary>
        [HttpPost]
        public ContractInvoiceInfoAndRefundInvoiceInfo_Out GetContractInvoiceInfoAndRefundInvoiceInfoByInvoiceApplId(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractInvoiceInfoAndRefundInvoiceInfoByInvoiceApplId(id);
        }

        /// <summary>
        /// 根据合同id和发票申请id获取可申请开票金额
        /// </summary>
        [HttpPost]
        public decimal GetMayApplInvoicedAmountByContractIdAndApplId(string ContractId, string ApplId)
        {
            return BLL_ContractInvoice.Instance.GetMayApplInvoicedAmountByContractIdAndApplId(ContractId, ApplId);
        }

        /// <summary>
        /// 根据合同发票申请Id获取发票信息、发票申请信息、退票信息
        /// </summary>
        [HttpPost]
        public ContractInvoiceInfoAndRefundInvoiceInfo_Out GetContractInvoiceInfoAndRefundInvoiceInfoRefundedAndPendingRefundConfirmationByInvoiceApplId(string id)
        {
            return BLL_ContractInvoice.Instance.GetContractInvoiceInfoAndRefundInvoiceInfoRefundedAndPendingRefundConfirmationByInvoiceApplId(id);
        }

        /// <summary>
        /// 根据合同发票申请Id获取发票的发票编号信息
        /// </summary>
        [HttpPost]
        public List<GetOriginalInvoiceList_Out> GetInvoiceInfoByInvoiceApplId(string invoiceApplId)
        {
            return BLL_ContractInvoice.Instance.GetInvoiceInfoByInvoiceApplId(invoiceApplId);
        }

        /// <summary>
        /// 根据合同Id获取发票信息
        /// </summary>
        [HttpPost]
        public List<InvoiceInfo_Out> GetInvoiceInfoByContractId(string contractId)
        {
            return BLL_ContractInvoice.Instance.GetInvoiceInfoByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id和开票时间获取发票信息列表
        /// </summary>
        [HttpPost]
        public ApiTableOut<ContractInvoiceList_Out> GetContractInvoiceListByContractId(ContractInvoiceList_In contractInvoiceListIn)
        {
            return BLL_ContractInvoice.Instance.GetContractInvoiceListByContractId(contractInvoiceListIn);
        }

        /// <summary>
        /// 根据客户Id和开票时间获取发票信息列表
        /// </summary>
        [HttpPost]
        public ApiTableOut<ContractInvoiceList_Out> GetContractInvoiceListByCustomerId(ContractInvoiceList_CustomerId_In contractInvoiceListCustomerIdIn)
        {
            return BLL_ContractInvoice.Instance.GetContractInvoiceListByCustomerId(contractInvoiceListCustomerIdIn);
        }

        /// <summary>
        /// 获取形式发票票号
        /// </summary>
        [HttpPost]
        public string GetFormalInvoiceNumber()
        {
            return BLL_ContractInvoice.Instance.GetFormalInvoiceNumber();
        }

        /// <summary>
        /// 下载发票
        /// </summary>
        [HttpPost]
        public IActionResult DownloadInvoice(string invoiceId)
        {
            Stream result = BLL_ContractInvoice.Instance.DownloadInvoice(invoiceId, Response);
            string ContentType = "application/pdf";
            return new FileStreamResult(result, ContentType);
        }

        /// <summary>
        /// 修改应付金额
        /// </summary>
        [HttpPost]
        public void UpdateBalanceDue(UpdateBalanceDueIn updateBalanceDueIn)
        {
            BLL_ContractInvoice.Instance.UpdateBalanceDue(updateBalanceDueIn);
        }

        /// <summary>
        /// 根据合同发票Id获取开票金额、应付金额
        /// </summary>
        [HttpPost]
        public GetBalanceDue_Out GetBalanceByInvoiceId(string id)
        {
            return BLL_ContractInvoice.Instance.GetBalanceByInvoiceId(id);
        }


        /// <summary>
        /// 获取下载发票列表
        /// </summary>
        [HttpPost]
        public List<BM_FileInfo> GetDownloadInvoiceList(string invoiceId)
        {
            return BLL_ContractInvoice.Instance.GetDownloadInvoiceList(invoiceId);
        }

        /// <summary>
        /// 根据类型下载发票
        /// </summary>
        [HttpGet]
        public IActionResult DownloadInvoiceByType(string id, string fileType)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(fileType))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误!"
                });
            }
            
            if (fileType == "ProformaTicket")
            {
                Stream result = BLL_ContractInvoice.Instance.DownloadInvoice(id, Response);
                string ContentType = "application/pdf";
                return new FileStreamResult(result, ContentType);
            }
            else
            {
                return BLL_Attachfile.Instance.Preview(id, fileType, Response);
            }
        }
    }
}
