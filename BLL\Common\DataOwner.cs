﻿using Aspose.Pdf;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using DocumentFormat.OpenXml.Drawing.Charts;
using LgyUtil;
using Magicodes.IE.Core;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Org.BouncyCastle.Crypto;
using SkiaSharp;
using SqlSugar;
using System.Linq.Expressions;
using System.Reflection;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.BLL.Common
{
    public class DataOwner
    {
        //根据用户id获取用户类型,根据用户类型确定获取数据范围
        //用户类型分为管理员、销售、部门领导、会计(审核人)等
        //管理员看全部
        //销售看自己
        //部门领导看本部门下全部
        //会计(审核人)看全部
        //public static List<IConditionalModel> GetCreateData(string userId, string fieldName)
        //{
        //    var conModels = new List<IConditionalModel>();
        //    int UserType = RedisCache.UserInfo.GetUserInfo(userId).UserType;
        //    if (UserType == EnumUserType.Manager.ToInt() || UserType == EnumUserType.Reviewer.ToInt())
        //    {
        //        //查询所有
        //    }
        //    else if (UserType == EnumUserType.Sales.ToInt())
        //    {
        //        //查询属于创建人的
        //        conModels.Add(new ConditionalModel { FieldName = fieldName, ConditionalType = ConditionalType.Equal, FieldValue = userId });
        //    }
        //    else if (UserType == EnumUserType.DepartmentLeaders.ToInt())
        //    {
        //        //查询部门下所有人的
        //        //查询属于创建人的
        //        conModels.Add(new ConditionalModel { FieldName = fieldName, ConditionalType = ConditionalType.In, FieldValue = userId });
        //        //SqlSugar.UtilMethods.ChangeType2(it, typeof(int))
        //        //SqlFunc.Subqueryable<Db_crm_contract_productinfo>().Where(s => s.ContractId == r.Id && s.Deleted == false).Any()
        //    }
        //    return conModels;
        //}

        //public static List<IConditionalModel> GetCreateData(string userId)
        //{
        //    return GetCreateData(userId, "r.CreateUser");
        //}

        //public static Expressionable<T> GetCreateUser<T>(string userId, string fieldName) where T : class, new()
        //{
        //    var exp = Expressionable.Create<T>();
        //    int UserType = RedisCache.UserInfo.GetUserInfo(userId).UserType;
        //    if (UserType == EnumUserType.Manager.ToInt() || UserType == EnumUserType.Reviewer.ToInt())
        //    {

        //    }
        //    else if (UserType == EnumUserType.Sales.ToInt())
        //    {
        //        exp.And(r => r.CreateUser == userId);
        //    }
        //    else if (UserType == EnumUserType.DepartmentLeaders.ToInt())
        //    {

        //    }
        //    return exp;
        //}


        /// <summary>
        /// 根据用户id获取用户类型,根据用户类型确定获取数据范围
        /// 用户类型分为管理员、销售、部门领导、会计(审核人)等
        /// 管理员看全部
        /// 销售看自己
        /// 部门领导看本部门下全部
        /// 会计(审核人)看全部
        /// </summary>
        /// <param name="fieldName"></param>
        /// <param name="issuerOnly">只个人看，不添加管理员权限的判断</param>
        /// <returns></returns>
        public static string GetCreateData(string fieldName,bool issuerOnly = false)
        {
            string userId = TokenModel.Instance.id;
            var conModels = "";
            var orgId = RedisCache.UserInfo.GetUserInfo(userId).OrganizationId;
            if (!issuerOnly  && (string.IsNullOrEmpty(orgId) || orgId == Guid.Empty.ToString()))
                //查询所有
                conModels = "1 = 1";
            else
                //查询属于fieldName
                conModels = string.Format("{0} = '{1}'", fieldName, userId);


            /*var UserType = RedisCache.UserInfo.GetUserInfo(userId).UserType;
            if (UserType == EnumUserType.Manager.ToInt()*//* || UserType == EnumUserType.Reviewer.ToInt()*//*)
            {
                
            }
            else if (UserType == EnumUserType.Sales.ToInt())
            {
                //查询属于fieldName
                conModels = string.Format("{0} = '{1}'", fieldName, userId);
            }*/
            /*else if (UserType == EnumUserType.DepartmentLeaders.ToInt())
            {
                //查询部门下所有人的
                //查询属于fieldName
                conModels = string.Format("EXISTS(SELECT * FROM v_organizationuser WHERE(UserId = {0} and ParentUserId = '{1}'))", fieldName, userId);
            }*/
            return conModels;
        }

        public static string GetCreateData<T>()
        {
            Type typeDB = typeof(T);
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            return GetCreateData(tableName + ".CreateUser");
        }

        //验证是否存在业务处理流程中
        public static string IsBusinessProcessing(string dataIdFieldName)
        {
            if (string.IsNullOrEmpty(dataIdFieldName))
            return "1 = 2";
            string userId = TokenModel.Instance.id;
            //var conModels = string.Format("EXISTS(SELECT p.* FROM sys_workflow_pending p Left JOIN sys_userinrole u ON (( p.RecipientRoleId = u.RoleId ) AND ( u.Deleted = 0 )) WHERE ((( p.DataId = {0} ) AND ( p.Deleted = 0 )) AND (( p.RecipientUserId = '{1}' ) OR ( u.UserId = '{1}' ))))", dataIdFieldName, userId);
            var conModels = string.Format("EXISTS(SELECT p.* FROM sys_workflow_pending p Left JOIN sys_workflow_pending_recipientroleid pr on p.Id = pr.WorkFlowPendingId and pr.Deleted = 0 Left JOIN sys_userinrole u ON (( pr.RecipientRoleId = u.RoleId ) AND ( u.Deleted = 0 )) Left JOIN sys_workflow_pending_recipientuserid pu on p.Id = pu.WorkFlowPendingId and pu.Deleted = 0 WHERE ((( p.DataId = {0} ) AND ( p.Deleted = 0 )) AND (( pu.RecipientUserId = '{1}' ) OR ( u.UserId = '{1}' ))))", dataIdFieldName, userId);
            //var conModels = string.Format("({0}  in (SELECT p.DataId FROM sys_workflow_pending p Left JOIN sys_workflow_pending_recipientroleid pr on p.Id = pr.WorkFlowPendingId and pr.Deleted = 0 Left JOIN sys_userinrole u ON (( pr.RecipientRoleId = u.RoleId ) AND ( u.Deleted = 0 )) Left JOIN sys_workflow_pending_recipientuserid pu on p.Id = pu.WorkFlowPendingId and pu.Deleted = 0 WHERE ((( p.Deleted = 0 )) AND (( pu.RecipientUserId = '{1}' ) OR ( u.UserId = '{1}' )))))", dataIdFieldName, userId);

            return conModels;
        }

        public static string IsBusinessProcessing<T>()
        {
            Type typeDB = typeof(T);
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            return IsBusinessProcessing(tableName + ".Id");
        }

        //验证是否存在客户私有池中
        public static string IsPrivatePoolCustomer(string dataIdFieldName)
        {
            string userId = TokenModel.Instance.id;
            //var conModels = string.Format("EXISTS(SELECT p.* FROM v_customer p WHERE (( p.CustomerId = {0} ) AND ( p.UserId = '{1}' ) AND (p.privatePool = 1)))", dataIdFieldName, userId);
            var conModels = string.Format("(EXISTS(SELECT p.* FROM v_customer p WHERE (( p.CustomerId = {0} ) AND ( p.UserId = '{1}' ) AND ( p.privatePool = 1 ))) or EXISTS(SELECT p.* FROM v_customer p left join v_customer_merge cm on cm.AfterCustomerId = p.CustomerId WHERE((cm.BeforeCustomerId = {0}) AND ( p.UserId = '{1}' ) AND ( p.privatePool = 1 ))))", dataIdFieldName, userId);
            return conModels;
        }
        /// <summary>
        /// 20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限
        /// </summary>
        /// <param name="fieldName"></param>
        /// <param name="dataIdFieldName"></param>
        /// <param name="customerOwnerUserFieldName">当前客户所属人字段</param>
        /// <param name="IssuerOnly">只个人看，不添加管理员权限的判断</param>
        /// <returns></returns>
        public static string BusinessData(string fieldName, string dataIdFieldName, string customerOwnerUserFieldName = "",bool IssuerOnly = false)
        {
            return "(" + GetCreateData(fieldName, IssuerOnly) + " or " + IsBusinessProcessing(dataIdFieldName) + " or " + GetCustomerData(customerOwnerUserFieldName, IssuerOnly) + ")";
        }

        public static string BusinessData<T>()
        {
            Type typeDB = typeof(T);
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            return BusinessData(tableName + ".CreateUser", tableName + ".Id");
        }

        public static string BusinessData<T>(Expression<Func<T, string>> ownerFieldName, Expression<Func<T, string>> dataIdFieldName)
        {
            string ownerFieldNameString = (ownerFieldName.Body as MemberExpression).Member.Name;
            string dataIdFieldNameString = (dataIdFieldName.Body as MemberExpression).Member.Name;

            Type typeDB = typeof(T);
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;

            return BusinessData(tableName + "." + ownerFieldNameString, tableName + "." + dataIdFieldNameString);
        }

        public static string BusinessData<T, F>(Expression<Func<T, string>> ownerFieldName, Expression<Func<F, string>> dataIdFieldName)
        {
            string ownerFieldNameString = (ownerFieldName.Body as MemberExpression).Member.Name;
            string dataIdFieldNameString = (dataIdFieldName.Body as MemberExpression).Member.Name;

            Type typeOwnerDB = typeof(T);
            string tableOwnerName = ((SugarTable)(typeOwnerDB.GetCustomAttributes(true)[2])).TableName;
            Type typeDataIdDB = typeof(F);
            string tableDataIdName = ((SugarTable)(typeDataIdDB.GetCustomAttributes(true)[2])).TableName;

            return BusinessData(tableOwnerName + "." + ownerFieldNameString, tableDataIdName + "." + dataIdFieldNameString);
        }

        public static string GetCustomerData(string fieldName,bool issuerOnly = false)
        {
            if (string.IsNullOrEmpty(fieldName))
                return " 1 = 2 ";
            string userId = TokenModel.Instance.id;
            var conModels = "";
            var orgId = RedisCache.UserInfo.GetUserInfo(userId).OrganizationId;
            if (!issuerOnly && (string.IsNullOrEmpty(orgId) || orgId == Guid.Empty.ToString()))
                //查询所有
                conModels = "1 = 1";
            else
                //查询属于fieldName
                conModels = string.Format("{0} = '{1}'", fieldName, userId);


            /*var UserType = RedisCache.UserInfo.GetUserInfo(userId).UserType;
            if (UserType == EnumUserType.Manager.ToInt()*//* || UserType == EnumUserType.Reviewer.ToInt()*//*)
            {
                
            }
            else if (UserType == EnumUserType.Sales.ToInt())
            {
                //查询属于fieldName
                conModels = string.Format("{0} = '{1}'", fieldName, userId);
            }*/
            /*else if (UserType == EnumUserType.DepartmentLeaders.ToInt())
            {
                //查询部门下所有人的
                //查询属于fieldName
                conModels = string.Format("EXISTS(SELECT * FROM v_organizationuser WHERE(UserId = {0} and ParentUserId = '{1}'))", fieldName, userId);
            }*/
            return conModels;
        }
    }
}
