﻿using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BusinessModel;

namespace CRM2_API.BLL.Common
{
    public class Com_SysUser
    {
        private static readonly AsyncLocal<UserBasicInfo> _user = new();

        public static UserBasicInfo Instance
        {
            get { return _user.Value ?? new(); }
        }

        public static void SetUser(UserBasicInfo user)
        {
            _user.Value = user;
        }
    }
}
