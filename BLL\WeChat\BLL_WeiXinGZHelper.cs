﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.AppSetting;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.IO;
using System.Net;
using DocumentFormat.OpenXml.Spreadsheet;

namespace CRM2_API.BLL.WeChat
{
    public class BLL_WeiXinGZHelper : BaseBLL<BLL_WeiXinGZHelper>
    {
        ///// <summary>
        /////  获取unionid
        ///// </summary>
        ///// <param name="openid"></param>
        ///// <returns></returns>
        //public WechatInfo GetUnionid(string openid)
        //{
        //    string access_token = GetAcess_Token();

        //    string getinfo = string.Format("https://api.weixin.qq.com/cgi-bin/user/info?access_token={0}&openid={1}&lang=zh_CN", access_token, openid);
        //    JObject info = NetUtil.Get_ReturnString(getinfo).DeserializeNewtonJson<JObject>();

        //    string unionid = info["unionid"].ToString();
        //    WechatInfo weixin_info = new WechatInfo();//自己定义的dto
        //    weixin_info.openId = openid;
        //    weixin_info.nickname = StringUtil.RemoveEmoji(info["nickname"].ToString());
        //    //weixin_info.sex = info["sex"].ToString();
        //    //weixin_info.province = info["province"].ToString();
        //    //weixin_info.city = info["city"].ToString();
        //    //weixin_info.headimgurl = info["headimgurl"].ToString();//大小为30×30像素的QQ空间头像URL。
        //    weixin_info.unionid = info["unionid"].ToString();//用户统一标识。针对一个微信开放平台帐号下的应用，同一用户的unionid是唯一的。

        //    return weixin_info;
        //}

        /// <summary>
        /// 获取access_token
        /// </summary>
        public string GetAcess_Token()
        {
            string strURL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + AppSettings.WeChatConfig.AppID + "&secret=" + AppSettings.WeChatConfig.AppSecret;
            var model = NetUtil.Get_ReturnString(strURL).DeserializeNewtonJson<AccessToken>();
            return model.access_token;
        }

        public WechatInfo getWeixinUserInfoJSON(string code)
        {
            string apiurl = string.Format("https://api.weixin.qq.com/sns/oauth2/access_token?appid={0}&secret={1}&code={2}&grant_type=authorization_code", AppSettings.WeChatConfig.AppID, AppSettings.WeChatConfig.AppSecret, code);

            System.GC.Collect();
            System.Net.ServicePointManager.DefaultConnectionLimit = 200;
            WebRequest request = WebRequest.Create(apiurl);

            WebResponse response = request.GetResponse();
            Stream stream = response.GetResponseStream();
            Encoding encode = Encoding.UTF8;
            StreamReader reader = new StreamReader(stream, encode);
            string jsonText = reader.ReadToEnd();

            JObject jo1 = (JObject)JsonConvert.DeserializeObject(jsonText);
            LogUtil.AddLog("getWeixinUserInfoJSON_1 " + JsonConvert.SerializeObject(jo1));
            string access_token = jo1["access_token"].ToString();
            string refresh_token = jo1["refresh_token"].ToString();
            string openid = jo1["openid"].ToString();

            ////根据OpenID获取用户信息 可以显示更多 用的就几个 需要的可以自己在下面加
            string getinfo = string.Format("https://api.weixin.qq.com/sns/userinfo?access_token={0}&openid={1}", access_token, openid);
            request = WebRequest.Create(getinfo);
            response = request.GetResponse();
            stream = response.GetResponseStream();
            reader = new StreamReader(stream, encode);
            string userStr = reader.ReadToEnd();
            //this.Label1.Text = userStr;
            //this.Label2.Text = openIdStr;
            JObject info = (JObject)JsonConvert.DeserializeObject(userStr);
            LogUtil.AddLog("getWeixinUserInfoJSON_2 " + JsonConvert.SerializeObject(info));

            WechatInfo weixin_info = new WechatInfo();//自己定义的dto
            weixin_info.openId = openid;
            weixin_info.nickname = info["nickname"].ToString();
            weixin_info.sex = info["sex"].ToString();
            weixin_info.province = info["province"].ToString();
            weixin_info.city = info["city"].ToString();
            weixin_info.headimgurl = info["headimgurl"].ToString();//大小为30×30像素的QQ空间头像URL。
            weixin_info.unionid = info["unionid"].ToString();//用户统一标识。针对一个微信开放平台帐号下的应用，同一用户的unionid是唯一的。
            reader.Close();
            stream.Flush();
            stream.Close();
            response.Close();
            return weixin_info;
        }

        public string Batchtagging(List<string> openid, int tag)
        {
            string access_token = GetAcess_Token();

            string getinfo = string.Format("https://api.weixin.qq.com/cgi-bin/tags/members/batchtagging?access_token={0}", access_token);
            BatchtaggingInfo postobj = new BatchtaggingInfo();
            postobj.tagid = tag;
            postobj.openid_list = openid;
            string poststring = JsonConvert.SerializeObject(postobj);
            JObject info = NetUtil.Post_ReturnString(getinfo, poststring).DeserializeNewtonJson<JObject>();

            //string errcode = info["errcode"].ToString();
            string errmsg = info["errmsg"].ToString();

            return errmsg;
        }
    }
}
