﻿using CRM2_API.BLL;
using CRM2_API.BLL.Common.Com_EmailHelper;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    [Description("合同服务管理-邓白氏")]
    public class ContractServiceInfo4DBController : MyControllerBase
    {
        public ContractServiceInfo4DBController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 根据查询条件获取合同服务信息邓白氏申请信息列表
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchContractProductServiceInfoDBApplList_Out> SearchContractProductServiceInfoDBApplList(SearchContractProductServiceInfoDBApplList_In search_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.SearchContractProductServiceInfoDBApplList(search_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据申请id获取合同服务申请信息_邓白氏信息
        /// </summary>
        /// <param name="applyId"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceApplInfoDBByApplId_Out GetContractServiceApplInfoDBByApplId(string applyId)
        {
            return BLL_ContractServiceDB.Instance.GetContractServiceApplInfoDBByApplId(applyId);
        }


        /// <summary>
        /// 撤销合同服务信息邓白氏申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void RevokeContractProductServiceInfoDBAudit(OperateContractProductServiceInfoDBAudit_In operate_In)
        {
            BLL_ContractServiceDB.Instance.RevokeContractProductServiceInfoDBAudit(operate_In);
        }

        /// <summary>
        /// 作废合同服务信息邓白氏申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void VoidContractProductServiceInfoDBAudit(OperateContractProductServiceInfoDBAudit_In operate_In)
        {
            BLL_ContractServiceDB.Instance.VoidContractProductServiceInfoDBAudit(operate_In);
        }
        /// <summary>
        /// 删除合同服务信息邓白氏申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void DeleteContractProductServiceInfoDBAudit(OperateContractProductServiceInfoDBAudit_In operate_In)
        {
            BLL_ContractServiceDB.Instance.DeleteContractProductServiceInfoDBAudit(operate_In);
        }

        /// <summary>
        /// 添加邓白氏邮件模板信息
        /// </summary>
        /// <param name="add_In"></param>
        [HttpPost, PreLog]
        public void AddDBEmailTemplate([FromForm] AddDBEmailTemplate_In add_In)
        {
            BLL_ContractServiceDB.Instance.AddDBEmailTemplate(add_In);
        }

        /// <summary>
        /// 修改邓白氏邮件模板信息
        /// </summary>
        /// <param name="upd_In"></param>
        [HttpPost, PreLog]
        public void UpdateDBEmailTemplate([FromForm] UpdateDBEmailTemplate_In upd_In)
        {
            BLL_ContractServiceDB.Instance.UpdateDBEmailTemplate(upd_In);
        }

        /// <summary>
        /// 删除邓白氏邮件模板信息
        /// </summary>
        /// <param name="Id"></param>
        [HttpPost, PreLog]
        public void DeleteDBEmailTemplate(string Id)
        {
            if (string.IsNullOrEmpty(Id))
                throw new ApiException("未选择要删除的邮件模板");
            var Ids = Id.Split(",").ToList();
            DbOpe_crm_db_emailtemplate.Instance.DeleteData(Ids);
            /* 删除模板文件，现在模板不传文件，注释代码
            DbOpe_crm_db_emailtemplate_attachfile.Instance.DeleteDBEmailTemplateAttachFileByTempId(Id);*/
        }

        /// <summary>
        /// 根据查询条件获取邓白氏邮件模板信息列表
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchDBEmailTemplateList_Out> SearchDBEmailTemplateList(SearchDBEmailTemplateList_In search_In)
        {
            var total = 0;
            var list = DbOpe_crm_db_emailtemplate.Instance.SearchDBEmailTemplateList(search_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据id获取邓白氏邮件模板信息列表
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetDBEmailTemplateById_Out GetDBEmailTemplateById(string Id)
        {
            return DbOpe_crm_db_emailtemplate.Instance.GetDBEmailTemplateById(Id);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息_邓白氏信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoDBByApplId_Out GetContractServiceInfoDBByApplId(string Id)
        {
            return BLL_ContractServiceDB.Instance.GetContractServiceInfoDBByApplId(Id);
           
        }

        /// <summary>
        /// 根据申请id获取合同服务信息_邓白氏信息-服务变更使用
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoDBByApplId4ChangeApply_Out GetContractServiceInfoDBByApplId4ChangeApply(string Id)
        {
            return BLL_ContractServiceDB.Instance.GetContractServiceInfoDBByApplId4ChangeApply(Id);
        }
        /// <summary>
        /// 根据合同产品信息表id获取合同服务信息_邓白氏信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoDBByContractProductInfoId_Out GetContractServiceInfoDBByContractProductInfoId(string Id)
        {
            return DbOpe_crm_contract_serviceinfo_db.Instance.GetContractServiceInfoDBByContractProductInfoId(Id);
        }

        [HttpPost, SkipRSAKey, SkipRightCheck, SkipRecordLog, SkipIPCheck]
        public void MailReadTest()
        {
            Com_EmailHelper.ReadDBEmail_Admin();
        }

        [HttpPost, SkipRSAKey, SkipRightCheck, SkipRecordLog, SkipIPCheck]
        public void SynchroDBData()
        {
            BLL_ContractServiceDB.Instance.SynchroDBData();
        }


    }
}
