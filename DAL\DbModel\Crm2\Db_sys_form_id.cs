﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_form_id")]
    public class Db_sys_form_id
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:FormId
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string FormId {get;set;}

           /// <summary>
           /// Desc:Ip
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string Ip {get;set;}

           /// <summary>
           /// Desc:组
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int GroupIndex {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>           
           public bool Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:修改时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
