﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.Common.Com_EmailHelper;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.VisualBasic;
using SqlSugar;
using System.Globalization;
using System.Security.Policy;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;

namespace CRM2_API.BLL
{
    public class BLL_ContractServiceDB : BaseBLL<BLL_ContractServiceDB>
    {
        /// <summary>
        /// 添加邓白氏邮件模板信息
        /// </summary>
        /// <param name="add_In"></param>
        public void AddDBEmailTemplate(AddDBEmailTemplate_In add_In)
        {
            var emailTemp = new Db_crm_db_emailtemplate();
            emailTemp.Id = Guid.NewGuid().ToString();
            emailTemp.TemplateNum = (DbOpe_crm_db_emailtemplate.Instance.GetQueryCount(emailTemp) + 1).ToString().PadLeft(3, '0');
            emailTemp.SendEmail = add_In.SendEmail;
            emailTemp.Addressee = add_In.Addressee;
            emailTemp.MakeCopy = add_In.MakeCopy;
            emailTemp.Theme = add_In.Theme;
            emailTemp.Content = add_In.Content;
            /* if (add_In.DBEmailTemplateAttachFile != null && add_In.DBEmailTemplateAttachFile.Count > 0)
             {
                 Util<DbOpe_crm_db_emailtemplate_attachfile, BM_AttachFile> tempAttachFile = new Util<DbOpe_crm_db_emailtemplate_attachfile, BM_AttachFile>(DbOpe_crm_db_emailtemplate_attachfile.Instance);
                 tempAttachFile.UploadFile(add_In.DBEmailTemplateAttachFile, EnumAttachFileType.DBEmailTemplate.ToString(), UserId, emailTemp.Id);
             }*/
            emailTemp.Deleted = false;
            emailTemp.CreateDate = DateTime.Now;
            emailTemp.CreateUser = UserId;
            DbOpe_crm_db_emailtemplate.Instance.Insert(emailTemp);
        }

        /// <summary>
        /// 修改邓白氏邮件模板信息
        /// </summary>
        /// <param name="upd_In"></param>
        public void UpdateDBEmailTemplate(UpdateDBEmailTemplate_In upd_In)
        {
            var curTemp = DbOpe_crm_db_emailtemplate.Instance.QueryByPrimaryKey(upd_In.Id);
            curTemp.SendEmail = upd_In.SendEmail;
            curTemp.Addressee = upd_In.Addressee;
            curTemp.MakeCopy = upd_In.MakeCopy;
            curTemp.Theme = upd_In.Theme;
            curTemp.Content = upd_In.Content;
            curTemp.UpdateDate = DateTime.Now;
            curTemp.UpdateUser = UserId;
            //维护已上传文件信息
            //DbOpe_crm_db_emailtemplate_attachfile.Instance.DeleteDBEmailTemplateAttachFileList(upd_In.DBEmailTemplateFileInfo, upd_In.Id);
            /*//上传合同附件
            if (upd_In.DBEmailTemplateAttachFile != null && upd_In.DBEmailTemplateAttachFile.Count > 0)
            {
                Util<DbOpe_crm_db_emailtemplate_attachfile, BM_AttachFile> tempAttachFile = new Util<DbOpe_crm_db_emailtemplate_attachfile, BM_AttachFile>(DbOpe_crm_db_emailtemplate_attachfile.Instance);
                tempAttachFile.UploadFile(upd_In.DBEmailTemplateAttachFile, EnumAttachFileType.DBEmailTemplate.ToString(), UserId, upd_In.Id);
            }*/
            DbOpe_crm_db_emailtemplate.Instance.Update(curTemp);
        }

        /// <summary>
        /// 登记合同服务信息邓白氏申请信息
        /// </summary>
        /// <param name="audit_In"></param>
        /// <exception cref="ApiException"></exception>
        public void AuditContractProductServiceInfoDBAppl(AuditContractProductServiceInfoDBAppl_In audit_In)
        {
            //根据ProductServiceInfoDBApplId获取申请表信息
            var apply = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.QueryByPrimaryKey(audit_In.ProductServiceInfoDBApplId);

            //登记的workflow记录
            var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(apply.ContractId);
            #region 判断是否可以进行登记操作
            /*获取当前申请绑定的服务数据，如果没有绑定服务数据&&申请处在提交状态--可以登记; 如果绑定了服务数据且服务数据是被驳回状态--可以登记; 其余情况不可进行登记*/
            bool couldRegistered = false;
            var curService = DbOpe_crm_contract_serviceinfo_db.Instance.GetData(g => g.ProductServiceInfoDBApplId == apply.Id && g.IsHistory == false);
            if (curService == null && apply.State == EnumProcessStatus.Submit.ToInt())
                couldRegistered = true;
            else if (curService != null && (curService.State == EnumContractServiceState.RETURNED || curService.State == EnumContractServiceState.TO_BE_OPENED))
                couldRegistered = true;
            if (!couldRegistered)
                throw new ApiException("当前申请无法进行初审操作");
            #endregion
            //如果登记人员执行拒绝操作，修改申请数据apply的状态为拒绝，如果是服务变更数据，对已经锁定的旧数据放开锁定
            if (!audit_In.State)
            {
                apply.State = EnumProcessStatus.Refuse.ToInt();
                //放开被变更的服务数据可操作
                var curOpenService = DbOpe_crm_contract_serviceinfo_db.Instance.GetData(g => (g.State == EnumContractServiceState.VALID || g.State == EnumContractServiceState.OUT) && g.ContractProductInfoId == apply.ContractProductInfoId);
                if (curOpenService != null)
                {
                    var currentAppl = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataById(curOpenService.ProductServiceInfoDBApplId);
                    currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(currentAppl);
                }
                apply.Feedback = audit_In.FeedBack;
                apply.ReviewerDate = DateTime.Now;
                apply.ReviewerId = UserId;
                apply.UpdateDate = DateTime.Now;
                apply.UpdateUser = UserId;
                apply.Remark4List = audit_In.FeedBack;
                if (apply.ProcessingType == (int)EnumProcessingType.Change)
                    //拒绝后该数据状态为无效
                    apply.IsInvalid = (int)EnumIsInvalid.Invalid;
                DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply);
                
                // 更新合同产品申请状态为拒绝
                ContractProductApplStateHelper.UpdateContractProductApplState(
                    apply.ContractProductInfoId, 
                    EnumProcessStatus.Refuse, 
                    UserId
                    );


                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = apply.CreateUser;
                message.MessageTypeToId = apply.Id;
                message.MessagemMainAboutDes = contract.ContractName;
                message.LocalFeedBack = audit_In.FeedBack;
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.DBService, EnumMessageStateInfo.Refus, apply.ContractId);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            }
            //如果初审登记通过，需要验证合同信息，判断合同是否到账
            else
            {
                if (string.IsNullOrEmpty(audit_In.SubscriptionPeriod))
                    throw new ApiException("SubscriptionPeriod不可为空!");
                var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
                if (contractInfo == null)
                    throw new ApiException("未找到合同信息");
                if (string.IsNullOrEmpty(contractInfo.ContractNum))
                    throw new ApiException("未到账合同不能开通服务");
                apply.Remark4List = audit_In.FeedBack;
                DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply);
                
                // 更新合同产品申请状态为待审核（初审通过）
                ContractProductApplStateHelper.UpdateContractProductApplState(
                    apply.ContractProductInfoId, 
                    EnumProcessStatus.Submit, 
                    UserId
                    );
            }
            var now = DateTime.Now;

            var servDBInfo = audit_In.MappingTo<Db_crm_contract_serviceinfo_db>();
            servDBInfo.Id = Guid.NewGuid().ToString();
            servDBInfo.ContractId = apply.ContractId;
            servDBInfo.ProductId = apply.ProductId;
            servDBInfo.ContractProductInfoId = apply.ContractProductInfoId;
            if (!string.IsNullOrEmpty(audit_In.SubscriptionPeriod))
            {
                var servCycleList = audit_In.SubscriptionPeriod.Split("-").ToArray();
                //判断SubscriptionPeriod的值是否是日期格式
                DateTime scStart, scEnd = new DateTime();
                if (DateTime.TryParse(servCycleList[0], out scStart))
                    servDBInfo.ServiceCycleStart = scStart;
                else
                    servDBInfo.ServiceCycleStart = audit_In.ServiceCycleStart;

                if (DateTime.TryParse(servCycleList[1], out scEnd))
                    servDBInfo.ServiceCycleEnd = scEnd;
                else
                    servDBInfo.ServiceCycleEnd = audit_In.ServiceCycleStart;
            }
            /*servDBInfo.ServiceCycleStart = servCycleList[0].ToString().Trim().ToDateTime();
            servDBInfo.ServiceCycleEnd = servCycleList[1].ToString().Trim().ToDateTime();*/
            servDBInfo.State = audit_In.State ? EnumContractServiceState.TO_BE_REVIEW : EnumContractServiceState.REFUSE;
            servDBInfo.Deleted = false;
            servDBInfo.IsChanged = false;
            servDBInfo.CreateDate = now;
            servDBInfo.CreateUser = UserId;
            servDBInfo.RegisteredId = UserId;
            servDBInfo.RegisteredTime = now;
            servDBInfo.RegisteredRemark = audit_In.FeedBack;
            servDBInfo.IsHistory = false;
            servDBInfo.ProcessingType = apply.ProcessingType;
            servDBInfo.ServiceMonth = audit_In.ServiceMonth;
            //判断是否是被驳回数据
            if (curService != null)
            {
                //如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
                curService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(curService);
                //被驳回服务重新初审通过时，删除被驳回服务的到账绑定关系
                DbOpe_crm_contract_receiptregister_service.Instance.DeleteReturnServiceLinkDataQueue(curService.Id);
            }
            DbOpe_crm_contract_serviceinfo_db.Instance.InsertQueue(servDBInfo);

            //如果初审通过，记录SiteID & 保存合同附件
            if (audit_In.State)
            {
                if (!string.IsNullOrEmpty(audit_In.SiteID))//servDBInfo.ProcessingType == EnumProcessingType.Add.ToInt() && 
                {
                    //服务申请开通服务时，写入SiteID数据
                    var siteData = new Db_crm_contract_serviceinfo_db_siteid();
                    siteData.SITEID = audit_In.SiteID;
                    siteData.ContractServiceInfoDBId = servDBInfo.Id;
                    DbOpe_crm_contract_serviceinfo_db_siteid.Instance.InsertDataQueue(siteData);
                }
                var filePathList = new List<string>();
                //上传合同附件
                if (audit_In.Appendix != null && audit_In.Appendix.Count > 0)
                {
                    Util<DbOpe_crm_contract_serviceinfo_db_attachfile, BM_AttachFile> tempAttachFile = new Util<DbOpe_crm_contract_serviceinfo_db_attachfile, BM_AttachFile>(DbOpe_crm_contract_serviceinfo_db_attachfile.Instance);
                    filePathList = tempAttachFile.UploadFileReturnFileUrls(audit_In.Appendix, EnumAttachFileType.DBEmailTemplate.ToString(), UserId, servDBInfo.Id);
                }
                //绑定到账信息
                if (audit_In.ReceiptRegisterIds != null && audit_In.ReceiptRegisterIds.Count > 0)
                {
                    DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptDataQueue(audit_In.ReceiptRegisterIds, servDBInfo.Id, EnumProductType.DandB);
                }
            }
            string dataState = String.Empty;
            if (audit_In.State)
                dataState = EnumContractServiceOpenState.ToBeReview.GetEnumDescription();
            else
                dataState = EnumContractServiceOpenState.Refuse.GetEnumDescription();
            //string dataState = servDBInfo.State.GetEnumDescription().ToString();// Dictionary.ContractServiceOpenState.First(e => e.Value == apply.State.ToInt().ToString()).Name;
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_db, Db_crm_contract>("邓白氏服务审批流程", apply.Id, servDBInfo, contract, audit_In.FeedBack, dataState, "初审");
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.SaveQueues();

        }


        /// <summary>
        /// 邓白氏开通-复核
        /// </summary>
        /// <param name="review_In"></param>
        public void ReviewContractProductServiceInfoDBAppl(ReviewContractProductServiceInfoDBAppl_In review_In)
        {
            //根据ProductServiceInfoDBApplId获取申请表信息
            var apply = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.QueryByPrimaryKey(review_In.ProductServiceInfoDBApplId);
            //根据ProductServiceInfoDBApplId获取服务表信息
            var curService = DbOpe_crm_contract_serviceinfo_db.Instance.GetData(g => g.ProductServiceInfoDBApplId == apply.Id && g.IsHistory == false);

            #region 判断是否可以进行复核操作
            /*如果存在绑定服务数据&&服务数据为待复核状态--可以复核;其余情况不可进行复核*/
            if (!(curService != null && curService.State == EnumContractServiceState.TO_BE_REVIEW))
                throw new ApiException("当前申请无法进行复核操作");
            #endregion

            var now = DateTime.Now;
            //驳回
            if (!review_In.State)
            {
                curService.State = EnumContractServiceState.RETURNED;
                apply.Remark4List = review_In.FeedBack;
            }
            //开通
            else
            {
                //合同信息验证
                var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
                if (contractInfo == null)
                    throw new ApiException("未找到合同信息");
                if (string.IsNullOrEmpty(contractInfo.ContractNum))
                    throw new ApiException("未到账合同不能开通服务产品");
                //处理申请信息
                apply.State = EnumProcessStatus.Pass.ToInt();
                apply.ReviewerDate = now;
                apply.ReviewerId = UserId;
                apply.UpdateDate = now;
                apply.UpdateUser = UserId;
                apply.Feedback = review_In.FeedBack;
                apply.Remark4List = review_In.ReviewerRemark;
                DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply);
                
                // 更新合同产品申请状态为通过
                ContractProductApplStateHelper.UpdateContractProductApplState(
                    apply.ContractProductInfoId, 
                    EnumProcessStatus.Pass, 
                    UserId
                    );
                //服务状态置位生效
                curService.State = EnumContractServiceState.VALID;
                curService.SubscriptionPeriod = curService.ServiceCycleStart.Value.ToString("dd MMM.yyyy", CultureInfo.CreateSpecificCulture("en-GB")) + "-" + curService.ServiceCycleEnd.Value.ToString("dd MMM.yyyy", CultureInfo.CreateSpecificCulture("en-GB"));
                /* //判断SubscriptionPeriod的值是否是日期格式
                 * DateTime.Now.ToString("dd MMM.yyyy", CultureInfo.CreateSpecificCulture("en-GB"));
                    DateTime scStart, scEnd = new DateTime();
                    if (DateTime.TryParse(servCycleList[0], out scStart))
                        servDBInfo.ServiceCycleStart = scStart;
                    else
                        servDBInfo.ServiceCycleStart = audit_In.ServiceCycleStart;

                    if (DateTime.TryParse(servCycleList[1], out scEnd))
                        servDBInfo.ServiceCycleEnd = scEnd;
                    else
                        servDBInfo.ServiceCycleEnd = audit_In.ServiceCycleStart;
                */


                //如果是服务变更，对原开通数据进行处理
                if (curService.ProcessingType == EnumProcessingType.Change.ToInt())
                {//服务申请修改 需要找到当前的服务记录数据(根据ContractProductInfoId)，修改isChanged=true，补充ChangedId,当前数据补充HistoryId
                    //获取原服务数据进行修改
                    var originalServe = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByConProInfoId(apply.ContractProductInfoId);
                    originalServe.IsChanged = true;
                    originalServe.ChangedId = curService.Id;
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    originalServe.State = EnumContractServiceState.INVALID;
                    DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(originalServe);
                    //变更服务补充HistoryId
                    curService.HistoryId = originalServe.Id;
                }
                //发送邮件
                Com_EmailHelper.SendOpenDBServiceEmail(curService, review_In.FilePathList, curService.ProcessingType.ToInt());

                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = apply.CreateUser;
                message.MessageTypeToId = apply.Id;
                message.MessagemMainAboutDes = contractInfo.ContractName;
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.DBService, EnumMessageStateInfo.Pass, apply.ContractId);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            }
            curService.ReviewerId = UserId;
            curService.ReviewerTime = now;
            curService.ReviewerRemark = review_In.ReviewerRemark;
            curService.UpdateDate = now;
            curService.UpdateUser = UserId;
            curService.Remark = review_In.FeedBack;
            DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(curService);
            //开通的workflow记录
            var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(apply.ContractId);
            string dataState = String.Empty;
            if (review_In.State)
                dataState = EnumContractServiceOpenState.Open.GetEnumDescription();
            else
                dataState = EnumContractServiceOpenState.Returned.GetEnumDescription();
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_db, Db_crm_contract>("邓白氏服务审批流程", apply.Id, curService, contract, apply.ApplicantId, curService.Remark, dataState, "复核");

            #region 2024.3.19改为所有流转记录都保存审核反馈，这段内容注释
            /*string dataState = String.Empty;
            string remark = String.Empty;
            if (review_In.State)
            {
                dataState = EnumContractServiceOpenState.Open.GetEnumDescription();
                remark = curService.Remark;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_db, Db_crm_contract>("邓白氏服务审批流程", apply.Id, curService, contract, apply.ApplicantId, remark, dataState, "复核");
            }
            else
            {
                dataState = EnumContractServiceOpenState.Returned.GetEnumDescription();
                remark = curService.ReviewerRemark;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_db, Db_crm_contract>("邓白氏服务审批流程", apply.Id, curService, contract, remark, dataState, "复核");
            }*/
            #endregion
            //string dataState = curService.State.GetEnumDescription().ToString(); //string dataState = Dictionary.ContractServiceOpenState.First(e => e.Value == apply.State.ToInt().ToString()).Name;

            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.SaveQueues();
            //11.20 开通服务要刷新合同的保护截止日
            if (review_In.State)
                DbOpe_crm_contract.Instance.RefreshContractProtectDate(apply.ContractId);
        }

        /// <summary>
        /// 撤销合同服务信息邓白氏申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        public void RevokeContractProductServiceInfoDBAudit(OperateContractProductServiceInfoDBAudit_In operate_In)
        {
            //声明可撤销状态的集合, 待复核、被驳回、拒绝、已开通状态的数据可以进行撤销操作
            var couldRevokeOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.ToBeReview, EnumContractServiceOpenState.Returned, EnumContractServiceOpenState.Refuse, EnumContractServiceOpenState.Open };
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetServiceDBApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以撤销
            if (!couldRevokeOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可撤销");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByApplyId(operate_In);
            var now = DateTime.Now;
            //待复核状态撤销到待开通/待变更状态：申请数据不变，服务数据状态改为TO_BE_OPENED(10)
            if (apply.OpenState == EnumContractServiceOpenState.ToBeReview)
            {
                //服务数据状态改为TO_BE_OPENED(10)
                service.State = EnumContractServiceState.TO_BE_OPENED;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(service);
            }
            //被驳回状态撤销到待复核状态：申请数据不变，服务数据状态改为TO_BE_REVIEW(20)
            else if (apply.OpenState == EnumContractServiceOpenState.Returned)
            {
                //服务数据状态改为TO_BE_REVIEW(20)
                service.State = EnumContractServiceState.TO_BE_REVIEW;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(service);
            }
            //拒绝状态撤销到待开通/待变更状态：申请数据状态改为EnumProcessStatus.Submit(1),其他申请置位失效
            else if (apply.OpenState == EnumContractServiceOpenState.Refuse)
            {
                //申请数据状态改为EnumProcessStatus.Submit(1)
                apply.State = EnumProcessStatus.Submit.ToInt();
                apply.UpdateDate = now;
                apply.UpdateUser = UserId;
                DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_db_appl>());
                //服务数据状态改为TO_BE_OPENED(10)
                service.State = EnumContractServiceState.TO_BE_OPENED;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(service);
                //其他的申请要置为无效，只保留当前操作的这个申请
                var otherAppls = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataList(g => g.ContractProductInfoId == apply.ContractProductInfoId && g.Id != operate_In.ApplyId);
                otherAppls.ForEach(a =>
                {
                    DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateData(r => new Db_crm_contract_productserviceinfo_db_appl { IsInvalid = (int)EnumIsInvalid.Invalid }, r => a.Id == r.Id);
                });
            }
            //已开通状态撤销到待复核状态
            else if (apply.OpenState == EnumContractServiceOpenState.Open)
            {
                //申请数据状态改为EnumProcessStatus.Submit(1)
                apply.State = EnumProcessStatus.Submit.ToInt();
                apply.UpdateDate = now;
                apply.UpdateUser = UserId;
                //服务状态变更为待复核EnumContractServiceState.TO_BE_REVIEW(20)
                DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_db_appl>());
                service.State = EnumContractServiceState.TO_BE_REVIEW;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(service);
                //其他的申请要置为无效，只保留当前操作的这个申请
                var otherAppls = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataList(g => g.ContractProductInfoId == apply.ContractProductInfoId && g.Id != operate_In.ApplyId);
                otherAppls.ForEach(a =>
                {
                    DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateData(r => new Db_crm_contract_productserviceinfo_db_appl { IsInvalid = (int)EnumIsInvalid.Invalid }, r => a.Id == r.Id);
                });
                //如果是服务变更，原服务恢复正常
                if (apply.ProcessingType == EnumProcessingType.Change.ToInt())
                {
                    var originalServe = DbOpe_crm_contract_serviceinfo_db.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    originalServe.IsChanged = false;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(originalServe);
                }
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.SaveQueues();
            //workflow撤销
            var apply_after = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetServiceDBApplyInfoWithStateByApplyId(operate_In.ApplyId);
            var apply_workflow = apply.MappingTo<Db_crm_contract_productserviceinfo_db_appl>();
            apply_workflow.State = apply_after.OpenState.ToInt();
            string state = ((EnumContractServiceOpenState)apply.OpenState).GetEnumDescription();
            BLL_WorkFlow.Instance.CancelWorkflowPending("邓白氏服务审批流程", apply_after.Id, state, apply_workflow);

            #region 原撤销逻辑 2023.12.12注释代码
            /*
            //获取申请数据 
            var apply = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
            //待开通、待变更(申请为Submit)状态的数据不可撤销
            if (apply.State == EnumProcessStatus.Submit.ToInt())
                throw new ApiException("所选的申请不可撤销");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByApplyId(operate_In);
            //若存在服务，过期(服务为过期)、作废(服务为作废或失效)状态的数据不可撤销
            if (service != null && (service.State == EnumContractServiceState.INVALID || service.State == EnumContractServiceState.VOID || service.State == EnumContractServiceState.OUT))
                throw new ApiException("所选的申请不可撤销");
            //如果存在服务，处理服务后续内容
            if (service != null)
            {
                //服务的状态改为作废
                //service.State = EnumContractServiceState.VOID;
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(service);
                //如果是服务变更，原服务恢复正常
                if (!string.IsNullOrEmpty(service.HistoryId))
                {
                    var originalServe = DbOpe_crm_contract_serviceinfo_db.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    originalServe.IsChanged = false;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(originalServe);
                }
                //workflow撤销
                BLL_WorkFlow.Instance.CancelWorkflowPending("邓白氏服务审批流程", service.Id);
                //邮件追回--待完成
            }
            #region 撤销不对锁定数据状态进行处理2023.11.17
            *//*//如果是服务变更申&&申请由拒绝撤销为待变更，检查当前服务，将对应数据设置为失效
            else if (apply.State == EnumProcessStatus.Refuse.ToInt() && apply.ProcessingType == EnumProcessingType.Change.ToInt())
            {
                var currentService = DbOpe_crm_contract_serviceinfo_db.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false);
                if (currentService != null)
                {
                    var currentAppl = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataById(currentService.ProductServiceInfoDBApplId);
                    currentAppl.IsInvalid = EnumIsInvalid.Invalid.ToInt();
                    DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(currentAppl);
                }
            }*//*
            #endregion
            //申请的状态改为待开通
            apply.State = EnumProcessStatus.Submit.ToInt();
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply);
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.SaveQueues();
            //其他的申请要置为无效，只保留当前操作的这个申请
            var otherAppls = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataList(g => g.ContractProductInfoId == apply.ContractProductInfoId && g.Id != operate_In.ApplyId);
            otherAppls.ForEach(a =>
            {
                //a.IsInvalid = (int)EnumIsInvalid.Invalid;
                DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateData(r => new Db_crm_contract_productserviceinfo_db_appl { IsInvalid = (int)EnumIsInvalid.Invalid }, r => a.Id == r.Id);
            });
            */
            #endregion
        }

        /// <summary>
        /// 作废合同服务信息邓白氏申请审核信息  待开通 待变更 已开通 的服务可以作废
        /// </summary>
        /// <param name="operate_In"></param>
        public void VoidContractProductServiceInfoDBAudit(OperateContractProductServiceInfoDBAudit_In operate_In)
        {

            //声明可作废状态的集合, 过期、作废状态的数据不可以进行作废操作
            var couldRevokeOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.OverDue, EnumContractServiceOpenState.Void };
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetServiceDBApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以撤销
            if (couldRevokeOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可撤销");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByApplyId(operate_In);
            //当前时间
            var now = DateTime.Now;
            //申请的状态改为作废
            apply.State = EnumProcessStatus.Void.ToInt();
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_db_appl>());
            //如果存在服务，处理服务后续内容
            if (service != null)
            {
                //服务的状态改为作废
                service.State = EnumContractServiceState.VOID;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(service);
            }
            //服务变更处理
            if (apply.ProcessingType == EnumProcessingType.Change.ToInt())
            {
                var originalServe = new Db_crm_contract_serviceinfo_db();
                //已开通服务撤销，原服务恢复正常
                if (apply.OpenState == EnumContractServiceOpenState.Open)
                {
                    originalServe = DbOpe_crm_contract_serviceinfo_db.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(originalServe);
                }
                //未开通服务的撤销，获取原服务数据
                else
                    originalServe = DbOpe_crm_contract_serviceinfo_db.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false && g.IsHistory == false);
                //将原服务数据置位生效状态
                if (originalServe.ProductServiceInfoDBApplId.IsNotNullOrEmpty())
                {
                    var originalAppl = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataById(originalServe.ProductServiceInfoDBApplId);
                    originalAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(originalAppl);
                }
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.SaveQueues();
            #region 原作废逻辑 2023.12.12注释代码
            /*
            //获取申请数据 
            var apply = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
            *//* 2023.10.8 拒绝状态可以作废
            //申请为拒绝状态的数据不可以作废
            if (apply.State == EnumProcessStatus.Refuse.ToInt())
                throw new ApiException("所选的申请不可作废");
            *//*
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByApplyId(operate_In);
            //若存在服务，过期(服务为过期)、作废(服务为作废或失效)状态的数据不可以作废
            if (service != null && (service.State == EnumContractServiceState.INVALID || service.State == EnumContractServiceState.VOID || service.State == EnumContractServiceState.OUT))
                throw new ApiException("所选的申请不可作废");
            //申请的状态改为待开通 //改为作废
            apply.State = EnumProcessStatus.Void.ToInt();
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply);


            //如果存在服务，处理服务后续内容
            if (service != null)
            {
                //服务的状态改为作废
                service.State = EnumContractServiceState.VOID;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(service);
                //如果是服务变更，原服务恢复正常
                if (!string.IsNullOrEmpty(service.HistoryId))
                {
                    var originalServe = DbOpe_crm_contract_serviceinfo_db.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(originalServe);

                    var originalAppl = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataById(originalServe.ProductServiceInfoDBApplId);
                    originalAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(originalAppl);
                }
                //workflow撤销
                //BLL_WorkFlow.Instance.CancelWorkflowPending("邓白氏服务审批流程", service.Id);
                //邮件追回--待完成
            }
            //如果作废的是服务变更申请，且没有开通服务，则解除被变更的服务锁定状态
            else if (apply.ProcessingType == (int)EnumProcessingType.Change)
            {
                //获取被变更的服务数据
                var currentService = DbOpe_crm_contract_serviceinfo_db.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false);
                if (currentService != null)
                {
                    var currentAppl = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataById(currentService.ProductServiceInfoDBApplId);
                    currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(currentAppl);
                }
            }
            */
            #endregion
        }

        /// <summary>
        /// 删除合同服务信息邓白氏申请审核信息  过期、拒绝、作废的服务可以删除;2023.11.17 只有拒绝的数据可以删除
        /// </summary>
        /// <param name="operate_In"></param>
        public void DeleteContractProductServiceInfoDBAudit(OperateContractProductServiceInfoDBAudit_In operate_In)
        {
            //声明可删除状态的集合, 拒绝、作废状态的数据可以进行删除操作
            var couldDeleteOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.Refuse, EnumContractServiceOpenState.Void };
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetServiceDBApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以删除
            if (!couldDeleteOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可删除");
            //删除申请
            apply.Deleted = true;
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_db_appl>());
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByApplyId(operate_In);
            if (service != null)
            {
                //删除服务
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(service);
            }
            #region 删除不需要对锁定数据进行处理，删除之前都处理过了
            /* 
            //如果删除的是服务变更申请，则解除被变更的服务锁定状态
            if (apply.ProcessingType == (int)EnumProcessingType.Change)
            {
                //获取被变更的服务数据
                var currentService = DbOpe_crm_contract_serviceinfo_db.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false && g.IsHistory == false);
                if (currentService != null)
                {
                    var currentAppl = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataById(currentService.ProductServiceInfoDBApplId);
                    currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(currentAppl);
                }
            }
            */
            #endregion
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.SaveQueues();
            #region 原删除逻辑，2023.12.13注释代码
            /*
                        //获取申请数据 
                        var apply = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
                        *//*2023.11.18注释，改为拒绝状态可以删除
                         * //申请待开通、待变更状态的数据不可以删除
                        if (apply.State == EnumProcessStatus.Submit.ToInt())
                            throw new ApiException("所选的申请不可删除");*//*
                        //获取服务数据
                        var service = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByApplyId(operate_In);
                        //存在服务且服务状态正常的数据不可以删除 或 不存在服务且申请状态不为拒绝的数据 不可以删除 2023.11.17 添加
                        if ((service != null && service.State == EnumContractServiceState.VALID) || (service == null && apply.State != EnumContractServiceOpenState.Refuse.ToInt()))
                            throw new ApiException("所选的申请不可删除");
                        //删除申请
                        apply.Deleted = true;
                        apply.UpdateUser = UserId;
                        apply.UpdateDate = DateTime.Now;
                        DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(apply);
                        if (service != null)
                        {
                            //删除服务
                            service.Deleted = true;
                            service.UpdateUser = UserId;
                            service.UpdateDate = DateTime.Now;
                            DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(service);
                        }

                        //如果删除的是服务变更申请，则解除被变更的服务锁定状态
                        if (apply.ProcessingType == (int)EnumProcessingType.Change)
                        {
                            //获取被变更的服务数据
                            var currentService = DbOpe_crm_contract_serviceinfo_db.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false);
                            if (currentService != null)
                            {
                                var currentAppl = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetDataById(currentService.ProductServiceInfoDBApplId);
                                currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                                DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateQueue(currentAppl);
                            }
                        }

                         //提交sql队列
                        DbOpe_crm_contract_productserviceinfo_db_appl.Instance.SaveQueues();
            */
            #endregion
        }

        /// <summary>
        /// 根据申请id获取合同服务申请信息_邓白氏信息
        /// </summary>
        /// <param name="applyId"></param>
        /// <returns></returns>
        public GetContractServiceApplInfoDBByApplId_Out GetContractServiceApplInfoDBByApplId(string applyId)
        {
            //获取申请详细信息
            var ret = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetContractServiceApplInfoDBByApplId(applyId);
            //获取合同详细信息
            ret.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(ret.ContractId);
            //到账备注列表
            ret.ReceiptRemarks = DbOpe_crm_contract_receiptregister.Instance.GetRemarksByContractId(ret.ContractId);

            //查找当前甲方公司是否存在其他合同开通邓白氏服务
            var oldSiteId = DbOpe_crm_contract_serviceinfo_db.Instance.GetOldContractProductIdByCompanyId(ret.ContractInfo.FirstParty, ret.ContractId);
            //获取产品详细信息
            ret.ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(ret.ContractProductInfoId).MappingTo<GetContractServiceApplInfoDBByApplId_Out_ProductInfo>();
            //初审查看信息
            ret.RegisterInfo = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetToRegisterInfo(applyId);
            ret.RegisterInfo.OldSITEID = oldSiteId;
            ret.RegisterInfo.SubscriptionType = ret.ProductInfo.ProductName;
            //复核查看信息
            ret.ReviewInfo = DbOpe_crm_contract_serviceinfo_db.Instance.GetReviewInfo(applyId);
            if (ret.ReviewInfo != null)
                ret.ReviewInfo.OldSITEID = oldSiteId;
            //历史服务信息
            ret.HistoryDBServiceList = GetHistoryDBServiceList(ret.Id, ret.ProcessingType.Value, ret.ContractInfo.Id);
            //合同的所有到账信息
            ret.ReceiptRegisterCollectionList = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(ret.ContractId, String.Empty, EnumProductType.DandB, ret.ReviewInfo == null ? null : ret.ReviewInfo.Id);
            //当前服务已绑定的到账信息
            if (ret.ReviewInfo != null)
                ret.LinkedReceiptRegisterIds = DbOpe_crm_contract_receiptregister_service.Instance.GetReceiptRegisterIdsByServiceId(ret.ReviewInfo.Id);
            return ret;
        }

        /// <summary>
        /// 根据合同产品信息表id获取合同服务信息_邓白氏信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public GetContractServiceInfoDBByApplId_Out GetContractServiceInfoDBByApplId(string Id)
        {
            //获取申请详细信息
            var obj = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetContractServiceInfoDBByApplId(Id);
            //获取合同信息
            obj.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(obj.ContractId);
            #region 历史服务信息
            obj.HistoryDBServiceList = GetHistoryDBServiceList(obj.Id, obj.ProcessingType.ToInt(), obj.ContractInfo.Id);
            #endregion

            #region 变更信息
            if (obj.ProcessingType == EnumProcessingType.Change)
            {
                var curData = new DBChangeItemProp();
                var compareData = new DBChangeItemProp();

                if (obj.State == EnumContractServiceOpenState.TobeChanged)
                {//待变更状态,当前数据取申请数据,被比较数据取当前在服数据
                    curData = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetApplChangeItemProp(Id);
                    compareData = DbOpe_crm_contract_serviceinfo_db.Instance.GetServDataChangeItemPropByContractId(obj.ContractId);
                }
                else if (obj.State == EnumContractServiceOpenState.ToBeReview || obj.State == EnumContractServiceOpenState.Returned)
                {//待复核状态，当前数据取当前服务表数据，被比较数据取当前在服数据
                    curData = DbOpe_crm_contract_serviceinfo_db.Instance.GetServDataChangeItemPropByServiceId(obj.ServiceId);
                    compareData = DbOpe_crm_contract_serviceinfo_db.Instance.GetServDataChangeItemPropByContractId(obj.ContractId);
                }
                else if (obj.State == EnumContractServiceOpenState.Open || obj.State == EnumContractServiceOpenState.NearlyEnd || obj.State == EnumContractServiceOpenState.OverDue || obj.State == EnumContractServiceOpenState.Void)
                {//开通、即将到期、过期、作废状态,当前数据取当前服务表数据，被比较数据取记录的历史数据
                    curData = DbOpe_crm_contract_serviceinfo_db.Instance.GetServDataChangeItemPropByServiceId(obj.ServiceId);
                    compareData = DbOpe_crm_contract_serviceinfo_db.Instance.GetServDataChangeItemPropByChangedId(obj.ServiceId);
                }
                else if (obj.State == EnumContractServiceOpenState.Refuse)
                {//拒绝状态，当前数据取当前的申请数据，被比较数据先获取当前在服数据，如果当前在服数据是在申请之后产生的，则获取申请时间之前的最新的被变更的通过的服务
                    var applData = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.QueryByPrimaryKey(Id);
                    var servData = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByConProInfoId(obj.ContractProductInfoId);
                    if (servData.CreateDate > applData.CreateDate)
                        compareData = DbOpe_crm_contract_serviceinfo_db.Instance.GetRefuseDataChangeItemPropByContractProductInfoId(obj.ContractProductInfoId, applData.CreateDate.Value);
                    else
                        compareData = servData.MappingTo<DBChangeItemProp>();
                    curData = applData.MappingTo<DBChangeItemProp>();
                }

                obj.DBChangeItems = new List<DBChangeItems>();
                var changeAbleCols = new List<DBChangeItems>()
                {
                     new DBChangeItems(){ PropKey = "CustomerCompanyName", PropName = "Customer Company Name",IsArray = false },
                     new DBChangeItems(){ PropKey = "CustomerContactName", PropName = "Customer Contact Name",IsArray = false },
                     new DBChangeItems(){ PropKey = "CustomerContactEmailAddress", PropName = "Customer Contact Email Address",IsArray = false },
                     new DBChangeItems(){ PropKey = "SalesRepEmailAddress", PropName = "Sales Rep Email Address",IsArray = false },
                     new DBChangeItems(){ PropKey = "SubscriptionPeriod", PropName = "Subscription Period",IsArray = false },
                     new DBChangeItems(){ PropKey = "ServiceCycle", PropName = "服务周期",IsArray = false },
                     //new DBChangeItems(){ PropKey = "Remark", PropName = "备注",IsArray = false },
                };

                foreach (var col in changeAbleCols)
                {
                    if (obj.State == EnumContractServiceOpenState.TobeChanged || col.PropKey == "SalesRepEmailAddress")
                        continue;
                    var curV = curData.GetType().GetProperty(col.PropKey).GetValue(curData, null);
                    var compareV = compareData.GetType().GetProperty(col.PropKey).GetValue(compareData, null);
                    if (curV?.ToString() != compareV?.ToString())
                    {
                        var newItem = col.MappingTo<DBChangeItems>();
                        newItem.ChangeValue = curV?.ToString();
                        obj.DBChangeItems.Add(newItem);
                    }
                }
            }
            #endregion

            //整理摒除冗余属性数据
            var retObj = obj.MappingTo<GetContractServiceInfoDBByApplId_Out>();
            return retObj;
        }

        /// <summary>
        /// 根据申请id获取合同服务信息_邓白氏信息-服务变更使用
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public GetContractServiceInfoDBByApplId4ChangeApply_Out GetContractServiceInfoDBByApplId4ChangeApply(string Id)
        {
            //获取申请详细信息
            var obj = DbOpe_crm_contract_serviceinfo_db.Instance.GetContractServiceInfoDBByApplId4ChangeApply(Id);
            //获取合同信息
            obj.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(obj.ContractId);
            //整理摒除冗余属性数据
            var retObj = obj.MappingTo<GetContractServiceInfoDBByApplId4ChangeApply_Out>();
            return retObj;
        }

        /// <summary>
        /// 获取历史服务信息
        /// </summary>
        /// <param name="hisParamApplyId"></param>
        /// <param name="processingType"></param>
        /// <param name="contractId"></param>
        /// <returns></returns>
        private List<HistoryDBServiceInfo> GetHistoryDBServiceList(string hisParamApplyId, int processingType, string contractId)
        {
            var retList = new List<HistoryDBServiceInfo>();
            var isChange = processingType == (int)EnumProcessingType.Change;
            //如果是续约合同的服务或是变更的服务，需要查找历史数据
            while (isChange)
            {
                //声明历史数据
                var hisServe = new HistoryDBServiceInfo_Mid();
                //如果当前服务是变更数据，取被变更的服务信息
                if (isChange)
                    //获取被变更的服务数据信息
                    hisServe = DbOpe_crm_contract_serviceinfo_db.Instance.GetChangedDBServiceInfoByContractId(hisParamApplyId, contractId);
                if (hisServe != null)
                {
                    //修改isChange 判断是否是变更的服务
                    isChange = hisServe.ProcessingType == (int)EnumProcessingType.Change;
                    //修改查找历史数据要用的ContractId
                    hisParamApplyId = hisServe.ApplyId;
                    //填充返回列表
                    retList.Add(hisServe.MappingTo<HistoryDBServiceInfo>());
                }
            }
            return retList;
        }
        /// <summary>
        /// 同步邓白氏数据
        /// </summary>
        public void SynchroDBData()
        {
            //填充数据
            var dataList = new List<SynchroDBData_InnerClass>();
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "26186", ServiceStart = DateTime.Parse("2024-08-06"), ServiceEnd = DateTime.Parse("2025-08-05"), CompanyName = "安徽涌诚机械有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "26183", ServiceStart = DateTime.Parse("2024-08-06"), ServiceEnd = DateTime.Parse("2025-08-05"), CompanyName = "大连弘润莲花食品有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "26159", ServiceStart = DateTime.Parse("2024-08-05"), ServiceEnd = DateTime.Parse("2026-08-04"), CompanyName = "河南驰诚电气股份有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "26133", ServiceStart = DateTime.Parse("2024-08-05"), ServiceEnd = DateTime.Parse("2025-08-04"), CompanyName = "山东得兴生物科技有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "26112", ServiceStart = DateTime.Parse("2024-07-30"), ServiceEnd = DateTime.Parse("2025-07-29"), CompanyName = "深圳坤邦标价用品有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "26098", ServiceStart = DateTime.Parse("2024-07-30"), ServiceEnd = DateTime.Parse("2025-07-29"), CompanyName = "扬州一丰铜业有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "26034", ServiceStart = DateTime.Parse("2024-07-25"), ServiceEnd = DateTime.Parse("2026-07-24"), CompanyName = "深圳市力辉电机有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "25988", ServiceStart = DateTime.Parse("2024-07-22"), ServiceEnd = DateTime.Parse("2025-07-21"), CompanyName = "临沂驰立进出口有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "25741", ServiceStart = DateTime.Parse("2024-07-18"), ServiceEnd = DateTime.Parse("2025-07-17"), CompanyName = "上海新维特生物科技有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "25895", ServiceStart = DateTime.Parse("2024-07-15"), ServiceEnd = DateTime.Parse("2025-07-14"), CompanyName = "深圳市磊通科技有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "25861", ServiceStart = DateTime.Parse("2024-07-10"), ServiceEnd = DateTime.Parse("2025-07-09"), CompanyName = "上海孚因流体动力设备股份有限公司" });
            dataList.Add(new SynchroDBData_InnerClass() { ContractNum = "25644", ServiceStart = DateTime.Parse("2024-07-04"), ServiceEnd = DateTime.Parse("2025-07-03"), CompanyName = "深圳三佰服装有限公司" });
            var now = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.TransDeal(() =>
            {
                //循环处理
                foreach (var it in dataList)
                {
                    //根据客户编码查找申请数据
                    var data = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetSynchroDBApplyData(it.ContractNum);
                    if (data != null)
                    {
                        #region 修改申请数据状态
                        var applyData = data.MappingTo<Db_crm_contract_productserviceinfo_globalsearch_appl>();
                        applyData.State = EnumProcessStatus.Pass.ToInt();
                        applyData.ReviewerDate = now;
                        applyData.ReviewerId = UserId;
                        applyData.Feedback = "同步数据自动通过";
                        applyData.Remark4List = "同步数据自动通过";
                        DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateData(applyData);
                        #endregion
                        #region 创建或修改服务数据
                        Db_crm_contract_serviceinfo_db serviceData = new Db_crm_contract_serviceinfo_db();
                        if (data.ServiceOpenState == EnumContractServiceOpenState.ToBeOpened || data.ServiceOpenState == EnumContractServiceOpenState.Returned)
                        {
                            serviceData.ContractId = data.ContractId;
                            serviceData.ProductId = data.ProductId;
                            serviceData.ContractProductInfoId = data.ContractProductInfoId;
                            serviceData.ServiceCycleStart = data.ServiceCycleStart;
                            serviceData.ServiceCycleEnd = data.ServiceCycleEnd;
                            serviceData.ServiceMonth = data.ServiceMonth;
                            serviceData.State = EnumContractServiceState.VALID;
                            serviceData.IsChanged = false;
                            serviceData.IsHistory = false;//如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
                            serviceData.RegisteredId = UserId;
                            serviceData.RegisteredTime = now;
                            serviceData.RegisteredRemark = "同步数据自动通过";
                            serviceData.ProcessingType = data.ProcessingType;
                            serviceData.ReviewerId = UserId;
                            serviceData.ReviewerTime = now;
                            serviceData.ReviewerRemark = "同步数据自动通过";
                            serviceData.UpdateDate = now;
                            serviceData.UpdateUser = UserId;
                            serviceData.Remark = String.Empty;
                            serviceData.Id = DbOpe_crm_contract_serviceinfo_db.Instance.InsertDataReturnId(serviceData).ToString();
                        }
                        else if (data.ServiceOpenState == EnumContractServiceOpenState.ToBeReview)
                        {
                            serviceData = DbOpe_crm_contract_serviceinfo_db.Instance.GetDbServiceByContractNum(data.ContractId);
                            serviceData.State = EnumContractServiceState.VALID;
                            serviceData.ReviewerId = UserId;
                            serviceData.ReviewerTime = now;
                            serviceData.ReviewerRemark = "同步数据自动通过";
                            serviceData.Remark = String.Empty;
                            DbOpe_crm_contract_serviceinfo_db.Instance.UpdateData(serviceData);
                        }
                        #endregion
                        #region 处理驳回数据
                        if (data.ServiceOpenState == EnumContractServiceOpenState.Returned)
                        {
                            var returnData = DbOpe_crm_contract_serviceinfo_db.Instance.GetData(g => g.ProductServiceInfoDBApplId == applyData.Id && g.IsHistory == false);
                            returnData.IsHistory = true;
                            DbOpe_crm_contract_serviceinfo_db.Instance.UpdateData(returnData);
                        }
                        #endregion
                    }

                }
            });


        }
    }
}



