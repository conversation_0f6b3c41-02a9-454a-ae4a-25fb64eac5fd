﻿using Microsoft.AspNetCore.Http;
using System.IO;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Common.AppSetting;
using QCloud;

namespace CRM2_API.BLL.Common
{
    public class Util<T, F> where T : IAttachFile<F>, new() where F : class, new()
    {
        public Util(T value)
        {
            this.Value = value;
        }
        public T Value { get; set; }

        public void UploadFile(IFormFileCollection ContractFile, string type, string userId, string objectId)
        {
            if (ContractFile != null && ContractFile.Count > 0)
            {
                foreach (IFormFile file in ContractFile)
                {
                    string AttachFileId = Guid.NewGuid().ToString();

                    //设置文件上传路径
                    //string fileHead = $"/FileUpload/" + type + "/" + DateTime.Now.ToString("yyyyMMdd");
                    string fileHead = $"{AppSettings.DownLoadFilePath}/" + type + "/" + DateTime.Now.ToString("yyyyMMdd");
                    string fileName = file.FileName;
                    string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
                    string fullFileName = $"{fileHead}/" + AttachFileId;

                    if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                    {
                        using (Stream stream = file.OpenReadStream())
                        {
                            QCloudOperator qCloud = new QCloudOperator();
                            qCloud.UploadStream(fullFileName, stream, file.ContentType);
                            BM_AttachFile AttachFile = new BM_AttachFile()
                            {
                                id = AttachFileId,
                                objectId = objectId,
                                fileName = fileName,
                                fullFileName = fullFileName,
                                fileExtension = fileExtension,
                                FileSize = (int)file.Length,
                                userId = userId
                            };
                            F f = new F();
                            AttachFile.MappingTo(f);
                            Value.SaveAttachFile(f);
                        }
                    }
                    else
                    {
                        //创建文件夹，保存文件
                        string path = Path.GetDirectoryName(fullFileName);
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }

                        //将流写入文件
                        using (Stream stream = file.OpenReadStream())
                        {
                            // 把 Stream 转换成 byte[]
                            byte[] bytes = new byte[stream.Length];
                            stream.Read(bytes, 0, bytes.Length);
                            // 设置当前流的位置为流的开始
                            stream.Seek(0, SeekOrigin.Begin);
                            // 把 byte[] 写入文件
                            using (FileStream fs = new(fullFileName, FileMode.Create))
                            {
                                using (BinaryWriter bw = new(fs))
                                {
                                    bw.Write(bytes);
                                    BM_AttachFile AttachFile = new BM_AttachFile()
                                    {
                                        id = AttachFileId,
                                        objectId = objectId,
                                        fileName = fileName,
                                        fullFileName = fullFileName,
                                        fileExtension = fileExtension,
                                        FileSize = (int)file.Length,
                                        userId = userId
                                    };
                                    F f = new F();
                                    AttachFile.MappingTo(f);
                                    Value.SaveAttachFile(f);
                                }
                            }
                        }
                    }


                }
            }
        }

        public List<string> UploadFileReturnFileUrls(IFormFileCollection ContractFile, string type, string userId, string objectId)
        {
            var filePathList = new List<string>();
            if (ContractFile != null && ContractFile.Count > 0)
            {

                foreach (IFormFile file in ContractFile)
                {
                    string AttachFileId = Guid.NewGuid().ToString();
                    //设置文件上传路径
                    //string fileHead = $"/FileUpload/" + type + "/" + DateTime.Now.ToString("yyyyMMddHHmmfff");
                    string fileHead = $"{AppSettings.DownLoadFilePath}/" + type + "/" + DateTime.Now.ToString("yyyyMMddHHmmfff");
                    string fileName = file.FileName;
                    string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
                    string fullFileName = $"{fileHead}/" + fileName;
                    filePathList.Add($"{fullFileName}");


                    if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                    {
                        using (Stream stream = file.OpenReadStream())
                        {
                            QCloudOperator qCloud = new QCloudOperator();
                            qCloud.UploadStream(fullFileName, stream,file.ContentType);
                            BM_AttachFile AttachFile = new BM_AttachFile()
                            {
                                id = AttachFileId,
                                objectId = objectId,
                                fileName = fileName,
                                fullFileName = fullFileName,
                                fileExtension = fileExtension,
                                FileSize = (int)file.Length,
                                userId = userId
                            };
                            F f = new F();
                            AttachFile.MappingTo(f);
                            Value.SaveAttachFile(f);
                        }
                    }
                    else
                    {
                        //创建文件夹，保存文件
                        string path = Path.GetDirectoryName(fullFileName);
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }

                        //将流写入文件
                        using (Stream stream = file.OpenReadStream())
                        {
                            // 把 Stream 转换成 byte[]
                            byte[] bytes = new byte[stream.Length];
                            stream.Read(bytes, 0, bytes.Length);
                            // 设置当前流的位置为流的开始
                            stream.Seek(0, SeekOrigin.Begin);
                            // 把 byte[] 写入文件
                            using (FileStream fs = new(fullFileName, FileMode.Create))
                            {
                                using (BinaryWriter bw = new(fs))
                                {
                                    bw.Write(bytes);
                                    BM_AttachFile AttachFile = new BM_AttachFile()
                                    {
                                        id = AttachFileId,
                                        objectId = objectId,
                                        fileName = fileName,
                                        fullFileName = fullFileName,
                                        fileExtension = fileExtension,
                                        FileSize = (int)file.Length,
                                        userId = userId
                                    };
                                    F f = new F();
                                    AttachFile.MappingTo(f);
                                    Value.SaveAttachFile(f);
                                }
                            }
                        }
                    }



                  
                }
            }
            return filePathList;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="subjectId"></param>
        /// <param name="attachFiles"></param>
        /// <param name="attachEnumOption"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public bool UploadFiles(string subjectId, IFormFileCollection attachFiles, AttachEnumOption? attachEnumOption = AttachEnumOption.Base)
        {
            bool result = false;
            SnowflakeId s = new(2, 2);
            if (string.IsNullOrEmpty(subjectId))
            {
                throw new ArgumentNullException("主表主键不可为空!");
            }

            if (attachFiles is not null and { Count: > 0 })
            {
                List<(string fileName, string filePath, string fileExtension, int fileSize)> fileInfos =
                    new(attachFiles.Count);
                foreach (IFormFile file in attachFiles)
                {
                    //string fileSavePath = AppSettings.fileSavePath ?? "/FileUpload";
                    //string fileSavePath = "/FileUpload";
                    //string fileHead = $"{fileSavePath}/{attachEnumOption.ToString()}/{DateTime.Now:yyyyMMdd}";
                    string fileHead = $"{AppSettings.DownLoadFilePath}/{attachEnumOption.ToString()}/{DateTime.Now:yyyyMMdd}";
                    //设置文件上传路径
                    string fileName = file.FileName;
                    string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
                    string saveFileName = $"{fileName.Substring(0, fileName.LastIndexOf("."))}_{s.NextId()}.{fileExtension}";
                    string fullSaveFilePath = $"{fileHead}/{saveFileName}";

                    if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                    {
                        using (Stream stream = file.OpenReadStream())
                        {
                            byte[] bytes = new byte[stream.Length];
                            stream.Read(bytes, 0, bytes.Length);
                            stream.Seek(0, SeekOrigin.Begin);
                            QCloudOperator qCloud = new QCloudOperator();
                            qCloud.UploadStream(fullSaveFilePath, stream,file.ContentType);
                            //组装参数
                            (string fileName, string filePath, string fileExtension, int fileSize) fileInfo = (fileName,
                                fullSaveFilePath, fileExtension, (int)file.Length);
                            fileInfos.Add(fileInfo);
                        }
                    }
                    else
                    {

                        ////创建文件夹，保存文件
                        string? path = Path.GetDirectoryName(fullSaveFilePath);
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }

                        //保存文件  文件存在则先删除原来的文件
                        // if (File.Exists(fullFileName))
                        // {
                        //     File.Delete(fullFileName);
                        // }

                        //将流写入文件
                        using var stream = File.OpenWrite(fullSaveFilePath);
                        file.CopyTo(stream);
                        //组装参数
                        (string fileName, string filePath, string fileExtension, int fileSize) fileInfo = (fileName,
                            fullSaveFilePath, fileExtension, (int)file.Length);
                        fileInfos.Add(fileInfo);

                    }
                }
                BM_AttachFiles AttachFiles = new()
                {
                    subjectId = subjectId,
                    filesInfos = fileInfos,
                    attachEnumOption = attachEnumOption
                };
                F f = new();
                AttachFiles.MappingTo(f);
                result = Value.SaveAttachFile(f);
            }
            return result;
        }
    }
}
