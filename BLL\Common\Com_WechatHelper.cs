﻿using CRM2_API.Common.AppSetting;
using CRM2_API.Model.System;
using Newtonsoft.Json.Linq;

namespace CRM2_API.BLL.Common
{
    /// <summary>
    /// 微信帮助类
    /// </summary>
    public class Com_WechatHelper
    {
        /// <summary>
        /// 出错日志记录的文件名
        /// </summary>
        const string LogName = "Wx";


        /// <summary>
        /// 根据openid，获取微信用户的信息
        /// </summary>
        /// <param name="openId"></param>
        /// <returns></returns>
        public static WxUserInfo GetWxUserInfo(string openId)
        {
            string accessToken = GetAccessToken();
            try
            {
                return NetUtil.Get_ReturnModel<WxUserInfo>($"https://api.weixin.qq.com/cgi-bin/user/info?access_token={accessToken}&openid={openId}&lang=zh_CN");
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog(e.Message, LogName, e);
            }
            throw new ApiException("获取用户微信信息失败");
        }
        /// <summary>
        /// 获取微信临时授权码
        /// </summary>
        /// <returns></returns>
        private static string GetAccessToken()
        {
            try
            {
                string strURL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + AppSettings.WeChatLogin.AppId + "&secret=" + AppSettings.WeChatLogin.AppSecret;
                var model = NetUtil.Get_ReturnString(strURL).DeserializeNewtonJson<JObject>();
                if (model.ContainsKey("access_token"))
                    return model["access_token"].ToString();
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog(e.Message, LogName, e);
            }
            throw new ApiException("微信授权码获取异常");
        }
        /// <summary>
        /// 微信用户信息类
        /// </summary>
        public class WxUserInfo
        {
            public string openid { get; set; }
            public string nickname { get; set; }
            public int sex { get; set; }
            public string province { get; set; }
            public string city { get; set; }
            public string country { get; set; }
            public string headimgurl { get; set; }
            public string unionid { get; set; }
        }
    }
}
