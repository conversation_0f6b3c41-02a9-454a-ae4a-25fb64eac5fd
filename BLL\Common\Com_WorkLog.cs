﻿using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;

namespace CRM2_API.BLL.Common
{
    public class Com_WorkLog 
    {
        private static readonly AsyncLocal<Db_sys_operation_log> _workLog = new();

        public static Db_sys_operation_log Instance
        {
            get { return _workLog.Value ?? new(); }
        }

        public static void SetWorkLog(Db_sys_operation_log workLog)
        {
            _workLog.Value = workLog;
            DbOpe_sys_operation_log.Instance.Insert(workLog);
        }
    }
}
