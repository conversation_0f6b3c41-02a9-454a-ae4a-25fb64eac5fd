using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 报告内容表
    /// </summary>
    [SugarTable("crm_report_content")]
    public class Db_crm_report_content
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 报告ID
        /// </summary>
        public string ReportId { get; set; }

        /// <summary>
        /// 报告类型：1-日报，2-周报，3-月报
        /// </summary>
        public int ReportType { get; set; }

        /// <summary>
        /// 报告标题
        /// </summary>
        public string ReportTitle { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 团队ID
        /// </summary>
        public string TeamId { get; set; }

        /// <summary>
        /// 团队名称
        /// </summary>
        public string TeamName { get; set; }

        /// <summary>
        /// 报告日期
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// 报告年份
        /// </summary>
        public int ReportYear { get; set; }

        /// <summary>
        /// 报告月份
        /// </summary>
        public int? ReportMonth { get; set; }

        /// <summary>
        /// 报告周数
        /// </summary>
        public int? ReportWeek { get; set; }

        /// <summary>
        /// 模块键名
        /// </summary>
        public string ModuleKey { get; set; }

        /// <summary>
        /// 模块标题
        /// </summary>
        public string ModuleTitle { get; set; }

        /// <summary>
        /// 模块排序
        /// </summary>
        public int ModuleOrder { get; set; }

        /// <summary>
        /// 子项键名，如"电话量"、"拜访量"等
        /// </summary>
        public string SectionKey { get; set; }

        /// <summary>
        /// 子项标题
        /// </summary>
        public string SectionTitle { get; set; }

        /// <summary>
        /// 子项排序
        /// </summary>
        public int SectionOrder { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateDate { get; set; }
    }
} 