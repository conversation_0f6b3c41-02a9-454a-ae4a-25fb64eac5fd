﻿using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbModel.Crm2;
using System.Xml.Linq;

namespace CRM2_API.BLL.Common
{
    public static class Dictionary
    {
        private static List<Db_sys_dictionary> contractMethod;

        public static List<Db_sys_dictionary> ContractMethod
        {
            get
            {
                if (contractMethod == null)
                {
                    contractMethod = Set("ContractMethod");
                }
                return contractMethod;
            }
            private set { contractMethod = value; }
        }


        private static List<Db_sys_dictionary> contractType;

        public static List<Db_sys_dictionary> ContractType
        {
            get
            {
                if (contractType == null)
                {
                    contractType = Set("ContractType");
                }
                return contractType;
            }
            private set { contractType = value; }
        }


        private static List<Db_sys_dictionary> paymentType;

        public static List<Db_sys_dictionary> PaymentType
        {
            get
            {
                if (paymentType == null)
                {
                    paymentType = Set("PaymentType");
                }
                return paymentType;
            }
            private set { paymentType = value; }
        }


        private static List<Db_sys_dictionary> isReceipt;

        public static List<Db_sys_dictionary> IsReceipt
        {
            get
            {
                if (isReceipt == null)
                {
                    isReceipt = Set("IsReceipt");
                }
                return isReceipt;
            }
            private set { isReceipt = value; }
        }


        private static List<Db_sys_dictionary> paymentMethod;

        public static List<Db_sys_dictionary> PaymentMethod
        {
            get
            {
                if (paymentMethod == null)
                {
                    paymentMethod = Set("PaymentMethod");
                }
                return paymentMethod;
            }
            private set { paymentMethod = value; }
        }


        private static List<Db_sys_dictionary> recoveryStatus;

        public static List<Db_sys_dictionary> RecoveryStatus
        {
            get
            {
                if (recoveryStatus == null)
                {
                    recoveryStatus = Set("RecoveryStatus");
                }
                return recoveryStatus;
            }
            private set { recoveryStatus = value; }
        }


        private static List<Db_sys_dictionary> currency;

        public static List<Db_sys_dictionary> Currency
        {
            get
            {
                if (currency == null)
                {
                    currency = Set("Currency");
                }
                return currency;
            }
            private set { currency = value; }
        }


        private static List<Db_sys_dictionary> isState;

        public static List<Db_sys_dictionary> IsState
        {
            get
            {
                if (isState == null)
                {
                    isState = Set("IsState");
                }
                return isState;
            }
            private set { isState = value; }
        }


        private static List<Db_sys_dictionary> contractStatus;

        public static List<Db_sys_dictionary> ContractStatus
        {
            get
            {
                if (contractStatus == null)
                {
                    contractStatus = Set("ContractStatus");
                }
                return contractStatus;
            }
            private set { contractStatus = value; }
        }


        private static List<Db_sys_dictionary> serviceStatus;

        public static List<Db_sys_dictionary> ServiceStatus
        {
            get
            {
                if (serviceStatus == null)
                {
                    serviceStatus = Set("ServiceStatus");
                }
                return serviceStatus;
            }
            private set { serviceStatus = value; }
        }


        private static List<Db_sys_dictionary> contactInformation;

        public static List<Db_sys_dictionary> ContactInformation
        {
            get
            {
                if (contactInformation == null)
                {
                    contactInformation = Set("ContactInformation");
                }
                return contactInformation;
            }
            private set { contactInformation = value; }
        }

        private static List<Db_sys_dictionary> isInvoice;

        public static List<Db_sys_dictionary> IsInvoice
        {
            get
            {
                if (isInvoice == null)
                {
                    isInvoice = Set("IsInvoice");
                }
                return isInvoice;
            }
            private set { isInvoice = value; }
        }


        private static List<Db_sys_dictionary> applicableYearsName;

        public static List<Db_sys_dictionary> ApplicableYearsName
        {
            get
            {
                if (applicableYearsName == null)
                {
                    applicableYearsName = Set("ApplicableYearsName");
                }
                return applicableYearsName;
            }
            private set { applicableYearsName = value; }
        }

        private static List<Db_sys_dictionary> contractVersionName;

        public static List<Db_sys_dictionary> ContractVersionName
        {
            get
            {
                if (contractVersionName == null)
                {
                    contractVersionName = Set("ContractVersionName");
                }
                return contractVersionName;
            }
            private set { contractVersionName = value; }
        }

        private static List<Db_sys_dictionary> contractMethodName;
        public static List<Db_sys_dictionary> ContractMethodName
        {
            get
            {
                if (contractMethodName == null)
                {
                    contractMethodName = Set("ContractMethodName");
                }
                return contractMethodName;
            }
            private set { contractMethodName = value; }
        }

        private static List<Db_sys_dictionary> applicableProductsName;

        public static List<Db_sys_dictionary> ApplicableProductsName
        {
            get
            {
                if (applicableProductsName == null)
                {
                    applicableProductsName = Set("ApplicableProductsName");
                }
                return applicableProductsName;
            }
            private set { applicableProductsName = value; }
        }

        private static List<Db_sys_dictionary> lifeCycleName;

        public static List<Db_sys_dictionary> LifeCycleName
        {
            get
            {
                if (lifeCycleName == null)
                {
                    lifeCycleName = Set("LifeCycleName");
                }
                return lifeCycleName;
            }
            private set { lifeCycleName = value; }
        }

        private static List<Db_sys_dictionary> chargingParameters;

        public static List<Db_sys_dictionary> ChargingParameters
        {
            get
            {
                if (chargingParameters == null)
                {
                    chargingParameters = Set("ChargingParameters");
                }
                return chargingParameters;
            }
            private set { chargingParameters = value; }
        }

        private static List<Db_sys_dictionary> retailCountry;

        public static List<Db_sys_dictionary> RetailCountry
        {
            get
            {
                if (retailCountry == null)
                {
                    retailCountry = Set("RetailCountry");
                }
                return retailCountry;
            }
            private set { retailCountry = value; }
        }

        private static List<Db_sys_dictionary> nationalType;

        public static List<Db_sys_dictionary> NationalType
        {
            get
            {
                if (nationalType == null)
                {
                    nationalType = Set("NationalType");
                }
                return nationalType;
            }
            private set { nationalType = value; }
        }

        private static List<Db_sys_dictionary> identifyStatus;

        public static List<Db_sys_dictionary> IdentifyStatus
        {
            get
            {
                if (identifyStatus == null)
                {
                    identifyStatus = Set("IdentifyStatus");
                }
                return identifyStatus;
            }
            private set { identifyStatus = value; }
        }

        private static List<Db_sys_dictionary> matchingStatus;

        public static List<Db_sys_dictionary> MatchingStatus
        {
            get
            {
                if (matchingStatus == null)
                {
                    matchingStatus = Set("MatchingStatus");
                }
                return matchingStatus;
            }
            private set { matchingStatus = value; }
        }

        private static List<Db_sys_dictionary> processStatus;

        public static List<Db_sys_dictionary> ProcessStatus
        {
            get
            {
                if (processStatus == null)
                {
                    processStatus = Set("ProcessStatus");
                }
                return processStatus;
            }
            private set { processStatus = value; }
        }

        private static List<Db_sys_dictionary> processingType;

        public static List<Db_sys_dictionary> ProcessingType
        {
            get
            {
                if (processingType == null)
                {
                    processingType = Set("ProcessingType");
                }
                return processingType;
            }
            private set { processingType = value; }
        }

        private static List<Db_sys_dictionary> invoicingStatus;

        public static List<Db_sys_dictionary> InvoicingStatus
        {
            get
            {
                if (invoicingStatus == null)
                {
                    invoicingStatus = Set("InvoicingStatus");
                }
                return invoicingStatus;
            }
            private set { invoicingStatus = value; }
        }

        private static List<Db_sys_dictionary> isSignReceiving;

        public static List<Db_sys_dictionary> IsSignReceiving
        {
            get
            {
                if (isSignReceiving == null)
                {
                    isSignReceiving = Set("IsSignReceiving");
                }
                return isSignReceiving;
            }
            private set { isSignReceiving = value; }
        }

        private static List<Db_sys_dictionary> auditStatus;

        public static List<Db_sys_dictionary> AuditStatus
        {
            get
            {
                if (auditStatus == null)
                {
                    auditStatus = Set("AuditStatus");
                }
                return auditStatus;
            }
            private set { auditStatus = value; }
        }

        private static List<Db_sys_dictionary> isReminder;

        public static List<Db_sys_dictionary> IsReminder
        {
            get
            {
                if (isReminder == null)
                {
                    isReminder = Set("IsReminder");
                }
                return isReminder;
            }
            private set { isReminder = value; }
        }

        private static List<Db_sys_dictionary> billingType;

        public static List<Db_sys_dictionary> BillingType
        {
            get
            {
                if (billingType == null)
                {
                    billingType = Set("BillingType");
                }
                return billingType;
            }
            private set { billingType = value; }
        }

        private static List<Db_sys_dictionary> invoiceType;

        public static List<Db_sys_dictionary> InvoiceType
        {
            get
            {
                if (invoiceType == null)
                {
                    invoiceType = Set("InvoiceType");
                }
                return invoiceType;
            }
            private set { invoiceType = value; }
        }

        private static List<Db_sys_dictionary> invoicingForm;

        public static List<Db_sys_dictionary> InvoicingForm
        {
            get
            {
                if (invoicingForm == null)
                {
                    invoicingForm = Set("InvoicingForm");
                }
                return invoicingForm;
            }
            private set { invoicingForm = value; }
        }


        private static List<Db_sys_dictionary> isSecret;

        public static List<Db_sys_dictionary> IsSecret
        {
            get
            {
                if (isSecret == null)
                {
                    isSecret = Set("IsSecret");
                }
                return isSecret;
            }
            private set { isSecret = value; }
        }

        private static List<Db_sys_dictionary> registerState;

        public static List<Db_sys_dictionary> RegisterState
        {
            get
            {
                if (registerState == null)
                {
                    registerState = Set("RegisterState");
                }
                return registerState;
            }
            private set { registerState = value; }
        }

        private static List<Db_sys_dictionary> temporaryAccountState;

        public static List<Db_sys_dictionary> TemporaryAccountState
        {
            get
            {
                if (temporaryAccountState == null)
                {
                    temporaryAccountState = Set("TemporaryAccountState");
                }
                return temporaryAccountState;
            }
            private set { temporaryAccountState = value; }
        }

        private static List<Db_sys_dictionary> temporaryAccountUserState;

        public static List<Db_sys_dictionary> TemporaryAccountUserState
        {
            get
            {
                if (temporaryAccountUserState == null)
                {
                    temporaryAccountUserState = Set("TemporaryAccountUserState");
                }
                return temporaryAccountUserState;
            }
            private set { temporaryAccountUserState = value; }
        }

        private static List<Db_sys_dictionary> temporaryAccountType;

        public static List<Db_sys_dictionary> TemporaryAccountType
        {
            get
            {
                if (temporaryAccountType == null)
                {
                    temporaryAccountType = Set("TemporaryAccountType");
                }
                return temporaryAccountType;
            }
            private set { temporaryAccountType = value; }
        }

        private static List<Db_sys_dictionary> accountsOpenedNum;

        public static List<Db_sys_dictionary> AccountsOpenedNum
        {
            get
            {
                if (accountsOpenedNum == null)
                {
                    accountsOpenedNum = Set("AccountsOpenedNum");
                }
                return accountsOpenedNum;
            }
            private set { accountsOpenedNum = value; }
        }

        private static List<Db_sys_dictionary> duration;

        public static List<Db_sys_dictionary> Duration
        {
            get
            {
                if (duration == null)
                {
                    duration = Set("Duration");
                }
                return duration;
            }
            private set { duration = value; }
        }

        private static List<Db_sys_dictionary> customerType;

        public static List<Db_sys_dictionary> CustomerType
        {
            get
            {
                if (customerType == null)
                {
                    customerType = Set("CustomerType");
                }
                return customerType;
            }
            private set { customerType = value; }
        }

        private static List<Db_sys_dictionary> expenseType;

        public static List<Db_sys_dictionary> ExpenseType
        {
            get
            {
                if (expenseType == null)
                {
                    expenseType = Set("ExpenseType");
                }
                return expenseType;
            }
            private set { expenseType = value; }
        }

        private static List<Db_sys_dictionary> workflowPendingState;

        public static List<Db_sys_dictionary> WorkflowPendingState
        {
            get
            {
                if (workflowPendingState == null)
                {
                    workflowPendingState = Set("WorkflowPendingState");
                }
                return workflowPendingState;
            }
            private set { workflowPendingState = value; }
        }

        private static List<Db_sys_dictionary> contractChangeAuditState;

        public static List<Db_sys_dictionary> ContractChangeAuditState
        {
            get
            {
                if (contractChangeAuditState == null)
                {
                    contractChangeAuditState = Set("ContractChangeAuditState");
                }
                return contractChangeAuditState;
            }
            private set { contractChangeAuditState = value; }
        }

        private static List<Db_sys_dictionary> contractServiceOpenState;

        public static List<Db_sys_dictionary> ContractServiceOpenState
        {
            get
            {
                if (contractServiceOpenState == null)
                {
                    contractServiceOpenState = Set("ContractServiceOpenState");
                }
                return contractServiceOpenState;
            }
            private set { contractServiceOpenState = value; }
        }

        private static List<Db_sys_dictionary> customerAddSubCompanyAuditState;

        public static List<Db_sys_dictionary> CustomerAddSubCompanyAuditState
        {
            get
            {
                if (customerAddSubCompanyAuditState == null)
                {
                    customerAddSubCompanyAuditState = Set("CustomerAddSubCompanyAuditState");
                }
                return customerAddSubCompanyAuditState;
            }
            private set { customerAddSubCompanyAuditState = value; }
        }

        private static List<Db_sys_dictionary> customerMergeAuditState;

        public static List<Db_sys_dictionary> CustomerMergeAuditState
        {
            get
            {
                if (customerMergeAuditState == null)
                {
                    customerMergeAuditState = Set("CustomerMergeAuditState");
                }
                return customerMergeAuditState;
            }
            private set { customerMergeAuditState = value; }
        }

        private static List<Db_sys_dictionary> contractPaperAuditStatus;

        public static List<Db_sys_dictionary> ContractPaperAuditStatus
        {
            get
            {
                if (contractPaperAuditStatus == null)
                {
                    contractPaperAuditStatus = Set("ContractPaperAuditStatus");
                }
                return contractPaperAuditStatus;
            }
            private set { contractPaperAuditStatus = value; }
        }

        private static List<Db_sys_dictionary> contractTemplateStatus;

        public static List<Db_sys_dictionary> ContractTemplateStatus
        {
            get
            {
                if (contractTemplateStatus == null)
                {
                    contractTemplateStatus = Set("ContractTemplateStatus");
                }
                return contractTemplateStatus;
            }
            private set { contractTemplateStatus = value; }
        }

        private static List<Db_sys_dictionary> usageStatus;

        public static List<Db_sys_dictionary> UsageStatus
        {
            get
            {
                if (usageStatus == null)
                {
                    usageStatus = Set("UsageStatus");
                }
                return usageStatus;
            }
            private set { usageStatus = value; }
        }

        private static List<Db_sys_dictionary> iAEType;

        public static List<Db_sys_dictionary> IAEType
        {
            get
            {
                if (iAEType == null)
                {
                    iAEType = Set("IAEType");
                }
                return iAEType;
            }
            private set { iAEType = value; }
        }

        private static List<Db_sys_dictionary> hsCodeType;

        public static List<Db_sys_dictionary> HsCodeType
        {
            get
            {
                if (hsCodeType == null)
                {
                    hsCodeType = Set("HsCodeType");
                }
                return hsCodeType;
            }
            private set { hsCodeType = value; }
        }

        private static List<Db_sys_dictionary> contractAuditType;

        public static List<Db_sys_dictionary> ContractAuditType
        {
            get
            {
                if (contractAuditType == null)
                {
                    contractAuditType = Set("ContractAuditType");
                }
                return contractAuditType;
            }
            private set { contractAuditType = value; }
        }

        private static List<Db_sys_dictionary> contractAuditCategory;

        public static List<Db_sys_dictionary> ContractAuditCategory
        {
            get
            {
                if (contractAuditCategory == null)
                {
                    contractAuditCategory = Set("ContractAuditCategory");
                }
                return contractAuditCategory;
            }
            private set { contractAuditCategory = value; }
        }

        private static List<Db_sys_dictionary> messageType;

        public static List<Db_sys_dictionary> MessageType
        {
            get
            {
                if (messageType == null)
                {
                    messageType = Set("MessageType");
                }
                return messageType;
            }
            private set { messageType = value; }
        }

        private static List<Db_sys_dictionary> sysMessagesType;

        public static List<Db_sys_dictionary> SysMessagesType
        {
            get
            {
                if (sysMessagesType == null)
                {
                    sysMessagesType = Set("SysMessagesType");
                }
                return sysMessagesType;
            }
            private set { sysMessagesType = value; }
        }

        private static List<Db_sys_dictionary> sysMessagesState;

        public static List<Db_sys_dictionary> SysMessagesState
        {
            get
            {
                if (sysMessagesState == null)
                {
                    sysMessagesState = Set("SysMessagesState");
                }
                return sysMessagesState;
            }
            private set { sysMessagesState = value; }
        }


        private static List<Db_sys_dictionary> registerAchievementState;

        public static List<Db_sys_dictionary> RegisterAchievementState
        {
            get
            {
                if (registerAchievementState == null)
                {
                    registerAchievementState = Set("RegisterAchievementState");
                }
                return registerAchievementState;
            }
            private set { registerAchievementState = value; }
        }

        private static List<Db_sys_dictionary> maintenanceStatus;

        public static List<Db_sys_dictionary> MaintenanceStatus
        {
            get
            {
                if (maintenanceStatus == null)
                {
                    maintenanceStatus = Set("MaintenanceStatus");
                }
                return maintenanceStatus;
            }
            private set { maintenanceStatus = value; }
        }

        private static List<Db_sys_dictionary> exchangeApplState;

        public static List<Db_sys_dictionary> ExchangeApplState
        {
            get
            {
                if (exchangeApplState == null)
                {
                    exchangeApplState = Set("ExchangeApplState");
                }
                return exchangeApplState;
            }
            private set { exchangeApplState = value; }
        }

        private static List<Db_sys_dictionary> stampReviewStatus;

        public static List<Db_sys_dictionary> StampReviewStatus
        {
            get
            {
                if (stampReviewStatus == null)
                {
                    stampReviewStatus = Set("StampReviewStatus");
                }
                return stampReviewStatus;
            }
            private set { stampReviewStatus = value; }
        }

        private static List<Db_sys_dictionary> verifyConfirm;

        public static List<Db_sys_dictionary> VerifyConfirm
        {
            get
            {
                if (verifyConfirm == null)
                {
                    verifyConfirm = Set("VerifyConfirm");
                }
                return verifyConfirm;
            }
            private set { verifyConfirm = value; }
        }

        private static List<Db_sys_dictionary> congratulations;

        public static List<Db_sys_dictionary> Congratulations
        {
            get
            {
                if (congratulations == null)
                {
                    congratulations = Set("Congratulations");
                }
                return congratulations;
            }
            private set { congratulations = value; }
        }

        private static List<Db_sys_dictionary> isSettlementExchange;

        public static List<Db_sys_dictionary> IsSettlementExchange
        {
            get
            {
                if (isSettlementExchange == null)
                {
                    isSettlementExchange = Set("IsSettlementExchange");
                }
                return isSettlementExchange;
            }
            private set { isSettlementExchange = value; }
        }

        private static List<Db_sys_dictionary> contractStatusMarketing;

        public static List<Db_sys_dictionary> ContractStatusMarketing
        {
            get
            {
                if (contractStatusMarketing == null)
                {
                    contractStatusMarketing = Set("ContractStatusMarketing");
                }
                return contractStatusMarketing;
            }
            private set { contractStatusMarketing = value; }
        }

        private static List<Db_sys_dictionary> Set(string name)
        {
            List<Db_sys_dictionary> list = new List<Db_sys_dictionary>();
            var dictionary = LocalCache.LC_SysDictionary.SysDictionaryCache.FirstOrDefault(e => e.Value.Equals(name))?.Children;
            if (dictionary != null)
            {
                list.AddRange(dictionary);
            }
            return list;
        }




        public static void Clear()
        {
            contractMethod = null;
            contractType = null;
            paymentType = null;
            isReceipt = null;
            paymentMethod = null;
            recoveryStatus = null;
            currency = null;
            isState = null;
            contractStatus = null;
            serviceStatus = null;
            contactInformation = null;
            isInvoice = null;
            applicableYearsName = null;
            contractVersionName = null;
            applicableProductsName = null;
            lifeCycleName = null;
            chargingParameters = null;
            retailCountry = null;
            nationalType = null;
            identifyStatus = null;
            matchingStatus = null;
            processStatus = null;
            processingType = null;
            invoicingStatus = null;
            isSignReceiving = null;
            auditStatus = null;
            isReminder = null;
            billingType = null;
            invoiceType = null;
            invoicingForm = null;
            isSecret = null;
            registerState = null;
            temporaryAccountState = null;
            temporaryAccountUserState = null;
            temporaryAccountType = null;
            accountsOpenedNum = null;
            duration = null;
            customerType = null;
            expenseType = null;
            workflowPendingState = null;
            contractChangeAuditState = null;
            contractServiceOpenState = null;
            customerAddSubCompanyAuditState = null;
            customerMergeAuditState = null;
            contractPaperAuditStatus = null;
            contractTemplateStatus = null;
            usageStatus = null;
            iAEType = null;
            hsCodeType = null;
            contractAuditType = null;
            contractAuditCategory = null;
            registerAchievementState = null;
            maintenanceStatus = null;
            exchangeApplState = null;
            stampReviewStatus = null;
            verifyConfirm = null;
            congratulations = null;
            isSettlementExchange = null;
            contractStatusMarketing = null;
        }
        
        private static List<Db_sys_dictionary> recordState;

        public static List<Db_sys_dictionary> RecordState
        {
            get
            {
                if (recordState == null)
                {
                    recordState = Set("RecordState");
                }
                return recordState;
            }
            private set { recordState = value; }
        }
        
        private static List<Db_sys_dictionary> accountType;

        public static List<Db_sys_dictionary> AccountType
        {
            get
            {
                if (accountType == null)
                {
                    accountType = Set("AccountType");
                }
                return accountType;
            }
            private set { accountType = value; }
        }

        #region 跟踪方式 
        
        private static List<Db_sys_dictionary> trackingType;
        public static List<Db_sys_dictionary> TrackingType
        {
            get
            {
                if (trackingType == null)
                {
                    trackingType = Set("TrackingType");
                }
                return trackingType;
            }
            private set { trackingType = value; }
        }
        #endregion

        #region 跟踪目的
        private static List<Db_sys_dictionary> trackingPurpose;

        public static List<Db_sys_dictionary> TrackingPurpose
        {
            get
            {
                if (trackingPurpose == null)
                {
                    trackingPurpose = Set("TrackingPurpose");
                }
                return trackingPurpose;
            }
            private set { trackingPurpose = value; }
        }
        #endregion

        #region 跟踪阶段
        private static List<Db_sys_dictionary> trackingStage;

        public static List<Db_sys_dictionary> TrackingStage
        {
            get
            {
                if (trackingStage == null)
                {
                    trackingStage = Set("TrackingStage");
                }
                return trackingStage;
            }
            private set { trackingStage = value; }
        }
        #endregion

        #region 跟踪事件
        private static List<Db_sys_dictionary> trackingEvents;
        public static List<Db_sys_dictionary> TrackingEvents
        {
            get
            {
                if (trackingEvents == null)
                {
                    trackingEvents = Set("TrackingEvents");
                }
                return trackingEvents;
            }
            private set { trackingEvents = value; }
        }
        #endregion

        #region 日程计划状态
        private static List<Db_sys_dictionary> schedulePlanStatus;
        public static List<Db_sys_dictionary> SchedulePlanStatus
        {
            get
            {
                if (schedulePlanStatus == null)
                {
                    schedulePlanStatus = Set("SchedulePlanStatus");
                }
                return schedulePlanStatus;
            }
            private set { schedulePlanStatus = value; }
        }
        #endregion

        #region 知识库文章类型
        private static List<Db_sys_dictionary> articleType;
        public static List<Db_sys_dictionary> ArticleType
        {
            get
            {
                if (articleType == null)
                {
                    articleType = Set("ArticleType");
                }
                return articleType;
            }
            private set { articleType = value; }
        }
        #endregion

        #region 知识库文章状态
        private static List<Db_sys_dictionary> articleSubmitState;
        public static List<Db_sys_dictionary> ArticleSubmitState
        {
            get
            {
                if (articleSubmitState == null)
                {
                    articleSubmitState = Set("ArticleSubmitState");
                }
                return articleSubmitState;
            }
            private set { articleSubmitState = value; }
        }
        #endregion
        
        #region 通知类型
        private static List<Db_sys_dictionary> noticeType;
        public static List<Db_sys_dictionary> NoticeType
        {
            get
            {
                if (noticeType == null)
                {
                    noticeType = Set("NoticeType");
                }
                return noticeType;
            }
            private set { noticeType = value; }
        }
        #endregion

        #region 通知发布范围
        private static List<Db_sys_dictionary> noticeSendRange;
        public static List<Db_sys_dictionary> NoticeSendRange
        {
            get
            {
                if (noticeSendRange == null)
                {
                    noticeSendRange = Set("NoticeSendRange");
                }
                return noticeSendRange;
            }
            private set { noticeSendRange = value; }
        }
        #endregion

        #region 通知发布状态
        private static List<Db_sys_dictionary> noticeState;
        public static List<Db_sys_dictionary> NoticeState
        {
            get
            {
                if (noticeState == null)
                {
                    noticeState = Set("NoticeState");
                }
                return noticeState;
            }
            private set { noticeState = value; }
        }
        #endregion

        #region 字典的一些方法封装
        
        /// <summary>
        /// 通过字典value从字典列表中查找对应的字典name值
        /// </summary>
        /// <param name="dictList">字典列表</param>
        /// <param name="value">字典value值</param>
        /// <returns></returns>
        public static string GetDictNameByValue(List<Db_sys_dictionary> dictList, string value)
        {
            if (dictList == null || dictList.Count == 0 || string.IsNullOrEmpty(value))
            {
                return string.Empty;
            }
            return dictList?.FirstOrDefault(e => e.Value.Equals(value))?.Name;
        }
        
        /// <summary>
        /// 通过字典名称获取与之对应的字典列表
        /// </summary>
        /// <param name="name">字典名称</param>
        /// <param name="type">字典所对应的枚举值,若无法通过字典名称定位到具体的列表，则通过枚举类型生成对应的字典列表</param>
        /// <returns></returns>
        public static List<Db_sys_dictionary> GetDictListByName(string name, Type? type = default)
        {
            List<Db_sys_dictionary> result = default;
            if (!string.IsNullOrEmpty(name))
            {
                result = Set(name);
            }

            if ((result == null || result.Count == 0) && type != null && type.IsEnum)
            {
                result ??= new();
                var enumDict = EnumUtil.GetEnumDescriptionDic(type);
                if (enumDict is not null and { Count: > 0 })
                {
                    Db_sys_dictionary temp;
                    foreach ((string? key, string? value) in enumDict)
                    {
                        temp = new()
                        {
                            Name = value,
                            Value = key,
                        };
                        result.Add(temp);
                    }
                }
            }
            return result ?? new();
        }
        
        #endregion
        
    }
}
