﻿using System;
using System.Linq;
using System.Text;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_operation_org_log")]
    public class Db_sys_operation_org_log
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:被操作的组织主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string orgId { get; set; }

        /// <summary>
        /// Desc:操作人
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string operateName { get; set; }

        /// <summary>
        /// Desc:操作类型
        /// Default:
        /// Nullable:False
        /// </summary>           
        public EnumOrgLogType operateType { get; set; }

        /// <summary>
        /// Desc:系统日志主表主键关联
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string sysLogId { get; set; }

        /// <summary>
        /// Desc:操作时间
        /// Default:
        /// Nullable:False
        /// </summary>           
        public DateTime opterateTime { get; set; }

        /// <summary>
        /// Desc:操作内容
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string content { get; set; }

    }
}
