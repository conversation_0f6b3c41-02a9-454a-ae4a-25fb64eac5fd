using CRM2_API.BLL.Common;
using CRM2_API.Common.Utils;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using CRM2_API.Common.AppSetting;
using QCloud;
using CRM2_API.Common.Cache;
using CRM2_API.Common;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 退票OCR识别相关
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        #region 退票附件OCR识别

        /// <summary>
        /// 识别退票附件并与退票申请信息进行比对
        /// </summary>
        /// <param name="fileBytes">文件字节数组</param>
        /// <param name="refundId">退票申请ID</param>
        /// <returns>OCR识别比对结果</returns>
        public async Task<InvoiceOcrCompareResult> RecognizeAndCompareRefundAttachmentAsync(byte[] fileBytes, string refundId)
        {
            try
            {
                // 验证退票申请ID并获取申请信息
                var refundApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => r.Id == refundId && r.Deleted != true);
                if (refundApplication == null)
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "未找到退票申请信息"
                    };
                }

                // 获取发票信息
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == refundApplication.InvoiceId && i.Deleted != true);
                if (invoice == null)
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "未找到关联的发票信息"
                    };
                }

                // 获取应用信息转换为视图模型
                var applicationInfo = new InvoiceApplicationInfo
                {
                    Id = refundApplication.Id,
                    ContractId = refundApplication.ContractId,
                    AppliedAmount = refundApplication.RefundAmount,
                    // 获取发票信息中的BillingCompany和BillingCompanyName
                    BillingCompany = invoice.BillingCompany,
                    BillingCompanyName = DbOpe_crm_collectingcompany.Instance.GetData(c => 
                        c.Id == invoice.BillingCompany && c.Deleted == false)?.SellerName ?? string.Empty,
                    // 根据原始示例模式补全属性
                    ApplicantId = refundApplication.ApplicantId,
                    ApplicantName = RedisCache.UserWithOrg.GetUserById(refundApplication.ApplicantId)?.UserWithOrgFullName ?? string.Empty,
                    ApplyTime = refundApplication.CreateDate,
                    AuditStatus = EnumInvoiceApplicationStatus.Applied,
                    DisplayStatus = EnumInvoiceDisplayStatus.InvoiceApplied,
                    BillingType = (EnumBillingType)(invoice.BillingType),
                    InvoiceType = (EnumInvoiceType)(invoice.InvoiceType),
                    BillingHeader = invoice.BillingHeader,
                    // 根据实际情况获取开票抬头名称
                    BillingHeaderName = invoice.BillingHeader,
                    CreditCode = invoice.CreditCode,
                    Recipient = invoice.Recipient,
                    Email = invoice.Email,
                    Remark = refundApplication.RefundReason,
                    ExpectedInvoicingDate = null,
                    InvoiceDetails = invoice.InvoicingDetails
                };

                // 根据文件类型调用不同的OCR识别方法
                UmiOcrService.OcrInvoiceInfo invoiceInfo = null;

                //这里换一种实现方式，用pdf读取二维码信息
                var analyzePdfQRCodeResult = await InvoiceReadUtil.AnalyzePdfQRCode(fileBytes);

                //尝试从pdf中直接读取其他信息
                invoiceInfo = InvoiceReadUtil.ExtractInvoiceInfoByFixedCoordinates(fileBytes);
                if (invoiceInfo == null)
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "OCR识别失败"
                    };
                }


                // 转换为OcrInvoiceResult
                var ocrResult = new OcrInvoiceResult
                {
                    InvoiceType = invoiceInfo.InvoiceType,
                    InvoiceCode = invoiceInfo.InvoiceCode,
                    InvoiceNumber = analyzePdfQRCodeResult.InvoiceNo == "?" ? invoiceInfo.InvoiceNumber : analyzePdfQRCodeResult.InvoiceNo,
                    InvoiceDate = analyzePdfQRCodeResult.InvoiceDate == "?" ? (invoiceInfo.InvoiceDate == null ? "" : invoiceInfo.InvoiceDate.Value.ToString("yyyy-MM-dd")) : analyzePdfQRCodeResult.InvoiceDate,
                    BuyerName = invoiceInfo.BuyerName,
                    BuyerTaxNumber = invoiceInfo.BuyerTaxNumber,
                    SellerName = invoiceInfo.SellerName,
                    SellerTaxNumber = invoiceInfo.SellerTaxNumber,
                    TotalAmount = invoiceInfo.TotalAmount,
                    TotalTax = invoiceInfo.TotalTax,
                    TotalAmountWithTax = analyzePdfQRCodeResult.InvoiceAmount == "?" ? invoiceInfo.TotalAmountWithTax.ToString("F2") : analyzePdfQRCodeResult.InvoiceAmount,
                    OriginalText = invoiceInfo.OriginalText,
                    Remark = invoiceInfo.Remark,
                    InvoiceDetails = analyzePdfQRCodeResult.InvoiceDetails == "?" ? "" : analyzePdfQRCodeResult.InvoiceDetails
                };

                ocrResult.CollectingCompanyId = RedisCache.CollectingCompany.GetCollectingCompanyBySellerName(ocrResult.SellerName)?.Id ?? string.Empty;
                // 保存OCR识别结果
                var ocrId = await SaveRefundOcrResultAsync(refundId, ocrResult);
                
                // 保存退票附件
                await SaveRefundAttachmentAsync(fileBytes, refundId, ocrResult.InvoiceNumber);

                // 创建比对结果对象
                var compareResult = new InvoiceOcrCompareResult
                {
                    OcrInfo = ocrResult,
                    ApplicationInfo = applicationInfo,
                    ComparisonResults = new List<FieldComparisonResult>(),
                    IsMatch = false // 默认为false，后面根据比对结果设置
                };

                // 进行字段比对
                CompareRefundFields(compareResult, invoice);

                // 计算是否匹配
                bool isMatch = true;
                foreach (var fieldResult in compareResult.ComparisonResults)
                {
                    // 如果任何一个必要字段不匹配，则整体不匹配
                    if (fieldResult.IsRequired && !fieldResult.IsMatch)
                    {
                        isMatch = false;
                        break;
                    }
                }

                compareResult.IsMatch = isMatch;
                
                // 保存比对结果
                SaveRefundComparisonResult(ocrId, compareResult); 

                return compareResult;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理退票附件OCR识别比对时发生错误: {ex.Message}");
                
                return new InvoiceOcrCompareResult
                {
                    Remark = $"处理失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 进行退票附件字段比对
        /// </summary>
        /// <param name="compareResult">比对结果对象</param>
        /// <param name="invoice">原始发票信息</param>
        private void CompareRefundFields(InvoiceOcrCompareResult compareResult, Db_crm_invoice invoice)
        {
            var ocrInfo = compareResult.OcrInfo;
            var comparisonResults = compareResult.ComparisonResults;
            var applicationInfo = compareResult.ApplicationInfo;
            
            // 获取系统中的发票类型描述
            string expectedInvoiceTypeDesc = EnumHelper.GetEnumDescription<EnumInvoiceType>(applicationInfo.InvoiceType);
            
            // 将OCR识别的发票类型转换为系统类型描述
            string actualInvoiceTypeDesc = ConvertOcrInvoiceTypeToSystemType(ocrInfo.InvoiceType);
            
            // 发票类型比对
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "InvoiceType",
                FieldDescription = "发票类型",
                ExpectedValue = expectedInvoiceTypeDesc,
                ActualValue = actualInvoiceTypeDesc,
                IsMatch = string.Equals(expectedInvoiceTypeDesc, actualInvoiceTypeDesc, StringComparison.OrdinalIgnoreCase),
                IsRequired = true
            });
            
            // // 发票代码比对
            // comparisonResults.Add(new FieldComparisonResult
            // {
            //     FieldName = "InvoiceCode",
            //     FieldDescription = "发票代码",
            //     ExpectedValue = invoice.InvoiceCode,
            //     ActualValue = ocrInfo.InvoiceCode,
            //     IsMatch = string.Equals(invoice.InvoiceCode, ocrInfo.InvoiceCode, StringComparison.OrdinalIgnoreCase),
            //     IsRequired = true
            // });

            // // 发票号码比对
            // comparisonResults.Add(new FieldComparisonResult
            // {
            //     FieldName = "InvoiceNumber",
            //     FieldDescription = "发票号码",
            //     ExpectedValue = invoice.InvoiceNumber,
            //     ActualValue = ocrInfo.InvoiceNumber,
            //     IsMatch = string.Equals(invoice.InvoiceNumber, ocrInfo.InvoiceNumber, StringComparison.OrdinalIgnoreCase),
            //     IsRequired = true
            // });

            // 开票日期比对 - 只比对日期部分
            string expectedDateStr = "";
            string actualDateStr = ocrInfo.InvoiceDate?? "";
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "InvoiceDate",
                FieldDescription = "开票日期",
                ExpectedValue = expectedDateStr,
                ActualValue = actualDateStr,
                IsMatch = string.Equals(expectedDateStr, actualDateStr),
                IsRequired = true
            });

            // 金额比对
            var expectedAmount = applicationInfo.AppliedAmount?.ToString("F2") ?? "0.00";
            var actualAmount = ocrInfo.TotalAmountWithTax;
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "TotalAmount",
                FieldDescription = "发票金额",
                ExpectedValue = expectedAmount,
                ActualValue = actualAmount,
                IsMatch = decimal.Parse(expectedAmount) == decimal.Parse(actualAmount),
                IsRequired = true
            });

            // 购买方名称比对
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "BuyerName",
                FieldDescription = "购买方名称",
                ExpectedValue = invoice.BillingHeader,
                ActualValue = ocrInfo.BuyerName,
                IsMatch = string.Equals(invoice.BillingHeader, ocrInfo.BuyerName, StringComparison.OrdinalIgnoreCase),
                IsRequired = true
            });

            // 销售方名称比对
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "SellerName",
                FieldDescription = "销售方名称",
                ExpectedValue = RedisCache.CollectingCompany.GetCollectingCompanyById(invoice.BillingCompany)?.SellerName ?? string.Empty,
                ActualValue = ocrInfo.SellerName,
                IsMatch = string.Equals(RedisCache.CollectingCompany.GetCollectingCompanyById(invoice.BillingCompany)?.SellerName ?? string.Empty, ocrInfo.SellerName, StringComparison.OrdinalIgnoreCase),
                IsRequired = true
            });
        }

        /// <summary>
        /// 保存退票OCR识别结果到数据库
        /// </summary>
        private async Task<string> SaveRefundOcrResultAsync(string refundId, OcrInvoiceResult ocrResult)
        {
            var entity = new Db_crm_invoice_recognition
            {
                Id = Guid.NewGuid().ToString("N"),
                InvoiceApplicationId = refundId, // 这里使用退票申请ID
                InvoiceCode = ocrResult.InvoiceCode,
                InvoiceNumber = ocrResult.InvoiceNumber,
                InvoiceDate = DateTime.Parse(ocrResult.InvoiceDate),
                BuyerName = ocrResult.BuyerName,
                SellerName = ocrResult.SellerName,
                BuyerTaxCode = ocrResult.BuyerTaxNumber,
                SellerTaxCode = ocrResult.SellerTaxNumber,
                TotalAmount = decimal.Parse(ocrResult.TotalAmountWithTax),
                InvoiceTypeString = ocrResult.InvoiceType,
                InvoiceDetails = ocrResult.InvoiceDetails,
                InvoiceAmount = ocrResult.TotalAmount,
                TaxAmount = ocrResult.TotalTax,
                RecognitionStatus = 1, // 1=成功
                RecognitionResult = ocrResult.OriginalText,
                RecognitionType = 2, // 2=退票
                Deleted = false,
                CreateUser = TokenModel.Instance.id,
                CreateDate = DateTime.Now,
                UpdateUser = TokenModel.Instance.id,
                UpdateDate = DateTime.Now
            };

            await Task.Run(() => {
                DbOpe_crm_invoice_recognition.Instance.Insert(entity);
            });

            return entity.Id;
        }

        /// <summary>
        /// 保存退票比对结果到数据库
        /// </summary>
        private void SaveRefundComparisonResult(string ocrId, InvoiceOcrCompareResult compareResult)
        {
            // 结果转换：true = 1 (完全匹配), false = 3 (不匹配)
            int matchResult = compareResult.IsMatch ? 1 : 3;
            
            var entity = new Db_crm_invoice_comparison
            {
                Id = Guid.NewGuid().ToString("N"),
                RecognitionId = ocrId,
                InvoiceApplicationId = compareResult.ApplicationInfo.Id,
                ComparisonResult = matchResult,
                ComparisonDetails = System.Text.Json.JsonSerializer.Serialize(compareResult.ComparisonResults),
                ProcessorRemark = compareResult.Remark,
                ComparisonType = 2, // 2=退票
                Deleted = false,
                CreateUser = TokenModel.Instance.id,
                CreateDate = DateTime.Now,
                UpdateUser = TokenModel.Instance.id,
                UpdateDate = DateTime.Now
            };

            DbOpe_crm_invoice_comparison.Instance.Insert(entity);
        }

        /// <summary>
        /// 保存退票附件到crm_invoice_refund_attachment表
        /// </summary>
        /// <param name="fileBytes">文件字节数组</param>
        /// <param name="refundId">退票申请ID</param>
        /// <param name="invoiceNumber">发票号码，用于生成文件名</param>
        /// <returns>附件ID</returns>
        private async Task<string> SaveRefundAttachmentAsync(byte[] fileBytes, string refundId, string invoiceNumber)
        {
            try
            {
                // 删除该退票申请ID关联的所有旧附件记录
                var oldAttachments = DbOpe_crm_invoice_refund_attachment.Instance.GetDataList(a => 
                    a.RefundApplicationId == refundId && a.Deleted == false).ToList();
                
                if (oldAttachments != null && oldAttachments.Count > 0)
                {
                    foreach (var oldAttachment in oldAttachments)
                    {
                        // 标记为已删除
                        oldAttachment.Deleted = true;
                        oldAttachment.UpdateUser = TokenModel.Instance.id;
                        oldAttachment.UpdateDate = DateTime.Now;
                        DbOpe_crm_invoice_refund_attachment.Instance.Update(oldAttachment);
                        
                        LogUtil.AddLog($"已删除旧退票附件，ID: {oldAttachment.Id}, 退票申请ID: {refundId}");
                    }
                }

                string attachmentId = Guid.NewGuid().ToString();
                string fileExtension = "pdf"; // 假设文件是PDF格式，如果需要动态判断可以增加相应逻辑
                string fileName = $"退票-{invoiceNumber}.{fileExtension}";
                string fileHead = $"{AppSettings.DownLoadFilePath}/InvoiceRefund/{DateTime.Now.ToString("yyyyMMdd")}";
                string fullFileName = $"{fileHead}/{attachmentId}.{fileExtension}";
                
                // 创建文件夹，保存文件
                string path = Path.GetDirectoryName(fullFileName);
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                
                // 如果使用云存储
                if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                {
                    using (MemoryStream stream = new MemoryStream(fileBytes))
                    {
                        QCloudOperator qCloud = new QCloudOperator();
                        qCloud.UploadStream(fullFileName, stream, "application/pdf");
                    }
                }
                else
                {
                    // 保存到本地文件系统
                    await File.WriteAllBytesAsync(fullFileName, fileBytes);
                }
                
                // 创建附件记录
                var attachment = new Db_crm_invoice_refund_attachment
                {
                    Id = attachmentId,
                    RefundApplicationId = refundId,
                    FileName = fileName,
                    FilePath = fullFileName,
                    FileExtension = fileExtension,
                    FileSize = fileBytes.Length,
                    AttachmentType = 1, // 1:申请附件
                    Deleted = false,
                    CreateUser = TokenModel.Instance.id,
                    CreateDate = DateTime.Now,
                    UpdateUser = TokenModel.Instance.id,
                    UpdateDate = DateTime.Now
                };
                
                // 保存附件记录到数据库
                DbOpe_crm_invoice_refund_attachment.Instance.Insert(attachment);
                
                LogUtil.AddLog($"保存退票附件成功，ID: {attachmentId}, 退票申请ID: {refundId}, 发票号: {invoiceNumber}");
                
                return attachmentId;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"保存退票附件失败: {ex.Message}");
                return string.Empty;
            }
        }
        #endregion
    }
} 