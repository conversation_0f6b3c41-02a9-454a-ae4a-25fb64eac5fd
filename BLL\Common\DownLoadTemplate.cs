﻿using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml;
using System.IO;
using DW = DocumentFormat.OpenXml.Drawing.Wordprocessing;
using PIC = DocumentFormat.OpenXml.Drawing.Pictures;
using A = DocumentFormat.OpenXml.Drawing;
using Text = DocumentFormat.OpenXml.Wordprocessing.Text;
using Spire.Pdf;
using Spire.Pdf.Graphics;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Png;
using Spire.Pdf.General.Find;

namespace CRM2_API.BLL.Common
{
    public class DownLoadTemplate
    {
        public static Stream DownLoadDocTemplate(byte[] file, List<TemplateField> item, List<TemplateZhTableField> rows, string contractDescription, string picturePath)
        {
            using (MemoryStream stream = new MemoryStream())
            {
                stream.Write(file, 0, file.Length);
                stream.Position = 0;

                using (WordprocessingDocument wordDoc = WordprocessingDocument.Open(stream, true))
                {
                    //替换模板标签
                    var body = wordDoc.MainDocumentPart!.Document.Body;
                    var paras = body!.Elements<Paragraph>();
                    foreach (var para in paras)
                    {
                        var runs = para.Elements<Run>();
                        foreach (var run in runs)
                        {
                            var texts = run.Elements<Text>();
                            foreach (var text in texts)
                            {
                                List<TemplateField> field = item.Where(r => r.FieldName == text.Text).ToList();
                                if (field.Count() > 0)
                                {
                                    string FieldName = field.First().FieldName;
                                    string FieldValue = field.First().FieldValue;
                                    text.Text = text.Text.Replace(FieldName, FieldValue);
                                }
                            }
                        }
                    }

                    //动态添加产品信息
                    Table table = wordDoc.MainDocumentPart.Document.Body.Elements<Table>().First();

                    TableRow row_d = table.Elements<TableRow>().ElementAt(1);
                    TableCell cell_d = row_d.Elements<TableCell>().ElementAt(1);
                    Paragraph p_d = cell_d.Elements<Paragraph>().First();
                    Run r = p_d.Elements<Run>().First();
                    Text t = r.Elements<Text>().First();
                    t.Text = contractDescription;

                    // 添加数据
                    foreach (TemplateZhTableField tableField in rows)
                    {
                        TableRow row = new TableRow();
                        TableRowProperties tabRowProps = row.AppendChild(new TableRowProperties(new TableRowHeight { Val = 600, HeightType = HeightRuleValues.Exact }));

                        TableCell CellTitel = new TableCell();

                        //垂直居中显示
                        var CellTitelTcProperties = new TableCellProperties();
                        CellTitelTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellTitelPproperties = new ParagraphProperties();
                        CellTitelPproperties.AppendChild(new Justification { Val = JustificationValues.Center });
                        //设置字体样式
                        RunProperties CellTitelRunProperties = new RunProperties();//属性
                        CellTitelRunProperties.Append(new Bold());
                        CellTitelRunProperties.Append(new BoldComplexScript());
                        CellTitelRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellTitelRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellTitelRun = new Run();
                        CellTitelRun.Append(CellTitelRunProperties);
                        CellTitelRun.Append(new Text(tableField.CellTitel));
                        var CellTitelRunText = new Paragraph();
                        CellTitelRunText.Append(CellTitelRun);
                        CellTitelRunText.Append(CellTitelPproperties);
                        CellTitel.AppendChild(CellTitelTcProperties);
                        CellTitel.AppendChild(CellTitelRunText);
                        //CellTitel.Append(new Paragraph(new Run(new Text(tableField.CellTitel))));
                        row.Append(CellTitel);

                        TableCell CellContent = new TableCell();
                        //垂直居中显示
                        var CellContentTcProperties = new TableCellProperties();
                        CellContentTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellContentPproperties = new ParagraphProperties();
                        CellContentPproperties.AppendChild(new Justification { Val = JustificationValues.Left });
                        //设置字体样式
                        RunProperties CellContentRunProperties = new RunProperties();//属性
                        CellContentRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellContentRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellContentRun = new Run();
                        CellContentRun.Append(CellContentRunProperties);
                        CellContentRun.Append(new Text(tableField.CellContent));
                        var CellContentRunText = new Paragraph();
                        CellContentRunText.Append(CellContentRun);
                        CellContentRunText.Append(CellContentPproperties);
                        CellContent.AppendChild(CellContentTcProperties);
                        CellContent.AppendChild(CellContentRunText);

                        //CellContent.Append(new Paragraph(new Run(new Text(tableField.CellContent))));
                        row.Append(CellContent);

                        TableCell CellType = new TableCell();
                        //垂直居中显示
                        var CellTypeTcProperties = new TableCellProperties();
                        CellTypeTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellTypePproperties = new ParagraphProperties();
                        CellTypePproperties.AppendChild(new Justification { Val = JustificationValues.Center });
                        //设置字体样式
                        RunProperties CellTypeRunProperties = new RunProperties();//属性
                        CellTypeRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellTypeRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellTypeRun = new Run();
                        CellTypeRun.Append(CellTypeRunProperties);
                        CellTypeRun.Append(new Text(tableField.CellType));
                        var CellTypeRunText = new Paragraph();
                        CellTypeRunText.Append(CellTypeRun);
                        CellTypeRunText.Append(CellTypePproperties);
                        CellType.AppendChild(CellTypeTcProperties);
                        CellType.AppendChild(CellTypeRunText);

                        //CellType.Append(new Paragraph(new Run(new Text(tableField.CellType))));
                        row.Append(CellType);

                        table.Elements<TableRow>().First().InsertAfterSelf(row);
                    }

                    //string picturePath = "F:\\workGit\\crm2\\crm2_api\\CRM2_API\\Template\\图章.png";
                    string picType = "png";
                    ImagePartType imagePartType;
                    ImagePart imagePart = null;
                    // 通过后缀名判断图片类型, true 表示忽视大小写
                    if (Enum.TryParse<ImagePartType>(picType, true, out imagePartType))
                    {
                        imagePart = wordDoc.MainDocumentPart.AddImagePart(imagePartType);
                    }
                    imagePart.FeedData(File.Open(picturePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite)); // 读取图片二进制流

                    MainDocumentPart mainPart = wordDoc.MainDocumentPart;
                    var bookmarks = from bm in mainPart.Document.Body.Descendants<BookmarkStart>()
                                    where bm.Name == "stamp"
                                    select bm;
                    var bookmark = bookmarks.SingleOrDefault();
                    if (bookmark != null)
                    {
                        OpenXmlElement elem = bookmark.NextSibling();
                        while (elem != null && !(elem is BookmarkEnd))
                        {
                            OpenXmlElement nextElem = elem.NextSibling();
                            elem.Remove();
                            elem = nextElem;
                        }
                        var parent = bookmark.Parent;
                        Run run = new Run(new RunProperties());
                        Run stamp = CreateImageParagraph2(wordDoc.MainDocumentPart.GetIdOfPart(imagePart), Path.GetFileNameWithoutExtension(picturePath), 1, 230, 230, 6, false);
                        parent.InsertAfter<Run>(new Run(new Run(stamp)), bookmark);
                    }

                    wordDoc.Save();
                }

                MemoryStream result = new MemoryStream();
                stream.WriteTo(result);
                result.Position = 0;
                return result;
            }
        }

        public static Stream PrintPdfFile(Stream file, string pngUrl)
        {
            //加载PDF测试文档
            PdfDocument doc = new PdfDocument();
            doc.LoadFromStream(file);
            string[] pngName = pngUrl.Split(";");
            int heightOld = -140;
            int weighOld = 120;
            for (int j = 0; j < pngName.Length; j++)
            {
                //获取分割后的印章图片
                Image[] images = GetImage(doc.Pages.Count, pngName[j]);
                float x = 0;
                float y = 0;

                PdfUnitConvertor convert = new PdfUnitConvertor();
                PdfPageBase pageBase = null;
                // 将图片绘制到PDF页面上的指定位置
                for (int i = 0; i < doc.Pages.Count; i++)
                {
                    pageBase = doc.Pages[i];
                    x = pageBase.Size.Width - convert.ConvertToPixels(images[i].Width, PdfGraphicsUnit.Point); //- 40;
                    y = pageBase.Size.Height / 2 + heightOld;
                    pageBase.Canvas.SetTransparency(0.8f, 0.8f, PdfBlendMode.Normal);//设置背景透明
                    pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(images[i])), x, y); //new System.Drawing.PointF(x, y));//,new SizeF()

                    //if (i == doc.Pages.Count - 1)
                    //{
                    //    pageBase.Canvas.SetTransparency(0.8f,0.8f,PdfBlendMode.Normal); //设置背景透明
                    //    pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(GetImage(pngName[j]))), weighOld, pageBase.Size.Height / 2);// new System.Drawing.PointF(weighOld, pageBase.Size.Height / 2)); //,new SizeF()
                    //    weighOld = weighOld + 200;
                    //}

                    //添加尾章
                    PdfTextFindCollection stamp = pageBase.FindText("乙方（盖章）：", TextFindParameter.WholeWord);
                    if (stamp.Finds.Length > 0)
                    {
                        Image stampimage = GetImage(pngName[j]);
                        float stampx = stamp.Finds[0].Position.X;
                        float stampy = stamp.Finds[0].Position.Y - 60;
                        if (pageBase.Size.Height - stampy < stampimage.Height)
                        {
                            stampy = pageBase.Size.Height - stampimage.Height;
                        }
                        pageBase.Canvas.SetTransparency(2f, 2f, PdfBlendMode.Normal);
                        pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(stampimage)), stampx, stampy);
                    }
                }
                heightOld = heightOld + 140;
            }

            ////保存文档
            ////doc.SaveToFile("Result-pdf");
            ////System.Diagnostics.Process .Start("Result.pdf");
            //string output = @fileUrl;//添加图片后的新pdf文件的路径
            ////save pdf file
            //doc.SaveToFile(output);
            Stream streamPDF = new MemoryStream();
            doc.SaveToStream(streamPDF);
            return streamPDF;
        }

        private static Stream ImageToStream(Image image)
        {
            MemoryStream ms = new MemoryStream();
            image.Save(ms, new PngEncoder());
            return ms;
        }

        //定义GetImage万法，根据PDF页数分割印章图片
        private static Image[] GetImage(int num, string pngUrl)
        {
            List<Image> lists = new List<Image>();
            Image image = GetImage(pngUrl);//Image.FromFile(pngurl);
            int w = image.Width / num;
            for (int i = 0; i < num; i++)
            {
                Rectangle rect = new Rectangle(i * w, 0, w, image.Height);
                var clone = image.Clone(i => i.Resize(image.Width, image.Height).Crop(rect));
                MemoryStream ms = new MemoryStream();
                clone.Save(ms, new PngEncoder());
                Image imageNewSave = Image.Load(ms.ToArray());
                lists.Add(imageNewSave);
            }

            //Bitmap bitmap = null;
            //for (int i = 0; i < num; i++)
            //{
            //    bitmap = new Bitmap(w, image.Height);
            //    using (Graphics g = System.Drawing.Graphics.FromImage(bitmap))
            //    {
            //        g.Clear(Color.Transparent);

            //        Rectangle rect = new Rectangle(i * w, 0, w, image.Height);
            //        g.SmoothingMode = SmoothingMode.AntiAlias;
            //        g.DrawImage(image, new System.Drawing.Rectangle(0, 0, bitmap.Width, bitmap.Height), rect, GraphicsUnit.Pixel);
            //    }
            //    bitmap.MakeTransparent(System.Drawing.Color.Transparent);//设置背景透明
            //    //string pngName = Path.Combine(Directory.GetCurrentDirectory(), "Template\\" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".png"); //@"F:\Pictures\Camera Roll\" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".png";
            //    MemoryStream ms = new MemoryStream();
            //    bitmap.Save(ms, new PngEncoder());
            //    Image imageNewSave = Image.Load(ms);
            //    lists.Add(imageNewSave);
            //}
            return lists.ToArray();
        }

        //定义GetImage方法，根据PDF页数分割印章图片
        private static Image GetImage(string pngUrl)
        {
            Image image = Image.Load(pngUrl);
            image.Mutate(a => a.Resize(170, 170));
            ////Bitmap bitmapProxy = new Bitmap(image, new Size(230, 230));
            //image.Mutate(a => a.Resize(230, 230));
            //image.Dispose();
            //for (int i = 0; i < bitmapProxy.Width; i++)
            //{
            //    for (int j = 0; j < bitmapProxy.Height; j++)
            //    {
            //        Color c = bitmapProxy.GetPixel(i, j);
            //        if (!(c.R < 240 || c.G < 240 || c.B < 240))
            //        {
            //            bitmapProxy.SetPixel(i,j,Color.Transparent);//设置背景透明
            //        }
            //    }
            //}
            //bitmapProxy.MakeTransparent(System.Drawing.Color.Transparent);//设置背景透明
            //图片另算为
            //string pngName = Path.Combine(Directory.GetCurrentDirectory(), "Template\\" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".png"); //@"F:\Pictures\Camera Roll\" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".png";
            MemoryStream ms = new MemoryStream();
            image.Save(ms, new PngEncoder());
            Image imageNewSave = Image.Load(ms.ToArray());
            return imageNewSave;
        }

        /// <summary>
        /// 创建图片段落
        /// </summary>
        /// <param name="relationshipId">图片引用id</param>
        /// <param name="picname">图片名</param>
        /// <param name="picindex">图片在doc中的索引，即第几张图</param>
        /// <param name="width">宽度像素</param>
        /// <param name="height">高度像素</param>
        /// <param name="wrapType">图片环绕类型0=嵌入，1=四周环绕，2=紧密型环绕，3=穿越型环绕，4=上下型环绕，5=衬于文字下方，6=浮于文字上方</param>
        /// <param name="horizontalcenter">水平居中，仅对wrapType=0生效</param>
        /// <returns></returns>
        private static Run CreateImageParagraph2(string relationshipId, string picname, int picindex, int width, int height, int wrapType = 0, bool horizontalcenter = false)
        {
            Run run1 = new Run();

            RunProperties runProperties1 = new RunProperties();
            RunFonts runFonts2 = new RunFonts() { Hint = FontTypeHintValues.EastAsia, EastAsiaTheme = ThemeFontValues.MinorEastAsia };
            Languages languages2 = new Languages() { EastAsia = "zh-CN" };

            runProperties1.Append(runFonts2);
            runProperties1.Append(languages2);

            Drawing drawing1 = new Drawing();

            DW.Anchor anchor1 = new DW.Anchor() { DistanceFromTop = 0U, DistanceFromBottom = 0U, DistanceFromLeft = 0U, DistanceFromRight = 0U, SimplePos = false, RelativeHeight = 0U, BehindDoc = false, Locked = false, LayoutInCell = true, AllowOverlap = true };
            DW.Inline inline1 = new DW.Inline() { DistanceFromTop = 0U, DistanceFromBottom = 0U, DistanceFromLeft = 0U, DistanceFromRight = 0U };
            DocumentFormat.OpenXml.OpenXmlCompositeElement anchorline = anchor1;
            if (wrapType == 0) anchorline = inline1;

            DW.SimplePosition simplePosition1 = new DW.SimplePosition() { X = 0L, Y = 0L };

            DW.HorizontalPosition horizontalPosition1 = new DW.HorizontalPosition() { RelativeFrom = DW.HorizontalRelativePositionValues.Character };
            DW.PositionOffset positionOffset1 = new DW.PositionOffset();
            positionOffset1.Text = "0";

            horizontalPosition1.Append(positionOffset1);

            DW.VerticalPosition verticalPosition1 = new DW.VerticalPosition() { RelativeFrom = DW.VerticalRelativePositionValues.Paragraph };
            DW.PositionOffset positionOffset2 = new DW.PositionOffset();
            positionOffset2.Text = "0";

            verticalPosition1.Append(positionOffset2);
            DW.Extent extent1 = new DW.Extent() { Cx = (long)(width * 0.0264583 * 360000), Cy = (long)(height * 0.0264583 * 360000) };
            DW.EffectExtent effectExtent1 = new DW.EffectExtent() { LeftEdge = 0L, TopEdge = 0L, RightEdge = 0L, BottomEdge = 0L };
            DocumentFormat.OpenXml.OpenXmlElement wrap1 = null;
            var wrapPolygon1 = new DW.WrapPolygon();
            var startPoint1 = new DW.StartPoint() { X = 0L, Y = 0L };
            var lineTo1 = new DW.LineTo() { X = 0L, Y = 21405L };
            var lineTo2 = new DW.LineTo() { X = 21501L, Y = 21405L };
            var lineTo3 = new DW.LineTo() { X = 21501L, Y = 0L };
            var lineTo4 = new DW.LineTo() { X = 0L, Y = 0L };
            wrapPolygon1.Append(startPoint1);
            wrapPolygon1.Append(lineTo1);
            wrapPolygon1.Append(lineTo2);
            wrapPolygon1.Append(lineTo3);
            wrapPolygon1.Append(lineTo4);
            switch (wrapType)
            {
                case 1: wrap1 = new DW.WrapSquare() { WrapText = DW.WrapTextValues.BothSides }; break;
                case 2:
                    wrap1 = new DW.WrapTight() { WrapText = DW.WrapTextValues.BothSides };
                    wrap1.Append(wrapPolygon1);
                    break;
                case 3:
                    wrap1 = new DW.WrapThrough() { WrapText = DW.WrapTextValues.BothSides };
                    wrap1.Append(wrapPolygon1);
                    break;
                case 4:
                    wrap1 = new DW.WrapTopBottom();
                    break;
                case 5: wrap1 = new DW.WrapNone(); anchor1.BehindDoc = true; break;
                case 6: wrap1 = new DW.WrapNone(); anchor1.BehindDoc = false; break;
            }

            DW.DocProperties docProperties1 = new DW.DocProperties() { Id = (uint)picindex, Name = "Picture " + picindex, Description = picname };

            DW.NonVisualGraphicFrameDrawingProperties nonVisualGraphicFrameDrawingProperties1 = new DW.NonVisualGraphicFrameDrawingProperties();

            A.GraphicFrameLocks graphicFrameLocks1 = new A.GraphicFrameLocks() { NoChangeAspect = true };
            graphicFrameLocks1.AddNamespaceDeclaration("a", "http://schemas.openxmlformats.org/drawingml/2006/main");

            nonVisualGraphicFrameDrawingProperties1.Append(graphicFrameLocks1);

            A.Graphic graphic1 = new A.Graphic();
            graphic1.AddNamespaceDeclaration("a", "http://schemas.openxmlformats.org/drawingml/2006/main");

            A.GraphicData graphicData1 = new A.GraphicData() { Uri = "http://schemas.openxmlformats.org/drawingml/2006/picture" };

            PIC.Picture picture1 = new PIC.Picture();
            picture1.AddNamespaceDeclaration("pic", "http://schemas.openxmlformats.org/drawingml/2006/picture");

            PIC.NonVisualPictureProperties nonVisualPictureProperties1 = new PIC.NonVisualPictureProperties();
            PIC.NonVisualDrawingProperties nonVisualDrawingProperties1 = new PIC.NonVisualDrawingProperties() { Id = (uint)picindex, Name = "Picture " + picindex, Description = picname };

            PIC.NonVisualPictureDrawingProperties nonVisualPictureDrawingProperties1 = new PIC.NonVisualPictureDrawingProperties();
            A.PictureLocks pictureLocks1 = new A.PictureLocks() { NoChangeAspect = true };

            nonVisualPictureDrawingProperties1.Append(pictureLocks1);

            nonVisualPictureProperties1.Append(nonVisualDrawingProperties1);
            nonVisualPictureProperties1.Append(nonVisualPictureDrawingProperties1);

            PIC.BlipFill blipFill1 = new PIC.BlipFill();
            A.Blip blip1 = new A.Blip() { Embed = relationshipId };

            A.Stretch stretch1 = new A.Stretch();
            A.FillRectangle fillRectangle1 = new A.FillRectangle();

            stretch1.Append(fillRectangle1);

            blipFill1.Append(blip1);
            blipFill1.Append(stretch1);
            PIC.ShapeProperties shapeProperties1 = new PIC.ShapeProperties();

            A.Transform2D transform2D1 = new A.Transform2D();
            A.Offset offset1 = new A.Offset() { X = 0L, Y = 0L };
            A.Extents extents1 = new A.Extents() { Cx = (long)(width * 0.0264583 * 360000), Cy = (long)(height * 0.0264583 * 360000) };

            transform2D1.Append(offset1);
            transform2D1.Append(extents1);

            A.PresetGeometry presetGeometry1 = new A.PresetGeometry() { Preset = A.ShapeTypeValues.Rectangle };
            A.AdjustValueList adjustValueList1 = new A.AdjustValueList();

            presetGeometry1.Append(adjustValueList1);

            shapeProperties1.Append(transform2D1);
            shapeProperties1.Append(presetGeometry1);

            picture1.Append(nonVisualPictureProperties1);
            picture1.Append(blipFill1);
            picture1.Append(shapeProperties1);

            graphicData1.Append(picture1);
            graphic1.Append(graphicData1);

            anchorline.Append(simplePosition1);
            anchor1.Append(horizontalPosition1);
            anchor1.Append(verticalPosition1);
            anchorline.Append(extent1);
            anchorline.Append(effectExtent1);
            if (wrap1 != null)
                anchorline.Append(wrap1);
            anchorline.Append(docProperties1);
            anchorline.Append(nonVisualGraphicFrameDrawingProperties1);
            anchorline.Append(graphic1);
            drawing1.Append(anchorline);

            run1.Append(runProperties1);
            run1.Append(drawing1);

            return run1;
        }
    }
}
