﻿using System.ComponentModel.DataAnnotations;

namespace CRM2_API.Model.BusinessModel.BM_GtisOpe
{
            /// <summary>
    /// 
    /// </summary>
    public class AppOptionModel
    {
        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName { get; set; }

        /// <summary>
        /// 剩余可分配账号数
        /// </summary>
        /// <returns></returns>
        public int AssignableNum { get; set; }
                /// <summary>
        /// 应用分配最大账号数
        /// </summary>
        public int MaxAccount { get; set; }

        /// <summary>
        /// 应用开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 应用结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 应用选项
    /// </summary>
    public class AppOptions
    {
        /// <summary>
        /// 应用分配最大账号数
        /// </summary>
        public int MaxAccount { get; set; }

        /// <summary>
        /// 应用开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 应用结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }
}
