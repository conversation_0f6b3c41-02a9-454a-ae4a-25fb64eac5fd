﻿using CRM2_API.Model.BusinessModel;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        public class RC_ContractNo
        {
            const string CONTRACTNO = "contractno_";

            /// <summary>
            /// 校验合同号
            /// </summary>
            /// <param name="contractNo"></param>
            /// <returns></returns>
            public static bool CheckContractNo(string contractNo)
            {
                var key = CONTRACTNO + contractNo;
                return RedisHelper.Exists(key);
            }

            public static void SaveContractNo(string contractNo)
            {
                RedisHelper.Set(CONTRACTNO + contractNo, contractNo/*, TimeSpan.FromMinutes(60)*/);
            }

            public static void DelContractNo(string contractNo)
            {
                var key = CONTRACTNO + contractNo;
                RedisHelper.Del(key);
            }
        }
    }
}
