﻿using System;
using System.Linq;
using System.Text;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_contract_invoice_state")]
    public class Db_v_contract_invoice_state
    {
        /// <summary>
        /// Desc:合同ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ContractId { get; set; }

        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>           
        public EnumContractInvoiceDisplayStatus DisplayStatus { get; set; }

    }
}
