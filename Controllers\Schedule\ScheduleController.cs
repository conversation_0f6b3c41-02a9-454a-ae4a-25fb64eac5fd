﻿using System.ComponentModel;
using CRM2_API.BLL.Schedule;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.Schedule;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 日程计划控制器
    /// </summary>
    [Description("日程计划控制器")]
    public class ScheduleController : MyControllerBase
    {
        public ScheduleController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 根据查询条件获取日程计划信息列表
        /// </summary>
        /// <param name="searchScheduleListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchScheduleList_Out> SearchScheduleList(SearchScheduleList_In searchScheduleListIn)
        {
            int total = 0;
            var list = DbOpe_crm_schedule.Instance.SearchScheduleList(searchScheduleListIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据日程计划Id获取日程计划信息
        /// </summary>
        /// <param name="getScheduleByIdIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public GetScheduleById_Out GetScheduleById(GetScheduleById_In getScheduleByIdIn)
        {
            //验证Id是否为空
            if (getScheduleByIdIn == null || string.IsNullOrEmpty(getScheduleByIdIn.Id))
                throw new ApiException("日程计划主键不可为空");
            return DbOpe_crm_schedule.Instance.GetScheduleById(getScheduleByIdIn.Id);
        }

        /// <summary>
        /// 根据客户表主键Id获取日程计划信息
        /// </summary>
        /// <param name="getScheduleByCustomerIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetScheduleByCustomerId_Out> GetScheduleByCustomerId(GetScheduleByCustomerId_In getScheduleByCustomerIdIn)
        {
            if (getScheduleByCustomerIdIn == null || string.IsNullOrEmpty(getScheduleByCustomerIdIn.CustomerId))
                throw new ApiException("客户表主键不可为空");
            int total = 0;
            var list = DbOpe_crm_schedule.Instance.GetScheduleByCustomerId(getScheduleByCustomerIdIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据日程计划表主键Id设置日程计划为已读
        /// </summary>
        /// <param name="setScheduleReadByIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public SetScheduleReadById_Out SetScheduleReadById(SetScheduleReadById_In setScheduleReadByIdIn)
        {
            //验证Id是否为空
            if (setScheduleReadByIdIn == null || string.IsNullOrEmpty(setScheduleReadByIdIn.Id))
                throw new ApiException("日程计划表主键不可为空");
            return BLL_Schedule.Instance.SetScheduleReadById(setScheduleReadByIdIn);
        }

        /// <summary>
        /// 添加日程计划信息
        /// </summary>
        /// <param name="addScheduleIn"></param>
        /// <returns></returns>
        [HttpPost]
        public AddSchedule_Out AddSchedule([FromForm] AddSchedule_In addScheduleIn)
        {
            addScheduleIn = addScheduleIn ?? throw new ApiException("请求数据错误!");
            return BLL_Schedule.Instance.AddSchedule(addScheduleIn, true, false, "", true);
        }

        /// <summary>
        /// 修改日程计划信息
        /// </summary>
        /// <param name="updateScheduleIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdateSchedule_Out UpdateSchedule([FromForm] UpdateSchedule_In updateScheduleIn)
        {
            updateScheduleIn = updateScheduleIn ?? throw new ApiException("请求数据错误!");
            return BLL_Schedule.Instance.UpdateSchedule(updateScheduleIn);
        }

        /// <summary>
        /// 删除日程计划信息
        /// </summary>
        /// <param name="deleteScheduleIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public DeleteSchedule_Out DeleteSchedule(DeleteSchedule_In deleteScheduleIn)
        {
            if (deleteScheduleIn is null || string.IsNullOrEmpty(deleteScheduleIn.Id))
                throw new ApiException("主键不可为空");
            return BLL_Schedule.Instance.DeleteSchedule(deleteScheduleIn);
        }

        /// <summary>
        /// 取消日程计划信息
        /// </summary>
        /// <param name="cancelScheduleIn"></param>
        /// <returns></returns>
        [HttpPost]
        public CancelSchedule_Out CancelSchedule(CancelSchedule_In cancelScheduleIn)
        {
            if (cancelScheduleIn is null || string.IsNullOrEmpty(cancelScheduleIn.Id))
                throw new ApiException("主键不可为空");
            return BLL_Schedule.Instance.CancelSchedule(cancelScheduleIn);
        }

        /// <summary>
        /// 延期日程计划信息
        /// </summary>
        /// <param name="delayScheduleIn"></param>
        /// <returns></returns>
        [HttpPost]
        public DelaySchedule_Out DelaySchedule(DelaySchedule_In delayScheduleIn)
        {
            //验证Id是否为空
            if (delayScheduleIn == null || string.IsNullOrEmpty(delayScheduleIn.Id))
                throw new ApiException("日程计划表主键不可为空");
            return BLL_Schedule.Instance.DelaySchedule(delayScheduleIn);
        }

        /// <summary>
        /// 完成日程计划信息
        /// </summary>
        /// <param name="completeScheduleIn"></param>
        /// <returns></returns>
        [HttpPost]
        public CompleteSchedule_Out CompleteSchedule([FromForm] CompleteSchedule_In completeScheduleIn)
        {
            //验证Id是否为空
            if (completeScheduleIn == null || string.IsNullOrEmpty(completeScheduleIn.Id))
                throw new ApiException("日程计划表主键不可为空");
            return BLL_Schedule.Instance.CompleteSchedule(completeScheduleIn);
        }

        /// <summary>
        /// 通过年和月份字符串及一些查询条件返回该年月有日程计划的日期列表
        /// </summary>
        /// <param name="yearWithMonthIn">年和月份字符串及一些查询条件</param>
        /// <returns>该年月有日程计划的日期列表</returns>
        [HttpPost, SkipRightCheck]
        public List<string> GetHasDataDatetimeByYearWithMonth(GetHasDataDatetimeByYearWithMonth_In yearWithMonthIn)
        {
            return DbOpe_crm_schedule.Instance.GetHasDataDatetimeByYearWithMonth(yearWithMonthIn);
        }
    }
}