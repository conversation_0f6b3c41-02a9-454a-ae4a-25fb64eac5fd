using CRM2_API.Common;
using CRM2_API.Common.DingTalk;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 钉钉相关接口
    /// </summary>
    [Description("钉钉相关接口")]
    public class DingTalkController : MyControllerBase
    {
        public DingTalkController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取钉钉 JSAPI 配置信息
        /// </summary>
        /// <param name="url">当前页面的完整URL，不包含#后面的部分</param>
        /// <returns>钉钉 JSAPI 配置信息</returns>
        [HttpPost, Skip<PERSON><PERSON><PERSON><PERSON><PERSON>, UseDefault<PERSON><PERSON><PERSON>ey, PreLog, SkipIPCheck]
        public CRM2_API.Common.DingTalk.DingTalk.JsapiSignature GetJsapiConfig(string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url))
                {
                    throw new ApiException("URL不能为空");
                }
                return CRM2_API.Common.DingTalk.DingTalk.GetJsapiSignature(url);
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取钉钉JSAPI配置失败: {ex.Message}");
            }
        }
    }
} 