﻿using CRM2_API.Controllers.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.BLL;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Common.Filter;

namespace CRM2_API.Controllers
{
    [Description("数据字典器")]
    public class DictionaryController : MyControllerBase
    {
        public DictionaryController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        ///// <summary>
        ///// 添加数据字典信息
        ///// </summary>
        ///// <param name="addDictionary_In"></param>
        //[HttpPost]
        //public void AddDictionary(AddDictionary_In addDictionary_In)
        //{
        //    BLL_Dictionary.Instance.AddDictionary(addDictionary_In);
        //}

        ///// <summary>
        ///// 修改数据字典信息
        ///// </summary>
        ///// <param name="updateDictionary_In"></param>
        //[HttpPost]
        //public void UpdateDictionary(UpdateDictionary_In updateDictionary_In)
        //{
        //    BLL_Dictionary.Instance.UpdateDictionary(updateDictionary_In);
        //}

        ///// <summary>
        ///// 删除数据字典信息
        ///// </summary>
        ///// <param name="Ids"></param>
        //[HttpPost]
        //public void DeleteDictionary(string Ids)
        //{
        //    BLL_Dictionary.Instance.DeleteDictionary(Ids);
        //}

        /// <summary>
        /// 根据数据字典Id获取数据字典信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost, SkipRightCheck]
        public Dictionary_Out GetDictionaryById(string Id)
        {
            if (string.IsNullOrEmpty(Id))
            {
                throw new ApiException("数据字典主键不可为空");
            }
            return DbOpe_sys_dictionary.Instance.GetDictionaryById(Id);
        }

        /// <summary>
        /// 根据查询条件获取数据字典信息列表
        /// </summary>
        /// <param name="searchDictionaryListIn"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public ApiTableOut<Dictionary_Out> SearchDictionaryList(SearchDictionaryList_In searchDictionaryListIn)
        {
            int total = 0;
            var list = DbOpe_sys_dictionary.Instance.SearchDictionaryList(searchDictionaryListIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据数据字典ParentId获取数据字典信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost, SkipRightCheck]
        public List<Dictionary_Out> GetDictionaryByParentId(string Id)
        {
            if (string.IsNullOrEmpty(Id))
            {
                throw new ApiException("数据字典父级节点主键不可为空");
            }
            return DbOpe_sys_dictionary.Instance.GetDictionaryByParentId(Id);
        }

        /// <summary>
        /// 根据数据字典ParentName获取数据字典信息
        /// </summary>
        /// <param name="Name"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost, SkipRightCheck]
        public List<Dictionary_Out> GetDictionaryByParentName(string Name)
        {
            if (string.IsNullOrEmpty(Name))
            {
                throw new ApiException("数据字典父级节点名称不可为空");
            }
            return DbOpe_sys_dictionary.Instance.GetDictionaryByParentName(Name);
        }

        /// <summary>
        /// 根据数据字典ParentValue获取数据字典信息
        /// </summary>
        /// <param name="Value"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost, SkipRightCheck]
        public List<Dictionary_Out> GetDictionaryByParentValue(string Value)
        {
            if (string.IsNullOrEmpty(Value))
            {
                throw new ApiException("数据字典父级节点值不可为空");
            }
            return DbOpe_sys_dictionary.Instance.GetDictionaryByParentValue(Value);
        }


        /// <summary>
        /// 根据统计数据字典ParentName获取统计数据字典信息
        /// </summary>
        /// <param name="Name"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost, SkipRightCheck]
        public List<Dictionary_Sta_Out> GetStaDictionaryByParentName(string Name)
        {
            if (string.IsNullOrEmpty(Name))
            {
                throw new ApiException("数据字典父级节点名称不可为空");
            }
            return DbOpe_sys_dictionary_statictics.Instance.GetDictionaryByParentName(Name);
        }
        /// <summary>
        /// 获取统计数据字典信息
        /// </summary>
        /// <param name="dictionary_Sta_In"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost, SkipRightCheck]
        public List<Dictionary_Sta_Out> GetStaDictionaryByPageAndParentName(Dictionary_Sta_In  dictionary_Sta_In)
        {
            return BLL_Statistics.Instance.GetStaDictionaryByPageAndParentName(dictionary_Sta_In);
        }
    }
}
