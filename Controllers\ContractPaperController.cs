﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.ContractPaper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    [Description("纸质合同控制器")]
    public class ContractPaperController : MyControllerBase
    {

        public ContractPaperController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 添加纸质合同申领信息
        /// </summary>
        /// <param name="conPaperApply"></param>
        [HttpPost, PreLog]
        public void AddContractPaperApply(AddContractPaperApply_In conPaperApply)
        {
            BLL_ContractPaper.Instance.AddContractPaperApply(conPaperApply);
        }

        /// <summary>
        /// 修改纸质合同申领信息
        /// </summary>
        /// <param name="conPaperApply"></param>
        [HttpPost, PreLog]
        public void UpdateContractPaperApply(UpdateContractPaperApply_In conPaperApply)
        {
            BLL_ContractPaper.Instance.UpdateContractPaperApply(conPaperApply);
        }

        /// <summary>
        /// 删除纸质合同申领信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void DeleteContractPaperApply(string Ids)
        {
            BLL_ContractPaper.Instance.DeleteContractPaperApply(Ids);
        }

        /// <summary>
        /// 撤销纸质合同申领信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void RevokeContractPaperApply(string Ids)
        {
            BLL_ContractPaper.Instance.RevokeContractPaperApply(Ids);
        }


        /// <summary>
        /// 获取纸质合同申领信息列表--查询条件--申领人内容
        /// </summary>
        /// <returns></returns>
        [HttpPost, PreLog, SkipRightCheck]
        public List<GetSearchContractPaperApplyListClaimants_Out> GetSearchContractPaperApplyListClaimants()
        {
            return DbOpe_crm_contract_paperapply.Instance.GetSearchContractPaperApplyListClaimants();
        }
        /// <summary>
        /// 根据查询条件获取纸质合同申领信息列表
        /// </summary>
        /// <param name="searchList_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchContractPaperApplyList_Out> SearchContractPaperApplyList(SearchContractPaperApplyList_In searchList_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_paperapply.Instance.SearchContractPaperApplyList(searchList_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据纸质合同申领信息Id获取纸质合同申领信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractPaperApplyInfoById_Out GetContractPaperApplyInfoById(string Id)
        {
            return DbOpe_crm_contract_paperapply.Instance.GetContractPaperApplyInfoById(Id);
        }

        /// <summary>
        /// 根据纸质合同申领id获取合同信息列表
        /// </summary>
        /// <param name="serchCon_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<GetContractByContractPaperApplyId_Out> GetContractByContractPaperApplyId(GetContractByContractPaperApplyId_In serchCon_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_paperapply.Instance.GetContractByContractPaperApplyId(serchCon_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据纸质合同申领id获取纸质合同分发列表信息
        /// </summary>
        /// <param name="serchCon_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<GetContractPaperDistributeByContractPaperApplyId_Out> GetContractPaperDistributeByContractPaperApplyId(GetContractPaperDistributeByContractPaperApplyId_In serchCon_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_paperapply.Instance.GetContractPaperDistributeByContractPaperApplyId(serchCon_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据查询条件获取纸质合同申领信息列表
        /// </summary>
        /// <param name="searchList_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchContractPaperAuditList_Out> SearchContractPaperAuditList(SearchContractPaperAuditList_In searchList_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_paperaudit.Instance.SearchContractPaperAuditList(searchList_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 审核纸质合同申领信息
        /// </summary>
        /// <param name="auditApply_In"></param>
        [HttpPost, PreLog]
        public void AuditContractPaperApply(AuditContractPaperApply_In auditApply_In)
        {
            BLL_ContractPaper.Instance.AuditContractPaperApply(auditApply_In);
        }

        /// <summary>
        /// 撤销审核纸质合同申领信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void RevokeAuditContractPaperApply(string Ids)
        {
            BLL_ContractPaper.Instance.RevokeAuditContractPaperApply(Ids);
        }

        /// <summary>
        /// 作废审核纸质合同申领信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void VoidAuditContractPaperApply(string Ids)
        {
            BLL_ContractPaper.Instance.VoidAuditContractPaperApply(Ids);
        }

        /// <summary>
        /// 获取纸质合同待分发列表数据
        /// </summary>
        /// <param name="distributeList_In"></param>
        [HttpPost, PreLog]
        public ApiTableOut<GetContractPaperTobeDistributeList_Out> GetContractPaperTobeDistributeList(GetContractPaperTobeDistributeList_In distributeList_In)
        {
            int total = 0;
            var list = BLL_ContractPaper.Instance.GetContractPaperTobeDistributeList(distributeList_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 待分发列表页面头部纸质合同申领明细的合同版本及实发份数
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public List<GetContractPaperTobeDistributeDetailList_Out> GetContractPaperTobeDistributeDetailList(string Id)
        {
            return DbOpe_crm_contract_paperapply_detail.Instance.GetContractPaperTobeDistributeDetailList(Id);
        }

        /// <summary>
        /// 根据申领Id获取已分发的纸质合同分发数据
        /// </summary>
        /// <param name="distributeList_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<GetContractPaperDistributeList_Out> GetContractPaperDistributeList(GetContractPaperDistributeList_In distributeList_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_paper_distribute.Instance.GetContractPaperDistributeList(distributeList_In, ref total);
            return GetApiTableOut(list, total);
        }


        /// <summary>
        /// 维护(新增保存或修改)纸质合同分发信息
        /// </summary>
        /// <param name="distributeList_In"></param>
        [HttpPost, PreLog]
        public void DistributeContractPaper(List<DistributeContractPaper_In> distributeList_In)
        {
            BLL_ContractPaper.Instance.DistributeContractPaper(distributeList_In);
        }


        /// <summary>
        /// 根据纸质合同申领信息Id获取纸质合同申领信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractPaperApplyInfoById_Out GetContractPaperApplyInfoByAuditId(string Id)
        {
            //根据审核Id-auditId获取申请Id-applyId
            var applyId = DbOpe_crm_contract_paperaudit.Instance.GetApplyIdByAuditId(Id);
            return DbOpe_crm_contract_paperapply.Instance.GetContractPaperApplyInfoById(applyId);
        }

        /// <summary>
        /// 根据纸质合同申领id获取合同信息列表
        /// </summary>
        /// <param name="serchCon_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<GetContractByContractPaperApplyId_Out> GetContractByContractPaperAuditId(GetContractByContractPaperApplyId_In serchCon_In)
        {
            //将search_In的Id由根据审核Id-auditId改为申请Id-applyId
            serchCon_In.Id = DbOpe_crm_contract_paperaudit.Instance.GetApplyIdByAuditId(serchCon_In.Id);
            int total = 0;
            var list = DbOpe_crm_contract_paperapply.Instance.GetContractByContractPaperApplyId(serchCon_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据纸质合同申领id获取纸质合同分发列表信息
        /// </summary>
        /// <param name="serchCon_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<GetContractPaperDistributeByContractPaperAuditId_Out> GetContractPaperDistributeByContractPaperAuditId(GetContractPaperDistributeByContractPaperAuditId_In serchCon_In)
        {
            
            int total = 0;
            var list = DbOpe_crm_contract_paperapply.Instance.GetContractPaperDistributeByContractPaperAuditId(serchCon_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 获取当前用户可以使用的纸质合同号
        /// </summary>
        [HttpPost, PreLog]
        public List<GetContractPaperEntitiesByRecipientId_Out> GetContractPaperEntitiesByRecipientId(string contractPaperEntityId)
        {
            return DbOpe_crm_contract_paper_distribute.Instance.GetContractPaperEntitiesByRecipientId(contractPaperEntityId);
        }



    }
}


