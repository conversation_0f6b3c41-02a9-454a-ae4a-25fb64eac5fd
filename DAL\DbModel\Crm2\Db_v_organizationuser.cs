﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_organizationuser")]
    public class Db_v_organizationuser
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string ParentId {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Id {get;set;}

           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ParentUserId {get;set;}

           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

    }
}
