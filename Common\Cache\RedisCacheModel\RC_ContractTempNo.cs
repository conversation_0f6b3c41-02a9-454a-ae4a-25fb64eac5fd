﻿namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class ContractTempNo
        {
            const string CONTRACTTEMPNO = "conTempNo_";
            public static List<string> CheckTempNo(List<string> tempNoList)
            {
                List<string> list = new List<string>();
                tempNoList.ForEach(tempNo =>
                {
                    if (RedisHelper.Exists(CONTRACTTEMPNO + tempNo))
                        list.Add(tempNo);
                });
                return list;
            }
            public static void SaveTempNo(string tempNo)
            {
                RedisHelper.Set(CONTRACTTEMPNO + tempNo, tempNo, TimeSpan.FromMinutes(60));
            }

            public static void SaveTempNoList(List<string> tempNoList)
            {
                tempNoList.ForEach(tempNo =>
                {
                    RedisHelper.Set(CONTRACTTEMPNO + tempNo, tempNo, TimeSpan.FromMinutes(60));
                });
            }
        }
        
    }
}
