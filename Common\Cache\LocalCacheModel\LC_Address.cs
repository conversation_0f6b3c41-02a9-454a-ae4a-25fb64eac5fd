﻿using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;

namespace CRM2_API.Common.Cache
{
    public partial class LocalCache//最外层必须用partial包着
    {
        /// <summary>
        /// 缓存例子
        /// </summary>
        public class LC_Address : ILocalCache
        {
            #region 设置缓存例子
            public static List<Country_OUT> CountryCache { private set; get; } = new List<Country_OUT>(0);//需要先实例化，否则下面lock报错
            public static List<CountryAndArea_OUT> CountryAndAreaCache { private set; get; } = new List<CountryAndArea_OUT>(0);//需要先实例化，否则下面lock报错
            public static List<Province_OUT> ProvinceCache { private set; get; } = new List<Province_OUT>(0);//需要先实例化，否则下面lock报错
            public static List<City_OUT> CityCache { private set; get; } = new List<City_OUT>(0);//需要先实例化，否则下面lock报错



            public void SetCache()
            {
                lock (CountryCache)//一定要锁这个对象，否则出现使用脏数据的情况
                {
                    CountryCache = DbOpe_sys_country.Instance.GetData();
                }
                lock (CountryAndAreaCache)//一定要锁这个对象，否则出现使用脏数据的情况
                {
                    CountryAndAreaCache = DbOpe_sys_country.Instance.GetData()
                        .MappingTo<List<CountryAndArea_OUT>>();
                    var province_OUTs = DbOpe_sys_province.Instance.GetData()
                        .Where(e => e.Name == "香港" || e.Name == "澳门" || e.Name == "台湾省").Select(e => new CountryAndArea_OUT
                        {
                            Id = e.Id,
                            Name = e.Name,
                            NameEN = ""
                        }).ToList();
                    CountryAndAreaCache.AddRange(province_OUTs);
                }
                lock (ProvinceCache)//一定要锁这个对象，否则出现使用脏数据的情况
                {
                    ProvinceCache = DbOpe_sys_province.Instance.GetData();
                }
                lock (CityCache)//一定要锁这个对象，否则出现使用脏数据的情况
                {
                    CityCache = DbOpe_sys_city.Instance.GetData();
                }
            }
            #endregion
        }
    }
}
