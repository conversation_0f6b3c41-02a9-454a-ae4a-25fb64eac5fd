﻿namespace CRM2_API.Common.AppSetting
{
    public class Setting_WeChatConfig
    {
        public string AppID { get; set; }
        public string AppSecret { get; set; }
        public string grant_type { get; set; }
        public string EncodingAESKey { get; set; }
        public string access_token_url { get; set; }
        public string token { get; set; }
        public string at_time { get; set; }
        public string send_url { get; set; }
        public string access_token { get; set; }

        /// <summary>
        /// 会议提醒
        /// </summary>
        public string TempleteID1 { get; set; }
        /// <summary>
        /// 会议提醒
        /// </summary>
        public string TempleteID2 { get; set; }
        /// <summary>
        /// 数据更新提醒
        /// </summary>
        public string TempleteID_DataUpdate { get; set; }
        /// <summary>
        /// 会议参加提醒
        /// </summary>
        public string TempleteID3 { get; set; }
        /// <summary>
        /// 预定业务成功通知
        /// </summary>
        public string TempleteID_Reserve { get; set; }

        public string message_url { get; set; }

        public string message_id { get; set; }
    }
}
