﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Mvc.Filters;
using SqlSugar;

namespace CRM2_API.Common.Filter
{
    public class WorkLog
    {
        /// <summary>
        /// 接口进入前调用filter
        /// </summary>
        [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
        public class PreLogAttribute : Attribute, IActionFilter
        {
            /// <summary>
            /// 出接口之前
            /// </summary>
            /// <param name="context"></param>
            /// <exception cref="NotImplementedException"></exception>
            public void OnActionExecuted(ActionExecutedContext context)
            {
                var workLog = Com_WorkLog.Instance;
                if (context.Exception != null)
                {
                    workLog.MethodResult = false;
                    workLog.ExceptionContent = context.Exception.Message.ToString();
                }
                else
                    workLog.MethodResult = true;
                var sysUser = RedisCache.UserInfo.GetUserInfo(TokenModel.Instance.id);
                DbOpe_sys_operation_log.Instance.UpdateOnActionExecuted(workLog, sysUser);
            }

            /// <summary>
            /// 进入接口
            /// </summary>
            /// <param name="context"></param>
            /// <exception cref="NotImplementedException"></exception>
            public void OnActionExecuting(ActionExecutingContext context)
            {
                //判断接口是否跳过身份验证
                var isSkipAuthCheck = context.Filters.Any(f => f is SkipAuthCheckAttribute);
                //创建log数据
                Db_sys_operation_log sysOpeLog = new Db_sys_operation_log();
                if (isSkipAuthCheck)
                {
                    //获取接口的ControllerName
                    var controllerName = context.ActionDescriptor.DisplayName.Split('.')[2].ToString().Replace("Controller", "");
                    //获取接口的ActionName
                    var methodName = context.ActionDescriptor.DisplayName.Split('.')[3].Replace(" (CRM2_API)", "");
                    sysOpeLog.FormId = DbOpe_sys_form.Instance.GetSysForm(controllerName, methodName).Id;
                }
                else
                {
                    //对应formId
                    sysOpeLog.FormId = Com_SysForm.Instance.FormId;
                }
                
                //创建时间
                sysOpeLog.OperationDate = DateTime.Now;
                //日志主键Guid
                sysOpeLog.Id = Guid.NewGuid().ToString();
                //操作人使用设备环境
                sysOpeLog.OperationEnv = NetUtil.GetUserAgentDetail(context.HttpContext.Request.Headers.UserAgent).IsComputer ? EnumOpeEnv.Web : EnumOpeEnv.Mobile;
                //操作人使用ip地址
                sysOpeLog.OperationContent = "登录IP:" + NetUtil.GetIp_Nginx(context.HttpContext);
                //创建日志
                Com_WorkLog.SetWorkLog(sysOpeLog);
            }

            private bool IsMobileDevice(string userAgent)
            {
                string[] mobileAgents = { "iphone", "android", "phone", "mobile", "wap", "netfront", "java", "opera mobi", "opera mini", "ucweb", "windows ce", "symbian", "series", "webos", "sony", "blackberry", "dopod", "nokia", "samsung", "palmsource", "xda", "pieplus", "meizu", "midp", "cldc", "motorola", "foma", "docomo", "up.browser", "up.link", "blazer", "helio", "hosin", "huawei", "novarra", "coolpad", "webos", "techfaith", "palmsource", "alcatel", "amoi", "ktouch", "nexian", "ericsson", "philips", "sagem", "wellcom", "bunjalloo", "maui", "smartphone", "iemobile", "spice", "bird", "zte-", "longcos", "pantech", "gionee", "portalmmm", "jig browser", "hiptop", "benq", "haier", "^lct", "320x320", "240x320", "176x220", "w3c ", "acs-", "alav", "alca", "amoi", "audi", "avan", "benq", "bird", "blac", "blaz", "brew", "cell", "cldc", "cmd-", "dang", "doco", "eric", "hipt", "inno", "ipaq", "java", "jigs", "kddi", "keji", "leno", "lg-c", "lg-d", "lg-g", "lge-", "maui", "maxo", "midp", "mits", "mmef", "mobi", "mot-", "moto", "mwbp", "nec-", "newt", "noki", "oper", "palm", "pana", "pant", "phil", "play", "port", "prox", "qwap", "sage", "sams", "sany", "sch-", "sec-", "send", "seri", "sgh-", "shar", "sie-", "siem", "smal", "smar", "sony", "sph-", "symb", "t-mo", "teli", "tim-", "tosh", "tsm-", "upg1", "upsi", "vk-v", "voda", "wap-", "wapa", "wapi", "wapp", "wapr", "webc", "winw", "winw", "xda", "xda-", "googlebot-mobile" };

                bool isMoblie = false;
                //排除 Windows 桌面系统或苹果桌面系统 
                if (!string.IsNullOrEmpty(userAgent) && !userAgent.Contains("macintosh") && (!userAgent.Contains("windows nt") || (userAgent.Contains("windows nt") && userAgent.Contains("compatible; msie 9.0;"))))
                {
                    for (int i = 0; i < mobileAgents.Length; i++)
                    {
                        if (userAgent.ToLower().IndexOf(mobileAgents[i]) >= 0)
                        {
                            isMoblie = true;
                            break;
                        }
                    }
                }
                return isMoblie;
            }
        }


    }





    /// <summary>
    /// 接口完成后调用Filter
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class PostLogAttribute : Attribute, IResultFilter
    {
        public void OnResultExecuted(ResultExecutedContext context)
        {

        }

        public void OnResultExecuting(ResultExecutingContext context)
        {

        }
    }

}

