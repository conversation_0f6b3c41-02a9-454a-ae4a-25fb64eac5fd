using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.ServiceOpening;
using System;
using System.Threading.Tasks;

namespace CRM2_API.Common.Utils
{
    /// <summary>
    /// 外部系统调用日志助手类
    /// </summary>
    public static class ExternalSystemCallLogger
    {
        /// <summary>
        /// 开始记录外部系统调用
        /// </summary>
        /// <param name="callModel">调用模型</param>
        /// <returns>调用记录ID</returns>
        public static string StartCall(ExternalSystemCallModel callModel)
        {
            try
            {
                var logId = Guid.NewGuid().ToString();
                var logEntity = new Db_crm_external_system_call_log
                {
                    Id = logId,
                    BusinessType = callModel.BusinessType.ToString(),
                    BusinessId = callModel.BusinessId,
                    ContractId = callModel.ContractId,
                    ContractCode = callModel.ContractCode,
                    SystemName = callModel.SystemName.ToString(),
                    ApiMethod = callModel.ApiMethod,
                    ApiUrl = callModel.ApiUrl,
                    RequestHeaders = callModel.RequestHeadersJson,
                    RequestParams = callModel.RequestParamsJson,
                    CallStartTime = DateTime.Now,
                    IsSuccess = false, // 开始时设为false，完成时更新
                    CreateUser = callModel.CreateUser,
                    CreateDate = DateTime.Now
                };

                var dbOpe = new DbOpe_crm_external_system_call_log();
                dbOpe.Insert(logEntity);

                return logId;
            }
            catch (Exception ex)
            {
                // 记录日志失败不应该影响业务流程
                Console.WriteLine($"记录外部系统调用开始日志失败: {ex.Message}");
                return Guid.NewGuid().ToString(); // 返回一个临时ID，避免后续更新时出错
            }
        }

        /// <summary>
        /// 完成外部系统调用记录
        /// </summary>
        /// <param name="callId">调用ID</param>
        /// <param name="result">调用结果</param>
        public static void CompleteCall(string callId, ExternalSystemCallResult result)
        {
            try
            {
                var dbOpe = new DbOpe_crm_external_system_call_log();
                var logEntity = dbOpe.GetDataById(callId);
                
                if (logEntity != null)
                {
                    logEntity.IsSuccess = result.IsSuccess;
                    logEntity.ResponseStatus = result.ResponseStatus;
                    logEntity.ResponseData = result.ResponseData;
                    logEntity.ErrorMessage = result.ErrorMessage;
                    logEntity.CallEndTime = result.CallEndTime ?? DateTime.Now;
                    logEntity.Duration = result.Duration;
                    logEntity.UpdateUser = logEntity.CreateUser;
                    logEntity.UpdateDate = DateTime.Now;

                    dbOpe.Update(logEntity);
                }
            }
            catch (Exception ex)
            {
                // 记录日志失败不应该影响业务流程
                Console.WriteLine($"完成外部系统调用日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录异步外部系统调用
        /// </summary>
        /// <param name="callModel">调用模型</param>
        /// <param name="callAction">调用操作</param>
        /// <returns>调用结果</returns>
        public static async Task<ExternalSystemCallResult> LogCallAsync(
            ExternalSystemCallModel callModel, 
            Func<Task<ExternalSystemCallResult>> callAction)
        {
            var callId = StartCall(callModel);
            var result = new ExternalSystemCallResult
            {
                CallId = callId,
                CallStartTime = DateTime.Now
            };

            try
            {
                result = await callAction();
                result.CallId = callId;
            }
            catch (Exception ex)
            {
                result.SetError($"调用异常: {ex.Message}");
            }
            finally
            {
                CompleteCall(callId, result);
            }

            return result;
        }

        /// <summary>
        /// 记录同步外部系统调用
        /// </summary>
        /// <param name="callModel">调用模型</param>
        /// <param name="callAction">调用操作</param>
        /// <returns>调用结果</returns>
        public static ExternalSystemCallResult LogCall(
            ExternalSystemCallModel callModel, 
            Func<ExternalSystemCallResult> callAction)
        {
            var callId = StartCall(callModel);
            var result = new ExternalSystemCallResult
            {
                CallId = callId,
                CallStartTime = DateTime.Now
            };

            try
            {
                result = callAction();
                result.CallId = callId;
            }
            catch (Exception ex)
            {
                result.SetError($"调用异常: {ex.Message}");
            }
            finally
            {
                CompleteCall(callId, result);
            }

            return result;
        }

        /// <summary>
        /// 清理过期日志
        /// </summary>
        /// <param name="daysToKeep">保留天数，默认90天</param>
        /// <returns>清理的记录数</returns>
        public static int CleanupExpiredLogs(int daysToKeep = 90)
        {
            try
            {
                var dbOpe = new DbOpe_crm_external_system_call_log();
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                
                // 获取需要删除的记录
                var expiredLogs = dbOpe.GetDataList(x => x.CreateDate < cutoffDate);
                
                // TODO: 实现删除逻辑
                return expiredLogs.Count;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理过期外部系统调用日志失败: {ex.Message}");
                return 0;
            }
        }
    }
} 