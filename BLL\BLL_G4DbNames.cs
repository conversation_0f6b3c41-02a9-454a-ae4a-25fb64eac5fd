﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Spreadsheet;
using LgyUtil;
using System.Linq;
using static CRM2_API.Model.BLLModel.Enum.G4DBEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.BLL
{
    public class BLL_G4DbNames : BaseBLL<BLL_G4DbNames>
    {
        /// <summary>
        /// 获取Gtis国家信息 （同时调用gtis系统尝试进行国家同步）
        /// </summary>
        public List<G4DbNames> GetG4DbNames(string productId = "")
        {
            if (string.IsNullOrEmpty(productId))
            {
                //TryGetGtisG4DbNames();
                return DbOpe_sys_g4_dbnames.Instance.GetG4DbNamesInfo(EnumG4DBPrams.Default);
            }
            else
            {
                var gtisProductType = DbOpe_crm_product_gtis_relation.Instance.GetData(d => d.ProductId == productId);
                if (gtisProductType == null)
                {
                    throw new ApiException("该服务产品与GTIS系统连接未配置");
                }
                else
                {
                    switch (gtisProductType.GTIS_ProductCustomerType)
                    {
                        case 1:
                            //零售
                            return new List<G4DbNames>();
                        case 2:
                            if (gtisProductType.Remark == "1")
                            {
                                //出口
                                // return DbOpe_sys_g4_dbnames.Instance.GetDataList(d=>d.CType == 1 && d.IsGtisDeleted == false).ToList().MappingTo<List<G4DbNames>>();
                                return DbOpe_sys_g4_dbnames.Instance.GetG4DbNamesInfo(EnumG4DBPrams.Import);
                            }
                            else
                            {
                                //进口+中国出口统计
                                //return DbOpe_sys_g4_dbnames.Instance.GetDataList(d => (d.CType == 0 || d.SID == 603) && d.IsGtisDeleted == false).ToList().MappingTo<List<G4DbNames>>();
                                return DbOpe_sys_g4_dbnames.Instance.GetG4DbNamesInfo(EnumG4DBPrams.Export);
                            }
                        default:
                            //return DbOpe_sys_g4_dbnames.Instance.GetDataList(d => d.IsGtisDeleted == false).ToList().MappingTo<List<G4DbNames>>();
                            return DbOpe_sys_g4_dbnames.Instance.GetG4DbNamesInfo(EnumG4DBPrams.Default);
                    }
                }
            }
        }
        public void TryGetGtisG4DbNames()
        {
            string dbNamesKey = "g4dbname";
            //刷新间隔 1:1分钟内只会刷新一次
            int refreshSpan = 1;
            try
            {
                //获取Redis里的刷新标记
                string flag = RedisHelper.Get(dbNamesKey);
                if (!string.IsNullOrEmpty(flag))
                {
                    //1分钟内刷新过，不再刷新了
                    return;
                }
                DbOpe_sys_g4_dbnames.Instance.TransDeal(() =>
                {
                    //获数据取库里所有国家
                    var exists = DbOpe_sys_g4_dbnames.Instance.GetDataAllList().ToList();
                    //获取gtis里所有g4国家
                    var gtis = BLL_GtisOpe.Instance.GetGtisAllSids().Result;
                    //再把数据库里国家都标记为删除
                    //DbOpe_sys_g4_dbnames.Instance.DeleteDataList(new List<Db_sys_g4_dbnames>() { }, exists.Select(e => e.Id).ToList());
                    foreach (var d in gtis)
                    {
                        var exist = exists.Find(e => e.SID == d.SID);
                        if (exist == null)
                        {
                            var newObj = d.MappingTo<Db_sys_g4_dbnames>();
                            newObj.Id = Guid.NewGuid().ToString();
                            newObj.IsGtisDeleted = false;
                            DbOpe_sys_g4_dbnames.Instance.Insert(newObj);
                        }
                        else
                        {
                            d.MappingTo(exist);
                            exist.IsGtisDeleted = false;
                            DbOpe_sys_g4_dbnames.Instance.Update(exist);
                        }
                    }
                    var GtisDeletedSids = new List<int>();
                    foreach (var exist in exists)
                    {
                        var egtis = gtis.Find(s => s.SID == exist.SID);
                        if (egtis == null)
                        {
                            exist.IsGtisDeleted = true;
                            DbOpe_sys_g4_dbnames.Instance.Update(exist);
                            //获取删除的sid集合
                            GtisDeletedSids.Add(exist.SID.GetValueOrDefault(0));
                        }
                    }

                    //删除临时账号系统模板中的Sid
                    var sysTscountryList = DbOpe_sys_tscountry.Instance.GetDataAllList();
                    sysTscountryList.ForEach(sysTscountry =>
                    {
                        var tsSids = sysTscountry.Sid.Split(',').ToList().MappingTo<List<int>>();
                        GtisDeletedSids.ForEach(removeSid =>
                        {
                            var index = tsSids.IndexOf(removeSid);
                            if(index != -1)
                                tsSids.RemoveAt(index);
                        });
                        sysTscountry.Sid = String.Join(",", tsSids);
                        DbOpe_sys_tscountry.Instance.Update(sysTscountry);
                    });
                    //删除临时账号个人模板中的sid
                    DbOpe_sys_usertscountry_details.Instance.RemoveGtisDeletedSids(GtisDeletedSids);
                });

                //Redis记录刷新标记
                RedisHelper.Set(dbNamesKey, DateTime.Now.ToString(), TimeSpan.FromMinutes(refreshSpan));
            }
            catch
            {
                Console.WriteLine("同步G4DbNames失败");
            }

        }
    }
}
