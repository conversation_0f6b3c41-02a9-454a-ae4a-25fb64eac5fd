﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_subcompany_related")]
    public class Db_v_subcompany_related
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string Id {get;set;}

           /// <summary>
           /// Desc:客户表id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string CustomerId {get;set;}

           /// <summary>
           /// Desc:公司名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CompanyName {get;set;}

           /// <summary>
           /// Desc:信用代码
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreditCode {get;set;}

           /// <summary>
           /// Desc:公司名称短描
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CompanyNameDM {get;set;}
           /// <summary>
           /// Desc:客户名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CustomerName {get;set;}

           /// <summary>
        /// Desc:企业类型 0：其他地区企业 1：中国大陆企业 2：个人
        /// Default:0
        /// Nullable:False
        /// </summary>           
        public int CreditType { get; set; }

           /// <summary>
           /// Desc:是否保密：0：否 1：是
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? IsSecret {get;set;}

           /// <summary>
           /// Desc:联系人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Contacts {get;set;}

           /// <summary>
           /// Desc:职务
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Job {get;set;}

           /// <summary>
           /// Desc:联系方式
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContactWay {get;set;}

           /// <summary>
           /// Desc:固定电话
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Telephone {get;set;}

           /// <summary>
           /// Desc:传真号码
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Fax {get;set;}

           /// <summary>
           /// Desc:邮箱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Email {get;set;}

           /// <summary>
           /// Desc:详细地址-国家
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int Country {get;set;}

           /// <summary>
           /// Desc:详细地址-省
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int Province {get;set;}

           /// <summary>
           /// Desc:详细地址-市
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int City {get;set;}

           /// <summary>
           /// Desc:详细地址-详细地址
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Address {get;set;}

           /// <summary>
           /// Desc:是否有效 1:有效 0：无效	
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int IsValid {get;set;}

           /// <summary>
           /// Desc:是否主公司 1:是 0：否
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int IsMain {get;set;}

           /// <summary>
           /// Desc:是否是历史数据
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? IsHistory {get;set;}

           /// <summary>
           /// Desc:是否可以编辑名称 1：是 0：否
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool CanEdit {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int Deleted {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Relateds {get;set;}
           public string RelatedDMs { get;set;}

    }
}
