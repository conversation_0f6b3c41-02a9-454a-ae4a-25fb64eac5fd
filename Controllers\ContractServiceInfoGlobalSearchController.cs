﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;

using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    [Description("合同服务管理-环球搜")]
    public class ContractServiceInfoGlobalSearchController : MyControllerBase
    {
        public ContractServiceInfoGlobalSearchController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 根据查询条件获取合同服务信息环球搜申请信息列表
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchContractProductServiceInfoGlobalSearchApplList_Out> SearchContractProductServiceInfoGlobalSearchApplList(SearchContractProductServiceInfoGlobalSearchApplList_In search_In)
        {
            int total = 0;
            //var list = BLL_ContractServiceGlobalSearch.Instance.SearchContractProductServiceInfoGlobalSearchApplList(search_In, ref total);
            var list = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.SearchContractProductServiceInfoGlobalSearchApplList(search_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 异步获取环球搜账号状态
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public List<GetGlobalSearchCodeStateAsync_Out> GetGlobalSearchCodeStateAsync(List<GetGlobalSearchCodeStateAsync_In> search_In)
        {
            return BLL_ContractServiceGlobalSearch.Instance.GetGlobalSearchCodeStateAsync(search_In);
        }

        /// <summary>
        /// 根据申请ID获取环球搜开通账号信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public List<GetContractServiceInfoGlobalSearchUserListByApplId_Out> GetContractServiceInfoGlobalSearchUserListByApplId(string Id)
        {
            return BLL_ContractServiceGlobalSearch.Instance.GetContractServiceInfoGlobalSearchUserListByApplId(Id);
            //return DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetContractServiceInfoGlobalSearchUserListByApplId(Id);
        }

        /// <summary>
        /// 根据申请id获取合同服务申请信息_环球搜信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoApplyInfoGlobalSearchByApplId_Out GetContractServiceInfoApplyInfoGlobalSearchByApplId(string Id)
        {
            return BLL_ContractServiceGlobalSearch.Instance.GetContractServiceInfoApplyInfoGlobalSearchByApplId(Id);
        }

        /// <summary>
        /// 撤销合同服务信息环球搜申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void RevokeContractProductServiceInfoGlobalSearchAudit(OperateContractProductServiceInfoGlobalSearchAudit_In operate_In)
        {
            BLL_ContractServiceGlobalSearch.Instance.RevokeContractProductServiceInfoGlobalSearchAudit(operate_In);
        }

        /// <summary>
        /// 作废合同服务信息环球搜申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void VoidContractProductServiceInfoGlobalSearchAudit(OperateContractProductServiceInfoGlobalSearchAudit_In operate_In)
        {
            BLL_ContractServiceGlobalSearch.Instance.VoidContractProductServiceInfoGlobalSearchAudit(operate_In);
        }

        /// <summary>
        /// 删除合同服务信息环球搜申请信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void DeleteContractProductServiceInfoGlobalSearchAudit(OperateContractProductServiceInfoGlobalSearchAudit_In operate_In)
        {
            BLL_ContractServiceGlobalSearch.Instance.DeleteContractProductServiceInfoGlobalSearchAudit(operate_In);
        }

        /// <summary>
        /// 设置环球搜用户状态信息
        /// </summary>
        /// <param name="userList_In"></param>
        [HttpPost, PreLog]
        public void SetContractServiceInfoGlobalSearchUser(List<SetContractServiceInfoGlobalSearchUser_In> userList_In)
        {
            BLL_ContractServiceGlobalSearch.Instance.SetContractServiceInfoGlobalSearchUser(userList_In);
        }

        /// <summary>
        /// 根据申请Id批量修改服务用户状态
        /// </summary>
        /// <param name="applyList_In"></param>
        [HttpPost, PreLog]
        public void SetContractServiceInfoGlobalSearchUserByApplyIds(SetContractServiceInfoGlobalSearchUserByApplyIds_In applyList_In)
        {
            BLL_ContractServiceGlobalSearch.Instance.SetContractServiceInfoGlobalSearchUserByApplyIds(applyList_In);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息_环球搜信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoGlobalSearchByApplId_Out GetContractServiceInfoGlobalSearchByApplId(string Id)
        {
            return BLL_ContractServiceGlobalSearch.Instance.GetContractServiceInfoGlobalSearchByApplId(Id);
        }

        /// <summary>
        /// 根据合同产品信息表id获取合同服务信息_环球搜信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoGlobalSearchByContractProductInfoId_Out GetContractServiceInfoGlobalSearchByContractProductInfoId(string Id)
        {
            return DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetContractServiceInfoGlobalSearchByContractProductInfoId(Id);
        }

        /// <summary>
        /// 验证当前申请的合同款是否到账
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public bool CheckGlobalSearchApplAchievement(string contractId)
        {
            return DbOpe_crm_contract_receiptregister.Instance.GetConfirmedContractReceiptRegisterByContractId(contractId).Count == 0;
        }

        [HttpPost, PreLog]
        public void PresentedGlobalSearchService(PresentedGlobalSearchService_In present_In)
        {
            BLL_ContractServiceGlobalSearch.Instance.PresentedGlobalSearchService(present_In);
        }

        /// <summary>
        /// 获取被续约合同的环球搜服务信息
        /// </summary>
        /// <param name="getOldGlobalSearchServiceInfo_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetOldGlobalSearchServiceInfo_Out? GetOldGlobalSearchServiceInfo(GetOldGlobalSearchServiceInfo_In getOldGlobalSearchServiceInfo_In)
        {
            return BLL_ContractServiceGlobalSearch.Instance.GetOldGlobalSearchServiceInfo(getOldGlobalSearchServiceInfo_In);
        }


        /// <summary>
        /// 记录账号密码发送状态
        /// </summary>
        /// <param name="recordSendUserPwd_In"></param>
        [HttpPost, PreLog]
        public void RecordSendUserPwd(RecordSendUserPwd_In recordSendUserPwd_In)
        {
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.RecordSendUserPwd(recordSendUserPwd_In);
        }

        /// <summary>
        /// 根据查询条件获取合同服务信息环球搜已开通的编码信息
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost]
        public Stream DownloadGlobalSearchCodeInfo(DownloadGlobalSearchCodeInfo_In search_In)
        {
            return BLL_ContractServiceGlobalSearch.Instance.DownloadGlobalSearchCodeInfo(search_In);
            /*var list = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetDownloadGlobalSearchCodeInfo(search_In);
            var exporter = new ExcelExporter();
            var result = await exporter.ExportAsByteArray<DownloadGlobalSearchCodeInfoExcelDto>(list);
            var fs = new MemoryStream(result);
            return fs;*/
        }


        /// <summary>
        /// 变更或续约的环球搜服务数据的修改类型
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public void HistoryGlobalSearchModifyType()
        {
            BLL_ContractServiceGlobalSearch.Instance.HistoryGlobalSearchModifyType();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.bushuju();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
        }

        [HttpPost]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public int UpdateSingleGlobalSearchData()
        {
            return DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateSingleGlobalSearchData();
            //return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.bushuju();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
        }


        /// <summary>
        /// 处理执行脚本关联后，仍然缺少GlobalSearchSerivceId的gtis_user数据
        /// </summary>
        [HttpPost]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public void DealGtisUserMissServiceId()
        {
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.DealGtisUserMissServiceId();
            //return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.bushuju();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
        }

        [HttpPost]
        [SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public void SynchroGlobalUserData()
        {
            BLL_ContractServiceGlobalSearch.Instance.SynchroGlobalUserData();
        }


        [HttpPost]
        [SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        [SkipAuthCheck]
        public void SupplementGlobalSearchAccount20250407()
        {
            BLL_ContractServiceGlobalSearch.Instance.SupplementGlobalSearchAccount20250407();
        }

        
    }
}