﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_HsCode;

namespace CRM2_API.Controllers
{
    [Description("海关编码")]
    public class HsCodeController : MyControllerBase
    {
        public HsCodeController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 根据查询条件获取海关编码列表
        /// </summary>
        /// <param name="searchHsCodeListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchHsCodeList_Out> SearchHsCodeList(SearchHsCodeList_In searchHsCodeListIn)
        {
            return BLL_HsCode.Instance.SearchHsCodeList(searchHsCodeListIn);
        }

        /// <summary>
        /// 上传海关编码
        /// </summary>
        /// <param name="uploadHsCode_In"></param>
        /// <returns></returns>
        [HttpPost]
        public List<HsCode_Out> UploadHsCode([FromForm] UploadHsCode_In uploadHsCode_In)
        {
            return BLL_HsCode.Instance.UploadHsCode(uploadHsCode_In);
        }

        /// <summary>
        /// 添加海关编码
        /// </summary>
        /// <param name="hsCode_In"></param>
        /// <returns></returns>
        [HttpPost]
        public void AddHsCode(List<HsCode_In> hsCode_In)
        {
            BLL_HsCode.Instance.AddHsCode(hsCode_In);
        }

        /// <summary>
        /// 上传海关编码计划
        /// </summary>
        /// <param name="uploadHsCodePlanned_In"></param>
        /// <returns></returns>
        [HttpPost]
        public List<HsCodePlanned_Out> UploadHsCodePlanned([FromForm] UploadHsCodePlanned_In uploadHsCodePlanned_In)
        {
            return BLL_HsCode.Instance.UploadHsCodePlanned(uploadHsCodePlanned_In);
        }

        /// <summary>
        /// 添加海关编码计划
        /// </summary>
        /// <param name="hsCodePlanned_In"></param>
        /// <returns></returns>
        [HttpPost]
        public void AddHsCodePlanned(List<HsCodePlanned_In> hsCodePlanned_In)
        {
            BLL_HsCode.Instance.AddHsCodePlanned(hsCodePlanned_In);
        }

        /// <summary>
        /// 获取海关编码国家
        /// </summary>
        /// <param name="ProductId"></param>
        /// <returns></returns>
        [HttpPost]
        public List<HsCodeCountry_Out> GetHsCodeCountryListByProductId(string ProductId)
        {
            return BLL_HsCode.Instance.GetHsCodeCountryListByProductId(ProductId);
        }

        /// <summary>
        /// 获取海关编码国家
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<HsCodeCountry_Out> GetHsCodeCountryList()
        {
            return BLL_HsCode.Instance.GetHsCodeCountryList();
        }
    }
}
