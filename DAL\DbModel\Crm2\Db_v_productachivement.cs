﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_productachivement")]
    public class Db_v_productachivement
    {
        public string ProductAchivementId { get; set; }
        public string ProductId { get; set; }
        public string ProductName { get; set; }
        public int? TypeKey { get; set; }
        public int? ChargingParameters { get; set; }
        public int CountryCount { get; set; }
        public int AccountCount { get; set; }
        public int CodeCount { get; set; }
        public int SetCount { get; set; }
        public decimal PerValue { get; set; }
        public decimal SumValue { get; set; }

    }
}
