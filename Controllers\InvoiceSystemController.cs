using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.ControllersViewModel.Common;
using System.ComponentModel;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System.IO;
using CRM2_API.Model.System;
using CRM2_API.Common.Filter;
using CRM2_API.Model.BusinessModel;
using CRM2_API.BLL.InvoiceSystem;
using static CRM2_API.Model.BLLModel.InvoiceSystem.VM_InvoiceSystem;
using CRM2_API.Model.ControllersViewModel.InvoiceSystem;
using System.Web;
using System.Text;
using CRM2_API.DAL.DbModelOpe.Crm2;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 发票系统控制器
    /// </summary>
    [Description("发票系统")]
    public class InvoiceSystemController : MyControllerBase
    {
        public InvoiceSystemController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        #region 正式票（包括退票）的查询统计相关接口
        /// <summary>
        /// 从发票角度查询发票记录
        /// </summary>
        /// <param name="param">查询条件</param>
        /// <returns>发票查询结果</returns>
        [HttpPost]
        public ApiTableOut<InvoiceQueryResult_Out> SearchInvoiceByConditions(InvoiceQuery_In param)
        {
            try
            {
                // 参数验证
                if (param == null)
                {
                    throw new ApiException("查询参数不能为空");
                }

                // 调用BLL层方法查询发票记录
                return BLL_ContractInvoiceNew.Instance.SearchInvoiceByConditions(param);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"查询发票记录业务异常: {ex.Message}", this.GetType().FullName, "SearchInvoiceByConditions");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"查询发票记录系统异常: {ex.Message}", this.GetType().FullName, "SearchInvoiceByConditions");
                throw new ApiException("系统异常，请联系管理员");
            }
        }
        /// <summary>
        /// 发票统计
        /// </summary>
        /// <param name="param">统计查询条件</param>
        /// <returns>发票统计结果</returns>
        [HttpPost]
        public InvoiceStatistics_Out GetInvoiceDetailStatistics(InvoiceStatisticsQuery_In param)
        {
            try
            {
                // 调用BLL层方法进行发票统计
                return BLL_ContractInvoiceNew.Instance.GetInvoiceDetailStatistics(param);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"发票统计业务异常: {ex.Message}", this.GetType().FullName, "GetInvoiceDetailStatistics");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"发票统计系统异常: {ex.Message}", this.GetType().FullName, "GetInvoiceDetailStatistics");
                throw new ApiException("系统异常，请联系管理员");
            }
        }
        /// <summary>
        /// 从到账角度查询发票记录
        /// </summary>
        /// <param name="param">查询条件</param>
        /// <returns>到账发票查询结果</returns>
        [HttpPost]
        public ApiTableOut<ReceiptInvoiceQueryResult_Out> SearchReceiptInvoiceByConditions(ReceiptInvoiceQuery_In param)
        {
            try
            {
                // 参数验证
                if (param == null)
                {
                    throw new ApiException("查询参数不能为空");
                }

                // 调用BLL层方法查询到账发票记录
                return BLL_ContractInvoiceNew.Instance.SearchReceiptInvoiceByConditions(param);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"查询到账发票记录业务异常: {ex.Message}", this.GetType().FullName, "SearchReceiptInvoiceByConditions");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"查询到账发票记录系统异常: {ex.Message}", this.GetType().FullName, "SearchReceiptInvoiceByConditions");
                throw new ApiException("系统异常，请联系管理员");
            }
        }

        /// <summary>
        /// 到账发票统计
        /// </summary>
        /// <param name="param">统计查询条件</param>
        /// <returns>到账发票统计结果</returns>
        [HttpPost]
        public ReceiptInvoiceStatistics_Out GetReceiptInvoiceStatistics(ReceiptInvoiceStatisticsQuery_In param)
        {
            try
            {
                // 参数验证
                if (param == null)
                {
                    throw new ApiException("统计参数不能为空");
                }

                // 调用BLL层方法进行到账发票统计
                return BLL_ContractInvoiceNew.Instance.GetReceiptInvoiceDetailStatistics(param);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"到账发票统计业务异常: {ex.Message}", this.GetType().FullName, "GetReceiptInvoiceStatistics");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"到账发票统计系统异常: {ex.Message}", this.GetType().FullName, "GetReceiptInvoiceStatistics");
                throw new ApiException("系统异常，请联系管理员");
            }
        }
        #endregion

        #region 发票申请相关接口

        /// <summary>
        /// 创建发票申请
        /// </summary>
        /// <param name="request">发票申请请求</param>
        /// <returns>申请ID</returns>
        [HttpPost]
        public string CreateInvoiceApplication([FromForm] CreateInvoiceApplicationRequest request)
        {
            try
            {
                // 调用BLL层的发票申请业务方法
                return BLL_ContractInvoiceNew.Instance.CreateInvoiceApplication(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"创建发票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"创建发票申请系统异常: {ex.Message}");
                throw new ApiException("创建发票申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 作废发票申请
        /// </summary>
        /// <param name="request">作废发票申请请求</param>
        /// <returns>是否作废成功</returns>
        [HttpPost]
        public bool InvalidateInvoiceApplication(InvalidateInvoiceApplicationRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.Id))
                {
                    throw new ApiException("发票申请ID不能为空");
                }

                // 调用BLL层的发票作废业务方法
                return BLL_ContractInvoiceNew.Instance.InvalidateInvoiceApplication(request.Id);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"作废发票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"作废发票申请系统异常: {ex.Message}");
                throw new ApiException("作废发票申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 更新发票申请
        /// </summary>
        /// <param name="request">更新发票申请请求</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        public bool UpdateInvoiceApplication([FromForm] UpdateInvoiceApplicationRequest request)
        {
            try
            {
                // 调用BLL层的发票更新业务方法
                return BLL_ContractInvoiceNew.Instance.UpdateInvoiceApplication(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"更新发票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"更新发票申请系统异常: {ex.Message}");
                throw new ApiException("更新发票申请失败，请联系系统管理员");
            }
        }



        #endregion

        #region 发票审核相关接口

        /// <summary>   
        /// 更新发票备注（开具发票）
        /// </summary>
        /// <param name="request">更新发票备注请求</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        public void UpdateInvoiceRemarkByApplicationId(UpdateInvoiceRemarkRequest request)
        {
            BLL_ContractInvoiceNew.Instance.UpdateInvoiceRemarkByApplicationId(request.InvoiceApplicationId, request.Remark);
        }

        /// <summary>   
        /// 更新发票备注(到账开票查询/开票查询)
        /// </summary>
        /// <param name="request">更新发票备注请求</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        public void UpdateInvoiceRemark(UpdateInvoiceRemarkByInvoiceIdRequest request)
        {
            BLL_ContractInvoiceNew.Instance.UpdateInvoiceRemark(request.InvoiceId, request.Remark);
        }

        /// <summary>
        /// 审核发票申请
        /// </summary>
        /// <param name="request">审核请求</param>
        /// <returns>审核结果</returns>
        [HttpPost]
        public bool AuditInvoiceApplication(AuditInvoiceApplicationRequest request)
        {
            try
            {
                // 调用BLL层的发票审核业务方法
                return BLL_ContractInvoiceNew.Instance.AuditInvoiceApplication(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"审核发票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"审核发票申请系统异常: {ex.Message}");
                throw new ApiException("审核发票申请失败，请联系系统管理员");
            }
        }
        /// <summary>
        /// 经办人上传发票OCR识别并与申请信息比对接口
        /// </summary>
        /// <param name="request">识别发票请求</param>
        /// <returns>OCR识别结果和比对信息</returns>
        [HttpPost]
        public InvoiceOcrCompareResult RecognizeAndCompareInvoiceOcr([FromForm] RecognizeInvoiceOcrRequest request)
        {
            try
            {
                // 验证请求
                if (request == null)
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "请求不能为空"
                    };
                }

                // 验证文件
                if (request.File == null || request.File.Length == 0)
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "未提供文件或文件为空"
                    };
                }

                // 验证文件格式
                string extension = Path.GetExtension(request.File.FileName).ToLowerInvariant();
                string[] allowedExtensions = { ".jpg", ".jpeg", ".png", ".pdf" };

                if (!allowedExtensions.Contains(extension))
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "不支持的文件格式，请上传jpg、jpeg、png或pdf格式文件"
                    };
                }

                // 验证申请ID
                if (string.IsNullOrEmpty(request.ApplicationId))
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "发票申请ID不能为空"
                    };
                }

                // 读取文件到字节数组
                byte[] fileBytes;
                using (var ms = new MemoryStream())
                {
                    request.File.CopyTo(ms);
                    fileBytes = ms.ToArray();
                }

                // 调用BLL层方法进行OCR识别和比对
                var result = BLL_ContractInvoiceNew.Instance.RecognizeAndCompareInvoiceOcrAsync(fileBytes, request.ApplicationId).GetAwaiter().GetResult();

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"发票OCR识别和比对接口异常: {ex.Message}");
                if (ex.InnerException != null)
                {
                    LogUtil.AddErrorLog($"内部异常: {ex.InnerException.Message}");
                }

                return new InvoiceOcrCompareResult
                {
                    Remark = $"识别和比对发票失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 复核发票申请
        /// </summary>
        /// <param name="request">复核请求</param>
        /// <returns>复核结果</returns>
        [HttpPost]
        public bool ReviewInvoiceApplication(ReviewInvoiceApplicationRequest request)
        {
            try
            {
                // 调用BLL层的发票复核业务方法
                return BLL_ContractInvoiceNew.Instance.ReviewInvoiceApplication(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"复核发票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"复核发票申请系统异常: {ex.Message}");
                throw new ApiException("复核发票申请失败，请联系系统管理员");
            }
        }


        #endregion

        #region 发票匹配相关接口


        /// <summary>
        /// 查询合同下所有确认的到账及其匹配发票情况
        /// </summary>
        /// <param name="request">查询合同到账请求</param>
        /// <returns>合同下所有已确认到账及其匹配的发票信息</returns>
        [HttpPost]
        public ApiTableOut<ContractReceiptMatchingInfo> QueryContractReceiptsWithMatchingStatus(QueryContractReceiptsRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ContractId))
                {
                    throw new ApiException("合同ID不能为空");
                }

                // 调用BLL层业务方法获取合同下所有已确认的到账及其匹配发票情况
                return BLL_ContractInvoiceNew.Instance.GetContractReceiptsWithMatchingStatus(request.ContractId);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"查询合同到账及发票匹配情况业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"查询合同到账及发票匹配情况系统异常: {ex.Message}");
                throw new ApiException("查询合同到账及发票匹配情况失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 手动匹配发票和到账
        /// </summary>
        /// <param name="request">匹配请求</param>
        /// <returns>匹配ID</returns>
        [HttpPost]
        public string ManualMatching(ManualMatchingRequest request)
        {
            try
            {
                // 调用BLL层的手动匹配业务方法
                return BLL_ContractInvoiceNew.Instance.ManualMatching(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"手动匹配发票和到账业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"手动匹配发票和到账系统异常: {ex.Message}");
                throw new ApiException("手动匹配发票和到账失败，请联系系统管理员");
            }
        }

        ///// <summary>
        ///// 自动匹配发票和到账
        ///// </summary>
        ///// <param name="request">自动匹配请求</param>
        ///// <returns>匹配结果</returns>
        //[HttpPost]
        //public bool AutoMatching(AutoMatchingRequest request)
        //{
        //    try
        //    {
        //        // 参数验证
        //        if (request == null || string.IsNullOrEmpty(request.ReceiptId))
        //        {
        //            throw new ApiException("到账记录ID不能为空");
        //        }

        //        // 调用BLL层的自动匹配业务方法
        //        return BLL_ContractInvoiceNew.Instance.AutoMatching(request.ReceiptId);
        //    }
        //    catch (ApiException ex)
        //    {
        //        // 记录业务异常
        //        LogUtil.AddErrorLog($"自动匹配发票和到账业务异常: {ex.Message}");
        //        throw;
        //    }
        //    catch (Exception ex)
        //    {
        //        // 记录系统异常
        //        LogUtil.AddErrorLog($"自动匹配发票和到账系统异常: {ex.Message}");
        //        throw new ApiException("自动匹配发票和到账失败，请联系系统管理员");
        //    }
        //}

        /// <summary>
        /// 复核匹配
        /// </summary>
        /// <param name="request">复核请求</param>
        /// <returns>复核结果</returns>
        [HttpPost]
        public bool ReviewMatching(ReviewMatchingRequest request)
        {
            try
            {
                // 参数验证
                if (request == null)
                {
                    throw new ApiException("审核请求不能为空");
                }

                if (string.IsNullOrEmpty(request.MatchingId))
                {
                    throw new ApiException("匹配ID不能为空");
                }

                if (request.ReviewResult != 1 && request.ReviewResult != 2)
                {
                    throw new ApiException("审核结果不正确，必须为1(通过)或2(拒绝)");
                }

                // 调用BLL层的匹配审核业务方法
                return BLL_ContractInvoiceNew.Instance.ReviewMatching(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"审核匹配申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"审核匹配申请系统异常: {ex.Message}");
                throw new ApiException("审核匹配申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 通过发票申请ID获取匹配详情
        /// </summary>
        /// <param name="request">通过发票申请ID查询匹配详情请求</param>
        /// <returns>匹配详情</returns>
        [HttpPost]
        public MatchingDetailResponse GetMatchingDetail(GetMatchingByApplicationIdRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ApplicationId))
                {
                    throw new ApiException("发票申请ID不能为空");
                }

                // 调用BLL层获取匹配详情
                return BLL_ContractInvoiceNew.Instance.GetMatchingDetailByApplicationId(request.ApplicationId);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"通过发票申请ID获取匹配详情业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"通过发票申请ID获取匹配详情系统异常: {ex.Message}");
                throw new ApiException("通过发票申请ID获取匹配详情失败，请联系系统管理员");
            }
        }

        #endregion

        #region 退票相关接口

        /// <summary>
        /// 保存退票申请（创建或更新）
        /// </summary>
        /// <param name="request">退票申请请求</param>
        /// <returns>退票申请ID</returns>
        [HttpPost]
        public string SaveInvoiceRefund(CreateInvoiceRefundRequest request)
        {
            try
            {
                // 调用BLL层的退票申请业务方法
                return BLL_ContractInvoiceNew.Instance.SaveInvoiceRefund(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"保存退票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"保存退票申请系统异常: {ex.Message}");
                throw new ApiException("保存退票申请失败，请联系系统管理员");
            }
        }
        /// <summary>
        /// 取消退票申请
        /// </summary>
        /// <param name="request">取消退票申请请求</param>
        /// <returns>取消结果</returns>
        [HttpPost]
        public bool CancelInvoiceRefund(CancelInvoiceRefundRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.Id))
                {
                    throw new ApiException("退票申请ID不能为空");
                }

                // 调用BLL层的退票取消业务方法
                return BLL_ContractInvoiceNew.Instance.InvalidateInvoiceRefund(request.Id);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"取消退票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"取消退票申请系统异常: {ex.Message}");
                throw new ApiException("取消退票申请失败，请联系系统管理员");
            }
        }


        #endregion

        #region 退票审核相关接口
        /// <summary>
        /// 经办人上传退票附件OCR识别并与原发票信息比对接口
        /// </summary>
        /// <param name="request">识别退票附件请求</param>
        /// <returns>OCR识别结果和比对信息</returns>
        [HttpPost]
        public InvoiceOcrCompareResult RecognizeAndCompareRefundAttachment([FromForm] RecognizeRefundAttachmentRequest request)
        {
            try
            {
                // 验证请求
                if (request == null)
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "请求不能为空"
                    };
                }

                // 验证文件
                if (request.File == null || request.File.Length == 0)
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "未提供文件或文件为空"
                    };
                }

                // 验证文件格式
                string extension = Path.GetExtension(request.File.FileName).ToLowerInvariant();
                string[] allowedExtensions = { ".jpg", ".jpeg", ".png", ".pdf" };

                if (!allowedExtensions.Contains(extension))
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "不支持的文件格式，请上传jpg、jpeg、png或pdf格式文件"
                    };
                }

                // 验证退票申请ID
                if (string.IsNullOrEmpty(request.RefundId))
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "退票申请ID不能为空"
                    };
                }

                // 读取文件到字节数组
                byte[] fileBytes;
                using (var ms = new MemoryStream())
                {
                    request.File.CopyTo(ms);
                    fileBytes = ms.ToArray();
                }

                // 调用BLL层方法进行OCR识别和比对
                var result = BLL_ContractInvoiceNew.Instance.RecognizeAndCompareRefundAttachmentAsync(fileBytes, request.RefundId).GetAwaiter().GetResult();

                return result;
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"退票附件OCR识别业务异常: {ex.Message}");
                return new InvoiceOcrCompareResult { Remark = ex.Message };
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"退票附件OCR识别系统异常: {ex.Message}");
                return new InvoiceOcrCompareResult { Remark = "处理退票附件时发生错误，请联系系统管理员" };
            }
        }

        /// <summary>
        /// 审核退票申请
        /// </summary>
        /// <param name="request">审核请求</param>
        /// <returns>是否成功</returns>
        [HttpPost]
        public bool ReviewInvoiceRefund(ReviewInvoiceRefundRequest request)
        {
            try
            {
                if (request == null)
                {
                    throw new ApiException("请求参数不能为空");
                }

                // 调用业务层方法
                return BLL_ContractInvoiceNew.Instance.ReviewInvoiceRefund(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"审核退票申请失败: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"审核退票申请失败: {ex.Message}");
                throw new ApiException("审核退票申请失败，请联系系统管理员");
            }
        }
        /// <summary>
        /// 确认退票复核结果
        /// </summary>
        /// <param name="request">复核确认请求</param>
        /// <returns>处理结果</returns>
        [HttpPost]
        public bool ConfirmInvoiceRefundReview(ReviewInvoiceRefundConfirmationRequest request)
        {
            try
            {
                // 参数验证
                if (request == null)
                {
                    throw new ApiException("请求参数不能为空");
                }

                // 调用BLL层方法确认复核结果
                return BLL_ContractInvoiceNew.Instance.ConfirmInvoiceRefundReview(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"确认退票复核结果业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"确认退票复核结果系统异常: {ex.Message}");
                throw new ApiException("处理退票复核确认时发生错误，请联系系统管理员");
            }
        }

        #endregion

        #region 公共接口

        /// <summary>
        /// 发票坐标可视化请求
        /// </summary>
        public class VisualizeInvoiceCoordinatesRequest
        {
            /// <summary>
            /// 发票PDF文件
            /// </summary>
            public IFormFile InvoiceFile { get; set; }
        }

        /// <summary>
        /// 发票坐标可视化 - 生成带有坐标标记的发票图像
        /// </summary>
        /// <param name="request">可视化请求</param>
        /// <returns>坐标可视化图像</returns>
        [HttpPost]
        public IActionResult VisualizeInvoiceCoordinates([FromForm] VisualizeInvoiceCoordinatesRequest request)
        {
            try
            {
                // 验证请求
                if (request == null)
                {
                    throw new ApiException("请求不能为空");
                }

                // 验证文件
                if (request.InvoiceFile == null || request.InvoiceFile.Length == 0)
                {
                    throw new ApiException("未提供发票文件或文件为空");
                }

                // 验证文件格式
                string extension = Path.GetExtension(request.InvoiceFile.FileName).ToLowerInvariant();
                string[] allowedExtensions = { ".pdf" };

                if (!allowedExtensions.Contains(extension))
                {
                    throw new ApiException("不支持的文件格式，目前仅支持PDF格式");
                }

                // 读取文件到字节数组
                byte[] fileBytes;
                using (var ms = new MemoryStream())
                {
                    request.InvoiceFile.CopyTo(ms);
                    fileBytes = ms.ToArray();
                }

                // 保存PDF到临时文件
                string tempDir = Path.Combine(Path.GetTempPath(), "InvoiceCoordinates");
                if (!Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }

                // 生成唯一文件名
                string pdfFileName = $"invoice_{Guid.NewGuid()}.pdf";
                string imageFileName = $"coordinates_{Guid.NewGuid()}.png";

                string pdfFilePath = Path.Combine(tempDir, pdfFileName);
                string imageFilePath = Path.Combine(tempDir, imageFileName);

                // 保存PDF文件
                File.WriteAllBytes(pdfFilePath, fileBytes);

                // 调用坐标可视化工具
                Common.Utils.InvoiceReadUtil.VisualizeCoordinates(pdfFilePath, imageFilePath);

                // 检查输出文件是否生成
                if (!File.Exists(imageFilePath))
                {
                    throw new ApiException("坐标可视化图像生成失败");
                }

                // 读取生成的图像
                byte[] imageBytes = File.ReadAllBytes(imageFilePath);

                // 清理临时文件
                try
                {
                    File.Delete(pdfFilePath);
                    File.Delete(imageFilePath);
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"清理临时文件时出错: {ex.Message}");
                    // 不中断流程，继续返回结果
                }

                // 返回图像
                return new FileContentResult(imageBytes, "image/png")
                {
                    FileDownloadName = $"invoice_coordinates_{DateTime.Now:yyyyMMddHHmmss}.png"
                };
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"生成发票坐标可视化图像业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"生成发票坐标可视化图像系统异常: {ex.Message}");
                throw new ApiException("生成发票坐标可视化图像失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 获取发票列表
        /// </summary>
        /// <param name="request">获取发票列表请求</param>
        /// <returns>发票列表</returns>
        [HttpPost]
        public ApiTableOut<InvoiceApplicationListItem> GetInvoicesByContractId(GetInvoicesByContractIdRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ContractId))
                {
                    throw new ApiException("合同ID不能为空");
                }

                // 调用BLL层方法获取发票列表
                return BLL_ContractInvoiceNew.Instance.GetInvoicesByContractId(
                    request.ContractId,
                    request.PageNumber,
                    request.PageSize,
                    request.InvoicingDateStart,
                    request.InvoicingDateEnd,
                    request.InvoiceNumber,
                    request.DisplayStatus);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"获取发票列表业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"获取发票列表系统异常: {ex.Message}");
                throw new ApiException("获取发票列表失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 查询发票列表
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>发票列表数据</returns>
        [HttpPost]
        public ApiTableOut<InvoiceApplicationListItem> QueryInvoiceAndRefundList(QueryInvoiceApplicationListRequest request)
        {
            try
            {
                // 调用BLL层查询列表（不包含统计信息）
                return BLL_ContractInvoiceNew.Instance.QueryInvoiceAndRefundList(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"查询发票列表业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"查询发票列表系统异常: {ex.Message}");
                throw new ApiException("查询发票列表失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 获取发票统计信息（包括申请和退款）
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>发票统计信息</returns>
        [HttpPost]
        public InvoiceStatisticsResponse GetInvoiceStatistics(QueryInvoiceApplicationListRequest query)
        {
            try
            {
                // 调用BLL层获取统计信息（不包含具体列表数据）
                return BLL_ContractInvoiceNew.Instance.GetInvoiceStatistics(query);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"获取发票统计信息业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"获取发票统计信息系统异常: {ex.Message}");
                throw new ApiException("获取发票统计信息失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 计算合同可开票金额
        /// </summary>
        /// <param name="availableInvoiceAmountRequest"></param>
        /// <returns>可开票金额</returns>
        [HttpPost]
        public decimal CalculateAvailableInvoiceAmount(AvailableInvoiceAmountRequest availableInvoiceAmountRequest)
        {
            try
            {
                if (availableInvoiceAmountRequest == null || string.IsNullOrEmpty(availableInvoiceAmountRequest.ContractId))
                {
                    throw new ApiException("合同ID不能为空");
                }

                // 调用BLL层方法计算可开票金额
                return BLL_ContractInvoiceNew.Instance.CalculateAvailableInvoiceAmount(
                    availableInvoiceAmountRequest.ContractId,
                    availableInvoiceAmountRequest.ApplicationId);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"计算可开票金额业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"计算可开票金额系统异常: {ex.Message}, {ex.StackTrace}");
                throw new ApiException("计算可开票金额失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 催票提醒
        /// </summary>
        /// <param name="request">催票请求</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public bool ReminderInvoiceApplication(ReminderInvoiceRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request?.Id))
                {
                    throw new ApiException("发票申请ID不能为空");
                }

                // 调用新的BLL方法进行催票
                return BLL_ContractInvoiceNew.Instance.ReminderInvoiceApplication(request.Id, UserTokenInfo.id);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"催票操作业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"催票操作系统异常: {ex.Message}");
                throw new ApiException("催票操作失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 修改应付金额
        /// </summary>
        /// <param name="request">修改请求</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public bool UpdateBalanceDue(UpdateBalanceDueRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request?.Id))
                {
                    throw new ApiException("发票ID不能为空");
                }

                if (request.BalanceDue < 0)
                {
                    throw new ApiException("应付金额不能小于0");
                }

                // 调用BLL层方法修改应付金额
                return BLL_ContractInvoiceNew.Instance.UpdateBalanceDue(request, UserTokenInfo.id);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"修改应付金额业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"修改应付金额系统异常: {ex.Message}");
                throw new ApiException("修改应付金额失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 获取应付金额
        /// </summary>
        /// <param name="request">获取应付金额请求</param>
        /// <returns>应付金额信息</returns>
        [HttpPost]
        public BalanceDueResponse GetBalanceDue(GetBalanceDueRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.Id))
                {
                    throw new ApiException("发票ID不能为空");
                }

                // 调用BLL层方法获取应付金额
                return BLL_ContractInvoiceNew.Instance.GetBalanceDue(request.Id);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"获取应付金额业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"获取应付金额系统异常: {ex.Message}");
                throw new ApiException("获取应付金额失败，请联系系统管理员");
            }
        }


        /// <summary>
        /// 获取发票详情（包括退票信息、申请信息、审核信息等）
        /// </summary>
        /// <param name="request">获取发票详情请求</param>
        /// <returns>发票详情</returns>
        [HttpPost]
        public InvoiceDetailResponse GetInvoiceDetail(GetInvoiceDetailRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.InvoiceId))
                {
                    throw new ApiException("发票ID不能为空");
                }

                // 调用BLL层的发票详情业务方法
                return BLL_ContractInvoiceNew.Instance.GetInvoiceDetailWithRefundInfo(request.InvoiceId);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"获取发票详情业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"获取发票详情系统异常: {ex.Message}");
                throw new ApiException("获取发票详情失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 获取发票OCR比对结果详情
        /// </summary>
        /// <param name="request">获取发票OCR比对结果详情请求</param>
        /// <returns>OCR比对结果详情</returns>
        [HttpPost]
        public InvoiceOcrCompareDetail GetInvoiceOcrCompareDetail(GetInvoiceOcrCompareDetailRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ApplicationId))
                {
                    throw new ApiException("申请ID不能为空");
                }

                return BLL_ContractInvoiceNew.Instance.GetInvoiceOcrCompareDetail(request.ApplicationId);
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"获取发票OCR比对结果详情业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取发票OCR比对结果详情出错: {ex.Message}");
                throw new ApiException("获取发票OCR比对结果详情失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 根据类型下载发票
        /// </summary>
        [HttpGet]
        public IActionResult DownloadInvoice(string id, string fileType)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return new JsonResult(new
                    {
                        code = StatusCodes.Status404NotFound,
                        Msg = "参数错误!"
                    });
                }

                if (fileType == "ProformaTicket" || string.IsNullOrEmpty(fileType))
                {
                    // 对于形式发票，直接使用id作为发票id处理(申请Id)
                    Stream result = BLL_ContractInvoiceNew.Instance.DownloadInvoice(id, true);
                    string ContentType = "application/pdf";

                    // 获取合同名称作为文件名基础
                    var invoiceappl = DbOpe_crm_invoice_application.Instance.GetData(i => i.Id == id && i.Deleted != true);
                    var contract = DbOpe_crm_contract.Instance.GetContractById(invoiceappl.ContractId, true);
                    string fileName = "形式发票";

                    if (contract != null && !string.IsNullOrEmpty(contract.ContractName))
                    {
                        // 处理文件名，去除特殊字符，包括点号(.)
                        fileName = "发票-" + contract.ContractName.Replace(".", "_").Replace("/", "_").Replace("\\", "_")
                            .Replace(":", "_").Replace("*", "_").Replace("?", "_").Replace("\"", "_")
                            .Replace("<", "_").Replace(">", "_").Replace("|", "_");
                    }

                    // 添加文件扩展名
                    fileName = fileName + ".pdf";

                    // 设置Content-Disposition头，指定文件名
                    string encodeFilename = HttpUtility.UrlEncode(fileName, Encoding.GetEncoding("UTF-8"));
                    Response.Headers.Add("Content-Disposition", "attachment; filename=" + encodeFilename);

                    return new FileStreamResult(result, ContentType);
                }
                else
                {
                    // 对于附件，使用BLL_Attachfile处理
                    return BLL_Attachfile.Instance.Preview(id, fileType, Response);
                }
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"下载发票附件业务异常: {ex.Message}");
                return new JsonResult(new
                {
                    code = StatusCodes.Status500InternalServerError,
                    Msg = ex.Message
                });
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"下载发票附件系统异常: {ex.Message}, {ex.StackTrace}");
                return new JsonResult(new
                {
                    code = StatusCodes.Status500InternalServerError,
                    Msg = "下载发票失败，请联系系统管理员"
                });
            }
        }


        /// <summary>
        /// 获取下载发票列表
        /// </summary>
        [HttpPost]
        public List<BM_FileInfo> GetDownloadInvoiceList(string invoiceId)
        {
            return BLL_ContractInvoiceNew.Instance.GetInvoiceAttachments(invoiceId);
        }
        #endregion

        #region 发票系统历史数据迁移
        /// <summary>
        /// 执行发票系统历史数据迁移
        /// </summary>
        /// <param name="batchSize">迁移请求参数</param>
        /// <param name="migrationCutoffDate">迁移截止日期</param>
        /// <returns>迁移结果</returns>
        [HttpPost]
        [SkipRightCheck]
        public IActionResult MigrateInvoiceData(int batchSize, DateTime? migrationCutoffDate)
        {
            var result = InvoiceMigrationService.Instance.MigrateInvoiceData(batchSize, migrationCutoffDate);

            if (result.Success)
            {
                return new JsonResult(new
                {
                    success = true,
                    message = result.Message,
                    data = new
                    {
                        totalCount = result.TotalCount,
                        successCount = result.SuccessCount,
                        failedCount = result.TotalCount - result.SuccessCount
                    }
                });
            }
            else
            {
                return new JsonResult(new
                {
                    success = false,
                    message = result.Message,
                    data = new
                    {
                        totalCount = result.TotalCount,
                        successCount = result.SuccessCount,
                        failedCount = result.TotalCount - result.SuccessCount
                    }
                });
            }
        }

        /// <summary>
        /// 查找并自动匹配系统中已开票但未正确匹配到账的发票
        /// </summary>
        /// <param name="executeUpdate">是否执行实际的更新操作，默认为true，设置为false时仅统计不执行实际操作</param>
        /// <returns>匹配结果统计</returns>
        [HttpPost]
        [SkipRightCheck]
        public Dictionary<string, int> FindAndAutoMatchInvoices(bool executeUpdate = true)
        {
            try
            {
                // 记录开始时间
                var startTime = DateTime.Now;
                LogUtil.AddLog($"开始执行发票自动匹配 - executeUpdate: {executeUpdate}");

                // 调用BLL层的自动匹配修复方法
                var result = BLL_ContractInvoiceNew.Instance.FindAndAutoMatchInvoices(executeUpdate);

                // 记录结束时间及执行耗时
                var endTime = DateTime.Now;
                var timeSpan = endTime - startTime;
                LogUtil.AddLog($"发票自动匹配完成 - 总数: {result["总数"]}, 成功: {result["成功"]}, 失败: {result["失败"]}, 耗时: {timeSpan.TotalSeconds:F2}秒");

                return result;
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"查找并自动匹配发票业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"查找并自动匹配发票系统异常: {ex.Message}");
                throw new ApiException("查找并自动匹配发票失败，请联系系统管理员");
            }
        }
        #endregion

        #region 补充发票
        /// <summary>
        /// 上传并识别补充发票
        /// </summary>
        /// <param name="request">发票文件</param>
        /// <returns>识别结果</returns>
        [HttpPost]
        public RecognizeSupplementInvoice_Out UploadAndRecognizeSupplementInvoice([FromForm] UploadSupplementInvoice_In request)
        {                // 读取文件到字节数组
            byte[] fileBytes;
            using (var ms = new MemoryStream())
            {
                request.InvoiceFile.CopyTo(ms);
                fileBytes = ms.ToArray();
            }

            // 调用BLL层方法
            return BLL_ContractInvoiceNew.Instance.UploadAndRecognizeSupplementInvoice(fileBytes, request.ApplicationId).Result;
        }

        /// <summary>
        /// 上传并识别补充发票退票凭证
        /// </summary>
        /// <param name="request">退票凭证上传请求</param>
        /// <returns>识别结果</returns>
        [HttpPost]
        public RecognizeSupplementInvoice_Out UploadSupplementInvoiceRefund([FromForm] UploadSupplementInvoiceRefund_In request)
        {
            try
            {
                // 参数验证
                if (request == null)
                {
                    throw new ApiException("请求参数不能为空");
                }

                if (request.RefundFile == null)
                {
                    throw new ApiException("退票凭证文件不能为空");
                }

                if (string.IsNullOrEmpty(request.OriginalInvoiceId))
                {
                    throw new ApiException("原发票ID不能为空");
                }
                // 读取文件到字节数组
                byte[] fileBytes;
                using (var ms = new MemoryStream())
                {
                    request.RefundFile.CopyTo(ms);
                    fileBytes = ms.ToArray();
                }

                // 调用BLL层方法
                return BLL_ContractInvoiceNew.Instance.UploadAndRecognizeSupplementInvoiceRefund(fileBytes, request.OriginalInvoiceId, request.RefundApplicationId).Result;
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"上传补充发票退票凭证业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"上传补充发票退票凭证系统异常: {ex.Message}");
                throw new ApiException("上传补充发票退票凭证失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 提交补充发票
        /// </summary>
        /// <param name="request">提交请求</param>
        /// <returns>提交结果</returns>
        [HttpPost]
        public SubmitSupplementInvoice_Out SubmitSupplementInvoice(SubmitSupplementInvoice_In request)
        {
            // 调用BLL层方法
            return BLL_ContractInvoiceNew.Instance.SubmitSupplementInvoice(request).Result;
        }

        /// <summary>
        /// 提交补充发票退票申请
        /// </summary>
        /// <param name="request">退票申请请求</param>
        /// <returns>申请ID</returns>
        [HttpPost]
        public SubmitSupplementInvoiceRefund_Out SubmitSupplementInvoiceRefund(SubmitSupplementInvoiceRefund_In request)
        {
            try
            {
                // 调用BLL层的补充发票退票申请方法
                return BLL_ContractInvoiceNew.Instance.SubmitSupplementInvoiceRefund(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"提交补充发票退票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"提交补充发票退票申请系统异常: {ex.Message}");
                throw new ApiException("提交补充发票退票申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 作废补充发票
        /// </summary>
        /// <param name="request">作废补充发票请求</param>
        /// <returns>作废结果</returns>
        [HttpPost]
        public bool InvalidateSupplementInvoice(InvalidateSupplementInvoiceRequest request)
        {
            try
            {
                // 参数验证
                if (request == null || string.IsNullOrEmpty(request.InvoiceId))
                {
                    throw new ApiException("补充发票ID不能为空");
                }

                if (string.IsNullOrEmpty(request.InvoiceBackgroundRemark))
                {
                    throw new ApiException("发票备注不能为空");
                }

                // 调用BLL层方法作废补充发票
                return BLL_ContractInvoiceNew.Instance.InvalidateSupplementInvoice(
                    request.InvoiceId,
                    request.InvoiceBackgroundRemark
                );
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"作废补充发票业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"作废补充发票系统异常: {ex.Message}");
                throw new ApiException("作废补充发票失败，请联系系统管理员");
            }
        }
        #endregion


        #region 到账开票权限相关接口

        /// <summary>
        /// 根据到账ID获取权限详情信息
        /// </summary>
        /// <param name="request">获取权限请求</param>
        /// <returns>权限详情信息</returns>
        [HttpPost]
        public VM_InvoiceReceiptAuthority.AuthorityDetailResponse GetAuthorityByReceiptId(VM_InvoiceReceiptAuthority.GetAuthorityRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ReceiptId))
                {
                    throw new ApiException("到账ID不能为空");
                }

                // 调用BLL层方法获取权限信息
                return BLL_ContractInvoiceNew.Instance.GetAuthorityByReceiptId(request.ReceiptId);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"获取到账权限信息业务异常: {ex.Message}", this.GetType().FullName, "GetAuthorityByReceiptId");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"获取到账权限信息系统异常: {ex.Message}", this.GetType().FullName, "GetAuthorityByReceiptId");
                throw new ApiException("系统异常，请联系管理员");
            }
        }
        /// <summary>
        /// 保存权限记录（创建或更新）
        /// </summary>
        /// <param name="request">权限请求</param>
        /// <returns>权限ID</returns>
        [HttpPost]
        public string SaveAuthority(VM_InvoiceReceiptAuthority.SaveAuthorityRequest request)
        {
            try
            {
                if (request == null)
                {
                    throw new ApiException("权限请求不能为空");
                }

                if (string.IsNullOrEmpty(request.ReceiptId))
                {
                    throw new ApiException("到账ID不能为空");
                }

                // 调用BLL层方法保存权限
                return BLL_ContractInvoiceNew.Instance.SaveAuthority(request);
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"保存权限记录业务异常: {ex.Message}", this.GetType().FullName, "SaveAuthority");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"保存权限记录系统异常: {ex.Message}", this.GetType().FullName, "SaveAuthority");
                throw new ApiException("系统异常，请联系管理员");
            }
        }

        /// <summary>
        /// 批量自动判断到账开票权限
        /// </summary>
        /// <returns>成功判断的权限记录数量</returns>
        [HttpPost]
        public int BatchAutoJudgeAuthority()
        {
            try
            {
                // 调用BLL层方法批量判断权限
                return BLL_ContractInvoiceNew.Instance.BatchAutoJudgeAuthority();
            }
            catch (ApiException ex)
            {
                // 记录业务异常
                LogUtil.AddErrorLog($"批量自动判断权限业务异常: {ex.Message}", this.GetType().FullName, "BatchAutoJudgeAuthority");
                throw;
            }
            catch (Exception ex)
            {
                // 记录系统异常
                LogUtil.AddErrorLog($"批量自动判断权限系统异常: {ex.Message}", this.GetType().FullName, "BatchAutoJudgeAuthority");
                throw new ApiException("系统异常，请联系管理员");
            }
        }

        #endregion

        #region 发票忽略/取消忽略
        /// <summary>
        /// 忽略/取消忽略
        /// </summary>
        /// <param name="request">强制忽略请求</param>
        /// <returns>强制忽略结果</returns>
        [HttpPost]
        public void ForceIgnoreInvoiceApplication(ForceIgnoreInvoiceApplicationRequest request)
        {
            // 调用BLL层方法强制忽略发票
            BLL_ContractInvoiceNew.Instance.ForceIgnoreInvoiceApplication(request.InvoiceApplicationId, request.IsIgnore);
        }
        #endregion

        #region 发票导出
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ExportInvoiceByConditions(InvoiceQuery_In_Export request)
        {
            Stream result = BLL_ContractInvoiceNew.Instance.ExportInvoiceByConditions(request);
            string encodeFilename = HttpUtility.UrlEncode("导出发票结果", Encoding.GetEncoding("UTF-8"));
            Response.Headers.Add("download-name", HttpUtility.UrlEncode(encodeFilename + ".xlsx"));
            Response.Headers.Add("Access-Control-Expose-Headers", "download-name");
            Response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename + ".xlsx");
            return new FileStreamResult(result, "application/octet-stream");
        }
        #endregion

    }
} 