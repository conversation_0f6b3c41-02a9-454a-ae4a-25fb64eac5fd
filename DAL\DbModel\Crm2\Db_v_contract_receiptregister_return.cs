﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_contract_receiptregister_return")]
    public class Db_v_contract_receiptregister_return
    {
           /// <summary>
           /// Desc:合同到账登记表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractReceiptRegisterId {get;set;}

           /// <summary>
           /// Desc:业绩扣除
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? PerformanceDeduction {get;set;}

    }
}
