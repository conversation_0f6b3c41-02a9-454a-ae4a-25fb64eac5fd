﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using iText.StyledXmlParser.Css.Resolve.Shorthand.Impl;
using Newtonsoft.Json;
using SqlSugar;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.IO;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.BLLModel.Enum.PrivateServiceEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;

namespace CRM2_API.BLL
{
    public partial class BLL_ContractService : BaseBLL<BLL_ContractService>
    {
        /// <summary>
        /// 撤销:（已开通、被驳回、待复核、拒绝   需要验证服务有效性以及撤回后不能存在多审核 ）
        /// 若开通状态为已开通，撤销后，需向GTIS发送账号指令
        /// </summary>
        public void RevokeContractProductServiceInfoGtisAudit(string applId)
        {
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.TransDeal(() =>
            {
                var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(applId);
                if (applData == null || applData.Deleted == true)
                {
                    throw new ApiException("未找到对应服务产品申请信息");
                }
                if (applData.IsInvalid != (int)EnumIsInvalid.Effective)
                {
                    throw new ApiException("已失效的审核无法进行操作");
                }
                //获取合同信息
                var info = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(applData.ContractId);
                if (info == null)
                {
                    throw new ApiException("未找到合同信息");
                }
                //获取登记信息
                int serviceState = (int)EnumContractServiceState.TO_BE_OPENED;
                DateTime? serviceCycleEnd = null;
                var registerInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetRegisterInfoByApplId(applId);
                if (registerInfo != null)
                {
                    serviceState = registerInfo.State.Value;
                    serviceCycleEnd = registerInfo.ServiceCycleEnd;
                }
                //能撤回的肯定都有gtisdata了
                var gtisData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == registerInfo.Id);
                if (gtisData == null || gtisData.Deleted == true)
                {
                    throw new ApiException("未找到可以撤回的服务信息");
                }
                ////获取当前服务产品审核中的申请
                //var appls = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataList(d => d.ContractProductInfoId == applData.ContractProductInfoId && d.State == (int)EnumProcessStatus.Submit);
                //获取当前开通状态
                EnumContractServiceOpenState state = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceOpenState(applData.State.Value, applData.ProcessingType.Value, serviceState, serviceCycleEnd);
                switch (state)
                {
                    case EnumContractServiceOpenState.Open:
                    case EnumContractServiceOpenState.NearlyEnd:
                        //gtis申请验证
                        DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.CheckAppl(gtisData.ContractProductInfoId, gtisData.ProcessingType == (int)EnumProcessingType.Change, applId, applData.RenewFirstParty);
                        ////当前有审核中的申请时，不能撤销（同时只能有一个审批）
                        //if (appls.Count > 0)
                        //{
                        //    throw new ApiException("对应的服务产品存在审核中的申请时，不能进行撤销操作");
                        //}
                        //撤回已开通 -->  待复核
                        gtisData.State = (int)EnumContractServiceState.TO_BE_REVIEW;
                        //还要调用gtis接口来撤销账号/撤销变更
                        BLL_GtisOpe.Instance.DelCompany(gtisData.ContractNum, currentUser, applData.ProcessingType == (int)EnumProcessingType.Change || info.ContractType == (int)EnumContractType.ReNew ? gtisData.Id : "").Wait();
                        //这个申请要回到待审核状态
                        applData.State = (int)EnumProcessStatus.Submit;
                        DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);
                        //如果是变更的话，需要把被变更的那个服务恢复
                        if (applData.ProcessingType == (int)EnumProcessingType.Change)
                        {
                            var historyGtisData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(d => d.Id == gtisData.HistoryId);
                            historyGtisData.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
                            historyGtisData.ChangedId = null;
                            historyGtisData.State = (int)EnumContractServiceState.VALID;
                            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(historyGtisData);
                        }
                        else if (info.ContractType == (int)EnumContractType.ReNew && gtisData.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate && !string.IsNullOrEmpty(gtisData.HistoryId))
                        {
                            var historyRenewGtisData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(d => d.Id == gtisData.HistoryId);
                            historyRenewGtisData.State = (int)EnumContractServiceState.VALID;
                            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(historyRenewGtisData);
                        }
                        DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(gtisData);
                        if (applData.ProcessingType == (int)EnumProcessingType.Change)
                        {

                            BLL_WorkFlow.Instance.CancelWorkflowPending("GTIS服务变更审批流程", applData.Id, ((EnumContractServiceOpenState)state).GetEnumDescription(), gtisData);
                        }
                        else
                        {
                            BLL_WorkFlow.Instance.CancelWorkflowPending("GTIS服务审批流程", applData.Id, ((EnumContractServiceOpenState)state).GetEnumDescription(), gtisData);
                        }
                        break;
                    case EnumContractServiceOpenState.ToBeReview:
                        //撤回待复核 --> 待开通/待变更
                        gtisData.State = (int)EnumContractServiceState.TO_BE_OPENED;
                        DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(gtisData);
                        if (applData.ProcessingType == (int)EnumProcessingType.Change)
                        {

                            BLL_WorkFlow.Instance.CancelWorkflowPending("GTIS服务变更审批流程", applData.Id, ((EnumContractServiceOpenState)state).GetEnumDescription(), applData);
                        }
                        else
                        {
                            BLL_WorkFlow.Instance.CancelWorkflowPending("GTIS服务审批流程", applData.Id, ((EnumContractServiceOpenState)state).GetEnumDescription(), applData);
                        }
                        break;
                    case EnumContractServiceOpenState.Returned:
                        //撤回被驳回 --> 待复核
                        gtisData.State = (int)EnumContractServiceState.TO_BE_REVIEW;
                        DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(gtisData);
                        if (applData.ProcessingType == (int)EnumProcessingType.Change)
                        {

                            BLL_WorkFlow.Instance.CancelWorkflowPending("GTIS服务变更审批流程", applData.Id, ((EnumContractServiceOpenState)state).GetEnumDescription(), gtisData);
                        }
                        else
                        {
                            BLL_WorkFlow.Instance.CancelWorkflowPending("GTIS服务审批流程", applData.Id, ((EnumContractServiceOpenState)state).GetEnumDescription(), gtisData);
                        }
                        break;
                    case EnumContractServiceOpenState.Refuse:
                        //gtis申请验证
                        DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.CheckAppl(gtisData.ContractProductInfoId, gtisData.ProcessingType == (int)EnumProcessingType.Change, applId, applData.RenewFirstParty);
                        ////当前有审核中的申请时，不能撤销（同时只能有一个审批）
                        //if (appls.Count > 0)
                        //{
                        //    throw new ApiException("对应的服务产品存在审核中的申请时，不能进行撤销操作");
                        //}
                        //如果是服务变更的话，要看一下被变更的服务是否还有效(当前产品是否还在开通)
                        if (gtisData.ProcessingType == (int)EnumProcessingType.Change)
                        {
                            var oldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(applData.ContractProductInfoId);
                            if (oldGtis == null)
                            {
                                throw new ApiException("当前申请要变更的原服务产品已经失效，无法申请服务变更");
                            }
                        }
                        //撤回拒绝 --> 待开通/待变更
                        gtisData.State = (int)EnumContractServiceState.TO_BE_OPENED;
                        //这个申请要回到待审核状态
                        applData.State = (int)EnumProcessStatus.Submit;
                        DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);
                        DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(gtisData);
                        if (applData.ProcessingType == (int)EnumProcessingType.Change)
                        {

                            BLL_WorkFlow.Instance.CancelWorkflowPending("GTIS服务变更审批流程", applData.Id, ((EnumContractServiceOpenState)state).GetEnumDescription(), applData);
                        }
                        else
                        {
                            BLL_WorkFlow.Instance.CancelWorkflowPending("GTIS服务审批流程", applData.Id, ((EnumContractServiceOpenState)state).GetEnumDescription(), applData);
                        }
                        break;
                    default:
                        throw new ApiException("当前状态无法进行撤销操作");
                }

            });

        }

        /// <summary>
        /// 作废:（已开通、被驳回、待复核、拒绝）
        /// 若开通状态为已开通，作废后，需向GTiS发送账号指令
        /// 作废生效的审核要把历史审核变为有效
        /// </summary>
        /// <param name="applId"></param>
        public void CancelContractProductServiceInfoGtisAudit(string applId)
        {
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.TransDeal(() =>
            {
                var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(applId);
                if (applData == null || applData.Deleted == true)
                {
                    throw new ApiException("未找到对应服务产品申请信息");
                }
                if (applData.IsInvalid != (int)EnumIsInvalid.Effective)
                {
                    throw new ApiException("已变更导致失效的审核无法进行作废操作");
                }
                //获取合同信息
                var info = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(applData.ContractId);
                if (info == null)
                {
                    throw new ApiException("未找到合同信息");
                }
                //获取登记信息
                int serviceState = (int)EnumContractServiceState.TO_BE_OPENED;
                DateTime? serviceCycleEnd = null;
                var registerInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetRegisterInfoByApplId(applId);
                if (registerInfo != null)
                {
                    serviceState = registerInfo.State.Value;
                    serviceCycleEnd = registerInfo.ServiceCycleEnd;
                }
                //能撤回的肯定都有gtisdata了
                var gtisData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == registerInfo.Id);
                if (gtisData == null || gtisData.Deleted == true)
                {
                    throw new ApiException("未找到可以作废的服务信息");
                }
                //获取当前服务产品之前的所有未作废申请（不包括当前操作的申请）按复核时间倒序排列
                var oldappls = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDatas(applId, applData.ContractProductInfoId);
                //获取当前开通状态
                EnumContractServiceOpenState state = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceOpenState(applData.State.Value, applData.ProcessingType.Value, serviceState, serviceCycleEnd);
                switch (state)
                {
                    case EnumContractServiceOpenState.Open:
                    case EnumContractServiceOpenState.NearlyEnd:
                        //作废已开通
                        //还要调用gtis接口来撤销账号/撤销变更
                        BLL_GtisOpe.Instance.DelCompany(gtisData.ContractNum, currentUser, applData.ProcessingType == (int)EnumProcessingType.Change || info.ContractType == (int)EnumContractType.ReNew ? gtisData.Id : "").Wait();
                        //如果是变更的话，需要把被变更的那个服务恢复
                        if (applData.ProcessingType == (int)EnumProcessingType.Change)
                        {
                            var historyGtisData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(d => d.Id == gtisData.HistoryId);
                            historyGtisData.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
                            historyGtisData.ChangedId = null;
                            historyGtisData.State = (int)EnumContractServiceState.VALID;
                            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(historyGtisData);
                        }
                        else if (info.ContractType == (int)EnumContractType.ReNew && gtisData.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate && !string.IsNullOrEmpty(gtisData.HistoryId))
                        {
                            var historyRenewGtisData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(d => d.Id == gtisData.HistoryId);
                            historyRenewGtisData.State = (int)EnumContractServiceState.VALID;
                            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(historyRenewGtisData);
                        }
                        break;
                    case EnumContractServiceOpenState.ToBeReview:
                        //作废待复核
                        break;
                    case EnumContractServiceOpenState.Returned:
                        //作废被驳回
                        break;
                    case EnumContractServiceOpenState.Refuse:
                        //作废拒绝
                        break;
                    default:
                        throw new ApiException("当前状态无法进行作废操作");
                }
                //把服务作废
                gtisData.State = (int)EnumContractServiceState.VOID;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(gtisData);
                //如果这个申请当前是生效的，要把之前上一个未作废的申请置为生效
                if (applData.IsInvalid == (int)EnumIsInvalid.Effective && oldappls.Count > 0)
                {
                    oldappls[0].IsInvalid = (int)EnumIsInvalid.Effective;
                    DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(oldappls[0]);
                }
                //把当前申请作废 + 失效
                applData.State = (int)EnumProcessStatus.Void;
                applData.IsInvalid = (int)EnumIsInvalid.Invalid;
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);

                if (applData.ProcessingType == (int)EnumProcessingType.Change)
                {
                    BLL_WorkFlow.Instance.VoidWorkflowPending("GTIS服务变更审批流程", applData.Id, "作废");
                }
                else
                {
                    BLL_WorkFlow.Instance.VoidWorkflowPending("GTIS服务审批流程", applData.Id, "作废");
                }
            });
        }


    }
}
