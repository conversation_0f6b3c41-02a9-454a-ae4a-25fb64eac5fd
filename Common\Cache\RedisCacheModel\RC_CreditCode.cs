﻿using CRM2_API.Model.BusinessModel;

namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class CreditCode
        {
            const string CREDITCODE = "creditcode_";
            public static bool Check(string companyName)
            {
                var key = CREDITCODE + companyName;
                return RedisHelper.Exists(key);
            }
            public static string GetCreditCode(string companyName)
            {
                var key = CREDITCODE + companyName;
                if (RedisHelper.Exists(key))
                    return RedisHelper.Get<string>(key);
                else
                    return null;
            }
            public static void Save(string companyName,string creditCode)
            {
                var key = CREDITCODE + companyName;
                RedisHelper.Set(key, creditCode, TimeSpan.FromMinutes(60));
            }
        }
        
    }
}
