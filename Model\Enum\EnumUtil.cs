using System.ComponentModel;

namespace CRM2_API.Model.BLLModel.Enum
{

        public enum EnumContractMethod
        {
            [Description("纸质合同")]
            PaperContracts = 1,
            [Description("电子合同")]
            ElectronicContract = 2
        }

        public enum EnumElectronicEntityStatus
        {
            [Description("已使用")]
            Used = 1,
            [Description("未使用")]
            UnUse = 2,
            [Description("作废")]
            Void = 3,
        }

        public enum EnumIsReceipt
        {
            [Description("全部到账")]
            AllReceived = 1,
            [Description("部分到账")]
            PartialReceipt = 2,
            [Description("未到账")]
            NotReceived = 3
        }

        public enum EnumPaymentMethod
        {
            [Description("银行")]
            Bank = 1,
            [Description("现金")]
            Cash = 2,
            [Description("银行+现金")]
            BankAndCash = 3,
            [Description("承兑")]
            Honour = 4
        }

        public enum EnumContractStatus
        {
            [Description("待审核")]
            Submit = 1,
            //[Description("审核中")]
            //InReview = 2,
            [Description("通过")]
            Pass = 3,
            [Description("拒绝")]
            Refuse = 4,
            [Description("待确认")]
            ToBeConfirmed = 5,
            [Description("草稿")]
            Draft = 6,
            [Description("作废")]
            Cancel = 7,
            //[Description("确认通过")]
            //PassConfirmed = 8,
            //[Description("确认拒绝")]
            //RefuseConfirmed = 9
            [Description("自动通过")]
            AutoPass = 8,
            [Description("超级子账号待审核")]
            SuperSubAccountSubmit = 9,
            [Description("超级子账号通过")]
            SuperSubAccountPass = 10,
            [Description("超级子账号拒绝")]
            SuperSubAccountRefuse = 11,
            [Description("初审通过")]
            InitialAuditPass = 12,
            [Description("初审拒绝")]
            InitialAuditRefuse = 13,


            [Description("初审通过待中队条款审核")]
            InitialAuditPassSubmitOrgRegimentAudit = 121,
            [Description("初审通过待大队条款审核")]
            InitialAuditPassSubmitOrgBrigadeAudit = 122,
            [Description("初审通过待战队条款审核")]
            InitialAuditPassSubmitOrgDivisionAudit = 123,
            [Description("中队条款通过")]
            OrgRegimentAuditPass = 14,
            [Description("中队条款拒绝")]
            OrgRegimentAuditRefuse = 15,
            [Description("中队条款通过待大队条款审核")]
            OrgRegimentAuditSubmitOrgBrigadeAudit = 141,
            [Description("中队条款通过待战队条款审核")]
            OrgRegimentAuditSubmitOrgDivisionAudit = 142,
            [Description("大队条款通过")]
            OrgBrigadeAuditPass = 16,
            [Description("大队条款拒绝")]
            OrgBrigadeAuditRefuse = 17,
            [Description("大队条款通过待战队条款审核")]
            OrgBrigadeAuditSubmitOrgDivisionAudit = 161,
            [Description("战队条款通过")]
            OrgDivisionAuditPass = 18,
            [Description("战队条款拒绝")]
            OrgDivisionAuditRefuse = 19,

            [Description("提交待中队条款审核")]
            SubmitOrgRegimentAudit = 201,
            [Description("提交待大队条款审核")]
            SubmitOrgBrigadeAudit = 202,
            [Description("提交待战队条款审核")]
            SubmitOrgDivisionAudit = 203,
            [Description("提交中队条款通过")]
            SubmitOrgRegimentAuditPass = 21,
            [Description("提交中队条款拒绝")]
            SubmitOrgRegimentAuditRefuse = 22,
            [Description("提交中队条款通过待大队条款审核")]
            SubmitOrgRegimentAuditSubmitOrgBrigadeAudit = 211,
            [Description("提交中队条款通过待战队条款审核")]
            SubmitOrgRegimentAuditSubmitOrgDivisionAudit = 212,
            [Description("提交大队条款通过")]
            SubmitOrgBrigadeAuditPass = 23,
            [Description("提交大队条款拒绝")]
            SubmitOrgBrigadeAuditRefuse = 24,
            [Description("提交大队条款通过待战队条款审核")]
            SubmitOrgBrigadeAuditSubmitOrgDivisionAudit = 231,
            [Description("提交战队条款通过")]
            SubmitOrgDivisionAuditPass = 25,
            [Description("提交战队条款拒绝")]
            SubmitOrgDivisionAuditRefuse = 26,
        }

        public enum EnumSubmitSourceType
        {
            [Description("初审")]
            InitialAudit = 1,
            [Description("中队")]
            OrgRegimentAudit = 2,
            [Description("大队")]
            OrgBrigadeAudit = 3,
            [Description("战队")]
            OrgDivisionAudit = 4,
            [Description("超级子账号提交")]
            SuperSubAccountSubmit = 5,
        }

        public enum EnumIsSpecial
        {
            [Description("是")]
            Yes = 1,
            [Description("否")]
            No = 0
        }

        public enum EnumUsageStatus
        {
            [Description("未使用")]
            UnUse = 0,
            [Description("使用")]
            Use = 1,
            [Description("作废")]
            Cancel = 2,
            [Description("丢失")]
            Lose = 3
        }

        public enum EnumRecoveryStatus
        {
            [Description("已回收")]
            Recycled = 1,
            [Description("未回收")]
            Unrecycled = 2
        }

        public enum EnumUrgeRegistration
        {
            [Description("未催单")]
            NoReminder = 0,
            [Description("已催单")]
            Reminded = 1
        }

        public enum EnumWorkflowPendingState
        {
            [Description("未查看")]
            NotViewed = 1,
            [Description("查看")]
            Viewed = 2,
            [Description("处理")]
            Handle = 3,
            [Description("作废")]
            Cancel = 4
        }

        public enum EnumAuditType
        {
            [Description("手动审核")]
            ManualReview = 1,
            [Description("自动审核")]
            AutomaticReview = 2
        }

        public enum EnumRecipientType
        {
            [Description("角色")]
            RoleId = 1,
            [Description("用户")]
            UserId = 2,
            [Description("上级节点实际处理人")]
            PreviousNodeHandler = 3,
            [Description("流程发起人")]
            ProcessInitiator = 4,
            [Description("指定人")]
            Assigner = 5
        }

        public enum EnumAvatarImageFileType
        {
            /// <summary>
            /// 用户头像
            /// </summary>
            [Description("用户头像")]
            AvatarImage
        }

        public enum EnumAttachFileType
        {
            /// <summary>
            /// 合同附件
            /// </summary>
            [Description("合同附件")]
            Contract,
            /// <summary>
            /// 合同变更申请附件
            /// </summary>
            [Description("合同变更申请附件")]
            ContractChangeAppl,
            /// <summary>
            /// 合同特殊附件
            /// </summary>
            [Description("合同特殊附件")]
            ContractSpecial,
            /// <summary>
            /// 合同支付信息附件
            /// </summary>
            [Description("合同支付信息附件")]
            ContractPaymentInfo,
            /// <summary>
            /// 发票申请信息附件
            /// </summary>
            [Description("发票申请信息附件")]
            ContractInvoiceAppl,
            /// <summary>
            /// 发票信息附件
            /// </summary>
            [Description("发票信息附件")]
            ContractInvoice,
            /// <summary>
            /// 红冲发票信息附件
            /// </summary>
            [Description("红冲发票信息附件")]
            RefundContractInvoice,
            /// <summary>
            /// 邓白氏邮件模板附件
            /// </summary>
            [Description("邓白氏邮件模板附件")]
            DBEmailTemplate,
            /// <summary>
            /// 电子合同
            /// </summary>
            [Description("电子合同")]
            ElectronicContract,
            /// <summary>
            /// 邓白氏合同原件
            /// </summary>
            [Description("邓白氏合同原件")]
            DBOriginalContract,
            /// <summary>
            /// 已盖章合同
            /// </summary>
            [Description("已盖章合同")]
            SealedContract,
            /// <summary>
            /// 期刊邮寄快递快照
            /// </summary>
            [Description("期刊邮寄快递快照")]
            MailingFiles,
            /// <summary>
            /// 回款交易凭证
            /// </summary>
            [Description("回款交易凭证")]
            TransactionReceipt,
            /// <summary>
            /// 公司关系说明附件
            /// </summary>
            [Description("公司关系说明附件")]
            Relation,
            /// <summary>
            /// 合同其他盖章附件
            /// </summary>
            [Description("合同其他盖章附件")]
            OtherStampReview,
        }

        public enum EnumConfirmState
        {
            [Description("同意")]
            Ok = 1,
            [Description("拒绝")]
            Refuse = 2
        }

        public enum EnumProcessStatus
        {
            [Description("待审核")]
            Submit = 1,
            [Description("通过")]
            Pass = 2,
            [Description("拒绝")]
            Refuse = 3,
            [Description("作废")]
            Void = 4
        }

        public enum EnumProcessingType
        {
            [Description("服务申请")]
            Add = 1,
            [Description("服务变更")]
            Change = 2
        }

        public enum EnumIsInvalid
        {
            [Description("有效")]
            Effective = 0,
            [Description("无效")]
            Invalid = 1
        }

        public enum EnumMatchingStatus
        {
            [Description("待匹配")]
            ToBeMatched = 1,
            [Description("匹配待登记")]
            MatchingPendingRegistration = 2,
            [Description("已登记")]
            Registered = 3,
            [Description("识别失败")]
            RecognitionFailed = 4
        }

        public enum EnumAutoMatchingState
        {
            [Description("已匹配待确认")]
            MatchedToBeConfirmed = 1,
            [Description("已确认")]
            Confirmed = 2,
            [Description("已否认")]
            Denied = 3
        }

        public enum EnumIdentifyStatus
        {
            [Description("成功")]
            Success = 1,
            [Description("失败")]
            Fail = 2
        }

        public enum EnumMaintenanceStatus
        {
            [Description("提交")]
            Submit = 1,
            [Description("通过")]
            Pass = 2
        }

        /// <summary>
        /// 快捷搜索 0:全部 1:今日新增 2:本周新增
        /// </summary>
        public enum EnumQueryListType
        {
            [Description("全部")]
            All = 0,
            [Description("今日新增")]
            Today = 1,
            [Description("本周新增")]
            Week = 2
        }

        public enum EnumContractInvoiceQueryListType
        {
            [Description("全部")]
            All = 0,
            [Description("今日新增")]
            Today = 1,
            [Description("本周新增")]
            Week = 2,
            [Description("催单申请")]
            Reminder = 3
        }

        public enum EnumInvoicingStatus
        {
            [Description("全部开票")]
            Invoiced = 1,
            [Description("未开票")]
            NotInvoiced = 2,
            [Description("部分开票")]
            PartialInvoiced = 3,
            [Description("部分退票")]
            PartialRefund = 4,
            [Description("全部退票")]
            AllRefunds = 5
        }

        public enum EnumIsSignReceiving
        {
            [Description("未寄出")]
            NotSentOut = 1,
            [Description("已寄出")]
            SentOut = 2,
            [Description("已签收")]
            SignedFor = 3,
            [Description("确认签收")]
            ConfirmReceipt = 4
        }

        public enum EnumAuditStatus
        {
            [Description("待审核")]
            Submit = 1,
            [Description("通过")]
            Pass = 2,
            [Description("拒绝")]
            Refuse = 3,
            [Description("草稿")]
            Draft = 4,
            [Description("作废")]
            Cancel = 5
        }

        public enum EnumRefundStatus
        {
            [Description("未退票")]
            NotRefunded = 1,
            [Description("待退票")]
            TicketToBeRefunded = 2,
            [Description("待退票确认")]
            PendingRefundConfirmation = 3,
            [Description("已退票")]
            TicketRefunded = 4,
            [Description("有疑问")]
            Questionable = 5
        }

        public enum EnumIsReminder
        {
            [Description("已催票")]
            UrgedTicket = 1,
            [Description("未催票")]
            UnUrgedTicket = 2
        }

        public enum EnumBillingType
        {
            [Description("到账开票")]
            InvoicingUponReceipt = 1,
            [Description("先开票后付款")]
            InvoicingBeforePayment = 2,
            [Description("补充发票")]
            SupplementInvoice = 3
        }

        public enum EnumInvoiceType
        {
            /// <summary>
            /// 普票
            /// </summary>
            [Description("普票")]
            UniversalTicket = 1,
            /// <summary>
            /// 专票
            /// </summary>
            [Description("专票")]
            SpecialTicket = 2,
            /// <summary>
            /// 形式发票
            /// </summary>
            [Description("形式发票")]
            ProformaTicket = 3
        }

        public enum EnumContractInvoiceAuditState
        {
            [Description("开票")]
            Ok = 1,
            [Description("拒绝")]
            Refuse = 2
        }

        public enum EnumInvoicingForm
        {
            [Description("纸质发票")]
            PaperInvoice = 1,
            [Description("电子发票")]
            ElectronicInvoice = 2
        }

        public enum EnumRefundStateConfirm
        {
            [Description("已确认")]
            Confirmed = 1,
            [Description("有疑问")]
            Questionable = 2
        }

        public enum EnumPaymentType
        {
            [Description("常规")]
            Routine = 1,
            [Description("预付款")]
            AdvanceCharge = 2,
            [Description("首付款")]
            DownPayment = 3,
            [Description("尾款")]
            BalancePayment = 4
        }

        public enum EnumRegisterState
        {
            [Description("待复核")]
            Submit = 1,
            [Description("待确认")]
            Pass = 2,
            [Description("拒绝")]
            Refuse = 3,
            [Description("自动待登记")]
            MatchingPendingRegistration = 4,
            [Description("未确认")]
            UnConfirmed = 5,
            [Description("已确认")]
            Confirmed = 6,
            [Description("有疑问")]
            Questionable = 7,
            [Description("催单中")]
            Reminder = 8,
            [Description("已锁定")]
            Locked = 9,
            [Description("待登记")]
            ToBeRegistered = 10,
            [Description("名称一致待登记")]
            PartMatchingPendingRegistration = 11,
            [Description("关联名称待登记")]
            ManualMatchingPendingRegistration = 12
        }

        public enum EnumLocked
        {
            [Description("已锁定")]
            Locked = 1,
            [Description("未锁定")]
            UnLocked = 2
        }

        public enum EnumAchievementState
        {
            [Description("待确认")]
            UnConfirmed = 1,
            [Description("已确认")]
            Confirmed = 2,
            [Description("有疑问")]
            Questionable = 3,
            [Description("撤销")]
            Revoke = 4
        }
        public enum EnumContractChangeAuditState
        {
            [Description("待审核")]
            InReview = 1,
            [Description("通过")]
            Pass = 2,
            [Description("拒绝")]
            Refuse = 3,
            //[Description("修改")]
            //Change = 4,
            //[Description("确认")]
            //Confirm = 5
        }

        public enum EnumProjectinfoType
        {
            [Description("退还项目")]
            RefundItems = 1,
            [Description("返还业绩")]
            ReturnPerformance = 2,
            [Description("其他项目")]
            Others = 3
        }

        public enum EnumReceiptRegisterAudit
        {
            [Description("通过")]
            Pass = 1,
            [Description("拒绝")]
            Refuse = 2
        }

        public enum EnumTemporaryAccountState
        {
            [Description("正常")]
            Normal = 1,
            [Description("异常")]
            Abnormal = 2,
            [Description("停用")]
            Deactivate = 3,
            [Description("过期")]
            Expire = 4
        }

        public enum EnumTemporaryAccountUserState
        {
            [Description("启用")]
            Enable = 1,
            [Description("异常")]
            Abnormal = 2,
            [Description("停用")]
            Deactivate = 3
        }

        public enum EnumTemporaryAccountEnable
        {
            [Description("启用")]
            Enable = 1,
            [Description("停用")]
            Deactivate = 2
        }

        public enum EnumTemporaryAccountType
        {
            [Description("主账号")]
            Primary = 1,
            [Description("子账号")]
            Sub = 2
        }

        public enum EnumAccountsOpenedNum
        {
            [Description("1")]
            One = 1,
            [Description("2")]
            Two = 2,
            [Description("3")]
            Three = 3
        }

        public enum EnumDuration
        {
            [Description("30")]
            Thirty = 1,
            [Description("60")]
            Sixty = 2,
            [Description("90")]
            Ninety = 3,
            [Description("120")]
            OneHundredAndTwenty = 4
        }

        public enum EnumDelayTimes
        {
            [Description("1")]
            One = 1,
            [Description("2")]
            Two = 2,
            [Description("3")]
            Three = 3
        }

        public enum EnumExpenseType
        {
            [Description("正常")]
            Normal = 1,
            [Description("赠送")]
            Give = 2,
            [Description("自费")]
            SelfFunded = 3
        }

        public enum EnumMailingStatus
        {
            [Description("本期已寄")]
            Mailed = 1,
            [Description("全部已寄")]
            AllMailed = 2,
            [Description("待邮寄")]
            ToBeMailing = 3,
            [Description("未邮寄")]
            UnMail = 4,
            [Description("作废")]
            Void = 5,
            [Description("过期")]
            OverDue = 6,
            [Description("未开始")]
            HaveNotBegun = 7,
            [Description("即将到期")]
            NearlyEnd = 8
        }

        public enum EnumContractWordTemplateVersion
        {
            [Description("中文版")]
            ChineseVersion = 1,
            [Description("英文版")]
            EnglishVersion = 2,
            [Description("纯英文版")]
            PureEnglishVersion = 3
        }

        public enum EnumOperate
        {
            [Description("申请")]
            Appl = 1,
            [Description("审核")]
            Examine = 2

        }

        public enum EnumSysMessagesState
        {
            [Description("未查看")]
            NotViewed = 1,
            [Description("已查看")]
            Viewed = 2
        }

        public enum EnumSysMessagesIsHidden
        {
            [Description("不隐藏")]
            NoHidden = 1,
            [Description("隐藏")]
            Hidden = 2
        }

        public enum EnumSysMessagesType
        {
            [Description("修改密码")]
            ModifyPassWord = 1,
            [Description("修改手机号")]
            ModifyPhone = 2,
            [Description("邓白氏匹配异常")]
            DBServiceAbnormal = 3,
            [Description("签约服务即将到期")]
            ContractServiceinfoDue = 4
        }

        public enum EnumMessageType
        {
            [Description("发布")]
            Publish = 1,
            [Description("评论")]
            Comment = 2,
            [Description("系统")]
            System = 3,
            [Description("通知")]
            Notice = 4,
            [Description("公告")]
            PublicAnnouncement = 5
        }

        public enum EnumAllowLogin
        {
            [Description("是否允许邮箱号登录")]
            AllowEmailLogin = 1,
            [Description("是否允许用户名密码登录")]
            AllowUserNameLogin = 2,
            [Description("是否允许手机号登录")]
            AllowTelphoneLogin = 3,
            [Description("是否允许微信登录")]
            AllowWechatLogin = 4,
            [Description("是否允许钉钉登录")]
            AllowDingdingLogin = 5,
            [Description("是否允许短信验证码登录")]
            AllowMessageAuthCodeLogin = 6,
        }

        public enum EnumExchangeApplState
        {
            [Description("待审核")]
            Submit = 1,
            [Description("通过")]
            Pass = 2,
            [Description("拒绝")]
            Refuse = 3,
            [Description("草稿")]
            Draft = 4
        }

        public enum EnumExchangeState
        {
            [Description("启用")]
            Enable = 1,
            [Description("停用")]
            Deactivate = 2
        }

        public enum EnumWeek
        {
            [Description("日")]
            Sunday = 0,
            [Description("一")]
            Monday = 1,
            [Description("二")]
            Tuesday = 2,
            [Description("三")]
            Wednesday = 3,
            [Description("四")]
            Thursday = 4,
            [Description("五")]
            Friday = 5,
            [Description("六")]
            Saturday = 6
        }

        public enum EnumLogicalOperator
        {
            [Description("And")]
            And = 1,
            [Description("Or")]
            Or = 2
        }

        public enum EnumDataPermissionDataSource
        {
            [Description("All")]
            All = 1,
            [Description("Self")]
            Self = 2,
            [Description("Associated")]
            Associated = 3
        }

        public enum EnumDataSourceRelationShip
        {
            [Description("Associated")]
            Associated = 1,
            [Description("NoAssociated")]
            NoAssociated = 2
        }

        public enum EnumIsManualMatching
        {
            [Description("手动匹配")]
            Manual = 1,
            [Description("自动匹配")]
            Auto = 0
        }

        public enum EnumIsMatchingAll
        {
            [Description("全部匹配")]
            All = 1,
            [Description("部分匹配")]
            Part = 0
        }

        public enum EnumIsAutoMatching
        {
            [Description("自动匹配")]
            Auto = 1,
            [Description("手动匹配")]
            Manual = 0
        }

        public enum EnumStampReviewStatus
        {
            [Description("未上传")]
            NotUploaded = 1,
            [Description("未审核")]
            UnAudited = 2,
            [Description("已审核")]
            Approved = 3,
            [Description("拒绝")]
            Refuse = 4
        }

        public enum EnumOtherStampReviewStatus
        {
            [Description("未上传")]
            NotUploaded = 1,
            [Description("未审核")]
            UnAudited = 2,
            [Description("已审核")]
            Approved = 3,
            [Description("拒绝")]
            Refuse = 4
        }

        public enum EnumIsCustomContractDescription
        {
            [Description("是")]
            Yes = 1,
            [Description("否")]
            No = 2
        }

        public enum EnumInitialAuditStatus
        {
            [Description("待审核")]
            Submit = 1,
            [Description("通过")]
            Pass = 2,
            [Description("拒绝")]
            Refuse = 3
        }

        public enum EnumDivisionAuditStatus
        {
            [Description("待审核")]
            Submit = 1,
            [Description("通过")]
            Pass = 2,
            [Description("拒绝")]
            Refuse = 3
        }

        public enum EnumBrigadeAuditStatus
        {
            [Description("待审核")]
            Submit = 1,
            [Description("通过")]
            Pass = 2,
            [Description("拒绝")]
            Refuse = 3
        }

        public enum EnumRegimentAuditStatus
        {
            [Description("待审核")]
            Submit = 1,
            [Description("通过")]
            Pass = 2,
            [Description("拒绝")]
            Refuse = 3
        }

        public enum EnumWorkFlow
        {
            [Description("合同审批流程")]
            Contract = 1,
            [Description("GTIS审批流程")]
            GTIS = 2,
            [Description("客户关系审批流程")]
            Customer = 3,
            [Description("到账登记流程")]
            Receipt = 4
        }

        public enum EnumStampReviewSubmitStatus
        {
            [Description("通过")]
            Pass = 1,
            [Description("拒绝")]
            Refuse = 2
        }

        public enum EnumOtherStampReviewSubmitStatus
        {
            [Description("通过")]
            Pass = 1,
            [Description("拒绝")]
            Refuse = 2
        }

        public enum EnumVerifyConfirm
        {
            [Description("未确认")]
            NoVerifyConfirm = 1,
            [Description("已确认")]
            VerifyConfirm = 2
        }

        public enum EnumCongratulations
        {
            [Description("未恭喜确认")]
            NoCongratulations = 1,
            [Description("已恭喜确认")]
            Congratulations = 2
        }

        public enum EnumLoingDaysScope
        {
            [Description("超过30天")]
            Morethan30 = 0,
            [Description("超过45天")]
            Morethan45 = 1,
            [Description("超过60天")]
            Morethan60 = 2,
            [Description("超过90天")]
            Morethan90 = 3,
            [Description("未登录过")]
            NeverLogin = 5,
            [Description("全部")]
            ForAll = 4,
        }

        public enum EnumIsInvoiceAuditPart
        {
            [Description("正常")]
            Normal = 1,
            [Description("作废")]
            Cancel = 2,
            [Description("退票")]
            Refund = 3,
        }

        public enum EnumTeamAuditType
        {
            [Description("已审核过")]
            Audited = 1,
            [Description("未审核过")]
            Audit = 2,
        }

        public enum EnumStampPositionType
        {
            [Description("骑缝章")]
            PagingSeal = 1,
            [Description("普通章")]
            CommonSeal = 2,
        }

        public enum EnumGtisDiscountType
        {
            /// <summary>
            /// 不使用
            /// </summary>
            [Description("不使用")]
            Unused = 1,
            /// <summary>
            /// 优惠券
            /// </summary>
            [Description("优惠券")]
            Coupon = 2,
            /// <summary>
            /// 合并合同
            /// </summary>
            [Description("合并合同")]
            MergeContract = 3,
        }

        public enum EnumDownloadFileType
        {
            [Description("PDF")]
            PDF = 1,
            [Description("WORD")]
            WORD = 2,
            [Description("PNG")]
            PNG = 3,
        }
        /// <summary>
        /// 尾款申请剩余服务、变更服务内容、个人服务天数延期、优惠券延期、其他；
        /// 新增：SalesWits相关变更原因（基于增项产品动态显示）
        /// </summary>
        public enum EnumGtisServiceChangeProject
        {
            /// <summary>
            /// 尾款申请剩余服务
            /// </summary>
            [Description("尾款申请剩余服务")]
            ApplyResidualService = 1,
            /// <summary>
            /// 变更服务内容
            /// </summary>
            [Description("变更服务内容")]
            ChangeServiceContent = 2,
            /// <summary>
            /// 个人服务天数延期
            /// </summary>
            [Description("个人服务天数延期")]
            DelayPersonalServiceDays = 3,
            /// <summary>
            /// 优惠券延期
            /// </summary>
            [Description("优惠券延期")]
            DelayCoupons = 4,
            /// <summary>
            /// 其他
            /// </summary>
            [Description("其他")]
            OtherReason = 5,

            // === 新增：基于增项产品的动态变更原因 ===
            /// <summary>
            /// 开通SaleWits（需要合同中有SalesWits基础产品或新增资源产品）
            /// </summary>
            [Description("开通SaleWits")]
            OpenSaleWits = 6,
            /// <summary>
            /// SalesWits充值（需要合同中有充值产品）
            /// </summary>
            [Description("SalesWits充值")]
            SalesWitsRecharge = 7,
            /// <summary>
            /// SalesWits新增子账号（需要合同中有新增资源产品）
            /// </summary>
            [Description("SalesWits新增子账号")]
            SalesWitsAddAccount = 8,
        }
    
}
