﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.ContractTemplate;
using CRM2_API.Model.ControllersViewModel.ContractTemplateApply;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    [Description("合同说明模板控制器")]
    public class ContractTemplateController : MyControllerBase
    {
        public ContractTemplateController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 添加合同说明模板申请信息
        /// </summary>
        /// <param name="templateApply_In"></param>
        [HttpPost, PreLog]
        public void AddContractTemplateApply(AddContractTemplateApply_In templateApply_In)
        {
            BLL_ContractTemplate.Instance.AddContractTemplateApply(templateApply_In);
        }

        /// <summary>
        /// 修改合同说明模板申请信息
        /// </summary>
        /// <param name="templateApply_In"></param>
        [HttpPost, PreLog]
        public void UpdateContractTemplateApply(UpdateContractTemplateApply_In templateApply_In)
        {
            BLL_ContractTemplate.Instance.UpdateContractTemplateApply(templateApply_In);
        }

        /// <summary>
        /// 删除合同说明模板信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void DeleteContractTemplate(string Ids)
        {
            BLL_ContractTemplate.Instance.DeleteContractTemplate(Ids);
        }

        /// <summary>
        /// 撤销合同说明模板申请信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void RevokeContractTemplateApply(string Ids)
        {
            BLL_ContractTemplate.Instance.RevokeContractTemplateApply(Ids);
        }


        /// <summary>
        /// 根据查询条件获取合同说明模板信息列表
        /// </summary>
        /// <param name="searchContractTemplateList_In"></param>
        [HttpPost, PreLog]
        public ApiTableOut<SearchContractTemplateList_Out> SearchContractTemplateList(SearchContractTemplateList_In searchContractTemplateList_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_template.Instance.SearchContractTemplateList(searchContractTemplateList_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据合同说明模板信息Id获取合同说明模板信息
        /// </summary>
        /// <param name="Id"></param>
        [HttpPost, PreLog]
        public GetContractTemplateById_Out GetContractTemplateById(string Id)
        {
            if (string.IsNullOrEmpty(Id))
                throw new ApiException("未选择模板");
            return DbOpe_crm_contract_template.Instance.GetContractTemplateById(Id);
        }

        /// <summary>
        /// 根据合同说明模板id获取合同信息列表
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<GetContractByContractTemplateId_Out> GetContractByContractTemplateId(GetContractByContractTemplateId_In search_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_template.Instance.GetContractByContractTemplateId(search_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据查询条件获取合同说明模板申请信息列表
        /// </summary>
        /// <param name="schConTempApplies_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchContractTemplateApplyList_Out> SearchContractTemplateApplyList(SearchContractTemplateApplyList_In schConTempApplies_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_templateapply.Instance.SearchContractTemplateApplyList(schConTempApplies_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据合同说明模板申请信息Id获取合同说明模板申请信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractTemplateApplyById_Out GetContractTemplateApplyById(string Id)
        {
            return DbOpe_crm_contract_templateapply.Instance.GetContractTemplateApplyById(Id);
        }

        /// <summary>
        /// 审核合同说明模板申请信息
        /// </summary>
        /// <param name="apply"></param>
        [HttpPost, PreLog]
        public void AuditContractTemplateApply(AuditContractTemplateApply_In apply)
        {
            BLL_ContractTemplate.Instance.AuditContractTemplateApply(apply);
        }

        /// <summary>
        /// 撤销合同说明模板申请审核信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void RevokeContractTemplateAudit(string Ids)
        {
            BLL_ContractTemplate.Instance.RevokeContractTemplateAudit(Ids);
        }

        /// <summary>
        /// 作废合同说明模板申请信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void VoidContractTemplateAudit(List<string> Ids)
        {
            BLL_ContractTemplate.Instance.VoidContractTemplateAudit(Ids);
        }

        /// <summary>
        /// 获取合同说明模板主键+描述，无分页
        /// </summary>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<GetUnlimitedContractTemplateList_Out> GetUnlimitedContractTemplateList(GetUnlimitedContractTemplateList_In search_In)
        {
            int total = 0;
            var list = BLL_ContractTemplate.Instance.GetUnlimitedContractTemplateList(search_In, ref total);
            return GetApiTableOut(list, total);

        }

        /// <summary>
        /// 获取用户当前可申请的最大模板数
        /// </summary>
        /// <returns></returns>
        [HttpPost, PreLog]
        public int GetApplyCountUpLimit()
        {
            return BLL_ContractTemplate.Instance.GetAllowApplyLimit();
        }

        /// <summary>
        /// 根据服务年限筛选产品列表
        /// </summary>
        /// <param name="Cycle"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public List<GetProductListByServiceCycle_Out> GetProductListByServiceCycle(int? Cycle)
        {
            return DbOpe_crm_product.Instance.GetProductListByServiceCycle(Cycle);
        }

        /// <summary>
        /// 根据产品IdList获取服务年限，如果存在不同的服务年限，返回null
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public int? GetServiceCycleByProductList(GetServiceCycleByProductList_In search_In)
        {
            return DbOpe_crm_product.Instance.GetServiceCycleByProductList(search_In);
        }

        /// <summary>
        /// 合同说明模板使用产品列表
        /// </summary>
        /// <param name="IsApply"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public List<GetTemplateProductList_Out> GetTemplateProductList(bool IsApply)
        {
            return BLL_ContractTemplate.Instance.GetTemplateProductList(IsApply);
        }

        /// <summary>
        /// 后台人员修改合同说明模板
        /// </summary>
        /// <param name="updateContractTemplate_In"></param>
        [HttpPost, PreLog]
        public void UpdateContractTemplate(UpdateContractTemplate4Manager_In updateContractTemplate_In)
        {
            BLL_ContractTemplate.Instance.UpdateContractTemplate4Manager(updateContractTemplate_In);
        }

        /// <summary>
        /// 根据GTIS产品或环球搜产品赠送月份，自动选定特殊的固定模板
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetSpecialContractTemplateList_Out GetSpecialContractTemplateList(GetSpecialContractTemplateList_In search_In)
        {
            return BLL_ContractTemplate.Instance.GetSpecialContractTemplateList(search_In);
        }
    }
}


