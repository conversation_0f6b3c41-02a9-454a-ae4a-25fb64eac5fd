﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;
using CRM2_API.Model.BLLModel.Enum;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///发票原件明细信息表
    ///</summary>
    [SugarTable("crm_original_invoice_details")]
    public class Db_crm_original_invoice_details
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:发票原件信息表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OriginalInvoiceId {get;set;}

           /// <summary>
           /// Desc:发票形式
           /// Default:
           /// Nullable:False
           /// </summary>           
           public EnumInvoicingForm InvoiceForm {get;set;}

           /// <summary>
           /// Desc:发票类型
           /// Default:
           /// Nullable:False
           /// </summary>           
           public EnumInvoiceType InvoiceType {get;set;}

           /// <summary>
           /// Desc:发放份数
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int IssuedNum {get;set;}

           /// <summary>
           /// Desc:发票编号开始
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string InvoiceNoStart {get;set;}

           /// <summary>
           /// Desc:发票编号结束
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string InvoiceNoEnd {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>           
           public bool Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
