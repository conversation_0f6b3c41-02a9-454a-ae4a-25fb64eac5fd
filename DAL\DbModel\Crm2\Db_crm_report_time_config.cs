using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 报告时间配置表
    /// </summary>
    [SugarTable("crm_report_time_config")]
    public partial class Db_crm_report_time_config
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 报告类型：1-日报，2-周报，3-月报
        /// </summary>
        public int ReportType { get; set; }

        /// <summary>
        /// 配置名称
        /// </summary>
        public string ConfigName { get; set; }

        /// <summary>
        /// 规定时间类型：1-固定时间，2-相对时间
        /// </summary>
        public int RegularTimeType { get; set; }

        /// <summary>
        /// 规定时间值，JSON格式
        /// </summary>
        public string RegularTimeValue { get; set; }

        /// <summary>
        /// 截止时间类型：1-固定时间，2-相对时间
        /// </summary>
        public int FinalTimeType { get; set; }

        /// <summary>
        /// 截止时间值，JSON格式
        /// </summary>
        public string FinalTimeValue { get; set; }

        /// <summary>
        /// 迟交时间类型：1-固定时间，2-相对时间
        /// </summary>
        public int? LateTimeType { get; set; }

        /// <summary>
        /// 迟交时间值，JSON格式
        /// </summary>
        public string LateTimeValue { get; set; }

        /// <summary>
        /// 是否忽略节假日：0-不忽略，1-忽略
        /// </summary>
        public bool? IgnoreHoliday { get; set; }

        /// <summary>
        /// 是否启用：0-否，1-是
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        public DateTime? EffectiveDate { get; set; }

        /// <summary>
        /// 失效日期
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 删除标识
        /// </summary>
        public bool? Deleted { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? UpdateDate { get; set; }
    }
} 