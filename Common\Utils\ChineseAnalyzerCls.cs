﻿using JiebaNet.Segmenter;
using Lucene.Net.Analysis.Standard;
using Lucene.Net.Analysis;
using Lucene.Net.Util;
using System.Text.RegularExpressions;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbModelOpe.Crm2;
/// <summary>
///ChineseAnalyzer 的摘要说明
/// </summary>
public class ChineseAnalyzerCls
{
    public bool CheckEffectiveNameInput(string name, ref List<string> splitKeyWordsAfter)
    {
        if (string.IsNullOrEmpty(name))
        {
            return false;
        }
        var inputKeyWord = name.Trim().Replace("'", "");
        var inputKeyWordDM = ReplaceKeyword(inputKeyWord);
        if (inputKeyWord.Length >= 1)
        {
            var exceptWords = DbOpe_crm_tianyancha_othernames.Instance.Query_crm_tianyancha_othernames().Select(o => o.Names).ToList();
            exceptWords = exceptWords.Concat(LocalCache.LC_Address.CountryAndAreaCache.Select(c => c.Name)).ToList();
            exceptWords = exceptWords.Concat(LocalCache.LC_Address.CountryAndAreaCache.Select(c => c.NameEN)).ToList();
            exceptWords = exceptWords.Concat(LocalCache.LC_Address.ProvinceCache.Select(c => c.Name)).ToList();
            exceptWords = exceptWords.Concat(LocalCache.LC_Address.CityCache.Select(c => c.Name)).ToList();
            if (exceptWords.Where(t => t.Contains(inputKeyWord)).Count() <= 0)
            {
                //分词
                var splitKeyWords = GetSplitWords(inputKeyWord);
                splitKeyWordsAfter = new List<string>();
                //分词后排除单个字的分词和非关键词库里的词
                foreach (var splitKeyWord in splitKeyWords)
                {
                    if (exceptWords.Where(t => t.Contains(splitKeyWord)).Count() > 0)
                    {
                        continue;
                    }
                    else
                    {
                        splitKeyWordsAfter.Add(splitKeyWord);
                    }
                }
                if (splitKeyWordsAfter.Count != 0)
                {
                    return true;
                }
            }
        }
        return false;
    }

    public bool IsNotChinese(string input)
    {
        return !Regex.IsMatch(input, @"[\u4e00-\u9fa5]");
    }

    public List<string> GetSplitWords(string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return new List<string>();
        }

        // 标准化空格
        text = NormalizeSpaces(text);

        if (IsNotChinese(text))
        {
            //其他语言情况 空格分词
            var splitSpaceWords = text.Split(" ").Where(s => !string.IsNullOrEmpty(s)).ToList();
            return splitSpaceWords;
        }
        else
        {
            var segmenter = new JiebaSegmenter();
            //var cfs = segmenter.CutForSearch(dmStr).ToList();
            var c = segmenter.Cut(text).ToList();
            //var ca = segmenter.Cut(dmStr, true).ToList();
            //var cahmm = segmenter.Cut(dmStr, true, false).ToList();
            //var cfshmm = segmenter.CutForSearch(dmStr, false).ToList();
            //var chmm = segmenter.Cut(dmStr, false, false).ToList();
            //var ts = segmenter.Tokenize(dmStr, TokenizerMode.Search).ToList();
            //var t = segmenter.Tokenize(dmStr, TokenizerMode.Default).ToList();
            //var r = cfs.Concat(ca).Distinct().ToList();
            return c;
        }
    }

    public string ReplaceKeyword(string keyword, string replace = "")
    {
        if (keyword != "")
        {
            // 首先处理各种空格字符
            keyword = keyword.Replace("\u00A0", replace); // 不间断空格 (Non-breaking space)
            keyword = keyword.Replace("\u3000", replace); // 全角空格
            keyword = keyword.Replace("\u2002", replace); // EN SPACE
            keyword = keyword.Replace("\u2003", replace); // EM SPACE
            keyword = keyword.Replace("\u2009", replace); // THIN SPACE
            keyword = keyword.Replace("\u200B", replace); // 零宽空格

            // 原有的替换逻辑
            keyword = keyword.Replace("/", replace);
            keyword = keyword.Replace("#", replace).Replace("*", replace).Replace("_", replace);
            keyword = keyword.Replace("№", replace).Replace("°", replace).Replace("′", replace);
            keyword = keyword.Replace("‘", replace).Replace("’", replace).Replace("—", replace);
            keyword = keyword.Replace("–", replace).Replace("ˉ", replace).Replace("‐", replace).Replace("─", replace).Replace("-", replace);
            keyword = keyword.Replace("€", replace).Replace("“", replace).Replace("”", replace);
            keyword = keyword.Replace("^", replace).Replace("~", replace).Replace("┤", replace).Replace("ì", replace);
            keyword = keyword.Replace("。", replace).Replace("┐", replace).Replace("¨", replace).Replace("±", replace);
            keyword = keyword.Replace("§", replace).Replace("·", replace).Replace(",", replace).Replace("﹠", replace);
            keyword = keyword.Replace("¤", replace).Replace("├", replace).Replace("…", replace);
            keyword = keyword.Replace("└", replace).Replace("μ", replace).Replace("╚", replace).Replace("，", replace);
            keyword = keyword.Replace("+", replace).Replace(" and ", replace).Replace("&", replace).Replace(" ", replace).Replace("  ", replace).Replace("  ", replace);
            keyword = keyword.Replace(",", replace).Replace(".", replace).Replace("'", replace).Replace("(", replace).Replace(")", replace).Replace("（", replace).Replace("）", replace).Replace("。", replace);
            keyword = keyword.Replace("/", replace).Replace("\\", replace).Replace("\"", replace).Replace("*", replace).Replace("%", replace);
            keyword = keyword.Replace(";", replace).Replace(":", replace).Replace("“", replace).Replace("”", replace).Replace("?", replace).Replace("？", replace);
            keyword = keyword.Replace("■", replace).Replace("|", replace).Replace("<", replace).Replace(">", replace).Replace("[", replace).Replace("]", replace);
            keyword = keyword.Replace("、", replace).Replace("<<", replace).Replace(">>", replace).Replace("《", replace).Replace("》", replace);//Replace("«", replace).Replace("»", replace);
            keyword = keyword.Replace("；", replace).Replace(";", replace).Replace(":", replace).Replace("；", replace).Replace("'", replace).Replace("‘", replace).Replace("’", replace).Replace("“", replace).Replace("”", replace).Replace("\"", replace).Replace(",", replace);
            keyword = GetPureStr(keyword);
        }
        return keyword;
    }

    public string GetPureStr(string str)
    {
        List<int> asciiArray = new List<int> { 2, 9, 10, 11, 13, 26 };

        string temp = "";
        CharEnumerator CEnumerator = str.GetEnumerator();

        while (CEnumerator.MoveNext())
        {
            byte[] array = new byte[1];
            array = System.Text.Encoding.ASCII.GetBytes(CEnumerator.Current.ToString());

            int asciicode = (short)(array[0]);

            if (!asciiArray.Contains(asciicode))
            {
                temp += CEnumerator.Current.ToString();
            }
        }
        return temp;
    }

    /// <summary>
    /// 标准化字符串中的各种空格字符
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>标准化后的字符串</returns>
    private string NormalizeSpaces(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        // 替换各种特殊空格为标准空格
        input = input.Replace("\u00A0", " "); // 不间断空格 (Non-breaking space)
        input = input.Replace("\u3000", " "); // 全角空格
        input = input.Replace("\u2002", " "); // EN SPACE
        input = input.Replace("\u2003", " "); // EM SPACE
        input = input.Replace("\u2009", " "); // THIN SPACE
        input = input.Replace("\u200B", " "); // 零宽空格

        // 规范化多个连续空格
        input = Regex.Replace(input, @"\s+", " ").Trim();

        return input;
    }

    /// <summary>
    /// 分析字符串中的Unicode字符
    /// </summary>
    /// <param name="text">要分析的字符串</param>
    /// <returns>字符分析信息</returns>
    public string AnalyzeString(string text)
    {
        if (string.IsNullOrEmpty(text))
            return "空字符串";

        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        sb.AppendLine($"字符串: \"{text}\"");
        sb.AppendLine($"长度: {text.Length}");

        for (int i = 0; i < text.Length; i++)
        {
            char c = text[i];
            int unicodeValue = (int)c;
            string charType = char.IsWhiteSpace(c) ? "空白字符" :
                             char.IsControl(c) ? "控制字符" :
                             char.IsPunctuation(c) ? "标点符号" :
                             char.IsDigit(c) ? "数字" :
                             char.IsLetter(c) ? "字母" : "其他";

            sb.AppendLine($"位置 {i}: '{c}' (U+{unicodeValue:X4}) - {charType}");
        }

        return sb.ToString();
    }
}