using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using CRM2_API.Common.JWT;
using SqlSugar;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 退票相关功能
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        #region 退票相关方法

        /// <summary>
        /// 保存发票退票申请（创建或更新）
        /// </summary>
        /// <param name="request">退票申请请求</param>
        /// <returns>退票申请ID</returns>
        public string SaveInvoiceRefund(CreateInvoiceRefundRequest request)
        {
            if (request == null)
            {
                throw new ApiException("请求参数不能为空");
            }
            
            // 从TokenModel获取用户ID
            string userId = TokenModel.Instance.id;
            
            // 1. 验证发票是否存在
            var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                i.Id == request.InvoiceId && 
                i.Deleted != true);
                
            if (invoice == null)
            {
                throw new ApiException("未找到发票信息");
            }
            
            // 如果没有提供合同ID，则从发票记录中获取
            string contractId = request.ContractId;
            if (string.IsNullOrEmpty(contractId))
            {
                contractId = invoice.ContractId;
                // 如果发票中的合同ID也为空，抛出异常
                if (string.IsNullOrEmpty(contractId))
                {
                    throw new ApiException("无法确定发票所属合同，请提供合同ID");
                }
            }
            
            // 2. 验证发票所属合同权限
            var contract = DbOpe_crm_contract.Instance.GetContractById(contractId, true);
            if (contract == null)
            {
                throw new ApiException("未找到合同或该用户没有合同权限");
            }
            
            // 验证发票是否属于该合同
            if (invoice.ContractId != contractId)
            {
                throw new ApiException("发票不属于指定的合同");
            }
            
            // 3. 查询是否已有退票申请
            var existingApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => 
                r.InvoiceId == request.InvoiceId && 
                r.Deleted != true);
                
            // 标记是否为更新模式
            bool isUpdate = false;
            string refundId = string.Empty;
                
            if (existingApplication != null)
            {
                // 如果已有申请且状态为已完成，不允许修改
                if (existingApplication.AuditStatus == (int)EnumRefundApplicationStatus.Completed)
                {
                    throw new ApiException("该发票已完成退票，不能再次申请");
                }
                
                // 如果已有申请且状态为处理中，不允许修改
                if (existingApplication.AuditStatus == (int)EnumRefundApplicationStatus.Processed)
                {
                    throw new ApiException("该发票的退票申请正在处理中，不能修改");
                }
                
                // 如果已有申请且状态为申请中或已拒绝，可以更新
                if (existingApplication.AuditStatus == (int)EnumRefundApplicationStatus.Applied ||
                    existingApplication.AuditStatus == (int)EnumRefundApplicationStatus.Rejected)
                {
                    isUpdate = true;
                    refundId = existingApplication.Id;
                }
            }
            
            // 4. 验证退票金额
            decimal refundAmount = request.RefundAmount ?? 0;
            decimal invoiceAmount = invoice.InvoicedAmount;
            
            if (refundAmount <= 0)
            {
                throw new ApiException("退票金额必须大于0");
            }
            
            if (refundAmount > invoiceAmount)
            {
                throw new ApiException($"退票金额 {refundAmount} 不能大于发票金额 {invoiceAmount}");
            }
            
            // 5. 创建或更新退票申请
            try
            {
                // 使用事务创建或更新退票申请
                DbOpe_crm_invoice_refund_application.Instance.TransDeal(() =>
                {
                    if (isUpdate)
                    {
                        // 更新现有申请
                        existingApplication.ContractId = contractId;
                        existingApplication.RefundReason = request.RefundReason;
                        existingApplication.RefundAmount = request.RefundAmount ?? 0;
                        existingApplication.AuditStatus = (int)EnumRefundApplicationStatus.Applied; // 重置为申请中状态
                        existingApplication.UpdateUser = userId;
                        existingApplication.UpdateDate = DateTime.Now;
                        
                        // 更新退票申请记录
                        DbOpe_crm_invoice_refund_application.Instance.Update(existingApplication);
                        
                        LogUtil.AddLog($"更新退票申请成功，ID: {refundId}，发票ID: {request.InvoiceId}");
                    }
                    else
                    {
                        // 创建新的退票申请
                        var refundApplication = new Db_crm_invoice_refund_application
                        {
                            Id = Guid.NewGuid().ToString(),
                            ContractId = contractId,
                            InvoiceId = request.InvoiceId,
                            ApplicantId = userId,
                            ApplyTime = DateTime.Now,
                            RefundReason = request.RefundReason,
                            AuditStatus = (int)EnumRefundApplicationStatus.Applied,
                            RefundAmount = request.RefundAmount ?? 0,
                            RefundBackgroundRemark = invoice.Remark,
                            Deleted = false,
                            CreateUser = userId,
                            CreateDate = DateTime.Now,
                            UpdateUser = userId,
                            UpdateDate = DateTime.Now
                        };
                        
                        // 插入退票申请记录
                        DbOpe_crm_invoice_refund_application.Instance.Insert(refundApplication);
                        refundId = refundApplication.Id;
                        
                        LogUtil.AddLog($"创建退票申请成功，ID: {refundId}，发票ID: {request.InvoiceId}");
                    }
                    
                    // 更新发票的退票状态 - 在审核前仍保持正常状态
                    invoice.RefundStatus = (int)EnumInvoiceRefundStatus.Normal;
                    invoice.UpdateUser = userId;
                    invoice.UpdateDate = DateTime.Now;
                    DbOpe_crm_invoice.Instance.Update(invoice);
                    
                    // 同时更新发票申请的退票状态
                    UpdateInvoiceApplicationRefundStatus(
                        invoice.InvoiceApplicationId, 
                        EnumRefundApplicationStatus.Applied);
                });
                
                // 更新发票显示状态
                if (!string.IsNullOrEmpty(invoice.InvoiceApplicationId))
                {
                    UpdateInvoiceDisplayStatus(invoice.InvoiceApplicationId);
                }
                
                return refundId;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"保存退票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"保存退票申请系统异常: {ex.Message}");
                throw new ApiException("保存退票申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 审核发票退票申请（初审）
        /// </summary>
        /// <param name="request">退票审核请求</param>
        /// <returns>是否审核成功</returns>
        public bool ReviewInvoiceRefund(ReviewInvoiceRefundRequest request)
        {
            if (request == null)
            {
                throw new ApiException("请求参数不能为空");
            }
            
            // 从TokenModel获取用户ID
            string userId = TokenModel.Instance.id;
            
            // 1. 验证退票申请是否存在
            var refundApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => 
                r.Id == request.Id && 
                r.Deleted != true);
                
            if (refundApplication == null)
            {
                throw new ApiException("未找到退票申请信息");
            }
            
            // 2. 验证退票申请状态是否可以审核
            if (refundApplication.AuditStatus != (int)EnumRefundApplicationStatus.Applied)
            {
                throw new ApiException("当前退票申请状态不可审核");
            }
            // 3. 验证发票是否存在
            var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                i.Id == refundApplication.InvoiceId && 
                i.Deleted != true);
                
            if (invoice == null)
            {
                throw new ApiException("未找到发票信息");
            }
            
            // 3. 验证是否已进行OCR识别和比对（仅在审核通过时检查）
            if (request.ReviewResult == 1)
            {
                // 查询OCR识别记录
                var ocrRecognitions = DbOpe_crm_invoice_recognition.Instance.GetDataList(r => 
                    r.InvoiceApplicationId == request.Id && 
                    r.RecognitionType == 2 && // 类型2表示退票
                    r.Deleted != true);
                
                if (ocrRecognitions == null || !ocrRecognitions.Any())
                {
                    throw new ApiException("审核通过前必须进行OCR识别");
                }
                
                // 检查审核通过时是否提供了编辑后的红字发票信息
                if (request.EditedRefundInfo == null)
                {
                    throw new ApiException("审核通过时必须提供退票信息");
                }
                
                // 验证基本必填信息
                if (string.IsNullOrEmpty(request.EditedRefundInfo.RefundInvoiceNumber))
                {
                    throw new ApiException("退票发票号码不能为空");
                }
                
                if (!request.EditedRefundInfo.RefundDate.HasValue)
                {
                    throw new ApiException("退票日期不能为空");
                }
                
                if (request.EditedRefundInfo.RefundAmount <= 0)
                {
                    throw new ApiException("退票金额必须大于0");
                }
                if(request.EditedRefundInfo.RefundAmount > invoice.TotalAmount)
                {
                    throw new ApiException("退票金额不能大于原发票金额");
                }
            }
            
            try
            {
                // 使用事务处理审核流程
                DbOpe_crm_invoice_refund_application.Instance.TransDeal(() =>
                {
                         // 查找是否已有该申请的审核记录
                    var existingReviews = DbOpe_crm_invoice_refund_review.Instance.GetDataList(
                        r => r.RefundApplicationId == request.Id && 
                             r.Deleted != true)
                        .OrderByDescending(r => r.ProcessTime)
                        .ToList();
                    
                    if (existingReviews != null && existingReviews.Count > 0)
                    {
                        // 有历史记录，且当前申请状态是"已拒绝"被驳回后重新提交
                        // 将以前的审核记录标记为删除（软删除），保留历史并避免数据混乱
                        foreach (var review in existingReviews)
                        {
                            review.Deleted = true;
                            review.UpdateUser = userId;
                            review.UpdateDate = DateTime.Now;
                            DbOpe_crm_invoice_refund_review.Instance.Update(review);
                            LogUtil.AddLog($"标记旧审核记录为删除状态，ID: {review.Id}, 申请ID: {request.Id}");
                        }
                    }
                    


                    // 创建退票审核记录
                    var refundReview = new Db_crm_invoice_refund_review
                    {
                        Id = Guid.NewGuid().ToString(),
                        RefundApplicationId = request.Id,
                        InvoiceId = refundApplication.InvoiceId,
                        ContractId = refundApplication.ContractId,
                        ProcessorId = userId,
                        ProcessTime = DateTime.Now,
                        ProcessorRemark = request.ReviewComments,
                        Deleted = false,
                        CreateUser = userId,
                        CreateDate = DateTime.Now,
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now
                    };
                    
                    // 如果审核通过且提供了编辑后的退票信息
                    if (request.ReviewResult == 1 && request.EditedRefundInfo != null)
                    {
                        refundReview.AuditStatus = (int)EnumRefundReviewStatus.WaitingAudit;
                        // 记录经办人修改后的退票信息
                        refundReview.RefundNumber = request.EditedRefundInfo.RefundInvoiceNumber;
                        refundReview.RefundDate = request.EditedRefundInfo.RefundDate;
                        refundReview.RefundAmount = request.EditedRefundInfo.RefundAmount??0;
                        refundReview.InvoiceType = request.EditedRefundInfo.InvoiceType;
                        
                        // 记录修改的字段信息（JSON格式）
                        var latestRecognition = DbOpe_crm_invoice_recognition.Instance.GetDataList(r => 
                            r.InvoiceApplicationId == request.Id && 
                            r.RecognitionType == 2 && 
                            r.RecognitionStatus == 1 && 
                            r.Deleted != true)
                            .OrderByDescending(r => r.CreateDate)
                            .FirstOrDefault();
                        
                        if (latestRecognition != null)
                        {
                            List<ModifiedField> modifiedFields = new List<ModifiedField>();
                            
                            // 检查发票号码是否被修改
                            if (latestRecognition.InvoiceNumber != request.EditedRefundInfo.RefundInvoiceNumber)
                            {
                                modifiedFields.Add(new ModifiedField
                                {
                                    FieldName = "RefundInvoiceNumber",
                                    FieldDescription = "红字发票号码",
                                    OriginalValue = latestRecognition.InvoiceNumber,
                                    ModifiedValue = request.EditedRefundInfo.RefundInvoiceNumber
                                });
                            }
                            
                            // 检查发票日期是否被修改
                            string originalDateStr = latestRecognition.InvoiceDate?.ToString("yyyy-MM-dd") ?? "";
                            string newDateStr = request.EditedRefundInfo.RefundDate?.ToString("yyyy-MM-dd") ?? "";
                            if (originalDateStr != newDateStr)
                            {
                                modifiedFields.Add(new ModifiedField
                                {
                                    FieldName = "RefundDate",
                                    FieldDescription = "红字发票日期",
                                    OriginalValue = originalDateStr,
                                    ModifiedValue = newDateStr
                                });
                            }
                            
                            // 检查发票金额是否被修改
                            if (latestRecognition.TotalAmount != request.EditedRefundInfo.RefundAmount)
                            {
                                modifiedFields.Add(new ModifiedField
                                {
                                    FieldName = "RefundAmount",
                                    FieldDescription = "退票金额",
                                    OriginalValue = latestRecognition.TotalAmount?.ToString("F2") ?? "0.00",
                                    ModifiedValue = (request.EditedRefundInfo.RefundAmount == null?0: request.EditedRefundInfo.RefundAmount.Value).ToString("F2")
                                });
                            }
                            
                            // 将修改字段转为JSON存储
                            if (modifiedFields.Any())
                            {
                                refundReview.ModifiedFields = System.Text.Json.JsonSerializer.Serialize(modifiedFields);
                            }
                        }
                    }
                    else{
                        refundReview.AuditStatus = (int)EnumRefundReviewStatus.ProcessRejected;

                    }
                    
                    // 插入退票审核记录
                    DbOpe_crm_invoice_refund_review.Instance.Insert(refundReview);
                    
                    // 更新退票申请记录
                    if (request.ReviewResult == 1)
                    {
                        // 审核通过 - 更新状态为待复核
                        refundApplication.AuditStatus = (int)EnumRefundApplicationStatus.Processed;
                        
                        // 如果提供了编辑后的退票信息，更新到退票申请
                        if (request.EditedRefundInfo != null)
                        {
                            // 此处可以添加退票信息到退票申请（如果需要）
                            // 例如，可以将红字发票号码等信息保存在申请中
                            LogUtil.AddLog($"更新退票申请退票信息，申请ID: {request.Id}，退票号码: {request.EditedRefundInfo.RefundInvoiceNumber}");
                        }
                    }
                    else
                    {
                        // 审核拒绝 - 更新状态为已拒绝
                        refundApplication.AuditStatus = (int)EnumRefundApplicationStatus.Rejected;
                        
                        if (invoice != null)
                        {
                            // 审核拒绝后，将发票状态设置回正常
                            invoice.RefundStatus = (int)EnumInvoiceRefundStatus.Normal;
                            invoice.UpdateUser = userId;
                            invoice.UpdateDate = DateTime.Now;
                            DbOpe_crm_invoice.Instance.Update(invoice);
                            
                            // 同时更新发票申请的退票状态
                            UpdateInvoiceApplicationRefundStatus(
                                invoice.InvoiceApplicationId, 
                                EnumRefundApplicationStatus.Applied);
                        }
                    }
                    
                    refundApplication.UpdateUser = userId;
                    refundApplication.UpdateDate = DateTime.Now;
                    refundApplication.RefundBackgroundRemark = request.RefundBackgroundRemark;
                    DbOpe_crm_invoice_refund_application.Instance.Update(refundApplication);
                    
                    LogUtil.AddLog($"审核退票申请   ID: {request.Id}, 结果: {(request.ReviewResult == (int)EnumRefundReviewStatus.Approved ? "通过" : "拒绝")}");
                });
                
                // 更新发票显示状态
                invoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == refundApplication.InvoiceId && i.Deleted != true);
                if (invoice != null && !string.IsNullOrEmpty(invoice.InvoiceApplicationId))
                {
                    UpdateInvoiceDisplayStatus(invoice.InvoiceApplicationId);
                }
                
                return true;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"审核退票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"审核退票申请系统异常: {ex.Message}");
                throw new ApiException("审核退票申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 作废发票退票申请
        /// </summary>
        /// <param name="id">退票申请ID</param>
        /// <returns>是否作废成功</returns>
        public bool InvalidateInvoiceRefund(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                throw new ApiException("退票申请ID不能为空");
            }
            
            // 从TokenModel获取用户ID
            string userId = TokenModel.Instance.id;
            
            // 1. 验证退票申请是否存在
            var refundApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => 
                r.Id == id && 
                r.Deleted != true);
                
            if (refundApplication == null)
            {
                throw new ApiException("未找到退票申请信息");
            }
            
            // 2. 验证退票申请状态是否可以撤销
            if (refundApplication.AuditStatus != (int)EnumRefundApplicationStatus.Applied &&
                refundApplication.AuditStatus != (int)EnumRefundApplicationStatus.Processed &&
                refundApplication.AuditStatus != (int)EnumRefundApplicationStatus.Rejected)
            {
                throw new ApiException("当前退票申请状态不可撤销");
            }
            
            try
            {
                // 使用事务处理作废
                DbOpe_crm_invoice_refund_application.Instance.TransDeal(() =>
                {
                    // 获取发票信息
                    var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                        i.Id == refundApplication.InvoiceId && 
                        i.Deleted != true);
                        
                    if (invoice != null)
                    {
                        // 作废退票申请时将发票状态设置回正常
                        invoice.RefundStatus = (int)EnumInvoiceRefundStatus.Normal;
                        invoice.UpdateUser = userId;
                        invoice.UpdateDate = DateTime.Now;
                        DbOpe_crm_invoice.Instance.Update(invoice);
                        
                        // 同时更新发票申请的退票状态
                        UpdateInvoiceApplicationRefundStatus(
                            invoice.InvoiceApplicationId, 
                            EnumRefundApplicationStatus.Invalidated);
                            
                        LogUtil.AddLog($"发票 {invoice.Id} 退票状态已重置为正常");
                    }
                    
                    // 更新退票申请为已作废状态
                    refundApplication.AuditStatus = (int)EnumRefundApplicationStatus.Invalidated;
                    refundApplication.UpdateUser = userId;
                    refundApplication.UpdateDate = DateTime.Now;
                    DbOpe_crm_invoice_refund_application.Instance.Update(refundApplication);
                    
                    LogUtil.AddLog($"作废退票申请成功，ID: {id}");
                });
                
                // 更新发票显示状态
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == refundApplication.InvoiceId && i.Deleted != true);
                if (invoice != null && !string.IsNullOrEmpty(invoice.InvoiceApplicationId))
                {
                    UpdateInvoiceDisplayStatus(invoice.InvoiceApplicationId);
                }
                
                return true;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"作废退票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"作废退票申请系统异常: {ex.Message}");
                throw new ApiException("作废退票申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 更新发票申请的退票状态
        /// </summary>
        /// <param name="invoiceApplicationId">发票申请ID</param>
        /// <param name="refundStatus">退票状态</param>
        /// <returns></returns>
        public void UpdateInvoiceApplicationRefundStatus(string invoiceApplicationId, EnumRefundApplicationStatus refundStatus)
        {
            try
            {
                if (string.IsNullOrEmpty(invoiceApplicationId))
                {
                    LogUtil.AddErrorLog($"更新发票申请退票状态，申请ID为空");
                    return;
                }

                bool result = DbOpe_crm_invoice_application.Instance.UpdateRefundStatus(invoiceApplicationId, refundStatus, TokenModel.Instance.id);
                if (result)
                {
                    LogUtil.AddLog($"更新发票申请退票状态成功，申请ID：{invoiceApplicationId}，退票状态：{refundStatus}");
                }
                else
                {
                    LogUtil.AddErrorLog($"更新发票申请退票状态失败，申请ID：{invoiceApplicationId}，退票状态：{refundStatus}");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新发票申请退票状态异常，申请ID：{invoiceApplicationId}，退票状态：{refundStatus}，异常信息：{ex.Message}", ex);
                throw new ApiException($"更新发票申请退票状态异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 确认退票复核结果（复核）
        /// </summary>
        /// <param name="request">退票复核确认请求</param>
        /// <returns>操作结果</returns>
        public bool ConfirmInvoiceRefundReview(ReviewInvoiceRefundConfirmationRequest request)
        {
            if (request == null || string.IsNullOrEmpty(request.RefundApplicationId))
            {
                throw new ApiException("请求参数异常");
            }

            // 获取退票申请信息
            var refundApp = DbOpe_crm_invoice_refund_application.Instance.GetData(c => 
                c.Id == request.RefundApplicationId && c.Deleted != true);
                
            if (refundApp == null)
            {
                throw new ApiException("未找到相关退票申请");
            }

            // 只有"待复核"状态的申请可以确认复核结果
            if (refundApp.AuditStatus != (int)EnumRefundApplicationStatus.Processed)
            {
                throw new ApiException("该退票申请当前状态不可复核");
            }

            try
            {
                // 使用事务处理复核流程
                DbOpe_crm_invoice_refund_application.Instance.TransDeal(() =>
                {
                    // 更新退票申请状态
                    int auditStatus;
                    if (request.ReviewResult) // 审核通过
                    {
                        auditStatus = (int)EnumRefundApplicationStatus.Completed;
                    }
                    else // 审核拒绝
                    {
                        // 审核拒绝 - 更新状态为申请中 经办人重新提交
                        auditStatus = (int)EnumRefundApplicationStatus.Applied;
                    }
                    // 更新申请状态
                    refundApp.UpdateUser = TokenModel.Instance.id;
                    refundApp.UpdateDate = DateTime.Now;
                    refundApp.AuditStatus = auditStatus;
                    refundApp.RefundBackgroundRemark = request.RefundBackgroundRemark;
                    DbOpe_crm_invoice_refund_application.Instance.Update(refundApp);

                    // 查找最新的一条审核记录
                    var currentReview = DbOpe_crm_invoice_refund_review.Instance.GetDataList(
                        r => r.RefundApplicationId == request.RefundApplicationId && 
                             r.Deleted != true)
                        .OrderByDescending(r => r.ProcessTime)
                        .FirstOrDefault();
                        
                    if (currentReview == null)
                    {
                        // 未找到审核记录，记录错误日志
                        LogUtil.AddErrorLog($"复核发票申请失败: 未找到该申请的审核记录，ID: {request.RefundApplicationId}");
                        throw new ApiException("未找到该申请的审核记录，无法完成复核");
                    }
                    
                    // 更新审核记录的审核状态
                    currentReview.AuditStatus = request.ReviewResult ? 
                        (int)EnumRefundReviewStatus.Approved : 
                        (int)EnumRefundReviewStatus.RefundReviewRejected;   

                    currentReview.UpdateUser = TokenModel.Instance.id;
                    currentReview.UpdateDate = DateTime.Now;
                    currentReview.AuditorId =  TokenModel.Instance.id;
                    currentReview.AuditTime = DateTime.Now;
                    currentReview.AuditFeedback = request.ReviewComment;
                    DbOpe_crm_invoice_refund_review.Instance.Update(currentReview);

                    // 如果审核通过，更新发票退票状态
                    if (request.ReviewResult)
                    {
                        // 获取原发票信息
                        var originalInvoice = DbOpe_crm_invoice.Instance.GetData(i => 
                            i.Id == refundApp.InvoiceId && 
                            i.Deleted != true);
                            
                        if (originalInvoice != null)
                        {
                            // 根据退票金额与发票金额比较，决定是全部红冲还是部分红冲
                            if (currentReview.RefundAmount >= originalInvoice.InvoicedAmount)
                            {
                                // 退票金额大于等于发票金额，标记为全部红冲
                                originalInvoice.RefundStatus = (int)EnumInvoiceRefundStatus.FullyRefunded;
                            }
                            else
                            {
                                // 退票金额小于发票金额，标记为部分红冲
                                originalInvoice.RefundStatus = (int)EnumInvoiceRefundStatus.PartiallyRefunded;
                            }
                            
                            // 更新原发票状态
                            originalInvoice.UpdateUser = TokenModel.Instance.id;
                            originalInvoice.UpdateDate = DateTime.Now;
                            DbOpe_crm_invoice.Instance.Update(originalInvoice);
                            
                            // 同时更新发票申请的退票状态
                            UpdateInvoiceApplicationRefundStatus(
                                originalInvoice.InvoiceApplicationId, 
                                EnumRefundApplicationStatus.Completed);
                            
                            LogUtil.AddLog($"原发票 {originalInvoice.Id} 退票状态更新为: {(originalInvoice.RefundStatus == (int)EnumInvoiceRefundStatus.FullyRefunded ? "全部红冲" : "部分红冲")}");
                            
                            // 处理原发票的匹配状态和匹配关系
                            if (originalInvoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess)
                            {
                                // 之前已匹配成功，改为"匹配成功（作废）"
                                originalInvoice.MatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccessVoid;
                            }
                            else
                            {
                                // 所有其他状态，改为"未匹配（作废）"
                                originalInvoice.MatchingStatus = (int)EnumInvoiceMatchingStatus.NotMatchedVoid;
                            }

                            // 更新发票状态
                            var redInvoiceId = Guid.NewGuid().ToString();
                            originalInvoice.UpdateUser = TokenModel.Instance.id;
                            originalInvoice.UpdateDate = DateTime.Now;
                            originalInvoice.RelatedInvoiceId = redInvoiceId;
                            DbOpe_crm_invoice.Instance.Update(originalInvoice);
                            
                            // 释放发票关联的到账记录
                            ReleaseInvoiceReceiptMatching(originalInvoice.InvoiceApplicationId, TokenModel.Instance.id);
                            
                            // 创建红字发票记录
                            var redInvoice = new Db_crm_invoice
                            {
                                Id = redInvoiceId,
                                ContractId = originalInvoice.ContractId,
                                ReceiptId = originalInvoice.ReceiptId,
                                InvoiceApplicationId = request.RefundApplicationId, // 使用退票申请ID作为申请ID
                                InvoiceReviewId = currentReview.Id, // 使用退票审核ID
                                InvoiceNumber = currentReview.RefundNumber, // 红字发票号码
                                InvoiceCode = "", // 可能没有识别发票代码，暂留空
                                InvoicingDate = currentReview.RefundDate, // 退票日期
                                InvoicedAmount = currentReview.RefundAmount, // 退票金额
                                TotalAmount = currentReview.RefundAmount, // 总金额
                                TaxAmount = 0, // 如果有税额信息可以设置
                                BillingCompany = originalInvoice.BillingCompany, // 与原发票相同
                                BillingHeader = originalInvoice.BillingHeader, // 与原发票相同
                                CreditCode = originalInvoice.CreditCode, // 与原发票相同
                                BillingType = originalInvoice.BillingType, // 与原发票相同
                                InvoiceType = currentReview.InvoiceType == null?originalInvoice.InvoiceType:currentReview.InvoiceType.Value, // 发票类型
                                InvoicingDetails = "", // 退票明细
                                MatchingStatus = originalInvoice.MatchingStatus, // 与原发票相同
                                RefundStatus = (int)EnumInvoiceRefundStatus.Normal, // 红字发票本身状态为正常
                                Recipient = originalInvoice.Recipient, // 与原发票相同
                                Email = originalInvoice.Email, // 与原发票相同
                                IsSupplementInvoice = false, // 不是补充发票
                                InvoiceLink = "", // 可能没有链接
                                TransactionType = 2, // 2表示退票
                                RelatedInvoiceId = originalInvoice.Id, // 关联原发票ID
                                Remark = request.RefundBackgroundRemark,
                                Deleted = false,
                                CreateUser = TokenModel.Instance.id,
                                CreateDate = DateTime.Now,
                                UpdateUser = TokenModel.Instance.id,
                                UpdateDate = DateTime.Now
                            };
                            
                            // 插入红字发票记录
                            bool insertResult = DbOpe_crm_invoice.Instance.Insert(redInvoice) > 0;
                            if (!insertResult)
                            {
                                throw new ApiException("创建红字发票记录失败");
                            }
                            
                            // 更新退票申请记录，关联红字发票ID
                            refundApp.RedInvoiceId = redInvoice.Id;
                            DbOpe_crm_invoice_refund_application.Instance.Update(refundApp);
                            
                            // 更新最新的审核记录，关联红字发票ID
                            currentReview.RedInvoiceId = redInvoice.Id;
                            DbOpe_crm_invoice_refund_review.Instance.Update(currentReview);
                            
                            LogUtil.AddLog($"成功创建红字发票记录，ID: {redInvoice.Id}, 红字发票号: {redInvoice.InvoiceNumber}, 金额: {redInvoice.InvoicedAmount}");
                            LogUtil.AddLog($"已更新退票申请和审核记录，关联红字发票ID: {redInvoice.Id}");
                        }
                    }
                    
                    LogUtil.AddLog($"确认退票复核结果   ID: {request.RefundApplicationId}, 结果: {(request.ReviewResult ? "通过" : "拒绝")}");
                });
                
                // 更新发票显示状态
                var originalInvoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == refundApp.InvoiceId && i.Deleted != true);
                if (originalInvoice != null && !string.IsNullOrEmpty(originalInvoice.InvoiceApplicationId))
                {
                    UpdateInvoiceDisplayStatus(originalInvoice.InvoiceApplicationId);
                }
                
                return true;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"确认退票复核结果业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"确认退票复核结果系统异常: {ex.Message}");
                throw new ApiException("确认退票复核结果失败，请联系系统管理员");
            }
        }

        #endregion
        
        #region 匹配关系释放

        /// <summary>
        /// 释放发票和到账记录的匹配关系
        /// </summary>
        /// <param name="applicationId">发票申请ID</param>
        /// <param name="userId">操作用户ID</param>
        private void ReleaseInvoiceReceiptMatching(string applicationId, string userId)
        {
            if (string.IsNullOrEmpty(applicationId))
            {
                LogUtil.AddLog($"申请ID为空，无法释放匹配关系");
                return;
            }
            
            // 查找与申请ID关联的所有匹配记录
            var matchingRecords = DbOpe_crm_invoice_receipt_matching.Instance.GetDataList(m => 
                m.InvoiceApplicationId == applicationId && m.Deleted != true);
            
            // 如果没有匹配记录，直接返回
            if (matchingRecords == null || matchingRecords.Count == 0)
            {
                LogUtil.AddLog($"申请[{applicationId}]没有关联的到账匹配记录，无需释放");
                return;
            }
            
            foreach (var record in matchingRecords)
            {
                // 软删除匹配记录
                record.Deleted = true;
                record.UpdateUser = userId;
                record.UpdateDate = DateTime.Now;
                record.Remark = (record.Remark ?? "") + "(因退票释放)";
                DbOpe_crm_invoice_receipt_matching.Instance.Update(record);
                
                // 记录日志
                LogUtil.AddLog($"因退票解除匹配关系: 申请ID[{applicationId}], 到账记录[{record.ReceiptId}]");
            }
        }

        #endregion
    }
} 