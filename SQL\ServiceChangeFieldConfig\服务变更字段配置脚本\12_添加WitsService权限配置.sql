-- 为GetWitsServiceInfo接口添加权限配置
-- 执行日期: 2025-01-29

-- ================================
-- 变量定义
-- ================================
SET @now = NOW();
SET @userId = 'sys'; -- 系统创建

-- ================================
-- 1. 为慧思产品模块添加GetWitsServiceInfo接口权限
-- ================================
-- 动态获取慧思产品模块ID
SET @witsProductId = (SELECT `Id` FROM sys_form WHERE `Describe` = '合同管理-服务管理-慧思产品' AND `Deleted` = 0 LIMIT 1);

-- 检查慧思产品模块是否存在并插入权限配置
INSERT INTO sys_form (`Id`, `Name`, `Describe`, `Type`, `ParentId`, `Title`, `OrderNum`, `ControllerName`, `MethodName`, `LogTemplate`, `Deleted`, `CreateUser`, `CreateDate`)
SELECT 
    UUID(), 
    '获取慧思服务信息', 
    '合同管理-服务管理-慧思产品-获取慧思服务信息', 
    5, 
    @witsProductId, 
    '获取慧思服务信息', 
    100, 
    'ContractProductService', 
    'GetWitsServiceInfo', 
    NULL, 
    0, 
    @userId, 
    @now
WHERE @witsProductId IS NOT NULL;

-- 显示慧思产品模块配置结果
SELECT 
    CASE 
        WHEN @witsProductId IS NOT NULL THEN CONCAT('慧思产品模块权限配置成功，模块ID: ', @witsProductId)
        ELSE '警告：未找到慧思产品模块，请检查模块描述是否正确'
    END AS message;

-- ================================
-- 2. 为合同列表模块添加GetWitsServiceInfo接口权限
-- ================================
-- 动态获取合同列表模块ID
SET @contractListId = (SELECT `Id` FROM sys_form WHERE `Describe` = '合同管理-销售合同-合同列表' AND `Deleted` = 0 LIMIT 1);

-- 检查合同列表模块是否存在并插入权限配置
INSERT INTO sys_form (`Id`, `Name`, `Describe`, `Type`, `ParentId`, `Title`, `OrderNum`, `ControllerName`, `MethodName`, `LogTemplate`, `Deleted`, `CreateUser`, `CreateDate`)
SELECT 
    UUID(), 
    '获取慧思服务信息', 
    '合同管理-销售合同-合同列表-获取慧思服务信息', 
    5, 
    @contractListId, 
    '获取慧思服务信息', 
    100, 
    'ContractProductService', 
    'GetWitsServiceInfo', 
    NULL, 
    0, 
    @userId, 
    @now
WHERE @contractListId IS NOT NULL;

-- 显示合同列表模块配置结果
SELECT 
    CASE 
        WHEN @contractListId IS NOT NULL THEN CONCAT('合同列表模块权限配置成功，模块ID: ', @contractListId)
        ELSE '警告：未找到合同列表模块，请检查模块描述是否正确'
    END AS message;

-- ================================
-- 验证配置结果
-- ================================
SELECT 'GetWitsServiceInfo接口权限配置完成！' AS message;

-- 显示添加的权限配置
SELECT 
    f.Id,
    f.Name,
    f.Describe,
    f.Title,
    f.ControllerName,
    f.MethodName,
    f.ParentId,
    p.Title as ParentTitle
FROM sys_form f
LEFT JOIN sys_form p ON f.ParentId = p.Id
WHERE f.MethodName = 'GetWitsServiceInfo' 
  AND f.ControllerName = 'ContractProductService'
  AND f.Deleted = 0
ORDER BY f.CreateDate DESC; 