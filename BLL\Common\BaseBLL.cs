﻿using CRM2_API.Common.JWT;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace CRM2_API.BLL.Common
{
    /// <summary>
    /// HttpContext访问器持有者，用于在非控制器类中访问HttpContext
    /// </summary>
    public static class HttpContextAccessorHolder
    {
        public static IHttpContextAccessor HttpContextAccessor { get; set; }
    }

    /// <summary>
    /// 服务持有者，用于在非控制器类中访问注册的服务
    /// </summary>
    public static class ServiceHolder
    {
        /// <summary>
        /// 任务服务
        /// </summary>
        public static object TaskService { get; set; }

        // 可以添加更多服务
    }

    /// <summary>
    /// BLL基础类
    /// </summary>
    /// <typeparam name="BLLClass">实际操作BLL的类</typeparam>
    public class BaseBLL<BLLClass> where BLLClass : class,new()
    {
        private static AsyncLocal<BLLClass> _Instance=new AsyncLocal<BLLClass>();

        /// <summary>
        /// 用户token信息
        /// </summary>
        public TokenModel UserTokenInfo => TokenModel.Instance;

        public string UserId => TokenModel.Instance.id;
        /// <summary>
        /// 实例化当前线程的BLL对象
        /// </summary>
        public static BLLClass Instance
        {
            get
            {
                if(_Instance.Value is null)
                    _Instance.Value=new BLLClass();
                return _Instance.Value;
            }
        }
        /// <summary>
        /// 获取注入的服务
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        protected T GetService<T>() where T : class
        {
            return ServiceLocator.GetService<T>();
        }
    }
    /// <summary>
    /// 静态服务定位器
    /// </summary>
    public static class ServiceLocator
    {
        private static IServiceProvider? _serviceProvider;

        /// <summary>
        /// 初始化服务定位器
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        public static void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <summary>
        /// 获取服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        public static T GetService<T>() where T : class
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("服务定位器尚未初始化。请先调用Initialize方法。");
            }

            return _serviceProvider.GetRequiredService<T>();
        }
    }

}
