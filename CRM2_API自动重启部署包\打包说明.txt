# 打包说明

要将这些配置文件打包并传输到服务器，可以按照以下步骤操作：

## 打包文件

在本地执行以下命令将所有配置文件打包：

```bash
# 进入部署包目录
cd CRM2_API自动重启部署包

# 打包为tar.gz文件
tar -czvf crm-api-restart-deploy.tar.gz *
```

## 传输到服务器

使用scp或其他工具将打包文件传输到服务器：

```bash
# 使用scp命令传输
scp crm-api-restart-deploy.tar.gz 用户名@服务器IP:/tmp/
```

## 在服务器上解压

登录到服务器后，执行以下操作：

```bash
# 进入临时目录
cd /tmp

# 解压文件
mkdir crm-deploy
tar -xzvf crm-api-restart-deploy.tar.gz -C crm-deploy
cd crm-deploy

# 设置执行权限
chmod +x install-crm-service.sh
chmod +x monitor-crm-service.sh

# 按照部署说明进行部署
```

## 注意事项

1. 确保所有文件都使用UTF-8编码，避免中文字符显示乱码
2. 确保shell脚本使用Unix格式的行尾（LF而不是CRLF）
3. 部署前确认配置文件中的路径与服务器实际路径一致 