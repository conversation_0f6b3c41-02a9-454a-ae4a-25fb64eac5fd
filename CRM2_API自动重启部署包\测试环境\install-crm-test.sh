#!/bin/bash

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
  echo "请使用sudo或root权限运行此脚本"
  exit 1
fi

echo "=== 开始安装CRM2 API测试环境服务 ==="

# 检查脚本是否有Windows行尾问题
if file "$0" | grep -q CRLF; then
  echo "警告：脚本包含Windows行尾(CRLF)，这可能导致问题。"
  echo "请运行: dos2unix $0"
  exit 1
fi

# 停止现有服务
echo "正在停止现有CRM服务..."
pkill -f "dotnet.*CRM2_API.dll" || true

# 复制服务文件到systemd目录
cp crm-api-test.service /etc/systemd/system/

# 确保日志目录存在
mkdir -p /home/<USER>/log
chmod 755 /home/<USER>/log

# 重新加载systemd配置
systemctl daemon-reload

# 启用服务（开机自启）
systemctl enable crm-api-test.service
echo "已设置开机自启动"

# 从publish目录复制最新文件
if [ -d "/home/<USER>/publish" ]; then
  echo "从publish目录复制最新文件..."
  cp -rf /home/<USER>/publish/* /home/<USER>/api/Release/
  echo "文件复制完成"
else
  echo "警告: /home/<USER>/publish 目录不存在，未执行文件复制"
fi

# 启动服务
echo "正在启动CRM2 API服务..."
systemctl start crm-api-test.service
echo "服务已启动"

# 检查服务状态
echo "=== 服务状态 ==="
systemctl status crm-api-test.service

echo ""
echo "=== 安装完成 ==="
echo "您可以使用以下命令管理服务："
echo "  启动服务: sudo systemctl start crm-api-test.service"
echo "  停止服务: sudo systemctl stop crm-api-test.service"
echo "  重启服务: sudo systemctl restart crm-api-test.service"
echo "  查看状态: sudo systemctl status crm-api-test.service"
echo "  查看日志: sudo journalctl -u crm-api-test.service" 