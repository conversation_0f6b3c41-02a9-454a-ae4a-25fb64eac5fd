using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        /// <summary>
        /// 收款公司信息表缓存
        /// </summary>
        public class CollectingCompany
        {
            const string COLLECTINGCOMPANY_ALL = "collectingcompany_all";
            const string COLLECTINGCOMPANY_ID = "collectingcompany_id_";
            const string COLLECTINGCOMPANY_SELLERNAME = "collectingcompany_sellername_";

            /// <summary>
            /// 缓存的简化模型
            /// </summary>
            public class CollectingCompanySimple
            {
                /// <summary>
                /// 主键ID
                /// </summary>
                public string Id { get; set; }
                
                /// <summary>
                /// 公司名称
                /// </summary>
                public string CollectingCompanyName { get; set; }
                /// <summary>
                /// 销售方名称
                /// </summary>
                public string SellerName { get; set; }
                
                /// <summary>
                /// 隐藏标识
                /// </summary>
                public bool? isHidden { get; set; }
            }
            /// <summary>
            /// 初始化收款公司缓存
            /// </summary>
            public static void InitializeCache()
            {
                // 从数据库获取并更新缓存
                var dbCompanies = DbOpe_crm_collectingcompany.Instance.GetDataList(r => r.Deleted == false);
                ClearAllCache();
                if (dbCompanies != null && dbCompanies.Count > 0)
                {
                    var companies = dbCompanies.Select(r => new CollectingCompany.CollectingCompanySimple
                    {
                        Id = r.Id,
                        CollectingCompanyName = r.CollectingCompanyName,
                        SellerName = r.SellerName,
                        isHidden = r.isHidden
                    }).ToList();
                    // 保存到缓存
                    CollectingCompany.SaveAllCollectingCompanies(companies);
                }
            }
            
            /// <summary>
            /// 获取所有收款公司简化信息
            /// </summary>
            /// <returns>收款公司列表，如果缓存不存在返回null</returns>
            public static List<CollectingCompanySimple> GetAllCollectingCompanies()
            {
                if (RedisHelper.Exists(COLLECTINGCOMPANY_ALL))
                    return RedisHelper.Get<List<CollectingCompanySimple>>(COLLECTINGCOMPANY_ALL);
                return null;
            }

            /// <summary>
            /// 保存所有收款公司简化信息到缓存
            /// </summary>
            /// <param name="companies">收款公司列表</param>
            public static void SaveAllCollectingCompanies(List<CollectingCompanySimple> companies)
            {
                RedisHelper.Set(COLLECTINGCOMPANY_ALL, companies, TimeSpan.FromHours(26));

                // 同时保存每个公司的单独缓存
                if (companies != null && companies.Count > 0)
                {
                    foreach (var company in companies)
                    {
                        if (!string.IsNullOrEmpty(company.Id))
                        {
                            SaveCollectingCompany(company);
                        }

                        // 同时保存按销售方名称的缓存
                        if (!string.IsNullOrEmpty(company.SellerName))
                        {
                            SaveCollectingCompanyBySellerName(company);
                        }
                    }
                }
            }

            /// <summary>
            /// 根据ID获取收款公司简化信息
            /// </summary>
            /// <param name="id">收款公司ID</param>
            /// <returns>收款公司简化信息，如果缓存不存在返回null</returns>
            public static CollectingCompanySimple GetCollectingCompanyById(string id)
            {
                var key = COLLECTINGCOMPANY_ID + id;
                if (RedisHelper.Exists(key))
                    return RedisHelper.Get<CollectingCompanySimple>(key);
                
                // 当单独的ID缓存不存在时，尝试从全部缓存中获取
                var allCompanies = GetAllCollectingCompanies();
                if (allCompanies != null && allCompanies.Count > 0)
                {
                    var company = allCompanies.FirstOrDefault(c => c.Id == id);
                    if (company != null)
                    {
                        // 找到后同时保存到单独缓存
                        SaveCollectingCompany(company);
                        return company;
                    }
                }
                return null;
            }

            /// <summary>
            /// 根据名称获取收款公司简化信息
            /// </summary>
            /// <param name="sellerName">销售方名称</param>
            /// <returns>收款公司简化信息，如果缓存不存在返回null</returns>
            public static CollectingCompanySimple  GetCollectingCompanyBySellerName(string sellerName)
            {
                var key = COLLECTINGCOMPANY_SELLERNAME + sellerName;
                if (RedisHelper.Exists(key))
                    return RedisHelper.Get<CollectingCompanySimple>(key);
                
                // 当单独的名称缓存不存在时，尝试从全部缓存中获取
                var allCompanies = GetAllCollectingCompanies();
                if (allCompanies != null && allCompanies.Count > 0)
                {
                    var company = allCompanies.FirstOrDefault(c => c.SellerName == sellerName);
                    if (company != null)
                    {
                        // 找到后同时保存到单独缓存
                        SaveCollectingCompany(company);
                        return company;
                    }
                }
                return null;
            }

            /// <summary>
            /// 保存单个收款公司简化信息到缓存
            /// </summary>
            /// <param name="company">收款公司简化信息</param>
            public static void SaveCollectingCompany(CollectingCompanySimple company)
            {
                var key = COLLECTINGCOMPANY_ID + company.Id;
                RedisHelper.Set(key, company, TimeSpan.FromHours(26));
            }

            /// <summary>
            /// 保存单个收款公司简化信息到缓存（按名称）
            /// </summary>
            /// <param name="company">收款公司简化信息</param>
            public static void SaveCollectingCompanyBySellerName(CollectingCompanySimple company)
            {
                if (!string.IsNullOrEmpty(company.CollectingCompanyName))
                {
                    var key = COLLECTINGCOMPANY_SELLERNAME + company.SellerName;
                    RedisHelper.Set(key, company, TimeSpan.FromHours(26));
                }
            }

            /// <summary>
            /// 根据ID清除单个收款公司缓存
            /// </summary>
            /// <param name="id">收款公司ID</param>
            public static void ClearCompanyCache(string id)
            {
                var key = COLLECTINGCOMPANY_ID + id;
                RedisHelper.Del(key);
            }

            /// <summary>
            /// 清除所有收款公司缓存
            /// </summary>
            public static void ClearAllCache()
            {
                RedisHelper.Del(COLLECTINGCOMPANY_ALL);
                var keys = RedisHelper.Keys(COLLECTINGCOMPANY_ID + "*");
                if (keys != null && keys.Length > 0)
                    RedisHelper.Del(keys);
                
                var nameKeys = RedisHelper.Keys(COLLECTINGCOMPANY_SELLERNAME + "*");
                if (nameKeys != null && nameKeys.Length > 0)
                    RedisHelper.Del(nameKeys);
            }
        }
    }
} 