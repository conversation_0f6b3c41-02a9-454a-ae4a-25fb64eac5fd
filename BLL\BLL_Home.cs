﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System.IO;
using System.Web;
using CRM2_API.BLL.Article;
using CRM2_API.BLL.Notice;
using CRM2_API.BLL.Schedule;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_BusinessMessage;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_Home;
using static CRM2_API.Model.ControllersViewModel.VM_Messages;
using System.Collections.Generic;
using CRM2_API.Model.ControllersViewModel.Article;
using CRM2_API.Model.ControllersViewModel.Notice;
using CRM2_API.BLL.RemindInfo;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using System.Diagnostics.Contracts;
using CRM2_API.Common.AppSetting;
using QCloud;


namespace CRM2_API.BLL
{
    public class BLL_Home : BaseBLL<BLL_Home>
    {
        /// <summary>
        /// 设置后台接口权限白名单规则是否启用
        /// </summary>
        public void ChangeTypeValidState(bool toValid)
        {
            DbOpe_sys_form_rule.Instance.ChangeTypeValidState(0, toValid);
        }
        /// <summary>
        /// 获取业务消息种类
        /// </summary>
        public List<BusinessMessageType> GetBusinessMessageType()
        {
            return DbOpe_sys_business_messagetype.Instance.GetBusinessMessageType(UserId);
        }

        /// <summary>
        /// 添加用户业务消息种类
        /// </summary>
        public void AddUserBusinessMessagetype(List<string> businessMessagetype)
        {
            DbOpe_sys_user_business_messagetype.Instance.AddUserBusinessMessagetype(businessMessagetype, UserId);
        }

        /// <summary>
        /// 修改用户业务消息种类
        /// </summary>
        public void UpdateUserBusinessMessagetype(List<string> businessMessagetypeIds)
        {
            DbOpe_sys_user_business_messagetype.Instance.UpdateUserBusinessMessagetype(businessMessagetypeIds, UserId);
        }

        /// <summary>
        /// 获取用户业务消息种类
        /// </summary>
        public List<BusinessMessageTypeAndInfo> GetUserBusinessMessageType()
        {
            return DbOpe_sys_user_business_messagetype.Instance.GetUserBusinessMessageType(UserId);
        }

        /// <summary>
        /// 设置用户业务消息种类为已处理
        /// </summary>
        public void UpdateUserBusinessMessageToHandle(string businessMessagetypeId)
        {
            DbOpe_sys_workflow_pending.Instance.UpdateUserBusinessMessageToHandle(businessMessagetypeId, UserId);
        }

        /// <summary>
        /// 获取用户业务消息信息
        /// </summary>
        public List<BusinessMessageTypeAndInfo> GetBusinessMessageAndInfoType()
        {
            return DbOpe_sys_business_messagetype.Instance.GetBusinessMessageAndInfoType(UserId);
        }

        /// <summary>
        /// 获取用户消息信息
        /// </summary>
        public List<MessagesList_Out> GetMessagesList()
        {
            return DbOpe_sys_messages.Instance.GetMessagesList(UserId);
        }

        /// <summary>
        /// 根据查询条件获取用户消息信息列表
        /// </summary>
        public ApiTableOut<SearchMessagesList_Out> SearchMessagesList(SearchMessagesList_In searchMessagesListIn)
        {
            int total = 0;
            return new ApiTableOut<SearchMessagesList_Out> { Data = DbOpe_sys_messages.Instance.SearchMessagesList(searchMessagesListIn, UserId, ref total), Total = total };
        }

        /// <summary>
        /// 获取用户消息提醒信息
        /// </summary>
        public List<MessageReminder_Out> GetMessageReminderList()
        {
            List<MessageReminder> list = DbOpe_sys_messageremindertype.Instance.GetMessageReminder(UserId);
            List<MessageReminder> articleUpdateList = BLL_Article.Instance.GetUNReadUpdateMessage();    //知识库文章更新
            List<MessageReminder> articleCommentList = BLL_Article.Instance.GetUNReadCommentMessage();    //知识库评论
            List<MessageReminder> articleReplyList = BLL_Article.Instance.GetUNReadReplyMessage();    //知识库回复
            List<MessageReminder> ipRecordModerated = BLL_ContractOverseasIPRecord.Instance.GetUNReadModeratedIPRecord(); //ip备案待审核
            List<MessageReminder> ipRecordAuditResult = BLL_ContractOverseasIPRecord.Instance.GetUNReadAuditResultIPRecord();   //ip备案已审核
            List<MessageReminder> scheduleTomorrow = BLL_Schedule.Instance.GetUNReadTomorrowScheduleMessage();   //日程计划明天
            List<MessageReminder> scheduleToday = BLL_Schedule.Instance.GetUNReadTodayScheduleMessage();  //日程计划今天
            List<MessageReminder> scheduleExpire = BLL_Schedule.Instance.GetUNReadExpireScheduleMessage(); //日程计划即将过期
            List<MessageReminder> notice = BLL_Notice.Instance.GetUNReadNoticeMessage();   //公告通知

            list = list.Concat(articleUpdateList).Concat(articleCommentList).Concat(articleReplyList)
                .Concat(ipRecordModerated).Concat(ipRecordAuditResult).Concat(scheduleTomorrow).Concat(scheduleToday)
                .Concat(scheduleExpire).Concat(notice).ToList();

            List<Db_sys_messagereminder_received> receiveds = new List<Db_sys_messagereminder_received>();
            foreach (MessageReminder mr in list)
            {
                receiveds.Add(new Db_sys_messagereminder_received()
                {
                    MessageReminderTypeId = mr.MessageReminderTypeId,
                    WorkflowPendingId = mr.WorkflowPendingId,
                    SubjectId = mr.SubjectId,
                    UserId = UserId,
                    State = 1
                });
            }
            if (receiveds.Count() > 0)
            {
                DbOpe_sys_messagereminder_received.Instance.InsertListData(receiveds);
            }
            //List<MessageReminder_Out> result = list.Where(r=>r.MessageReminderTypeId != null).GroupBy(r => new { r.Name, r.Type, r.Content, r.Url }).Select(r => new MessageReminder_Out() { Name = r.Key.Name, Type = r.Key.Type, Url = r.Key.Url, Content = r.Key.Content.Replace("{Num}", r.Count().ToString()) }).ToList();
            List<MessageReminder_Out> result = list.GroupBy(r => new { r.Name, r.Type, r.Content, r.Url }).Select(r => new MessageReminder_Out() { Name = r.Key.Name, Type = r.Key.Type, Url = r.Key.Url, Content = r.Key.Content.Replace("{Num}", r.Count().ToString()) }).ToList();
            return result;//DbOpe_sys_messageremindertype.Instance.GetMessageReminderList(UserId);
        }

        /// <summary>
        /// 根据用户Id获取用户的晋升历史记录
        /// </summary>
        /// <returns></returns>
        public List<GetUserPromotionListByUserId_Out> GetPromotionList()
        {
            return DbOpe_crm_user_promotion.Instance.GetPromotionList(UserId);
        }

        /// <summary>
        /// 修改用户登录设置
        /// </summary>
        public void UpdateLoginTypeUser(List<string> loginTypeIds)
        {
            if (loginTypeIds == null || loginTypeIds.Count == 0)
                throw new ApiException("不可禁用所有登录方式");
            DbOpe_sys_login_type_user.Instance.TransDeal(() =>
            {
                //DbOpe_sys_login_type_user.Instance.UpdateLoginTypeUser(loginTypeIds, UserId);

                List<LoginTypeUser_Out> list = DbOpe_sys_login_type.Instance.GetLoginTypeIdsAndUser(loginTypeIds, UserId);
                bool AllowEmailLogin = list.Where(r => r.Type == EnumAllowLogin.AllowEmailLogin.ToInt()).First().Selected;
                bool AllowUserNameLogin = list.Where(r => r.Type == EnumAllowLogin.AllowUserNameLogin.ToInt()).First().Selected;
                bool AllowTelphoneLogin = list.Where(r => r.Type == EnumAllowLogin.AllowTelphoneLogin.ToInt()).First().Selected;
                bool AllowWechatLogin = list.Where(r => r.Type == EnumAllowLogin.AllowWechatLogin.ToInt()).First().Selected;
                bool AllowDingdingLogin = list.Where(r => r.Type == EnumAllowLogin.AllowDingdingLogin.ToInt()).First().Selected;
                bool AllowMessageAuthCodeLogin = list.Where(r => r.Type == EnumAllowLogin.AllowMessageAuthCodeLogin.ToInt()).First().Selected;
                DbOpe_sys_user.Instance.UpdateData(r => new Db_sys_user { AllowEmailLogin = AllowEmailLogin, AllowUserNameLogin = AllowUserNameLogin, AllowTelphoneLogin = AllowTelphoneLogin, AllowWechatLogin = AllowWechatLogin, AllowDingdingLogin = AllowDingdingLogin, AllowMessageAuthCodeLogin = AllowMessageAuthCodeLogin }, UserId);
            });
        }

        /// <summary>0
        /// 获取用户登录设置
        /// </summary>
        public List<LoginTypeUser_Out> GetLoginTypeUser()
        {
            return DbOpe_sys_login_type.Instance.GetLoginTypeUser(UserId);
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        public GetUserInfo_Out GetUserInfo()
        {
            return DbOpe_sys_user.Instance.GetUserInfo(UserId, "03dcbcb6-33ad-49f9-b8bb-888329268d30");
        }

        /// <summary>
        /// 上传用户头像
        /// </summary>
        public void UploadAvatarImage(UploadAvatarImage_In uploadAvatarImageIn)
        {
            DbOpe_sys_user.Instance.TransDeal(() =>
            {
                if (uploadAvatarImageIn.AvatarImage != null && uploadAvatarImageIn.AvatarImage.Count > 0)
                {
                    Util<DbOpe_sys_user, BM_AttachFile> avatarImageAttachFile = new Util<DbOpe_sys_user, BM_AttachFile>(DbOpe_sys_user.Instance);
                    avatarImageAttachFile.UploadFile(uploadAvatarImageIn.AvatarImage, EnumAvatarImageFileType.AvatarImage.ToString(), UserId, UserId);
                }
            });
        }

        /// <summary>
        /// 下载头像
        /// </summary>
        /// <param name="response"></param>
        /// <returns></returns>
        public IActionResult LoadAvatarImage(HttpResponse response)
        {
            string fileName = string.Empty;
            string filePath = string.Empty;
            Db_sys_user user = DbOpe_sys_user.Instance.GetDataById(UserId);

            if (user.AvatarImage.IsNullOrEmpty())
            {
                return new JsonResult(new
                {
                    Msg = "当前用户还没有上传头像!"
                });
            }

            fileName = "AvatarImage";
            filePath = user.AvatarImage;

            if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
            {
                QCloudOperator qCloudOperator = new QCloudOperator();
                if (!qCloudOperator.Exists(filePath))
                {
                    return new JsonResult(new
                    {
                        Msg = "文件不存在!"
                    });

                }
                var data = qCloudOperator.OpenRead(filePath);
                using (MemoryStream memoryStream = new MemoryStream(data))
                {
                    string encodeFilename = HttpUtility.UrlEncode(fileName, Encoding.GetEncoding("UTF-8"));
                    MemoryStream resultStream = new MemoryStream(data);
                    response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                    return new FileStreamResult(resultStream, "application/octet-stream");
                }
            }
            else
            {
                if (!File.Exists(filePath))
                {
                    return new JsonResult(new
                    {
                        Msg = "文件不存在!"
                    });
                }
                var stream = File.OpenRead(filePath);
                string encodeFilename = HttpUtility.UrlEncode(fileName, Encoding.GetEncoding("UTF-8"));
                response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                return new FileStreamResult(stream, "application/octet-stream");
            }
        }

        /// <summary>
        /// 获取用户业务消息信息和通知
        /// </summary>
        public List<BusinessMessageTypeAndInfoAndMessagesList> GetBusinessMessageAndInfoTypeAndMessagesList()
        {
            List<BusinessMessageTypeAndInfoAndMessagesList> MessagesList = DbOpe_sys_messages.Instance.GetBusinessMessageAndInfoTypeAndMessagesList(UserId);
            List<BusinessMessageTypeAndInfoAndMessagesList> BusinessMessage = DbOpe_sys_business_messagetype.Instance.GetBusinessMessageAndInfoTypeAndMessagesList(UserId);

            List<BusinessMessageTypeAndInfoAndMessagesList> list = new List<BusinessMessageTypeAndInfoAndMessagesList>();
            list.AddRange(MessagesList);
            list.AddRange(BusinessMessage);
            return list;
        }

        /// <summary>
        /// 删除用户业务消息信息和通知
        /// </summary>
        public void DeleteBusinessMessageAndInfoTypeAndMessagesList(string id)
        {
            DbOpe_sys_user.Instance.TransDeal(() =>
            {
                DbOpe_sys_user_business_message_hidden.Instance.UpdateBusinessMessageAndInfoTypeAndMessagesList(id, UserId);
                DbOpe_sys_user_business_message_hidden.Instance.DeleteBusinessMessageAndInfoTypeAndMessagesList(id, UserId);
            });
        }

        /// <summary>
        /// 获取用户业务消息信息和通知
        /// </summary>
        public ApiTableOut<SearchBusinessMessageAndInfoTypeAndMessages_Out> SearchBusinessMessageAndInfoTypeAndMessages(SearchBusinessMessageAndInfoTypeAndMessages_In SearchBusinessMessageAndInfoTypeAndMessages_In)
        {
            int total = 0;
            if (SearchBusinessMessageAndInfoTypeAndMessages_In.Id == Guid.Empty.ToString())
            {
                return new ApiTableOut<SearchBusinessMessageAndInfoTypeAndMessages_Out> { Data = DbOpe_sys_messages.Instance.SearchBusinessMessageAndInfoTypeAndMessagesList(SearchBusinessMessageAndInfoTypeAndMessages_In, UserId, ref total), Total = total };
            }
            else
            {
                return new ApiTableOut<SearchBusinessMessageAndInfoTypeAndMessages_Out> { Data = DbOpe_sys_business_messagetype.Instance.SearchBusinessMessageAndInfoTypeAndMessagesList(SearchBusinessMessageAndInfoTypeAndMessages_In, UserId, ref total), Total = total };
            }
        }

        /// <summary>
        /// 变更系统消息已读状态
        /// </summary>
        public void UpdateMessagesState(string id)
        {
            DbOpe_sys_messages.Instance.UpdateMessagesState(id);
        }

        /// <summary>
        /// 隐藏系统消息
        /// </summary>
        public void HiddenMessages(string id)
        {
            DbOpe_sys_messages.Instance.HiddenMessages(id);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setMessageIsReadIn"></param>
        /// <exception cref="NotImplementedException"></exception>
        public NoticeBase_Out SetIsReadInMessageCenter(SetMessageIsRead_In setMessageIsReadIn)
        {
            EnumMessageType messageType = setMessageIsReadIn.MessagesType.ToEnum<EnumMessageType>();
            if (setMessageIsReadIn.Id is not null)
            {
                switch (messageType)
                {
                    case EnumMessageType.Publish:
                        DbOpe_crm_article_browse.Instance.AddArticleBrowse(setMessageIsReadIn.Id);
                        break;
                    case EnumMessageType.Comment:
                        GetArticleCommentList_In gcl = new GetArticleCommentList_In();
                        gcl.ArticleId = setMessageIsReadIn.Id;
                        BLL_ArticleComment.Instance.GetArticleCommentList(gcl);
                        break;
                    case EnumMessageType.System:
                        UpdateMessagesState(setMessageIsReadIn.Id);
                        break;
                    case EnumMessageType.Notice:
                        DbOpe_crm_notice_browse.Instance.SetIsReadNotice(setMessageIsReadIn.Id);
                        break;
                    case EnumMessageType.PublicAnnouncement:
                        DbOpe_crm_notice_browse.Instance.SetIsReadNotice(setMessageIsReadIn.Id);
                        break;
                    default: break;
                }
            }
            NoticeBase_Out result = new NoticeBase_Out();
            result.Data = 1;
            return result;
        }
    }
}
