﻿using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace CRM2_API.Common.Middleware
{
    /// <summary>
    /// 线程异常处理中间件
    /// </summary>
    public class ExceptionMiddleware : IMiddleware
    {
        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            try
            {
                await next(context);
            }
            catch (Exception exp)
            {
                //多线程异常时，只使用第二层的异常
                if (exp.InnerException != null)
                    exp = exp.InnerException;
                string msg = "";
                Enum_ReturnErrorCode errCode = Enum_ReturnErrorCode.ApiException;
                //记录主动抛出的异常
                if (exp is ApiException e)
                {
                    msg = e.Message;
                    errCode = e.ErrorCode;
                }
                    
                else//非主动抛出的异常
                {
                    errCode = Enum_ReturnErrorCode.Error;
                    msg = exp.Message;
                    //记录日志
                    if (!(exp is ApiException))
                    {
                        // 记录完整的异常信息，包括堆栈和内部异常
                        LogUtil.AddErrorLog($"全局异常: {exp.Message}", exp);
                        
                        // 记录请求信息以便问题复现
                        LogUtil.AddErrorLog($"请求路径: {context.Request.Path}");
                        LogUtil.AddErrorLog($"请求方法: {context.Request.Method}");
                        
                        // 记录重要的请求头
                        if (context.Request.Headers.ContainsKey("IsMobile"))
                            LogUtil.AddErrorLog($"IsMobile: {context.Request.Headers["IsMobile"]}");
                        if (context.Request.Headers.ContainsKey("Authorization"))
                            LogUtil.AddErrorLog("Authorization头存在");
                        if (context.Request.Headers.ContainsKey("SpecialKey"))
                            LogUtil.AddErrorLog("SpecialKey头存在");
                        
                        // 如果有多层嵌套异常，记录整个异常链
                        var innerExp = exp.InnerException;
                        int level = 1;
                        while (innerExp != null)
                        {
                            LogUtil.AddErrorLog($"内部异常({level}): {innerExp.Message}");
                            LogUtil.AddErrorLog($"内部异常({level})堆栈: {innerExp.StackTrace}");
                            innerExp = innerExp.InnerException;
                            level++;
                        }
                    }
                }
                context.Response.ContentType = "application/text";
                context.Response.StatusCode = 200;
                //将错误代码，添加到header中
                context.Response.Headers.Add("error-code", errCode.ToInt().ToString());
                context.Response.Headers.Add("Access-Control-Expose-Headers", "error-code");
                //最后输出字符串
                await context.Response.WriteAsync(msg);
            }
        }
    }
}
