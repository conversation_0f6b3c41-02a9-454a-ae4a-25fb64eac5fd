﻿using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 合同收款登记视图模型
    /// </summary>
    [SugarTable("v_searchcontractreceiptregisterlist_join")]
    public class Db_v_searchcontractreceiptregisterlist_join
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(ColumnName = "Id")]
        public string Id { get; set; }

        /// <summary>
        /// 合同编号
        /// </summary>
        [SugarColumn(ColumnName = "ContractNum")]
        public string ContractNum { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "Remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 保护期截止日期
        /// </summary>
        [SugarColumn(ColumnName = "ProtectionDeadline")]
        public string ProtectionDeadline { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [SugarColumn(ColumnName = "ContractNo")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 合同名称
        /// </summary>
        [SugarColumn(ColumnName = "ContractName")]
        public string ContractName { get; set; }

        /// <summary>
        /// 合同金额
        /// </summary>
        [SugarColumn(ColumnName = "ContractAmount")]
        public decimal? ContractAmount { get; set; }

        /// <summary>
        /// 外币合同金额
        /// </summary>
        [SugarColumn(ColumnName = "FCContractAmount")]
        public decimal? FCContractAmount { get; set; }

        /// <summary>
        /// 美元合同金额
        /// </summary>
        [SugarColumn(ColumnName = "USDContractAmount")]
        public decimal? USDContractAmount { get; set; }

        /// <summary>
        /// 欧元合同金额
        /// </summary>
        [SugarColumn(ColumnName = "EURContractAmount")]
        public decimal? EURContractAmount { get; set; }

        /// <summary>
        /// 合同金额折算
        /// </summary>
        [SugarColumn(ColumnName = "ContractAmountConvert")]
        public decimal? ContractAmountConvert { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        [SugarColumn(ColumnName = "Currency")]
        public int? Currency { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [SugarColumn(ColumnName = "State")]
        public int? State { get; set; }

        /// <summary>
        /// 付款公司名称
        /// </summary>
        [SugarColumn(ColumnName = "PaymentCompanyName")]
        public string PaymentCompanyName { get; set; }

        /// <summary>
        /// 是否收款
        /// </summary>
        [SugarColumn(ColumnName = "IsReceipt")]
        public int? IsReceipt { get; set; }

        /// <summary>
        /// 付款类型
        /// </summary>
        [SugarColumn(ColumnName = "PaymentType")]
        public string PaymentType { get; set; }

        /// <summary>
        /// 收款公司名称
        /// </summary>
        [SugarColumn(ColumnName = "CollectingCompanyName")]
        public string CollectingCompanyName { get; set; }

        /// <summary>
        /// 付款方式
        /// </summary>
        [SugarColumn(ColumnName = "PaymentMethod")]
        public string PaymentMethod { get; set; }

        /// <summary>
        /// 到账金额
        /// </summary>
        [SugarColumn(ColumnName = "ArrivalAmount")]
        public decimal? ArrivalAmount { get; set; }

        /// <summary>
        /// 银行支付金额
        /// </summary>
        [SugarColumn(ColumnName = "BankPaymentAmount")]
        public decimal? BankPaymentAmount { get; set; }

  /// <summary>
        /// 现金支付金额
        /// </summary>
        [SugarColumn(ColumnName = "CashPaymentAmount")]
        public decimal? CashPaymentAmount { get; set; }

        /// <summary>
        /// 汇票
        /// </summary>
        [SugarColumn(ColumnName = "Honour")]
        public decimal? Honour { get; set; }

        /// <summary>
        /// 银行支付金额统计
        /// </summary>
        [SugarColumn(ColumnName = "BankPaymentAmountSta")]
        public decimal? BankPaymentAmountSta { get; set; }

        /// <summary>
        /// 现金支付金额统计
        /// </summary>
        [SugarColumn(ColumnName = "CashPaymentAmountSta")]
        public decimal? CashPaymentAmountSta { get; set; }

        /// <summary>
        /// 退款业绩
        /// </summary>
        [SugarColumn(ColumnName = "ReturnPerformance")]
        public decimal? ReturnPerformance { get; set; }

        /// <summary>
        /// 退款项
        /// </summary>
        [SugarColumn(ColumnName = "RefundItems")]
        public decimal? RefundItems { get; set; }

        /// <summary>
        /// 其他项业绩
        /// </summary>
        [SugarColumn(ColumnName = "OtheritemsPerformance")]
        public decimal? OtheritemsPerformance { get; set; }

        /// <summary>
        /// 到账日期
        /// </summary>
        [SugarColumn(ColumnName = "ArrivalDate")]
        public string ArrivalDate { get; set; }

        /// <summary>
        /// 是否保密
        /// </summary>
        [SugarColumn(ColumnName = "IsSecret")]
        public int? IsSecret { get; set; }

        /// <summary>
        /// 扣减业绩
        /// </summary>
        [SugarColumn(ColumnName = "DeductPerformance")]
        public decimal? DeductPerformance { get; set; }

        /// <summary>
        /// 销售业绩
        /// </summary>
        [SugarColumn(ColumnName = "SalesPerformance")]
        public decimal? SalesPerformance { get; set; }

        /// <summary>
        /// 有效业绩
        /// </summary>
        [SugarColumn(ColumnName = "EffectivePerformance")]
        public decimal? EffectivePerformance { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "CreateDate")]
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 审核日期
        /// </summary>
        [SugarColumn(ColumnName = "ReviewerDate")]
        public DateTime? ReviewerDate { get; set; }

        /// <summary>
        /// 发行人ID
        /// </summary>
        [SugarColumn(ColumnName = "Issuer")]
        public string Issuer { get; set; }

        /// <summary>
        /// 组织单位名称
        /// </summary>
        [SugarColumn(ColumnName = "OrgDivisionName")]
        public string OrgDivisionName { get; set; }

        /// <summary>
        /// 组织旅名称
        /// </summary>
        [SugarColumn(ColumnName = "OrgBrigadeName")]
        public string OrgBrigadeName { get; set; }

        /// <summary>
        /// 组织团名称
        /// </summary>
        [SugarColumn(ColumnName = "OrgRegimentName")]
        public string OrgRegimentName { get; set; }

        /// <summary>
        /// 组织单位ID
        /// </summary>
        [SugarColumn(ColumnName = "OrgDivisionId")]
        public string OrgDivisionId { get; set; }

        /// <summary>
        /// 组织旅ID
        /// </summary>
        [SugarColumn(ColumnName = "OrgBrigadeId")]
        public string OrgBrigadeId { get; set; }

        /// <summary>
        /// 组织团ID
        /// </summary>
        [SugarColumn(ColumnName = "OrgRegimentId")]
        public string OrgRegimentId { get; set; }

        /// <summary>
        /// 合同ID
        /// </summary>
        [SugarColumn(ColumnName = "ContractId")]
        public string ContractId { get; set; }

        /// <summary>
        /// 收款信息自动匹配ID
        /// </summary>
        [SugarColumn(ColumnName = "CollectionInfoAutoMatchingId")]
        public string CollectionInfoAutoMatchingId { get; set; }

        /// <summary>
        /// 类型（1：收款登记；2：自动匹配）
        /// </summary>
        [SugarColumn(ColumnName = "Type")]
        public int Type { get; set; }

        /// <summary>
        /// 是否催登记
        /// </summary>
        [SugarColumn(ColumnName = "IsUrgeRegistration")]
        public int IsUrgeRegistration { get; set; }

        /// <summary>
        /// 归属月份
        /// </summary>
        [SugarColumn(ColumnName = "BelongingMonth")]
        public DateTime? BelongingMonth { get; set; }

        /// <summary>
        /// 是否结算汇兑
        /// </summary>
        [SugarColumn(ColumnName = "IsSettlementExchange")]
        public int? IsSettlementExchange { get; set; }

        /// <summary>
        /// 签订日期
        /// </summary>
        [SugarColumn(ColumnName = "SigningDate")]
        public DateTime? SigningDate { get; set; }

        /// <summary>
        /// 是否海外客户
        /// </summary>
        [SugarColumn(ColumnName = "IsOverseasCustomer")]
        public bool? IsOverseasCustomer { get; set; }

        /// <summary>
        /// 是否重复收款客户ID
        /// </summary>
        [SugarColumn(ColumnName = "IsIsRepeatedReceiptCustomerId")]
        public string IsIsRepeatedReceiptCustomerId { get; set; }

        /// <summary>
        /// 重复收款发行人
        /// </summary>
        [SugarColumn(ColumnName = "RepeatedReceiptIssuer")]
        public string RepeatedReceiptIssuer { get; set; }

        /// <summary>
        /// 合同组织单位名称
        /// </summary>
        [SugarColumn(ColumnName = "ContractOrgDivisionName")]
        public string ContractOrgDivisionName { get; set; }

        /// <summary>
        /// 合同组织旅名称
        /// </summary>
        [SugarColumn(ColumnName = "ContractOrgBrigadeName")]
        public string ContractOrgBrigadeName { get; set; }

        /// <summary>
        /// 合同组织团名称
        /// </summary>
        [SugarColumn(ColumnName = "ContractOrgRegimentName")]
        public string ContractOrgRegimentName { get; set; }

        /// <summary>
        /// 合同组织单位ID
        /// </summary>
        [SugarColumn(ColumnName = "ContractOrgDivisionId")]
        public string ContractOrgDivisionId { get; set; }

        /// <summary>
        /// 合同组织旅ID
        /// </summary>
        [SugarColumn(ColumnName = "ContractOrgBrigadeId")]
        public string ContractOrgBrigadeId { get; set; }

        /// <summary>
        /// 合同组织团ID
        /// </summary>
        [SugarColumn(ColumnName = "ContractOrgRegimentId")]
        public string ContractOrgRegimentId { get; set; }

        /// <summary>
        /// 收款银行名称
        /// </summary>
        [SugarColumn(ColumnName = "CollectingBankName")]
        public string CollectingBankName { get; set; }

        /// <summary>
        /// 更新备注
        /// </summary>
        [SugarColumn(ColumnName = "UpdateRemark")]
        public string UpdateRemark { get; set; }

        /// <summary>
        /// 是否代付款
        /// </summary>
        [SugarColumn(ColumnName = "IsBehalfPayment")]
        public bool? IsBehalfPayment { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        [SugarColumn(ColumnName = "Country")]
        public int? Country { get; set; }

        /// <summary>
        /// 全局搜索单位
        /// </summary>
        [SugarColumn(ColumnName = "GlobalSearchUnit")]
        public decimal? GlobalSearchUnit { get; set; }

        /// <summary>
        /// 不计入业绩金额
        /// </summary>
        [SugarColumn(ColumnName = "NonPerformanceAmount")]
        public decimal? NonPerformanceAmount { get; set; }

        /// <summary>
        /// 充值金额
        /// </summary>
        [SugarColumn(ColumnName = "RechargeAmount")]
        public decimal? RechargeAmount { get; set; }
    }
}