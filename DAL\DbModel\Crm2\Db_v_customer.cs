﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_customer")]
    public class Db_v_customer
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string CustomerId {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int privatePool {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int publicPool {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PrivatePoolId {get;set;}

           /// <summary>
           /// Desc:用户id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? BeginDate {get;set;}

           /// <summary>
           /// Desc:保护截止日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ProtectionDeadline {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PublicPoolId {get;set;}

           /// <summary>
           /// Desc:负责人组织ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserOrgId {get;set;}

           /// <summary>
           /// Desc:所属战队
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public string OrgDivisionId {get;set;}

           /// <summary>
           /// Desc:所属战队
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgDivisionName {get;set;}

           /// <summary>
           /// Desc:所属大队
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public string OrgBrigadeId {get;set;}

           /// <summary>
           /// Desc:所属大队
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public string OrgBrigadeName {get;set;}

           /// <summary>
           /// Desc:所属中队
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public string OrgRegimentId {get;set;}

           /// <summary>
           /// Desc:所属中队
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public string OrgRegimentName {get;set;}

    }
}
