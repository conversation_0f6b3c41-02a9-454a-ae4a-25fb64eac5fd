# CheckWitsChangeAppl 服务变更检查方法实现说明

## 📋 概述

基于现有的 `CheckWitsAppl` 方法，我创建了一个专门用于服务变更场景的检查方法 `CheckWitsChangeAppl`。该方法用于验证服务变更申请是否可以提交，确保变更申请的合法性和数据完整性。

## 🔧 方法签名

```csharp
/// <summary>
/// 验证服务变更申请是否可以提交
/// </summary>
/// <param name="changeApply_In">服务变更申请输入参数</param>
/// <param name="contract">合同信息</param>
/// <exception cref="ApiException"></exception>
public void CheckWitsChangeAppl(VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In, Db_crm_contract contract)
```

## 🏗️ 核心验证逻辑

### 1. 基础验证
- **数据权限检查**: 验证用户是否有权限操作该合同
- **合同状态检查**: 确保合同已审核通过才能进行服务变更
- **重复申请检查**: 防止同一产品系列存在多个未审核的申请

### 2. 变更原因验证
- **变更原因必填**: 确保变更原因不能为空
- **变更原因合法性**: 验证变更原因枚举值的有效性
- **合同产品ID验证**: 确保每个变更原因都关联了有效的合同产品

### 3. 当前服务状态验证
- **在服服务查找**: 获取当前产品系列下已通过且在服的服务信息
- **服务存在性检查**: 确保存在可变更的服务

### 4. 优惠券和个人服务天数验证
- **优惠券使用验证**: 检查优惠券的有效性和使用权限
- **个人服务天数验证**: 验证个人服务天数的使用限制
- **超额服务天数计算**: 确保超额服务天数计算正确

### 5. 服务时间验证
- **新增月数验证**: 验证各服务类型的新增服务月数和时间周期
- **时间计算准确性**: 确保服务变更后的时间计算正确

### 6. 账号变更验证
- **用户变更合法性**: 验证用户权限变更的合理性
- **权限一致性检查**: 确保未申请的服务权限不发生变更
- **账号数量匹配**: 验证账号数量与权限数量的匹配性

### 7. 变更内容验证
- **变更原因与内容一致性**: 确保变更原因与实际变更内容匹配
- **服务时间合理性**: 验证变更后的服务时间设置合理

## 🔍 辅助方法

### CheckWitsChangeAppl_Sub
```csharp
private void CheckWitsChangeAppl_Sub(DateTime? startDate, DateTime? endDate, int? addMonth, int couponCount, int perlongServiceDays, string errStrFront)
```
专门用于验证服务变更中的时间计算，与新申请不同的是，这里验证的是新增月数而不是总月数。

### GetCurrentWitsServiceBySeriesId
```csharp
private Db_crm_contract_productserviceinfo_wits_appl GetCurrentWitsServiceBySeriesId(string contractProductInfoSeriesId)
```
根据产品系列ID获取当前在服的Wits服务信息。

### ValidateUserChanges
```csharp
private void ValidateUserChanges(List<VM_ContractServiceChange.UpdateGtisSeriesAppl_In_UserList> changeUserList, 
    List<Db_crm_contract_serviceinfo_wits_user> currentUsers, 
    VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In)
```
验证用户变更的合法性，包括权限变更和账号数量验证。

### ValidateUserPermissionChanges
```csharp
private void ValidateUserPermissionChanges(VM_ContractServiceChange.UpdateGtisSeriesAppl_In_UserList changeUser, 
    Db_crm_contract_serviceinfo_wits_user currentUser, 
    VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In)
```
验证用户权限变更的合理性，确保未申请的服务权限不发生变更。

### ValidateAccountPermissionCounts
```csharp
private void ValidateAccountPermissionCounts(List<VM_ContractServiceChange.UpdateGtisSeriesAppl_In_UserList> userList, 
    VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In)
```
验证账号数量与权限数量的匹配性。

### ValidateChangeContent
```csharp
private void ValidateChangeContent(VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In, 
    Db_crm_contract_productserviceinfo_wits_appl currentWitsService)
```
验证变更内容的合理性，确保变更原因与变更内容一致。

### ValidateChangeServiceTime
```csharp
private void ValidateChangeServiceTime(VM_ContractServiceChange.AddWitsUpdateAppl_In changeApply_In, 
    Db_crm_contract_productserviceinfo_wits_appl currentWitsService)
```
验证服务变更的时间合理性。

## 🆚 与 CheckWitsAppl 的主要区别

| 方面 | CheckWitsAppl (新申请) | CheckWitsChangeAppl (服务变更) |
|------|----------------------|------------------------------|
| **输入参数** | `AddWitsAppl_In` | `VM_ContractServiceChange.AddWitsUpdateAppl_In` |
| **验证重点** | 新申请的合法性 | 变更申请的合法性 |
| **时间验证** | 验证总服务月数 | 验证新增服务月数 |
| **账号验证** | 新账号生成或使用原有账号 | 现有账号的权限变更 |
| **变更原因** | 不涉及 | 必须验证变更原因 |
| **当前服务** | 不需要 | 必须获取当前在服服务 |
| **权限变更** | 不涉及 | 验证权限变更的合理性 |

## 🎯 使用场景

该方法适用于以下服务变更场景：
1. **尾款申请剩余服务** - 验证尾款申请的合法性
2. **变更服务内容** - 验证服务内容变更的合理性
3. **个人服务天数延期** - 验证延期申请的有效性
4. **优惠券延期** - 验证优惠券使用的合法性
5. **增加SalesWits账户数** - 验证账户数增加的合理性
6. **SalesWits充值** - 验证充值申请的有效性

## 🔒 异常处理

方法会在以下情况抛出 `ApiException`：
- 用户没有数据权限
- 合同状态不允许变更
- 存在未审核的申请
- 变更原因为空或无效
- 未找到当前在服服务
- 优惠券失效或无法使用
- 个人服务天数超限
- 服务时间设置不正确
- 用户权限变更不合理
- 账号数量与权限不匹配
- 变更内容与变更原因不一致

## 📝 使用示例

```csharp
// 在服务变更申请提交前调用
try
{
    var contract = DbOpe_crm_contract.Instance.GetData(x => x.Id == changeApply_In.ContractId);
    CheckWitsChangeAppl(changeApply_In, contract);
    
    // 验证通过，继续处理变更申请
    // ...
}
catch (ApiException ex)
{
    // 处理验证失败的情况
    LogUtil.AddErrorLog($"服务变更申请验证失败: {ex.Message}");
    throw;
}
```

## 🚀 扩展建议

1. **缓存优化**: 对频繁查询的数据添加缓存机制
2. **异步处理**: 对复杂验证逻辑考虑异步处理
3. **配置化**: 将部分验证规则配置化，提高灵活性
4. **日志增强**: 添加更详细的验证过程日志
5. **单元测试**: 为各种验证场景编写完整的单元测试

这个方法为服务变更申请提供了全面的验证保障，确保变更申请的数据完整性和业务合理性。
