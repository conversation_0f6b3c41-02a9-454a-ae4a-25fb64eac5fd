﻿using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using Spire.Pdf;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Numerics;
using Image = SixLabors.ImageSharp.Image;

namespace CRM2_API.BLL.Common
{
    public class PdfConvertImage
    {
        public static Stream ProcessPdfImages(Stream stream)
        {
            using (PdfDocument pdfDocument = new PdfDocument(stream))
            {
                Stream imageStream = new MemoryStream();
                if (pdfDocument.Pages.Count > 0)
                {
                    pdfDocument.SaveToImageStream(0, imageStream, "image/png");
                }
                Image<Rgba32> baseImage = PdfConvertImage.StreamToImage(imageStream);
                Image<Rgba32> combinedImage = new Image<Rgba32>(baseImage.Width, baseImage.Height * pdfDocument.Pages.Count);
                for (int i = 0; i < pdfDocument.Pages.Count; i++)
                {
                    Stream streamPNG = new MemoryStream();
                    pdfDocument.SaveToImageStream(i, streamPNG, "image/png");
                    streamPNG.Position = 0;
                    Image<Rgba32> imageToCombine = PdfConvertImage.StreamToImage(streamPNG);
                    int y = imageToCombine.Height * i;

                    combinedImage.Mutate(x => x
                        .DrawImage(imageToCombine, new SixLabors.ImageSharp.Point(0, y), 1f)
                        );
                }
                MemoryStream memoryStream = new MemoryStream();
                combinedImage.Save(memoryStream, new PngEncoder());
                return memoryStream;
            }
        }

        public static SixLabors.ImageSharp.Image<Rgba32> StreamToImage(Stream stream)
        {
            // 将流转换为字节数组
            byte[] bytes = new byte[stream.Length];
            stream.Read(bytes, 0, bytes.Length);

            // 使用MemoryStream和BitmapFactory将字节数组转换为Image对象
            using (MemoryStream memoryStream = new MemoryStream(bytes))
            {
                return SixLabors.ImageSharp.Image.Load<Rgba32>(memoryStream);
            }
        }

        public static Stream ProcessPdfImagesOld(Stream stream)
        {
            using (PdfDocument pdfDocument = new PdfDocument(stream))
            {
                //for (int i = 0; i < pdfDocument.Pages.Count; i++)
                //{
                //    System.Drawing.Image bmp = pdfDocument.SaveAsImage(i, Spire.Pdf.Graphics.PdfImageType.Bitmap);
                //    string fileName = string.Format("Page-{0}.png", i + 1);
                //    bmp.Save(fileName, System.Drawing.Imaging.ImageFormat.Png);
                //}

                // 创建一个内存流来保存图片
                Stream imageStream = new MemoryStream();

                // 将PDF的每一页保存为PNG图片，并写入到内存流中
                if (pdfDocument.Pages.Count > 0)
                {
                    pdfDocument.SaveToImageStream(0, imageStream, "image/png");
                }
                Image<Rgba32> baseImage = PdfConvertImage.StreamToImage(imageStream);
                //Image baseImage = Image.Load(imageStream.ToBytes());
                Image<Rgba32> combinedImage = new Image<Rgba32>(baseImage.Width, baseImage.Height * pdfDocument.Pages.Count);
                Stream streamPNG = new MemoryStream();
                for (int i = 0; i < pdfDocument.Pages.Count; i++)
                {
                    //Stream img = pdfDocument.SaveAsImage(i);
                    //img.streamPNG
                    streamPNG = new MemoryStream();
                    pdfDocument.SaveToImageStream(i, streamPNG, "image/png");

                    streamPNG.Position = 0;

                    //using (Image imageToCombine = Image.Load(streamPNG.ToBytes()))
                    //{
                    Image<Rgba32> imageToCombine = PdfConvertImage.StreamToImage(streamPNG);
                    // 确定合成图片的位置
                    //float x = baseImage.Width - imageToCombine.Width;
                    //float y = baseImage.Height - imageToCombine.Height;
                    int y = imageToCombine.Height * i;
                    combinedImage.Mutate(x => x
                        .DrawImage(imageToCombine, new SixLabors.ImageSharp.Point(0, y), 1f)
                        );
                    //.DrawText("Combined", font, Rgba32.Black, new PointF(x, y)));

                    //baseImage.Save(outputPath);
                    //}
                }
                MemoryStream memoryStream = new MemoryStream();

                combinedImage.Save(memoryStream, new PngEncoder());
                return memoryStream;
                    // 现在 memoryStream 包含图片数据，可以进一步处理或者保存
                
                //pdfDocument.SaveToImageStream(i, streamPDF, "image/png");
                //return baseImage.Save();
                //return imageStream;
            }
        }

    }
}
