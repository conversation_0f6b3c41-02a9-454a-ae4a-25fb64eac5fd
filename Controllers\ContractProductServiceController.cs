﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.ServiceOpening;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.IO;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.Controllers
{
    [Description("合同管理-服务管理")]
    //[SkipAuthCheck]
    //[SkipRSAKey]
    public class ContractProductServiceController : MyControllerBase
    {
        public ContractProductServiceController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        /// <summary>
        /// 服务情况列表
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        //[SkipAuthCheck, SkipIPCheck]
        //[SkipRightCheck]
        //[SkipRSAKey]
        //[SkipRecordLog]
        public ApiTableOut<SearchServiceContractList_Out> SearchServiceContractList(SearchServiceContractList_In condition) {
            int total = 0;
            var list = DbOpe_crm_contract.Instance.SearchServiceContractList(condition, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 服务情况统计
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        //[SkipAuthCheck, SkipIPCheck]
        //[SkipRightCheck]
        //[SkipRSAKey]
        //[SkipRecordLog]
        public SearchServiceContractSta_Out SearchServiceContractSta(SearchServiceContractList_In condition)
        {
            return DbOpe_crm_contract.Instance.SearchServiceContractSta(condition);
        }

        /// <summary>
        /// 登记合同服务信息邓白氏申请信息
        /// </summary>
        [HttpPost]
        public void AuditContractProductServiceInfoDBAppl([FromForm] AuditContractProductServiceInfoDBAppl_In regist_In)
        {
            BLL_ContractServiceDB.Instance.AuditContractProductServiceInfoDBAppl(regist_In);
        }

        /// <summary>
        /// 审核合同服务信息邓白氏申请信息
        /// </summary>
        [HttpPost]
        public void ReviewContractProductServiceInfoDBAppl(ReviewContractProductServiceInfoDBAppl_In audit_In)
        {
            BLL_ContractServiceDB.Instance.ReviewContractProductServiceInfoDBAppl(audit_In);
        }



        /// <summary>
        /// 审核合同服务信息环球搜申请信息--初审登记
        /// </summary>
        [HttpPost]
        public void AuditContractProductServiceInfoGlobalSearchAppl(AuditContractProductServiceInfoGlobalSearchAppl_In audit_In)
        {
            BLL_ContractServiceGlobalSearch.Instance.AuditContractProductServiceInfoGlobalSearchAppl(audit_In);
        }

        /// <summary>
        /// 审核合同服务信息环球搜申请信息--复核
        /// </summary>
        [HttpPost]
        public void ReviewContractProductServiceInfoGlobalSearchAppl(ReviewContractProductServiceInfoGlobalSearchAppl_In review_In)
        {
            BLL_ContractServiceGlobalSearch.Instance.ReviewContractProductServiceInfoGlobalSearchAppl(review_In);
        }

        /// <summary>
        /// 审核合同服务信息慧思学院申请信息--初审登记
        /// </summary>
        [HttpPost]
        public void AuditContractProductServiceInfoCollegeAppl(AuditContractProductServiceInfoCollegeAppl_In audit_In)
        {
            BLL_ContractServiceCollege.Instance.AuditContractProductServiceInfoCollegeAppl(audit_In);
        }

        /// <summary>
        /// 审核合同服务信息慧思学院申请信息--复核
        /// </summary>
        [HttpPost]
        public void ReviewContractProductServiceInfoCollegeAppl(ReviewContractProductServiceInfoCollegeAppl_In audit_In)
        {
            BLL_ContractServiceCollege.Instance.ReviewContractProductServiceInfoCollegeAppl(audit_In);
        }

        /// <summary>
        /// 登记合同服务信息期刊邮寄的快递信息
        /// </summary>
        [HttpPost]
        public void RegisterContractProductServiceInfoMailing([FromForm] RegisterContractProductServiceInfoMailing_In register_In)
        {
            BLL_ContractServiceProject.Instance.RegisterContractProductServiceInfoMailing(register_In);
        }

        /// <summary>
        /// 审核合同服务信息其他数据申请信息--初审登记
        /// </summary>
        [HttpPost]
        public void AuditContractProductServiceInfoOtherDataAppl(AuditContractProductServiceInfoOtherDataAppl_In audit_In)
        {
            BLL_ContractServiceOtherData.Instance.AuditContractProductServiceInfoOtherDataAppl(audit_In);
        }

        /// <summary>
        /// 审核合同服务信息其他数据申请信息--复核
        /// </summary>
        /// <param name="register_In"></param>
        [HttpPost]
        public void ReviewContractProductServiceInfoOtherDataAppl(ReviewContractProductServiceInfoOtherDataAppl_In register_In)
        {
            BLL_ContractServiceOtherData.Instance.ReviewContractProductServiceInfoOtherDataAppl(register_In);
        }


        #region gtis系列产品
        /*
         1.销售经理服务申请从合同中获取服务信息   √
         2.销售经理提交服务申请                   √
         3.初审人员查看服务申请列表               √
         4.初审人员查看单个服务申请信息           √
         5.初审人员通过初审                       √
         6.复核人员查看单个服务申请和初审信息     √
         7.复核人员通过服务审批                   √
         */


        /// <summary>
        /// 续约申请时获取账号信息并同步数据
        /// </summary>
        /// <param name="contractNum"></param>
        /// <returns></returns>
        [HttpPost]
        public OriginUserGroupInfo GetAndSynchroUserInfo(string contractNum)
        {
            return BLL_ContractService.Instance.GetAndSynchroUserInfo(contractNum);
        }

        /// <summary>
        /// 查看服务申请列表
        /// </summary>
        /// <param name="search_In"></param>
        [HttpPost]
        public ApiTableOut<SearchWitsApplList_Out> SearchWitsApplList(SearchWitsApplList_In search_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance.SearchWitsApplList(search_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据申请Id获取账号信息
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <returns></returns>
        [HttpPost]
        //[CheckFormAccess]
        public List<AddOrAuditWitsApplUser> GetUserListByWitsApplId(string witsApplId)
        {
            return DbOpe_crm_contract_serviceinfo_wits.Instance.GetUserListByWitsApplId(witsApplId);
        }

        /// <summary>
        /// 根据applyId获取Wits申请信息（初审复核时使用）
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        public GetWitsApplyInfo4Audit_Out GetWitsApplyInfo4Audit(string Id)
        {
            return BLL_ContractService.Instance.GetWitsApplyInfo4Audit(Id);
        }

        /// <summary>
        /// 审核合同服务信息Wits申请信息--初审登记
        /// </summary>
        [HttpPost]
        public void AuditWitsAppl(AuditWitsAppl_In audit_In)
        {
            BLL_ContractService.Instance.AuditWitsAppl(audit_In);
        }

        /// <summary>
        /// 审核合同服务信息Wits申请信息--复核
        /// </summary>
        /// <param name="review_In"></param>
        [HttpPost]
        public void ReviewWitsAppl(ReviewWitsApply_In review_In)
        {
            BLL_ContractService.Instance.ReviewWitsAppl(review_In);
        }

        /// <summary>
        /// 根据慧思服务ID获取已开通的服务信息
        /// </summary>
        /// <param name="serviceId">慧思服务ID</param>
        /// <returns>慧思服务已开通信息</returns>
        [HttpPost]
        public WitsServiceInfoResponse GetWitsServiceInfo(string serviceId)
        {
            return BLL_WitsService.Instance.GetWitsServiceInfoByServiceId(serviceId); 
        }
        #endregion



        #region gtis
        /// <summary>
        /// 赠送产品
        /// </summary>
        /// <param name="addFreeProductServices_In"></param>
        /// <returns></returns>
        [HttpPost]
        public void AddFreeProductServices(AddFreeProductServices_In addFreeProductServices_In)
        {
            BLL_ContractService.Instance.AddFreeProductServices(addFreeProductServices_In);
        }
        /// <summary>
        /// 根据查询条件获取合同服务信息环球慧思申请信息列表
        /// </summary>
        /// <param name="searchContractProductServiceInfoGtisAppl_IN"></param>
        /// <returns></returns>
        [HttpPost]
        //[CheckFormAccess]
        public ApiTableOut<SearchContractProductServiceInfoGtisAppl_OUT> SearchContractProductServiceInfoGtisApplList(SearchContractProductServiceInfoGtisAppl_IN searchContractProductServiceInfoGtisAppl_IN)
        {
            return BLL_ContractService.Instance.SearchContractProductServiceInfoGtisApplList(searchContractProductServiceInfoGtisAppl_IN);
        }

        
        /// <summary>
        /// 查找是否可以赠送
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public CheckGtisFreeAble_OUT CheckGtisFreeAble(string id)
        {
            return BLL_ContractService.Instance.CheckGtisFreeAble(id);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息_环球慧思信息（申请信息+在服信息）
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        [HttpPost]
        public ContractServiceGtisInfo_OUT GetContractServiceInfoGtisByApplId(string applId)
        {
            return BLL_ContractService.Instance.GetContractServiceInfoGtisByApplId(applId);
        }
        /// <summary>
        /// 获取续约公司被续约合同中的GTIS服务信息
        /// </summary>
        /// <param name="getOldContractGtisInfo_In"></param>
        /// <returns></returns>
        [HttpPost]
        public GtisInfo_OUT GetOldContractGtisInfoByContractId(GetOldContractGtisInfo_In getOldContractGtisInfo_In)
        {
            return BLL_ContractService.Instance.GetOldContractGtisInfoByContractId(getOldContractGtisInfo_In.ContractId, getOldContractGtisInfo_In.FirstParty);
        }

        /// <summary>
        /// 获取续约公司被续约合同中的GTIS服务信息
        /// </summary>
        /// <param name="oldContractGtisInfo_In"></param>
        /// <returns></returns>
        [HttpPost]
        public GtisOldInfo_OUT GetOldContractGtisInfo(GetOldContractGtisInfo_In oldContractGtisInfo_In)
        {
            return BLL_ContractService.Instance.GetOldContractGtisInfo(oldContractGtisInfo_In);
        }
        /// <summary>
        /// 根据申请id获取环球慧思用户信息列表
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        [HttpPost]
        public List<ServiceInfoGtisUser_OUT> GetContractServiceInfoGtisUserByApplId(string applId)
        {
            return BLL_ContractService.Instance.GetContractServiceInfoGtisUserByApplId(applId);
        }
        /// <summary>
        /// 确认发送GTIS账号 
        /// </summary>
        /// <param name="applId"></param>
        [HttpPost]
        public void SendGtisAccount(string applId)
        {
            BLL_ContractService.Instance.SendAccount(applId);
        }
        /// <summary>
        /// 设置环球慧思用户状态信息(启用/停用)
        /// </summary>
        /// <param name="setContractServiceInfoGtisUser_IN"></param>
        [HttpPost]
        public void SetContractServiceInfoGtisUser(SetContractServiceInfoGtisUser_IN setContractServiceInfoGtisUser_IN)
        {
            BLL_ContractService.Instance.SetContractServiceInfoGtisUser(setContractServiceInfoGtisUser_IN);
        }
        /// <summary>
        /// 启用/停用环球慧思产品中所有账户
        /// </summary>
        /// <param name="setContractServiceInfoGtisUserAll_IN"></param>
        [HttpPost]
        public void SetContractServiceInfoGtisUserAll(SetContractServiceInfoGtisUserAll_IN setContractServiceInfoGtisUserAll_IN)
        {
            BLL_ContractService.Instance.SetContractServiceInfoGtisUserAll(setContractServiceInfoGtisUserAll_IN);
        }
        /// <summary>
        /// 审核合同服务信息环球慧思申请信息
        /// </summary>
        /// <param name="auditContractProductServiceInfoGtisAppl_IN"></param>
        [HttpPost]
        public void AuditContractProductServiceInfoGtisAppl(AuditContractProductServiceInfoGtisAppl_IN auditContractProductServiceInfoGtisAppl_IN)
        {
            BLL_ContractService.Instance.AuditContractProductServiceInfoGtisAppl(auditContractProductServiceInfoGtisAppl_IN);
        }
        /// <summary>
        /// 复核/开通GTIS服务产品
        /// </summary>
        /// <param name="reviewContractProductServiceInfoGtisAppl_IN"></param>
        [HttpPost]
        public void ReviewContractProductServiceInfoGtisAppl(ReviewContractProductServiceInfoGtisAppl_IN reviewContractProductServiceInfoGtisAppl_IN)
        {
            BLL_ContractService.Instance.ReviewContractProductServiceInfoGtisAppl(reviewContractProductServiceInfoGtisAppl_IN);
        }

        /// <summary>
        /// 撤销:（已开通、被驳回、待复核、拒绝   需要验证服务有效性以及撤回后不能存在多审核 ）
        /// 若开通状态为已开通，撤销后，需向GTIS发送账号指令
        /// </summary>
        /// <param name="applId"></param>
        [HttpPost]
        public void RevokeContractProductServiceInfoGtisAudit(string applId)
        {
            BLL_ContractService.Instance.RevokeContractProductServiceInfoGtisAudit(applId);
        }


        /// <summary>
        /// 作废:（已开通、被驳回、待复核、拒绝）
        /// 若开通状态为已开通，作废后，需向GTiS发送账号指令
        /// 作废生效的审核要把历史审核变为有效
        /// </summary>
        /// <param name="applId"></param>
        [HttpPost]
        public void CancelContractProductServiceInfoGtisAudit(string applId)
        {
            BLL_ContractService.Instance.CancelContractProductServiceInfoGtisAudit(applId);
        }
        /// <summary>
        /// 获取服务产品状态选择
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public List<int> GetUserContractProductServiceStates()
        {
            return BLL_ContractService.Instance.GetUserStates();
        }

        /// <summary>
        /// 获取GTIS用户使用日志
        /// </summary>
        /// <param name="gtisUserLog_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GtisUserUseLog_OUT> GetContractServiceInfoGtisUserLog(GtisUserLog_IN gtisUserLog_IN)
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserLog(gtisUserLog_IN);
        }
        /// <summary>
        /// 获取GTIS指定账号的按月使用情况
        /// </summary>
        /// <param name="gtisUserMonthLog_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GtisUserMonthLog_OUT> GetContractServiceInfoGtisUserMonthLog(GtisUserMonthLog_IN gtisUserMonthLog_IN)
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserMonthLog(gtisUserMonthLog_IN);
        }
        /// <summary>
        /// 获取GTIS账号使用日志详情
        /// </summary>
        /// <param name="gtisUserLogDetail_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public ApiTableOut<BM_UserOpeLogDetail> GetContractServiceInfoGtisUserLogDetailTable(GtisUserLogDetail_IN gtisUserLogDetail_IN)
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserLogDetailTable(gtisUserLogDetail_IN);
        }
        /// <summary>
        /// 获取GTIS账号导出日志详情
        /// </summary>
        /// <param name="gtisUserDownloadLogDetail_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public ApiTableOut<BM_UserExportLogDetail> GetContractServiceInfoGtisDownloadLogDetailTable(GtisUserDownloadLogDetail_IN gtisUserDownloadLogDetail_IN)
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisDownloadLogDetailTable(gtisUserDownloadLogDetail_IN);
        }

        /// <summary>
        /// 同步Gtis用户信息数据
        /// </summary>
        /// <param name="ContractNum"></param>
        [HttpPost]
        public void SynchroGtisUserData(string ContractNum)
        {
            List<Db_crm_contract_serviceinfo_gtis_user> userDatas = new List<Db_crm_contract_serviceinfo_gtis_user>();
            string gtisContractNum = string.Empty;
            BLL_ContractService.Instance.SynchroGtisUserData(ContractNum, ref userDatas, ref gtisContractNum);
        }
        /// <summary>
        /// 下载日志
        /// </summary>
        [HttpPost]
        public IActionResult DownloadGtisUserLog(List<string> gtisUserIds)
        {
            Stream result = DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.DownloadGtisUserLog(gtisUserIds);
            return new FileStreamResult(result, "application/octet-stream");
        }

        /// <summary>
        /// 检查子账号授权国家次数是否减少
        /// </summary>
        /// <param name="checkAuthorizationNumReduce_In"></param>
        /// <returns></returns>
        [HttpPost]
        public bool CheckAuthorizationNumReduce(CheckAuthorizationNumReduce_In checkAuthorizationNumReduce_In)
        {
            return BLL_ContractService.Instance.CheckAuthorizationNumReduce(checkAuthorizationNumReduce_In);
        }

        /// <summary>
        /// 服务情况列表
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchServicesList4ServeManage_OUT> SearchSericesList4Service(SearchServicesList4ServeManage_OUT_IN condition)
        {
            int total = 0;
            var list = DbOpe_crm_contract_serviceinfo_gtis.Instance.SearchSericesList(condition, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 刷新过期服务
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public void RefreshService()
        {
            //处理过期的GTIS正式
            DbOpe_crm_contract_serviceinfo_gtis.Instance.CheckGtisServiceOutOfTime();
            //查找过期的GTIS临时
            //标记过期的D&B邓白氏
            List<string> DBOverDueServiceIds = DbOpe_crm_contract_serviceinfo_db.Instance.GetOverDueServiceIds();
            DbOpe_crm_contract_serviceinfo_db.Instance.OverDueContractService(DBOverDueServiceIds);
            //标记过期的环球搜
            List<string> GlobalSearchOverDueServiceIds = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetOverDueServiceIds();
            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.OverDueContractService(GlobalSearchOverDueServiceIds);
            List<string> GlobalSearchOverDueServiceUserIds = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetOverDueServiceUserIds(GlobalSearchOverDueServiceIds);
            DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.OverDueContractService(GlobalSearchOverDueServiceUserIds);
            //标记过期的慧思学院服务
            List<string> CollegeOverDueServiceIds = DbOpe_crm_contract_serviceinfo_college.Instance.GetOverDueServiceIds();
            DbOpe_crm_contract_serviceinfo_college.Instance.OverDueContractService(CollegeOverDueServiceIds);
            //停用过期的慧思学院服务对应的账号
            List<string> CollegeOverDueServiceContacts = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetOverDueServiceContacts(CollegeOverDueServiceIds);
            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.OverDueContractService(CollegeOverDueServiceContacts);
            //标记过期的其他数据
            List<string> OtherDataOverDueServiceIds = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetOverDueServiceIds();
            DbOpe_crm_contract_serviceinfo_otherdata.Instance.OverDueContractService(OtherDataOverDueServiceIds);
        }


        /// <summary>
        /// 刷新密码
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public void Refresh()
        {
            DbOpe_sys_user.Instance.RefreshUserPassword();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.bushuju();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
        }
        /// <summary>
        /// 补gtis数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public int bushuju()
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Bushuju();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.bushuju();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
        }
        /// <summary>
        /// 补子账号
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public int buzizhanghao()
        {
            LogUtil.AddLog("buzizhanghao start");
            return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Buzizhanghao();

            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.bushuju();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
        }
        /// <summary>
        /// updatedate
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public int updatedate()
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Updatedate();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.bushuju();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
        }
        /// <summary>
        /// updatedate
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipRecordLog]
        public int buhuanqiusou()
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Buhuanqiusou();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.bushuju();
            //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.updatedate();
        }
        #endregion
    }
}
