﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;
using static CRM2_API.Model.ControllersViewModel.VM_OriginalInvoice;

namespace CRM2_API.Controllers
{
    [Description("发票原件控制器")]
    public class OriginalInvoiceController : MyControllerBase
    {
        public OriginalInvoiceController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 添加发票原件信息
        /// </summary>
        /// <param name="addInfo_In"></param>
        [HttpPost, PreLog]
        public void AddOriginalInvoice(AddOriginalInvoice_In addInfo_In)
        {
            BLL_OriginalInvoice.Instance.AddOriginalInvoice(addInfo_In);
        }

        /// <summary>
        /// 修改发票原件信息
        /// </summary>
        /// <param name="updInfo_In"></param>
        [HttpPost, PreLog]
        public void UpdateOriginalInvoice(UpdateOriginalInvoice_In updInfo_In)
        {
            BLL_OriginalInvoice.Instance.UpdateOriginalInvoice(updInfo_In);
        }

        /// <summary>
        /// 删除发票原件信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void DeleteOriginalInvoice(string Ids)
        {
            BLL_OriginalInvoice.Instance.DeleteOriginalInvoice(Ids);
        }

        /// <summary>
        /// 根据查询条件获取发票原件信息列表
        /// </summary>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchOriginalInvoiceList_Out> SearchOriginalInvoiceList(SearchOriginalInvoiceList_In search_In)
        {
            int total = 0;
            var list = DbOpe_crm_original_invoice.Instance.SearchOriginalInvoiceList(search_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据id获取发票原件信息列表
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetOriginalInvoiceById_Out GetOriginalInvoiceById(string Id)
        {
            return DbOpe_crm_original_invoice.Instance.GetOriginalInvoiceById(Id);
        }

        /// <summary>
        /// 根据发票原件信息id获取合同和发票信息列表
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<GetContractAndInvoiceByOriginalInvoiceId_Out> GetContractAndInvoiceByOriginalInvoiceId(GetContractAndInvoiceByOriginalInvoiceId_In search_In)
        {
            int total = 0;
            var list = DbOpe_crm_original_invoice_details_entity.Instance.GetContractAndInvoiceByOriginalInvoiceId(search_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据开票公司、发票类型、发票形式获取未使用的发票实体，无分页
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public List<GetOriginalInvoiceList_Out> GetOriginalInvoiceList(GetOriginalInvoiceList_In search_In)
        {
            return DbOpe_crm_original_invoice.Instance.GetOriginalInvoiceList(search_In);
        }
    }
}
