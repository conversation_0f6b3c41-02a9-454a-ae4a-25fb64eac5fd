﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace CRM2_API.Common
{
    /// <summary>
    /// Body属性string参数Trim处理
    /// </summary>
    public class StringTrimJsonConverter : JsonConverter<string>
    {
        public override string? Read<PERSON>son(JsonReader reader, Type objectType, string? existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            Console.WriteLine(reader.Value);
            if (reader.Value == null)
            {
                return null;
            }
            else
            {
                return reader.Value.ToString().Trim();
            }
        }

        public override void WriteJson(JsonWriter writer, string? value, JsonSerializer serializer)
        {
            writer.WriteValue(value);
        }
    }
}
