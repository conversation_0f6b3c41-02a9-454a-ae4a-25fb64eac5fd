[Unit]
Description=CRM2 API Test Service
After=network.target

[Service]
# 确保服务以root用户运行，拥有足够权限
User=root
WorkingDirectory=/home/<USER>/api/Release
# 在启动服务前先尝试杀死占用6001端口的进程 (仅使用ss)
ExecStartPre=-/bin/sh -c 'pid=$(ss -tulpn | grep ":6001 " | awk \'{print $7}\' | cut -d"," -f2 | cut -d"=" -f2 | grep -o "[0-9]*"); [ -n "$pid" ] && kill -9 $pid || true'
# 设置文件权限
ExecStartPre=/bin/chmod -R 755 /home/<USER>/api/Release
ExecStart=/usr/bin/dotnet /home/<USER>/api/Release/CRM2_API.dll
# 设置 OpenSSL 配置
Environment=OPENSSL_CONF=/etc/ssl/openssl_legacy.cnf
Restart=always
# 重启延迟10秒
RestartSec=10
# 崩溃或异常退出时重启
KillSignal=SIGINT
# 标准输出和错误输出重定向到日志文件
StandardOutput=append:/home/<USER>/log
StandardError=append:/home/<USER>/log

[Install]
WantedBy=multi-user.target 