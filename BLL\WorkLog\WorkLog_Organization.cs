﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;

namespace CRM2_API.BLL.WorkLog
{
    public class WorkLog_Organization : BaseWorkLog<WorkLog_Organization>
    {
        /// <summary>
        /// 添加组织的相关日志
        /// </summary>
        /// <param name="sysOrg">当前组织信息</param>
        /// <param name="subIdList">下级组织Id集合</param>
        public void AddOrgLog(Db_sys_organization sysOrg, List<string> subIdList)
        {
            //记录上级组织日志，并返回上级组织名称
            var parentOrgName = JoinOrSeparateParentOrgLog(sysOrg.ParentId, sysOrg.OrgName, EnumOrgLogType.Join);
            //记录下级组织日志，并返回下级组织名称集合字符串
            string subOrgs = AddOrRemoveSubOrgLog(subIdList, sysOrg.OrgName, EnumOrgLogType.AddSub);
            //编辑当前主日志详情
            string logContent = string.Format("{0}新增组织{1},新增组织详情为[部门名称:{2},团队类型:{3},上级组织名称:{4},下级组织名称:{5}]",
                RedisCache.UserInfo.GetUserInfo(UserTokenInfo.id).Name, sysOrg.OrgName, string.IsNullOrEmpty(sysOrg.DepartmentName) ? "无" : sysOrg.DepartmentName, sysOrg.OrgType.GetEnumDescription(), string.IsNullOrEmpty(parentOrgName) ? parentOrgName : "无", string.IsNullOrEmpty(subOrgs) ? "无" : subOrgs);
            //记录主体操作日志
            AddOrgWorkLog(sysOrg.Id, EnumOrgLogType.Add, logContent);
        }

        /// <summary>
        /// 更新组织相关日志
        /// </summary>
        /// <param name="updOrg_In">新组织信息</param>
        /// <param name="hisOrg">旧组织信息</param>
        /// <param name="hisSubOrgIds">旧组织关联下级组织Id集合</param>
        public void UpdateOrgLog(UpdateOrganization_In updOrg_In, Db_sys_organization hisOrg, List<string> hisSubOrgIds)
        {
            //日志详情字符串
            string logContent = string.Empty;
            //整理变化的内容 orgName,departmentName,orgtype,remark,
            var dicRet = LogCompareAttribute.Compare(updOrg_In, hisOrg);
            //主日志更新信息字符串
            string updateContent = string.Empty;
            //循环变化的内容，整理除上下级组织外的 updateContent
            foreach (var key in dicRet.Keys)
            {
                //判断上级组织是否发生变化
                updateContent += key + ":" + dicRet[key] + ";";
            }
            #region 上级组织日志
            if (!updOrg_In.ParentId.Equals(hisOrg.ParentId))
            {
                string parentOrgName = "空";
                //判断原数据有无上级组织，若有对原上级组织进行记录
                if (!"00000000-0000-0000-0000-000000000000".Equals(hisOrg.ParentId))
                    JoinOrSeparateParentOrgLog(hisOrg.ParentId, updOrg_In.OrgName, EnumOrgLogType.Separate);
                //判断修改后有无上级组织，若有则对新的上级组织进行记录
                if (!"00000000-0000-0000-0000-000000000000".Equals(updOrg_In.ParentId))
                    parentOrgName = JoinOrSeparateParentOrgLog(updOrg_In.ParentId, updOrg_In.OrgName, EnumOrgLogType.Join);
                //整理更新内容
                updateContent += "上级组织:" + parentOrgName;
            }
            #endregion

            #region 下级组织日志
            //获取updOrg_In.SubOrgIds的组织名称
            var curSubOrgIds = string.IsNullOrEmpty(updOrg_In.SubOrgIds)? new List<string>() : updOrg_In.SubOrgIds.Split(",").ToList();
            //求交集
            var interSubOrgIds = hisSubOrgIds.Intersect(curSubOrgIds);
            //计算删除的关联
            var toDelSubOrgIds = hisSubOrgIds.Except(interSubOrgIds).ToList();
            //计算添加的关联
            var toAddSubOrgIds = curSubOrgIds.Except(interSubOrgIds).ToList();
            //记录删除下级组织部分的日志，并返回删除的下级组织名称集合字符串
            string delOrgName = AddOrRemoveSubOrgLog(toDelSubOrgIds, updOrg_In.OrgName, EnumOrgLogType.AddSub);
            //记录新增下级组织部分的日志，并返回新增的下级组织名称集合字符串
            string addOrgName = AddOrRemoveSubOrgLog(toAddSubOrgIds, updOrg_In.OrgName, EnumOrgLogType.RemoveSub);
            updateContent += "下级组织:" + (string.IsNullOrEmpty(delOrgName) ? "" : ("移除:" + delOrgName + ";")) + (string.IsNullOrEmpty(addOrgName) ? "" : ("新增:" + addOrgName + ";"));
            #endregion

            #region 当前更新组织主日志
            //整理最终修改内容
            updateContent = "修改内容:[" + (string.IsNullOrEmpty(updateContent) ? "无" : updateContent) + "]";
            //记录操作日志
            AddOrgWorkLog(updOrg_In.Id, EnumOrgLogType.Modify, string.Format("{0}修改组织{1}," + updateContent, RedisCache.UserInfo.GetUserInfo(UserTokenInfo.id).Name, updOrg_In.OrgName));
            #endregion
        }

        /// <summary>
        /// 批量删除组织的相关日志
        /// </summary>
        /// <param name="idList"></param>
        public void DeleteOrgsLog(List<string> idList)
        {
            //获取传入组织及其上级组织信息
            var orgList = DbOpe_sys_organization.Instance.GetOrgsByIds(idList);
            foreach (var sysOrg in orgList)
            {
                //记录删除日志
                AddOrgWorkLog(sysOrg.Id, EnumOrgLogType.Delete, string.Format("{0}删除组织{1},", RedisCache.UserInfo.GetUserInfo(UserTokenInfo.id).Name, sysOrg.OrgName));
                //记录脱离上级组织的日志
                JoinOrSeparateParentOrgLog(sysOrg, EnumOrgLogType.Separate);
            }
        }

        /// <summary>
        /// 批量启用组织的相关日志
        /// </summary>
        /// <param name="idList"></param>
        public void StartOrgsLog(List<string> idList)
        {
            foreach (string sysOrgId in idList)
            {
                //获取组织名称
                var orgName = DbOpe_sys_organization.Instance.GetOrgById(sysOrgId).OrgName;
                //记录日志
                AddOrgWorkLog(sysOrgId, EnumOrgLogType.Start, string.Format("{0}启用了组织{1}", RedisCache.UserInfo.GetUserInfo(UserTokenInfo.id).Name, orgName));
            }
        }

        /// <summary>
        /// 批量停用组织的相关日志
        /// </summary>
        /// <param name="idList">停用的组织Id集合</param>
        public void StopOrgsLog(List<string> idList)
        {
            //获取组织信息
            var orgList = DbOpe_sys_organization.Instance.GetOrgsByIds(idList);
            foreach (var sysOrg in orgList)
            {
                //获取组织名称
                var orgName = sysOrg.OrgName;
                //获取下级组织信息
                var subOrgs = sysOrg.SubOrgBasicInfo;
                //记录下级组织解除关联关系的日志
                AddOrRemoveSubOrgLog(subOrgs, orgName, EnumOrgLogType.RemoveSub);
                //记录下级员工解除关联关系的日志 
                foreach (var sysUser in sysOrg.UserBasicInfo)
                {
                    WorkLog_User.Instance.AddOrgWorkLog(sysOrg.Id, EnumUserLogType.Separate, string.Format("因组织{0}停用，{1}离开组织", orgName, sysUser.Name));
                }
                //记录日志
                AddOrgWorkLog(sysOrg.Id, EnumOrgLogType.Stop, string.Format("{0}停用了组织{1}", RedisCache.UserInfo.GetUserInfo(UserTokenInfo.id).Name, orgName));
            }
        }

        /// <summary>
        /// 合并下级组织日志记录
        /// </summary>
        /// <param name="curOrg"></param>
        /// <param name="mergeOrg"></param>
        public void MergeOrgLog(Db_sys_organization curOrg, Db_sys_organization mergeOrg)
        {
            //合并主体的操作日志
            AddOrgWorkLog(curOrg.Id, EnumOrgLogType.Merge, string.Format("{0}合并了组织{1}", curOrg.OrgName, mergeOrg.OrgName));
            //被合并组织的日志
            AddOrgWorkLog(mergeOrg.Id, EnumOrgLogType.BeMerged, string.Format("{0}被合并到组织{1}中", mergeOrg.OrgName, curOrg.OrgName));
        }

        /// <summary>
        /// 合并同级组织日志记录
        /// </summary>
        /// <param name="curOrg"></param>
        /// <param name="mergeOrg"></param>
        /// <param name="mergeSubOrgs"></param>
        /// <param name="mergeUsers"></param>
        public void MergeSameLevelOrgLog(Db_sys_organization curOrg, Db_sys_organization mergeOrg, List<OrganizationBasicInfo> mergeSubOrgs, List<Db_sys_user> mergeUsers)
        {
            //同级被合并组织直属人员迁移的日志
            foreach (var mergeUser in mergeUsers)
            {
                WorkLog_User.Instance.AddOrgWorkLog(mergeUser.Id, EnumUserLogType.Transfer, string.Format("{0}因组织{1}被合并,迁移到组织{2}中", mergeUser.Name, mergeOrg.OrgName, curOrg.OrgName));
            }
            //被同级组织合并而删除的日志
            AddOrgWorkLog(mergeOrg.Id, EnumOrgLogType.Delete, string.Format("{0}由于被同级组织{1}合并而删除", mergeOrg.OrgName, curOrg.OrgName));
            //吸纳与被迁移组织主体的日志
            foreach (var mergeSubOrg in mergeSubOrgs)
            {
                //合并主体的操作日志
                AddOrgWorkLog(curOrg.Id, EnumOrgLogType.Merge, string.Format("{0}合并了组织{1}的下级组织{2}", curOrg.OrgName, mergeOrg.OrgName, mergeSubOrg.OrgName));
                //被合并组织的日志
                AddOrgWorkLog(mergeSubOrg.Id, EnumOrgLogType.BeMerged, string.Format("{0}随上级组织{1}被合并到组织{2}中", mergeSubOrg.OrgName, mergeOrg.OrgName, curOrg.OrgName));
            }
        }


        /// <summary>
        /// 组织新增或移除下级组织
        /// 仅知道下级组织Id集合
        /// </summary>
        /// <param name="subIdList">下级组织Id集合</param>
        /// <param name="orgName">上级组织名称</param>
        /// <param name="type">新增或移除</param>
        /// <returns>下级组织名称集合</returns>
        private string AddOrRemoveSubOrgLog(List<string> subIdList, string orgName, EnumOrgLogType type)
        {
            var a = DbOpe_sys_organization.Instance.GetOrgDicByIds(subIdList);
            //循环记录被关联组织的日志，并整理下级组织名称subOrgs的值
            foreach (string subOrgId in subIdList)
            {
                var subOrg = DbOpe_sys_organization.Instance.GetOrgById(subOrgId);
                //获取下级组织名称
                var subOrgName = subOrg == null ? "" : subOrg.OrgName;
                string logContent = string.Empty;
                if (type == EnumOrgLogType.AddSub)
                    logContent = string.Format("{0}新增下级组织{1}", orgName, subOrgName);
                else if (type == EnumOrgLogType.RemoveSub)
                    logContent = string.Format("{0}移除下级组织{1}", orgName, subOrgName);
                //记录操作日志
                AddOrgWorkLog(subOrgId, type, logContent);
            }
            //返回下级组织名称集合
            return String.Join(",", DbOpe_sys_organization.Instance.GetOrgsByIds(subIdList).Select(e => e.OrgName).ToArray());
        }
        /// <summary>
        /// 组织新增或移除下级组织
        /// 知道下级组织完整信息
        /// </summary>
        /// <param name="subOrgs">下级组织信息集合</param>
        /// <param name="orgName">当前组织名称</param>
        /// <param name="type">新增或移除</param>
        private void AddOrRemoveSubOrgLog(List<OrganizationBasicInfo> subOrgs, string orgName, EnumOrgLogType type)
        {
            //循环记录被关联组织的日志，并整理下级组织名称subOrgs的值
            foreach (var subOrg in subOrgs)
            {
                //获取下级组织名称
                var subOrgName = subOrg.OrgName;
                string logContent = string.Empty;
                if (type == EnumOrgLogType.AddSub)
                    logContent = string.Format("{0}新增下级组织{1}", orgName, subOrgName);
                else if (type == EnumOrgLogType.RemoveSub)
                    logContent = string.Format("{0}移除下级组织{1}", orgName, subOrgName);
                //记录操作日志
                AddOrgWorkLog(subOrg.Id, type, logContent);
            }
        }
        /// <summary>
        /// 组织加入或脱离上级组织
        /// </summary>
        /// <param name="parentId">上级组织Id</param>
        /// <param name="orgName">当前组织名称</param>
        /// <param name="type">加入或脱离</param>
        /// <returns>上级组织名称</returns>
        private string JoinOrSeparateParentOrgLog(string parentId, string orgName, EnumOrgLogType type)
        {
            var parentOrg = DbOpe_sys_organization.Instance.GetOrgById(parentId);
            //获取上级组织名称
            var parentOrgName = parentOrg ==null ? "": parentOrg.OrgName;
            string logContent = string.Empty;
            if (type == EnumOrgLogType.Join)
                logContent = string.Format("{0}加入上级组织{1}", orgName, parentOrgName);
            else if (type == EnumOrgLogType.RemoveSub)
                logContent = string.Format("{0}离开上级组织{1}", orgName, parentOrgName);
            //记录操作日志
            AddOrgWorkLog(parentId, type, logContent);
            //返回上级组织名称
            return parentOrgName;
        }

        /// <summary>
        /// 组织加入或脱离上级组织
        /// 知道组织和上级组织基本信息
        /// </summary>
        /// <param name="sysOrg">组织及上级组织基本信息</param>
        /// <param name="type">加入或脱离</param>
        private void JoinOrSeparateParentOrgLog(OrganizationAndParentBasicInfo sysOrg, EnumOrgLogType type)
        {
            string logContent = string.Empty;
            if (type == EnumOrgLogType.Join)
                logContent = string.Format("{0}加入上级组织{1}", sysOrg.OrgName, sysOrg.ParentOrgName);
            else if (type == EnumOrgLogType.RemoveSub)
                logContent = string.Format("{0}离开上级组织{1}", sysOrg.OrgName, sysOrg.ParentOrgName);
            //记录操作日志
            AddOrgWorkLog(sysOrg.ParentId, type, logContent);
        }

        /// <summary>
        /// 记录组织操作日志
        /// </summary>
        /// <param name="sysOrgId"></param>
        /// <param name="enumOrgLogType"></param>
        /// <param name="log"></param>
        private void AddOrgWorkLog(string sysOrgId, EnumOrgLogType enumOrgLogType, string log)
        {
            var workLog = Com_WorkLog.Instance;
            var sysOpeOrgLog = new Db_sys_operation_org_log();
            sysOpeOrgLog.Id = Guid.NewGuid().ToString();
            sysOpeOrgLog.orgId = sysOrgId;
            sysOpeOrgLog.operateName = RedisCache.UserInfo.GetUserInfo(UserTokenInfo.id).Name;
            sysOpeOrgLog.operateType = enumOrgLogType;
            sysOpeOrgLog.sysLogId = workLog.Id;
            sysOpeOrgLog.opterateTime = DateTime.Now;
            sysOpeOrgLog.content = log;
            DbOpe_sys_operation_org_log.Instance.InsertQueue(sysOpeOrgLog);
        }
    }
}
