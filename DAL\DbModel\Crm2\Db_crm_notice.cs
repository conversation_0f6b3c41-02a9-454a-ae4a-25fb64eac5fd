﻿using System;
using System.Linq;
using System.Text;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///公告通知表
    ///</summary>
    [SugarTable("crm_notice")]
    public class Db_crm_notice
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:公告通知号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string NoticeNo {get;set;}

           /// <summary>
           /// Desc:作者Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:标题
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Title {get;set;}

           /// <summary>
           /// Desc:文章类型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public EnumNoticeType? Type {get;set;}

           /// <summary>
           /// Desc:发送范围
           /// Default:
           /// Nullable:True
           /// </summary>           
           public EnumNoticeSendRange? SendRange {get;set;}

           /// <summary>
           /// Desc:内容
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Content {get;set;}

           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>           
           public EnumNoticeState? State {get;set;}

           /// <summary>
           /// Desc:缩列图
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Thumbnail {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
