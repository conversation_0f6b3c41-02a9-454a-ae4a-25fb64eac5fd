﻿using Aspose.Pdf.Generator;
using CRM2_API.Common;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Net;

namespace CRM2_API.Common
{
    public class IPOperator
    {

        private readonly List<string> _ipPatterns;
        public IPOperator(IEnumerable<string> ipPatterns)
        {
            _ipPatterns = ipPatterns.ToList();
        }

        public bool IsIpInList(string ipAddress)
        {
            var ipSegments = ipAddress.Split('.');
            return _ipPatterns.Any(pattern => MatchesPattern(ipSegments, pattern.Split('.')));
        }

        private bool MatchesPattern(string[] ipSegments, string[] patternSegments)
        {
            if (ipSegments.Length != patternSegments.Length)
            {
                return false;
            }

            for (int i = 0; i < ipSegments.Length; i++)
            {
                if (!MatchesSegment(ipSegments[i], patternSegments[i]))
                {
                    return false;
                }
            }

            return true;
        }

        private bool MatchesSegment(string ipSegment, string patternSegment)
        {
            if (patternSegment == "*")
            {
                return true; // 通配符匹配任意字符
            }

            return ipSegment == patternSegment; // 简单的字符串比较
        }
        //public void Test()
        //{
        //    var ipPatterns = new List<string> {
        //    "192.168.1.*",
        //    "10.0.*.*",
        //    "**********"
        //};

        //    var ipMatcher = new IPOperator(ipPatterns);

        //    var testIps = new List<string> {
        //    "************",
        //    "********",
        //    "**********",
        //    "**********",
        //    "************"
        //};

        //    foreach (var ip in testIps)
        //    {
        //        Console.WriteLine($"{ip}: {ipMatcher.IsIpInList(ip)}");
        //    }
        //}
    }

}
