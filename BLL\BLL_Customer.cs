﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.BLL.RemindInfo;
using CRM2_API.BLL.TrackingRecord;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Utils.IdentityCard;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.Services.PreInspection;
using Microsoft.AspNetCore.Http;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SqlSugar;
using System.ComponentModel;
using System.IO;
using System.Reflection;

namespace CRM2_API.BLL
{
    public partial class BLL_Customer : BaseBLL<BLL_Customer>
    {
        private string currentUser;

        public BLL_Customer()
        {
            currentUser = UserId;
#if DEBUG
            //currentUser = "00f72030-c806-4cde-a22a-e2bd17c2b08a";
            //currentUser = "78fe2d7b-6ce4-4040-a30b-3a2f10b9a972";
            //currentUser = "df18b32a-c6c1-4f9c-951f-aa36fe770628";
            //currentUser = "02880719-b912-4df5-a5e0-acf0c010a505";
            //currentUser = "5bb52b03-2476-4fc8-b7df-66844727e5ec";
            //currentUser = "02880719-b912-4df5-a5e0-acf0c010a505";
#endif
        }

        #region 客户服务产品

        public List<SearchServicesList_OUT> SearchSericesList(SearchServicesList_IN condition, ref int total)
        {
            return DbOpe_crm_customer.Instance.SearchSericesList(condition, currentUser, ref total);
        }
        /// <summary>
        /// 根据申请id获取环球慧思用户信息列表
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public List<ServiceInfoGtisUser_OUT> GetGtisServiceUserByApplId(string applId)
        {
            DbOpe_crm_customer.Instance.CheckApplAuth(applId, currentUser);
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserByApplId(applId);
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_环球慧思信息（申请信息+ 登记复核信息 + 在服信息）
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public ContractServiceGtisInfo_OUT GetGtisServiceInfoByApplId(string applId)
        {
            DbOpe_crm_customer.Instance.CheckApplAuth(applId, currentUser);
            return BLL_ContractService.Instance.GetContractServiceInfoGtisByApplId(applId);
            //return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceInfoGtisByApplId(applId);
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_慧思学院信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public GetContractServiceInfoCollegeByApplId_Out GetCollegeInfoByApplId(string applId)
        {
            DbOpe_crm_customer.Instance.CheckApplAuth(applId, currentUser);
            //获取申请详细信息
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetContractServiceInfoCollegeByApplId(applId);
            //获取合同详细信息
            apply.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);

            return apply;
        }
        /// <summary>
        /// 根据申请ID获取环球搜开通账号信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public List<GetContractServiceInfoGlobalSearchUserListByApplId_Out> GetGlobalSearchServiceUserListByApplId(string applId)
        {
            DbOpe_crm_customer.Instance.CheckApplAuth(applId, currentUser);
            return BLL_ContractServiceGlobalSearch.Instance.GetContractServiceInfoGlobalSearchUserListByApplId(applId);
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_环球搜信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public GetContractServiceInfoGlobalSearchByApplId_Out GetGlobalSearchInfoByApplId(string applId)
        {
            DbOpe_crm_customer.Instance.CheckApplAuth(applId, currentUser);
            //获取申请详细信息
            var apply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetContractServiceInfoGlobalSearchByApplId(applId);
            //获取合同详细信息
            apply.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
            return apply;
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_邓白氏信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public GetContractServiceInfoDBByApplId_Out GetDBInfoByApplId(string applId)
        {
            DbOpe_crm_customer.Instance.CheckApplAuth(applId, currentUser);

            return BLL_ContractServiceDB.Instance.GetContractServiceInfoDBByApplId(applId);
            /* //获取申请详细信息
             var obj = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetContractServiceInfoDBByApplId(applId);
             //获取合同信息
             obj.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(obj.ContractId);
             //整理摒除冗余属性数据
             var retObj = obj.MappingTo<GetContractServiceInfoDBByApplId_Out>();
             return retObj;*/
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_其他数据信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public GetContractServiceInfoOtherDataByApplId_Out GetOtherDataInfoByApplId(string applId)
        {
            DbOpe_crm_customer.Instance.CheckApplAuth(applId, currentUser);
            //获取申请详细信息
            return DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetContractServiceInfoOtherDataByApplId(applId);
        }

        /// <summary>
        /// 申请加急处理
        /// </summary>
        /// <param name="updateApplUrgent_In"></param>
        public void UpdateApplUrgent(List<UpdateApplUrgent_In> updateApplUrgent_In)
        {
            //gtis或vip加急
            var gtisApplyIds = updateApplUrgent_In.Where(e => e.ProductType == EnumProductType.Gtis || e.ProductType == EnumProductType.Vip).Select(e => e.ApplyId).ToList();
            if (gtisApplyIds != null && gtisApplyIds.Count > 0)
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.UpdateApplUrgent(gtisApplyIds);
            //邓白氏加急
            var dbApplyIds = updateApplUrgent_In.Where(e => e.ProductType == EnumProductType.DandB).Select(e => e.ApplyId).ToList();
            if (dbApplyIds != null && dbApplyIds.Count > 0)
                DbOpe_crm_contract_productserviceinfo_db_appl.Instance.UpdateApplUrgent(dbApplyIds);
            //环球搜加急
            var globalsearchApplyIds = updateApplUrgent_In.Where(e => e.ProductType == EnumProductType.Global).Select(e => e.ApplyId).ToList();
            if (globalsearchApplyIds != null && globalsearchApplyIds.Count > 0)
                DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateApplUrgent(globalsearchApplyIds);
            //慧思学院加急
            var collegeApplyIds = updateApplUrgent_In.Where(e => e.ProductType == EnumProductType.GlobalWitsSchool).Select(e => e.ApplyId).ToList();
            if (collegeApplyIds != null && collegeApplyIds.Count > 0)
                DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateApplUrgent(collegeApplyIds);
            //其他数据加急
            var otherApplyIds = updateApplUrgent_In.Where(e => e.ProductType == EnumProductType.Other).Select(e => e.ApplyId).ToList();
            if (otherApplyIds != null && otherApplyIds.Count > 0)
                DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateApplUrgent(otherApplyIds);

            DbOpe_crm_customer.Instance.SaveQueues();
        }
        #endregion

        #region 20240217 客户使用情况
        /// <summary>
        /// 获取客户/甲方公司下所有gtis账号使用情况列表
        /// </summary>
        /// <param name="gtisUserUseLog_IN"></param>
        /// <returns></returns>
        public ApiTableOut<GtisUserUseLog_OUT> GetCustomerGtisUserLog(GtisUserUseLog_IN gtisUserUseLog_IN)
        {
            ApiTableOut<GtisUserUseLog_OUT> r = new ApiTableOut<GtisUserUseLog_OUT>();
            r.Data = new List<GtisUserUseLog_OUT>();
            PrivateCustomerSimpleInfo customerInfo = new PrivateCustomerSimpleInfo();
            if (DbOpe_crm_customer_privatepool.Instance.CheckInPool(gtisUserUseLog_IN.CustomerId, ref customerInfo, currentUser))
            {
                List<string> appls = new List<string>();
                if (string.IsNullOrEmpty(gtisUserUseLog_IN.ApplId))
                {
                    appls = DbOpe_crm_customer_privatepool.Instance.GetCustomerGtisNewlyAppls(gtisUserUseLog_IN.CustomerId, gtisUserUseLog_IN.CompanyId, currentUser);
                }
                else
                {
                    appls = new List<string>() { gtisUserUseLog_IN.ApplId };
                }
                foreach (var applId in appls)
                {
                    //这里面刷新账号状态了，要试一下效率
                    var temp = DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserLog(new GtisUserLog_IN { ApplId = applId });
                    r.Data = r.Data.Concat(temp.Data);
                    r.Total += temp.Total;
                }
                return r;
            }
            else
            {
                LogUtil.AddErrorLog("权限错误：" + gtisUserUseLog_IN.SerializeNewtonJson());
                throw new ApiException("查询GTIS账号使用情况权限错误");
            }
        }
        /// <summary>
        /// 获取GTIS指定账号的按月使用情况
        /// </summary>
        /// <param name="gtisUserMonthLog_IN"></param>
        /// <returns></returns>
        public ApiTableOut<GtisUserMonthLog_OUT> GetGtisUserMonthLog(GtisUserMonthLog_IN gtisUserMonthLog_IN)
        {
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.CheckGtisUserAuth(new List<string>() { gtisUserMonthLog_IN.GtisUserId }, currentUser);
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserMonthLog(gtisUserMonthLog_IN);
        }
        /// <summary>
        /// 获取GTIS账号使用日志详情
        /// </summary>
        /// <param name="gtisUserLogDetail_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ApiTableOut<BM_UserOpeLogDetail> GetGtisUserLogDetailTable(GtisUserLogDetail_IN gtisUserLogDetail_IN)
        {
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.CheckGtisUserAuth(new List<string>() { gtisUserLogDetail_IN.GtisUserId }, currentUser);
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserLogDetailTable(gtisUserLogDetail_IN);
        }
        /// <summary>
        /// 获取GTIS账号导出详情
        /// </summary>
        /// <param name="gtisUserDownloadLogDetail_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ApiTableOut<BM_UserExportLogDetail> GetGtisDownloadLogDetailTable(GtisUserDownloadLogDetail_IN gtisUserDownloadLogDetail_IN)
        {
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.CheckGtisUserAuth(new List<string>() { gtisUserDownloadLogDetail_IN.GtisUserId }, currentUser);
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisDownloadLogDetailTable(gtisUserDownloadLogDetail_IN);
        }
        /// <summary>
        /// 获取GTIS账号导出详情
        /// </summary>
        /// <param name="gtisUserIds"></param>
        /// <returns></returns>
        public Stream DownloadGtisUserLog(List<string> gtisUserIds)
        {
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.CheckGtisUserAuth(gtisUserIds, currentUser);
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.DownloadGtisUserLog(gtisUserIds);
        }


        /// <summary>
        /// 获取全部客户最新登录情况列表
        /// </summary>
        /// <param name="allGtisUserUseLog_IN"></param>
        /// <returns></returns>
        public ApiTableOut<GtisAllUserUseLog_OUT> GetAllCustomerGtisUserLog(AllGtisUserUseLog_IN allGtisUserUseLog_IN)
        {
            return DbOpe_crm_contract_serviceinfo_gtis.Instance.GetAllCustomerGtisUserLogNew(allGtisUserUseLog_IN);
        }

        public GtisAllUserUseLog_OUT GetGtisAccountCountryInfoLog(GtisUseLog_IN gtisUseLog_IN)
        {
            return DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisAccountCountryInfoLog(gtisUseLog_IN);
        }


        public ApiTableOut<BM_OneUserOperateLogByMonth> GetGtisUserOperateLogByMonthLog(GtisUseLog_IN gtisUseLog_IN)
        {
            return DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisUserOperateLogByMonthLog(gtisUseLog_IN);
        }

        /// <summary>
        /// 获取phoneID使用日志详情
        /// </summary>
        /// <param name="gtisUserLogDetail_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ApiTableOut<BM_UserOpeLogDetail> GetGtisUserLogDetailTableByPhoneId(GtisUserLogDetailByPhoneId_IN gtisUserLogDetail_IN)
        {
            BM_GtisOpeGetOpeLogDetal bM_GtisOpeGetOpeLogDetal = gtisUserLogDetail_IN.MappingTo<BM_GtisOpeGetOpeLogDetal>();
            bM_GtisOpeGetOpeLogDetal.SysUserID = gtisUserLogDetail_IN.GtisUserId;
            bM_GtisOpeGetOpeLogDetal.PhoneID = gtisUserLogDetail_IN.PhoneId;
            bM_GtisOpeGetOpeLogDetal.PageNum = gtisUserLogDetail_IN.PageNumber;
            return BLL_GtisOpe.Instance.GetOpeLogDetail(bM_GtisOpeGetOpeLogDetal).Result;
        }

        /// <summary>
        /// 使用者导出详情
        /// </summary>
        /// <param name="gtisUserDownloadLogDetail_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ApiTableOut<BM_UserExportLogDetail> GetUserDownloadLogDetailTable(GtisUserDownloadLogDetail_IN gtisUserDownloadLogDetail_IN)
        {
            //暂时不验证,后续反推
            //DbOpe_crm_contract_serviceinfo_gtis_user.Instance.CheckGtisUserAuth(new List<string>() { gtisUserDownloadLogDetail_IN.GtisUserId }, currentUser);
            //return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisDownloadLogDetailTable(gtisUserDownloadLogDetail_IN);
            if (gtisUserDownloadLogDetail_IN.PhoneID.IsNullOrEmpty())
            {
                throw new ApiException("未找到使用者");
            }
            BM_GtisOpeGetExportLogDetal bM_GtisOpeGetExportLogDetal = gtisUserDownloadLogDetail_IN.MappingTo<BM_GtisOpeGetExportLogDetal>();
            //bM_GtisOpeGetExportLogDetal.SysUserID = user.UserId;
            return BLL_GtisOpe.Instance.GetExportLogDetail(bM_GtisOpeGetExportLogDetal).Result;
        }


        /// <summary>
        /// 获取团队临时账号使用情况列表
        /// </summary>
        /// <param name="demoUserQuery_IN"></param>
        /// <returns></returns>
        public ApiTableOut<BM_GtisOpe_DemoInfoStates> GetDemoQuery(DemoUserQuery_IN demoUserQuery_IN)
        {
            BM_GtisOpeDemoInfoStates_In log = new BM_GtisOpeDemoInfoStates_In();
            log = demoUserQuery_IN.MappingTo<BM_GtisOpeDemoInfoStates_In>();
            UserOrgTreeInfo ut = DbOpe_crm_contract_receiptregister.Instance.GetUserOrgTreeInfo(UserId);
            List<string> UserIds = new List<string>();

            if (demoUserQuery_IN.OrgIds.Count > 0)
            {
                if (!demoUserQuery_IN.OrgIds.Contains(ut.OrgId) && demoUserQuery_IN.OrgIds.Count > 2 && ut.OrgId != Guid.Empty.ToString())
                {
                    throw new ApiException("仅支持查看自己队伍的使用情况");
                }
                if (ut.UserType == 2)
                {
                    //throw new ApiException("非团队管理者");
                    UserIds.Add(UserId);
                }
                else
                {
                    if (ut.OrgId != Guid.Empty.ToString())
                    {
                        GetUserByOrgId_In o = new GetUserByOrgId_In { OrgId = ut.OrgId, PageNumber = 1, PageSize = 999 };
                        int noUse = 0;
                        List<string> userParts = DbOpe_sys_user.Instance.GetUserByOrgId(o, ref noUse).Select(i => i.Id).ToList();
                        UserIds.AddRange(userParts);
                    }
                    else
                    {
                        demoUserQuery_IN.OrgIds.ForEach(org =>
                        {
                            GetUserByOrgId_In o = new GetUserByOrgId_In { OrgId = org, PageNumber = 1, PageSize = 999 };
                            int noUse = 0;
                            List<string> userParts = DbOpe_sys_user.Instance.GetUserByOrgId(o, ref noUse).Select(i => i.Id).ToList();
                            UserIds.AddRange(userParts);
                        });
                    }
                }
            }
            else
            {
                GetUserByOrgId_In o = new GetUserByOrgId_In { OrgId = ut.OrgId, PageNumber = 1, PageSize = 999 };
                int noUse = 0;
                if (ut.UserType == 2)
                {
                    //throw new ApiException("非团队管理者");
                    UserIds.Add(UserId);
                }
                UserIds = ut.UserType == 2 ? UserIds : DbOpe_sys_user.Instance.GetUserByOrgId(o, ref noUse).Select(i => i.Id).ToList();
                if (ut.OrgId == Guid.Empty.ToString())
                {
                    UserIds = new List<string>();
                }
            }
            log.SysUserIDs = UserIds;
            log.Year = demoUserQuery_IN.QueryDate.Year;
            log.Months = demoUserQuery_IN.QueryDate.Month;

            return UserIds.Count == 0 ? new ApiTableOut<BM_GtisOpe_DemoInfoStates>() : BLL_GtisOpe.Instance.GetDemoUserQuery(log).Result;
        }

        /// <summary>
        /// 根据人员Id获取临时账号使用详情
        /// </summary>
        public ApiTableOut<BM_UserOpeLogDetail> GetDemoQueryOpeLogDetail(DemoUserQueryDetail_IN demoUserQueryDetailIn)
        {

            BM_GtisOpeGetOpeLogDetal bM_GtisOpeGetOpeLogDetal = demoUserQueryDetailIn.MappingTo<BM_GtisOpeGetOpeLogDetal>();
            bM_GtisOpeGetOpeLogDetal.PageNum = demoUserQueryDetailIn.PageNumber;
            bM_GtisOpeGetOpeLogDetal.PageSize = demoUserQueryDetailIn.PageSize;
            bM_GtisOpeGetOpeLogDetal.SysUserID = demoUserQueryDetailIn.OprID;
            bM_GtisOpeGetOpeLogDetal.OpeType = demoUserQueryDetailIn.OpeType;
            bM_GtisOpeGetOpeLogDetal.Year = demoUserQueryDetailIn.QueryDate.Year;
            bM_GtisOpeGetOpeLogDetal.Months = demoUserQueryDetailIn.QueryDate.Month;
            return BLL_GtisOpe.Instance.GetDemoQueryOpeLogDetail(bM_GtisOpeGetOpeLogDetal).Result;
        }

        /// <summary>
        /// 获取G5电话号码列表
        /// </summary>
        /// <param name="getG5PhoneList_In"></param>
        /// <returns></returns>
        public ApiTableOut<BM_GtisOpe5Phone> GetG5PhoneList(GetG5PhoneList_In getG5PhoneList_In)
        {
            return BLL_GtisOpe.Instance.GetG5Phone_List(getG5PhoneList_In.PageNumber, getG5PhoneList_In.PageSize).Result;
        }

        public ApiTableOut<BM_GtisOpe5Phone> GetG5Phone_New()
        {
            return BLL_GtisOpe.Instance.GetG5Phone_New().Result;
        }

        #endregion

        #region 提醒相关
        public List<RemindInfoOut> GetRemindInfos()
        {
            List<RemindInfoOut> remindInfoOuts = new List<RemindInfoOut>();
            //RemindInfoOut remindInfoOut = new RemindInfoOut() {
            //    RemindDataNum = 3,
            //    RemindDataIdAndNames = new List<ApiDictionary> {
            //    },
            //    RemindTypeDetail = EnumRemindType.SaveCustomerNearlyRelease.GetEnumDescription(),
            //    MaxShowNameNum = 5,
            //    RemindToPath = "/Customer/Index",
            //    RemindDataIdDbName = "CustomerId",
            //    RemindType = EnumRemindType.SaveCustomerNearlyRelease
            //};
            var SignCustomerNearlyRelease = DbOpe_crm_customer_privatepool.Instance.ProtectNearlyReleaseQuery(currentUser, EnumRemindType.SignCustomerNearlyRelease)
                .Select(it => new ApiDictionary() { Value = it.CustomerId, Name = it.CustomerName }).ToList();
            if (SignCustomerNearlyRelease.Count > 0)
            {
                remindInfoOuts.Add(new RemindInfoOut()
                {
                    RemindDataNum = SignCustomerNearlyRelease.Count,
                    RemindDataIdAndNames = SignCustomerNearlyRelease,
                    RemindTypeDetail = EnumRemindType.SignCustomerNearlyRelease.GetEnumDescription(),
                    MaxShowNameNum = 5,
                    RemindNumUnit = "个",
                    RemindToPath = "/customer/manage?EnumRemindType=11",
                    RemindDataIdDbName = "CustomerId",
                    RemindFormName = "私有池",
                    RemindType = EnumRemindType.SignCustomerNearlyRelease
                });
            }
            var SaveCustomerNearlyRelease = DbOpe_crm_customer_privatepool.Instance.ProtectNearlyReleaseQuery(currentUser, EnumRemindType.SaveCustomerNearlyRelease)
                .Select(it => new ApiDictionary() { Value = it.CustomerId, Name = it.CustomerName }).ToList();
            if (SaveCustomerNearlyRelease.Count > 0)
            {
                remindInfoOuts.Add(new RemindInfoOut()
                {
                    RemindDataNum = SaveCustomerNearlyRelease.Count,
                    RemindDataIdAndNames = SaveCustomerNearlyRelease,
                    RemindTypeDetail = EnumRemindType.SaveCustomerNearlyRelease.GetEnumDescription(),
                    MaxShowNameNum = 5,
                    RemindNumUnit = "个",
                    RemindToPath = "/customer/manage?EnumRemindType=12",
                    RemindDataIdDbName = "CustomerId",
                    RemindFormName = "私有池",
                    RemindType = EnumRemindType.SaveCustomerNearlyRelease
                });
            }
            var ServiceNearlyEnd = DbOpe_crm_customer_privatepool.Instance.ProtectNearlyReleaseQuery(currentUser, EnumRemindType.ServiceNearlyEnd)
                .Select(it => new ApiDictionary() { Value = it.CustomerId, Name = it.CustomerName }).ToList();
            if (ServiceNearlyEnd.Count > 0)
            {
                remindInfoOuts.Add(new RemindInfoOut()
                {
                    RemindDataNum = ServiceNearlyEnd.Count,
                    RemindDataIdAndNames = ServiceNearlyEnd,
                    RemindTypeDetail = EnumRemindType.ServiceNearlyEnd.GetEnumDescription(),
                    MaxShowNameNum = 5,
                    RemindNumUnit = "个",
                    RemindToPath = "/customer/manage?EnumRemindType=13",
                    RemindDataIdDbName = "CustomerId",
                    RemindFormName = "私有池",
                    RemindType = EnumRemindType.ServiceNearlyEnd
                });
            }

            return remindInfoOuts;
        }
        #endregion

        #region 私有池
        public void AddUpdatePrivatePoolCustomerAuth(CompanyId_IN companyId_IN)
        {
            var company = DbOpe_crm_customer_subcompany.Instance.GetCompanyInfoById(companyId_IN.CompanyId);
            if (company == null)
            {
                throw new ApiException("未找到该公司信息");
            }
            PrivateCustomerSimpleInfo privateCustomerSimpleInfo = new PrivateCustomerSimpleInfo();
            var exist = DbOpe_crm_customer_privatepool.Instance.CheckInPool(company.CustomerId, ref privateCustomerSimpleInfo);
            if (!exist)
            {
                throw new ApiException("未在客户经理私有池找到该客户信息");
            }
            company.CanEdit = true;
            DbOpe_crm_customer_subcompany.Instance.Update(company);
        }
        /// <summary>
        /// 添加私有池客户
        /// </summary>
        /// <param name="addCustomer_IN"></param>
        public string AddPrivatePoolCustomer(AddCustomer_IN<AddSubCompany_IN> addCustomer_IN)
        {
            /*
             * 自动生成保护截止日，生成方式：T+60。
             * 排重：验证客户信息是否重复，已存在则无法保存，返回相应提示信息(到私有池和公有池和个人临时池查询重复）
             * 验证客户保留数，如果为0则不可以保存，返回相应提示信息。
             * 慧思ID(客户编号)：自动生成，生成规则：？。
             * 其他临时池的相同名称/代码客户要置为失效
             * 如果这里添加了子公司的话，子公司需要提交审核，關係說明必須填
             * 记录客户归属日志（9.18添加）
             * 11.23 从资源池添加的 删除对应资源池的数据
             */
            var user = currentUser;
            var nowDate = DateTime.Now;
            string customerId = "";

            #region 获取唯一信息
            bool exist = false;
            CustomerUniqueInfo customerUniqueInfo = GetUniqueCustomerInfo(addCustomer_IN.MappingTo<CheckCompanyUniqueInfo>(), ref exist);
            #endregion

            #region 客户
            var crmCustomer = addCustomer_IN.MappingTo<Db_crm_customer>();
            crmCustomer.Id = customerUniqueInfo.CustomerId;
            crmCustomer.CreateUser = user;
            crmCustomer.CreateDate = nowDate;
            crmCustomer.IsValid = (int)EnumCustomerValid.VALID;
            crmCustomer.CustomerNum = customerUniqueInfo.CustomerNum;
            crmCustomer.IsSupplementary = (int)EnumNeedSupplement.NoNeed;
            customerId = crmCustomer.Id;
            #endregion

            #region 主公司
            //验证公司名称/代码的有效性（后补）
            Db_crm_customer_subcompany mainCompany = addCustomer_IN.MappingTo<Db_crm_customer_subcompany>();
            mainCompany.Id = customerUniqueInfo.MainCompanyId;
            mainCompany.CreateUser = user;
            mainCompany.CreateDate = nowDate;
            mainCompany.CustomerId = crmCustomer.Id;
            mainCompany.IsValid = (int)EnumCompanyValid.VALID;
            mainCompany.IsMain = (int)EnumCustomerCompanyMain.Main;
            mainCompany.CompanyNameDM = new ChineseAnalyzerCls().ReplaceKeyword(mainCompany.CompanyName);
            #endregion

            #region 子公司审核
            //验证公司名称/代码的有效性（后补）
            var auditId = Guid.NewGuid().ToString();
            var hasSubAudit = addCustomer_IN.SubCompanys != null && addCustomer_IN.SubCompanys.Count > 0;
            List<Db_crm_addsubcompany_audit_subcompany> auditSubCompanys = new List<Db_crm_addsubcompany_audit_subcompany>();
            Db_crm_addsubcompany_audit audit = new Db_crm_addsubcompany_audit();
            if (hasSubAudit)
            {
                if (StringUtil.IsNullOrEmpty(addCustomer_IN.RelationshipDescription))
                {
                    throw new ApiException("请填写关系说明");
                }
                if (addCustomer_IN.RelationshipFileInfo == null || addCustomer_IN.RelationshipFileInfo.Count == 0)
                {
                    throw new ApiException("请上传关系说明文件");
                }
                addCustomer_IN.SubCompanys.ForEach(c =>
                {
                    var customerSubCompanyId = Guid.NewGuid().ToString();
                    var auditSubCompany = c.MappingTo<Db_crm_addsubcompany_audit_subcompany>();
                    auditSubCompany.AddSubCompanyAuditId = auditId;
                    auditSubCompany.CustomerSubCompanyId = customerSubCompanyId;
                    auditSubCompany.Id = Guid.NewGuid().ToString();
                    auditSubCompany.CreateUser = user;
                    auditSubCompany.CreateDate = nowDate;
                    auditSubCompanys.Add(auditSubCompany);
                    //行业产品
                    DbOpe_crm_addsubcompany_audit_subcompany_mainbusiness.Instance.SaveBusiness(customerSubCompanyId, c.CustomerIndustry, c.MainProducts);
                    //保存联系信息
                    DbOpe_crm_addsubcompany_audit_subcompany_contacts.Instance.SaveContacts(customerSubCompanyId, auditId, c.ContactInfos, false);
                });
                audit = new Db_crm_addsubcompany_audit()
                {
                    Id = auditId,
                    CustomerId = crmCustomer.Id,
                    ApplicantId = user,
                    ApplicantDate = nowDate,
                    State = (int)EnumCustomerAddSubCompanyAuditState.InProcess,
                    CreateUser = user,
                    CreateDate = nowDate,
                    RelationshipDescription = addCustomer_IN.RelationshipDescription
                };
            }
            #endregion

            #region 获取需要排重的所有公司名称/代码
            var checkCompanys = auditSubCompanys.ToList().MappingTo<List<CheckCompanyUniqueInfo>>();
            checkCompanys.Add(mainCompany.MappingTo<CheckCompanyUniqueInfo>());
            #endregion

            #region 验证名称/代码合法性
            CheckCreditCode(checkCompanys);
            #endregion

            #region 公有/私有池排重
            DbOpe_crm_customer.Instance.CheckDuplicateCustomer(checkCompanys);
            #endregion

            #region 验证是不是已经在审核中
            CheckAuditInProcess(checkCompanys);
            #endregion

            #region 个人临时池排重
            DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(checkCompanys, user);
            #endregion

            #region 私有池
            //验证客户保留数
            DbOpe_crm_customer_privatepool.Instance.CheckMaxSavePrivateCustomer(user);
            Db_crm_customer_privatepool crmCustomer_pp = new Db_crm_customer_privatepool()
            {
                Id = Guid.NewGuid().ToString(),
                CustomerId = crmCustomer.Id,
                UserId = user,
                ProtectionDeadline = FormatProtectDate(),
                //CollectionTime = nowDate,
                State = (int)EnumCustomerPrivateRelease.Not,
                CreateType = (int)EnumCustomerPrivateCreateType.Create,
                ReleaseType = (int)EnumCustomerPrivateReleaseType.Not,
                Deleted = (int)EnumCustomerDel.NotDel,
                CreateUser = user,
                CreateDate = DateTime.Now
            };
            #endregion

            DbOpe_crm_customer.Instance.TransDeal(() =>
            {
                //行业与产品
                DbOpe_crm_customer_subcompany_mainbusiness.Instance.SaveBusiness(mainCompany.Id, addCustomer_IN.CustomerIndustry, addCustomer_IN.MainProducts);
                //添加
                if (!exist)
                {
                    DbOpe_crm_customer.Instance.Insert(crmCustomer);
                    DbOpe_crm_customer_subcompany.Instance.Insert(mainCompany);
                    
                    // 触发子公司添加事件
                    Services.PreInspection.CompanyNamePreprocessEventListener.Instance.OnSubCompanyAdded(mainCompany);
                }
                else
                {
                    DbOpe_crm_customer.Instance.Update(crmCustomer);
                    DbOpe_crm_customer_subcompany.Instance.Update(mainCompany);
                }
                //联系信息
                DbOpe_crm_customer_subcompany_contacts.Instance.SaveContacts(mainCompany.Id, addCustomer_IN.ContactInfos);
                //采购主体关联提醒
                DbOpe_crm_customer_subcompany_related.Instance.SaveRelateds(mainCompany.Id, addCustomer_IN.RelatedCompanies.MappingTo<List<SubCompanyRelated_IN>>());
                DbOpe_crm_customer_privatepool.Instance.Insert(crmCustomer_pp);
                if (hasSubAudit)
                {
                    DbOpe_crm_addsubcompany_audit.Instance.Insert(audit);
                    DbOpe_crm_addsubcompany_audit_subcompany.Instance.Insert(auditSubCompanys);
                    Util<DbOpe_crm_addsubcompany_attachfile, BM_AttachFiles> attachFiles = new(DbOpe_crm_addsubcompany_attachfile.Instance);
                    var uploadResult = attachFiles.UploadFiles(audit.Id, addCustomer_IN.RelationshipFileInfo, AttachEnumOption.SubRelation);
                    if (!uploadResult)
                    {
                        throw new ApiException("文件上传异常!");
                    }
                    foreach (var au in auditSubCompanys)
                    {
                        //保存联系信息
                        DbOpe_crm_addsubcompany_audit_subcompany_contacts.Instance.RefreshDefault(au.CustomerSubCompanyId, auditId);
                    }
                }
                //同步客户跟踪记录
                BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(crmCustomer.Id, customerUniqueInfo.MainCompanyId, "", "", (EnumTrackingStage)crmCustomer.TrackingStage, 1, true, false, "", EnumCustomerDataSource.Private, true);
                //记录客户归属日志（9.18添加）
                DbOpe_crm_customer_org_log.Instance.CreateOrCollectCustomerLog(new CreateOrCollectCustomerLogParams()
                {
                    CreateFlag = true,
                    CustomerId = crmCustomer.Id,
                    PrivatePoolId = crmCustomer_pp.Id,
                    UserId = crmCustomer_pp.UserId
                });
                //刷新临时池主公司相关的失效信息  子公司没审核呢 不刷
                DbOpe_crm_customer_subcompany_temporary.Instance.RefreshVaildCompanys(new List<CheckCompanyUniqueInfo>() { mainCompany.MappingTo<CheckCompanyUniqueInfo>() }, EnumCompanyValid.INVALID);
                //执行
                //DbOpe_crm_customer_subcompany.Instance.SaveQueues();

                #region 从资源池添加的 删除对应资源池的数据
                if (!string.IsNullOrEmpty(addCustomer_IN.DataSourceId))
                {
                    switch (addCustomer_IN.DataSource)
                    {
                        case EnumCustomerSource.Fair:
                            DbOpe_crm_customer_canton_fair.Instance.DeleteById(addCustomer_IN.DataSourceId);
                            break;
                        case EnumCustomerSource.ChinaExport:
                            DbOpe_crm_customer_china_exporter.Instance.DeleteById(addCustomer_IN.DataSourceId);
                            break;
                        case EnumCustomerSource.Import:
                            DbOpe_crm_customer_import.Instance.CollectById(addCustomer_IN.DataSourceId);
                            break;
                        default:
                            throw new ApiException("参数错误");
                    }
                }
                #endregion
                //11.23 删除资源池同名数据(未审核的子公司也先删了，拒绝的话再恢复)
                CheckAndDeleteSourceCustomer(checkCompanys);
                //240811 作废重复的采购主体关联提醒
                VoidCompanyRelated(checkCompanys);
                if (hasSubAudit)
                {
                    //保存客户信息备份
                    DbOpe_crm_customer_copy.Instance.SaveCustomerCopy(EnumCustomerCopyAuditType.AddSub, mainCompany.CustomerId, auditId);
                    string dataState = Dictionary.CustomerAddSubCompanyAuditState.First(e => e.Value == audit.State.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.AddWorkFlow("子公司审批流程", audit.Id, audit, mainCompany, addCustomer_IN.RelationshipDescription, dataState, "新建");
                }
            });
            return customerId;
        }
        /// <summary>
        /// 修改私有池客户（名称和代码是否可以更改 要看canedit标志位）
        /// </summary>
        /// <param name="updateCustomer_IN"></param>
        public void UpdatePrivatePoolCustomer(UpdateCustomer_IN<UpdateSubCompany_IN> updateCustomer_IN)
        {
            /*
             * 如果有新加的子公司名称/代码，要排重，排重后提交审核
             * 排重：验证客户信息是否重复，已存在则无法保存，返回相应提示信息(到私有池和公有池和个人临时池查询重复）
             * 保存后会自动将补录标志记为（无需补录）
             * 1.13 canedit true 更新名字和代码
             * 240630 审核中的话 canedit 也不能更新名字和代码
             */
            var user = currentUser;
            var nowDate = DateTime.Now;
            DbOpe_crm_customer.Instance.TransDeal(() =>
            {
                #region 原有客户信息
                CustomerInfo customerInfo = DbOpe_crm_customer.Instance.GetCustomerInfoById(updateCustomer_IN.CustomerId);
                CustomerInfo oldCustomerInfo = customerInfo.CloneNewtonJson();
                #endregion

                #region can edit 验证更新名字CompanyName和代码CreditCode 240630 审核中的话 canedit 也不能更新名字和代码
                var canEdit = customerInfo.mainCompany.CanEdit;
                var customerAuditFlag = CheckAuditInProcess(new List<string>() { updateCustomer_IN.CustomerId }, false);
                if (canEdit && customerAuditFlag.Count == 0)
                {
                    if (StringUtil.IsNullOrEmpty(updateCustomer_IN.CompanyName))
                    {
                        throw new ApiException("客户名称不能为空");
                    }
                    if (updateCustomer_IN.CreditType == null)
                    {
                        throw new ApiException("公司类型不能为空");
                    }
                    if (StringUtil.IsNullOrEmpty(updateCustomer_IN.CreditCode) && updateCustomer_IN.CreditType != (int)EnumCompanyType.Person)
                    {
                        throw new ApiException("信用代码不能为空");
                    }
                }
                else
                {
                    //不是canedit的话，名字CompanyName和代码CreditCode取数据库原来的值
                    updateCustomer_IN.CompanyName = oldCustomerInfo.mainCompany.CompanyName;
                    updateCustomer_IN.CreditCode = oldCustomerInfo.mainCompany.CreditCode;
                    updateCustomer_IN.CreditType = oldCustomerInfo.mainCompany.CreditType;
                }
                #endregion

                #region 更新客户信息
                updateCustomer_IN.MappingTo(customerInfo);
                Db_crm_customer crmCustomer = customerInfo.MappingTo<Db_crm_customer>();
                crmCustomer.IsSupplementary = (int)EnumNeedSupplement.NoNeed;
                DbOpe_crm_customer.Instance.Update(crmCustomer);
                #endregion

                #region 更新主公司信息 
                updateCustomer_IN.MappingTo(customerInfo.mainCompany);
                Db_crm_customer_subcompany mainCompany = customerInfo.mainCompany.MappingTo<Db_crm_customer_subcompany>();
                mainCompany.CanEdit = false;
                
                // 检查公司名称是否发生变化
                if (mainCompany.CompanyName != oldCustomerInfo.mainCompany.CompanyName)
                {
                    // 触发公司名称更新事件
                    Services.PreInspection.CompanyNamePreprocessEventListener.Instance.OnCompanyNameUpdated(mainCompany.Id, mainCompany.CompanyName);
                }
                
                //行业与产品
                DbOpe_crm_customer_subcompany_mainbusiness.Instance.SaveBusiness(mainCompany.Id, updateCustomer_IN.CustomerIndustry, updateCustomer_IN.MainProducts);
                #endregion

                #region 排重的公司列表
                List<CheckCompanyUniqueInfo> checkCompanys = new List<CheckCompanyUniqueInfo>();
                if (canEdit)
                {
                    checkCompanys.Add(new CheckCompanyUniqueInfo()
                    {
                        CompanyName = mainCompany.CompanyName,
                        CreditCode = mainCompany.CreditCode,
                        CreditType = mainCompany.CreditType
                    });
                }
                #endregion

                #region 更新子公司信息（新名字的公司要提交审核）
                if (updateCustomer_IN.SubCompanys == null)
                {
                    updateCustomer_IN.SubCompanys = new List<UpdateSubCompany_IN>();
                }
                //验证公司名称/代码的有效性（后补）
                Db_crm_addsubcompany_audit audit = new Db_crm_addsubcompany_audit();
                var auditId = Guid.NewGuid().ToString();
                //需要排重的所有公司名称/代码
                checkCompanys = checkCompanys.Concat(updateCustomer_IN.SubCompanys.FindAll(c => string.IsNullOrEmpty(c.Id)).MappingTo<List<CheckCompanyUniqueInfo>>()).ToList();
                //需要提交审核的公司列表
                List<Db_crm_addsubcompany_audit_subcompany> auditSubCompanys = new List<Db_crm_addsubcompany_audit_subcompany>();
                //不是新添加的公司
                List<Db_crm_customer_subcompany> updateSubCompanys = new List<Db_crm_customer_subcompany>();

                updateCustomer_IN.SubCompanys.ForEach(c =>
                {
                    string customerSubCompanyId;
                    if (StringUtil.IsNullOrEmpty(c.Id))
                    {
                        customerSubCompanyId = Guid.NewGuid().ToString();
                        var auditSubCompany = c.MappingTo<Db_crm_addsubcompany_audit_subcompany>();
                        auditSubCompany.AddSubCompanyAuditId = auditId;
                        auditSubCompany.CustomerSubCompanyId = customerSubCompanyId;
                        auditSubCompany.Id = Guid.NewGuid().ToString();
                        auditSubCompany.CreateUser = user;
                        auditSubCompany.CreateDate = nowDate;
                        auditSubCompanys.Add(auditSubCompany);
                        //行业产品
                        DbOpe_crm_addsubcompany_audit_subcompany_mainbusiness.Instance.SaveBusiness(customerSubCompanyId, c.CustomerIndustry, c.MainProducts);
                        //联系信息
                        DbOpe_crm_addsubcompany_audit_subcompany_contacts.Instance.SaveContacts(customerSubCompanyId, auditId, c.ContactInfos, false);
                    }
                    else
                    {
                        customerSubCompanyId = c.Id;
                        var subComInfo = customerInfo.subCompanyList.Find(c => c.Id == customerSubCompanyId);
                        c.MappingTo(subComInfo);
                        updateSubCompanys.Add(subComInfo.MappingTo<Db_crm_customer_subcompany>());
                        //行业产品
                        DbOpe_crm_customer_subcompany_mainbusiness.Instance.SaveBusiness(customerSubCompanyId, c.CustomerIndustry, c.MainProducts);
                        //联系信息
                        DbOpe_crm_customer_subcompany_contacts.Instance.SaveContacts(customerSubCompanyId, c.ContactInfos, false);
                        //采购主体关联提醒
                        DbOpe_crm_customer_subcompany_related.Instance.SaveRelateds(customerSubCompanyId, c.RelatedCompanies);
                    }
                });
                var hasSubAudit = auditSubCompanys.Count > 0;
                if (hasSubAudit)
                {
                    if (StringUtil.IsNullOrEmpty(updateCustomer_IN.RelationshipDescription))
                    {
                        throw new ApiException("请填写关系说明");
                    }
                    if (updateCustomer_IN.RelationshipFileInfo == null || updateCustomer_IN.RelationshipFileInfo.Count == 0)
                    {
                        throw new ApiException("请上传关系说明文件");
                    }
                    var checkAuditCompanys = checkCompanys.MappingTo<List<CheckCompanyUniqueInfo>>();
                    checkAuditCompanys.Add(customerInfo.mainCompany.MappingTo<CheckCompanyUniqueInfo>());
                    CheckAuditInProcess(checkAuditCompanys);
                    audit = new Db_crm_addsubcompany_audit()
                    {
                        Id = auditId,
                        CustomerId = crmCustomer.Id,
                        ApplicantId = user,
                        ApplicantDate = nowDate,
                        CreateUser = user,
                        CreateDate = nowDate,
                        RelationshipDescription = updateCustomer_IN.RelationshipDescription
                    };
                }
                #endregion

                #region 如果代码修改，需要进行排重（除去当前客户本身，与其他客户进行查重）
                //if (mainCompany.CreditCode != oldCustomerInfo.mainCompany.CreditCode)
                //{
                //    var ccUniqueInfo = new List<CheckCompanyUniqueInfo>() {new CheckCompanyUniqueInfo()
                //{
                //    CompanyName = mainCompany.CompanyName,
                //    CreditCode = mainCompany.CreditCode
                //} };
                //    DbOpe_crm_customer.Instance.CheckDuplicateCustomer(ccUniqueInfo, new List<string>() { mainCompany.CustomerId });
                //    DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(ccUniqueInfo, user);
                //}
                #endregion

                #region 验证名称/代码合法性
                CheckCreditCode(checkCompanys);
                #endregion

                #region 公有/私有池排重
                if (canEdit)
                {
                    //这里主公司的名称/代码是有可能修改了的，排重时要排除当前客户本身
                    DbOpe_crm_customer.Instance.CheckDuplicateCustomer(checkCompanys, new List<string>() { crmCustomer.Id });
                }
                else
                {
                    DbOpe_crm_customer.Instance.CheckDuplicateCustomer(checkCompanys);
                }
                #endregion

                #region 验证是不是已经在审核中
                var allCheckCompanys = updateCustomer_IN.SubCompanys.MappingTo<List<CheckCompanyUniqueInfo>>();
                allCheckCompanys.Add(mainCompany.MappingTo<CheckCompanyUniqueInfo>());
                CheckAuditInProcess(allCheckCompanys);
                #endregion

                #region 个人临时池排重
                DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(checkCompanys, user);
                #endregion

                //更新
                DbOpe_crm_customer.Instance.Update(crmCustomer);
                DbOpe_crm_customer_subcompany.Instance.Update(new List<Db_crm_customer_subcompany>() { mainCompany });
                DbOpe_crm_customer_subcompany.Instance.Update(updateSubCompanys);
                //联系人信息
                DbOpe_crm_customer_subcompany_contacts.Instance.SaveContacts(mainCompany.Id, updateCustomer_IN.ContactInfos);
                //采购主体关联提醒
                DbOpe_crm_customer_subcompany_related.Instance.SaveRelateds(mainCompany.Id, updateCustomer_IN.RelatedCompanies.MappingTo<List<SubCompanyRelated_IN>>());
                foreach (var updateSub in updateSubCompanys)
                {
                    //联系信息
                    DbOpe_crm_customer_subcompany_contacts.Instance.RefreshDefault(updateSub.Id);
                }
                if (hasSubAudit)
                {
                    //10.27 
                    DbOpe_crm_addsubcompany_audit.Instance.Insert(audit);
                    DbOpe_crm_addsubcompany_audit_subcompany.Instance.Insert(auditSubCompanys);
                    //11.23 删除资源池同名数据(未审核的子公司也先删了，拒绝的话再恢复)
                    CheckAndDeleteSourceCustomer(checkCompanys);
                    Util<DbOpe_crm_addsubcompany_attachfile, BM_AttachFiles> attachFiles = new(DbOpe_crm_addsubcompany_attachfile.Instance);
                    var uploadResult = attachFiles.UploadFiles(audit.Id, updateCustomer_IN.RelationshipFileInfo, AttachEnumOption.SubRelation);
                    if (!uploadResult)
                    {
                        throw new ApiException("文件上传异常!");
                    }
                    foreach (var au in auditSubCompanys)
                    {
                        //保存联系信息
                        DbOpe_crm_addsubcompany_audit_subcompany_contacts.Instance.RefreshDefault(au.CustomerSubCompanyId, auditId);
                    }
                }
                if (updateCustomer_IN.TrackingStage != oldCustomerInfo.TrackingStage)
                {
                    //同步客户跟踪记录
                    BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(crmCustomer.Id, mainCompany.Id, "", "", (EnumTrackingStage)crmCustomer.TrackingStage, 1, false, true, "", EnumCustomerDataSource.Private, true);
                }
                if (canEdit)
                {
                    //刷新临时池主公司相关的失效信息  子公司没审核呢 不刷
                    DbOpe_crm_customer_subcompany_temporary.Instance.RefreshVaildCompanys(new List<CheckCompanyUniqueInfo>() { mainCompany.MappingTo<CheckCompanyUniqueInfo>() }, EnumCompanyValid.INVALID);
                    //240811 作废重复的采购主体关联提醒
                    VoidCompanyRelated(new List<CheckCompanyUniqueInfo>() { mainCompany.MappingTo<CheckCompanyUniqueInfo>() });
                }

                if (hasSubAudit)
                {
                    //保存客户信息备份
                    DbOpe_crm_customer_copy.Instance.SaveCustomerCopy(EnumCustomerCopyAuditType.AddSub, mainCompany.CustomerId, auditId);
                    string dataState = Dictionary.CustomerAddSubCompanyAuditState.First(e => e.Value == audit.State.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.AddWorkFlow("子公司审批流程", audit.Id, audit, mainCompany, updateCustomer_IN.RelationshipDescription, dataState, "修改");
                }
            });

        }
        /// <summary>
        /// 根据查询条件获取私有池客户信息列表
        /// </summary>
        /// <param name="queryCustomer_IN"></param>
        /// <param name="total"></param>
        public List<QueryCustomer_OUT> SearchPrivatePoolCustomerList(QueryCustomer_IN queryCustomer_IN, ref int total)
        {
            var user = currentUser;
            var list = DbOpe_crm_customer_privatepool.Instance.SearchPrivatePoolCustomerList(queryCustomer_IN, user, ref total);

            var customerIds = list.Select(l => l.Id).ToList();
            var checkL = CheckAuditInProcess(customerIds, false);
            var getL = DbOpe_crm_customer_privatepool.Instance.QueryCustomerGetNum(customerIds);
            var releaseL = CheckCustomerLock(customerIds, false);
            var gtisC = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetGtisServiceCompanys();
            list.ForEach(l =>
            {
                l.AuditFlag = checkL.Contains(l.Id);
                l.LockFlag = releaseL.Contains(l.Id);
                l.GetTimes = !getL.ContainsKey(l.Id) ? 0 : getL[l.Id];
                l.SubCompanys.ForEach(c =>
                {
                    c.GtisFlag = gtisC.Contains(c.Id);
                });
                l.GtisFlag = l.SubCompanys.Find(c => c.GtisFlag) != null || gtisC.Contains(l.MainCompanyId);
            });
            return list;
        }
        /// <summary>
        /// 获取当前用户的剩余可保留客户数量信息。系统可保留用户数量减去当前用户的已保留用户数量（已经24.4.26到账不算）
        /// </summary>
        /// <returns></returns>
        public int GetRetainCustomersNumber()
        {
            var user = currentUser;
            return DbOpe_crm_customer_privatepool.Instance.GetMaxSavePrivateCustomerNumLeft(user);
        }
        /// <summary>
        /// 获取用户的可保留数量
        /// </summary>
        /// <returns></returns>
        public int GetAllUserRetainCustomersNumber(GetRetainCustomersNumber_IN getRetainCustomersNumber_IN)
        {
            return DbOpe_crm_customer_privatepool.Instance.GetMaxSavePrivateCustomerNumLeft(getRetainCustomersNumber_IN.UserId);
        }
        /// <summary>
        /// 将客户信息从私有池移入临时池
        /// </summary>
        /// <param name="customerIds"></param>
        public void MovePrivatePoolToTemporaryPool(List<string> customerIds)
        {
            /*
             * 审核中存不进去临时池的（后面添加）
             * 有过合同的是存不进去临时池的（后面添加）
             *  需要判断临时池是不是已经有这个客户（未删除未释放的）
                如果有要看参数里是否覆盖临时池信息
                
                移动到临时池相当于
                1、添加一个临时客户
                2、原客户释放到公有池
                3、临时池添加新临时客户的记录
                4、如果覆盖临时池记录，要将原临时池记录删除(临时客户和临时池记录一起删除)
                5、恢复相关公司在临时池的有效性

                记录客户归属日志（9.18添加）
             */
            var user = currentUser;
            DbOpe_crm_customer_privatepool.Instance.TransDeal(() =>
            {
                //检查客户是否锁定
                CheckCustomerLock(customerIds);
                //验证是不是已经在审核中
                CheckAuditInProcess(customerIds);
                ////验证客户信息是否需要补录
                //CheckCustomerInfoNeedSupplementary(customerIds);
                DbOpe_crm_customer_privatepool.Instance.MoveToTemp(customerIds, user, false);
                //记录客户归属日志（9.18添加）
                foreach (var customerId in customerIds)
                {
                    DbOpe_crm_customer_org_log.Instance.ReleaseCustomerLog(customerId);
                }
            });

        }
        /// <summary>
        /// 将客户信息从私有池释放到公有池
        /// </summary>
        /// <param name="releasePrivateCustomer_IN"></param>
        public void ReleasePrivatePoolToPublicPool(ReleasePrivateCustomer_IN releasePrivateCustomer_IN)
        {
            /*
             * 有过合同的是存不进去临时池的（后面添加）
             *  需要判断临时池是不是已经有这个客户（未删除未释放的）
                如果有要看参数里是否覆盖临时池信息
                
                移动到临时池相当于
                1、添加一个临时客户
                2、原客户释放到公有池
                3、临时池添加新临时客户的记录
                4、如果覆盖临时池记录，要将原临时池记录删除(临时客户和临时池记录一起删除)
                5、恢复相关公司在临时池的有效性
                记录客户归属日志（9.18添加）
             */
            var user = currentUser;
            DbOpe_crm_customer_privatepool.Instance.TransDeal(() =>
            {
                CheckCustomerLock(releasePrivateCustomer_IN.CustomerIds);
                //验证是不是已经在审核中
                CheckAuditInProcess(releasePrivateCustomer_IN.CustomerIds);
                ////验证客户信息是否需要补录 20240709 取消完善信息限制
                //CheckCustomerInfoNeedSupplementary(releasePrivateCustomer_IN.CustomerIds);
                DbOpe_crm_customer_privatepool.Instance.MoveToPublic(releasePrivateCustomer_IN.CustomerIds, releasePrivateCustomer_IN.ReleaseReason, user);
                //记录客户归属日志（9.18添加）
                foreach (var customerId in releasePrivateCustomer_IN.CustomerIds)
                {
                    DbOpe_crm_customer_org_log.Instance.ReleaseCustomerLog(customerId);
                }
            });

        }
        /// <summary>
        /// 对私有池的客户信息进行延期操作，延长保护截止日期。
        /// </summary>
        /// <param name="customerIds"></param>
        public void DelayPrivatePool(List<string> customerIds)
        {
            /*
             * 如果延期次数超过系统规定的延期次数（系统参数），则无法进行延期，否则可进行延期操作。
             * 延长客户保护截止日以T+60（公共参数延期时间）
             * 20240712 延期判断是否还有保留名额；负名额的不能再延期
             */
            var user = currentUser;
            DbOpe_crm_customer_privatepool.Instance.TransDeal(() =>
            {
                List<PrivateCustomerDelayInfo> delayInfos = new List<PrivateCustomerDelayInfo>();
                DbOpe_crm_customer_privatepool.Instance.CheckDelayEnable(customerIds, user, ref delayInfos);
                DbOpe_crm_customer_privatepool.Instance.ExcuteDelay(delayInfos, user, GetProtectDelayDay());
            });
        }
        /// <summary>
        /// 移交私有客户
        /// </summary>
        /// <param name="transferPrivateCustomer_IN"></param>
        public void TransferPrivatePool(TransferPrivateCustomer_IN transferPrivateCustomer_IN)
        {
            /*
             * 移交时合同怎么处理呢？（后补）
             * 2024.4.8 移交时合同也移交给接收人
             * 240620 验证是否在审核中
                 对私有池的客户信息进行移交操作，将选中的当前客户信息移交到接收人的私有池中。
                首先将当前用户的私有池客户做状态和释放时间、释放原因的更新操作，然后在对接收人的私有池客户做新增或更新操作。
                如果接收人从未领取过该客户则直接新增，否则要继承之前的领取时间、保护截止日等（也做新增记录）。
                验证包括接收人可保存客户数，满足则可以移交，否则无法移交。
                接收人曾经已经领取过该客户的，需要判断该客户是否还在保护截止日中，如果还在则需要计算保护截止日期，
                用上一次的领取时间和释放时间计算出上一次的占有时间，在通过当前时间和系统保护截止日参数以及占有时间，计算出这次的保护截止日。
                如果涉及到多次领取以及（唯一客户（客户名相同但数据库主键不同））的，就需要进行多次计算累加。
                如果已超出保护截止日，则进行一次延期，如果延期次数已用尽，则无法进行移交操作。

            记录客户归属日志（9.18添加）
             */
            var user = currentUser;
            var dt = DateTime.Now;
            if (user == transferPrivateCustomer_IN.RecipientUserId)
            {
                throw new ApiException("不能移交给自己");
            }
            DbOpe_crm_customer_transfer.Instance.TransDeal(() =>
            {
                //验证客户信息是否需要补录
                //CheckCustomerInfoNeedSupplementary(transferPrivateCustomer_IN.CustomerIds);
                //240620 验证是否在审核中
                CheckAuditInProcess(transferPrivateCustomer_IN.CustomerIds);
                List<PrivateCustomerSimpleInfo> privateCustomerInfos = DbOpe_crm_customer_privatepool.Instance.GetPrivateCustomerInfo(transferPrivateCustomer_IN.CustomerIds, user);
                var leftNum = DbOpe_crm_customer_privatepool.Instance.GetMaxSavePrivateCustomerNumLeft(transferPrivateCustomer_IN.RecipientUserId);
                foreach (PrivateCustomerSimpleInfo privateCustomerInfo in privateCustomerInfos)
                {
                    if (privateCustomerInfo.Deleted == (int)EnumCustomerDel.Del || privateCustomerInfo.State != (int)EnumCustomerPrivateRelease.Not)
                    {
                        throw new ApiException(privateCustomerInfo.CustomerName + "已被释放或删除，不能移交");
                    }
                }
                if (transferPrivateCustomer_IN.CustomerIds.Count > leftNum)
                {
                    throw new ApiException("接收人剩余可保存客户数不足");
                }
                #region  获取接受者的各级组织信息
                //获取用户信息
                var recipientUser = DbOpe_sys_user.Instance.GetDbSysUserById(transferPrivateCustomer_IN.RecipientUserId);
                //获取组织信息树，从当前组织追溯出所有上级组织
                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(recipientUser.OrganizationId);
                string? OrgDivisionId = null;
                string OrgDivisionName = "";
                string? OrgBrigadeId = null;
                string OrgBrigadeName = "";
                string? OrgRegimentId = null;
                string OrgRegimentName = "";
                foreach (var org in orgList)
                {
                    switch (org.OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            OrgDivisionId = org.Id;
                            OrgDivisionName = org.OrgName;
                            break;
                        case EnumOrgType.Battalion:
                            OrgBrigadeId = org.Id;
                            OrgBrigadeName = org.OrgName;
                            break;
                        case EnumOrgType.Squadron:
                            OrgRegimentId = org.Id;
                            OrgRegimentName = org.OrgName;
                            break;
                    }
                }
                #endregion

                #region 添加移交记录
                foreach (string customerId in transferPrivateCustomer_IN.CustomerIds)
                {
                    var transferId = Guid.NewGuid().ToString();
                    DbOpe_crm_customer_transfer.Instance.Insert(new Db_crm_customer_transfer()
                    {
                        Id = transferId,
                        UserId = user,
                        RecipientUserId = transferPrivateCustomer_IN.RecipientUserId,
                        CustomerId = customerId,
                        TransferDate = dt,
                        CreateDate = dt,
                        CreateUser = user
                    });


                    #region 处理合同和发票
                    var companys = DbOpe_crm_customer_subcompany.Instance.GetSubcompanyListByCustomerId(customerId, user).Select(c => c.Id).ToList();
                    //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限  所以这个客户要带着所有的合同/发票/到账一起移交
                    //var contracts = DbOpe_crm_contract.Instance.GetDataList(c => c.CreateUser == user && companys.Contains(c.FirstParty));
                    var contracts = DbOpe_crm_contract.Instance.GetDataList(c => companys.Contains(c.FirstParty));
                    var contractIds = contracts.Select(c => c.Id).Distinct().ToList();
                    var payments = DbOpe_crm_contract_paymentinfo.Instance.GetDataList(c => contractIds.Count > 0 && contractIds.Contains(c.ContractId)).ToList();
                    var cps = DbOpe_crm_contract_productinfo.Instance.GetDataList(c => contractIds.Count > 0 && contractIds.Contains(c.ContractId)).ToList();
                    foreach (var contract in contracts)
                    {
                        //contract.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        contract.Issuer = transferPrivateCustomer_IN.RecipientUserId;
                        contract.OrgDivisionId = OrgDivisionId;
                        contract.OrgDivisionName = OrgDivisionName;
                        contract.OrgBrigadeId = OrgBrigadeId;
                        contract.OrgBrigadeName = OrgBrigadeName;
                        contract.OrgRegimentId = OrgRegimentId;
                        contract.OrgRegimentName = OrgRegimentName;
                        contract.UpdateDate = dt;
                        contract.UpdateUser = user;
                        DbOpe_crm_contract.Instance.UpdateBase(contract);
                        DbOpe_crm_contract_transfer.Instance.Insert(new Db_crm_contract_transfer()
                        {
                            Id = Guid.NewGuid().ToString(),
                            TransferId = transferId,
                            ContractId = contract.Id
                        });
                    }
                    foreach (var payment in payments)
                    {
                        payment.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        payment.UpdateDate = dt;
                        payment.UpdateUser = user;
                        DbOpe_crm_contract_paymentinfo.Instance.Update(payment);
                    }
                    foreach (var cp in cps)
                    {
                        cp.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        cp.UpdateDate = dt;
                        cp.UpdateUser = user;
                        DbOpe_crm_contract_productinfo.Instance.UpdateBase(cp);
                    }
                    //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限  所以这个客户要带着所有的合同/发票/到账一起移交
                    //发票权限跟随合同权限（Issuer）
                    //var invoices = DbOpe_crm_contract_invoice.Instance.GetDataList(i => i.CreateUser == user && contracts.Select(c => c.Id).Contains(i.ContractId));
                    //var invoiceAppls = DbOpe_crm_contract_invoiceappl.Instance.GetDataList(i => i.CreateUser == user && contracts.Select(c => c.Id).Contains(i.ContractId));
                    var invoices = DbOpe_crm_contract_invoice.Instance.GetDataList(i => contracts.Select(c => c.Id).Contains(i.ContractId));
                    var invoiceAppls = DbOpe_crm_contract_invoiceappl.Instance.GetDataList(i => contracts.Select(c => c.Id).Contains(i.ContractId));
                    foreach (var invoice in invoices)
                    {
                        //invoice.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        invoice.UpdateDate = dt;
                        invoice.UpdateUser = user;
                        DbOpe_crm_contract_invoice.Instance.Update(invoice);
                        DbOpe_crm_contract_invoice_transfer.Instance.Insert(new Db_crm_contract_invoice_transfer()
                        {
                            Id = Guid.NewGuid().ToString(),
                            TransferId = transferId,
                            ContractInvoiceId = invoice.Id
                        });
                    }
                    foreach (var iAppl in invoiceAppls)
                    {
                        //iAppl.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        iAppl.UpdateDate = dt;
                        iAppl.UpdateUser = user;
                        DbOpe_crm_contract_invoiceappl.Instance.UpdateBase(iAppl);
                    }
                    #endregion
                    #region 原用户私有池状态更新
                    Db_crm_customer_privatepool orginUpdate = privateCustomerInfos.Find(c => c.CustomerId == customerId).MappingTo<Db_crm_customer_privatepool>();
                    orginUpdate.State = (int)EnumCustomerPrivateRelease.Release;
                    orginUpdate.ReleaseType = (int)EnumCustomerPrivateReleaseType.Manual;
                    orginUpdate.ReleaseReason = "移交";
                    orginUpdate.ReleaseTime = dt;
                    orginUpdate.UpdateDate = dt;
                    orginUpdate.UpdateUser = user;
                    DbOpe_crm_customer_privatepool.Instance.Update(orginUpdate);
                    #endregion
                    #region 新用户私有池添加
                    var poolList = DbOpe_crm_customer_privatepool.Instance.MoveToUserPrivate(new List<string>() { customerId }, user, transferPrivateCustomer_IN.RecipientUserId, FormatProtectDate(), GetProtectDelayDay(), EnumCustomerPrivateCreateType.Transfer);
                    #endregion
                    //记录客户归属日志（9.18添加）
                    DbOpe_crm_customer_org_log.Instance.TransferCustomerLog(new TransferCustomerLogParams()
                    {
                        CustomerId = customerId,
                        SourceUserId = user,
                        TargetUserId = transferPrivateCustomer_IN.RecipientUserId,
                        PrivatePoolId = poolList.Find(d => d.CustomerId == customerId).Id
                    });

                    //2024年9月3日 领取/分配客户增加一条新的跟踪记录 作为统计使用
                    //获取当前客户主公司信息
                    var mainCom = DbOpe_crm_customer_subcompany.Instance.GetCustomerMainCompany(customerId);
                    //同步客户跟踪记录
                    BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(customerId, mainCom.Id, "", "", EnumTrackingStage.CollectFromPool, 1, true, true, "", EnumCustomerDataSource.Private, true);
                }
                #endregion
            });

        }
        /// <summary>
        /// 移交私有客户（管理者）
        /// </summary>
        /// <param name="transferPrivateCustomer_IN"></param>
        public void TransferPrivatePoolAdmin(TransferPrivateCustomer_IN transferPrivateCustomer_IN)
        {
            /*
             * 移交时合同怎么处理呢？（后补）
             * 2024.4.8 移交时合同也移交给接收人
             * 240620 验证是否在审核中
                 对私有池的客户信息进行移交操作，将选中的当前客户信息移交到接收人的私有池中。
                首先将当前用户的私有池客户做状态和释放时间、释放原因的更新操作，然后在对接收人的私有池客户做新增或更新操作。
                如果接收人从未领取过该客户则直接新增，否则要继承之前的领取时间、保护截止日等（也做新增记录）。
                验证包括接收人可保存客户数，满足则可以移交，否则无法移交。
                接收人曾经已经领取过该客户的，需要判断该客户是否还在保护截止日中，如果还在则需要计算保护截止日期，
                用上一次的领取时间和释放时间计算出上一次的占有时间，在通过当前时间和系统保护截止日参数以及占有时间，计算出这次的保护截止日。
                如果涉及到多次领取以及（唯一客户（客户名相同但数据库主键不同））的，就需要进行多次计算累加。
                如果已超出保护截止日，则进行一次延期，如果延期次数已用尽，则无法进行移交操作。

            记录客户归属日志（9.18添加）
             */
            //var user = currentUser;
            var dt = DateTime.Now;
            DbOpe_crm_customer_transfer.Instance.TransDeal(() =>
            {
                //240723 拒绝所有待审核
                CheckAuditInProcess(transferPrivateCustomer_IN.CustomerIds);
                List<PrivateCustomerSimpleInfo> privateCustomerInfos = DbOpe_crm_customer_privatepool.Instance.GetPrivateCustomerInfo(transferPrivateCustomer_IN.CustomerIds);
                foreach (PrivateCustomerSimpleInfo privateCustomerInfo in privateCustomerInfos)
                {
                    if (privateCustomerInfo.Deleted == (int)EnumCustomerDel.Del || privateCustomerInfo.State != (int)EnumCustomerPrivateRelease.Not)
                    {
                        throw new ApiException(privateCustomerInfo.CustomerName + "已被释放或删除，不能移交");
                    }
                }

                #region  获取接受者的各级组织信息
                //获取用户信息
                var recipientUser = DbOpe_sys_user.Instance.GetDbSysUserById(transferPrivateCustomer_IN.RecipientUserId);
                //获取组织信息树，从当前组织追溯出所有上级组织
                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(recipientUser.OrganizationId);
                string? OrgDivisionId = null;
                string OrgDivisionName = "";
                string? OrgBrigadeId = null;
                string OrgBrigadeName = "";
                string? OrgRegimentId = null;
                string OrgRegimentName = "";
                foreach (var org in orgList)
                {
                    switch (org.OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            OrgDivisionId = org.Id;
                            OrgDivisionName = org.OrgName;
                            break;
                        case EnumOrgType.Battalion:
                            OrgBrigadeId = org.Id;
                            OrgBrigadeName = org.OrgName;
                            break;
                        case EnumOrgType.Squadron:
                            OrgRegimentId = org.Id;
                            OrgRegimentName = org.OrgName;
                            break;
                    }
                }
                #endregion
                #region 添加移交记录
                foreach (string customerId in transferPrivateCustomer_IN.CustomerIds)
                {
                    var privateP = privateCustomerInfos.Find(p => p.CustomerId == customerId);
                    if (privateP == null)
                    {
                        throw new ApiException(customerId + "未找到信息");
                    }
                    var transferId = Guid.NewGuid().ToString();
                    DbOpe_crm_customer_transfer.Instance.Insert(new Db_crm_customer_transfer()
                    {
                        Id = transferId,
                        UserId = privateP.UserId,
                        RecipientUserId = transferPrivateCustomer_IN.RecipientUserId,
                        CustomerId = customerId,
                        TransferDate = dt,
                        CreateDate = dt,
                        CreateUser = currentUser
                    });
                    #region 处理合同和发票
                    var companys = DbOpe_crm_customer_subcompany.Instance.GetSubcompanyListByCustomerId(customerId, privateP.UserId).Select(c => c.Id).ToList();
                    //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限  所以这个客户要带着所有的合同/发票/到账一起移交
                    //var contracts = DbOpe_crm_contract.Instance.GetDataList(c => c.CreateUser == privateP.UserId && companys.Contains(c.FirstParty));
                    var contracts = DbOpe_crm_contract.Instance.GetDataList(c => companys.Contains(c.FirstParty));
                    var contractIds = contracts.Select(c => c.Id).Distinct().ToList();
                    var payments = DbOpe_crm_contract_paymentinfo.Instance.GetDataList(c => contractIds.Count > 0 && contractIds.Contains(c.ContractId)).ToList();
                    var cps = DbOpe_crm_contract_productinfo.Instance.GetDataList(c => contractIds.Count > 0 && contractIds.Contains(c.ContractId)).ToList();
                    foreach (var contract in contracts)
                    {
                        //contract.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        contract.Issuer = transferPrivateCustomer_IN.RecipientUserId;
                        contract.OrgDivisionId = OrgDivisionId;
                        contract.OrgDivisionName = OrgDivisionName;
                        contract.OrgBrigadeId = OrgBrigadeId;
                        contract.OrgBrigadeName = OrgBrigadeName;
                        contract.OrgRegimentId = OrgRegimentId;
                        contract.OrgRegimentName = OrgRegimentName;
                        contract.UpdateDate = dt;
                        contract.UpdateUser = currentUser;
                        DbOpe_crm_contract.Instance.UpdateBase(contract);
                        DbOpe_crm_contract_transfer.Instance.Insert(new Db_crm_contract_transfer()
                        {
                            Id = Guid.NewGuid().ToString(),
                            TransferId = transferId,
                            ContractId = contract.Id
                        });
                    }
                    foreach (var payment in payments)
                    {
                        payment.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        payment.UpdateDate = dt;
                        payment.UpdateUser = currentUser;
                        DbOpe_crm_contract_paymentinfo.Instance.Update(payment);
                    }
                    foreach (var cp in cps)
                    {
                        cp.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        cp.UpdateDate = dt;
                        cp.UpdateUser = currentUser;
                        DbOpe_crm_contract_productinfo.Instance.UpdateBase(cp);
                    }
                    //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限  所以这个客户要带着所有的合同/发票/到账一起移交
                    //发票权限跟随合同权限（Issuer）
                    //var invoices = DbOpe_crm_contract_invoice.Instance.GetDataList(i => i.CreateUser == privateP.UserId && contracts.Select(c => c.Id).Contains(i.ContractId));
                    //var invoiceAppls = DbOpe_crm_contract_invoiceappl.Instance.GetDataList(i => i.CreateUser == privateP.UserId && contracts.Select(c => c.Id).Contains(i.ContractId));
                    var invoices = DbOpe_crm_contract_invoice.Instance.GetDataList(i => contracts.Select(c => c.Id).Contains(i.ContractId));
                    var invoiceAppls = DbOpe_crm_contract_invoiceappl.Instance.GetDataList(i => contracts.Select(c => c.Id).Contains(i.ContractId));
                    foreach (var invoice in invoices)
                    {
                        //invoice.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        invoice.UpdateDate = dt;
                        invoice.UpdateUser = currentUser;
                        DbOpe_crm_contract_invoice.Instance.Update(invoice);
                        DbOpe_crm_contract_invoice_transfer.Instance.Insert(new Db_crm_contract_invoice_transfer()
                        {
                            Id = Guid.NewGuid().ToString(),
                            TransferId = transferId,
                            ContractInvoiceId = invoice.Id
                        });
                    }
                    foreach (var iAppl in invoiceAppls)
                    {
                        //iAppl.CreateUser = transferPrivateCustomer_IN.RecipientUserId;
                        iAppl.UpdateDate = dt;
                        iAppl.UpdateUser = currentUser;
                        DbOpe_crm_contract_invoiceappl.Instance.UpdateBase(iAppl);
                    }
                    #endregion
                    #region 原用户私有池状态更新
                    Db_crm_customer_privatepool orginUpdate = privateCustomerInfos.Find(c => c.CustomerId == customerId).MappingTo<Db_crm_customer_privatepool>();
                    orginUpdate.State = (int)EnumCustomerPrivateRelease.Release;
                    orginUpdate.ReleaseType = (int)EnumCustomerPrivateReleaseType.Manual;
                    orginUpdate.ReleaseReason = "移交";
                    orginUpdate.ReleaseTime = dt;
                    orginUpdate.UpdateDate = dt;
                    orginUpdate.UpdateUser = currentUser;
                    DbOpe_crm_customer_privatepool.Instance.Update(orginUpdate);
                    #endregion
                    #region 新用户私有池添加
                    //如果不是签约客户，保护期按领取重新计算
                    var signCustomer = false;
                    DateTime productDeadLine = FormatProtectDate();
                    //如果是签约客户且未过期，保护期不变
                    if (privateP.TrackingStage == (int)EnumTrackingStage.Received && privateP.ProtectionDeadline != null && privateP.ProtectionDeadline.Value.GetDaysStart() > dt)
                    {
                        productDeadLine = privateP.ProtectionDeadline.Value;
                        signCustomer = true;
                    }
                    var poolList = DbOpe_crm_customer_privatepool.Instance.MoveToUserPrivate(new List<string>() { customerId }, currentUser, transferPrivateCustomer_IN.RecipientUserId, productDeadLine, GetProtectDelayDay(), EnumCustomerPrivateCreateType.Transfer, signCustomer);
                    #endregion
                    //记录客户归属日志（9.18添加）
                    DbOpe_crm_customer_org_log.Instance.TransferCustomerLog(new TransferCustomerLogParams()
                    {
                        CustomerId = customerId,
                        SourceUserId = privateP.UserId,
                        TargetUserId = transferPrivateCustomer_IN.RecipientUserId,
                        PrivatePoolId = poolList.Find(d => d.CustomerId == customerId).Id
                    });
                }
                #endregion
            });

        }



        /// <summary>
        /// 合并私有客户
        /// </summary>
        /// <param name="mergePrivateCustomer_IN"></param>
        /// <param name="isHaveFlow"></param> 
        /// <param name="userId"></param> 
        public string MergePrivatePoolApply(MergePrivateCustomer_IN mergePrivateCustomer_IN, bool isHaveFlow = true, string userId = "00000000-0000-0000-0000-000000000000")
        {
            /*
             对私有池的客户信息进行合并申请。添加记录到Crm_MergeCompany_Audit合并公司审核表，并添加工作流数据，生成待办信息。
             可选择多个客户一起合并，不限制选择客户数量
             审核中的添加的合并公司不能签合同。已经参与合并公司审核的是不能再做合并公司申请的，必须等到上一个流程结束。
             
             下面这些其实是合并审核通过后做的：
             员工可对私有池客户进行合并为一个客户，合并后多个慧思ID号会延用主公司的慧思ID，被合并公司慧思ID隐藏，不显示该字段，
             其他企业信息照常显示，以及被合并公司所签约的销售合同、发票、期刊、审核记录等全部融合到主公司下，可一并进行查询
            (这里有个问题就是合并的customerID其实是不变的，那么其他模块如果还按照customerid去查询那对应的记录就不对了  怎么处理？ 后补)
            */
            if (isHaveFlow)
            {
                if (mergePrivateCustomer_IN.RelationshipDescription == null)
                {
                    throw new ApiException("关系说明不可为空");
                }
                if (mergePrivateCustomer_IN.RelationshipFileInfo == null)
                {
                    throw new ApiException("关系说明文件不可为空");
                }
            }

            var user = userId;//currentUser;
            var dt = DateTime.Now;
            var auditId = Guid.NewGuid().ToString();
            //主公司必须在客户列表里
            if (StringUtil.IsNullOrEmpty(mergePrivateCustomer_IN.MainCustomerId))
            {
                throw new ApiException("未设置主公司");
            }
            if (mergePrivateCustomer_IN.CustomerIds.IndexOf(mergePrivateCustomer_IN.MainCustomerId) < 0)
            {
                throw new ApiException("设置的主公司不在要合并的客户中");
            }
            //DbOpe_crm_mergecompany_audit.Instance.TransDeal(() =>
            //{
            //验证是不是都是私有池的有效客户
            List<PrivateCustomerSimpleInfo> customerInfoList = new List<PrivateCustomerSimpleInfo>();
            DbOpe_crm_customer_privatepool.Instance.CheckUserPrivateCustomerValid(mergePrivateCustomer_IN.CustomerIds, user, ref customerInfoList);
            //验证是不是已经在审核中
            CheckAuditInProcess(mergePrivateCustomer_IN.CustomerIds);
            //验证客户信息是否需要补录
            CheckCustomerInfoNeedSupplementary(mergePrivateCustomer_IN.CustomerIds);
            //添加合并审核信息
            //var auditId = Guid.NewGuid().ToString();
            var audit = new Db_crm_mergecompany_audit()
            {
                Id = auditId,
                CustomerId = mergePrivateCustomer_IN.MainCustomerId,
                RelationshipDescription = mergePrivateCustomer_IN.RelationshipDescription,
                ApplicantId = user,
                ApplicantDate = dt,
                CreateDate = dt,
                CreateUser = user
            };
            DbOpe_crm_mergecompany_audit.Instance.Insert(audit);
            if (isHaveFlow)
            {
                Util<DbOpe_crm_mergecompany_attachfile, BM_AttachFiles> attachFiles = new Util<DbOpe_crm_mergecompany_attachfile, BM_AttachFiles>(DbOpe_crm_mergecompany_attachfile.Instance);
                var uploadResult = attachFiles.UploadFiles(audit.Id, mergePrivateCustomer_IN.RelationshipFileInfo, AttachEnumOption.MergeRelation);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
            }
            foreach (string customerId in mergePrivateCustomer_IN.CustomerIds)
            {
                //被合并的客户才加关系记录，合并的目标客户不用加
                if (customerId != mergePrivateCustomer_IN.MainCustomerId)
                {
                    var auditCompany = new Db_crm_mergecompany_relationship()
                    {
                        Id = Guid.NewGuid().ToString(),
                        MergeCompanyAuditId = auditId,
                        MainCustomerId = mergePrivateCustomer_IN.MainCustomerId,
                        SubCustomerMainCompanyId = customerInfoList.Find(c => c.CustomerId == customerId).MainCompanyId,
                        SubCustomerId = customerId,
                        IsValid = (int)EnumCustomerMergeRelationValid.INVALID
                    };
                    DbOpe_crm_mergecompany_relationship.Instance.Insert(auditCompany);
                }
                //保存客户信息备份
                DbOpe_crm_customer_copy.Instance.SaveCustomerCopy(EnumCustomerCopyAuditType.Merge, customerId, auditId);
            }
            if (isHaveFlow)
            {
                string dataState = Dictionary.CustomerMergeAuditState.First(e => e.Value == audit.State.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow("合并客户审批流程", auditId, audit, customerInfoList.Find(c => c.CustomerId == mergePrivateCustomer_IN.MainCustomerId), mergePrivateCustomer_IN.RelationshipDescription, dataState, "新建");
            }

            //});
            return auditId;
        }
        /// <summary>
        /// 添加子公司申请
        /// </summary>
        /// <param name="addSubCompany_IN"></param>
        public void AddSubCompanyPrivatePoolApply(AddSubCompanyApply_IN addSubCompany_IN)
        {
            /*
             对私有池的客户信息进行添加子公司申请。添加记录到Crm_AddSubCompany_Audit 添加子公司审核表，并添加工作流数据，生成待办信息。
             审核中的添加的子公司不能签合同。客户再添加子公司审核过程中是不能再做添加子公司申请的，必须等到上一个流程结束。
            */
            var user = currentUser;
            var dt = DateTime.Now;

            //验证主公司有效性
            List<PrivateCustomerSimpleInfo> customerInfoList = new List<PrivateCustomerSimpleInfo>();
            DbOpe_crm_customer_privatepool.Instance.CheckUserPrivateCustomerValid(new List<string>() { addSubCompany_IN.CustomerId }, user, ref customerInfoList);
            PrivateCustomerSimpleInfo mainCompany = customerInfoList[0];
            //验证子公司是不是已经存在
            var checkCompanys = addSubCompany_IN.SubCompanys.ToList().MappingTo<List<CheckCompanyUniqueInfo>>();
            DbOpe_crm_customer.Instance.CheckDuplicateCustomer(checkCompanys);
            DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(checkCompanys, user);
            #region 验证名称/代码合法性
            CheckCreditCode(checkCompanys);
            #endregion
            //验证是不是已经在审核中
            CheckAuditInProcess(checkCompanys);
            //验证客户信息是否需要补录
            CheckCustomerInfoNeedSupplementary(new List<string>() { addSubCompany_IN.CustomerId });

            DbOpe_crm_addsubcompany_audit.Instance.TransDeal(() =>
            {
                //添加审核信息
                var auditId = Guid.NewGuid().ToString();
                var audit = new Db_crm_addsubcompany_audit()
                {
                    Id = auditId,
                    CustomerId = mainCompany.CustomerId,
                    ApplicantId = user,
                    State = (int)EnumCustomerAddSubCompanyAuditState.InProcess,
                    ApplicantDate = dt,
                    CreateDate = dt,
                    CreateUser = user,
                    RelationshipDescription = addSubCompany_IN.RelationshipDescription
                };
                DbOpe_crm_addsubcompany_audit.Instance.Insert(audit);
                Util<DbOpe_crm_addsubcompany_attachfile, BM_AttachFiles> attachFiles = new(DbOpe_crm_addsubcompany_attachfile.Instance);
                var uploadResult = attachFiles.UploadFiles(audit.Id, addSubCompany_IN.RelationshipFileInfo, AttachEnumOption.SubRelation);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
                //保存客户信息备份
                DbOpe_crm_customer_copy.Instance.SaveCustomerCopy(EnumCustomerCopyAuditType.AddSub, mainCompany.CustomerId, auditId);

                List<Db_crm_addsubcompany_audit_subcompany> auditCompanys = new List<Db_crm_addsubcompany_audit_subcompany>();
                addSubCompany_IN.SubCompanys.ForEach(auditCompany_IN =>
                {
                    Db_crm_addsubcompany_audit_subcompany auditCompany = auditCompany_IN.MappingTo<Db_crm_addsubcompany_audit_subcompany>();
                    var companyId = Guid.NewGuid().ToString();
                    auditCompany.Id = companyId;
                    auditCompany.CustomerSubCompanyId = companyId;
                    auditCompany.AddSubCompanyAuditId = auditId;
                    auditCompanys.Add(auditCompany);
                    //行业产品
                    DbOpe_crm_addsubcompany_audit_subcompany_mainbusiness.Instance.SaveBusiness(companyId, auditCompany_IN.CustomerIndustry, auditCompany_IN.MainProducts);
                    //联系信息
                    DbOpe_crm_addsubcompany_audit_subcompany_contacts.Instance.SaveContacts(companyId, auditId, auditCompany_IN.ContactInfos, false);
                });
                DbOpe_crm_addsubcompany_audit_subcompany.Instance.Insert(auditCompanys);
                foreach (var au in auditCompanys)
                {
                    DbOpe_crm_addsubcompany_audit_subcompany_contacts.Instance.RefreshDefault(au.CustomerSubCompanyId, auditId);
                }
                string dataState = Dictionary.CustomerAddSubCompanyAuditState.First(e => e.Value == audit.State.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow("子公司审批流程", auditId, audit, mainCompany, addSubCompany_IN.RelationshipDescription, dataState, "新建");
            });
        }
        /// <summary>
        /// 根据id获取客户基本信息
        /// </summary>
        /// <param name="customerId"></param>
        /// <param name="temp"></param>
        /// <returns></returns>
        public CustomerInfo_OUT GetCustomerById(string customerId, bool temp = false)
        {
            return DbOpe_crm_customer.Instance.GetCustomerOutInfoById(customerId, currentUser, temp);
        }
        /// <summary>
        /// 获取客户的领取历史记录（只有在公有池的可以看）
        /// </summary>
        /// <param name="customerId"></param>
        /// <returns></returns>
        public List<QueryCustomerGetHistory_OUT> QueryCustomerGetHistory(string customerId)
        {
            return DbOpe_crm_customer_privatepool.Instance.QueryCustomerGetHistory(customerId, currentUser);
        }

        /// <summary>
        /// 根据私有池客户Id获取公司名称列表
        /// </summary>
        /// <param name="customerId"></param>
        /// <returns></returns>
        public List<GetSubcompanyList> GetSubcompanyListByCustomerId(string customerId)
        {
            return DbOpe_crm_customer_subcompany.Instance.GetSubcompanyListByCustomerId(customerId, currentUser);
        }
        /// <summary>
        /// 根据私有池公司Id获取公司名称列表
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public List<GetSubcompanyList> GetSubcompanyListByCompanyId(string companyId)
        {
            return DbOpe_crm_customer_subcompany.Instance.GetSubcompanyListByCompanyId(companyId, currentUser);
        }
        /// <summary>
        /// 根据客户Id获取相关记录（私有池/公有池）
        /// </summary>
        /// <param name="customerRecord_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<CustomerRecord_OUT> GetRecordsByCustomerId(CustomerRecord_IN customerRecord_IN, ref int total)
        {
            return DbOpe_crm_customer_privatepool.Instance.GetRecordsByCustomerId(customerRecord_IN, currentUser, ref total);
        }
        /// <summary>
        /// 获取私有池客户数量及昨日变化
        /// </summary>
        /// <returns></returns>
        public GetUserCustomerAbstract_OUT GetUserCustomerAbstrct()
        {
            return DbOpe_crm_customer_privatepool.Instance.GetUserCustomerAbstrct(currentUser);
        }

        /// <summary>
        /// 根据客户名称获取用户私有池客户信息
        /// </summary>
        public List<PrivateCustomerInfo> GetPrivateCustomerInfo(string customerName)
        {
            return DbOpe_crm_customer_privatepool.Instance.GetPrivateCustomerInfo(customerName, UserId);
        }
        #endregion

        #region 临时池 20240511 临时客户  取消必填项目 
        /// <summary>
        /// 添加临时池客户
        /// </summary>
        /// <param name="addTempCustomerEmpty_IN"></param>
        public void AddTemporaryPoolCustomerEmpty(AddTempCustomerEmpty_IN addTempCustomerEmpty_IN)
        {
            /*
             * 新加临时池取消大部分的必填项目，只有名称和类型必填  
             * 新加临时池时 只验证个人临时池重复性（名称+代码）  
             * 后续临时池如果移动到私有池时，需要进行名称+代码的规范，并且将记录合并到规范后对应的正式客户里，如果这时客户已经被别人保留，则不能私有
             * 临时池客户分两种：1、新建的 这种可以随意填写 最后移动到私有池时需要绑定到正式客户里 2、私有池移入的 不能修改名字和代码（与正式客户有绑定关系）
             */
            var user = currentUser;
            var nowDate = DateTime.Now;


            #region 客户
            Db_crm_customer_temporary crmTempCustomer = addTempCustomerEmpty_IN.MappingTo<Db_crm_customer_temporary>();
            crmTempCustomer.Id = Guid.NewGuid().ToString();
            crmTempCustomer.CreateUser = user;
            crmTempCustomer.CreateDate = nowDate;
            crmTempCustomer.IsValid = (int)EnumCustomerValid.VALID;
            //临时池新建的客户，对应的正式客户id填写00000000-0000-0000-0000-000000000000
            crmTempCustomer.CustomerId = Guid.Empty.ToString();
            //crmTempCustomer.CustomerNum = customerUniqueInfo.CustomerNum;
            crmTempCustomer.CustomerOrderNum = DbOpe_crm_customer_temporarypool.Instance.FormatCustomerOrderNum(user);
            #endregion

            Db_crm_customer_subcompany_temporary tempMainCompany = addTempCustomerEmpty_IN.MappingTo<Db_crm_customer_subcompany_temporary>();

            #region 获取需要排重的所有公司名称/代码
            var checkCompanys = new List<CheckCompanyUniqueInfo>();
            checkCompanys.Add(tempMainCompany.MappingTo<CheckCompanyUniqueInfo>());
            #endregion


            //#region 验证名称/代码合法性
            //CheckCreditCode(checkCompanys);
            //#endregion
            //#region 公有/私有池排重
            //DbOpe_crm_customer.Instance.CheckDuplicateCustomer(checkCompanys);
            //#endregion
            //#region 验证是不是已经在审核中
            //CheckAuditInProcess(checkCompanys);
            //#endregion

            #region 个人临时池排重
            DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(checkCompanys, user);
            #endregion
            DbOpe_crm_customer_temporary.Instance.TransDeal(() =>
            {
                //#region 如果不存在，保存一份正式客户信息
                //if (!exist)
                //{
                //    //添加一个正式客户记录，这个客户私有/公有池子都不在,标识为无效状态
                //    var customerAdd = crmTempCustomer.MappingTo<Db_crm_customer>();
                //    customerAdd.Id = customerUniqueInfo.CustomerId;
                //    customerAdd.IsValid = (int)EnumCustomerValid.INVALID;
                //    customerAdd.IsSupplementary = (int)EnumNeedSupplement.NoNeed;
                //    DbOpe_crm_customer.Instance.Insert(customerAdd);
                //    var companyAdd = tempMainCompany.MappingTo<Db_crm_customer_subcompany>();
                //    companyAdd.Id = customerUniqueInfo.MainCompanyId;
                //    companyAdd.CustomerId = customerUniqueInfo.CustomerId;
                //    companyAdd.IsValid = (int)EnumCompanyValid.INVALID;
                //    companyAdd.CompanyNameDM = new ChineseAnalyzerCls().ReplaceKeyword(companyAdd.CompanyName);
                //    DbOpe_crm_customer_subcompany.Instance.Insert(companyAdd);
                //}
                //#endregion

                #region 主公司
                //验证公司名称/代码的有效性（后补）
                tempMainCompany.Id = Guid.NewGuid().ToString();
                //临时池新建的公司，对应的正式公司id填写00000000-0000-0000-0000-000000000000
                tempMainCompany.SubCompanyId = Guid.Empty.ToString();
                tempMainCompany.CreateUser = user;
                tempMainCompany.CreateDate = nowDate;
                tempMainCompany.IsValid = (int)EnumCompanyValid.VALID;
                tempMainCompany.CustomerTemporaryId = crmTempCustomer.Id;
                tempMainCompany.IsMain = (int)EnumCustomerCompanyMain.Main;
                //行业与产品
                DbOpe_crm_customer_subcompany_mainbusiness_temporary.Instance.SaveBusiness(tempMainCompany.Id, addTempCustomerEmpty_IN.CustomerIndustry, addTempCustomerEmpty_IN.MainProducts);

                #endregion

                #region 个人临时池
                Db_crm_customer_temporarypool crmCustomer_pp = new Db_crm_customer_temporarypool()
                {
                    Id = Guid.NewGuid().ToString(),
                    CustomerId = crmTempCustomer.CustomerId,
                    CustomerTemporaryId = crmTempCustomer.Id,
                    UserId = user,
                    //CollectionTime = nowDate,
                    State = (int)EnumCustomerTempRemove.Not,
                    CreateType = (int)EnumCustomerTempCreateType.Create,
                    ReleaseType = (int)EnumCustomerPrivateReleaseType.Not,
                    Deleted = (int)EnumCustomerDel.NotDel,
                    CreateUser = user,
                    CreateDate = DateTime.Now
                };
                #endregion

                //添加
                DbOpe_crm_customer_temporary.Instance.Insert(crmTempCustomer);
                DbOpe_crm_customer_subcompany_temporary.Instance.Insert(tempMainCompany);
                DbOpe_crm_customer_temporarypool.Instance.Insert(crmCustomer_pp);
                //联系信息
                DbOpe_crm_customer_subcompany_contacts_temporary.Instance.SaveContacts(tempMainCompany.SubCompanyId, user, addTempCustomerEmpty_IN.ContactInfos);
                //采购主体关联提醒
                DbOpe_crm_customer_subcompany_related_temporary.Instance.SaveRelateds(tempMainCompany.Id, addTempCustomerEmpty_IN.RelatedCompanies);
                //同步客户跟踪记录
                BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(crmTempCustomer.Id, tempMainCompany.Id, "", "", (EnumTrackingStage)crmTempCustomer.TrackingStage, 1, true, false, "", EnumCustomerDataSource.Temporary, true);
            });

        }
        /// <summary>
        /// 维护临时池客户信息
        /// </summary>
        /// <param name="updateTempCustomerEmpty_IN"></param>
        public void UpdateTemporaryPoolCustomerEmpty(UpdateTempCustomerEmpty_IN updateTempCustomerEmpty_IN)
        {
            /*
             临时池客户分两种：
                1、新建的 这种可以随意填写 最后移动到私有池时需要绑定到正式客户里 
                2、私有池移入的 不能修改名字和代码和类型（与正式客户有绑定关系）
             */
            var user = currentUser;
            var nowDate = DateTime.Now;
            #region 原有客户信息
            TempCustomerInfo customerInfo = DbOpe_crm_customer_temporary.Instance.GetTempCustomerInfoById(updateTempCustomerEmpty_IN.TemporaryCustomerId);
            TempCustomerInfo oldCustomerInfo = customerInfo.CloneNewtonJson();
            #endregion
            bool canEdit = oldCustomerInfo.CustomerId == Guid.Empty.ToString();
            if (!canEdit)
            {
                //私有池移入的 不能修改名字和代码和类型
                updateTempCustomerEmpty_IN.CompanyName = oldCustomerInfo.mainCompany.CompanyName;
                updateTempCustomerEmpty_IN.CreditCode = oldCustomerInfo.mainCompany.CreditCode;
                updateTempCustomerEmpty_IN.CreditType = oldCustomerInfo.mainCompany.CreditType;
            }
            DbOpe_crm_customer_subcompany_temporary.Instance.TransDeal(() =>
            {
                #region 更新客户信息
                updateTempCustomerEmpty_IN.MappingTo(customerInfo);
                Db_crm_customer_temporary crmCustomer = customerInfo.MappingTo<Db_crm_customer_temporary>();
                DbOpe_crm_customer_temporary.Instance.Update(crmCustomer);
                #endregion

                #region 更新主公司信息
                updateTempCustomerEmpty_IN.MappingTo(customerInfo.mainCompany);
                Db_crm_customer_subcompany_temporary mainCompany = customerInfo.mainCompany.MappingTo<Db_crm_customer_subcompany_temporary>();
                //行业与产品
                DbOpe_crm_customer_subcompany_mainbusiness_temporary.Instance.SaveBusiness(mainCompany.Id, updateTempCustomerEmpty_IN.CustomerIndustry, updateTempCustomerEmpty_IN.MainProducts);

                #endregion

                #region 如果名称代码修改需要进行临时池排重
                if (canEdit && mainCompany.CompanyName != oldCustomerInfo.mainCompany.CompanyName)
                {
                    var ccUniqueInfo = new List<CheckCompanyUniqueInfo>() {new CheckCompanyUniqueInfo()
                    {
                        CompanyName = mainCompany.CompanyName,
                        CreditCode = mainCompany.CreditCode
                    } };
                    DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(ccUniqueInfo, user);
                }
                #endregion
                //更新
                DbOpe_crm_customer_temporary.Instance.Update(crmCustomer);
                DbOpe_crm_customer_subcompany_temporary.Instance.Update(new List<Db_crm_customer_subcompany_temporary>() { mainCompany });
                //联系信息
                DbOpe_crm_customer_subcompany_contacts_temporary.Instance.SaveContacts(mainCompany.SubCompanyId, user, updateTempCustomerEmpty_IN.ContactInfos);
                //采购主体关联提醒
                DbOpe_crm_customer_subcompany_related_temporary.Instance.SaveRelateds(mainCompany.Id, updateTempCustomerEmpty_IN.RelatedCompanies);
                if (updateTempCustomerEmpty_IN.TrackingStage != oldCustomerInfo.TrackingStage)
                {
                    //同步客户跟踪记录
                    BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(crmCustomer.Id, mainCompany.Id, "", "", (EnumTrackingStage)crmCustomer.TrackingStage, 1, false, false, "", EnumCustomerDataSource.Temporary, true);
                }
            });

        }

        #endregion

        #region 临时池
        /// <summary>
        /// 添加临时池客户
        /// </summary>
        /// <param name="addTempCustomer_IN"></param>
        public void AddTemporaryPoolCustomer(AddTempCustomer_IN addTempCustomer_IN)
        {
            /*
             * 排重：验证客户信息是否重复，已存在则无法保存，返回相应提示信息(到私有池和公有池和个人临时池查询重复）
             * 创建临时客户的同时,要在正式客户库里同时创建一个正式客户（但这个客户暂时还不在私有公有池里，只是做个正式客户唯一信息的备份，因为同名的临时客户都要和同一个正式客户做绑定）
             */
            var user = currentUser;
            var nowDate = DateTime.Now;


            #region 获取唯一信息
            bool exist = false;
            CustomerUniqueInfo customerUniqueInfo = GetUniqueCustomerInfo(addTempCustomer_IN.MappingTo<CheckCompanyUniqueInfo>(), ref exist);
            #endregion

            #region 客户
            Db_crm_customer_temporary crmTempCustomer = addTempCustomer_IN.MappingTo<Db_crm_customer_temporary>();
            crmTempCustomer.Id = Guid.NewGuid().ToString();
            crmTempCustomer.CreateUser = user;
            crmTempCustomer.CreateDate = nowDate;
            crmTempCustomer.IsValid = (int)EnumCustomerValid.VALID;
            crmTempCustomer.CustomerId = customerUniqueInfo.CustomerId;
            crmTempCustomer.CustomerNum = customerUniqueInfo.CustomerNum;
            crmTempCustomer.CustomerOrderNum = DbOpe_crm_customer_temporarypool.Instance.FormatCustomerOrderNum(user);
            #endregion

            Db_crm_customer_subcompany_temporary tempMainCompany = addTempCustomer_IN.MappingTo<Db_crm_customer_subcompany_temporary>();

            #region 获取需要排重的所有公司名称/代码
            var checkCompanys = new List<CheckCompanyUniqueInfo>();
            checkCompanys.Add(tempMainCompany.MappingTo<CheckCompanyUniqueInfo>());
            #endregion


            #region 验证名称/代码合法性
            CheckCreditCode(checkCompanys);
            #endregion
            #region 公有/私有池排重
            DbOpe_crm_customer.Instance.CheckDuplicateCustomer(checkCompanys);
            #endregion

            #region 验证是不是已经在审核中
            CheckAuditInProcess(checkCompanys);
            #endregion

            #region 个人临时池排重
            DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(checkCompanys, user);
            #endregion
            DbOpe_crm_customer_temporary.Instance.TransDeal(() =>
            {
                #region 如果不存在，保存一份正式客户信息
                if (!exist)
                {
                    //添加一个正式客户记录，这个客户私有/公有池子都不在,标识为无效状态
                    var customerAdd = crmTempCustomer.MappingTo<Db_crm_customer>();
                    customerAdd.Id = customerUniqueInfo.CustomerId;
                    customerAdd.IsValid = (int)EnumCustomerValid.INVALID;
                    customerAdd.IsSupplementary = (int)EnumNeedSupplement.NoNeed;
                    DbOpe_crm_customer.Instance.Insert(customerAdd);
                    var companyAdd = tempMainCompany.MappingTo<Db_crm_customer_subcompany>();
                    companyAdd.Id = customerUniqueInfo.MainCompanyId;
                    companyAdd.CustomerId = customerUniqueInfo.CustomerId;
                    companyAdd.IsValid = (int)EnumCompanyValid.INVALID;
                    companyAdd.CompanyNameDM = new ChineseAnalyzerCls().ReplaceKeyword(companyAdd.CompanyName);
                    DbOpe_crm_customer_subcompany.Instance.Insert(companyAdd);
                }
                #endregion

                #region 主公司
                //验证公司名称/代码的有效性（后补）
                tempMainCompany.Id = Guid.NewGuid().ToString();
                tempMainCompany.SubCompanyId = customerUniqueInfo.MainCompanyId;
                tempMainCompany.CreateUser = user;
                tempMainCompany.CreateDate = nowDate;
                tempMainCompany.IsValid = (int)EnumCompanyValid.VALID;
                tempMainCompany.CustomerTemporaryId = crmTempCustomer.Id;
                tempMainCompany.IsMain = (int)EnumCustomerCompanyMain.Main;
                //行业与产品
                DbOpe_crm_customer_subcompany_mainbusiness_temporary.Instance.SaveBusiness(tempMainCompany.Id, addTempCustomer_IN.CustomerIndustry, addTempCustomer_IN.MainProducts);

                #endregion

                #region 个人临时池
                Db_crm_customer_temporarypool crmCustomer_pp = new Db_crm_customer_temporarypool()
                {
                    Id = Guid.NewGuid().ToString(),
                    CustomerId = crmTempCustomer.CustomerId,
                    CustomerTemporaryId = crmTempCustomer.Id,
                    UserId = user,
                    //CollectionTime = nowDate,
                    State = (int)EnumCustomerTempRemove.Not,
                    CreateType = (int)EnumCustomerTempCreateType.Create,
                    ReleaseType = (int)EnumCustomerPrivateReleaseType.Not,
                    Deleted = (int)EnumCustomerDel.NotDel,
                    CreateUser = user,
                    CreateDate = DateTime.Now
                };
                #endregion

                //添加
                DbOpe_crm_customer_temporary.Instance.Insert(crmTempCustomer);
                DbOpe_crm_customer_subcompany_temporary.Instance.Insert(tempMainCompany);
                DbOpe_crm_customer_temporarypool.Instance.Insert(crmCustomer_pp);
                //联系信息
                DbOpe_crm_customer_subcompany_contacts_temporary.Instance.SaveContacts(tempMainCompany.SubCompanyId, user, addTempCustomer_IN.ContactInfos);
                //采购主体关联提醒
                DbOpe_crm_customer_subcompany_related_temporary.Instance.SaveRelateds(tempMainCompany.Id, addTempCustomer_IN.RelatedCompanies);
                //同步客户跟踪记录
                BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(crmTempCustomer.Id, tempMainCompany.Id, "", "", (EnumTrackingStage)crmTempCustomer.TrackingStage, 1, true, false, "", EnumCustomerDataSource.Temporary, true);
            });

        }

        /// <summary>
        /// 维护临时池客户信息（10.26 代码不可以更改，914的作废  9.14修改：代码可以改，名称不允许修改）
        /// </summary>
        /// <param name="updateTempCustomer_IN"></param>
        public void UpdateTemporaryPoolCustomer(UpdateTempCustomer_IN updateTempCustomer_IN)
        {
            var user = currentUser;
            var nowDate = DateTime.Now;

            DbOpe_crm_customer_subcompany_temporary.Instance.TransDeal(() =>
            {
                #region 原有客户信息
                TempCustomerInfo customerInfo = DbOpe_crm_customer_temporary.Instance.GetTempCustomerInfoById(updateTempCustomer_IN.TemporaryCustomerId);
                TempCustomerInfo oldCustomerInfo = customerInfo.CloneNewtonJson();
                #endregion

                #region 更新客户信息
                updateTempCustomer_IN.MappingTo(customerInfo);
                Db_crm_customer_temporary crmCustomer = customerInfo.MappingTo<Db_crm_customer_temporary>();
                DbOpe_crm_customer_temporary.Instance.Update(crmCustomer);
                #endregion

                #region 更新主公司信息
                updateTempCustomer_IN.MappingTo(customerInfo.mainCompany);
                Db_crm_customer_subcompany_temporary mainCompany = customerInfo.mainCompany.MappingTo<Db_crm_customer_subcompany_temporary>();
                //行业与产品
                DbOpe_crm_customer_subcompany_mainbusiness_temporary.Instance.SaveBusiness(mainCompany.Id, updateTempCustomer_IN.CustomerIndustry, updateTempCustomer_IN.MainProducts);

                #endregion

                //#region 如果代码修改（新代码不为空），需要进行排重（除去当前客户本身，与其他客户进行查重）
                //if (mainCompany.CreditCode != oldCustomerInfo.mainCompany.CreditCode && StringUtil.IsNotNullOrEmpty(mainCompany.CreditCode))
                //{
                //    var ccUniqueInfo = new List<CheckCompanyUniqueInfo>() {new CheckCompanyUniqueInfo()
                //{
                //    CompanyName = mainCompany.CompanyName,
                //    CreditCode = mainCompany.CreditCode
                //} };
                //    DbOpe_crm_customer.Instance.CheckDuplicateCustomer(ccUniqueInfo, new List<string>() { customerInfo.CustomerId });
                //    DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(ccUniqueInfo, user);
                //}
                //#endregion

                //更新
                DbOpe_crm_customer_temporary.Instance.Update(crmCustomer);
                DbOpe_crm_customer_subcompany_temporary.Instance.Update(new List<Db_crm_customer_subcompany_temporary>() { mainCompany });
                //联系信息
                DbOpe_crm_customer_subcompany_contacts_temporary.Instance.SaveContacts(mainCompany.SubCompanyId, user, updateTempCustomer_IN.ContactInfos);
                //采购主体关联提醒
                DbOpe_crm_customer_subcompany_related_temporary.Instance.SaveRelateds(mainCompany.Id, updateTempCustomer_IN.RelatedCompanies);
                if (updateTempCustomer_IN.TrackingStage != oldCustomerInfo.TrackingStage)
                {
                    //同步客户跟踪记录
                    BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(crmCustomer.Id, mainCompany.Id, "", "", (EnumTrackingStage)crmCustomer.TrackingStage, 1, false, false, "", EnumCustomerDataSource.Temporary, true);
                }
            });

        }
        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="queryCustomer_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<QueryTempCustomer_OUT> SearchTemporaryPoolCustomerList(QueryTempCustomer_IN queryCustomer_IN, ref int total)
        {
            var user = currentUser;
            return DbOpe_crm_customer_temporarypool.Instance.SearchTemporaryPoolCustomerList(queryCustomer_IN, user, ref total);
        }
        /// <summary>
        /// 移入私有池
        /// </summary>
        /// <param name="tempCustomerIds"></param>
        /// <exception cref="Exception"></exception>
        public void MoveTemporaryPoolToPrivatePool(List<string> tempCustomerIds)
        {
            /*
             1、验证客户状态，只有客户状态为有效（不在其他用户私有池）才可移入私有池
             2、对临时池数据做状态变更。
             3、查询一下公有池，如果正式客户在公有池，对公有池数据做状态变更，相当于一次领取，只是信息用临时池的信息
             3、移入私有池--用临时客户的信息覆盖正式客户信息，变更私有池数据状态
             ps：如果正式客户存在慧思ID，沿用之前的慧思ID。
             如果不存在则生成慧思ID。
             4、再次转入到私有池仍将使用存入前保护截止日时间，若无时间、并无延期次数，则无法存入私有池
             5、查找临时池中是否还有相同的客户名称，如果存在相同的客户名称，则将其变为无效状态。

            记录客户归属日志（9.18添加）
             */
            var user = currentUser;
            DateTime dt = DateTime.Now;
            DbOpe_crm_customer_temporarypool.Instance.TransDeal(() =>
            {
                List<Db_crm_customer_temporarypool> tempUpdateList = new List<Db_crm_customer_temporarypool>();
                //验证可保留客户数
                DbOpe_crm_customer_privatepool.Instance.CheckMaxSavePrivateCustomer(user);
                //判断是不是在当前用户临时池可用
                DbOpe_crm_customer_temporarypool.Instance.CheckInPool(tempCustomerIds, user);
                //获取临时池客户信息
                List<TemporaryCustomerSimpleInfo> tempCustomerInfos = DbOpe_crm_customer_temporarypool.Instance.GetTempCustomerInfo(tempCustomerIds, user);
                //要变为失效的所有公司代码信息
                List<CheckCompanyUniqueInfo> checkCompanys = new List<CheckCompanyUniqueInfo>();
                foreach (TemporaryCustomerSimpleInfo tempCustomerInfo in tempCustomerInfos)
                {
                    //判断客户有效
                    if (tempCustomerInfo.IsValid == EnumCustomerValid.INVALID)
                    {
                        throw new ApiException("操作失败:客户(" + tempCustomerInfo.CustomerName + ")已失效");
                    }
                    //用临时客户信息覆盖正式客户信息
                    DbOpe_crm_customer.Instance.CopyFromTempCustomer(tempCustomerInfo.CustomerTemporaryId, ref checkCompanys);
                    //临时池记录更新
                    tempCustomerInfo.ReleaseType = (int)EnumCustomerTempRemoveType.Manual;
                    tempCustomerInfo.State = (int)EnumCustomerTempRemove.Remove;
                    tempCustomerInfo.ReleaseTime = dt;
                    tempCustomerInfo.UpdateDate = dt;
                    tempCustomerInfo.UpdateUser = user;
                    tempUpdateList.Add(tempCustomerInfo);
                }
                //获取对应的正式客户ID
                var customerIds = tempCustomerInfos.Select(t => t.CustomerId).ToList();
                //查询在公有池的记录
                var publicExists = DbOpe_crm_customer_publicpool.Instance.GetPublicCustomerInfo(customerIds);
                //更新公有池领取状态和次数
                DbOpe_crm_customer_publicpool.Instance.AddGetTimes(publicExists.Select(p => p.Id).ToList(), user);
                //私有池添加正式客户记录
                var poolData = DbOpe_crm_customer_privatepool.Instance.MoveToUserPrivate(customerIds, user, user, FormatProtectDate(), GetProtectDelayDay(), EnumCustomerPrivateCreateType.GetTemp);
                //临时池记录状态更新
                DbOpe_crm_customer_temporarypool.Instance.Update(tempUpdateList);
                //刷新客户相关公司的失效信息 
                DbOpe_crm_customer_subcompany_temporary.Instance.RefreshVaildCompanys(checkCompanys, EnumCompanyValid.INVALID);
                //240811 作废重复的采购主体关联提醒
                VoidCompanyRelated(checkCompanys);
                //记录客户归属日志（9.18添加）
                foreach (var customerId in customerIds)
                {
                    DbOpe_crm_customer_org_log.Instance.CreateOrCollectCustomerLog(new CreateOrCollectCustomerLogParams()
                    {
                        CreateFlag = true,
                        CustomerId = customerId,
                        PrivatePoolId = poolData.Find(p => p.CustomerId == customerId).Id,
                        UserId = user
                    });
                }
            });
        }
        /// <summary>
        /// 将客户信息从临时池删除
        /// </summary>
        /// <param name="tempCustomerIds"></param>
        public void DeleteTemporaryPool(List<string> tempCustomerIds)
        {
            /*
             1、将临时客户和临时记录改为删除状态
             2、相关记录（追踪日志信息等）关联到有效客户中（改变相关记录的可见标志位）
             */
            var user = currentUser;
            DateTime dt = DateTime.Now;
            DbOpe_crm_customer_temporary.Instance.TransDeal(() =>
            {
                List<Db_crm_customer_temporarypool> tempUpdateList = new List<Db_crm_customer_temporarypool>();
                //判断是不是在当前用户临时池可用
                DbOpe_crm_customer_temporarypool.Instance.CheckInPool(tempCustomerIds, user);
                //获取临时池客户信息
                List<TemporaryCustomerSimpleInfo> tempCustomerInfos = DbOpe_crm_customer_temporarypool.Instance.GetTempCustomerInfo(tempCustomerIds, user);
                foreach (TemporaryCustomerSimpleInfo tempCustomerInfo in tempCustomerInfos)
                {
                    //临时池更新
                    tempCustomerInfo.ReleaseType = (int)EnumCustomerTempRemoveType.Manual;
                    tempCustomerInfo.State = (int)EnumCustomerTempRemove.Remove;
                    tempCustomerInfo.Deleted = (int)EnumCustomerDel.Del;
                    tempCustomerInfo.ReleaseTime = dt;
                    tempCustomerInfo.UpdateDate = dt;
                    tempCustomerInfo.UpdateUser = user;
                    tempUpdateList.Add(tempCustomerInfo);
                }
                //更新临时池状态为删除
                DbOpe_crm_customer_temporarypool.Instance.Update(tempUpdateList);
                //删除临时客户信息
                DbOpe_crm_customer_temporary.Instance.Delete(tempCustomerIds);
                //改变相关记录的可见标志位
                var customerIds = tempCustomerInfos.Select(t => t.CustomerId).ToList();
                OpenCustomerLog(customerIds);
            });
        }
        /// <summary>
        /// 根据客户Id获取相关记录（临时池）
        /// </summary>
        /// <param name="tempCustomerRecord_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<TempCustomerRecord_OUT> GetTempRecordsByCustomerId(TempCustomerRecord_IN tempCustomerRecord_IN, ref int total)
        {
            return DbOpe_crm_customer_temporarypool.Instance.GetTempRecordsByCustomerId(tempCustomerRecord_IN, currentUser, ref total);
        }
        #endregion

        #region 公有池
        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="queryCustomer_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<QueryPublicCustomer_OUT> SearchPublicPoolCustomerList(QueryPublicCustomer_IN queryCustomer_IN, ref int total)
        {
            var user = currentUser;
            return DbOpe_crm_customer_publicpool.Instance.SearchPublicPoolCustomerList(queryCustomer_IN, user, ref total);
        }
        /// <summary>
        /// 获取客户公有池剩余领取次数
        /// </summary>
        /// <returns></returns>
        public int PublicPoolCollectSurplusTimes()
        {
            var user = currentUser;
            return DbOpe_crm_customer_publicpool.Instance.PublicPoolCollectSurplusTimes(user, GetPublicGetNumPerDay());
        }
        /// <summary>
        /// 领取公有池客户信息到私有池
        /// </summary>
        /// <param name="customerIds"></param>
        public void CollectPublicPoolToPrivatePool(List<string> customerIds)
        {
            /*
             验证用户剩余可领取客户数量，小于领取数量则无法领取。
            被冻结客户无法进行领取。（曾经领取过的客户，还没超过冻结时间。）
            （公有池记录做领取操作，私有池如果是首次领取做新增操作，如果以前领取过则获取上次领取所占用的时间，再做新增操作的时候要将上次占用的时间计算进去。
            每一次领取和释放算一次完整操作，再次领取需要新增一条记录。）
             */
            var user = currentUser;
            DbOpe_crm_customer_publicpool.Instance.TransDeal(() =>
            {
                var poolData = DbOpe_crm_customer_publicpool.Instance.PublicToPrivate(customerIds, user, user, GetCustomerDisableDays(), GetPublicGetNumPerDay(), FormatProtectDate());
                //记录客户归属日志（9.18添加）
                foreach (var customerId in customerIds)
                {
                    DbOpe_crm_customer_org_log.Instance.CreateOrCollectCustomerLog(new CreateOrCollectCustomerLogParams()
                    {
                        CreateFlag = false,
                        CustomerId = customerId,
                        PrivatePoolId = poolData.Find(p => p.CustomerId == customerId).Id,
                        UserId = user
                    });
                }
            });
        }
        /// <summary>
        /// 指派公有池客户信息到指定用户私有池
        /// </summary>
        /// <param name="assignPublicCustomer_IN"></param>
        public void AssignPublicPoolToPrivatePool(AssignPublicCustomer_IN assignPublicCustomer_IN)
        {
            /*
             指派公有池客户信息到指定用户私有池。（类似领取公有池客户操作。）
             验证用户剩余可领取客户数量，小于领取数量则无法领取。
             被冻结客户无法进行领取。（曾经领取过的客户，还没超过冻结时间。）
             记录指派信息到Crm_Customer_PublicPool_Assign 公有池指派表即子表。
             */
            var user = currentUser;
            DateTime dt = DateTime.Now;
            DbOpe_crm_customer_publicpool.Instance.TransDeal(() =>
            {
                var poolData = DbOpe_crm_customer_publicpool.Instance.PublicToPrivate(assignPublicCustomer_IN.CustomerIds, user, assignPublicCustomer_IN.AssignUserId, GetCustomerDisableDays(), GetPublicGetNumPerDay(), FormatProtectDate());
                foreach (var customerId in assignPublicCustomer_IN.CustomerIds)
                {
                    var assignId = Guid.NewGuid().ToString();
                    //Db_crm_customer_publicpool crm_Customer_Publicpool = DbOpe_crm_customer_publicpool.Instance.GetCustomerExist(customerId);
                    var publicId = poolData.Find(p => p.CustomerId == customerId).PublicPoolId;
                    DbOpe_crm_customer_publicpool_assign.Instance.Insert(new Db_crm_customer_publicpool_assign()
                    {
                        Id = assignId,
                        AssignUserId = assignPublicCustomer_IN.AssignUserId,
                        DesignateUserId = user,
                        DesignateTime = dt,
                        CreateDate = dt,
                        CreateUser = user
                    });
                    DbOpe_crm_customer_publicpool_assign_sub.Instance.Insert(new Db_crm_customer_publicpool_assign_sub()
                    {
                        Id = Guid.NewGuid().ToString(),
                        CustomerPublicPoolAssignId = assignId,
                        CustomerId = customerId,
                        PublicPoolId = publicId,
                        CreateDate = dt,
                        CreateUser = user
                    });
                    //记录客户归属日志（9.18添加）
                    DbOpe_crm_customer_org_log.Instance.CreateOrCollectCustomerLog(new CreateOrCollectCustomerLogParams()
                    {
                        CreateFlag = false,
                        CustomerId = customerId,
                        PrivatePoolId = poolData.Find(p => p.CustomerId == customerId).Id,
                        UserId = assignPublicCustomer_IN.AssignUserId,
                    });
                }
            });
        }
        #endregion

        #region 团队池
        /// <summary>
        /// 移交离职人员客户(团队)
        /// </summary>
        /// <param name="transferPrivateCustomerTeam_IN"></param>
        public void TransferPrivatePoolTeam(TransferPrivateCustomerTeam_IN transferPrivateCustomerTeam_IN)
        {
            var dt = DateTime.Now;
            //验证操作人是否是管理员
            var userInfo = DbOpe_sys_user.Instance.GetDbSysUserById(currentUser);
            if (userInfo.UserType != (int)EnumUserType.Manager)
            {
                throw new ApiException("当前用户没有权限");
            }

            var managedOrgs = DbOpe_sys_organization.Instance.GetDataList(o => o.ParentId == userInfo.OrganizationId).Select(o => o.Id).ToList();
            //获取直属队队长
            var teamLeaders = DbOpe_sys_user.Instance.GetDataList(u =>
                SqlFunc.ContainsArray(managedOrgs, u.OrganizationId)
                && u.UserType == (int)EnumUserType.Manager
                && u.Deleted == false)
            .Select(u => u.Id)
            .ToList();


            DbOpe_crm_customer_transfer.Instance.TransDeal(() =>
            {
                //240723 拒绝所有待审核
                CheckAuditInProcess(transferPrivateCustomerTeam_IN.CustomerIds);
                //获取接收的用户信息
                var recipientUser = DbOpe_sys_user.Instance.GetDbSysUserById(transferPrivateCustomerTeam_IN.RecipientUserId);

                if (recipientUser.OrganizationId != userInfo.OrganizationId && !teamLeaders.Contains(transferPrivateCustomerTeam_IN.RecipientUserId))
                {
                    throw new ApiException("接收人不在直属团队内");
                }
                #region  获取接受者的各级组织信息
                //获取组织信息树，从当前组织追溯出所有上级组织
                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(recipientUser.OrganizationId);
                string? OrgDivisionId = null;
                string OrgDivisionName = "";
                string? OrgBrigadeId = null;
                string OrgBrigadeName = "";
                string? OrgRegimentId = null;
                string OrgRegimentName = "";
                foreach (var org in orgList)
                {
                    switch (org.OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            OrgDivisionId = org.Id;
                            OrgDivisionName = org.OrgName;
                            break;
                        case EnumOrgType.Battalion:
                            OrgBrigadeId = org.Id;
                            OrgBrigadeName = org.OrgName;
                            break;
                        case EnumOrgType.Squadron:
                            OrgRegimentId = org.Id;
                            OrgRegimentName = org.OrgName;
                            break;
                    }
                }
                #endregion
                List<PrivateCustomerSimpleInfo> privateCustomerInfos = DbOpe_crm_customer_privatepool.Instance.GetPrivateCustomerInfo(transferPrivateCustomerTeam_IN.CustomerIds, "");
                //var leftNum = DbOpe_crm_customer_privatepool.Instance.GetMaxSavePrivateCustomerNumLeft(transferPrivateCustomer_IN.RecipientUserId);
                foreach (PrivateCustomerSimpleInfo privateCustomerInfo in privateCustomerInfos)
                {
                    if (privateCustomerInfo.Deleted == (int)EnumCustomerDel.Del || privateCustomerInfo.State != (int)EnumCustomerPrivateRelease.Not)
                    {
                        throw new ApiException(privateCustomerInfo.CustomerName + "已被释放或删除，不能移交");
                    }
                    //验证离职
                    var leaveUserInfo = DbOpe_sys_user.Instance.GetDbSysUserById(privateCustomerInfo.UserId);
                    if (leaveUserInfo.UserStatus)
                    {
                        throw new ApiException("当前用户尚未离职，不能操作其客户");
                    }


                    if (leaveUserInfo.OrganizationId != userInfo.OrganizationId && !teamLeaders.Contains(leaveUserInfo.Id))
                    {
                        throw new ApiException("客户所属人不在直属团队内");
                    }
                    var customerId = privateCustomerInfo.CustomerId;
                    #region 添加移交记录
                    var transferId = Guid.NewGuid().ToString();
                    DbOpe_crm_customer_transfer.Instance.Insert(new Db_crm_customer_transfer()
                    {
                        Id = transferId,
                        //原ID
                        UserId = leaveUserInfo.Id,
                        //接收ID
                        RecipientUserId = transferPrivateCustomerTeam_IN.RecipientUserId,
                        CustomerId = customerId,
                        TransferDate = dt,
                        CreateDate = dt,
                        CreateUser = currentUser
                    });


                    #region 处理合同和发票
                    var companys = DbOpe_crm_customer_subcompany.Instance.GetSubcompanyListByCustomerId(customerId, leaveUserInfo.Id).Select(c => c.Id).ToList();
                    //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限  所以这个客户要带着所有的合同/发票/到账一起移交
                    //var contracts = DbOpe_crm_contract.Instance.GetDataList(c => c.CreateUser == leaveUserInfo.Id && companys.Contains(c.FirstParty));
                    var contracts = DbOpe_crm_contract.Instance.GetDataList(c => companys.Contains(c.FirstParty));
                    var contractIds = contracts.Select(c => c.Id).Distinct().ToList();
                    var payments = DbOpe_crm_contract_paymentinfo.Instance.GetDataList(c => contractIds.Count > 0 && contractIds.Contains(c.ContractId)).ToList();
                    var cps = DbOpe_crm_contract_productinfo.Instance.GetDataList(c => contractIds.Count > 0 && contractIds.Contains(c.ContractId)).ToList();
                    foreach (var contract in contracts)
                    {
                        //contract.CreateUser = transferPrivateCustomerTeam_IN.RecipientUserId;
                        contract.Issuer = transferPrivateCustomerTeam_IN.RecipientUserId;
                        contract.OrgDivisionId = OrgDivisionId;
                        contract.OrgDivisionName = OrgDivisionName;
                        contract.OrgBrigadeId = OrgBrigadeId;
                        contract.OrgBrigadeName = OrgBrigadeName;
                        contract.OrgRegimentId = OrgRegimentId;
                        contract.OrgRegimentName = OrgRegimentName;
                        contract.UpdateDate = dt;
                        contract.UpdateUser = currentUser;
                        DbOpe_crm_contract.Instance.UpdateBase(contract);
                        DbOpe_crm_contract_transfer.Instance.Insert(new Db_crm_contract_transfer()
                        {
                            Id = Guid.NewGuid().ToString(),
                            TransferId = transferId,
                            ContractId = contract.Id
                        });
                    }
                    foreach (var payment in payments)
                    {
                        payment.CreateUser = transferPrivateCustomerTeam_IN.RecipientUserId;
                        payment.UpdateDate = dt;
                        payment.UpdateUser = currentUser;
                        DbOpe_crm_contract_paymentinfo.Instance.Update(payment);
                    }
                    foreach (var cp in cps)
                    {
                        cp.CreateUser = transferPrivateCustomerTeam_IN.RecipientUserId;
                        cp.UpdateDate = dt;
                        cp.UpdateUser = currentUser;
                        DbOpe_crm_contract_productinfo.Instance.UpdateBase(cp);
                    }
                    //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限  所以这个客户要带着所有的合同/发票/到账一起移交
                    //发票权限跟随合同权限（Issuer）
                    //var invoices = DbOpe_crm_contract_invoice.Instance.GetDataList(i => i.CreateUser == leaveUserInfo.Id && contracts.Select(c => c.Id).Contains(i.ContractId));
                    //var invoiceAppls = DbOpe_crm_contract_invoiceappl.Instance.GetDataList(i => i.CreateUser == leaveUserInfo.Id && contracts.Select(c => c.Id).Contains(i.ContractId));
                    var invoices = DbOpe_crm_contract_invoice.Instance.GetDataList(i => contracts.Select(c => c.Id).Contains(i.ContractId));
                    var invoiceAppls = DbOpe_crm_contract_invoiceappl.Instance.GetDataList(i => contracts.Select(c => c.Id).Contains(i.ContractId));
                    foreach (var invoice in invoices)
                    {
                        //invoice.CreateUser = transferPrivateCustomerTeam_IN.RecipientUserId;
                        invoice.UpdateDate = dt;
                        invoice.UpdateUser = currentUser;
                        DbOpe_crm_contract_invoice.Instance.Update(invoice);
                        DbOpe_crm_contract_invoice_transfer.Instance.Insert(new Db_crm_contract_invoice_transfer()
                        {
                            Id = Guid.NewGuid().ToString(),
                            TransferId = transferId,
                            ContractInvoiceId = invoice.Id
                        });
                    }
                    foreach (var iAppl in invoiceAppls)
                    {
                        //iAppl.CreateUser = transferPrivateCustomerTeam_IN.RecipientUserId;
                        iAppl.UpdateDate = dt;
                        iAppl.UpdateUser = currentUser;
                        DbOpe_crm_contract_invoiceappl.Instance.UpdateBase(iAppl);
                    }
                    #endregion
                    #region 原用户私有池状态更新
                    Db_crm_customer_privatepool orginUpdate = privateCustomerInfos.Find(c => c.CustomerId == customerId).MappingTo<Db_crm_customer_privatepool>();
                    orginUpdate.State = (int)EnumCustomerPrivateRelease.Release;
                    orginUpdate.ReleaseType = (int)EnumCustomerPrivateReleaseType.Manual;
                    orginUpdate.ReleaseReason = "移交";
                    orginUpdate.ReleaseTime = dt;
                    orginUpdate.UpdateDate = dt;
                    orginUpdate.UpdateUser = currentUser;
                    DbOpe_crm_customer_privatepool.Instance.Update(orginUpdate);
                    #endregion
                    #region 新用户私有池添加
                    //如果不是签约客户，保护期按领取重新计算
                    // var signCustomer = false;
                    DateTime productDeadLine = FormatProtectDate();
                    //如果是签约客户且未过期，保护期不变
                    if (privateCustomerInfo.TrackingStage == (int)EnumTrackingStage.Received && privateCustomerInfo.ProtectionDeadline != null && privateCustomerInfo.ProtectionDeadline.Value.GetDaysStart() > dt)
                    {
                        productDeadLine = privateCustomerInfo.ProtectionDeadline.Value;
                        // signCustomer = true;
                    }
                    var poolList = DbOpe_crm_customer_privatepool.Instance.MoveToUserPrivate(new List<string>() { customerId }, currentUser, transferPrivateCustomerTeam_IN.RecipientUserId, productDeadLine, GetProtectDelayDay(), EnumCustomerPrivateCreateType.Transfer, true);
                    #endregion
                    //记录客户归属日志（9.18添加）
                    DbOpe_crm_customer_org_log.Instance.TransferCustomerLog(new TransferCustomerLogParams()
                    {
                        CustomerId = customerId,
                        SourceUserId = leaveUserInfo.Id,
                        TargetUserId = transferPrivateCustomerTeam_IN.RecipientUserId,
                        PrivatePoolId = poolList.Find(d => d.CustomerId == customerId).Id
                    });
                    #endregion
                }



            });
        }

        /// <summary>
        /// 根据查询条件获取团队池客户信息列表 
        /// </summary>
        /// <param name="queryTeamCustomer_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<QueryTeamCustomer_OUT> SearchTeamPoolCustomerList(QueryTeamCustomer_IN queryTeamCustomer_IN, ref int total)
        {
            return DbOpe_crm_customer_privatepool.Instance.SearchTeamPoolCustomerList(queryTeamCustomer_IN, currentUser, ref total);
        }
        /// <summary>
        /// 预验客户
        /// </summary>
        /// <param name="customerName"></param>
        /// <returns></returns>
        public PreInspectionCustomerResult_OUT PreInspectionCustomerList(string customerName)
        {
            //return DbOpe_crm_customer_privatepool.Instance.PreInspectionCustomerList(customerName, currentUser);
            return new PreInspectionOptimizer().PreInspectionCustomerList(customerName, currentUser,true);
        }

        /// <summary>
        /// 预验客户-查看客户签约保留状态
        /// </summary>
        /// <param name="checkCustomer_In"></param>
        /// <returns></returns>
        public string PreInspectionCustomerHasSigned(PreInspectionCustomerHasSigned_IN checkCustomer_In)
        {
            return DbOpe_crm_customer_privatepool.Instance.PreInspectionCustomerHasSigned(checkCustomer_In, currentUser);
        }
        /// <summary>
        /// 获取归属客户信息
        /// </summary>
        /// <param name="ascriptionCustomer_IN"></param>
        /// <returns></returns>
        public ApiTableOut<AscriptionCustomer_OUT> AscriptionCustomerList(AscriptionCustomer_IN ascriptionCustomer_IN)
        {
            return DbOpe_crm_customer_privatepool.Instance.AscriptionCustomerList(ascriptionCustomer_IN);
        }
        ///// <summary>
        ///// 获取归属客户信息
        ///// </summary>
        ///// <param name="customerName"></param>
        ///// <returns></returns>
        //public List<AscriptionCustomer_OUT> AscriptionCustomerList(string customerName)
        //{
        //    return DbOpe_crm_customer_privatepool.Instance.AscriptionCustomerList(customerName);
        //}
        #endregion

        #region 中国出口企业
        /// <summary>
        /// 根据查询条件获取中国出口企业客户信息列表
        /// </summary>
        /// <param name="searchExportCustomer_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SearchExportCustomer_OUT> SearchExportCustomerList(SearchExportCustomer_IN searchExportCustomer_IN, ref int total)
        {
            return DbOpe_crm_customer_export.Instance.SearchExportCustomerList(searchExportCustomer_IN, ref total);
        }
        /// <summary>
        /// 获取中出池客户详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SearchExportCustomer_OUT GetExportCustomer(string id)
        {
            return DbOpe_crm_customer_export.Instance.GetExportCustomer(id);
        }
        /// <summary>
        /// 根据查询条件获取中国出口企业客户信息列表
        /// </summary>
        /// <param name="searchExportCustomer_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SearchExportCustomer_OUT> SearchChinaExportCustomerList(SearchExportCustomer_IN searchExportCustomer_IN, ref int total)
        {
            return DbOpe_crm_customer_china_exporter.Instance.SearchExportCustomerList(searchExportCustomer_IN, ref total);
        }
        /// <summary>
        /// 获取中国出口企业资源池下拉选项
        /// </summary>
        /// <param name="searchExportCustomer_IN"></param>
        /// <returns></returns>
        public SearchExportCustomerSelections_OUT SearchExportCustomerSelections(SearchExportCustomerSelections_IN searchExportCustomer_IN)
        {
            return DbOpe_crm_customer_china_exporter.Instance.SearchExportCustomerSelections(searchExportCustomer_IN);
        }
        /// <summary>
        /// 获取中出池客户详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SearchExportCustomer_OUT GetChinaExportCustomer(string id)
        {
            return DbOpe_crm_customer_china_exporter.Instance.GetExportCustomer(id);
        }
        /// <summary>
        /// 领取中国出口客户信息到私有池
        /// </summary>
        /// <param name="CustomerExportIds"></param>
        public void CollectExportToPrivatePool(List<string> CustomerExportIds)
        {
            /*
             领取中国出口客户信息到私有池。
             首先将中国出口企业信息领取到公有池，然后再将客户从公有池领取到私有池。
             将中国出口企业信息在公有池做新增操作，然后给中国出口企业信息打已领取标识，已领取的客户以后就不可以搜索到，然后再将新增的公有池客户信息做领取到私有池操作。
             逻辑同公有池到私有池领取操作。
             验证用户剩余可领取客户数量，小于领取数量则无法领取。
             被冻结客户无法进行领取。（曾经领取过的客户，还没超过冻结时间。）
             Sys_Customer_PrivatePool 客户临时池表  PublicPoolId字段记录私有池与公有池关系，CreateType记录临时池创建方式（新增、移入）。
             */
            DateTime dt = DateTime.Now;
            var user = currentUser;
            DbOpe_crm_customer_export.Instance.TransDeal(() =>
            {
                //验证是否在中出池子里
                var exportCustomers = DbOpe_crm_customer_export.Instance.GetExportCustomers(CustomerExportIds, false);
                foreach (string id in CustomerExportIds)
                {
                    var exist = exportCustomers.Find(c => c.Id == id);
                    if (exist == null)
                    {
                        throw new ApiException("操作失败:未在中出池找到客户");
                    }
                    else if (exist.Deleted == (int)EnumCustomerDel.Del || exist.IsCollection == (int)EnumCustomerExportGet.Get)
                    {
                        throw new ApiException("操作失败:" + exist.EnterpriseName + "已被领取");
                    }
                }
                #region 中出到公有池 并且添加客户信息 
                exportCustomers.ForEach(c =>
                {
                    c.IsCollection = (int)EnumCustomerExportGet.Get;
                    c.CollectionUser = user;
                    c.UpdateDate = dt;
                    c.UpdateUser = user;
                });
                DbOpe_crm_customer_export.Instance.Update(exportCustomers);
                List<Db_crm_customer_publicpool> publicAdd = new List<Db_crm_customer_publicpool>();
                List<Db_crm_customer> customerAdd = new List<Db_crm_customer>();
                List<Db_crm_customer_subcompany> companyAdd = new List<Db_crm_customer_subcompany>();
                exportCustomers.ForEach(c =>
                {
                    customerAdd.Add(new Db_crm_customer()
                    {
                        Id = c.Id,
                        CustomerNum = FormatHSId(),
                        CustomerSource = (int)EnumCustomerSource.ChinaExport,
                        IsSupplementary = (int)EnumNeedSupplement.Need,
                        IsValid = (int)EnumCustomerValid.VALID,
                        CreateUser = user,
                        CreateDate = dt
                    });
                    companyAdd.Add(new Db_crm_customer_subcompany()
                    {
                        Id = Guid.NewGuid().ToString(),
                        CustomerId = c.Id,
                        CompanyName = c.EnterpriseName,
                        Contacts = c.Contacts,
                        ContactWay = c.ContactWay,
                        Job = c.Job,
                        Address = c.EnterpriseAddress,
                        Country = LocalCache.LC_Address.CountryAndAreaCache.Find(cache => cache.Name == "中国")?.Id ?? -1,
                        Province = LocalCache.LC_Address.ProvinceCache.Find(cache => cache.Name == c.Province)?.Id ?? -1,
                        IsMain = (int)EnumCustomerCompanyMain.Main,
                        IsValid = (int)EnumCompanyValid.VALID,
                        CreateDate = dt,
                        CreateUser = user,
                        CompanyNameDM = new ChineseAnalyzerCls().ReplaceKeyword(c.EnterpriseName)
                    });
                    publicAdd.Add(new Db_crm_customer_publicpool()
                    {
                        Id = Guid.NewGuid().ToString(),
                        CustomerId = c.Id,
                        GetTimes = 0,
                        State = (int)EnumCustomerPublicGet.Not,
                        CreateDate = dt,
                        CreateUser = user
                    });
                });
                DbOpe_crm_customer.Instance.Insert(customerAdd);
                DbOpe_crm_customer_subcompany.Instance.Insert(companyAdd);
                DbOpe_crm_customer_publicpool.Instance.Insert(publicAdd);
                //公有池到私有池
                var poolData = DbOpe_crm_customer_publicpool.Instance.PublicToPrivate(publicAdd, user, user, GetCustomerDisableDays(), GetPublicGetNumPerDay(), FormatProtectDate());
                #endregion
                //同步客户跟踪记录
                var companyAddDict = companyAdd.ToDictionary(k => k.CustomerId, v => v);
                foreach (Db_crm_customer customer in customerAdd)
                {
                    string customerSubCompanyId = string.Empty;
                    if (companyAddDict.TryGetValue(customer.Id, out Db_crm_customer_subcompany? companyAddItem))
                    {
                        if (companyAddItem != null)
                        {

                            customerSubCompanyId = companyAddItem.Id;
                        }
                        else
                        {
                            throw new ApiException("操作失败:未找到客户对应的公司信息");
                        }
                    };
                    BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(customer.Id, customerSubCompanyId, "", "", (EnumTrackingStage)customer.TrackingStage, 1, true, false, "", EnumCustomerDataSource.Private, true);
                    DbOpe_crm_customer_org_log.Instance.CreateOrCollectCustomerLog(new CreateOrCollectCustomerLogParams()
                    {
                        CreateFlag = true,
                        CustomerId = customer.Id,
                        PrivatePoolId = poolData.Find(p => p.CustomerId == customer.Id).Id,
                        UserId = user
                    });
                }
            });
        }
        #endregion

        #region 广交会
        /// <summary>
        /// 根据查询条件获取广交会客户信息列表
        /// </summary>
        /// <param name="searchFairCustomer_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SearchFairCustomer_OUT> SearchFairCustomerList(SearchFairCustomer_IN searchFairCustomer_IN, ref int total)
        {
            return DbOpe_crm_customer_fair.Instance.SearchFairCustomerList(searchFairCustomer_IN, ref total);
        }
        /// <summary>
        /// 获取广交会客户信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SearchFairCustomer_OUT GetFairCustomer(string id)
        {
            return DbOpe_crm_customer_fair.Instance.GetFairCustomer(id);
        }
        /// <summary>
        /// 根据查询条件获取广交会客户信息列表
        /// </summary>
        /// <param name="searchFairCustomer_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SearchFairCustomer_OUT> SearchCantonFairCustomerList(SearchFairCustomer_IN searchFairCustomer_IN, ref int total)
        {
            return DbOpe_crm_customer_canton_fair.Instance.SearchFairCustomerList(searchFairCustomer_IN, ref total);
        }
        /// <summary>
        /// 获取广交会企业资源池下拉选项
        /// </summary>
        /// <param name="searchFairCustomerSelections_IN"></param>
        /// <returns></returns>
        public SearchFairCustomerSelections_OUT SearchFairCustomerSelections(SearchFairCustomerSelections_IN searchFairCustomerSelections_IN)
        {
            return DbOpe_crm_customer_canton_fair.Instance.SearchFairCustomerSelections(searchFairCustomerSelections_IN);
        }
        /// <summary>
        /// 获取广交会客户信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SearchFairCustomer_OUT GetCantonFairCustomer(string id)
        {
            return DbOpe_crm_customer_canton_fair.Instance.GetFairCustomer(id);
        }
        /// <summary>
        /// 领取广交会客户信息到私有池
        /// </summary>
        /// <param name="CustomerFairIds"></param>
        public void CollectFairToPrivatePool(List<string> CustomerFairIds)
        {
            /*
            领取广交会客户信息到私有池。
            首先将广交会企业信息领取到公有池，然后再将客户从公有池领取到私有池。
            即 将广交会企业信息在公有池做新增操作，然后给广交会企业信息打已领取标识，已领取的客户以后就不可以搜索到
            然后再将新增的公有池客户信息做领取到私有池操作。逻辑同公有池到私有池领取操作
            。验证用户剩余可领取客户数量，小于领取数量则无法领取。
            被冻结客户无法进行领取。（曾经领取过的客户，还没超过冻结时间。）
            Sys_Customer_PrivatePool 客户临时池表  PublicPoolId字段记录私有池与公有池关系，CreateType记录临时池创建方式（新增、移入）。
             */
            DateTime dt = DateTime.Now;
            var user = currentUser;
            DbOpe_crm_customer_fair.Instance.TransDeal(() =>
            {
                //验证是否在广交会池子里
                var fairCustomers = DbOpe_crm_customer_fair.Instance.GetFairCustomers(CustomerFairIds, false, false);
                foreach (string id in CustomerFairIds)
                {
                    var exist = fairCustomers.Find(c => c.Id == id);
                    if (exist == null || exist.Deleted == (int)EnumCustomerDel.Del)
                    {
                        throw new ApiException("操作失败:未在广交会企业中找到客户");
                    }
                    else if (exist.Deleted == (int)EnumCustomerDel.Del || exist.IsCollection == (int)EnumCustomerExportGet.Get)
                    {
                        throw new ApiException("操作失败:" + exist.EnterpriseName + "已被领取或删除");
                    }
                }
                #region 广交会到公有池 并且添加客户信息 
                fairCustomers.ForEach(c =>
                {
                    c.IsCollection = (int)EnumCustomerExportGet.Get;
                    c.CollectionUser = user;
                    c.UpdateDate = dt;
                    c.UpdateUser = user;
                });
                DbOpe_crm_customer_fair.Instance.Update(fairCustomers);
                List<Db_crm_customer_publicpool> publicAdd = new List<Db_crm_customer_publicpool>();
                List<Db_crm_customer> customerAdd = new List<Db_crm_customer>();
                List<Db_crm_customer_subcompany> companyAdd = new List<Db_crm_customer_subcompany>();
                List<Db_crm_customer_subcompany_mainbusiness> businessAdd = new List<Db_crm_customer_subcompany_mainbusiness>();
                fairCustomers.ForEach(c =>
                {
                    customerAdd.Add(new Db_crm_customer()
                    {
                        Id = c.Id,
                        CustomerNum = FormatHSId(),
                        CustomerSource = (int)EnumCustomerSource.Fair,
                        CustomerNature = c.EnterpriseType,
                        CustomerSize = c.EnterpriseSize,
                        IsSupplementary = (int)EnumNeedSupplement.Need,
                        IsValid = (int)EnumCustomerValid.VALID,
                        CreateUser = user,
                        CreateDate = dt
                    });
                    var companyId = Guid.NewGuid().ToString();
                    companyAdd.Add(new Db_crm_customer_subcompany()
                    {
                        Id = companyId,
                        CustomerId = c.Id,
                        CompanyName = c.EnterpriseName,
                        ContactWay = c.ContactWay,
                        Contacts = c.Contacts,
                        Telephone = c.Telephone,
                        Job = c.Job,
                        Fax = c.Fax,
                        Email = c.Email,
                        Address = c.EnterpriseAddress,
                        IsMain = (int)EnumCustomerCompanyMain.Main,
                        IsValid = (int)EnumCompanyValid.VALID,
                        CreateDate = dt,
                        CreateUser = user,
                        CompanyNameDM = new ChineseAnalyzerCls().ReplaceKeyword(c.EnterpriseName)
                    });
                    var fairBusiness = DbOpe_crm_customer_fair_mainbusiness.Instance.GetCompanyBusiness(c.Id);
                    businessAdd = fairBusiness.MappingTo<List<Db_crm_customer_subcompany_mainbusiness>>();
                    businessAdd.ForEach(b =>
                    {
                        b.SubCompanyId = companyId;
                    });
                    publicAdd.Add(new Db_crm_customer_publicpool()
                    {
                        Id = Guid.NewGuid().ToString(),
                        CustomerId = c.Id,
                        GetTimes = 0,
                        State = (int)EnumCustomerPublicGet.Not,
                        CreateDate = dt,
                        CreateUser = user
                    });
                });
                DbOpe_crm_customer.Instance.Insert(customerAdd);
                DbOpe_crm_customer_subcompany.Instance.Insert(companyAdd);
                DbOpe_crm_customer_publicpool.Instance.Insert(publicAdd);
                DbOpe_crm_customer_subcompany_mainbusiness.Instance.Insert(businessAdd);
                #endregion
                //公有池到私有池
                var poolData = DbOpe_crm_customer_publicpool.Instance.PublicToPrivate(publicAdd, user, user, GetCustomerDisableDays(), GetPublicGetNumPerDay(), FormatProtectDate());
                //同步客户跟踪记录
                //记录客户归属日志（9.18添加）
                var companyAddDict = companyAdd.ToDictionary(k => k.CustomerId, v => v);
                foreach (Db_crm_customer customer in customerAdd)
                {
                    string customerSubCompanyId = string.Empty;
                    if (companyAddDict.TryGetValue(customer.Id, out Db_crm_customer_subcompany? subcompany))
                    {
                        if (subcompany != null)
                        {
                            customerSubCompanyId = subcompany.Id;
                        }
                        else
                        {
                            throw new Exception("没有找到对应的公司信息");
                        }
                    }
                    BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(customer.Id, customerSubCompanyId, "", "", (EnumTrackingStage)customer.TrackingStage, 1, true, false, "", EnumCustomerDataSource.Private, true);
                    DbOpe_crm_customer_org_log.Instance.CreateOrCollectCustomerLog(new CreateOrCollectCustomerLogParams()
                    {
                        CreateFlag = true,
                        CustomerId = customer.Id,
                        PrivatePoolId = poolData.Find(p => p.CustomerId == customer.Id).Id,
                        UserId = user
                    });
                }
            });
        }
        #endregion

        #region 关系审核

        /// <summary>
        /// 获取合并客户审核详情
        /// </summary>
        /// <param name="auditId"></param>
        public GetMergeCustomerAudit_OUT GetMergeCustomerAuditInfo(string auditId)
        {
            return DbOpe_crm_mergecompany_relationship.Instance.GetAuditInfo(auditId);
        }

        /// <summary>
        /// 获取添加子公司审核详情
        /// </summary>
        /// <param name="auditId"></param>
        public GetSubCompanyAudit_OUT GetAddSubCompanyAuditInfo(string auditId)
        {
            return DbOpe_crm_addsubcompany_audit_subcompany.Instance.GetAuditInfo(auditId);
        }

        /// <summary>
        /// 拆分子公司（因为没有审核所以就直接拆了，做个记录就行）
        /// </summary>
        /// <param name="splitCompanyAudit_IN"></param>
        public void SplitCompanyAudit(SplitAddSubCompanyAudit_IN splitCompanyAudit_IN)
        {
            DbOpe_crm_splitcompany_audit.Instance.SplitCompanyAudit(splitCompanyAudit_IN, currentUser);
        }

        /// <summary>
        /// 拆分合并客户（因为没有审核所以就直接拆了，做个记录就行）
        /// </summary>
        /// <param name="splitMergeCustomerAudit_IN"></param>
        public void SplitMergeCustomerAudit(SplitMergeCustomerAudit_IN splitMergeCustomerAudit_IN)
        {
            DbOpe_crm_contract.Instance.TransDeal(() =>
            {
                DbOpe_crm_splitcompany_audit.Instance.SplitMergeCustomerAudit(splitMergeCustomerAudit_IN, currentUser);
            });
        }

        /// <summary>
        /// 审核合并关系
        /// </summary>
        /// <param name="mergeCustomerAudit_IN"></param>
        public void MergePrivatePoolAudit(MergeCustomerAudit_IN mergeCustomerAudit_IN)
        {
            DbOpe_crm_mergecompany_relationship.Instance.MergePrivatePoolAudit(mergeCustomerAudit_IN, currentUser);
        }
        /// <summary>
        /// 审核子公司关系
        /// </summary>
        /// <param name="addSubCompanyAudit_IN"></param>
        public void AddSubCompanyPrivatePoolAudit(AddSubCompanyAudit_IN addSubCompanyAudit_IN)
        {
            DbOpe_crm_addsubcompany_audit_subcompany.Instance.AddSubCompanyPrivatePoolAudit(addSubCompanyAudit_IN, currentUser);
        }
        /// <summary>
        /// 查询审核列表
        /// </summary>
        /// <param name="searchCustomerAudit_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SearchCustomerAudit_OUT> SearchCustomerCompanyRelationshipAudit(SearchCustomerAudit searchCustomerAudit_IN, ref int total)
        {
            total = 0;
            return DbOpe_crm_addsubcompany_audit.Instance.SearchCustomerCompanyRelationshipAudit(searchCustomerAudit_IN, currentUser, ref total, true);
        }
        /// <summary>
        /// 获取客户审核列表（非管理人员用，不区分数据权限，但需要验证客户权限）
        /// </summary>
        /// <param name="getCustomerAuditInfo_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SearchCustomerAudit_OUT> GetCustomerAuditInfoById(GetCustomerAuditInfo_IN getCustomerAuditInfo_IN, ref int total)
        {
            //验证客户权限
            var cai = DbOpe_crm_customer.Instance.CheckCustomerAuthInfo(getCustomerAuditInfo_IN.CustomerId, currentUser);
            if (!cai.CanView)
            {
                throw new ApiException("未找到对应的客户信息(或没有查看权限)");
            }
            return DbOpe_crm_addsubcompany_audit.Instance.SearchCustomerCompanyRelationshipAudit(getCustomerAuditInfo_IN.MappingTo<SearchCustomerAudit>(), currentUser, ref total, false);
        }
        /// <summary>
        /// 查询客户审核历史记录
        /// </summary>
        /// <param name="getCustomerAuditRecord_IN"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<GetCustomerAuditRecord_OUT> GetCustomerAuditRecords(GetCustomerAuditRecord_IN getCustomerAuditRecord_IN, ref int total)
        {
            total = 0;
            return DbOpe_crm_addsubcompany_audit.Instance.GetCustomerAuditRecords(getCustomerAuditRecord_IN, ref total);
        }

        /// <summary>
        /// 验证是否在审核流程中
        /// </summary>
        /// <param name="crmCustomerCompanys"></param>
        public void CheckAuditInProcess(List<CheckCompanyUniqueInfo> crmCustomerCompanys)
        {
            DbOpe_crm_addsubcompany_audit_subcompany.Instance.CheckAuditInProcess(crmCustomerCompanys);
            DbOpe_crm_mergecompany_relationship.Instance.CheckAuditInProcess(crmCustomerCompanys);
        }
        /// <summary>
        /// 验证是否在审核流程中
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="throwException"></param>
        public List<string> CheckAuditInProcess(List<string> customerIds, bool throwException = true)
        {
            var c1 = DbOpe_crm_addsubcompany_audit.Instance.CheckAuditInProcess(customerIds, throwException);
            var c2 = DbOpe_crm_mergecompany_relationship.Instance.CheckAuditInProcess(customerIds, throwException);
            return c1.Concat(c2).ToList().Distinct().ToList();
        }
        /// <summary>
        /// 验证是否在审核流程中
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="mergeCompanyAuditId"></param>
        /// <param name="throwException"></param>
        public List<string> CheckAuditInProcessNoSelf(List<string> customerIds, string mergeCompanyAuditId, bool throwException = true)
        {
            var c1 = DbOpe_crm_addsubcompany_audit.Instance.CheckAuditInProcessNoSelf(customerIds, mergeCompanyAuditId, throwException);
            var c2 = DbOpe_crm_mergecompany_relationship.Instance.CheckAuditInProcessNoSelf(customerIds, mergeCompanyAuditId, throwException);
            return c1.Concat(c2).ToList().Distinct().ToList();
        }
        /// <summary>
        /// 拒绝所有相关审核
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="throwException"></param>
        public void CancelAuditInProcess(List<string> customerIds, bool throwException = true)
        {
            DbOpe_crm_addsubcompany_audit.Instance.CancelAuditInProcess(customerIds);
            DbOpe_crm_mergecompany_relationship.Instance.CancelAuditInProcess(customerIds);
        }
        /// <summary>
        /// 查询审核所有提交人
        /// </summary>
        /// <param name="searchCustomerApplicant_IN"></param>
        /// <returns></returns>
        public List<GetOrganizationWithUserTree_Out> SearchCustomerCompanyRelationshipApplicants(SearchCustomerApplicant_IN searchCustomerApplicant_IN)
        {
            return DbOpe_crm_addsubcompany_audit.Instance.SearchCustomerCompanyRelationshipApplicants(searchCustomerApplicant_IN.MappingTo<SearchCustomerAudit>());
        }
        #endregion

        #region 作废
        ///// <summary>
        ///// 修改私有池客户
        ///// </summary>
        ///// <param name="updateCustomer_IN"></param>
        //public void UpdatePrivatePoolCustomer(UpdateCustomer_IN updateCustomer_IN)
        //{
        //    /*
        //     * 排重：验证客户信息是否重复，已存在则无法保存，返回相应提示信息(到私有池和公有池和个人临时池查询重复）
        //     * 其他临时池的相同名称/代码客户要置为失效
        //     * 如果更改了名称或代码的话，这里得刷新一下客户唯一表的绑定关系,删除原有绑定关系，按新的名称和代码去绑定客户信息
        //     * 如果有子公司名称/代码变更，要提交审核
        //     */
        //    var user = UserId;
        //    var nowDate = DateTime.Now;

        //    #region 获取原有的客户信息
        //    crm_customer crmCustomer = updateCustomer_IN.MappingTo<crm_customer>();
        //    var currentCustomerInfo = DbOpe_crm_customer.Instance.GetCustomerInfoById(crmCustomer.Id);
        //    var mainCompany_old = currentCustomerInfo.mainCompany;
        //    var subCompanys_old = currentCustomerInfo.subCompanyList;
        //    #endregion

        //    #region 更新客户
        //    crmCustomer.UpdateUser = user;
        //    crmCustomer.UpdateDate = nowDate;
        //    DbOpe_crm_customer.Instance.UpdateQueue(crmCustomer);
        //    #endregion

        //    #region 更新主公司
        //    //验证公司名称/代码的有效性（后补）
        //    crm_customer_subcompany mainCompany = updateCustomer_IN.MappingTo<crm_customer_subcompany>();
        //    mainCompany.UpdateUser = user;
        //    mainCompany.UpdateDate = nowDate;
        //    mainCompany.IsMain = (int)EnumCustomerCompanyMain.Main;
        //    #endregion

        //    #region 公司变更/审核 变更时旧名字的公司被拆分（无需审核，添加拆分记录） 新名字的公司要提交审核
        //    //验证公司名称/代码的有效性（后补）
        //    crm_addsubcompany_audit audit = new crm_addsubcompany_audit();
        //    crm_splitcompany_audit splitAudit = new crm_splitcompany_audit();
        //    var auditId = Guid.NewGuid().ToString();
        //    var splitAuditId = Guid.NewGuid().ToString();
        //    //所有子公司
        //    List<crm_customer_subcompany> newSubCompanys = updateCustomer_IN.SubCompanys.MappingTo<List<crm_customer_subcompany>>();
        //    //需要提交审核的公司列表
        //    List<crm_addsubcompany_audit_subcompany> auditSubCompanys = new List<crm_addsubcompany_audit_subcompany>();
        //    //没变更名称/代码，直接更新其他信息的公司
        //    List<crm_customer_subcompany> updateSubCompanys = new List<crm_customer_subcompany>();
        //    //需要记录拆分的公司列表
        //    List<crm_splitcompany_audit_companylist> splitSubcompanys = new List<crm_splitcompany_audit_companylist>();
        //    //新添加的子公司，需要提交审核
        //    List<crm_addsubcompany_audit_subcompany> insertAuditCompanys = newSubCompanys.FindAll(c => string.IsNullOrEmpty(c.Id)).MappingTo<List<crm_addsubcompany_audit_subcompany>>();
        //    //不是新添加的公司，循环判断是不是改了名字/代码，改了的要去进行拆分和审核，没改的直接更新信息
        //    List<crm_customer_subcompany> updateCompanys = updateSubCompanys.FindAll(c => !string.IsNullOrEmpty(c.Id));
        //    //被修改了名称、代码的原公司列表
        //    List<crm_customer_subcompany> updateOriginCompanys = new List<crm_customer_subcompany>();
        //    List<crm_customer_subcompany> updateOriginCompanys_sub = new List<crm_customer_subcompany>();
        //    //主公司是否修改了名称/代码
        //    var mainNameChange = mainCompany_old.CompanyName != mainCompany.CompanyName || mainCompany_old.CreditCode != mainCompany.CreditCode;
        //    if (mainNameChange)
        //    {
        //        updateOriginCompanys.Add(mainCompany_old);
        //    }
        //    foreach (var updateCompany in updateCompanys) { 
        //        var oldCompanyInfo = subCompanys_old.Find(c => c.Id == updateCompany.Id);
        //        if (oldCompanyInfo != null)
        //        {
        //            if (oldCompanyInfo.CompanyName != updateCompany.CompanyName || oldCompanyInfo.CreditCode != updateCompany.CreditCode)
        //            {
        //                auditSubCompanys.Add(updateCompany.MappingTo<crm_addsubcompany_audit_subcompany>());
        //                splitSubcompanys.Add(new crm_splitcompany_audit_companylist()
        //                {
        //                    Id= Guid.NewGuid().ToString(),
        //                    SplitCompanyAuditId = splitAuditId,
        //                    CustomerId = crmCustomer.Id,
        //                    CustomerSubCompanyId = oldCompanyInfo.Id,
        //                    CreateUser = user,
        //                    CreateDate = nowDate
        //                });
        //                updateOriginCompanys_sub.Add(oldCompanyInfo);
        //                updateOriginCompanys.Add(oldCompanyInfo);
        //            }
        //            else {
        //                updateSubCompanys.Add(updateCompany);
        //            }
        //        }
        //    }
        //    auditSubCompanys.ForEach(c => {
        //        c.AddSubCompanyAuditId = auditId;
        //        c.CustomerSubCompanyId = Guid.NewGuid().ToString();
        //        c.Id = Guid.NewGuid().ToString();
        //        c.CreateUser = user;
        //        c.CreateDate = nowDate;
        //    });
        //    updateSubCompanys.ForEach(c => {
        //        c.UpdateUser = user;
        //        c.UpdateDate = nowDate;
        //    });
        //    var hasSubAudit = auditSubCompanys.Count > 0;
        //    if (hasSubAudit)
        //    {
        //        auditSubCompanys.ForEach(c => {
        //            c.AddSubCompanyAuditId = auditId;
        //            c.Id = Guid.NewGuid().ToString();
        //            c.CreateUser = user;
        //            c.CreateDate = nowDate;
        //        });
        //        audit = new crm_addsubcompany_audit()
        //        {
        //            Id = auditId,
        //            CustomerId = crmCustomer.Id,
        //            ApplicantId = user,
        //            ApplicantDate = nowDate,
        //            CreateUser = user,
        //            CreateDate = nowDate
        //        };
        //    }
        //    var hasSplitAudit = splitSubcompanys.Count > 0;
        //    if (hasSubAudit)
        //    {
        //        splitAudit = new crm_splitcompany_audit()
        //        {
        //            Id = auditId,
        //            CustomerId = crmCustomer.Id,
        //            ApplicantId = user,
        //            ApplicantDate = nowDate,
        //            CreateUser = user,
        //            CreateDate = nowDate
        //        };
        //    }
        //    #endregion

        //    #region 获取需要排重的所有公司名称/代码
        //    var crmCustomerCompanys = newSubCompanys.ToList();
        //    crmCustomerCompanys.Add(mainCompany);
        //    #endregion

        //    #region 私有池排重
        //    DbOpe_crm_customer_privatepool.Instance.CheckCompanyExist(crmCustomerCompanys,new List<string>() { crmCustomer.Id });
        //    #endregion

        //    #region 公有池排重
        //    DbOpe_crm_customer_publicpool.Instance.CheckCompanyExist(crmCustomerCompanys, new List<string>() { crmCustomer.Id });
        //    #endregion

        //    #region 个人临时池排重
        //    DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(crmCustomerCompanys, new List<string>() { crmCustomer.Id });
        //    #endregion

        //    //更新
        //    DbOpe_crm_customer.Instance.UpdateQueue(crmCustomer);
        //    DbOpe_crm_customer_subcompany.Instance.UpdateQueue(mainCompany);
        //    DbOpe_crm_customer_subcompany.Instance.UpdateQueue(updateSubCompanys);
        //    if (hasSubAudit)
        //    {
        //        DbOpe_crm_addsubcompany_audit.Instance.UpdateQueue(audit);
        //        DbOpe_crm_addsubcompany_audit_subcompany.Instance.UpdateQueue(auditSubCompanys);
        //    }
        //    if (hasSplitAudit)
        //    {
        //        DbOpe_crm_splitcompany_audit.Instance.UpdateQueue(splitAudit);
        //        DbOpe_crm_splitcompany_audit_companylist .Instance.UpdateQueue(splitSubcompanys);
        //    }
        //    //刷新主公司相关的失效信息  （新加的子公司没审核呢 不刷）
        //    DbOpe_crm_customer_subcompany.Instance.RefreshVaildCompanys(new List<crm_customer_subcompany>() { mainCompany }, EnumCompanyValid.INVALID, true);

        //    //如果主/子公司名称变了 把对应原来名字的客户重新置为有效 并且子公司要执行拆分动作
        //    DbOpe_crm_customer_subcompany.Instance.RefreshVaildCompanys(updateOriginCompanys, EnumCompanyValid.VALID, true);
        //    DbOpe_crm_customer_subcompany.Instance.ExcuteSplitCompanys(crmCustomer.Id, updateOriginCompanys_sub, true);

        //    //刷新客户唯一表绑定关系 
        //    DbOpe_crm_customer_unique.Instance.RefreshUnique(new List<crm_customer_subcompany>() { mainCompany }, crmCustomer.Id, true);

        //    //执行
        //    DbOpe_crm_customer_subcompany.Instance.SaveQueues();
        //}

        #endregion

        #region 其他
        /// <summary>
        /// 保留公司后，作废该公司相关的采购主体关联提醒
        /// </summary>
        /// <param name="checkCompanyUniqueInfos"></param>
        public void VoidCompanyRelated(List<CheckCompanyUniqueInfo> checkCompanyUniqueInfos)
        {
            if (checkCompanyUniqueInfos != null && checkCompanyUniqueInfos.Count > 0)
            {
                DbOpe_crm_customer_subcompany_related.Instance.VoidCompanyRelated(checkCompanyUniqueInfos);
                DbOpe_crm_customer_subcompany_related_temporary.Instance.VoidCompanyRelated(checkCompanyUniqueInfos);
            }
        }
        /// <summary>
        /// 恢复公司相关的作废的采购主体关联提醒
        /// </summary>
        /// <param name="checkCompanyUniqueInfos"></param>
        public void ReactiveCompanyRelated(List<CheckCompanyUniqueInfo> checkCompanyUniqueInfos)
        {
            if (checkCompanyUniqueInfos != null && checkCompanyUniqueInfos.Count > 0)
            {
                DbOpe_crm_customer_subcompany_related.Instance.ReactiveCompanyRelated(checkCompanyUniqueInfos);
                DbOpe_crm_customer_subcompany_related_temporary.Instance.ReactiveCompanyRelated(checkCompanyUniqueInfos);
            }
        }

        /// <summary>
        /// 验证客户是否锁定（有合同，不能进行释放等操作）
        /// </summary>
        /// <param name="customerIds"></param>
        /// <param name="exception">直接抛出异常</param>
        public List<string> CheckCustomerLock(List<string> customerIds, bool exception = true)
        {
            return DbOpe_crm_customer.Instance.CheckCustomerLock(customerIds, exception);
        }
        /// <summary>
        /// 检查客户信息是否需要补录
        /// </summary>
        /// <param name="customerIds"></param>
        /// <returns></returns>
        public void CheckCustomerInfoNeedSupplementary(List<string> customerIds)
        {
            var r = DbOpe_crm_customer.Instance.CheckCustomerInfoNeedSupplementaryCustomerNames(customerIds);
            if (r.Count != 0)
            {
                throw new ApiException("客户信息不完整，需要先补录客户信息（" + r.JoinToString() + ")");
            }
        }
        /// <summary>
        /// 公开客户的相关跟踪记录和日志
        /// </summary>
        /// <param name="customerIds"></param>
        public void OpenCustomerLog(List<string> customerIds)
        {
            var logs = DbOpe_crm_trackingrecord.Instance.GetTrackingRecordNumberByCustomerId(customerIds, UserId);
            var schedules = DbOpe_crm_schedule.Instance.GetScheduleByCustomerId(customerIds, UserId);
            logs.ForEach(l => { l.IsVisible = 1; });
            schedules.ForEach(l => { l.IsVisible = 1; });
            DbOpe_crm_trackingrecord.Instance.Update(logs);
            DbOpe_crm_schedule.Instance.Update(schedules);
        }
        /// <summary>
        /// 获取客户下的公司列表
        /// </summary>
        /// <param name="customerId_IN"></param>
        /// <returns></returns>
        public List<Company_OUT> SearchCustomerCompanyList(CommonCustomerId_IN customerId_IN)
        {
            var customerInfo = DbOpe_crm_customer.Instance.GetCustomerOutInfoById(customerId_IN.CustomerId, currentUser, customerId_IN.Temp);
            var list = customerInfo.SubCompanys.ToList();
            list.Add(customerInfo);
            return list;
        }
        /// <summary>
        /// 获取正式客户唯一信息
        /// </summary>
        /// <returns></returns>
        public CustomerUniqueInfo GetUniqueCustomerInfo(CheckCompanyUniqueInfo checkCompanyUniqueInfo, ref bool exist)
        {
            return DbOpe_crm_customer.Instance.GetUniqueCustomerInfo(checkCompanyUniqueInfo, ref exist);
        }
        /// <summary>
        /// 生成保护截止日期
        /// </summary>
        /// <returns></returns>
        private DateTime FormatProtectDate()
        {
            return DateTime.Now.AddDays(GetPrivateGetProtectDay());
        }
        /// <summary>
        /// 私有池保护天数
        /// </summary>
        /// <returns></returns>
        private int GetPrivateGetProtectDay()
        {
            return DbOpe_sys_comparam.Instance.GetComparams("PrivateProtectionTime");
        }
        /// <summary>
        /// 私有池延期天数
        /// </summary>
        /// <returns></returns>
        private int GetProtectDelayDay()
        {
            return DbOpe_sys_comparam.Instance.GetComparams("PrivatePostponementsDay");
        }
        /// <summary>
        /// 获取每日领取次数
        /// </summary>
        /// <returns></returns>
        private int GetPublicGetNumPerDay()
        {
            return DbOpe_sys_comparam.Instance.GetComparams("PublicDayClaimNum");
        }
        /// <summary>
        /// 获取系统冻结时间
        /// </summary>
        /// <returns></returns>
        private int GetCustomerDisableDays()
        {
            return DbOpe_sys_comparam.Instance.GetComparams("PrivateReleaseClaimDays");
        }
        /// <summary>
        /// 生成慧思ID
        /// </summary>
        /// <returns></returns>
        private string FormatHSId()
        {
            return DbOpe_crm_customer.Instance.FormatHSId();
        }
        /// <summary>
        /// 修改客户表跟踪阶段信息
        /// </summary>
        /// <param name="updateParams">待更新的Map,键为客户表Id,值为跟踪阶段值</param>
        public void ModifyCustomerTrackingStageInfo(Dictionary<string, EnumTrackingStage> updateParams)
        {
            if (updateParams == null || updateParams.Count == 0)
            {
                return;
            }
            var dtList = new List<Dictionary<string, object?>>();
            Dictionary<string, object?> dt = new Dictionary<string, object?>();
            foreach ((string customerId, EnumTrackingStage stage) in updateParams)
            {
                dt = new Dictionary<string, object?>();
                dt.Add("Id", customerId);
                dt.Add("TrackingStage", stage.ToIntNullable());
                dt.Add("UpdateUser", UserTokenInfo.id);
                dt.Add("UpdateDate", DateTime.Now);
                dtList.Add(dt);
            }
            DbOpe_crm_customer.Instance.ModifyCustomerTrackingStageInfo(dtList);
        }
        /// <summary>
        /// 删除资源池同名资源
        /// </summary>
        /// <param name="newCustomers"></param>
        public void CheckAndDeleteSourceCustomer(List<CheckCompanyUniqueInfo> newCustomers)
        {
            var names = newCustomers.Select(c => c.CompanyName).ToList();
            DbOpe_crm_customer_canton_fair.Instance.DeleteByName(names);
            DbOpe_crm_customer_china_exporter.Instance.DeleteByName(names);
        }
        /// <summary>
        /// 恢复资源池同名资源
        /// </summary>
        /// <param name="companyName"></param>
        public void ActiveSourceCustomer(string companyName)
        {
            DbOpe_crm_customer_canton_fair.Instance.Active(companyName);
            DbOpe_crm_customer_china_exporter.Instance.Active(companyName);
        }
        public string testsw1()
        {
            if (HttpContextAccessorHolder.HttpContextAccessor?.HttpContext == null)
            {
                return "未知IP地址";
            }
            
            return HttpContextAccessorHolder.HttpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "未知IP地址";
        }
        /// <summary>
        /// 添加代付款公司  //20250116处理代付款公司重名的情况（都是代付款公司，不在池子）
        /// </summary>
        /// <param name="companyName"></param>
        /// <returns></returns>
        public string AddPaymentCompany(string companyName)
        {
            CheckInPrivatePoolData checkInPrivatePoolData = new CheckInPrivatePoolData();
            //其他人保留的同名公司不添加
            var privateExist = DbOpe_crm_customer_privatepool.Instance.CheckInPool(companyName, ref checkInPrivatePoolData);
            if (privateExist)
            {
                if (checkInPrivatePoolData.UserId != currentUser)
                {
                    throw new Exception("该代付款公司已保留，不能作为代付款公司");
                }
                else
                {
                    //如果是当前用户保留的公司，则直接返回公司Id
                    return checkInPrivatePoolData.CompanyId;
                }
            }
            else
            {
                string companyId = "";
                //如果是公有库的公司，则使用公有库的公司Id
                var publicExist = DbOpe_crm_customer_publicpool.Instance.CheckInPool(companyName, ref companyId);
                if (publicExist)
                {
                    return companyId;
                }
                else
                {
                    //20250116处理代付款公司重名的情况（都是代付款公司，不在池子）
                    //私有/公有都没找到，要在代付款公司里找同名公司，如果有同名公司，则直接返回该公司的Id
                    var exist = DbOpe_crm_customer_subcompany.Instance.GetData(c => c.CompanyName == companyName
                        && c.CustomerId == Guid.Empty.ToString()
                        && c.IsValid == (int)EnumCompanyValid.INVALID
                        && c.IsMain == (int)EnumCustomerCompanyMain.Sub
                    );
                    if (exist != null)
                    {
                        return exist.Id;
                    }
                    else
                    {
                        //公司库里没找到，新建一个公司信息（标记为失效）
                        companyId = Guid.NewGuid().ToString();
                        DbOpe_crm_customer_subcompany.Instance.Insert(new Db_crm_customer_subcompany()
                        {
                            Id = companyId,
                            CustomerId = Guid.Empty.ToString(),
                            CompanyName = companyName,
                            CreditType = (int)EnumCompanyType.Other,
                            IsValid = (int)EnumCompanyValid.INVALID,
                            Country = LocalCache.LC_Address.CountryCache.FirstOrDefault(c => c.Name == "中国").Id,
                            Province = LocalCache.LC_Address.ProvinceCache.FirstOrDefault(c => c.Name == "北京市").Id,
                            City = LocalCache.LC_Address.CityCache.FirstOrDefault(c => c.Name == "北京市").Id,
                            IsMain = (int)EnumCustomerCompanyMain.Sub
                        });
                        return companyId;
                    }
                }
            }

        }
        #endregion

        #region 仲裁
        /// <summary>
        /// 申请仲裁
        /// </summary>
        /// <param name="customerArbitrate_In"></param>
        public void CustomerArbitrateApply(CustomerArbitrateApply_In customerArbitrate_In)
        {
            Db_crm_customer_arbitrate obj = new Db_crm_customer_arbitrate();
            obj.UserIds = string.Join(',', customerArbitrate_In.UserIdList);
            obj.Content = customerArbitrate_In.Content;
            obj.State = EnumArbitrateState.Apply;
            DbOpe_crm_customer_arbitrate.Instance.InsertData(obj);
        }

        /// <summary>
        /// 查询仲裁的客户列表
        /// </summary>
        /// <param name="query_In"></param>
        /// <returns></returns>
        public List<QueryCustomer_OUT> SearchArbitrateCompany(SearchArbitrateCompany_In query_In)
        {
            var user = currentUser;
            var list = DbOpe_crm_customer_privatepool.Instance.SearchArbitrateCompany(query_In);

            var customerIds = list.Select(l => l.Id).ToList();
            var checkL = CheckAuditInProcess(customerIds, false);
            var getL = DbOpe_crm_customer_privatepool.Instance.QueryCustomerGetNum(customerIds);
            var releaseL = CheckCustomerLock(customerIds, false);
            var gtisC = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetGtisServiceCompanys();
            list.ForEach(l =>
            {
                l.AuditFlag = checkL.Contains(l.Id);
                l.LockFlag = releaseL.Contains(l.Id);
                l.GetTimes = !getL.ContainsKey(l.Id) ? 0 : getL[l.Id];
                l.SubCompanys.ForEach(c =>
                {
                    c.GtisFlag = gtisC.Contains(c.Id);
                });
                l.GtisFlag = l.SubCompanys.Find(c => c.GtisFlag) != null || gtisC.Contains(l.MainCompanyId);
            });
            return list;
        }

        /// <summary>
        /// 仲裁客户归属
        /// </summary>
        /// <param name="arbCusBelong_In"></param>
        public void ArbitrateCustomerRight(ArbitrateCustomerBelong_In arbCusBelong_In)
        {
            //移交客户
            TransferPrivatePoolAdmin(arbCusBelong_In.MappingTo<TransferPrivateCustomer_IN>());
            //移交业绩
            if (arbCusBelong_In.ReceiptRegisterIds != null || arbCusBelong_In.ReceiptRegisterIds.Count > 0)
            {
                //销售 Issuer  战队 OrgDivisionName OrgDivisionId 大队 OrgBrigadeName OrgBrigadeId 中队 OrgRegimentName OrgRegimentId
                var Issuer = arbCusBelong_In.RecipientUserId;
                var OrgRegimentName = string.Empty;
                var OrgRegimentId = string.Empty;
                var OrgBrigadeName = string.Empty;
                var OrgBrigadeId = string.Empty;
                var OrgDivisionName = string.Empty;
                var OrgDivisionId = string.Empty;

                var isuserOrgId = DbOpe_sys_user.Instance.QueryByPrimaryKey(Issuer).OrganizationId;
                var orgTree = DbOpe_sys_organization.Instance.GetBranchUpOrgList(isuserOrgId).First();
                while (orgTree != null)
                {
                    if (orgTree.Id != Guid.Empty.ToString())
                        orgTree = orgTree.Child.First();
                    else if (orgTree.OrgType == EnumOrgType.BattleTeam)
                    {
                        OrgDivisionName = orgTree.OrgName;
                        OrgDivisionId = orgTree.Id;
                        orgTree = orgTree.Child.First();
                    }
                    else if (orgTree.OrgType == EnumOrgType.Battalion)
                    {
                        OrgRegimentName = orgTree.OrgName;
                        OrgRegimentId = orgTree.Id;
                        orgTree = orgTree.Child.First();
                    }
                    else if (orgTree.OrgType == EnumOrgType.Squadron)
                    {
                        OrgBrigadeName = orgTree.OrgName;
                        OrgBrigadeId = orgTree.Id;
                        orgTree = null;
                    }
                }
                arbCusBelong_In.ReceiptRegisterIds.ForEach(recRegId =>
                {
                    var r = DbOpe_crm_contract_receiptregister.Instance.QueryByPrimaryKey(recRegId);
                    r.Issuer = Issuer;
                    r.OrgRegimentName = OrgRegimentName;
                    r.OrgRegimentId = OrgRegimentId;
                    r.OrgBrigadeName = OrgBrigadeName;
                    r.OrgBrigadeId = OrgBrigadeId;
                    r.OrgDivisionName = OrgDivisionName;
                    r.OrgDivisionId = OrgDivisionId;
                    DbOpe_crm_contract_receiptregister.Instance.UpdateData(r);
                });



            }
        }
        #endregion


        #region 导入资源池

        /// <summary>
        /// 获取导入客户列表
        /// </summary>
        public List<SearchImportCustomer_OUT> SearchImportCustomerList(SearchImportCustomer_IN searchImportCustomer_IN, ref int total)
        {
            return DbOpe_crm_customer_import.Instance.SearchImportCustomerList(searchImportCustomer_IN, ref total);
        }
        /// <summary>
        /// 获取导入客户详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SearchImportCustomer_OUT GetImportCustomerInfo(string id)
        {
            return DbOpe_crm_customer_import.Instance.GetImportCustomerInfo(id);
        }

        /// <summary>
        /// 获取Execl数据
        /// </summary>
        /// <param name="file">execl</param>
        /// <returns></returns>
        public List<ExcelCustomerIn> ExcelCutomerIn(IFormFile file)
        {
            // 获取上传文件后缀
            string ext = Path.GetExtension(file.FileName).ToLower();
            if (!ext.Contains("xls") && !ext.Contains("xlsx"))
            {
                throw new ApiException("文件有误，只支持上传XLS、XLSX文件");
            }
            // 限制单次只能上传20M
            float fileSize = file.Length / 1024 / 1024;
            if (fileSize > 20)
            {
                throw new ApiException("文件大小超过限制(20M)");
            }
            try
            {
                // 文件流处理
                MemoryStream ms = new MemoryStream();
                file.CopyTo(ms);
                ms.Seek(0, SeekOrigin.Begin);
                // 根据Excel版本进行处理
                IWorkbook workbook = ext == ".xls" ? (IWorkbook)new HSSFWorkbook(ms) : new XSSFWorkbook(ms);
                // 获取Excel第一张工作簿
                ISheet sheet = workbook.GetSheetAt(0);
                // 获取数据行数
                int num = sheet.LastRowNum;
                // 数据
                List<ExcelCustomerIn> infos = new List<ExcelCustomerIn>();
                ExcelCustomerIn exampleInfo = new ExcelCustomerIn();
                //字段和对应的列索引
                List<ExcelCustomerIn_Relation> relations = new List<ExcelCustomerIn_Relation>();

                //生产所需字段和对应的DescriptionAttribute
                PropertyInfo[] propertyInfos = exampleInfo.GetType().GetProperties();
                foreach (var propertyInfo in propertyInfos)
                {
                    var descriptionAttribute = Attribute.GetCustomAttribute(propertyInfo, typeof(DescriptionAttribute)) as DescriptionAttribute;
                    if (descriptionAttribute == null)
                    {
                        continue;
                    }
                    relations.Add(new ExcelCustomerIn_Relation()
                    {
                        FieldName = propertyInfo.Name,
                        RelatedColNames = descriptionAttribute.Description.Split(",").ToList()
                    });
                }

                //先找到标题行 
                int? headRowIndex = null;
                for (int i = 0; i < num; i++)
                {
                    var row = sheet.GetRow(i);
                    if (row == null) { continue; }
                    for (int j = 0; j < row.LastCellNum; j++)
                    {
                        var cell = row.GetCell(j).ToString();
                        if (!string.IsNullOrEmpty(cell))
                        {
                            var findRelation = relations.Find(r => r.RelatedColNames.Contains(cell) && r.FieldName == "CompanyName");
                            if (findRelation != null)
                            {
                                headRowIndex = i;
                                findRelation.ColIndex = j;
                                break;
                            }
                        }
                    }
                    if (headRowIndex != null)
                    {
                        break;
                    }
                }

                if (headRowIndex == null)
                {
                    throw new ApiException("找不到标题行");
                }



                //找到各个字段对应的列索引
                var headRow = sheet.GetRow(headRowIndex.Value);
                for (int j = 0; j < headRow.LastCellNum; j++)
                {
                    var cell = headRow.GetCell(j).ToString();
                    if (!string.IsNullOrEmpty(cell))
                    {
                        var findRelation = relations.Find(r => r.RelatedColNames.Contains(cell));
                        if (findRelation != null)
                        {
                            findRelation.ColIndex = j;
                        }
                    }
                }


                for (int i = headRowIndex.Value + 1; i <= num; i++)
                {
                    // 获取指定行数据
                    IRow row = sheet.GetRow(i);
                    if (row == null) { continue; }
                    ExcelCustomerIn info = new ExcelCustomerIn();
                    // 获取所有找到索引的字段，逐个赋值
                    foreach (var relation in relations.Where(r => r.ColIndex != null).ToList())
                    {
                        info.GetType().GetProperty(relation.FieldName).SetValue(info, row.GetCell(relation.ColIndex.Value)?.ToString());
                    }
                    infos.Add(info);
                }
                return infos;
            }
            catch (Exception e)
            {
                throw new ApiException("数据处理出错");
            }
        }

        /// <summary>
        /// 导入资源池
        /// </summary>
        /// <param name="allData">data</param>
        /// <param name="customerSource">customerSource</param>
        /// <returns></returns>
        public void ImportExcelCustomer(List<ExcelCustomerIn> allData, EnumExcelCustomerSource customerSource)
        {
            // 检查数据是否为空
            if (allData == null || !allData.Any())
            {
                throw new ApiException("读取到数据为空");
            }
            // 获取排重用的数据
            var existCompanys = DbOpe_crm_customer_import.Instance.GetDataList(i => i.Deleted == false);
            var existContacts = DbOpe_crm_customer_import_contacts.Instance.GetDataList(i => i.Deleted == false);

            // 每200条数据为一组进行迭代
            for (int i = 0; i < allData.Count; i += 200)
            {
                // 计算每组数据的结束索引（注意防止超出集合范围）
                int endIndex = Math.Min(i + 200, allData.Count);

                // 获取当前组的数据
                var batchData = allData.Skip(i).Take(endIndex - i).ToList();

                // 对当前组数据进行处理并插入

                ImportBatchData(batchData, customerSource, ref existCompanys, ref existContacts);
            }

        }
        /// <summary>
        /// 分组插入数据
        /// </summary>
        /// <param name="batchData"></param>
        /// <param name="customerSource"></param>
        /// <param name="existCompanys"></param>
        /// <param name="existContacts"></param>
        private void ImportBatchData(List<ExcelCustomerIn> batchData, EnumExcelCustomerSource customerSource, ref List<Db_crm_customer_import> existCompanys, ref List<Db_crm_customer_import_contacts> existContacts)
        {
            List<Db_crm_customer_import> db_crm_customer_imports = new List<Db_crm_customer_import>();
            List<Db_crm_customer_import_contacts> db_crm_customer_import_contacts = new List<Db_crm_customer_import_contacts>();
            foreach (var item in batchData)
            {
                item.Source = customerSource;
                var findExistCompany = existCompanys.Find(i => i.CompanyName == item.CompanyName);
                if (findExistCompany != null)
                {
                    //公司已存在 检查联系人
                    var findExistContact = existContacts.Find(i =>
                    i.CompanyId == findExistCompany.Id
                    && i.Phone == item.Phone
                    && i.Job == item.Job
                    && i.ContactName == item.ContactName
                    && i.Tel == item.Tel
                    && i.Email == item.Email
                    );
                    if (findExistContact == null)
                    {
                        var newContact = item.MappingTo<Db_crm_customer_import_contacts>();
                        newContact.Id = Guid.NewGuid().ToString();
                        newContact.CompanyId = findExistCompany.Id;
                        db_crm_customer_import_contacts.Add(newContact);
                        existContacts.Add(newContact);
                    }
                }
                else
                {
                    //公司不存在 创建新公司并记录联系人
                    var newCompany = item.MappingTo<Db_crm_customer_import>();
                    newCompany.Id = Guid.NewGuid().ToString();
                    db_crm_customer_imports.Add(newCompany);
                    existCompanys.Add(newCompany);
                    var newContact = item.MappingTo<Db_crm_customer_import_contacts>();
                    newContact.Id = Guid.NewGuid().ToString();
                    newContact.CompanyId = newCompany.Id;
                    db_crm_customer_import_contacts.Add(newContact);
                    existContacts.Add(newContact);
                }
            }
            if (existCompanys.Any())
            {
                DbOpe_crm_customer_import.Instance.Insert(db_crm_customer_imports);
            }
            if (existContacts.Any())
            {
                DbOpe_crm_customer_import_contacts.Instance.Insert(db_crm_customer_import_contacts);
            }
        }
        #endregion

        /// <summary>
        /// 获取当前请求的客户端IP地址
        /// </summary>
        /// <returns>客户端IP地址</returns>
        public string GetClientIpAddress()
        {
            if (HttpContextAccessorHolder.HttpContextAccessor?.HttpContext == null)
            {
                return "未知IP地址";
            }
            
            return HttpContextAccessorHolder.HttpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "未知IP地址";
        }

    }

}

