﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using Microsoft.AspNetCore.Http;
using QCloud;
using SqlSugar;
using System.Diagnostics.Contracts;
using System.IO;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;

namespace CRM2_API.BLL
{
    public class BLL_TradeLeads : BaseBLL<BLL_TradeLeads>
    {
        /// <summary>
        /// 保存附件并返回相应的实体列表[未对数据库进行操作,实体列表需要插入到数据库中]
        /// </summary>
        /// <param name="tradeId">工作报告表</param>
        /// <param name="attachFiles">附件</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public List<Db_crm_tradeleads_attachfile> saveAttachfiles(string tradeId, IFormFileCollection? attachFiles)
        {
            if (string.IsNullOrEmpty(tradeId))
            {
                throw new ArgumentNullException("工作报告表主键不可为空!");
            }

            List<Db_crm_tradeleads_attachfile> FileList = null;
            Db_crm_tradeleads_attachfile attachfile;
            if (attachFiles is not null && attachFiles.Count > 0)
            {
                FileList = new List<Db_crm_tradeleads_attachfile>(attachFiles.Count);
                foreach (IFormFile file in attachFiles)
                {
                    //string fileSavePath = AppSettings.fileSavePath ?? "/FileUpload";
                    string AttachFileId = Guid.NewGuid().ToString();

                    string fileSavePath = "/FileUpload";
                    string fileHead = $"{fileSavePath}/TradeLeads/{DateTime.Now:yyyyMMdd}";
                    //设置文件上传路径
                    string fileName = file.FileName;
                    String fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
                    //string fullFileName = $"{fileHead}/{Path.GetFileName(file.FileName)}";
                    string fullFileName = $"{fileHead}/" + AttachFileId;


                    if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                    {
                        using (Stream stream = file.OpenReadStream())
                        {
                            QCloudOperator qCloud = new QCloudOperator();
                            qCloud.UploadStream(fullFileName, stream, file.ContentType);
                            //将信息写入到附件表中
                            attachfile = new Db_crm_tradeleads_attachfile
                            {
                                //Id = Guid.NewGuid().ToString(),
                                Id = AttachFileId,
                                Type = 0,
                                TradeLeadsId = tradeId,
                                FileName = fileName,
                                FilePath = fullFileName,
                                FileExtension = fileExtension,
                                FileSize = (int)file.Length,
                                Deleted = false,
                                CreateUser = TokenModel.Instance.id,
                                CreateDate = DateTime.Now
                            };
                            FileList.Add(attachfile);
                        }
                    }
                    else
                    {
                        ////创建文件夹，保存文件
                        string? path = Path.GetDirectoryName(fullFileName);
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }

                        //保存文件  文件存在则先删除原来的文件
                        if (File.Exists(fullFileName))
                        {
                            File.Delete(fullFileName);
                        }

                        //将流写入文件
                        using Stream stream = file.OpenReadStream();
                        // 把 Stream 转换成 byte[]
                        byte[] bytes = new byte[stream.Length];
                        stream.Read(bytes, 0, bytes.Length);
                        // 设置当前流的位置为流的开始
                        stream.Seek(0, SeekOrigin.Begin);
                        // 把 byte[] 写入文件
                        using FileStream fs = new(fullFileName, FileMode.Create);
                        using BinaryWriter bw = new(fs);
                        bw.Write(bytes);
                        //将信息写入到附件表中
                        attachfile = new Db_crm_tradeleads_attachfile
                        {
                            //Id = Guid.NewGuid().ToString(),
                            Id = AttachFileId,
                            Type = 0,
                            TradeLeadsId = tradeId,
                            FileName = fileName,
                            FilePath = fullFileName,
                            FileExtension = fileExtension,
                            FileSize = (int)file.Length,
                            Deleted = false,
                            CreateUser = TokenModel.Instance.id,
                            CreateDate = DateTime.Now
                        };
                        FileList.Add(attachfile);
                    }
                }
            }
            return FileList;
        }

        /// <summary>
        /// 合并调整过期和发送提醒方法，一次操作，不分开两次任务
        /// </summary>
        public void SendIncomingExpiredMessgage()
        {
            //过期的更新为关闭
            var hasexpiredList = DbOpe_crm_tradeleads.Instance.GetIncomingExpiredList(false);
            if (hasexpiredList != null)
            {
                hasexpiredList.ForEach(it =>
                {
                    it.State = 1;
                    DbOpe_crm_tradeleads.Instance.UpdateData(it);
                });
            }
            var needsendMessageList = DbOpe_crm_tradeleads.Instance.GetIncomingExpiredList(true);
            if (needsendMessageList != null)
            {
                needsendMessageList.ForEach(item =>
                {
                    MessageMainInfo message = new MessageMainInfo();
                    message.Issuer = item.CreateUser;
                    message.MessageTypeToId = item.Id;
                    message.MessagemMainAboutDes = item.TradeTitle;

                    MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.TradeleadsAbout, EnumMessageStepInfo.Release, EnumMessageStateInfo.NearlyRelease);
                    //BLL_MessageCenter.Instance.RealTimeSend(giveBack);
                });
            }
        }
    }
}
