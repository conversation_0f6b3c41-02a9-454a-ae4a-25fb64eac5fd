﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///公共参数表(字典表方式)
    ///</summary>
    [SugarTable("sys_comparam")]
    public class Db_sys_comparam
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:参数主键
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ParamKey { get; set; }

        /// <summary>
        /// Desc:参数值
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ParamValue { get; set; }

        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Remark { get; set; }

        /// <summary>
        /// Desc:分类
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Type { get; set; }

        /// <summary>
        /// Desc:继承的id，当前记录生效会使父记录失效
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Inherit { get; set; }

        /// <summary>
        /// Desc:等待生效,为1时则为等待生效.0或NULL则无意义
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? WaitEffec { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// Desc:生效时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? TakingEffectTime { get; set; }

        /// <summary>
        /// Desc:失效时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? ExpirationTime { get; set; }

        public override bool Equals(object? obj)
        {
            return obj is Db_sys_comparam comparam &&
                   Id == comparam.Id;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Id);
        }
    }
}
