﻿using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using System.Data;
using System.Linq.Dynamic.Core;
using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Presentation;
using static Lucene.Net.Util.Fst.Util;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.VariantTypes;
using System.Diagnostics.Contracts;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using DocumentFormat.OpenXml.Spreadsheet;

namespace CRM2_API.BLL.Common
{
    public class AutoAuditReviewRules
    {
        public static bool IsAutoAuditCheck(string ContractId, Contract_In contractIn, PaymentInfo_In paymentInfo, List<ProductInfo_In> productInfoIn, List<Db_sys_review_rules> rules, string parentId, string userId)
        {
            bool result = true;
            List<Db_sys_review_rules> list = rules.Where(r => r.ParentId == parentId).ToList();
            foreach (Db_sys_review_rules rule in list)
            {
                bool isCheck = ConditionCheck(ContractId, contractIn, paymentInfo, productInfoIn, rule.RulesKey, rule.RulesValue, userId);
                if (rule.Type == EnumLogicalOperator.And.ToInt())
                {
                    if (isCheck)
                    {
                        result = IsAutoAuditCheck(ContractId, contractIn, paymentInfo, productInfoIn, rules, rule.Id, userId);
                        if (result == false)
                        {
                            break;
                        }
                    }
                    else
                    {
                        result = false;
                        break;
                    }
                }
                if (rule.Type == EnumLogicalOperator.Or.ToInt())
                {
                    if (isCheck)
                    {
                        result = IsAutoAuditCheck(ContractId, contractIn, paymentInfo, productInfoIn, rules, rule.Id, userId);
                        break;
                    }
                    else
                    {
                        result = false;
                    }
                }
                //result = IsAutoAuditCheck(addContractIn, rules, rule.Id);
            }
            return result;
        }

        private static bool ConditionCheck(string ContractId, Contract_In addContractIn, PaymentInfo_In paymentInfo, List<ProductInfo_In> productInfoIn, string rulesKey, string rulesValue, string userId)
        {
            bool result = false;
            List<Contract_In> datalist = new List<Contract_In>() { addContractIn };
            List<PaymentInfo_In> paymentInfoList = new List<PaymentInfo_In>() { paymentInfo };
            if (rulesKey == "ContractCondition")
            {
                if (datalist.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    result = true;
                }
            }
            if (rulesKey == "PaymentInfoCondition")
            {
                if (paymentInfoList.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    result = true;
                }
            }
            if (rulesKey == "ProductCount")
            {
                if (productInfoIn.Count == rulesValue.ToInt())
                {
                    result = true;
                }
            }
            if (rulesKey == "ProductCondition")
            {
                if (productInfoIn.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    result = true;
                }
            }
            if (rulesKey == "ProductCondition_NotContain")
            {
                if (productInfoIn.AsQueryable().Where(rulesValue).ToList().Count() == 0)
                {
                    result = true;
                }
            }
            if (rulesKey == "AmountCompare")
            {
                if (datalist.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    result = true;
                }
                //decimal ContractAmount = addContractIn.ContractAmount.Value;
                //decimal ProductPrice = 0;
                //List<ProductInfo_In> productInfos = productInfoIn.AsQueryable().Where(rulesValue).ToList();
                //foreach (ProductInfo_In productInfo in productInfos)
                //{
                //    string ProductId = productInfo.ProductId.ToString();
                //    Db_sys_product_month_year monthYear = DbOpe_sys_product_month_year.Instance.GetData(r => r.Month == productInfo.OpeningMonths);
                //    if (monthYear == null)
                //    {
                //        throw new ApiException("当前合同选择的服务周期在所选产品中不存在,请维护相关产品价格中的服务周期");
                //    }
                //    Db_crm_product_price productPrice = DbOpe_crm_product_price.Instance.GetData(r => r.ProductId == ProductId && r.Currency == addContractIn.Currency);// && r.ServiceCycle == monthYear.Year);
                //    if (productPrice == null)
                //    {
                //        throw new ApiException("当前合同选择的币种在所选产品中不存在,请维护相关产品币种");
                //    }

                //    ProductPrice = ProductPrice + productPrice.Price;
                //}
                //if (ContractAmount == ProductPrice)
                //{
                //    result = true;
                //}
            }
            if (rulesKey == "AmountCompare_ProductType")
            {
                //decimal ContractAmount = addContractIn.ContractAmount.Value;
                //decimal ProductPrice = 0;
                List<ProductInfo_In> productInfos = productInfoIn.AsQueryable().Where(rulesValue).ToList();
                foreach (ProductInfo_In productInfo in productInfos)
                {
                    string ProductId = productInfo.ProductId.ToString();
                    if (productInfo.ProductType != EnumProductType.Periodicals && productInfo.ProductType != EnumProductType.Event)
                    {
                        //Db_sys_product_month_year monthYear = DbOpe_sys_product_month_year.Instance.GetData(r => r.Month == productInfo.OpeningMonths);
                        //if (monthYear == null)
                        //{
                        //    throw new ApiException("当前合同选择的服务周期在所选产品中不存在,请维护相关产品价格中的服务周期");
                        //}
                    }
                    Db_crm_product_price productPrice = DbOpe_crm_product_price.Instance.GetData(r => r.ProductId == ProductId && r.Currency == addContractIn.Currency);//&& r.ServiceCycle == monthYear.Year);
                    if (!(ProductId == Guid.Empty.ToString() && productInfo.ProductType == EnumProductType.Gtis))
                    {
                        if (productPrice == null)
                        {
                            throw new ApiException("当前合同选择的币种在所选产品中不存在,请维护相关产品币种");
                        }
                    }
                    //ProductPrice = ProductPrice + productPrice.Price;
                }
                if (productInfos.Count > 0)
                {
                    result = true;
                }
            }
            if (rulesKey == "AmountCompare_AmountRange")
            {
                decimal ContractAmount = 0;
                if (addContractIn.Currency != EnumCurrency.CNY.ToInt())
                {
                    ContractAmount = addContractIn.FCContractAmount.Value;
                }
                else
                {
                    ContractAmount = addContractIn.ContractAmount.Value;
                }

                decimal ProductPrice = 0;
                decimal ProductPriceMinimum = 0;
                List<ProductInfo_In> productInfos = productInfoIn.AsQueryable().ToList();
                foreach (ProductInfo_In productInfo in productInfos)
                {
                    string ProductId = productInfo.ProductId.ToString();
                    if (productInfo.ProductType != EnumProductType.Periodicals && productInfo.ProductType != EnumProductType.Event)
                    {
                        //Db_sys_product_month_year monthYear = DbOpe_sys_product_month_year.Instance.GetData(r => r.Month == productInfo.OpeningMonths);
                        //if (monthYear == null)
                        //{
                        //    throw new ApiException("当前合同选择的服务周期在所选产品中不存在,请维护相关产品价格中的服务周期");
                        //}
                    }
                    Db_crm_product_price productPrice = DbOpe_crm_product_price.Instance.GetData(r => r.ProductId == ProductId && r.Currency == addContractIn.Currency);// && r.ServiceCycle == monthYear.Year);
                    if (!(ProductId == Guid.Empty.ToString() && productInfo.ProductType == EnumProductType.Gtis))
                    {
                        if (productPrice == null)
                        {
                            throw new ApiException("当前合同选择的币种在所选产品中不存在,请维护相关产品币种");
                        }
                    }

                    //环球搜和慧思学院取最低价
                    if ((productInfo.ProductType == EnumProductType.Global || productInfo.ProductType == EnumProductType.GlobalWitsSchool|| productInfo.ProductType == EnumProductType.AdditionalResource) && addContractIn.Currency.Value == EnumCurrency.CNY.ToInt())
                    {
                        //List<Db_crm_product_price> productPriceList = DbOpe_crm_product_price.Instance.GetDataList(r => r.ProductId == ProductId && r.Currency == addContractIn.Currency).OrderBy(r=>r.Price).ToList();
                        List<ProductRules_Out> ProductRulesInfo = ProductRules.GetProductRules(productInfoIn, addContractIn.SalesCountry, addContractIn.Currency.Value, addContractIn.FirstParty.ToString());
                        //获取最低价格
                        List<ProductRules_Out> PRS = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).ToList();
                        if (addContractIn.IsOverseasCustomer && PRS.Count > 0)
                        {
                            ProductRulesPrice RulesPrice = PRS.First().PriceList.Where(r => r.Currency == addContractIn.Currency.Value).OrderBy(r => r.Price).First();
                            ProductPrice = ProductPrice + (RulesPrice.Price * productInfo.OpeningYears.Value);
                            if (productInfo.ProductType == EnumProductType.Global)
                            {
                                ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                                //List<ProductRulesPrice> pl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                                ////ProductPrice = ProductPrice + (pl.First().Price * productInfo.SubAccountsNum.Value);
                                //ProductPrice = ProductPrice + (productInfo.SubAccountsProductPrice * productInfo.SetNum.Value);
                                List<ProductRulesPrice> pl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value && r.Id == productInfo.SubAccountsProductPriceId.ToString()).ToList();
                                ProductPrice = (ProductPrice + ((pl.First().Price * (productInfo.SetNum == null ? 0 : productInfo.SetNum.Value)) * productInfo.OpeningYears.Value));
                                //********不再计算环球搜子账号价格
                                //int SubAccountsNum = productInfo.SubAccountsNum.Value;
                                //if (SubAccountsNum > 0)
                                //{
                                //    SubAccountsNum = SubAccountsNum - 1;
                                //}
                                //bool isHaveGtis = productInfos.Where(r => (r.ProductType == EnumProductType.Gtis && r.ProductId != Guid.Empty) || r.ProductType == EnumProductType.Combination).Any();
                                //if (!isHaveGtis)
                                //{
                                //    ProductPrice = ProductPrice + (pl.First().Price * SubAccountsNum);
                                //}
                                //********不再计算环球搜子账号价格
                            }
                        }
                        else
                        {
                            ProductPrice = ProductPrice + productInfo.ContractProductinfoPriceTotal;
                        }
                    }
                    else
                    {
                        ProductPrice = ProductPrice + productInfo.ContractProductinfoPriceTotal;
                    }

                    ProductPriceMinimum = ProductPriceMinimum + productInfo.ContractProductinfoPriceMinimumTotal;
                    //ProductPrice = ProductPrice + productInfo.ContractProductinfoPriceTotal; //(productPrice.Price * monthYear.Year.Value);
                }

                if ((ProductPrice - ContractAmount) == rulesValue.ToInt() && (ProductPrice - ContractAmount) == 0)
                {
                    result = true;
                }

                if (addContractIn.IsOverseasCustomer && (ProductPriceMinimum - ContractAmount) <= rulesValue.ToInt() && (ProductPriceMinimum - ContractAmount) <= 0)
                {
                    result = true;
                }
                //if ((productInfos.Where(r => r.ProductType == EnumProductType.Global || r.ProductType == EnumProductType.GlobalWitsSchool).Count() > 0) && addContractIn.Currency.Value == EnumCurrency.CNY.ToInt())
                //{
                //    //if (ContractAmount >= ProductPrice)
                //    //{
                //    //    result = true;
                //    //}

                //    if ((ProductPrice - ContractAmount) <= rulesValue.ToInt() && (ProductPrice - ContractAmount) <= 0)
                //    {
                //        result = true;
                //    }

                //    if ((ProductPriceMinimum - ContractAmount) <= rulesValue.ToInt() && (ProductPriceMinimum - ContractAmount) <= 0)
                //    {
                //        result = true;
                //    }
                //    //else if ((ContractAmount - ProductPrice) >= rulesValue.ToInt() && (ContractAmount - ProductPrice) >= 0)
                //    //{
                //    //    result = true;
                //    //}
                //}
                //else
                //{
                //    if ((ProductPrice - ContractAmount) <= rulesValue.ToInt() && (ProductPrice - ContractAmount) >= 0)
                //    {
                //        result = true;
                //    }
                //    else if ((ContractAmount - ProductPrice) <= rulesValue.ToInt() && (ContractAmount - ProductPrice) >= 0)
                //    {
                //        result = true;
                //    }

                //    if ((ProductPriceMinimum - ContractAmount) <= rulesValue.ToInt() && (ProductPriceMinimum - ContractAmount) >= 0)
                //    {
                //        result = true;
                //    }
                //    else if ((ContractAmount - ProductPriceMinimum) <= rulesValue.ToInt() && (ContractAmount - ProductPriceMinimum) >= 0)
                //    {
                //        result = true;
                //    }
                //}
                ////if ((ProductPrice - ContractAmount) <= rulesValue.ToInt() && (ProductPrice - ContractAmount) >= 0)
                ////{
                ////    result = true;
                ////}
                ////else if ((ContractAmount - ProductPrice) <= rulesValue.ToInt() && (ContractAmount - ProductPrice) >= 0)
                ////{
                ////    result = true;
                ////}
            }
            if (rulesKey == "AmountCompare_CheckProductAmount")
            {
                if (datalist.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    List<ProductRules_Out> ProductRulesInfo = ProductRules.GetProductRules(productInfoIn, addContractIn.SalesCountry, addContractIn.Currency.Value, addContractIn.FirstParty.ToString(),addContractIn.ParentContractId);
                    foreach (ProductInfo_In productInfo in productInfoIn)
                    {
                        //验证价格是否在范围内
                        ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                        bool checksign = true;
                        List<ProductRulesPrice> pl = p.PriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                        foreach (ProductRulesPrice prp in pl)
                        {
                            if (prp.PriceMode == EnumPriceMode.FixedAndStaringSale.ToInt())
                            {
                                //等于固定价格和范围内
                                if (productInfo.ContractProductinfoPrice >= prp.Price && productInfo.ContractProductinfoPrice <= prp.PricePart2)
                                {
                                    checksign = false;
                                    break;
                                }
                            }

                            if (prp.PriceMode == EnumPriceMode.Fixed.ToInt())
                            {
                                //等于固定价格
                                if (productInfo.ContractProductinfoPrice == prp.Price || productInfo.SubAccountsProductPrice == prp.Price)
                                {
                                    checksign = false;
                                    break;
                                }
                            }
                            if (prp.PriceMode == EnumPriceMode.StaringSale.ToInt())
                            {
                                //大于起售价格
                                if (productInfo.ContractProductinfoPrice >= prp.Price)
                                {
                                    checksign = false;
                                    break;
                                }
                            }
                            if (prp.PriceMode == EnumPriceMode.Interval.ToInt())
                            {
                                //在区间价格范围内
                                if (productInfo.ContractProductinfoPrice >= prp.Price && productInfo.ContractProductinfoPrice <= prp.PricePart2)
                                {
                                    checksign = false;
                                    break;
                                }
                            }
                            if (prp.PriceMode == EnumPriceMode.purchase.ToInt())
                            {
                                //等于起购复购
                                if (productInfo.ContractProductinfoPrice == prp.Price)
                                {
                                    checksign = false;
                                    break;
                                }
                            }
                            if (prp.PriceMode == EnumPriceMode.MainAccount.ToInt())
                            {
                                //等于主账号
                                if (productInfo.ContractProductinfoPrice >= prp.Price)
                                {
                                    checksign = false;
                                    break;
                                }
                            }
                            if (prp.PriceMode == EnumPriceMode.SubAccount.ToInt())
                            {
                                //等于子账号
                                if (productInfo.ContractProductinfoPrice == prp.Price)
                                {
                                    checksign = false;
                                    break;
                                }
                            }
                            if (prp.PriceMode == EnumPriceMode.Give.ToInt())
                            {
                                //等于赠送
                                if (productInfo.ContractProductinfoPrice == prp.Price)
                                {
                                    checksign = false;
                                    break;
                                }
                            }
                        }
                        if (checksign)
                        {
                            throw new ApiException("产品价格不在有效范围内");
                        }

                        if (p.SubaccountsPriceList != null)
                        {
                            decimal SubAccountsProductPrice = productInfo.SubAccountsProductPrice == null ? 0 : productInfo.SubAccountsProductPrice.Value;
                            bool checksignsub = true;
                            List<ProductRulesPrice> spl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                            foreach (ProductRulesPrice prp in spl)
                            {
                                if (prp.PriceMode == EnumPriceMode.FixedAndStaringSale.ToInt())
                                {
                                    //等于固定价格和范围内
                                    if (SubAccountsProductPrice >= prp.Price && SubAccountsProductPrice <= prp.PricePart2)
                                    {
                                        checksignsub = false;
                                        break;
                                    }
                                }
                                if (prp.PriceMode == EnumPriceMode.Fixed.ToInt())
                                {
                                    //等于固定价格
                                    if (SubAccountsProductPrice == prp.Price)
                                    {
                                        checksignsub = false;
                                        break;
                                    }
                                }
                                if (prp.PriceMode == EnumPriceMode.StaringSale.ToInt())
                                {
                                    //大于起售价格
                                    if (SubAccountsProductPrice >= prp.Price)
                                    {
                                        checksignsub = false;
                                        break;
                                    }
                                }
                                if (prp.PriceMode == EnumPriceMode.Interval.ToInt())
                                {
                                    //在区间价格范围内
                                    if (SubAccountsProductPrice >= prp.Price && SubAccountsProductPrice <= prp.PricePart2)
                                    {
                                        checksignsub = false;
                                        break;
                                    }
                                }
                                if (prp.PriceMode == EnumPriceMode.purchase.ToInt())
                                {
                                    //等于起购复购
                                    if (SubAccountsProductPrice == prp.Price)
                                    {
                                        checksignsub = false;
                                        break;
                                    }
                                }
                                if (prp.PriceMode == EnumPriceMode.MainAccount.ToInt())
                                {
                                    //等于主账号
                                    if (SubAccountsProductPrice >= prp.Price)
                                    {
                                        checksignsub = false;
                                        break;
                                    }
                                }
                                if (prp.PriceMode == EnumPriceMode.SubAccount.ToInt())
                                {
                                    //等于子账号
                                    if (SubAccountsProductPrice == prp.Price)
                                    {
                                        checksignsub = false;
                                        break;
                                    }
                                }
                                if (prp.PriceMode == EnumPriceMode.Give.ToInt())
                                {
                                    //等于赠送
                                    if (SubAccountsProductPrice == prp.Price)
                                    {
                                        checksignsub = false;
                                        break;
                                    }
                                }
                            }
                            if (checksignsub)
                            {
                                throw new ApiException("产品价格不在有效范围内");
                            }
                        }

                        //List<ProductRulesPrice> spl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                        //foreach (ProductRulesPrice prp in spl)
                        //{
                        //    if (prp.PriceMode == EnumPriceMode.Fixed.ToInt())
                        //    {
                        //        //等于固定价格
                        //        if (productInfo.ContractProductinfoPrice == prp.Price)
                        //        {
                        //            checksign = false;
                        //            break;
                        //        }
                        //    }
                        //    if (prp.PriceMode == EnumPriceMode.StaringSale.ToInt())
                        //    {
                        //        //大于起售价格
                        //        if (productInfo.ContractProductinfoPrice > prp.Price)
                        //        {
                        //            checksign = false;
                        //            break;
                        //        }
                        //    }
                        //    if (prp.PriceMode == EnumPriceMode.Interval.ToInt())
                        //    {
                        //        //在区间价格范围内
                        //        if (productInfo.ContractProductinfoPrice > prp.Price && productInfo.ContractProductinfoPrice < prp.PricePart2)
                        //        {
                        //            checksign = false;
                        //            break;
                        //        }
                        //    }
                        //    if (prp.PriceMode == EnumPriceMode.purchase.ToInt())
                        //    {
                        //        //等于起购复购
                        //        if (productInfo.ContractProductinfoPrice == prp.Price)
                        //        {
                        //            checksign = false;
                        //            break;
                        //        }
                        //    }
                        //    if (prp.PriceMode == EnumPriceMode.MainAccount.ToInt())
                        //    {
                        //        //等于主账号
                        //        if (productInfo.ContractProductinfoPrice == prp.Price)
                        //        {
                        //            checksign = false;
                        //            break;
                        //        }
                        //    }
                        //    if (prp.PriceMode == EnumPriceMode.SubAccount.ToInt())
                        //    {
                        //        //等于子账号
                        //        if (productInfo.ContractProductinfoPrice == prp.Price)
                        //        {
                        //            checksign = false;
                        //            break;
                        //        }
                        //    }
                        //    if (prp.PriceMode == EnumPriceMode.Give.ToInt())
                        //    {
                        //        //等于赠送
                        //        if (productInfo.ContractProductinfoPrice == prp.Price)
                        //        {
                        //            checksign = false;
                        //            break;
                        //        }
                        //    }
                        //}
                        //验证国家是否在范围内
                        List<int> countrys = new List<int>();
                        if (productInfo.Countrys.IsNotNullOrEmpty())
                        {
                            countrys = productInfo.Countrys.Split(",").Select(s => Convert.ToInt32(s)).ToList();
                        }
                        if (countrys.Count > 0)
                        {
                            int G4DbNamesCount = p.G4DbNames.Where(r => countrys.Contains(r.SID.Value)).Count();
                            if (G4DbNamesCount != countrys.Count)
                            {
                                throw new ApiException("产品国家不在有效范围内");
                            }
                        }
                    }
                    //验证合同价格和产品价格相同
                    //decimal ContractAmount = addContractIn.ContractAmount.Value;
                    decimal ContractAmount = 0;
                    if (addContractIn.Currency != EnumCurrency.CNY.ToInt())
                    {
                        ContractAmount = addContractIn.FCContractAmount.Value;
                    }
                    else
                    {
                        ContractAmount = addContractIn.ContractAmount.Value;
                    }
                    decimal ProductPrice = 0;
                    decimal ProductPriceTotal = 0;
                    //List<ProductInfo_In> productInfos = productInfoIn.AsQueryable().Where(rulesValue).ToList();
                    decimal jpp = 0;
                    decimal ProductPriceMinimum = 0;
                    foreach (ProductInfo_In productInfo in productInfoIn)
                    {
                        string ProductId = productInfo.ProductId.ToString();
                        int frequency = 1;
                        if (productInfo.ProductType != EnumProductType.Periodicals && productInfo.ProductType != EnumProductType.Event)
                        {
                            //Db_sys_product_month_year monthYear = DbOpe_sys_product_month_year.Instance.GetData(r => r.Month == productInfo.OpeningMonths);
                            //if (monthYear == null)
                            //{
                            //    throw new ApiException("当前合同选择的服务周期在所选产品中不存在,请维护相关产品价格中的服务周期");
                            //}
                            //frequency = monthYear.Year.Value;
                            frequency = productInfo.OpeningYears.Value;
                        }
                        Db_crm_product_price productPrice = DbOpe_crm_product_price.Instance.GetData(r => r.ProductId == ProductId && r.Currency == addContractIn.Currency);// && r.ServiceCycle == monthYear.Year);
                        if (!(ProductId == Guid.Empty.ToString() && productInfo.ProductType == EnumProductType.Gtis))
                        {
                            if (productPrice == null)
                            {
                                throw new ApiException("当前合同选择的币种在所选产品中不存在,请维护相关产品币种");
                            }
                        }

                        decimal ContractProductinfoPriceTotal = productInfo.ContractProductinfoPriceTotal;
                        decimal ContractProductinfoPrice = productInfo.ContractProductinfoPrice;
                        //if (productInfo.ProductType == EnumProductType.Vip)
                        //{
                        //    //获取国家
                        //    List<int> countrys = new List<int>();
                        //    if (productInfo.Countrys.IsNotNullOrEmpty())
                        //    {
                        //        countrys = productInfo.Countrys.Split(",").Select(s => Convert.ToInt32(s)).ToList();
                        //    }
                        //    int countrysCount = DbOpe_sys_g4_dbnames.Instance.GetDataList(r => countrys.Contains(r.SID.Value)).GroupBy(r => r.BelongToSid).Count();
                        //    ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                        //    List<ProductRulesPrice> pl = p.PriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                        //    ContractProductinfoPrice = ContractProductinfoPrice + (pl.First().PricePart2 * (countrysCount - 1));

                        //    //特殊零售价格销售的数据 价格 18000 的 俄罗斯  //包含俄罗斯优先级中
                        //    List<Db_crm_product_rules_specialretailprice> specialretailpriceList = DbOpe_crm_product_rules_specialretailprice.Instance.GetDataList(r => r.ProductRulesId == p.RuleId && countrys.Contains(r.Sid.Value));
                        //    if (specialretailpriceList.Count > 0)
                        //    {
                        //        if (specialretailpriceList.First().MinCountryNum > countrysCount)
                        //        {
                        //            ContractProductinfoPrice = pl.First().PricePart1;
                        //        }
                        //    }

                        //    //组合售卖  //组合售卖优先级最高
                        //    List<Db_crm_product_rules_combinationsales> combinationsalesList = DbOpe_crm_product_rules_combinationsales.Instance.GetDataList(r => r.ProductRulesId == p.RuleId);
                        //    if (combinationsalesList.Count > 0)
                        //    {
                        //        Guid emptyGuid = Guid.Empty;
                        //        List<string> ProductIds = productInfoIn.Where(r => r.ProductId != emptyGuid).Select(r => r.ProductId.ToString()).ToList();
                        //        bool isHaveCombinationSales = combinationsalesList.Where(r => ProductIds.Contains(r.CombinationSalesProductId)).Any();
                        //        if (isHaveCombinationSales)
                        //        {
                        //            ContractProductinfoPrice = (pl.First().PricePart2 * countrysCount);
                        //        }
                        //    }
                        //}

                        if (productInfo.ProductType == EnumProductType.Vip)
                        {
                            decimal ContractProductinfoPriceVip = 0;
                            //获取国家
                            List<int> countrys = new List<int>();
                            if (productInfo.Countrys.IsNotNullOrEmpty())
                            {
                                countrys = productInfo.Countrys.Split(",").Select(s => Convert.ToInt32(s)).ToList();
                            }
                            int countrysCount = DbOpe_sys_g4_dbnames.Instance.GetDataList(r => countrys.Contains(r.SID.Value)).GroupBy(r => r.BelongToSid).Count();
                            ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                            List<ProductRulesPrice> pl = p.PriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                            ContractProductinfoPriceVip = pl.First().PricePart1 + (pl.First().PricePart2 * (countrysCount - 1));

                            //特殊零售价格销售的数据 价格 18000 的 俄罗斯  //包含俄罗斯优先级中
                            List<Db_crm_product_rules_specialretailprice> specialretailpriceList = DbOpe_crm_product_rules_specialretailprice.Instance.GetDataList(r => r.ProductRulesId == p.RuleId && countrys.Contains(r.Sid.Value));
                            if (specialretailpriceList.Count > 0)
                            {
                                if (specialretailpriceList.First().MinCountryNum > countrysCount)
                                {
                                    ContractProductinfoPriceVip = pl.First().PricePart1;
                                }
                            }

                            //组合售卖  //组合售卖优先级最高
                            List<Db_crm_product_rules_combinationsales> combinationsalesList = DbOpe_crm_product_rules_combinationsales.Instance.GetDataList(r => r.ProductRulesId == p.RuleId);
                            if (combinationsalesList.Count > 0)
                            {
                                Guid emptyGuid = Guid.Empty;
                                List<string> ProductIds = productInfoIn.Where(r => r.ProductId != emptyGuid).Select(r => r.ProductId.ToString()).ToList();
                                bool isHaveCombinationSales = combinationsalesList.Where(r => ProductIds.Contains(r.CombinationSalesProductId)).Any();
                                if (isHaveCombinationSales)
                                {
                                    ContractProductinfoPriceVip = (pl.First().PricePart2 * countrysCount);
                                }
                            }

                            if (ContractProductinfoPriceVip > ContractProductinfoPrice)
                            {
                                throw new ApiException("Vip零售产品价格不一致");
                            }
                        }


                        if (productInfo.ProductType == EnumProductType.Global)
                        {
                            ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                            //List<ProductRulesPrice> pl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                            ////ContractProductinfoPrice = ContractProductinfoPrice + (pl.First().Price * productInfo.SubAccountsNum.Value);
                            //ContractProductinfoPrice = ContractProductinfoPrice + (productInfo.SubAccountsProductPrice * productInfo.SetNum.Value);
                            List<ProductRulesPrice> pl = p.PriceList.Where(r => r.Currency == addContractIn.Currency.Value && r.Id == productInfo.SubAccountsProductPriceId.ToString()).ToList();
                            ContractProductinfoPrice = ContractProductinfoPrice + (pl.First().Price * (productInfo.SetNum == null ? 0 : productInfo.SetNum.Value));
                            //********不再计算环球搜子账号价格
                            //int SubAccountsNum = productInfo.SubAccountsNum.Value;
                            //if (SubAccountsNum > 0)
                            //{
                            //    SubAccountsNum = SubAccountsNum - 1;
                            //}
                            //bool isHaveGtis = productInfoIn.Where(r => (r.ProductType == EnumProductType.Gtis && r.ProductId != Guid.Empty) || r.ProductType == EnumProductType.Combination).Any();
                            //if (!isHaveGtis)
                            //{
                            //    ContractProductinfoPrice = ContractProductinfoPrice + (pl.First().Price * SubAccountsNum);
                            //}
                            //********不再计算环球搜子账号价格
                            //ContractProductinfoPrice = ContractProductinfoPrice + (pl.First().Price * SubAccountsNum);
                        }
                        if (productInfo.ProductType == EnumProductType.Gtis && productInfo.ProductId == Guid.Empty)
                        {
                            ContractProductinfoPrice = ContractProductinfoPrice * productInfo.AuthorizationNum.Value;
                        }
                        if (productInfo.ProductType == EnumProductType.Other)
                        {
                            ContractProductinfoPrice = ContractProductinfoPrice * productInfo.CodesNum.Value;
                        }

                        if (productInfo.ProductType == EnumProductType.AdditionalResource)
                        {
                            ContractProductinfoPrice = ContractProductinfoPrice * productInfo.SubAccountsNum.Value;
                            //frequency = productInfo.SubAccountsNum.Value;
                        }


                        ProductPrice = ProductPrice + (ContractProductinfoPrice * frequency);
                        ProductPriceTotal = ProductPriceTotal + ContractProductinfoPriceTotal;

                        if (productInfo.ProductType != EnumProductType.Global && productInfo.ProductType != EnumProductType.GlobalWitsSchool)
                        {
                            jpp = jpp + (ContractProductinfoPrice * frequency);
                        }
                        if (productInfo.ProductType == EnumProductType.Global)
                        {
                            ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                            //List<ProductRulesPrice> pl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                            ////jpp = jpp + (pl.First().Price * productInfo.SubAccountsNum.Value);
                            //jpp = jpp + (productInfo.SubAccountsProductPrice * productInfo.SetNum.Value);
                            List<ProductRulesPrice> pl = p.PriceList.Where(r => r.Currency == addContractIn.Currency.Value && r.Id == productInfo.SubAccountsProductPriceId.ToString()).ToList();
                            jpp = jpp + (pl.First().Price * (productInfo.SetNum == null ? 0 : productInfo.SetNum.Value));
                            //********不再计算环球搜子账号价格
                            //int SubAccountsNum = productInfo.SubAccountsNum.Value;
                            //if (SubAccountsNum > 0)
                            //{
                            //    SubAccountsNum = SubAccountsNum - 1;
                            //}
                            //bool isHaveGtis = productInfoIn.Where(r => (r.ProductType == EnumProductType.Gtis && r.ProductId != Guid.Empty) || r.ProductType == EnumProductType.Combination).Any();
                            //if (!isHaveGtis)
                            //{
                            //    jpp = jpp + (pl.First().Price * SubAccountsNum);
                            //}
                            //********不再计算环球搜子账号价格
                            //jpp = jpp + (pl.First().Price * SubAccountsNum);
                        }

                        if (ContractProductinfoPriceTotal != (ContractProductinfoPrice * frequency))
                        {
                            throw new ApiException("当前产品价格总价不一致");
                        }
                        ProductPriceMinimum = ProductPriceMinimum + productInfo.ContractProductinfoPriceMinimumTotal;
                    }
                    if (ProductPrice != ProductPriceTotal)
                    {
                        throw new ApiException("当前产品价格总价不一致");
                    }
                    if (ContractAmount == ProductPrice)
                    {
                        result = true;
                    }
                    else if (ContractAmount >= ProductPriceMinimum)
                    {
                        result = true;
                    }
                    //else if (ContractAmount >= jpp && addContractIn.Currency.Value == EnumCurrency.CNY.ToInt())
                    //{
                    //    result = true;
                    //}
                    else if (addContractIn.IsOverseasCustomer == true || addContractIn.IsOverseasCustomer == false)
                    {
                        //合同总价格可以低于咱们的产品最低价格之和，走手动审批
                        result = true;
                    }
                    else
                    {
                        throw new ApiException("合同金额不能小于产品最低总价");
                    }
                }
            }
            if (rulesKey == "OverseasCustomerCompare")
            {
                if (datalist.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    Guid FirstParty = datalist.First().FirstParty;
                    Db_crm_customer_subcompany subcompany = DbOpe_crm_customer_subcompany.Instance.GetDataById(FirstParty.ToString());
                    if (datalist.First().IsOverseasCustomer)
                    {
                        if (subcompany.CreditType == EnumCompanyType.Other.ToInt())
                        {
                            result = true;
                        }
                    }
                    else
                    {
                        //240820 个人客户不能算作境内然后走自动审核 需要手动审核
                        //if (subcompany.CreditType == EnumCompanyType.CN.ToInt() || subcompany.CreditType == EnumCompanyType.Person.ToInt())
                        if (subcompany.CreditType == EnumCompanyType.CN.ToInt())
                        {
                            result = true;
                        }
                    }
                }
            }
            if (rulesKey == "ContractCondition_MergeCompany")
            {
                if (datalist.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    List<string> validCustomerIds = new List<string>();
                    if (addContractIn.ContractType == EnumContractType.ReNew.ToInt())
                    {
                        List<string> CustomerIds = DbOpe_crm_customer_privatepool.Instance.GetPrivateCustomerIdsByContractNum(userId, datalist.First().RenewalContractNum);
                        if (ContractId != Guid.Empty.ToString())
                        {
                            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(ContractId);
                            if (contract.MergeCompanyAuditId.IsNotNullOrEmpty())
                            {
                                var checkL = BLL_Customer.Instance.CheckAuditInProcessNoSelf(CustomerIds, contract.MergeCompanyAuditId, false);
                                foreach (string customerId in CustomerIds)
                                {
                                    if (!checkL.Contains(customerId))
                                    {
                                        validCustomerIds.Add(customerId);
                                    }
                                }
                            }
                            else
                            {
                                var checkL = BLL_Customer.Instance.CheckAuditInProcess(CustomerIds, false);
                                foreach (string customerId in CustomerIds)
                                {
                                    if (!checkL.Contains(customerId))
                                    {
                                        validCustomerIds.Add(customerId);
                                    }
                                }
                            }
                        }
                        else
                        {
                            var checkL = BLL_Customer.Instance.CheckAuditInProcess(CustomerIds, false);
                            foreach (string customerId in CustomerIds)
                            {
                                if (!checkL.Contains(customerId))
                                {
                                    validCustomerIds.Add(customerId);
                                }
                            }
                        }

                        if (validCustomerIds.Count() > 1)
                        {
                            throw new ApiException("客户编码重复请联系管理员");
                        }
                        if (validCustomerIds.Count() == 0)
                        {
                            throw new ApiException("该客户编码未找到对应的合同");
                        }

                        Db_crm_customer_subcompany sc = DbOpe_crm_customer_subcompany.Instance.GetDataById(addContractIn.FirstParty.ToString());
                        if (validCustomerIds.First() != sc.CustomerId)
                        {
                            result = false;
                        }
                        else
                        {
                            result = true;
                        }
                    }
                    else
                    {
                        result = true;
                    }
                }
            }
            if (rulesKey == "OpeningMonths")
            {
                result = true;
                if (addContractIn.ContractType != (int)EnumContractType.AddItem)
                {
                    List<ProductMonthYear_Out> MonthYear = BLL_Contract.Instance.GetProductMonthYear();
                    foreach (ProductInfo_In productInfo in productInfoIn)
                    {
                        List<ProductMonthYear_Out> Month = MonthYear.Where(r => r.Year == productInfo.OpeningYears && r.Month == productInfo.OpeningMonths).ToList();
                        if (Month.Count() == 0)
                        {
                            result = false;
                            break;
                        }
                    }
                }
            }
            if (rulesKey == "SubAccountsNumCompare")
            {
                result = true;
                List<Db_crm_product> productList = DbOpe_crm_product.Instance.GetDataAllList().ToList();
                foreach (ProductInfo_In productInfo in productInfoIn)
                {
                    string ProductId = productInfo.ProductId.ToString();
                    List<Db_crm_product> product = productList.Where(r => r.Id == ProductId).ToList();
                    if (product.Count > 0)
                    {
                        //环球搜不验证子账号
                        if (productInfo.ProductType == EnumProductType.Global)
                        {
                            continue;
                        }
                        if (productInfo.ProductType == EnumProductType.Global || productInfo.ProductType == EnumProductType.Vip)
                        {
                            bool isHaveGtis = productInfoIn.Where(r => (r.ProductType == EnumProductType.Gtis && r.ProductId != Guid.Empty) || r.ProductType == EnumProductType.Combination).Any();
                            if (!isHaveGtis)
                            {
                                if (product.First().SubCount < productInfo.SubAccountsNum)
                                {
                                    result = false;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            if (product.First().SubCount < productInfo.SubAccountsNum)
                            {
                                result = false;
                                break;
                            }
                        }
                    }

                }
            }
            return result;
        }

        public static bool IsAutoAuditCheck(string ContractId, Contract_In contractIn, PaymentInfo_In paymentInfo, List<ProductInfo_In> productInfoIn, List<Db_sys_review_rules> rules, string parentId, string userId, ref List<AutoAuditReturnMessageContent_Out> CheckMessage)
        {
            bool result = true;
            List<Db_sys_review_rules> list = rules.Where(r => r.ParentId == parentId).ToList();
            foreach (Db_sys_review_rules rule in list)
            {
                bool isCheck = ConditionCheck(ContractId, contractIn, paymentInfo, productInfoIn, rule.RulesKey, rule.RulesValue, userId);
                if (rule.Type == EnumLogicalOperator.And.ToInt())
                {
                    if (isCheck)
                    {
                        //CheckMessage.Add(new AutoAuditReturnMessageContent_Out { Message = rule.Name, Success = true });//20240515 不显示
                        //result = IsAutoAuditCheck(contractIn, paymentInfo, productInfoIn, rules, rule.Id, ref CheckMessage); //20240515 不显示
                        result = IsAutoAuditCheck(ContractId, contractIn, paymentInfo, productInfoIn, rules, rule.Id, userId);//20240515 不显示
                        if (result == false)
                        {
                            //CheckMessage.Clear();
                            if (rule.RulesKey != "AmountCompare_CheckProductAmount")
                            {
                                CheckMessage.Add(new AutoAuditReturnMessageContent_Out { Message = rule.Name, Success = false });
                            }
                            //break;
                        }
                        else
                        {
                            if (rule.RulesKey != "AmountCompare_CheckProductAmount")
                            {
                                CheckMessage.Add(new AutoAuditReturnMessageContent_Out { Message = rule.Name, Success = true });
                            }
                        }
                    }
                    else
                    {
                        //CheckMessage.Clear();
                        if (rule.RulesKey != "AmountCompare_CheckProductAmount")
                        {
                            CheckMessage.Add(new AutoAuditReturnMessageContent_Out { Message = rule.Name, Success = false });
                        }
                        result = false;
                        //break;
                    }
                }
                if (rule.Type == EnumLogicalOperator.Or.ToInt())
                {
                    if (isCheck)
                    {
                        //CheckMessage.Clear();
                        //CheckMessage.Add(new AutoAuditReturnMessageContent_Out { Message = rule.Name, Success = true });//20240515 不显示
                        //result = IsAutoAuditCheck(contractIn, paymentInfo, productInfoIn, rules, rule.Id, ref CheckMessage); //20240515 不显示
                        result = IsAutoAuditCheck(ContractId, contractIn, paymentInfo, productInfoIn, rules, rule.Id, userId); //20240515 不显示
                        break;
                    }
                    else
                    {
                        //CheckMessage.Add(rule.Name);
                        //CheckMessage.Add(new AutoAuditReturnMessageContent_Out { Message = rule.Name, Success = false });//20240515 不显示
                        result = false;
                    }
                }
                //result = IsAutoAuditCheck(addContractIn, rules, rule.Id);
            }
            return result;
        }

        //public static bool IsAutoAuditCheck(Contract_In contractIn, PaymentInfo_In paymentInfo, List<ProductInfo_In> productInfoIn, List<Db_sys_review_rules> rules, string parentId, ref List<string> CheckMessage)
        //{
        //    bool result = true;
        //    List<Db_sys_review_rules> list = rules.Where(r => r.ParentId == parentId).ToList();
        //    foreach (Db_sys_review_rules rule in list)
        //    {
        //        bool isCheck = ConditionCheck(contractIn, paymentInfo, productInfoIn, rule.RulesKey, rule.RulesValue);
        //        if (rule.Type == EnumLogicalOperator.And.ToInt())
        //        {
        //            if (isCheck)
        //            {
        //                result = IsAutoAuditCheck(contractIn, paymentInfo, productInfoIn, rules, rule.Id, ref CheckMessage);
        //                if (result == false)
        //                {
        //                    CheckMessage.Clear();
        //                    CheckMessage.Add(rule.Name);
        //                    break;
        //                }
        //            }
        //            else
        //            {
        //                CheckMessage.Clear();
        //                CheckMessage.Add(rule.Name);
        //                result = false;
        //                break;
        //            }
        //        }
        //        if (rule.Type == EnumLogicalOperator.Or.ToInt())
        //        {
        //            if (isCheck)
        //            {
        //                CheckMessage.Clear();
        //                result = IsAutoAuditCheck(contractIn, paymentInfo, productInfoIn, rules, rule.Id, ref CheckMessage);
        //                break;
        //            }
        //            else
        //            {
        //                CheckMessage.Add(rule.Name);
        //                result = false;
        //            }
        //        }
        //        //result = IsAutoAuditCheck(addContractIn, rules, rule.Id);
        //    }
        //    return result;
        //}

        public static void AmountCompare_CheckProductAmount(Contract_In addContractIn, PaymentInfo_In paymentInfo, List<ProductInfo_In> productInfoIn, List<Db_sys_review_rules> rules, string parentId, string userId, ref List<CheckProductPrice_Out> CheckMessage)
        {
            List<ProductRules_Out> ProductRulesInfo = ProductRules.GetProductRules(productInfoIn, addContractIn.SalesCountry, addContractIn.Currency.Value, addContractIn.FirstParty.ToString(),addContractIn.ParentContractId);
            foreach (ProductInfo_In productInfo in productInfoIn)
            {
                //验证价格是否在范围内
                ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                bool checksign = true;
                List<ProductRulesPrice> pl = p.PriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                foreach (ProductRulesPrice prp in pl)
                {
                    if (prp.PriceMode == EnumPriceMode.FixedAndStaringSale.ToInt())
                    {
                        //等于固定价格和范围内
                        if (productInfo.ContractProductinfoPrice >= prp.Price && productInfo.ContractProductinfoPrice <= prp.PricePart2)
                        {
                            checksign = false;
                            //CheckMessage.Add(new CheckProductPrice_Out { Message = prp.ProductName, Success = false });
                            break;
                        }
                    }

                    if (prp.PriceMode == EnumPriceMode.Fixed.ToInt())
                    {
                        //等于固定价格
                        if (productInfo.ContractProductinfoPrice == prp.Price || productInfo.SubAccountsProductPrice == prp.Price)
                        {
                            checksign = false;
                            //CheckMessage.Add(new CheckProductPrice_Out { Message = prp.ProductName, Success = false });
                            break;
                        }
                    }
                    if (prp.PriceMode == EnumPriceMode.StaringSale.ToInt())
                    {
                        //大于起售价格
                        if (productInfo.ContractProductinfoPrice >= prp.Price)
                        {
                            checksign = false;
                            //CheckMessage.Add(new CheckProductPrice_Out { Message = prp.ProductName, Success = false });
                            break;
                        }
                    }
                    if (prp.PriceMode == EnumPriceMode.Interval.ToInt())
                    {
                        //在区间价格范围内
                        if (productInfo.ContractProductinfoPrice >= prp.Price && productInfo.ContractProductinfoPrice <= prp.PricePart2)
                        {
                            checksign = false;
                            //CheckMessage.Add(new CheckProductPrice_Out { Message = prp.ProductName, Success = false });
                            break;
                        }
                    }
                    if (prp.PriceMode == EnumPriceMode.purchase.ToInt())
                    {
                        //等于起购复购
                        if (productInfo.ContractProductinfoPrice == prp.Price)
                        {
                            checksign = false;
                            //CheckMessage.Add(new CheckProductPrice_Out { Message = prp.ProductName, Success = false });
                            break;
                        }
                    }
                    if (prp.PriceMode == EnumPriceMode.MainAccount.ToInt())
                    {
                        //等于主账号
                        if (productInfo.ContractProductinfoPrice >= prp.Price)
                        {
                            checksign = false;
                            //CheckMessage.Add(new CheckProductPrice_Out { Message = prp.ProductName, Success = false });
                            break;
                        }
                    }
                    if (prp.PriceMode == EnumPriceMode.SubAccount.ToInt())
                    {
                        //等于子账号
                        if (productInfo.ContractProductinfoPrice == prp.Price)
                        {
                            checksign = false;
                            //CheckMessage.Add(new CheckProductPrice_Out { Message = prp.ProductName, Success = false });
                            break;
                        }
                    }
                    if (prp.PriceMode == EnumPriceMode.Give.ToInt())
                    {
                        //等于赠送
                        if (productInfo.ContractProductinfoPrice == prp.Price)
                        {
                            checksign = false;
                            //CheckMessage.Add(new CheckProductPrice_Out { Message = prp.ProductName, Success = false });
                            break;
                        }
                    }
                }
                if (checksign)
                {
                    if (productInfo.ProductId == Guid.Empty && productInfo.ProductType == EnumProductType.Gtis)
                    {
                        CheckMessage.Add(new CheckProductPrice_Out { Message = "超级子账号产品价格不在有效范围内", Success = false });
                    }
                    else
                    {
                        Db_crm_product product = DbOpe_crm_product.Instance.GetDataById(productInfo.ProductId.ToString());
                        CheckMessage.Add(new CheckProductPrice_Out { Message = product.ProductName + "产品价格不在有效范围内", Success = false });
                    }
                }

                if (p.SubaccountsPriceList != null)
                {
                    decimal SubAccountsProductPrice = productInfo.SubAccountsProductPrice == null ? 0 : productInfo.SubAccountsProductPrice.Value;
                    bool checksignsub = true;
                    List<ProductRulesPrice> spl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                    foreach (ProductRulesPrice prp in spl)
                    {
                        if (prp.PriceMode == EnumPriceMode.FixedAndStaringSale.ToInt())
                        {
                            //等于固定价格和范围内
                            if (SubAccountsProductPrice >= prp.Price && SubAccountsProductPrice <= prp.PricePart2)
                            {
                                checksignsub = false;
                                break;
                            }
                        }
                        if (prp.PriceMode == EnumPriceMode.Fixed.ToInt())
                        {
                            //等于固定价格
                            if (SubAccountsProductPrice == prp.Price)
                            {
                                checksignsub = false;
                                break;
                            }
                        }
                        if (prp.PriceMode == EnumPriceMode.StaringSale.ToInt())
                        {
                            //大于起售价格
                            if (SubAccountsProductPrice >= prp.Price)
                            {
                                checksignsub = false;
                                break;
                            }
                        }
                        if (prp.PriceMode == EnumPriceMode.Interval.ToInt())
                        {
                            //在区间价格范围内
                            if (SubAccountsProductPrice >= prp.Price && SubAccountsProductPrice <= prp.PricePart2)
                            {
                                checksignsub = false;
                                break;
                            }
                        }
                        if (prp.PriceMode == EnumPriceMode.purchase.ToInt())
                        {
                            //等于起购复购
                            if (SubAccountsProductPrice == prp.Price)
                            {
                                checksignsub = false;
                                break;
                            }
                        }
                        if (prp.PriceMode == EnumPriceMode.MainAccount.ToInt())
                        {
                            //等于主账号
                            if (SubAccountsProductPrice >= prp.Price)
                            {
                                checksignsub = false;
                                break;
                            }
                        }
                        if (prp.PriceMode == EnumPriceMode.SubAccount.ToInt())
                        {
                            //等于子账号
                            if (SubAccountsProductPrice == prp.Price)
                            {
                                checksignsub = false;
                                break;
                            }
                        }
                        if (prp.PriceMode == EnumPriceMode.Give.ToInt())
                        {
                            //等于赠送
                            if (SubAccountsProductPrice == prp.Price)
                            {
                                checksignsub = false;
                                break;
                            }
                        }
                    }
                    if (checksignsub)
                    {
                        if (productInfo.ProductId == Guid.Empty && productInfo.ProductType == EnumProductType.Gtis)
                        {
                            CheckMessage.Add(new CheckProductPrice_Out { Message = "超级子账号产品价格不在有效范围内", Success = false });
                        }
                        else
                        {
                            Db_crm_product product = DbOpe_crm_product.Instance.GetDataById(productInfo.ProductId.ToString());
                            CheckMessage.Add(new CheckProductPrice_Out { Message = product.ProductName + "产品价格不在有效范围内", Success = false });
                        }
                    }
                }

                //验证国家是否在范围内
                List<int> countrys = new List<int>();
                if (productInfo.Countrys.IsNotNullOrEmpty())
                {
                    countrys = productInfo.Countrys.Split(",").Select(s => Convert.ToInt32(s)).ToList();
                }
                if (countrys.Count > 0)
                {
                    int G4DbNamesCount = p.G4DbNames.Where(r => countrys.Contains(r.SID.Value)).Count();
                    if (G4DbNamesCount != countrys.Count)
                    {
                        CheckMessage.Add(new CheckProductPrice_Out { Message = "产品国家不在有效范围内", Success = false });
                    }
                }
            }
            //验证合同价格和产品价格相同
            //decimal ContractAmount = addContractIn.ContractAmount.Value;
            decimal? ContractAmount;
            if (addContractIn.Currency != EnumCurrency.CNY.ToInt())
            {
                ContractAmount = addContractIn.FCContractAmount;
            }
            else
            {
                ContractAmount = addContractIn.ContractAmount;
            }
            decimal ProductPrice = 0;
            decimal ProductPriceTotal = 0;
            //List<ProductInfo_In> productInfos = productInfoIn.AsQueryable().Where(rulesValue).ToList();
            decimal jpp = 0;
            decimal ProductPriceMinimum = 0;
            foreach (ProductInfo_In productInfo in productInfoIn)
            {
                string ProductId = productInfo.ProductId.ToString();
                int frequency = 1;
                if (productInfo.ProductType != EnumProductType.Periodicals && productInfo.ProductType != EnumProductType.Event)
                {
                    frequency = productInfo.OpeningYears.Value;
                }
                Db_crm_product_price productPrice = DbOpe_crm_product_price.Instance.GetData(r => r.ProductId == ProductId && r.Currency == addContractIn.Currency);// && r.ServiceCycle == monthYear.Year);
                if (!(ProductId == Guid.Empty.ToString() && productInfo.ProductType == EnumProductType.Gtis))
                {
                    if (productPrice == null)
                    {
                        throw new ApiException("当前合同选择的币种在所选产品中不存在,请维护相关产品币种");
                    }
                }

                decimal ContractProductinfoPriceTotal = productInfo.ContractProductinfoPriceTotal;
                decimal ContractProductinfoPrice = productInfo.ContractProductinfoPrice;

                if (productInfo.ProductType == EnumProductType.Vip)
                {
                    decimal ContractProductinfoPriceVip = 0;
                    //获取国家
                    List<int> countrys = new List<int>();
                    if (productInfo.Countrys.IsNotNullOrEmpty())
                    {
                        countrys = productInfo.Countrys.Split(",").Select(s => Convert.ToInt32(s)).ToList();
                    }
                    int countrysCount = DbOpe_sys_g4_dbnames.Instance.GetDataList(r => countrys.Contains(r.SID.Value)).GroupBy(r => r.BelongToSid).Count();
                    ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                    List<ProductRulesPrice> pl = p.PriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                    ContractProductinfoPriceVip = pl.First().PricePart1 + (pl.First().PricePart2 * (countrysCount - 1));

                    //特殊零售价格销售的数据 价格 18000 的 俄罗斯  //包含俄罗斯优先级中
                    List<Db_crm_product_rules_specialretailprice> specialretailpriceList = DbOpe_crm_product_rules_specialretailprice.Instance.GetDataList(r => r.ProductRulesId == p.RuleId && countrys.Contains(r.Sid.Value));
                    if (specialretailpriceList.Count > 0)
                    {
                        if (specialretailpriceList.First().MinCountryNum > countrysCount)
                        {
                            ContractProductinfoPriceVip = pl.First().PricePart1;
                        }
                    }

                    //组合售卖  //组合售卖优先级最高
                    List<Db_crm_product_rules_combinationsales> combinationsalesList = DbOpe_crm_product_rules_combinationsales.Instance.GetDataList(r => r.ProductRulesId == p.RuleId);
                    if (combinationsalesList.Count > 0)
                    {
                        Guid emptyGuid = Guid.Empty;
                        List<string> ProductIds = productInfoIn.Where(r => r.ProductId != emptyGuid).Select(r => r.ProductId.ToString()).ToList();
                        bool isHaveCombinationSales = combinationsalesList.Where(r => ProductIds.Contains(r.CombinationSalesProductId)).Any();
                        if (isHaveCombinationSales)
                        {
                            ContractProductinfoPriceVip = (pl.First().PricePart2 * countrysCount);
                        }
                    }

                    if (ContractProductinfoPriceVip > ContractProductinfoPrice)
                    {
                        Db_crm_product product = DbOpe_crm_product.Instance.GetDataById(productInfo.ProductId.ToString());
                        CheckMessage.Add(new CheckProductPrice_Out { Message = product.ProductName + "产品价格不在有效范围内", Success = false });
                        return;
                    }
                }


                if (productInfo.ProductType == EnumProductType.Global)
                {
                    ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                    //List<ProductRulesPrice> pl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                    ////ContractProductinfoPrice = ContractProductinfoPrice + (pl.First().Price * productInfo.SubAccountsNum.Value);
                    //ContractProductinfoPrice = ContractProductinfoPrice + (productInfo.SubAccountsProductPrice * productInfo.SetNum.Value);
                    //修改 2025年7月24日 替换掉原本的子账号关联表内容，因为已经不存在子账号
                    // List<ProductRulesPrice> pl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value && r.Id == productInfo.SubAccountsProductPriceId.ToString()).ToList();
                    List<ProductRulesPrice> pl = p.PriceList.Where(r => r.Currency == addContractIn.Currency.Value && r.Id == productInfo.SubAccountsProductPriceId.ToString()).ToList();
                    ContractProductinfoPrice = ContractProductinfoPrice + ((pl.Count() > 0 ? pl.First().Price : (productInfo.SubAccountsProductPrice == null ? 0 : productInfo.SubAccountsProductPrice.Value)) * (productInfo.SetNum == null ? 0 : productInfo.SetNum.Value));
                    //********不再计算环球搜子账号价格
                    //int SubAccountsNum = productInfo.SubAccountsNum.Value;
                    //if (SubAccountsNum > 0)
                    //{
                    //    SubAccountsNum = SubAccountsNum - 1;
                    //}
                    //bool isHaveGtis = productInfoIn.Where(r => (r.ProductType == EnumProductType.Gtis && r.ProductId != Guid.Empty) || r.ProductType == EnumProductType.Combination).Any();
                    //if (!isHaveGtis)
                    //{
                    //    ContractProductinfoPrice = ContractProductinfoPrice + (pl.First().Price * SubAccountsNum);
                    //}
                    //********不再计算环球搜子账号价格
                    //ContractProductinfoPrice = ContractProductinfoPrice + (pl.First().Price * SubAccountsNum);
                }
                if (productInfo.ProductType == EnumProductType.Gtis && productInfo.ProductId == Guid.Empty)
                {
                    ContractProductinfoPrice = ContractProductinfoPrice * productInfo.AuthorizationNum.Value;
                }
                if (productInfo.ProductType == EnumProductType.Other)
                {
                    ContractProductinfoPrice = ContractProductinfoPrice * productInfo.CodesNum.Value;
                }

                if (productInfo.ProductType == EnumProductType.AdditionalResource)
                {
                    ContractProductinfoPrice = ContractProductinfoPrice * productInfo.SubAccountsNum.Value;
                }

                ProductPrice = ProductPrice + (ContractProductinfoPrice * frequency);
                ProductPriceTotal = ProductPriceTotal + ContractProductinfoPriceTotal;

                if (productInfo.ProductType != EnumProductType.Global && productInfo.ProductType != EnumProductType.GlobalWitsSchool)
                {
                    jpp = jpp + (ContractProductinfoPrice * frequency);
                }
                if (productInfo.ProductType == EnumProductType.Global)
                {
                    ProductRules_Out p = ProductRulesInfo.Where(r => r.ProductId == productInfo.ProductId).First();
                    //List<ProductRulesPrice> pl = p.SubaccountsPriceList.Where(r => r.Currency == addContractIn.Currency.Value).ToList();
                    ////jpp = jpp + (pl.First().Price * productInfo.SubAccountsNum.Value);
                    //jpp = jpp + (productInfo.SubAccountsProductPrice * productInfo.SetNum.Value);
                    List<ProductRulesPrice> pl = p.PriceList.Where(r => r.Currency == addContractIn.Currency.Value && r.Id == productInfo.SubAccountsProductPriceId.ToString()).ToList();
                    jpp = jpp + ((pl.Count() > 0 ? pl.First().Price : (productInfo.SubAccountsProductPrice == null ? 0 : productInfo.SubAccountsProductPrice.Value)) * (productInfo.SetNum == null ? 0 : productInfo.SetNum.Value));
                    //********不再计算环球搜子账号价格
                    //int SubAccountsNum = productInfo.SubAccountsNum.Value;
                    //if (SubAccountsNum > 0)
                    //{
                    //    SubAccountsNum = SubAccountsNum - 1;
                    //}
                    //bool isHaveGtis = productInfoIn.Where(r => (r.ProductType == EnumProductType.Gtis && r.ProductId != Guid.Empty) || r.ProductType == EnumProductType.Combination).Any();
                    //if (!isHaveGtis)
                    //{
                    //    jpp = jpp + (pl.First().Price * SubAccountsNum);
                    //}
                    //********不再计算环球搜子账号价格
                    //jpp = jpp + (pl.First().Price * SubAccountsNum);
                }


                if (ContractProductinfoPriceTotal != (ContractProductinfoPrice * frequency))
                {
                    Db_crm_product product = DbOpe_crm_product.Instance.GetDataById(productInfo.ProductId.ToString());
                    CheckMessage.Add(new CheckProductPrice_Out { Message = product.ProductName + "产品价格与总价不一致", Success = false });
                }
                ProductPriceMinimum = ProductPriceMinimum + productInfo.ContractProductinfoPriceMinimumTotal;
            }
            if (ProductPrice != ProductPriceTotal)
            {
                CheckMessage.Add(new CheckProductPrice_Out { Message = "当前产品价格与总价不一致", Success = false });
            }
            if (ContractAmount != null)
            {
                //合同总价格可以低于咱们的产品最低价格之和，走手动审批
                //if (addContractIn.IsOverseasCustomer == false)
                //{
                //    //if ((ContractAmount != ProductPrice) && !(ContractAmount >= jpp && addContractIn.Currency.Value == EnumCurrency.CNY.ToInt()))
                //    //{
                //    //    CheckMessage.Add(new CheckProductPrice_Out { Message = "合同金额不能小于产品总价", Success = false });
                //    //}
                //    if (ContractAmount < ProductPriceMinimum)
                //    {
                //        CheckMessage.Add(new CheckProductPrice_Out { Message = "合同金额不能小于产品最低总价", Success = false });
                //    }
                //}
            }
        }
    }
}
