using CRM2_API.BLL.Common;
using CRM2_API.BLL.GlobalSearchOpe;
using CRM2_API.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Utils;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BLLModel.ServiceOpening;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.System;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CRM2_API.BLL.ServiceOpening
{
    /// <summary>
    /// 服务开通 - 各服务处理器
    /// </summary>
    public partial class BLL_ServiceOpening
    {
        #region 环球搜服务处理
        
        /// <summary>
        /// 处理环球搜服务
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>环球搜处理结果</returns>
        private GlobalSearchResult ProcessGlobalSearchService(ServiceOpeningParams openingParams)
        {
            var result = new GlobalSearchResult();

            try
            {
                LogUtil.AddLog("开始处理环球搜服务");

                // 基于预处理结果直接执行相应操作，无需重复查询和判断
                var preprocessResult = openingParams.GlobalSearchPreprocessResult;
                var globalSearchParams = GetDefaultGlobalSearchParams(openingParams);

                if (preprocessResult.OperationType == GlobalSearchOperationType.NewService)
                {
                    // 新增服务：调用批量创建接口
                    LogUtil.AddLog("基于预处理结果，执行环球搜新增服务");
                    result = CreateNewGlobalSearchService(openingParams, globalSearchParams);
                }
                else if (preprocessResult.OperationType == GlobalSearchOperationType.UpdateService)
                {
                    // 更新服务：调用更新接口
                    LogUtil.AddLog($"基于预处理结果，执行环球搜更新服务，现有账号数量: {preprocessResult.ExistingAccountCount}");
                    result = UpdateExistingGlobalSearchService(openingParams, globalSearchParams, preprocessResult);
                }
                else
                {
                    throw new ApiException($"未知的环球搜操作类型: {preprocessResult.OperationType}");
                }

                if (result.Success)
                {
                    LogUtil.AddLog($"环球搜服务处理成功: {result.Message}");
                }
                else
                {
                    LogUtil.AddErrorLog($"环球搜服务处理失败: {result.Message}");
                }

            }
            catch (Exception ex)
            {
                var errorMsg = $"环球搜服务处理异常，需要变更环球搜: {openingParams.MainService.IsGlobalSearchAudit}, 合同ID: {openingParams.Contract?.Id}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new ApiException(errorMsg);
            }

            return result;
        }
        
        
        /// <summary>
        /// 获取默认环球搜参数
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>环球搜参数</returns>
        private dynamic GetDefaultGlobalSearchParams(ServiceOpeningParams openingParams)
        {
            // 只从真实的特殊配置中获取参数
            if (openingParams.GlobalSearchSpecialConfig?.ServiceInfo != null)
            {
                var globalSearchParams = openingParams.GlobalSearchSpecialConfig.ServiceInfo;
                return new
                {
                    ServiceCycleStart = globalSearchParams.ServiceCycleStart,
                    ServiceCycleEnd = globalSearchParams.ServiceCycleEnd,
                    SettlementLevel = globalSearchParams.SettlementLevel,
                    SettlementMonth = globalSearchParams.SettlementMonth,
                    SettlementCount = globalSearchParams.SettlementCount
                };
            }

            // 如果没有特殊配置数据，抛出异常
            throw new ApiException("环球搜服务配置数据缺失，无法获取服务参数");
        }
        
        #endregion
        
        #region GTIS服务处理
        
        /// <summary>
        /// 处理GTIS服务
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="globalSearchResult">环球搜处理结果</param>
        /// <returns>GTIS处理结果</returns>
        private GtisResult ProcessGtisService(ServiceOpeningParams openingParams, GlobalSearchResult globalSearchResult)
        {
            var result = new GtisResult();
            
            try
            {
                LogUtil.AddLog("开始处理GTIS服务");

                // 基于预处理结果直接执行相应操作，无需重复查询和判断
                var preprocessResult = openingParams.GtisPreprocessResult;

                if (preprocessResult.OperationType == GtisOperationType.NewService)
                {
                    // 新增服务：调用GTIS新增接口
                    LogUtil.AddLog("基于预处理结果，执行GTIS新增服务");
                    result = CreateNewGtisService(openingParams, globalSearchResult, preprocessResult);
                }
                else if (preprocessResult.OperationType == GtisOperationType.UpdateService)
                {
                    // 更新服务：调用GTIS更新接口
                    LogUtil.AddLog($"基于预处理结果，执行GTIS更新服务，用户分类: {preprocessResult.UserClassification?.AddUsers?.Count ?? 0}新增, {preprocessResult.UserClassification?.UpdateUsers?.Count ?? 0}更新, {preprocessResult.UserClassification?.DelUsers?.Count ?? 0}删除");
                    result = UpdateExistingGtisService(openingParams, globalSearchResult, preprocessResult);
                }
                else
                {
                    throw new ApiException($"未知的GTIS操作类型: {preprocessResult.OperationType}");
                }

                if (result.Success)
                {
                    LogUtil.AddLog($"GTIS服务处理成功: {result.Message}");
                }
                else
                {
                    LogUtil.AddErrorLog($"GTIS服务处理失败: {result.Message}");
                }

            }
            catch (Exception ex)
            {
                var errorMsg = $"GTIS服务处理异常，需要GTIS: {openingParams.MainService.HasGtisApp}, 合同ID: {openingParams.Contract?.Id}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new ApiException(errorMsg);
            }

            return result;
        }
        
        #endregion
        
        #region SaleWits服务处理
        
        /// <summary>
        /// 处理SaleWits服务（直接读取预设资源并下发）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>SaleWits处理结果</returns>
        private async Task<SaleWitsResult> ProcessSaleWitsService(ServiceOpeningParams openingParams)
        {
            var result = new SaleWitsResult();

            try
            {
                LogUtil.AddLog("开始处理SaleWits服务");

                // 1. 获取数据准备阶段的资源配置
                var resourceDataResult = openingParams.SalesWitsSpecialConfig?.ResourceDataResult;
                if (resourceDataResult == null || !resourceDataResult.Success)
                {
                    throw new CRM2_API.Model.System.ApiException($"SaleWits资源数据准备失败: {resourceDataResult?.Message ?? "未找到资源数据"}");
                }

                // // 2. 获取GTIS租户ID
                var tenantId = await GetTenantIdFromGtis(openingParams);
                // LogUtil.AddLog($"获取GTIS租户ID成功: {tenantId}");
                //var tenantId = "";

                // 3. 直接使用预设的资源数量调用SaleWits接口（包含充值）
                var presetResources = resourceDataResult.PresetResources;

                // 确定操作人名称：定时下发使用固定名称，人工审核下发使用审核人名称
                var operatorName = !string.IsNullOrEmpty(openingParams.OperatorName)
                    ? openingParams.OperatorName
                    : "系统定时下发";

                var distributionResult = await CallSaleWitsResourceDistributionApi(
                    tenantId,
                    presetResources.EmailCount,
                    presetResources.TokenCount,
                    presetResources.RechargeAmount,
                    operatorName);

                if (distributionResult.IsSuccess)
                {
                    result.Success = true;
                    result.Message = $"SaleWits资源下发成功: 邮件{presetResources.EmailCount}封, Token{presetResources.TokenCount}万个";
                    result.DistributionId = distributionResult.DistributionId;
                    result.DistributionTime = distributionResult.DistributionTime;
                    result.ResourceManagementId = openingParams.SalesWitsSpecialConfig?.ServiceInfo?.ResourceManagementId;

                    // 4. 记录下发记录到数据库
                    RecordSaleWitsDistributionToDatabase(openingParams, resourceDataResult, distributionResult);

                    LogUtil.AddLog($"SaleWits服务处理成功: {result.Message}, ResourceManagementId: {result.ResourceManagementId}");
                }
                else
                {
                    result.Success = false;
                    result.Message = $"SaleWits资源下发失败: {distributionResult.ErrorMessage}";

                    LogUtil.AddErrorLog($"SaleWits资源下发失败: {distributionResult.ErrorMessage}");
                    throw new CRM2_API.Model.System.ApiException(result.Message);
                }

            }
            catch (CRM2_API.Model.System.ApiException)
            {
                // 重新抛出业务异常，保持原有的错误信息和上下文
                throw;
            }
            catch (Exception ex)
            {
                var errorMsg = $"SaleWits服务处理异常，需要变更SaleWits: {openingParams.MainService.IsSalesWitsAudit}, 合同ID: {openingParams.Contract?.Id}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }

            return result;
        }



        #endregion
        
        #region 慧思学院服务处理
        
        /// <summary>
        /// 处理慧思学院服务
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>慧思学院处理结果</returns>
        private CollegeResult ProcessCollegeService(ServiceOpeningParams openingParams)
        {
            var result = new CollegeResult();

            try
            {
                LogUtil.AddLog("开始处理慧思学院服务");

                // 1. 验证慧思学院配置
                if (openingParams.CollegeBasicConfig == null)
                {
                    var errorMsg = "慧思学院基础配置为空，无法处理服务开通";
                    LogUtil.AddErrorLog(errorMsg);
                    throw new CRM2_API.Model.System.ApiException(errorMsg);
                }

                // 2. 获取慧思学院配置信息
                var collegeConfig = GetCollegeConfiguration(openingParams);
                LogUtil.AddLog($"慧思学院配置获取成功: 账号数量={collegeConfig.MaxAccountsNum}, 服务周期={collegeConfig.ServiceCycleStart:yyyy-MM-dd}至{collegeConfig.ServiceCycleEnd:yyyy-MM-dd}");

                // 3. 更新用户慧思学院权限
                var permissionUpdateResult = UpdateUserCollegePermissions(openingParams, collegeConfig);

                if (permissionUpdateResult.Success)
                {
                    result.Success = true;
                    result.Message = $"慧思学院服务开通成功: {permissionUpdateResult.Message}";
                    result.UpdatedUsersCount = permissionUpdateResult.UpdatedUsersCount;
                    result.ConfigurationInfo = $"账号数量: {collegeConfig.MaxAccountsNum}, 服务周期: {collegeConfig.ServiceCycleStart:yyyy-MM-dd}至{collegeConfig.ServiceCycleEnd:yyyy-MM-dd}";

                    LogUtil.AddLog($"慧思学院服务处理成功: {result.Message}");
                }
                else
                {
                    result.Success = false;
                    result.Message = $"慧思学院权限更新失败: {permissionUpdateResult.ErrorMessage}";

                    LogUtil.AddErrorLog($"慧思学院权限更新失败: {permissionUpdateResult.ErrorMessage}");
                    throw new CRM2_API.Model.System.ApiException(result.Message);
                }

            }
            catch (CRM2_API.Model.System.ApiException)
            {
                // 重新抛出业务异常，保持原有的错误信息和上下文
                throw;
            }
            catch (Exception ex)
            {
                var errorMsg = $"慧思学院服务处理异常，需要变更慧思学院: {openingParams.MainService.IsCollegeAudit}, 合同ID: {openingParams.Contract?.Id}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new CRM2_API.Model.System.ApiException(errorMsg);
            }

            return result;
        }

        #endregion

        #region 环球搜变更辅助方法

        /// <summary>
        /// 创建新的环球搜服务（新开通场景）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="globalSearchParams">环球搜参数</param>
        /// <returns>创建结果</returns>
        private GlobalSearchResult CreateNewGlobalSearchService(ServiceOpeningParams openingParams, dynamic globalSearchParams)
        {
            var result = new GlobalSearchResult();
            var bllGlobalSearch = new BLL_GlobalSearch();

            try
            {
                // 准备请求参数
                var requestParams = new
                {
                    ContractNum = openingParams.Contract.ContractNum,
                    GtisAccount = GetMainGtisAccount(openingParams),
                    ServiceCycleStart = globalSearchParams.ServiceCycleStart ?? DateTime.Now,
                    ServiceCycleEnd = globalSearchParams.ServiceCycleEnd ?? DateTime.Now.AddYears(1),
                    SettlementLevel = globalSearchParams.SettlementLevel ?? "A+",
                    SettlementMonth = globalSearchParams.SettlementMonth ?? 12,
                    SubAccountCount = GetGlobalSearchSubAccountCount(openingParams),
                    Memo = "新开环球搜"
                };

                // 记录外部接口调用日志
                var batchResult = LogExternalCall("环球搜批量创建", openingParams.Contract.ContractNum, requestParams, () =>
                {
                    // 调用批量创建接口
                    return bllGlobalSearch.CreateGlobalSearchAccountBatch(
                        openingParams.Contract.ContractNum,
                        GetMainGtisAccount(openingParams),
                        globalSearchParams.ServiceCycleStart ?? DateTime.Now,
                        globalSearchParams.ServiceCycleEnd ?? DateTime.Now.AddYears(1),
                        globalSearchParams.SettlementLevel ?? "A+",
                        globalSearchParams.SettlementMonth ?? 12,
                        GetGlobalSearchSubAccountCount(openingParams),
                        "新开环球搜"
                    );
                });

                result.Success = batchResult.Success;
                result.Message = batchResult.Success ?
                    $"环球搜账号创建成功，共创建{batchResult.SuccessCount}个账号" :
                    "环球搜账号创建失败";
                result.PrimaryCode = batchResult.PrimaryAccount;
                result.SubCodes = batchResult.SubAccounts ?? new List<string>();

            }
            catch (Exception ex)
            {
                var errorMsg = $"环球搜账号创建异常，主账号数: {globalSearchParams.PrimaryAccountsNum}, 子账号数: {globalSearchParams.SubAccountsNum}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new ApiException(errorMsg);
            }

            return result;
        }

        /// <summary>
        /// 更新现有环球搜服务（续约场景）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="globalSearchParams">环球搜参数</param>
        /// <param name="preprocessResult">预处理结果</param>
        /// <returns>更新结果</returns>
        private GlobalSearchResult UpdateExistingGlobalSearchService(ServiceOpeningParams openingParams, dynamic globalSearchParams, GlobalSearchPreprocessResult preprocessResult)
        {
            var result = new GlobalSearchResult();
            var bllGlobalSearch = new BLL_GlobalSearch();

            try
            {
                if (preprocessResult.HasExistingService)
                {
                    // 更新现有账号的服务期
                    var primaryAccount = preprocessResult.ExistingAccounts.FirstOrDefault(a => a.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount);
                    if (primaryAccount == null)
                    {
                        throw new ApiException("原合同找不到环球搜服务主账号，无法续约");
                    }
                        // 准备请求参数
                        var requestParams = new
                        {
                            AccountNumber = primaryAccount.AccountNumber,
                            ContractNum = openingParams.Contract.ContractNum,
                            GtisAccount = GetMainGtisAccount(openingParams),
                            ServiceCycleStart = globalSearchParams.ServiceCycleStart ?? DateTime.Now,
                            ServiceCycleEnd = globalSearchParams.ServiceCycleEnd ?? DateTime.Now.AddYears(1),
                            SettlementLevel = globalSearchParams.SettlementLevel ?? "A+",
                            SettlementMonth = globalSearchParams.SettlementMonth ?? 12,
                            Memo = "续约环球搜"
                        };

                        // 记录外部接口调用日志
                        var updateResult = LogExternalCall("环球搜账号更新", openingParams.Contract.ContractNum, requestParams, () =>
                        {
                            return bllGlobalSearch.UpdateGlobalSearchUser(
                                primaryAccount.AccountNumber,
                                openingParams.Contract.ContractNum,
                                GetMainGtisAccount(openingParams),
                                globalSearchParams.ServiceCycleStart ?? DateTime.Now,
                                globalSearchParams.ServiceCycleEnd ?? DateTime.Now.AddYears(1),
                                globalSearchParams.SettlementLevel ?? "A+",
                                globalSearchParams.SettlementMonth ?? 12,
                                "续约环球搜"
                            );
                        });

                        if (!updateResult)
                        {
                            throw new ApiException($"更新环球搜账号失败: {primaryAccount.AccountNumber}");
                        }
                    

                    result.Success = true;
                    result.Message = "环球搜账号续约成功";
                    result.PrimaryCode = preprocessResult.ExistingPrimaryCode;
                    result.SubCodes = preprocessResult.ExistingSubCodes;
                }
                else
                {
                    // 如果没有现有服务，作为新增处理
                    LogUtil.AddLog("续约场景中未发现现有环球搜服务，作为新增处理");
                    return CreateNewGlobalSearchService(openingParams, globalSearchParams);
                }

            }
            catch (Exception ex)
            {
                var errorMsg = $"环球搜服务更新异常，合同ID: {openingParams.Contract?.Id}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new ApiException(errorMsg);
            }

            return result;
        }
        #endregion

        #region GTIS服务辅助方法

        /// <summary>
        /// 创建新的GTIS服务（新开通和续约重新开通场景）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="globalSearchResult">环球搜结果</param>
        /// <param name="preprocessResult">预处理结果</param>
        /// <returns>创建结果</returns>
        private GtisResult CreateNewGtisService(ServiceOpeningParams openingParams, GlobalSearchResult globalSearchResult, GtisPreprocessResult preprocessResult)
        {
            var result = new GtisResult();

            try
            {
                // 1. 格式化GTIS新建参数（直接使用wits_user，并传入环球搜结果）
                var addGtisUser = FormatGtisUserForAdd(openingParams, preprocessResult.CurrentWitsUsers, globalSearchResult);

                // 2. 绑定环球搜码（如果有）
                if (globalSearchResult != null && globalSearchResult.Success)
                {
                    // 绑定HqsCodeList到GTIS参数
                    addGtisUser.HqsCodeList = globalSearchResult.AllCodes;
                    LogUtil.AddLog($"GTIS新增用户绑定环球搜码列表，数量: {globalSearchResult.AllCodes?.Count ?? 0}");
                }

                // 3. 记录外部接口调用日志并调用GTIS开通接口
                var gtisRetModels = LogExternalCall("GTIS用户新增", openingParams.Contract.ContractNum, addGtisUser, () =>
                {
                    return _gtisBll.AddUser(addGtisUser).Result;
                });

                // 4. 构建返回结果
                result.Success = gtisRetModels != null && gtisRetModels.Count > 0;
                result.Message = result.Success ? "GTIS新账号开通成功" : "GTIS新账号开通失败";
                result.UserResults = gtisRetModels != null ? BuildGtisUserResults(preprocessResult.CurrentWitsUsers, gtisRetModels) : new List<GtisUserResult>();

            }
            catch (Exception ex)
            {
                var errorMsg = $"GTIS新账号开通异常，用户数量: {preprocessResult.CurrentWitsUsers?.Count ?? 0}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new ApiException(errorMsg);
            }

            return result;
        }

        /// <summary>
        /// 更新现有GTIS服务（变更和续约续期服务场景）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="globalSearchResult">环球搜结果</param>
        /// <param name="preprocessResult">预处理结果</param>
        /// <returns>更新结果</returns>
        private GtisResult UpdateExistingGtisService(ServiceOpeningParams openingParams, GlobalSearchResult globalSearchResult, GtisPreprocessResult preprocessResult)
        {
            var result = new GtisResult();

            try
            {
                // 1. 格式化GTIS续约参数
                LogUtil.AddLog("开始格式化GTIS续约参数");
                var renewalContact = FormatGtisUserForRenewal(openingParams, preprocessResult.UserClassification);

                // 2. 绑定环球搜码（优先使用本次开通的，如果没有则使用历史的）
                List<string> hqsCodeList = null;

                if (globalSearchResult != null && globalSearchResult.Success && globalSearchResult.AllCodes?.Count > 0)
                {
                    // 本次开通了环球搜，使用本次的环球搜码
                    hqsCodeList = globalSearchResult.AllCodes;
                    LogUtil.AddLog($"GTIS续约用户绑定本次开通的环球搜码列表，数量: {hqsCodeList.Count}");
                }
                else if (openingParams.GlobalSearchSpecialConfig?.HistoryGlobalSearchCodes?.Count > 0)
                {
                    // 本次没有开通环球搜，但历史有环球搜码，使用历史的
                    hqsCodeList = openingParams.GlobalSearchSpecialConfig.HistoryGlobalSearchCodes.Keys.ToList();
                    LogUtil.AddLog($"GTIS续约用户绑定历史环球搜码列表，数量: {hqsCodeList.Count}");
                }

                if (hqsCodeList != null && hqsCodeList.Count > 0)
                {
                    renewalContact.HqsCodeList = hqsCodeList;
                    LogUtil.AddLog($"最终绑定的环球搜码: {string.Join(", ", hqsCodeList)}");
                }
                else
                {
                    LogUtil.AddLog("未找到可用的环球搜码，不绑定HqsCodeList");
                }

                // 3. 记录外部接口调用日志并调用GTIS续约接口
                var gtisRetModels = LogExternalCall("GTIS用户续约", openingParams.Contract.ContractNum, renewalContact, () =>
                {
                    return _gtisBll.RenewalContact(renewalContact).Result;
                });

                // 4. 构建返回结果
                // 只要接口调用成功（不抛异常且不返回null）就算成功
                result.Success = gtisRetModels != null;
                result.Message = result.Success ? "GTIS续约/变更成功" : "GTIS续约/变更失败";
                if (gtisRetModels != null)
                {
                    BuildGtisRenewalResults(preprocessResult.UserClassification, gtisRetModels, result);
                }

                LogUtil.AddLog($"GTIS服务更新完成，结果: {result.Message}");

            }
            catch (Exception ex)
            {
                var errorMsg = $"GTIS服务更新异常，用户分类结果: {preprocessResult.UserClassification?.AddUsers?.Count ?? 0}新增用户, {preprocessResult.UserClassification?.UpdateUsers?.Count ?? 0}更新用户, {preprocessResult.UserClassification?.DelUsers?.Count ?? 0}删除用户, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new ApiException(errorMsg);
            }

            return result;
        }

        #endregion

        #region SaleWits资源下发处理方法

        /// <summary>
        /// 调用SaleWits资源下发和充值API
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <param name="emailCount">邮件数量</param>
        /// <param name="tokenCount">Token数量（万个）</param>
        /// <param name="rechargeAmount">充值金额（元）</param>
        /// <param name="opUserName">操作人名称</param>
        /// <returns>下发结果</returns>
        private async Task<SaleWitsApiResult> CallSaleWitsResourceDistributionApi(string tenantId, int emailCount, int tokenCount, decimal rechargeAmount,string opUserName)
        {
            var result = new SaleWitsApiResult
            {
                DistributionTime = DateTime.Now,
                TenantId = tenantId
            };

            try
            {
                LogUtil.AddLog($"开始调用SaleWits资源下发/充值API，租户ID: {tenantId}, 邮件数: {emailCount}, Token数: {tokenCount}万个, 充值金额: {rechargeAmount}元");

                // 准备请求参数
                var requestParams = new
                {
                    TenantId = tenantId,
                    RechargeAmount = (int)rechargeAmount, // 充值金额（元），转换为int类型
                    EmailCount = emailCount,
                    TokenCount = tokenCount * 10000 // 转换为实际token数量（万->个）
                };

                // 记录外部接口调用日志并调用SaleWits接口
                var creditResult = LogExternalCall("SaleWits资源下发", tenantId, requestParams, () =>
                {
                    return _saleWitsBll.CreditAsync(
                        tenantId,
                        (int)rechargeAmount, // 充值金额（元），转换为int类型
                        emailCount,
                        tokenCount * 10000, // 转换为实际token数量（万->个）
                        opUserName
                    ).Result;
                });

                // 处理接口返回结果
                if (creditResult.code == 200)
                {
                    result.IsSuccess = true;

                    // 构建操作描述
                    var operations = new List<string>();
                    if (rechargeAmount > 0) operations.Add($"充值{rechargeAmount}元");
                    if (emailCount > 0) operations.Add($"邮件{emailCount}封");
                    if (tokenCount > 0) operations.Add($"Token{tokenCount}万个");

                    result.Message = $"SaleWits操作成功: {string.Join(", ", operations)}";
                    result.DistributionId = Guid.NewGuid().ToString(); // 生成下发记录ID
                    result.ResponseData = creditResult.SerializeNewtonJson();

                    LogUtil.AddLog($"SaleWits API调用成功: {result.Message}");
                }
                else
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = $"SaleWits接口返回错误: 代码{creditResult.code}, 消息{creditResult.errCodeMsg}";
                    result.ResponseData = creditResult.SerializeNewtonJson();

                    LogUtil.AddErrorLog($"SaleWits API调用失败: {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                var errorMsg = $"SaleWits资源下发API调用异常，租户ID: {tenantId}, 错误: {ex.Message}";
                LogUtil.AddErrorLog($"{errorMsg}，详细异常: {ex}");
                throw new ApiException(errorMsg);
            }

            return result;
        }

        /// <summary>
        /// 记录SaleWits资源下发记录到数据库
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="resourceDataResult">资源数据结果</param>
        /// <param name="apiResult">API调用结果</param>
        /// <returns></returns>
        private void RecordSaleWitsDistributionToDatabase(
            ServiceOpeningParams openingParams,
            SaleWitsResourceDataResult resourceDataResult,
            SaleWitsApiResult apiResult)
        {
            try
            {
                LogUtil.AddLog("开始记录SaleWits资源下发记录到数据库");

                var distributionRecord = new DAL.DbModel.Crm2.Db_crm_salewits_resource_distribution
                {
                    Id = apiResult.DistributionId,
                    ResourceManagementId = openingParams.SalesWitsSpecialConfig?.ServiceInfo?.ResourceManagementId,
                    ServiceId = openingParams.MainService.Id,
                    ContractId = openingParams.Contract.Id,
                    TenantId = apiResult.TenantId,
                    DistributionType = resourceDataResult.DistributionType.ToString(),
                    AccountCount = openingParams.SalesWitsBasicConfig?.MaxAccountsNum ?? 0,
                    MonthsCount = resourceDataResult.PresetResources.MonthsCount,
                    EmailCount = resourceDataResult.PresetResources.EmailCount,
                    TokenCount = resourceDataResult.PresetResources.TokenCount,
                    RechargeAmount = resourceDataResult.PresetResources.RechargeAmount,
                    IsSuccess = apiResult.IsSuccess,
                    ErrorMessage = apiResult.IsSuccess ? null : apiResult.ErrorMessage,
                    DistributionTime = apiResult.DistributionTime,
                    CreateUser = "System",
                    CreateDate = DateTime.Now
                };

                // 保存到数据库
                var insertResult = DbOpe_crm_salewits_resource_distribution.Instance.Insert(distributionRecord);

                if (insertResult > 0)
                {
                    LogUtil.AddLog($"SaleWits资源下发记录保存成功，记录ID: {distributionRecord.Id}");
                }
                else
                {
                    LogUtil.AddErrorLog("SaleWits资源下发记录保存失败");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"记录SaleWits资源下发记录异常: {ex}");
                // 不抛出异常，避免影响主流程
            }
        }

        #endregion



        #region 外部接口调用日志

        /// <summary>
        /// 记录外部接口调用日志到数据库
        /// </summary>
        /// <param name="apiName">接口名称</param>
        /// <param name="contractNum">合同号</param>
        /// <param name="requestParams">请求参数对象</param>
        /// <param name="action">执行的操作</param>
        /// <returns>操作结果</returns>
        private T LogExternalCall<T>(string apiName, string contractNum, object requestParams, Func<T> action)
        {
            var startTime = DateTime.Now;
            var logId = Guid.NewGuid().ToString();

            try
            {
                // 执行业务操作
                var result = action();
                var duration = (int)(DateTime.Now - startTime).TotalMilliseconds;

                // 异步记录成功日志到数据库，不影响主业务
                Task.Run(() => SaveExternalCallLog(logId, apiName, contractNum, requestParams, result, true, duration, null));

                return result;
            }
            catch (Exception ex)
            {
                var duration = (int)(DateTime.Now - startTime).TotalMilliseconds;

                // 异步记录失败日志到数据库，不影响主业务
                Task.Run(() => SaveExternalCallLog(logId, apiName, contractNum, requestParams, null, false, duration, ex.Message));

                throw;
            }
        }

        /// <summary>
        /// 保存外部接口调用日志到数据库
        /// </summary>
        private void SaveExternalCallLog(string logId, string apiName, string contractNum, object requestParams, object? responseData, bool isSuccess, int duration, string? errorMessage)
        {
            try
            {
                var log = new Db_crm_external_system_call_log
                {
                    Id = logId,
                    SystemName = apiName.Contains("GTIS") ? "GTIS" : apiName.Contains("环球搜") ? "GlobalSearch" : "SaleWits",
                    ApiMethod = "POST",
                    ApiUrl = apiName,
                    BusinessType = "ServiceOpening",
                    BusinessId = contractNum,
                    ContractCode = contractNum,
                    RequestParams = requestParams?.SerializeNewtonJson() ?? "",
                    ResponseData = responseData?.SerializeNewtonJson() ?? "",
                    IsSuccess = isSuccess,
                    CallStartTime = DateTime.Now.AddMilliseconds(-duration),
                    CallEndTime = DateTime.Now,
                    Duration = duration,
                    ErrorMessage = errorMessage ?? "",
                    CreateDate = DateTime.Now,
                    CreateUser = "System"
                };

                new DbOpe_crm_external_system_call_log().Insert(log);
            }
            catch (Exception ex)
            {
                // 日志记录失败不影响主业务，只记录到系统日志
                LogUtil.AddErrorLog($"保存外部接口调用日志失败: {ex.Message}");
            }
        }

        #endregion
    }
}