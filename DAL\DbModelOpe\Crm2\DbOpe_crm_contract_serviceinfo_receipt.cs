using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using SqlSugar;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_serviceinfo_receipt表操作
    /// </summary>
    public class DbOpe_crm_contract_receiptregister_service : DbOperateCrm2<Db_crm_contract_receiptregister_service, DbOpe_crm_contract_receiptregister_service>
    {
        /// <summary>
        /// 关联服务和到账信息
        /// </summary>
        /// <param name="receiptRegisterIds"></param>
        /// <param name="servId"></param>
        /// <param name="productType"></param>
        public void LinkReceiptData(List<string> receiptRegisterIds, string servId, EnumProductType productType)
        {
            receiptRegisterIds.ForEach(rrId =>
            {
                var obj = new Db_crm_contract_receiptregister_service();
                obj.ReceiptRegisterId = rrId;
                obj.ServiceId = servId;
                obj.ProductType = productType;
                InsertData(obj);
            });
        }

        /// <summary>
        /// 关联服务和到账信息
        /// </summary>
        /// <param name="receiptRegisterIds"></param>
        /// <param name="servId"></param>
        /// <param name="productType"></param>
        public void LinkReceiptDataQueue(List<string> receiptRegisterIds, string servId, EnumProductType productType)
        {
            receiptRegisterIds.ForEach(rrId =>
            {
                var obj = new Db_crm_contract_receiptregister_service();
                obj.ReceiptRegisterId = rrId;
                obj.ServiceId = servId;
                obj.ProductType = productType;
                InsertDataQueue(obj);
            });
        }

        /// <summary>
        /// 查看当前到账是否与当前服务类型的服务数据进行过绑定
        /// </summary>
        /// <param name="receiptRegisterId"></param>
        /// <param name="productType"></param>
        /// <param name="servId"></param>
        /// <returns></returns>
        public bool CouldLinkService(string receiptRegisterId, EnumProductType productType, string servId)
        {
            return !Queryable
                .Where(e => e.ReceiptRegisterId == receiptRegisterId)
                .Where(e => e.ProductType == productType)
                .WhereIF(!string.IsNullOrEmpty(servId), e => e.ServiceId != servId)
                .Where(e => e.Deleted == false)
                .Any();

        }

        /// <summary>
        /// 被驳回服务重新初审通过时，删除被驳回服务的到账绑定关系
        /// </summary>
        /// <param name="servId"></param>
        public void DeleteReturnServiceLinkData(string servId)
        {
            Updateable
                .SetColumns(e => e.Deleted == true)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .SetColumns(e => e.UpdateUser == UserId)
                .Where(e => e.ServiceId == servId)
                .ExecuteCommand();

        }

        /// <summary>
        /// 被驳回服务重新初审通过时，删除被驳回服务的到账绑定关系
        /// </summary>
        /// <param name="servId"></param>
        public void DeleteReturnServiceLinkDataQueue(string servId)
        {
            Updateable
                .SetColumns(e => e.Deleted == true)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .SetColumns(e => e.UpdateUser == UserId)
                .Where(e => e.ServiceId == servId)
                .AddQueue();

        }

        /// <summary>
        /// 被驳回服务重新初审通过时，删除被驳回服务的到账绑定关系
        /// </summary>
        /// <param name="rrId"></param>
        public void DeleteReturnServiceLinkDataByReceiptRegisterId(string rrId)
        {
            Updateable
                .SetColumns(e => e.Deleted == true)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .SetColumns(e => e.UpdateUser == UserId)
                .Where(e => e.ReceiptRegisterId == rrId)
                .ExecuteCommand();

        }

        /// <summary>
        /// 查看当前到账是否与已绑定当前服务
        /// </summary>
        /// <param name="receiptRegisterId"></param>
        /// <param name="serviceId"></param>
        /// <returns></returns>
        public bool CheckLinkCurService(string receiptRegisterId, string serviceId)
        {
            return Queryable
                .Where(e => e.ReceiptRegisterId == receiptRegisterId)
                .Where(e => e.ServiceId == serviceId)
                .Where(e => e.Deleted == false)
                .Any();

        }

        /// <summary>
        /// 根据serviceId获取已绑定的到账信息
        /// </summary>
        /// <param name="serviceId"></param>
        /// <returns></returns>
        public List<string> GetReceiptRegisterIdsByServiceId(string serviceId)
        {
            return Queryable
                .Where(e => e.ServiceId == serviceId)
                .Where(e => e.Deleted == false)
                .Select(e => e.ReceiptRegisterId)
                .ToList();
        }

        public List<GetReceiptRegisterDataByServiceId_Out> GetReceiptRegisterDataByServiceId(string serviceId)
        {
            return Queryable
                .LeftJoin<Db_crm_contract_receiptregister>((e, f) => e.ReceiptRegisterId == f.Id)
                .Where((e, f) => e.ServiceId == serviceId)
                .Where((e, f) => e.Deleted == false)
                .Select((e, f) => new GetReceiptRegisterDataByServiceId_Out
                {
                    ReceiptRegisterId = e.ReceiptRegisterId,
                    SettlementCount = f.GlobalSearchUnit == null ? 0 : f.GlobalSearchUnit.Value,
                })
                .ToList();

        }

        /// <summary>
        /// 根据服务Id获取全部绑定到账的最晚到账时间
        /// </summary>
        /// <param name="serviceId"></param>
        /// <returns></returns>
        public DateTime? GetLastArriveDateByServiceId(string serviceId)
        {
            return Queryable
                .LeftJoin<Db_v_contractreceiptregistercollectioninfo>((e, f) => e.ReceiptRegisterId == f.ContractReceiptRegisterId)
                .Where(e => e.ServiceId == serviceId)
                .Where(e => e.Deleted == false)
                .Max((e, f) => f.ArrivalDate);
        }

        public decimal? GetGlobalSearchSettlementCountByReceiptRegisterId(List<string> rrIds)
        {
            return Db.Queryable<Db_crm_contract_receiptregister>()
                .Where(e => rrIds.Contains(e.Id))
                .Sum(e => e.GlobalSearchUnit);
        }
    }
}
