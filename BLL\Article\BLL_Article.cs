﻿using System.IO;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Web;
using Aspose.Pdf;
using CRM2_API.BLL.Common;
using CRM2_API.BLL.WorkLog;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Article;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using JiebaNet.Segmenter.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NPOI.HPSF;
using QCloud;
using static Lucene.Net.Util.Fst.Util;

namespace CRM2_API.BLL.Article
{
    public class BLL_Article : BaseBLL<BLL_Article>
    {
        private static readonly string ArticleTextKey = "ARTICLE_TEXT_";

        /// <summary>
        /// 添加文章信息
        /// </summary>
        /// <param name="addArticleIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public AddArticle_Out AddArticle(AddArticle_In addArticleIn)
        {
            AddArticle_Out result = new() { Data = 0 };
            var insertId = addArticleIn.Id ?? Guid.NewGuid().ToString();
            var newEntity = new Db_crm_article
            {
                Id = insertId,
                UserId = UserTokenInfo.id,
                Title = addArticleIn.Title,
                Type = addArticleIn.Type,
                SectionId = addArticleIn.SectionId,
                Content = addArticleIn.Content,
                State = addArticleIn.SubmitState,
                //State = EnumArticleSubmitState.NewBuilt,    //添加文章默认就是新建
                IsRecommend = 0,
                Deleted = false,
                CreateUser = TokenModel.Instance.id,
                CreateDate = DateTime.Now,
            };
            DbOpe_crm_article.Instance.InsertQueue(newEntity);

            List<Db_crm_article_articleintips> tipsList = new List<Db_crm_article_articleintips>();
            if (!string.IsNullOrEmpty(addArticleIn.ArtcleTipsJsonStr) && (addArticleIn.ArtcleTipsJsonStr == null || addArticleIn.ArtcleTips.Count == 0))
            {
                //addArticleIn.ArtcleTips = JsonSerializer.Deserialize<List<ArtcleTips>>(addArticleIn.ArtcleTipsJsonStr);
                List<string> ArtTipsList = addArticleIn.ArtcleTipsJsonStr.Split(',').ToList();
                if (ArtTipsList.Count > 0)
                { 
                    foreach (var artTips in ArtTipsList)
                    {
                        Db_crm_article_articleintips tip = new Db_crm_article_articleintips();
                        tip.Id = Guid.NewGuid().ToString();
                        tip.ArticleId = insertId;
                        tip.TipsId = artTips;
                        tip.Deleted = false;
                        tip.CreateDate = DateTime.Now;
                        tip.CreateUser = UserId;
                        tipsList.Add(tip);
                    }
                }
                //addArticleIn.ArtcleTips.ForEach(item =>
                //{
                //    Db_crm_article_articleintips tip = new Db_crm_article_articleintips();
                //    tip.Id = Guid.NewGuid().ToString();
                //    tip.ArticleId = insertId;
                //    tip.TipsId = item.Id;
                //    tip.Deleted = false;
                //    tip.CreateDate = DateTime.Now;
                //    tip.CreateUser = UserId;
                //    tipsList.Add(tip);

                //});
                DbOpe_crm_article_articleintips.Instance.InsertQueue(tipsList);
            }
            if (addArticleIn.ArticleAttachFile is not null and { Count: > 0 })
            {
                Util<DbOpe_crm_article_attachfile, BM_AttachFiles> util = new(DbOpe_crm_article_attachfile.Instance);
                bool uploadResult = util.UploadFiles(insertId, addArticleIn.ArticleAttachFile, AttachEnumOption.Article);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
            }
            DbOpe_crm_article.Instance.SaveQueues();
            result.Data = 1;
            return result;
        }

        /// <summary>
        /// 修改文章信息
        /// </summary>
        /// <param name="updateArticleIn"></param>
        /// <returns></returns>
        public UpdateArticle_Out UpdateArticle(UpdateArticle_In updateArticleIn)
        {
            UpdateArticle_Out result = new() { Data = 0 };
            var dbData = DbOpe_crm_article.Instance.QueryByPrimaryKey(updateArticleIn.Id);
            if (dbData is null)
            {
                throw new ApiException("要修改的知识库文章不存在!");
            }

            var checkArticleCreatorResult = CheckArticleCreator(dbData);
            if (!checkArticleCreatorResult)
            {
                throw new ApiException("无法修改非本人创建的知识库文章!");
            }

            if (dbData.State != EnumArticleSubmitState.Draft)
            {
                throw new ApiException("仅当知识库文章状态为草稿时可修改!");
            }
            //筛选出保存的附件
            List<string> saveIdList = GetAttIdListByContent(updateArticleIn.Content);
            //获取数据中已保存的附件[在提交前富文本的附件已经保存，因此更新的富文本的附件此时已经保存在数据库中了]
            List<Db_crm_article_attachfile> contentAtt = DbOpe_crm_article_attachfile.Instance.GetArticleAttachFilesByArticleid(updateArticleIn.Id, 2);
            List<string> contentAttIdList = contentAtt.Select(s => s.Id).ToList();
            //获取差集[标记删除]
            List<string> deleteAttIdList = contentAttIdList.Except(saveIdList).ToList();
            if (deleteAttIdList is not null and { Count: > 0 })
            {
                DbOpe_crm_article_attachfile.Instance.DeleteAttachFileByIdList(deleteAttIdList);
            }

            dbData.Title = updateArticleIn.Title;
            dbData.Type = updateArticleIn.Type;
            dbData.Content = updateArticleIn.Content;
            dbData.SectionId = updateArticleIn.SectionId;
            dbData.State = updateArticleIn.SubmitState;
            //dbData.State = EnumArticleSubmitState.NewBuilt; //修改后为发布状态
            dbData.UpdateUser = TokenModel.Instance.id;
            dbData.UpdateDate = DateTime.Now;
            DbOpe_crm_article.Instance.UpdateQueue(dbData);


            List<Db_crm_article_articleintips> tipsList = new List<Db_crm_article_articleintips>();
            if (!string.IsNullOrEmpty(updateArticleIn.ArtcleTipsJsonStr) && (updateArticleIn.ArtcleTipsJsonStr == null || updateArticleIn.ArtcleTips.Count == 0))
            {
                
                DbOpe_crm_article_articleintips.Instance.DeleteOldTips(updateArticleIn.Id);

                List<string> ArtTipsList = updateArticleIn.ArtcleTipsJsonStr.Split(',').ToList();
                if (ArtTipsList.Count > 0)
                {
                    foreach (var artTips in ArtTipsList)
                    {
                        Db_crm_article_articleintips tip = new Db_crm_article_articleintips();
                        tip.Id = Guid.NewGuid().ToString();
                        tip.ArticleId = updateArticleIn.Id;
                        tip.TipsId = artTips;
                        tip.Deleted = false;
                        tip.CreateDate = DateTime.Now;
                        tip.CreateUser = UserId;
                        tipsList.Add(tip);
                    }
                };
                DbOpe_crm_article_articleintips.Instance.InsertQueue(tipsList);
            }

            //附件操作
            var attachFileList = DbOpe_crm_article_attachfile.Instance.GetArticleAttachFilesByArticleid(updateArticleIn.Id);
            var attachFileIdList = attachFileList.Select(w => w.Id).ToList();
            if (updateArticleIn.ArticleAttachFile is null or { Count: 0 })
            {
                if (attachFileIdList is not null and { Count: > 0 })
                {
                    DbOpe_crm_article_attachfile.Instance.DeleteAttachFileByIdList(attachFileIdList);
                }
            }
            else
            {
                var attachFileUpdateIdList = new List<string>();
                FormFileCollection formFileCollection = new();
                foreach (Update_ArticleAttachFile updateArticleAttachFile in updateArticleIn.ArticleAttachFile)
                {
                    if (attachFileIdList.Contains(updateArticleAttachFile.Id))
                    {
                        attachFileUpdateIdList.Add(updateArticleAttachFile.Id);
                        continue;
                    }

                    if (updateArticleAttachFile.File != null)
                    {
                        formFileCollection.Add(updateArticleAttachFile.File);
                    }

                }

                if (formFileCollection.Count > 0)
                {
                    Util<DbOpe_crm_article_attachfile, BM_AttachFiles> util = new(DbOpe_crm_article_attachfile.Instance);
                    bool uploadResult = util.UploadFiles(dbData.Id, formFileCollection, AttachEnumOption.Article);
                    if (!uploadResult)
                    {
                        throw new ApiException("文件上传异常!");
                    }
                }
                var attachFileDeleteIdList = attachFileIdList.Except(attachFileUpdateIdList).ToList();
                DbOpe_crm_article_attachfile.Instance.DeleteAttachFileByIdList(attachFileDeleteIdList);
            }

            int execCount = DbOpe_crm_article.Instance.SaveQueues();
            if (execCount > 0)
            {
                result.Data = 1;
            }

            return result;
        }

        /// <summary>
        /// 删除文章信息
        /// </summary>
        /// <param name="articleIds"></param>
        /// <returns></returns>
        public DeleteArticle_Out DeleteArticle(string articleIds)
        {
            List<string> articleIdList = articleIds.Split(",").ToList();
            if (articleIdList is null or { Count: 0 })
            {
                throw new ApiException("文章表主键列表不可为空");
            }

            Dictionary<string, Db_crm_article> articleDict = DbOpe_crm_article.Instance.GetArticleDictByIdList(articleIdList);
            Db_crm_article temp;
            foreach (string articleId in articleIdList)
            {
                if (articleDict.ContainsKey(articleId))
                {
                    temp = articleDict[articleId];
                    var checkArticleCreatorResult = CheckArticleCreator(temp);
                    if (!checkArticleCreatorResult)
                    {
                        throw new ApiException($"无法删除非本人创建的知识库文章!");
                    }
                    if (temp.State != EnumArticleSubmitState.Draft)
                    {
                        throw new ApiException($"非草稿状态下的文章不可删除!");
                    }
                }
                else
                {
                    throw new ApiException($"对应的知识库文章不存在!");
                }
            }

            return DbOpe_crm_article.Instance.DeleteArticle(articleIdList);
        }

        /// <summary>
        /// 下架文章信息
        /// </summary>
        /// <param name="articleIds"></param>
        /// <returns></returns>
        public RevokeArticle_Out RevokeArticle(string articleIds)
        {
            List<string> articleIdList = articleIds.Split(",").ToList();
            if (articleIdList is null or { Count: 0 })
            {
                throw new ApiException("文章表主键列表不可为空");
            }

            Dictionary<string, Db_crm_article> articleDict = DbOpe_crm_article.Instance.GetArticleDictByIdList(articleIdList);
            Db_crm_article temp;
            foreach (string articleId in articleIdList)
            {
                if (articleDict.ContainsKey(articleId))
                {
                    temp = articleDict[articleId];
                    var checkArticleCreatorResult = CheckArticleCreator(temp);
                    if (!checkArticleCreatorResult)
                    {
                        throw new ApiException($"无法下架非本人创建的知识库文章!");
                    }
                    if (temp.State != EnumArticleSubmitState.NewBuilt)
                    {
                        throw new ApiException($"非发布状态下的文章不可下架!");
                    }
                }
                else
                {
                    throw new ApiException($"对应的知识库文章不存在!");
                }
            }

            return DbOpe_crm_article.Instance.RevokeArticle(articleIdList);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="attId"></param>
        /// <param name="response"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public IActionResult DownLoad(string attId, HttpResponse response)
        {
            Db_crm_article_attachfile att = DbOpe_crm_article_attachfile.Instance.QueryByPrimaryKey(attId);
            if (att is not null)
            {

                if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                {
                    QCloudOperator qCloudOperator = new QCloudOperator();
                    if (!qCloudOperator.Exists(att.FilePath))
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });

                    }
                    var data = qCloudOperator.OpenRead(att.FilePath);
                    using (MemoryStream memoryStream = new MemoryStream(data))
                    {
                        string encodeFilename = HttpUtility.UrlEncode(att.FilePath, Encoding.GetEncoding("UTF-8"));
                        MemoryStream resultStream = new MemoryStream(data);
                        response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                        return new FileStreamResult(resultStream, "application/octet-stream");
                    }
                }
                else
                {
                    if (!File.Exists(att.FilePath))
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    var stream = File.OpenRead(att.FilePath);
                    /*string fileExt = att.FileExtension;
                    var provider = new FileExtensionContentTypeProvider();
                    var contentType = provider.Mappings[$".{fileExt}"];*/
                    string encodeFilename = HttpUtility.UrlEncode(att.FileName, Encoding.GetEncoding("UTF-8"));
                    response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                    return new FileStreamResult(stream, "application/octet-stream");
                }
            }
            return new JsonResult(new
            {
                Msg = "文件不存在!"
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="upload"></param>
        /// <returns></returns>
        public IActionResult UploadImage(ArticleTextImageUpload upload)
        {
            string insertId = Guid.NewGuid().ToString();
            SnowflakeId s = new(2, 2);
            //string fileSavePath = AppSettings.fileSavePath ?? "/FileUpload";
            string fileSavePath = "/FileUpload";
            string fileHead = $"{fileSavePath}/ArticleText/{DateTime.Now:yyyyMMdd}";
            //设置文件上传路径
            string fileName = upload.File.FileName;
            string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
            string saveFileName = $"{fileName.Substring(0, fileName.LastIndexOf("."))}_{s.NextId()}.{fileExtension}";
            string fullSaveFilePath = $"{fileHead}/{saveFileName}";

            ////创建文件夹，保存文件
            string? path = Path.GetDirectoryName(fullSaveFilePath);
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            //将流写入文件
            using var stream = File.OpenWrite(fullSaveFilePath);
            upload.File.CopyTo(stream);

            Db_crm_article_attachfile attachfile = new()
            {
                Id = insertId,
                ArticleId = upload.ArticleId,
                Source = 2,
                FileName = fileName,
                FilePath = fullSaveFilePath,
                FileExtension = fileExtension,
                FileSize = (int)upload.File.Length,
                Deleted = false,
                CreateUser = UserTokenInfo.id,
                CreateDate = DateTime.Now
            };
            DbOpe_crm_article_attachfile.Instance.Insert(attachfile);

            return new OkObjectResult(new
            {
                location = $"/Article/DownLoad?attId={insertId}",
            });
        }

        /// <summary>
        /// 获取文件【方案2】
        /// </summary>
        /// <param name="attId"></param>
        /// <param name="articleId"></param>
        /// <param name="response"></param>
        /// <returns></returns>
        public IActionResult DownLoadNew(string attId, string articleId, HttpResponse response)
        {
            string cacheKey = $"{ArticleTextKey}{articleId}";
            Db_crm_article_attachfile att = default;
            if (RedisHelper.Exists(cacheKey))
            {
                List<Db_crm_article_attachfile> cacheAttList = RedisHelper.LRange<Db_crm_article_attachfile>(cacheKey, 0, -1).ToList();
                if (cacheAttList is not null and { Count: > 0 })
                {
                    att = cacheAttList.FirstOrDefault(w => w.Id.Equals(attId));
                }
            }

            if (att is null)
            {
                att = DbOpe_crm_article_attachfile.Instance.QueryByPrimaryKey(attId);
            }

            if (att is not null)
            {
                if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                {
                    QCloudOperator qCloudOperator = new QCloudOperator();
                    if (!qCloudOperator.Exists(att.FilePath))
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });

                    }
                    var data = qCloudOperator.OpenRead(att.FilePath);
                    using (MemoryStream memoryStream = new MemoryStream(data))
                    {
                        string encodeFilename = HttpUtility.UrlEncode(att.FilePath, Encoding.GetEncoding("UTF-8"));
                        MemoryStream resultStream = new MemoryStream(data);
                        response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                        return new FileStreamResult(resultStream, "application/octet-stream");
                    }
                }
                else
                {
                    if (!File.Exists(att.FilePath))
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    var stream = File.OpenRead(att.FilePath);
                    /*string fileExt = att.FileExtension;
                    var provider = new FileExtensionContentTypeProvider();
                    var contentType = provider.Mappings[$".{fileExt}"];*/
                    string encodeFilename = HttpUtility.UrlEncode(att.FileName, Encoding.GetEncoding("UTF-8"));
                    response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                    return new FileStreamResult(stream, "application/octet-stream");
                }
            }
            return new JsonResult(new
            {
                Msg = "文件不存在!"
            });
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="upload"></param>
        /// <returns></returns>
        public IActionResult UploadImageNew(ArticleTextImageUpload upload)
        {
            upload.ArticleId ??= Guid.NewGuid().ToString();
            string insertId = Guid.NewGuid().ToString();
            SnowflakeId s = new(2, 2);
            //string fileSavePath = AppSettings.fileSavePath ?? "/FileUpload";
            string fileSavePath = "/FileUpload";
            string fileHead = $"{fileSavePath}/ArticleText/{DateTime.Now:yyyyMMdd}";
            //设置文件上传路径
            string fileName = upload.File.FileName;
            string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
            string saveFileName = $"{fileName.Substring(0, fileName.LastIndexOf("."))}_{s.NextId()}.{fileExtension}";
            string fullSaveFilePath = $"{fileHead}/{saveFileName}";

            ////创建文件夹，保存文件
            string? path = Path.GetDirectoryName(fullSaveFilePath);
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            //将流写入文件
            using var stream = File.OpenWrite(fullSaveFilePath);
            upload.File.CopyTo(stream);

            Db_crm_article_attachfile attachfile = new()
            {
                Id = insertId,
                ArticleId = upload.ArticleId,
                Source = 2,
                FileName = fileName,
                FilePath = fullSaveFilePath,
                FileExtension = fileExtension,
                FileSize = (int)upload.File.Length,
                Deleted = false,
                CreateUser = UserTokenInfo.id,
                CreateDate = DateTime.Now
            };

            string cacheKey = $"{ArticleTextKey}{upload.ArticleId}";
            RedisHelper.RPush(cacheKey, attachfile);
            RedisHelper.Expire(cacheKey, 60 * 60 * 24);    //1天过期

            return new OkObjectResult(new
            {
                code = StatusCodes.Status200OK,
                articleId = upload.ArticleId,
                location = $"/Article/DownLoadNew?attId={insertId}&articleId={upload.ArticleId}",
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="articleId"></param>
        /// <returns></returns>
        public IActionResult ArticleTextHeartbeat(string articleId)
        {
            string cacheKey = $"{ArticleTextKey}{articleId}";
            if (RedisHelper.Exists(cacheKey))
            {
                RedisHelper.Expire(cacheKey, 60 * 60 * 24);    //1天过期
                return new OkObjectResult(new
                {
                    code = StatusCodes.Status200OK,
                    articleId,
                });
            }
            return new JsonResult(new
            {
                code = StatusCodes.Status404NotFound,
                Msg = "缓存key不存在!"
            });
        }

        /// <summary>
        /// 添加文章信息[方案2专属]
        /// </summary>
        /// <param name="addArticleIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public AddArticle_Out AddArticleNew(AddArticle_In addArticleIn)
        {
            if (string.IsNullOrEmpty(addArticleIn.Id))
            {
                string cacheKey = $"{ArticleTextKey}{addArticleIn.Id}";
                if (!string.IsNullOrEmpty(addArticleIn.Content) && RedisHelper.Exists(cacheKey))
                {
                    List<Db_crm_article_attachfile> cacheAttList = RedisHelper.LRange<Db_crm_article_attachfile>(cacheKey, 0, -1).ToList();
                    List<string> saveIdList = GetAttIdListByContent(addArticleIn.Content);
                    var saveAttList = cacheAttList.Where(w => saveIdList.Contains(w.Id)).ToList();
                    if (saveAttList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_article_attachfile.Instance.InsertQueue(saveAttList);
                    }
                    RedisHelper.Del(cacheKey);
                }
                else
                {
                    throw new ApiException("添加文章信息Id非报备值"); //方案2的id非前端生成，若携带id并且缓存中不存在则该id为伪造。
                }
            }

            AddArticle_Out result = new() { Data = 0 };
            var insertId = addArticleIn.Id ?? Guid.NewGuid().ToString();
            var newEntity = new Db_crm_article
            {
                Id = insertId,
                UserId = UserTokenInfo.id,
                Title = addArticleIn.Title,
                Type = addArticleIn.Type,
                Content = addArticleIn.Content,
                //State = addArticleIn.SubmitState,
                State = EnumArticleSubmitState.NewBuilt,    //添加文章默认就是新建
                IsRecommend = 0,
                Deleted = false,
                CreateUser = TokenModel.Instance.id,
                CreateDate = DateTime.Now,
            };
            DbOpe_crm_article.Instance.InsertQueue(newEntity);
            if (addArticleIn.ArticleAttachFile is not null and { Count: > 0 })
            {
                Util<DbOpe_crm_article_attachfile, BM_AttachFiles> util = new(DbOpe_crm_article_attachfile.Instance);
                bool uploadResult = util.UploadFiles(insertId, addArticleIn.ArticleAttachFile, AttachEnumOption.Article);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
            }
            DbOpe_crm_article.Instance.SaveQueues();
            result.Data = 1;
            return result;
        }

        /// <summary>
        /// 通过内容筛选出图片附件id列表
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        private List<string> GetAttIdListByContent(string content)
        {
            var result = new List<string>();
            Regex r = new("(api/Article/DownLoad)[A-Za-z]*\\?attId=[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}(&articleId=[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12})?(&Authorization=(\\w)+)?");
            var matches = r.Matches(content);
            if (matches.Count > 0)
            {
                var urlList = matches.Select(s => s.Value).ToList();
                foreach (string url in urlList)
                {
                    string queryStr = url.Substring(url.IndexOf("?") + 1);
                    var coll = HttpUtility.ParseQueryString(queryStr);
                    if (coll.Count > 0 && coll.AllKeys.Contains("attId"))
                    {
                        result.Add(coll["attId"]);
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 校验知识库的创建人是否为当前登陆人
        /// </summary>
        /// <param name="article"></param>
        /// <returns>true == 校验通过,false == 校验不通过</returns>
        private bool CheckArticleCreator(Db_crm_article article)
        {
            if (TokenModel.Instance is null) return false;
            if (string.IsNullOrEmpty(article.CreateUser)) return false;
            if (article.CreateUser.Equals(TokenModel.Instance.id)) return true;
            return false;
        }

        /// <summary>
        /// 获取当前登录人未读的知识库发布消息
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessagesList_Out> GetUNReadPublishMessage()
        {
            return DbOpe_crm_article.Instance.GetUNReadPublishMessage();
        }

        /// <summary>
        /// 获取当前登录人未读的知识库更新消息
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessageReminder> GetUNReadUpdateMessage()
        {
            return DbOpe_crm_article.Instance.GetUNReadUpdateMessage();
        }

        /// <summary>
        /// 获取当前登录人未读的知识库评论信息
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessageReminder> GetUNReadCommentMessage()
        {
            return DbOpe_crm_article_comment.Instance.GetUNReadCommentMessage();
        }

        /// <summary>
        /// 获取当前登录人未读的知识库回复信息
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessageReminder> GetUNReadReplyMessage()
        {
            return DbOpe_crm_article_comment.Instance.GetUNReadReplyMessage();
        }

        /// <summary>
        /// 当前用户是否拥有知识库权限
        /// 通过是否拥有GetById权限判断
        /// </summary>
        /// <returns></returns>
        public bool CurrentHasArticleDataAuth()
        {
            string articleDataFunctionId = "5ba03973-214c-11ee-bb56-30d042e24322";  //根据Id获取详情FormId,根据此Id判断用户是否有权限
            var interfaces = RedisCache.UserInterfaceRight.GetUserInterfaceRight(UserId);   // 当前用户拥有的接口权限
            return interfaces.FirstOrDefault(w => w.FormId.Equals(articleDataFunctionId)) != null;
        }

        /// <summary>
        /// 新增板块
        /// </summary>
        /// <param name="addArticleSectionIn"></param>
        /// <returns></returns>
        public ArticleBase_Out AddArticleSection(AddArticleSection_In addArticleSectionIn)
        {
            ArticleBase_Out result = new ArticleBase_Out();
            if (DbOpe_crm_article_section.Instance.SectionNameValidating(addArticleSectionIn.SectionName))
            {
                throw new ApiException("板块名称已存在,请检查");
            }

            if (!addArticleSectionIn.VisibleRange.IsNotNull())
            {
                throw new ApiException("可见范围必选");
            }
            if (addArticleSectionIn.VisibleRange.IsNotNull() && !addArticleSectionIn.VisibleType.IsNotNull())
            {
                throw new ApiException("可见类型必选");
            }
            if (addArticleSectionIn.VisibleType.IsNotNull() && addArticleSectionIn.UserOrOrgList.Count == 0)
            {
                throw new ApiException("可见人员/机构必选");
            }

            Db_crm_article_section crmSection = addArticleSectionIn.MappingTo<Db_crm_article_section>();
            //生成主键
            crmSection.Id = Guid.NewGuid().ToString();

            crmSection.SectionNum = (DbOpe_crm_article_section.Instance.GetSectionNumCount() + 1).ToString().PadLeft(5, '0');
            crmSection.VisibleRange = (int)addArticleSectionIn.VisibleRange;
            crmSection.Deleted = false;
            //创建人 当前登录人员
            crmSection.CreateUser = UserTokenInfo.id;
            crmSection.CreateDate = DateTime.Now;

            List<Db_crm_article_sectionrange> sectionRangeList = new List<Db_crm_article_sectionrange>();
            //如果是范围可见
            if (addArticleSectionIn.VisibleRange == EnumSectionVisibleRange.Parts)
            {
                if (addArticleSectionIn.UserOrOrgList.Count > 0)
                {

                    addArticleSectionIn.UserOrOrgList.ForEach(item =>
                    {
                        Db_crm_article_sectionrange sectionRange = item.MappingTo<Db_crm_article_sectionrange>();
                        sectionRange.Id = Guid.NewGuid().ToString();
                        sectionRange.SectionId = crmSection.Id;
                        sectionRange.OrgUserId = item.OrgUserId;
                        sectionRange.VisibleType = addArticleSectionIn.VisibleType.IsNotNull() ? (int)addArticleSectionIn.VisibleType : 0;
                        sectionRange.Deleted = false;
                        sectionRange.CreateUser = UserTokenInfo.id;
                        sectionRange.CreateDate = DateTime.Now;
                        sectionRangeList.Add(sectionRange);
                    });
                }
            }

            List<Db_crm_article_section> childSectionList = new List<Db_crm_article_section>();
            //子版块
            if (addArticleSectionIn.ChildSection != null && addArticleSectionIn.ChildSection.Count > 0)
            {
                addArticleSectionIn.ChildSection.ForEach(item =>
                {
                    Db_crm_article_section childSection = item.MappingTo<Db_crm_article_section>();
                    //生成主键
                    childSection.Id = Guid.NewGuid().ToString();
                    //编码（后台赋值）以3位堆积数字按顺序生成，如：001
                    //似乎没用
                    childSection.SectionNum = null;
                    childSection.VisibleRange = (int)addArticleSectionIn.VisibleRange;
                    //子版块增加id
                    childSection.RootId = crmSection.Id;
                    childSection.ParentId = crmSection.Id;

                    childSection.Deleted = false;
                    //创建人 当前登录人员
                    childSection.CreateUser = UserTokenInfo.id;
                    childSection.CreateDate = DateTime.Now;

                    childSectionList.Add(childSection);
                });
            }

            DbOpe_crm_article_section.Instance.InsertQueue(crmSection);
            DbOpe_crm_article_section.Instance.InsertQueue(childSectionList);
            DbOpe_crm_article_sectionrange.Instance.InsertQueue(sectionRangeList);
            DbOpe_crm_article_section.Instance.SaveQueues();

            result.Data = 1;
            return result;
        }
        /// <summary>
        /// 更新板块
        /// </summary>
        /// <param name="updateArticleSection"></param>
        /// <returns></returns>
        public ArticleBase_Out UpdateArticleSection(UpdateArticleSection_In updateArticleSection)
        {
            return DbOpe_crm_article_section.Instance.UpdateArticleSection(updateArticleSection);
        }

        public AddTips_Out AddArticleTipsList(List<AddArticleTips_In> addArticleTipsList)
        {
            AddTips_Out result = new AddTips_Out();
            string message = string.Empty;
            if (addArticleTipsList.Count > 0)
            {
                List<Db_crm_article_tips> tipsList = new List<Db_crm_article_tips>();
                addArticleTipsList.ForEach(item =>
                {
                    if (DbOpe_crm_article_tips.Instance.TipsNameValidating(item.TipsName))
                    {
                        message = " " + item.TipsName + " ";
                    }
                    else
                    {
                        Db_crm_article_tips tip = item.MappingTo<Db_crm_article_tips>();
                        tip.CreateDate = DateTime.Now;
                        tip.CreateUser = UserId;
                        tip.UpdateDate = DateTime.Now;
                        tip.UpdateUser = UserId;
                        tipsList.Add(tip);
                    }
                });
                DbOpe_crm_article_tips.Instance.InsertListDataQueue(tipsList);
            }
            DbOpe_crm_article_tips.Instance.SaveQueues();

            if (message.IsNullOrEmpty())
            {
                result.Data = 1;
            }
            else
            {
                result.Data = 0;
                result.Message = "标签" + message + "已存在";
            }
            return result;
        }
    }
}