using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 报告附件表
    /// </summary>
    [SugarTable("crm_report_attachment")]
    public partial class Db_crm_report_attachment
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Required]
        public string Id { get; set; }

        /// <summary>
        /// 报告ID
        /// </summary>
        [Required]
        public string ReportId { get; set; }

        /// <summary>
        /// 报告类型：1-日报，2-周报，3-月报
        /// </summary>
        public int ReportType { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 报告日期
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// 模块键名（如：work_review、customer_add等）
        /// </summary>
        public string ModuleKey { get; set; }

        /// <summary>
        /// 模块标题（冗余字段，便于展示）
        /// </summary>
        public string ModuleTitle { get; set; }

        /// <summary>
        /// 模块排序（冗余字段）
        /// </summary>
        public int ModuleOrder { get; set; }

        /// <summary>
        /// 章节键名（如：summary、phone_calls等）
        /// </summary>
        public string SectionKey { get; set; }

        /// <summary>
        /// 章节标题（冗余字段，便于展示）
        /// </summary>
        public string SectionTitle { get; set; }

        /// <summary>
        /// 章节排序（冗余字段）
        /// </summary>
        public int SectionOrder { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        [Required]
        public string FileName { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        [Required]
        public string FilePath { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件类型（扩展名，如：.jpg、.png、.pdf等）
        /// </summary>
        public string FileType { get; set; }



        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        [Required]
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateDate { get; set; }
    }
} 