-- 服务变更原因可变更字段配置表 - 追加缺失字段脚本
-- 执行日期: 2025-01-29
-- 目的：为原始创建脚本补充缺失的字段

-- ================================
-- 1. 添加字段权限控制字段
-- ================================

-- 检查并添加FieldPermission字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'crm_service_change_reason_field_config' 
                     AND COLUMN_NAME = 'FieldPermission');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE crm_service_change_reason_field_config ADD COLUMN FieldPermission INT DEFAULT 1 COMMENT ''字段权限：1-可修改，2-仅展示''', 
    'SELECT ''FieldPermission字段已存在'' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加ApplyScenario字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'crm_service_change_reason_field_config' 
                     AND COLUMN_NAME = 'ApplyScenario');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE crm_service_change_reason_field_config ADD COLUMN ApplyScenario VARCHAR(10) DEFAULT ''both'' COMMENT ''适用场景：apply-申请，audit-审核，both-通用''', 
    'SELECT ''ApplyScenario字段已存在'' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加TriggerFields字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'crm_service_change_reason_field_config' 
                     AND COLUMN_NAME = 'TriggerFields');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE crm_service_change_reason_field_config ADD COLUMN TriggerFields TEXT NULL COMMENT ''触发字段列表：JSON格式，当这些字段变更时本字段在审核时可修改''', 
    'SELECT ''TriggerFields字段已存在'' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ================================
-- 2. 添加复杂对象字段支持
-- ================================

-- 检查并添加IsArray字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'crm_service_change_reason_field_config' 
                     AND COLUMN_NAME = 'IsArray');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE crm_service_change_reason_field_config ADD COLUMN IsArray TINYINT(1) DEFAULT 0 COMMENT ''是否为数组/列表类型字段：0-否，1-是''', 
    'SELECT ''IsArray字段已存在'' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加FieldType字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'crm_service_change_reason_field_config' 
                     AND COLUMN_NAME = 'FieldType');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE crm_service_change_reason_field_config ADD COLUMN FieldType VARCHAR(50) DEFAULT ''simple'' COMMENT ''字段类型：simple-简单类型，list-列表类型，object-对象类型''', 
    'SELECT ''FieldType字段已存在'' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加ComparisonMethod字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = DATABASE() 
                     AND TABLE_NAME = 'crm_service_change_reason_field_config' 
                     AND COLUMN_NAME = 'ComparisonMethod');

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE crm_service_change_reason_field_config ADD COLUMN ComparisonMethod VARCHAR(50) DEFAULT ''direct'' COMMENT ''比较方式：direct-直接比较，list_diff-列表差异，json_compare-JSON比较''', 
    'SELECT ''ComparisonMethod字段已存在'' AS message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ================================
-- 3. 更新表注释
-- ================================

-- 更新表注释以反映完整功能
ALTER TABLE crm_service_change_reason_field_config 
COMMENT = '服务变更原因可变更字段配置表（支持权限控制、场景区分和复杂对象字段）';

-- ================================
-- 4. 验证字段添加结果
-- ================================

SELECT '字段追加脚本执行完成！' AS message;

-- 显示表结构
SELECT 
    COLUMN_NAME as '字段名',
    COLUMN_TYPE as '字段类型',
    IS_NULLABLE as '是否可空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '字段说明'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_service_change_reason_field_config'
ORDER BY ORDINAL_POSITION;

-- 显示新增字段统计
SELECT 
    COUNT(*) as '总字段数',
    SUM(CASE WHEN COLUMN_NAME IN ('FieldPermission', 'ApplyScenario', 'TriggerFields', 'IsArray', 'FieldType', 'ComparisonMethod') THEN 1 ELSE 0 END) as '新增字段数'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_service_change_reason_field_config';

-- ================================
-- 5. 字段说明总结
-- ================================

/*
新增字段说明：

1. FieldPermission (INT): 字段权限控制
   - 1: 可修改
   - 2: 仅展示

2. ApplyScenario (VARCHAR(10)): 适用场景
   - 'apply': 申请场景
   - 'audit': 审核场景  
   - 'both': 通用场景

3. TriggerFields (TEXT): 触发字段列表
   - JSON格式存储
   - 当这些字段变更时，本字段在审核时可修改

4. IsArray (TINYINT(1)): 是否为数组/列表类型
   - 0: 否
   - 1: 是

5. FieldType (VARCHAR(50)): 字段类型
   - 'simple': 简单类型
   - 'list': 列表类型
   - 'object': 对象类型

6. ComparisonMethod (VARCHAR(50)): 比较方式
   - 'direct': 直接比较
   - 'list_diff': 列表差异比较
   - 'json_compare': JSON比较

这些字段的添加使得服务变更字段配置表能够支持：
- 更精细的权限控制（可修改 vs 仅展示）
- 场景区分（申请 vs 审核）
- 复杂对象字段的处理（数组、列表、对象）
- 字段间的依赖关系（触发字段）
*/ 