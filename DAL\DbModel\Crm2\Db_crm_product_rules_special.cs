﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_product_rules_special")]
    public class Db_crm_product_rules_special
    {
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:用户主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// Desc:允许超级子账号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool AllowSuperSubAccount { get; set; }

        /// <summary>
        /// Desc:允许专业版进口搭配零售
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool AllowGtisImportVIP { get; set; }

        /// <summary>
        /// Desc:允许专业版出口搭配零售
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool AllowGtisExportVIP { get; set; }

        /// <summary>
        /// Desc:允许添加专业版出口
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool AllowGtisExport { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:False
        /// </summary>           
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
