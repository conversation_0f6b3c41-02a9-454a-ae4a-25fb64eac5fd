﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using LgyUtil;
using SqlSugar;

namespace CRM2_API.BLL
{
    public class BLL_Dictionary : BaseBLL<BLL_Dictionary>
    {
        /// <summary>
        /// 添加数据字典信息
        /// </summary>
        /// <param name="addDictionary_In"></param>
        public void AddDictionary(AddDictionary_In addDictionary_In) {

            Db_sys_dictionary sys_dictionary = addDictionary_In.MappingTo<Db_sys_dictionary>();
            sys_dictionary.Id = Guid.NewGuid().ToString();
            sys_dictionary.Deleted = false;
            sys_dictionary.CreateUser = UserTokenInfo.id;
            sys_dictionary.CreateDate = DateTime.Now;
            DbOpe_sys_dictionary.Instance.Insert(sys_dictionary);
        }

        /// <summary>
        /// 修改数据字典信息
        /// </summary>
        /// <param name="updateDictionary_In"></param>
        public void UpdateDictionary(UpdateDictionary_In updateDictionary_In)
        {
            //Db_sys_dictionary dictionary = updateDictionary_In.MappingTo<Db_sys_dictionary>();
            //dictionary.UpdateUser = UserTokenInfo.id;
            //dictionary.UpdateDate = DateTime.Now;
            //DbOpe_sys_dictionary.Instance.(dictionary);

            DbOpe_sys_dictionary.Instance.UpdateDictionary(updateDictionary_In, UserTokenInfo.id);
        }

        /// <summary>
        /// 删除数据字典信息，多条记录全部执行成功则返回成功，否则返回失败。只做逻辑删除，修改Deleted字段为true。
        /// </summary>
        /// <param name="Ids"></param>
        public void DeleteDictionary(string Ids)
        {
            //验证传入的Ids是否为空
            if (string.IsNullOrEmpty(Ids)) {
                throw new ApiException("数据字典主键不可为空");
            }
            var idList = Ids.Split(",").ToList();
            DbOpe_sys_dictionary.Instance.DeleteDictionary(idList);
        }
    }
}
