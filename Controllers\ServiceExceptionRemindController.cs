﻿using CRM2_API.BLL;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.IO;
using static CRM2_API.Common.Filter.WorkLog;
using static CRM2_API.Model.ControllersViewModel.VM_ServiceExceptionRemind;

namespace CRM2_API.Controllers
{
    [Description("服务异常提醒控制器")]
    public class ServiceExceptionRemindController : MyControllerBase
    {

        public ServiceExceptionRemindController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {

        }
        /// <summary>
        /// 异常提醒录入检索
        /// </summary>
        /// <param name="addExceptionRemind_Search_In"></param>
        [HttpPost]
        public AddExceptionRemind_Search_Out AddExceptionRemind_Search(AddExceptionRemind_Search_In addExceptionRemind_Search_In)
        {
            return BLL_ServiceExceptionRemind.Instance.AddExceptionRemind_Search(addExceptionRemind_Search_In);
        }
        /// <summary>
        /// 异常提醒录入
        /// </summary>
        /// <param name="addExceptionRemind_In"></param>
        [HttpPost]
        public void AddExceptionRemind(AddExceptionRemind_In addExceptionRemind_In)
        {
            BLL_ServiceExceptionRemind.Instance.AddExceptionRemind(addExceptionRemind_In);
        }


        /// <summary>
        /// 查询异常记录
        /// </summary>
        /// <param name="condition_In"></param>
        [HttpPost]
        public ApiTableOut<SearchExceptionRemindList_Out> SearchExceptionRemindList(SearchExceptionRemindList_In condition_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_serviceinfo_exception_remind.Instance.SearchExceptionRemindList(condition_In, ref total);
            return GetApiTableOut(list, total);
        }


        /// <summary>
        /// 异常记录详情
        /// </summary>
        /// <param name="remind_Id"></param>
        /// <returns></returns>
        [HttpPost]
        public SearchExceptionRemindList_Out GetExceptionRemind(string remind_Id)
        {
            return DbOpe_crm_contract_serviceinfo_exception_remind.Instance.GetExceptionRemind(remind_Id);
        }

        /// <summary>
        /// 处理异常记录
        /// </summary>
        /// <param name="deal_In"></param>
        [HttpPost]
        public void DealExceptionRemind(DealExceptionRemind_In deal_In)
        {
            BLL_ServiceExceptionRemind.Instance.DealExceptionRemind(deal_In);
        }


        /// <summary>
        /// 下载异常提醒信息列表内容
        /// </summary>
        /// <param name="condition_In"></param>
        /// <returns></returns>
        [HttpPost]
        public Stream DownloadExceptionRemindList(SearchExceptionRemindList_In condition_In)
        {
            return BLL_ServiceExceptionRemind.Instance.DownloadExceptionRemindInfo(condition_In);
        }

    }


}
