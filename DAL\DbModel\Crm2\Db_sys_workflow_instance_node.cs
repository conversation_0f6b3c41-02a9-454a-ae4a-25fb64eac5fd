﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///审批流节点实例节点表
    ///</summary>
    [SugarTable("sys_workflow_instance_node")]
    public class Db_sys_workflow_instance_node
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:审批流节点实例表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string WorkFlowInstanceId {get;set;}

           /// <summary>
           /// Desc:审批流表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string WorkFlowId {get;set;}

           /// <summary>
           /// Desc:审批流节点id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string WorkFlowNodeId {get;set;}

           /// <summary>
           /// Desc:所属表单id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string FormId {get;set;}

           /// <summary>
           /// Desc:记录id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string DataId {get;set;}

           /// <summary>
           /// Desc:实际处理人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OperationUserId {get;set;}

           /// <summary>
           /// Desc:下一个流程节点id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string NextFormId {get;set;}

           /// <summary>
           /// Desc:下一个流程节点实例节点id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string NextWorkFlowInstanceNodeId { get; set; }

           /// <summary>
           /// Desc:是否叶子节点
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? IsLeaf { get; set; }

           /// <summary>
           /// Desc:记录备注
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string DataRemark { get; set; }

           /// <summary>
           /// Desc:记录状态
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string DataState { get; set; }

           /// <summary>
           /// Desc:记录操作
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string DataOperate { get; set; }

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
