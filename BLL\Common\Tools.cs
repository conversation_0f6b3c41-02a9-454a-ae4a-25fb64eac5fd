﻿using CRM2_API.Model.BusinessModel;
using Microsoft.AspNetCore.Http;
using System.Collections;
using System.Data;
using System.Reflection;
using System.Text.RegularExpressions;

namespace CRM2_API.BLL.Common
{
    public class Tools
    {
        public static DataTable ToDataTable<T>(IEnumerable<T> collection)
        {
            var props = typeof(T).GetProperties();
            var dt = new DataTable();
            dt.Columns.AddRange(props.Select(p => new DataColumn(p.Name, p.PropertyType)).ToArray());
            if (collection.Count() > 0)
            {
                for (int i = 0; i < collection.Count(); i++)
                {
                    ArrayList tempList = new ArrayList();
                    foreach (PropertyInfo pi in props)
                    {
                        object obj = pi.GetValue(collection.ElementAt(i), null);
                        tempList.Add(obj);
                    }
                    object[] array = tempList.ToArray();
                    dt.LoadDataRow(array, true);
                }
            }
            return dt;
        }

        /// <summary>
        /// 将List转化为DataTable核心方法
        /// </summary>
        /// <returns></returns>
        public DataTable ListToDataTable<T>(List<T> data)
        {
            #region 创建一个DataTable，以实体名称作为DataTable名称

            var tableName = typeof(T).Name;
            DataTable dt = new DataTable
            {
                TableName = tableName
            };

            #endregion

            #region 拿取列名，以实体的属性名作为列名       

            var properties = typeof(T).GetProperties();
            foreach (var item in properties)
            {
                var curFileName = item.Name;
                dt.Columns.Add(curFileName);
            }

            #endregion

            #region 列赋值
            foreach (var item in data)
            {
                DataRow dr = dt.NewRow();
                var columns = dt.Columns;

                var curPropertyList = item.GetType().GetProperties();
                foreach (var p in curPropertyList)
                {
                    var name = p.Name;
                    var curValue = p.GetValue(item);

                    int i = columns.IndexOf(name);
                    dr[i] = curValue;
                }

                dt.Rows.Add(dr);
            }

            #endregion  

            return dt;
        }

        public static string Format(string str, object obj)
        {
            if (str.Length == 0)
            {
                return str;
            }
            string s = str;
            if (obj.GetType().Name == "JObject")
            {
                foreach (var item in (Newtonsoft.Json.Linq.JObject)obj)
                {
                    var k = item.Key.ToString();
                    var v = item.Value.ToString();
                    s = Regex.Replace(s, "\\{" + k + "\\}", v, RegexOptions.IgnoreCase);
                }
            }
            else
            {
                foreach (System.Reflection.PropertyInfo p in obj.GetType().GetProperties())
                {
                    var key = p.Name;
                    var value = "";
                    if (p.GetValue(obj) != null)
                    {
                        value = p.GetValue(obj).ToString();
                    }
                    s = Regex.Replace(s, "\\{" + key + "\\}", value, RegexOptions.IgnoreCase);
                }
            }
            return s;
        }

        public static List<BM_FileInfo> GetUploadInfoId(List<AttachFileUploadInfo> FileUploadInfo)
        {
            List<BM_FileInfo> result = new List<BM_FileInfo>();
            foreach (var item in FileUploadInfo)
            {
                if (item.Id != null)
                {
                    if (item.Id != "")
                    {
                        result.Add(new BM_FileInfo { Id = item.Id });
                    }
                }
            }
            return result;
        }

        public static FormFileCollection GetUploadInfoFile(List<AttachFileUploadInfo> FileUploadInfo)
        {
            FormFileCollection result = new FormFileCollection();
            foreach (var item in FileUploadInfo)
            {
                if (item.File != null)
                {
                    result.Add(item.File);
                }
            }
            return result;
        }

        /// <summary>
        /// 阿拉伯数字转换成中文数字
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        public static string NumToChinese(string x)
        {
            string[] pArrayNum = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
            //为数字位数建立一个位数组  
            string[] pArrayDigit = { "", "十", "百", "千" };
            //为数字单位建立一个单位数组  
            string[] pArrayUnits = { "", "万", "亿", "万亿" };
            var pStrReturnValue = ""; //返回值  
            var finger = 0; //字符位置指针  
            var pIntM = x.Length % 4; //取模  
            int pIntK;
            if (pIntM > 0)
                pIntK = x.Length / 4 + 1;
            else
                pIntK = x.Length / 4;
            //外层循环,四位一组,每组最后加上单位: ",万亿,",",亿,",",万,"  
            for (var i = pIntK; i > 0; i--)
            {
                var pIntL = 4;
                if (i == pIntK && pIntM != 0)
                    pIntL = pIntM;
                //得到一组四位数  
                var four = x.Substring(finger, pIntL);
                var P_int_l = four.Length;
                //内层循环在该组中的每一位数上循环  
                for (int j = 0; j < P_int_l; j++)
                {
                    //处理组中的每一位数加上所在的位  
                    int n = Convert.ToInt32(four.Substring(j, 1));
                    if (n == 0)
                    {
                        if (j < P_int_l - 1 && Convert.ToInt32(four.Substring(j + 1, 1)) > 0 && !pStrReturnValue.EndsWith(pArrayNum[n]))
                            pStrReturnValue += pArrayNum[n];
                    }
                    else
                    {
                        if (!(n == 1 && (pStrReturnValue.EndsWith(pArrayNum[0]) | pStrReturnValue.Length == 0) && j == P_int_l - 2))
                            pStrReturnValue += pArrayNum[n];
                        pStrReturnValue += pArrayDigit[P_int_l - j - 1];
                    }
                }
                finger += pIntL;
                //每组最后加上一个单位:",万,",",亿," 等  
                if (i < pIntK) //如果不是最高位的一组  
                {
                    if (Convert.ToInt32(four) != 0)
                        //如果所有4位不全是0则加上单位",万,",",亿,"等  
                        pStrReturnValue += pArrayUnits[i - 1];
                }
                else
                {
                    //处理最高位的一组,最后必须加上单位  
                    pStrReturnValue += pArrayUnits[i - 1];
                }
            }
            return pStrReturnValue;
        }
    }
}
