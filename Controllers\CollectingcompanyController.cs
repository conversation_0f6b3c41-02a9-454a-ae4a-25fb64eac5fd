﻿using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using CRM2_API.Common.Filter;

namespace CRM2_API.Controllers
{
    [Description("收款公司(乙方公司)")]
    public class CollectingcompanyController : MyControllerBase
    {
        public CollectingcompanyController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取收款公司信息表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<ApiDictionary> GetCollectingcompanyList()
        {
            return DbOpe_crm_collectingcompany.Instance.GetCollectingcompanyList();
        }

        /// <summary>
        /// 根据用户id获取收款公司信息表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<ApiDictionary> GetCollectingcompanyListByUserId(bool isOverseasCustomer)
        {
            return DbOpe_crm_collectingcompany.Instance.GetCollectingcompanyListByUserId(isOverseasCustomer);
        }

        /// <summary>
        /// 根据用户id和合同id获取收款公司信息表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<ApiDictionary> GetCollectingcompanyListByUserIdAndContractId(string contractId, bool isOverseasCustomer)
        {
            return DbOpe_crm_collectingcompany.Instance.GetCollectingcompanyListByUserIdAndContractId(contractId, isOverseasCustomer);
        }

        /// <summary>
        /// 根据用户id获取收款公司信息表包括全部合同的乙方公司
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<ApiDictionary> GetAllCollectingCompanyListByUserId(bool isOverseasCustomer)
        {
            return DbOpe_crm_collectingcompany.Instance.GetAllCollectingCompanyListByUserId(isOverseasCustomer);
        }
    }
}
