using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CRM2_API.BLL.Common;
using SqlSugar;
using System.Linq.Expressions;
using CRM2_API.DAL.DbCommon;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.JWT;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.BLLModel.InvoiceSystem;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 匹配功能部分
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        /// <summary>
        /// 手动匹配发票和到账
        /// 追加：验证到账开票权限
        /// </summary>
        /// <param name="request">匹配请求</param>
        /// <returns>匹配ID</returns>
        public string ManualMatching(ManualMatchingRequest request)
        {
            // 参数验证
            if (request == null)
            {
                throw new ApiException("匹配请求不能为空");
            }

            if (string.IsNullOrEmpty(request.InvoiceId))
            {
                throw new ApiException("发票ID不能为空");
            }

            if (string.IsNullOrEmpty(request.ReceiptId))
            {
                throw new ApiException("到账ID不能为空");
            }

            // 获取当前用户ID
            string userId = TokenModel.Instance.id;
            if (string.IsNullOrEmpty(userId))
            {
                throw new ApiException("无法获取当前用户信息");
            }

            // 获取发票信息
            var invoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == request.InvoiceId && i.Deleted != true);
            if (invoice == null)
            {
                throw new ApiException("发票不存在或已被删除");
            }

            // 验证发票状态，只有未匹配的发票才能进行匹配
            if (invoice.MatchingStatus != (int)EnumInvoiceMatchingStatus.NotReceived && 
                invoice.MatchingStatus != (int)EnumInvoiceMatchingStatus.WaitingConfirm && 
                invoice.MatchingStatus != (int)EnumInvoiceMatchingStatus.Rejected)
            {
                throw new ApiException("当前发票状态不允许进行匹配操作");
            }
            
            // 验证发票状态，"匹配成功（作废）"和"未匹配（作废）"状态的发票不能进行匹配
            if (invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccessVoid || 
                invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.NotMatchedVoid)
            {
                throw new ApiException("已退票作废的发票不允许进行匹配操作");
            }
            
            // 验证发票退票状态，不允许退票中的发票进行匹配
            if (invoice.RefundStatus != (int)EnumInvoiceRefundStatus.Normal)
            {
                throw new ApiException("已退票的发票不允许进行匹配操作");
            }
            
            // 验证交易类型，不允许红字发票（退票）进行匹配
            if (invoice.TransactionType == 2)
            {
                throw new ApiException("红字发票（退票）不允许进行匹配操作");
            }

            // 获取到账信息
            var receipt = DbOpe_crm_contract_receiptregister.Instance.GetData(r => r.Id == request.ReceiptId && r.Deleted != true);
            if (receipt == null)
            {
                throw new ApiException("到账记录不存在或已被删除");
            }

            // 验证到账状态，只有已确认的到账才能进行匹配
            if (receipt.AchievementState != (int)EnumAchievementState.Confirmed)
            {
                throw new ApiException("只有已确认的到账记录才能进行匹配");
            }

            // 验证到账开票权限
            var authority = DbOpe_crm_invoice_receipt_authority.Instance.GetByReceiptId(request.ReceiptId);
            if(authority == null)
            {
                throw new ApiException("未找到到账开票权限记录");
            }
            if(authority.CanInvoice == (int)EnumCanInvoice.No)
            {
                throw new ApiException("选定的到账没有开票权限，不允许进行匹配");
            }

            // 验证发票和到账是否属于同一合同
            if (invoice.ContractId != receipt.ContractId)
            {
                throw new ApiException("发票和到账记录必须属于同一合同");
            }
            


            // 使用事务处理
            string matchingId = string.Empty;
            DbOpe_crm_invoice_receipt_matching.Instance.TransDeal(() =>
            {
                try
                {
                    // 检查是否已存在匹配关系
                    var existingMatching = DbOpe_crm_invoice_receipt_matching.Instance.GetDataList(
                        m => (m.InvoiceId == request.InvoiceId || m.ReceiptId == request.ReceiptId)
                        && m.MatchingStatus != (int)EnumInvoiceMatchingStatus.MatchSuccessVoid
                        && m.MatchingStatus != (int)EnumInvoiceMatchingStatus.NotMatchedVoid
                        && m.Deleted != true);
                        
                    if (existingMatching != null && existingMatching.Count > 0)
                    {
                        foreach (var exMatching in existingMatching){
                            if (exMatching.MatchingStatus == (int)EnumInvoiceMatchingStatus.Rejected){
                                CancelRejectedMatching(exMatching.Id);
                            }
                            else{
                                if(exMatching.ReceiptId == request.ReceiptId){
                                    throw new ApiException("当前到账已存在匹配关系");
                                }
                                else{
                                    throw new ApiException("当前发票已存在匹配关系");
                                }
                            }
                        }
                    }
                    // 创建匹配记录
                    matchingId = Guid.NewGuid().ToString();
                    var matching = new Db_crm_invoice_receipt_matching
                    {
                        Id = matchingId,
                        InvoiceId = request.InvoiceId,
                        ReceiptId = request.ReceiptId,
                        InvoiceApplicationId = invoice.InvoiceApplicationId,
                        MatchingStatus = (int)EnumInvoiceMatchingStatus.WaitingAudit, // 匹配待复核状态
                        MatchingTime = DateTime.Now,
                        MatchingUser = userId,
                        Remark = request.MatchingRemark,
                        CreateUser = userId,
                        CreateDate = DateTime.Now,
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now,
                        Deleted = false
                    };

                    // 插入匹配记录
                    DbOpe_crm_invoice_receipt_matching.Instance.Insert(matching);

                    // 更新发票的匹配状态
                    DbOpe_crm_invoice.Instance.UpdateData(i => new Db_crm_invoice
                    {
                        MatchingStatus = (int)EnumInvoiceMatchingStatus.WaitingAudit, // 匹配待复核状态
                        Remark = request.InvoiceBackgroundRemark, // 更新发票备注
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now
                    }, request.InvoiceId);

                    // 记录日志
                    LogUtil.AddErrorLog($"用户[{userId}]手动匹配了发票[{invoice.InvoiceNumber}]和到账记录");
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"手动匹配发票和到账记录失败: {ex.Message}");
                    throw new ApiException("匹配操作失败，请稍后重试");
                }
            });
            
            return matchingId;
        }

        /// <summary>
        /// 查询合同下所有确认的到账及其匹配发票情况(指定到账ID时，只查询指定到账ID的匹配情况)
        /// 追加：开票权限的返回
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="receiptId">到账ID</param>
        /// <returns>合同下所有已确认到账及其匹配的发票信息</returns>
        public ApiTableOut<ContractReceiptMatchingInfo> GetContractReceiptsWithMatchingStatus(string contractId,string receiptId = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(contractId))
                {
                    throw new ApiException("合同ID不能为空");
                }

                // 查询合同是否存在
                var contract = DbOpe_crm_contract.Instance.GetContractById(contractId);
                if (contract == null)
                {
                    throw new ApiException("未找到合同信息");
                }

                // 使用JOIN直接关联所有相关表，一次性获取数据
                var query = DbContext.Crm2Db.Queryable<Db_crm_contract_receiptregister>()
                    // 关联到账明细和收款信息
                    .LeftJoin<Db_crm_contract_receipt_details>((r, d) => r.Id == d.ContractReceiptRegisterId && d.Deleted != true)
                    .LeftJoin<Db_crm_collectioninfo>((r, d, c) => d.CollectionInfoId == c.Id && c.Deleted != true)
                    // 关联匹配记录
                    .LeftJoin<Db_crm_invoice_receipt_matching>((r, d, c, m) => r.Id == m.ReceiptId && m.Deleted != true)
                    // 关联发票申请表 - 直接关联申请ID
                    .LeftJoin<Db_crm_invoice_application>((r, d, c, m, a) => m.InvoiceApplicationId == a.Id && a.Deleted != true)
                    // 关联发票信息表 - 通过申请ID关联
                    .LeftJoin<Db_crm_invoice>((r, d, c, m, a, i) =>
                        (m.InvoiceApplicationId == i.InvoiceApplicationId || m.InvoiceId == i.Id) && i.Deleted != true)
                    // 关联用户信息
                    .LeftJoin<Db_sys_user>((r, d, c, m, a, i, u) => m.MatchingUser == u.Id)
                    // 关联到账开票权限
                    .LeftJoin<Db_crm_invoice_receipt_authority>((r, d, c, m, a, i, u, au) => r.Id == au.ReceiptId && au.Deleted != true)
                    .Where((r, d, c, m, a, i, u, au) =>
                        r.ContractId == contractId &&
                        r.AchievementState == (int)EnumAchievementState.Confirmed &&
                        r.Deleted != true)
                    .WhereIF(!string.IsNullOrEmpty(receiptId), (r, d, c, m, a, i, u, au) => r.Id == receiptId)
                    .Where((r, d, c, m, a, i, u, au) => au.CanInvoice == (int)EnumCanInvoice.Yes)
                    // 只查询允许开票的到账记录
                    .Where((r, d, c, m, a, i, u, au) => r.IsAllowInvoicing == 1);

                // 查询获取所有相关数据
                var receiptsInfo = query.Select((r, d, c, m, a, i, u, au) => new
                {
                    // 到账信息
                    ReceiptId = r.Id,
                    ContractId = r.ContractId,
                    ReceiptRemark = r.Remark,
                    ReceiptCreateDate = r.CreateDate,
                    ReceiptStatus = r.AchievementState,
                    
                    // 收付款信息
                    PaymentCompany = c.PaymentCompany,
                    PaymentCompanyName = c.PaymentCompanyName,
                    CollectingCompany = c.CollectingCompany,
                    CollectingCompanyName = c.CollectingCompanyName,
                    ArrivalAmount = c.ArrivalAmount,
                    ArrivalDate = c.ArrivalDate,
                    
                    // 匹配信息
                    MatchingId = m.Id,
                    MatchingStatus = m.MatchingStatus,
                    MatchingTime = m.MatchingTime,
                    MatchingUser = m.MatchingUser,
                    MatchingRemark = m.Remark,
                    InvoiceApplicationId = m.InvoiceApplicationId,
                    
                    // 发票申请信息
                    ApplicationId = a.Id,
                    AppliedAmount = a.AppliedAmount,
                    BillingCompanyApp = a.BillingCompany,
                    BillingHeaderApp = a.BillingHeader,
                    ApplicationCreateDate = a.CreateDate,
                    
                    // 发票信息
                    InvoiceId = i.Id,
                    InvoiceNumber = i.InvoiceNumber,
                    InvoiceCode = i.InvoiceCode,
                    InvoiceAmount = i.InvoicedAmount,
                    InvoiceDate = i.InvoicingDate,
                    BillingCompany = i.BillingCompany,
                    BillingHeader = i.BillingHeader,
                    
                    // 用户信息
                    UserName = u.Name,

                    // 到账开票权限
                    CanInvoice = au.CanInvoice
                })
                .MergeTable()
                .OrderByDescending(it => it.ArrivalDate)
                .ToList();

                var result = new List<ContractReceiptMatchingInfo>();

                foreach (var receipt in receiptsInfo)
                {
                    // 准备匹配发票信息
                    var matchedInvoices = new List<MatchedInvoiceInfo>();
                    EnumInvoiceMatchingStatus matchingStatus = EnumInvoiceMatchingStatus.NotInvoiced;
                    string matchingUser = null;
                    string matchingUserName = null;
                    
                    // 如果存在匹配记录
                    if (receipt.MatchingId != null)
                    {
                        // 设置匹配状态和用户信息
                        matchingStatus = receipt.MatchingStatus != null 
                            ? (EnumInvoiceMatchingStatus)(int)receipt.MatchingStatus 
                            : EnumInvoiceMatchingStatus.NotInvoiced;
                            
                        matchingUser = receipt.MatchingUser;
                        matchingUserName = !string.IsNullOrEmpty(receipt.UserName) 
                            ? receipt.UserName 
                            : receipt.MatchingUser;
                        
                        // 情况1：有正式发票
                        if (receipt.InvoiceId != null)
                        {
                            decimal invoiceAmount = receipt.InvoiceAmount != null ? (decimal)receipt.InvoiceAmount : 0;
                            
                            matchedInvoices.Add(new MatchedInvoiceInfo
                            {
                                InvoiceId = receipt.InvoiceId,
                                MatchingId = receipt.MatchingId,
                                InvoiceNumber = receipt.InvoiceNumber,
                                InvoiceCode = receipt.InvoiceCode,
                                InvoiceCompany = receipt.BillingCompany,
                                ReceivingCompany = receipt.BillingHeader,
                                InvoiceAmount = invoiceAmount,
                                InvoiceDate = receipt.InvoiceDate,
                                MatchingStatus = matchingStatus,
                                MatchingTime = receipt.MatchingTime,
                                MatchingUser = matchingUser,
                                MatchingUserName = matchingUserName
                            });
                        }
                        // 情况2：只有发票申请
                        else if (receipt.ApplicationId != null)
                        {
                            decimal appliedAmount = receipt.AppliedAmount != null ? (decimal)receipt.AppliedAmount : 0;
                            
                            matchedInvoices.Add(new MatchedInvoiceInfo
                            {
                                InvoiceId = "",
                                MatchingId = receipt.MatchingId,
                                InvoiceNumber = "申请中",
                                InvoiceCode = "申请中",
                                InvoiceCompany = receipt.BillingCompanyApp,
                                ReceivingCompany = receipt.BillingHeaderApp,
                                InvoiceAmount = appliedAmount,
                                InvoiceDate = receipt.ApplicationCreateDate,
                                MatchingStatus = matchingStatus,
                                MatchingTime = receipt.MatchingTime,
                                MatchingUser = matchingUser,
                                MatchingUserName = matchingUserName
                            });
                        }
                    }
                    
                    // 获取到账状态名称
                    string receiptStatusName = "未知状态";
                    if (receipt.ReceiptStatus != null)
                    {
                        receiptStatusName = GetEnumDescription((EnumAchievementState)(int)receipt.ReceiptStatus);
                    }
                    
                    // 默认币种
                    int currency = (int)EnumCurrency.CNY; // 默认为人民币
                    string currencyName = GetEnumDescription<EnumCurrency>(EnumCurrency.CNY);
                    
                    // 处理到账金额
                    decimal receiptAmount = receipt.ArrivalAmount != null ? (decimal)receipt.ArrivalAmount : 0;
                    
                    // 处理到账状态
                    int receiptStatus = receipt.ReceiptStatus != null ? (int)receipt.ReceiptStatus : 0;
                    
                    // 添加到结果集
                    result.Add(new ContractReceiptMatchingInfo
                    {
                        ReceiptId = receipt.ReceiptId,
                        ContractId = receipt.ContractId,
                        ContractNumber = contract.ContractNo,
                        ContractName = contract.ContractName,
                        FirstPartyName = GetFirstPartyName(contract.FirstParty),
                        PaymentCompany = receipt.PaymentCompanyName ?? "",
                        ReceiveCompany = receipt.CollectingCompanyName ?? "收款公司",
                        ReceiveCompanyId = receipt.CollectingCompany,
                        ReceiptAmount = receiptAmount,
                        Currency = currency,
                        CurrencyName = currencyName,
                        ReceiptDate = receipt.ArrivalDate,
                        ReceiptStatus = receiptStatus,
                        ReceiptStatusName = receiptStatusName,
                        Remark = receipt.ReceiptRemark,
                        MatchedInvoices = matchedInvoices,
                        MatchingUser = matchingUser,
                        MatchingUserName = matchingUserName,
                        MatchingStatus = matchingStatus,
                        CanInvoice = (EnumCanInvoice)receipt.CanInvoice
                    });
                }
                
                return new ApiTableOut<ContractReceiptMatchingInfo> { Data = result, Total = result.Count };
            }
            catch (ApiException)
            {
                throw; // 业务异常直接抛出
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取合同到账匹配状态异常: {ex.Message}");
                throw new ApiException("获取合同到账匹配状态失败，请联系系统管理员");
            }
        }
        
        /// <summary>
        /// 获取甲方公司名称
        /// </summary>
        private string GetFirstPartyName(string firstPartyId)
        {
            if (string.IsNullOrEmpty(firstPartyId))
            {
                return "";
            }
            
            var company = DbOpe_crm_customer_subcompany.Instance.GetData(c => c.Id == firstPartyId);
            return company?.CompanyName ?? "";
        }
        
        /// <summary>
        /// 获取枚举的描述文字
        /// </summary>
        private static string GetEnumDescription<T>(T value) where T : Enum
        {
            var fieldInfo = value.GetType().GetField(value.ToString());
            var attributes = (DescriptionAttribute[])fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false);
            return attributes.Length > 0 ? attributes[0].Description : value.ToString();
        }

        /// <summary>
        /// 自动判断并生成开票权限，自动匹配发票和到账
        /// </summary>
        /// <param name="receiptId">到账记录ID，指定要匹配的单笔到账</param>
        /// <returns>是否匹配成功</returns>
        public bool AutoSetAuthorityAndMatching(string receiptId)
        {
            // 获取当前用户ID
            string userId = TokenModel.Instance.id;
            if (string.IsNullOrEmpty(userId))
            {
                throw new ApiException("无法获取当前用户信息");
            }

            try
            {
                // 获取指定的到账记录
                var receipt = DbOpe_crm_contract_receiptregister.Instance.GetData(r => 
                    r.Id == receiptId && 
                    r.AchievementState == (int)EnumAchievementState.Confirmed && 
                    r.Deleted != true);
                    
                if (receipt == null)
                {
                    throw new ApiException("到账记录不存在或未确认");
                }
                if(receipt.IsAllowInvoicing != 1)
                {
                    // 到账记录不允许开票，则不进行自动匹配
                    LogUtil.AddDebugLog(receiptId +"到账记录不允许开票，则不进行自动匹配");
                    return false;
                }
                // 获取完整的到账信息（包括支付公司和收款公司等）
                var receiptWithDetailsQuery = DbContext.Crm2Db
                    .Queryable<Db_crm_contract_receiptregister, Db_crm_contract_receipt_details, Db_crm_collectioninfo>(
                        (r, d, c) => new JoinQueryInfos(
                            JoinType.Left, r.Id == d.ContractReceiptRegisterId,
                            JoinType.Left, d.CollectionInfoId == c.Id
                        ))
                    .Where((r, d, c) => r.Id == receiptId && r.Deleted != true)
                    .Select((r, d, c) => new {
                        CollectingCompany = c.CollectingCompany,
                        CollectingCompanyName = c.CollectingCompanyName,
                        PaymentCompany = c.PaymentCompany,
                        PaymentCompanyName = c.PaymentCompanyName,
                        ArrivalAmount = c.ArrivalAmount
                    })
                    .ToList();
                
                var receiptWithDetails = receiptWithDetailsQuery.FirstOrDefault();
                
                if (receiptWithDetails == null)
                {
                    throw new ApiException("无法获取到账详细信息");
                }
                
                // 检查是否已匹配
                bool isMatched = DbContext.Crm2Db.Queryable<Db_crm_invoice_receipt_matching>()
                    .Any(m => m.ReceiptId == receiptId && m.Deleted != true &&
                        m.MatchingStatus != (int)EnumInvoiceMatchingStatus.MatchSuccessVoid &&
                        m.MatchingStatus != (int)EnumInvoiceMatchingStatus.NotMatchedVoid);
                    
                if (isMatched)
                {
                    //到账记录已存在发票匹配,则不进行再次自动匹配
                    LogUtil.AddDebugLog(receiptId + "到账记录已存在发票匹配,则不进行再次自动匹配");
                    return false;
                }

                // 自动判断并生成开票权限
                var canInvoice = AutoJudgeAuthority(receiptId);

                if(!canInvoice)
                {
                    // 没有开票权限，则不进行匹配
                    LogUtil.AddDebugLog(receiptId + "没有开票权限，则不进行匹配");
                    return false;
                }

                // 获取合同下的所有未匹配发票
                var unMatchedInvoices = DbOpe_crm_invoice.Instance.GetDataList(i => 
                    i.ContractId == receipt.ContractId && 
                    (i.MatchingStatus == (int)EnumInvoiceMatchingStatus.NotReceived || 
                     i.MatchingStatus == (int)EnumInvoiceMatchingStatus.WaitingConfirm) && 
                    i.RefundStatus == (int)EnumInvoiceRefundStatus.Normal && // 只包含未退票状态
                    i.MatchingStatus != (int)EnumInvoiceMatchingStatus.MatchSuccessVoid &&
                    i.MatchingStatus != (int)EnumInvoiceMatchingStatus.NotMatchedVoid &&
                    i.BillingType != (int)EnumBillingType.SupplementInvoice&& //补充发票不进行自动匹配
                    i.TransactionType != 2 && // 排除红字发票（退票）
                    i.Deleted != true)
                    .ToList();

                if (unMatchedInvoices == null || unMatchedInvoices.Count == 0)
                {
                    LogUtil.AddDebugLog(receiptId + "没有找到可匹配的发票");
                    return false;
                    //throw new ApiException("没有找到可匹配的发票");
                }

                // 找到金额完全匹配的发票
                var exactMatchInvoice = unMatchedInvoices.FirstOrDefault(i => 
                    // 金额匹配（保留两位小数）
                    Math.Round(i.InvoicedAmount, 2) == Math.Round(receiptWithDetails.ArrivalAmount.GetValueOrDefault(), 2) &&
                    // 开票公司对应收款公司
                    i.BillingCompany == receiptWithDetails.CollectingCompany &&
                    // 收票公司/抬头对应付款公司
                    i.BillingHeader == receiptWithDetails.PaymentCompanyName
                );

                if (exactMatchInvoice != null)
                {
                    // 找到完全匹配，创建匹配记录
                    CreateMatchingRecord(exactMatchInvoice, receipt, userId, true);
                    return true;
                }
                else
                {
                    // 未找到完全匹配的发票，将所有未匹配的发票状态设为匹配待确认
                    foreach (var invoice in unMatchedInvoices)
                    {
                        // 更新发票为匹配待确认状态
                        DbOpe_crm_invoice.Instance.UpdateData(i => new Db_crm_invoice
                        {
                            MatchingStatus = (int)EnumInvoiceMatchingStatus.WaitingConfirm, // 匹配待确认状态
                            UpdateUser = userId,
                            UpdateDate = DateTime.Now
                        }, invoice.Id);
                        
                        // 记录日志
                        LogUtil.AddErrorLog($"发票[{invoice.InvoiceNumber}]自动匹配失败，设置为匹配待确认状态");
                    }
                    
                    return false;
                }
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"单笔到账自动匹配发票失败: {ex.Message}");
                throw new ApiException("自动匹配发票失败，请稍后重试");
            }
        }
        
        /// <summary>
        /// 创建匹配记录
        /// </summary>
        /// <param name="invoice">发票</param>
        /// <param name="receipt">到账记录</param>
        /// <param name="userId">用户ID</param>
        /// <param name="isExactMatch">是否完全匹配</param>
        /// <returns>匹配ID</returns>
        private string CreateMatchingRecord(Db_crm_invoice invoice, Db_crm_contract_receiptregister receipt, string userId, bool isExactMatch)
        {
            // 匹配状态，完全匹配则直接成功，否则设为待复核
            var matchingStatus = isExactMatch ? 
                (int)EnumInvoiceMatchingStatus.MatchSuccess : 
                (int)EnumInvoiceMatchingStatus.WaitingAudit;
            
            string matchingId = Guid.NewGuid().ToString();
            
            DbOpe_crm_invoice_receipt_matching.Instance.TransDeal(() =>
            {
                // 创建匹配记录
                var matching = new Db_crm_invoice_receipt_matching
                {
                    Id = matchingId,
                    InvoiceId = invoice.Id,
                    ReceiptId = receipt.Id,
                    InvoiceApplicationId = invoice.InvoiceApplicationId,
                    MatchingStatus = matchingStatus,
                    MatchingTime = DateTime.Now,
                    MatchingUser = userId,
                    Remark = isExactMatch ? "系统自动匹配，金额、公司名称完全一致" : "系统自动匹配候选，需人工确认",
                    CreateUser = userId,
                    CreateDate = DateTime.Now,
                    UpdateUser = userId,
                    UpdateDate = DateTime.Now,
                    Deleted = false
                };

                // 插入匹配记录
                DbOpe_crm_invoice_receipt_matching.Instance.Insert(matching);

                // 更新发票的匹配状态
                DbOpe_crm_invoice.Instance.UpdateData(i => new Db_crm_invoice
                {
                    MatchingStatus = matchingStatus,
                    UpdateUser = userId,
                    UpdateDate = DateTime.Now
                }, invoice.Id);
                
                // 记录日志
                string actionDesc = isExactMatch ? "自动匹配成功" : "创建自动匹配候选记录";
                LogUtil.AddErrorLog($"系统为发票[{invoice.InvoiceNumber}]和到账记录{actionDesc}");
            });
            
            return matchingId;
        }

        /// <summary>
        /// 审核匹配申请
        /// </summary>
        /// <param name="request">审核请求</param>
        /// <returns>审核结果</returns>
        public bool ReviewMatching(ReviewMatchingRequest request)
        {
            // 参数验证
            if (request == null)
            {
                throw new ApiException("审核请求不能为空");
            }

            if (string.IsNullOrEmpty(request.MatchingId))
            {
                throw new ApiException("匹配ID不能为空");
            }

            // 获取当前用户ID
            string userId = TokenModel.Instance.id;
            if (string.IsNullOrEmpty(userId))
            {
                throw new ApiException("无法获取当前用户信息");
            }

            try
            {
                // 获取匹配记录
                var matching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => 
                    m.Id == request.MatchingId && 
                    m.MatchingStatus == (int)EnumInvoiceMatchingStatus.WaitingAudit && 
                    m.Deleted != true);
                
                if (matching == null)
                {
                    throw new ApiException("匹配记录不存在或状态不正确");
                }

                // 获取发票信息
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                    i.Id == matching.InvoiceId && 
                    i.Deleted != true);
                
                if (invoice == null)
                {
                    throw new ApiException("发票信息不存在或已被删除");
                }

                // 获取到账信息
                var receipt = DbOpe_crm_contract_receiptregister.Instance.GetData(r => 
                    r.Id == matching.ReceiptId && 
                    r.Deleted != true);
                
                if (receipt == null)
                {
                    throw new ApiException("到账记录不存在或已被删除");
                }

                // 根据审核结果更新匹配状态
                int newMatchingStatus;
                string reviewRemark;
                
                if (request.ReviewResult == 1) // 通过
                {
                    newMatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccess;
                    reviewRemark = string.IsNullOrEmpty(request.ReviewComments) ? 
                        "审核通过" : request.ReviewComments;
                }
                else // 拒绝
                {
                    newMatchingStatus = (int)EnumInvoiceMatchingStatus.Rejected;
                    reviewRemark = string.IsNullOrEmpty(request.ReviewComments) ? 
                        "审核拒绝" : request.ReviewComments;
                }

                // 使用事务处理
                DbOpe_crm_invoice_receipt_matching.Instance.TransDeal(() =>
                {
                    // 更新匹配记录状态
                    DbOpe_crm_invoice_receipt_matching.Instance.UpdateData(m => new Db_crm_invoice_receipt_matching
                    {
                        MatchingStatus = newMatchingStatus,
                        Remark = reviewRemark,
                        // 发票匹配表可能没有审核相关字段，使用通用字段
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now
                    }, request.MatchingId);

                    // 更新发票匹配状态
                    DbOpe_crm_invoice.Instance.UpdateData(i => new Db_crm_invoice
                    {
                        MatchingStatus = newMatchingStatus,
                        Remark = request.InvoiceBackgroundRemark,
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now
                    }, matching.InvoiceId);

                    // 记录日志
                    string actionDesc = request.ReviewResult == 1 ? "审核通过" : "审核拒绝";
                    LogUtil.AddErrorLog($"用户[{userId}]对发票[{invoice.InvoiceNumber}]的匹配记录进行了{actionDesc}操作");
                });

                return true;
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"审核匹配申请失败: {ex.Message}");
                throw new ApiException("审核匹配申请失败，请稍后重试");
            }
        }

        /// <summary>
        /// 到账撤销后需要自动对相关的发票进行处理
        /// 1. 如果匹配的是发票申请记录，且尚未开具正式发票，则作废发票申请
        /// 2. 如果匹配的是正式发票，则需要解绑，刷新发票到账绑定状态
        /// </summary>
        /// <param name="receiptId">到账登记ID</param>
        /// <param name="unbindRemark">解绑原因备注</param>
        /// <param name="isClearAuthority">是否清除到账权限</param>
        /// <returns>是否解绑成功</returns>
        public bool UnbindInvoiceReceipt(string receiptId, string unbindRemark = null,bool isClearAuthority = true)
        {
            // 参数验证
            if (string.IsNullOrEmpty(receiptId))
            {
                throw new ApiException("到账登记ID不能为空");
            }

            // 获取当前用户ID
            string userId = TokenModel.Instance.id;
            if (string.IsNullOrEmpty(userId))
            {
                throw new ApiException("无法获取当前用户信息");
            }

            try
            {



                // 查找与该到账关联的匹配记录
                var matching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => 
                    m.ReceiptId == receiptId && 
                    m.Deleted != true);
                
                if (matching == null)
                {
                    return false;
                    //throw new ApiException("未找到与该到账关联的匹配记录");
                }

                // 检查是否存在发票申请记录
                bool hasInvoiceApplication = false;
                Db_crm_invoice_application invoiceApplication = null;
                if (!string.IsNullOrEmpty(matching.InvoiceApplicationId))
                {
                    invoiceApplication = DbOpe_crm_invoice_application.Instance.GetData(a => 
                        a.Id == matching.InvoiceApplicationId && 
                        a.Deleted != true);
                    
                    hasInvoiceApplication = (invoiceApplication != null);
                }

                // 获取发票信息
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                    i.Id == matching.InvoiceId && 
                    i.Deleted != true);
                
                // 使用事务处理
                DbOpe_crm_invoice_receipt_matching.Instance.TransDeal(() =>
                {
                    try
                    {
                        matching.Deleted = true;
                        matching.UpdateUser = userId;
                        matching.UpdateDate = DateTime.Now;
                        matching.Remark = string.IsNullOrEmpty(unbindRemark) ? 
                            "自动解除绑定" : unbindRemark;
                        DbOpe_crm_invoice_receipt_matching.Instance.Update(matching);
                        // 情况1：如果匹配的是发票申请记录，且尚未开具正式发票
                        if (hasInvoiceApplication && invoice == null)
                        {
                            if (invoiceApplication != null)
                            {
                                //作废发票申请
                               InvalidateInvoiceApplication(invoiceApplication.Id,true);
                            }
                        }
                        // 情况2：对于已开具的正式发票，需要确定解绑后的状态
                        else if (invoice != null)
                        {
                            // 判断合同中是否还有其他未匹配的到账
                            bool hasUnmatchedReceipts = false;
                            
                            if (!string.IsNullOrEmpty(invoice.ContractId))
                            {
                                // 查询合同下其他已确认但未匹配的到账记录 2025-05-12 增加开票权限限制
                                var unmatchedReceipts = DbContext.Crm2Db.Queryable<Db_crm_contract_receiptregister>()
                                    .LeftJoin<Db_crm_invoice_receipt_matching>((r, m) => r.Id == m.ReceiptId && m.Deleted != true)
                                    .LeftJoin<Db_crm_invoice_receipt_authority>((r, m, a) => r.Id == a.ReceiptId && a.Deleted != true)
                                    .Where((r, m, a) => r.ContractId == invoice.ContractId && 
                                           r.AchievementState == (int)EnumAchievementState.Confirmed && 
                                           r.Deleted != true &&
                                           r.Id != receiptId
                                           &&  m.Id == null &&
                                           a.CanInvoice == (int)EnumCanInvoice.Yes)
                                    .Select((r, m, a)  => new { r.Id })
                                    .ToList();
                                
                                if (unmatchedReceipts != null && unmatchedReceipts.Count > 0)
                                {
                                    hasUnmatchedReceipts = true;
                                }
                            }
                            
                            // 确定发票的新状态
                            int newInvoiceStatus;
                            if (hasUnmatchedReceipts)
                            {
                                // 合同中还有未匹配到账，则设置为"匹配待确认"状态
                                newInvoiceStatus = (int)EnumInvoiceMatchingStatus.WaitingConfirm;
                            }
                            else
                            {
                                // 没有未匹配到账，则设置为"未到账"状态
                                newInvoiceStatus = (int)EnumInvoiceMatchingStatus.NotReceived;
                            }
                            
                            // 更新发票的匹配状态
                            DbOpe_crm_invoice.Instance.UpdateData(i => new Db_crm_invoice
                            {
                                MatchingStatus = newInvoiceStatus,
                                UpdateUser = userId,
                                UpdateDate = DateTime.Now
                            }, invoice.Id);
                            
                            // 记录日志，包含状态变更信息
                            string statusDesc = newInvoiceStatus == (int)EnumInvoiceMatchingStatus.WaitingConfirm ? 
                                "匹配待确认" : "未到账";
                            LogUtil.AddErrorLog($"用户[{userId}]解除了发票[{invoice.InvoiceNumber}]与到账记录[{receiptId}]的绑定关系，发票状态变更为：{statusDesc}");
                        }
                        // 清除到账权限
                        if(isClearAuthority)
                        {
                            ClearReceiptAuthority(receiptId);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddErrorLog($"解除发票和到账记录绑定关系失败: {ex.Message}");
                        throw new ApiException("解除绑定操作失败，请稍后重试");
                    }
                });
                
                return true;
            }
            catch (ApiException)
            {
                throw; // 业务异常直接抛出
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"解除发票和到账绑定异常: {ex.Message}");
                throw new ApiException("解除发票和到账绑定失败，请联系系统管理员");
            }
        }


        /// <summary>
        /// 撤销拒绝状态的匹配记录（需要更改匹配状态）
        /// </summary>
        /// <param name="matchingId">匹配ID</param>
        /// <returns></returns>
        public bool CancelRejectedMatching(string matchingId){
            // 参数验证
            if (string.IsNullOrEmpty(matchingId))
            {
                throw new ApiException("发票申请ID不能为空");
            }
            
            try
            {
                // 获取当前用户ID
                string userId = TokenModel.Instance.id;
                if (string.IsNullOrEmpty(userId))
                {
                    throw new ApiException("无法获取当前用户信息");
                }
                
                // 查找与发票申请相关的匹配记录
                var matchingRecord = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => 
                    m.Id == matchingId && 
                    m.Deleted != true);
                    
                if (matchingRecord == null)
                {
                    // 没有找到匹配记录
                    throw new ApiException("未找到相关的匹配记录");
                }
                
                // 检查匹配状态，只有特定状态下才能撤销
                if (matchingRecord.MatchingStatus != (int)EnumInvoiceMatchingStatus.Rejected)
                {
                    throw new ApiException("当前匹配状态不允许撤销，只有已驳回的匹配可以撤销");
                }
                
                // 使用事务处理
                DbOpe_crm_invoice_receipt_matching.Instance.TransDeal(() =>
                {
                    // 标记为删除
                    matchingRecord.Deleted = true;
                    matchingRecord.UpdateUser = userId;
                    matchingRecord.UpdateDate = DateTime.Now;
                    
                    // 更新匹配记录
                    DbOpe_crm_invoice_receipt_matching.Instance.Update(matchingRecord);
                    
                    // 更新发票匹配状态为未到账
                    if (!string.IsNullOrEmpty(matchingRecord.InvoiceId))
                    {
                        // 如果有关联发票，更新发票的匹配状态
                        DbOpe_crm_invoice.Instance.UpdateData(invoice => new Db_crm_invoice
                        {
                            MatchingStatus = (int)EnumInvoiceMatchingStatus.NotReceived,
                            UpdateUser = userId,
                            UpdateDate = DateTime.Now
                        }, matchingRecord.InvoiceId);
                    }
                    
                    // 记录操作日志
                    LogUtil.AddErrorLog($"用户[{userId}]撤销了发票申请[{matchingRecord.InvoiceApplicationId}]的匹配");
                });
                
                return true;
            }
            catch (ApiException)
            {
                throw; // 抛出业务异常
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"撤销匹配异常: {ex.Message}");
                throw new ApiException("撤销匹配失败，请联系系统管理员");
            }
        }
        
        /// <summary>
        /// 刷新合同下其他匹配待确认/未到账的发票的匹配状态
        /// </summary>
        /// <param name="receiptId">到账登记ID</param>
        public void RefreshMatchingStatus(string receiptId)
        {
            // 获取当前用户ID
            string userId = TokenModel.Instance.id;
            if (string.IsNullOrEmpty(userId))
            {
                throw new ApiException("无法获取当前用户信息");
            }
            //获取到账信息
            var receipt = DbOpe_crm_contract_receiptregister.Instance.GetData(r => 
                r.Id == receiptId && 
                r.Deleted != true);
            if (receipt == null)
            {
                throw new ApiException("到账记录不存在或已被删除");
            }
            //获取合同信息
            var contract = DbOpe_crm_contract.Instance.GetData(c => 
                c.Id == receipt.ContractId && 
                c.Deleted != true);
            if (contract == null)
            {
                throw new ApiException("合同不存在或已被删除");
            }   
            //获取合同下所有匹配待确认/未到账的发票Id
            // 获取合同下的所有未匹配发票
            var unMatchedInvoiceIds = DbOpe_crm_invoice.Instance.GetDataList(i => 
                    i.ContractId == receipt.ContractId && 
                    (i.MatchingStatus == (int)EnumInvoiceMatchingStatus.NotReceived || 
                     i.MatchingStatus == (int)EnumInvoiceMatchingStatus.WaitingConfirm) && 
                    i.RefundStatus == (int)EnumInvoiceRefundStatus.Normal && // 只包含未退票状态
                    i.MatchingStatus != (int)EnumInvoiceMatchingStatus.MatchSuccessVoid &&
                    i.MatchingStatus != (int)EnumInvoiceMatchingStatus.NotMatchedVoid &&
                    i.TransactionType != 2 && // 排除红字发票（退票）
                    i.Deleted != true)
                    .Select(i =>i.Id)
                .ToList();
            if (unMatchedInvoiceIds == null)
            {
                return;
            }
            else{
                //获取合同下所有可以开票但还没匹配的到账
                var hasCanInvoicedReceipts = DbContext.Crm2Db.Queryable<Db_crm_contract_receiptregister>()
                    .LeftJoin<Db_crm_invoice_receipt_authority>((r, a) => r.Id == a.ReceiptId && a.Deleted != true)
                    .LeftJoin<Db_crm_invoice_receipt_matching>((r, a, m) => r.Id == m.ReceiptId && m.Deleted != true)
                    .Where((r, a, m) => r.ContractId == contract.Id && 
                    r.AchievementState == (int)EnumAchievementState.Confirmed && 
                    r.Deleted != true &&
                    a.CanInvoice == (int)EnumCanInvoice.Yes &&
                    // 未匹配
                    (m.Id == null))
                    .Any();
                
                //更新发票的匹配状态
                foreach (var invoice in unMatchedInvoiceIds)
                {
                    if (hasCanInvoicedReceipts)
                    {
                        DbOpe_crm_invoice.Instance.UpdateData(i => new Db_crm_invoice
                        {
                            MatchingStatus = (int)EnumInvoiceMatchingStatus.WaitingConfirm,
                            UpdateUser = userId,
                            UpdateDate = DateTime.Now
                        }, invoice);
                    }
                    else
                    {
                        DbOpe_crm_invoice.Instance.UpdateData(i => new Db_crm_invoice
                        {
                            MatchingStatus = (int)EnumInvoiceMatchingStatus.NotReceived,
                            UpdateUser = userId,
                            UpdateDate = DateTime.Now
                        }, invoice);
                    }
                }

            }
           
            
        }
    }
} 