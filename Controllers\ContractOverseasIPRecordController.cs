﻿using System.ComponentModel;
using CRM2_API.BLL;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.ContractOverseasIPRecord;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 合同管理 - 海外IP备案控制器
    /// </summary>
    [Description("合同管理 - 海外IP备案控制器")]
    public class ContractOverseasIPRecordController : MyControllerBase
    {
        public ContractOverseasIPRecordController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 根据查询条件获取海外IP备案列表。
        /// </summary>
        /// <param name="searchIpKeepRecordListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchIPKeepRecordListNew_Out> SearchIPKeepRecordList(SearchIPKeepRecordList_In searchIpKeepRecordListIn)
        {
            int total = 0;
            var list = DbOpe_crm_ipkeeprecord.Instance.SearchIPKeepRecordListNew(searchIpKeepRecordListIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据查询条件获取海外IP备案审核列表。
        /// </summary>
        /// <param name="searchIpKeepRecordListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchIPKeepRecordListNew_Out> SearchIPKeepRecordApplyList(SearchIPKeepRecordList_In searchIpKeepRecordListIn)
        {
            searchIpKeepRecordListIn.TableLabel = EnumRecordTableLabel.Review;
            int total = 0;
            var list = DbOpe_crm_ipkeeprecord.Instance.SearchIPKeepRecordListNew(searchIpKeepRecordListIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据IP备案Id获取IP备案信息。
        /// </summary>
        /// <param name="getIpKeepRecordByIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public GetIPKeepRecordByIdNew_Out GetIPKeepRecordById(GetIPKeepRecordById_In getIpKeepRecordByIdIn)
        {
            if (getIpKeepRecordByIdIn == null || string.IsNullOrEmpty(getIpKeepRecordByIdIn.Id))
            {
                throw new ApiException("IP备案表主键不可为空!");
            }
            return DbOpe_crm_ipkeeprecord.Instance.GetIPKeepRecordByIdNew(getIpKeepRecordByIdIn.Id);
        }
        
        /// <summary>
        /// 添加IP备案申请信息。
        /// </summary>
        /// <param name="addIpKeepRecordApplyIn"></param>
        /// <returns></returns>
        [HttpPost]
        public AddIPKeepRecordApply_Out AddIPKeepRecordApply([FromForm] AddIPKeepRecordApply_In addIpKeepRecordApplyIn)
        {
            return BLL_ContractOverseasIPRecord.Instance.AddIPKeepRecordApply(addIpKeepRecordApplyIn);
        }
        
        /// <summary>
        /// 修改IP备案申请信息。
        /// </summary>
        /// <param name="updateIpKeepRecordApplyIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdateIPKeepRecordApply_Out UpdateIPKeepRecordApply([FromForm] UpdateIPKeepRecordApply_In updateIpKeepRecordApplyIn)
        {
            if (updateIpKeepRecordApplyIn == null || string.IsNullOrEmpty(updateIpKeepRecordApplyIn.Id))
            {
                throw new ApiException("IP备案表主键不可为空!");
            }
            return BLL_ContractOverseasIPRecord.Instance.UpdateIPKeepRecordApply(updateIpKeepRecordApplyIn);
        }

        /// <summary>
        /// 删除IP备案信息，当备案状态为通过、拒绝、作废时，可操作删除。验证数据权限。
        /// </summary>
        /// <param name="deleteIpKeepRecordIn"></param>
        /// <returns></returns>
        [HttpPost]
        public DeleteIPKeepRecord_Out DeleteIPKeepRecord(DeleteIPKeepRecord_In deleteIpKeepRecordIn)
        {
            if (deleteIpKeepRecordIn == null || string.IsNullOrEmpty(deleteIpKeepRecordIn.Id))
            {
                throw new ApiException("IP备案表主键不可为空!");
            }
            return BLL_ContractOverseasIPRecord.Instance.DeleteIPKeepRecord(deleteIpKeepRecordIn);
        }
        
        /// <summary>
        /// 撤销IP备案申请信息，当备案状态为通过、拒绝、作废时，可操作返回到上一个状态。验证数据权限
        /// </summary>
        /// <param name="recordApplyIn"></param>
        /// <returns></returns>
        [HttpPost]
        public RevokeIPKeepRecordApply_Out RevokeIPKeepRecordApply(RevokeIPKeepRecordApply_In recordApplyIn)
        {
            if (recordApplyIn == null || string.IsNullOrEmpty(recordApplyIn.Id))
            {
                throw new ApiException("IP备案表主键不可为空!");
            }
            return BLL_ContractOverseasIPRecord.Instance.RevokeIPKeepRecordApply(recordApplyIn);
        }

        /// <summary>
        /// 作废IP备案信息，当备案状态为通过、待审核时，可对申请备案信息进行作废处理
        /// </summary>
        /// <param name="voidIpKeepRecordApplyIn"></param>
        /// <returns></returns>
        [HttpPost]
        public VoidIPKeepRecordApply_Out VoidIPKeepRecordApply(VoidIPKeepRecordApply_In voidIpKeepRecordApplyIn)
        {
            if (voidIpKeepRecordApplyIn == null || string.IsNullOrEmpty(voidIpKeepRecordApplyIn.Id))
            {
                throw new ApiException("IP备案表主键不可为空!");
            }
            return BLL_ContractOverseasIPRecord.Instance.VoidIPKeepRecordApply(voidIpKeepRecordApplyIn);
        }
        
        /// <summary>
        /// 审核IP备案申请信息。
        /// </summary>
        /// <param name="auditIpKeepRecordApplyIn"></param>
        /// <returns></returns>
        [HttpPost]
        public AuditIPKeepRecordApply_Out AuditIPKeepRecordApply(AuditIPKeepRecordApply_In auditIpKeepRecordApplyIn)
        {
            if (auditIpKeepRecordApplyIn == null || string.IsNullOrEmpty(auditIpKeepRecordApplyIn.Id))
            {
                throw new ApiException("IP备案申请表主键id不可为空!");
            }
            return BLL_ContractOverseasIPRecord.Instance.AuditIPKeepRecordApply(auditIpKeepRecordApplyIn);
        }

        /// <summary>
        /// 根据查询条件获取海外IP备案申请列表。
        /// </summary>
        /// <param name="searchIpKeepRecordApplyListIn"></param>
        /// <returns></returns>
        [HttpPost]
        [Obsolete]
        public ApiTableOut<SearchIPKeepRecordApplyList_Out> SearchIPKeepRecordApplyListOld(SearchIPKeepRecordApplyList_In searchIpKeepRecordApplyListIn)
        {
            int total = 0;
            var list = new List<SearchIPKeepRecordApplyList_Out>();
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据账号类别获取Gtis用户列表
        /// </summary>
        /// <param name="gtisUserByAccountCategoryIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetGtisUserByAccountCategory_Out> GetGtisUserByAccountCategory(GetGtisUserByAccountCategory_In gtisUserByAccountCategoryIn)
        {
            int total = 0;
            var list =  DbOpe_crm_ipkeeprecord_user.Instance.GetGtisUserByAccountCategory(gtisUserByAccountCategoryIn, ref total);
            return GetApiTableOut(list, total);
        }
        
        /// <summary>
        /// 根据国家Id获取地址信息（省级别）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public List<Province_OUT> GetAddress_Province(Province_In provinceIn)
        {
            return LocalCache.LC_Address.ProvinceCache
                .Where(w => w.CountryID == provinceIn.CountryID).ToList();
        }

        /// <summary>
        /// 根据省Id获取地址信息(市级别)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public List<City_OUT> GetAddress_City(City_In cityIn)
        {
            return LocalCache.LC_Address.CityCache.Where(w => w.ProvinceID == cityIn.ProvinceID).ToList();
        }

        /// <summary>
        /// 根据查询条件查询合同列表
        /// </summary>
        /// <param name="searchSelectContractListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchSelectContractList_Out> SearchSelectContractList(
            SearchSelectContractList_In searchSelectContractListIn)
        {
            int total = 0;
            var list =  DbOpe_crm_ipkeeprecord.Instance.SearchSelectContractList(searchSelectContractListIn, ref total);
            return GetApiTableOut(list, total);
        }
        
        /// <summary>
        /// 根据合同Id获取合同基本信息
        /// </summary>
        /// <param name="getContractInfoByIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public Contract_Info GetContractInfoById(GetContractInfoById_In getContractInfoByIdIn)
        {
            if (getContractInfoByIdIn == null || string.IsNullOrEmpty(getContractInfoByIdIn.ContractId))
            {
                throw new ApiException("合同Id不可为空");
            }
            return DbOpe_crm_ipkeeprecord.Instance.GetContractInfoById(getContractInfoByIdIn.ContractId);
        }
    }
}