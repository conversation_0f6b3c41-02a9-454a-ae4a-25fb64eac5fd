# SaleWits资源下发操作人参数实现总结

## 需求描述

用户要求在SaleWits资源下发接口中添加操作人参数，具体需求：
- 如果是定时下发，使用写死的固定名称
- 如果是人工审核下发，需要写服务审核人的名字

## 实现方案

### 1. 数据模型修改

**文件**: `Model/BLLModel/ServiceOpening/ServiceOpeningModels.cs`

在 `ServiceOpeningParams` 类中添加了操作人信息字段：

```csharp
#region 操作人信息

/// <summary>
/// 操作人ID
/// </summary>
public string OperatorId { get; set; }

/// <summary>
/// 操作人名称
/// </summary>
public string OperatorName { get; set; }

#endregion
```

### 2. 服务开通方法修改

**文件**: `BLL/ServiceOpening/BLL_ServiceOpening.cs`

修改 `ExecuteServiceOpening` 方法签名，添加操作人ID参数：

```csharp
/// <summary>
/// 执行组合服务开通
/// </summary>
/// <param name="serviceId">服务ID（crm_contract_serviceinfo_wits表ID）</param>
/// <param name="isTestMode">是否为测试模式</param>
/// <param name="operatorId">操作人ID（可选，用于人工审核下发）</param>
/// <returns>开通结果</returns>
public async Task<ServiceOpeningResult> ExecuteServiceOpening(string serviceId, bool isTestMode = false, string operatorId = null)
```

### 3. 数据准备方法修改

**文件**: `BLL/ServiceOpening/BLL_ServiceOpening.DataPreparation.cs`

修改 `PrepareOpeningParams` 方法，添加操作人信息设置逻辑：

```csharp
// 4. 设置操作人信息
if (!string.IsNullOrEmpty(operatorId))
{
    openingParams.OperatorId = operatorId;
    // 获取操作人姓名
    try
    {
        var user = DbOpe_sys_user.Instance.GetDbSysUserById(operatorId);
        openingParams.OperatorName = user?.Name ?? operatorId;
    }
    catch (Exception ex)
    {
        LogUtil.AddErrorLog($"获取操作人姓名失败，用户ID：{operatorId}，错误：{ex.Message}");
        openingParams.OperatorName = operatorId; // 使用ID作为备用
    }
}
```

### 4. SaleWits资源下发逻辑修改

**文件**: `BLL/ServiceOpening/BLL_ServiceOpening.ServiceProcessors.cs`

在 `ProcessSaleWitsService` 方法中添加操作人名称判断逻辑：

```csharp
// 确定操作人名称：定时下发使用固定名称，人工审核下发使用审核人名称
var operatorName = !string.IsNullOrEmpty(openingParams.OperatorName) 
    ? openingParams.OperatorName 
    : "系统定时下发";

var distributionResult = await CallSaleWitsResourceDistributionApi(
    tenantId,
    presetResources.EmailCount,
    presetResources.TokenCount,
    presetResources.RechargeAmount,
    operatorName);
```

### 5. 人工审核下发调用修改

**文件**: `BLL/BLL_ContractServiceGTISSeries.cs`

在 `ReviewWitsAppl` 方法中修改服务开通调用，传递审核人ID：

```csharp
//2.执行组合服务开通（传递审核人ID）
var retVal = BLL_ServiceOpening.Instance.ExecuteServiceOpening(serveData.Id, false, UserId).Result;
```

### 6. 定时下发操作人设置

**文件**: `BLL/ServiceOpening/BLL_ServiceOpening.cs`

在 `CreateAnnualDistributionParams` 方法中设置定时下发的固定操作人信息：

```csharp
// 设置定时下发的固定操作人信息
openingParams.OperatorId = "SYSTEM";
openingParams.OperatorName = "系统定时下发";
```

## 实现效果

### 人工审核下发场景
1. 用户在审核界面点击"复核通过"
2. 系统调用 `ReviewWitsAppl` 方法
3. 该方法调用 `ExecuteServiceOpening` 时传递当前审核人的 `UserId`
4. 系统根据 `UserId` 查询用户表获取审核人姓名
5. 在调用SaleWits资源下发接口时，传递审核人姓名作为操作人

### 定时下发场景
1. 系统定时任务触发 `ExecuteSaleWitsScheduledDistribution`
2. 调用 `CreateAnnualDistributionParams` 创建参数时设置固定操作人信息
3. 操作人ID设置为 "SYSTEM"，操作人名称设置为 "系统定时下发"
4. 在调用SaleWits资源下发接口时，传递固定的操作人名称

## 技术要点

1. **向后兼容**: 新增的 `operatorId` 参数为可选参数，不影响现有调用
2. **异常处理**: 获取用户姓名时添加了异常处理，确保即使查询失败也不影响主流程
3. **默认值处理**: 当没有传递操作人信息时，默认使用 "系统定时下发" 作为操作人名称
4. **数据一致性**: 确保定时下发和人工审核下发都能正确传递操作人信息

## 测试建议

1. **人工审核测试**: 创建一个服务申请，进行审核通过操作，验证操作人是否为审核人姓名
2. **定时下发测试**: 等待定时任务执行或手动触发，验证操作人是否为 "系统定时下发"
3. **异常情况测试**: 测试用户ID不存在的情况，验证系统是否能正常处理
4. **日志验证**: 检查日志中是否正确记录了操作人信息

## 相关文件清单

- `Model/BLLModel/ServiceOpening/ServiceOpeningModels.cs` - 数据模型
- `BLL/ServiceOpening/BLL_ServiceOpening.cs` - 主服务开通逻辑
- `BLL/ServiceOpening/BLL_ServiceOpening.DataPreparation.cs` - 数据准备逻辑
- `BLL/ServiceOpening/BLL_ServiceOpening.ServiceProcessors.cs` - SaleWits处理逻辑
- `BLL/BLL_ContractServiceGTISSeries.cs` - 审核流程逻辑
