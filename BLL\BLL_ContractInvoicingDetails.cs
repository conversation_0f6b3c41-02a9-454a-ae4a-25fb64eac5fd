﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.System;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoicingDetails;

namespace CRM2_API.BLL
{
    public class BLL_ContractInvoicingDetails : BaseBLL<BLL_ContractInvoicingDetails>
    {
        /// <summary>
        /// 添加合同发票开票明细信息
        /// </summary>
        public void AddInvoicingDetails(List<AddInvoicingDetails_In> addInvoicingDetailsIn)
        {
            DbOpe_crm_contract_invoice_invoicingdetails_item.Instance.TransDeal(() =>
            {
                foreach (AddInvoicingDetails_In d in addInvoicingDetailsIn)
                {
                    Db_crm_contract_invoice_invoicingdetails_item item = d.MappingTo<Db_crm_contract_invoice_invoicingdetails_item>();
                    item.OrderNum = DbOpe_crm_contract_invoice_invoicingdetails_item.Instance.GetInvoicingDetailsItemMaxOrderNum();
                    DbOpe_crm_contract_invoice_invoicingdetails_item.Instance.InsertData(item);
                }
            });
        }

        /// <summary>
        /// 获取合同发票开票明细信息
        /// </summary>
        public List<InvoicingDetails_Out> GetInvoicingDetails()
        {
            return DbOpe_crm_contract_invoice_invoicingdetails_item.Instance.GetInvoicingDetails();
        }

        /// <summary>
        /// 获取合同发票开票明细信息全部包含删除
        /// </summary>
        public List<InvoicingDetails_Out> GetInvoicingDetailsAll()
        {
            return DbOpe_crm_contract_invoice_invoicingdetails_item.Instance.GetInvoicingDetailsAll();
        }

        /// <summary>
        /// 删除合同发票开票明细信息
        /// </summary>
        public void DeleteInvoicingDetails(string id)
        {
            Db_crm_contract_invoice_invoicingdetails_item item = DbOpe_crm_contract_invoice_invoicingdetails_item.Instance.GetInvoicingDetailsById(id, true);
            //验证数据权限
            if (item == null)
            {
                throw new ApiException("当前操作人无法删除开票明细信息");
            }
            DbOpe_crm_contract_invoice_invoicingdetails_item.Instance.DeleteData(id);
        }
    }
}
