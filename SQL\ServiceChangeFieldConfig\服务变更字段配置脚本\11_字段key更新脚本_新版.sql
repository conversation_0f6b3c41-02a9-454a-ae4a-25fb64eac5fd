-- 服务变更字段配置 - 字段key更新脚本（新版）
-- 执行日期: 2025-02-01
-- 根据key变动 copy.md更新字段名称

-- ================================
-- 更新字段key映射
-- ================================

-- 1. 更新GtisServiceStart为GtisServiceCycleStart
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisServiceCycleStart', FieldName = 'GTIS服务开始日期'
WHERE FieldKey = 'GtisServiceStart';

-- 2. 更新GtisServiceEnd为GtisServiceCycleEnd
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisServiceCycleEnd', FieldName = 'GTIS服务结束日期'
WHERE FieldKey = 'GtisServiceEnd';

-- 3. 更新CountryList为GtisApplCountry
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisApplCountry', FieldName = '申请国家'
WHERE FieldKey = 'CountryList';

-- 4. 更新GtisCustomReport为GtisWordRptPermissions
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisWordRptPermissions', FieldName = '定制报告'
WHERE FieldKey = 'GtisCustomReport';

-- 5. 更新GtisCustomReportNum为GtisWordRptMaxTimes
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisWordRptMaxTimes', FieldName = '定制报告数'
WHERE FieldKey = 'GtisCustomReportNum';

-- 6. 更新GtisSubAccountCountryPermission为GtisAuthorizationNum
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisAuthorizationNum', FieldName = '授权数量'
WHERE FieldKey = 'GtisSubAccountCountryPermission';

-- 7. 更新GtisShareUsageTotal为GtisShareUsageNum
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisShareUsageNum', FieldName = '共享使用总数'
WHERE FieldKey = 'GtisShareUsageTotal';

-- 8. 更新GtisUserNum为GtisSharePeopleNum
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisSharePeopleNum', FieldName = '使用者数'
WHERE FieldKey = 'GtisUserNum';

-- 9. 更新SalesWitsGiftTokenNum为SalesWitsTokenCount
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'SalesWitsTokenCount', FieldName = 'SalesWits赠送Token数量'
WHERE FieldKey = 'SalesWitsGiftTokenNum';

-- 10. 更新SalesWitsGiftEmailNum为SalesWitsEmailCount
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'SalesWitsEmailCount', FieldName = 'SalesWits赠送邮件数量'
WHERE FieldKey = 'SalesWitsGiftEmailNum';

-- 11. 更新SalesWitsRecharge为SalesWitsAddCredit
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'SalesWitsAddCredit', FieldName = 'SaleWits充值'
WHERE FieldKey = 'SalesWitsRecharge';

-- 12. 更新CouponList为CounponDetailIdList
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'CounponDetailIdList', FieldName = '优惠券列表'
WHERE FieldKey = 'CouponList';

-- ================================
-- 更新TriggerFields中的字段引用
-- ================================

-- 1. 更新TriggerFields中的GtisServiceStart为GtisServiceCycleStart
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisServiceStart', 'GtisServiceCycleStart')
WHERE TriggerFields LIKE '%GtisServiceStart%';

-- 2. 更新TriggerFields中的GtisServiceEnd为GtisServiceCycleEnd
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisServiceEnd', 'GtisServiceCycleEnd')
WHERE TriggerFields LIKE '%GtisServiceEnd%';

-- 3. 更新TriggerFields中的CountryList为GtisApplCountry
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'CountryList', 'GtisApplCountry')
WHERE TriggerFields LIKE '%CountryList%';

-- 4. 更新TriggerFields中的GtisCustomReport为GtisWordRptPermissions
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisCustomReport', 'GtisWordRptPermissions')
WHERE TriggerFields LIKE '%GtisCustomReport%';

-- 5. 更新TriggerFields中的GtisCustomReportNum为GtisWordRptMaxTimes
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisCustomReportNum', 'GtisWordRptMaxTimes')
WHERE TriggerFields LIKE '%GtisCustomReportNum%';

-- 6. 更新TriggerFields中的GtisSubAccountCountryPermission为GtisAuthorizationNum
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisSubAccountCountryPermission', 'GtisAuthorizationNum')
WHERE TriggerFields LIKE '%GtisSubAccountCountryPermission%';

-- 7. 更新TriggerFields中的GtisShareUsageTotal为GtisShareUsageNum
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisShareUsageTotal', 'GtisShareUsageNum')
WHERE TriggerFields LIKE '%GtisShareUsageTotal%';

-- 8. 更新TriggerFields中的GtisUserNum为GtisSharePeopleNum
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisUserNum', 'GtisSharePeopleNum')
WHERE TriggerFields LIKE '%GtisUserNum%';

-- 9. 更新TriggerFields中的SalesWitsGiftTokenNum为SalesWitsTokenCount
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'SalesWitsGiftTokenNum', 'SalesWitsTokenCount')
WHERE TriggerFields LIKE '%SalesWitsGiftTokenNum%';

-- 10. 更新TriggerFields中的SalesWitsGiftEmailNum为SalesWitsEmailCount
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'SalesWitsGiftEmailNum', 'SalesWitsEmailCount')
WHERE TriggerFields LIKE '%SalesWitsGiftEmailNum%';

-- 11. 更新TriggerFields中的SalesWitsRecharge为SalesWitsAddCredit
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'SalesWitsRecharge', 'SalesWitsAddCredit')
WHERE TriggerFields LIKE '%SalesWitsRecharge%';

-- 12. 更新TriggerFields中的CouponList为CounponDetailIdList
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'CouponList', 'CounponDetailIdList')
WHERE TriggerFields LIKE '%CouponList%';

-- ================================
-- 验证更新结果
-- ================================

SELECT '字段key更新完成！' AS message;

-- 显示更新后的字段统计
SELECT 
    FieldKey,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT ChangeReasonEnum ORDER BY ChangeReasonEnum) as change_reasons
FROM crm_service_change_reason_field_config 
WHERE FieldKey IN (
    'GtisServiceCycleStart', 'GtisServiceCycleEnd', 'GtisApplCountry', 
    'GtisWordRptPermissions', 'GtisWordRptMaxTimes', 'GtisAuthorizationNum',
    'GtisShareUsageNum', 'GtisSharePeopleNum', 'SalesWitsTokenCount',
    'SalesWitsEmailCount', 'SalesWitsAddCredit', 'CounponDetailIdList'
)
GROUP BY FieldKey
ORDER BY FieldKey;