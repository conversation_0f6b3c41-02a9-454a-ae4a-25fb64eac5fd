﻿using System.Diagnostics;
using System.Diagnostics.Contracts;
using System.Text.Json;
using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.Common.Cron;
using CRM2_API.Common.Utils;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.ContractOverseasIPRecord;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using Quartz;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;

namespace CRM2_API.BLL
{
    public class BLL_ContractOverseasIPRecord : BaseBLL<BLL_ContractOverseasIPRecord>
    {
        /// <summary>
        /// ip备案定时任务的前缀
        /// </summary>
        public static readonly string IPKeepRecordTaskPrefix = "refreshIPKeepRecordStateTask_";

        /// <summary>
        /// 添加IP备案申请信息。
        /// </summary>
        /// <param name="addIpKeepRecordApplyIn"></param>
        /// <returns></returns>
        public AddIPKeepRecordApply_Out AddIPKeepRecordApply(AddIPKeepRecordApply_In addIpKeepRecordApplyIn)
        {
            Db_crm_contract contract = null;
            if (addIpKeepRecordApplyIn.AccountType != EnumAccountType.TemporaryAccount)
            {
                //240912 合同、发票、到账权限更改
                contract = DbOpe_crm_contract.Instance.GetContractById(addIpKeepRecordApplyIn.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("未找到合同或该用户没有合同权限");
                }
            }



            AddIPKeepRecordApply_Out result = new() { Data = 0 };

            //验证开始时间是否为同一天 修改 2024年6月13日
            List<string> userListCheck = new List<string>();


            var insertId = Guid.NewGuid().ToString();
            var ipkeeprecordEntity = new Db_crm_ipkeeprecord
            {
                Id = insertId,
                AccountType = addIpKeepRecordApplyIn.AccountType,
                ContractId = addIpKeepRecordApplyIn.ContractId,
                Remark = addIpKeepRecordApplyIn.Remark,
                //State = addIpKeepRecordApplyIn.RecordState,
                State = EnumRecordState.Submit, //提交后就是待审核
                ExpirationDate = addIpKeepRecordApplyIn.ExpirationDate,
                Deleted = false,
                CreateUser = TokenModel.Instance.id,
                CreateDate = DateTime.Now,
            };
            DbOpe_crm_ipkeeprecord.Instance.InsertQueue(ipkeeprecordEntity);
            if (!string.IsNullOrEmpty(addIpKeepRecordApplyIn.IPKeepRecordUserAndAreaJsonStr) && (addIpKeepRecordApplyIn.IPKeepRecordUserAndArea == null || addIpKeepRecordApplyIn.IPKeepRecordUserAndArea.Count == 0))
            {
                try
                {
                    addIpKeepRecordApplyIn.IPKeepRecordUserAndArea = JsonSerializer.Deserialize<List<Update_IPKeepRecordUserAndArea>>(addIpKeepRecordApplyIn.IPKeepRecordUserAndAreaJsonStr);
                }
                catch (Exception _)
                {
                    addIpKeepRecordApplyIn.IPKeepRecordUserAndArea = null;
                }
            }
            if (addIpKeepRecordApplyIn.IPKeepRecordUserAndArea is not null and { Count: > 0 })
            {
                int addUserAndAreaSort = 0;
                List<Db_crm_ipkeeprecord_userandarea> userAndAreaInsertList = new(addIpKeepRecordApplyIn.IPKeepRecordUserAndArea.Count);
                Db_crm_ipkeeprecord_userandarea userAndAreaTemp = default;
                String userAndAreaInsertId = default;

                List<Db_crm_ipkeeprecord_user> userInsertList = new();
                Db_crm_ipkeeprecord_user userTemp = default;

                List<Db_crm_ipkeeprecord_area> areaInsertList = new();
                Db_crm_ipkeeprecord_area areaTemp = default;
                int userSortNum, areaSortNum;

                foreach (Update_IPKeepRecordUserAndArea addUserAndAreaItem in addIpKeepRecordApplyIn.IPKeepRecordUserAndArea)
                {
                    userSortNum = 0;
                    areaSortNum = 0;
                    //1.主表
                    userAndAreaInsertId = Guid.NewGuid().ToString();
                    userAndAreaTemp = new()
                    {
                        Id = userAndAreaInsertId,
                        IPKeepRecordId = insertId,
                        Deleted = false,
                        SortNum = ++addUserAndAreaSort,
                        CreateUser = TokenModel.Instance.id,
                        CreateDate = DateTime.Now,
                    };
                    userAndAreaInsertList.Add(userAndAreaTemp);

                    //2.用户
                    foreach (GetByID_IPKeepRecordUserAndArea_UserId userItem in addUserAndAreaItem.UserList)
                    {
                        userTemp = new()
                        {
                            Id = Guid.NewGuid().ToString(),
                            IPKeepRecordUserAndAreaId = userAndAreaInsertId,
                            AccountType = addIpKeepRecordApplyIn.AccountType,
                            UserId = userItem.UserId,
                            Deleted = false,
                            SortNum = ++userSortNum,
                            CreateUser = TokenModel.Instance.id,
                            CreateDate = DateTime.Now,
                        };
                        userListCheck.Add(userItem.UserId);

                        userInsertList.Add(userTemp);
                    }

                    //3.地区
                    foreach (GetByID_IPKeepRecordUserAndArea_AreaId areaItem in addUserAndAreaItem.AreaList)
                    {
                        areaTemp = new()
                        {
                            Id = Guid.NewGuid().ToString(),
                            IPKeepRecordUserAndAreaId = userAndAreaInsertId,
                            CountryId = LocalCache.LC_Address.ProvinceCache?.FirstOrDefault(cache => cache.Id == areaItem.AreaId)?.CountryID ?? -1,
                            AreaId = areaItem.AreaId,
                            Deleted = false,
                            SortNum = ++areaSortNum,
                            CreateUser = TokenModel.Instance.id,
                            CreateDate = DateTime.Now,
                        };
                        areaInsertList.Add(areaTemp);
                    }
                }

                if (userAndAreaInsertList is not null and { Count: > 0 })
                {
                    DbOpe_crm_ipkeeprecord_userandarea.Instance.InsertQueue(userAndAreaInsertList);
                }

                if (areaInsertList is not null and { Count: > 0 })
                {
                    DbOpe_crm_ipkeeprecord_area.Instance.InsertQueue(areaInsertList);
                }

                if (userInsertList is not null and { Count: > 0 })
                {
                    DbOpe_crm_ipkeeprecord_user.Instance.InsertQueue(userInsertList);
                }
            }
            //验证添加的用户 修改 2024年6月13日
            if (addIpKeepRecordApplyIn.AccountType == EnumAccountType.TemporaryAccount)
            {
                var checkUser = DbOpe_crm_ipkeeprecord_user.Instance.CheckUser(userListCheck);
                if (checkUser is null)
                {
                    throw new ApiException("账号开通时间不一致，请分别申请!");
                }
            }


            if (addIpKeepRecordApplyIn.IPKeepRecordAttachFile is not null and { Count: > 0 })
            {
                FormFileCollection formFileCollection = new();
                foreach (Update_IPKeepRecordAttachFile updateIpKeepRecordAttachFile in addIpKeepRecordApplyIn.IPKeepRecordAttachFile)
                {
                    formFileCollection.Add(updateIpKeepRecordAttachFile.File);
                }
                Util<DbOpe_crm_ipkeeprecord_attachfile, BM_AttachFiles> util = new(DbOpe_crm_ipkeeprecord_attachfile.Instance);
                bool uploadResult = util.UploadFiles(insertId, formFileCollection, AttachEnumOption.ContractIpKeepRecord);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
            }

            if (addIpKeepRecordApplyIn.State == 1)
            {
                Db_crm_ipkeeprecordapply applyEntity = new()
                {
                    Id = Guid.NewGuid().ToString(),
                    IPKeepRecordId = insertId,
                    IsNewBuild = true,
                    State = EnumRecordState.Submit,
                    ApplicantId = TokenModel.Instance.id,
                    ApplicantDate = DateTime.Now,
                    Remark = addIpKeepRecordApplyIn.Remark,
                    Deleted = false,
                    CreateUser = TokenModel.Instance.id,
                    CreateDate = DateTime.Now,
                    Version = Guid.NewGuid().ToString(),
                };
                DbOpe_crm_ipkeeprecordapply.Instance.InsertQueue(applyEntity);
            }
            DbOpe_crm_ipkeeprecord.Instance.SaveQueues();
            result.Data = 1;

            string dataState = EnumRecordState.Submit.GetEnumDescription();

            if (contract != null)
            {
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_ipkeeprecord, Db_crm_contract>("IP备案申请审批流程", insertId, ipkeeprecordEntity, contract, addIpKeepRecordApplyIn.Remark, dataState, "新建");
            }
            else
            {
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_ipkeeprecord>("IP备案申请审批流程", insertId, ipkeeprecordEntity, addIpKeepRecordApplyIn.Remark, dataState, "新建");
            }
            return result;
        }

        /// <summary>
        /// 修改IP备案申请信息。
        /// </summary>
        /// <param name="updateIpKeepRecordApplyIn"></param>
        /// <returns></returns>
        public UpdateIPKeepRecordApply_Out UpdateIPKeepRecordApply(UpdateIPKeepRecordApply_In updateIpKeepRecordApplyIn)
        {
            Db_crm_contract contract = null;
            //2024年10月30日 临时账号不需要合同验证部分
            if (updateIpKeepRecordApplyIn.AccountType == EnumAccountType.FormalAccount)
            {
                //240912 合同、发票、到账权限更改
                contract = DbOpe_crm_contract.Instance.GetContractById(updateIpKeepRecordApplyIn.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("未找到合同或该用户没有合同权限");
                }

            }


            UpdateIPKeepRecordApply_Out result = new() { Data = 0 };
            var recordData = GetIpKeepRecordDataById(updateIpKeepRecordApplyIn.Id);
            if (recordData == null)
            {
                throw new ApiException("Id所对应的IP备案申请信息不存在!");
            }

            if (recordData.State is not (EnumRecordState.Expire or EnumRecordState.Refuse or EnumRecordState.Draft))
            {
                throw new ApiException("IP备案申请信息非过期、拒绝、草稿状态下禁止修改!");
            }

            //验证开始时间是否为同一天 修改 2024年6月13日
            List<string> userListCheck = new List<string>();

            EnumAccountType? beforeType = recordData.AccountType;   //之前的用户类别
            recordData.AccountType = updateIpKeepRecordApplyIn.AccountType;
            if (updateIpKeepRecordApplyIn.AccountType == EnumAccountType.FormalAccount)
            {
                recordData.ContractId = updateIpKeepRecordApplyIn.ContractId;
            }
            else
            {
                recordData.ContractId = null;
            }

            recordData.Remark = updateIpKeepRecordApplyIn.Remark;
            //recordData.State = updateIpKeepRecordApplyIn.RecordState;
            recordData.State = EnumRecordState.Submit;
            recordData.ExpirationDate = updateIpKeepRecordApplyIn.ExpirationDate;
            DbOpe_crm_ipkeeprecord.Instance.UpdateQueue(recordData);

            if (!string.IsNullOrEmpty(updateIpKeepRecordApplyIn.IPKeepRecordUserAndAreaJsonStr) &&
                (updateIpKeepRecordApplyIn.IPKeepRecordUserAndArea == null ||
                 updateIpKeepRecordApplyIn.IPKeepRecordUserAndArea.Count == 0))
            {
                try
                {
                    updateIpKeepRecordApplyIn.IPKeepRecordUserAndArea = JsonSerializer.Deserialize<List<Update_IPKeepRecordUserAndArea>>(updateIpKeepRecordApplyIn.IPKeepRecordUserAndAreaJsonStr);
                }
                catch (Exception _)
                {
                    updateIpKeepRecordApplyIn.IPKeepRecordUserAndArea = null;
                }
            }

            if (beforeType == updateIpKeepRecordApplyIn.AccountType)
            {
                if (updateIpKeepRecordApplyIn.IPKeepRecordUserAndArea is not null and { Count: > 0 })
                {
                    //数据库中存在的地区数据
                    List<GetByID_IPKeepRecordUserAndArea> iPKeepRecordUserAndAreaData =
                        DbOpe_crm_ipkeeprecord_userandarea.Instance.GetIPKeepRecordUserAndAreaDataById(
                            updateIpKeepRecordApplyIn.Id);
                    //转化为字典
                    Dictionary<string, GetByID_IPKeepRecordUserAndArea> iPKeepRecordUserAndAreaDict =
                        iPKeepRecordUserAndAreaData.ToDictionary(k => k.Id, v => v);

                    String userAndAreaInsertId = default;
                    int userAndAreaSourNum = 0;

                    List<Db_crm_ipkeeprecord_userandarea> userAndAreaInsertList = new();
                    Db_crm_ipkeeprecord_userandarea userAndAreaTemp = default;
                    var userAndAreaUpdateList = new List<Dictionary<string, object>>();
                    var userAndAreaDeleteList = new List<Dictionary<string, object>>();
                    Dictionary<string, object> userAndAreaDictTemp = default;
                    List<string> userAndAreaNotDeleteIdList = new();

                    List<Db_crm_ipkeeprecord_user> userInsertList = new();
                    Db_crm_ipkeeprecord_user userTemp = default;
                    var userUpdateList = new List<Dictionary<string, object>>();
                    var userDeleteList = new List<Dictionary<string, object>>();
                    Dictionary<string, object> userDictTemp = default;

                    List<Db_crm_ipkeeprecord_area> areaInsertList = new();
                    Db_crm_ipkeeprecord_area areaTemp = default;
                    var areaUpdateList = new List<Dictionary<string, object>>();
                    var areaDeleteList = new List<Dictionary<string, object>>();
                    Dictionary<string, object> areaDictTemp = default;

                    int userSortNum, areaSortNum;

                    foreach (Update_IPKeepRecordUserAndArea updateIpKeepRecordUserAndArea in updateIpKeepRecordApplyIn.IPKeepRecordUserAndArea)
                    {
                        userSortNum = 0;
                        areaSortNum = 0;

                        if (string.IsNullOrEmpty(updateIpKeepRecordUserAndArea.Id))
                        {
                            //直接走新增
                            //1.主表
                            userAndAreaInsertId = Guid.NewGuid().ToString();
                            userAndAreaTemp = new()
                            {
                                Id = userAndAreaInsertId,
                                IPKeepRecordId = updateIpKeepRecordApplyIn.Id,
                                Deleted = false,
                                SortNum = ++userAndAreaSourNum,
                                CreateUser = TokenModel.Instance.id,
                                CreateDate = DateTime.Now,
                            };
                            userAndAreaInsertList.Add(userAndAreaTemp);

                            //2.用户
                            foreach (GetByID_IPKeepRecordUserAndArea_UserId userItem in updateIpKeepRecordUserAndArea.UserList)
                            {
                                userTemp = new()
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    IPKeepRecordUserAndAreaId = userAndAreaInsertId,
                                    AccountType = updateIpKeepRecordApplyIn.AccountType,
                                    UserId = userItem.UserId,
                                    Deleted = false,
                                    SortNum = ++userSortNum,
                                    CreateUser = TokenModel.Instance.id,
                                    CreateDate = DateTime.Now,
                                };
                                userListCheck.Add(userItem.UserId);
                                userInsertList.Add(userTemp);
                            }

                            //3.地区
                            foreach (GetByID_IPKeepRecordUserAndArea_AreaId areaItem in updateIpKeepRecordUserAndArea.AreaList)
                            {
                                areaTemp = new()
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    IPKeepRecordUserAndAreaId = userAndAreaInsertId,
                                    CountryId = LocalCache.LC_Address.ProvinceCache?.FirstOrDefault(cache => cache.Id == areaItem.AreaId)?.CountryID ?? -1,
                                    AreaId = areaItem.AreaId,
                                    Deleted = false,
                                    SortNum = ++areaSortNum,
                                    CreateUser = TokenModel.Instance.id,
                                    CreateDate = DateTime.Now,
                                };
                                areaInsertList.Add(areaTemp);

                            }
                        }
                        else
                        {
                            //拿数据库中的数据
                            var DbUserAndAreaData = iPKeepRecordUserAndAreaDict[updateIpKeepRecordUserAndArea.Id] ?? throw new ApiException("地区参数错误!");
                            //1.主表
                            userAndAreaDictTemp = new()
                            {
                                {"Id",updateIpKeepRecordUserAndArea.Id},
                                {"SortNum",++userAndAreaSourNum},
                                {"UpdateDate",DateTime.Now},
                                {"UpdateUser",TokenModel.Instance.id},
                            };
                            userAndAreaUpdateList.Add(userAndAreaDictTemp);
                            userAndAreaNotDeleteIdList.Add(updateIpKeepRecordUserAndArea.Id);

                            //2.用户
                            foreach (GetByID_IPKeepRecordUserAndArea_UserId userItem in updateIpKeepRecordUserAndArea.UserList)
                            {
                                if (string.IsNullOrEmpty(userItem.Id))
                                {
                                    userTemp = new()
                                    {
                                        Id = Guid.NewGuid().ToString(),
                                        IPKeepRecordUserAndAreaId = updateIpKeepRecordUserAndArea.Id,
                                        AccountType = updateIpKeepRecordApplyIn.AccountType,
                                        UserId = userItem.UserId,
                                        Deleted = false,
                                        SortNum = ++userSortNum,
                                        CreateUser = TokenModel.Instance.id,
                                        CreateDate = DateTime.Now,
                                    };
                                    userListCheck.Add(userItem.UserId);
                                    userInsertList.Add(userTemp);
                                }
                                else
                                {
                                    userDictTemp = new()
                                    {
                                        {"Id",userItem.Id},
                                        {"UserId",userItem.UserId},
                                        {"SortNum",++userSortNum},
                                        {"UpdateDate",DateTime.Now},
                                        {"UpdateUser",TokenModel.Instance.id},
                                    };
                                    userListCheck.Add(userItem.UserId);
                                    userUpdateList.Add(userDictTemp);
                                }
                            }
                            // 需要删除的用户数据
                            var userRemoveList = DbUserAndAreaData.UserList.Except(updateIpKeepRecordUserAndArea.UserList.Where(w => !string.IsNullOrEmpty(w.Id)).ToList());
                            foreach (GetByID_IPKeepRecordUserAndArea_UserId userRemoveItem in userRemoveList)
                            {
                                userDictTemp = new()
                                {
                                    {"Id",userRemoveItem.Id},
                                    {"Deleted",true},
                                    {"UpdateDate",DateTime.Now},
                                    {"UpdateUser",TokenModel.Instance.id},
                                };
                                userDeleteList.Add(userDictTemp);
                            }
                            //3.地区
                            foreach (GetByID_IPKeepRecordUserAndArea_AreaId areaItem in updateIpKeepRecordUserAndArea.AreaList)
                            {
                                if (string.IsNullOrEmpty(areaItem.Id))
                                {
                                    areaTemp = new()
                                    {
                                        Id = Guid.NewGuid().ToString(),
                                        IPKeepRecordUserAndAreaId = updateIpKeepRecordUserAndArea.Id,
                                        CountryId = LocalCache.LC_Address.ProvinceCache?.FirstOrDefault(cache => cache.Id == areaItem.AreaId)?.CountryID ?? -1,
                                        AreaId = areaItem.AreaId,
                                        Deleted = false,
                                        SortNum = ++areaSortNum,
                                        CreateUser = TokenModel.Instance.id,
                                        CreateDate = DateTime.Now,
                                    };
                                    areaInsertList.Add(areaTemp);
                                }
                                else
                                {
                                    areaDictTemp = new()
                                    {
                                        {"Id",areaItem.Id},
                                        {"AreaId",areaItem.AreaId},
                                        {"SortNum",++areaSortNum},
                                        {"UpdateDate",DateTime.Now},
                                        {"UpdateUser",TokenModel.Instance.id},
                                    };
                                    areaUpdateList.Add(areaDictTemp);
                                }
                            }
                            // 3.地区需要删除的数据
                            var areaRemoveList = DbUserAndAreaData.AreaList.Except(updateIpKeepRecordUserAndArea.AreaList.Where(w => !string.IsNullOrEmpty(w.Id)).ToList());
                            foreach (GetByID_IPKeepRecordUserAndArea_AreaId areaRemoveItem in areaRemoveList)
                            {
                                areaDictTemp = new()
                                {
                                    {"Id",areaRemoveItem.Id},
                                    {"Deleted",true},
                                    {"UpdateDate",DateTime.Now},
                                    {"UpdateUser",TokenModel.Instance.id},
                                };
                                areaDeleteList.Add(areaDictTemp);
                            }
                        }
                    }

                    var areaDeleteByMainIdList = new List<Dictionary<string, object>>();
                    var userDeleteByMainIdList = new List<Dictionary<string, object>>();

                    foreach (GetByID_IPKeepRecordUserAndArea dbExistsUserAndAreaData in iPKeepRecordUserAndAreaData)
                    {
                        if (userAndAreaNotDeleteIdList.Contains(dbExistsUserAndAreaData.Id)) continue;
                        userAndAreaDictTemp = new()
                        {
                            {"Id",dbExistsUserAndAreaData.Id},
                            {"Deleted",true},
                            {"UpdateDate",DateTime.Now},
                            {"UpdateUser",TokenModel.Instance.id},
                        };
                        userAndAreaDeleteList.Add(userAndAreaDictTemp);

                        userDictTemp = new()
                        {
                            {"IPKeepRecordUserAndAreaId",dbExistsUserAndAreaData.Id},
                            {"Deleted",true},
                            {"UpdateDate",DateTime.Now},
                            {"UpdateUser",TokenModel.Instance.id},
                        };
                        userDeleteByMainIdList.Add(userDictTemp);

                        areaDictTemp = new()
                        {
                            {"IPKeepRecordUserAndAreaId",dbExistsUserAndAreaData.Id},
                            {"Deleted",true},
                            {"UpdateDate",DateTime.Now},
                            {"UpdateUser",TokenModel.Instance.id},
                        };
                        areaDeleteByMainIdList.Add(areaDictTemp);
                    }

                    if (userAndAreaInsertList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_userandarea.Instance.InsertQueue(userAndAreaInsertList);
                    }

                    if (areaInsertList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_area.Instance.InsertQueue(areaInsertList);
                    }

                    if (userInsertList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_user.Instance.InsertQueue(userInsertList);
                    }

                    if (userAndAreaUpdateList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_userandarea.Instance.UpdateByDict(userAndAreaUpdateList);
                    }

                    if (userAndAreaDeleteList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_userandarea.Instance.UpdateByDict(userAndAreaDeleteList);
                    }

                    if (areaUpdateList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_area.Instance.UpdateByDict(areaUpdateList);
                    }

                    if (areaDeleteList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_area.Instance.UpdateByDict(areaDeleteList);
                    }

                    if (userUpdateList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_user.Instance.UpdateByDict(userUpdateList);
                    }

                    if (userDeleteList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_user.Instance.UpdateByDict(userDeleteList);
                    }

                    if (areaDeleteByMainIdList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_area.Instance.UpdateByDict(areaDeleteByMainIdList, "IPKeepRecordUserAndAreaId");
                    }

                    if (userDeleteByMainIdList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_user.Instance.UpdateByDict(userDeleteByMainIdList, "IPKeepRecordUserAndAreaId");
                    }

                }
                else
                {
                    //清空所有用户及地区
                    DbOpe_crm_ipkeeprecord_userandarea.Instance.RemoveIPKeepRecordUserAndAreaDataById(updateIpKeepRecordApplyIn.Id);
                }
            }
            else
            {
                //清空所有用户及地区
                DbOpe_crm_ipkeeprecord_userandarea.Instance.RemoveIPKeepRecordUserAndAreaDataById(updateIpKeepRecordApplyIn.Id);
                if (updateIpKeepRecordApplyIn.IPKeepRecordUserAndArea is not null and { Count: > 0 })
                {
                    int addUserAndAreaSort = 0;
                    List<Db_crm_ipkeeprecord_userandarea> userAndAreaInsertList = new(updateIpKeepRecordApplyIn.IPKeepRecordUserAndArea.Count);
                    Db_crm_ipkeeprecord_userandarea userAndAreaTemp = default;
                    String userAndAreaInsertId = default;

                    List<Db_crm_ipkeeprecord_user> userInsertList = new();
                    Db_crm_ipkeeprecord_user userTemp = default;

                    List<Db_crm_ipkeeprecord_area> areaInsertList = new();
                    Db_crm_ipkeeprecord_area areaTemp = default;
                    int userSortNum, areaSortNum;

                    foreach (Update_IPKeepRecordUserAndArea addUserAndAreaItem in updateIpKeepRecordApplyIn.IPKeepRecordUserAndArea)
                    {
                        userSortNum = 0;
                        areaSortNum = 0;
                        //1.主表
                        userAndAreaInsertId = Guid.NewGuid().ToString();
                        userAndAreaTemp = new()
                        {
                            Id = userAndAreaInsertId,
                            IPKeepRecordId = updateIpKeepRecordApplyIn.Id,
                            Deleted = false,
                            SortNum = ++addUserAndAreaSort,
                            CreateUser = TokenModel.Instance.id,
                            CreateDate = DateTime.Now,
                        };
                        userAndAreaInsertList.Add(userAndAreaTemp);

                        //2.用户
                        foreach (GetByID_IPKeepRecordUserAndArea_UserId userItem in addUserAndAreaItem.UserList)
                        {
                            userTemp = new()
                            {
                                Id = Guid.NewGuid().ToString(),
                                IPKeepRecordUserAndAreaId = userAndAreaInsertId,
                                AccountType = updateIpKeepRecordApplyIn.AccountType,
                                UserId = userItem.UserId,
                                Deleted = false,
                                SortNum = ++userSortNum,
                                CreateUser = TokenModel.Instance.id,
                                CreateDate = DateTime.Now,
                            };

                            userListCheck.Add(userItem.UserId);
                            userInsertList.Add(userTemp);
                        }

                        //3.地区
                        foreach (GetByID_IPKeepRecordUserAndArea_AreaId areaItem in addUserAndAreaItem.AreaList)
                        {
                            areaTemp = new()
                            {
                                Id = Guid.NewGuid().ToString(),
                                IPKeepRecordUserAndAreaId = userAndAreaInsertId,
                                CountryId = LocalCache.LC_Address.ProvinceCache?.FirstOrDefault(cache => cache.Id == areaItem.AreaId)?.CountryID ?? -1,
                                AreaId = areaItem.AreaId,
                                Deleted = false,
                                SortNum = ++areaSortNum,
                                CreateUser = TokenModel.Instance.id,
                                CreateDate = DateTime.Now,
                            };
                            areaInsertList.Add(areaTemp);
                        }
                    }

                    if (userAndAreaInsertList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_userandarea.Instance.InsertQueue(userAndAreaInsertList);
                    }

                    if (areaInsertList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_area.Instance.InsertQueue(areaInsertList);
                    }

                    if (userInsertList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_ipkeeprecord_user.Instance.InsertQueue(userInsertList);
                    }
                }
            }

            //验证添加的用户 修改 2024年6月13日
            if (updateIpKeepRecordApplyIn.AccountType == EnumAccountType.TemporaryAccount)
            {
                var checkUser = DbOpe_crm_ipkeeprecord_user.Instance.CheckUser(userListCheck);
                if (checkUser is null)
                {
                    throw new ApiException("账号开通时间不一致，请分别申请!");
                }
            }

            //附件操作
            var attachFileList = DbOpe_crm_ipkeeprecord_attachfile.Instance.GetAttachFileByRecordId(updateIpKeepRecordApplyIn.Id);
            var attachFileIdList = attachFileList.Select(w => w.Id).ToList();
            if (updateIpKeepRecordApplyIn.IPKeepRecordAttachFile is null or { Count: 0 })
            {
                if (attachFileIdList is not null and { Count: > 0 })
                {
                    DbOpe_crm_ipkeeprecord_attachfile.Instance.DeleteAttachFileByIdList(attachFileIdList);
                }
            }
            else
            {
                var attachFileUpdateIdList = new List<string>();
                FormFileCollection formFileCollection = new();
                foreach (Update_IPKeepRecordAttachFile updateIpKeepRecordAttachFile in updateIpKeepRecordApplyIn.IPKeepRecordAttachFile)
                {
                    if (attachFileIdList.Contains(updateIpKeepRecordAttachFile.Id))
                    {
                        attachFileUpdateIdList.Add(updateIpKeepRecordAttachFile.Id);
                        continue;
                    }

                    if (updateIpKeepRecordAttachFile.File != null)
                    {
                        formFileCollection.Add(updateIpKeepRecordAttachFile.File);
                    }

                }

                if (formFileCollection.Count > 0)
                {
                    Util<DbOpe_crm_ipkeeprecord_attachfile, BM_AttachFiles> util = new(DbOpe_crm_ipkeeprecord_attachfile.Instance);
                    bool uploadResult = util.UploadFiles(updateIpKeepRecordApplyIn.Id, formFileCollection, AttachEnumOption.ContractIpKeepRecord);
                    if (!uploadResult)
                    {
                        throw new ApiException("文件上传异常!");
                    }
                }
                var attachFileDeleteIdList = attachFileIdList.Except(attachFileUpdateIdList).ToList();
                DbOpe_crm_ipkeeprecord_attachfile.Instance.DeleteAttachFileByIdList(attachFileDeleteIdList);
            }

            if (updateIpKeepRecordApplyIn.State == 1)
            {
                Db_crm_ipkeeprecordapply applyEntity = new()
                {
                    Id = Guid.NewGuid().ToString(),
                    IPKeepRecordId = updateIpKeepRecordApplyIn.Id,
                    IsNewBuild = false,
                    ApplicantId = TokenModel.Instance.id,
                    ApplicantDate = DateTime.Now,
                    Remark = updateIpKeepRecordApplyIn.Remark,
                    State = EnumRecordState.Submit,
                    Deleted = false,
                    CreateUser = TokenModel.Instance.id,
                    CreateDate = DateTime.Now,
                    Version = Guid.NewGuid().ToString(),
                };
                DbOpe_crm_ipkeeprecordapply.Instance.InsertQueue(applyEntity);
            }

            int execCount = DbOpe_crm_ipkeeprecord.Instance.SaveQueues();
            if (execCount > 0)
            {
                result.Data = 1;
            }

            string dataState = EnumRecordState.Submit.GetEnumDescription();

            if (contract != null)
            {
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_ipkeeprecord, Db_crm_contract>("IP备案申请审批流程", updateIpKeepRecordApplyIn.Id, recordData, contract, updateIpKeepRecordApplyIn.Remark, dataState, "新建");
            }
            else
            {
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_ipkeeprecord>("IP备案申请审批流程", updateIpKeepRecordApplyIn.Id, recordData, updateIpKeepRecordApplyIn.Remark, dataState, "新建");
            }

            return result;
        }

        /// <summary>
        /// 删除IP备案信息，当备案状态为通过、拒绝、作废、草稿时，可操作删除。验证数据权限。
        /// </summary>
        /// <param name="deleteIpKeepRecordIn"></param>
        /// <returns></returns>
        public DeleteIPKeepRecord_Out DeleteIPKeepRecord(DeleteIPKeepRecord_In deleteIpKeepRecordIn)
        {
            var result = new DeleteIPKeepRecord_Out { Data = 0 };
            var recordData = GetIpKeepRecordDataById(deleteIpKeepRecordIn.Id);
            if (recordData == null)
            {
                throw new ApiException("Id所对应的IP备案申请信息不存在!");
            }

            //申请
            if (deleteIpKeepRecordIn.TableLabel == EnumRecordTableLabel.Apply)
            {
                if (recordData.State is EnumRecordState.Cancel or EnumRecordState.Draft or EnumRecordState.Expire)
                {
                    result = DbOpe_crm_ipkeeprecord.Instance.DeleteIPKeepRecord(deleteIpKeepRecordIn.Id, true);
                }
                else
                {
                    throw new ApiException("IP备案申请信息非草稿、过期或作废状态下禁止删除!");
                }
            }

            //审核
            if (deleteIpKeepRecordIn.TableLabel == EnumRecordTableLabel.Review)
            {
                if (recordData.State is EnumRecordState.Cancel or EnumRecordState.Expire)
                {
                    result = DbOpe_crm_ipkeeprecord.Instance.DeleteIPKeepRecord(deleteIpKeepRecordIn.Id, true);
                }
                else
                {
                    throw new ApiException("IP备案申请信息非过期或作废状态下禁止删除!");
                }
            }

            return result;
        }

        /// <summary>
        /// 撤销IP备案申请信息，当备案状态为通过、拒绝、作废时，可操作返回到上一个状态【待审核】。验证数据权限
        /// </summary>
        /// <param name="recordApplyIn"></param>
        /// <returns></returns>
        public RevokeIPKeepRecordApply_Out RevokeIPKeepRecordApply(RevokeIPKeepRecordApply_In recordApplyIn)
        {
            var result = new RevokeIPKeepRecordApply_Out { Data = 0 };
            var recordData = GetIpKeepRecordDataById(recordApplyIn.Id);
            if (recordData == null)
            {
                throw new ApiException("Id所对应的IP备案申请信息不存在!");
            }

            //申请界面
            if (recordApplyIn.TableLabel == EnumRecordTableLabel.Apply)
            {
                if (recordData.State is EnumRecordState.Submit)
                {
                    result = ModifyIpKeepRecordStateById(recordApplyIn.Id, EnumRecordState.Draft, true) as RevokeIPKeepRecordApply_Out;
                }
                else
                {
                    throw new ApiException("IP备案申请信息非提交状态下禁止撤销!");
                }
            }

            //审核界面
            else if (recordApplyIn.TableLabel == EnumRecordTableLabel.Review)
            {
                if (recordData.State is EnumRecordState.Pass or EnumRecordState.Refuse)
                {
                    //如果是通过状态的撤销,需要取消定时任务,且调用GTIS接口取消用户
                    if (recordData.State is EnumRecordState.Pass)
                    {
                        //// 取消定时任务
                        //string jobName = $"{IPKeepRecordTaskPrefix}{recordApplyIn.Id}";
                        //CronUtil.StopJob(jobName);

                        // 调用GTIS撤销
                        DbOpe_crm_ipkeeprecord.Instance.CancelGtisIPKeepRecord(recordData);
                    }
                    result = ModifyIpKeepRecordStateById(recordApplyIn.Id, EnumRecordState.Submit, true) as RevokeIPKeepRecordApply_Out;
                }
                else
                {
                    throw new ApiException("IP备案申请信息非通过、拒绝状态下禁止撤销!");
                }
            }

            //撤销已发送的消息
            string dataState = recordData.State.GetEnumDescription();
            Db_crm_ipkeeprecord dataObject = DbOpe_crm_ipkeeprecord.Instance.GetDataById(recordData.Id);
            BLL_WorkFlow.Instance.CancelWorkflowPending("IP备案申请审批流程", recordData.Id, dataState, dataObject);

            return result;
        }

        /// <summary>
        /// 作废IP备案信息，当备案状态为通过、待审核、拒绝时，可对申请备案信息进行作废处理
        /// </summary>
        /// <param name="voidIpKeepRecordApplyIn"></param>
        /// <returns></returns>
        public VoidIPKeepRecordApply_Out VoidIPKeepRecordApply(VoidIPKeepRecordApply_In voidIpKeepRecordApplyIn)
        {
            var result = new VoidIPKeepRecordApply_Out { Data = 0 };
            var recordData = GetIpKeepRecordDataById(voidIpKeepRecordApplyIn.Id);
            if (recordData == null)
            {
                throw new ApiException("Id所对应的IP备案申请信息不存在!");
            }

            //申请
            if (voidIpKeepRecordApplyIn.TableLabel == EnumRecordTableLabel.Apply)
            {
                if (recordData.State is EnumRecordState.Pass or EnumRecordState.Submit or EnumRecordState.Refuse)
                {
                    //如果是通过状态的作废,需要取消定时任务,且调用GTIS接口取消用户
                    if (recordData.State is EnumRecordState.Pass)
                    {
                        //// 取消定时任务
                        //string jobName = $"{IPKeepRecordTaskPrefix}{voidIpKeepRecordApplyIn.Id}";
                        //CronUtil.StopJob(jobName);

                        // 调用GTIS撤销
                        DbOpe_crm_ipkeeprecord.Instance.CancelGtisIPKeepRecord(recordData);
                    }
                    result = ModifyIpKeepRecordStateById(voidIpKeepRecordApplyIn.Id, EnumRecordState.Cancel, true) as VoidIPKeepRecordApply_Out;

                }
                else
                {
                    throw new ApiException("IP备案申请信息非通过、待审核及拒绝状态下禁止修改!");
                }
            }

            //审核
            else if (voidIpKeepRecordApplyIn.TableLabel == EnumRecordTableLabel.Review)
            {
                if (recordData.State is EnumRecordState.Pass or EnumRecordState.Submit or EnumRecordState.Refuse)
                {
                    //如果是通过状态的作废,需要取消定时任务,且调用GTIS接口取消用户
                    if (recordData.State is EnumRecordState.Pass)
                    {
                        //// 取消定时任务
                        //string jobName = $"{IPKeepRecordTaskPrefix}{voidIpKeepRecordApplyIn.Id}";
                        //CronUtil.StopJob(jobName);

                        // 调用GTIS撤销
                        DbOpe_crm_ipkeeprecord.Instance.CancelGtisIPKeepRecord(recordData);
                    }
                    result = ModifyIpKeepRecordStateById(voidIpKeepRecordApplyIn.Id, EnumRecordState.Cancel, true) as VoidIPKeepRecordApply_Out;
                }
                else
                {
                    throw new ApiException("IP备案申请信息非通过、待审核及拒绝状态下禁止修改!");
                }
            }

            //撤销已发送的消息
            BLL_WorkFlow.Instance.VoidWorkflowPending("IP备案申请审批流程", voidIpKeepRecordApplyIn.Id, "作废");

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="iPKeepRecordId"></param>
        /// <returns></returns>
        public Db_crm_ipkeeprecord GetIpKeepRecordDataById(string iPKeepRecordId)
        {
            return DbOpe_crm_ipkeeprecord.Instance.GetDataById(iPKeepRecordId);
        }

        /// <summary>
        /// 修改IP备案信息状态
        /// </summary>
        /// <param name="iPKeepRecordId">ip备案主键</param>
        /// <param name="modifyState">需要修改成的状态</param>
        /// <param name="canSyncApplyState">是否在最新的审核表中同步状态</param>
        /// <returns></returns>
        public IPKeepRecordBase_Out ModifyIpKeepRecordStateById(string iPKeepRecordId, EnumRecordState modifyState, bool canSyncApplyState = false)
        {
            return DbOpe_crm_ipkeeprecord.Instance.ModifyIpKeepRecordStateById(iPKeepRecordId, modifyState, canSyncApplyState);
        }

        /// <summary>
        /// 审核IP备案申请信息。
        /// </summary>
        /// <param name="auditIpKeepRecordApplyIn"></param>
        /// <returns></returns>
        public AuditIPKeepRecordApply_Out AuditIPKeepRecordApply(AuditIPKeepRecordApply_In auditIpKeepRecordApplyIn)
        {
            var combinResult = DbOpe_crm_ipkeeprecordapply.Instance.AuditIPKeepRecordApply(auditIpKeepRecordApplyIn);
            if (auditIpKeepRecordApplyIn.State == EnumRecordState.Pass && combinResult.result.Data > 0)
            {
                AddTimingRefreshIPKeepRecordTask(combinResult.dbData);
            }

            string dataState = EnumRecordState.Submit.GetEnumDescription();
            Db_crm_ipkeeprecordapply apply = DbOpe_crm_ipkeeprecordapply.Instance.GetDataById(auditIpKeepRecordApplyIn.Id);
            Db_crm_ipkeeprecord ipkeeprecord = DbOpe_crm_ipkeeprecord.Instance.GetDataById(apply.IPKeepRecordId);
            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(ipkeeprecord.ContractId);
            if (contract != null)
            {
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_ipkeeprecord, Db_crm_contract>("IP备案申请审批流程", ipkeeprecord.Id, ipkeeprecord, contract, ipkeeprecord.Remark, dataState, "新建");
            }
            else
            {
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_ipkeeprecord>("IP备案申请审批流程", ipkeeprecord.Id, ipkeeprecord, ipkeeprecord.Remark, dataState, "新建");
            }


            return combinResult.result;
        }

        /// <summary>
        /// 添加ip备案过期的定时任务
        /// </summary>
        /// <param name="ipkeeprecord"></param>
        public void AddTimingRefreshIPKeepRecordTask(Db_crm_ipkeeprecord ipkeeprecord)
        {
            if (ipkeeprecord.ExpirationDate.Value < DateTime.Now)
            {
                DbOpe_crm_ipkeeprecord.Instance.ExpireIPKeepRecord(ipkeeprecord);    //状态修改为过期
                return;
            }
            string cronExpression = ScheduledTaskUtil.GetCronByDateTime(ipkeeprecord.ExpirationDate.Value);
            string jobName = $"{IPKeepRecordTaskPrefix}{ipkeeprecord.Id}";
            
            if (ScheduledTaskUtil.CheckCronExpression(cronExpression))
            {
                SafeCronUtil.AddCronJob(jobName, () =>
                {
                    DbOpe_crm_ipkeeprecord.Instance.ExpireIPKeepRecord(ipkeeprecord);    //状态修改为过期
                    Debug.WriteLine($"{jobName}任务执行完成！ \t {DateTime.Now}");
                }, cronExpression);
            }
        }

        /// <summary>
        /// 获取当前待审核的IP备案
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessageReminder> GetUNReadModeratedIPRecord()
        {
            return DbOpe_crm_ipkeeprecord.Instance.GetUNReadModeratedIPRecord();
        }

        /// <summary>
        /// 获取当前已经审核的IP备案
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessageReminder> GetUNReadAuditResultIPRecord()
        {
            return DbOpe_crm_ipkeeprecord.Instance.GetUNReadAuditResultIPRecord();
        }


    }
}