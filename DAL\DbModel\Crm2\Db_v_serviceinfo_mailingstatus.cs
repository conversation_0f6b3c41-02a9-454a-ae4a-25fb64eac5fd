﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;
using CRM2_API.Model.BLLModel.Enum;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_serviceinfo_mailingstatus")]
    public class Db_v_serviceinfo_mailingstatus
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Id {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public EnumMailingStatus mailedStatus {get;set;}

    }
}
