using CRM2_API.DAL.DbModelOpe.Crm2;
using System;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        /// <summary>
        /// 缓存初始化类，用于在应用启动时预加载缓存数据
        /// </summary>
        public class CacheInit
        {
            /// <summary>
            /// 初始化所有缓存
            /// </summary>
            public static void InitAllCache()
            {
                try
                {
                    // 初始化收款公司缓存
                    CollectingCompany.InitializeCache();
                    
                    // 初始化用户与组织机构缓存
                    UserWithOrg.InitializeCache();

                    // 初始化付款公司缓存
                    PayingCompany.InitializeCache();
                    
                    // 初始化排除词列表缓存
                    ExceptWords.InitializeCache();
                    
                    // 初始化黑名单用户缓存
                    BlacklistUsers.InitializeCache();
                    
                    LogUtil.AddLog("缓存初始化完成");
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"缓存初始化失败: {ex.Message}");
                }
            }

        }
    }
} 