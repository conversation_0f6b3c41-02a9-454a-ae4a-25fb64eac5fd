﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_tianyancha_log")]
    public class Db_crm_tianyancha_log
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:查询api日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? Indate { get; set; }

        /// <summary>
        /// Desc:用户id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string SysID { get; set; }

        /// <summary>
        /// Desc:查询类型 1-精确 2-模糊
        /// Default:
        /// Nullable:True
        /// </summary>           
        public short? SType { get; set; }

        /// <summary>
        /// Desc:搜索词
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Keyword { get; set; }

    }
}
