﻿using CRM2_API.Model.BLLModel.Enum;

namespace CRM2_API.Common.Cache
{

    public partial class RedisCache
    {
        public class LoginCode
        {
            /// <summary>
            /// 校验验证码
            /// </summary>
            /// <param name="key"></param>
            /// <returns></returns>
            public static string CheckLoginCode(string key)
            {
                return RedisHelper.Get(key).ToString();
            }

            /// <summary>
            /// 验证缓存中是否存在验证码
            /// </summary>
            /// <param name="key"></param>
            /// <returns></returns>
            public static bool CheckLoginCodeExist(string key)
            {
               return RedisHelper.Exists(key);
            }

            /// <summary>
            /// 存储验证码缓存
            /// </summary>
            /// <param name="phoneOrEmail"></param>
            /// <param name="code"></param>
            /// <param name="type"></param>
            public static void SaveVerifyCode(string phoneOrEmail, EnumLoginCode type, string code)
            {
                RedisHelper.Set(phoneOrEmail + type.ToString(), code, TimeSpan.FromMinutes(10));
            }

            /// <summary>
            /// 删除验证码缓存
            /// </summary>
            /// <param name="phoneOrEmail"></param>
            /// <param name="type"></param>
            public static void DeleteVerifyCode(string phoneOrEmail, EnumLoginCode type)
            {
                RedisHelper.Del(phoneOrEmail + type.ToString());
            }

        }

    }
}
