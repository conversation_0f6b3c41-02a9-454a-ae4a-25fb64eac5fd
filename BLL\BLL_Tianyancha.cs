﻿using CRM2_API.BLL.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModel.Gtis;
using CRM2_API.DAL.DbModelOpe.Crm2;
using System.Net;
using System.IO;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using CRM2_API.Common.AppSetting;
using System.Text.RegularExpressions;

namespace CRM2_API.BLL
{
    public class BLL_Tianyancha : BaseBLL<BLL_Tianyancha>
    {

        public string DealCompanys()
        {
            int total = 0;
            var companyList = DbOpe_crm_tianyancha_aa.Instance.Query_crm_tianyancha_aa(); //处理列表  isok=0
            for (int i = 0; i < companyList.Count; i++)
            {
                Db_crm_tianyancha_aa model = companyList[i];
                string cname = model.company;
                if (cname.Contains("*有限公司") || cname.Contains("X有限公司") || cname.Contains("x有限公司"))
                    cname = model.rzcompany;

                cname = cname.Replace('、', ' ').Replace('/', ' ').Replace(',', ' ').Replace('，', ' ').Replace(";", "").Replace("；", "");
                //cname = cname.Replace("(", " (").Replace(")", ") ").Replace("（", " （").Replace("）", "） ");
                cname = cname.Replace("公司", "公司 ");

                string[] tmp = cname.Split(' ');

                for (int n = 0; n < tmp.Length; n++)
                {
                    //cname = tmp[n].Trim().Replace("(", "").Replace(")", "").Replace("（", "").Replace("）", "");
                    cname = tmp[n].Trim();
                    if (cname == "")
                        continue;

                    try
                    {
                        List<Db_crm_tianyancha_info> infos = new List<Db_crm_tianyancha_info>();
                        string error_code = "";
                        QueryTianyancha(cname, ref infos, ref error_code);
                        if (infos.Count == 0)
                        {
                            model.isok = 4;
                            model.updatetime = DateTime.Now.ToString();
                            DbOpe_crm_tianyancha_aa.Instance.Update(model);
                        }
                        else
                        {

                            model.isok = 3;
                            model.updatetime = DateTime.Now.ToString();
                            DbOpe_crm_tianyancha_aa.Instance.Update(model);
                        }
                    }
                    catch (Exception ex)
                    {
                        model.isok = 4;
                        model.updatetime = DateTime.Now.ToString();
                        DbOpe_crm_tianyancha_aa.Instance.Update(model);
                    }

                    //Thread.Sleep(2000); //暂停10
                }
            }

            return "ok";
        }

        /// <summary>
        /// 名称搜索，返回20条。
        /// </summary>
        /// <param name="companyname"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public List<Db_crm_tianyancha_info> QueryTianyanchaList(string companyname, ref int total)
        {
            if (string.IsNullOrEmpty(companyname))
                throw new ApiException("输入内容不可为空！");

            //英文名称暂不提供查询
            var hasEN = ContainsNonChineseCharacters(companyname);
            if (hasEN)
                throw new ApiException("英文名称和字符暂不能查询，请输入对应中文名称！");

            //先查询crm库是否有记录
            string shortname = GetShortName(companyname);//输入词短描
            var infoList = DbOpe_crm_tianyancha_info.Instance.Query_crm_tianyancha_info(companyname, shortname, ref total);
            if (infoList.Count == 0)  //本地库不存在
            {
                //检查该用户使用次数
                if (CheckUsedAPI())
                    throw new ApiException("查询次数已到上限，暂不能查询！");

                //本地库没有记录，调用天眼查api （搜索方法）
                string api_token = AppSettings.TianyanchaAPI.Tianyancha_Token;
                string api_url = AppSettings.TianyanchaAPI.Tianyancha_QueryUrl + companyname;  //搜索方法url
                string resJson = Tianyancha_httpGet(api_url, api_token); //api返回结果

                if (!string.IsNullOrEmpty(resJson))
                {
                    #region //解析结果
                    JObject jsonObject = (JObject)JsonConvert.DeserializeObject(resJson); //解析结果
                    string error_code = jsonObject["error_code"].ToString(); //执行结果代码
                    if (error_code == "0")
                    {
                        JObject dataJson = (JObject)JsonConvert.DeserializeObject(jsonObject["result"].ToString());
                        List<JObject> items = dataJson["items"].ToObject<List<JObject>>();
                        for (int i = 0; i < items.Count; i++)
                        {
                            JObject itemone = (JObject)JsonConvert.DeserializeObject(items[i].ToString());
                            Db_crm_tianyancha_info model = new Db_crm_tianyancha_info();
                            model.Id = Guid.NewGuid().ToString();
                            model.Indate = DateTime.Now;
                            model.CompanyName = itemone["name"].ToString();
                            model.ShortName = GetShortName(itemone["name"].ToString());  // 处理后的短描
                            model.RegStatus = itemone["regStatus"].ToString();
                            model.Base = itemone["base"].ToString();
                            model.RegCapital = itemone["regCapital"].ToString();
                            model.MatchType = itemone["matchType"].ToString();
                            model.LegalPersonName = itemone["legalPersonName"].ToString();
                            model.EstiblishTime = Convert.ToDateTime(itemone["estiblishTime"].ToString()).ToString();
                            model.OrgNumber = itemone["orgNumber"].ToString();
                            model.Cid = itemone["id"].ToString();
                            model.RegNumber = itemone["regNumber"].ToString();
                            model.CreditCode = itemone["creditCode"].ToString();
                            model.LegalPersonType = itemone["type"].ToString() == "" ? (short)0 : short.Parse(itemone["type"].ToString());
                            model.CompanyOrgType = TypeToCompanyOrgType(itemone["companyType"].ToString());
                            DbOpe_crm_tianyancha_info.Instance.Insert(model);
                        }

                        //记录调用日志表  crm_tianyancha_log  
                        Db_crm_tianyancha_log logModel = new Db_crm_tianyancha_log();
                        logModel.Id = Guid.NewGuid().ToString();
                        logModel.Indate = DateTime.Now;
                        //logModel.SysID = "0000-0000-0000-0000"; //UserTokenInfo.id; //UserTokenInfo.id 登陆用户id
                        logModel.SysID = UserTokenInfo.id; //UserTokenInfo.id; //UserTokenInfo.id 登陆用户id
                        logModel.SType = 2;
                        logModel.Keyword = companyname;
                        DbOpe_crm_tianyancha_log.Instance.Insert(logModel);
                    }
                    #endregion
                }
            }

            return infoList;
        }

        /// <summary>
        /// 名称精确搜索（24.4.17 包括历史名称/去掉短描搜索）
        /// </summary>
        /// <param name="companyname"></param>
        /// <param name="infoList"></param>
        /// <param name="error_code"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public void QueryTianyancha(string companyname, ref List<Db_crm_tianyancha_info> infoList, ref string error_code)
        {
            infoList = new List<Db_crm_tianyancha_info>();
            if (string.IsNullOrEmpty(companyname))
            {
                throw new ApiException("输入内容不可为空！");
            }


            // //英文名称暂不提供查询
            // var hasEN = ContainsNonChineseCharacters(companyname);
            // if (hasEN)
            //     throw new ApiException("英文名称和字符暂不能查询，请输入对应中文名称！");
            //先查询crm库是否有记录
            //string shortname = GetShortName(companyname);//输入词短描
            int total = 0;
            infoList = DbOpe_crm_tianyancha_info.Instance.Query_crm_tianyancha_info(companyname, "", ref total);

            if (infoList.Count == 0)  //本地库不存在
            {
                //检查该用户使用次数  AppSettings.TianyanchaAPI.Tianyancha_MaxNum
                if (CheckUsedAPI())
                {
                    throw new ApiException("查询次数已到上限，暂不能查询！");
                }

                //本地库没有记录，调用天眼查api
                //天津新东方培训学校有限公司    中航重机股份有限公司
                string api_token = AppSettings.TianyanchaAPI.Tianyancha_Token;
                string api_url = AppSettings.TianyanchaAPI.Tianyancha_BasicInfoUrl + companyname;
                string resJson = Tianyancha_httpGet(api_url, api_token); //api返回结果

                if (!string.IsNullOrEmpty(resJson))
                {
                    //{"result":null,"reason":"无数据","error_code":300000}
                    #region 返回代码说明
                    /* 300000  无数据
                       300001  请求失败
                       300002  账号失效
                       300003  账号过期
                       300004  访问频率过快
                       300005  无权限访问此api
                       300006  余额不足
                       300007  剩余次数不足
                       300008  缺少必要参数
                       300009  账号信息有误
                       300010  URL不存在
                       300011  此IP无权限访问此api
                       300012  报告生成中  */
                    #endregion

                    #region //解析结果
                    JObject jsonObject = (JObject)JsonConvert.DeserializeObject(resJson); //解析结果
                    error_code = jsonObject["error_code"].ToString(); //执行结果代码
                    if (error_code == "0")
                    {
                        JObject dataJson = (JObject)JsonConvert.DeserializeObject(jsonObject["result"].ToString());
                        //添加或更新本地库?
                        Db_crm_tianyancha_info model = new Db_crm_tianyancha_info();
                        #region 值处理
                        model.ActualCapital = dataJson["actualCapital"].ToString();
                        model.ActualCapitalCurrency = dataJson["actualCapitalCurrency"].ToString();
                        model.Alias = dataJson["alias"].ToString();
                        model.ApprovedTime = dataJson["approvedTime"].ToString() == "" ? "" : GetDateTime(dataJson["approvedTime"].ToString()).ToString();
                        model.Base = dataJson["base"].ToString();
                        model.BondName = dataJson["bondName"].ToString();
                        model.BondNum = dataJson["bondNum"].ToString();
                        model.BondType = dataJson["bondType"].ToString();
                        model.BusinessScope = dataJson["businessScope"].ToString();
                        model.CancelDate = dataJson["cancelDate"].ToString() == "" ? "" : GetDateTime(dataJson["cancelDate"].ToString()).ToString();
                        model.CancelReason = dataJson["cancelReason"].ToString();
                        model.Category = dataJson["industryAll"]["category"].ToString() + "|" + dataJson["industryAll"]["categoryBig"].ToString() + "|" +
                                         dataJson["industryAll"]["categoryMiddle"].ToString() + "|" + dataJson["industryAll"]["categorySmall"].ToString(); //国民经济行业分类
                        model.Cid = dataJson["id"].ToString();
                        model.City = dataJson["city"].ToString();
                        model.CompanyName = dataJson["name"].ToString();
                        model.CompanyOrgType = dataJson["companyOrgType"].ToString();
                        model.CreditCode = dataJson["creditCode"].ToString();
                        model.District = dataJson["district"].ToString();
                        model.EstiblishTime = dataJson["estiblishTime"].ToString() == "" ? "" : GetDateTime(dataJson["estiblishTime"].ToString()).ToString();
                        model.FromTime = dataJson["fromTime"].ToString() == "" ? "" : GetDateTime(dataJson["fromTime"].ToString()).ToString();
                        List<string> hisnames = dataJson["historyNameList"].ToObject<List<string>>();
                        if (hisnames != null)
                        {
                            for (int i = 0; i < hisnames.Count; i++)
                            {
                                model.HistoryNames += hisnames[i] + "|";
                            }
                        }
                        model.Id = Guid.NewGuid().ToString();
                        model.Indate = DateTime.Now;
                        model.Industry = dataJson["industry"].ToString();
                        model.IsMicroEnt = dataJson["isMicroEnt"].ToString() == "" ? (short)0 : short.Parse(dataJson["isMicroEnt"].ToString());
                        model.LegalPersonName = dataJson["legalPersonName"].ToString();
                        model.LegalPersonType = dataJson["type"].ToString() == "" ? (short)0 : short.Parse(dataJson["type"].ToString());
                        model.OrgNumber = dataJson["orgNumber"].ToString();
                        model.PercentileScore = dataJson["percentileScore"].ToString() == "" ? 0 : int.Parse(dataJson["percentileScore"].ToString());
                        model.Property3 = dataJson["property3"].ToString();
                        model.RegCapital = dataJson["regCapital"].ToString();
                        model.RegCapitalCurrency = dataJson["regCapitalCurrency"].ToString();
                        model.RegInstitute = dataJson["regInstitute"].ToString();
                        model.RegLocation = dataJson["regLocation"].ToString();
                        model.RegNumber = dataJson["regNumber"].ToString();
                        model.RegStatus = dataJson["regStatus"].ToString();
                        model.RevokeDate = dataJson["revokeDate"].ToString() == "" ? "" : GetDateTime(dataJson["revokeDate"].ToString()).ToString();
                        model.RevokeReason = dataJson["revokeReason"].ToString();
                        model.ShortName = GetShortName(dataJson["name"].ToString());  // 处理后的短描
                        model.SocialStaffNum = dataJson["socialStaffNum"].ToString() == "" ? 0 : int.Parse(dataJson["socialStaffNum"].ToString());
                        model.StaffNumRange = dataJson["staffNumRange"].ToString();
                        model.Tags = dataJson["tags"].ToString();
                        model.TaxNumber = dataJson["taxNumber"].ToString();
                        model.UsedBondName = dataJson["usedBondName"].ToString();
                        model.UpdateTimes = dataJson["updateTimes"].ToString() == "" ? "" : GetDateTime(dataJson["updateTimes"].ToString()).ToString();
                        model.ToTime = dataJson["toTime"].ToString() == "" ? "" : GetDateTime(dataJson["toTime"].ToString()).ToString();
                        #endregion
                        DbOpe_crm_tianyancha_info.Instance.Insert(model);

                        //记录调用日志表  crm_tianyancha_log                        
                        Db_crm_tianyancha_log logModel = new Db_crm_tianyancha_log();
                        logModel.Id = Guid.NewGuid().ToString();
                        logModel.Indate = DateTime.Now;
                        //logModel.SysID = "0000-0000-0000-0000"; //UserTokenInfo.id; //UserTokenInfo.id 登陆用户id
                        logModel.SysID = UserTokenInfo.id; //UserTokenInfo.id; //UserTokenInfo.id 登陆用户id
                        logModel.SType = 1;
                        logModel.Keyword = companyname;
                        DbOpe_crm_tianyancha_log.Instance.Insert(logModel);


                        infoList.Add(model);    //加入返回列表
                    }
                    #endregion
                }
            }
        }

        /// <summary>
        /// 名称精确搜索（24.10.21 去掉全英文验证）
        /// </summary>
        /// <param name="companyname"></param>
        /// <param name="checkEnStr"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public List<Db_crm_tianyancha_info> QueryTianyanchaWithEnStrCheck(string companyname, bool checkEnStr, ref int total)
        {
            if (string.IsNullOrEmpty(companyname))
                throw new ApiException("输入内容不可为空！");
            if (checkEnStr)
            {
                //英文名称暂不提供查询
                var hasEN = ContainsNonChineseCharacters(companyname);
                if (hasEN)
                    throw new ApiException("英文名称和字符暂不能查询，请输入对应中文名称！");

            }

            //先查询crm库是否有记录
            //string shortname = GetShortName(companyname);//输入词短描
            var infoList = DbOpe_crm_tianyancha_info.Instance.Query_crm_tianyancha_info(companyname, "", ref total);

            if (infoList.Count == 0)  //本地库不存在 & 专票/普票
            {
                //检查该用户使用次数  AppSettings.TianyanchaAPI.Tianyancha_MaxNum
                if (CheckUsedAPI())
                    throw new ApiException("查询次数已到上限，暂不能查询！");

                //本地库没有记录，调用天眼查api
                //天津新东方培训学校有限公司    中航重机股份有限公司
                string api_token = AppSettings.TianyanchaAPI.Tianyancha_Token;
                string api_url = AppSettings.TianyanchaAPI.Tianyancha_BasicInfoUrl + companyname;
                string resJson = Tianyancha_httpGet(api_url, api_token); //api返回结果

                if (!string.IsNullOrEmpty(resJson))
                {
                    //{"result":null,"reason":"无数据","error_code":300000}
                    #region 返回代码说明
                    /* 300000  无数据
                       300001  请求失败
                       300002  账号失效
                       300003  账号过期
                       300004  访问频率过快
                       300005  无权限访问此api
                       300006  余额不足
                       300007  剩余次数不足
                       300008  缺少必要参数
                       300009  账号信息有误
                       300010  URL不存在
                       300011  此IP无权限访问此api
                       300012  报告生成中  */
                    #endregion

                    #region //解析结果
                    JObject jsonObject = (JObject)JsonConvert.DeserializeObject(resJson); //解析结果
                    string error_code = jsonObject["error_code"].ToString(); //执行结果代码
                    if (error_code == "0")
                    {

                        JObject dataJson = (JObject)JsonConvert.DeserializeObject(jsonObject["result"].ToString());
                        if (dataJson.ContainsKey("creditCode") && !StringUtil.IsNullOrEmpty(dataJson["creditCode"].ToString()))
                        {


                            //添加或更新本地库?
                            Db_crm_tianyancha_info model = new Db_crm_tianyancha_info();
                            #region 值处理
                            model.ActualCapital = dataJson["actualCapital"].ToString();
                            model.ActualCapitalCurrency = dataJson["actualCapitalCurrency"].ToString();
                            model.Alias = dataJson["alias"].ToString();
                            model.ApprovedTime = dataJson["approvedTime"].ToString() == "" ? "" : GetDateTime(dataJson["approvedTime"].ToString()).ToString();
                            model.Base = dataJson["base"].ToString();
                            model.BondName = dataJson["bondName"].ToString();
                            model.BondNum = dataJson["bondNum"].ToString();
                            model.BondType = dataJson["bondType"].ToString();
                            model.BusinessScope = dataJson["businessScope"].ToString();
                            model.CancelDate = dataJson["cancelDate"].ToString() == "" ? "" : GetDateTime(dataJson["cancelDate"].ToString()).ToString();
                            model.CancelReason = dataJson["cancelReason"].ToString();
                            model.Category = dataJson["industryAll"]["category"].ToString() + "|" + dataJson["industryAll"]["categoryBig"].ToString() + "|" +
                                             dataJson["industryAll"]["categoryMiddle"].ToString() + "|" + dataJson["industryAll"]["categorySmall"].ToString(); //国民经济行业分类
                            model.Cid = dataJson["id"].ToString();
                            model.City = dataJson["city"].ToString();
                            model.CompanyName = dataJson["name"].ToString();
                            model.CompanyOrgType = dataJson["companyOrgType"].ToString();
                            model.CreditCode = dataJson["creditCode"].ToString();
                            model.District = dataJson["district"].ToString();
                            model.EstiblishTime = dataJson["estiblishTime"].ToString() == "" ? "" : GetDateTime(dataJson["estiblishTime"].ToString()).ToString();
                            model.FromTime = dataJson["fromTime"].ToString() == "" ? "" : GetDateTime(dataJson["fromTime"].ToString()).ToString();
                            List<string> hisnames = dataJson["historyNameList"].ToObject<List<string>>();
                            if (hisnames != null)
                            {
                                for (int i = 0; i < hisnames.Count; i++)
                                {
                                    model.HistoryNames += hisnames[i] + "|";
                                }
                            }
                            model.Id = Guid.NewGuid().ToString();
                            model.Indate = DateTime.Now;
                            model.Industry = dataJson["industry"].ToString();
                            model.IsMicroEnt = dataJson["isMicroEnt"].ToString() == "" ? (short)0 : short.Parse(dataJson["isMicroEnt"].ToString());
                            model.LegalPersonName = dataJson["legalPersonName"].ToString();
                            model.LegalPersonType = dataJson["type"].ToString() == "" ? (short)0 : short.Parse(dataJson["type"].ToString());
                            model.OrgNumber = dataJson["orgNumber"].ToString();
                            model.PercentileScore = dataJson["percentileScore"].ToString() == "" ? 0 : int.Parse(dataJson["percentileScore"].ToString());
                            model.Property3 = dataJson["property3"].ToString();
                            model.RegCapital = dataJson["regCapital"].ToString();
                            model.RegCapitalCurrency = dataJson["regCapitalCurrency"].ToString();
                            model.RegInstitute = dataJson["regInstitute"].ToString();
                            model.RegLocation = dataJson["regLocation"].ToString();
                            model.RegNumber = dataJson["regNumber"].ToString();
                            model.RegStatus = dataJson["regStatus"].ToString();
                            model.RevokeDate = dataJson["revokeDate"].ToString() == "" ? "" : GetDateTime(dataJson["revokeDate"].ToString()).ToString();
                            model.RevokeReason = dataJson["revokeReason"].ToString();
                            model.ShortName = GetShortName(dataJson["name"].ToString());  // 处理后的短描
                            model.SocialStaffNum = dataJson["socialStaffNum"].ToString() == "" ? 0 : int.Parse(dataJson["socialStaffNum"].ToString());
                            model.StaffNumRange = dataJson["staffNumRange"].ToString();
                            model.Tags = dataJson["tags"].ToString();
                            model.TaxNumber = dataJson["taxNumber"].ToString();
                            model.UsedBondName = dataJson["usedBondName"].ToString();
                            model.UpdateTimes = dataJson["updateTimes"].ToString() == "" ? "" : GetDateTime(dataJson["updateTimes"].ToString()).ToString();
                            model.ToTime = dataJson["toTime"].ToString() == "" ? "" : GetDateTime(dataJson["toTime"].ToString()).ToString();
                            #endregion
                            DbOpe_crm_tianyancha_info.Instance.Insert(model);

                            //记录调用日志表  crm_tianyancha_log                        
                            Db_crm_tianyancha_log logModel = new Db_crm_tianyancha_log();
                            logModel.Id = Guid.NewGuid().ToString();
                            logModel.Indate = DateTime.Now;
                            //logModel.SysID = "0000-0000-0000-0000"; //UserTokenInfo.id; //UserTokenInfo.id 登陆用户id
                            logModel.SysID = UserTokenInfo.id; //UserTokenInfo.id; //UserTokenInfo.id 登陆用户id
                            logModel.SType = 1;
                            logModel.Keyword = companyname;
                            DbOpe_crm_tianyancha_log.Instance.Insert(logModel);


                            infoList.Add(model);    //加入返回列表
                        }
                    }
                    #endregion
                }
            }

            return infoList;
        }


        /// <summary>
        /// 是否不是注册的公司
        /// </summary>
        /// <param name="companyname"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public bool QueryNotIn(string companyname)
        {
            if (string.IsNullOrEmpty(companyname))
                throw new ApiException("输入内容不可为空！");

            //处理英文括号问题（转为中文括号）
            companyname = companyname.Replace("(", "（").Replace(")", "）");

            //英文名称暂不提供查询
            var hasEN = ContainsNonChineseCharacters(companyname);
            if (hasEN)
                return true;

            //先查询crm库是否有记录
            int total = 0;
            var infoList = DbOpe_crm_tianyancha_info.Instance.Query_crm_tianyancha_info(companyname, "", ref total);

            if (infoList.Count == 0)  //本地库不存在
            {
                //检查该用户使用次数  AppSettings.TianyanchaAPI.Tianyancha_MaxNum
                if (CheckUsedAPI())
                    throw new ApiException("查询次数已到上限，暂不能查询！");

                //本地库没有记录，调用天眼查api
                //天津新东方培训学校有限公司    中航重机股份有限公司
                string api_token = AppSettings.TianyanchaAPI.Tianyancha_Token;
                string api_url = AppSettings.TianyanchaAPI.Tianyancha_BasicInfoUrl + companyname;
                string resJson = Tianyancha_httpGet(api_url, api_token); //api返回结果

                if (!string.IsNullOrEmpty(resJson))
                {
                    //{"result":null,"reason":"无数据","error_code":300000}
                    #region 返回代码说明
                    /* 300000  无数据
                       300001  请求失败
                       300002  账号失效
                       300003  账号过期
                       300004  访问频率过快
                       300005  无权限访问此api
                       300006  余额不足
                       300007  剩余次数不足
                       300008  缺少必要参数
                       300009  账号信息有误
                       300010  URL不存在
                       300011  此IP无权限访问此api
                       300012  报告生成中  */
                    #endregion

                    #region //解析结果
                    JObject jsonObject = (JObject)JsonConvert.DeserializeObject(resJson); //解析结果
                    string error_code = jsonObject["error_code"].ToString(); //执行结果代码
                    if (error_code == "0")
                    {
                        JObject dataJson = (JObject)JsonConvert.DeserializeObject(jsonObject["result"].ToString());
                        //添加或更新本地库?
                        Db_crm_tianyancha_info model = new Db_crm_tianyancha_info();
                        #region 值处理
                        model.ActualCapital = dataJson["actualCapital"].ToString();
                        model.ActualCapitalCurrency = dataJson["actualCapitalCurrency"].ToString();
                        model.Alias = dataJson["alias"].ToString();
                        model.ApprovedTime = dataJson["approvedTime"].ToString() == "" ? "" : GetDateTime(dataJson["approvedTime"].ToString()).ToString();
                        model.Base = dataJson["base"].ToString();
                        model.BondName = dataJson["bondName"].ToString();
                        model.BondNum = dataJson["bondNum"].ToString();
                        model.BondType = dataJson["bondType"].ToString();
                        model.BusinessScope = dataJson["businessScope"].ToString();
                        model.CancelDate = dataJson["cancelDate"].ToString() == "" ? "" : GetDateTime(dataJson["cancelDate"].ToString()).ToString();
                        model.CancelReason = dataJson["cancelReason"].ToString();
                        model.Category = dataJson["industryAll"]["category"].ToString() + "|" + dataJson["industryAll"]["categoryBig"].ToString() + "|" +
                                         dataJson["industryAll"]["categoryMiddle"].ToString() + "|" + dataJson["industryAll"]["categorySmall"].ToString(); //国民经济行业分类
                        model.Cid = dataJson["id"].ToString();
                        model.City = dataJson["city"].ToString();
                        model.CompanyName = dataJson["name"].ToString();
                        model.CompanyOrgType = dataJson["companyOrgType"].ToString();
                        model.CreditCode = dataJson["creditCode"].ToString();
                        model.District = dataJson["district"].ToString();
                        model.EstiblishTime = dataJson["estiblishTime"].ToString() == "" ? "" : GetDateTime(dataJson["estiblishTime"].ToString()).ToString();
                        model.FromTime = dataJson["fromTime"].ToString() == "" ? "" : GetDateTime(dataJson["fromTime"].ToString()).ToString();
                        List<string> hisnames = dataJson["historyNameList"].ToObject<List<string>>();
                        if (hisnames != null)
                        {
                            for (int i = 0; i < hisnames.Count; i++)
                            {
                                model.HistoryNames += hisnames[i] + "|";
                            }
                        }
                        model.Id = Guid.NewGuid().ToString();
                        model.Indate = DateTime.Now;
                        model.Industry = dataJson["industry"].ToString();
                        model.IsMicroEnt = short.Parse(dataJson["isMicroEnt"].ToString());
                        model.LegalPersonName = dataJson["legalPersonName"].ToString();
                        model.LegalPersonType = short.Parse(dataJson["type"].ToString());
                        model.OrgNumber = dataJson["orgNumber"].ToString();
                        model.PercentileScore = dataJson["approvedTime"].ToString() == "" ? 0 : int.Parse(dataJson["percentileScore"].ToString());
                        model.Property3 = dataJson["property3"].ToString();
                        model.RegCapital = dataJson["regCapital"].ToString();
                        model.RegCapitalCurrency = dataJson["regCapitalCurrency"].ToString();
                        model.RegInstitute = dataJson["regInstitute"].ToString();
                        model.RegLocation = dataJson["regLocation"].ToString();
                        model.RegNumber = dataJson["regNumber"].ToString();
                        model.RegStatus = dataJson["regStatus"].ToString();
                        model.RevokeDate = dataJson["revokeDate"].ToString() == "" ? "" : GetDateTime(dataJson["revokeDate"].ToString()).ToString();
                        model.RevokeReason = dataJson["revokeReason"].ToString();
                        model.ShortName = GetShortName(dataJson["name"].ToString());  // 处理后的短描
                        int tempV;
                        int.TryParse(dataJson["socialStaffNum"].ToString(), out tempV);
                        model.SocialStaffNum = tempV;
                        model.StaffNumRange = dataJson["staffNumRange"].ToString();
                        model.Tags = dataJson["tags"].ToString();
                        model.TaxNumber = dataJson["taxNumber"].ToString();
                        model.UsedBondName = dataJson["usedBondName"].ToString();
                        model.UpdateTimes = dataJson["updateTimes"].ToString() == "" ? "" : GetDateTime(dataJson["updateTimes"].ToString()).ToString();
                        model.ToTime = dataJson["toTime"].ToString() == "" ? "" : GetDateTime(dataJson["toTime"].ToString()).ToString();
                        #endregion
                        DbOpe_crm_tianyancha_info.Instance.Insert(model);

                        //记录调用日志表  crm_tianyancha_log                        
                        Db_crm_tianyancha_log logModel = new Db_crm_tianyancha_log();
                        logModel.Id = Guid.NewGuid().ToString();
                        logModel.Indate = DateTime.Now;
                        //logModel.SysID = "0000-0000-0000-0000"; //UserTokenInfo.id; //UserTokenInfo.id 登陆用户id
                        logModel.SysID = UserTokenInfo.id; //UserTokenInfo.id; //UserTokenInfo.id 登陆用户id
                        logModel.SType = 1;
                        logModel.Keyword = companyname;
                        DbOpe_crm_tianyancha_log.Instance.Insert(logModel);


                        infoList.Add(model);    //加入返回列表
                    }
                    else
                    {
                        if (error_code == "300000")
                        {
                            return true;
                        }
                        else
                        {
                            throw new ApiException("查询API错误（error_code=" + error_code + "）");
                        }

                    }
                    #endregion
                }
            }

            return false;
        }


        /// <summary>
        /// 请求天眼查api
        /// </summary>
        /// <param name="url"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        private string Tianyancha_httpGet(string url, string token)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            // set header
            WebHeaderCollection headers = new WebHeaderCollection();
            headers.Add("Authorization", token);
            request.UserAgent = null;
            request.Headers = headers;
            request.Method = "GET";

            // response deal
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            var httpStatusCode = (int)response.StatusCode;
            //Console.WriteLine("返回码为 {0}", httpStatusCode);
            if (httpStatusCode == 200)
            {
                Stream myResponseStream = response.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(myResponseStream, Encoding.GetEncoding("utf-8"));
                string retString = myStreamReader.ReadToEnd();
                myStreamReader.Close();
                myResponseStream.Close();
                return retString;
            }
            else
            {   // todo 可以通过返回码判断处理
                //Console.WriteLine("未返回数据 {0}", httpStatusCode);
                throw new ApiException("未返回数据，Code=" + httpStatusCode);
            }
        }

        /// <summary>
        /// 13位时间戳转 日期格式 1652338858008 -> 2022-05-12 03:08:58
        /// </summary>
        /// <param name="timestr"></param>
        /// <returns></returns>
        private DateTime GetDateTime(string timestr)
        {
            long timestamp = long.Parse(timestr);
            long begtime = timestamp * 10000;
            DateTime dt_1970 = new DateTime(1970, 1, 1, 8, 8, 0);
            long tricks_1970 = dt_1970.Ticks;           //1970年1月1日刻度
            long time_tricks = tricks_1970 + begtime;   //日志日期刻度
            DateTime dt = new DateTime(time_tricks);    //共化为DateTime
            return dt;
        }


        private string GetKeywordDel(string keyword)
        {
            keyword = keyword.Replace(":", "");
            keyword = keyword.Replace(" ,", "");
            keyword = keyword.Replace(",", "");
            keyword = keyword.Replace("，", "");
            keyword = keyword.Replace("（", "");
            keyword = keyword.Replace("）", "");
            keyword = keyword.Replace(".", "");
            keyword = keyword.Replace("\"", "");
            keyword = keyword.Replace("(", "");
            keyword = keyword.Replace(")", "");
            keyword = keyword.Replace("& ", "");
            keyword = keyword.Replace(" &", "");
            keyword = keyword.Replace(" & ", "");
            keyword = keyword.Replace("\\", "");
            keyword = keyword.Replace("-", "");
            keyword = keyword.Replace(";", "");
            keyword = keyword.Replace("\"", "");

            keyword = new System.Text.RegularExpressions.Regex("[\\s]+").Replace(keyword, " ");
            keyword = keyword.Trim();

            return keyword;
        }

        /// <summary>
        /// 形成公司短描
        /// </summary>
        /// <param name="companyname"></param>
        /// <returns></returns>
        private string GetShortName(string companyname)
        {
            companyname = GetKeywordDel(companyname);

            var nameList = DbOpe_crm_tianyancha_othernames.Instance.Query_crm_tianyancha_othernames();
            if (nameList != null)
            {
                for (int i = 0; i < nameList.Count; i++)
                {
                    Db_crm_tianyancha_othernames model = nameList[i];
                    companyname = companyname.Replace(model.Names, "");
                }
            }
            return companyname;
        }

        /// <summary>
        /// 判断是否包含非中文字符
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private bool ContainsNonChineseCharacters(string input)
        {
            // 正则表达式模式，用于匹配非中文字符（不含全角字符）
            // [\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\u4e00-\u9fa5] 涵盖了基础汉字、CJK扩展汉字、全角标点符号等
            // [^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\u4e00-\u9fa5] 表示上述范围外的字符
            // 注意：这里\u4e00-\u9fff被重复列出是为了清晰展示范围，实际可以合并或简化
            // 但由于\u4e00-\u9fa5已经包含了\u4e00-\u9fff，[\u3000-\u303f\uff00-\uffef\u4e00-\u9fa5]的补集
            // 但为了保险起见（考虑到可能的字符集扩展或特定需求），这里保留重复范围
            // 简化后的非中文字符正则表达式应为：[^一-龥\u3000-\u303f\uff00-\uffef]+（注意：一-龥是汉字的大致范围，但不如\u4e00-\u9fa5精确）
            // 但由于我们需要包括全角字符，所以仍使用原始范围，并排除它们
            //string pattern = @"[^\u4e00-\u9fff\u3000-\u303F\uFF00-\uFFEF\u4E00-\u9FA5]";
            string pattern = @"[^\u4E00-\u9FA5\u3000-\u303F\uFF00-\uFFEF.]";

            // 注意：上面的正则表达式中，\u3000-\u303F和\uFF00-\uFFEF分别代表CJK标点符号和全角ASCII字符（包括全角括号）
            // \u4E00-\u9FA5是基础汉字范围（与\u4e00-\u9fff相同，但大写形式更常用）

            // 使用Regex类的IsMatch方法来检查输入字符串是否包含非中文字符（不含已列出的全角字符）
            // 注意：由于我们想要检测至少一个非中文字符的存在，因此不需要在正则表达式开头加^，在结尾加$
            // 如果要检测整个字符串是否全部由非中文字符组成，则需要添加这些符号
            return Regex.IsMatch(input, pattern);
        }




        private string TypeToCompanyOrgType(string companytype)
        {
            string res = companytype;
            switch (companytype)
            {
                case "1":
                    res = "有限责任公司";
                    break;
                case "2":
                    res = "香港企业";
                    break;
                case "3":
                    res = "社会组织";
                    break;
                case "4":
                    res = "律所";
                    break;
                case "5":
                    res = "事业单位";
                    break;
                case "6":
                    res = "基金会";
                    break;
                case "7":
                    res = "不存在法人、注册资本、统一社会信用代码、经营状态";
                    break;
                case "8":
                    res = "台湾企业";
                    break;
                case "9":
                    res = "新机构";
                    break;
                default:
                    break;
            }
            return res;
            //1：公司；2：香港企业；3：社会组织；4：律所；5：事业单位；6：基金会；7 - 不存在法人、注册资本、统一社会信用代码、经营状态; 8：台湾企业；9 - 新机构
        }


        /// <summary>
        /// 检查api调用次数  true=超出
        /// </summary>
        /// <returns></returns>
        private bool CheckUsedAPI()
        {
            bool res = false;
            int count = DbOpe_crm_tianyancha_log.Instance.Query_crm_tianyancha_log("0000-0000-0000-0000"); //UserTokenInfo.id
            if (count >= int.Parse(AppSettings.TianyanchaAPI.Tianyancha_MaxNum))
                res = true; //超过限制

            return res;
        }


    }

}
