﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using CRM2_API.Common.Filter;
using CRM2_API.Common.Cache;

namespace CRM2_API.Controllers
{
    [Description("员工控制器")]
    //[SkipAuthCheck]
    public class UserController : MyControllerBase
    {

        public UserController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        /// <summary>
        /// 添加员工信息
        /// </summary>
        /// <param name="addUser_In"></param>
        [HttpPost]
        public void AddUser(AddUser_In addUser_In)
        {
            BLL_User.Instance.AddUser(addUser_In);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }
        /// <summary>
        /// 生成员工账号
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public string CreateUserName()
        {
            return BLL_User.Instance.CreateUserName();
        }
        /// <summary>
        /// 生成密码
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public string CreateUserPassword()
        {
            return new RandomUtil(8, Enum_RandomFormat.NumberAndLetter).GetRandom();
        }
        /// <summary>
        /// 修改员工信息
        /// </summary>
        /// <param name="updUser_In"></param>
        [HttpPost]
        public void UpdateUser(UpdateUser_In updUser_In)
        {
            BLL_User.Instance.UpdateUser(updUser_In);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }

        /// <summary>
        /// 补充员工信息
        /// </summary>
        /// <param name="supplementUser_In"></param>
        [HttpPost, SkipUserSupplementInfo, SkipRightCheck]
        public void SupplementUser(SupplementUser_In supplementUser_In)
        {
            BLL_User.Instance.SupplementUser(supplementUser_In);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.ClearAllCache();
        }
        /// <summary>
        /// 删除用户信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost]
        public void DeleteUser(string Ids)
        {
            BLL_User.Instance.DeleteUser(Ids);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }

        /// <summary>
        /// 修改用户状态信息
        /// </summary>
        /// <param name="updUserSta_In"></param>
        [HttpPost]
        public void UpdateUserStatus(UpdateUserStatus_In updUserSta_In)
        {
            BLL_User.Instance.UpdateUserStatus(updUserSta_In);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }

        /// <summary>
        /// 根据用户Id获取用户信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public GetUserById_Out GetUserById(string Id)
        {
            //验证Id是否为空
            if (string.IsNullOrEmpty(Id))
                throw new ApiException("员工主键不可为空");
            //查询用户信息
            return DbOpe_sys_user.Instance.GetUserById(Id);
        }

        /// <summary>
        /// 根据查询条件获取用户信息列表
        /// </summary>
        /// <param name="searchUserList_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchUserList_Out> SearchUserList(SearchUserList_In searchUserList_In)
        {
            int total = 0;
            var list = DbOpe_sys_user.Instance.SearchUserList(searchUserList_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 批量修改用户最大可保存客户数
        /// </summary>
        /// <param name="batchUpdUserMaxSaveCusInList"></param>
        [HttpPost]
        public void BatchUpdateUserMaxSaveCustomer(List<BatchUpdateUserMaxSaveCustomer_In> batchUpdUserMaxSaveCusInList)
        {
            //求每个用户的销售额
            batchUpdUserMaxSaveCusInList.ForEach(x =>
            {
                DbOpe_sys_user.Instance.BatchUpdateUserMaxSaveCustomer(x.Id, x.MaxSaveCustomer);
            });
            DbOpe_sys_user.Instance.SaveQueues();
        }

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="Id"></param>
        [HttpPost]
        public void ResetUserPassword(string Id)
        {
            BLL_User.Instance.ResetUserPassword(Id);
        }

        /// <summary>
        /// 绑定用户微信
        /// </summary>
        /// <param name="bindUserWeChat_In"></param>
        [HttpPost]
        public void BindUserWeChat(BindUserWeChat_In bindUserWeChat_In)
        {
            BLL_User.Instance.BindUserWeChat(bindUserWeChat_In);
        }

        /// <summary>
        /// 解绑用户微信
        /// </summary>
        /// <param name="Id"></param>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public void UnBindUserWeChat(string Id)
        {
            if (string.IsNullOrEmpty(Id))
                throw new ApiException("用户主键不可为空");
            DbOpe_sys_user.Instance.UnBindUserWeChat(Id);
        }

        /// <summary>
        /// 根据角色Id获取用户信息
        /// </summary>
        /// <param name="getUserByRoleId_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetUserByRoleId_Out> GetUserByRoleId(GetUserByRoleId_In getUserByRoleId_In)
        {
            int total = 0;
            var list = DbOpe_sys_user.Instance.GetUserByRoleId(getUserByRoleId_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据组织Id获取用户信息
        /// </summary>
        /// <param name="getUserByOrgId_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetUserByOrgId_Out> GetUserByOrgId(GetUserByOrgId_In getUserByOrgId_In)
        {
            int total = 0;
            var list = DbOpe_sys_user.Instance.GetUserByOrgId(getUserByOrgId_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 修改用户登录设置，设置5种登录方式的启用和禁用状态。
        /// </summary>
        /// <param name="updOrgOptIn"></param>
        [HttpPost]
        public void UpdateUserLoginOption(UpdateUserLoginOption_In updOrgOptIn)
        {
            DbOpe_sys_user.Instance.UpdateUserLoginOption(updOrgOptIn);
        }
        /// <summary>
        /// 根据用户token获取用户姓名
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public string GetUserNameByUserToken()
        {
            return BLL_User.Instance.GetUserNameByUserToken();
           
        }
        
        /// <summary>
        /// H5端 根据条件查询用户列表
        /// </summary>
        /// <param name="searchUserListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<H5SearchUserList_Out> H5SearchUserList(H5SearchUserList_In searchUserListIn)
        {
            int total = 0;
            var list = BLL_User.Instance.GetH5SearchUserList(searchUserListIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 获取登录用户的用户信息
        /// </summary>
        /// <returns></returns>
        [HttpPost,SkipRightCheck]
        public GetLoginUserInfo_Out GetLoginUserInfo()
        {
            return DbOpe_sys_user.Instance.GetLoginUserInfo();
        }


        /// <summary>
        /// 根据用户token获取同组织人员信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<GetSameOrgUserInfoByToken_Out> GetSameOrgUserInfoByToken()
        {
            return BLL_User.Instance.GetSameOrgUserInfoByToken();
        }

        /// <summary>
        /// 根据用户Id获取用户的晋升记录
        /// </summary>
        /// <param name="getUserPromotionListByUserIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetUserPromotionListByUserId_Out> GetUserPromotionList(GetUserPromotionListByUserId_In getUserPromotionListByUserIdIn)
        {
            if (getUserPromotionListByUserIdIn is null || string.IsNullOrEmpty(getUserPromotionListByUserIdIn.UserId))
            {
                throw new ApiException("用户主键不可为空");
            }

            return DbOpe_crm_user_promotion.Instance.GetUserPromotionList(getUserPromotionListByUserIdIn.UserId);
        }

        /// <summary>
        /// 获取中文姓名的拼音字符串
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        [HttpPost]
        public string GetNamePinyinStr(string name)
        {
            return BLL_User.Instance.GetNamePinyinStr(name);
        }
        /// <summary>
        /// 验证unionId是否存在（删除的不算）
        /// </summary>
        /// <param name="unionId"></param>
        /// <returns></returns>
        [HttpGet, SkipAuthCheck, SkipRSAKey, SkipIPCheck, SkipRecordLog]
        public bool CheckUnionIdExist(string unionId)
        {
            return DbOpe_sys_user.Instance.CheckUnionIdExist(unionId);
        }
    }
}
