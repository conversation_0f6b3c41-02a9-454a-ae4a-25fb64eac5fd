﻿using System.Diagnostics;
using CRM2_API.BLL.Common;
using CRM2_API.Common.JWT;
using CRM2_API.Common.Cron;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.Promotion;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using JiebaNet.Segmenter.Common;
using NPOI.SS.Formula.Functions;
using Quartz;
using static Aspose.Pdf.Operator;
using static Lucene.Net.Util.Fst.Util;

namespace CRM2_API.BLL.Promotion
{
    public class BLL_Promotion : BaseBLL<BLL_Promotion>
    {
        /// <summary>
        /// 修改晋升信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updatePromotionIns"></param>
        /// <param name="isTimingRefresh"></param>
        /// <returns></returns>
        public UpdatePromotion_Out UpdatePromotion(UpdatePromotion_In updatePromotionIns, bool isTimingRefresh = false)
        {
            UpdatePromotion_Out result = new() { Data = 0 };
            //所有的职级描述信息
            var allUserPromotionDescribeList = DbOpe_crm_user_promotion_describe.Instance.GetUserPromotionDescribeArrayList();
            //通过职级信息的RankCode做成字典
            var allUserPromotionDescribeDict = allUserPromotionDescribeList.ToDictionary(k => k.RankCode, v => v);

            /*var promotionStartKeyList = updatePromotionIns.Data.Where(s => !string.IsNullOrEmpty(s.PromotionStartKey)).Select(s => s.PromotionStartKey).ToHashSet();
            var promotionEndKeyList = updatePromotionIns.Data.Select(s => s.PromotionEndKey).ToHashSet();
            promotionStartKeyList.UnionWith(promotionEndKeyList);
            List<string> allPromotionKeyList = promotionStartKeyList.ToList();
            var checkPromotionKeyResult = CheckPromotionKey(allPromotionKeyList);
            if (!checkPromotionKeyResult)
            {
                throw new ApiException("员工职级key所对应的信息不存在");
            }*/

            //开始更新或插入流程
            List<Db_sys_promotion> insertList = new(); // 插入列表
            List<Db_sys_promotion> updateList = new();  // 更新列表
            Db_sys_promotion temp = null;   //临时实体
            bool isOpenJob = false; //是否开启定时任务标记
            List<string> notRemoveIdList = new();   //不删除的Id列表
            List<string> removeIdList = new();  //删除的Id列表
            int sortNum = 0;    //排序码
            EnumPromotionType promotionType = updatePromotionIns.Type;  //前端传来的晋升类型，是职员、中队还是大队晋升?
            //开始操作
            //拿到接口参数列表中id存在的id列表，这些Id的数据需要更新
            List<string> updateIdList = updatePromotionIns.Data.Where(w => !string.IsNullOrEmpty(w.Id)).Select(s => s.Id).Distinct().ToList();
            //根据更新的Id列表去查询数据库中的数据
            List<Db_sys_promotion> promotionListByIdList = DbOpe_sys_promotion.Instance.GetPromotionListByIdList(updateIdList);
            //将待更新的制作为字典
            Dictionary<string, Db_sys_promotion> promotionsDictById = promotionListByIdList.ToDictionary(k => k.Id, v => v);

            //获取以晋升类型为key的字典数据
            Dictionary<EnumPromotionType, List<Db_sys_promotion>> promotionDict = DbOpe_sys_promotion.Instance.GetPromotionDictByTypeList(new List<EnumPromotionType>() { promotionType });

            List<Db_sys_promotion> promotionByType = new(); //根据晋升类型的晋升数据列表
            if (promotionDict.ContainsKey(promotionType))   //如果字典中存在前端传来的晋升类型数据，则赋值
            {
                promotionByType = promotionDict[promotionType];
            }

            ///
            /// 本地方法
            /// 用来添加数据
            /// type 操作类型
            /// inItem 前端传来的数据
            /// 
            Action<string, PromotionBase> addPromotion = (type, inItem) =>
            {
                //将前端传来的职级编码转换为职级的Id
                string promotionStartKey = allUserPromotionDescribeDict.ContainsKey(inItem.PromotionStartKey.ToString())
                    ? allUserPromotionDescribeDict[inItem.PromotionStartKey.ToString()].Id
                    : String.Empty;
                //将前端传来的职级编码转换为职级的Id
                string PromotionEndKey = allUserPromotionDescribeDict.ContainsKey(inItem.PromotionEndKey.ToString())
                    ? allUserPromotionDescribeDict[inItem.PromotionEndKey.ToString()].Id
                    : String.Empty;
                temp = new()
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = promotionType,
                    PromotionStartKey = promotionStartKey,
                    PromotionEndKey = PromotionEndKey,
                    PromotionConditionType1 = inItem.PromotionConditionType1,
                    PromotionConditionValue1 = inItem.PromotionConditionValue1,
                    PromotionConditionType2 = inItem.PromotionConditionType2,
                    PromotionConditionValue2 = inItem.PromotionConditionValue2,
                    Remark = string.Empty,
                    Deleted = false,
                    SortNum = ++sortNum,
                    CreateUser = TokenModel.Instance.id,
                    CreateDate = DateTime.Now
                };

                // 如果添加类型是 add 则有生效的上一条，rootAdd则是完全的新增，数据库中不存在生效的上一条
                if (type.Equals("add"))
                {
                    temp.Inherit = inItem.Id; //将id复制给新建的Inherit属性，表明新建的这条是继承的当前这条
                }

                // 如果开启定时任务
                if (isTimingRefresh)
                {
                    temp.WaitEffec = true;  //设置实体的定时属性标记
                    isOpenJob = true;   //开启定时任务标记
                }
                else
                {
                    temp.TakingEffectTime = DateTime.Now;   //否则立即生效
                    // CronUtil.StopJob("refreshPromotionTask");   // 停止定时任务已不再需要，SafeCronUtil支持同名任务替换
                }

                insertList.Add(temp);
            };

            Action<PromotionBase> modifyPromotion = inItem =>
            {
                //将前端传来的职级编码转换为职级的Id
                string promotionStartKey = allUserPromotionDescribeDict.ContainsKey(inItem.PromotionStartKey.ToString())
                    ? allUserPromotionDescribeDict[inItem.PromotionStartKey.ToString()].Id
                    : String.Empty;
                //将前端传来的职级编码转换为职级的Id
                string PromotionEndKey = allUserPromotionDescribeDict.ContainsKey(inItem.PromotionEndKey.ToString())
                    ? allUserPromotionDescribeDict[inItem.PromotionEndKey.ToString()].Id
                    : String.Empty;

                temp.Type = promotionType;
                temp.PromotionStartKey = promotionStartKey;
                temp.PromotionEndKey = PromotionEndKey;
                temp.PromotionConditionType1 = inItem.PromotionConditionType1;
                temp.PromotionConditionValue1 = inItem.PromotionConditionValue1;
                temp.PromotionConditionType2 = inItem.PromotionConditionType2;
                temp.PromotionConditionValue2 = inItem.PromotionConditionValue2;
                temp.SortNum = ++sortNum;

                temp.UpdateUser = TokenModel.Instance.id;
                temp.UpdateDate = DateTime.Now;

                // 如果没有开启定时任务
                if (!isTimingRefresh)
                {
                    temp.WaitEffec = null;  //定时属性标记为null
                    temp.TakingEffectTime = DateTime.Now;   //生效时间为立即生效
                    // CronUtil.StopJob("refreshPromotionTask");   //停止定时任务已不再需要，SafeCronUtil支持同名任务替换
                }
                else
                {
                    isOpenJob = true;   //将定时任务标记为true
                }
                notRemoveIdList.Add(temp.Id);   //这条是修改的，不能删除，添加到不能删除的列表中
                updateList.Add(temp);   //添加到更新列表
            };

            foreach (PromotionBase inItem in updatePromotionIns.Data)
            {
                // id为空,则为新建
                if (string.IsNullOrEmpty(inItem.Id))
                {
                    //将前端传来的职级编码转换为职级的Id
                    string promotionStartKey = allUserPromotionDescribeDict.ContainsKey(inItem.PromotionStartKey.ToString())
                        ? allUserPromotionDescribeDict[inItem.PromotionStartKey.ToString()].Id
                        : String.Empty;
                    //将前端传来的职级编码转换为职级的Id
                    string PromotionEndKey = allUserPromotionDescribeDict.ContainsKey(inItem.PromotionEndKey.ToString())
                        ? allUserPromotionDescribeDict[inItem.PromotionEndKey.ToString()].Id
                        : String.Empty;
                    // 晋升类型的晋升数据列表中是否存在当前职称开始和结束的待生效数据?
                    temp = promotionByType.FirstOrDefault(w =>
                        w.PromotionStartKey == promotionStartKey &&
                        w.PromotionEndKey == PromotionEndKey &&
                        w.WaitEffec == true);
                    // 存在就修改这条数据即可
                    if (temp != null)
                    {
                        modifyPromotion(inItem);
                    }
                    // 不存在则新建
                    else
                    {
                        addPromotion("rootAdd", inItem);
                    }
                }
                // id 不为空
                else
                {
                    // 是否存在继承于当前Id的待生效数据，这条数据会被移除掉
                    temp = promotionListByIdList.FirstOrDefault(s =>
                        inItem.Id.Equals(s.Inherit) && s.WaitEffec == true);

                    // 存在，则修改当前数据
                    if (temp != null)
                    {
                        modifyPromotion(inItem);
                    }
                    else
                    {
                        // 从待更新的字典中根据Id拿到待更新的数据
                        temp = promotionsDictById[inItem.Id];
                        // 如果是待生效
                        if (temp.WaitEffec == true)
                        {
                            //则修改
                            modifyPromotion(inItem);
                        }
                        else
                        {
                            //如果已经生效，则新建，新建的那条继承当前数据，所以为add。
                            addPromotion("add", inItem);
                        }
                    }
                }
            }
            // 如果开启的定时任务
            if (isTimingRefresh)
            {
                //将当前待生效的数据移除[排除掉不被移除的生效数据]
                removeIdList.AddRange(
                    promotionByType
                        .Where(w => w.WaitEffec == true)
                        .Select(s => s.Id)
                        .ToList()
                        .Except(notRemoveIdList)
                        .ToList());

            }
            // 如果没有开启定时任务
            else
            {
                //将当前的数据移除[排除掉不被移除的数据]
                removeIdList.AddRange(
                    promotionByType
                        .Select(s => s.Id)
                        .ToList()
                        .Except(notRemoveIdList)
                        .ToList());
            }

            if (insertList is not null and { Count: > 0 })
            {
                DbOpe_sys_promotion.Instance.Insert(insertList);
            }

            if (updateList is not null and { Count: > 0 })
            {
                DbOpe_sys_promotion.Instance.Update(updateList);
            }

            if (removeIdList is not null and { Count: > 0 })
            {
                DbOpe_sys_promotion.Instance.RemoveByIdList(removeIdList);
            }

            DbOpe_sys_promotion.Instance.SaveQueues();
            result.Data = 1;
            if (isTimingRefresh && isOpenJob)
            {
                AddTimingRefreshPromotionTask();
            }

            return result;
        }


        /// <summary>
        /// Promotion定时任务
        /// </summary>
        public void AddTimingRefreshPromotionTask()
        {
            string jobName = $"refreshPromotionTask";
            string cronExpression = "*/25 * * * * ?";
            
            SafeCronUtil.AddCronJob(jobName, () =>
            {
                RefreshPromotionParam();
                Debug.WriteLine($"{jobName}任务执行完成！ \t {DateTime.Now}");
            }, cronExpression);
        }

        /// <summary>
        /// 刷新Promotion操作Action
        /// </summary>
        private void RefreshPromotionParam()
        {
            List<Db_sys_promotion> allData =
                DbOpe_sys_promotion.Instance.GetAllPromotion();
            List<Db_sys_promotion> newData = allData.Where(w => w.WaitEffec == true).ToList();
            //判断更新的类型
            List<EnumPromotionType> newTypeList = newData.Select(s => s.Type.Value).Distinct().ToList();
            allData = allData.Where(w => newTypeList.Contains(w.Type.Value)).ToList();
            if (newData is null or { Count: 0 }) return;
            List<Db_sys_promotion> oldData = allData.Except(newData).ToList();
            //移除旧的参数并修改新的参数
            DbOpe_sys_promotion.Instance.RemoveByIdList(oldData.Select(s => s.Id).ToList());
            foreach (Db_sys_promotion newItem in newData)
            {
                newItem.WaitEffec = null;
                newItem.TakingEffectTime = DateTime.Now;
                newItem.UpdateUser = UserTokenInfo.id;
                newItem.UpdateDate = DateTime.Now;
            }

            DbOpe_sys_promotion.Instance.Update(newData);
            DbOpe_sys_promotion.Instance.SaveQueues();
        }

        /// <summary>
        /// 校验职称信息是否存在
        /// </summary>
        /// <param name="promotionKeys"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public bool CheckPromotionKey(List<string> promotionKeys)
        {
            if (promotionKeys is not null and { Count: > 0 })
            {
                var userPromotionDescribes = DbOpe_sys_promotion.Instance.GetUserPromotionDescribeByIds(promotionKeys);
                foreach (string userPromotionDescribeId in promotionKeys)
                {
                    if (!userPromotionDescribes.ContainsKey(userPromotionDescribeId))
                    {
                        throw new ApiException($"对应的职称信息不存在");
                    }
                }
            }

            return true;
        }

        public CustomerMaxSaverResult CustomerSaverCountAutoUpdate(DateTime dateTime_In)
        {
            try
            {
                //调整保留家数的时间点有两种：
                //1、每月1日，对于所有试用期转正人员，调整保留家数；
                //2、1/4/7/10月份的1日，每个人，计算当天前3/6个月的业绩，并以此为基准，进行保留家数调整；
                //（4/10月1日计算当天前3个月的业绩（只对增加家数的人员调整），
                //1、7月计算当天前6个月的业绩（对增、减同时调整）
                OrgRankStatistics_IN orgRankStatistics_IN = new OrgRankStatistics_IN();
                //时间值

                DateTime now = dateTime_In;
                //if (now.Month == 6)
                //{
                //    orgRankStatistics_IN.DateStart = new DateTime(now.Year, 1, 1, 0, 0, 0);
                //    orgRankStatistics_IN.DateEnd = new DateTime(now.Year, 6, 30, 23, 59, 59);
                //}
                switch (now.Month)
                {
                    case 1:
                        {
                            orgRankStatistics_IN.DateStart = new DateTime(now.Year - 1, 7, 1, 0, 0, 0);
                            orgRankStatistics_IN.DateEnd = new DateTime(now.Year - 1, 12, 31, 23, 59, 59);
                            break;
                        }
                    case 4:
                        {
                            orgRankStatistics_IN.DateStart = new DateTime(now.Year, 1, 1, 0, 0, 0);
                            orgRankStatistics_IN.DateEnd = new DateTime(now.Year, 3, 31, 23, 59, 59);
                            break;
                        }
                    case 7:
                        {
                            orgRankStatistics_IN.DateStart = new DateTime(now.Year, 1, 1, 0, 0, 0);
                            orgRankStatistics_IN.DateEnd = new DateTime(now.Year, 6, 30, 23, 59, 59);
                            break;
                        }
                    case 10:
                        {
                            orgRankStatistics_IN.DateStart = new DateTime(now.Year, 7, 1, 0, 0, 0);
                            orgRankStatistics_IN.DateEnd = new DateTime(now.Year, 9, 30, 23, 59, 59);
                            break;
                        }
                    default:
                        orgRankStatistics_IN.DateStart = DateTime.Now.AddMonths(-1).AddDays(1 - DateTime.Now.Day).Date;
                        orgRankStatistics_IN.DateEnd = DateTime.Now.AddMonths(-1).AddDays(1 - DateTime.Now.Day).Date.AddMonths(1).AddSeconds(-1);
                        break;
                }
                //得到了业绩
                orgRankStatistics_IN.EnumStatisticsValue = EnumStatisticsValue.EffectAchivement;
                orgRankStatistics_IN.EnumRankOrgType = EnumStatisticOrgType.User;
                
                var achivement = DbOpe_crm_contract_receiptregister.Instance.CalcCustomerCountEachMSH(orgRankStatistics_IN, "", true);
                //低于3万：20家
                //3万及以上6万以下：30家
                //6万及以上：40家
                //12万及以上：50家
                //18万及以上：60家
                //24万及以上：70家
                //30万及以上：80家
                List<Db_sys_usermaxsaveustomer_calclog> addDataList = new List<Db_sys_usermaxsaveustomer_calclog>();
                //获取一下规则(旧表利用,方便配置)
                var comparamRetain = DbOpe_sys_comparam_retain.Instance.GetAllComparamRetains().Select(i => new { i.PerformanceAmountStart, i.PerformanceAmountEnd, i.RetainCustomers }).ToList();

                achivement.ForEach(a =>
                {
                    Db_sys_usermaxsaveustomer_calclog adddata = new Db_sys_usermaxsaveustomer_calclog();
                    adddata.Id = Guid.NewGuid().ToString();
                    adddata.UserId = a.UserId;
                    //adddata.Year = orgRankStatistics_IN.DateStart.Value.Year;
                    //adddata.Month = orgRankStatistics_IN.DateEnd.Value.Month;
                    adddata.Year = now.Year;
                    adddata.Month = now.Month;
                    adddata.MaxSaveCustomerOld = DbOpe_sys_user.Instance.GetDataById(a.UserId).MaxSaveCustomer;
                    adddata.EffectAchivement = a.UserValue;
                    adddata.Deleted = false;
                    //switch (a.UserValue)
                    //{
                    //    case decimal n when n < 3:
                    //        adddata.MaxSaveCustomerCalc = 20;
                    //        break;
                    //    case decimal n when n < 6 && n >= 3:
                    //        adddata.MaxSaveCustomerCalc = 30;
                    //        break;
                    //    case decimal n when n < 12 && n > 6:
                    //        adddata.MaxSaveCustomerCalc = 40;
                    //        break;
                    //    case decimal n when n < 18 && n >= 12:
                    //        adddata.MaxSaveCustomerCalc = 50;
                    //        break;
                    //    case decimal n when n < 24 && n >= 18:
                    //        adddata.MaxSaveCustomerCalc = 60;
                    //        break;
                    //    case decimal n when n < 30 && n >= 24:
                    //        adddata.MaxSaveCustomerCalc = 70;
                    //        break;
                    //    case decimal n when n >= 30:
                    //        adddata.MaxSaveCustomerCalc = 80;
                    //        break;
                    //    default:

                    //        break;
                    //}

                    comparamRetain.ForEach(c =>
                    {
                        if (a.UserValue >= c.PerformanceAmountStart * 10000 && a.UserValue <= c.PerformanceAmountEnd * 10000)
                        {
                            adddata.MaxSaveCustomerCalc = c.RetainCustomers;
                        }
                    });



                    adddata.MaxSaveCustomerCalc = adddata.MaxSaveCustomerCalc.IsNull() ? 20 : adddata.MaxSaveCustomerCalc;
                    if (now.Month == 4 || now.Month == 10)
                    {

                        adddata.MaxSaveCustomerFinal = adddata.MaxSaveCustomerCalc > adddata.MaxSaveCustomerOld ? adddata.MaxSaveCustomerCalc : adddata.MaxSaveCustomerOld;
                        if (adddata.MaxSaveCustomerFinal.IsNull())
                        {
                            adddata.MaxSaveCustomerFinal = 20;
                        }
                    }
                    else
                    if (now.Month == 1 || now.Month == 7)
                    {
                        adddata.MaxSaveCustomerFinal = adddata.MaxSaveCustomerCalc;
                        if (adddata.MaxSaveCustomerFinal.IsNull())
                        {
                            adddata.MaxSaveCustomerFinal = 20;
                        }
                    }
                    else
                    {
                        //临时规则 非周期日计算但插入数据默认删除，方便更新时获取到正确的数据
                        adddata.Deleted = true;
                    }
                    //adddata.MaxSaveCustomerFinal = adddata.MaxSaveCustomerOld;

                    adddata.CreateUser = UserId.IsNull() ? "00000000-0000-0000-0000-000000000000" : UserId;
                    adddata.CreateDate = DateTime.Now;
                    addDataList.Add(adddata);
                });
                DbOpe_sys_usermaxsaveustomer_calclog.Instance.DeleteOldDataByYearMonth(now.Year, now.Month);
                DbOpe_sys_usermaxsaveustomer_calclog.Instance.SaveQueues();
                //DbOpe_sys_usermaxsaveustomer_calclog.Instance.InsertListDataQueue(addDataList);
                int changedRow = DbOpe_sys_usermaxsaveustomer_calclog.Instance.Insert(addDataList);
                //DbOpe_sys_usermaxsaveustomer_calclog.Instance.SaveQueues();
                CustomerMaxSaverResult result = new CustomerMaxSaverResult();
                result.ResultCount = changedRow;
                result.ResultCode = 1;
                result.ResultMessage = "OK";

                return result;
            }
            catch (Exception ex)
            {
                throw new ApiException(ex.Message.ToString());
            }

        }

        public CustomerMaxSaverResult AcceptUpdateCustomerMaxSaverResult(CustomerMaxSaverResult_In testCustomer)
        {
            try
            {
                return DbOpe_sys_usermaxsaveustomer_calclog.Instance.AcceptUpdateCustomerMaxSaverResult(testCustomer);
            }
            catch (Exception ex)
            {
                throw new ApiException(ex.Message.ToString());
            }
        }

    }
}