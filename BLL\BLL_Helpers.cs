using CRM2_API.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using CRM2_API.Model.BLLModel.Enum;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 通用业务辅助方法
    /// </summary>
    public static class BusinessHelper
    {
        /// <summary>
        /// 获取发票类型名称
        /// </summary>
        public static string GetInvoiceTypeName(int invoiceType)
        {
            return ExtensionMethod.GetEnumDescription((EnumInvoiceType)invoiceType);
        }

        /// <summary>
        /// 获取开票类型名称
        /// </summary>
        public static string GetBillingTypeName(int billingType)
        {
            return ExtensionMethod.GetEnumDescription((EnumBillingType)billingType);
        }

        /// <summary>
        /// 获取发票申请状态名称
        /// </summary>
        public static string GetInvoiceApplicationStatusName(int status)
        {
            return ExtensionMethod.GetEnumDescription((EnumAuditStatus)status);
        }

        /// <summary>
        /// 获取退票状态名称
        /// </summary>
        public static string GetRefundStatusName(int refundStatus)
        {
            return ExtensionMethod.GetEnumDescription((EnumRefundStatus)refundStatus);
        }


        /// <summary>
        /// 获取合同状态名称
        /// </summary>
        public static string GetContractStatusName(int status)
        {
            switch (status)
            {
                case 1:
                    return "草稿";
                case 2:
                    return "待审核";
                case 3:
                    return "审核通过";
                case 4:
                    return "审核不通过";
                case 5:
                    return "已签约";
                case 6:
                    return "执行中";
                case 7:
                    return "已完成";
                case 8:
                    return "已终止";
                default:
                    return "未知";
            }
        }

        /// <summary>
        /// 获取维度名称
        /// </summary>
        public static string GetDimensionName(int dimensionType)
        {
            switch (dimensionType)
            {
                case 1:
                    return "按月份";
                case 2:
                    return "按发票类型";
                case 3:
                    return "按开票公司";
                case 4:
                    return "按发票抬头";
                case 5:
                    return "按部门";
                default:
                    return "未知";
            }
        }

        /// <summary>
        /// 根据用户ID获取用户名称
        /// </summary>
        public static string GetUserName(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                return "";
            }

            try
            {
                // 此处假设DbOpe_sys_user是用户表的操作类
                var userObj = DbOpe_sys_user.Instance.GetDataById(userId);
                return userObj?.UserName ?? userId;
            }
            catch
            {
                // 如果出错，直接返回用户ID
                return userId;
            }
        }
    }
} 