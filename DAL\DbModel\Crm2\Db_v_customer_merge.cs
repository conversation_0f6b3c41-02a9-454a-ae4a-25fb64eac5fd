﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_customer_merge")]
    public class Db_v_customer_merge
    {
        /// <summary>
        /// Desc:合并前客户ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string BeforeCustomerId { get; set; }

        /// <summary>
        /// Desc:合并后客户ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AfterCustomerId { get; set; }
        /// <summary>
        /// Desc:合并路径（ID）
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerMergePath { get; set; }
        /// <summary>
        /// Desc:合并路径（客户名称）
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerMergePathName { get; set; }

    }
}
