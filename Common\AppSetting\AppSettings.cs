﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Reflection;
using System.IO;

namespace CRM2_API.Common.AppSetting
{
    /// <summary>
    /// 全局配置appsettings.json文件的配置
    /// </summary>
    public class AppSettings
    {
        /// <summary>
        /// 启动端口
        /// </summary>
        public static int Port { get; set; }
        /// <summary>
        /// 启动环境
        /// </summary>
        public static Enum_SystemSettingEnv Env { get; set; }
        /// <summary>
        /// 连接字符串
        /// </summary>
        public static Setting_ConnectionStrings ConnectionStrings { get; set; }
        /// <summary>
        /// jwt配置项
        /// </summary>
        public static Setting_Jwt Jwt { get; set; }
        /// <summary>
        /// 阿里云手机短信配置
        /// </summary>
        public static Setting_AliPhoneMessage AliPhoneMessage { get; set; }
        /// <summary>
        /// 邮箱发送配置
        /// </summary>
        public static Setting_Email Email { get; set; }
        /// <summary>
        /// 微信的登录配置
        /// </summary>
        public static Setting_WeChatLogin WeChatLogin { get; set; }

        /// <summary>
        /// 微信的登录配置
        /// </summary>
        public static Setting_WeChatConfig WeChatConfig { get; set; }

        /// <summary>
        /// 约定的RSA秘钥配置
        /// </summary>
        public static Setting_RSAKey RSAKey { get; set; }
        /// <summary>
        /// 环球搜相关接口地址
        /// </summary>
        public static Setting_GlobalSearchAPI GlobalSearchAPI { get; set; }
        /// <summary>
        /// gtis地址和header
        /// </summary>
        public static Setting_GtisApi GtisApi { get; set; }
        /// <summary>
        /// 天眼查API配置
        /// </summary>
        public static Setting_TianyanchaAPI TianyanchaAPI { get; set; }
        /// <summary>
        /// 默认静默时间期限
        /// </summary>
        public static int SilenceMinutes { get; set; }

        /// <summary>
        /// 绑定微信菜单
        /// </summary>
        public static bool BindWeChatMenu { get; set; }

        /// <summary>
        /// 自动作废合同天数
        /// </summary>
        public static int AutoVoidContractDays { get; set; }

        /// <summary>
        /// 文件保存路径
        /// </summary>
        public static string DownLoadFilePath { get; set; }

        /// <summary>
        /// 文件保存路径(旧系统文件)
        /// </summary>
        public static string OldDownLoadFilePath { get; set; }

        public static string GTISTypeDescriptionDate2025 { get; set; }
        public static string GTISTypeDescriptionContent2025 { get; set; }
        public static string GTISTypeDescriptionDate2026 { get; set; }
        public static string GTISTypeDescriptionContent2026 { get; set; }
        public static string GTISTypeDescriptionDate2027 { get; set; }
        public static string GTISTypeDescriptionContent2027 { get; set; }
        public static string GTISTypeDescriptionDate2028 { get; set; }
        public static string GTISTypeDescriptionContent2028 { get; set; }
        public static Setting_QCloud QCloud { get; set; }
        public static Setting_DingTalk DingTalk { get; set; }
        /// <summary>
        /// 日志配置
        /// </summary>
        public static Setting_LogConfig LogConfig { get; set; }

        /// <summary>
        /// UmiOCR服务配置
        /// </summary>
        public static Setting_UmiOcr UmiOcr { get; set; }

        /// <summary>
        /// SaleWits资源下发配置
        /// </summary>
        public static Setting_SaleWits SaleWits { get; set; }

        /// <summary>
        /// 初始化配置
        /// </summary>
        public static void InitSetting()
        {

          var ENVIRONMENT =  Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (!string.IsNullOrEmpty(ENVIRONMENT))
            {
                ENVIRONMENT = "."+ ENVIRONMENT;
            }
            LogUtil.AddErrorLog($"---------ENVIRONMENT----------{ENVIRONMENT}");
            //读取json配置项
            JObject jsonSetting = FileUtil.ReadFileShare(AppDomain.CurrentDomain.BaseDirectory + $"appsettings{ENVIRONMENT}.json").DeserializeNewtonJson<JObject>();
            LogUtil.AddLog(AppDomain.CurrentDomain.BaseDirectory + $"appsettings{ENVIRONMENT}.json");
            //自动解析配置项
            Type settingType = typeof(AppSettings);
            PropertyInfo[] props = typeof(AppSettings).GetProperties(System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.Public);
            props.ForEach(p =>
            {
                if (jsonSetting.ContainsKey(p.Name))
                {
                    p.SetValue(null, jsonSetting[p.Name].ToObject(p.PropertyType));
                }
            });
            //继续处理连接字符串
            string dbEncyptStr = "tianjin*";//数据库解密字符串
            ConnectionStrings.CRM2 = EncryptUtil.DecryptDES(ConnectionStrings.CRM2, dbEncyptStr);
            ConnectionStrings.GTIS = EncryptUtil.DecryptDES(ConnectionStrings.GTIS, dbEncyptStr);
            ConnectionStrings.LOG = EncryptUtil.DecryptDES(ConnectionStrings.LOG, dbEncyptStr);
            LogUtil.AddErrorLog($"---------CRM2----------{ConnectionStrings.CRM2}");
            LogUtil.AddErrorLog($"---------GTIS----------{ConnectionStrings.GTIS}");
            LogUtil.AddErrorLog($"---------LOG----------{ConnectionStrings.LOG}");
            Email.Pwd = EncryptUtil.DecryptDES(Email.Pwd, dbEncyptStr);
            
            // 解密ClickHouse配置
            if (LogConfig != null)
            {
                if (!string.IsNullOrEmpty(LogConfig.ClickHouseHost))
                    LogConfig.ClickHouseHost = EncryptUtil.DecryptDES(LogConfig.ClickHouseHost, dbEncyptStr);
                
                if (!string.IsNullOrEmpty(LogConfig.ClickHouseUser))
                    LogConfig.ClickHouseUser = EncryptUtil.DecryptDES(LogConfig.ClickHouseUser, dbEncyptStr);
                
                if (!string.IsNullOrEmpty(LogConfig.ClickHousePassword))
                    LogConfig.ClickHousePassword = EncryptUtil.DecryptDES(LogConfig.ClickHousePassword, dbEncyptStr);
                
                LogUtil.AddErrorLog($"---------ClickHouseHost----------{LogConfig.ClickHouseHost}");
            }
        }
    }

    /// <summary>
    /// 系统启动环境
    /// </summary>
    public enum Enum_SystemSettingEnv
    {
        /// <summary>
        /// 开发环境
        /// </summary>
        Debug = 0,
        /// <summary>
        /// 生产环境
        /// </summary>
        Product = 1,
        /// <summary>
        /// 测试环境
        /// </summary>
        Test = 2,

    }

    /// <summary>
    /// 日志配置
    /// </summary>
    public class Setting_LogConfig
    {
        /// <summary>
        /// 是否启用日志记录
        /// </summary>
        public bool EnableLogging { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; }

        /// <summary>
        /// 日志目录
        /// </summary>
        public string LogDirectory { get; set; }

        /// <summary>
        /// ClickHouse主机地址
        /// </summary>
        public string ClickHouseHost { get; set; } = "localhost";

        /// <summary>
        /// ClickHouse TCP端口
        /// </summary>
        public int ClickHousePort { get; set; } = 8087;

        /// <summary>
        /// ClickHouse用户名
        /// </summary>
        public string ClickHouseUser { get; set; } = "default";

        /// <summary>
        /// ClickHouse密码
        /// </summary>
        public string ClickHousePassword { get; set; } = "";

        /// <summary>
        /// 获取解析后的日志目录
        /// </summary>
        /// <returns>解析后的目录路径</returns>
        public string GetResolvedLogDirectory()
        {
            // 如果未配置日志目录，默认使用程序目录下的Log文件夹
            string directory = LogDirectory ?? Path.Combine(Directory.GetCurrentDirectory(), "Log");
            
            // 解析环境变量
            if (directory.Contains("%"))
            {
                directory = Environment.ExpandEnvironmentVariables(directory);
            }
            
            // 确保是绝对路径
            if (!Path.IsPathRooted(directory))
            {
                directory = Path.Combine(Directory.GetCurrentDirectory(), directory);
            }
            
            return directory;
        }
    }
}
