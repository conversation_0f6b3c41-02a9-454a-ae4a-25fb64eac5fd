﻿using CRM2_API.BLL;
using CRM2_API.Common;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BLLModel.PreInspection;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.Services.PreInspection;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.ComponentModel;
using System.IO;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 客户控制器
    /// </summary>
    [Description("客户控制器")]
    public class CustomerController : MyControllerBase
    {
        public CustomerController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        #region 客户服务产品

        /// <summary>
        /// 根据查询条件获取客户服务产品列表
        /// </summary>
        /// <param name="queryCustomer_IN"></param>
        [HttpPost]
        public ApiTableOut<SearchServicesList_OUT> SearchSericesList(SearchServicesList_IN queryCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchSericesList(queryCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 根据申请id获取环球慧思用户信息列表
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        [HttpPost]
        public List<ServiceInfoGtisUser_OUT> GetGtisServiceUserByApplId(string applId)
        {
            return BLL_Customer.Instance.GetGtisServiceUserByApplId(applId);
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_环球慧思信息（申请信息+ 登记复核信息 + 在服信息）
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        [HttpPost]
        public ContractServiceGtisInfo_OUT GetGtisServiceInfoByApplId(string applId)
        {
            return BLL_Customer.Instance.GetGtisServiceInfoByApplId(applId);
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_慧思学院信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        [HttpPost]
        public GetContractServiceInfoCollegeByApplId_Out GetCollegeInfoByApplId(string applId)
        {
            return BLL_Customer.Instance.GetCollegeInfoByApplId(applId); ;
        }
        /// <summary>
        /// 根据申请ID获取环球搜开通账号信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetContractServiceInfoGlobalSearchUserListByApplId_Out> GetGlobalSearchServiceUserListByApplId(string applId)
        {
            return BLL_Customer.Instance.GetGlobalSearchServiceUserListByApplId(applId);
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_环球搜信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        [HttpPost]
        public GetContractServiceInfoGlobalSearchByApplId_Out GetGlobalSearchInfoByApplId(string applId)
        {
            return BLL_Customer.Instance.GetGlobalSearchInfoByApplId(applId);
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_邓白氏信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        [HttpPost]
        public GetContractServiceInfoDBByApplId_Out GetDBInfoByApplId(string applId)
        {
            return BLL_Customer.Instance.GetDBInfoByApplId(applId);
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_其他数据信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        [HttpPost]
        public GetContractServiceInfoOtherDataByApplId_Out GetOtherDataInfoByApplId(string applId)
        {
            return BLL_Customer.Instance.GetOtherDataInfoByApplId(applId);
        }

        /// <summary>
        /// 申请加急处理
        /// </summary>
        /// <param name="updateApplUrgent_In"></param>
        /// <returns></returns>
        [HttpPost]
        public void UpdateApplUrgent(List<UpdateApplUrgent_In> updateApplUrgent_In)
        {
            BLL_Customer.Instance.UpdateApplUrgent(updateApplUrgent_In);
        }


        #endregion

        #region 客户使用情况
        /// <summary>
        /// 获取客户下所有gtis账号使用情况列表
        /// </summary>
        /// <param name="gtisUserUseLog_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GtisUserUseLog_OUT> GetCustomerGtisUserLog(GtisUserUseLog_IN gtisUserUseLog_IN)
        {
            return BLL_Customer.Instance.GetCustomerGtisUserLog(gtisUserUseLog_IN);
        }
        /// <summary>
        /// 获取GTIS指定账号的按月使用情况
        /// </summary>
        /// <param name="gtisUserMonthLog_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GtisUserMonthLog_OUT> GetGtisUserMonthLog(GtisUserMonthLog_IN gtisUserMonthLog_IN)
        {
            return BLL_Customer.Instance.GetGtisUserMonthLog(gtisUserMonthLog_IN);
        }
        /// <summary>
        /// 获取GTIS账号使用日志详情
        /// </summary>
        /// <param name="gtisUserLogDetail_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public ApiTableOut<BM_UserOpeLogDetail> GetGtisUserLogDetailTable(GtisUserLogDetail_IN gtisUserLogDetail_IN)
        {
            return BLL_Customer.Instance.GetGtisUserLogDetailTable(gtisUserLogDetail_IN);
        }
        /// <summary>
        /// 获取GTIS账号导出详情
        /// </summary>
        /// <param name="gtisUserDownloadLogDetail_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public ApiTableOut<BM_UserExportLogDetail> GetGtisDownloadLogDetailTable(GtisUserDownloadLogDetail_IN gtisUserDownloadLogDetail_IN)
        {
            return BLL_Customer.Instance.GetGtisDownloadLogDetailTable(gtisUserDownloadLogDetail_IN);
        }
        /// <summary>
        /// GTIS账号导出
        /// </summary>
        /// <param name="gtisUserIds"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DownloadGtisUserLog(List<string> gtisUserIds)
        {
            Stream result = BLL_Customer.Instance.DownloadGtisUserLog(gtisUserIds);
            return new FileStreamResult(result, "application/octet-stream");
        }

        /// <summary>
        /// 获取账号下所有客户下gtis账号使用情况列表
        /// </summary>
        /// <param name="allGtisUserUseLog_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GtisAllUserUseLog_OUT> GetAllCustomerGtisUserLog(AllGtisUserUseLog_IN allGtisUserUseLog_IN)
        {
            return BLL_Customer.Instance.GetAllCustomerGtisUserLog(allGtisUserUseLog_IN);
        }

        /// <summary>
        /// 查询当前用户有效的Gtis客户列表（下拉菜单用) 
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<CustomersvCodesInfo> GetGtisSvcodes()
        {
            string UserId = UserTokenInfo.id;
            return DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisSvcodes(UserId);
        }
        /// <summary>
        /// 根据svcode，账号id获取账号的国家列表，附属其他内容
        /// </summary>
        /// <param name="gtisUseLog_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public GtisAllUserUseLog_OUT GetGtisAccountCountryInfoLog(GtisUseLog_IN gtisUseLog_IN)
        {
            return BLL_Customer.Instance.GetGtisAccountCountryInfoLog(gtisUseLog_IN);
        }

        /// <summary>
        /// 根据用户phoneID当前用户的按月统计操作日志
        /// </summary>
        /// <param name="gtisUseLog_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<BM_OneUserOperateLogByMonth> GetGtisUserOperateLogByMonthLog(GtisUseLog_IN gtisUseLog_IN)
        {
            return BLL_Customer.Instance.GetGtisUserOperateLogByMonthLog(gtisUseLog_IN);
        }
        /// <summary>
        /// 获取phoneID使用日志详情
        /// </summary>
        /// <param name="gtisUserLogDetailByPhoneId_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public ApiTableOut<BM_UserOpeLogDetail> GetGtisUserLogDetailTableByPhoneId(GtisUserLogDetailByPhoneId_IN gtisUserLogDetailByPhoneId_IN)
        {
            return BLL_Customer.Instance.GetGtisUserLogDetailTableByPhoneId(gtisUserLogDetailByPhoneId_IN);
        }

        /// <summary>
        /// 获取使用者导出详情
        /// </summary>
        /// <param name="gtisUserDownloadLogDetail_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public ApiTableOut<BM_UserExportLogDetail> GetUserDownloadLogDetailTable(GtisUserDownloadLogDetail_IN gtisUserDownloadLogDetail_IN)
        {
            return BLL_Customer.Instance.GetUserDownloadLogDetailTable(gtisUserDownloadLogDetail_IN);
        }

        /// <summary>
        /// 管理者获取团队临时账号使用情况
        /// </summary>
        /// <param name="demoUserQuery_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost, SkipRightCheck]
        public ApiTableOut<BM_GtisOpe_DemoInfoStates> GetDemoQuery(DemoUserQuery_IN demoUserQuery_IN)
        {
            return BLL_Customer.Instance.GetDemoQuery(demoUserQuery_IN);
        }


        /// <summary>
        /// 管理者获取团队临时账号使用情况详细
        /// </summary>
        /// <param name="demoUserQuery_IN"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost, SkipRightCheck]
        public ApiTableOut<BM_UserOpeLogDetail> GetDemoQueryOpeLogDetail(DemoUserQueryDetail_IN demoUserQuery_IN)
        {
            return BLL_Customer.Instance.GetDemoQueryOpeLogDetail(demoUserQuery_IN);
        }

        /// <summary>
        /// 获取手机号新增删除列表
        /// </summary>
        /// <param name="getG5PhoneList_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<BM_GtisOpe5Phone> GetG5PhoneList(GetG5PhoneList_In getG5PhoneList_In)
        {
            return BLL_Customer.Instance.GetG5PhoneList(getG5PhoneList_In);
        }

        /// <summary>
        /// 获取最新手机号新增删除数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<BM_GtisOpe5Phone> GetG5Phone_New()
        {
            return BLL_Customer.Instance.GetG5Phone_New();
        }
        #endregion

        #region 私有池
        /// <summary>
        /// 添加私有池客户
        /// </summary>
        /// <param name="addCustomer_In"></param>
        [HttpPost]
        public string AddPrivatePoolCustomer([FromForm] AddCustomer_IN<AddSubCompany_IN> addCustomer_In)
        {
            return BLL_Customer.Instance.AddPrivatePoolCustomer(addCustomer_In);
        }
        /// <summary>
        /// 修改私有池客户（名称/代码不让改）
        /// </summary>
        /// <param name="updateCustomer_IN"></param>
        [HttpPost]
        //[CustomerAuthCheck]
        public void UpdatePrivatePoolCustomer([FromForm] UpdateCustomer_IN<UpdateSubCompany_IN> updateCustomer_IN)
        {
            BLL_Customer.Instance.UpdatePrivatePoolCustomer(updateCustomer_IN);
        }
        /// <summary>
        /// 开放修改私有池客户名称权限
        /// </summary>
        /// <param name="companyId_IN"></param>
        [HttpPost]
        //[CustomerAuthCheck]
        public void AddUpdatePrivatePoolCustomerAuth(CompanyId_IN companyId_IN)
        {
            BLL_Customer.Instance.AddUpdatePrivatePoolCustomerAuth(companyId_IN);
        }
        /// <summary>
        /// 根据查询条件获取私有池客户信息列表
        /// </summary>
        /// <param name="queryCustomer_IN"></param>
        [HttpPost]
        public ApiTableOut<QueryCustomer_OUT> SearchPrivatePoolCustomerList(QueryCustomer_IN queryCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchPrivatePoolCustomerList(queryCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 获取当前用户的剩余可保留客户数量信息。系统可保留用户数量减去当前用户的已保留用户数量
        /// </summary>
        [HttpPost]
        public int GetRetainCustomersNumber()
        {
            return BLL_Customer.Instance.GetRetainCustomersNumber();
        }
        /// <summary>
        /// 获取所有用户的可保留数量
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public int GetAllUserRetainCustomersNumber(GetRetainCustomersNumber_IN getRetainCustomersNumber_IN)
        {
            return BLL_Customer.Instance.GetAllUserRetainCustomersNumber(getRetainCustomersNumber_IN);
        }
        /// <summary>
        /// 将客户从私有池移入临时池
        /// </summary>
        /// <param name="customerIdList_IN"></param>
        [HttpPost]
        //[CustomerAuthCheck("CustomerIds",true)]
        public void MovePrivatePoolToTemporaryPool(CustomerIdList_IN customerIdList_IN)
        {
            BLL_Customer.Instance.MovePrivatePoolToTemporaryPool(customerIdList_IN.CustomerIds);
        }

        /// <summary>
        /// 将客户信息从私有池释放到公有池
        /// </summary>
        /// <param name="releasePrivateCustomer_IN"></param>
        [HttpPost]
        //[CustomerAuthCheck("CustomerIds", true)]
        public void ReleasePrivatePoolToPublicPool(ReleasePrivateCustomer_IN releasePrivateCustomer_IN)
        {
            BLL_Customer.Instance.ReleasePrivatePoolToPublicPool(releasePrivateCustomer_IN);
        }
        /// <summary>
        /// 对私有池的客户信息进行延期操作，延长保护截止日期。 20240712 延期判断是否还有保留名额；负名额的不能再延期
        /// </summary>
        /// <param name="customerIdList_IN"></param>
        [HttpPost]
        //[CustomerAuthCheck("CustomerIds", true)]
        public void DelayPrivatePool(CustomerIdList_IN customerIdList_IN)
        {
            BLL_Customer.Instance.DelayPrivatePool(customerIdList_IN.CustomerIds);
        }
        /// <summary>
        /// 移交私有客户
        /// </summary>
        /// <param name="transferPrivateCustomer_IN"></param>
        [HttpPost]
        //[CustomerAuthCheck("CustomerIds", true)]
        public void TransferPrivatePool(TransferPrivateCustomer_IN transferPrivateCustomer_IN)
        {
            BLL_Customer.Instance.TransferPrivatePool(transferPrivateCustomer_IN);
        }
        /// <summary>
        /// 移交私有客户(管理员)
        /// </summary>
        /// <param name="transferPrivateCustomer_IN"></param>
        [HttpPost]
        //[SkipRightCheck]
        [SkipRSAKey]
        //[SkipAuthCheck]
        //[SkipRecordLog]
        //[CustomerAuthCheck("CustomerIds", true)]
        public void TransferPrivatePoolAdmin(TransferPrivateCustomer_IN transferPrivateCustomer_IN)
        {
            BLL_Customer.Instance.TransferPrivatePoolAdmin(transferPrivateCustomer_IN);
        }
        /// <summary>
        /// 合并私有客户
        /// </summary>
        /// <param name="mergePrivateCustomer_IN"></param>
        [HttpPost]
        //[CustomerAuthCheck("CustomerIds", true)]
        //[CustomerAuthCheck]
        public void MergePrivatePoolApply([FromForm] MergePrivateCustomer_IN mergePrivateCustomer_IN)
        {
            BLL_Customer.Instance.MergePrivatePoolApply(mergePrivateCustomer_IN);
        }
        /// <summary>
        /// 添加子公司申请
        /// </summary>
        /// <param name="addSubCompany_IN"></param>
        [HttpPost]
        //[CustomerAuthCheck]
        public void AddSubCompanyPrivatePoolApply([FromForm] AddSubCompanyApply_IN addSubCompany_IN)
        {
            BLL_Customer.Instance.AddSubCompanyPrivatePoolApply(addSubCompany_IN);
        }

        /// <summary>
        /// 根据id获取客户基本信息
        /// </summary>
        /// <param name="customerId_IN"></param>
        /// <returns></returns>
        [HttpPost]
        //[CustomerAuthCheck]
        public CustomerInfo_OUT GetCustomerById(CommonCustomerId_IN customerId_IN)
        {
            return BLL_Customer.Instance.GetCustomerById(customerId_IN.CustomerId, customerId_IN.Temp);
        }
        /// <summary>
        /// 根据id获取客户审核记录（非管理人员用，验证客户权限）
        /// </summary>
        /// <param name="getCustomerAuditInfo_IN"></param>
        /// <returns></returns>
        [HttpPost]
        //[CustomerAuthCheck]
        public ApiTableOut<SearchCustomerAudit_OUT> GetCustomerAuditInfoById(GetCustomerAuditInfo_IN getCustomerAuditInfo_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.GetCustomerAuditInfoById(getCustomerAuditInfo_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 根据私有池客户Id获取公司名称列表
        /// </summary>
        /// <param name="customerId_IN"></param>
        /// <returns></returns>
        [HttpPost]
        //[CustomerAuthCheck]
        public List<GetSubcompanyList> GetSubcompanyListByCustomerId(CustomerId_IN customerId_IN)
        {
            return BLL_Customer.Instance.GetSubcompanyListByCustomerId(customerId_IN.CustomerId);
        }
        /// <summary>
        /// 根据私有池甲方公司获取子公司列表
        /// </summary>
        /// <param name="companyId_IN"></param>
        /// <returns></returns>
        [HttpPost]
        //[CustomerAuthCheck]
        public List<GetSubcompanyList> GetSubcompanyListByCompanyId(CompanyId_IN companyId_IN)
        {
            return BLL_Customer.Instance.GetSubcompanyListByCompanyId(companyId_IN.CompanyId);
        }
        /// <summary>
        /// 获取私有池客户产品信息列表
        /// </summary>
        /// <param name="customerId_IN"></param>
        /// <returns></returns>
        [HttpPost]
        //[CustomerAuthCheck]
        public List<CustomerContractList_Out> GetPrivateCustomerContractList(CustomerId_IN customerId_IN)
        {
            return BLL_Contract.Instance.SearchCustomerContractList(customerId_IN.CustomerId);
        }

        /// <summary>
        /// 根据客户Id获取相关记录（私有池/公有池）
        /// </summary>
        /// <param name="customerRecord_IN"></param>
        /// <returns></returns>
        [HttpPost]
        //[CustomerAuthCheck]
        public ApiTableOut<CustomerRecord_OUT> GetRecordsByCustomerId(CustomerRecord_IN customerRecord_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.GetRecordsByCustomerId(customerRecord_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 根据客户业绩摘要
        /// </summary>
        /// <param name="customerId_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [CustomerViewAuthCheck]
        public CustomerAchievementAbstract_OUT GetCustomerAchievementAbstract(CustomerId_IN customerId_IN)
        {
            return BLL_ContractAchievement.Instance.GetCustomerAchievementAbstract(customerId_IN.CustomerId);
        }
        /// <summary>
        /// 获取私有池客户数量及昨日变化
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public GetUserCustomerAbstract_OUT GetUserCustomerAbstrct()
        {
            return BLL_Customer.Instance.GetUserCustomerAbstrct();
        }
        #endregion

        #region 临时池
        /// <summary>
        /// 添加临时池客户
        /// </summary>
        /// <param name="addTempCustomer_IN"></param>
        [HttpPost]
        public void AddTemporaryPoolCustomer(AddTempCustomer_IN addTempCustomer_IN)
        {
            BLL_Customer.Instance.AddTemporaryPoolCustomer(addTempCustomer_IN);
        }
        /// <summary>
        /// 维护临时池客户信息（名称/代码不让改）
        /// </summary>
        /// <param name="updateTempCustomer_IN"></param>
        [HttpPost]
        //[CustomerAuthCheck]
        public void UpdateTemporaryPoolCustomer(UpdateTempCustomer_IN updateTempCustomer_IN)
        {
            BLL_Customer.Instance.UpdateTemporaryPoolCustomer(updateTempCustomer_IN);
        }
        /// <summary>
        /// 根据查询条件获取私有池客户信息列表
        /// </summary>
        /// <param name="queryTempCustomer_IN"></param>
        [HttpPost]
        public ApiTableOut<QueryTempCustomer_OUT> SearchTemporaryPoolCustomerList(QueryTempCustomer_IN queryTempCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchTemporaryPoolCustomerList(queryTempCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 移入私有池
        /// </summary>
        /// <param name="tempCustomerIdList_IN"></param>
        [HttpPost]
        public void MoveTemporaryPoolToPrivatePool(TempCustomerIdList_IN tempCustomerIdList_IN)
        {
            BLL_Customer.Instance.MoveTemporaryPoolToPrivatePool(tempCustomerIdList_IN.TempCustomerIds);
        }
        /// <summary>
        /// 将客户信息从临时池删除
        /// </summary>
        /// <param name="tempCustomerIdList_IN"></param>
        [HttpPost]
        public void DeleteTemporaryPool(TempCustomerIdList_IN tempCustomerIdList_IN)
        {
            BLL_Customer.Instance.DeleteTemporaryPool(tempCustomerIdList_IN.TempCustomerIds);
        }
        /// <summary>
        /// 获取公有池客户的领取历史记录
        /// </summary>
        /// <param name="customerId_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public List<QueryCustomerGetHistory_OUT> QueryCustomerGetHistory(CustomerId_IN customerId_IN)
        {
            return BLL_Customer.Instance.QueryCustomerGetHistory(customerId_IN.CustomerId);
        }
        /// <summary>
        /// 根据客户Id获取相关记录（临时池）
        /// </summary>
        /// <param name="tempCustomerRecord_IN"></param>
        /// <returns></returns>
        [HttpPost]
        //[CustomerAuthCheck]
        public ApiTableOut<TempCustomerRecord_OUT> GetTempRecordsByCustomerId(TempCustomerRecord_IN tempCustomerRecord_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.GetTempRecordsByCustomerId(tempCustomerRecord_IN, ref total);
            return GetApiTableOut(list, total);
        }
        #endregion

        #region 团队池

        /// <summary>
        /// 根据查询条件获取团队池客户信息列表
        /// </summary>
        /// <param name="queryTeamCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<QueryTeamCustomer_OUT> SearchTeamPoolCustomerList(QueryTeamCustomer_IN queryTeamCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchTeamPoolCustomerList(queryTeamCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 预验客户
        /// </summary>
        /// <param name="checkCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public PreInspectionCustomerResult_OUT PreInspectionCustomerList(CheckCustomer_IN checkCustomer_IN)
        {
            return BLL_Customer.Instance.PreInspectionCustomerList(checkCustomer_IN.CustomerName);
        }
        /// <summary>
        /// 预验客户-查看客户签约保留状态
        /// </summary>
        /// <param name="checkCustomer_In"></param>
        /// <returns></returns>
        [HttpPost]
        public string PreInspectionCustomerHasSigned(PreInspectionCustomerHasSigned_IN checkCustomer_In)
        {
            return BLL_Customer.Instance.PreInspectionCustomerHasSigned(checkCustomer_In);
        }
        /// <summary>
        /// 获取归属客户信息
        /// </summary>
        /// <param name="ascriptionCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<AscriptionCustomer_OUT> AscriptionCustomerList(AscriptionCustomer_IN ascriptionCustomer_IN)
        {
            return BLL_Customer.Instance.AscriptionCustomerList(ascriptionCustomer_IN);
        }
        ///// <summary>
        ///// 获取归属客户信息
        ///// </summary>
        ///// <param name="checkCustomer_IN"></param>
        ///// <returns></returns>
        //[HttpPost]
        //public List<AscriptionCustomer_OUT> AscriptionCustomerList(CheckCustomer_IN checkCustomer_IN)
        //{
        //    return BLL_Customer.Instance.AscriptionCustomerList(checkCustomer_IN.CustomerName);
        //}
        /// <summary>
        /// 移交离职人员客户
        /// </summary>
        /// <param name="transferPrivateCustomer_IN"></param>
        [HttpPost]
        public void TransferPrivatePoolTeam(TransferPrivateCustomerTeam_IN transferPrivateCustomer_IN)
        {
            BLL_Customer.Instance.TransferPrivatePoolTeam(transferPrivateCustomer_IN);
        }
        #endregion

        #region 公有池

        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="queryCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<QueryPublicCustomer_OUT> SearchPublicPoolCustomerList(QueryPublicCustomer_IN queryCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchPublicPoolCustomerList(queryCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 获取公有池客户产品信息列表
        /// </summary>
        /// <param name="customerId_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public List<CustomerContractList_Out> GetPublicCustomerContractList(CustomerId_IN customerId_IN)
        {
            return BLL_Contract.Instance.SearchCustomerContractList(customerId_IN.CustomerId);
        }
        /// <summary>
        /// 获取客户公有池剩余领取次数
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public int PublicPoolCollectSurplusTimes()
        {
            return BLL_Customer.Instance.PublicPoolCollectSurplusTimes();
        }
        /// <summary>
        /// 领取公有池客户信息到私有池
        /// </summary>
        /// <param name="customerIdList_IN"></param>
        [HttpPost]
        public void CollectPublicPoolToPrivatePool(CustomerIdList_IN customerIdList_IN)
        {
            BLL_Customer.Instance.CollectPublicPoolToPrivatePool(customerIdList_IN.CustomerIds);
        }
        /// <summary>
        /// 指派公有池客户信息到指定用户私有池
        /// </summary>
        /// <param name="assignPublicCustomer_IN"></param>
        [HttpPost]
        public void AssignPublicPoolToPrivatePool(AssignPublicCustomer_IN assignPublicCustomer_IN)
        {
            BLL_Customer.Instance.AssignPublicPoolToPrivatePool(assignPublicCustomer_IN);
        }
        #endregion

        #region 关系审核
        /// <summary>
        /// 审核合并关系
        /// </summary>
        /// <param name="mergeCustomerAudit_IN"></param>
        [HttpPost]
        public void MergePrivatePoolAudit(MergeCustomerAudit_IN mergeCustomerAudit_IN)
        {
            BLL_Customer.Instance.MergePrivatePoolAudit(mergeCustomerAudit_IN);
        }
        /// <summary>
        /// 审核子公司关系
        /// </summary>
        /// <param name="addSubCompanyAudit_IN"></param>
        [HttpPost]
        public void AddSubCompanyPrivatePoolAudit(AddSubCompanyAudit_IN addSubCompanyAudit_IN)
        {
            BLL_Customer.Instance.AddSubCompanyPrivatePoolAudit(addSubCompanyAudit_IN);
        }
        /// <summary>
        /// 查询审核列表
        /// </summary>
        /// <param name="searchCustomerAudit_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchCustomerAudit_OUT> SearchCustomerCompanyRelationshipAudit(SearchCustomerAudit_IN searchCustomerAudit_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchCustomerCompanyRelationshipAudit(searchCustomerAudit_IN.MappingTo<SearchCustomerAudit>(), ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 查询审核人列表
        /// </summary>
        /// <param name="searchCustomerApplicant_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetOrganizationWithUserTree_Out> SearchCustomerCompanyRelationshipApplicants(SearchCustomerApplicant_IN searchCustomerApplicant_IN)
        {
            return BLL_Customer.Instance.SearchCustomerCompanyRelationshipApplicants(searchCustomerApplicant_IN);
        }



        /// <summary>
        /// 查询客户审核历史记录
        /// </summary>
        /// <param name="getCustomerAuditRecord_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetCustomerAuditRecord_OUT> GetCustomerAuditRecords(GetCustomerAuditRecord_IN getCustomerAuditRecord_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.GetCustomerAuditRecords(getCustomerAuditRecord_IN, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 获取合并客户审核详情
        /// </summary>
        /// <param name="auditId_IN"></param>
        [HttpPost]
        public GetMergeCustomerAudit_OUT GetMergeCustomerAuditInfo(AuditId_IN auditId_IN)
        {
            return BLL_Customer.Instance.GetMergeCustomerAuditInfo(auditId_IN.AuditId);
        }

        /// <summary>
        /// 获取添加子公司审核详情
        /// </summary>
        /// <param name="auditId_IN"></param>
        [HttpPost]
        public GetSubCompanyAudit_OUT GetAddSubCompanyAuditInfo(AuditId_IN auditId_IN)
        {
            return BLL_Customer.Instance.GetAddSubCompanyAuditInfo(auditId_IN.AuditId);
        }
        /// <summary>
        /// 拆分子公司
        /// </summary>
        /// <param name="splitCompanyAudit_IN"></param>
        [HttpPost]
        public void SplitCompanyAudit(SplitAddSubCompanyAudit_IN splitCompanyAudit_IN)
        {
            BLL_Customer.Instance.SplitCompanyAudit(splitCompanyAudit_IN);
        }
        /// <summary>
        /// 拆分合并客户
        /// </summary>
        /// <param name="splitMergeCustomerAudit_IN"></param>
        [HttpPost]
        public void SplitMergeCustomerAudit(SplitMergeCustomerAudit_IN splitMergeCustomerAudit_IN)
        {
            BLL_Customer.Instance.SplitMergeCustomerAudit(splitMergeCustomerAudit_IN);
        }


        #endregion

        #region 资源池

        /// <summary>
        /// 导入客户
        /// </summary>
        /// <param name="importCustomer"></param>
        [HttpPost]
        public void ImportCustomer([FromForm] ImportCustomerClass importCustomer)
        {
            var data = BLL_Customer.Instance.ExcelCutomerIn(importCustomer.uFile);
            BLL_Customer.Instance.ImportExcelCustomer(data, EnumExcelCustomerSource.ZJ);

        }
        /// <summary>
        /// 根据查询条件获取导入客户列表
        /// </summary>
        /// <param name="searchImportCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchImportCustomer_OUT> SearchImportCustomerList(SearchImportCustomer_IN searchImportCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchImportCustomerList(searchImportCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 获取导入客户详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public SearchImportCustomer_OUT GetImportCustomerInfo(string id)
        {
            return BLL_Customer.Instance.GetImportCustomerInfo(id);
        }






        /// <summary>
        /// 根据查询条件获取中国出口企业客户信息列表（作废）
        /// </summary>
        /// <param name="searchExportCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchExportCustomer_OUT> SearchExportCustomerList(SearchExportCustomer_IN searchExportCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchExportCustomerList(searchExportCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 获取中出池客户详细信息（作废）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public SearchExportCustomer_OUT GetExportCustomer(string id)
        {
            return BLL_Customer.Instance.GetExportCustomer(id);
        }
        /// <summary>
        /// 根据查询条件获取中国出口企业客户信息列表
        /// </summary>
        /// <param name="searchExportCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchExportCustomer_OUT> SearchChinaExportCustomerList(SearchExportCustomer_IN searchExportCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchChinaExportCustomerList(searchExportCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 获取中国出口企业下拉列表
        /// </summary>
        /// <param name="searchExportCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public SearchExportCustomerSelections_OUT SearchExportCustomerSelections(SearchExportCustomerSelections_IN searchExportCustomer_IN)
        {
            return BLL_Customer.Instance.SearchExportCustomerSelections(searchExportCustomer_IN);
        }
        /// <summary>
        /// 获取中出池客户详细信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public SearchExportCustomer_OUT GetChinaExportCustomer(string id)
        {
            return BLL_Customer.Instance.GetChinaExportCustomer(id);
        }
        /// <summary>
        /// 领取中国出口客户信息到私有池
        /// </summary>
        /// <param name="customerExportIdList_IN"></param>
        [HttpPost]
        public void CollectExportToPrivatePool(CustomerExportIdList_IN customerExportIdList_IN)
        {
            BLL_Customer.Instance.CollectExportToPrivatePool(customerExportIdList_IN.CustomerExportIds);
        }
        /// <summary>
        /// 根据查询条件获取广交会客户信息列表（作废）
        /// </summary>
        /// <param name="searchFairCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchFairCustomer_OUT> SearchFairCustomerList(SearchFairCustomer_IN searchFairCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchFairCustomerList(searchFairCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 获取广交会客户信息（作废）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public SearchFairCustomer_OUT GetFairCustomer(string id)
        {
            return DbOpe_crm_customer_fair.Instance.GetFairCustomer(id);
        }
        /// <summary>
        /// 根据查询条件获取广交会客户信息列表
        /// </summary>
        /// <param name="searchFairCustomer_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchFairCustomer_OUT> SearchCantonFairCustomerList(SearchFairCustomer_IN searchFairCustomer_IN)
        {
            int total = 0;
            var list = BLL_Customer.Instance.SearchCantonFairCustomerList(searchFairCustomer_IN, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 获取广交会企业资源池下拉列表
        /// </summary>
        /// <param name="searchFairCustomerSelections_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public SearchFairCustomerSelections_OUT SearchFairCustomerSelections(SearchFairCustomerSelections_IN searchFairCustomerSelections_IN)
        {
            return BLL_Customer.Instance.SearchFairCustomerSelections(searchFairCustomerSelections_IN);
        }
        /// <summary>
        /// 获取广交会客户信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public SearchFairCustomer_OUT GetCantonFairCustomer(string id)
        {
            return BLL_Customer.Instance.GetCantonFairCustomer(id);
        }
        /// <summary>
        /// 领取广交会客户信息到私有池
        /// </summary>
        /// <param name="customerFairIdList_IN"></param>
        [HttpPost]
        public void CollectFairToPrivatePool(CustomerFairIdList_IN customerFairIdList_IN)
        {
            BLL_Customer.Instance.CollectFairToPrivatePool(customerFairIdList_IN.CustomerFairIds);
        }
        #endregion

        #region 其他

        /// <summary>
        /// 获取信用代码
        /// </summary>
        /// <param name="getCreditCode_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public GetCreditCode_OUT GetCreditCode(GetCreditCode_IN getCreditCode_IN)
        {
            //return BLL_Customer.Instance.GetCreditCode(getCreditCode_IN);
            return BLL_Customer.Instance.GetCreditCodeNew(getCreditCode_IN);
        }
        /// <summary>
        /// 获取客户下的公司列表
        /// </summary>
        /// <param name="customerId_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public List<Company_OUT> SearchCustomerCompanyList(CommonCustomerId_IN customerId_IN)
        {
            return BLL_Customer.Instance.SearchCustomerCompanyList(customerId_IN);
        }

        /// <summary>
        /// 获取地址信息（国家）
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        public List<Country_OUT> GetAddress_Country()
        {
            return LocalCache.LC_Address.CountryCache;
        }

        /// <summary>
        /// 获取地址信息（国家和地区）
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        public List<CountryAndArea_OUT> GetAddress_CountryAndArea()
        {
            return LocalCache.LC_Address.CountryAndAreaCache;
        }
        /// <summary>
        /// 获取地址信息（省）
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        public List<Province_OUT> GetAddress_Province()
        {
            return LocalCache.LC_Address.ProvinceCache
                .Where(w => w is { CountryID: 337, Extend: false or null }).ToList();
        }
        /// <summary>
        /// 获取地址信息（市）
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        public List<City_OUT> GetAddress_City()
        {
            return LocalCache.LC_Address.CityCache;
        }
        /// <summary>
        /// 获取主营产品和客户行业
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        public List<CustomerIndustry_OUT> GetCustomerIndustryAndMainProducts()
        {
            //return LocalCache.LC_MainProduct.CustomerIndustryCache;
            return DbOpe_sys_customerindustry.Instance.GetData();
        }
        /// <summary>
        /// 
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public string ExcuteContractOverDueCheck()
        {
            //var r = .DbMaintenance.GetTableInfoList
            //DbOpe_crm_customer_privatepool.Instance.ExcuteProtectCheck();
            //BLL_ContractService.Instance.ExcuteDelayProcessAppls();
            //return BLL_Customer.Instance.testsw1();
            //查找过期的客户
            //BLL_Customer.Instance.testsw1();
            BLL_Contract.Instance.AutoVoidContract();
            //DbOpe_crm_customer_temporarypool.Instance.GetCompanyExist(new List<CheckCompanyUniqueInfo>() { new CheckCompanyUniqueInfo() { 
            //    CompanyName = "AO \"RNG； АО \"РНГ\" ； RNG JSC"
            //} });;
            return null;
        }
        /// <summary>
        /// 
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public string ExcuteSchedue()
        {
            //var r = .DbMaintenance.GetTableInfoList
            //DbOpe_crm_customer_privatepool.Instance.ExcuteProtectCheck();
            //BLL_ContractService.Instance.ExcuteDelayProcessAppls();
            //return BLL_Customer.Instance.testsw1();
            //查找过期的客户
            //DbOpe_crm_customer_privatepool.Instance.ExcuteProtectCheck();
            DbOpe_crm_customer.Instance.ExcuteContractOverDueCheck();
            DbOpe_crm_customer_privatepool.Instance.ExcuteProtectCheck();
            DbOpe_crm_customer_privatepool.Instance.ExcuteLeaveUserProtectCheck();
            //BLL_PrivateService.Instance.UpdateGetBackDetail();
            //DbOpe_crm_customer_temporarypool.Instance.GetCompanyExist(new List<CheckCompanyUniqueInfo>() { new CheckCompanyUniqueInfo() { 
            //    CompanyName = "AO \"RNG； АО \"РНГ\" ； RNG JSC"
            //} });;
            return null;
        }
        /// <summary>
        /// 
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public string ExcuteLeaveUserProtectCheck()
        {
            //var r = .DbMaintenance.GetTableInfoList
            //DbOpe_crm_customer_privatepool.Instance.ExcuteProtectCheck();
            //BLL_ContractService.Instance.ExcuteDelayProcessAppls();
            //return BLL_Customer.Instance.testsw1();
            DbOpe_crm_customer_privatepool.Instance.ExcuteLeaveUserProtectCheck();
            //DbOpe_crm_customer_temporarypool.Instance.GetCompanyExist(new List<CheckCompanyUniqueInfo>() { new CheckCompanyUniqueInfo() { 
            //    CompanyName = "AO \"RNG； АО \"РНГ\" ； RNG JSC"
            //} });;
            return null;
        }



        /// <summary>
        /// 
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public string CompanyDM()
        {
            //var r = .DbMaintenance.GetTableInfoList
            //DbOpe_crm_customer_privatepool.Instance.ExcuteProtectCheck();
            //BLL_ContractService.Instance.ExcuteDelayProcessAppls();
            //return BLL_Customer.Instance.testsw1();
            //查找过期的客户
            DbOpe_crm_customer_subcompany.Instance.CompanyDM();
            //DbOpe_crm_customer_temporarypool.Instance.GetCompanyExist(new List<CheckCompanyUniqueInfo>() { new CheckCompanyUniqueInfo() { 
            //    CompanyName = "AO \"RNG； АО \"РНГ\" ； RNG JSC"
            //} });;
            return null;
        }

        /// <summary>
        /// 
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        //[SkipAuthCheck]
        [SkipRecordLog]
        public string CompanyToRelated()
        {
            DbOpe_crm_customer_subcompany.Instance.CompanyToRelated();
            return null;
        }
        #endregion

        #region 仲裁
        /*仲裁流程
         1.销售总监在员工管理页面勾选2个或多个员工，提交仲裁申请，需要在仲裁内容中填写公司名称
         2.后台人员 
            1）查看仲裁申请列表，只能查看未处理的仲裁申请
            2）根据仲裁内容搜索要仲裁的公司，搜索只能在仲裁人员的保留客户中搜索
            3）处理，仲裁后需要将业绩进行移交
         */


        /// <summary>
        /// 申请仲裁
        /// </summary>
        /// <param name="customerArbitrate_In"></param>
        [HttpPost]
        public void CustomerArbitrateApply(CustomerArbitrateApply_In customerArbitrate_In)
        {
            BLL_Customer.Instance.CustomerArbitrateApply(customerArbitrate_In);
        }
        /// <summary>
        /// 仲裁信息列表
        /// </summary>
        /// <param name="searchCustomerArbitrateApplyList_In"></param>
        /// <returns></returns>
        [HttpPost]
        public List<SearchCustomerArbitrateApplyList_Out> SearchCustomerArbitrateApplyList(SearchCustomerArbitrateApplyList_In searchCustomerArbitrateApplyList_In)
        {
            return DbOpe_crm_customer_arbitrate.Instance.SearchCustomerArbitrateApplyList(searchCustomerArbitrateApplyList_In);
        }

        /// <summary>
        /// 查询仲裁的客户列表
        /// </summary>
        /// <param name="query_In"></param>
        [HttpPost]
        public List<QueryCustomer_OUT> SearchArbitrateCompany(SearchArbitrateCompany_In query_In)
        {
            return BLL_Customer.Instance.SearchArbitrateCompany(query_In);
        }


        /// <summary>
        /// 获取客户业绩列表
        /// </summary>
        [HttpPost]
        public ApiTableOut<SearchContractReceiptRegisterList_Out> GetCustomerReceiptregisterList(GetCustomerReceiptregisterList_In getCustomerReceiptregisterList_In)
        {
            int total = 0;
            var list = DbOpe_crm_customer_arbitrate.Instance.GetCustomerReceiptregisterList(getCustomerReceiptregisterList_In, ref total);
            return GetApiTableOut(list, total);

        }

        /// <summary>
        /// 仲裁客户归属及业绩归属
        /// </summary>
        /// <param name="arbCusBelong_In"></param>
        [HttpPost]
        public void ArbitrateCustomerRight(ArbitrateCustomerBelong_In arbCusBelong_In)
        {
            BLL_Customer.Instance.ArbitrateCustomerRight(arbCusBelong_In);
        }

        /// <summary>
        /// 确认仲裁申请
        /// </summary>
        /// <param name="confirm_In"></param>
        [HttpPost]
        public void ConfirmArbitrateApply(ConfirmArbitrateApply_In confirm_In)
        {
            DbOpe_crm_customer_arbitrate.Instance.ConfirmArbitrateApply(confirm_In.Id);
        }
        #endregion

        #region 预验记录导出
        /// <summary>
        /// 查询预验记录（带分页）
        /// </summary>
        /// <param name="param">查询参数</param>
        /// <returns>分页结果</returns>
        [HttpPost]
        public ApiTableOut<PreInspectionExportRecord_OUT> SearchPreInspectionRecords(PreInspectionExportParam_IN param)
        {
            try
            {
                // 参数验证
                if (param == null)
                {
                    throw new Exception("参数不能为空");
                }
                
                int total = 0;
                PreInspectionOptimizer optimizer = new PreInspectionOptimizer();
                
                // 使用分页方法查询数据
                var records = optimizer.GetPreInspectionRecords(param, param.PageNumber ?? 1, param.PageSize ?? 10, ref total);
                
                // 返回分页结果
                return new ApiTableOut<PreInspectionExportRecord_OUT>
                {
                    Data = records,
                    Total = total
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"查询预验记录失败: {ex.Message}", ex);
                throw;
            }
        }
        
        /// <summary>
        /// 导出预验记录到Excel
        /// </summary>
        /// <param name="param">导出参数</param>
        /// <returns>Excel文件</returns>
        [HttpPost]
        public IActionResult ExportPreInspectionRecordsToExcel(PreInspectionExportParam_IN param)
        {
            try
            {
                if (param == null)
                {
                    return new JsonResult(new { success = false, message = "参数不能为空" });
                }
                
                // 调用优化器服务导出记录
                PreInspectionOptimizer optimizer = new PreInspectionOptimizer();
                var stream = optimizer.ExportPreInspectionRecords(param);
                
                // 生成文件名
                string fileName = $"预验记录导出_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                
                // 返回Excel文件
                return new FileStreamResult(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                {
                    FileDownloadName = fileName
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"导出预验记录到Excel失败: {ex.Message}", ex);
                return new JsonResult(new { success = false, message = "导出预验记录到Excel失败" });
            }
        }
        #endregion
    }
}
