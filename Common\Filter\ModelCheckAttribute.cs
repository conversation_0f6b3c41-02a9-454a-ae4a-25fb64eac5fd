﻿using CRM2_API.Model.System;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Org.BouncyCastle.Asn1.X509;
using LgyUtil;
using CRM2_API.Model.ControllersViewModel;
using Parlot.Fluent;
using CRM2_API.Common.AppSetting;

namespace CRM2_API.Common.Filter
{
    /// <summary>
    /// 模型验证过滤器
    /// </summary>
    public class ModelCheckAttribute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            if (context.ModelState.ErrorCount>0)
            {
                //进入此位置，证明前端传入的参数反序列化时发生错误
                if (AppSettings.Env == Enum_SystemSettingEnv.Product)
                    throw new ApiException("参数错误");
                else
                    throw new ApiException(((ModelStateEntry)context.ModelState.Values.Where(r => r.Errors.Count > 0).First()).SerializeNewtonJson());// + context.ModelState.Values.Where(r=>r.Errors.Count > 0).First().Errors.First().ErrorMessage);
            }

            //验证参数
            if (context.ActionArguments.HaveContent())
            {
                var ret = ModelCheckUtil.CheckModelResult(context.ActionArguments.First().Value);
                if (!ret.IsVaild)
                {
                    throw new ApiException(ret.ErrorMembers.First().ErrorMessage);
                }
                else
                {
                    //请求的第一个参数，如果属性有StringTrim特性，将标记的字符串进行trim操作
                    context.ActionArguments.First().Value.TrimAll();
                }
            }
        }
    }
}
