﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///分期信息表
    ///</summary>
    [SugarTable("sys_isstage")]
    public class Db_sys_isstage
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:中文服务费
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ServiceChargeZh {get;set;}

           /// <summary>
           /// Desc:英文服务费
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ServiceChargeEn_Usd { get;set;}

           /// <summary>
           /// Desc:英文服务费
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ServiceChargeEn_Eur { get; set; }

           /// <summary>
           /// Desc:英文服务费
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ServiceChargeEn_Cny { get; set; }

           /// <summary>
           /// Desc:中文付款期限
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PaymentTermZh {get;set;}

           /// <summary>
           /// Desc:英文付款期限
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PaymentTermEn {get;set;}

           /// <summary>
           /// Desc:中文服务交付
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ServiceDeliveryZh {get;set;}

           /// <summary>
           /// Desc:英文服务交付
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ServiceDeliveryEn {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
