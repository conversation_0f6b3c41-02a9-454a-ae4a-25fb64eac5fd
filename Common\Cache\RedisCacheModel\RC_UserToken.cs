﻿namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class UserToken
        {
            const string USERTOKENKEY = "UserToken_";
            /// <summary>
            /// 获取用户当前Redis缓存中的的UserToken
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            /// <returns></returns>
            public static string GetUserToken(string userId, bool IsMobile)
            {
                var key = USERTOKENKEY + userId + (IsMobile ? "_Mobile" : "");
                return RedisHelper.Get(key);
            }
            /// <summary>
            /// 验证接口调用传入的userToken是否是当前Redis缓存中的userToken
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            /// <param name="userToken"></param>
            /// <returns></returns>
            public static bool CheckUserToken(string userId, bool IsMobile, string userToken)
            {
                var key = USERTOKENKEY + userId + (IsMobile ? "_Mobile" : "");
                var redisUserToken = RedisHelper.Get(key);
                var retBool = true;

                if (redisUserToken == null)
                    retBool = false;
                else if (!redisUserToken.Equals(userToken))
                    retBool = false;
                if (!retBool)
                    LogUtil.AddLog("接口token验证失败。UserId =" + userId + ", redisToken = " + redisUserToken + ", headerToken = " + userToken);

                return retBool;

            }
            /// <summary>
            /// 保存用户的UserToken
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            /// <param name="userToken"></param>
            public static void SaveUserToken(string userId, bool IsMobile, string userToken)
            {
                var key = USERTOKENKEY + userId + (IsMobile ? "_Mobile" : "");
                RedisHelper.Set(key, userToken);
            }
            /// <summary>
            /// 删除用户的UserToken
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            public static void DeleteUserToken(string userId, bool IsMobile)
            {
                var key = USERTOKENKEY + userId + (IsMobile ? "_Mobile" : "");
                RedisHelper.Del(key);
            }

        }

    }
}
