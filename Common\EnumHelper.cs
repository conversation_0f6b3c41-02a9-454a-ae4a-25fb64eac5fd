﻿using System.ComponentModel;
using System.Reflection;

namespace CRM2_API.Common
{
    public static class EnumHelper
    {
                /// <summary>
        /// 安全地获取枚举的描述信息，如果枚举值无效则返回空字符串
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="value">枚举值</param>
        /// <returns>枚举描述或空字符串</returns>
        public static string GetEnumDescription<T>(object value) where T : struct, IConvertible
        {
            if (value == null)
            {
                return string.Empty;
            }

            try
            {
                // 尝试将值转换为枚举类型
                if (typeof(T).IsEnum && global::System.Enum.IsDefined(typeof(T), value))
                {
                    T enumValue = (T)value;
                    FieldInfo field = typeof(T).GetField(enumValue.ToString());
                    if (field != null)
                    {
                        DescriptionAttribute attribute = Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) as DescriptionAttribute;
                        if (attribute != null)
                        {
                            return attribute.Description;
                        }
                        return enumValue.ToString();
                    }
                }

                // 尝试通过数值转换为枚举类型
                if (typeof(T).IsEnum && value is IConvertible convertible)
                {
                    try
                    {
                        int intValue = convertible.ToInt32(null);
                        if (global::System.Enum.IsDefined(typeof(T), intValue))
                        {
                            T enumValue = (T)global::System.Enum.ToObject(typeof(T), intValue);
                            FieldInfo field = typeof(T).GetField(enumValue.ToString());
                            if (field != null)
                            {
                                DescriptionAttribute attribute = Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) as DescriptionAttribute;
                                if (attribute != null)
                                {
                                    return attribute.Description;
                                }
                                return enumValue.ToString();
                            }
                        }
                    }
                    catch
                    {
                        // 忽略转换错误
                    }
                }
            }
            catch
            {
                // 忽略所有错误
            }

            return string.Empty;
        }

    }
}
