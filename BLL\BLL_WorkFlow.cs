﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using LgyUtil;
using SqlSugar;
using System.Data;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Reflection;
using System.Security.Cryptography.Xml;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_WorkflowPending;

namespace CRM2_API.BLL
{
    public class BLL_WorkFlow : BaseBLL<BLL_WorkFlow>
    {
        public void AddWorkFlow<T, F>(string workFlowName, string dataId, T dataObject, F showObject, string assigner, string dataRemark, string dataState, string dataOperate)
        {
            string formId = Com_SysForm.Instance.FormId;
            //查找该节点的上级节点，是开始节点
            List<Db_sys_workflow_node> StartWorkFlowNodeList = DbOpe_sys_workflow_node.Instance.GetStartWorkFlowNodeByWorkFlowNameAndFormId(workFlowName, formId);
            //查找该节点下的流程条件信息
            List<Db_sys_workflow_node> workflowNodes = DbOpe_sys_workflow_node.Instance.GetWorkFlowNodeByWorkFlowNameAndFormId(workFlowName, formId);
            foreach (Db_sys_workflow_node workflowNode in workflowNodes)
            {
                #region 反射查找
                //string flowCondition = workflowNode.FlowCondition;
                //string key = flowCondition.Split('=')[0];
                //string value= flowCondition.Split('=')[1];
                //Type type = dataObject.GetType();//获取类型
                //PropertyInfo ConditionKey = type.GetProperty(key);
                //String ConditionValue = ConditionKey.GetValue(dataObject).ToString();
                #endregion
                #region 转DataTable查找
                //string flowCondition = workflowNode.FlowCondition;
                //DataTable dt = Tools.ToDataTable<T>(new List<T>() { dataObject });
                //if (dt.Select(flowCondition).Length > 0)
                //{
                //    
                //}
                #endregion
                //根据流程条件和数据,找到符合条件的流程条件信息
                string flowCondition = workflowNode.FlowCondition;
                List<T> datalist = new List<T>() { dataObject };
                var matchConditionCount = 0;
                try
                {
                    matchConditionCount = datalist.AsQueryable().Where(flowCondition).ToList().Count();
                }
                catch (Exception ex)
                {
                    continue;
                }
                if (matchConditionCount > 0)
                {
                    //添加流程实例
                    string workflowInstanceId = Guid.NewGuid().ToString();
                    //验证是否存在流程实例
                    Db_sys_workflow_instance instance = DbOpe_sys_workflow_instance.Instance.GetWorkflowInstanceByWorkFlowIdAndDataId(workflowNode.WorkFlowId, dataId);
                    if (instance == null)
                    {
                        //验证是否为起始节点,如果是流程开始则创建流程实例,否则查找已存在的流程实例
                        if (StartWorkFlowNodeList.Where(r => r.NextFormId == workflowNode.FormId).Any())
                        {
                            //找到符合条件，创建流程实例
                            DbOpe_sys_workflow_instance.Instance.AddWorkflowInstance(workflowInstanceId, dataId, workflowNode.WorkFlowId, UserId);
                        }
                        else
                        {
                            throw new ApiException("未找到流程实例");
                        }
                    }
                    else
                    {
                        workflowInstanceId = instance.Id;
                    }

                    //记录流程实例
                    string workflowInstanceNodeId = Guid.NewGuid().ToString();
                    DbOpe_sys_workflow_instance_node.Instance.AddWorkflowInstanceNode(workflowInstanceNodeId, workflowInstanceId, workflowNode.WorkFlowId, workflowNode.Id, formId, dataId, UserId, workflowNode.NextFormId, UserId, dataRemark, dataState, dataOperate);
                    List<Db_sys_workflow_instance_node> instanceNodes = DbOpe_sys_workflow_instance_node.Instance.GetWorkFlowInstanceNodeLeafNodeByDataIdAndNextFormId(workflowInstanceId, workflowNode.WorkFlowId, dataId, formId);
                    foreach (Db_sys_workflow_instance_node node in instanceNodes)
                    {
                        DbOpe_sys_workflow_instance_node.Instance.UpdateNextWorkFlowInstanceNode(node.Id, workflowInstanceNodeId);
                    }

                    //发送待办信息
                    string workflowPendingId = Guid.NewGuid().ToString();
                    string pendingUrl = String.Format(workflowNode.FlowUrl, dataId);
                    string pendingContent = Tools.Format(workflowNode.InfoTemp, showObject);


                    //根据接收类型设置接收人和接收角色
                    string recipientRoleId = Guid.Empty.ToString();
                    string recipientUserId = Guid.Empty.ToString();
                    List<string> recipientRoleIdList = new List<string>();
                    List<string> recipientUserIdList = new List<string>();
                    if (workflowNode.RecipientType == EnumRecipientType.RoleId.ToInt() || workflowNode.RecipientType == EnumRecipientType.UserId.ToInt())
                    {
                        if (workflowNode.RecipientType == EnumRecipientType.RoleId.ToInt())
                        {
                            recipientRoleId = workflowNode.RoleId;
                            List<Db_sys_workflow_node_roleid> list = DbOpe_sys_workflow_node_roleid.Instance.GetDataList(r => r.WorkFlowNodeId == workflowNode.Id);
                            recipientRoleIdList = list.Select(r => r.RoleId).ToList();
                        }
                        else if (workflowNode.RecipientType == EnumRecipientType.UserId.ToInt())
                        {
                            recipientUserId = workflowNode.UserId;
                            List<Db_sys_workflow_node_userid> list = DbOpe_sys_workflow_node_userid.Instance.GetDataList(r => r.WorkFlowNodeId == workflowNode.Id);
                            recipientUserIdList = list.Select(r => r.UserId).ToList();
                        }
                        DbOpe_sys_workflow_pending.Instance.AddWorkflowPending(workflowPendingId, workflowNode.WorkFlowId, workflowNode.Id, formId, UserId, dataId, recipientRoleId, recipientUserId, pendingUrl, pendingContent, recipientRoleIdList, recipientUserIdList);
                    }
                    else if (workflowNode.RecipientType == EnumRecipientType.PreviousNodeHandler.ToInt())
                    {
                        //根据流程名、流程节点、FormID、DataID，找到上一级节点处理人
                        //List<Db_sys_workflow_instance_node> instanceNodes = DbOpe_sys_workflow_instance_node.Instance.GetWorkFlowInstanceNodeLeafNodeByDataIdAndNextFormId(workflowInstanceId, workflowNode.WorkFlowId, dataId, formId);
                        foreach (Db_sys_workflow_instance_node node in instanceNodes)
                        {
                            recipientUserId = node.OperationUserId;
                            recipientUserIdList.Add(node.OperationUserId);
                            DbOpe_sys_workflow_pending.Instance.AddWorkflowPending(Guid.NewGuid().ToString(), workflowNode.WorkFlowId, workflowNode.Id, formId, UserId, dataId, recipientRoleId, recipientUserId, pendingUrl, pendingContent, recipientRoleIdList, recipientUserIdList);
                        }
                    }
                    else if (workflowNode.RecipientType == EnumRecipientType.Assigner.ToInt())
                    {
                        recipientUserId = assigner;
                        recipientUserIdList.Add(assigner);
                        DbOpe_sys_workflow_pending.Instance.AddWorkflowPending(workflowPendingId, workflowNode.WorkFlowId, workflowNode.Id, formId, UserId, dataId, recipientRoleId, recipientUserId, pendingUrl, pendingContent, recipientRoleIdList, recipientUserIdList);
                    }

                    //处理待办信息（更新上一条待办信息的执行人）
                    foreach (Db_sys_workflow_instance_node node in instanceNodes)
                    {
                        DbOpe_sys_workflow_pending.Instance.UpdateWorkflowPendingHandle(node.WorkFlowId, node.WorkFlowNodeId, node.FormId, dataId, UserId, dataState);
                    }

                    ////只要符合一个条件其他的就不在执行,不写就是一个节点多条件流出
                    //break;
                }
            }
        }

        public void AddWorkFlowNew<T, F>(string workFlowName, string dataId, T dataObject, F showObject, string assigner, string dataRemark, string dataState, string dataOperate, string formId, string user_Id)
        {
            //string formId = Com_SysForm.Instance.FormId;
            //查找该节点的上级节点，是开始节点
            List<Db_sys_workflow_node> StartWorkFlowNodeList = DbOpe_sys_workflow_node.Instance.GetStartWorkFlowNodeByWorkFlowNameAndFormId(workFlowName, formId);
            //查找该节点下的流程条件信息
            List<Db_sys_workflow_node> workflowNodes = DbOpe_sys_workflow_node.Instance.GetWorkFlowNodeByWorkFlowNameAndFormId(workFlowName, formId);
            foreach (Db_sys_workflow_node workflowNode in workflowNodes)
            {
                #region 反射查找
                //string flowCondition = workflowNode.FlowCondition;
                //string key = flowCondition.Split('=')[0];
                //string value= flowCondition.Split('=')[1];
                //Type type = dataObject.GetType();//获取类型
                //PropertyInfo ConditionKey = type.GetProperty(key);
                //String ConditionValue = ConditionKey.GetValue(dataObject).ToString();
                #endregion
                #region 转DataTable查找
                //string flowCondition = workflowNode.FlowCondition;
                //DataTable dt = Tools.ToDataTable<T>(new List<T>() { dataObject });
                //if (dt.Select(flowCondition).Length > 0)
                //{
                //    
                //}
                #endregion
                //根据流程条件和数据,找到符合条件的流程条件信息
                string flowCondition = workflowNode.FlowCondition;
                List<T> datalist = new List<T>() { dataObject };
                var matchConditionCount = 0;
                try
                {
                    matchConditionCount = datalist.AsQueryable().Where(flowCondition).ToList().Count();
                }
                catch (Exception ex)
                {
                    continue;
                }
                if (matchConditionCount > 0)
                {
                    //添加流程实例
                    string workflowInstanceId = Guid.NewGuid().ToString();
                    //验证是否存在流程实例
                    Db_sys_workflow_instance instance = DbOpe_sys_workflow_instance.Instance.GetWorkflowInstanceByWorkFlowIdAndDataId(workflowNode.WorkFlowId, dataId);
                    if (instance == null)
                    {
                        //验证是否为起始节点,如果是流程开始则创建流程实例,否则查找已存在的流程实例
                        if (StartWorkFlowNodeList.Where(r => r.NextFormId == workflowNode.FormId).Any())
                        {
                            //找到符合条件，创建流程实例
                            DbOpe_sys_workflow_instance.Instance.AddWorkflowInstance(workflowInstanceId, dataId, workflowNode.WorkFlowId, user_Id);
                        }
                        else
                        {
                            throw new ApiException("未找到流程实例");
                        }
                    }
                    else
                    {
                        workflowInstanceId = instance.Id;
                    }

                    //记录流程实例
                    string workflowInstanceNodeId = Guid.NewGuid().ToString();
                    DbOpe_sys_workflow_instance_node.Instance.AddWorkflowInstanceNode(workflowInstanceNodeId, workflowInstanceId, workflowNode.WorkFlowId, workflowNode.Id, formId, dataId, user_Id, workflowNode.NextFormId, user_Id, dataRemark, dataState, dataOperate);
                    List<Db_sys_workflow_instance_node> instanceNodes = DbOpe_sys_workflow_instance_node.Instance.GetWorkFlowInstanceNodeLeafNodeByDataIdAndNextFormId(workflowInstanceId, workflowNode.WorkFlowId, dataId, formId);
                    foreach (Db_sys_workflow_instance_node node in instanceNodes)
                    {
                        DbOpe_sys_workflow_instance_node.Instance.UpdateNextWorkFlowInstanceNode(node.Id, workflowInstanceNodeId);
                    }

                    //发送待办信息
                    string workflowPendingId = Guid.NewGuid().ToString();
                    string pendingUrl = String.Format(workflowNode.FlowUrl, dataId);
                    string pendingContent = Tools.Format(workflowNode.InfoTemp, showObject);


                    //根据接收类型设置接收人和接收角色
                    string recipientRoleId = Guid.Empty.ToString();
                    string recipientUserId = Guid.Empty.ToString();
                    List<string> recipientRoleIdList = new List<string>();
                    List<string> recipientUserIdList = new List<string>();
                    if (workflowNode.RecipientType == EnumRecipientType.RoleId.ToInt() || workflowNode.RecipientType == EnumRecipientType.UserId.ToInt())
                    {
                        if (workflowNode.RecipientType == EnumRecipientType.RoleId.ToInt())
                        {
                            recipientRoleId = workflowNode.RoleId;
                            List<Db_sys_workflow_node_roleid> list = DbOpe_sys_workflow_node_roleid.Instance.GetDataList(r => r.WorkFlowNodeId == workflowNode.Id);
                            recipientRoleIdList = list.Select(r => r.RoleId).ToList();
                        }
                        else if (workflowNode.RecipientType == EnumRecipientType.UserId.ToInt())
                        {
                            recipientUserId = workflowNode.UserId;
                            List<Db_sys_workflow_node_userid> list = DbOpe_sys_workflow_node_userid.Instance.GetDataList(r => r.WorkFlowNodeId == workflowNode.Id);
                            recipientUserIdList = list.Select(r => r.UserId).ToList();
                        }
                        DbOpe_sys_workflow_pending.Instance.AddWorkflowPending(workflowPendingId, workflowNode.WorkFlowId, workflowNode.Id, formId, user_Id, dataId, recipientRoleId, recipientUserId, pendingUrl, pendingContent, recipientRoleIdList, recipientUserIdList);
                    }
                    else if (workflowNode.RecipientType == EnumRecipientType.PreviousNodeHandler.ToInt())
                    {
                        //根据流程名、流程节点、FormID、DataID，找到上一级节点处理人
                        //List<Db_sys_workflow_instance_node> instanceNodes = DbOpe_sys_workflow_instance_node.Instance.GetWorkFlowInstanceNodeLeafNodeByDataIdAndNextFormId(workflowInstanceId, workflowNode.WorkFlowId, dataId, formId);
                        foreach (Db_sys_workflow_instance_node node in instanceNodes)
                        {
                            recipientUserId = node.OperationUserId;
                            recipientUserIdList.Add(node.OperationUserId);
                            DbOpe_sys_workflow_pending.Instance.AddWorkflowPending(Guid.NewGuid().ToString(), workflowNode.WorkFlowId, workflowNode.Id, formId, user_Id, dataId, recipientRoleId, recipientUserId, pendingUrl, pendingContent, recipientRoleIdList, recipientUserIdList);
                        }
                    }
                    else if (workflowNode.RecipientType == EnumRecipientType.Assigner.ToInt())
                    {
                        recipientUserId = assigner;
                        recipientUserIdList.Add(assigner);
                        DbOpe_sys_workflow_pending.Instance.AddWorkflowPending(workflowPendingId, workflowNode.WorkFlowId, workflowNode.Id, formId, user_Id, dataId, recipientRoleId, recipientUserId, pendingUrl, pendingContent, recipientRoleIdList, recipientUserIdList);
                    }

                    //处理待办信息（更新上一条待办信息的执行人）
                    foreach (Db_sys_workflow_instance_node node in instanceNodes)
                    {
                        DbOpe_sys_workflow_pending.Instance.UpdateWorkflowPendingHandle(node.WorkFlowId, node.WorkFlowNodeId, node.FormId, dataId, user_Id, dataState);
                    }

                    ////只要符合一个条件其他的就不在执行,不写就是一个节点多条件流出
                    //break;
                }
            }
        }


        public void AddWorkFlow<T>(string workFlowName, string dataId, T dataObject, string dataRemark, string dataState, string dataOperate)
        {
            AddWorkFlow(workFlowName, dataId, dataObject, dataObject, "", dataRemark, dataState, dataOperate);
        }

        public void AddWorkFlow<T, F>(string workFlowName, string dataId, T dataObject, F showObject, string dataRemark, string dataState, string dataOperate)
        {
            AddWorkFlow(workFlowName, dataId, dataObject, showObject, "", dataRemark, dataState, dataOperate);
        }

        public void VoidWorkflowPending(string workFlowName, string dataId, string stateName)
        {
            DbOpe_sys_workflow_instance_node.Instance.TransDeal(() =>
            {
                DbOpe_sys_workflow_pending.Instance.UpdateWorkflowPendingNotViewedStatus(workFlowName, dataId, EnumWorkflowPendingState.Cancel.ToInt(), stateName);
                List<Db_sys_workflow_instance_node> instanceNodes = DbOpe_sys_workflow_instance_node.Instance.GetWorkFlowInstanceNodeLeafNodeByDataIdAndWorkFlowName(workFlowName, dataId);
                foreach (Db_sys_workflow_instance_node node in instanceNodes)
                {
                    //作废一条就够了
                    DbOpe_sys_workflow_instance_operate.Instance.AddWorkFlowInstanceOperate(node.WorkFlowInstanceId, node.Id, node.WorkFlowId, node.WorkFlowNodeId, node.FormId, node.DataId, UserId, node.NextFormId, UserId, "", stateName, "作废");
                    break;
                }
            });
        }

        public void VoidWorkflowPending(string workFlowName, string dataId, string stateName, string userId)
        {
            DbOpe_sys_workflow_instance_node.Instance.TransDeal(() =>
            {
                DbOpe_sys_workflow_pending.Instance.UpdateWorkflowPendingNotViewedStatus(workFlowName, dataId, EnumWorkflowPendingState.Cancel.ToInt(), stateName);
                List<Db_sys_workflow_instance_node> instanceNodes = DbOpe_sys_workflow_instance_node.Instance.GetWorkFlowInstanceNodeLeafNodeByDataIdAndWorkFlowName(workFlowName, dataId);
                foreach (Db_sys_workflow_instance_node node in instanceNodes)
                {
                    //作废一条就够了
                    DbOpe_sys_workflow_instance_operate.Instance.AddWorkFlowInstanceOperate(node.WorkFlowInstanceId, node.Id, node.WorkFlowId, node.WorkFlowNodeId, node.FormId, node.DataId, userId, node.NextFormId, userId, "", stateName, "作废");
                    break;
                }
            });
        }

        public void CancelWorkflowPending(string workFlowName, string dataId, string stateName)
        {
            DbOpe_sys_workflow_instance_node.Instance.TransDeal(() =>
            {
                DbOpe_sys_workflow_pending.Instance.UpdateWorkflowPendingNotViewedStatus(workFlowName, dataId, EnumWorkflowPendingState.Cancel.ToInt(), stateName);
                List<Db_sys_workflow_instance_node> instanceNodes = DbOpe_sys_workflow_instance_node.Instance.GetWorkFlowInstanceNodeLeafNodeByDataIdAndWorkFlowName(workFlowName, dataId);
                foreach (Db_sys_workflow_instance_node node in instanceNodes)
                {
                    //作废一条就够了
                    DbOpe_sys_workflow_instance_operate.Instance.AddWorkFlowInstanceOperate(node.WorkFlowInstanceId, node.Id, node.WorkFlowId, node.WorkFlowNodeId, node.FormId, node.DataId, UserId, node.NextFormId, UserId, "", stateName, "撤销");
                    break;
                }
            });
        }

        public void CancelWorkflowPending<T>(string workFlowName, string dataId, string stateName, T dataObject)
        {
            DbOpe_sys_workflow_instance_node.Instance.TransDeal(() =>
            {
                List<Db_sys_workflow_node> workflowNodeList = GetWorkFlowNodeByWorkFlowName(workFlowName, dataObject);
                List<string> nextFormId = workflowNodeList.Select(r => r.NextFormId).ToList();
                DbOpe_sys_workflow_pending.Instance.UpdateWorkflowPendingNotViewedStatus(workFlowName, dataId, EnumWorkflowPendingState.Cancel.ToInt(), stateName);
                CancelWorkflowInstanceNode(workFlowName, dataId, stateName, nextFormId);
            });
        }

        public void CancelWorkflowInstanceNode(string workFlowName, string dataId, string stateName, List<string> nextFormId)
        {
            List<Db_sys_workflow_instance_node> instanceNodes = DbOpe_sys_workflow_instance_node.Instance.GetWorkFlowInstanceNodeLeafNodeByDataIdAndWorkFlowName(workFlowName, dataId, nextFormId);
            foreach (Db_sys_workflow_instance_node node in instanceNodes)
            {
                //多流程并发会显示多个撤销记录，如果要只显示一个，这直接退出
                DbOpe_sys_workflow_instance_node.Instance.UpdateNextWorkFlowInstanceNodeLeafById(node.Id);
                DbOpe_sys_workflow_instance_node.Instance.UpdateNextWorkFlowInstanceNodeIsLeafByNextWorkFlowInstanceNodeId(node.Id);
                DbOpe_sys_workflow_instance_operate.Instance.AddWorkFlowInstanceOperate(node.WorkFlowInstanceId, node.Id, node.WorkFlowId, node.WorkFlowNodeId, node.FormId, node.DataId, UserId, node.NextFormId, UserId, "", stateName, "撤销");
            }
        }

        private List<Db_sys_workflow_node> GetWorkFlowNodeByWorkFlowName<T>(string workFlowName, T dataObject)
        {
            List<Db_sys_workflow_node> result = new List<Db_sys_workflow_node>();
            List<Db_sys_workflow_node> workflowNodes = DbOpe_sys_workflow_node.Instance.GetWorkFlowNodeByWorkFlowName(workFlowName);
            foreach (Db_sys_workflow_node workflowNode in workflowNodes)
            {
                string flowCondition = workflowNode.FlowCondition;
                List<T> datalist = new List<T>() { dataObject };
                if (flowCondition.IsNotNullOrEmpty())
                {
                    string key = flowCondition.Split('=')[0];
                    Type type = dataObject.GetType();
                    PropertyInfo ConditionKey = type.GetProperty(key);
                    if (ConditionKey != null)
                    {
                        if (datalist.AsQueryable().Where(flowCondition).ToList().Count() > 0)
                        {
                            result.Add(workflowNode);
                        }
                    }
                }
            }
            return result;
        }

        public bool IsHaveWorkflowPending(string dataId)
        {
            return DbOpe_sys_workflow_pending.Instance.GetWorkflowPendingByDataIdAndUserId(dataId, UserId).Count() > 0;
        }

        public bool IsHaveWorkflowPending(List<string> dataIds)
        {
            var l = DbOpe_sys_workflow_pending.Instance.GetWorkflowPendingByDataIdAndUserId(dataIds, UserId);
            return l.Select(t => t.DataId).Distinct().ToList().Count == dataIds.Count;
        }

        /// <summary>
        /// 获取用户待办信息
        /// </summary>
        public List<WorkflowPending_Out> GetWorkflowPending()
        {
            return DbOpe_sys_workflow_pending.Instance.GetWorkflowPendingByUserId(UserId);
        }

        /// <summary>
        /// 变更用户待办信息未查看为已查看
        /// </summary>
        public void UpdateWorkflowPendingStateToViewed(string id)
        {
            DbOpe_sys_workflow_pending.Instance.UpdateWorkflowPendingStateToViewed(id, UserId);
        }

        /// <summary>
        /// 获取用户已处理信息
        /// </summary>
        public List<WorkflowInfo_Out> GetWorkflowHandleInfo(string dataId, string workFlowName)
        {
            return DbOpe_sys_workflow_pending.Instance.GetWorkflowHandleInfo(dataId, workFlowName);
        }

        /// <summary>
        /// 获取用户已处理信息以及业务信息
        /// </summary>
        public List<WorkflowBusinessInfo_Out> GetWorkflowBusinessInfo(string dataId, string workFlowName)
        {
            List<WorkflowBusinessInfo_Out> result = new List<WorkflowBusinessInfo_Out>();
            //List<WorkflowBusinessInfo_Out> listRevoke = DbOpe_sys_workflow_pending.Instance.GetRevokeWorkflowPendingByUserId(dataId, workFlowName);
            List<WorkflowBusinessInfo_Out> listOperate = DbOpe_sys_workflow_instance_operate.Instance.GetWorkFlowInstanceOperate(dataId, workFlowName);
            List<WorkflowBusinessInfo_Out> list = DbOpe_sys_workflow_instance_node.Instance.GetWorkflowBusinessInfo(dataId, workFlowName);

            result.AddRange(listOperate);
            result.AddRange(list);
            //result = result.OrderBy(r => Convert.ToDateTime(r.CreateDate)).ToList();

            result = result.OrderByDescending(r => r.OrderDate).ToList();
            return result;
        }

        /// <summary>
        /// 查询用户已处理信息
        /// </summary>
        public ApiTableOut<WorkflowInfo_Out> SearchContractWorkflowHandleInfo(SearchWorkflowHandleInfo_In searchWorkflowHandleInfoIn)
        {
            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(searchWorkflowHandleInfoIn.dataId, true);
            if (contract == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            int total = 0;
            return new ApiTableOut<WorkflowInfo_Out> { Data = DbOpe_sys_workflow_pending.Instance.SearchWorkflowHandleInfo(searchWorkflowHandleInfoIn, ref total), Total = total };
        }
    }
}
