﻿using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.System;
using Newtonsoft.Json.Linq;

namespace CRM2_API.Common.JWT
{
    /// <summary>
    /// token中的模型
    /// </summary>
    public class TokenModel
    {
        public string id { get; set; }
        public string name { get; set; }
        public int suppled { get; set; }

        #region 获取token和模型的方法，当前请求的所有线程，都可以直接使用这个对象
        private static readonly AsyncLocal<string> token = new();
        /// <summary>
        /// 前端传入的token
        /// </summary>
        public static string Token
        {
            get { return token.Value ?? ""; }
        }

        private static readonly AsyncLocal<TokenModel> model = new();

        /// <summary>
        /// token中的模型
        /// </summary>
        public static TokenModel Instance
        {
            get { return model.Value ?? new TokenModel();}
        }

        public static void SetTokenModel(TokenModel tokenModel)
        {
            model.Value = tokenModel;
        }

        /// <summary>
        /// 设置token信息，解析token是，会进行验证
        /// </summary>
        /// <param name="headerToken"></param>
        /// <param name="IsMobile"></param>
        /// <param name="IsSkipUserSupplementInfo"></param>
        public static void SetAndCheckTokenInfo(string headerToken, bool IsMobile, bool IsSkipUserSupplementInfo)
        {
            token.Value = headerToken;
            model.Value = JwtHelper.GetTokenModel(token.Value);
            if (!RedisCache.UserToken.CheckUserToken(model.Value.id, IsMobile, headerToken))
                throw new ApiException("您的用户已在异地登录，请重新登录");
            if(!(model.Value.suppled == 1) && !IsSkipUserSupplementInfo)
                throw new ApiException("请完善用户信息");
        }
        #endregion
    }
}
