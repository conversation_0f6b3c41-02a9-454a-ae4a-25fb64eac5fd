﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///产品规则表_仅支持中国
    ///</summary>
    [SugarTable("crm_product_rules_supportchina")]
    public class Db_crm_product_rules_supportchina
    {
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:产品规则表Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ProductRulesId { get; set; }

        /// <summary>
        /// Desc:国家名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Name { get; set; }

        /// <summary>
        /// Desc:国家id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? Sid { get; set; }

        /// <summary>
        /// Desc:开放截止日期
        /// Default:
        /// Nullable:True
        /// </summary>       
        public DateTime? OpenDeadline4HK_MO_TWN { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
