using CRM2_API.Common.AppSetting;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        public class DingTalkJsapiTicket
        {
            const string JSAPI_TICKET_KEY = "DingTalkJsapiTicket";
            /// <summary>
            /// 获取 JSAPI Ticket
            /// </summary>
            /// <returns>JSAPI Ticket</returns>
            public static string Get()
            {
                var key = JSAPI_TICKET_KEY;
                return RedisHelper.Get(key);
            }
            
            /// <summary>
            /// 设置 JSAPI Ticket
            /// </summary>
            /// <param name="ticket">JSAPI Ticket</param>
            /// <param name="ts">过期时间</param>
            public static void Set(string ticket, TimeSpan ts)
            {
                var key = JSAPI_TICKET_KEY;
                RedisHelper.Set(key, ticket, ts);
            }
        }
    }
} 