﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoicingDetails;

namespace CRM2_API.Controllers
{
    [Description("合同发票开票明细信息")]
    public class ContractInvoicingDetailsController : MyControllerBase
    {
        public ContractInvoicingDetailsController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 添加合同发票开票明细信息
        /// </summary>
        [HttpPost]
        public void AddInvoicingDetails(List<AddInvoicingDetails_In> addInvoicingDetailsIn)
        {
            BLL_ContractInvoicingDetails.Instance.AddInvoicingDetails(addInvoicingDetailsIn);
        }

        /// <summary>
        /// 获取合同发票开票明细信息
        /// </summary>
        [HttpPost]
        public List<InvoicingDetails_Out> GetInvoicingDetails()
        {
            return BLL_ContractInvoicingDetails.Instance.GetInvoicingDetails();
        }

        /// <summary>
        /// 获取合同发票开票明细信息全部包含删除
        /// </summary>
        [HttpPost]
        public List<InvoicingDetails_Out> GetInvoicingDetailsAll()
        {
            return BLL_ContractInvoicingDetails.Instance.GetInvoicingDetailsAll();
        }

        /// <summary>
        /// 删除合同发票开票明细信息
        /// </summary>
        [HttpPost]
        public void DeleteInvoicingDetails(string id)
        {
             BLL_ContractInvoicingDetails.Instance.DeleteInvoicingDetails(id);
        }
    }
}
