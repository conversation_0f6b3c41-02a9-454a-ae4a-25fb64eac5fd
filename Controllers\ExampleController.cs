﻿using CRM2_API.BLL;
using CRM2_API.BLL.BLL_Example;
using CRM2_API.BLL.Common.Com_EmailHelper;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Filter;
using CRM2_API.Common.JWT;
using CRM2_API.Common.Utils;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.International.Converters.PinYinConverter;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NJsonSchema;

using System.Collections.Concurrent;
using System.ComponentModel;
using System.Diagnostics.Contracts;
using System.IO;
using System.Reflection;
using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Taobao.Top.Link.Endpoints;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 测试控制器
    /// </summary>
    [Description("样例控制器")]
    [SkipIPCheck]
    public class ExampleController : MyControllerBase
    {
        //public ExampleController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        //{
        //    if (AppSettings.Env == Enum_SystemSettingEnv.Product)
        //        throw new ApiException("没有权限");
        //}

        /// <summary>
        /// 测试方法
        /// </summary>
        /// <param name="w">传入的模型，里面涉及模型验证</param>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck]
        public WeatherForecast_Out[] TestPost(WeatherForecast_In w)
        {
            return BLL_Test.Instance.GetWeahterForcast();
        }
        /// <summary>
        /// 测试方法2
        /// </summary>
        /// <param name="w">传入的模型，里面涉及模型验证</param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<WeatherForecast_Out> TestPost2(WeatherForecast_In w)
        {
            var arr = BLL_Test.Instance.GetWeahterForcast();
            return GetApiTableOut(arr, arr.Length);
        }
        /// <summary>
        /// 测试登录,返回token
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck]
        public string Login()
        {
            return JwtHelper.CreateToken(new TokenModel { id = "1", name = "aaaa" });
        }
        /// <summary>
        /// 测试下载
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck]
        public Stream DownLoadTest()
        {
            FileStream fs = new FileStream("appsettings.json", FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
            SetDownloadFileName("测试文件.json");
            return fs;
        }
        /// <summary>
        /// 测试上传
        /// </summary>
        /// <param name="tFile"></param>
        [HttpPost, SkipAuthCheck, SkipRecordLog, SkipRightCheck, SkipRSAKey]
        public void UploadFileTest([FromForm] TestUploadClass tFile)
        {
            //var data = BLL_Customer.Instance.ExcelCutomerIn(tFile.uFile[0]);
            //BLL_Customer.Instance.ImportExcelCustomer(data, EnumExcelCustomerSource.ZJ);

        }
        static ConcurrentDictionary<string, Type> dicAllControllType = new ConcurrentDictionary<string, Type>();
        /// <summary>
        /// 获取请求和输出的ts模型
        /// </summary>
        /// <param name="url">复制请求后面路由地址，粘贴此位置</param>
        /// <returns></returns>
        [HttpGet, SkipAuthCheck]
        public Stream GetControllerMethodTs(string url)
        {
            string controller = "";
            string method = "";
            try
            {
                url = url.TrimStart("/api/");
                controller = url.Split("/")[0];
                method = url.Split("/")[1];
                if (controller.IsNullOrEmpty() || method.IsNullOrEmpty())
                {
                    throw new ApiException("填写的格式不正确，应为：/api/Controller/Method");
                }
            }
            catch (Exception e)
            {
                if (e is ApiException ee)
                    throw ee;
                throw;
            }
            //缓存所有控制器
            if (dicAllControllType.Count == 0)
            {
                lock (dicAllControllType)
                {
                    var ass = Assembly.GetExecutingAssembly();
                    var allType = ass.GetTypes();
                    var connType = allType.FindAll(t => t.Namespace?.RegexIsMatch(@"CRM2_API\.Controllers.*?") == true && t.Name.EndsWith("Controller"));
                    connType.ForEach(t =>
                    {
                        dicAllControllType.TryAdd(t.Name.TrimEnd("Controller").ToUpper(), t);
                    });
                }
            }
            //构造请求参数和返回参数
            StringBuilder sb = new StringBuilder();
            if (dicAllControllType.TryGetValue(controller.TrimEnd("Controller").ToUpper(), out var t))
            {
                var m = t.GetMethod(method);
                if (m is null)
                    throw new ApiException("方法名不正确");
                Response.ContentType = "application/octet-stream";
                Response.Headers.Add("Content-Disposition", "attachment;filename=" + controller + "_" + method + "_Ts" + ".zip");
                return BLL_GenerateModelToTS.Instance.GenerateMethod(m);
            }
            else
            {
                throw new ApiException("未找到控制器名");
            }
        }

        /// <summary>
        /// 获取GUID
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck]
        public List<string> GetGuid(int number)
        {
            List<string> guids = new List<string>();
            for (int i = 0; i < number; i++)
            {
                guids.Add(Guid.NewGuid().ToString());
            }
            return guids;
        }

        [HttpPost, SkipAuthCheck]
        public void GetDictionaryTree()
        {
            var dicTree = DbOpe_sys_dictionary.Instance.GetDictionaryTree();
            var years = dicTree.First(e => e.Value.Equals("ApplicableYearsName")).Children.First(e => e.Value == "2").Name;
        }


        [HttpPost, SkipAuthCheck, SkipRSAKey]
        public void CheckSpecialKey(string publicKey, string privateKey, string aesKey, string specialKey)
        {
            var specialKey_new = EncryptUtil.RSAPublicEncrypt_Pem(aesKey, publicKey);
            var boolSpe = specialKey_new.Equals(specialKey);
            var aesKey_new = EncryptUtil.RSAPrivateDecrypt_Pem(specialKey, privateKey);
            var boolAes = aesKey_new.Equals(aesKey);
        }


        [HttpPost, SkipAuthCheck, SkipRSAKey]
        public List<string> LoginAESTest(LoginByPwd_In loginByPwdIn)
        {
            //var jString = { "LoginName":lo}
            var jObject = JsonConvert.SerializeObject(loginByPwdIn);
            var aesKey = new RandomUtil(16, Enum_RandomFormat.NumberAndLetter).GetRandom().ToString();
            var vector = "hqhscrm220230101";
            var body = EncryptUtil.AESEncrypt(jObject, aesKey, vector);
            //(string publicKey, string privateKey) = EncryptUtil.GetRSAKey_Pem();
            var publicKey = AppSettings.RSAKey.PublicKey.ToString();
            var specialKey = EncryptUtil.RSAPublicEncrypt_Pem(aesKey, publicKey);

            List<string> retList = new List<string>();
            retList.Add(body);
            retList.Add(specialKey);

            return retList;
        }

        [HttpPost, SkipAuthCheck, SkipRSAKey]
        public List<string> AddOrgest(AddOrganization_In loginByPwdIn)
        {
            //var jString = { "LoginName":lo}
            var jObject = JsonConvert.SerializeObject(loginByPwdIn);
            var aesKey = new RandomUtil(16, Enum_RandomFormat.NumberAndLetter).GetRandom().ToString();
            var vector = "hqhscrm220230101";
            var body = EncryptUtil.AESEncrypt(jObject, aesKey, vector);
            var publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlgCyT6rTpSLPYhLNoKwfr/RoAxXrmYKjsb7TpK4rm3MymQx/rW1DJHqWTCGT++g45xHpaYfFwOBIjzF3IlWhnFOdL9cBG+TAde3ElOEoHV+ZXpJqgEzqLSeiRaeAS5cOZjGiQyFlAach0h09tNm2U60dHAn6Qo3YH0rXkJoUf/eyGrzgXSmxC2J3+Z7MqT8uIi9imk5ZR31rQmfUlFxor9xFU7ny/yTdigBQED5mxIjddhYZLXxyYCOaIU0ZX9Me94J8l7eH8tK+hswFsnh8eSwBZ+CK/d6/TPXv7Xxvg74gZMz1G4K9g5ytiE92oqWuGMQlxVFx8V0KPHpJhgvcBwIDAQAB";
            var specialKey = EncryptUtil.RSAPublicEncrypt_Pem(aesKey, publicKey);
            List<string> retList = new List<string>();
            retList.Add(body);
            retList.Add(specialKey);

            return retList;
        }

        [HttpPost, SkipAuthCheck, SkipRSAKey]
        public List<string> UpdateOrgest(UpdateOrganization_In loginByPwdIn)
        {
            //var jString = { "LoginName":lo}
            var jObject = JsonConvert.SerializeObject(loginByPwdIn);
            var aesKey = new RandomUtil(16, Enum_RandomFormat.NumberAndLetter).GetRandom().ToString();
            var vector = "hqhscrm220230101";
            var body = EncryptUtil.AESEncrypt(jObject, aesKey, vector);
            var publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlgCyT6rTpSLPYhLNoKwfr/RoAxXrmYKjsb7TpK4rm3MymQx/rW1DJHqWTCGT++g45xHpaYfFwOBIjzF3IlWhnFOdL9cBG+TAde3ElOEoHV+ZXpJqgEzqLSeiRaeAS5cOZjGiQyFlAach0h09tNm2U60dHAn6Qo3YH0rXkJoUf/eyGrzgXSmxC2J3+Z7MqT8uIi9imk5ZR31rQmfUlFxor9xFU7ny/yTdigBQED5mxIjddhYZLXxyYCOaIU0ZX9Me94J8l7eH8tK+hswFsnh8eSwBZ+CK/d6/TPXv7Xxvg74gZMz1G4K9g5ytiE92oqWuGMQlxVFx8V0KPHpJhgvcBwIDAQAB";
            var specialKey = EncryptUtil.RSAPublicEncrypt_Pem(aesKey, publicKey);
            List<string> retList = new List<string>();
            retList.Add(body);
            retList.Add(specialKey);

            return retList;
        }



        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public void GlobalSearchAPITest()
        {
            var url = string.Format(AppSettings.GlobalSearchAPI.AddPrimaryUserUrl, "99901", "xxxx_testwu", "2023-08-08", "2024-08-07");
            var str = NetUtil.Post_ReturnString(url);
            var ret = str.DeserializeNewtonJson<JObject>();
            var id = ret["globalsouID"].ToString();
        }


        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public void MailTest(IFormFileCollection formFiles)
        {
            //Com_EmailHelper.ReadDBEmail();
            var filePathList = new List<string>();
            foreach (IFormFile file in formFiles)
            {
                var filePath = Path.Combine(Directory.GetCurrentDirectory(), file.FileName);
                //将流写入文件
                using (Stream stream = file.OpenReadStream())
                {
                    // 把 Stream 转换成 byte[]
                    byte[] bytes = new byte[stream.Length];
                    stream.Read(bytes, 0, bytes.Length);
                    // 设置当前流的位置为流的开始
                    stream.Seek(0, SeekOrigin.Begin);
                    // 把 byte[] 写入文件
                    using (FileStream fs = new(filePath, FileMode.Create))
                    {
                        using (BinaryWriter bw = new(fs))
                        {
                            bw.Write(bytes);
                            /*BM_AttachFile AttachFile = new BM_AttachFile()
                            {
                                id = AttachFileId,
                                objectId = objectId,
                                fileName = fileName,
                                fullFileName = fullFileName,
                                fileExtension = fileExtension,
                                FileSize = (int)file.Length,
                                userId = userId
                            };
                            F f = new F();
                            AttachFile.MappingTo(f);
                            Value.SaveAttachFile(f);*/
                        }
                    }
                }

                filePathList.Add(filePath);
            }
            String sendMsg = "2023年11月1日16:49:03";
            Com_EmailHelper.SendOpenDBServiceEmail("<EMAIL>", "邓白氏", sendMsg, filePathList);
            //var mail = new MailHandler();
            //mail.SendMail("test", "测试邮件");
        }

        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public void MailReadTest()
        {
            Com_EmailHelper.ReadDBEmail();
        }

        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public void FormRoleTest(string formId)
        {
            var list = DbOpe_sys_form.Instance.GetSubSysForm(formId);
            var a = list;
        }

        [HttpPost]
        public void CheckGit()
        {
        }

        /// <summary>
        /// 添加用户
        /// </summary>
        /// <param name="addUserInfo"></param>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck]
        public async Task<List<BM_AddGtisUserRetModel>> AddUser(BM_AddGtisUser addUserInfo)
        {
            return await BLL_GtisOpe.Instance.AddUser(addUserInfo);
        }


        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public void TestGroupBy()
        {
            var list = new List<CheckHasSameValidContacts_Out_Sub>();
            var a = new CheckHasSameValidContacts_Out_Sub();
            a.Name = "aaa"; a.Phone = "15100000001";
            var b = new CheckHasSameValidContacts_Out_Sub();
            b.Name = "aaa"; b.Phone = "15100000002";
            var aa = new CheckHasSameValidContacts_Out_Sub();
            aa.Name = "aaaaaa"; aa.Phone = "15100000001";
            list.Add(a);
            list.Add(b);
            list.Add(aa);

            var same = list.GroupBy(e => e.Phone).Where(e => e.Count() > 1).Select(e => e.Key).ToList();

        }

        [HttpPost, SkipRSAKey, SkipRightCheck]
        public string AESDecrypt(string SpecialKey, string body, bool IsMobile, bool IsSkipAuth)
        {
            //1.从Header中读取 特殊秘钥
            //var specialKey = context.HttpContext.Request.Headers["SpecialKey"];
            //2.从Header中读取当前使用环境是否为移动端
            //var IsMobile = context.HttpContext.Request.Headers["IsMobile"].ToString().ToBool();
            //3.从token中读取用户Id,根据用户Id的缓存获取用户私钥
            var userId = TokenModel.Instance.id;
            string privateKey = string.Empty;
            if (IsSkipAuth)
                privateKey = AppSettings.RSAKey.PrivateKey.ToString();
            else
                privateKey = RedisCache.RSAKey.GetPrivateKey(userId, IsMobile);
            //4.使用用户私钥解密特殊秘钥，得到对称加密的AES秘钥
            var aesKey = EncryptUtil.RSAPrivateDecrypt_Pem(SpecialKey, privateKey);
            //5.使用AES秘钥解密httpbody内容，将解密后的body内容写入到context.Request.Body中，覆盖原有body
            ////定义16位偏移量
            string vector = "hqhscrm220230101";
            ////解密现有body后存入新body
            var newBody = EncryptUtil.AESDecrypt(body, aesKey, vector);
            return newBody;
        }

        [HttpPost, SkipRSAKey, SkipRightCheck]
        public void SSETest()
        {
            //RedisCache.SilenceTime.CheckSilenceTime(UserID)
            NetUtil.SendSseMessage(Response, "123", "true", "登出", TimeSpan.FromMinutes(1));
        }

        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public void GetMatchingData(int SID, string HSCode, int IAEType, List<string> TimePeriods)
        {

            DbOpe_sys_hscode.Instance.GetMatchingData(SID, HSCode, IAEType, TimePeriods);
        }




        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public string ShowVersion()
        {
            return "2024073001";
        }

        [HttpPost, SkipRSAKey, SkipRightCheck]
        public void GetTianyancha()
        {
            BLL_Tianyancha.Instance.DealCompanys();
        }
        /// <summary>
        /// 晋升测试
        /// </summary>
        //[HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        //[Obsolete]
        //public int PromotionTest(int yearIn, int monthIn, int dayIn)
        //{
        //    DateTime dd = new DateTime(yearIn, monthIn, dayIn);
        //    return BLL_Test.Instance.DoPromotionJob(dd);
        //}


        //[HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck, SkipSilence]
        //public void ParentOrgList(string id)
        //{
        //    var parentOrgList = DbOpe_sys_organization.Instance.GetParentOrgList(id);

        //}

        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public string GetPinyin(string name)
        {
            string ChineseReg = "^[\\u4E00-\\u9FA5]+$";
            var pySb = new StringBuilder();
            foreach (var itemChar in name)
            {
                //过滤非汉字的字符，直接返回
                var reg = new Regex(ChineseReg);
                if (!reg.IsMatch(itemChar.ToString()))
                {
                    pySb.Append(itemChar);
                }
                else
                {
                    var chineseChar = new ChineseChar(itemChar);
                    var pyStr = chineseChar.Pinyins.First().ToLower();
                    pySb.Append(pyStr.Substring(0, pyStr.Length - 1));
                }
            }
            return pySb.ToString();
        }

        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public List<BM_GtisOpeUserInfo> GetGtisInfo(string gtisContractNum)
        {   //根据客户编码获取G5系统中账号信息
            var a = BLL_GtisOpe.Instance.GetUserInfo(gtisContractNum).Result;
            return a;
        }


        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public List<ServiceChangeTypeTree> GetServiceChangeTypeTree()
        {
            return DbOpe_crm_contract_serviceinfo_changetype.Instance.GetServiceChangeTypeTree();
        }

        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public void TextOrg()
        {
            //获取抄送人列表
            var parentOrgList = DbOpe_sys_organization.Instance.GetParentOrgList("047f8a8b-1dc8-43c4-89e0-a5175389e164");
        }


        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public void TextPlus(string level, string month, string count)
        {
            var a = level;
        }

        [HttpPost, SkipRightCheck, SkipAuthCheck]
        public void TestFuc()
        {
            SalesWitsReleaseMonths checkCanAddSalesWits = DbOpe_crm_product.Instance.GetAddItemContractGtisReleaseMonths("9db56bd2-56ae-49d8-b5bd-59912514de6d");
            GetProductList_In a = new GetProductList_In();
            a.IsDESC = false;
            a.PageNumber = 1;
            a.PageSize = 100;
            //a.ParentContractId = "ec8044a8-0fac-4f96-8713-9c8a775cb0f8";
            a.FirstParty = "4e607f78-dde8-45c7-b593-14c010d843d2";
            int page = 0;
            DbOpe_crm_product.Instance.GetProductList(a,ref  page);

        }

        [HttpPost, SkipRightCheck]
        public async Task TestSendMessage(string Id)
        {
            //调用G5发送消息接口 注意 服务过期等三个不直接发送消息
            var GetSendData = DbOpe_sys_messagecenterdetail.Instance.FindData(Id);
            if (GetSendData != null)
            {
                // 并行执行两个异步任务
                var dingTalkTask = BLL_MessageCenter.Instance.sendDingTalkMessage(GetSendData.MessageType.Value.ToEnum<EnumMessageCenterType>(),
                    GetSendData.Id, GetSendData.MessageVXPath);

                var gzhTask = BLL_MessageCenter.Instance.sendGZHMessage(GetSendData.MessageVXPath);

                // 等待两个任务完成
                await Task.WhenAll(dingTalkTask, gzhTask);
            }
        }

        #region  2025.7 新版服务申请流程方法本地单元测试入口
        /// <summary>
        /// 根据合同Id获取合同产品信息
        /// 服务申请时使用,原方法在9789行
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        [HttpPost, UseDefaultRSAKey, SkipIPCheck, SkipRightCheck]
        public GetProductInfoByContractId4ServeApply_Out GetProductInfoByContractId4ServeApply(string contractId)
        {


            var students = new List<Student>
            {
                new Student { Name = "Alice", Score = 85 },
                new Student { Name = "Bob", Score = 90 },
                new Student { Name = "Charlie", Score = 85 },
                new Student { Name = "David", Score = 90 },
                new Student { Name = "Eve", Score = 75 }
            };

            var groupedStudents = students.GroupBy(student => student.Score);

            foreach (var group in groupedStudents)
            {
                var a = group.ToList();
                Console.WriteLine($"Score: {group.Key}");
                foreach (var student in group)
                {
                    Console.WriteLine($"  Name: {student.Name}, Score: {student.Score}");
                }
            }

            return BLL_ContractService.Instance.GetProductInfoByContractId4ServeApply(contractId);
        }
        #endregion


        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck]
        public void AccountNumOrderTest()
        {
            var list = new List<Account>();
            list.Add(new Account { Type = 1, Nummber = "3569874" });
            list.Add(new Account { Type = 0, Nummber = "********" });
            list.Add(new Account { Type = 0, Nummber = "********" });
            list.Add(new Account { Type = 0, Nummber = "********" });
            list.Add(new Account { Type = 0, Nummber = "********" });
            list.Add(new Account { Type = 0, Nummber = null });

            var list_1 = list.Where(e => !string.IsNullOrEmpty(e.Nummber)).OrderByDescending(e => e.Type).OrderBy(e => e.Nummber).Concat(list.Where(e => string.IsNullOrEmpty(e.Nummber))).ToList();


        }
    }

    class Student
    {
        public string Name { get; set; }
        public int Score { get; set; }
    }

    class Account
    {
        public int Type { get; set; }
        public string Nummber { get; set; }
    }
}
