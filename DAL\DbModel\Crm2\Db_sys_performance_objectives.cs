﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///业绩目标表
    ///</summary>
    [SugarTable("sys_performance_objectives")]
    public class Db_sys_performance_objectives
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:年份
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? ObjectivesYear {get;set;}

           /// <summary>
           /// Desc:组织id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgId {get;set;}

           /// <summary>
           /// Desc:1月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Jan {get;set;}

           /// <summary>
           /// Desc:2月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Feb {get;set;}

           /// <summary>
           /// Desc:3月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Mar {get;set;}

           /// <summary>
           /// Desc:4月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Apr {get;set;}

           /// <summary>
           /// Desc:5月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? May {get;set;}

           /// <summary>
           /// Desc:6月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Jun {get;set;}

           /// <summary>
           /// Desc:7月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Jul {get;set;}

           /// <summary>
           /// Desc:8月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Aug {get;set;}

           /// <summary>
           /// Desc:9月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Sept {get;set;}

           /// <summary>
           /// Desc:10月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Oct {get;set;}

           /// <summary>
           /// Desc:11月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Nov {get;set;}

           /// <summary>
           /// Desc:12月目标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Dec {get;set;}

           /// <summary>
           /// Desc:年度汇总值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? AnnualSummary {get;set;}

           /// <summary>
           /// Desc:继承的id，当前记录生效会使父记录失效
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Inherit {get;set;}

           /// <summary>
           /// Desc:等待生效,为1时则为等待生效.0或NULL则无意义
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? WaitEffec {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

           /// <summary>
           /// Desc:生效时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? TakingEffectTime {get;set;}

           /// <summary>
           /// Desc:失效时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ExpirationTime {get;set;}

           public override bool Equals(object? obj)
           {
               return obj is Db_sys_performance_objectives objectives &&
                      Id == objectives.Id;
           }

           public override int GetHashCode()
           {
               return HashCode.Combine(Id);
           }
    }
}
