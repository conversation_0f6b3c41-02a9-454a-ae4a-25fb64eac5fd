﻿using CRM2_API.BLL;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Bibliography;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.IO;


namespace CRM2_API.Controllers
{
    /// <summary>
    /// 统计控制器
    /// </summary>
    [Description("统计控制器")]
    //[SkipAuthCheck]
    //[SkipRSAKey]
    public class StatisticsController : MyControllerBase
    {
        public StatisticsController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        #region 业绩统计页面最新接口（与统计分析统一业绩统计算法）
        /// <summary>
        /// 老系统的业绩排行统计
        /// </summary>
        /// <param name="achiveRankStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public List<AchiveRankStatistics_OUT> OrgAchivementRankStatistics(AchiveRankStatistics_IN achiveRankStatistics_IN)
        {
            return BLL_Statistics.Instance.AchivementRankStatistics(achiveRankStatistics_IN);
        }
        #endregion



        #region 新-统计分析模块

        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public StaticticsTableResult GetAchivemntTrendQuery(GetAchivementTrendQuery_IN getAchivementTrendQuery_IN)
        {
            return BLL_Statistics.Instance.GetAchivemntTrendQuery(getAchivementTrendQuery_IN);
        }
        /// <summary>
        /// 客户统计
        /// </summary>
        /// <param name="getCustomerNowQuery_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public StaticticsTableResult GetCustomerNowStatictics(GetCustomerNowQuery_IN getCustomerNowQuery_IN)
        {
            return BLL_Statistics.Instance.GetCustomerNowStatictics(getCustomerNowQuery_IN);
        }
        /// <summary>
        /// 获取业绩/到账类统计分析结果
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public StaticticsTableResult GetAchivemntAboutStaticticsQuery(GetAchivemntAboutStaticticsQuery getAchivemntAboutStaticticsQuery_In)
        {
            return BLL_Statistics.Instance.GetAchivemntAboutStaticticsQuery(getAchivemntAboutStaticticsQuery_In);
        }
        /// <summary>
        /// 获取客户跟进类统计分析结果
        /// </summary>
        /// <param name="getTrackingRecordStaInQuery"></param>
        /// <returns></returns>
        [HttpPost]
        public StaticticsTableResult GetTrackingRecordStaQuery(GetTrackingRecordStaInQuery  getTrackingRecordStaInQuery)
        {
            return BLL_Statistics.Instance.GetTrackingRecordStaQuery(getTrackingRecordStaInQuery);
        }
        #endregion

        #region 首页自动刷新用的接口
        /// <summary>
        /// 客户情况实时统计
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipSilenceAttribute]
        public CustomerNowStatistics_OUT Auto_CustomerNowStatictics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.CustomerNowStatictics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 用户主页相关统计值
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipSilenceAttribute]
        public UserIndexStatisticsEx_OUT Auto_GetUserIndexStatisticsEx(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.GetUserIndexStatisticsEx(userIndexStatistics_IN);
        }
        /// <summary>
        /// 老系统的业绩排行统计
        /// </summary>
        /// <param name="orgRankStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public List<UserRankStatistics_OUT> Auto_OrgAchivementRankStatistics(OrgRankStatistics_IN orgRankStatistics_IN)
        {
            return BLL_Statistics.Instance.OrgAchivementRankStatistics(orgRankStatistics_IN);
        }
        /// <summary>
        /// 首页-销售业绩
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public SalesDataStatistics_OUT Auto_SalesDateStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesDateStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 用户主页相关统计值
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public UserIndexStatistics_OUT Auto_GetUserIndexStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.GetUserIndexStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// （组织）首页-业绩排行
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public SalesRankStatistics_OUT Auto_SalesOrgRankStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesOrgRankStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// （个人）首页-业绩排行
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public SalesRankStatistics_OUT Auto_SalesUserRankStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesUserRankStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 首页-全公司业绩排行（新）
        /// </summary>
        /// <param name="userIndexCompanyRankStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public CompanyRankStatistics_OUT Auto_CompanyRankStatistics(UserIndexCompanyRankStatistics_IN userIndexCompanyRankStatistics_IN)
        {
            return BLL_Statistics.Instance.CompanyRankStatistics(userIndexCompanyRankStatistics_IN);
        }
        /// <summary>
        /// 首页-销售目标统计
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public SalesTargetStatistics_OUT Auto_SalesTargetStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesTargetStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 销售漏斗
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public SalesFunnelStatistics_OUT Auto_SalesFunnelStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesFunnelStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 跟踪排行
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public TrackingRecordRankStatistics_OUT Auto_TrackingRecordRankStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.TrackingRecordRankStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 首页-客户统计(个人)
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipSilenceAttribute]
        public CustomerStatistics_OUT Auto_CustomerStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.CustomerStatistics(userIndexStatistics_IN);
        }
        #endregion

        #region 老的业绩统计页面用的接口
        ///// <summary>
        ///// 老系统的业绩排行统计
        ///// </summary>
        ///// <param name="orgRankStatistics_IN"></param>
        ///// <returns></returns>
        //[HttpPost]
        //public List<UserRankStatistics_OUT> OrgAchivementRankStatistics(OrgRankStatistics_IN orgRankStatistics_IN)
        //{
        //    return BLL_Statistics.Instance.OrgAchivementRankStatistics(orgRankStatistics_IN);
        //}
        /// <summary>
        /// 业绩排行统计导出
        /// </summary>
        /// <param name="orgRankStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetOrgAchivementRankDownload(AchiveRankStatistics_IN orgRankStatistics_IN)
        {
            Stream result = BLL_Statistics.Instance.GetOrgAchivementRankDownload(orgRankStatistics_IN);
            return new FileStreamResult(result, "application/octet-stream");
        }
        /// <summary>
        /// 个人业绩 API
        /// </summary>
        /// <param name="rankStatisticsAPI_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        public List<RankStatisticsAPI_OUT> AchivementStatisticsAPI(RankStatisticsAPI_IN rankStatisticsAPI_IN)
        {
            var p = rankStatisticsAPI_IN.MappingTo<OrgRankStatistics_IN>();
            p.EnumRankOrgType = EnumStatisticOrgType.User;
            return BLL_Statistics.Instance.OrgAchivementRankStatistics(p,Guid.Empty.ToString()).MappingTo<List<RankStatisticsAPI_OUT>>();
        }

        #endregion

        #region 用户主页相关统计
        /// <summary>
        /// 用户主页相关统计值
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public UserIndexStatisticsEx_OUT GetUserIndexStatisticsEx(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.GetUserIndexStatisticsEx(userIndexStatistics_IN);
        }
        /// <summary>
        /// 客户情况实时统计
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public CustomerNowStatistics_OUT CustomerNowStatictics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.CustomerNowStatictics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 首页-销售业绩（12.25 有效业绩）
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesDataStatistics_OUT SalesDateStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesDateStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 用户主页相关统计值
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public UserIndexStatistics_OUT GetUserIndexStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.GetUserIndexStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// （组织）首页-业绩排行
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesRankStatistics_OUT SalesOrgRankStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesOrgRankStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// （个人）首页-业绩排行
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesRankStatistics_OUT SalesUserRankStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesUserRankStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 首页-全公司业绩排行（新）
        /// </summary>
        /// <param name="userIndexCompanyRankStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public CompanyRankStatistics_OUT CompanyRankStatistics(UserIndexCompanyRankStatistics_IN userIndexCompanyRankStatistics_IN)
        {
            return BLL_Statistics.Instance.CompanyRankStatistics(userIndexCompanyRankStatistics_IN);
        }
        /// <summary>
        /// 首页-销售目标统计
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesTargetStatistics_OUT SalesTargetStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesTargetStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 销售漏斗
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesFunnelStatistics_OUT SalesFunnelStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.SalesFunnelStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 跟踪排行
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public TrackingRecordRankStatistics_OUT TrackingRecordRankStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.TrackingRecordRankStatistics(userIndexStatistics_IN);
        }
        /// <summary>
        /// 首页-客户统计(个人)
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public CustomerStatistics_OUT CustomerStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            return BLL_Statistics.Instance.CustomerStatistics(userIndexStatistics_IN);
        }
        #endregion

        #region 管理日报、工作周报、工作月报相关统计
        /// <summary>
        /// 管理日报用的统计
        /// </summary>
        [HttpPost]
        public ManagerReportStatistics_OUT ManagerReportStatistics()
        {
            return BLL_Statistics.Instance.ManagerReportStatistics();
        }
        /// <summary>
        /// 工作周报用的统计
        /// </summary>
        [HttpPost]
        public WeekWorkReportStatistics_OUT WeekWorkReportStatistics()
        {
            return BLL_Statistics.Instance.WeekWorkReportStatistics();
        }
        /// <summary>
        /// 工作月报用的统计
        /// </summary>
        [HttpPost]
        public MonthWorkReportStatistics_OUT MonthWorkReportStatistics()
        {
            return BLL_Statistics.Instance.MonthWorkReportStatistics();
        }
        #endregion
    }
}
