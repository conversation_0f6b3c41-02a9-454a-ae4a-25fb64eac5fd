﻿using System;
using System.Linq;
using System.Text;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_form")]
    public partial class Db_sys_form
    {
        /*public Db_sys_form()
        {


        }*/
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:名称	
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string Name { get; set; }

        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Describe { get; set; }

        /// <summary>
        /// Desc:类型：菜单(模块、表单)、列表、列、按钮、后台方法，后台方法关联到实际的按钮或者列表下面
        /// Default:
        /// Nullable:False
        /// </summary>           
        public EnumFormType Type { get; set; }

        /// <summary>
        /// Desc:父级节点
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ParentId { get; set; }

        /// <summary>
        /// Desc:显示名称
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string Title { get; set; }

        /// <summary>
        /// Desc:顺序号
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int OrderNum { get; set; }

        /// <summary>
        /// Desc:模块名称：对应后台程序控制层名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ControllerName { get; set; }

        /// <summary>
        /// Desc:方法名称:对应后台程序控制层的方法名称，实现后台权限控制
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MethodName { get; set; }

        /// <summary>
        /// Desc:日志模板
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string LogTemplate { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:False
        /// </summary>           
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:修改时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// 树形结构下级
        /// </summary>
        [SqlSugar.SugarColumn(IsIgnore = true)]
        public List<Db_sys_form> Children { get; set; }

    }
}
