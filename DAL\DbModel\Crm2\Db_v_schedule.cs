﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_schedule")]
    public class Db_v_schedule
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string Id {get;set;}

           /// <summary>
           /// Desc:用户id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:1 == 私有池, 2 == 临时池
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? CustomerDataSource {get;set;}

           /// <summary>
           /// Desc:客户表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OriginTableCustomerId {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CustomerId {get;set;}

           /// <summary>
           /// Desc:客户名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CustomerName {get;set;}

           /// <summary>
           /// Desc:客户来源 0:搜索引擎 1:转介绍 2:广告推广 3:中国出口企业 4:广交会 5:其他
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? CustomerSource {get;set;}

           /// <summary>
           /// Desc:客户级别 0:未知 1:A类 2:B类 3:C类
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? CustomerLevel {get;set;}

           /// <summary>
           /// Desc:服务状态 0:无服务：全部开通 2：待开通 3：申请服务: 4：服务变更: 5：部分开通: 6：服务过期: 7：服务拒绝
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? ServiceState {get;set;}

           /// <summary>
           /// Desc:(私有池)领取时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CollectionTime {get;set;}

           /// <summary>
           /// Desc:(临时池显示)客户临时序号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CustomerOrderNum {get;set;}

           /// <summary>
           /// Desc:慧思ID/客户编号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CustomerNum {get;set;}

           /// <summary>
           /// Desc:客户性质 0:未知 1:国企 2:私企 3:离岸 4:境外 5:其他
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? CustomerNature {get;set;}

           /// <summary>
           /// Desc:客户规模 0:未知 1:小型企业 2:中性企业 3:大型企业
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? CustomerSize {get;set;}

           /// <summary>
           /// Desc:快照时当前客户绑定的用户Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CustomerBindUserId {get;set;}

           /// <summary>
           /// Desc:快照时当前客户绑定的用户所属的组织机构Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CustomerBindUserOrgId {get;set;}

           /// <summary>
           /// Desc:快照时当前客户绑定的用户所属的组织机构名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CustomerBindUserOrgName {get;set;}

           /// <summary>
           /// Desc:合同表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractId {get;set;}

           /// <summary>
           /// Desc:跟踪方式
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? TrackingType {get;set;}

           /// <summary>
           /// Desc:跟踪目的
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? TrackingPurpose {get;set;}

           /// <summary>
           /// Desc:跟踪阶段
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? TrackingStage {get;set;}

           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Remark {get;set;}

           /// <summary>
           /// Desc:访客时间开始
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? VisitorTimeStart {get;set;}

           /// <summary>
           /// Desc:访客时间结束
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? VisitorTimeEnd {get;set;}

           /// <summary>
           /// Desc:上次设置的访客时间开始
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? OldVisitorTimeStart {get;set;}

           /// <summary>
           /// Desc:上次设置的访客时间结束
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? OldVisitorTimeEnd {get;set;}

           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? State {get;set;}

           /// <summary>
           /// Desc:跟踪事件
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? TrackingEvents {get;set;}

           /// <summary>
           /// Desc:是否可见
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? IsVisible {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
