﻿using CRM2_API.Model.ControllersViewModel.Common;
using System.ComponentModel;

namespace CRM2_API.BLL.RemindInfo
{
    /// <summary>
    /// 飘红提醒信息输出
    /// </summary>
    public class ApiRemindInfoOut
    {
        public List<RemindInfoOut> RemindInfoOuts { get; set; }
    }
    /// <summary>
    /// 飘红提醒信息接口
    /// </summary>
    public class RemindInfoOut : RedirectInfo
    {
        /// <summary>
        /// 提醒数量
        /// </summary>
        public int RemindDataNum { get; set; }
        /// <summary>
        /// 提醒文字
        /// </summary>
        public string RemindMsg
        {
            get
            {
                return FormatRemindMsg();
            }
        }
        /// <summary>
        /// 提醒相关记录的数据Id和名称
        /// </summary>
        public List<ApiDictionary> RemindDataIdAndNames { get; set; }
        /// <summary>
        /// 提醒相关记录的数据Id数据库名称
        /// </summary>
        public string RemindDataIdDbName { get; set; }
        /// <summary>
        /// 提醒类型
        /// </summary>
        public EnumRemindType RemindType { get; set; }
        /// <summary>
        /// 提醒类型详细
        /// </summary>
        public string RemindTypeDetail { get; set; }
        /// <summary>
        /// 展示名称最大数量（多余的...）
        /// </summary>
        public int MaxShowNameNum { get; set; }
        /// <summary>
        /// 提醒数量单位
        /// </summary>
        public string RemindNumUnit { get; set; }
        public string FormatRemindMsg()
        {
            //您有5个即将到期的保留客户：名称1，名称2，名称3，名称4，名称5...
            if (RemindDataNum == 0)
            {
                return "";
            }
            else
            {
                string msg = "您有{0}{1}{2}:{3}{4}";
                string showName = RemindDataIdAndNames.Select(o => o.Name).ToList().Take(MaxShowNameNum).ToList().JoinToString(",");
                string ignoreChar;
                if (RemindDataNum > MaxShowNameNum)
                {
                    ignoreChar = "...";
                }
                else
                {
                    ignoreChar = "";
                }
                return string.Format(msg, RemindDataNum, RemindNumUnit, RemindTypeDetail, showName, ignoreChar);
            }
        }

    }
    /// <summary>
    /// 飘红提醒类型
    /// </summary>
    public enum EnumRemindType
    {
        [Description("签约客户即将到期")]
        SignCustomerNearlyRelease = 11,
        [Description("保留客户即将到期")]
        SaveCustomerNearlyRelease = 12,
        [Description("客户服务即将到期")]
        ServiceNearlyEnd = 13,
        [Description("到账业绩尚未确认")]
        AchivementToConfirm = 14,
        [Description("合同服务尚未申请")]
        ServiceNeedApply = 15,
        [Description("客户的账号存在异常")]
        ServiceExceptionRemind_Handing = 16,
        [Description("下属员工的客户账号存在异常")]
        ServiceExceptionRemind_Copy = 17
    }
    public class RedirectInfo
    {
        /// <summary>
        /// 目标路径
        /// </summary>
        public string RemindToPath { get; set; }
        /// <summary>
        /// 标签页form名称
        /// </summary>
        public string RemindFormName { get; set; }
    }
}
