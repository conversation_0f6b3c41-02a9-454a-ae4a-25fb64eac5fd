﻿using CRM2_API.Common.JWT;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using SqlSugar;
using System.Linq.Expressions;
using System.Reflection;

namespace CRM2_API.DAL.DbCommon
{
    /// <summary>
    /// crm2数据库单表通用操作基础类
    /// </summary>
    /// <typeparam name="DbModel"></typeparam>
    /// <typeparam name="DbOperate">数据库操作类</typeparam>
    public class DbOperateCrm2Ex<DbModel, DbOperate> :DbOperateCrm2<DbModel, DbOperate> where DbModel : class, new() where DbOperate : class, new()
    {
        private void setProperty(DbModel entity, string key, object value)
        {
            System.Type t = typeof(DbModel);
            PropertyInfo[] pc = t.GetProperties();//获取到泛型所有属性的集合
            foreach (PropertyInfo pi in pc)
            {
                if (pi.Name == key)//判断属性的类型是不是String
                {
                    if (pi.Name == "Deleted" && (pi.PropertyType == typeof(bool?)|| pi.PropertyType == typeof(bool)))
                    {
                        //deleted有的表用的是tinyint 在model里类型是bool
                        pi.SetValue(entity, Convert.ToInt32(value) == 0 ? false : true, null);//给泛型的属性赋值
                    }
                    else {
                        pi.SetValue(entity, value, null);//给泛型的属性赋值
                    }
                    
                    return;
                }
            }
        }
        private void setProperty(List<DbModel> entitys, string key, object value)
        {
            System.Type t = typeof(DbModel);
            PropertyInfo[] pc = t.GetProperties();//获取到泛型所有属性的集合
            foreach (DbModel entity in entitys)
            {
                foreach (PropertyInfo pi in pc)
                {
                    if (pi.Name == key)//判断属性的类型是不是String
                    {
                        pi.SetValue(entity, value, null);//给泛型的属性赋值
                        break;
                    }
                }
            }
        }
        new public void Insert(DbModel obj)
        {
            var user = TokenModel.Instance.id;
            setProperty(obj, "CreateDate", DateTime.Now);
            setProperty(obj, "CreateUser", user);
            setProperty(obj, "Deleted", 0);
            Db.Insertable(obj).ExecuteCommand();
        }
        new public void InsertQueue(DbModel obj)
        {
            var user = TokenModel.Instance.id;
            setProperty(obj, "CreateDate", DateTime.Now);
            setProperty(obj, "CreateUser", user);
            setProperty(obj, "Deleted", 0);
            Db.Insertable(obj).AddQueue();
        }

        public void InsertQueueWhichDeleteIsBool(DbModel obj)
        {
            var user = TokenModel.Instance.id;
            setProperty(obj, "CreateDate", DateTime.Now);
            setProperty(obj, "CreateUser", user);
            setProperty(obj, "Deleted", false);
            Db.Insertable(obj).AddQueue();
        }
        new public void Insert(List<DbModel> objs)
        {
            var user = TokenModel.Instance.id;
            objs.ForEach(obj => {
                setProperty(obj, "CreateDate", DateTime.Now);
                setProperty(obj, "CreateUser", user);
                setProperty(obj, "Deleted", 0);
            });
            Db.Insertable(objs).ExecuteCommand();
        }
        new public void InsertQueue(List<DbModel> objs)
        {
            if (objs.Count == 0)
            {
                return;
            }
            var user = TokenModel.Instance.id;
            objs.ForEach(obj => {
                setProperty(obj, "CreateDate", DateTime.Now);
                setProperty(obj, "CreateUser", user);
                setProperty(obj, "Deleted", 0);
            });
            Db.Insertable(objs).AddQueue();
        }
        new public void Update(DbModel obj)
        {
            var user = TokenModel.Instance.id;
            setProperty(obj, "UpdateDate", DateTime.Now);
            setProperty(obj, "UpdateUser", user);
            Db.Updateable(obj).IgnoreColumns(new string[2] { "CreateDate", "CreateUser" }).ExecuteCommand();
        }
        public void UpdateBase(DbModel obj)
        {
            Db.Updateable(obj).ExecuteCommand();
        }
        new public void UpdateQueue(DbModel obj)
        {
            var user = TokenModel.Instance.id;
            setProperty(obj, "UpdateDate", DateTime.Now);
            setProperty(obj, "UpdateUser", user);
            Db.Updateable(obj).IgnoreColumns(new string[2] { "CreateDate", "CreateUser" }).AddQueue();
        }
        new public void Update(List<DbModel> objs)
        {
            var user = TokenModel.Instance.id;
            objs.ForEach(obj => {
                setProperty(obj, "UpdateDate", DateTime.Now);
                setProperty(obj, "UpdateUser", user);
            });
            Db.Updateable(objs).IgnoreColumns(new string[2] { "CreateDate", "CreateUser" }).ExecuteCommand();
        }
        new public void UpdateQueue(List<DbModel> objs)
        {
            if (objs.Count == 0)
            {
                return;
            }
            var user = TokenModel.Instance.id;
            objs.ForEach(obj => {
                setProperty(obj, "UpdateDate", DateTime.Now);
                setProperty(obj, "UpdateUser", user);
            });
            Db.Updateable(objs).IgnoreColumns(new string[2] { "CreateDate", "CreateUser" }).AddQueue();
        }
        public void Update(DbModel obj, string[] updateColumns)
        {
            var user = TokenModel.Instance.id;
            setProperty(obj, "UpdateDate", DateTime.Now);
            setProperty(obj, "UpdateUser", user);
            updateColumns = updateColumns.Concat(new string[2] { "UpdateDate", "UpdateUser" }).ToArray();
            Db.Updateable(obj).UpdateColumns(updateColumns)
                .IgnoreColumns(new string[2] { "CreateDate", "CreateUser" }).ExecuteCommand();
        }
        public void UpdateQueue(DbModel obj, string[] updateColumns)
        {
            var user = TokenModel.Instance.id;
            setProperty(obj, "UpdateDate", DateTime.Now);
            setProperty(obj, "UpdateUser", user);
            updateColumns = updateColumns.Concat(new string[2] { "UpdateDate", "UpdateUser" }).ToArray();
            Db.Updateable(obj).UpdateColumns(updateColumns)
                .IgnoreColumns(new string[2] { "CreateDate", "CreateUser" }).AddQueue();
        }
        public void Update(List<DbModel> objs, string[] updateColumns)
        {
            var user = TokenModel.Instance.id;
            objs.ForEach(obj => {
                setProperty(obj, "UpdateDate", DateTime.Now);
                setProperty(obj, "UpdateUser", user);
            });
            updateColumns = updateColumns.Concat(new string[2] { "UpdateDate", "UpdateUser" }).ToArray();
            Db.Updateable(objs).UpdateColumns(updateColumns)
                .IgnoreColumns(new string[2] { "CreateDate", "CreateUser" }).ExecuteCommand();
        }
        public void UpdateQueue(List<DbModel> objs, string[] updateColumns)
        {
            if (objs.Count == 0)
            {
                return;
            }
            var user = TokenModel.Instance.id;
            objs.ForEach(obj => {
                setProperty(obj, "UpdateDate", DateTime.Now);
                setProperty(obj, "UpdateUser", user);
            });
            updateColumns = updateColumns.Concat(new string[2] { "UpdateDate", "UpdateUser" }).ToArray();
            Db.Updateable(objs).UpdateColumns(updateColumns)
                .IgnoreColumns(new string[2] { "CreateDate", "CreateUser" }).AddQueue();
        }
        public void UpdateColumns(Dictionary<string, object> keyValuePairs, string[] whereColumns)
        {
            var user = TokenModel.Instance.id;
            keyValuePairs["UpdateDate"] = DateTime.Now;
            keyValuePairs["UpdateUser"] = user;
            var dbName = Db.EntityMaintenance.GetEntityInfo<DbModel>().DbTableName;
            Db.Updateable(keyValuePairs).AS(dbName).WhereColumns(whereColumns).ExecuteCommand();
        }
        public void UpdateColumnsQueue(Dictionary<string, object> keyValuePairs, string[] whereColumns)
        {
            var user = TokenModel.Instance.id;
            keyValuePairs["UpdateDate"] = DateTime.Now;
            keyValuePairs["UpdateUser"] = user;
            var dbName = Db.EntityMaintenance.GetEntityInfo<DbModel>().DbTableName;
            Db.Updateable(keyValuePairs).AS(dbName).WhereColumns(whereColumns).AddQueue();
        }

        public void DeleteData_RealDelete(Expression<Func<DbModel, bool>> predicate)
        {
            Db.Deleteable<DbModel>().Where(predicate).ExecuteCommand();
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="dateSpanParams"></param>
        /// <param name="enumDateStatisticsType"></param>
        /// <param name="addUp">累加 （坐标轴展示结束时间）</param>
        /// <returns></returns>
        public ISugarQueryable<StatisticsDateSpan> GetStatisticsDateSpanQuery(DateSpanParams dateSpanParams, EnumDateStatisticsType enumDateStatisticsType = EnumDateStatisticsType.Current,bool addUp = false) {
            var dt = DateTime.Now;
            DateTime startDate = dt.AddDays(1 - dt.Day);  //本月月初
            DateTime endDate = startDate.AddMonths(1).AddDays(-1);  //本月月末
            string showFormat = "MM.dd";
            if (dateSpanParams.DateStart != null && dateSpanParams.DateEnd != null)
            {
                startDate = DateTime.Parse(dateSpanParams.DateStart.Value.ToString("yyyy-MM-dd"));
                endDate = DateTime.Parse(dateSpanParams.DateEnd.Value.ToString("yyyy-MM-dd"));
                var startYear = startDate.Year;
                var endYear = endDate.Year;
                while (endYear >= startYear)
                {
                    endYear--;
                }
            }
            if (enumDateStatisticsType == EnumDateStatisticsType.TongBi)
            {
                startDate = startDate.AddYears(-1);
                endDate = endDate.AddYears(-1);
                dateSpanParams.DateStart = startDate;
                dateSpanParams.DateEnd = endDate;
            }
            int dateDiffDays = new TimeSpan(endDate.Ticks - startDate.Ticks).TotalDays.ToInt();
            ISugarQueryable<StatisticsDateSpan> queryableLeft = null;
            List<StatisticsDateSpan> dateList = new List<StatisticsDateSpan>();
            int index = 0;
            var tempDate = startDate;
            var tempEndDate = endDate;
            switch (dateDiffDays)
            {
                //case <= 31:
                //    if (enumDateStatisticsType == EnumDateStatisticsType.HuanBi)
                //    {
                //        //环比向前多统计一个周期,index值设初始置到-1
                //        index--;
                //    }
                //    for (; index <= dateDiffDays; index++)
                //    {
                //        dateList.Add(new StatisticsDateSpan()
                //        {
                //            StartDate = startDate.AddDays(index),
                //            EndDate = startDate.AddDays(index),
                //            Key = startDate.AddDays(index).ToString("yyyy-MM-dd"),
                //            Index = index
                //        });
                //    }
                //    queryableLeft = Db.Reportable(dateList).ToQueryable();//转成时间数组
                //    break;
                case >=180: //年   按月统计
                    DateTime startMonth = startDate.AddDays(1 - startDate.Day);  //起始月月初
                    tempDate = startDate;
                    tempEndDate = startMonth.AddMonths(1).AddDays(-1);
                    showFormat = "MM";
                    var tempShowFormat = showFormat;
                    if (enumDateStatisticsType == EnumDateStatisticsType.HuanBi)
                    {
                        //环比向前多统计一个周期,index值设-1
                        var huanbiStartDate = startMonth.AddMonths(-1);
                        var huanbiEndDate = startMonth.GetMonthEnd();
                        if (huanbiStartDate.Day != 1)
                        {
                            tempShowFormat = "MMdd";
                            dateList.Add(new StatisticsDateSpan()
                            {
                                StartDate = huanbiStartDate,
                                EndDate = huanbiEndDate,
                                Key = addUp ? huanbiEndDate.ToString(showFormat) : huanbiStartDate.ToString(tempShowFormat) + "-" + huanbiEndDate.ToString(tempShowFormat),
                                Index = -1
                            });
                        }
                        else {
                            dateList.Add(new StatisticsDateSpan()
                            {
                                StartDate = huanbiStartDate,
                                EndDate = huanbiEndDate,
                                Key = huanbiStartDate.ToString(showFormat),
                                Index = -1
                            });
                        }
                        dateSpanParams.DateStart = huanbiStartDate;
                    }
                    if (tempDate.Day != 1)
                    {
                        tempShowFormat = "MMdd";
                        dateList.Add(new StatisticsDateSpan()
                        {
                            StartDate = tempDate,
                            EndDate = tempEndDate,
                            Key = addUp? tempDate.ToString(showFormat): tempDate.ToString(tempShowFormat) + "-" + tempEndDate.ToString(tempShowFormat),
                            Index = index++
                        });
                    }
                    else {
                        dateList.Add(new StatisticsDateSpan()
                        {
                            StartDate = tempDate,
                            EndDate = tempEndDate,
                            Key = tempDate.ToString(showFormat),
                            Index = index++
                        });
                    }
                    tempDate = tempEndDate.AddDays(1);//下月第一天
                    while (tempDate <= endDate)
                    {
                        tempEndDate = tempDate.AddMonths(1).AddDays(-1); //下月最后一天
                        if (tempEndDate > endDate)
                        {
                            tempShowFormat = "MMdd";
                            tempEndDate = endDate;
                            dateList.Insert(0, new StatisticsDateSpan()
                            {
                                StartDate = tempDate,
                                EndDate = tempEndDate,
                                Key = addUp ? tempDate.ToString(showFormat):tempDate.ToString(tempShowFormat) + "-" + tempEndDate.ToString(tempShowFormat),
                                Index = index++
                            });
                        }
                        else {
                            dateList.Insert(0, new StatisticsDateSpan()
                            {
                                StartDate = tempDate,
                                EndDate = tempEndDate,
                                Key = tempDate.ToString(showFormat),
                                Index = index++
                            });
                        }
                        tempDate = tempEndDate.AddDays(1);//下月第一天
                    }
                    queryableLeft = Db.Reportable(dateList).ToQueryable();
                    break;
                case >= 31: // 半年、季度 按15天统计
                    tempDate = startDate;
                    tempEndDate = startDate.AddDays(14);
                    if (enumDateStatisticsType == EnumDateStatisticsType.HuanBi)
                    {
                        var firstSpanDiffDays = new TimeSpan(tempEndDate.Ticks - tempDate.Ticks).TotalDays.ToInt();
                        //环比向前多统计一个周期,index值设-1
                        var huanbiStartDate = tempDate.AddDays(-firstSpanDiffDays);
                        var huanbiEndDate = tempEndDate.AddDays(-firstSpanDiffDays);
                        dateList.Add(new StatisticsDateSpan()
                        {
                            StartDate = huanbiStartDate,
                            EndDate = huanbiEndDate,
                            Key = huanbiStartDate.ToString(showFormat) + "-" + huanbiEndDate.ToString(showFormat),
                            Index = -1
                        });
                        dateSpanParams.DateStart = huanbiStartDate;
                    }
                    dateList.Add(new StatisticsDateSpan()
                    {
                        StartDate = tempDate,
                        EndDate = tempEndDate,
                        Key = addUp? tempEndDate.ToString(showFormat): tempDate.ToString(showFormat) + "-" + tempEndDate.ToString(showFormat),
                        Index = index++
                    });
                    tempDate = tempEndDate.AddDays(1);//下15天的第一天
                    while (tempDate <= endDate)
                    {
                        tempEndDate = tempDate.AddDays(14); //下15天的最后一天
                        if (tempEndDate > endDate)
                        {
                            tempEndDate = endDate;
                        }
                        dateList.Insert(0, new StatisticsDateSpan()
                        {
                            StartDate = tempDate,
                            EndDate = tempEndDate,
                            Key = addUp ? tempEndDate.ToString(showFormat):tempDate.ToString(showFormat) + "-" + tempEndDate.ToString(showFormat),
                            Index = index++
                        });
                        tempDate = tempEndDate.AddDays(1);//下15天的第一天
                    }
                    queryableLeft = Db.Reportable(dateList).ToQueryable();
                    break;
                default:
                    DateTime startWeek = startDate.AddDays(1 - Convert.ToInt32(startDate.DayOfWeek.ToString("d")));  //起始日期的周一
                    tempDate = startDate;
                    tempEndDate = startWeek.AddDays(6);
                    if (enumDateStatisticsType == EnumDateStatisticsType.HuanBi)
                    {
                        var firstSpanDiffDays = new TimeSpan(tempEndDate.Ticks - tempDate.Ticks).TotalDays.ToInt();
                        //环比向前多统计一个周期,index值设-1
                        var huanbiStartDate = tempDate.AddDays(-firstSpanDiffDays);
                        var huanbiEndDate = tempEndDate.AddDays(-firstSpanDiffDays);
                        dateList.Add(new StatisticsDateSpan()
                        {
                            StartDate = huanbiStartDate,
                            EndDate = huanbiEndDate,
                            Key = huanbiStartDate.ToString(showFormat) + "-" + huanbiEndDate.ToString(showFormat),
                            Index = -1
                        });
                        dateSpanParams.DateStart = huanbiStartDate;
                    }
                    dateList.Add(new StatisticsDateSpan()
                    {
                        StartDate = tempDate,
                        EndDate = tempEndDate,
                        Key = addUp ? tempEndDate.ToString(showFormat):tempDate.ToString(showFormat) + "-" + tempEndDate.ToString(showFormat),
                        Index = index++
                    });
                    tempDate = tempEndDate.AddDays(1);//下周周一
                    while (tempDate <= endDate)
                    {
                        tempEndDate = tempDate.AddDays(6);
                        if (tempEndDate > endDate)
                        {
                            tempEndDate = endDate;
                        }
                        dateList.Insert(0, new StatisticsDateSpan()
                        {
                            StartDate = tempDate,
                            EndDate = tempEndDate,
                            Key = addUp ? tempEndDate.ToString(showFormat):tempDate.ToString(showFormat) + "-" + tempEndDate.ToString(showFormat),
                            Index = index++
                        });
                        tempDate = tempDate.AddDays(7);//下周周一
                    }
                    queryableLeft = Db.Reportable(dateList).ToQueryable();
                    break;
            }
            return queryableLeft;
        }
    }
}
