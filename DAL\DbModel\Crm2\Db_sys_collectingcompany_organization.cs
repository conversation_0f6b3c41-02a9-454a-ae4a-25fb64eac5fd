﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///收款公司组织机构关系表
    ///</summary>
    [SugarTable("sys_collectingcompany_organization")]
    public class Db_sys_collectingcompany_organization
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:收款公司表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CollectingCompanyId {get;set;}

           /// <summary>
           /// Desc:组织机构表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrganizationId {get;set;}

           /// <summary>
           /// Desc:是否境外客户
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? IsOverseasCustomer { get; set; }

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
