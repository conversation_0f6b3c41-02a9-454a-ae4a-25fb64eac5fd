﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///产品规则表_环球搜慧思学院价格子账号价格
    ///</summary>
    [SugarTable("crm_product_rules_globalsearch_subaccounts")]
    public class Db_crm_product_rules_globalsearch_subaccounts
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:环球搜慧思学院价格表Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string GlobalSearchandCollegePriceId {get;set;}

           /// <summary>
           /// Desc:产品类型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? ProductType {get;set;}

           /// <summary>
           /// Desc:产品id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ProductId {get;set;}

           /// <summary>
           /// Desc:价格id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ProductPriceId {get;set;}

           /// <summary>
           /// Desc:价格
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? Price {get;set;}

           /// <summary>
           /// Desc:币种
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? Currency {get;set;}

           /// <summary>
           /// Desc:价格模式
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? PriceMode {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
