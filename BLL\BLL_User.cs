﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using SqlSugar;
using System.Linq;
using System.Security.Cryptography;
using CRM2_API.BLL.Promotion;
using CRM2_API.Model.BusinessModel;
using System.Text.RegularExpressions;
using Microsoft.International.Converters.PinYinConverter;

namespace CRM2_API.BLL
{
    public class BLL_User : BaseBLL<BLL_User>
    {
        /// <summary>
        /// 添加员工信息
        /// 员工状态默认为启用，登录账号：自动生成，明码显示，可二次修改，生成规则hs开头+7位随机数字，登录密码：自动生成，明码显示，可二次修改，生成规则8位随机数字+字母（区分大小写），添加完员工信息后，以及短信方式发送账号、密码到设置的手机号上，发送格式“您好！XXX（自动获取员工姓名），您的慧思CRM系统的登账账号：XXXXXX（自动获取），登录密码：XXXXXX（自动获取），请即时登录使用。若有疑问请联系管理员！”
        /// </summary>
        /// <param name="addUser_In"></param>
        public void AddUser(AddUser_In addUser_In)
        {
            Db_sys_user sysUser = addUser_In.MappingTo<Db_sys_user>();
            if ((addUser_In.RoleId == "9fbe8362-b96f-46da-902a-aae767aa8b94" || addUser_In.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026") && string.IsNullOrEmpty(addUser_In.OrganizationId))
                throw new ApiException("所属组织不可为空");
            //如果添加销售主管角色，验证加入的组织中是否已存在销售主管
            var orgHasManager = DbOpe_sys_user.Instance.CheckOrgHasManager(addUser_In.OrganizationId);
            if (addUser_In.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026" && orgHasManager)
                throw new ApiException("该组织已存在销售主管");

            //生成主键
            sysUser.Id = Guid.NewGuid().ToString();
            //生成员工编号
            sysUser.UserNum = (DbOpe_sys_user.Instance.GetQueryCount(sysUser) + 1).ToString().PadLeft(5, '0');
            //如果添加销售主管或销售总监，添加工号
            if (addUser_In.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026" || addUser_In.RoleId == "0cafa3b1-1d14-4047-ae52-2feb0bf2c1cc")
                sysUser.JobNum = DbOpe_sys_user.Instance.GetJobNum();
            //校验密码
            //验证登录账号是否重复
            if (DbOpe_sys_user.Instance.CheckUserName(sysUser.UserName, sysUser.Id))
                throw new ApiException("登录账号已存在");
            //验证手机号是否重复
            if (!string.IsNullOrEmpty(sysUser.Telephone) && DbOpe_sys_user.Instance.CheckUserPhone(sysUser.Telephone, sysUser.Id))
                throw new ApiException("该手机号已被注册");
            //验证邮箱号是否重复
            if (!string.IsNullOrEmpty(sysUser.Email) && DbOpe_sys_user.Instance.CheckUserEmail(sysUser.Email, sysUser.Id))
                throw new ApiException("该邮箱已被注册");
            //验证身份证号是否合法
            if (!string.IsNullOrEmpty(sysUser.IDNo))
            {
                var year = addUser_In.IDNo.Substring(6, 4).ToInt();
                string? regex;
                if ((year % 400 == 0) || (year % 100 != 0 && (year % 4 == 0)))//闰年
                    regex = "^[1-9]\\d{5}(19|20)\\d{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2]\\d|3[0-1])|(04|06|09|11)(0[1-9]|[1-2]\\d|30)|02(0[1-9]|[1-2]\\d))\\d{3}[\\dXx]$";
                else//平年
                    regex = "^[1-9]\\d{5}(19|20)\\d{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2]\\d|3[0-1])|(04|06|09|11)(0[1-9]|[1-2]\\d|30)|02(0[1-9]|1\\d|2[0-8]))\\d{3}[\\dXx]$";
                if (!Regex.IsMatch(addUser_In.IDNo, regex))
                    throw new ApiException("身份证号格式错误");
                //验证身份证号是否重复
                if (DbOpe_sys_user.Instance.CheckUserIdNo(sysUser.IDNo, sysUser.Id))
                    throw new ApiException("身份证号已存在");
            }


            //加密密码 使用登录名+密码，再进行md5
            var oriPwd = "GLOBALWITS135"; //sysUser.PassWord;
            sysUser.PassWord = EncryptUtil.GetMd5(sysUser.UserName + oriPwd);
            //配置其他参数
            sysUser.UserStatus = true;
            sysUser.WeChatIsBind = false;
            sysUser.DingDingIsBind = false;
            sysUser.AllowUserNameLogin = true;
            sysUser.AllowTelphoneLogin = string.IsNullOrEmpty(sysUser.Telephone) ? false : true;
            sysUser.AllowMessageAuthCodeLogin = string.IsNullOrEmpty(sysUser.Telephone) ? false : true;
            sysUser.AllowEmailLogin = string.IsNullOrEmpty(sysUser.Email) ? false : true;
            sysUser.AllowWechatLogin = false;//新建用户设置微信绑定状态为否
            sysUser.AllowDingdingLogin = false;//新建用户设置钉钉绑定状态为否
            sysUser.MaxSaveCustomer = GetMaxSaveCustomer();
            sysUser.HasSupplementInfo = false;
            sysUser.UserType = DbOpe_sys_roleinusertype.Instance.GetUserTypeByRoleId(addUser_In.RoleId);
            if (string.IsNullOrEmpty(addUser_In.OrganizationId))
                sysUser.OrganizationId = Guid.Empty.ToString();
            //添加员工,加入到sql队列中
            DbOpe_sys_user.Instance.InsertQueueWhichDeleteIsBool(sysUser);
            //配置对应关系List<sys_userinrole>
            /*////计算对应关系集合toInsRoleList
            var toInsRoleList = addUser_In.RoleIds.Split(',').ToList();
            ////若toInsRoleList含有元素，执行配置操作*/
            if (!string.IsNullOrEmpty(addUser_In.RoleId))
                InsertUserInRole(addUser_In.RoleId, sysUser.Id);
            //发送短信 根据514需求不再发送短信
            //Com_PhoneHelper.SendAddUserInfo(sysUser.Telephone, sysUser.Name, sysUser.UserName, oriPwd);
            //添加日志

            //如果用户类型是管理者,则添加初始化职称为“区域总监”, 并调用业绩计算接口
            if (sysUser.UserType == 1)
            {
                if (sysUser.OrganizationId == "00000000-0000-0000-0000-000000000000")
                {
                    BLL_UserPromotion.Instance.BackgroundUserPromotionInit(new List<string>()
                    {
                        sysUser.Id,
                    });
                }
                else
                    BLL_UserPromotion.Instance.LeadUserPromotionInit(new List<string>()
                    {
                        sysUser.Id,
                    });
                //调用业绩计算接口 2023.12.21添加
                var orgType = DbOpe_sys_organization.Instance.QueryByPrimaryKey(sysUser.OrganizationId).OrgType;
                DbOpe_sys_organization_params.Instance.ChangeOrgManager(new ChangeOrgManagerParams { OrgId = sysUser.OrganizationId, OrgType = orgType.ToInt(), NewManagerUserId = sysUser.Id, NewManagerUserName = sysUser.Name });
            }
            //如果用户类型是职员,则添加初始化职称为“职员”
            else if (sysUser.UserType == 2)
            {
                BLL_UserPromotion.Instance.ClerkUserPromotionInit(new List<string>()
                {
                    sysUser.Id,
                });
            }

            //执行sql队列
            DbOpe_sys_user.Instance.SaveQueues();
        }


        /// <summary>
        /// 获取新建用户可保存最大客户数
        /// </summary>
        /// <returns></returns>
        private int GetMaxSaveCustomer()
        {
            var retMaxSaveCustomer = 0;
            List<Db_sys_comparam_retain> comparamRetains = DbOpe_sys_comparam_retain.Instance.GetAllComparamRetains(1);
            comparamRetains = comparamRetains.Where(w => w.PerformanceAmountStart == 0).ToList();
            comparamRetains.Sort((a, b) =>
            {
                return a.PerformanceAmountEnd.Value.CompareTo(b.PerformanceAmountEnd);
            });
            var minRetains = comparamRetains.FirstOrDefault();
            if (minRetains is not null)
            {
                retMaxSaveCustomer = minRetains.RetainCustomers == null ? 0 : minRetains.RetainCustomers.ToInt();
            }

            return retMaxSaveCustomer;
        }


        /// <summary>
        /// 生成员工账号
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public string CreateUserName()
        {
            //随机获取20个账号
            List<string> nameList = new RandomUtil(7, Enum_RandomFormat.OnlyNumber).SetPrefix("hs").GetRandoms(20).ToList();
            //判断随机获取的20个账号在数据库中是否重复
            List<string> dbExistNameList = DbOpe_sys_user.Instance.CheckUserName(nameList);
            //判断随机获取的20个账号在缓存中是否重复
            List<string> redisExistNameList = RedisCache.UserName.CheckUserName(nameList);
            //取差集，获取不重复的账号
            List<string> restNameList = nameList.Except(dbExistNameList).Except(redisExistNameList).ToList();
            if (restNameList.Count == 0)
                throw new ApiException("账号生成失败，请重新生成");
            RedisCache.UserName.SaveUser(restNameList[0]);
            return restNameList[0];
        }

        /// <summary>
        /// 修改员工信息
        /// </summary>
        /// <param name="updUser_In"></param>
        public void UpdateUser(UpdateUser_In updUser_In)
        {
            /*//验证员工状态是否为停用
            if (DbOpe_sys_user.Instance.IsUserEnable(updUser_In.Id))
                throw new ApiException("当前员工为启用状态，无法修改");*/
            if ((updUser_In.RoleId == "9fbe8362-b96f-46da-902a-aae767aa8b94" || updUser_In.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026") && (string.IsNullOrEmpty(updUser_In.OrganizationId) || updUser_In.OrganizationId == Guid.Empty.ToString()))
                throw new ApiException("所属组织不可为空");
            //如果添加销售主管角色，验证加入的组织中是否已存在销售主管
            var orgHasManager = DbOpe_sys_user.Instance.CheckOrgHasManager(updUser_In.OrganizationId, updUser_In.Id);
            if (updUser_In.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026" && orgHasManager)
                throw new ApiException("该组织已存在销售主管");
            //验证手机号是否重复
            if (!string.IsNullOrEmpty(updUser_In.Telephone) && DbOpe_sys_user.Instance.CheckUserPhone(updUser_In.Telephone, updUser_In.Id))
                throw new ApiException("该手机号已被注册");
            //验证邮箱号是否重复
            if (!string.IsNullOrEmpty(updUser_In.Email) && DbOpe_sys_user.Instance.CheckUserEmail(updUser_In.Email, updUser_In.Id))
                throw new ApiException("该邮箱已被注册");
            //验证身份证号是否合法
            if (!string.IsNullOrEmpty(updUser_In.IDNo))
            {
                var year = updUser_In.IDNo.Substring(6, 4).ToInt();
                string? regex;
                if ((year % 400 == 0) || (year % 100 != 0 && (year % 4 == 0)))//闰年
                    regex = "^[1-9]\\d{5}(19|20)\\d{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2]\\d|3[0-1])|(04|06|09|11)(0[1-9]|[1-2]\\d|30)|02(0[1-9]|[1-2]\\d))\\d{3}[\\dXx]$";
                else//平年
                    regex = "^[1-9]\\d{5}(19|20)\\d{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2]\\d|3[0-1])|(04|06|09|11)(0[1-9]|[1-2]\\d|30)|02(0[1-9]|1\\d|2[0-8]))\\d{3}[\\dXx]$";
                if (!Regex.IsMatch(updUser_In.IDNo, regex))
                    throw new ApiException("身份证号格式错误");
                //验证身份证号是否重复
                if (DbOpe_sys_user.Instance.CheckUserIdNo(updUser_In.IDNo, updUser_In.Id))
                    throw new ApiException("身份证号已存在");
            }

            #region 20240802 wd 改bug （变化组织角色时 相应的管理者、客户记录没变化）
            //获取历史员工信息
            var oldSysuser = DbOpe_sys_user.Instance.QueryByPrimaryKey(updUser_In.Id);
            //获取历史角色ID
            var oldRoleId = DbOpe_sys_userinrole.Instance.GetSingleRoleIdByUserId(updUser_In.Id);
            #endregion
            //获取当前的员工信息
            var hisSysUser = DbOpe_sys_user.Instance.QueryByPrimaryKey(updUser_In.Id);
            //处理用户密码
            if (string.IsNullOrEmpty(updUser_In.PassWord))
                updUser_In.PassWord = hisSysUser.PassWord;
            else
            {
                //校验密码
                //配置密码
                updUser_In.PassWord = EncryptUtil.GetMd5(updUser_In.UserName + updUser_In.PassWord);
            }
            Db_sys_user sysUser = updUser_In.MappingTo(hisSysUser);
            //配置对应关系List<sys_userinrole>
            var hisRoleId = DbOpe_sys_userinrole.Instance.GetSingleRoleIdByUserId(sysUser.Id);
            if (!string.IsNullOrEmpty(hisRoleId) && !hisRoleId.Equals(updUser_In.RoleId))
            {//变更角色 
                //从普通销售变更到销售主管,调用业绩计算方法
                if (oldRoleId == "9fbe8362-b96f-46da-902a-aae767aa8b94" && updUser_In.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026")
                {
                    var orgType = DbOpe_sys_organization.Instance.QueryByPrimaryKey(updUser_In.OrganizationId).OrgType;
                    DbOpe_sys_organization_params.Instance.ChangeOrgManager(new ChangeOrgManagerParams { OrgId = updUser_In.OrganizationId, OrgType = orgType.ToInt(), NewManagerUserId = updUser_In.Id, NewManagerUserName = updUser_In.Name });
                }
                //从销售主管变为普通销售,调用业绩计算方法, 人员信息传空值
                else if (oldRoleId == "313f9d6b-8feb-462c-9bdb-703436a37026" && updUser_In.RoleId == "9fbe8362-b96f-46da-902a-aae767aa8b94")
                {
                    var orgType = DbOpe_sys_organization.Instance.QueryByPrimaryKey(updUser_In.OrganizationId).OrgType;
                    DbOpe_sys_organization_params.Instance.ChangeOrgManager(new ChangeOrgManagerParams { OrgId = updUser_In.OrganizationId, OrgType = orgType.ToInt(), NewManagerUserId = "", NewManagerUserName = "" });
                }

                DbOpe_sys_userinrole.Instance.DelSingleUserInRoleByUserIdQueue(hisRoleId, sysUser.Id, sysUser.UpdateUser);
                InsertUserInRole(updUser_In.RoleId, sysUser.Id);
            }
            else if (string.IsNullOrEmpty(hisRoleId))
            {
                InsertUserInRole(updUser_In.RoleId, sysUser.Id);
            }
            //判断是否修改组织
            if (oldSysuser.OrganizationId != updUser_In.OrganizationId)
            {
                var parentOrgList = DbOpe_sys_organization.Instance.GetParentOrgList(updUser_In.OrganizationId);
                var moveParams = new UserMoveCustomerLogParams()
                {
                    UserId = updUser_In.Id,
                    TargetOrgId = string.IsNullOrEmpty(updUser_In.OrganizationId) ? Guid.Empty.ToString() : updUser_In.OrganizationId,
                    New_OrgDivisionId = parentOrgList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id,
                    New_OrgDivisionName = parentOrgList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName,
                    New_OrgBrigadeId = parentOrgList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id,
                    New_OrgBrigadeName = parentOrgList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName,
                    New_OrgRegimentId = parentOrgList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id,
                    New_OrgRegimentName = parentOrgList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName,
                };
                DbOpe_crm_customer_org_log.Instance.UserMoveCustomerLog(moveParams);
            }

            //配置其他参数
            if (string.IsNullOrEmpty(updUser_In.OrganizationId))
                sysUser.OrganizationId = Guid.Empty.ToString();
            sysUser.UserType = DbOpe_sys_roleinusertype.Instance.GetUserTypeByRoleId(updUser_In.RoleId);
            sysUser.AllowTelphoneLogin = !string.IsNullOrEmpty(sysUser.Telephone);
            sysUser.AllowMessageAuthCodeLogin = !string.IsNullOrEmpty(sysUser.Telephone);
            sysUser.AllowEmailLogin = !string.IsNullOrEmpty(sysUser.Email);
            sysUser.UpdateUser = UserTokenInfo.id;
            sysUser.UpdateDate = DateTime.Now;
            //更新员工信息，加入到sql队列中
            DbOpe_sys_user.Instance.UpdateQueue(sysUser);

            #region  原单个人员对应多个角色 2023.10.11停用，现改为单个人员对应单个角色
            /* 
            ////获取用户当前的关联RoleIds
            var hisRoleList = DbOpe_sys_userinrole.Instance.GetRoleByUserId(sysUser.Id);
            ////获取要修改的关联RoleIds
            var curRoleList = updUser_In.RoleIds.Split(',').ToList();
            ////计算hisRoleList和curRoleList的交集
            var interRoleList = hisRoleList.Intersect(curRoleList).ToList();
            ////计算hisRoleList和interRoleList的差集，记为待删除集合
            var toDelRoleList = hisRoleList.Except(interRoleList).ToList();
            ////删除不需要的关联关系集合
            DbOpe_sys_userinrole.Instance.DelUserInRoleByUserIdQueue(toDelRoleList, sysUser.Id, sysUser.UpdateUser);
            ////计算curRoleList和interRoleList的差集，记为待增加集合
            var toInsRoleList = curRoleList.Except(interRoleList).ToList();
            ////若带增加集合包含元素，执行新增关联关系集合
            if (toInsRoleList.Count > 0)
                InsertUserInRole(toInsRoleList, sysUser.Id);
            */
            #endregion
            //发送短信(模板尚未添加)
            //Com_PhoneHelper.SendAddUserInfo(sysUser.Telephone, sysUser.Name, sysUser.UserName, orgPwd);
            //执行sql队列
            DbOpe_sys_user.Instance.SaveQueues();
        }



        /// <summary>
        /// 插入userinrole的关联关系
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="userId"></param>
        private void InsertUserInRole(string roleId, string userId)
        {
            Db_sys_userinrole sysUserRole = new Db_sys_userinrole();
            sysUserRole.Id = Guid.NewGuid().ToString();
            sysUserRole.UserId = userId;
            sysUserRole.RoleId = roleId;
            sysUserRole.Deleted = false;
            sysUserRole.CreateUser = UserTokenInfo.id;
            sysUserRole.CreateDate = DateTime.Now;
            //添加对应关系,加入到sql队列中
            DbOpe_sys_userinrole.Instance.InsertQueue(sysUserRole);
        }

        /// <summary>
        /// 补充员工信息
        /// </summary>
        /// <param name="supplementUser_In"></param>
        public void SupplementUser(SupplementUser_In supplementUser_In)
        {
            var userId = TokenModel.Instance.id;
            //获取当前的员工信息
            var sysUser = DbOpe_sys_user.Instance.QueryByPrimaryKey(userId);
            //验证手机号是否重复
            if (!string.IsNullOrEmpty(supplementUser_In.Telephone) && DbOpe_sys_user.Instance.CheckUserPhone(supplementUser_In.Telephone, userId))
                throw new ApiException("该手机号已被注册");
            //验证邮箱号是否重复
            if (!string.IsNullOrEmpty(supplementUser_In.Email) && DbOpe_sys_user.Instance.CheckUserEmail(supplementUser_In.Email, userId))
                throw new ApiException("该邮箱已被注册");
            #region 验证身份证号是否合法
            var year = supplementUser_In.IDNo.Substring(6, 4).ToInt();
            string? regex;
            if ((year % 400 == 0) || (year % 100 != 0 && (year % 4 == 0)))//闰年
                regex = "^[1-9]\\d{5}(19|20)\\d{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2]\\d|3[0-1])|(04|06|09|11)(0[1-9]|[1-2]\\d|30)|02(0[1-9]|[1-2]\\d))\\d{3}[\\dXx]$";
            else//平年
                regex = "^[1-9]\\d{5}(19|20)\\d{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2]\\d|3[0-1])|(04|06|09|11)(0[1-9]|[1-2]\\d|30)|02(0[1-9]|1\\d|2[0-8]))\\d{3}[\\dXx]$";
            if (!Regex.IsMatch(supplementUser_In.IDNo, regex))
                throw new ApiException("身份证号格式错误");
            //验证身份证号是否重复
            if (DbOpe_sys_user.Instance.CheckUserIdNo(supplementUser_In.IDNo, userId))
                throw new ApiException("身份证号已存在");
            #endregion
            //加密密码
            sysUser.PassWord = EncryptUtil.GetMd5(sysUser.UserName + supplementUser_In.PassWord);
            sysUser.Email = supplementUser_In.Email;
            sysUser.IDNo = supplementUser_In.IDNo;
            sysUser.Telephone = supplementUser_In.Telephone;
            sysUser.HasSupplementInfo = true;
            sysUser.AllowTelphoneLogin = true;
            sysUser.AllowMessageAuthCodeLogin = true;
            sysUser.AllowEmailLogin = true;
            //提交修改
            DbOpe_sys_user.Instance.UpdateData(sysUser);
        }


        /// <summary>
        /// 删除用户信息
        /// 多条记录全部执行成功则返回成功，否则返回失败。只做逻辑删除，修改Deleted字段为false
        /// </summary>
        /// <param name="Ids"></param>
        public void DeleteUser(string Ids)
        {
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("用户主键不可为空");
            //将ids转化为List
            List<string> idList = Ids.Split(",").ToList();
            if (DbOpe_sys_user.Instance.HasEnableUser(idList))
                throw new ApiException("所选员工中存在启用的员工，无法删除");
            //执行删除,添加到sql队列中
            DbOpe_sys_user.Instance.DeleteUser(idList, UserTokenInfo.id);
            //删除userinrole关联关系,添加到sql队列中
            DbOpe_sys_userinrole.Instance.DelUserInRoleByRoleIdsQueue(idList, UserTokenInfo.id);
            //执行sql队列
            DbOpe_sys_user.Instance.SaveQueues();
        }

        /// <summary>
        /// 修改用户状态信息
        /// 变更用户状态为启用、停用，多条记录全部执行成功则返回成功，否则返回失败。
        /// </summary>
        /// <param name="updUserSta_In"></param>
        public void UpdateUserStatus(UpdateUserStatus_In updUserSta_In)
        {
            /*
              //从普通销售变更到销售主管,调用业绩计算方法
                if (oldRoleId == "9fbe8362-b96f-46da-902a-aae767aa8b94" && updUser_In.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026")
                {
                    var orgType = DbOpe_sys_organization.Instance.QueryByPrimaryKey(updUser_In.OrganizationId).OrgType;
                    DbOpe_sys_organization_params.Instance.ChangeOrgManager(new ChangeOrgManagerParams { OrgId = updUser_In.OrganizationId, OrgType = orgType.ToInt(), NewManagerUserId = updUser_In.Id, NewManagerUserName = updUser_In.Name });
                }
                //从销售主管变为普通销售,调用业绩计算方法, 人员信息传空值
                else if (oldRoleId == "313f9d6b-8feb-462c-9bdb-703436a37026" && updUser_In.RoleId == "9fbe8362-b96f-46da-902a-aae767aa8b94")
                {
                    var orgType = DbOpe_sys_organization.Instance.QueryByPrimaryKey(updUser_In.OrganizationId).OrgType;
                    DbOpe_sys_organization_params.Instance.ChangeOrgManager(new ChangeOrgManagerParams { OrgId = updUser_In.OrganizationId, OrgType = orgType.ToInt(), NewManagerUserId = "", NewManagerUserName = "" });
                }
             
             
             */

            //将ids转化为List
            List<string> userIds = updUserSta_In.UserList.Select(e => e.Id).ToList();
            //查看修改的员工中存在销售主管
            var specialUserList = updUserSta_In.UserList.Where(e => e.RoleIdList.Contains("313f9d6b-8feb-462c-9bdb-703436a37026")).ToList();
            if (updUserSta_In.Status)
            {//停用变启用 
                //验证传入的员工列表中是否有已启用的员工
                if (DbOpe_sys_user.Instance.HasEnableUser(userIds))
                    throw new ApiException("所选员工中存在已启用的员工，无法启用");
                //验证传入的员工列表中是否有管理者冲突的情况
                if (DbOpe_sys_user.Instance.CheckOrgHasManager(userIds))
                    throw new ApiException("所选员工所在组织已存在销售主管，无法启用");
                //如果启用的员工中存在销售主管
                specialUserList.ForEach(user =>
                {
                    DbOpe_sys_organization_params.Instance.ChangeOrgManager(new ChangeOrgManagerParams { OrgId = user.OrganizationId, OrgType = user.OrganizationType.ToInt(), NewManagerUserId = user.Id, NewManagerUserName = user.Name });
                });
                DbOpe_sys_user.Instance.EnableUser(userIds, UserTokenInfo.id);
            }
            else
            {//启用变停用
                //验证传入的员工列表中是否有已停用的员工
                if (DbOpe_sys_user.Instance.HasStoppedUser(userIds))
                    throw new ApiException("所选员工中存在已停用的员工，无法停用");
                /* 停用员工不需要判断是否存在私有池客户，2024.7.29修改
                if (DbOpe_sys_user.Instance.CheckUserPrivatepoolState(userIds))
                    throw new ApiException("所选员工的私有池存在还有客户信息，无法停用");*/
                //如果停用的员工中存在销售主管
                specialUserList.ForEach(user =>
                {
                    DbOpe_sys_organization_params.Instance.ChangeOrgManager(new ChangeOrgManagerParams { OrgId = user.OrganizationId, OrgType = user.OrganizationType.ToInt(), NewManagerUserId = "", NewManagerUserName = "" });
                });

                DbOpe_sys_user.Instance.StopUser(userIds, UserTokenInfo.id);
            }
            //执行更新
            DbOpe_sys_user.Instance.SaveQueues();
        }

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="userId"></param>
        public void ResetUserPassword(string userId)
        {
            //查找user的手机号
            var sysUser = DbOpe_sys_user.Instance.QueryByPrimaryKey(userId);
            //生成8位密码
            string newPwd = new RandomUtil(8, Enum_RandomFormat.NumberAndLetter).GetRandom();
            //执行更新,密码为用户的登录名+新密码，再进行md5加密
            DbOpe_sys_user.Instance.ResetUserPassword(userId, EncryptUtil.GetMd5(sysUser.UserName + newPwd));
            //提交事务
            DbOpe_sys_user.Instance.SaveQueues();
            //发送短信(模板尚未添加)
            Com_PhoneHelper.SendResetUserPwd(sysUser.Telephone, sysUser.Name, newPwd);
        }

        /// <summary>
        /// 绑定用户微信
        /// </summary>
        /// <param name="bindUserWeChat_In"></param>
        public void BindUserWeChat(BindUserWeChat_In bindUserWeChat_In)
        {
            //根据code获取openId
            string openId = "";
            //绑定openId
            DbOpe_sys_user.Instance.BindUserWeChat(bindUserWeChat_In.Id, openId);
        }

        /// <summary>
        /// 根据用户token获取用户姓名及组织名称
        /// </summary>
        /// <returns></returns>
        public string GetUserNameByUserToken()
        {
            //获取用户信息
            var user = DbOpe_sys_user.Instance.GetDbSysUserById(UserId);
            //获取组织信息树，从当前组织追溯出所有上级组织
            var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
            //声明返回字符串，格式为：姓名(上级组织/上级组织/当前组织)
            var retStr = user.Name;
            //循环组织树列表进行排序，排序规则为上级组织/下级组织
            for (int i = 0; i < orgList.Count; i++)
            {
                if (i == 0)
                    retStr += "(";
                else
                    retStr += "/";
                retStr += orgList[i].OrgName;
                if (i == orgList.Count - 1)
                    retStr += ")";
            }
            return retStr;
        }

        /// <summary>
        /// H5端 查询用户列表
        /// </summary>
        /// <param name="searchUserListIn"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<H5SearchUserList_Out> GetH5SearchUserList(H5SearchUserList_In searchUserListIn, ref int total)
        {
            var userList = DbOpe_sys_user.Instance.GetH5SearchUserList(searchUserListIn, ref total);
            if (userList != null && userList.Count > 0 && searchUserListIn.HasLeftCustomerNumber == 1)
            {
                Dictionary<string, GetMaxSavePrivateCustomerNumLeft_OUT> userNumLeftDict = DbOpe_crm_customer_privatepool.Instance.GetMaxSavePrivateCustomerNumLeft();
                foreach (H5SearchUserList_Out item in userList)
                {
                    if (userNumLeftDict.ContainsKey(item.Id))
                    {
                        item.LeftCustomerNumber = userNumLeftDict[item.Id].LeftCustomerNumber;
                    }
                }
            }
            return userList;
        }

        /// <summary>
        /// 根据用户token获取用户姓名及组织名称
        /// </summary>
        /// <returns></returns>
        public List<GetSameOrgUserInfoByToken_Out> GetSameOrgUserInfoByToken()
        {
            //获取用户信息
            var user = DbOpe_sys_user.Instance.GetDbSysUserById(UserId);
            //获取同组织其他人员信息
            return DbOpe_sys_user.Instance.GetSameOrgUserInfoByToken(user.OrganizationId);
        }

        /// <summary>
        /// 获取中文姓名的拼音字符串
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public string GetNamePinyinStr(string name)
        {
            string ChineseReg = "^[\\u4E00-\\u9FA5]+$";
            var pySb = new StringBuilder();
            foreach (var itemChar in name)
            {
                //过滤非汉字的字符，直接返回
                var reg = new Regex(ChineseReg);
                if (!reg.IsMatch(itemChar.ToString()))
                    pySb.Append(itemChar);
                else
                {
                    var chineseChar = new ChineseChar(itemChar);
                    var pyStr = chineseChar.Pinyins.First().ToLower();
                    pySb.Append(pyStr.Substring(0, pyStr.Length - 1));
                }
            }
            return pySb.ToString();
        }
    }
}