﻿using Quartz;

namespace CRM2_API.Common.Utils
{
    /// <summary>
    /// 定时任务一些辅助方法
    /// </summary>
    public class ScheduledTaskUtil
    {
        /// <summary>
        /// 根据 Datetime获取 cron表达式
        /// </summary>
        /// <param name="time">时间</param>
        /// <returns></returns>
        public static string GetCronByDateTime(DateTime time)
        {
            return $"{time.Second} {time.Minute} {time.Hour} {time.Day} {time.Month} ? {time.Year}";
        }

        /// <summary>
        /// 校验Cron表达式是否正确
        /// </summary>
        /// <param name="cronExpression"></param>
        /// <returns></returns>
        public static bool CheckCronExpression(string cronExpression)
        {
            return CronExpression.IsValidExpression(cronExpression);
        }
    }
}