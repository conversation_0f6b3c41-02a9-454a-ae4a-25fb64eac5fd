using CRM2_API.Common;
using CRM2_API.Common.Utils;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.InvoiceSystem;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel;
using System.Reflection;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.InvoiceSystem.VM_InvoiceSystem;
using CRM2_API.Common.Cache;
using System.IO;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 统计查询部分
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        /// <summary>
        /// 导出发票查询记录
        /// </summary>
        /// <param name="param">查询条件</param>
        /// <returns>发票查询结果</returns>
        public Stream ExportInvoiceByConditions(InvoiceQuery_In_Export param)
        {
            try
            {
                var list = ExcuteInvoiceSearch(param)
                        .OrderByDescending(i => i.InvoicingDate)
                        .OrderByDescending(i => i.Id)
                        .ToList();
                var explist = list.MappingTo<List<InvoiceQuery_Out_Export>>();
    
                // 使用新增的ExportWithStyle方法导出Excel
                ExcelExporterNPOI exporter = new ExcelExporterNPOI();
                return exporter.ExportWithStyle(explist, "发票记录");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"导出发票查询记录异常: {ex.Message}");
                throw new ApiException("导出发票记录失败");
            }
        }
        
        /// <summary>
        /// 从发票角度查询发票记录
        /// </summary>
        /// <param name="param">查询条件</param>
        /// <returns>发票查询结果</returns>
        public ApiTableOut<InvoiceQueryResult_Out> SearchInvoiceByConditions(InvoiceQuery_In param)
        {
            int total = 0;
            var result = new List<InvoiceQueryResult_Out>();

            try
            {
                var query = ExcuteInvoiceSearch(param);
                // 获取统计结果
                // 1. 获取发票总金额    
                var amountStatQuery = query.Clone();
                var totalInvoiceAmount = amountStatQuery.MergeTable().Sum(r => r.InvoicedAmount);

                // 2. 获取发票总数量
                var countStatQuery = query.Clone();
                var totalInvoiceCount = countStatQuery.Count();

                // 3. 获取正数发票总金额和数量
                var positiveStatQuery = query.Clone().Where(i => i.TransactionType == 1);
                var positiveInvoiceAmount = positiveStatQuery.MergeTable().Sum(r => r.InvoicedAmount);
                var positiveInvoiceCount = positiveStatQuery.MergeTable().Count();

                // 4. 获取负数发票总金额和数量
                var negativeStatQuery = query.Clone().Where(i => i.TransactionType == 2);
                var negativeInvoiceAmount = negativeStatQuery.MergeTable().Sum(r => r.InvoicedAmount);
                var negativeInvoiceCount = negativeStatQuery.MergeTable().Count();

                // 计算实际总金额（正数票减去负数票）
                var actualInvoiceAmount = (positiveInvoiceAmount != null ? positiveInvoiceAmount : 0M) - (negativeInvoiceAmount != null ? negativeInvoiceAmount : 0M);

                // 分页查询
                var data = query
                    .OrderByDescending(i => i.InvoicingDate)
                    .OrderByDescending(i => i.Id)
                    .ToPageList(param.PageNumber, param.PageSize, ref total);

                // 为枚举填充显示名称
                foreach (var item in data)
                {
                    item.RowNum = data.IndexOf(item) + 1 + (param.PageNumber - 1) * param.PageSize;
                }

                result = data;

                // 创建统计结果字典
                var statisticsResult = new Dictionary<string, string>
                {
                    { "发票金额", actualInvoiceAmount.ToString("F2") },
                    { "发票数量", totalInvoiceCount.ToString() },
                    { "正数发票金额", positiveInvoiceAmount.ToString("F2") },
                    { "正数发票数量", positiveInvoiceCount.ToString() },
                    { "负数发票金额", negativeInvoiceAmount.ToString("F2") },
                    { "负数发票数量", negativeInvoiceCount.ToString() }
                };

                return new ApiTableOut<InvoiceQueryResult_Out>
                {
                    Data = result,
                    Total = total,
                    StatisticsResult = statisticsResult
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"从发票角度查询发票记录异常: {ex.Message}");
                throw new ApiException("查询发票记录失败");
            }
        }
        
        /// <summary>
        /// 执行发票查询Sql
        /// </summary>
        /// <param name="param">查询条件</param>
        /// <returns>结果</returns>
        private ISugarQueryable<InvoiceQueryResult_Out> ExcuteInvoiceSearch(IInvoiceQueryParams param)
        {
                var db = DbContext.Crm2Db;

                // 构建查询
                var query = db.Queryable<Db_crm_invoice>()
                    .LeftJoin<Db_crm_invoice_application>((i, a) => i.InvoiceApplicationId == a.Id && a.Deleted != true)
                    .LeftJoin<Db_crm_contract>((i, a, c) => a.ContractId == c.Id && c.Deleted != true)
                    .LeftJoin<Db_crm_invoice_receipt_matching>((i, a, c, m) => i.Id == m.InvoiceId && m.Deleted != true)
                    .LeftJoin<Db_v_contractreceiptregistercollectioninfo>((i, a, c, m, r) => m.ReceiptId == r.ContractReceiptRegisterId)
                    .LeftJoin<Db_crm_invoice>((i, a, c, m, r, o) => i.RelatedInvoiceId == o.Id && o.Deleted != true)
                    .Where((i, a, c,m,r,o) => i.Deleted != true);

                #region 应用过滤条件
                
                // 应用过滤条件
                if (param.IsPositiveInvoice.HasValue)
                {
                    if (param.IsPositiveInvoice.Value)
                    {
                    query = query.Where((i, a, c, m, r, o) => i.TransactionType == 1);
                }
                else
                {
                    query = query.Where((i, a, c, m, r, o) => i.TransactionType == 2);
                }
            }

                if (param.InvoiceStatus.HasValue)
                {
                    query = query.Where((i, a, c,m,r,o) => i.RefundStatus == param.InvoiceStatus.Value);
                }

                if (param.InvoiceType.HasValue)
                {
                    query = query.Where((i, a, c,m,r,o) => i.InvoiceType == param.InvoiceType.Value);
                }

                if (param.PaymentMethod.HasValue)
                {
                    query = query.Where((i, a, c,m,r,o) => !SqlFunc.IsNullOrEmpty(r.PaymentMethod)&&r.PaymentMethod == param.PaymentMethod.Value);
                }

                if (!string.IsNullOrEmpty(param.ReceivingCompany))
                {
                    query = query.Where((i, a, c,m,r,o) => i.BillingCompany.Contains(param.ReceivingCompany));
                }

                if (param.InvoicingDateStart.HasValue)
                {
                    query = query.Where((i, a, c,m,r,o) => i.InvoicingDate >= param.InvoicingDateStart.Value);
                }

                if (param.InvoicingDateEnd.HasValue)
                {
                    var endDate = param.InvoicingDateEnd.Value.GetDaysEnd();    
                    query = query.Where((i, a, c,m,r,o) => i.InvoicingDate <= endDate);
                }

                if (!string.IsNullOrEmpty(param.InvoiceNumber))
                {
                    query = query.Where((i, a, c,m,r,o) => i.InvoiceNumber.Contains(param.InvoiceNumber));
                }

                if (!string.IsNullOrEmpty(param.InvoiceTitle))
                {
                    query = query.Where((i, a, c,m,r,o) => i.BillingHeader.Contains(param.InvoiceTitle));
                }

                if (!string.IsNullOrEmpty(param.TaxpayerIdentificationNumber))
                {
                    query = query.Where((i, a, c,m,r,o) => i.CreditCode.Contains(param.TaxpayerIdentificationNumber));
                }

                if (param.InvoiceAmountStart.HasValue)
                {
                    query = query.Where((i, a, c,m,r,o) =>i.InvoicedAmount >= param.InvoiceAmountStart.Value);
                }

                if (param.InvoiceAmountEnd.HasValue)
                {
                    query = query.Where((i, a, c,m,r,o) => i.InvoicedAmount <= param.InvoiceAmountEnd.Value);
                }

                if (!string.IsNullOrEmpty(param.InvoiceBackgroundRemark))
                {
                    query = query.Where((i, a, c,m,r,o) => i.Remark.Contains(param.InvoiceBackgroundRemark));
                }
                // 未匹配状态
                var notMatchStatus = new List<int>
                {
                    (int)EnumInvoiceMatchingStatus.NotInvoiced,
                    (int)EnumInvoiceMatchingStatus.NotReceived,
                    (int)EnumInvoiceMatchingStatus.WaitingConfirm,
                    (int)EnumInvoiceMatchingStatus.WaitingAudit,
                    (int)EnumInvoiceMatchingStatus.Rejected
                };
                
                if (param.IsMatching != null && param.IsMatching.Any())
                {
                    // 创建一个状态列表用于收集所有需要查询的匹配状态
                    var matchingStatusList = new List<int>();

                    foreach (var matchingStatus in param.IsMatching)
                    {
                        if (matchingStatus == EnumInvoiceAccountMatching.Matched)
                        {
                            // 已匹配状态
                            matchingStatusList.Add((int)EnumInvoiceMatchingStatus.MatchSuccess);
                        }
                        else if (matchingStatus == EnumInvoiceAccountMatching.NotMatched)
                        {
                            // 未匹配状态：所有非"匹配成功"和非作废状态
                            matchingStatusList.AddRange(notMatchStatus);
                        }
                        else if (matchingStatus == EnumInvoiceAccountMatching.MatchedVoid)
                        {
                            // 匹配作废状态：包括"匹配成功（作废）"和"未匹配（作废）"
                            matchingStatusList.Add((int)EnumInvoiceMatchingStatus.MatchSuccessVoid);
                            matchingStatusList.Add((int)EnumInvoiceMatchingStatus.NotMatchedVoid);
                        }
                    }

                    // 使用状态列表构建查询条件
                    if (matchingStatusList.Any())
                    {
                        query = query.Where((i, a, c, m, r, o) => matchingStatusList.Contains(i.MatchingStatus));
                    }
                }
                
                if (param.IsPersonalInvoice.HasValue)
                {   
                    if (param.IsPersonalInvoice.Value)
                    {
                        query = query.Where((i, a, c,m,r,o) => i.IsPersonalInvoice == true);
                    }
                    else
                    {
                        query = query.Where((i, a, c,m,r,o) => i.IsPersonalInvoice == false);
                    }
                }
                if (param.BillingType != null)
                {
                    query = query.Where((i, a, c,m,r,o) => param.BillingType.Contains(i.BillingType));
                }
                if (!string.IsNullOrEmpty(param.ApplicantId))
                {
                    query = query.Where((i, a, c,m,r,o) => a.ApplicantId  == param.ApplicantId);
                }

                if (param.QuickSearch == EnumInvoiceQuickSearch.InvoiceBeforeReceipt)
                {
                    //先开票对应的查询条件： 是否匹配到帐：未匹配   ； 是否正数发票： 正数 ；   开票类型：先开票后付款，  发票状态：正常；
                    query = query.Where((i, a, c,m,r,o) =>
                     notMatchStatus.Contains(i.MatchingStatus) &&
                     i.TransactionType == 1 &&
                     i.BillingType == (int)EnumBillingType.InvoicingBeforePayment &&
                     i.RefundStatus == (int)EnumInvoiceRefundStatus.Normal);
                }

            // // 排序
            // if (!string.IsNullOrEmpty(param.SortField))
            // {
            //     query = query.OrderByPropertyName(param.SortField, param.IsDESC ? OrderByType.Desc : OrderByType.Asc);
            // }
            // else
            // {
            //     // 默认按创建时间倒序
            //     query = query.OrderBy((i, a, c) => i.CreateDate, OrderByType.Desc);
            // }
            #endregion
                return query.Select((i, a, c, m, r, o) => new InvoiceQueryResult_Out
                {
                    CollectingCompany = r.CollectingCompany,
                    PaymentCompanyName = SqlFunc.IsNull(r.PaymentCompanyName, ""),
                    CustomerNum = SqlFunc.IsNull(c.ContractNum, ""),
                    PaymentMethod = SqlFunc.IsNullOrEmpty(r.ContractReceiptRegisterId) ? null : r.PaymentMethod,
                    IsMatching = i.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess
                        ? EnumInvoiceAccountMatching.Matched
                        : (i.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccessVoid || i.MatchingStatus == (int)EnumInvoiceMatchingStatus.NotMatchedVoid
                            ? EnumInvoiceAccountMatching.MatchedVoid
                            : EnumInvoiceAccountMatching.NotMatched),
                    InvoiceBackgroundRemark = i.Remark,
                    ApplicantId = a.ApplicantId,
                    RelatedInvoiceDate = SqlFunc.IIF(o.Id != null && o.InvoicingDate != null, o.InvoicingDate.Value.ToString("yyyy-MM-dd"), ""),
                    TotalAmount = i.TransactionType == 1 ? i.TotalAmount : -i.TotalAmount,
                    InvoicedAmount = i.TransactionType == 1 ? i.InvoicedAmount : -i.InvoicedAmount
                }, true);
        }

        /// <summary>
        /// 发票统计
        /// </summary>
        /// <param name="param">统计查询条件</param>
        /// <returns>发票统计结果</returns>
        public InvoiceStatistics_Out GetInvoiceDetailStatistics(InvoiceStatisticsQuery_In param)
        {
            try
            {
                var db = DbContext.Crm2Db;

                // 构建查询
                var query = db.Queryable<Db_crm_invoice>()
                    .LeftJoin<Db_crm_invoice_application>((i, a) => i.InvoiceApplicationId == a.Id && a.Deleted != true)
                    .LeftJoin<Db_crm_contract>((i, a, c) => a.ContractId == c.Id && c.Deleted != true)
                    .LeftJoin<Db_crm_invoice_receipt_matching>((i, a, c, m) => i.Id == m.InvoiceId && m.Deleted != true)
                    .LeftJoin<Db_v_contractreceiptregistercollectioninfo>((i, a, c, m, r) => m.ReceiptId == r.ContractReceiptRegisterId)
                    .Where((i, a, c, m, r) => i.Deleted != true && i.Deleted != true);

                // 应用过滤条件
                if (param.InvoiceType.HasValue)
                {
                    query = query.Where((i, a, c, m, r) => i.InvoiceType == param.InvoiceType.Value);
                }

                if (param.InvoicingMonthStart.HasValue)
                {
                    var startDate = param.InvoicingMonthStart.Value.GetMonthStart();
                    query = query.Where((i, a, c, m, r) => i.InvoicingDate >= startDate);
                }

                if (param.InvoicingMonthEnd.HasValue)
                {
                    var endDate = param.InvoicingMonthEnd.Value.GetMonthEnd().GetDaysEnd();
                    query = query.Where((i, a, c, m, r) => i.InvoicingDate <= endDate);
                }

                if (!string.IsNullOrEmpty(param.ReceivingCompany))
                {
                    query = query.Where((i, a, c, m, r) => i.BillingCompany.Contains(param.ReceivingCompany));
                }

                if (param.PaymentMethod.HasValue)
                {
                    query = query.Where((i, a, c, m, r) => !SqlFunc.IsNullOrEmpty(r.PaymentMethod) && r.PaymentMethod == param.PaymentMethod.Value);
                }

                // 按数量统计
                var quantityStats = new List<InvoiceQuantityStatistics>();

                // 复制基础查询用于统计
                var countQuery = query;

                if (param.StatisticsDimension == 1) // 发票类型
                {
                    var result = countQuery
                        .GroupBy((i, a, c, m, r) => i.InvoiceType)
                        .Select((i, a, c, m, r) => new
                        {
                            GroupValue = i.InvoiceType,
                            PositiveCount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 1, 1, 0)),
                            NegativeCount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 2, 1, 0))
                        })
                        .ToList();

                    int rowNum = 1;
                    foreach (var item in result)
                    {
                        string itemName = GetStatisticsItemName(1, item.GroupValue.ToString());

                        quantityStats.Add(new InvoiceQuantityStatistics
                        {
                            RowNum = rowNum++,
                            StatisticsItem = itemName,
                            PositiveInvoiceCount = item.PositiveCount,
                            NegativeInvoiceCount = item.NegativeCount
                        });
                    }
                }
                else if (param.StatisticsDimension == 2) // 支付方式
                {
                    var result = countQuery
                        .Where((i, a, c, m, r) => r.ContractReceiptRegisterId != null)
                        .GroupBy((i, a, c, m, r) => r.PaymentMethod)
                        .Select((i, a, c, m, r) => new
                        {
                            GroupValue = r.PaymentMethod,
                            PositiveCount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 1, 1, 0)),
                            NegativeCount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 2, 1, 0))
                        })
                        .ToList();

                    int rowNum = 1;
                    foreach (var item in result)
                    {
                        string itemName = GetStatisticsItemName(2, item.GroupValue.ToString());

                        quantityStats.Add(new InvoiceQuantityStatistics
                        {
                            RowNum = rowNum++,
                            StatisticsItem = itemName,
                            PositiveInvoiceCount = item.PositiveCount,
                            NegativeInvoiceCount = item.NegativeCount
                        });
                    }
                }
                else // 收款单位
                {
                    var result = countQuery
                        .GroupBy(i => i.BillingCompany)
                        .Select(i => new
                        {
                            GroupValue = i.BillingCompany,
                            PositiveCount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 1, 1, 0)),
                            NegativeCount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 2, 1, 0))
                        })
                        .ToList();

                    int rowNum = 1;
                    foreach (var item in result)
                    {
                        string itemName = RedisCache.CollectingCompany.GetCollectingCompanyById(item.GroupValue)?.CollectingCompanyName ?? "";

                        quantityStats.Add(new InvoiceQuantityStatistics
                        {
                            RowNum = rowNum++,
                            StatisticsItem = itemName,
                            PositiveInvoiceCount = item.PositiveCount,
                            NegativeInvoiceCount = item.NegativeCount
                        });
                    }
                }

                // 按金额统计
                var amountStats = new List<InvoiceAmountStatistics>();

                // 复制基础查询用于统计
                var amountQuery = query;

                if (param.StatisticsDimension == 1) // 发票类型
                {
                    var result = amountQuery
                        .GroupBy(i => i.InvoiceType)
                        .Select(i => new
                        {
                            GroupValue = i.InvoiceType,
                            PositiveAmount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 1, i.InvoicedAmount, 0)),
                            NegativeAmount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 2, -i.InvoicedAmount, 0))
                        })
                        .ToList();

                    int rowNum = 1;
                    foreach (var item in result)
                    {
                        string itemName = GetStatisticsItemName(1, item.GroupValue.ToString());

                        decimal positiveAmount = item.PositiveAmount;
                        decimal negativeAmount = item.NegativeAmount;

                        // 使用公式计算税额：(开票金额/1.06)*0.06 保留两位小数
                        decimal positiveTaxAmount = Math.Round((positiveAmount / 1.06M) * 0.06M, 2);
                        decimal negativeTaxAmount = Math.Round((negativeAmount / 1.06M) * 0.06M, 2);

                        decimal positiveTotal = positiveAmount - positiveTaxAmount;
                        decimal negativeTotal = negativeAmount - negativeTaxAmount;
                        decimal actualAmount = positiveAmount - negativeAmount;
                        decimal actualTaxAmount = positiveTaxAmount - negativeTaxAmount;
                        decimal actualTotal = positiveTotal - negativeTotal;

                        amountStats.Add(new InvoiceAmountStatistics
                        {
                            RowNum = rowNum++,
                            StatisticsItem = itemName,
                            PositiveInvoiceAmount = positiveTotal,
                            PositiveInvoiceTaxAmount = positiveTaxAmount,
                            PositiveInvoiceTotalAmount = positiveAmount,
                            NegativeInvoiceAmount = negativeTotal,
                            NegativeInvoiceTaxAmount = negativeTaxAmount,
                            NegativeInvoiceTotalAmount = negativeAmount,
                            ActualInvoiceAmount = actualTotal,
                            ActualInvoiceTaxAmount = actualTaxAmount,
                            ActualInvoiceTotalAmount = actualAmount
                        });
                    }
                }
                else if (param.StatisticsDimension == 2) // 支付方式
                {
                    var result = amountQuery
                        .GroupBy((i, a, c, m, r) => r.PaymentMethod)
                        .Select((i, a, c, m, r) => new
                        {
                            GroupValue = r.PaymentMethod,
                            PositiveAmount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 1, i.InvoicedAmount, 0)),
                            NegativeAmount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 2, -i.InvoicedAmount, 0))
                        })
                        .ToList();

                    int rowNum = 1;
                    foreach (var item in result)
                    {
                        string itemName = GetStatisticsItemName(2, item.GroupValue.ToString());

                        decimal positiveAmount = item.PositiveAmount;
                        decimal negativeAmount = item.NegativeAmount;

                        // 使用公式计算税额：(开票金额/1.06)*0.06 保留两位小数
                        decimal positiveTaxAmount = Math.Round((positiveAmount / 1.06M) * 0.06M, 2);
                        decimal negativeTaxAmount = Math.Round((negativeAmount / 1.06M) * 0.06M, 2);

                        decimal positiveTotal = positiveAmount - positiveTaxAmount;
                        decimal negativeTotal = negativeAmount - negativeTaxAmount;
                        decimal actualAmount = positiveAmount - negativeAmount;
                        decimal actualTaxAmount = positiveTaxAmount - negativeTaxAmount;
                        decimal actualTotal = positiveTotal - negativeTotal;

                        amountStats.Add(new InvoiceAmountStatistics
                        {
                            RowNum = rowNum++,
                            StatisticsItem = itemName,
                            PositiveInvoiceAmount = positiveTotal,
                            PositiveInvoiceTaxAmount = positiveTaxAmount,
                            PositiveInvoiceTotalAmount = positiveAmount,
                            NegativeInvoiceAmount = negativeAmount,
                            NegativeInvoiceTaxAmount = negativeTaxAmount,
                            NegativeInvoiceTotalAmount = negativeAmount,
                            ActualInvoiceAmount = actualTotal,
                            ActualInvoiceTaxAmount = actualTaxAmount,
                            ActualInvoiceTotalAmount = actualAmount
                        });
                    }
                }
                else // 收款单位
                {
                    var result = amountQuery
                        .GroupBy(i => i.BillingCompany)
                        .Select(i => new
                        {
                            GroupValue = i.BillingCompany,
                            PositiveAmount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 1, i.InvoicedAmount, 0)),
                            NegativeAmount = SqlFunc.AggregateSum(SqlFunc.IIF(i.TransactionType == 2, -i.InvoicedAmount, 0))
                        })
                        .ToList();

                    int rowNum = 1;
                    foreach (var item in result)
                    {
                        string itemName = RedisCache.CollectingCompany.GetCollectingCompanyById(item.GroupValue)?.CollectingCompanyName ?? "";

                        decimal positiveAmount = item.PositiveAmount;
                        decimal negativeAmount = item.NegativeAmount;

                        // 使用公式计算税额：(开票金额/1.06)*0.06 保留两位小数
                        decimal positiveTaxAmount = Math.Round((positiveAmount / 1.06M) * 0.06M, 2);
                        decimal negativeTaxAmount = Math.Round((negativeAmount / 1.06M) * 0.06M, 2);

                        decimal positiveTotal = positiveAmount - positiveTaxAmount;
                        decimal negativeTotal = negativeAmount - negativeTaxAmount;
                        decimal actualAmount = positiveAmount - negativeAmount;
                        decimal actualTaxAmount = positiveTaxAmount - negativeTaxAmount;
                        decimal actualTotal = positiveTotal - negativeTotal;

                        amountStats.Add(new InvoiceAmountStatistics
                        {
                            RowNum = rowNum++,
                            StatisticsItem = itemName,
                            PositiveInvoiceAmount = positiveTotal,
                            PositiveInvoiceTaxAmount = positiveTaxAmount,
                            PositiveInvoiceTotalAmount = positiveAmount,
                            NegativeInvoiceAmount = negativeAmount,
                            NegativeInvoiceTaxAmount = negativeTaxAmount,
                            NegativeInvoiceTotalAmount = negativeAmount,
                            ActualInvoiceAmount = actualTotal,
                            ActualInvoiceTaxAmount = actualTaxAmount,
                            ActualInvoiceTotalAmount = actualAmount
                        });
                    }
                }

                return new InvoiceStatistics_Out
                {
                    QuantityStatistics = quantityStats,
                    AmountStatistics = amountStats
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"发票统计异常: {ex.Message}");
                throw new ApiException("获取发票统计信息失败");
            }
        }
        
        /// <summary>
        /// 从到账角度查询发票记录
        /// </summary>
        /// <param name="param">查询条件</param>
        /// <returns>到账发票查询结果</returns>
        public ApiTableOut<ReceiptInvoiceQueryResult_Out> SearchReceiptInvoiceByConditions(ReceiptInvoiceQuery_In param)
        {
            int total = 0;
            var result = new List<ReceiptInvoiceQueryResult_Out>();
            
            try
            {
                var db = DbContext.Crm2Db;

 // 构建查询
                var query = db.Queryable<Db_crm_contract_receiptregister>()
                    .LeftJoin<Db_v_contractreceiptregistercollectioninfo>((r, ci) => r.Id == ci.ContractReceiptRegisterId)
                    .LeftJoin<Db_v_contract_receiptregister_state>((r, ci, rs) => r.Id == rs.id)
                    .LeftJoin<Db_crm_invoice_receipt_matching>((r, ci, rs, m) => r.Id == m.ReceiptId && m.Deleted == false)
                    .LeftJoin<Db_crm_invoice>((r, ci, rs, m, i) => m.InvoiceId == i.Id && i.Deleted == false)
                    .LeftJoin<Db_crm_contract>((r, ci, rs, m, i, c) => r.ContractId == c.Id && c.Deleted == false)
                    .LeftJoin<Db_crm_invoice>((r, ci, rs, m, i, c, refund) => i.Id == refund.RelatedInvoiceId && i.Deleted == false)
                    .LeftJoin<Db_crm_invoice_receipt_authority>((r, ci, rs, m, i, c, refund, authority) => r.Id == authority.ReceiptId && authority.Deleted == false)
                    .Where((r, ci, rs, m, i, c, refund, authority) => rs.State == (int)EnumRegisterState.Confirmed || rs.State == (int)EnumRegisterState.Locked)
                    .Where((r, ci, rs, m, i, c, refund, authority) => ci.Currency == (int)EnumCurrency.CNY)
                    .Where((r, ci, rs, m, i, c, refund, authority) => r.Deleted == null || r.Deleted == false)
                    .Where((r, ci, rs, m, i, c, refund, authority) => refund.Id == null || refund.TransactionType == 2)
                    .Where((r, ci, rs, m, i, c, refund, authority) => ci.PaymentMethod == (int)EnumPaymentMethod.Bank || ci.PaymentMethod == (int)EnumPaymentMethod.Cash)
                    // 只查询允许开票的到账记录
                    .Where((r, ci, rs, m, i,c, refund,authority ) => r.IsAllowInvoicing == 1);
                // 应用过滤条件
                if (param.ReceiptDateStart.HasValue)
                {
                    var startDate = param.ReceiptDateStart.Value.GetDaysStart();
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => SqlFunc.IIF(ci.ArrivalDate!=null,ci.ArrivalDate >= startDate, false));
                }

                if (param.ReceiptDateEnd.HasValue)
                {   
                    var endDate = param.ReceiptDateEnd.Value.GetDaysEnd();
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => SqlFunc.IIF(ci.ArrivalDate!=null, ci.ArrivalDate <= endDate, false));
                }
                if(param.InvoicedDateStart.HasValue )
                {
                    var startDate = param.InvoicedDateStart.Value.GetDaysStart();
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => SqlFunc.IIF(i.InvoicingDate!=null,i.InvoicingDate >= startDate, false));
                }

                if (param.InvoicedDateEnd.HasValue)
                {
                    var endDate = param.InvoicedDateEnd.Value.GetDaysEnd();
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => SqlFunc.IIF(i.InvoicingDate!=null,i.InvoicingDate <= endDate, false));
                }
                

                if (param.IsInvoiced.HasValue)
                {
                    if (param.IsInvoiced == 1)
                    {
                        query = query.Where((r, ci, rs, m, i, c, refund,authority ) => m.Id!=null && m.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess);
                    }
                    else if (param.IsInvoiced == 0)
                    {
                        query = query.Where((r, ci, rs, m, i, c, refund,authority ) => m.Id==null || m.MatchingStatus != (int)EnumInvoiceMatchingStatus.MatchSuccess) ;
                    }
                }

                if (!string.IsNullOrEmpty(param.ReceivingCompany))
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => ci.CollectingCompany.Contains(param.ReceivingCompany));
                }

                if (!string.IsNullOrEmpty(param.InvoiceTitle))
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => i.Id!=null && i.BillingHeader.Contains(param.InvoiceTitle));
                }

                if (param.PaymentMethod.HasValue)
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => ci.PaymentMethod == param.PaymentMethod.Value);
                }

                if (param.ReceiptAmountStart.HasValue)
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => ci.ArrivalAmount >= param.ReceiptAmountStart.Value);
                }

                if (param.ReceiptAmountEnd.HasValue)
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => ci.ArrivalAmount <= param.ReceiptAmountEnd.Value);
                }
                if (param.IsPersonalInvoice.HasValue)
                {
                    if (param.IsPersonalInvoice.Value) 
                    {
                        // 当查询个人发票时，只返回有发票记录且是个人发票的数据
                        query = query.Where((r, ci, rs, m, i, c, refund,authority ) => i.Id != null && i.IsPersonalInvoice == true);
                    }
                    else 
                    {
                        // 当查询非个人发票时，返回有发票记录且不是个人发票的数据，或没有发票记录的数据
                        query = query.Where((r, ci, rs, m, i, c, refund,authority ) => (i.Id != null && i.IsPersonalInvoice == false) || i.Id == null);
                    }
                }
                if (param.BillingType != null)
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => i.Id!=null && param.BillingType.Contains(i.BillingType));
                }
                if (param.RefundStatus != null)
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => i.Id!=null && param.RefundStatus.Contains(i.RefundStatus));
                }
                if (!string.IsNullOrEmpty(param.InvoiceBackgroundRemark))
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => 
                        // 将三元运算符拆分为两个条件的OR组合
                        (refund.Id != null && refund.Remark.Contains(param.InvoiceBackgroundRemark)) || 
                        (refund.Id == null && i.Id != null && i.Remark.Contains(param.InvoiceBackgroundRemark))
                    );
                }
                if(!string.IsNullOrEmpty(param.AuthorityRemark))
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => authority.Id != null && authority.AuthorityRemark.Contains(param.AuthorityRemark));
                }
                if (!string.IsNullOrEmpty(param.PaymentCompany))
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => ci.PaymentCompanyName.Contains(param.PaymentCompany));
                }
                if (!string.IsNullOrEmpty(param.CustomerNum))
                {   
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => c.Id != null && c.ContractNum == param.CustomerNum);
                }
                if (param.IsDomesticRMB.HasValue)
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => authority.Id != null && authority.IsDomesticRmb == (int)param.IsDomesticRMB.Value);
                }
                if (param.CanInvoice.HasValue)
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => authority.Id != null && authority.CanInvoice == (int)param.CanInvoice.Value);
                }



                
                // // 排序
                // if (!string.IsNullOrEmpty(param.SortField))
                // {
                //     query = query.OrderByPropertyName(param.SortField, param.IsDESC ? OrderByType.Desc : OrderByType.Asc);
                // }
                // else
                // {
                //     // 默认按到账日期倒序
                //     query = query.OrderBy((r, ci, rs, m, i) => ci.ArrivalDate, OrderByType.Desc);
                // }

                // 获取统计结果
                // 1. 获取到账总金额和数量
                var receiptStatQuery = query.Clone();
                var totalReceiptAmount = receiptStatQuery.Sum((r, ci, rs, m, i, c, refund,authority ) => ci.ArrivalAmount);
                var totalReceiptCount = receiptStatQuery.Count();
                
                // 2. 获取已开票总金额和数量
                var invoicedStatQuery = query.Clone().Where((r, ci, rs, m, i, c, refund,authority ) => m.Id!=null && m.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess);
                var totalInvoicedAmount = invoicedStatQuery.Sum((r, ci, rs, m, i, c, refund,authority ) => i.InvoicedAmount);
                var totalInvoicedCount = invoicedStatQuery.Count();
                
                // 3. 获取未开票总金额和数量
                var totalUninvoicedAmount = totalReceiptAmount - totalInvoicedAmount;
                var totalUninvoicedCount = totalReceiptCount - totalInvoicedCount;

                // 处理可能为null的值
                decimal receiptAmount = totalReceiptAmount ?? 0M;
                decimal invoicedAmount = totalInvoicedAmount;
                decimal uninvoicedAmount = totalUninvoicedAmount ?? 0M;

                // 使用SqlSugar的分组查询
                result = query
                .OrderBy((r, ci, rs, m, i, c, refund,authority ) => ci.ArrivalDate, OrderByType.Desc)
                .OrderBy((r, ci, rs, m, i, c, refund,authority ) => i.Id, OrderByType.Desc)
                    .Select((r, ci, rs, m, i, c, refund,authority ) => new ReceiptInvoiceQueryResult_Out
                    {
                        ReceiptId = r.Id,
                        ReceiptDate = ci.ArrivalDate != null ? ci.ArrivalDate.Value.ToString("yyyy-MM-dd") : "",
                        PaymentMethod = ci.PaymentMethod,
                        ReceiptAmount = ci.ArrivalAmount,
                        InvoicedAmount = i.InvoicedAmount,
                        InvoicingDate = i.InvoicingDate != null ? i.InvoicingDate.Value.ToString("yyyy-MM-dd") : "",
                        InvoiceStatus = i.RefundStatus,
                        InvoiceType = i.InvoiceType,
                        InvoiceNumber = i.InvoiceNumber,
                        ReceiptStatus = r.IsReceipt,
                        IsInvoiced = m.Id != null && m.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess ? true : false,
                        ReceivingCompany = ci.CollectingCompany,
                        InvoiceCompany = i.BillingCompany,
                        PaymentCompanyName = ci.PaymentCompanyName,
                        InvoiceTitle = i.BillingHeader,
                        ContractNum = c.ContractNum,
                        IsPersonalInvoice = SqlFunc.IsNullOrEmpty(i.Id) ? false : i.IsPersonalInvoice,
                        BillingType = i.BillingType,
                        IsDomesticRMB = authority.IsDomesticRmb,
                        CanInvoice = authority.CanInvoice,
                        AuthorityRemark = authority.AuthorityRemark,
                        // 如果退票记录存在，则使用退票记录的备注  
                        InvoiceBackgroundRemark = refund.Id != null ? refund.Remark : i.Remark,
                        // 如果退票记录存在，则使用退票记录的ID，否则使用发票记录的ID
                        InvoiceId = refund.Id != null ? refund.Id : i.Id,
                        InvoiceApplicationId = i.InvoiceApplicationId
                    })
                    .Mapper(item=>{
                        item.InvoiceCompanyName = item.InvoiceCompany != null ? RedisCache.CollectingCompany.GetCollectingCompanyById(item.InvoiceCompany)?.CollectingCompanyName ?? "" : "";
                        item.ReceivingCompanyName = item.ReceivingCompany != null ? RedisCache.CollectingCompany.GetCollectingCompanyById(item.ReceivingCompany)?.CollectingCompanyName ?? "" : "";
                    })
                    .ToPageList(param.PageNumber, param.PageSize, ref total);

                // 转换为输出对象
                int index = 0;
                foreach (var item in result)
                {
                    // 计算未开票金额
                    item.UninvoicedAmount = item.ReceiptAmount - (item.InvoicedAmount ?? 0);

                    // 设置行号 - 修正计算方式
                    item.RowNum = index + 1 + (param.PageNumber - 1) * param.PageSize;
                    index++;
                }

                // 创建统计结果字典
                var statisticsResult = new Dictionary<string, string>
                {
                    { "到账总金额", receiptAmount.ToString("F2") },
                    { "到账数量", totalReceiptCount.ToString() },
                    { "已开票金额", invoicedAmount.ToString("F2") },
                    { "已开票数量", totalInvoicedCount.ToString() },
                    { "未开票金额", uninvoicedAmount.ToString("F2") },
                    { "未开票数量", totalUninvoicedCount.ToString() }
                };

                return new ApiTableOut<ReceiptInvoiceQueryResult_Out> 
                { 
                    Data = result, 
                    Total = total,
                    StatisticsResult = statisticsResult
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"从到账角度查询发票记录异常: {ex.Message}");
                throw new ApiException("查询到账发票记录失败");
            }
        }
        
        /// <summary>
        /// 到账发票统计
        /// </summary>
        /// <param name="param">统计查询条件</param>
        /// <returns>到账发票统计结果</returns>
        public ReceiptInvoiceStatistics_Out GetReceiptInvoiceDetailStatistics(ReceiptInvoiceStatisticsQuery_In param)
        {
            try
            {
                var db = DbContext.Crm2Db;
                var quantityStats = new List<ReceiptInvoiceQuantityStatistics>();
                var amountStats = new List<ReceiptInvoiceAmountStatistics>();

                var query = db.Queryable<Db_crm_contract_receiptregister>()
                    .LeftJoin<Db_v_contractreceiptregistercollectioninfo>((r, ci) => r.Id == ci.ContractReceiptRegisterId)
                    .LeftJoin<Db_v_contract_receiptregister_state>((r, ci,rs) => r.Id == rs.id )
                    .LeftJoin<Db_crm_invoice_receipt_matching>((r, ci, rs, m) => r.Id == m.ReceiptId && m.Deleted == false)
                    .LeftJoin<Db_crm_invoice>((r, ci, rs, m, i) => m.InvoiceId == i.Id && i.Deleted == false)
                    .LeftJoin<Db_crm_contract>((r, ci, rs, m, i, c) => r.ContractId == c.Id && c.Deleted == false)
                    .LeftJoin<Db_crm_invoice>((r, ci, rs, m, i,c,refund ) => i.Id == refund.RelatedInvoiceId && i.Deleted == false)
                    .LeftJoin<Db_crm_invoice_receipt_authority>((r, ci, rs, m, i,c,refund,authority ) => r.Id == authority.ReceiptId && authority.Deleted == false)
                    .Where((r, ci, rs, m, i,c, refund,authority ) => rs.State == (int)EnumRegisterState.Confirmed || rs.State == (int)EnumRegisterState.Locked)
                    .Where((r, ci, rs, m, i,c, refund,authority )  => ci.Currency == (int)EnumCurrency.CNY)
                    .Where((r, ci, rs, m, i,c, refund,authority ) => r.Deleted == null || r.Deleted == false)
                    .Where((r, ci, rs, m, i,c, refund,authority ) => refund.Id == null || refund.TransactionType == 2)
                    .Where((r, ci, rs, m, i,c, refund,authority ) => ci.PaymentMethod == (int)EnumPaymentMethod.Bank||ci.PaymentMethod == (int)EnumPaymentMethod.Cash)
                    // 只查询允许开票的到账记录
                    .Where((r, ci, rs, m, i,c, refund,authority ) => r.IsAllowInvoicing == 1);
                // 应用过滤条件
                if (param.ReceiptMonthStart.HasValue)
                {
                    var startDate = param.ReceiptMonthStart.Value.GetMonthStart();
                    query = query.Where((r, ci, rs, m, i,c, refund,authority ) => ci.ArrivalDate!=null && ci.ArrivalDate >= startDate);
                }

                if (param.ReceiptMonthEnd.HasValue)
                {
                    var endDate = param.ReceiptMonthEnd.Value.GetMonthEnd().GetDaysEnd();
                    query = query.Where((r, ci, rs, m, i,c, refund,authority ) => ci.ArrivalDate!=null && ci.ArrivalDate <= endDate);
                }

                if (!string.IsNullOrEmpty(param.ReceivingCompany))
                {
                    query = query.Where((r, ci, rs, m, i,c, refund,authority ) => ci.CollectingCompany.Contains(param.ReceivingCompany));
                }

                if (param.PaymentMethod.HasValue)
                {
                    query = query.Where((r, ci, rs, m, i,c, refund,authority ) => ci.PaymentMethod == param.PaymentMethod.Value);
                }
                if (param.IsDomesticRMB.HasValue)
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => authority.Id != null && authority.IsDomesticRmb == (int)param.IsDomesticRMB.Value);
                }
                if (param.CanInvoice.HasValue)
                {
                    query = query.Where((r, ci, rs, m, i, c, refund,authority ) => authority.Id != null && authority.CanInvoice == (int)param.CanInvoice.Value);
                }

                // 按支付方式统计
                if (param.StatisticsDimension == 1)
                {
                    // 执行分组查询
                    var statsResult = query
                            .GroupBy((r, ci, rs, m, i,c, refund,authority ) => new { ci.PaymentMethod })
                        .Select((r, ci, rs, m, i,c, refund,authority ) => new
                        {
                            GroupValue = ci.PaymentMethod,
                            
                            // 统计结果 - 使用AggregateCount替代CountDistinct
                            ReceiptCount = SqlFunc.AggregateCount(r.Id),
                            InvoicedCount = SqlFunc.AggregateSum( SqlFunc.IIF<int>(m.Id!=null&m.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess , 1, 0)),
                            RefundCount = SqlFunc.AggregateSum(
                                SqlFunc.IIF<int>(
                                    refund.Id != null,
                                    1, 
                                    0
                                )
                            ),
                            
                            ReceiptTotal = SqlFunc.AggregateSum(ci.ArrivalAmount),
                            InvoicedTotal = SqlFunc.AggregateSum(SqlFunc.IIF<decimal>(m.Id!=null &m.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess, i.InvoicedAmount, 0)),
                            RefundTotal = SqlFunc.AggregateSum(SqlFunc.IIF<decimal>(refund.Id != null, refund.InvoicedAmount, 0))
                        })
                        .ToList();

                    // 处理统计结果
                    int rowNum = 1;
                    foreach (var stat in statsResult)
                    {
                        if (stat.GroupValue == null) continue;
                        
                        string itemName = GetStatisticsItemName(2, stat.GroupValue.ToString());
                        int uninvoicedCount = stat.ReceiptCount - stat.InvoicedCount;
                        
                        // 添加数量统计
                        quantityStats.Add(new ReceiptInvoiceQuantityStatistics
                        {
                            RowNum = rowNum,
                            StatisticsItem = itemName,
                            ReceiptCount = stat.ReceiptCount,
                            InvoicedCount = stat.InvoicedCount,
                            UninvoicedCount = uninvoicedCount,
                            RefundCount = stat.RefundCount
                        });

                        // 添加金额统计
                        decimal receiptTotal = 0M;
                        if (stat.ReceiptTotal != null)
                        {
                            receiptTotal = Convert.ToDecimal(stat.ReceiptTotal);
                        }
                        
                        decimal invoicedTotal = 0M;
                        if (stat.InvoicedTotal != null)
                        {
                            invoicedTotal = Convert.ToDecimal(stat.InvoicedTotal);
                        }
                        
                        decimal uninvoicedTotal = receiptTotal - invoicedTotal;
                        
                        decimal refundTotal = 0M;
                        if (stat.RefundTotal != null)
                        {
                            refundTotal = Math.Abs(Convert.ToDecimal(stat.RefundTotal));
                        }

                        amountStats.Add(new ReceiptInvoiceAmountStatistics
                        {
                            RowNum = rowNum++,
                            StatisticsItem = itemName,
                            ReceiptTotalAmount = receiptTotal,
                            InvoicedTotalAmount = invoicedTotal,
                            UninvoicedTotalAmount = uninvoicedTotal,
                            RefundTotalAmount = refundTotal
                        });
                    }
                }
                else // 按收款单位统计
                {
                    // 执行分组查询
                    var statsResult = query
                            .GroupBy((r, ci, rs, m, i,c, refund,authority ) => new { ci.CollectingCompany })
                        .Select((r, ci, rs, m, i,c, refund,authority    ) => new
                        {
                            GroupValue = ci.CollectingCompany,
                            
                            // 统计结果 - 使用AggregateCount替代CountDistinct
                            ReceiptCount = SqlFunc.AggregateCount(r.Id),
                            InvoicedCount = SqlFunc.AggregateSum(SqlFunc.IIF<int>(m.Id != null&m.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess, 1, 0)),
                            RefundCount = SqlFunc.AggregateSum(
                                SqlFunc.IIF<int>(
                                    refund.Id != null,
                                    1, 
                                    0
                                )
                            ),
                            
                            ReceiptTotal = SqlFunc.AggregateSum(ci.ArrivalAmount),
                            InvoicedTotal = SqlFunc.AggregateSum(SqlFunc.IIF<decimal>(m.Id != null&m.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess, i.InvoicedAmount, 0)),
                            RefundTotal = SqlFunc.AggregateSum(SqlFunc.IIF<decimal>(refund.Id != null, refund.InvoicedAmount, 0))
                        })
                        .ToList();

                    // 处理统计结果
                    int rowNum = 1;
                    foreach (var stat in statsResult)
                    {
                        if (string.IsNullOrEmpty(stat.GroupValue)) continue;
                        
                        int uninvoicedCount = stat.ReceiptCount - stat.InvoicedCount;

                        string itemName =  RedisCache.CollectingCompany.GetCollectingCompanyById(stat.GroupValue)?.CollectingCompanyName ?? "";

                        // 添加数量统计
                        quantityStats.Add(new ReceiptInvoiceQuantityStatistics
                        {
                            RowNum = rowNum,
                            StatisticsItem = itemName,
                            ReceiptCount = stat.ReceiptCount,
                            InvoicedCount = stat.InvoicedCount,
                            UninvoicedCount = uninvoicedCount,
                            RefundCount = stat.RefundCount
                        });

                        // 添加金额统计
                        decimal receiptTotal = 0M;
                        if (stat.ReceiptTotal != null)
                        {
                            receiptTotal = Convert.ToDecimal(stat.ReceiptTotal);
                        }
                        
                        decimal invoicedTotal = 0M;
                        if (stat.InvoicedTotal != null)
                        {
                            invoicedTotal = Convert.ToDecimal(stat.InvoicedTotal);
                        }
                        
                        decimal uninvoicedTotal = receiptTotal - invoicedTotal;
                        
                        decimal refundTotal = 0M;
                        if (stat.RefundTotal != null)
                        {
                            refundTotal = Math.Abs(Convert.ToDecimal(stat.RefundTotal));
                        }

                        amountStats.Add(new ReceiptInvoiceAmountStatistics
                        {
                            RowNum = rowNum++,
                            StatisticsItem = itemName,
                            ReceiptTotalAmount = receiptTotal,
                            InvoicedTotalAmount = invoicedTotal,
                            UninvoicedTotalAmount = uninvoicedTotal,
                            RefundTotalAmount = refundTotal
                        });
                    }
                }

                return new ReceiptInvoiceStatistics_Out
                {
                    QuantityStatistics = quantityStats,
                    AmountStatistics = amountStats
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"到账发票统计异常: {ex.Message}");
                throw new ApiException("获取到账发票统计信息失败");
            }
        }
        
        /// <summary>
        /// 获取统计项显示名称
        /// </summary>
        /// <param name="dimensionType">维度类型</param>
        /// <param name="value">值</param>
        /// <returns>显示名称</returns>
        private string GetStatisticsItemName(int dimensionType, string value)
        {
            // 根据维度类型解析统计项目名称
            switch (dimensionType)
            {
                case 1: // 发票类型
                    if (int.TryParse(value, out int invoiceType))
                    {
                        var enumValue = (EnumInvoiceType)invoiceType;
                        return enumValue.GetEnumDescription();
                    }
                    return value;
                case 2: // 支付方式
                    if (int.TryParse(value, out int paymentMethod))
                    {
                        var enumValue = (EnumPaymentMethod)paymentMethod;
                        return enumValue.GetEnumDescription();
                    }
                    return value;
                case 3: // 收款单位
                    return value;
                default:
                    return value;
            }
        }
    }
} 