﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.TrackingRecord;
using CRM2_API.Model.System;
using JiebaNet.Segmenter.Common;
using LgyUtil;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Coupon;

namespace CRM2_API.BLL
{
    public class BLL_Coupon : BaseBLL<BLL_Coupon>
    {
        /// <summary>
        /// 依据到账传入内容发放优惠券
        /// </summary>
        /// <returns></returns>
        public void AddCouponByContractReceipt(AddCouponByContractReceipt_In addCouponByContractReceipt_In)
        {
            if (addCouponByContractReceipt_In.ContractReceiptRegisterId.IsNullOrEmpty() || addCouponByContractReceipt_In.ContractReceiptRegisterId == Guid.Empty.ToString())
            {
                throw new ApiException("到账不存在,请核实");
            }
            if (addCouponByContractReceipt_In.CompanyId.IsNullOrEmpty() && addCouponByContractReceipt_In.ContractId.IsNotNullOrEmpty())
            {
                var contract = DbOpe_crm_contract.Instance.GetDataById(addCouponByContractReceipt_In.ContractId);
                addCouponByContractReceipt_In.CompanyId = contract.FirstParty;
            }
            if (addCouponByContractReceipt_In.CompanyId.IsNullOrEmpty() || addCouponByContractReceipt_In.CompanyId == Guid.Empty.ToString())
            {
                throw new ApiException("对应客户不存在，请核实");
            }

            Db_crm_customer_coupon mainCoupon = new Db_crm_customer_coupon();
            mainCoupon = addCouponByContractReceipt_In.MappingTo<Db_crm_customer_coupon>();

            bool IsInEvent = DbOpe_crm_event.Instance.CheckIsInCouponEvent(DateTime.Now);

            if (!IsInEvent)
            {
                if (mainCoupon.YearNum == 0 && mainCoupon.CouponCount == 0)
                {
                    return;
                }
            }

            In3MonthCollectionDoSomething in3 = DbOpe_crm_customer_coupon.Instance.In3MonthCollectionDoSomething(addCouponByContractReceipt_In.CollectioninfoId, addCouponByContractReceipt_In.ContractId);
            in3.RegIds.ForEach(regId =>
            {
                Db_crm_customer_coupon item = new Db_crm_customer_coupon();
                item.ContractReceiptRegisterId = regId;
                item.CompanyId = in3.CompanyID;
                item.YearNum = 0;
                item.CouponCount = 0;
                item.State = 2;
                DbOpe_crm_customer_coupon.Instance.InsertDataQueue(item);
            });
            //mainCoupon.Id = Guid.NewGuid().ToString();
            //mainCoupon.CreateUser = UserId;
            //mainCoupon.CreateDate = DateTime.Now;


            string CouponId = DbOpe_crm_customer_coupon.Instance.InsertDataQueueReturnId(mainCoupon);
            for (int i = 0; i < addCouponByContractReceipt_In.CouponCount; i++)
            {
                Db_crm_customer_coupon_detail detail = new Db_crm_customer_coupon_detail();
                detail.CouponId = CouponId;
                detail.CouponType = addCouponByContractReceipt_In.CouponTypeIn.IsNull() ? (int)EnumCouponType.Granting : (int)addCouponByContractReceipt_In.CouponTypeIn;
                detail.CouponType = addCouponByContractReceipt_In.CouponTypeIn.IsNull() ? (int)EnumCouponType.Granting : (int)addCouponByContractReceipt_In.CouponTypeIn;
                detail.Deadline = addCouponByContractReceipt_In.Deadline;
                DbOpe_crm_customer_coupon_detail.Instance.InsertDataQueueReturnId(detail);
            }
            DbOpe_crm_customer_coupon.Instance.SaveQueues();
        }

        public void UpdateCouponByContractReceipt(UpdateCouponByContractReceipt_In updateCouponByContractReceipt_In)
        {
            DbOpe_crm_customer_coupon.Instance.RemoveUnvaildData(updateCouponByContractReceipt_In.Id);
            AddCouponByContractReceipt_In ins = updateCouponByContractReceipt_In.MappingTo<AddCouponByContractReceipt_In>();
            AddCouponByContractReceipt(ins);
        }

        /// <summary>
        /// 根据合同ID获取可用优惠券
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public List<CounponDetail> GetCanUseCouponByContractId(string ContractId)
        {
            return DbOpe_crm_customer_coupon.Instance.GetCouponDetailByContractId(ContractId);
        }

        /// <summary>
        /// 核销单个优惠券，使用中/已使用
        /// 服务申请时修改为使用中，复核确认修改为已使用
        /// </summary>
        /// <param name="couponDetailId"></param>
        /// <param name="changeType"></param>
        public CouponResult ChangeCouponType(string couponDetailId, EnumCouponType changeType)
        {
            return DbOpe_crm_customer_coupon.Instance.ChangeCouponType(couponDetailId, changeType);
        }

        /// <summary>
        /// 核销多张优惠券，使用中/已使用
        /// 服务申请时修改为使用中，复核确认修改为已使用
        /// </summary>
        /// <param name="couponDetailIdList"></param>
        /// <param name="changeType"></param>
        public CouponResult ChangeCouponType(List<string> couponDetailIdList, EnumCouponType changeType)
        {
            return DbOpe_crm_customer_coupon.Instance.ChangeCouponType(couponDetailIdList, changeType);
        }
        /// <summary>
        /// 确认到账更新发送中优惠卷为未使用
        /// </summary>
        /// <param name="regId"></param>
        /// <param name="CollectionId"></param>
        /// <param name="ContractId"></param>
        public void AuditRegisterUpdateCouponStates(string regId, string CollectionId, string ContractId)
        {
            DbOpe_crm_customer_coupon.Instance.ChangeCouponTypeByRegId(regId, CollectionId, ContractId);
        }
        /// <summary>
        /// 批量确认到账更新发送中优惠卷为未使用
        /// </summary>
        /// <param name="regIds"></param>
        public void BatchAuditRegisterUpdateCouponStates(List<string> regIds)
        {
            DbOpe_crm_customer_coupon.Instance.ChangeCouponTypeByRegId(regIds);
        }

        public void RefreshCoupanDeadline()
        {
            DbOpe_crm_customer_coupon.Instance.RefreshCoupanDeadline();
        }

        public void UpdateCouponCompany(string firstParty, string renewalContractNum, string receiptRegisterId)
        {
            DbOpe_crm_customer_coupon.Instance.UpdateCouponCompany(firstParty, renewalContractNum, receiptRegisterId);
        }
    }
}
