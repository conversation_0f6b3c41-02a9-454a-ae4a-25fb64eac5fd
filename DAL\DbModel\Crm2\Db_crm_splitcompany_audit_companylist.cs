﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_splitcompany_audit_companylist")]
    public partial class Db_crm_splitcompany_audit_companylist
    {
           public Db_crm_splitcompany_audit_companylist(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:拆分公司审核表id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string SplitCompanyAuditId {get;set;}

        /// <summary>
        /// Desc:原客户表id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string OldCustomerId { get; set; }
        /// <summary>
        /// Desc:新客户表id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string NewCustomerId { get; set; }

        /// <summary>
        /// Desc:子公司表id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string CustomerSubCompanyId {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int Deleted {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
