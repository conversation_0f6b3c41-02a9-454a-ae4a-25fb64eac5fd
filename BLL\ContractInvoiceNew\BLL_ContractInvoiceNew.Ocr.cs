using CRM2_API.BLL.Common;
using CRM2_API.Common.Utils;
using CRM2_API.Model.ControllersViewModel.Common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Linq;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using SqlSugar;
using CRM2_API.DAL.DbCommon;
using CRM2_API.Model.BusinessModel;
using Microsoft.AspNetCore.Http;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using CRM2_API.Common.JWT;
using Newtonsoft.Json;
using CRM2_API.Common.AppSetting;
using QCloud;
using CRM2_API.Common.Cache;
using CRM2_API.Common;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - OCR图像识别相关
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        #region OCR发票识别
        /// <summary>
        /// 识别发票并与申请单信息进行比对
        /// </summary>
        /// <param name="fileBytes">文件字节数组</param>
        /// <param name="applicationId">申请单ID</param>
        /// <returns>比对结果</returns>
        public async Task<InvoiceOcrCompareResult> RecognizeAndCompareInvoiceOcrAsync(byte[] fileBytes, string applicationId)
        {
            try
            {
                // 验证申请ID并获取申请信息
                var application = DbOpe_crm_invoice_application.Instance.GetData(a => a.Id == applicationId && a.Deleted != true);
                if (application == null)
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "未找到发票申请信息"
                    };
                }

                // 获取应用信息转换为视图模型
                var applicationInfo = new InvoiceApplicationInfo
                {
                    Id = application.Id,
                    ContractId = application.ContractId,
                    AppliedAmount = application.AppliedAmount,
                    BillingType = (EnumBillingType)(application.BillingType > 0 ? application.BillingType : 0),
                    InvoiceType = (EnumInvoiceType)(application.InvoiceType > 0 ? application.InvoiceType : 0),
                    BillingCompany = application.BillingCompany,
                    BillingCompanyName = DbOpe_crm_collectingcompany.Instance.GetData(c => 
                        c.Id == application.BillingCompany && c.Deleted == false)?.SellerName ?? string.Empty,
                    ApplicantId = application.ApplicantId,
                    ApplicantName = RedisCache.UserWithOrg.GetUserById(application.ApplicantId)?.UserWithOrgFullName ?? string.Empty,
                    ApplyTime = application.ApplyTime,
                    AuditStatus = (EnumInvoiceApplicationStatus)application.AuditStatus,
                    DisplayStatus = (EnumInvoiceDisplayStatus)(application.DisplayStatus ?? 0),
                    BillingHeader = application.BillingHeader,
                    BillingHeaderName = application.BillingHeader,
                    CreditCode = application.CreditCode,
                    Recipient = application.Recipient,
                    Email = application.Email,
                    Remark = application.Remark,
                    ExpectedInvoicingDate = application.ExpectedInvoicingDate,
                    InvoiceDetails = application.InvoicingDetails
                };

                // 根据文件类型调用不同的OCR识别方法
                UmiOcrService.OcrInvoiceInfo invoiceInfo = null;
                //这里换一种实现方式，用pdf读取二维码信息
                var analyzePdfQRCodeResult = await InvoiceReadUtil.AnalyzePdfQRCode(fileBytes);

                //尝试从pdf中直接读取其他信息
                invoiceInfo = InvoiceReadUtil.ExtractInvoiceInfoByFixedCoordinates(fileBytes);
                if (invoiceInfo == null)
                {
                    return new InvoiceOcrCompareResult
                    {
                        Remark = "OCR识别失败"
                    };
                }

                // 转换为OcrInvoiceResult
                var ocrResult = new OcrInvoiceResult
                {
                    InvoiceType = invoiceInfo.InvoiceType,
                    InvoiceCode = invoiceInfo.InvoiceCode,
                    InvoiceNumber = analyzePdfQRCodeResult.InvoiceNo == "?" ? invoiceInfo.InvoiceNumber : analyzePdfQRCodeResult.InvoiceNo,
                    InvoiceDate = analyzePdfQRCodeResult.InvoiceDate == "?" ? (invoiceInfo.InvoiceDate == null ? "" : invoiceInfo.InvoiceDate.Value.ToString("yyyy-MM-dd")) : analyzePdfQRCodeResult.InvoiceDate,
                    BuyerName = invoiceInfo.BuyerName,
                    BuyerTaxNumber = invoiceInfo.BuyerTaxNumber,
                    SellerName = invoiceInfo.SellerName,
                    SellerTaxNumber = invoiceInfo.SellerTaxNumber,
                    TotalAmount = invoiceInfo.TotalAmount,
                    TotalTax = invoiceInfo.TotalTax,
                    TotalAmountWithTax = analyzePdfQRCodeResult.InvoiceAmount == "?" ? invoiceInfo.TotalAmountWithTax.ToString("F2") : analyzePdfQRCodeResult.InvoiceAmount,
                    OriginalText = invoiceInfo.OriginalText,
                    Remark = invoiceInfo.Remark,
                    InvoiceDetails = analyzePdfQRCodeResult.InvoiceDetails == "?" ? "" : analyzePdfQRCodeResult.InvoiceDetails
                };
                ocrResult.CollectingCompanyId = RedisCache.CollectingCompany.GetCollectingCompanyBySellerName(ocrResult.SellerName)?.Id ?? string.Empty;
                // 保存OCR识别结果
                var ocrId = await SaveOcrResultAsync(applicationId, ocrResult);
                
                // 保存发票附件到crm_contract_invoice_attachment表
                await SaveInvoiceAttachmentAsync(fileBytes, applicationId, ocrResult.InvoiceNumber);

                // 创建比对结果对象
                var compareResult = new InvoiceOcrCompareResult
                {
                    OcrInfo = ocrResult,
                    ApplicationInfo = applicationInfo,
                    ComparisonResults = new List<FieldComparisonResult>(),
                    IsMatch = false // 默认为false，后面根据比对结果设置
                };

                // 进行字段比对
                CompareFields(compareResult);

                // 计算是否匹配
                bool isMatch = true;
                foreach (var fieldResult in compareResult.ComparisonResults)
                {
                    // 如果任何一个必要字段不匹配，则整体不匹配
                    if (fieldResult.IsRequired && !fieldResult.IsMatch)
                    {
                        isMatch = false;
                        break;
                    }
                }

                compareResult.IsMatch = isMatch;
                
                // 保存比对结果
                SaveComparisonResult(ocrId, compareResult);

                return compareResult;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理发票OCR识别比对时发生错误: {ex.Message}");
                
                return new InvoiceOcrCompareResult
                {
                    Remark = $"处理失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 进行字段比对
        /// </summary>
        /// <param name="compareResult">比对结果对象</param>
        private void CompareFields(InvoiceOcrCompareResult compareResult)
        {
            var ocrInfo = compareResult.OcrInfo;
            var appInfo = compareResult.ApplicationInfo;
            var comparisonResults = compareResult.ComparisonResults;

            // 发票代码比对 - 由于InvoiceApplicationInfo类中不存在这些属性，需要修改比对逻辑
            // 可以使用空字符串进行比对，或者修改为只对存在的属性进行比对

            // 获取系统中的发票类型描述
            string expectedInvoiceTypeDesc = EnumHelper.GetEnumDescription<EnumInvoiceType>(appInfo.InvoiceType);
            
            // 将OCR识别的发票类型转换为系统类型描述
            string actualInvoiceTypeDesc = ConvertOcrInvoiceTypeToSystemType(ocrInfo.InvoiceType);
            
            // 发票类型比对
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "InvoiceType",
                FieldDescription = "发票类型",
                ExpectedValue = expectedInvoiceTypeDesc,
                ActualValue = actualInvoiceTypeDesc,
                IsMatch = string.Equals(expectedInvoiceTypeDesc, actualInvoiceTypeDesc, StringComparison.OrdinalIgnoreCase),
                IsRequired = true
            });

            // 开票日期比对 - 只比对日期部分
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "InvoiceDate",
                FieldDescription = "开票日期",
                ExpectedValue = appInfo.ExpectedInvoicingDate.Value.ToString("yyyy-MM-dd")??"",
                ActualValue = ocrInfo.InvoiceDate,
                IsMatch = string.Equals(appInfo.ExpectedInvoicingDate.Value.ToString("yyyy-MM-dd"), ocrInfo.InvoiceDate, StringComparison.OrdinalIgnoreCase),
                IsRequired = true
            });

            // 金额比对
            var expectedAmount = appInfo.AppliedAmount?.ToString("F2") ?? "0.00";
            var actualAmount = ocrInfo.TotalAmountWithTax;
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "TotalAmount",
                FieldDescription = "发票金额",
                ExpectedValue = expectedAmount,
                ActualValue = actualAmount,
                IsMatch = decimal.Parse(expectedAmount) == decimal.Parse(actualAmount),
                IsRequired = true
            });

            // 购买方名称比对
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "BuyerName",
                FieldDescription = "购买方名称",
                ExpectedValue = appInfo.BillingHeader,
                ActualValue = ocrInfo.BuyerName,
                IsMatch = string.Equals(appInfo.BillingHeader, ocrInfo.BuyerName, StringComparison.OrdinalIgnoreCase),
                IsRequired = true
            });

            // 销售方名称比对
            comparisonResults.Add(new FieldComparisonResult
            {
                FieldName = "SellerName",
                FieldDescription = "销售方名称",
                ExpectedValue = appInfo.BillingCompanyName, // 直接使用BillingCompanyName
                ActualValue = ocrInfo.SellerName,
                IsMatch = string.Equals(appInfo.BillingCompanyName, ocrInfo.SellerName, StringComparison.OrdinalIgnoreCase),
                IsRequired = true
            });
        }

        /// <summary>
        /// 将OCR识别的发票类型字符串转换为系统中的发票类型描述
        /// </summary>
        /// <param name="ocrInvoiceTypeStr">OCR识别出的发票类型字符串</param>
        /// <returns>系统中的发票类型描述</returns>
        private string ConvertOcrInvoiceTypeToSystemType(string ocrInvoiceTypeStr)
        {
            if (string.IsNullOrWhiteSpace(ocrInvoiceTypeStr))
            {
                return "未识别";
            }

            // 专票匹配模式
            if (ocrInvoiceTypeStr.Contains("专用发票") || 
                ocrInvoiceTypeStr.Contains("增值税专用") ||
                ocrInvoiceTypeStr.Contains("专票"))
            {
                return EnumHelper.GetEnumDescription<EnumInvoiceType>(EnumInvoiceType.SpecialTicket);
            }
            // 普票匹配模式
            else if ((ocrInvoiceTypeStr.Contains("普通发票") || 
                     ocrInvoiceTypeStr.Contains("增值税普通") ||
                     ocrInvoiceTypeStr.Contains("普票")) &&
                     !ocrInvoiceTypeStr.Contains("专用"))
            {
                return EnumHelper.GetEnumDescription<EnumInvoiceType>(EnumInvoiceType.UniversalTicket);
            }
            // 形式发票匹配模式
            else if (ocrInvoiceTypeStr.Contains("形式发票"))
            {
                return EnumHelper.GetEnumDescription<EnumInvoiceType>(EnumInvoiceType.ProformaTicket);
            }
            
            // 如果无法匹配，则返回原始字符串
            return ocrInvoiceTypeStr;
        }
        /// <summary>
        /// 保存OCR识别结果到数据库
        /// </summary>
        private async Task<string> SaveOcrResultAsync(string applicationId, OcrInvoiceResult ocrResult,int recognitionType = 1)
        {
            var entity = new Db_crm_invoice_recognition
            {
                Id = Guid.NewGuid().ToString("N"),
                InvoiceApplicationId = applicationId,
                InvoiceCode = ocrResult.InvoiceCode,
                InvoiceNumber = ocrResult.InvoiceNumber,
                InvoiceDate = DateTime.Parse(ocrResult.InvoiceDate),
                InvoiceAmount = ocrResult.TotalAmount,
                TaxAmount = ocrResult.TotalTax,
                TotalAmount = decimal.Parse(ocrResult.TotalAmountWithTax),
                BuyerName = ocrResult.BuyerName,
                BuyerTaxCode = ocrResult.BuyerTaxNumber,
                SellerName = ocrResult.SellerName,
                SellerTaxCode = ocrResult.SellerTaxNumber,
                InvoiceDetails = ocrResult.InvoiceDetails,
                RecognitionStatus = 1, // 成功
                RecognitionType = recognitionType,
                RecognitionResult = ocrResult.OriginalText,
                InvoiceTypeString = ocrResult.InvoiceType,
                CreateDate = DateTime.Now,
                Deleted = false
            };
            
            // 使用数据访问层保存
            bool success = DbOpe_crm_invoice_recognition.Instance.Insert(entity);
            if (!success)
            {
                throw new Exception("保存OCR识别结果失败");
            }
            return entity.Id;
        }

        /// <summary>
        /// 保存比对结果到数据库
        /// </summary>
        /// <param name="ocrId">OCR记录ID</param>
        /// <param name="compareResult">比对结果</param>
        private void SaveComparisonResult(string ocrId, InvoiceOcrCompareResult compareResult)
        {
            try
            {
                var entity = new Db_crm_invoice_comparison
                {
                    Id = Guid.NewGuid().ToString("N"),
                    RecognitionId = ocrId,
                    InvoiceApplicationId = compareResult.ApplicationInfo.Id,
                    ComparisonDetails = JsonConvert.SerializeObject(compareResult.ComparisonResults),
                    ComparisonResult = compareResult.IsMatch ? 1 : 3, // 1=完全匹配，3=不匹配
                    CreateDate = DateTime.Now,
                    Deleted = false
                };
                
                // 使用数据访问层保存
                bool success = DbOpe_crm_invoice_comparison.Instance.Insert(entity);
                if (!success)
                {
                    LogUtil.AddErrorLog("保存比对结果失败");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"保存比对结果失败: {ex.Message}");
            }
        }

        
        /// <summary>
        /// 保存发票文件到crm_contract_invoice_attachment表
        /// </summary>
        /// <param name="fileBytes">文件字节数组</param>
        /// <param name="applicationId">发票申请ID</param>
        /// <param name="invoiceNumber">发票号码，用于生成文件名</param>
        /// <returns>附件ID</returns>
        private async Task<string> SaveInvoiceAttachmentAsync(byte[] fileBytes, string applicationId, string invoiceNumber)
        {
            try
            {
                // 删除该申请ID关联的所有旧附件记录
                var oldAttachments = DbOpe_crm_contract_invoice_attachment.Instance.GetDataList(a => 
                    a.ContractInvoiceId == applicationId && a.Deleted == false).ToList();
                
                if (oldAttachments != null && oldAttachments.Count > 0)
                {
                    foreach (var oldAttachment in oldAttachments)
                    {
                        // 标记为已删除
                        oldAttachment.Deleted = true;
                        oldAttachment.UpdateUser = TokenModel.Instance.id;
                        oldAttachment.UpdateDate = DateTime.Now;
                        DbOpe_crm_contract_invoice_attachment.Instance.Update(oldAttachment);
                        
                        LogUtil.AddLog($"已删除旧发票附件，ID: {oldAttachment.Id}, 申请ID: {applicationId}");
                    }
                }

                string attachmentId = Guid.NewGuid().ToString();
                string fileExtension = "pdf"; // 假设文件是PDF格式，如果需要动态判断可以增加相应逻辑
                string fileName = $"发票-{invoiceNumber}.{fileExtension}";
                string fileHead = $"{AppSettings.DownLoadFilePath}/ContractInvoice/{DateTime.Now.ToString("yyyyMMdd")}";
                string fullFileName = $"{fileHead}/{attachmentId}.{fileExtension}";
                
                // 创建文件夹，保存文件
                string path = Path.GetDirectoryName(fullFileName);
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                
                // 如果使用云存储
                if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                {
                    using (MemoryStream stream = new MemoryStream(fileBytes))
                    {
                        QCloudOperator qCloud = new QCloudOperator();
                        qCloud.UploadStream(fullFileName, stream, "application/pdf");
                    }
                }
                else
                {
                    // 保存到本地文件系统
                    await File.WriteAllBytesAsync(fullFileName, fileBytes);
                }
                
                // 创建附件记录
                var attachment = new Db_crm_contract_invoice_attachment
                {
                    Id = attachmentId,
                    ContractInvoiceId = applicationId, // 使用申请ID作为合同发票ID
                    FileName = fileName,
                    FilePath = fullFileName,
                    FileExtension = fileExtension,
                    FileSize = fileBytes.Length,
                    Deleted = false,
                    CreateUser = TokenModel.Instance.id,
                    CreateDate = DateTime.Now
                };
                
                // 保存附件记录到数据库
                DbOpe_crm_contract_invoice_attachment.Instance.Insert(attachment);
                
                LogUtil.AddLog($"保存发票附件成功，ID: {attachmentId}, 申请ID: {applicationId}, 发票号: {invoiceNumber}");
                
                return attachmentId;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"保存发票附件失败: {ex.Message}");
                return string.Empty;
            }
        }
        
        #endregion
    }
} 