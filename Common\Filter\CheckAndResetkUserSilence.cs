﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;

namespace CRM2_API.Common.Filter
{
    public class CheckAndResetkUserSilence : IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            //判断被访问接口是否跳过登录token验证,跳过token验证的接口(如登录相关接口)不需要进行权限验证
            if (!context.Filters.Any(f => f is SkipAuthCheckAttribute) && !context.Filters.Any(f => f is SkipSilenceAttribute))
            {
                //从TokenModel中获取用户主键userId
                var userId = TokenModel.Instance.id;
                //从Header中读取当前使用环境是否为移动端
                var IsMobile = false;
                if (context.HttpContext.Request.Method.Equals("GET"))
                    IsMobile = context.HttpContext.Request.Query["IsMobile"].ToString().ToBool();
                else
                    IsMobile = context.HttpContext.Request.Headers["IsMobile"].ToString().ToBool();
                if (RedisCache.SilenceTime.CheckSilenceTime(userId, IsMobile))
                    throw new ApiException("由于长时间未操作，您的登录状态已注销", Enum_ReturnErrorCode.OverSilenceTime);
                else
                    //存储用户静默截止时间
                    RedisCache.SilenceTime.SetSilenceTime(userId, IsMobile);
            }
        }
    }

    /// <summary>
    /// 跳过权限验证特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class SkipSilenceAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {

        }
    }
}
