﻿using CRM2_API.BLL;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;

using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    [Description("登录控制器")]
    public class LoginController : MyControllerBase
    {

        public LoginController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {

        }
        /// <summary>
        /// 通过手机号码/邮箱地址和验证码进行登录
        /// </summary>
        /// <param name="loginByVerificationCodeIn"></param>
        /// <returns></returns>
        [Http<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
        public string LoginByVerificationCode(LoginByVerificationCode_In loginByVerificationCodeIn)
        {
            return BLL_Login.Instance.LoginByVerificationCode(loginByVerificationCodeIn, HttpContext);
        }
        /// <summary>
        /// 获取手机号/邮箱号验证码
        /// </summary>
        /// <param name="getVerifyCodeIn"></param>
        [HttpPost, SkipAuthCheck, UseDefaultRSAKey, SkipIPCheck]
        public void GetVerificationCode(GetVerificationCode_In getVerifyCodeIn)
        {
            BLL_Login.Instance.GetVerificationCode(getVerifyCodeIn);
        }
        /// <summary>
        /// 通过账号/手机号/Email和密码进行登录
        /// </summary>
        /// <param name="loginByPwdIn"></param>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck, UseDefaultRSAKey, PreLog, SkipIPCheck]
        public string LoginByPwd(LoginByPwd_In loginByPwdIn)
        {
            return BLL_Login.Instance.LoginByPwd(loginByPwdIn, HttpContext);
        }
        /// <summary>
        /// 通过微信扫码进行登录
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck, UseDefaultRSAKey, SkipIPCheck]
        public string LoginByWeChartCode(string code)
        {
            return BLL_Login.Instance.LoginByWeChartCode(code, HttpContext);
        }
        ///// <summary>
        ///// 通过微信扫码进行登录
        ///// </summary>
        ///// <param name="code"></param>
        ///// <returns></returns>
        //[HttpPost, SkipAuthCheck, UseDefaultRSAKey, SkipIPCheck]
        //public LoginByWeChartCodeWithRedirect_Out LoginByWeChartCodeWithRedirect(string code)
        //{
        //    return BLL_Login.Instance.LoginByWeChartCode(code, HttpContext);
        //}

        /// <summary>
        /// 通过微信扫码进行登录
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck, UseDefaultRSAKey, SkipUserSupplementInfo, SkipIPCheck]
        public string LoginByWeChartGZCode(string code)
        {
            return BLL_Login.Instance.LoginByWeChartGZCode(code, HttpContext);
        }

        ///// <summary>
        ///// 通过微信Openid进行登录
        ///// </summary>
        ///// <param name="openid"></param>
        ///// <returns></returns>
        //[HttpPost]
        //public string LoginByOpenid(string openid)
        //{
        //    return BLL_Login.Instance.LoginByOpenid(openid, HttpContext);
        //}

        /// <summary>
        /// 通过微信扫码绑定微信
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpPost]
        public void BindWeChat(string code)
        {
            BLL_Login.Instance.BindWeChat(code);
        }

        /// <summary>
        /// 检测微信扫码绑定微信
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public bool CheckBindWeChat()
        {
            return BLL_Login.Instance.CheckBindWeChat();
        }

        /// <summary>
        /// 获取微信公众号二维码
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public string GetBindWxImg()
        {
            return BLL_Login.Instance.GetBindWxImg();
        }

        /// <summary>
        /// 解除绑定微信
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public void UnBindWeChat()
        {
            BLL_Login.Instance.UnBindWeChat();
        }

        /// <summary>
        /// 修改手机号--发送验证码
        /// </summary>
        /// <param name="getUpdatePhoneVerificationCode_In"></param>
        [HttpPost]
        public void GetUpdatePhoneVerificationCode(GetUpdatePhoneVerificationCode_In getUpdatePhoneVerificationCode_In)
        {
            BLL_Login.Instance.GetUpdatePhoneVerificationCode(getUpdatePhoneVerificationCode_In);
        }

        /// <summary>
        /// 修改手机号--校验验证码
        /// </summary>
        /// <param name="checkUpdatePhoneVerifyCode_In"></param>
        [HttpPost]
        public void CheckUpdatePhoneVerifyCode(CheckUpdatePhoneVerifyCode_In checkUpdatePhoneVerifyCode_In)
        {
            BLL_Login.Instance.CheckUpdatePhoneVerifyCode(checkUpdatePhoneVerifyCode_In);
        }

        /// <summary>
        ///  修改手机号--发送验证码
        /// </summary>
        /// <param name="getUpdateNewPhoneVerificationCode_In"></param>
        [HttpPost]
        public void GetUpdateNewPhoneVerificationCode(GetUpdateNewPhoneVerificationCode_In getUpdateNewPhoneVerificationCode_In)
        {
            BLL_Login.Instance.GetUpdateNewPhoneVerificationCode(getUpdateNewPhoneVerificationCode_In);
        }

        /// <summary>
        /// 通过手机号码修改手机号码
        /// </summary>
        /// <param name="updateNewPhoneByVerifyCode_In"></param>
        [HttpPost]
        public void UpdateNewPhoneByVerifyCode(UpdateNewPhoneByVerifyCode_In updateNewPhoneByVerifyCode_In)
        {
            BLL_Login.Instance.UpdateNewPhoneByVerifyCode(updateNewPhoneByVerifyCode_In);
        }

        /// <summary>
        /// 密码找回--发送验证码
        /// </summary>
        /// <param name="getRetPwdVerifyCode_In"></param>
        [HttpPost, SkipAuthCheck, UseDefaultRSAKey,SkipIPCheck]
        public void GetRetrievePwdVerificationCode(GetRetrievePwdVerificationCode_In getRetPwdVerifyCode_In)
        {
            BLL_Login.Instance.GetRetrievePwdVerificationCode(getRetPwdVerifyCode_In);
        }

        /// <summary>
        /// 找回密码--校验验证码
        /// </summary>
        /// <param name="checkRetrievePwdVerifyCode_In"></param>
        [HttpPost, SkipAuthCheck, UseDefaultRSAKey, SkipIPCheck]
        public void CheckRetrievePwdVerifyCode(CheckRetrievePwdVerifyCode_In checkRetrievePwdVerifyCode_In)
        {
            BLL_Login.Instance.CheckRetrievePwdVerifyCode(checkRetrievePwdVerifyCode_In);
        }

        /// <summary>
        /// 找回密码--重置密码
        /// </summary>
        /// <param name="retPwdByVerifyCode_In"></param>
        [HttpPost, SkipAuthCheck, UseDefaultRSAKey, SkipIPCheck]
        public void RetrievePwdByVerifyCode(RetrievePwdByVerifyCode_In retPwdByVerifyCode_In)
        {
            BLL_Login.Instance.RetrievePwdByVerifyCode(retPwdByVerifyCode_In);
        }

        /// <summary>
        /// 通过token获取用户基本信息(用户公钥)
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRSAKey, SkipUserSupplementInfo]
        public GetLoginBasicInfo_Out GetLoginBasicInfo()
        {
            return BLL_Login.Instance.GetLoginBasicInfo(HttpContext);
        }

        /// <summary>
        /// 登录后根据token获取用户可操作的菜单
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<Db_sys_form> GetFormMenusByUserId()
        {
            return RedisCache.UserMenuRight.GetUserMenuRight(UserTokenInfo.id);
            //return DbOpe_sys_form.Instance.GetFormMenusByUserId(UserTokenInfo.id);
        }

        /// <summary>
        /// 根据菜单Id获取可查看列表的集合
        /// </summary>
        /// <param name="formTableId"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<Db_sys_form> GetFormTablesByMenuId(string formTableId)
        {
            return DbOpe_sys_form.Instance.GetFormButtonsByTableId(formTableId, UserTokenInfo.id);
        }

        /// <summary>
        /// 创建SSE连接，监测静默时间
        /// </summary>
        [HttpGet, SkipRightCheck, SkipSilence]
        public async void CheckUserSilenceTimeSSE()
        {
            BLL_Login.Instance.CheckUserSilenceTimeSSE(HttpContext, Response);
        }

        /// <summary>
        /// 登出操作，删除Redis中的token
        /// </summary>
        [HttpPost, SkipRightCheck, SkipSilence]
        public void LogOut()
        {
            BLL_Login.Instance.LogOut(HttpContext);
        }

        /// <summary>
        /// 验证token是否有效
        /// </summary>
        /// <param name="verifyToken_In"></param>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck, UseDefaultRSAKey, SkipIPCheck,SkipRecordLog]
        public bool VerifyToken(VerifyToken_In verifyToken_In)
        {
            return BLL_Login.Instance.VerifyToken(verifyToken_In.UserId, verifyToken_In.Token, HttpContext);
        }

        /// <summary>
        /// 通过钉钉免登录code进行登录
        /// </summary>
        /// <param name="code">钉钉免登录code</param>
        /// <returns>登录token</returns>
        [HttpPost, SkipAuthCheck, UseDefaultRSAKey, SkipIPCheck]
        public string LoginByDingTalkCode(string code)
        {
            return BLL_Login.Instance.LoginByDingTalkCode(code, HttpContext);
        }

        /// <summary>
        /// 通过钉钉扫码绑定钉钉
        /// </summary>
        /// <param name="code">钉钉免登录code</param>
        /// <returns></returns>
        [HttpPost]
        public void BindDingTalk(string code)
        {
            BLL_Login.Instance.BindDingTalk(code);
        }

        /// <summary>
        /// 解除绑定钉钉
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public void UnBindDingTalk()
        {
            BLL_Login.Instance.UnBindDingTalk();
        }
    }


}
