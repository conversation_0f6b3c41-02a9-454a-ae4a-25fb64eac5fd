﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_contractisinvoice")]
    public class Db_v_contractisinvoice
    {
           /// <summary>
           /// Desc:合同表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string contractId {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int IsInvoice {get;set;}

    }
}
