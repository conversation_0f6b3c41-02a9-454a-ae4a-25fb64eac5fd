using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.Linq;
using SqlSugar;
using CRM2_API.DAL.DbCommon;
using Newtonsoft.Json;
using CRM2_API.Common.Cache;
using static CRM2_API.Common.Cache.RedisCache;
using Aspose.Pdf;
using CRM2_API.Common;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 查询部分
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {

        /// <summary>
        /// 根据发票申请ID获取发票信息(必须是已经开具的发票)
        /// </summary>
        /// <param name="invoiceApplicationId">发票申请ID</param>
        /// <returns>发票信息</returns>
        public InvoiceInfo GetInvoiceInfo(string invoiceApplicationId)
        {
            var application = DbOpe_crm_invoice_application.Instance.GetData(a => a.Id == invoiceApplicationId && a.Deleted != true);
            if (application == null)
            {
                throw new ApiException("未找到发票申请信息");
            }
            var invoice = DbOpe_crm_invoice.Instance.GetData(i => i.InvoiceApplicationId == invoiceApplicationId && i.Deleted != true);
            if (invoice == null)
            {
                throw new ApiException("未找到发票信息");
            }
            var contract = DbOpe_crm_contract.Instance.GetData(c => c.Id == invoice.ContractId && c.Deleted != true);
            if (contract == null && application.BillingType != (int)EnumBillingType.SupplementInvoice)
            {
                throw new ApiException("未找到合同信息");
            }
            //判断权限，后台管理人员可以看后台备注，客户经理不能看
            bool managerRole = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);

            var applicantName = RedisCache.UserWithOrg.GetUserById(application.ApplicantId)?.UserWithOrgFullName ?? string.Empty;
            return new InvoiceInfo
            {
                Id = invoice.Id,
                // 合同相关信息
                ContractId = invoice.ContractId,
                ContractNumber = contract != null ? contract.ContractNo : string.Empty,
                ContractName = contract != null ? contract.ContractName : string.Empty,
                            // 到账记录信息
                            ReceiptId = invoice.ReceiptId,
                            // 申请信息
                            InvoiceApplicationId = invoice.InvoiceApplicationId,
                            ApplicantName = applicantName,
                            // 审核信息
                            InvoiceReviewId = invoice.InvoiceReviewId,
                            // 基本发票信息
                            InvoiceNumber = invoice.InvoiceNumber,
                            InvoiceCode = invoice.InvoiceCode,
                            InvoiceDate = invoice.InvoicingDate,
                            InvoiceAmount = invoice.InvoicedAmount,
                            // 开票相关信息
                            BillingCompany = invoice.BillingCompany,
                            BillingCompanyName = RedisCache.CollectingCompany.GetCollectingCompanyById(invoice.BillingCompany)?.SellerName ?? string.Empty,
                            BillingHeader = invoice.BillingHeader,
                            CreditCode = invoice.CreditCode,
                            BillingType = invoice.BillingType,
                            InvoiceType = (EnumInvoiceType)invoice.InvoiceType,
                            InvoiceForm = EnumInvoicingForm.ElectronicInvoice, // 默认为电子发票，后续根据数据库字段补充
                            InvoicingDetails = invoice.InvoicingDetails,
                            // 接收人信息
                            Recipient = application.Recipient,
                            Email = application.Email,
                            // 状态信息
                            MatchingStatus = (EnumInvoiceMatchingStatus)invoice.MatchingStatus,
                            RefundStatus = (EnumInvoiceRefundStatus)invoice.RefundStatus,
                            TransactionType = invoice.TransactionType,
                            // 关联发票信息
                            RelatedInvoiceId = invoice.RelatedInvoiceId,
                            // 其他字段
                            IsSupplementInvoice = invoice.IsSupplementInvoice,
                            InvoiceLink = invoice.InvoiceLink,
                            // 发票后台备注
                            InvoiceBackgroundRemark = managerRole ? invoice.Remark : "--",
                            // 审计字段
                            CreateUser = invoice.CreateUser,
                            CreateDate = invoice.CreateDate,
                            UpdateUser = invoice.UpdateUser,
                            UpdateDate = invoice.UpdateDate,
                            // 标记为正式发票
                            IsProformaInvoice = false
            };
        }
        
        /// <summary>
        /// 根据发票申请ID获取发票详细信息（包括退票信息、申请信息、审核信息等）
        /// </summary>
        /// <param name="invoiceApplicationId">发票申请ID</param>
        /// <returns>发票详细信息</returns>
        public InvoiceDetailResponse GetInvoiceDetailWithRefundInfo(string invoiceApplicationId)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(invoiceApplicationId))
                {
                    throw new ApiException("发票申请ID不能为空");
                }
                
                // 获取发票申请信息
                var application = DbOpe_crm_invoice_application.Instance.GetData(a => 
                    a.Id == invoiceApplicationId && a.Deleted != true);
                
                if (application == null)
                {
                    throw new ApiException("发票申请信息不存在或已被删除");
                }
                var isSupplementInvoice = application.BillingType == (int)EnumBillingType.SupplementInvoice;

                Model.ControllersViewModel.VM_ContractInvoice.ContractInfoAndReceipt_Out contract = null;
                if(!isSupplementInvoice){
                    // 获取合同信息
                    contract = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(application.ContractId, true);
                    if (contract == null)
                    {
                        throw new ApiException("没有数据权限或合同信息不存在");
                    }
                }

                // 检查权限
                bool superRole = BLL_Role.Instance.CheckSuperUser();
                // 判断用户是否为后台管理人员
                bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                if (!superRole)
                {
                    // 获取当前用户信息
                    var currentUser = DbOpe_sys_user.Instance.GetUserById(UserId);
                    if (!isBackendManager)
                    {
                        if(isSupplementInvoice){
                            throw new ApiException("没有权限查看该补充发票信息");
                        }
                        // 检查是否为当前客户的客户经理
                        var hasPermission = DbContext.Crm2Db.Queryable<Db_v_customer_subcompany_private_user>()
                            .Any(cspu => cspu.Id == contract.FirstParty && 
                                       cspu.CompanyCurrentUser == UserId);
                                    
                        if (!hasPermission)
                        {
                            throw new ApiException("没有权限查看该发票信息");
                        }
                    }
                }

                // 查询发票信息 - 正式发票
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                    i.InvoiceApplicationId == invoiceApplicationId && i.Deleted != true);
            
                // 查询形式发票信息
                var proformaInvoice = DbOpe_crm_proforma_invoice.Instance.GetData(p => 
                    p.ApplicationId == invoiceApplicationId && p.Deleted != true);

                // 获取申请人信息 - 使用缓存获取申请人信息
                string applicantName = string.Empty;
                if (!string.IsNullOrEmpty(application.ApplicantId))
                {
                    // 使用缓存获取用户信息
                    var applicant = DbOpe_v_userwithorg.Instance.GetUserByIdFromCache(application.ApplicantId);
                    applicantName = applicant?.UserWithOrgFullName ?? string.Empty;
                }
                
                // 获取开票公司名称 - 使用缓存获取收款公司信息
                string billingCompanyName = string.Empty;
                if (!string.IsNullOrEmpty(application.BillingCompany))
                {
                    // 使用缓存获取收款公司信息
                    var company = DbOpe_crm_collectingcompany.Instance.GetCollectingCompanyByIdFromCache(application.BillingCompany);
                    if (company != null)
                    {
                        // 如果能找到对应的公司记录，使用公司名称
                        billingCompanyName = company.SellerName;
                    }
                    else
                    {
                        // 如果找不到公司记录，直接使用BillingCompany的值作为公司名称
                        billingCompanyName = application.BillingCompany;
                    }
                }
                
                // 获取开票抬头名称
                string billingHeaderName = string.Empty;
                if (!string.IsNullOrEmpty(application.BillingHeader))
                {
                    var header = DbOpe_crm_customer_subcompany.Instance.GetData(cs => cs.Id == application.BillingHeader && cs.Deleted == 0);
                    billingHeaderName = header?.CompanyName ?? string.Empty;
                }
                
                // 初始化返回对象
                var response = new InvoiceDetailResponse
                {
                    // 发票申请信息
                    ApplicationInfo = new InvoiceApplicationInfo
                    {
                        Id = application.Id,
                        ContractId = application.ContractId,
                        ApplicantId = application.ApplicantId,
                        ReceiptId = application.ReceiptId,
                        ApplicantName = applicantName,
                        ApplyTime = application.ApplyTime,
                        AuditStatus = (EnumInvoiceApplicationStatus)application.AuditStatus,
                        DisplayStatus = (EnumInvoiceDisplayStatus)(application.DisplayStatus ?? 0),
                        AppliedAmount = application.AppliedAmount,
                        BillingType = (EnumBillingType)application.BillingType,
                        InvoiceType = (EnumInvoiceType)application.InvoiceType,
                        BillingCompany = application.BillingCompany,
                        BillingCompanyName = billingCompanyName,
                        BillingHeader = application.BillingHeader,
                        // 使用查询到的开票抬头名称
                        BillingHeaderName = billingHeaderName,
                        CreditCode = application.CreditCode,
                        Recipient = application.Recipient,
                        Email = application.Email,
                        Remark = application.Remark,
                        ExpectedInvoicingDate = application.ExpectedInvoicingDate,
                        // 添加发票明细信息
                        InvoiceDetails = application.InvoicingDetails
                    },
                    
                    // 合同信息
                    ContractInfo = contract,
                    
                    // 初始化列表，避免空引用
                    ReviewInfo = new List<InvoiceReviewInfo>(),
                    MatchingInfo = new List<InvoiceMatchingInfo>(),
                    ApplicationAttachments = new List<BM_FileInfo>()
                };
                

                if(application.BillingType == (int)EnumBillingType.InvoicingUponReceipt){
                    var applicationMatchingReceiptInfo = GetContractReceiptsWithMatchingStatus(application.ContractId,application.ReceiptId).Data.FirstOrDefault();
                    if(applicationMatchingReceiptInfo != null){
                        response.ApplicationMatchingReceiptInfo = applicationMatchingReceiptInfo;
                    }
                }



                // 获取发票申请的附件 - 从已有的合同发票附件表中查询
                var applicationAttachments = DbContext.Crm2Db.Queryable<Db_crm_contract_invoiceappl_attachment>()
                    .Where(a => a.ContractInvoiceApplId == application.Id && a.Deleted != true)
                    .Select(a => new BM_FileInfo
                    {
                        Id = a.Id,
                        FileName = a.FileName,
                        FilePath = a.FilePath
                    })
                    .ToList();
                
                // 设置申请附件
                if (applicationAttachments != null && applicationAttachments.Any())
                {
                    response.ApplicationAttachments = applicationAttachments;
                }
                
                // 获取匹配记录 - 无论匹配状态如何都查询
                var matchings = DbContext.Crm2Db.Queryable<Db_crm_invoice_receipt_matching>() 
                            .LeftJoin<Db_crm_contract_receiptregister>((m, r) => m.ReceiptId == r.Id && r.Deleted != true)
                            .LeftJoin<Db_crm_contract_receipt_details>((m, r, rd) => r.Id == rd.ContractReceiptRegisterId && rd.Deleted != true)
                            .LeftJoin<Db_crm_collectioninfo>((m, r, rd, ci) => rd.CollectionInfoId == ci.Id && ci.Deleted != true)
                            .Where(m => m.InvoiceApplicationId == invoiceApplicationId && m.Deleted != true)
                            .Select((m, r, rd, ci) => new InvoiceMatchingInfo
                            {
                                Id = m.Id,
                                ReceiptId = m.ReceiptId,
                                ReceiptAmount = ci.ArrivalAmount ?? 0, // 使用回款信息表的到账金额
                                ReceiptDate = ci.ArrivalDate ?? DateTime.Now, // 使用回款信息表的到账日期
                                ReceiptNumber = m.ReceiptId, // 使用匹配表的收据ID作为收据编号
                                MatchingAmount = ci.ArrivalAmount ?? 0, // 使用回款信息表的到账金额作为匹配金额
                                MatchingTime = m.MatchingTime ?? DateTime.Now, // 匹配时间，如果为空则使用当前时间
                                MatchingStatus = (EnumInvoiceMatchingStatus)m.MatchingStatus,
                                MatchingMethod = (int)EnumIsManualMatching.Auto, // 默认为自动匹配
                                AuditorId = m.AuditorId ?? string.Empty, // 审核人ID，如果为空则使用空字符串
                                MatchingUser = m.MatchingUser ?? string.Empty, // 匹配操作人ID，如果为空则使用空字符串
                                AuditTime = m.AuditTime ?? DateTime.Now, // 审核时间，如果为空则使用当前时间
                                AuditRemark = m.AuditRemark ?? string.Empty, // 审核备注，如果为空则使用空字符串/ 匹配操作人名称，如果为空则使用空字符串
                                Currency = ci.Currency ?? 0,
                                PaymentCompany = ci.PaymentCompany,
                                PaymentCompanyName = ci.PaymentCompanyName,
                                CollectingCompany = ci.CollectingCompany
                            })
                            .Mapper(t => {
                                t.MatchingUserName = GetMatchingUserNameFromCache(t.MatchingUser);
                                t.AuditorName = GetAuditorNameFromCache(t.AuditorId);
                                t.CurrencyName = EnumHelper.GetEnumDescription<EnumCurrency>(t.Currency);
                                t.CollectingCompanyName = RedisCache.CollectingCompany.GetCollectingCompanyById(t.CollectingCompany)?.SellerName??"";
                            })
                            .ToList();
                        
                response.MatchingInfo = matchings;
                // 如果是形式发票，填充形式发票信息
                if (application.InvoiceType == (int)EnumInvoiceType.ProformaTicket && proformaInvoice != null)
                {
                    response.InvoiceInfo = new InvoiceInfo
                    {
                        Id = proformaInvoice.Id,
                        // 合同相关信息
                        ContractId = proformaInvoice.ContractId,
                        ContractNumber = contract != null ? contract.ContractNo : string.Empty,
                        ContractName = contract != null ? contract.ContractName : string.Empty,
                        // 申请信息
                        InvoiceApplicationId = proformaInvoice.ApplicationId,
                        ApplicantName = applicantName,
                        // 基本发票信息
                        InvoiceNumber = proformaInvoice.InvoiceNumber,
                        InvoiceDate = proformaInvoice.InvoiceDate,
                        InvoiceAmount = proformaInvoice.Amount,
                        // 开票相关信息
                        BillingCompany = application.BillingCompany,
                        BillingCompanyName = billingCompanyName,
                        BillingHeader = proformaInvoice.BillingHeader,
                        CreditCode = proformaInvoice.TaxpayerCode,
                        BillingType = application.BillingType,
                        InvoiceType = EnumInvoiceType.ProformaTicket,
                        InvoiceForm = EnumInvoicingForm.ElectronicInvoice,
                        InvoicingDetails = proformaInvoice.InvoiceDetails,
                        // 接收人信息
                        Recipient = proformaInvoice.Recipient,
                        Email = proformaInvoice.Email,
                        // PDF路径
                        InvoiceLink = proformaInvoice.PdfFilePath,
                        // 发票后台备注
                        InvoiceBackgroundRemark = "--",
                        // 审计字段
                        CreateUser = proformaInvoice.CreateUser,
                        CreateDate = proformaInvoice.CreateDate,
                        UpdateUser = proformaInvoice.UpdateUser,
                        UpdateDate = proformaInvoice.UpdateDate,
                        // 标记为形式发票
                        IsProformaInvoice = true,
                        MatchingStatus = matchings != null && matchings.Any() ? (EnumInvoiceMatchingStatus)matchings[0].MatchingStatus : EnumInvoiceMatchingStatus.NotReceived
                    };
                }
                // 如果正式发票存在，填充发票信息
                // 不是后台管理人员的话，不能看审核中的正式发票和附件等
                else if (invoice != null)
                {
                    response.InvoiceInfo = GetInvoiceInfo(invoiceApplicationId);
                              
                    // 查询退票申请信息
                    var refundApplication = DbContext.Crm2Db.Queryable<Db_crm_invoice_refund_application>()
                        .LeftJoin<Db_crm_invoice_refund_review>((r, refund) => r.Id == refund.RefundApplicationId && refund.Deleted != true)
                        .LeftJoin<Db_v_userwithorg>((r, refund, u) => r.ApplicantId == u.Id)
                        // 关联红字发票信息
                        .LeftJoin<Db_crm_invoice>((r, refund, u, redInv) => r.RedInvoiceId == redInv.Id && redInv.Deleted != true)
                        .Where((r, refund, u, redInv) => r.InvoiceId == invoice.Id && r.Deleted != true)
                        .OrderByDescending((r, refund, u, redInv) => r.ApplyTime)
                        .Select((r, refund, u, redInv) => new InvoiceRefundInfo
                        {
                            Id = r.Id,
                            ApplicantId = r.ApplicantId,
                            ApplicantName = u.UserWithOrgFullName,
                            ApplyTime = r.ApplyTime ?? DateTime.Now,
                            RefundReason = r.RefundReason,
                            RefundAmount = r.RefundAmount,
                            AuditStatus = (EnumRefundApplicationStatus)r.AuditStatus,
                            RefundNumber = refund.RefundNumber,
                            RefundDate = refund.RefundDate ?? DateTime.Now,
                            InvoiceType =  refund.InvoiceType == null ? invoice.InvoiceType : (int)refund.InvoiceType.Value,
                            // 添加红字发票信息
                            RedInvoiceId = r.RedInvoiceId,
                            RedInvoiceNumber = redInv.InvoiceNumber,
                            AuditTime = null,
                            AuditorId = string.Empty,
                            AuditorName = string.Empty,
                            ReviewInfo = new List<InvoiceRefundReviewInfo>(),
                            //  退票后台备注
                            RefundBackgroundRemark = isBackendManager ? r.RefundBackgroundRemark : "--"
                        })
                        .First();

                    // 如果存在退票申请，查询所有审核记录
                    if (refundApplication != null)
                    {
                        var refundReviewInfo = DbContext.Crm2Db.Queryable<Db_crm_invoice_refund_review>()
                            .Where(r => r.RefundApplicationId == refundApplication.Id)
                            .OrderBy(r =>  r.Deleted)
                            .OrderByDescending(r =>  r.ProcessTime)
                            .Select(r => new InvoiceRefundReviewInfo
                            {
                                InvoiceType = r.InvoiceType == null ? invoice.InvoiceType : (int)r.InvoiceType.Value,
                            },true)
                            .Mapper(t => {
                                t.AuditorName = GetAuditorNameFromCache(t.AuditorId);
                                t.ProcessorName = GetAuditorNameFromCache(t.ProcessorId);
                                //客户经理只能看拒绝状态的经办人备注
                                if(!isBackendManager ){
                                    t.AuditFeedback = "--";
                                    if(refundApplication.AuditStatus != EnumRefundApplicationStatus.Rejected){
                                        t.ProcessorRemark = "--";
                                    }
                                }
                                t.BillingHeader = invoice.BillingHeader;
                                t.BillingCompany = invoice.BillingCompany;
                                t.BillingCompanyName = RedisCache.CollectingCompany.GetCollectingCompanyById(invoice.BillingCompany)?.SellerName ?? string.Empty;
                            })
                            .ToList();

                            // 设置审核记录列表
                            refundApplication.ReviewInfo = refundReviewInfo;

                            // 如果有审核记录，设置最新的审核信息
                            if (refundReviewInfo.Any())
                            {
                                var latestReview = refundReviewInfo.First();
                                refundApplication.AuditTime = latestReview.AuditTime;
                                refundApplication.AuditorId = latestReview.AuditorId;
                                refundApplication.AuditorName = latestReview.AuditorName;
                            }

                            // 初始化退票附件列表
                            refundApplication.Attachments = new List<BM_FileInfo>();
                            
                            // 获取退票申请的附件
                            var refundAttachments = DbContext.Crm2Db.Queryable<Db_crm_invoice_refund_attachment>()
                                .Where(a => a.RefundApplicationId == refundApplication.Id && a.Deleted != true)
                                .Select(a => new BM_FileInfo
                                {
                                    Id = a.Id,
                                    FileName = a.FileName,
                                    FilePath = a.FilePath
                                })
                                .ToList();
                            
                            // 设置退票附件
                            if (refundAttachments != null && refundAttachments.Any())
                            {
                                refundApplication.Attachments = refundAttachments;
                            }

                            // 获取退票OCR识别结果
                            var refundOcrResult = DbContext.Crm2Db.Queryable<Db_crm_invoice_recognition>()
                                .Where(r => r.InvoiceApplicationId == refundApplication.Id && r.Deleted != true)
                                .OrderByDescending(r => r.CreateDate)
                                .First();   

                            if (refundOcrResult != null)
                            {
                                refundApplication.OcrInfo = new OcrInvoiceResult
                                {
                                    RecognitionId = refundOcrResult.Id,
                                    InvoiceCode = refundOcrResult.InvoiceCode,
                                    InvoiceNumber = refundOcrResult.InvoiceNumber,  
                                    InvoiceDate = refundOcrResult.InvoiceDate?.ToString("yyyy-MM-dd"),
                                    BuyerName = refundOcrResult.BuyerName,
                                    SellerName = refundOcrResult.SellerName,
                                    BuyerTaxNumber = refundOcrResult.BuyerTaxCode,
                                    SellerTaxNumber = refundOcrResult.SellerTaxCode,
                                    TotalAmount = refundOcrResult.InvoiceAmount ?? 0,
                                    TotalTax = refundOcrResult.TaxAmount ?? 0,
                                    TotalAmountWithTax = (refundOcrResult.TotalAmount ?? 0).ToString("F2"),
                                    InvoiceDetails = refundOcrResult.InvoiceDetails,
                                    OriginalText = refundOcrResult.RecognitionResult,
                                    InvoiceType = refundOcrResult.InvoiceTypeString
                                };  
                                refundApplication.OcrInfo.CollectingCompanyId = RedisCache.CollectingCompany.GetCollectingCompanyBySellerName(refundOcrResult.SellerName)?.Id ?? string.Empty;
                                // 查询比对结果记录
                                var comparisonRecord = DbContext.Crm2Db.Queryable<Db_crm_invoice_comparison>()
                                    .Where(r => r.RecognitionId == refundOcrResult.Id && r.Deleted != true)
                                    .OrderByDescending(r => r.CreateDate)
                                    .First();
                                
                                if (comparisonRecord != null && !string.IsNullOrEmpty(comparisonRecord.ComparisonDetails))
                                {
                                    try
                                    {
                                        // 从JSON字符串解析比对结果
                                        var fieldComparisonList = JsonConvert.DeserializeObject<List<FieldComparisonResult>>(comparisonRecord.ComparisonDetails);
                                        if (fieldComparisonList != null && fieldComparisonList.Any())
                                        {
                                            refundApplication.ComparisonResults = fieldComparisonList;
                                        }
                                        else
                                        {
                                            refundApplication.ComparisonResults = new List<FieldComparisonResult>();
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        LogUtil.AddErrorLog($"解析发票比对结果时发生错误: {ex.Message}");
                                        refundApplication.ComparisonResults = new List<FieldComparisonResult>();
                                    }
                                }
                                else
                                {
                                    refundApplication.ComparisonResults = new List<FieldComparisonResult>();
                                }
                            }
                            if(refundApplication.AuditStatus == EnumRefundApplicationStatus.Completed){
                                var refundInvoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == refundApplication.RedInvoiceId && i.Deleted != true);
                                refundApplication.RefundInvoiceInfo = new InvoiceInfo
                                {
                                    Id = refundInvoice.Id,
                                    InvoiceNumber = refundInvoice.InvoiceNumber,
                                    InvoiceDate = refundInvoice.InvoicingDate,
                                    InvoiceAmount = refundInvoice.InvoicedAmount,
                                    BillingCompany = refundInvoice.BillingCompany,
                                    BillingCompanyName = RedisCache.CollectingCompany.GetCollectingCompanyById(refundInvoice.BillingCompany)?.SellerName ?? string.Empty,
                                    BillingHeader = refundInvoice.BillingHeader,
                                    CreditCode = refundInvoice.CreditCode,
                                    BillingType = refundInvoice.BillingType,
                                    InvoiceType = (EnumInvoiceType)refundInvoice.InvoiceType,
                                    InvoiceForm = EnumInvoicingForm.ElectronicInvoice,
                                    InvoicingDetails = refundInvoice.InvoicingDetails,
                                    Recipient = refundInvoice.Recipient,
                                    Email = refundInvoice.Email,
                                    InvoiceLink = refundInvoice.InvoiceLink,
                                    // 发票后台备注
                                    InvoiceBackgroundRemark =  isBackendManager ? refundInvoice.Remark : "--",
                                    CreateUser = refundInvoice.CreateUser,
                                    CreateDate = refundInvoice.CreateDate,
                                    UpdateUser = refundInvoice.UpdateUser,
                                    UpdateDate = refundInvoice.UpdateDate,
                                    IsProformaInvoice = false,
                                    MatchingStatus = (EnumInvoiceMatchingStatus)refundInvoice.MatchingStatus,
                                    RefundStatus = (EnumInvoiceRefundStatus)refundInvoice.RefundStatus,
                                    TransactionType = refundInvoice.TransactionType,
                                    RelatedInvoiceId = refundInvoice.RelatedInvoiceId,
                                    IsSupplementInvoice = refundInvoice.IsSupplementInvoice,
                                    InvoiceCode = refundInvoice.InvoiceCode,
                                };
                            }
                            response.RefundInfo = refundApplication;
                        }
                    }
                
                // 获取发票附件
                var attachments = DbContext.Crm2Db.Queryable<Db_crm_contract_invoice_attachment>()
                        .Where(a => a.ContractInvoiceId == invoiceApplicationId && a.Deleted != true)
                        .Select(a => new BM_FileInfo
                        {
                            Id = a.Id,
                            FileName = a.FileName,
                            FilePath = a.FilePath
                        })
                        .ToList();
                    
                response.Attachments = attachments;
                // 获取OCR识别结果和比对结果
                // 查询OCR识别记录
                var ocrRecognition = DbContext.Crm2Db.Queryable<Db_crm_invoice_recognition>()
                    .Where(r => r.InvoiceApplicationId == invoiceApplicationId && r.Deleted != true)
                    .OrderByDescending(r => r.CreateDate)
                    .First();

                if (ocrRecognition != null)
                {
                    // 设置OCR识别结果
                    response.OcrInfo = new OcrInvoiceResult
                    {
                        RecognitionId = ocrRecognition.Id,
                        InvoiceCode = ocrRecognition.InvoiceCode,
                        InvoiceNumber = ocrRecognition.InvoiceNumber,
                        InvoiceDate = ocrRecognition.InvoiceDate?.ToString("yyyy-MM-dd"),
                        BuyerName = ocrRecognition.BuyerName,
                        SellerName = ocrRecognition.SellerName,
                        BuyerTaxNumber = ocrRecognition.BuyerTaxCode,
                        SellerTaxNumber = ocrRecognition.SellerTaxCode,
                        TotalAmount = ocrRecognition.InvoiceAmount ?? 0,
                        TotalTax = ocrRecognition.TaxAmount ?? 0,
                        TotalAmountWithTax = (ocrRecognition.TotalAmount ?? 0).ToString("F2"),
                        InvoiceDetails = ocrRecognition.InvoiceDetails,
                        OriginalText = ocrRecognition.RecognitionResult,
                        InvoiceType = ocrRecognition.InvoiceTypeString
                    };
                    response.OcrInfo.CollectingCompanyId = RedisCache.CollectingCompany.GetCollectingCompanyBySellerName(ocrRecognition.SellerName)?.Id ?? string.Empty;
                    // 查询比对结果记录
                    var comparisonRecord = DbContext.Crm2Db.Queryable<Db_crm_invoice_comparison>()
                        .Where(r => r.InvoiceApplicationId == invoiceApplicationId && r.RecognitionId == ocrRecognition.Id && r.Deleted != true)
                        .OrderByDescending(r => r.CreateDate)
                        .First();
                    
                    if (comparisonRecord != null && !string.IsNullOrEmpty(comparisonRecord.ComparisonDetails))
                    {
                        try
                        {
                            // 从JSON字符串解析比对结果
                            var fieldComparisonList = JsonConvert.DeserializeObject<List<FieldComparisonResult>>(comparisonRecord.ComparisonDetails);
                            if (fieldComparisonList != null && fieldComparisonList.Any())
                            {
                                response.ComparisonResults = fieldComparisonList;
                            }
                            else
                            {
                                response.ComparisonResults = new List<FieldComparisonResult>();
                            }
                        }
                        catch (Exception ex)
                        {
                            LogUtil.AddErrorLog($"解析发票比对结果时发生错误: {ex.Message}");
                            response.ComparisonResults = new List<FieldComparisonResult>();
                        }
                    }
                    else
                        {
                            response.ComparisonResults = new List<FieldComparisonResult>();
                        }
                }
                
                // 获取发票审核记录
                var reviews = DbContext.Crm2Db.Queryable<Db_crm_invoice_review>()
                            .Where(r => r.InvoiceApplicationId == invoiceApplicationId )
                            //这里把删除的记录也查询出来，因为审核记录删除后，需要保留历史记录，详情里需要显示
                            // .Where(r => r.Deleted != true)
                            .OrderBy(r =>  r.Deleted)
                            .OrderByDescending(r =>  r.AuditTime)
                            .Select(r => new InvoiceReviewInfo
                            {
                            },true)
                            .Mapper(t => {
                                t.ProcessorName = GetAuditorNameFromCache(t.ProcessorId);
                                t.AuditorName = GetAuditorNameFromCache(t.AuditorId);
                                t.BillingCompanyName = RedisCache.CollectingCompany.GetCollectingCompanyById( t.BillingCompany)?.SellerName ?? "";
                                //客户经理只能看拒绝状态的经办人备注
                                if(!isBackendManager ){
                                    t.AuditFeedback = "--";
                                    if(application.AuditStatus != (int)EnumInvoiceApplicationStatus.Rejected){
                                        t.ProcessorRemark = "--";
                                    }
                                }
                            })
                            .ToList();
                        
                response.ReviewInfo = reviews;
                        
                if(!isBackendManager && application.AuditStatus != (int)EnumInvoiceApplicationStatus.Completed){
                        response.InvoiceInfo = null;
                        response.RefundInfo = null;
                        response.Attachments = null;
                        response.OcrInfo = null;
                        response.ComparisonResults = null;
                }

                // 判断权限，如果是后台人员 获取发票后台备注 (还没开票的取申请表的发票备注，已经开票的，如果已经开始退票，取退票备注，否则取发票备注；)
                if(isBackendManager){
                    if(response.InvoiceInfo == null){
                        //如果还没有开票，取申请表的发票备注
                        response.InvoiceBackgroundRemark = application.InvoiceBackgroundRemark;
                    }
                    else{
                        if(response.RefundInfo != null){
                            if(response.RefundInfo.RefundInvoiceInfo != null){
                                //如果退票成功，取退票发票备注 （invoice的remark）
                                response.InvoiceBackgroundRemark = response.RefundInfo.RefundInvoiceInfo.InvoiceBackgroundRemark;
                            }
                            else{
                                //如果退票未成功，取退票申请表的发票备注 （refund_application的refund_background_remark）
                                response.InvoiceBackgroundRemark = response.RefundInfo.RefundBackgroundRemark;
                            }
                        }
                        else{
                            //如果还没有退票，取发票备注 （invoice的remark）
                            response.InvoiceBackgroundRemark = response.InvoiceInfo.InvoiceBackgroundRemark;
                        }
                    }
                }
                else{
                    //如果不是后台人员，不显示发票后台备注
                    response.InvoiceBackgroundRemark = "--";
                }
                return response;
            }
            catch (ApiException ex)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取发票详细信息时发生错误: {ex.Message}");
                throw new ApiException("获取发票详细信息失败");
            }
        }

        /// <summary>
        /// 使用缓存获取审核人姓名
        /// </summary>
        /// <param name="auditorId">审核人ID</param>
        /// <returns>审核人姓名</returns>
        private string GetAuditorNameFromCache(string auditorId)
        {
            if (string.IsNullOrEmpty(auditorId))
                return string.Empty;
                
            // 使用缓存获取用户信息
            var auditor = DbOpe_v_userwithorg.Instance.GetUserByIdFromCache(auditorId);
            return auditor?.Name ?? string.Empty;
        }
        
        /// <summary>
        /// 使用缓存获取匹配操作人姓名
        /// </summary>
        /// <param name="matchingUserId">匹配操作人ID</param>
        /// <returns>匹配操作人姓名</returns>
        private string GetMatchingUserNameFromCache(string matchingUserId)
        {
            if (string.IsNullOrEmpty(matchingUserId))
                return string.Empty;
                
            // 使用缓存获取用户信息
            var matchingUser = DbOpe_v_userwithorg.Instance.GetUserByIdFromCache(matchingUserId);
            return matchingUser?.Name ?? string.Empty;
        }
        
        /// <summary>
        /// 根据合同ID获取发票列表
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="invoicingDateStart">开票开始日期</param>
        /// <param name="invoicingDateEnd">开票结束日期</param>
        /// <param name="invoiceNumber">发票号码</param>
        /// <param name="displayStatus">显示状态列表</param>
        /// <returns>发票列表</returns>
        public ApiTableOut<InvoiceApplicationListItem> GetInvoicesByContractId(
            string contractId, 
            int? pageNumber = null, 
            int? pageSize = null, 
            DateTime? invoicingDateStart = null, 
            DateTime? invoicingDateEnd = null, 
            string? invoiceNumber = null,
            List<int>? displayStatus = null)
        {
                // 初始化响应对象
                var response = new ApiTableOut<InvoiceApplicationListItem>();
                
                try
                {
                    // 参数验证
                if (string.IsNullOrEmpty(contractId))
                {
                    throw new ApiException("合同ID不能为空");
                }
                
                // 验证合同权限
                var contract = DbOpe_crm_contract.Instance.GetContractById(contractId, true);
                if (contract == null)
                {
                    throw new ApiException("没有数据权限或合同数据不存在");
                    }
                    
                    // 创建查询对象 - 直接使用数据库级别的查询
                    var db = DbContext.Crm2Db;

                var query_sql = db.Queryable<Db_crm_invoice_application,Db_crm_contract,Db_crm_invoice,Db_crm_invoice_review,Db_crm_invoice_refund_application,Db_v_customer_subcompany_private_user,Db_crm_proforma_invoice,Db_crm_invoice_receipt_matching,Db_crm_contract_receiptregister,Db_crm_contract_receipt_details,Db_crm_collectioninfo,Db_crm_invoice>
                    (
                        (invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation,collection,refundInvoice) => 
                        new JoinQueryInfos(
                            JoinType.Left, invoiceAppl.ContractId == contract.Id && contract.Deleted != true,
                            JoinType.Left, invoiceAppl.Id == invoice.InvoiceApplicationId && invoice.Deleted != true,
                            JoinType.Left, invoiceAppl.Id == invoiceAudit.InvoiceApplicationId && invoiceAudit.Deleted != true,
                            JoinType.Left, invoice.Id == invoiceRefundAppl.InvoiceId,
                            JoinType.Left, contract.FirstParty == cspu.Id,
                            JoinType.Left, invoiceAppl.Id == proInvoice.ApplicationId && proInvoice.Deleted != true,
                            JoinType.Left, invoiceAppl.Id == matching.InvoiceApplicationId && matching.Deleted != true,
                            JoinType.Left, matching.ReceiptId == receipt.Id && receipt.Deleted != true,
                            JoinType.Left, receiptRelation.ContractReceiptRegisterId == receipt.Id && receiptRelation.Deleted != true,
                            JoinType.Left, collection.Id == receiptRelation.CollectionInfoId && collection.Deleted != true,
                            JoinType.Left, invoice.RelatedInvoiceId == refundInvoice.Id && refundInvoice.Deleted != true
                        )
                    )
                    .Where(invoiceAppl => invoiceAppl.Deleted != true)
                    .Where(invoiceAppl => invoiceAppl.ContractId  == contractId);
                // 检查超级管理员权限 - 超级管理员可以查看所有数据
                bool superRole = BLL_Role.Instance.CheckSuperUser();
                
                // 非超级管理员需要应用权限过滤
                if (!superRole)
                {
                    // 获取当前用户信息，包括角色
                    var currentUser = DbOpe_sys_user.Instance.GetUserById(UserId);
                    
                    // 获取用户表单权限
                    var userFormIds = DbOpe_sys_form.Instance.GetFormIdsByUserId(UserId);
                    
                    // 检查是否有开票权限（经办人权限）
                    bool hasInvoiceOperatorPermission = userFormIds.Any(f => 
                        f.ControllerName == "InvoiceSystem" && 
                        (f.MethodName == "AuditInvoiceApplication" || 
                         f.MethodName == "AutoMatching" ||
                         f.MethodName == "ManualMatching"));
                    
                    // 检查是否有复核权限
                    bool hasInvoiceReviewPermission = userFormIds.Any(f => 
                        f.ControllerName == "InvoiceSystem" && 
                        (f.MethodName == "ReviewInvoiceApplication" || 
                         f.MethodName == "ReviewMatching" ||
                         f.MethodName == "ConfirmInvoiceRefundReview"));
                    
                    // 判断用户是否为后台管理人员
                    bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                    
                    // 判断是否有特定筛选条件
                    bool hasSpecificFilter = !string.IsNullOrEmpty(invoiceNumber) || 
                                           !string.IsNullOrEmpty(contractId) || 
                                           !string.IsNullOrEmpty(contract.FirstParty) ||
                                           !string.IsNullOrEmpty(contract.CustomerId) ||
                                           !string.IsNullOrEmpty(contract.ContractNo);

                    // 客户经理只能看自己客户的发票
                    if (!isBackendManager)
                    {
                        query_sql = query_sql.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) => 
                            cspu.CompanyCurrentUser == UserId // 客户当前所属人是自己
                        );
                    }
                    // 后台管理人员可以查看所有发票
                    else
                    {
                        // 后台管理人员可以查看所有发票，不添加任何限制
                    }
                }
                
                // 应用筛选条件
                if (invoicingDateStart.HasValue)
                {
                    query_sql = query_sql.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) => 
                        (invoice.InvoicingDate >= invoicingDateStart.Value) || 
                        (proInvoice.InvoiceDate >= invoicingDateStart.Value));
                }
                
                if (invoicingDateEnd.HasValue)
                {
                    DateTime endDate = invoicingDateEnd.Value.AddDays(1).AddSeconds(-1);
                    query_sql = query_sql.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) => 
                        (invoice.InvoicingDate <= endDate) || 
                        (proInvoice.InvoiceDate <= endDate));
                }
                
                if (!string.IsNullOrEmpty(invoiceNumber))
                {
                    query_sql = query_sql.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) => 
                        (invoice.InvoiceNumber.Contains(invoiceNumber)) || 
                        (proInvoice.InvoiceNumber.Contains(invoiceNumber)));
                }
                
                if (displayStatus != null && displayStatus.Count > 0)
                {
                    query_sql = query_sql.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) => displayStatus.Contains(invoiceAppl.DisplayStatus ?? 0));
                }

                // // 预加载所有需要的缓存数据
                // var userCache = new Dictionary<string, RedisCache.UserWithOrg.UserWithOrgSimple>();
                // var companyCache = new Dictionary<string, RedisCache.CollectingCompany.CollectingCompanySimple>();

                pageNumber = pageNumber ?? 1;
                pageSize = pageSize ?? 9999;
                int total = 0;
                // 使用SqlSugar的Select和ToPageList方法简化分页和数据获取
                var result = query_sql
                .OrderBy(invoiceAppl => invoiceAppl.ApplyTime, OrderByType.Desc)
                .Select(
                                     (invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection,refundInvoice) => new InvoiceApplicationListItem(){
                        // 基本信息
                        Id = invoiceAppl.Id,
                        InvoiceId = SqlFunc.IsNull(invoice.Id, proInvoice.Id),
                        RefundApplId = SqlFunc.IsNull(invoiceRefundAppl.Id, null),
                        
                        // 合同相关信息
                        ContractId = contract.Id,
                        ContractNum = contract.ContractNum,
                        ContractNumber = contract.ContractNo,
                        ContractName = contract.ContractName,
                        FirstPartyName = cspu.CompanyName,
                        ContractAmount = contract.ContractAmount,
                        ContractStatus = contract.ContractStatus ?? -1,
                        StampReviewStatus = contract.StampReviewStatus ?? -1,

                        Currency = (EnumCurrency)(contract.Currency ?? 1),
                        // 发票申请信息
                        ApplicationStatus = (EnumInvoiceApplicationStatus)invoiceAppl.AuditStatus,
                        ApplicationTime = invoiceAppl.ApplyTime,
                        DisplayStatus = (EnumInvoiceDisplayStatus)(invoiceAppl.DisplayStatus ?? 0),
                        IsReminded = invoiceAppl.IsReminded ?? (int)EnumIsReminder.UnUrgedTicket,
                        RemindedDate = invoiceAppl.RemindedDate,
                        ApplicantId = invoiceAppl.ApplicantId,

                        ApplicationInvoiceHeader = invoiceAppl.BillingHeader,
                        ApplicationInvoiceDetails = invoiceAppl.InvoicingDetails,
                        ApplicationInvoiceAmount= invoiceAppl.AppliedAmount,
                        BillingType = (EnumBillingType)invoiceAppl.BillingType,
                        InvoiceType = SqlFunc.IsNull((EnumInvoiceType)invoice.InvoiceType,(EnumInvoiceType)invoiceAppl.InvoiceType),
                        IsProformaInvoice = invoiceAppl.InvoiceType == (int)EnumInvoiceType.ProformaTicket,

                        // 发票信息
                        InvoiceAmount = SqlFunc.IsNull(invoice.TotalAmount, proInvoice.Amount),
                        InvoiceNumber = SqlFunc.IsNull(invoice.InvoiceNumber, proInvoice.InvoiceNumber),
                        InvoiceDate = SqlFunc.IIF(invoice.InvoicingDate != null, invoice.InvoicingDate.Value.ToString("yyyy-MM-dd"),
                        proInvoice.InvoiceDate != null ? proInvoice.InvoiceDate.Value.ToString("yyyy-MM-dd") : null),
                        InvoiceCompanyId = SqlFunc.IsNull(invoice.BillingCompany, proInvoice.BillingCompany),
                        InvoiceHearder = SqlFunc.IsNull(invoice.BillingHeader, proInvoice.BillingHeader),
                        InvoiceDetails = SqlFunc.IsNull(invoice.InvoicingDetails, proInvoice.InvoiceDetails),
                                              RefundStatus = !SqlFunc.IsNullOrEmpty(invoice.RefundStatus) ? invoice.RefundStatus : 
                                       (proInvoice.Id != null ? (int?)EnumInvoiceRefundStatus.Normal : null),
                        
                        // 退票信息
                        RefundAmount = SqlFunc.IsNull(refundInvoice.TotalAmount, invoiceRefundAppl.RefundAmount),
                        RefundApplicationStatus = (EnumRefundApplicationStatus)invoiceRefundAppl.AuditStatus,
                        
                        // 到账与匹配信息
                        MatchingStatus = SqlFunc.IIF(invoiceAppl.InvoiceType == (int)EnumInvoiceType.ProformaTicket && SqlFunc.IsNullOrEmpty(matching.MatchingStatus),
                            (EnumInvoiceMatchingStatus)EnumInvoiceMatchingStatus.NotReceived,
                            SqlFunc.IsNull((EnumInvoiceMatchingStatus)invoice.MatchingStatus, (EnumInvoiceMatchingStatus)matching.MatchingStatus)  ),
                        ReceiptDate = collection.ArrivalDate,
                        ReceiptAmount = collection.ArrivalAmount,
                        ReceiptCompanyName = collection.CollectingCompanyName,
                        CollectionBank = collection.CollectingBankName,
                        PaymentMethod = collection.PaymentMethod == null ? null : (EnumPaymentMethod)collection.PaymentMethod,
                        
                        // 处理人信息
                        ProcessorId = invoiceAudit.ProcessorId,
                        ProcessDate = invoiceAudit.ProcessTime,
                        AuditorId = invoiceAudit.AuditorId,
                        AuditDate = invoiceAudit.AuditTime  ,
                        AuditFeedback = invoiceAudit.AuditFeedback,
                        ProcessorRemark = invoiceAudit.ProcessorRemark
                    })
                    .Mapper(t => {
                        t.InvoiceCompanyName = t.InvoiceCompanyId != null ? RedisCache.CollectingCompany.GetCollectingCompanyById(t.InvoiceCompanyId)?.CollectingCompanyName ?? "" : "";
                        t.ApplicantName = t.ApplicantId != null ? RedisCache.UserWithOrg.GetUserById(t.ApplicantId)?.UserWithOrgFullName ?? "" : ""; 
                        t.ProcessorName = t.ProcessorId != null ? RedisCache.UserWithOrg.GetUserById(t.ProcessorId)?.Name ?? "" : "";
                        t.AuditorName = t.AuditorId != null ? RedisCache.UserWithOrg.GetUserById(t.AuditorId)?.Name ?? "" : "";
                        t.IsBehalfPaymentCompanyName = RedisCache.PayingCompany.GetPayingCompanyByContractId(t.ContractId)?.PayingCompanyName ?? "";  //后面通过缓存再做处理
                        t.ApplicationInvoiceCompany = t.ApplicationInvoiceCompany != null ? RedisCache.CollectingCompany.GetCollectingCompanyById(t.ApplicationInvoiceCompany)?.CollectingCompanyName ?? "" : "";
                    })
                    .ToPageList( (int)pageNumber, (int)pageSize, ref total);


                // 设置响应数据
                response.Data = result;
                response.Total = total;
                return response;
            }
            catch (ApiException)
            {
                throw; // 直接重新抛出业务异常
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取发票列表时发生错误: {ex.Message}, {ex.StackTrace}");
                throw new ApiException("获取发票列表失败");
            }
        }
    }
}