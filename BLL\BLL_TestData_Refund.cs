using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using CRM2_API.Common.JWT;
using System;
using System.Linq;
using System.Threading.Tasks;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 测试数据 BLL 类 - 退票相关测试数据
    /// </summary>
    public partial class BLL_TestData
    {
        /// <summary>
        /// 创建退票测试数据 - 自动执行完整的退票流程
        /// </summary>
        /// <param name="invoiceId">发票ID，如果为空则会先创建一个测试发票</param>
        /// <returns>退票测试结果</returns>
        public CompleteTestDataResult CreateRefundTestData(string invoiceId = null)
        {
            try
            {
                var result = new CompleteTestDataResult();
                
                // 如果未提供发票ID，则创建测试发票
                if (string.IsNullOrEmpty(invoiceId))
                {
                    // 创建发票申请并审核通过
                    var invoiceAppResult = PrepareInvoiceApplication();
                    if (invoiceAppResult == null)
                    {
                        throw new ApiException("创建发票申请失败");
                    }
                    
                    var auditResult = AuditInvoiceApplication(invoiceAppResult.InvoiceApplicationId);
                    if (auditResult == null)
                    {
                        throw new ApiException("审核发票申请失败");
                    }
                    
                    // 查询已创建的发票ID
                    var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                        i.InvoiceApplicationId == auditResult.InvoiceApplicationId && 
                        i.Deleted == false);
                        
                    if (invoice == null)
                    {
                        throw new ApiException("未找到已创建的发票记录");
                    }
                    
                    invoiceId = invoice.Id;
                    
                    // 复制发票申请和合同相关信息
                    result.ContractId = auditResult.ContractId;
                    result.ContractName = auditResult.ContractName;
                    result.CustomerId = auditResult.CustomerId;
                    result.CustomerName = auditResult.CustomerName;
                    result.InvoiceApplicationId = auditResult.InvoiceApplicationId;
                    result.InvoiceId = invoiceId;
                    result.InvoiceAmount = invoice.InvoicedAmount;
                }
                else
                {
                    // 如果提供了发票ID，则获取该发票的详细信息
                    var invoice = DbOpe_crm_invoice.Instance.GetDataById(invoiceId);
                    if (invoice == null || invoice.Deleted == true)
                    {
                        throw new ApiException($"未找到ID为 {invoiceId} 的发票或该发票已被删除");
                    }
                    
                    // 获取合同信息
                    var contract = DbOpe_crm_contract.Instance.GetDataById(invoice.ContractId);
                    if (contract == null || contract.Deleted == true)
                    {
                        throw new ApiException($"未找到ID为 {invoice.ContractId} 的合同或该合同已被删除");
                    }
                    
                    // 设置结果信息
                    result.ContractId = contract.Id;
                    result.ContractName = contract.ContractName;
                    result.CustomerId = contract.CustomerId;
                    
                    // 获取客户信息
                    var customer = DbOpe_crm_customer.Instance.GetDataById(contract.CustomerId);
                    if (customer != null)
                    {
                        // result.CustomerName = customer.CustomerName;
                    }
                    
                    result.InvoiceId = invoice.Id;
                    result.InvoiceAmount = invoice.InvoicedAmount;
                    result.InvoiceApplicationId = invoice.InvoiceApplicationId;
                }
                
                // 确保发票ID有效
                if (string.IsNullOrEmpty(invoiceId))
                {
                    throw new ApiException("无效的发票ID");
                }
                
                // 1. 创建退票申请
                var refundRequest = new CreateInvoiceRefundRequest
                {
                    InvoiceId = invoiceId,
                    ContractId = result.ContractId,
                    RefundReason = "系统自动创建的测试退票申请",
                    RefundAmount = result.InvoiceAmount // 默认全额退票
                };
                
                // 调用BLL方法创建退票申请
                BLL_ContractInvoiceNew invoiceBLL = new BLL_ContractInvoiceNew();
                string refundApplicationId = invoiceBLL.SaveInvoiceRefund(refundRequest);
                
                if (string.IsNullOrEmpty(refundApplicationId))
                {
                    throw new ApiException("创建退票申请失败");
                }
                
                // 2. 使用测试发票PDF进行OCR识别
                byte[] pdfBytes = GetTestInvoicePdfBytes();
                if (pdfBytes == null || pdfBytes.Length == 0)
                {
                    throw new ApiException("获取测试发票PDF失败");
                }
                
                // 执行OCR识别
                var ocrResult = invoiceBLL.RecognizeAndCompareRefundAttachmentAsync(pdfBytes, refundApplicationId).Result;
                if (ocrResult == null)
                {
                    throw new ApiException("OCR识别失败");
                }
                
                // 3. 创建退票审核请求（经办人审核）
                var reviewRequest = new ReviewInvoiceRefundRequest
                {
                    Id = refundApplicationId,
                    ReviewResult = 1, // 1表示通过
                    ReviewComments = "系统自动审核通过",
                    EditedRefundInfo = new VM_InvoiceSystem.EditedRefundInfo
                    {
                        RefundInvoiceNumber = "TEST" + DateTime.Now.ToString("yyyyMMddHHmmss"),
                        RefundDate = DateTime.Now,
                        RefundAmount = result.InvoiceAmount
                    }
                };
                
                // 执行退票审核
                bool reviewResult = invoiceBLL.ReviewInvoiceRefund(reviewRequest);
                if (!reviewResult)
                {
                    throw new ApiException("退票审核失败");
                }
                
                // 4. 创建退票复核确认请求（财务审核）
                var confirmRequest = new ReviewInvoiceRefundConfirmationRequest
                {
                    RefundApplicationId = refundApplicationId,
                    ReviewResult = true, // true表示通过
                    ReviewComment = "系统自动确认退票审核通过"
                };
                
                // 执行退票复核确认
                bool confirmResult = invoiceBLL.ConfirmInvoiceRefundReview(confirmRequest);
                if (!confirmResult)
                {
                    throw new ApiException("退票复核确认失败");
                }
                
                // 5. 查询创建的红字发票
                var redInvoice = DbOpe_crm_invoice.Instance.GetData(i => 
                    i.RelatedInvoiceId == invoiceId && 
                    i.TransactionType == 2 && // 2表示退票
                    i.Deleted == false);
                    
                if (redInvoice != null)
                {
                    result.SuccessMessage = $"退票测试数据创建成功! 红字发票号: {redInvoice.InvoiceNumber}";
                }
                else
                {
                    result.SuccessMessage = "退票测试数据创建成功!";
                }
                
                // 补充结果信息
                result.RefundApplicationId = refundApplicationId;
                
                return result;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"创建退票测试数据失败: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                // 错误处理
                LogUtil.AddErrorLog($"创建退票测试数据失败: {ex.Message}", ex);
                throw new ApiException($"创建退票测试数据失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 创建退票申请测试数据（不进行OCR和审核）
        /// </summary>
        /// <param name="invoiceId">发票ID，必须提供</param>
        /// <returns>退票申请ID</returns>
        public string CreateRefundApplicationTestData(string invoiceId)
        {
            if (string.IsNullOrEmpty(invoiceId))
            {
                throw new ApiException("必须提供发票ID");
            }
            
            try
            {
                // 获取发票信息
                var invoice = DbOpe_crm_invoice.Instance.GetDataById(invoiceId);
                if (invoice == null || invoice.Deleted == true)
                {
                    throw new ApiException($"未找到ID为 {invoiceId} 的发票或该发票已被删除");
                }
                
                // 创建退票申请
                var refundRequest = new CreateInvoiceRefundRequest
                {
                    InvoiceId = invoiceId,
                    ContractId = invoice.ContractId,
                    RefundReason = "系统自动创建的测试退票申请",
                    RefundAmount = invoice.InvoicedAmount // 默认全额退票
                };
                
                // 调用BLL方法创建退票申请
                BLL_ContractInvoiceNew invoiceBLL = new BLL_ContractInvoiceNew();
                string refundApplicationId = invoiceBLL.SaveInvoiceRefund(refundRequest);
                
                if (string.IsNullOrEmpty(refundApplicationId))
                {
                    throw new ApiException("创建退票申请失败");
                }
                
                return refundApplicationId;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"创建退票申请测试数据失败: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"创建退票申请测试数据失败: {ex.Message}", ex);
                throw new ApiException($"创建退票申请测试数据失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 对指定的退票申请进行OCR识别
        /// </summary>
        /// <param name="refundApplicationId">退票申请ID</param>
        /// <returns>OCR识别结果</returns>
        public InvoiceOcrCompareResult RecognizeRefundTestData(string refundApplicationId)
        {
            if (string.IsNullOrEmpty(refundApplicationId))
            {
                throw new ApiException("必须提供退票申请ID");
            }
            
            try
            {
                // 获取退票申请信息
                var refundApplication = DbOpe_crm_invoice_refund_application.Instance.GetDataById(refundApplicationId);
                if (refundApplication == null || refundApplication.Deleted == true)
                {
                    throw new ApiException($"未找到ID为 {refundApplicationId} 的退票申请或该申请已被删除");
                }
                
                // 使用测试发票PDF进行OCR识别
                byte[] pdfBytes = GetTestInvoicePdfBytes();
                if (pdfBytes == null || pdfBytes.Length == 0)
                {
                    throw new ApiException("获取测试发票PDF失败");
                }
                
                // 执行OCR识别
                BLL_ContractInvoiceNew invoiceBLL = new BLL_ContractInvoiceNew();
                var ocrResult = invoiceBLL.RecognizeAndCompareRefundAttachmentAsync(pdfBytes, refundApplicationId).Result;
                if (ocrResult == null)
                {
                    throw new ApiException("OCR识别失败");
                }
                
                return ocrResult;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"退票OCR识别失败: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"退票OCR识别失败: {ex.Message}", ex);
                throw new ApiException($"退票OCR识别失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 审核退票申请（经办人审核）
        /// </summary>
        /// <param name="refundApplicationId">退票申请ID</param>
        /// <returns>审核结果</returns>
        public bool ReviewRefundTestData(string refundApplicationId)
        {
            if (string.IsNullOrEmpty(refundApplicationId))
            {
                throw new ApiException("必须提供退票申请ID");
            }
            
            try
            {
                // 获取退票申请信息
                var refundApplication = DbOpe_crm_invoice_refund_application.Instance.GetDataById(refundApplicationId);
                if (refundApplication == null || refundApplication.Deleted == true)
                {
                    throw new ApiException($"未找到ID为 {refundApplicationId} 的退票申请或该申请已被删除");
                }
                
                // 获取发票信息
                var invoice = DbOpe_crm_invoice.Instance.GetDataById(refundApplication.InvoiceId);
                if (invoice == null || invoice.Deleted == true)
                {
                    throw new ApiException($"未找到退票申请关联的发票信息");
                }
                
                // 创建退票审核请求
                var reviewRequest = new ReviewInvoiceRefundRequest
                {
                    Id = refundApplicationId,
                    ReviewResult = 1, // 1表示通过
                    ReviewComments = "系统自动审核通过",
                    EditedRefundInfo = new VM_InvoiceSystem.EditedRefundInfo
                    {
                        RefundInvoiceNumber = "TEST" + DateTime.Now.ToString("yyyyMMddHHmmss"),
                        RefundDate = DateTime.Now,
                        RefundAmount = refundApplication.RefundAmount
                    }
                };
                
                // 执行退票审核
                BLL_ContractInvoiceNew invoiceBLL = new BLL_ContractInvoiceNew();
                bool reviewResult = invoiceBLL.ReviewInvoiceRefund(reviewRequest);
                if (!reviewResult)
                {
                    throw new ApiException("退票审核失败");
                }
                
                return true;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"退票审核失败: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"退票审核失败: {ex.Message}", ex);
                throw new ApiException($"退票审核失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 确认退票申请（财务审核确认）
        /// </summary>
        /// <param name="refundApplicationId">退票申请ID</param>
        /// <returns>确认结果</returns>
        public bool ConfirmRefundTestData(string refundApplicationId)
        {
            if (string.IsNullOrEmpty(refundApplicationId))
            {
                throw new ApiException("必须提供退票申请ID");
            }
            
            try
            {
                // 获取退票申请信息
                var refundApplication = DbOpe_crm_invoice_refund_application.Instance.GetDataById(refundApplicationId);
                if (refundApplication == null || refundApplication.Deleted == true)
                {
                    throw new ApiException($"未找到ID为 {refundApplicationId} 的退票申请或该申请已被删除");
                }
                
                // 创建退票复核确认请求
                var confirmRequest = new ReviewInvoiceRefundConfirmationRequest
                {
                    RefundApplicationId = refundApplicationId,
                    ReviewResult = true, // true表示通过
                    ReviewComment = "系统自动确认退票审核通过"
                };
                
                // 执行退票复核确认
                BLL_ContractInvoiceNew invoiceBLL = new BLL_ContractInvoiceNew();
                bool confirmResult = invoiceBLL.ConfirmInvoiceRefundReview(confirmRequest);
                if (!confirmResult)
                {
                    throw new ApiException("退票复核确认失败");
                }
                
                return true;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"退票复核确认失败: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"退票复核确认失败: {ex.Message}", ex);
                throw new ApiException($"退票复核确认失败: {ex.Message}");
            }
        }
    }
} 