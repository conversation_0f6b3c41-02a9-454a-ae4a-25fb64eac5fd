﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_contract_receiptregister")]
    public class Db_v_contract_receiptregister
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string Id {get;set;}

           /// <summary>
           /// Desc:客户编号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractNum { get; set; }

           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Remark { get; set; }

           /// <summary>
           /// Desc:保护截止日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ProtectionDeadline { get; set; }

           /// <summary>
           /// Desc:合同号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractNo {get;set;}

           /// <summary>
           /// Desc:合同名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractName {get;set;}

           /// <summary>
           /// Desc:合同金额
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? ContractAmount {get;set;}

           /// <summary>
           /// Desc:外币合同金额
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? FCContractAmount { get; set; }

           /// <summary>
           /// Desc:币种
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? Currency { get; set; }

           /// <summary>
           /// Desc:状态(到账登记审核状态)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? State {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PaymentCompanyName {get;set;}

           /// <summary>
           /// Desc:到账情况(是否到账)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? IsReceipt {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PaymentType {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CollectingCompanyName {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PaymentMethod {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? ArrivalAmount {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ArrivalDate {get;set;}

           /// <summary>
           /// Desc:是否私密
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? IsSecret {get;set;}

           /// <summary>
           /// Desc:业绩扣除
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? DeductPerformance {get;set;}

           /// <summary>
           /// Desc:销售业绩
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? SalesPerformance {get;set;}

           /// <summary>
           /// Desc:有效业绩（扣除后业绩）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? EffectivePerformance {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string IssuerName {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ReviewerName {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:审核时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ReviewerDate {get;set;}

           /// <summary>
           /// Desc:出单人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Issuer {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgDivisionName { get; set; }

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgBrigadeName { get; set; }

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgRegimentName { get; set; }

           /// <summary>
           /// Desc:合同表Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractId { get; set; }

           /// <summary>
           /// Desc:匹配待登记表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CollectionInfoAutoMatchingId { get; set; }

           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int Type { get; set; }

    }
}
