using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.ServiceOpening;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.Enum;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;
using CRM2_API.Common;
using CRM2_API.Common.Cache;
using CRM2_API.BLL.GtisOpe;
using System;
using System.Collections.Generic;
using System.Linq;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using CRM2_API.DAL.DbCommon;

namespace CRM2_API.BLL.ServiceOpening
{
    /// <summary>
    /// 服务开通 - GTIS参数格式化器
    /// </summary>
    public partial class BLL_ServiceOpening
    {
        #region GTIS新建参数格式化
        
        /// <summary>
        /// 格式化GTIS新建用户参数
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="witsUsers">Wits用户列表（wits_user是gtis_user的扩展，可以直接使用）</param>
        /// <param name="globalSearchResult">环球搜开通结果（当前开通流程中的结果）</param>
        /// <returns>GTIS新建参数</returns>
        private BM_AddGtisUser FormatGtisUserForAdd(ServiceOpeningParams openingParams,
            List<Db_crm_contract_serviceinfo_wits_user> witsUsers,
            GlobalSearchResult globalSearchResult = null)
        {
            var contract = openingParams.Contract;
            
            // 验证必要参数
            var gtisParams = GetGtisParams(contract, openingParams);
            
            var addGtisUser = new BM_AddGtisUser
            {
                // 基础服务信息
                SvCode = contract.ContractNum,
                CustomerType = gtisParams.CustomerType,
                CustomerType2 = gtisParams.CustomerType2,
                SharingTimes = gtisParams.ShareUsageNum,
                StartServerDate = gtisParams.ServiceCycleStart,
                EndServerDate = gtisParams.ServiceCycleEnd,
                MaxCountry = gtisParams.AuthorizationNum,
                MaxChildrens = gtisParams.SubAccountsNum,
                
                // 基础信息
                Company = openingParams.IsTestMode?"测试企业":  GetFirstPartyName(contract.FirstParty) ?? "",
                Addr = openingParams.IsTestMode?"测试企业地址":  contract.Address ?? "",
                UserName = openingParams.IsTestMode?"测试企业联系人":  contract.Contacts ?? "",
                Tel = openingParams.IsTestMode?"测试企业电话": GetContactPhone(contract),
                Email = openingParams.IsTestMode?"<EMAIL>": contract.Email ?? "",
                
                // 位置信息
                crm_country = openingParams.IsTestMode?"中国": LocalCache.LC_Address.CountryCache.FirstOrDefault(c => c.Id == contract.SalesCountry)?.Name??"", // 默认值，可能需要从合同中获取
                crm_city = openingParams.IsTestMode?"天津": LocalCache.LC_Address.CityCache.FirstOrDefault(c => c.Id == contract.City)?.Name??"", // 默认值，可能需要从合同中获取
                
                // 用户账号信息
                AllUserSharingTimes = FormatGtisUserList(witsUsers, openingParams),
                
                // 权限配置
                Sids = openingParams.GtisSids ?? new int[0],
                SidsRemove = openingParams.GtisSidsRemove ?? new int[0], // 从数据预处理中获取需要移除的SID
                
                // 环球搜集成（如果有）
                HqsCode = GetGlobalSearchCode(openingParams, globalSearchResult),
                
                // 其他默认值
                IsHistory = GetIsHistoryValue(openingParams), // 根据GTIS服务的IsGtisOldCustomer字段判断
                ForbidSearchExport = GetForbidSearchExportValue(openingParams), // 根据GTIS服务的ForbidSearchExport字段判断
                WordRptMaxTimes = GetWordRptMaxTimesValue(openingParams), // 根据GTIS服务的WordRptMaxTimes字段判断
                WordRptPermissions = GetWordRptPermissionsValue(openingParams), // 根据GTIS服务的WordRptPermissions字段判断
                ContractNum = contract.ContractNum,
                PayDate = openingParams.IsTestMode?DateTime.Now.ToString("yyyyMM"):  GetPayDateValue(openingParams), // 根据合同的ArrivalDate字段获取付款年月
                OtherInfo = openingParams.IsTestMode?"测试创建":  ""
            };
            
            return addGtisUser;
        }
        
        /// <summary>
        /// 格式化GTIS用户列表
        /// </summary>
        /// <param name="witsUsers">Wits用户列表（wits_user是gtis_user的扩展，可以直接使用）</param>
        /// <param name="openingParams">开通参数</param>
        /// <returns>格式化后的用户信息</returns>
        private List<BM_AddGtisUser.Crm2UserAndSharing> FormatGtisUserList(List<Db_crm_contract_serviceinfo_wits_user> witsUsers, ServiceOpeningParams openingParams)
        {
            var userList = new List<BM_AddGtisUser.Crm2UserAndSharing>();

            // 获取环球搜码列表（如果有的话）
            var globalSearchCodes = GetGlobalSearchCodesForUsers(openingParams);

            for (int i = 0; i < witsUsers.Count; i++)
            {
                var user = witsUsers[i];
                var userAndSharing = new BM_AddGtisUser.Crm2UserAndSharing
                {
                    CrmId = user.Id,
                    SharingTimes = user.SharePeopleNum ?? 10,
                    IsSupperSubUser = user.AllCountrySubUser ?? false,
                    Apps = GetUserAppsFromPermissions(user), // 根据每个用户的权限获取应用列表
                    AppOptions = GetCurrentUserAppOptions(user, openingParams), // 根据每个用户的权限获取应用选项

                    // 为每个用户分配环球搜码（如果有环球搜服务）
                    HqsCode = GetHqsCodeForUser(user, i, globalSearchCodes)
                };

                userList.Add(userAndSharing);
            }

            return userList;
        }

        /// <summary>
        /// 获取用户的环球搜码列表（基于GetGlobalSearchCode方法改造）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>环球搜码列表</returns>
        private List<string> GetGlobalSearchCodesForUsers(ServiceOpeningParams openingParams)
        {
            var codes = new List<string>();

            try
            {
                LogUtil.AddLog("开始获取环球搜码列表");

                // 2. 从预处理结果中获取现有环球搜码（续约或变更场景）
                if (openingParams.GlobalSearchPreprocessResult != null)
                {
                    if (!string.IsNullOrEmpty(openingParams.GlobalSearchPreprocessResult.ExistingPrimaryCode))
                    {
                        codes.Add(openingParams.GlobalSearchPreprocessResult.ExistingPrimaryCode);
                        LogUtil.AddLog($"从环球搜预处理结果获取到现有环球搜码: {openingParams.GlobalSearchPreprocessResult.ExistingPrimaryCode}");
                    }
                }

                // 3. 尝试从特殊配置中获取现有的环球搜码
                if (codes.Count == 0 && openingParams.GlobalSearchSpecialConfig?.ServiceInfo != null)
                {
                    var existingCode = GetExistingGlobalSearchCode(openingParams);
                    if (!string.IsNullOrEmpty(existingCode))
                    {
                        codes.Add(existingCode);
                        LogUtil.AddLog($"从环球搜特殊配置获取到现有环球搜码: {existingCode}");
                    }
                }

                // 4. 使用历史环球搜码（从特殊配置中获取）
                if (codes.Count == 0 && openingParams.GlobalSearchSpecialConfig?.HistoryGlobalSearchCodes?.Count > 0)
                {
                    codes.AddRange(openingParams.GlobalSearchSpecialConfig.HistoryGlobalSearchCodes.Keys);
                    LogUtil.AddLog($"使用历史环球搜码，数量: {codes.Count}");
                }

                // 5. 判断是否需要环球搜服务（仅在没有现有环球搜码时判断）
                if (codes.Count == 0 && openingParams.MainService.HasGlobalSearchApp != true)
                {
                    // 如果没有现有环球搜码，且当前也不需要环球搜服务，则返回空列表
                    LogUtil.AddLog("当前服务组合不包含环球搜服务，且没有现有环球搜码，GTIS用户不绑定环球搜码");
                }

                if (codes.Count == 0)
                {
                    LogUtil.AddLog("当前服务组合包含环球搜服务，但未获取到环球搜码");
                }

                return codes;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取环球搜码列表异常: {ex.Message}");
                return codes; // 返回空列表
            }
        }

        /// <summary>
        /// 为指定用户获取环球搜码
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="userIndex">用户索引（0为主账号，1+为子账号）</param>
        /// <param name="globalSearchCodes">可用的环球搜码列表</param>
        /// <returns>分配给该用户的环球搜码</returns>
        private string GetHqsCodeForUser(Db_crm_contract_serviceinfo_wits_user user, int userIndex, List<string> globalSearchCodes)
        {
            if (globalSearchCodes == null || globalSearchCodes.Count == 0)
            {
                return ""; // 没有环球搜服务，不分配环球搜码
            }

            // 确保有足够的环球搜码分配
            if (userIndex >= globalSearchCodes.Count)
            {
                LogUtil.AddLog($"警告：用户索引 {userIndex} 超出环球搜码数量 {globalSearchCodes.Count}，无法分配环球搜码");
                return "";
            }

            var assignedCode = globalSearchCodes[userIndex];
            LogUtil.AddLog($"为用户 {user.Id}（索引{userIndex}）分配环球搜码: {assignedCode}");

            return assignedCode;
        }
        
        #endregion
        
        #region GTIS续约/变更参数格式化
        
        /// <summary>
        /// 格式化GTIS续约/变更参数
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="userClassification">用户分类结果</param>
        /// <returns>GTIS续约参数</returns>
        private BM_GtisOpe_RenewalContact FormatGtisUserForRenewal(ServiceOpeningParams openingParams, 
            UserClassificationResult userClassification)
        {
            var contract = openingParams.Contract;
            var openingType = DetermineOpeningType(openingParams);
            var isChange = openingType == ServiceOpeningType.Change;
            
            // 验证必要参数
            var gtisParams = GetGtisParams(contract,openingParams);
            
            var renewalContact = new BM_GtisOpe_RenewalContact
            {
                // 基础服务信息
                SvCode = contract.ContractNum,
                SvCodeOld = GetOldContractNum(openingParams),
                CustomerType = gtisParams.CustomerType,
                CustomerType2 = gtisParams.CustomerType2,
                IsChange = isChange,
                ChangeId = gtisParams.serviceId,
                
                // 服务配置
                SharingTimes = gtisParams.ShareUsageNum,
                StartServerDate = gtisParams.ServiceCycleStart,
                EndServerDate = gtisParams.ServiceCycleEnd,
                MaxCountry = gtisParams.AuthorizationNum,
                MaxChildrens = gtisParams.SubAccountsNum,
                
                // 基础信息
                Company = openingParams.IsTestMode ? "测试企业" : GetFirstPartyName(contract.FirstParty) ?? "",
                Addr = openingParams.IsTestMode ? "测试企业地址" : contract.Address ?? "",
                UserName = openingParams.IsTestMode ? "测试企业联系人" : contract.Contacts ?? "",
                Tel = openingParams.IsTestMode ? "测试企业电话" : GetContactPhone(contract),
                Email = openingParams.IsTestMode ? "<EMAIL>" : contract.Email ?? "",

                // 位置信息
                crm_country = openingParams.IsTestMode ? "中国" : LocalCache.LC_Address.CountryCache.FirstOrDefault(c => c.Id == contract.SalesCountry)?.Name ?? "", // 从缓存中获取国家名称
                crm_city = openingParams.IsTestMode ? "天津" : LocalCache.LC_Address.CityCache.FirstOrDefault(c => c.Id == contract.City)?.Name ?? "", // 从缓存中获取城市名称
                
                // 权限配置
                Sids = openingParams.GtisSids ?? new int[0],
                SidsRemove = openingParams.GtisSidsRemove ?? new int[0], // 从数据预处理中获取需要移除的权限ID
                
                // 应用配置
                apps = openingParams.Apps.ToArray(),
                appOptions = GetGtisAppOptions(openingParams),
                
                // 用户变更操作
                ChangeAddUser = FormatAddUsers(userClassification.AddUsers),
                ChangeDisabledUser = FormatDisabledUsers(userClassification.DelUsers),
                ModifyUserSharingTimes = FormatModifyUsers(userClassification.UpdateUsers),

                // 用户权限变更（apps权限变更）
                UserPhoneCrmChange = userClassification.UserPermissionChanges ?? new List<BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeUserPermission>(),

                // 其他信息
                OtherInfo = openingParams.IsTestMode ? "测试续约" : "",
                PayDate = openingParams.IsTestMode ? DateTime.Now.ToString("yyyyMM") : GetPayDateValue(openingParams), // 根据合同的ArrivalDate字段获取付款年月
                IsHistory = GetIsHistoryValue(openingParams), // 根据GTIS服务的IsGtisOldCustomer字段判断
                ForbidSearchExport = GetForbidSearchExportValue(openingParams), // 根据GTIS服务的ForbidSearchExport字段判断
                WordRptMaxTimes = GetWordRptMaxTimesValue(openingParams), // 根据GTIS服务的WordRptMaxTimes字段判断
                WordRptPermissions = GetWordRptPermissionsValue(openingParams), // 根据GTIS服务的WordRptPermissions字段判断
                IsWordRptMaxTimesNow = GetIsWordRptMaxTimesNowValue(openingParams), // 根据业务逻辑判断是否立即生效
                IsDelSubCountry = GetIsDelSubCountryValue(openingParams) // 根据GTIS服务的DelAuthorizationNum字段判断
            };

            LogUtil.AddLog($"GTIS续约参数格式化完成，UserPhoneCrmChange数量: {renewalContact.UserPhoneCrmChange.Count}");

            return renewalContact;
        }
        
        /// <summary>
        /// 格式化新增用户
        /// </summary>
        /// <param name="addUsers">新增用户列表</param>
        /// <returns>新增用户参数</returns>
        private List<BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeAddUser> FormatAddUsers(
            List<Db_crm_contract_serviceinfo_wits_user> addUsers)
        {
            var changeAddUsers = new List<BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeAddUser>();

            foreach (var witsUser in addUsers)
            {
                var changeAddUser = new BM_GtisOpe_RenewalContact.BM_GtisOpe_ChangeAddUser
                {
                    CrmId = witsUser.Id,
                    SharingTimes = witsUser.SharePeopleNum ?? 1,
                    IsSupperSubUser = witsUser.AllCountrySubUser ?? false,
                    apps = GetCurrentUserApps(witsUser)
                    // HqsCode 不用填写
                };

                changeAddUsers.Add(changeAddUser);
            }

            return changeAddUsers;
        }


        
        /// <summary>
        /// 格式化停用用户
        /// </summary>
        /// <param name="delUsers">停用用户列表</param>
        /// <returns>停用用户参数</returns>
        private List<BM_GtisOpe_RenewalContact.BM_GtisOpe_DisabledUser> FormatDisabledUsers(
            List<Db_crm_contract_serviceinfo_gtis_user> delUsers)
        {
            var disabledUsers = new List<BM_GtisOpe_RenewalContact.BM_GtisOpe_DisabledUser>();
            
            foreach (var user in delUsers)
            {
                if (!string.IsNullOrEmpty(user.UserId))
                {
                    var disabledUser = new BM_GtisOpe_RenewalContact.BM_GtisOpe_DisabledUser
                    {
                        SysUserID = user.UserId
                    };
                    
                    disabledUsers.Add(disabledUser);
                }
            }
            
            return disabledUsers;
        }
        
        /// <summary>
        /// 格式化修改用户共享次数
        /// </summary>
        /// <param name="updateUsers">更新用户列表</param>
        /// <returns>修改用户参数</returns>
        private List<BM_GtisOpe_RenewalContact.BM_GtisOpe_ModifySharingTimes> FormatModifyUsers(
            List<Db_crm_contract_serviceinfo_gtis_user> updateUsers)
        {
            var modifyUsers = new List<BM_GtisOpe_RenewalContact.BM_GtisOpe_ModifySharingTimes>();
            
            foreach (var user in updateUsers)
            {
                if (!string.IsNullOrEmpty(user.UserId))
                {
                    var modifyUser = new BM_GtisOpe_RenewalContact.BM_GtisOpe_ModifySharingTimes
                    {
                        SysUserID = user.UserId,
                        SharingTimes = user.SharePeopleNum ?? 1
                    };
                    
                    modifyUsers.Add(modifyUser);
                }
            }
            
            return modifyUsers;
        }



        #endregion
        
        #region 辅助方法
        public class GtisParams
        {
            public int CustomerType { get; set; }
            public string CustomerType2 { get; set; }
            public int ShareUsageNum { get; set; }
            public DateTime ServiceCycleStart { get; set; }
            public DateTime ServiceCycleEnd { get; set; }
            public int AuthorizationNum { get; set; }
            public int SubAccountsNum { get; set; }
            public string serviceId{get;set;}
        }

        /// <summary>
        /// 获取GTIS参数
        /// </summary>
        /// <param name="contract">合同信息</param>
        /// <param name="openingParams">开通参数（用于判断是否只开通其他服务）</param>
        private GtisParams GetGtisParams(Db_crm_contract contract, ServiceOpeningParams openingParams)
        {
            GtisParams p = new GtisParams();
            // 如果GTIS参数为空，检查是否是只开通其他服务的情况
            if (openingParams == null)
            {
                throw new ArgumentException("GTIS服务参数不能为空");
            }
            else{
                if(openingParams.GtisSpecialConfig?.ServiceInfo!=null)
                {
                    var relation = GetGtisCustomerType(openingParams.GtisSpecialConfig.ServiceInfo.ProductId);
                    var gtisParams = openingParams.GtisSpecialConfig.ServiceInfo;
                    p.CustomerType = relation.Item1;
                    p.CustomerType2 = relation.Item2;
                    p.ShareUsageNum = gtisParams.ShareUsageNum == null?
                    throw new ArgumentException("共享使用总数不能为空"):gtisParams.ShareUsageNum.Value;
                    p.ServiceCycleStart = gtisParams.ServiceCycleStart
                    == null?throw new ArgumentException("服务周期开始时间不能为空"):gtisParams.ServiceCycleStart.Value;
                    p.ServiceCycleEnd = gtisParams.ServiceCycleEnd
                    == null?throw new ArgumentException("服务周期结束时间不能为空"):gtisParams.ServiceCycleEnd.Value;
                    p.AuthorizationNum = gtisParams.AuthorizationNum
                    == null?throw new ArgumentException("授权国家次数不能为空"):gtisParams.AuthorizationNum.Value;
                    p.SubAccountsNum = gtisParams.SubAccountsNum
                    == null?throw new ArgumentException("子账号数量不能为空"):gtisParams.SubAccountsNum.Value;  
                    p.serviceId = openingParams.MainService.Id;
                }
                else if (openingParams.MainService!=null)
                {
                    //处理没有gtis的SaleWits的开通
                    if (
                        openingParams.MainService.HasGtisApp == false 
                        && openingParams.MainService.IsSalesWitsAudit
                    )
                    {
                        var cService = openingParams.SalesWitsBasicConfig;
                        p.ShareUsageNum = openingParams.MainService.ShareUsageNum
                        == null?throw new ArgumentException("共享使用总数不能为空"):openingParams.MainService.ShareUsageNum.Value;
                        p.ServiceCycleStart = cService.ServiceCycleStart
                        == null?throw new ArgumentException("服务周期开始时间不能为空"):cService.ServiceCycleStart.Value;
                        p.ServiceCycleEnd = cService.ServiceCycleEnd
                        == null?throw new ArgumentException("服务周期结束时间不能为空"):cService.ServiceCycleEnd.Value;
                        p.AuthorizationNum = 1;
                        p.SubAccountsNum = openingParams.Users.Count()<=0
                        ?throw new ArgumentException("子账号数量不能为空"):openingParams.Users.Count() - 1 ;  
                        p.CustomerType = 4; //todo  接口目前没有
                                         p.serviceId = openingParams.MainService.Id;
                    }
                    //处理没有gtis/SaleWits的环球搜的开通
                    else if(
                        openingParams.MainService.HasGtisApp == false 
                        && openingParams.MainService.HasSalesWitsApp == false
                        && openingParams.MainService.IsGlobalSearchAudit
                    )
                    {
                        var gService = openingParams.GlobalSearchBasicConfig;
                        p.ShareUsageNum = openingParams.MainService.ShareUsageNum
                        == null?throw new ArgumentException("共享使用总数不能为空"):openingParams.MainService.ShareUsageNum.Value;
                        p.ServiceCycleStart = gService.ServiceCycleStart
                        == null?throw new ArgumentException("服务周期开始时间不能为空"):gService.ServiceCycleStart.Value;
                        p.ServiceCycleEnd = gService.ServiceCycleEnd
                        == null?throw new ArgumentException("服务周期结束时间不能为空"):gService.ServiceCycleEnd.Value;
                        p.AuthorizationNum = 1;
                        p.SubAccountsNum = openingParams.Users.Count()<=0
                        ?throw new ArgumentException("子账号数量不能为空"):openingParams.Users.Count() - 1 ;  
                        p.CustomerType = 4; //todo  接口目前没有
                                         p.serviceId = openingParams.MainService.Id;
                    }
                    
                    else{
                        throw new ArgumentException("GTIS服务参数不能为空");
                    }
                }
                else{
                    throw new ArgumentException("GTIS服务参数不能为空");
                }

            }

            if (contract == null)
                throw new ArgumentException("合同信息不能为空");

            if (string.IsNullOrEmpty(contract.ContractNum))
                throw new ArgumentException("客户编码不能为空（未到账不能开通服务）");
                return p;
        } 
        /// <summary>
        /// 获取GTIS客户类型
        /// </summary>
        /// <param name="productId">产品ID</param>
        /// <returns>客户类型</returns>
        private (int,string) GetGtisCustomerType(string productId)
        {
            var gtisRelation = DbOpe_crm_product_gtis_relation.Instance.GetData(d => d.ProductId == productId);
            if (gtisRelation == null)
                throw new ArgumentException("该服务产品与GTIS系统连接未配置");
            
            return (gtisRelation.GTIS_ProductCustomerType,gtisRelation.Remark);
        }
        
        /// <summary>
        /// 获取联系电话（优先级处理）
        /// </summary>
        /// <param name="contract">合同信息</param>
        /// <returns>联系电话</returns>
        private string GetContactPhone(Db_crm_contract contract)
        {
            return !string.IsNullOrEmpty(contract.ContactWay) ? contract.ContactWay : contract.Telephone ?? "";
        }
        
        /// <summary>
        /// 处理销售国家与常驻国家优先级
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>国家和城市信息</returns>
        private (string country, string city) ProcessCountryPriority(ServiceOpeningParams openingParams)
        {
            var contract = openingParams.Contract;
            var gtisCountries = openingParams.GtisCountries;
            var gtisCities = openingParams.GtisCities;
            
            string countryName = "";
            string cityName = "";
            
            // 优先使用销售国家
            if (contract.SalesCountry.HasValue && contract.SalesCountry.Value > 0)
            {
                var countryInfo = LocalCache.LC_Address.CountryAndAreaCache.Find(c => c.Id == contract.SalesCountry.Value);
                if (countryInfo != null)
                {
                    countryName = countryInfo.Name;
                    
                    // 特殊地区名称处理
                    if (countryName == "香港" || countryName == "澳门")
                        countryName = "中国" + countryName;
                    else if (countryName == "台湾省")
                        countryName = "中国台湾";
                }
            }
            else
            {
                // 使用常驻国家和城市（取第一个）
                if (gtisCountries == null || gtisCountries.Count == 0)
                {
                    throw new ArgumentException("GTIS国家不可为空");
                }
                
                var firstCountryId = gtisCountries.First();
                var countryInfo = LocalCache.LC_Address.CountryAndAreaCache.Find(c => c.Id == firstCountryId);
                if (countryInfo != null)
                {
                    countryName = countryInfo.Name;
                }
                
                // 获取城市信息
                if (gtisCities != null && gtisCities.Count > 0)
                {
                    var firstCityId = gtisCities.First();
                    if (firstCityId > 0)
                    {
                        var cityInfo = LocalCache.LC_Address.CityCache.Find(c => c.Id == firstCityId);
                        if (cityInfo != null)
                        {
                            cityName = cityInfo.Name;
                        }
                    }
                }
            }
            
            return (countryName, cityName);
        }
        
        /// <summary>
        /// 获取并合成G5备注信息
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>合成后的备注信息</returns>
        private string GetCombinedGtisRemarks(ServiceOpeningParams openingParams)
        {
            var gtisParams = openingParams.GtisSpecialConfig?.ServiceInfo;
            var contract = openingParams.Contract;
            
            var g5Remark = "";
            
            try
            {
                // 根据处理类型获取G5备注
                if (gtisParams.ProcessingType == (int)EnumProcessingType.Change)
                {
                    g5Remark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(contract.ContractNum).Result;
                }
                else if (contract.ContractType == (int)EnumContractType.ReNew && !string.IsNullOrEmpty(gtisParams.OldContractNum))
                {
                    g5Remark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(gtisParams.OldContractNum).Result;
                }
            }
            catch (Exception ex)
            {
                // 获取G5备注失败不应该中断流程，记录警告日志
                LogUtil.AddLog($"获取G5备注失败，将使用空备注，合同号: {gtisParams.OldContractNum}, 错误: {ex.Message}");
            }
            
            // 合成备注信息
            return string.IsNullOrEmpty(g5Remark) ? 
                gtisParams.ReviewerRemark : 
                (g5Remark + ";" + gtisParams.ReviewerRemark);
        }
        
        /// <summary>
        /// 处理合同金额币种转换
        /// </summary>
        /// <param name="contract">合同信息</param>
        /// <returns>合同金额字符串</returns>
        private string ProcessContractAmount(Db_crm_contract contract)
        {
            if (contract.ContractAmount.HasValue)
            {
                return contract.ContractAmount.Value.ToString();
            }
            else if (contract.FCContractAmount.HasValue)
            {
                return contract.FCContractAmount.Value.ToString();
            }
            
            return "0";
        }
        
        /// <summary>
        /// 格式化到账日期
        /// </summary>
        /// <param name="contract">合同信息</param>
        /// <returns>格式化后的到账日期（yyyyMM格式）</returns>
        private string FormatArrivalDate(Db_crm_contract contract)
        {
            try
            {
                // 参考DbOpe_crm_product_gtis_relation.cs中的实现
                // 使用GetContractAndReceiptByContractId方法获取合同和到账信息
                var info = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(contract.Id);
                if (info != null && !string.IsNullOrEmpty(info.ArrivalDate))
                {
                    return DateTime.Parse(info.ArrivalDate).ToString("yyyyMM");
                }

                // 如果没有到账记录，使用合同签约日期
                if (contract.SigningDate != null)
                {
                    return contract.SigningDate.Value.ToString("yyyyMM");
                }

                // 最后使用当前时间作为默认值
                return DateTime.Now.ToString("yyyyMM");
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"格式化到账日期异常: {ex.Message}，使用当前时间作为默认值");
                return DateTime.Now.ToString("yyyyMM");
            }
        }
        
        /// <summary>
        /// 处理地址信息拼接
        /// </summary>
        /// <param name="contract">合同信息</param>
        /// <returns>拼接后的地址信息</returns>
        private string FormatAddressInfo(Db_crm_contract contract)
        {
            try
            {
                var addressParts = new List<string>();

                // 参考DbOpe_crm_product_gtis_relation.cs中的逻辑
                // 根据合同表的Country、Province、City字段获取名称
                if (contract.Country != null)
                {
                    var country = DbOpe_sys_country.Instance.GetDataById(contract.Country.ToString());
                    if (country != null)
                    {
                        addressParts.Add(country.Name);
                    }
                }

                if (contract.Province != null)
                {
                    var province = DbOpe_sys_province.Instance.GetDataById(contract.Province.ToString());
                    if (province != null)
                    {
                        addressParts.Add(province.Name);
                    }
                }

                if (contract.City != null)
                {
                    var city = DbOpe_sys_city.Instance.GetDataById(contract.City.ToString());
                    if (city != null)
                    {
                        addressParts.Add(city.Name);
                    }
                }

                if (!string.IsNullOrEmpty(contract.Address))
                {
                    addressParts.Add(contract.Address);
                }

                return string.Join("/", addressParts.Where(x => !string.IsNullOrEmpty(x)));
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"格式化地址信息异常: {ex.Message}，使用合同地址字段");
                return contract.Address ?? "";
            }
        }
        
        /// <summary>
        /// 计算共享使用总数
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="gtisUsers">GTIS用户列表</param>
        /// <returns>共享使用总数</returns>
        private int CalculateSharingTimes(ServiceOpeningParams openingParams, List<Db_crm_contract_serviceinfo_gtis_user> gtisUsers)
        {
            var gtisParams = openingParams.GtisSpecialConfig?.ServiceInfo;
            
            // 计算所有用户的共享人数总和
            var totalUserSharingTimes = gtisUsers.Sum(u => u.SharePeopleNum ?? 0);
            
            if (gtisParams.ShareUsageNum == null || gtisParams.ShareUsageNum.Value == 0)
            {
                // 共享使用总数没写，自动设置为所有用户数+10
                return totalUserSharingTimes + 10;
            }
            else
            {
                // 使用设置的共享使用总数
                return gtisParams.ShareUsageNum.Value;
            }
        }
        
        
        /// <summary>
        /// 获取用户的应用权限列表（从权限字段构建）
        /// </summary>
        /// <param name="witsUser">用户信息</param>
        /// <returns>apps数组</returns>
        private string[] GetUserAppsFromPermissions(Db_crm_contract_serviceinfo_wits_user witsUser)
        {
            var apps = new List<string>();

            if (witsUser.GtisPermission == true)
                apps.Add("Gtis6");

            if (witsUser.GlobalSearchPermission == true)
                apps.Add("HQS");

            if (witsUser.SalesWitsPermission == true)
                apps.Add("CRM");

            if (witsUser.CollegePermission == true)
                apps.Add("College");

            return apps.ToArray();
        }

        /// <summary>
        /// 获取当前用户的应用选项
        /// </summary>
        /// <param name="witsUser">用户信息</param>
        /// <param name="openingParams">开通参数</param>
        /// <returns>当前用户的应用选项</returns>
        private List<AppOptionModel> GetCurrentUserAppOptions(Db_crm_contract_serviceinfo_wits_user witsUser, ServiceOpeningParams openingParams)
        {
            var appOptions = new List<AppOptionModel>();

            try
            {
                // GTIS应用选项 - 基于当前用户是否具有GTIS应用权限
                if (witsUser.GtisPermission == true && openingParams.MainService.HasGtisApp == true)
                {
                    appOptions.Add(new AppOptionModel
                    {
                        AppName = "Gtis6",
                        MaxAccount = openingParams.GtisBasicConfig.MaxAccountsNum ?? 0,
                        StartTime = openingParams.GtisBasicConfig.ServiceCycleStart,
                        EndTime = openingParams.GtisBasicConfig.ServiceCycleEnd
                    });
                }

                // 环球搜应用选项 - 基于当前用户是否具有环球搜应用权限
                if (witsUser.GlobalSearchPermission == true && openingParams.MainService.HasGlobalSearchApp == true)
                {
                    appOptions.Add(new AppOptionModel
                    {
                        AppName = "HQS",
                        MaxAccount = openingParams.GlobalSearchBasicConfig.MaxAccountsNum ?? 0,
                        StartTime = openingParams.GlobalSearchBasicConfig.ServiceCycleStart,
                        EndTime = openingParams.GlobalSearchBasicConfig.ServiceCycleEnd
                    });
                }

                // SalesWits应用选项 - 基于当前用户是否具有SalesWits应用权限
                if (witsUser.SalesWitsPermission == true && openingParams.MainService.HasSalesWitsApp == true)
                {
                    appOptions.Add(new AppOptionModel
                    {
                        AppName = "CRM",
                        MaxAccount = openingParams.SalesWitsBasicConfig.MaxAccountsNum ?? 0,
                        StartTime = openingParams.SalesWitsBasicConfig.ServiceCycleStart,
                        EndTime = openingParams.SalesWitsBasicConfig.ServiceCycleEnd
                    });
                }

                // 慧思学院应用选项 - 基于当前用户是否具有慧思学院应用权限
                if (witsUser.CollegePermission == true && openingParams.MainService.HasCollegeApp == true)
                {
                    appOptions.Add(new AppOptionModel
                    {
                        AppName = "College",
                        MaxAccount = openingParams.CollegeBasicConfig.MaxAccountsNum ?? 0,
                        StartTime = openingParams.CollegeBasicConfig.ServiceCycleStart,
                        EndTime = openingParams.CollegeBasicConfig.ServiceCycleEnd
                    });
                }

                LogUtil.AddLog($"用户 {witsUser.Id} 生成应用选项完成，应用数量: {appOptions.Count}");
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"用户 {witsUser.Id} 生成应用选项异常: {ex.Message}，返回空列表");
            }

            return appOptions;
        }

        /// <summary>
        /// 获取GTIS应用选项（保留原方法，用于其他场景）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>应用选项</returns>
        private List<AppOptionModel> GetGtisAppOptions(ServiceOpeningParams openingParams)
        {
            var appOptions = new List<AppOptionModel>();

            try
            {
                // GTIS应用选项 - 基于账号是否具有GTIS应用权限
                if (openingParams.MainService.HasGtisApp == true)
                {
                    appOptions.Add(new AppOptionModel
                    {
                        AppName = "Gtis6",
                        MaxAccount = openingParams.GtisBasicConfig.MaxAccountsNum??0,
                        StartTime = openingParams.GtisBasicConfig.ServiceCycleStart,
                        EndTime = openingParams.GtisBasicConfig.ServiceCycleEnd
                    });
                }

                // 环球搜应用选项 - 基于账号是否具有环球搜应用权限
                if (openingParams.MainService.HasGlobalSearchApp == true)
                {
                    appOptions.Add(new AppOptionModel
                    {
                        AppName = "HQS",
                        MaxAccount = openingParams.GlobalSearchBasicConfig.MaxAccountsNum??0,
                        StartTime = openingParams.GlobalSearchBasicConfig.ServiceCycleStart,
                        EndTime = openingParams.GlobalSearchBasicConfig.ServiceCycleEnd
                    });
                }

                // SalesWits应用选项 - 基于账号是否具有SalesWits应用权限
                if (openingParams.MainService.HasSalesWitsApp == true)
                {
                    appOptions.Add(new AppOptionModel
                    {
                        AppName = "CRM",
                        MaxAccount = openingParams.SalesWitsBasicConfig.MaxAccountsNum ?? 0,
                        StartTime = openingParams.SalesWitsBasicConfig.ServiceCycleStart,
                        EndTime = openingParams.SalesWitsBasicConfig.ServiceCycleEnd
                    });
                }

                // 慧思学院应用选项 - 基于账号是否具有慧思学院应用权限
                if (openingParams.MainService.HasCollegeApp == true)
                {
                    appOptions.Add(new AppOptionModel
                    {
                        AppName = "College",
                        MaxAccount = openingParams.CollegeBasicConfig.MaxAccountsNum ?? 0,
                        StartTime = openingParams.CollegeBasicConfig.ServiceCycleStart,
                        EndTime = openingParams.CollegeBasicConfig.ServiceCycleEnd
                    });
                }

                LogUtil.AddLog($"生成应用选项完成，应用数量: {appOptions.Count}");
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"生成应用选项异常: {ex.Message}，返回空列表");
            }

            return appOptions;
        }
        
        /// <summary>
        /// 获取环球搜码
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <param name="globalSearchResult">环球搜开通结果（当前开通流程中的结果）</param>
        /// <returns>环球搜码</returns>
        private string GetGlobalSearchCode(ServiceOpeningParams openingParams, GlobalSearchResult globalSearchResult = null)
        {
            try
            {
                LogUtil.AddLog("开始获取环球搜码");

                // 1. 优先从当前开通流程的环球搜结果中获取（新开通场景）
                if (globalSearchResult != null && globalSearchResult.Success && !string.IsNullOrEmpty(globalSearchResult.PrimaryCode))
                {
                    LogUtil.AddLog($"从当前开通流程的环球搜结果获取到环球搜码: {globalSearchResult.PrimaryCode}");
                    return globalSearchResult.PrimaryCode;
                }

                // 2. 从预处理结果中获取现有环球搜码（续约或变更场景）
                if (openingParams.GlobalSearchPreprocessResult != null)
                {
                    if (!string.IsNullOrEmpty(openingParams.GlobalSearchPreprocessResult.ExistingPrimaryCode))
                    {
                        LogUtil.AddLog($"从环球搜预处理结果获取到现有环球搜码: {openingParams.GlobalSearchPreprocessResult.ExistingPrimaryCode}");
                        return openingParams.GlobalSearchPreprocessResult.ExistingPrimaryCode;
                    }
                }

                // 2. 尝试从特殊配置中获取现有的环球搜码
                if (openingParams.GlobalSearchSpecialConfig?.ServiceInfo != null)
                {
                    var existingCode = GetExistingGlobalSearchCode(openingParams);
                    if (!string.IsNullOrEmpty(existingCode))
                    {
                        LogUtil.AddLog($"从环球搜特殊配置获取到现有环球搜码: {existingCode}");
                        return existingCode;
                    }
                }

                // 3. 判断是否需要环球搜服务（仅在没有现有环球搜码时判断）
                if (openingParams.MainService.HasGlobalSearchApp !=true)
                {
                    // 如果没有现有环球搜码，且当前也不需要环球搜服务，则返回空字符串
                    LogUtil.AddLog("当前服务组合不包含环球搜服务，且没有现有环球搜码，GTIS用户不绑定环球搜码");
                    return "";
                }

                LogUtil.AddLog("当前服务组合包含环球搜服务，但未获取到环球搜码");

                // 4. 无法获取到有效的环球搜码，抛出异常
                throw new ApiException("环球搜码获取失败：当前服务组合包含环球搜服务，但无法获取到有效的环球搜码，请检查环球搜服务是否正常开通");

            }
            catch (ApiException)
            {
                // 重新抛出业务异常
                throw;
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取环球搜码时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取现有的环球搜码
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>现有环球搜码</returns>
        private string GetExistingGlobalSearchCode(ServiceOpeningParams openingParams)
        {
            try
            {
                // 从环球搜特殊配置中获取现有的环球搜码
                var globalSearchParams = openingParams.GlobalSearchSpecialConfig?.ServiceInfo;
                if (globalSearchParams != null)
                {
                    // 查询该合同的环球搜用户记录，获取环球搜码
                    var globalSearchUsers = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance
                        .GetDataList(x => x.ContractServiceInfoGlobalSearchId == globalSearchParams.Id && x.Deleted == false);

                    if (globalSearchUsers != null && globalSearchUsers.Count > 0)
                    {
                        // 返回第一个用户的账号作为环球搜码（通常主账号）
                        var primaryUser = globalSearchUsers.FirstOrDefault(x => x.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount);
                        if (primaryUser != null && !string.IsNullOrEmpty(primaryUser.AccountNumber))
                        {
                            return primaryUser.AccountNumber;
                        }

                        // 如果没有主账号，返回第一个有账号的用户
                        var userWithAccount = globalSearchUsers.FirstOrDefault(x => !string.IsNullOrEmpty(x.AccountNumber));
                        if (userWithAccount != null)
                        {
                            return userWithAccount.AccountNumber;
                        }
                    }
                }

                return "";
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取现有环球搜码异常: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取IsHistory值（根据GTIS服务的IsGtisOldCustomer字段判断）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>IsHistory值：0=新客户，1=老客户</returns>
        private int GetIsHistoryValue(ServiceOpeningParams openingParams)
        {
            try
            {
                // 从GTIS特殊配置中获取IsGtisOldCustomer字段
                var isOldCustomer = openingParams.GtisSpecialConfig?.ServiceInfo?.IsGtisOldCustomer;

                // 根据DbOpe_crm_product_gtis_relation.FormatGtisUser的逻辑：
                // IsHistory = gtis.IsGtisOldCustomer == null ? 0 : (gtis.IsGtisOldCustomer.Value ? 1 : 0);
                var result = isOldCustomer == null ? 0 : (isOldCustomer.Value ? 1 : 0);

                LogUtil.AddLog($"IsHistory判断: IsGtisOldCustomer={isOldCustomer}, IsHistory={result}");
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取IsHistory值异常: {ex.Message}，默认返回0（新客户）");
                return 0; // 异常时默认为新客户
            }
        }

        /// <summary>
        /// 获取ForbidSearchExport值（根据GTIS服务的ForbidSearchExport字段判断）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>ForbidSearchExport值</returns>
        private bool GetForbidSearchExportValue(ServiceOpeningParams openingParams)
        {
            try
            {
                // 从GTIS特殊配置中获取ForbidSearchExport字段
                var forbidSearchExport = openingParams.GtisSpecialConfig?.ServiceInfo?.ForbidSearchExport;

                // 根据DbOpe_crm_product_gtis_relation.FormatGtisUser的逻辑：
                // ForbidSearchExport = gtis.ForbidSearchExport == null ? false : gtis.ForbidSearchExport.Value;
                var result = forbidSearchExport == null ? false : forbidSearchExport.Value;

                LogUtil.AddLog($"ForbidSearchExport判断: ForbidSearchExport={forbidSearchExport}, result={result}");
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取ForbidSearchExport值异常: {ex.Message}，默认返回false");
                return false; // 异常时默认为false
            }
        }

        /// <summary>
        /// 获取WordRptPermissions值（根据GTIS服务的WordRptPermissions字段判断）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>WordRptPermissions值</returns>
        private bool GetWordRptPermissionsValue(ServiceOpeningParams openingParams)
        {
            try
            {
                // 从GTIS特殊配置中获取WordRptPermissions字段
                var wordRptPermissions = openingParams.GtisSpecialConfig?.ServiceInfo?.WordRptPermissions;

                // 根据DbOpe_crm_product_gtis_relation.FormatGtisUser的逻辑：
                // WordRptPermissions = gtis.WordRptPermissions == null ? false : gtis.WordRptPermissions.Value;
                var result = wordRptPermissions == null ? false : wordRptPermissions.Value;

                LogUtil.AddLog($"WordRptPermissions判断: WordRptPermissions={wordRptPermissions}, result={result}");
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取WordRptPermissions值异常: {ex.Message}，默认返回false");
                return false; // 异常时默认为false
            }
        }

        /// <summary>
        /// 获取WordRptMaxTimes值（根据GTIS服务的WordRptMaxTimes字段判断）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>WordRptMaxTimes值</returns>
        private int GetWordRptMaxTimesValue(ServiceOpeningParams openingParams)
        {
            try
            {
                // 从GTIS特殊配置中获取WordRptMaxTimes字段
                var wordRptMaxTimes = openingParams.GtisSpecialConfig?.ServiceInfo?.WordRptMaxTimes;

                // 根据DbOpe_crm_product_gtis_relation.FormatGtisUser的逻辑：
                // WordRptMaxTimes = gtis.WordRptMaxTimes == null ? 0 : gtis.WordRptMaxTimes.Value;
                var result = wordRptMaxTimes == null ? 0 : wordRptMaxTimes.Value;

                LogUtil.AddLog($"WordRptMaxTimes判断: WordRptMaxTimes={wordRptMaxTimes}, result={result}");
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取WordRptMaxTimes值异常: {ex.Message}，默认返回0");
                return 0; // 异常时默认为0
            }
        }

        /// <summary>
        /// 获取PayDate值（根据合同的ArrivalDate字段获取付款年月）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>PayDate值（格式：yyyyMM）</returns>
        private string GetPayDateValue(ServiceOpeningParams openingParams)
        {
            try
            {
                var arrivalDate = DbContext.Crm2Db.Queryable<Db_crm_contract>()
                .LeftJoin<Db_crm_contract_receipt_details>((c,d)=>c.Id == d.ContractId)
                .LeftJoin<Db_crm_collectioninfo>((c,d,ci)=>d.CollectionInfoId == ci.Id)
                .Where((c, d, ci) => d.Deleted ==false && d.Deleted == false && ci.Deleted == false)
                .Where((c, d, ci) => c.Id == openingParams.Contract.Id)
                .OrderBy((c, d, ci) => ci.ArrivalDate)
                .Select((c, d, ci) => ci.ArrivalDate)
                .First();
                if (arrivalDate !=null)
                {
                    var result = arrivalDate.Value.ToString("yyyyMM");
                    LogUtil.AddLog($"PayDate判断: ArrivalDate={arrivalDate}, PayDate={result}");
                    return result;
                }
                else
                {
                    throw new ApiException("未找到到账日期");
                }
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取PayDate值异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取甲方公司名称
        /// </summary>
        /// <param name="firstParty">开通参数</param>
        /// <returns>PayDate值（格式：yyyyMM）</returns>
        private string GetFirstPartyName(string firstParty)
        {
            try
            {
                var result = DbContext.Crm2Db.Queryable<Db_crm_customer_subcompany>()
                .Where((c) => c.Id == firstParty && c.Deleted == 0)
                .Select((c) => c.CompanyName)
                .First();
                if (result !=null)
                {
                    LogUtil.AddLog($"获取甲方公司名称: {result}");
                    return result;
                }
                else
                {
                    throw new ApiException("未找到甲方公司名称");
                }
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取甲方公司名称异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取IsWordRptMaxTimesNow值（修改定制报告每年总次数是否马上生效）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>IsWordRptMaxTimesNow值</returns>
        private bool GetIsWordRptMaxTimesNowValue(ServiceOpeningParams openingParams)
        {
            try
            {
                // 根据DbOpe_crm_product_gtis_relation.FormatGtisUser的逻辑：
                // 在第255行设置为 userCommonParams.IsWordRptMaxTimesNow = true;
                // 这表示修改定制报告每年总次数立即生效，子账户已授权次数会被清0
                var result = true;

                LogUtil.AddLog($"IsWordRptMaxTimesNow判断: result={result}");
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取IsWordRptMaxTimesNow值异常: {ex.Message}，默认返回true");
                return true; // 异常时默认为true
            }
        }

        /// <summary>
        /// 获取IsDelSubCountry值（子账号授权国家次数是否降级）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>IsDelSubCountry值</returns>
        private bool GetIsDelSubCountryValue(ServiceOpeningParams openingParams)
        {
            try
            {
                // 从GTIS特殊配置中获取DelAuthorizationNum字段
                var delAuthorizationNum = openingParams.GtisSpecialConfig?.ServiceInfo?.DelAuthorizationNum;

                // 根据DbOpe_crm_product_gtis_relation.FormatGtisUser的逻辑：
                // userCommonParams.IsDelSubCountry = gtis.DelAuthorizationNum;
                var result = delAuthorizationNum ?? false;

                LogUtil.AddLog($"IsDelSubCountry判断: DelAuthorizationNum={delAuthorizationNum}, result={result}");
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"获取IsDelSubCountry值异常: {ex.Message}，默认返回false");
                return false; // 异常时默认为false
            }
        }

        #endregion
    }
}