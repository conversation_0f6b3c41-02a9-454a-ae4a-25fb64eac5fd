﻿using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.IdentityModel.Tokens;
using Namotion.Reflection;
using Newtonsoft.Json.Linq;

namespace CRM2_API.Common.Filter
{
    /// <summary>
    /// 自定义身份验证
    /// </summary>
    public class AuthAttribute : IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            //跳过身份验证
            if (context.Filters.Any(f => f is SkipAuthCheckAttribute))
            {
                return;
            }
            string tokenBearer = String.Empty;
            bool IsMobile = false;
            bool IsSkipUserSupplementInfo = context.Filters.Any(f => f is SkipUserSupplementInfoAttribute);
            if (context.HttpContext.Request.Headers.ContainsKey("Authorization"))
            {
                tokenBearer = context.HttpContext.Request.Headers["Authorization"];

            } 
            else if (context.HttpContext.Request.Method.Equals("GET") && context.HttpContext.Request.Query.ContainsKey("Authorization"))
            {
                tokenBearer = context.HttpContext.Request.Query["Authorization"];
                IsMobile = context.HttpContext.Request.Query["IsMobile"].ToString().ToBool();
            }
            else
            {
                throw new ApiException("您尚未登录系统", Enum_ReturnErrorCode.TokenError);
            }
            
            if (tokenBearer.IsNotNullOrEmpty())
            {
                try
                {
                    
                    if (context.HttpContext.Request.Headers.ContainsKey("IsMobile"))
                        IsMobile = context.HttpContext.Request.Headers["IsMobile"].ToString().ToBool();
                    TokenModel.SetAndCheckTokenInfo(tokenBearer.Replace("Bearer ", ""), IsMobile, IsSkipUserSupplementInfo);
                }
                catch (SecurityTokenExpiredException)
                {
                    throw new ApiException("登录状态已过期", Enum_ReturnErrorCode.TokenError);
                }
                catch (ApiException ex)
                {
                    throw new ApiException(ex.Message, Enum_ReturnErrorCode.RemoteLogin);
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"身份验证异常：{ex.Message}，堆栈：{ex.StackTrace}");
                    if (ex.InnerException != null)
                    {
                        LogUtil.AddErrorLog($"内部异常：{ex.InnerException.Message}");
                    }
                    throw new ApiException($"身份异常：{ex.Message}", Enum_ReturnErrorCode.TokenError);
                }
            }
        }
    }

    /// <summary>
    /// 跳过身份验证特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class SkipAuthCheckAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {

        }
    }

    /// <summary>
    /// 跳过是否完善身份信息验证特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class SkipUserSupplementInfoAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {

        }
    }
}
