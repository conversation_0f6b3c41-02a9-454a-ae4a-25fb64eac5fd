﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_product")]
    public class Db_crm_product
    {
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:产品编码
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string ProductNum { get; set; }

        /// <summary>
        /// Desc:产品名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ProductName { get; set; }

        /// <summary>
        /// Desc:产品英文名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ProductNameEN { get; set; }

        /// <summary>
        /// Desc:产品描述
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ProductDescription { get; set; }

        /// <summary>
        /// Desc:产品英文描述
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ProductDescriptionEn { get; set; }

        /// <summary>
        /// Desc:产品类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ProductType { get; set; }

        /// <summary>
        /// Desc:服务周期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ServiceCycle { get; set; }

        /// <summary>
        /// Desc:是否推荐选择
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? IsRecommend { get; set; }

        /// <summary>
        /// Desc:人民币标准价格
        /// Default:
        /// Nullable:True
        /// </summary>           
        public decimal? CNYTypical { get; set; }

        /// <summary>
        /// Desc:美元标准价格
        /// Default:
        /// Nullable:True
        /// </summary>           
        public decimal? USDTypical { get; set; }

        /// <summary>
        /// Desc:欧元标准价格
        /// Default:
        /// Nullable:True
        /// </summary>           
        public decimal? EURTypical { get; set; }

        /// <summary>
        /// Desc:周期月份最短
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int ServiceCycleStart { get; set; }

        /// <summary>
        /// Desc:周期月份最长
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int ServiceCycleEnd { get; set; }

        /// <summary>
        /// Desc:(GTIS)默认子账号数量
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? SubCount { get; set; }

        /// <summary>
        /// Desc:(GTIS)超级子账号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? SuperSubCount { get; set; }

        /// <summary>
        /// Desc:(GTIS)定制报告数量
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? GtisReport { get; set; }

        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Remark { get; set; }

        /// <summary>
        /// Desc:产品状态(草稿、启用、停用)
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int ProductState { get; set; }

        /// <summary>
        /// Desc:不允许售卖(国外)/福利产品
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int NotForSale { get; set; }

        /// <summary>
        /// Desc:产品简介
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string BriefIntroduction { get; set; }

        /// <summary>
        /// Desc:别名
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Aliss { get; set; }

        /// <summary>
        /// Desc:是否显示在合同中
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ShowInContract { get; set; }

        /// <summary>
        /// Desc:删除标记
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:False
        /// </summary>           
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:修改时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
