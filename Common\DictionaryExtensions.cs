﻿using System.Dynamic;

namespace CRM2_API.Common
{
    public static class DictionaryExtensions
    {
        /// <summary>
        /// 将IDictionary<string, object>转换为dynamic类型的ExpandoObject。
        /// </summary>
        /// <param name="dictionary">要转换的字典。</param>
        /// <returns>包含相同数据的ExpandoObject，可作为dynamic使用。</returns>
        public static dynamic ToDynamic(this IDictionary<string, object> dictionary)
        {
            var expando = new ExpandoObject() as IDictionary<string, object>;

            // 将字典的内容复制到ExpandoObject中
            foreach (var kvp in dictionary)
            {
                // 如果ExpandoObject中已经存在该键，则更新它
                // 否则，它会自动添加新键
                expando[kvp.Key] = kvp.Value;
            }

            // 返回dynamic类型的ExpandoObject
            return expando as dynamic;
        }
        /// <summary>
        /// 尝试从字典中获取指定键对应的值，并将其转换为指定的类型。
        /// 如果键不存在或值无法转换为指定类型，则返回该类型的默认值。
        /// </summary>
        /// <typeparam name="T">要转换成的目标类型。</typeparam>
        /// <param name="dictionary">要从中检索值的字典。</param>
        /// <param name="key">要检索的键。</param>
        /// <param name="value">如果找到并成功转换，则包含转换后的值；否则为默认值。</param>
        /// <returns>如果成功找到并转换了值，为false。</returns>
        public static bool TryGetDicValue<T>(this IDictionary<string, object> dictionary, string key, out T value)
        {
            // 首先检查键是否存在
            if (dictionary.TryGetValue(key, out object objValue))
            {
                // 然后尝试将值转换为指定类型
                try
                {
                    value = (T)Convert.ChangeType(objValue, typeof(T));
                    return true; // 转换成功
                }
                catch (InvalidCastException)
                {
                    // 如果转换失败，设置value为默认值
                    value = default(T);
                    return false;
                }
                catch (FormatException)
                {
                    // 如果格式不正确（例如，尝试将非数字字符串转换为int），也返回默认值
                    value = default(T);
                    return false;
                }
                catch (OverflowException)
                {
                    // 如果转换导致溢出（int），也返回默认值
                    value = default(T);
                    return false;
                }
            }

            // 如果键不存在，则value为默认值，返回false
            value = default(T);
            return false;
        }
        public static TValue GetDefaultValue<TKey, TValue>(this IDictionary<TKey, TValue> dictionary, TKey key)
        where TKey : notnull
        {
            if (dictionary.TryGetValue(key, out TValue value))
            {
                return value;
            }

            return default(TValue); // 对于引用类型是null
        }
    }

}
