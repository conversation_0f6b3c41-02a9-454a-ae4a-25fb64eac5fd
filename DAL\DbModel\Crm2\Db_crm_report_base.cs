using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 报告基础表
    /// </summary>
    [SugarTable("crm_report_base")]
    public class Db_crm_report_base
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 报告类型：1-日报，2-周报，3-月报
        /// </summary>
        public int ReportType { get; set; }

        /// <summary>
        /// 报告标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 创建用户姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 团队ID
        /// </summary>
        public string TeamId { get; set; }

        /// <summary>
        /// 团队名称
        /// </summary>
        public string TeamName { get; set; }

        /// <summary>
        /// 状态：1-草稿，2-已提交
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 提交状态：1-准时，2-迟交，3-未提交
        /// </summary>
        public int? SubmitStatus { get; set; }

        /// <summary>
        /// 报告日期
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// 报告年份
        /// </summary>
        public int ReportYear { get; set; }

        /// <summary>
        /// 报告月份
        /// </summary>
        public int? ReportMonth { get; set; }

        /// <summary>
        /// 报告周数
        /// </summary>
        public int? ReportWeek { get; set; }

        /// <summary>
        /// 是否自动生成：0-否，1-是
        /// </summary>
        public bool IsAutoGenerated { get; set; }

        /// <summary>
        /// 规定时间
        /// </summary>
        public DateTime? RegularDeadline { get; set; }

        /// <summary>
        /// 截止时间
        /// </summary>
        public DateTime? FinalDeadline { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SubmitTime { get; set; }

        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateDate { get; set; }

        /// <summary>
        /// 附件数量
        /// </summary>
        public int AttachmentCount { get; set; }

        /// <summary>
        /// 客户数量
        /// </summary>
        public int CustomerCount { get; set; }

        /// <summary>
        /// 点赞数量
        /// </summary>
        public int LikeCount { get; set; }

        /// <summary>
        /// 评论数量
        /// </summary>
        public int CommentCount { get; set; }

        /// <summary>
        /// 接收人数量
        /// </summary>
        public int ReceiverCount { get; set; }

        /// <summary>
        /// 抄送人数量
        /// </summary>
        public int CcCount { get; set; }
    }
} 