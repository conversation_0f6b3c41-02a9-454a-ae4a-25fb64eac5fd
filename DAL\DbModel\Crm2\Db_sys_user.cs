﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_user")]
    public partial class Db_sys_user
    {
        public Db_sys_user()
        {


        }
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:账号
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string UserName { get; set; }

        /// <summary>
        /// Desc:密码
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string PassWord { get; set; }

        /// <summary>
        /// Desc:员工编号
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string UserNum { get; set; }
        /// <summary>
        /// Desc:工号
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string JobNum { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string Name { get; set; }

        /// <summary>
        /// Desc:部门id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string DepartmentId { get; set; }

        /// <summary>
        /// Desc:所属组织id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string OrganizationId { get; set; }

        /// <summary>
        /// Desc:身份证号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string IDNo { get; set; }

        /// <summary>
        /// Desc:手机号
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string Telephone { get; set; }

        /// <summary>
        /// Desc:电子邮箱
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Email { get; set; }

        /// <summary>
        /// Desc:1-管理者;2-职工
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int UserType { get; set; }

        /// <summary>
        /// Desc:最大可保存客户数
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int MaxSaveCustomer { get; set; }

        /// <summary>
        /// Desc:可保存最大客户数(手动输入)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int MaxSaveCustomerMannualInput { get; set; }

        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Remark { get; set; }

        /// <summary>
        /// Desc:是否启用
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool UserStatus { get; set; }

        /// <summary>
        /// Desc:微信绑定标识
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool WeChatIsBind { get; set; }

        /// <summary>
        /// Desc:微信openid
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string WeChatOpenID { get; set; }

        /// <summary>
        /// Desc:微信公众号openid
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string WeChatOpenIDGZH { get; set; }

        /// <summary>
        /// Desc:钉钉绑定标识
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool DingDingIsBind { get; set; }

        /// <summary>
        /// Desc:钉钉openid
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string DingDingOpenID { get; set; }

        /// <summary>
        /// Desc:钉钉unionid
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string DingDingUnionID { get; set; }

        /// <summary>
        /// Desc:是否允许用户名登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowUserNameLogin { get; set; }

        /// <summary>
        /// Desc:是否允许手机号登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowTelphoneLogin { get; set; }

        /// <summary>
        /// Desc:是否允许短信验证码登录
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool AllowMessageAuthCodeLogin { get; set; }

        /// <summary>
        /// Desc:是否允许邮箱登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowEmailLogin { get; set; }

        /// <summary>
        /// Desc:是否允许微信登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowWechatLogin { get; set; }

        /// <summary>
        /// Desc:是否允许钉钉登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowDingdingLogin { get; set; }

        /// <summary>
        /// Desc:最后登录时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// Desc:最后登录IP
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string LastLoginIP { get; set; }

        /// <summary>
        /// Desc:微信昵称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string NickName { get; set; }

        /// <summary>
        /// Desc:头像图片
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AvatarImage { get; set; }

        /// <summary>
        /// Desc:入职时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? EntryTime { get; set; }

        /// <summary>
        /// Desc:是否补充必要信息
        /// Default:
        /// Nullable:True
        /// </summary>        
        public bool HasSupplementInfo { get; set; }

        /// <summary>
        /// Desc:离职时间
        /// Default:
        /// Nullable:True
        /// </summary>        
        public DateTime? ResignationTime { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:False
        /// </summary>           
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
