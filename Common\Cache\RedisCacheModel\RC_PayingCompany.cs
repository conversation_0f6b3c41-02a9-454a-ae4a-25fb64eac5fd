using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        /// <summary>
        /// 代付款公司信息表缓存
        /// </summary>
        public class PayingCompany
        {
            const string PAYINGCOMPANY_ALL = "payingcompany_all";
            const string PAYINGCOMPANY_ID = "payingcompany_id_";
            const string PAYINGCOMPANY_CONTRACTID = "payingcompany_contractid_";
            const string PAYINGCOMPANY_NAME = "payingcompany_name_";

            /// <summary>
            /// 缓存的简化模型
            /// </summary>
            public class PayingCompanySimple
            {
                /// <summary>
                /// 公司ID
                /// </summary>
                public string CompanyId { get; set; }
                
                /// <summary>
                /// 合同ID
                /// </summary>
                public string ContractId { get; set; }
                
                /// <summary>
                /// 代付款公司名称
                /// </summary>
                public string PayingCompanyName { get; set; }
                
                /// <summary>
                /// 隐藏标识
                /// </summary>
                public bool? IsHidden { get; set; }
            }

            /// <summary>
            /// 根据代付款公司名称获取合同ID列表
            /// </summary>
            /// <param name="payingCompanyName">代付款公司名称</param>
            /// <returns>找到的合同ID列表，如果不存在则返回空列表</returns>
            public static List<string> GetContractIdsByCompanyName(string payingCompanyName)
            {
                if (string.IsNullOrEmpty(payingCompanyName))
                    return new List<string>();
                
                var key = PAYINGCOMPANY_NAME + payingCompanyName;
                if (RedisHelper.Exists(key))
                    return RedisHelper.Get<List<string>>(key);
                
                // 当单独的名称缓存不存在时，尝试从全部缓存中获取
                var allCompanies = GetAllPayingCompanies();
                if (allCompanies != null && allCompanies.Count > 0)
                {
                    var companies = allCompanies.Where(c => c.PayingCompanyName == payingCompanyName).ToList();
                    if (companies.Count > 0)
                    {
                        // 找到后同时保存到单独缓存
                        var contractIds = companies.Select(c => c.ContractId).Where(id => !string.IsNullOrEmpty(id)).ToList();
                        SaveContractIdsByCompanyName(payingCompanyName, contractIds);
                        return contractIds;
                    }
                }
                return new List<string>();
            }

            /// <summary>
            /// 根据代付款公司名称获取第一个找到的合同ID
            /// </summary>
            /// <param name="payingCompanyName">代付款公司名称</param>
            /// <returns>找到的第一个合同ID，如果不存在则返回null</returns>
            public static string GetFirstContractIdByCompanyName(string payingCompanyName)
            {
                var contractIds = GetContractIdsByCompanyName(payingCompanyName);
                return contractIds.Count > 0 ? contractIds[0] : null;
            }

            /// <summary>
            /// 保存代付款公司名称与合同ID列表的映射关系到缓存
            /// </summary>
            /// <param name="payingCompanyName">代付款公司名称</param>
            /// <param name="contractIds">合同ID列表</param>
            public static void SaveContractIdsByCompanyName(string payingCompanyName, List<string> contractIds)
            {
                if (!string.IsNullOrEmpty(payingCompanyName) && contractIds != null && contractIds.Count > 0)
                {
                    var key = PAYINGCOMPANY_NAME + payingCompanyName;
                    RedisHelper.Set(key, contractIds, TimeSpan.FromHours(26));
                }
            }

            /// <summary>
            /// 添加单个合同ID到代付款公司的映射关系
            /// </summary>
            /// <param name="payingCompanyName">代付款公司名称</param>
            /// <param name="contractId">合同ID</param>
            public static void AddContractIdToCompanyName(string payingCompanyName, string contractId)
            {
                if (string.IsNullOrEmpty(payingCompanyName) || string.IsNullOrEmpty(contractId))
                    return;
                    
                var contractIds = GetContractIdsByCompanyName(payingCompanyName);
                if (!contractIds.Contains(contractId))
                {
                    contractIds.Add(contractId);
                    SaveContractIdsByCompanyName(payingCompanyName, contractIds);
                }
            }

            /// <summary>
            /// 初始化代付款公司缓存
            /// </summary>
            public static void InitializeCache()
            {
                try
                {
                    // 从数据库查询所有代付款公司信息
                    var payingCompanies = DbContext.Crm2Db.Queryable<Db_crm_contract_paymentinfo>()
                        .LeftJoin<Db_crm_customer_subcompany>((p, c) => p.PaymentCompany == c.Id && c.Deleted != (int)EnumCustomerDel.Del)
                        .Where((p, c)  => p.IsBehalfPayment == true && p.Deleted != true)
                        .Select((p, c)  => new 
                        {
                            p.Id,
                            ContractId = p.ContractId,
                            PaymentCompany = c.CompanyName
                        })
                        .ToList();

                    ClearAllCache();
                    if (payingCompanies != null && payingCompanies.Count > 0)
                    {
                        // 转换为缓存模型
                        List<PayingCompanySimple> cacheModels = new List<PayingCompanySimple>();
                        
                        // 按公司名称分组收集合同ID
                        Dictionary<string, List<string>> companyNameToContractIds = new Dictionary<string, List<string>>();
                        
                        foreach (var company in payingCompanies)
                        {
                            cacheModels.Add(new PayingCompanySimple
                            {
                                CompanyId = company.Id,
                                ContractId = company.ContractId,
                                PayingCompanyName = company.PaymentCompany,
                                IsHidden = false
                            });
                            
                            // 收集相同公司名称对应的所有合同ID
                            if (!string.IsNullOrEmpty(company.PaymentCompany) && !string.IsNullOrEmpty(company.ContractId))
                            {
                                if (!companyNameToContractIds.ContainsKey(company.PaymentCompany))
                                {
                                    companyNameToContractIds[company.PaymentCompany] = new List<string>();
                                }
                                
                                if (!companyNameToContractIds[company.PaymentCompany].Contains(company.ContractId))
                                {
                                    companyNameToContractIds[company.PaymentCompany].Add(company.ContractId);
                                }
                            }
                        }
                        
                        // 保存到缓存
                        SaveAllPayingCompanies(cacheModels);
                        
                        // 保存公司名称到合同ID列表的映射关系
                        foreach (var kvp in companyNameToContractIds)
                        {
                            SaveContractIdsByCompanyName(kvp.Key, kvp.Value);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 记录日志
                    LogUtil.AddLog("初始化代付款公司缓存失败", ex);
                }
            }

            /// <summary>
            /// 获取所有代付款公司简化信息
            /// </summary>
            /// <returns>代付款公司列表，如果缓存不存在返回null</returns>
            public static List<PayingCompanySimple> GetAllPayingCompanies()
            {
                if (RedisHelper.Exists(PAYINGCOMPANY_ALL))
                    return RedisHelper.Get<List<PayingCompanySimple>>(PAYINGCOMPANY_ALL);
                return null;
            }

            /// <summary>
            /// 保存所有代付款公司简化信息到缓存
            /// </summary>
            /// <param name="companies">代付款公司列表</param>
            public static void SaveAllPayingCompanies(List<PayingCompanySimple> companies)
            {
                RedisHelper.Set(PAYINGCOMPANY_ALL, companies, TimeSpan.FromHours(26));
                
                // 同时保存每个公司的单独缓存
                if (companies != null && companies.Count > 0)
                {
                    // 按公司名称分组收集合同ID
                    Dictionary<string, List<string>> companyNameToContractIds = new Dictionary<string, List<string>>();
                    
                    foreach (var company in companies)
                    {
                        if (!string.IsNullOrEmpty(company.CompanyId))
                        {
                            SavePayingCompany(company);
                        }
                        
                        // 同时保存按合同ID的缓存
                        if (!string.IsNullOrEmpty(company.ContractId))
                        {
                            SavePayingCompanyByContractId(company);
                        }
                        
                        // 收集相同公司名称对应的所有合同ID
                        if (!string.IsNullOrEmpty(company.PayingCompanyName) && !string.IsNullOrEmpty(company.ContractId))
                        {
                            if (!companyNameToContractIds.ContainsKey(company.PayingCompanyName))
                            {
                                companyNameToContractIds[company.PayingCompanyName] = new List<string>();
                            }
                            
                            if (!companyNameToContractIds[company.PayingCompanyName].Contains(company.ContractId))
                            {
                                companyNameToContractIds[company.PayingCompanyName].Add(company.ContractId);
                            }
                        }
                    }
                    
                    // 保存公司名称到合同ID列表的映射关系
                    foreach (var kvp in companyNameToContractIds)
                    {
                        SaveContractIdsByCompanyName(kvp.Key, kvp.Value);
                    }
                }
            }

            /// <summary>
            /// 根据公司ID获取代付款公司简化信息
            /// </summary>
            /// <param name="companyId">公司ID</param>
            /// <returns>代付款公司简化信息，如果缓存不存在返回null</returns>
            public static PayingCompanySimple GetPayingCompanyById(string companyId)
            {
                var key = PAYINGCOMPANY_ID + companyId;
                if (RedisHelper.Exists(key))
                    return RedisHelper.Get<PayingCompanySimple>(key);
                
                // 当单独的ID缓存不存在时，尝试从全部缓存中获取
                var allCompanies = GetAllPayingCompanies();
                if (allCompanies != null && allCompanies.Count > 0)
                {
                    var company = allCompanies.FirstOrDefault(c => c.CompanyId == companyId);
                    if (company != null)
                    {
                        // 找到后同时保存到单独缓存
                        SavePayingCompany(company);
                        return company;
                    }
                }
                return null;
            }

            /// <summary>
            /// 根据合同ID获取代付款公司简化信息
            /// </summary>
            /// <param name="contractId">合同ID</param>
            /// <returns>代付款公司简化信息，如果缓存不存在返回null</returns>
            public static PayingCompanySimple GetPayingCompanyByContractId(string contractId)
            {
                var key = PAYINGCOMPANY_CONTRACTID + contractId;
                if (RedisHelper.Exists(key))
                    return RedisHelper.Get<PayingCompanySimple>(key);
                
                // 当单独的合同ID缓存不存在时，尝试从全部缓存中获取
                var allCompanies = GetAllPayingCompanies();
                if (allCompanies != null && allCompanies.Count > 0)
                {
                    var company = allCompanies.FirstOrDefault(c => c.ContractId == contractId);
                    if (company != null)
                    {
                        // 找到后同时保存到单独缓存
                        SavePayingCompanyByContractId(company);
                        return company;
                    }
                }
                return null;
            }

            /// <summary>
            /// 保存单个代付款公司简化信息到缓存
            /// </summary>
            /// <param name="company">代付款公司简化信息</param>
            public static void SavePayingCompany(PayingCompanySimple company)
            {
                var key = PAYINGCOMPANY_ID + company.CompanyId;
                RedisHelper.Set(key, company, TimeSpan.FromHours(26));
            }

            /// <summary>
            /// 保存单个代付款公司简化信息到缓存（按合同ID）
            /// </summary>
            /// <param name="company">代付款公司简化信息</param>
            public static void SavePayingCompanyByContractId(PayingCompanySimple company)
            {
                if (!string.IsNullOrEmpty(company.ContractId))
                {
                    var key = PAYINGCOMPANY_CONTRACTID + company.ContractId;
                    RedisHelper.Set(key, company, TimeSpan.FromHours(26));
                }
            }

            /// <summary>
            /// 根据公司ID清除单个代付款公司缓存
            /// </summary>
            /// <param name="companyId">公司ID</param>
            public static void ClearCompanyCache(string companyId)
            {
                var key = PAYINGCOMPANY_ID + companyId;
                RedisHelper.Del(key);
            }

            /// <summary>
            /// 根据合同ID清除单个代付款公司缓存
            /// </summary>
            /// <param name="contractId">合同ID</param>
            public static void ClearCompanyCacheByContractId(string contractId)
            {
                var key = PAYINGCOMPANY_CONTRACTID + contractId;
                RedisHelper.Del(key);
            }

            /// <summary>
            /// 清除所有代付款公司缓存
            /// </summary>
            public static void ClearAllCache()
            {
                RedisHelper.Del(PAYINGCOMPANY_ALL);
                var keys = RedisHelper.Keys(PAYINGCOMPANY_ID + "*");
                if (keys != null && keys.Length > 0)
                    RedisHelper.Del(keys);
                
                var contractKeys = RedisHelper.Keys(PAYINGCOMPANY_CONTRACTID + "*");
                if (contractKeys != null && contractKeys.Length > 0)
                    RedisHelper.Del(contractKeys);
                    
                var nameKeys = RedisHelper.Keys(PAYINGCOMPANY_NAME + "*");
                if (nameKeys != null && nameKeys.Length > 0)
                    RedisHelper.Del(nameKeys);
            }

            /// <summary>
            /// 根据代付款公司名称模糊搜索匹配的公司信息
            /// </summary>
            /// <param name="nameKeyword">公司名称关键词</param>
            /// <returns>匹配的代付款公司列表</returns>
            public static List<PayingCompanySimple> SearchCompaniesByNameKeyword(string nameKeyword)
            {
                if (string.IsNullOrEmpty(nameKeyword))
                    return new List<PayingCompanySimple>();
                
                var allCompanies = GetAllPayingCompanies();
                if (allCompanies == null || allCompanies.Count == 0)
                    return new List<PayingCompanySimple>();
                
                // 忽略大小写进行模糊匹配
                return allCompanies
                    .Where(c => !string.IsNullOrEmpty(c.PayingCompanyName) && 
                                c.PayingCompanyName.IndexOf(nameKeyword, StringComparison.OrdinalIgnoreCase) >= 0)
                    .ToList();
            }

            /// <summary>
            /// 根据代付款公司名称模糊搜索匹配的合同ID列表
            /// </summary>
            /// <param name="nameKeyword">公司名称关键词</param>
            /// <returns>匹配的所有合同ID列表</returns>
            public static List<string> SearchContractIdsByNameKeyword(string nameKeyword)
            {
                if (string.IsNullOrEmpty(nameKeyword))
                    return new List<string>();
                
                var matchedCompanies = SearchCompaniesByNameKeyword(nameKeyword);
                if (matchedCompanies.Count == 0)
                    return new List<string>();
                
                // 提取并去重合同ID
                return matchedCompanies
                    .Where(c => !string.IsNullOrEmpty(c.ContractId))
                    .Select(c => c.ContractId)
                    .Distinct()
                    .ToList();
            }

            /// <summary>
            /// 根据代付款公司名称模糊搜索，返回匹配结果分组
            /// </summary>
            /// <param name="nameKeyword">公司名称关键词</param>
            /// <returns>按公司名称分组的合同ID字典</returns>
            public static Dictionary<string, List<string>> SearchCompanyNameToContractIdsMap(string nameKeyword)
            {
                if (string.IsNullOrEmpty(nameKeyword))
                    return new Dictionary<string, List<string>>();
                
                var matchedCompanies = SearchCompaniesByNameKeyword(nameKeyword);
                if (matchedCompanies.Count == 0)
                    return new Dictionary<string, List<string>>();
                
                // 按公司名称分组
                Dictionary<string, List<string>> result = new Dictionary<string, List<string>>();
                
                foreach (var company in matchedCompanies)
                {
                    if (string.IsNullOrEmpty(company.PayingCompanyName) || string.IsNullOrEmpty(company.ContractId))
                        continue;
                    
                    if (!result.ContainsKey(company.PayingCompanyName))
                    {
                        result[company.PayingCompanyName] = new List<string>();
                    }
                    
                    if (!result[company.PayingCompanyName].Contains(company.ContractId))
                    {
                        result[company.PayingCompanyName].Add(company.ContractId);
                    }
                }
                
                return result;
            }
        }
    }
} 