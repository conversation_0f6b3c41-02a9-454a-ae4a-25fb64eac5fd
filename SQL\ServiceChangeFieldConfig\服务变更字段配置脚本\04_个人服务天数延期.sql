-- 服务变更字段配置 - 个人服务天数延期 (ChangeReasonEnum = 3)
-- 执行日期: 2025-01-29

-- ================================
-- 个人服务天数延期 - 申请场景字段
-- ================================

-- 个人服务天数延期字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 3, 'PersonalServiceDays', '个人服务天数', 10, 1, 1, 'apply', NULL, '个人服务天数延期时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 3, 'SelfPaidDays', '自费天数', 11, 1, 1, 'apply', NULL, '个人服务天数延期时申请可修改', NOW(), 'system', NULL, 'system', 0);

-- GTIS服务申请时只读字段（时间相关）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 1, 3, 'GtisServiceMonth', 'GTIS服务开通月数', 20, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 3, 'GtisServiceStart', 'GTIS服务开始日期', 21, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 3, 'GtisServiceEnd', 'GTIS服务结束日期', 22, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0);

-- 环球搜服务申请时只读字段（时间相关）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 3, 3, 'GlobalSearchServiceMonth', '环球搜服务开通月数', 30, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 3, 'GlobalSearchServiceStart', '环球搜服务开始日期', 31, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 3, 'GlobalSearchServiceEnd', '环球搜服务结束日期', 32, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0);

-- 慧思学院服务申请时只读字段（时间相关）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 4, 3, 'CollegeServiceMonth', '慧思学院服务开通月数', 40, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 3, 'CollegeServiceStart', '慧思学院服务开始日期', 41, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 3, 'CollegeServiceEnd', '慧思学院服务结束日期', 42, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0);

-- SalesWits服务申请时只读字段（时间相关）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 2, 3, 'SalesWitsServiceMonth', 'SalesWits服务开通月数', 50, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 3, 'SalesWitsServiceStart', 'SalesWits服务开始日期', 51, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 3, 'SalesWitsServiceEnd', 'SalesWits服务结束日期', 52, 1, 2, 'apply', NULL, '个人服务天数延期时申请只读', NOW(), 'system', NULL, 'system', 0);

-- ================================
-- 个人服务天数延期 - 审核场景字段
-- ================================

-- 个人服务天数延期字段（审核场景）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 3, 'PersonalServiceDays', '个人服务天数', 60, 1, 1, 'audit', 'GtisServiceStart,GtisServiceEnd,GtisServiceMonth,GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,CollegeServiceStart,CollegeServiceEnd,CollegeServiceMonth,SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 3, 'SelfPaidDays', '自费天数', 61, 1, 1, 'audit', 'GtisServiceStart,GtisServiceEnd,GtisServiceMonth,GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,CollegeServiceStart,CollegeServiceEnd,CollegeServiceMonth,SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- GTIS服务审核时可修改字段（时间相关）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 1, 3, 'GtisServiceMonth', 'GTIS服务开通月数', 70, 1, 1, 'audit', 'GtisServiceStart,GtisServiceEnd,GtisServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 3, 'GtisServiceStart', 'GTIS服务开始日期', 71, 1, 1, 'audit', 'GtisServiceStart,GtisServiceEnd,GtisServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 3, 'GtisServiceEnd', 'GTIS服务结束日期', 72, 1, 1, 'audit', 'GtisServiceStart,GtisServiceEnd,GtisServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- 环球搜服务审核时可修改字段（时间相关）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 3, 3, 'GlobalSearchServiceMonth', '环球搜服务开通月数', 80, 1, 1, 'audit', 'GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 3, 'GlobalSearchServiceStart', '环球搜服务开始日期', 81, 1, 1, 'audit', 'GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 3, 'GlobalSearchServiceEnd', '环球搜服务结束日期', 82, 1, 1, 'audit', 'GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- 慧思学院服务审核时可修改字段（时间相关）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 4, 3, 'CollegeServiceMonth', '慧思学院服务开通月数', 90, 1, 1, 'audit', 'CollegeServiceStart,CollegeServiceEnd,CollegeServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 3, 'CollegeServiceStart', '慧思学院服务开始日期', 91, 1, 1, 'audit', 'CollegeServiceStart,CollegeServiceEnd,CollegeServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 3, 'CollegeServiceEnd', '慧思学院服务结束日期', 92, 1, 1, 'audit', 'CollegeServiceStart,CollegeServiceEnd,CollegeServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- SalesWits服务审核时可修改字段（时间相关）
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 2, 3, 'SalesWitsServiceMonth', 'SalesWits服务开通月数', 100, 1, 1, 'audit', 'SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 3, 'SalesWitsServiceStart', 'SalesWits服务开始日期', 101, 1, 1, 'audit', 'SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 3, 'SalesWitsServiceEnd', 'SalesWits服务结束日期', 102, 1, 1, 'audit', 'SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth,PersonalServiceDays,SelfPaidDays', '个人服务天数延期时审核可修改', NOW(), 'system', NULL, 'system', 0);

SELECT '个人服务天数延期配置完成！' AS message; 