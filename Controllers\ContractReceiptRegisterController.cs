﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.IO;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using System.Web;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;
using static CRM2_API.Model.ControllersViewModel.VM_SalesRules;
using CRM2_API.Common.Filter;

namespace CRM2_API.Controllers
{
    [Description("到账业绩")]
    public class ContractReceiptRegisterController : MyControllerBase
    {
        public ContractReceiptRegisterController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 添加到账登记
        /// </summary>
        [HttpPost]
        public void AddReceiptRegister(AddReceiptRegister_In addReceiptRegisterIn)
        {
            BLL_ContractReceiptRegister.Instance.AddReceiptRegister(addReceiptRegisterIn);
        }

        /// <summary>
        /// 修改到账登记
        /// </summary>
        [HttpPost]
        public void UpdateReceiptRegister(UpdateReceiptRegister_In updateReceiptRegisterIn)
        {
            BLL_ContractReceiptRegister.Instance.UpdateReceiptRegister(updateReceiptRegisterIn);
        }

        /// <summary>
        /// 修改已确认的到账登记
        /// </summary>
        [HttpPost]
        public void UpdateConfirmedReceiptRegister(UpdateReceiptRegister_In updateReceiptRegisterIn)
        {
            BLL_ContractReceiptRegister.Instance.UpdateConfirmedReceiptRegister(updateReceiptRegisterIn);
        }

        /// <summary>
        /// 到账登记复核
        /// </summary>
        [HttpPost]
        public void AuditContractReceiptRegister(AuditContractReceiptRegister_In auditContractReceiptRegisterIn)
        {
            BLL_ContractReceiptRegister.Instance.AuditContractReceiptRegister(auditContractReceiptRegisterIn);
        }

        /// <summary>
        /// 获取待锁定到账登记信息
        /// </summary>
        [HttpPost]
        public List<GetTobeLockContractReceiptRegister_Out> GetTobeLockContractReceiptRegister(GetTobeLockContractReceiptRegister_In getTobeLockContractReceiptRegisterIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetTobeLockContractReceiptRegister(getTobeLockContractReceiptRegisterIn);
        }

        /// <summary>
        /// 获取已锁定到账登记信息
        /// </summary>
        [HttpPost]
        public List<GetLockedContractReceiptRegister_Out> GetLockedContractReceiptRegister(GetLockedContractReceiptRegister_In getLockedContractReceiptRegisterIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetLockedContractReceiptRegister(getLockedContractReceiptRegisterIn);
        }

        /// <summary>
        /// 锁定合同到账登记业绩审核信息
        /// </summary>
        [HttpPost]
        public void LockContractReceiptRegisterAchievement(List<string> ids)
        {
            BLL_ContractReceiptRegister.Instance.LockContractReceiptRegisterAchievement(ids);
        }

        /// <summary>
        /// 解除锁定合同到账登记业绩审核信息
        /// </summary>
        [HttpPost]
        public void UnLockContractReceiptRegisterAchievement(List<string> ids)
        {
            BLL_ContractReceiptRegister.Instance.UnLockContractReceiptRegisterAchievement(ids);
        }

        /// <summary>
        /// 添加项目其他信息
        /// </summary>
        [HttpPost]
        public Guid AddProjectInfoOtherItem(ProjectInfoOtherItem_In projectInfoOtherItemIn)
        {
            return BLL_ContractReceiptRegister.Instance.AddProjectInfoOtherItem(projectInfoOtherItemIn);
        }

        /// <summary>
        /// 获取项目其他信息列表
        /// </summary>
        [HttpPost]
        public List<ProjectInfoOtherItemList_Out> GetProjectInfoOtherItemList()
        {
            return BLL_ContractReceiptRegister.Instance.GetProjectInfoOtherItemList();
        }

        /// <summary>
        /// 获取项目信息列表
        /// </summary>
        [HttpPost]
        public List<ProjectInfoList_Out> GetProjectInfoList()
        {
            return BLL_ContractReceiptRegister.Instance.GetProjectInfoList();
        }

        /// <summary>
        /// 根据匹配待登记Id获取匹配待登记信息
        /// </summary>
        [HttpPost]
        public MatchingRegistered_Out GetMatchingRegisteredById(string id)
        {
            return BLL_ContractReceiptRegister.Instance.GetMatchingRegisteredById(id);
        }

        /// <summary>
        /// 根据匹配待登记Id拆分匹配待登记信息
        /// </summary>
        [HttpPost]
        public void SplitMatchingRegisteredById(SplitMatchingRegisteredIn splitMatchingRegisteredIn)
        {
            BLL_ContractReceiptRegister.Instance.SplitMatchingRegisteredById(splitMatchingRegisteredIn);
        }

        /// <summary>
        /// 根据查询条件获取合同到账登记列表
        /// </summary>
        [HttpPost]
        public ApiTableOut<SearchContractReceiptRegisterList_Out> SearchContractReceiptRegisterList(SearchContractReceiptRegisterList_In searchContractReceiptRegisterListIn)
        {
            return BLL_ContractReceiptRegister.Instance.SearchContractReceiptRegisterList(searchContractReceiptRegisterListIn);
        }

        /// <summary>
        /// 根据查询条件获取合同到账登记恭喜确认列表
        /// </summary>
        [HttpPost]
        public ApiTableOut<SearchContractReceiptRegisterCongratulationsList_Out> SearchContractReceiptRegisterCongratulationsList(SearchContractReceiptRegisterCongratulationsList_In searchContractReceiptRegisterListIn)
        {
            return BLL_ContractReceiptRegister.Instance.SearchContractReceiptRegisterCongratulationsList(searchContractReceiptRegisterListIn);
        }

        /// <summary>
        /// 根据查询条件获取合同到账登记列表合计
        /// </summary>
        [HttpPost]
        public SearchContractReceiptRegisterList_Sta_Out SearchContractReceiptRegisterSta(SearchContractReceiptRegisterList_In searchContractReceiptRegisterListIn)
        {
            return BLL_ContractReceiptRegister.Instance.SearchContractReceiptRegisterSta(searchContractReceiptRegisterListIn);
        }

        /// <summary>
        /// 根据到账登记Id获取到账登记信息
        /// </summary>
        [HttpPost]
        public ReceiptRegister_Out GetReceiptRegisterById(string id)
        {
            return BLL_ContractReceiptRegister.Instance.GetReceiptRegisterById(id);
        }

        /// <summary>
        /// 撤销到账登记，当确认状态为待确认、待复核、被驳回时，可返回上一个状态，返回后可重新进行操作。
        /// </summary>
        [HttpPost]
        public void RevokeReceiptRegister(string id)
        {
            BLL_ContractReceiptRegister.Instance.RevokeReceiptRegister(id);
        }

        /// <summary>
        /// 根据合同id获取到账登记信息
        /// </summary>
        [HttpPost]
        public ApiTableOut<GetReceiptRegisterByContractId_Out> GetReceiptRegisterByContractId(GetReceiptRegisterByContractId_In GetReceiptRegisterByContractIdIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetReceiptRegisterByContractId(GetReceiptRegisterByContractIdIn);
        }

        /// <summary>
        /// 根据客户id和到账日期获取到账登记信息
        /// </summary>
        [HttpPost]
        public ApiTableOut<GetReceiptRegisterByCustomerId_Out> GetReceiptRegisterByCustomerId(GetReceiptRegisterByCustomerId_In getReceiptRegisterByCustomerIdIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetReceiptRegisterByCustomerId(getReceiptRegisterByCustomerIdIn);
        }

        /// <summary>
        /// 根据甲方公司和到账日期获取到账登记信息
        /// </summary>
        [HttpPost]
        public ApiTableOut<GetReceiptRegisterByFirstParty_Out> GetReceiptRegisterByFirstParty(GetReceiptRegisterByFirstParty_In getReceiptRegisterByFirstPartyIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetReceiptRegisterByFirstParty(getReceiptRegisterByFirstPartyIn);
        }

        /// <summary>
        /// 根据合同id获取回款金额和退款信息
        /// </summary>
        [HttpPost]
        public GetReceiptRegisterArrivalAmountInfoByContractId_Out GetReceiptRegisterArrivalAmountInfoByContractId(string contractId)
        {
            return BLL_ContractReceiptRegister.Instance.GetReceiptRegisterArrivalAmountInfoByContractId(contractId);
        }
        /// <summary>
        /// 根据到账登记表Id获取业绩确认详情信息（登记/复核用）
        /// </summary>
        /// <param name="contractReceiptRegisterId"></param>
        /// <returns></returns>
        [HttpPost]
        public ContractReceiptRegisterInfo_Out GetInfoByContractReceiptRegisterIdForAdmin(string contractReceiptRegisterId)
        {
            return BLL_ContractAchievement.Instance.GetInfoByContractReceiptRegisterId(contractReceiptRegisterId);
        }

        /// <summary>
        /// 获取待确认到账登记信息
        /// </summary>
        [HttpPost]
        public List<GetTobeConfirmedContractReceiptRegister_Out> GetTobeConfirmedContractReceiptRegister(GetTobeConfirmedContractReceiptRegister_In getTobeConfirmedContractReceiptRegisterIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetTobeConfirmedContractReceiptRegister(getTobeConfirmedContractReceiptRegisterIn);
        }

        /// <summary>
        /// 根据合同id获取已确认到账登记款项信息
        /// </summary>
        [HttpPost]
        public List<GetConfirmedReceiptDetailsByContractId_Out> GetConfirmedReceiptDetailsByContractId(string contractId)
        {
            return BLL_ContractReceiptRegister.Instance.GetConfirmedReceiptDetailsByContractId(contractId);
        }

        /// <summary>
        /// 删除到账登记，当状态为待登记可进行操作
        /// </summary>
        [HttpPost]
        public void DeleteReceiptRegister(string id, int type)
        {
            BLL_ContractReceiptRegister.Instance.DeleteReceiptRegister(id, type);
        }

        /// <summary>
        /// 获取合同到账登记状态字典
        /// </summary>
        [HttpPost]
        public List<Dictionary_Out> GetReceiptRegisterStateList()
        {
            return BLL_ContractReceiptRegister.Instance.GetReceiptRegisterStateList();
        }

        /// <summary>
        /// 恭喜确认到账登记
        /// </summary>
        [HttpPost]
        public void CongratulationsReceiptRegister(string id)
        {
            BLL_ContractReceiptRegister.Instance.CongratulationsReceiptRegister(id);
        }

        /// <summary>
        /// 撤销恭喜确认到账登记
        /// </summary>
        [HttpPost]
        public void RevokeCongratulationsReceiptRegister(string id)
        {
            BLL_ContractReceiptRegister.Instance.RevokeCongratulationsReceiptRegister(id);
        }

        /// <summary>
        /// 获取恭喜确认产品信息
        /// </summary>
        [HttpPost]
        public List<CongratulationsProductInfo_Out> GetCongratulationsProductInfoList(string ContractId)
        {
            return BLL_ContractReceiptRegister.Instance.GetCongratulationsProductInfoList(ContractId);
        }

        /// <summary>
        /// 根据合同id和银行到账信息获取恭喜确认信息
        /// </summary>
        [HttpPost]
        public List<Congratulations_Out> GetCongratulationsList(CongratulationsList_In congratulationsListIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetCongratulationsList(congratulationsListIn);
        }

        /// <summary>
        /// 根据合同id和银行到账信息获取恭喜确认信息
        /// </summary>
        [HttpPost]
        public string GetCongratulationsString(CongratulationsString_In congratulationsStringIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetCongratulationsString(congratulationsStringIn);
        }

        /// <summary>
        /// 到账业绩导出
        /// </summary>
        /// <param name="searchContractReceiptRegisterListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DownloadContractReceiptRegisterList(SearchContractReceiptRegisterList_In searchContractReceiptRegisterListIn)
        {
            Stream result = BLL_ContractReceiptRegister.Instance.DownloadContractReceiptRegisterList(searchContractReceiptRegisterListIn);
            string encodeFilename = HttpUtility.UrlEncode("到账登记导出", Encoding.GetEncoding("UTF-8"));
            Response.Headers.Add("download-name", HttpUtility.UrlEncode(encodeFilename + ".xlsx"));
            Response.Headers.Add("Access-Control-Expose-Headers", "download-name");
            Response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename + ".xlsx");
            return new FileStreamResult(result, "application/octet-stream");
        }

        /// <summary>
        /// 根据合同id获取优惠券
        /// </summary>
        [HttpPost]
        public List<Coupon_Out> GetCouponList(CouponList_In couponListIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetCouponList(couponListIn);
        }

        /// <summary>
        /// 根据合同id和银行到账id获取环球搜结算单位信息
        /// </summary>
        [HttpPost]
        public GetGlobalSearchUnitList_Out GetGlobalSearchUnitByContractIdAndCollectioninfoId(GetGlobalSearchUnitList_In globalSearchUnitIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetGlobalSearchUnitByContractIdAndCollectioninfoId(globalSearchUnitIn);
        }

        /// <summary>
        /// 添加环球搜结算修改信息
        /// </summary>
        [HttpPost, SkipRightCheck]
        public SalesRulesResult AddGlobalSearchUnitHistory(AddGlobalSearchUnitHis_In addGlobalSearchUnit)
        {
            return BLL_ContractReceiptRegister.Instance.AddGlobalSearchUnitHistory(addGlobalSearchUnit);
        }

        /// <summary>
        /// 查询环球搜结算单位修改历史
        /// </summary>
        [HttpPost, SkipRightCheck]
        public ApiTableOut<SearcrGlobalSearchUnitHis_Out> SearchGlobalSearchUnitHistory(SearchGlobalSearchUnitHis_In searchGlobalSearchUnitHis_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_receiptregister_globalunit.Instance.SearchGlobalSearchUnitHistory(searchGlobalSearchUnitHis_In, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 添加 调整开票权限 记录
        /// </summary>
        /// <param name="addAllowInvoicing_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public SalesRulesResult AddIsAllowInvoicingRemark(AddAllowInvoicing_In addAllowInvoicing_In)
        {
            return BLL_ContractReceiptRegister.Instance.AddIsAllowInvoicingRemark(addAllowInvoicing_In);
        }

        /// <summary>
        /// 查询 调整开票权限 记录历史
        /// </summary>
        /// <param name="allowInvoicing_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<AllowInvoicing_Out> SearchAllowIncoicingList(AllowInvoicing_In allowInvoicing_In)
        {
            return DbOpe_crm_contract_receiptregister_allowIncoicinghis.Instance.SearchAllowIncoicingList(allowInvoicing_In);
        }
        /// <summary>
        /// 验证 更新
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public void TestNew()
        {
            return;
        }

        /// <summary>
        /// 获取合同充值金额信息和建议分配
        /// </summary>
        /// <param name="getContractRechargeInfoIn">请求参数</param>
        /// <returns>充值金额信息和建议分配</returns>
        [HttpPost]
        public GetContractRechargeInfo_Out GetContractRechargeInfo(GetContractRechargeInfo_In getContractRechargeInfoIn)
        {
            return BLL_ContractReceiptRegister.Instance.GetContractRechargeInfo(getContractRechargeInfoIn);
        }
    }
}
