using System;
using System.Collections.Generic;
using System.IO;
using Microsoft.AspNetCore.Http;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Common.JWT;
using CRM2_API.Common.AppSetting;
using QCloud;
using CRM2_API.Model.System;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告附件处理业务逻辑
    /// </summary>
    public partial class BLL_WorkReportDatabase
    {
        /// <summary>
        /// 保存附件并返回相应的实体列表
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <param name="reportType">报告类型：1-日报，2-周报，3-月报</param>
        /// <param name="reportDate">报告日期</param>
        /// <param name="reportTitle">报告标题</param>
        /// <param name="moduleKey">模块键名</param>
        /// <param name="moduleTitle">模块标题</param>
        /// <param name="moduleOrder">模块排序</param>
        /// <param name="sectionKey">章节键名</param>
        /// <param name="sectionTitle">章节标题</param>
        /// <param name="sectionOrder">章节排序</param>
        /// <param name="attachFiles">附件文件集合</param>
        /// <returns>附件实体列表</returns>
        public List<Db_crm_report_attachment> SaveAttachments(
            string reportId,
            int reportType,
            DateTime reportDate,
            string reportTitle,
            string moduleKey,
            string moduleTitle,
            int moduleOrder,
            string sectionKey,
            string sectionTitle,
            int sectionOrder,
            IFormFileCollection attachFiles)
        {
            if (string.IsNullOrEmpty(reportId))
            {
                throw new ArgumentNullException(nameof(reportId), "报告ID不可为空!");
            }

            var attachmentList = new List<Db_crm_report_attachment>();
            
            if (attachFiles == null || attachFiles.Count == 0)
                return attachmentList;

            var currentUserId = TokenModel.Instance.id;
            var currentUserName = GetCurrentUserName();
            var currentTime = DateTime.Now;

            for (int i = 0; i < attachFiles.Count; i++)
            {
                var file = attachFiles[i];
                if (file == null || file.Length == 0)
                    continue;

                try
                {
                    var attachment = ProcessSingleFile(
                        file, reportId, reportType, reportDate, moduleKey, moduleTitle, moduleOrder,
                        sectionKey, sectionTitle, sectionOrder,
                        currentUserId, currentUserName, currentTime);

                    if (attachment != null)
                        attachmentList.Add(attachment);
                }
                catch (Exception ex)
                {
                    // 记录错误但不影响其他文件的处理
                    LogUtil.AddErrorLog($"处理附件失败：{file.FileName}，错误：{ex.Message}");
                    throw new ApiException($"处理附件失败：{file.FileName}，错误：{ex.Message}");
                }
            }

            return attachmentList;
        }

        /// <summary>
        /// 处理单个文件
        /// </summary>
        private Db_crm_report_attachment? ProcessSingleFile(
            IFormFile file,
            string reportId,
            int reportType,
            DateTime reportDate,
            string moduleKey,
            string moduleTitle,
            int moduleOrder,
            string sectionKey,
            string sectionTitle,
            int sectionOrder,
            string currentUserId,
            string currentUserName,
            DateTime currentTime)
        {
            // 生成唯一的附件ID
            string attachmentId = Guid.NewGuid().ToString();
            
            // 获取文件信息
            string originalFileName = file.FileName;
            string fileExtension = Path.GetExtension(originalFileName);
            string storageFileName = attachmentId + fileExtension;
            
            // 设置文件保存路径
            string fileSavePath = "/FileUpload";
            string fileHead = $"{fileSavePath}/WorkReports/NewReport/{DateTime.Now:yyyyMMdd}";
            string fullFilePath = $"{fileHead}/{storageFileName}";

            // 注意：数据库表中没有MD5和AttachmentType字段，这里不需要计算

            // 保存文件
            bool saveSuccess = SaveFileToStorage(file, fullFilePath);
            if (!saveSuccess)
                return null;

            // 创建附件实体
            var attachment = new Db_crm_report_attachment
            {
                Id = attachmentId,
                ReportId = reportId,
                ReportType = reportType,
                UserId = currentUserId,
                UserName = currentUserName,
                ReportDate = reportDate.Date,
                ModuleKey = moduleKey,
                ModuleTitle = moduleTitle,
                ModuleOrder = moduleOrder,
                SectionKey = sectionKey,
                SectionTitle = sectionTitle,
                SectionOrder = sectionOrder,
                FileName = originalFileName,
                FilePath = fullFilePath,
                FileSize = file.Length,
                FileType = fileExtension,
                Deleted = false,
                CreateUser = currentUserId,
                CreateDate = currentTime,
                UpdateUser = currentUserId,
                UpdateDate = currentTime
            };

            return attachment;
        }

        /// <summary>
        /// 保存文件到存储
        /// </summary>
        private bool SaveFileToStorage(IFormFile file, string fullFilePath)
        {
            try
            {
                // 读取文件字节
                byte[] fileBytes;
                using (var stream = file.OpenReadStream())
                {
                    using (var memoryStream = new MemoryStream())
                    {
                        stream.CopyTo(memoryStream);
                        fileBytes = memoryStream.ToArray();
                    }
                }

                // 检查是否启用腾讯云存储
                if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                {
                    // 上传到腾讯云
                    using (MemoryStream stream = new MemoryStream(fileBytes))
                    {
                        QCloudOperator qCloud = new QCloudOperator();
                        qCloud.UploadStream(fullFilePath, stream, file.ContentType);
                    }
                }
                else
                {
                    // 保存到本地文件系统
                    string? directoryPath = Path.GetDirectoryName(fullFilePath);
                    if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                    {
                        Directory.CreateDirectory(directoryPath);
                    }
                    File.WriteAllBytes(fullFilePath, fileBytes);
                }

                return true;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"保存文件失败：{fullFilePath}，错误：{ex.Message}");
                throw new ApiException($"保存文件失败：{fullFilePath}，错误：{ex.Message}");
            }
        }
    }
}
