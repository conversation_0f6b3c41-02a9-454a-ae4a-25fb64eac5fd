﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.ContractPaper;
using CRM2_API.Model.System;
using System.Linq;
using CRM2_API.Model.BusinessModel;
using DocumentFormat.OpenXml.Drawing.Diagrams;

namespace CRM2_API.BLL
{
    public class BLL_ContractPaper : BaseBLL<BLL_ContractPaper>
    {
        /// <summary>
        /// 添加纸质合同申领信息
        /// </summary>
        /// <param name="conPaperApply"></param>
        public void AddContractPaperApply(AddContractPaperApply_In conPaperApply)
        {
            lock (this)
            {
                //将传入参数映射到申领对象
                var paperApply = conPaperApply.MappingTo<Db_crm_contract_paperapply>();
                //生成主键
                paperApply.Id = Guid.NewGuid().ToString();
                //生成申领编号
                paperApply.ContractPaperApplyNo = (DbOpe_crm_contract_paperapply.Instance.GetQueryCount(paperApply) + 1).ToString().PadLeft(5, '0');
                //补充申领其他属性
                paperApply.ClaimantId = UserId;
                paperApply.ClaimantOrgId = DbOpe_sys_user.Instance.GetOrgIdByUserId(UserId);
                paperApply.Deleted = false;
                paperApply.CreateUser = UserId;
                paperApply.CreateDate = DateTime.Now;
                DbOpe_crm_contract_paperapply.Instance.InsertQueue(paperApply);
                var details = new List<Db_crm_contract_paperapply_detail>();
                //添加申领明细
                conPaperApply.ContractPaperApplyDetail.ForEach(it =>
                {
                    var detail = it.MappingTo<Db_crm_contract_paperapply_detail>();
                    detail.Id = Guid.NewGuid().ToString();
                    detail.ContractPaperApplyId = paperApply.Id;
                    detail.Deleted = false;
                    detail.CreateUser = UserId;
                    detail.CreateDate = DateTime.Now;
                    details.Add(detail);
                });
                DbOpe_crm_contract_paperapply_detail.Instance.InsertQueue(details);
                //判断是否提交审核，若提交审核，增加一条审核数据
                if (paperApply.State == EnumContractPaperStatus.Commit)
                {
                    var paperAudit = new Db_crm_contract_paperaudit();
                    paperAudit.Id = Guid.NewGuid().ToString();
                    paperAudit.ContractPaperApplyId = paperApply.Id;
                    paperAudit.ApplicantId = UserId;
                    paperAudit.ApplicantDate = DateTime.Now;
                    paperAudit.State = EnumContractPaperAuditStatus.Commit;
                    paperAudit.Deleted = false;
                    paperAudit.CreateUser = UserId;
                    paperAudit.CreateDate = DateTime.Now;
                    DbOpe_crm_contract_paperaudit.Instance.InsertQueue(paperAudit);
                    string dataState = Dictionary.ContractPaperAuditStatus.First(e => e.Value == paperAudit.State.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_paperaudit>("纸质合同申领审批流程", paperAudit.Id, paperAudit, conPaperApply.Remark, dataState, "新建");
                }
                //提交sql队列
                DbOpe_crm_contract_paperapply.Instance.SaveQueues();
            }
        }

        /// <summary>
        /// 修改纸质合同申领信息
        /// </summary>
        /// <param name="conPaperApply"></param>
        /// <exception cref="ApiException"></exception>
        public void UpdateContractPaperApply(UpdateContractPaperApply_In conPaperApply)
        {
            var paperApply = DbOpe_crm_contract_paperapply.Instance.QueryByPrimaryKey(conPaperApply.Id);
            if (paperApply.State != EnumContractPaperStatus.Draft && paperApply.State != EnumContractPaperStatus.Refuse)
                throw new ApiException("该申领无法修改");
            var hasAuditData = (paperApply.State == EnumContractPaperStatus.Refuse);
            paperApply = conPaperApply.MappingTo(paperApply);
            paperApply.UpdateUser = UserId;
            paperApply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_paperapply.Instance.UpdateQueue(paperApply);
            //获取修改的申领明细
            var updIds = conPaperApply.ContractPaperApplyDetail.Where(e => !string.IsNullOrEmpty(e.Id)).Select(e => e.Id).ToList();
            var updList = DbOpe_crm_contract_paperapply_detail.Instance.GetDetailsByIds(updIds);
            var updDetails = new List<Db_crm_contract_paperapply_detail>();
            updList.ForEach(it =>
            {
                var detail = conPaperApply.ContractPaperApplyDetail.Where(e => e.Id.Equals(it.Id)).First();
                it.ApplicationsNum = detail.ApplicationsNum;
                it.ContractVersion = detail.ContractVersion;
                it.UpdateDate = DateTime.Now;
                it.UpdateUser = UserId;
                updDetails.Add(it);
            });
            DbOpe_crm_contract_paperapply_detail.Instance.UpdateQueue(updDetails);
            //获取添加的明细内容
            var insList = conPaperApply.ContractPaperApplyDetail.Where(e => string.IsNullOrEmpty(e.Id)).ToList();
            var insDetails = new List<Db_crm_contract_paperapply_detail>();
            //添加申领明细
            insList.ForEach(it =>
            {
                var detail = it.MappingTo<Db_crm_contract_paperapply_detail>();
                detail.Id = Guid.NewGuid().ToString();
                detail.ContractPaperApplyId = paperApply.Id;
                detail.Deleted = false;
                detail.CreateUser = UserId;
                detail.CreateDate = DateTime.Now;
                insDetails.Add(detail);
            });
            //添加申领明细sql到执行队列
            DbOpe_crm_contract_paperapply_detail.Instance.InsertQueue(insDetails);
            //判断是否提交审核，若提交审核，增加一条审核数据
            if (paperApply.State == EnumContractPaperStatus.Commit)
            {
                var paperAudit = new Db_crm_contract_paperaudit();
                if (hasAuditData)
                {
                    paperAudit = DbOpe_crm_contract_paperaudit.Instance.GetAuditByApplyId(paperApply.Id);
                    paperAudit.State = EnumContractPaperAuditStatus.Commit;
                    paperAudit.UpdateUser = UserId;
                    paperAudit.UpdateDate = DateTime.Now;
                    DbOpe_crm_contract_paperaudit.Instance.UpdateQueue(paperAudit);
                }
                else
                {
                    paperAudit.Id = Guid.NewGuid().ToString();
                    paperAudit.ContractPaperApplyId = paperApply.Id;
                    paperAudit.ApplicantId = UserId;
                    paperAudit.ApplicantDate = DateTime.Now;
                    paperAudit.State = EnumContractPaperAuditStatus.Commit;
                    paperAudit.Deleted = false;
                    paperAudit.CreateUser = UserId;
                    paperAudit.CreateDate = DateTime.Now;
                    DbOpe_crm_contract_paperaudit.Instance.InsertQueue(paperAudit);
                }
                string dataState = Dictionary.ContractPaperAuditStatus.First(e => e.Value == paperAudit.State.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_paperaudit>("纸质合同申领审批流程", paperAudit.Id, paperAudit, conPaperApply.Remark, dataState, "修改");
            }
            //提交sql队列
            DbOpe_crm_contract_paperapply.Instance.SaveQueues();
        }

        /// <summary>
        /// 删除纸质合同申领信息
        /// </summary>
        /// <param name="Ids"></param>
        public void DeleteContractPaperApply(string Ids)
        {
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("未选择纸质合同申领数据");
            var idList = Ids.Split(',').ToList();
            //判断申领数据的状态是否都是草稿或拒绝
            if (DbOpe_crm_contract_paperapply.Instance.CheckApplyDelete(idList))
                throw new ApiException("所选的数据不可删除");
            //删除申领数据
            DbOpe_crm_contract_paperapply.Instance.DeleteApply(idList, UserId);
            //删除申领明细数据
            DbOpe_crm_contract_paperapply_detail.Instance.DeleteApplyDetail(idList, UserId);
            //提交sql队列
            DbOpe_crm_contract_paperapply.Instance.SaveQueues();
        }

        /// <summary>
        /// 撤销纸质合同申领信息
        /// </summary>
        /// <param name="Ids"></param>
        /// <exception cref="ApiException"></exception>
        public void RevokeContractPaperApply(string Ids)
        {
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("未选择纸质合同申领数据");
            var idList = Ids.Split(',').ToList();
            //判断申领数据的状态是否都提交
            if (DbOpe_crm_contract_paperapply.Instance.CheckApplyRevoke(idList))
                throw new ApiException("所选的数据不可撤销");
            //撤销纸质合同申领
            DbOpe_crm_contract_paperapply.Instance.RevokeApply(idList, UserId);
            //删除纸质合同申领审核
            DbOpe_crm_contract_paperaudit.Instance.DeleteAudit(idList, UserId);
            //提交sql队列
            DbOpe_crm_contract_paperapply.Instance.SaveQueues();
        }

        /// <summary>
        /// 根据纸质合同申领信息Id获取纸质合同申领信息
        /// </summary>
        /// <param name="Id"></param>
        public GetContractPaperApplyInfoById_Out GetContractPaperApplyInfoById(string Id)
        {
            return null;
        }

        /// <summary>
        /// 审核纸质合同申领信息
        /// </summary>
        /// <param name="auditApply_In"></param>
        public void AuditContractPaperApply(AuditContractPaperApply_In auditApply_In)
        {
            //获取当前的审核数据
            var audit = DbOpe_crm_contract_paperaudit.Instance.QueryByPrimaryKey(auditApply_In.Id);
            if (audit.State != EnumContractPaperAuditStatus.Commit)
                throw new ApiException("当前纸质合同申领不是提交状态，无法审核");
            //获取申领的数据
            var apply = DbOpe_crm_contract_paperaudit.Instance.GetApplyByAuditId(auditApply_In.Id);
            if (apply.State != EnumContractPaperStatus.Commit)
                throw new ApiException("当前纸质合同申领不是提交状态，无法审核");
            //判断审核结果是通过or拒绝
            if (auditApply_In.State == EnumContractPaperAuditStatus.Refuse)
                apply.State = EnumContractPaperStatus.Refuse;//修改申领数据的状态为拒绝状态
            else if (auditApply_In.State == EnumContractPaperAuditStatus.Agree)
            {
                apply.State = EnumContractPaperStatus.Agree;//修改申领数据的状态为通过状态
                //处理合同号，自动生成并验证唯一
                var contractNoList = new List<string>();
                auditApply_In.ContractPaperApplyDetail.ForEach(it =>
                {
                    //验证开始合同号是否是6-10位数字
                    if (!RegexUtil.IsNumber(it.ContractNoStart) || !(it.ContractNoStart.Length >= 6) || !(it.ContractNoStart.Length <= 10))
                        throw new ApiException("开始合同号格式错误，请重新核对");
                    //获取开始合同号位数
                    var len = it.ContractNoStart.Length;
                    var contractEnd = (it.ContractNoStart.ToLong() - 1 + it.ProvideNum).ToString().PadLeft(len, '0');
                    //判断结束合同号是否超过位数限制
                    if (contractEnd.Length > 10)
                        throw new ApiException("结束合同号位数超过位数限制，请重新核对");
                    //结束合同号是空
                    if (string.IsNullOrEmpty(it.ContractNoEnd))
                        it.ContractNoEnd = contractEnd;
                    else//结束合同号非空
                    {
                        //验证结束合同号是否是6-10位数字
                        if (!RegexUtil.IsNumber(it.ContractNoEnd) || !(it.ContractNoEnd.Length >= 6) || !(it.ContractNoEnd.Length <= 10))
                            throw new ApiException("结束合同号格式错误，请重新核对");
                        //结束合同号与实发份数不符
                        if (it.ContractNoEnd.ToInt() + 1 - it.ContractNoStart.ToInt() != it.ProvideNum)
                            throw new ApiException("结束合同号与实发份数不符，请重新核对");
                        //结束合同号位数不符
                        if (it.ContractNoEnd.Length != contractEnd.Length)
                            throw new ApiException("结束合同号位数与开始合同号位数不符，请重新核对");
                    }
                    //校验合同号是否重复
                    for (int i = it.ContractNoStart.ToInt(); i <= it.ContractNoEnd.ToInt(); i++)
                    {
                        if (contractNoList.Any(e => e == i.ToString().PadLeft(len, '0')))
                            throw new ApiException("存在重复的合同号，请重新核对填写");
                        else
                            contractNoList.Add(i.ToString().PadLeft(len, '0'));
                    }
                    DbOpe_crm_contract_paperapply_detail.Instance.UpdateDetailContractNo(it, UserId);
                });
                //验证填写的合同号是否已经被使用,作废和删除的合同号可以重复使用
                if (DbOpe_crm_contract_paper_entity.Instance.CheckPaperContractNoRepeat(contractNoList))
                    throw new ApiException("存在重复的合同号，请重新核对填写");
                //根据审核Id获取申领Id
                var applyId = DbOpe_crm_contract_paperaudit.Instance.GetApplyIdByAuditId(auditApply_In.Id);
                //循环创建纸质合同实体
                auditApply_In.ContractPaperApplyDetail.ForEach(it =>
                {
                    //创建纸质合同分发,默认分发给申领人
                    var distribute = new Db_crm_contract_paper_distribute();
                    distribute.Id = Guid.NewGuid().ToString();
                    distribute.ContractPaperApplyDetailId = it.Id;
                    distribute.ContractPaperApplyId = applyId;
                    distribute.IssuerId = UserId;
                    distribute.RecipientId = audit.ApplicantId;
                    distribute.ReceivedNum = it.ProvideNum;
                    distribute.ContractNoStart = it.ContractNoStart;//.PadLeft(8, '0');
                    distribute.ContractNoEnd = it.ContractNoEnd;//.PadLeft(8, '0');
                    distribute.Deleted = false;
                    distribute.CreateUser = UserId;
                    distribute.CreateDate = DateTime.Now;
                    DbOpe_crm_contract_paper_distribute.Instance.InsertQueue(distribute);

                    var len = it.ContractNoStart.Length;
                    for (int i = it.ContractNoStart.ToInt(); i <= it.ContractNoEnd.ToInt(); i++)
                    {
                        Db_crm_contract_paper_entity entity = new Db_crm_contract_paper_entity();
                        entity.Id = Guid.NewGuid().ToString();
                        entity.ContractPaperApplyId = applyId;
                        entity.ContractPaperApplyDetailId = it.Id;
                        entity.ContractNo = i.ToString().PadLeft(len, '0');
                        entity.State = EnumContractPaperEntityStatus.UnUse;
                        entity.Deleted = false;
                        entity.CreateUser = UserId;
                        entity.CreateDate = DateTime.Now;
                        DbOpe_crm_contract_paper_entity.Instance.InsertQueue(entity);
                        //绑定分发与合同实体
                        var distributeEntity = new Db_crm_contract_paper_distribute_entity();
                        distributeEntity.Id = Guid.NewGuid().ToString();
                        distributeEntity.ContractPaperDistributeId = distribute.Id;
                        distributeEntity.ContractPaperEntityId = entity.Id;
                        distributeEntity.Deleted = false;
                        distributeEntity.CreateUser = UserId;
                        distributeEntity.CreateDate = DateTime.Now;
                        DbOpe_crm_contract_paper_distribute_entity.Instance.InsertQueue(distributeEntity);
                    }
                });
            }
            //补充申领数据的更新时间和更新人
            apply.UpdateDate = DateTime.Now;
            apply.UpdateUser = UserId;
            //修改申领数据
            DbOpe_crm_contract_paperapply.Instance.UpdateQueue(apply);
            //修改审核数据
            DbOpe_crm_contract_paperaudit.Instance.AuditContractPaper(auditApply_In, UserId);
            string dataState = Dictionary.ContractPaperAuditStatus.First(e => e.Value == auditApply_In.State.ToInt().ToString()).Name;
            BLL_WorkFlow.Instance.AddWorkFlow<AuditContractPaperApply_In>("纸质合同申领审批流程", auditApply_In.Id, auditApply_In, auditApply_In.Feedback, dataState, "审核");
            //提交
            DbOpe_crm_contract_paperapply.Instance.SaveQueues();
        }

        /// <summary>
        /// 撤销审核纸质合同申领信息
        /// </summary>
        /// <param name="auditIds"></param>
        public void RevokeAuditContractPaperApply(string auditIds)
        {
            if (string.IsNullOrEmpty(auditIds))
                throw new ApiException("未选择要撤销的申领信息");
            List<string> auditIdList = auditIds.Split(',').ToList();
            if (!DbOpe_crm_contract_paperaudit.Instance.CheckCanRevoke(auditIdList))
                throw new ApiException("当前审核不可以撤销");
            //根据审核Id获取申领Id
            var applyIds = DbOpe_crm_contract_paperaudit.Instance.GetApplyIdByAuditId(auditIdList);
            //查询审核对应的合同
            var entityList = DbOpe_crm_contract_paper_entity.Instance.GetContractPaperEntityByApplyId(applyIds);
            //检索未使用的合同的Id
            var unUseEntityList = entityList.Where(e => e.State == EnumContractPaperEntityStatus.UnUse).Select(e => e.Id).ToList();
            //作废纸质合同
            DbOpe_crm_contract_paper_entity.Instance.DeleteContractPaperEntity(unUseEntityList, UserId);
            //处理paperapply_detail:清空 合同开始号、合同结束号、实发份数
            DbOpe_crm_contract_paperapply_detail.Instance.ClearAuditDataWhenRevoke(applyIds, UserId);
            //审核退到提交
            DbOpe_crm_contract_paperaudit.Instance.RevokeAuditContractPaperAudit(auditIdList, UserId);
            //申领退为提交
            DbOpe_crm_contract_paperapply.Instance.RevokeAuditContractPaperApply(applyIds, UserId);
            //获取当前的审核数据列表
            var old_auditList = DbOpe_crm_contract_paperaudit.Instance.GetAuditListByAuditIds(auditIdList);
            //提交
            DbOpe_crm_contract_paperapply.Instance.SaveQueues();
            //撤销已发送的消息
            var auditList = DbOpe_crm_contract_paperaudit.Instance.GetAuditListByAuditIds(auditIdList);
            auditList.ForEach(audit =>
            {
                //Db_crm_contract_paperapply audit = DbOpe_crm_contract_paperaudit.Instance.GetApplyByAuditId(auditId);
                //string dataState = Dictionary.ContractPaperAuditStatus.First(e => e.Value == audit.State.ToInt().ToString()).Name;
                string dataState = old_auditList.Where(e => e.Id == audit.Id).First().State.GetEnumDescription();
                BLL_WorkFlow.Instance.CancelWorkflowPending("纸质合同申领审批流程", audit.Id, dataState, audit);
            });
        }
        /// <summary>
        /// 作废审核纸质合同申领信息
        /// </summary>
        /// <param name="auditIds"></param>
        public void VoidAuditContractPaperApply(string auditIds)
        {
            if (string.IsNullOrEmpty(auditIds))
                throw new ApiException("未选择要作废的申领信息");
            List<string> auditIdList = auditIds.Split(',').ToList();

            if (!DbOpe_crm_contract_paperaudit.Instance.CheckCanVoid(auditIdList))
                throw new ApiException("当前审核不可以作废");
            //根据审核Id获取申领Id
            var applyIds = DbOpe_crm_contract_paperaudit.Instance.GetApplyIdByAuditId(auditIdList);
            //查询审核对应的合同
            var entityList = DbOpe_crm_contract_paper_entity.Instance.GetContractPaperEntityByApplyId(applyIds);
            //检索未使用的合同的Id
            var unUseEntityList = entityList.Where(e => e.State == EnumContractPaperEntityStatus.UnUse).Select(e => e.Id).ToList();
            //作废纸质合同
            DbOpe_crm_contract_paper_entity.Instance.VoidContractPaperEntity(unUseEntityList, UserId);
            //作废审核
            DbOpe_crm_contract_paperaudit.Instance.VoidAuditContractPaperAudit(auditIdList, UserId);
            //作废申领
            DbOpe_crm_contract_paperapply.Instance.VoidAuditContractPaperApply(auditIdList, UserId);
            //提交
            DbOpe_crm_contract_paperapply.Instance.SaveQueues();
        }

        /// <summary>
        /// 维护(新增保存或修改)纸质合同分发信息
        /// </summary>
        /// <param name="distributeList_In"></param>
        public void DistributeContractPaper(List<DistributeContractPaper_In> distributeList_In)
        {
            //查询明细表的数据
            var detailList = DbOpe_crm_contract_paperapply_detail.Instance.GetDetailsByIds(distributeList_In.Select(e => e.ContractPaperApplyDetailId).Distinct().ToList());
            //根据明细表Id获取对应的未使用的合同
            var contractList = DbOpe_crm_contract_paper_entity.Instance.GetContractPaperEntityByDetailIds(detailList.Select(e => e.Id).ToList());
            var distributeList = new List<Db_crm_contract_paper_distribute>();
            //循环明细表数据
            detailList.ForEach(detail =>
            {
                //获取匹配明细Id的输入数据列表
                var list = distributeList_In.Where(e => e.ContractPaperApplyDetailId == detail.Id && e.ReceivedNum != 0 && e.ReceivedNum != null).ToList();
                //取detail对应的未使用的纸质合同号，同时根据Int格式化后的contractNo进行排序
                var detailContractList = contractList.Where(e => e.ContractPaperApplyDetailId == detail.Id)
                .Select(e => new DistributeContractPaper4SortByContractNo
                {
                    Id = e.Id,
                    ContractNo = e.ContractNo,
                    ContractNo4Sort = e.ContractNo.ToInt()
                }).OrderBy(e => e.ContractNo4Sort).ToList();

                //取待分发的合同当前对应的分发数据列表,此刻获得完整的分发数据,和detailList循环结尾的 distributeList_Rest 对应进行分发数据操作
                var distributeIdList_Before = DbOpe_crm_contract_paper_distribute.Instance.GetDistributeListByContractEntityIds(detailContractList.Select(e => e.Id).ToList());

                //验证数量是否匹配：分发的合同数量不能超过可供分发的合同实体的数量
                if (list.Sum(e => e.ReceivedNum).ToInt() > detailContractList.Count)
                    throw new ApiException("实发份数超过申领份数，请重新核对");
                //循环匹配明细Id的输入数据列表，插入分发数据和分发实体关系数据
                list.ForEach(distribute_In =>
                {
                    var receivedNum = distribute_In.ReceivedNum == null ? 0 : distribute_In.ReceivedNum.ToInt();
                    //截取可分发合同号列表中的前ReceivedNum位，为这次list循环的可使用合同号
                    var distributeContractList = detailContractList.GetRange(0, receivedNum);
                    //新建分发数据
                    var distribute = distribute_In.MappingTo<Db_crm_contract_paper_distribute>();
                    distribute.ContractPaperApplyDetailId = detail.Id;
                    distribute.ContractPaperApplyId = detail.ContractPaperApplyId;
                    distribute.IssuerId = UserId;
                    distribute.RecipientId = distribute_In.RecipientId;
                    distribute.ContractNoStart = distributeContractList.Select(e => e.ContractNo).First();//分发的开始合同号，取可使用合同号列表的首位
                    distribute.ContractNoEnd = distributeContractList.Select(e => e.ContractNo).Last();//分发的结束合同号，取可使用合同号列表的末位
                    //提交insert队列，返回分发数据主键
                    var distributeId = DbOpe_crm_contract_paper_distribute.Instance.InsertDataQueueReturnId(distribute);
                    //循环分发数量，插入分发实体关系数据
                    distributeContractList.ForEach(distributeContract =>
                    {
                        //获取合同号对应的合同实体主键Id
                        var entityId = distributeContract.Id;
                        //删除该合同号对应的分发关系数据
                        DbOpe_crm_contract_paper_distribute_entity.Instance.DeleteByEntityId(entityId, UserId);
                        //创建新的分发实体关系数据
                        var distributeEntity = new Db_crm_contract_paper_distribute_entity();
                        distributeEntity.ContractPaperDistributeId = distributeId;//绑定分发Id
                        distributeEntity.ContractPaperEntityId = entityId;//绑定实体Id
                        DbOpe_crm_contract_paper_distribute_entity.Instance.InsertDataQueue(distributeEntity);
                    });
                    //detailContractList删除已经分发过的合同,进入下一次循环
                    detailContractList.RemoveRange(0, receivedNum);
                });
                //取剩余合同对应的分发数据列表
                var distributeIdList_Rest = DbOpe_crm_contract_paper_distribute.Instance.GetDistributeListByContractEntityIds(detailContractList.Select(e => e.Id).ToList());

                //取 distributeList_Before 与 distributeIdList_Rest 的差集，集合内容是需要删除的分发数据
                var distributeIdList_ToBeDelete = distributeIdList_Before.ExceptBy(distributeIdList_Rest.Select(e => e.Id), e => e.Id).ToList();
                //添加删除 distribute 数据的sql队列
                DbOpe_crm_contract_paper_distribute.Instance.DeleteDataQueue(distributeIdList_ToBeDelete.Select(e => e.Id).ToList());
                //此时先提交变动的分发数据
                DbOpe_crm_contract_paper_distribute.Instance.SaveQueues();
                //根据剩余合同对应的分发数据列表 distributeIdList_Rest ,获取对应的分发合同列表,并根据合同号排序,回写distribute数据
                distributeIdList_Rest.ForEach(rest =>
                {
                    var entities = DbOpe_crm_contract_paper_entity.Instance.GetContractPaperEntityByDistributeIds(rest.Id);
                    rest.ContractNoStart = entities.First().ContractNo;
                    rest.ContractNoEnd = entities.Last().ContractNo;
                    rest.ReceivedNum = entities.Count();
                    DbOpe_crm_contract_paper_distribute.Instance.UpdateDataQueue(rest);

                });
                //此时提交还存在的分发数据的修改sql
                DbOpe_crm_contract_paper_distribute.Instance.SaveQueues();

            });

        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="distributeList_In"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<GetContractPaperTobeDistributeList_Out> GetContractPaperTobeDistributeList(GetContractPaperTobeDistributeList_In distributeList_In, ref int total)
        {
            //纸质合同明细内容
            var details = DbOpe_crm_contract_paperapply_detail.Instance.GetContractPaperTobeDistributeDetailList(distributeList_In.ApplyId);
            //获取同一组织下的员工的列表
            var userList = DbOpe_sys_user.Instance.GetSameOrgUserInfoByUserId(distributeList_In, details, ref total);
            return userList;
        }
    }
}