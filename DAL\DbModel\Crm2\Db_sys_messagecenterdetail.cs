﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_messagecenterdetail")]
    public class Db_sys_messagecenterdetail
    {
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:接收人Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// Desc:消息主类型(待办/通知/其他)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? MessageType { get; set; }

        /// <summary>
        /// Desc:相关业务主键ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageTypeToId { get; set; }

        /// <summary>
        /// Desc:消息副类型(客户/合同/发票)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? MessageLocalType { get; set; }

        /// <summary>
        /// Desc:信息标题
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageTitle { get; set; }

        /// <summary>
        /// Desc:信息内容
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageDetail { get; set; }

        /// <summary>
        /// Desc:是否已读
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? IsRead { get; set; }

        /// <summary>
        /// Desc:是否删除
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? IsDelete { get; set; }

        /// <summary>
        /// Desc:路径
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageToPath { get; set; }

        /// <summary>
        /// Desc:form名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageFormName { get; set; }

        /// <summary>
        /// Desc:vx配置
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageVXPath { get; set; }

        /// <summary>
        /// Desc:钉钉消息发送状态
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? DingtalkSendState { get; set; }

        /// <summary>
        /// Desc:钉钉消息待办ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string DingtalkTaskId { get; set; }
        
        /// <summary>
        /// Desc:主数据审核意见
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string LocalFeedback { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
