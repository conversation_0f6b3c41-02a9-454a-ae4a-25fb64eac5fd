﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_contract_stampre")]
    public class Db_v_contract_stampre
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string ContractId {get;set;}

           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ElectronicContractAttachfileId {get;set;}

           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string SealedContractAttachfileId {get;set;}

    }
}
