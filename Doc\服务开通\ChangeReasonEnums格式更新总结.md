# ChangeReasonEnums 格式更新总结

## 📋 更新概述

本次更新将 `ChangeReasonEnums` 字段从原来的逗号分隔格式更新为 JSON 格式存储，同时保持向后兼容性。

## 🔄 格式变更

### 旧格式（逗号分隔）
```
"1,2,3"
```

### 新格式（JSON）
```json
{
  "ChangeReasons": [
    {
      "ChangeReason": 1,
      "ContractProductId": "product-id-1"
    },
    {
      "ChangeReason": 2,
      "ContractProductId": "product-id-2"
    }
  ]
}
```

## 📁 更新的文件

### 1. 数据访问层 (DAL)

#### `DAL/DbModelOpe/Crm2/DbOpe_crm_contract_productserviceinfo_gtis_appl.cs`
- **位置**: 第1252行
- **更新内容**: 更新 `GetApplGtisInfo` 方法中的变更原因解析逻辑
- **变更**: 从 `Split(",")` 改为 JSON 解析，增加兼容性处理

#### `DAL/DbModelOpe/Crm2/DbOpe_crm_contract_serviceinfo_gtis.cs`
- **位置**: 第1115、1167、1224、1349行
- **更新内容**: 更新多个方法中的变更原因解析逻辑
- **变更**: 所有 `Mapper` 方法中的变更原因解析都更新为 JSON 格式

### 2. 业务逻辑层 (BLL)

#### `BLL/BLL_ContractServiceGTIS.cs`
- **位置**: 第159行
- **更新内容**: 更新 `GetContractServiceInfoGtisByApplId` 方法中的变更原因解析
- **变更**: 从 `Split(",")` 改为 JSON 解析，增加兼容性处理

#### `BLL/BLL_ContractServiceGTISSeries.cs`
- **位置**: 第2214行
- **更新内容**: 更新 `GetWitsApplyInfo4Audit` 方法中的变更原因解析
- **变更**: 从 `Split(',')` 改为 JSON 解析，增加兼容性处理

### 3. 文档更新

#### `Doc/服务开通/SaleWits服务变更资源补发功能开发总结.md`
- **更新内容**: 更新示例代码中的变更原因解析逻辑
- **变更**: 从 `Split(',')` 改为 JSON 解析示例

#### `Doc/服务开通/SaleWits服务变更资源补发功能最终总结.md`
- **更新内容**: 更新示例代码中的变更原因解析逻辑
- **变更**: 从 `Split(',')` 改为 JSON 解析示例

## 🛡️ 兼容性处理

所有更新都采用了以下兼容性处理策略：

```csharp
try
{
    // 尝试解析JSON格式
    var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<dynamic>(changeReasonEnums);
    if (changeReasonData != null && changeReasonData.GetProperty("ChangeReasons").GetArrayLength() > 0)
    {
        var changeReasons = changeReasonData.GetProperty("ChangeReasons");
        foreach (var reason in changeReasons.EnumerateArray())
        {
            var changeReason = reason.GetProperty("ChangeReason").GetInt32();
            // 处理变更原因...
        }
    }
}
catch (System.Text.Json.JsonException)
{
    // JSON解析失败，尝试解析旧格式（兼容性处理）
    var changeReasonEnumsList = changeReasonEnums.Split(",").ToList();
    // 处理旧格式...
}
```

## ✅ 验证结果

- ✅ 项目编译成功
- ✅ 所有相关文件已更新
- ✅ 向后兼容性得到保证
- ✅ 文档示例代码已同步更新

## 🎯 更新效果

1. **数据存储更规范**: JSON 格式提供了更好的结构化数据存储
2. **扩展性更强**: 可以轻松添加更多字段（如产品ID、变更时间等）
3. **向后兼容**: 旧格式数据仍能正常解析
4. **代码一致性**: 所有相关代码都使用统一的解析方式

## 📝 注意事项

1. **数据迁移**: 现有数据库中的旧格式数据仍可正常使用
2. **新数据**: 新创建的变更原因数据将使用 JSON 格式
3. **性能影响**: JSON 解析相比字符串分割略有性能开销，但影响微乎其微
4. **错误处理**: 所有解析都包含了完善的异常处理机制

## 🔮 后续建议

1. **数据清理**: 可以考虑逐步将旧格式数据迁移为 JSON 格式
2. **监控**: 建议监控 JSON 解析的异常情况
3. **文档**: 更新相关 API 文档，说明新的数据格式
4. **测试**: 建议进行充分的回归测试，确保功能正常 