﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///工作周报表
    ///</summary>
    [SugarTable("crm_week_work_report")]
    public class Db_crm_week_work_report
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:所有者Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:本周工作总结
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string WorkSummary {get;set;}

           /// <summary>
           /// Desc:本周优质客户总结
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PremiumCustomersSummary {get;set;}

           /// <summary>
           /// Desc:本周业绩进展情况
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PerformanceProgress {get;set;}

           /// <summary>
           /// Desc:下周业绩预计和计划
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PerformanceForecast {get;set;}

           /// <summary>
           /// Desc:本周遇到的问题？需要什么协助
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ProblemsEncountered {get;set;}

           /// <summary>
           /// Desc:销售业绩
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? SalesAchivement {get;set;}

           /// <summary>
           /// Desc:有效业绩
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? EffectiveAchivement {get;set;}

           /// <summary>
           /// Desc:合同金额
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? ContractAmount {get;set;}

           /// <summary>
           /// Desc:本周新增A类客户
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? CustomerTypeA {get;set;}

           /// <summary>
           /// Desc:本周新增B类客户
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? CustomerTypeB {get;set;}

           /// <summary>
           /// Desc:本周新增C类客户
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? CustomerTypeC {get;set;}

           /// <summary>
           /// Desc:状态（浏览状态、提交状态）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? State {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
