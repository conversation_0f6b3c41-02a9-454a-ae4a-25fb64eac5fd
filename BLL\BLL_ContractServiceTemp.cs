﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using iText.StyledXmlParser.Css.Resolve.Shorthand.Impl;
using Newtonsoft.Json;
using SqlSugar;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.IO;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.BLLModel.Enum.PrivateServiceEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;

namespace CRM2_API.BLL
{
    public partial class BLL_ContractService : BaseBLL<BLL_ContractService>
    {
        public List<Tscountry> GetTemporaryAccountTemplateByTemplateId()
        {
            return DbOpe_sys_tscountry.Instance.GetDataAllList().OrderBy(r => r.CreateDate).Select(r => new Tscountry { Id = r.Id, Name = r.Name }).ToList();
        }

        public List<AccountCountry_Out> GetAccountCountryByTemplateId()
        {
            List<Tscountry> tscountry = DbOpe_sys_tscountry.Instance.GetDataAllList().OrderBy(r => r.CreateDate).Select(r => new Tscountry { Id = r.Id, Name = r.Name }).ToList();
            List<AccountCountry_Out> result = new List<AccountCountry_Out>();
            foreach (Tscountry t in tscountry)
            {
                AccountCountry_Out accountCountry_Out = new AccountCountry_Out();
                accountCountry_Out.Id = t.Id;
                accountCountry_Out.Name = t.Name;
                List<Db_sys_g4_dbnames> dbnames = DbOpe_sys_tscountry.Instance.GetAccountCountriesAll(t.Id);
                accountCountry_Out.Countrys = dbnames.Select(r => new Country_Out { Sid = r.SID.Value, CountryName = r.CountryName }).ToList();
                result.Add(accountCountry_Out);
            }

            List<Tscountry> usertscountry = DbOpe_sys_usertscountry.Instance.GetDataList(r => r.UserId == UserId).OrderBy(r => r.CreateDate).Select(r => new Tscountry { Id = r.Id, Name = r.Name }).ToList();
            foreach (Tscountry t in usertscountry)
            {
                AccountCountry_Out accountCountry_Out = new AccountCountry_Out();
                accountCountry_Out.Id = t.Id;
                accountCountry_Out.Name = t.Name;
                List<Db_sys_g4_dbnames> dbnames = DbOpe_sys_usertscountry_details.Instance.GetAccountCountriesAll(t.Id);
                accountCountry_Out.Countrys = dbnames.Select(r => new Country_Out { Sid = r.SID.Value, CountryName = r.CountryName }).ToList();
                result.Add(accountCountry_Out);
            }
            return result;
        }

        /// <summary>
        /// 根据模版Id创建GTIS临时账号
        /// </summary>
        public void AddContractServiceInfoTemporaryAccountByTemplateId(string TemplateId)
        {
            DbOpe_crm_contract_serviceinfo_temporary_account.Instance.TransDeal(() =>
            {
                List<AccountCountry> AccountCountry = DbOpe_sys_tscountry.Instance.GetAccountCountries(TemplateId);
                if (AccountCountry.Count == 0)
                {
                    AccountCountry = DbOpe_sys_usertscountry_details.Instance.GetAccountCountries(TemplateId);
                }
                ContractServiceInfoTemporaryAccount_IN contractServiceInfoTemporaryAccountIN = new ContractServiceInfoTemporaryAccount_IN();
                contractServiceInfoTemporaryAccountIN.CustomerId = "********-0000-0000-0000-********0000";
                contractServiceInfoTemporaryAccountIN.AccountsOpenedNum = 1;
                contractServiceInfoTemporaryAccountIN.OpenedDate = DateTime.Now;
                contractServiceInfoTemporaryAccountIN.IsOpenSubAccount = 0;
                contractServiceInfoTemporaryAccountIN.IsOpenGlobalSearch = 1;
                contractServiceInfoTemporaryAccountIN.Duration = 2;
                contractServiceInfoTemporaryAccountIN.Remark = "";
                contractServiceInfoTemporaryAccountIN.AccountCountry = AccountCountry;
                AddContractServiceInfoTemporaryAccount(contractServiceInfoTemporaryAccountIN);
            });
        }

        /// <summary>
        /// GTIS临时账号创建
        /// </summary>
        public void AddContractServiceInfoTemporaryAccount(ContractServiceInfoTemporaryAccount_IN contractServiceInfoTemporaryAccountIN)
        {
            DbOpe_crm_contract_serviceinfo_temporary_account.Instance.TransDeal(() =>
            {
                Db_sys_user user = DbOpe_sys_user.Instance.GetDbSysUserById(UserId);

                Db_crm_contract_serviceinfo_temporary_account account = new Db_crm_contract_serviceinfo_temporary_account();
                string TaskNum = (DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetQueryCount(account) + 1).ToString().PadLeft(5, '0');
                Guid serviceInfoTemporaryAccountID = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.AddContractServiceInfoTemporaryAccount(contractServiceInfoTemporaryAccountIN, TaskNum);
                BM_AddGtisUserDemo AddGtisUser = new BM_AddGtisUserDemo();
                AddGtisUser.OprID = UserId;
                AddGtisUser.OprName = user.Name;
                AddGtisUser.TaskID = TaskNum.ToInt();
                AddGtisUser.Tel = user.Telephone;
                AddGtisUser.Email = user.Email;
                AddGtisUser.OpenID = user.WeChatOpenIDGZH;
                AddGtisUser.UnionID = user.WeChatOpenID;
                AddGtisUser.Nickname = user.Name;
                AddGtisUser.crm_country = "中国";
                AddGtisUser.crm_city = "";
                AddGtisUser.Sids = contractServiceInfoTemporaryAccountIN.AccountCountry.Select(r => r.Sid).ToArray();
                AddGtisUser.DemoCount = contractServiceInfoTemporaryAccountIN.AccountsOpenedNum;
                AddGtisUser.UseHours = contractServiceInfoTemporaryAccountIN.Duration == 1 ? 0.5 : 1;
                AddGtisUser.IsOpenSubAccount = contractServiceInfoTemporaryAccountIN.IsOpenSubAccount == 1 ? true : false;
                AddGtisUser.IsOpenHqs = contractServiceInfoTemporaryAccountIN.IsOpenGlobalSearch == 1 ? true : false;
                AddGtisUser.AccountStartDate = contractServiceInfoTemporaryAccountIN.OpenedDate;

                List<BM_AddGtisUserDemoRetModel> AccountUser = BLL_GtisOpe.Instance.AddUserDemo(AddGtisUser).Result;
                DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.AddContractServiceInfoTemporaryAccountUser(AccountUser, serviceInfoTemporaryAccountID.ToString(), contractServiceInfoTemporaryAccountIN.Duration);
                DbOpe_crm_contract_serviceinfo_temporary_account_country.Instance.AddContractServiceInfoTemporaryAccountCountry(serviceInfoTemporaryAccountID.ToString(), contractServiceInfoTemporaryAccountIN.AccountCountry);

                string dataState = Dictionary.TemporaryAccountState.First(e => e.Value == EnumTemporaryAccountState.Normal.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<ContractServiceInfoTemporaryAccount_IN>("临时账号审核", serviceInfoTemporaryAccountID.ToString(), contractServiceInfoTemporaryAccountIN, "", dataState, "创建");
            });
        }

        /// <summary>
        /// 删除临时账号信息
        /// </summary>
        public void DeleteContractServiceInfoTemporaryAccount(string id)
        {
            Db_crm_contract_serviceinfo_temporary_account account = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetDataById(id);
            if (account.CreateUser != UserId)
            {
                throw new ApiException("该用户没有数据权限");
            }
            ContractServiceInfoTemporaryAccount_Out Data = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetContractServiceInfoTemporaryAccountById(id);
            List<BM_GtisOpe_DemoInfo> AccountUser = BLL_GtisOpe.Instance.GetDemoInfo(Data.TaskNum.ToInt()).Result;
            List<TemporaryAccountUser> result = Data.AccountUser;
            //crm中账号数大于返回的账号数，证明gtis删除过账号，整体状态标记为异常
            if (result.Count > AccountUser.Count)
            {
                Data.StateName = "异常";
                Data.State = 2;
            }
            else
            {
                string stateDesc = "正常";
                int state = 1;
                //有锁定，就显示异常，不管其他的状态
                if (AccountUser.Any(s => s.State == BM_Enum_DemoState.Lock))
                {
                    stateDesc = "异常";
                    state = 2;
                }
                //有停用，就算停用，停用状态其实是正常账户，还可以被启用
                else if (AccountUser.Any(s => s.State == BM_Enum_DemoState.Stop))
                {
                    stateDesc = "停用";
                    state = 3;
                }
                //全部过期，显示过期
                else if (AccountUser.All(s => s.State == BM_Enum_DemoState.Expired))
                {
                    stateDesc = "过期";
                    state = 4;
                }

                Data.StateName = stateDesc;
                Data.State = state;
            }

            if (Data.State == EnumTemporaryAccountState.Expire.ToInt() || Data.State == EnumTemporaryAccountState.Deactivate.ToInt())
            {
                DbOpe_crm_contract_serviceinfo_temporary_account.Instance.TransDeal(() =>
                {
                    DbOpe_crm_contract_serviceinfo_temporary_account.Instance.DeleteContractServiceInfoTemporaryAccount(id);
                    DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.DeleteContractServiceInfoTemporaryAccountUser(id);
                    DbOpe_crm_contract_serviceinfo_temporary_account_country.Instance.DeleteContractServiceInfoTemporaryAccountCountry(id);
                });
            }
            else
            {
                throw new ApiException("当前临时账号状态下，不可以删除");
            }
        }

        /// <summary>
        /// 变更合同服务信息临时账号状态信息
        /// </summary>
        public void UpdateContractServiceInfoTemporaryAccountState(string id, int state)
        {
            Db_crm_contract_serviceinfo_temporary_account account = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetDataById(id);
            if (account.CreateUser != UserId)
            {
                throw new ApiException("该用户没有数据权限");
            }
            //if (account.State == EnumTemporaryAccountState.Expire.ToInt())
            //{
            DbOpe_crm_contract_serviceinfo_temporary_account.Instance.TransDeal(() =>
            {
                DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.UpdateContractServiceInfoTemporaryAccountUserState(id, state);
                DbOpe_crm_contract_serviceinfo_temporary_account.Instance.UpdateContractServiceInfoTemporaryAccountState(id, state);

                List<Db_crm_contract_serviceinfo_temporary_account_user> users = DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.GetDataList(r => r.ServiceInfoTemporaryAccountID == id);

                List<BM_ModifyUserState> bM_ModifyUserStates = new List<BM_ModifyUserState>();
                int Deleted = (state == EnumTemporaryAccountUserState.Enable.ToInt() ? 0 : 1);
                foreach (Db_crm_contract_serviceinfo_temporary_account_user user in users)
                {
                    bM_ModifyUserStates.Add(new BM_ModifyUserState() { SysUserID = user.SysUserID, Deleted = Deleted });
                }

                BLL_GtisOpe.Instance.ModifyDemoUserState(bM_ModifyUserStates).Wait();
            });
            //}
            //else
            //{
            //    throw new ApiException("当前临时账号状态下，不可以变更账号状态");
            //}

            if (state == EnumTemporaryAccountEnable.Enable.ToInt())
            {
                Db_crm_contract_serviceinfo_temporary_account accountw = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetDataById(id);
                string dataState = Dictionary.TemporaryAccountState.First(e => e.Value == EnumTemporaryAccountState.Normal.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_temporary_account>("临时账号审核", id, accountw, "", dataState, "启用");
            }
            if (state == EnumTemporaryAccountEnable.Deactivate.ToInt())
            {
                Db_crm_contract_serviceinfo_temporary_account accountw = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetDataById(id);
                string dataState = Dictionary.TemporaryAccountState.First(e => e.Value == EnumTemporaryAccountState.Deactivate.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_temporary_account>("临时账号审核", id, accountw, "", dataState, "停用");
            }
        }

        /// <summary>
        /// 变更合同服务信息临时账号用户状态信息
        /// </summary>
        public void UpdateContractServiceInfoTemporaryAccountUserState(string sysUserID, int deleted)
        {
            Db_crm_contract_serviceinfo_temporary_account_user account = DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.GetData(r => r.SysUserID == sysUserID);
            if (account.CreateUser != UserId)
            {
                throw new ApiException("该用户没有数据权限");
            }

            DbOpe_crm_contract_serviceinfo_temporary_account.Instance.TransDeal(() =>
            {
                int state = (deleted == 0 ? EnumTemporaryAccountUserState.Enable.ToInt() : EnumTemporaryAccountUserState.Deactivate.ToInt());
                DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.UpdateData(r => new Db_crm_contract_serviceinfo_temporary_account_user { State = state }, r => r.SysUserID == sysUserID && r.CreateUser == UserId);
                List<BM_ModifyUserState> bM_ModifyUserStates = new List<BM_ModifyUserState>();
                bM_ModifyUserStates.Add(new BM_ModifyUserState() { SysUserID = sysUserID, Deleted = deleted });
                BLL_GtisOpe.Instance.ModifyDemoUserState(bM_ModifyUserStates).Wait();
            });
        }

        /// <summary>
        /// 延期合同服务信息临时账号信息
        /// </summary>
        public void DelayContractServiceInfoTemporaryAccount(string id, int duration)
        {
            Db_crm_contract_serviceinfo_temporary_account account = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetDataById(id);
            if (account.CreateUser != UserId)
            {
                throw new ApiException("该用户没有数据权限");
            }
            //if (account.State != EnumTemporaryAccountState.Deactivate.ToInt())
            //{
            DbOpe_crm_contract_serviceinfo_temporary_account.Instance.TransDeal(() =>
            {
                DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.UpdateContractServiceInfoTemporaryAccountUserDuration(id, duration);
            });
            //}
            //else
            //{
            //    throw new ApiException("当前临时账号状态下，不可以延期");
            //}
        }

        /// <summary>
        /// 获取延期合同服务信息临时账号信息
        /// </summary>
        public List<DelayContractServiceInfoTemporaryAccountList_Out> GetDelayContractServiceInfoTemporaryAccountListById(string id)
        {
            return DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.GetDelayContractServiceInfoTemporaryAccountListById(id);
        }

        /// <summary>
        /// 根据查询条件获取合同服务信息临时账号信息列表
        /// </summary>
        public ApiTableOut<SearchContractServiceInfoTemporaryAccountList_Out> SearchContractServiceInfoTemporaryAccountList(SearchContractServiceInfoTemporaryAccountList_In searchContractServiceInfoTemporaryAccountListIn)
        {
            int total = 0;
            int pagesize = 10;
            int pagenumreal = searchContractServiceInfoTemporaryAccountListIn.PageNumber;
            //伪装
            if (searchContractServiceInfoTemporaryAccountListIn.StatePara == 3)
            {
                searchContractServiceInfoTemporaryAccountListIn.PageNumber = 1;
                searchContractServiceInfoTemporaryAccountListIn.PageSize = 9000;
            }
            List<SearchContractServiceInfoTemporaryAccountList_Out> Data = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.SearchContractServiceInfoTemporaryAccountList(searchContractServiceInfoTemporaryAccountListIn, UserId, ref total);

            if (Data.Count > 0)
            {
                int[] taskIDs = Data.Select(r => r.TaskNum.ToInt()).ToArray();
                Dictionary<int, List<BM_Enum_DemoState>> statelist = BLL_GtisOpe.Instance.GetDemoState(taskIDs, searchContractServiceInfoTemporaryAccountListIn.StatePara).Result;
                List<SearchContractServiceInfoTemporaryAccountList_Out> UnvalidData = new List<SearchContractServiceInfoTemporaryAccountList_Out>();
                foreach (SearchContractServiceInfoTemporaryAccountList_Out r in Data)
                {
                    if (statelist.TryGetValue(r.TaskNum.ToInt(), out var allState))
                    {
                        string stateDesc = "正常";
                        int state = 1;
                        //有锁定，就显示异常，不管其他的状态
                        if (allState.Any(s => s == BM_Enum_DemoState.Lock) || r.AccountNums > allState.Count)
                        {
                            stateDesc = "异常";
                            state = 2;
                        }
                        //有停用，就算停用，停用状态其实是正常账户，还可以被启用
                        else if (allState.Any(s => s == BM_Enum_DemoState.Stop))
                        {
                            stateDesc = "停用";
                            state = 3;
                        }
                        //全部过期，显示过期
                        else if (allState.All(s => s == BM_Enum_DemoState.Expired))
                        {
                            stateDesc = "过期";
                            state = 4;
                        }
                        r.TemporaryAccountStateName = stateDesc;
                        r.TemporaryAccountState = state;
                    }
                    else
                    {
                        r.TemporaryAccountStateName = "未查询";
                        r.TemporaryAccountState = 5;
                        UnvalidData.Add(r);
                    }
                }
                if (UnvalidData.Count > 0)
                {
                    UnvalidData.ForEach(r =>
                    {
                        Data.Remove(r);
                    });
                    total = total - UnvalidData.Count;
                }
                //如果查询过期，重新分页
                if (searchContractServiceInfoTemporaryAccountListIn.StatePara == 3)
                {
                    total = Data.Count();
                    Data = Data.Skip((pagenumreal - 1) * pagesize).Take(pagesize).ToList();
                }

            }

            return new ApiTableOut<SearchContractServiceInfoTemporaryAccountList_Out> { Data = Data, Total = total };
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号信息
        /// </summary>
        public ContractServiceInfoTemporaryAccount_Out GetContractServiceInfoTemporaryAccountById(string id)
        {
            ContractServiceInfoTemporaryAccount_Out Data = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetContractServiceInfoTemporaryAccountById(id);

            List<BM_GtisOpe_DemoInfo> AccountUser = BLL_GtisOpe.Instance.GetDemoInfo(Data.TaskNum.ToInt()).Result;
            List<TemporaryAccountUser> result = Data.AccountUser;
            //crm中账号数大于返回的账号数，证明gtis删除过账号，整体状态标记为异常
            if (result.Count > AccountUser.Count)
            {
                Data.StateName = "异常";
                Data.State = 2;
            }
            else
            {
                string stateDesc = "正常";
                int state = 1;
                //有锁定，就显示异常，不管其他的状态
                if (AccountUser.Any(s => s.State == BM_Enum_DemoState.Lock))
                {
                    stateDesc = "异常";
                    state = 2;
                }
                //有停用，就算停用，停用状态其实是正常账户，还可以被启用
                else if (AccountUser.Any(s => s.State == BM_Enum_DemoState.Stop))
                {
                    stateDesc = "停用";
                    state = 3;
                }
                //全部过期，显示过期
                else if (AccountUser.All(s => s.State == BM_Enum_DemoState.Expired))
                {
                    stateDesc = "过期";
                    state = 4;
                }

                Data.StateName = stateDesc;
                Data.State = state;
            }

            foreach (TemporaryAccountUser user in result)
            {
                List<BM_GtisOpe_DemoInfo> Account = AccountUser.Where(r => r.SysUserID == user.SysUserID).ToList();
                if (Account.Count > 0)
                {
                    BM_GtisOpe_DemoInfo u = Account.First();
                    user.TemporaryAccountUserState = u.State.ToInt();
                    user.TemporaryAccountUserStateName = u.StateDesc;
                    user.StartTime = u.UseStartTime == null ? "" : u.UseStartTime.Value.ToString();
                    user.EndTime = u.UseEndTime == null ? "" : u.UseEndTime.Value.ToString();
                    user.LoginIP = u.LastLoginPlace;
                    user.RemainingUsageTime = u.LeftUseTime;
                }
                else
                    user.TemporaryAccountUserStateName = "未查询到数据";//未返回的账号，都标记为异常
            }

            return Data;
        }


        /// <summary>
        /// 根据临时账号Id获取合同服务信息临时账号用户信息
        /// </summary>
        public List<TemporaryAccountUser> GetTemporaryAccountUserByTemporaryAccountID(string serviceInfoTemporaryAccountID)
        {
            //Db_crm_contract_serviceinfo_temporary_account temporaryAccount = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetDataById(serviceInfoTemporaryAccountID);
            //List<BM_GtisOpe_DemoInfo> DemoInfo = BLL_GtisOpe.Instance.GetDemoInfo(temporaryAccount.TaskNum.ToInt(), UserId).Result;
            //Db_crm_contract_serviceinfo_temporary_account_user list = new Db_crm_contract_serviceinfo_temporary_account_user();
            //foreach (BM_GtisOpe_DemoInfo demo in DemoInfo)
            //{
            //    int State = demo.State.ToInt();
            //    DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.
            //        UpdateData(r => new Db_crm_contract_serviceinfo_temporary_account_user
            //        {
            //            LoginIP = demo.LastLoginPlace,
            //            RemainingUsageTime = Int32.Parse(demo.LeftUseTime),
            //            StartTime = demo.UseStartTime,
            //            EndTime = demo.UseEndTime,
            //            DelayTimes = demo.IsDelayed == true ? 1 : 0,
            //            State = State,
            //        }, r => r.SysUserID == demo.SysUserID);
            //}

            Db_crm_contract_serviceinfo_temporary_account account = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetDataById(serviceInfoTemporaryAccountID);
            List<BM_GtisOpe_DemoInfo> AccountUser = BLL_GtisOpe.Instance.GetDemoInfo(account.TaskNum.ToInt()).Result;
            List<TemporaryAccountUser> result = DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.GetTemporaryAccountUserByTemporaryAccountID(serviceInfoTemporaryAccountID, true);
            foreach (TemporaryAccountUser user in result)
            {
                List<BM_GtisOpe_DemoInfo> Account = AccountUser.Where(r => r.SysUserID == user.SysUserID).ToList();
                if (Account.Count > 0)
                {
                    BM_GtisOpe_DemoInfo u = Account.First();
                    user.TemporaryAccountUserState = u.State.ToInt();
                    user.TemporaryAccountUserStateName = u.StateDesc;
                    user.StartTime = u.UseStartTime == null ? "" : u.UseStartTime.Value.ToyyyyMMddHHmmss();
                    user.EndTime = u.UseEndTime == null ? "" : u.UseEndTime.Value.ToyyyyMMddHHmmss();
                    user.LoginIP = u.LastLoginPlace;
                    user.RemainingUsageTime = u.LeftUseTime;
                }
                else
                    user.TemporaryAccountUserStateName = "未查询到数据";//未返回的账号，都标记为异常
            }
            return result;
        }

        /// <summary>
        /// 获取当天开通演示账号剩余个数
        /// </summary>
        public int GetContractServiceInfoTemporaryAccountLeftOpenTimes()
        {
            return BLL_GtisOpe.Instance.GetDemoLeftOpenTimes(UserId).Result;
        }

        /// <summary>
        /// 根据TaskId获取合同服务信息临时账号用户日志信息
        /// </summary>
        /// <param name="serviceInfoTemporaryAccountID"></param>
        /// <returns></returns>
        public List<BM_AllUserOperateLog> GetAllDemoUserOperateLogSta(string serviceInfoTemporaryAccountID)
        {
            Db_crm_contract_serviceinfo_temporary_account account = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetDataById(serviceInfoTemporaryAccountID);
            if (account.CreateUser != UserId)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return BLL_GtisOpe.Instance.GetAllDemoUserOperateLogSta(account.TaskNum.ToInt(), UserId).Result;
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号用户按月统计
        /// </summary>
        public List<BM_OneUserOperateLogByMonth> GetOneUserOpeLogStaByMonth(string sysUserID)
        {
            Db_crm_contract_serviceinfo_temporary_account_user account = DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.GetData(r => r.SysUserID == sysUserID);
            if (account.CreateUser != UserId)
            {
                throw new ApiException("该用户没有数据权限");
            }
            return BLL_GtisOpe.Instance.GetOneUserOpeLogStaByMonth_Demo(account.SysUserID).Result;
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号用户详情
        /// </summary>
        public ApiTableOut<BM_UserOpeLogDetail> GetOpeLogDetail(TemporaryAccountUserLogDetail_IN temporaryAccountUserLogDetail)
        {
            Db_crm_contract_serviceinfo_temporary_account_user account = DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.GetData(r => r.SysUserID == temporaryAccountUserLogDetail.SysUserID);
            if (account.CreateUser != UserId)
            {
                throw new ApiException("该用户没有数据权限");
            }
            BM_GtisOpeGetOpeLogDetal bM_GtisOpeGetOpeLogDetal = temporaryAccountUserLogDetail.MappingTo<BM_GtisOpeGetOpeLogDetal>();
            bM_GtisOpeGetOpeLogDetal.PageNum = temporaryAccountUserLogDetail.PageNumber;
            return BLL_GtisOpe.Instance.GetOpeLogDetail_Demo(bM_GtisOpeGetOpeLogDetal).Result;
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号用户导出
        /// </summary>
        public Stream GetOpeLogDetailDownload(string sysUserID)
        {
            Db_crm_contract_serviceinfo_temporary_account_user account = DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.GetData(r => r.SysUserID == sysUserID);
            if (account.CreateUser != UserId)
            {
                throw new ApiException("该用户没有数据权限");
            }
            List<string> SysUserIDs = new List<string>();
            SysUserIDs.Add(account.SysUserID);
            BM_GtisOpeGetOpeLogDetalDownload bM_GtisOpeGetOpeLogDetalDownload = new BM_GtisOpeGetOpeLogDetalDownload();
            bM_GtisOpeGetOpeLogDetalDownload.SysUserIDs = SysUserIDs;
            return BLL_GtisOpe.Instance.GetOpeLogDetailDownload_Demo(bM_GtisOpeGetOpeLogDetalDownload).Result;
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号用户详情
        /// </summary>
        public ApiTableOut<BM_UserExportLogDetail> GetOpeExportLogDetail(TemporaryAccountUserExportLogDetail_IN temporaryAccountUserExportLogDetail)
        {
            Db_crm_contract_serviceinfo_temporary_account_user account = DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.GetData(r => r.SysUserID == temporaryAccountUserExportLogDetail.SysUserID);
            if (account.CreateUser != UserId)
            {
                throw new ApiException("该用户没有数据权限");
            }
            BM_GtisOpeGetExportLogDetal bM_GtisOpeGetExportLogDetal = temporaryAccountUserExportLogDetail.MappingTo<BM_GtisOpeGetExportLogDetal>();
            return BLL_GtisOpe.Instance.GetExportLogDetail_Demo(bM_GtisOpeGetExportLogDetal).Result;
        }


        public void AddUserTSCountry(AddUserTSCountry_In addUserTSCountryIn)
        {
            int UserTSCountryCount = DbOpe_sys_usertscountry.Instance.GetUserTSCountryCount(UserId);
            string SysUserTSCountryCount = DbOpe_sys_systemparameter.Instance.GetValueByKey("UserTsCountryNum");
            if (SysUserTSCountryCount.IsNotNullOrEmpty())
            {
                if (!(UserTSCountryCount < SysUserTSCountryCount.ToInt()))
                {
                    throw new ApiException("超出个人限定数量，无法新建");
                }
            }

            DbOpe_sys_usertscountry.Instance.AddUserTSCountry(addUserTSCountryIn);
        }

        public void UpdateUserTSCountry(UpdateUserTSCountry_In updateUserTSCountryIn)
        {
            GetUserTSCountryList_Out obj = BLL_ContractService.Instance.GetUserTSCountryById(updateUserTSCountryIn.Id);
            if (obj == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            DbOpe_sys_usertscountry.Instance.UpdateUserTSCountry(updateUserTSCountryIn);
        }

        public void DeleteUserTSCountryById(string id)
        {
            GetUserTSCountryList_Out obj = BLL_ContractService.Instance.GetUserTSCountryById(id);
            if (obj == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            DbOpe_sys_usertscountry.Instance.DeleteUserTSCountryById(id);
        }

        public ApiTableOut<SearchUserTSCountryList_Out> SearchUserTSCountryList(SearchUserTSCountryList_In searchUserTSCountryListIn)
        {
            int total = 0;
            return new ApiTableOut<SearchUserTSCountryList_Out> { Data = DbOpe_sys_usertscountry.Instance.SearchUserTSCountryList(searchUserTSCountryListIn, UserId, ref total), Total = total };
        }

        public GetUserTSCountryList_Out GetUserTSCountryById(string id)
        {
            return DbOpe_sys_usertscountry.Instance.GetUserTSCountryById(id, UserId);
        }


        /// <summary>
        /// 删除对美洲国家客户禁售的国家数据
        /// </summary>
        /// <param name="AccountId"></param>
        /// <param name="allNeedRemoveUserIds"></param>
        public void DeleteAmericaCountryData(string AccountId, List<string> allNeedRemoveUserIds)
        {
            if (string.IsNullOrEmpty(AccountId) && (allNeedRemoveUserIds == null || allNeedRemoveUserIds.Count == 0))
                throw new ApiException("参数缺失，无法删除国家数据");
            //整理美洲地区国家的Id集合
            var countryIds = DbOpe_sys_country.Instance.GetAmericaCountryIds();
            //整理不支持美洲地区国家销售的Sid集合
            var allNeedRemoveSids = DbOpe_crm_product_rules_localdatasourcesnotsupport.Instance.GetSidsRemoveList(countryIds);
            //没有SysUserId，需要用AccountId查询出SysUserId的数组，进行删除
            if (allNeedRemoveUserIds == null || allNeedRemoveUserIds.Count == 0)
            {
                var temporaryAccountUserList = GetTemporaryAccountUserByTemporaryAccountID(AccountId);
                if (temporaryAccountUserList != null)
                    allNeedRemoveUserIds = temporaryAccountUserList.Where(e => e.TemporaryAccountUserStateName != "未查询到数据").Select(e => e.SysUserID).ToList();
                else
                    throw new ApiException("未找到对应的临时账号");
            }
            BLL_GtisOpe.Instance.RemoveUserSid(allNeedRemoveUserIds.ToArray(), allNeedRemoveSids.ToArray()).Wait();
        }
    }
}
