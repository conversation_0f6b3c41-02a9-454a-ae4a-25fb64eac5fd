﻿using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BusinessModel;
using static CRM2_API.Model.ControllersViewModel.VM_FormDataCondition;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        public class UserInterfaceRight
        {
            const string INTERFACE = "interfacce_";

            public static List<FormInterfaceName>? GetUserInterfaceRight(string userId)
            {
                if (RedisHelper.Exists(INTERFACE + userId))
                    return RedisHelper.Get<List<FormInterfaceName>>(INTERFACE + userId);
                else
                    return null;
            }

            public static void SaveUserInterfaceRight(string userId, List<FormInterfaceName> formInterfaces)
            {
                RedisHelper.Set(INTERFACE + userId, formInterfaces/*, TimeSpan.FromMinutes(60)*/);
            }
        }

        public class UserMenuRight
        {
            const string MENU = "menu_";

            public static List<Db_sys_form> GetUserMenuRight(string userId)
            {
                if (RedisHelper.Exists(MENU + userId))
                    return RedisHelper.Get<List<Db_sys_form>>(MENU + userId);
                else
                    return new List<Db_sys_form>();
            }

            public static void SaveUserMenuRight(string userId, List<Db_sys_form> formInterfaces)
            {
                RedisHelper.Set(MENU + userId, formInterfaces/*, TimeSpan.FromMinutes(60)*/);
            }
        }

        public class UserButtonRight
        {
            const string BUTTON = "button_";

            public static List<Db_sys_form> GetUserButtonRight(string userId)
            {
                if (RedisHelper.Exists(BUTTON + userId))
                    return RedisHelper.Get<List<Db_sys_form>>(BUTTON + userId);
                else
                    return new List<Db_sys_form>();
            }

            public static void SaveUserButtonRight(string userId, List<Db_sys_form> formInterfaces)
            {
                RedisHelper.Set(BUTTON + userId, formInterfaces/*, TimeSpan.FromMinutes(60)*/);
            }
        }

        public class UserDataRight
        {
            const string DATA = "data_";

            public static List<FormDataCondition> GetUserDataRight(string userId)
            {
                if (RedisHelper.Exists(DATA + userId))
                    return RedisHelper.Get<List<FormDataCondition>>(DATA + userId);
                else
                    return new List<FormDataCondition>();
            }

            public static void SaveUserDataRight(string userId, List<FormDataCondition> formDataCondition)
            {
                RedisHelper.Set(DATA + userId, formDataCondition/*, TimeSpan.FromMinutes(60)*/);
            }
        }
    }

}
