using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.Enum;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Common.JWT;
using CRM2_API.Model.System;
using CRM2_API.BLL.Common;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告数据库操作业务逻辑类 - 评论点赞功能
    /// </summary>
    public partial class BLL_WorkReportDatabase
    {
        #region 用户身份验证

        /// <summary>
        /// 获取当前用户ID（评论功能专用）
        /// </summary>
        /// <returns>用户ID</returns>
        private string GetCurrentUserIdForComment()
        {
            if (string.IsNullOrEmpty(UserId))
            {
                throw new ApiException("用户身份验证失败，请重新登录");
            }
            return UserId;
        }

        #endregion

        #region 评论功能

        /// <summary>
        /// 获取报告评论列表
        /// </summary>
        /// <param name="input">查询输入</param>
        /// <returns>评论列表</returns>
        public List<GetReportCommentList_Out> GetReportCommentList(GetReportCommentList_In input)
        {
            if (input == null)
                throw new ApiException("输入参数不能为空");

            if (string.IsNullOrEmpty(input.ReportId))
                throw new ApiException("报告ID不能为空");

            try
            {
                // 获取当前用户信息
                var currentUserId = GetCurrentUserIdForComment();

                // 获取评论列表
                var comments = DbOpe_crm_report_comment.Instance.GetCommentList(
                    input.ReportId, 
                    input.CommentTypes, 
                    input.OnlyMyComments ?? false, 
                    currentUserId);

                // 获取用户信息（这里简化处理，实际应该从用户表获取）
                var result = new List<GetReportCommentList_Out>();

                foreach (var comment in comments)
                {
                    // 获取用户信息
                    var commentUser = DbOpe_sys_user.Instance.GetUserById(comment.CreateUser);
                    var commentOut = new GetReportCommentList_Out
                    {
                        Id = comment.Id,
                        ReportId = comment.ReportId,
                        CommentContent = comment.CommentText,
                        CommentType = (EnumCommentType)comment.CommentType,
                        SelectionText = comment.SelectionText ?? "",
                        ModuleKey = comment.ModuleKey ?? "",
                        SectionKey = comment.SectionKey ?? "",
                        ModuleOrder = comment.ModuleOrder,
                        SectionOrder = comment.SectionOrder,
                        ContentId = comment.ContentId ?? "",
                        TargetElementId = comment.TargetElementId ?? "",
                        CommentTime = comment.CreateDate,
                        UserId = comment.CreateUser,
                        UserName = commentUser?.Name ?? "",
                        AvatarImageUrl = commentUser?.AvatarImageUrl ?? "",
                        IsMe = comment.CreateUser == currentUserId,
                        IsReportAuthor = comment.CreateUser == comment.ReportUserId
                    };

                    result.Add(commentOut);
                }

                // 按照报告模板字段顺序排列评论
                return result.OrderBy(c => c.ModuleOrder)
                            .ThenBy(c => c.SectionOrder)
                            .ThenByDescending(c => c.CommentTime)
                            .ToList();
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取评论列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 添加报告评论
        /// </summary>
        /// <param name="input">评论输入</param>
        /// <returns>添加结果</returns>
        public AddReportComment_Out AddReportComment(AddReportComment_In input)
        {
            if (input == null)
                throw new ApiException("输入参数不能为空");

            if (string.IsNullOrEmpty(input.ReportId))
                throw new ApiException("报告ID不能为空");

            if (string.IsNullOrEmpty(input.CommentContent))
                throw new ApiException("评论内容不能为空");

            try
            {
                // 获取当前用户信息
                var currentUserId = GetCurrentUserIdForComment();
                var currentUserName = GetCurrentUserName();

                // 检查报告是否存在
                var report = DbOpe_crm_report_base.Instance.GetReportById(input.ReportId);
                if (report == null)
                    throw new ApiException("报告不存在或已被删除");
                
                // 检查报告状态，草稿状态的报告不允许评论
                if (report.Status == (int)EnumReportStatus.Draft)
                    throw new ApiException("草稿状态的报告不允许添加评论");

                // 权限验证：检查用户是否有评论权限（接收人或抄送人）
                if (!HasCommentPermission(input.ReportId, currentUserId))
                    throw new ApiException("您没有权限对此报告进行评论，只有报告的接收人和抄送人可以评论");

                // 创建评论对象
                var commentId = Guid.NewGuid().ToString();
                var comment = new Db_crm_report_comment
                {
                    Id = commentId,
                    ReportId = input.ReportId,
                    ReportType = report.ReportType,
                    ReportTitle = report.Title,
                    ReportUserId = report.UserId,
                    ReportUserName = report.UserName,
                    ReportDate = report.ReportDate,
                    ReportYear = report.ReportYear,
                    ReportMonth = report.ReportMonth,
                    ReportWeek = report.ReportWeek,
                    ModuleKey = input.ModuleKey ?? "",
                    ModuleTitle = GetModuleTitle(input.ModuleKey ?? "", report.ReportType),
                    ModuleOrder = GetModuleOrder(input.ModuleKey ?? "", report.ReportType),
                    SectionKey = input.SectionKey ?? "",
                    SectionTitle = GetSectionTitle(input.ModuleKey ?? "", input.SectionKey ?? "", report.ReportType),
                    SectionOrder = GetSectionOrder(input.ModuleKey ?? "", input.SectionKey ?? "", report.ReportType),
                    ContentId = input.ContentId ?? "",
                    CommentType = (int)input.CommentType,
                    CommentText = input.CommentContent,
                    SelectionText = input.SelectionText ?? "",
                    SelectionStart = input.SelectionStart,
                    SelectionEnd = input.SelectionEnd,
                    TargetElementId = input.TargetElementId ?? "",
                    Deleted = false,
                    CreateUser = currentUserId,

                    CreateDate = DateTime.Now,
                    UpdateUser = currentUserId,
                    UpdateDate = DateTime.Now
                };

                // 添加评论
                var resultId = DbOpe_crm_report_comment.Instance.AddComment(comment);

                // 更新报告评论数量
                UpdateReportCommentCount(input.ReportId);

                return new AddReportComment_Out
                {
                    CommentId = resultId,
                    Data = 1
                };
            }
            catch (Exception ex)
            {
                throw new ApiException($"添加评论失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新报告评论
        /// </summary>
        /// <param name="input">更新输入</param>
        /// <returns>更新结果</returns>
        public ReportInteractionResult_Out UpdateReportComment(UpdateReportComment_In input)
        {
            if (input == null)
                throw new ApiException("输入参数不能为空");

            if (string.IsNullOrEmpty(input.CommentId))
                throw new ApiException("评论ID不能为空");

            if (string.IsNullOrEmpty(input.CommentContent))
                throw new ApiException("评论内容不能为空");

            try
            {
                var currentUserId = GetCurrentUserIdForComment();

                // 获取评论信息以获取报告ID
                var comment = DbOpe_crm_report_comment.Instance.GetData(x => x.Id == input.CommentId);
                if (comment == null)
                    throw new ApiException("评论不存在或已被删除");

                DbOpe_crm_report_comment.Instance.UpdateComment(
                    input.CommentId, 
                    input.CommentContent, 
                    currentUserId);

                return new ReportInteractionResult_Out
                {
                    Data = 1,
                    Message = "评论更新成功"
                };
            }
            catch (Exception ex)
            {
                throw new ApiException($"更新评论失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除报告评论
        /// </summary>
        /// <param name="input">删除输入</param>
        /// <returns>删除结果</returns>
        public ReportInteractionResult_Out DeleteReportComment(DeleteReportComment_In input)
        {
            if (input == null)
                throw new ApiException("输入参数不能为空");

            if (string.IsNullOrEmpty(input.CommentId))
                throw new ApiException("评论ID不能为空");

            try
            {
                var currentUserId = GetCurrentUserIdForComment();

                // 获取评论信息以获取报告ID
                var comment = DbOpe_crm_report_comment.Instance.GetData(x => x.Id == input.CommentId);
                if (comment == null)
                    throw new ApiException("评论不存在或已被删除");

                DbOpe_crm_report_comment.Instance.DeleteComment(input.CommentId, currentUserId);

                // 更新报告评论数量
                UpdateReportCommentCount(comment.ReportId);

                return new ReportInteractionResult_Out
                {
                    Data = 1,
                    Message = "评论删除成功"
                };
            }
            catch (Exception ex)
            {
                throw new ApiException($"删除评论失败：{ex.Message}");
            }
        }

        #endregion

        #region 点赞功能

        /// <summary>
        /// 添加报告点赞
        /// </summary>
        /// <param name="input">点赞输入</param>
        /// <returns>点赞结果</returns>
        public AddReportLike_Out AddReportLike(AddReportLike_In input)
        {
            if (input == null)
                throw new ApiException("输入参数不能为空");

            if (string.IsNullOrEmpty(input.ReportId))
                throw new ApiException("报告ID不能为空");

            try
            {
                // 获取当前用户信息
                var currentUserId = GetCurrentUserIdForComment();
                var currentUserName = GetCurrentUserName();

                // 检查报告是否存在
                var report = DbOpe_crm_report_base.Instance.GetReportById(input.ReportId);
                if (report == null)
                    throw new ApiException("报告不存在或已被删除");

                // 权限验证：检查用户是否有点赞权限（接收人或抄送人）
                if (!HasCommentPermission(input.ReportId, currentUserId))
                    throw new ApiException("您没有权限对此报告进行点赞，只有报告的接收人和抄送人可以点赞");

                // 创建点赞对象
                var likeId = Guid.NewGuid().ToString();
                var like = new Db_crm_report_like
                {
                    Id = likeId,
                    ReportId = input.ReportId,
                    ReportType = report.ReportType,
                    ReportTitle = report.Title,
                    ReportUserId = report.UserId,
                    ReportUserName = report.UserName,
                    ReportDate = report.ReportDate,
                    ReportYear = report.ReportYear,
                    ReportMonth = report.ReportMonth,
                    ReportWeek = report.ReportWeek,
                    Deleted = false,
                    CreateUser = currentUserId,
                    CreateDate = DateTime.Now,
                    UpdateUser = currentUserId,
                    UpdateDate = DateTime.Now
                };

                // 添加点赞
                DbOpe_crm_report_like.Instance.AddLike(like);

                // 更新报告基础表中的点赞数量
                var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == input.ReportId);
                if (reportBase != null)
                {
                    reportBase.LikeCount = DbOpe_crm_report_like.Instance.GetLikeCount(input.ReportId);
                    reportBase.UpdateUser = GetCurrentUserIdForComment();
                    reportBase.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_base.Instance.Update(reportBase);
                }

                return new AddReportLike_Out
                {
                    Data = 1,
                    LikeCount = reportBase?.LikeCount ?? 0
                };
            }
            catch (ApiException)
            {
                // 直接重新抛出ApiException，不要包装
                throw;
            }
            catch (Exception ex)
            {
                throw new ApiException($"点赞失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 取消报告点赞
        /// </summary>
        /// <param name="input">取消点赞输入</param>
        /// <returns>取消点赞结果</returns>
        public CancelReportLike_Out CancelReportLike(CancelReportLike_In input)
        {
            if (input == null)
                throw new ApiException("输入参数不能为空");

            if (string.IsNullOrEmpty(input.ReportId))
                throw new ApiException("报告ID不能为空");

            try
            {
                var currentUserId = GetCurrentUserIdForComment();

                // 取消点赞
                DbOpe_crm_report_like.Instance.CancelLike(input.ReportId, currentUserId);

                // 更新报告基础表中的点赞数量
                var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == input.ReportId);
                if (reportBase != null)
                {
                    reportBase.LikeCount = DbOpe_crm_report_like.Instance.GetLikeCount(input.ReportId);
                    reportBase.UpdateUser = GetCurrentUserIdForComment();
                    reportBase.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_base.Instance.Update(reportBase);
                }

                return new CancelReportLike_Out
                {
                    Data = 1,
                    LikeCount = reportBase?.LikeCount ?? 0
                };
            }
            catch (Exception ex)
            {
                throw new ApiException($"取消点赞失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取报告点赞列表
        /// </summary>
        /// <param name="input">查询输入</param>
        /// <returns>点赞列表</returns>
        public List<GetReportLikeList_Out> GetReportLikeList(GetReportLikeList_In input)
        {
            if (input == null)
                throw new ApiException("输入参数不能为空");

            if (string.IsNullOrEmpty(input.ReportId))
                throw new ApiException("报告ID不能为空");

            try
            {
                var likes = DbOpe_crm_report_like.Instance.GetLikeList(input.ReportId);

                var result = new List<GetReportLikeList_Out>();

                foreach (var like in likes)
                {
                                        // 获取用户信息
                    var likeUser = DbOpe_sys_user.Instance.GetUserById(like.CreateUser);
                    var likeOut = new GetReportLikeList_Out
                    {
                        Id = like.Id,
                        UserId = like.CreateUser,
                        UserName = likeUser?.Name ?? "",
                        AvatarImageUrl = likeUser?.AvatarImageUrl ?? "",
                        LikeTime = like.CreateDate,
                        IsMe = like.CreateUser == UserId
                    };

                    result.Add(likeOut);
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取点赞列表失败：{ex.Message}");
            }
        }

        #endregion

        #region 互动统计

        /// <summary>
        /// 获取报告互动统计
        /// </summary>
        /// <param name="input">查询输入</param>
        /// <returns>互动统计</returns>
        public GetReportInteractionStats_Out GetReportInteractionStats(GetReportInteractionStats_In input)
        {
            if (input == null)
                throw new ApiException("输入参数不能为空");

            if (string.IsNullOrEmpty(input.ReportId))
                throw new ApiException("报告ID不能为空");

            try
            {
                var currentUserId = GetCurrentUserIdForComment();

                // 获取报告基础信息，使用存储的数量字段
                var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == input.ReportId);
                if (reportBase == null)
                {
                    throw new ApiException("报告不存在");
                }

                // 获取我的评论数量（需要实时查询）
                var myCommentCount = DbOpe_crm_report_comment.Instance.GetUserCommentCount(input.ReportId, currentUserId);
                var hasLiked = DbOpe_crm_report_like.Instance.HasLiked(input.ReportId, currentUserId);

                return new GetReportInteractionStats_Out
                {
                    ReportId = input.ReportId,
                    CommentCount = reportBase.CommentCount,
                    LikeCount = reportBase.LikeCount,
                    HasLiked = hasLiked,
                    MyCommentCount = myCommentCount
                };
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取互动统计失败：{ex.Message}");
            }
        }

        #endregion

        #region 计数更新方法

        /// <summary>
        /// 更新报告评论数量
        /// </summary>
        /// <param name="reportId">报告ID</param>
        private void UpdateReportCommentCount(string reportId)
        {
            try
            {
                var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == reportId);
                if (reportBase != null)
                {
                    reportBase.CommentCount = DbOpe_crm_report_comment.Instance.GetCommentCount(reportId);
                    reportBase.UpdateUser = GetCurrentUserIdForComment();
                    reportBase.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_base.Instance.Update(reportBase);
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新报告评论数量失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新报告点赞数量
        /// </summary>
        /// <param name="reportId">报告ID</param>
        private void UpdateReportLikeCount(string reportId)
        {
            try
            {
                var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == reportId);
                if (reportBase != null)
                {
                    reportBase.LikeCount = DbOpe_crm_report_like.Instance.GetLikeCount(reportId);
                    reportBase.UpdateUser = GetCurrentUserIdForComment();
                    reportBase.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_base.Instance.Update(reportBase);
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新报告点赞数量失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新报告抄送人数量
        /// </summary>
        /// <param name="reportId">报告ID</param>
        private void UpdateReportCcCount(string reportId)
        {
            try
            {
                var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == reportId);
                if (reportBase != null)
                {
                    reportBase.CcCount = DbOpe_crm_report_cc.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false).Count;
                    reportBase.UpdateUser = GetCurrentUserIdForComment();
                    reportBase.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_base.Instance.Update(reportBase);
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新报告抄送人数量失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 根据ModuleKey获取模块标题
        /// </summary>
        /// <param name="moduleKey">模块键名</param>
        /// <param name="reportType">报告类型</param>
        /// <returns>模块标题</returns>
        private string GetModuleTitle(string moduleKey, int reportType)
        {
            if (string.IsNullOrEmpty(moduleKey))
                return "";

            try
            {
                // 获取模板信息
                var template = GetCommentReportTemplate((EnumReportType)reportType);
                var module = template.Modules?.FirstOrDefault(m => m.ModuleKey == moduleKey);
                return module?.ModuleTitle ?? moduleKey;
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取模块标题失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据ModuleKey获取模块排序
        /// </summary>
        /// <param name="moduleKey">模块键名</param>
        /// <param name="reportType">报告类型</param>
        /// <returns>模块排序</returns>
        private int GetModuleOrder(string moduleKey, int reportType)
        {
            if (string.IsNullOrEmpty(moduleKey))
                return 0;

            try
            {
                var template = GetCommentReportTemplate((EnumReportType)reportType);
                var module = template.Modules?.FirstOrDefault(m => m.ModuleKey == moduleKey);
                return module?.ModuleOrder ?? 0;
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取模块排序失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据ModuleKey和SectionKey获取章节标题
        /// </summary>
        /// <param name="moduleKey">模块键名</param>
        /// <param name="sectionKey">章节键名</param>
        /// <param name="reportType">报告类型</param>
        /// <returns>章节标题</returns>
        private string GetSectionTitle(string moduleKey, string sectionKey, int reportType)
        {
            if (string.IsNullOrEmpty(moduleKey) || string.IsNullOrEmpty(sectionKey))
                return "";

            try
            {
                var template = GetCommentReportTemplate((EnumReportType)reportType);
                var module = template.Modules?.FirstOrDefault(m => m.ModuleKey == moduleKey);
                var section = module?.Sections?.FirstOrDefault(s => s.SectionKey == sectionKey);
                return section?.SectionTitle ?? sectionKey;
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取章节标题失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据ModuleKey和SectionKey获取章节排序
        /// </summary>
        /// <param name="moduleKey">模块键名</param>
        /// <param name="sectionKey">章节键名</param>
        /// <param name="reportType">报告类型</param>
        /// <returns>章节排序</returns>
        private int GetSectionOrder(string moduleKey, string sectionKey, int reportType)
        {
            if (string.IsNullOrEmpty(moduleKey) || string.IsNullOrEmpty(sectionKey))
                return 0;

            try
            {
                var template = GetCommentReportTemplate((EnumReportType)reportType);
                var module = template.Modules?.FirstOrDefault(m => m.ModuleKey == moduleKey);
                var section = module?.Sections?.FirstOrDefault(s => s.SectionKey == sectionKey);
                return section?.SectionOrder ?? 0;
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取章节排序失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取评论用的报告模板
        /// </summary>
        /// <param name="reportType">报告类型</param>
        /// <returns>模板信息</returns>
        private GetReportTemplate_Out GetCommentReportTemplate(EnumReportType reportType)
        {
            try
            {
                var templateBll = new BLL_WorkReport();
                return templateBll.GetReportTemplate(reportType);
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取报告模板失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查用户是否有评论/点赞权限（只有接收人或抄送人可以评论和点赞，创建者只能查看）
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <param name="userId">用户ID</param>
        /// <returns>有权限返回true</returns>
        private bool HasCommentPermission(string reportId, string userId)
        {
            try
            {
                // 检查报告状态
                var report = DbOpe_crm_report_base.Instance.GetReportById(reportId);
                if (report == null)
                    return false;
                
                // 草稿状态的报告不允许评论
                if (report.Status == (int)EnumReportStatus.Draft)
                    return false;
                
                // 检查是否是报告创建者 - 创建者不能评论和点赞，只能查看
                if (report.CreateUser == userId)
                    return false;
                
                // 检查是否是接收人
                var isReceiver = DbOpe_crm_report_receiver.Instance.IsReportReceiver(reportId, userId);
                if (isReceiver)
                    return true;

                // 检查是否是抄送人
                var isCc = IsReportCcUser(reportId, userId);
                if (isCc)
                    return true;

                return false;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"检查评论权限失败，报告ID：{reportId}，用户ID：{userId}，错误：{ex.Message}");
                throw new ApiException($"检查评论权限失败，报告ID：{reportId}，用户ID：{userId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查用户是否为报告抄送人
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <param name="userId">用户ID</param>
        /// <returns>是抄送人返回true</returns>
        private bool IsReportCcUser(string reportId, string userId)
        {
            try
            {
                var ccList = DbOpe_crm_report_cc.Instance.GetDataList(x => x.ReportId == reportId && x.CcUserId == userId && x.Deleted == false);
                return ccList.Any();
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"检查抄送人权限失败，报告ID：{reportId}，用户ID：{userId}，错误：{ex.Message}");
                throw new ApiException($"检查抄送人权限失败，报告ID：{reportId}，用户ID：{userId}，错误：{ex.Message}");
            }
        }

        #endregion
    }
}
