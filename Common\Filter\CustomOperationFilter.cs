﻿using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Text.Json;
using static CRM2_API.Model.ControllersViewModel.VM_Collectioninfo;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;

namespace CRM2_API.Common.Filter
{
    public class CustomOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {

            if (operation.RequestBody != null && operation.RequestBody.Content.TryGetValue("multipart/form-data", out var openApiMediaType))
            {
                //if (openApiMediaType.Schema.Properties.ContainsKey("addTransactionReceiptIn"))
                //{
                //    var options = new JsonSerializerOptions { WriteIndented = true };
                //    var array = new OpenApiArray {
                //        new OpenApiString(JsonSerializer.Serialize(new AddTransactionReceipt_In { TransactionReceipt= "", ArrivalAmount=0, ArrivalDate=DateTime.Now, BankPaymentAmount=0, CashPaymentAmount=0, CollectingCompanyName="", Currency=1, FCArrivalAmount=0, IdentifyStatus=1, PaymentCompanyName="", PaymentMethod=1 }, options)),
                //    };
                //    openApiMediaType.Schema.Properties["addTransactionReceiptIn"].Example = array;
                //}

                if (openApiMediaType.Schema.Properties.ContainsKey("ProductInfo"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new AddProductInfo_In { CodesNum=0, Countrys="", OpeningMonths=0, FirstOpeningMonths=0, OpeningYears=0, PeriodsNum=0, PrimaryAccountsNum=0, ProductId=Guid.NewGuid(), SetNum=0, SubAccountsNum=0}, options)),
                    };
                    openApiMediaType.Schema.Properties["ProductInfo"].Example = array;
                }

                if (openApiMediaType.Schema.Properties.ContainsKey("ContractTemplate"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new AddContractTemplate_In { ContractTemplateId=Guid.NewGuid(), ContractDescription = "" }, options)),
                    };
                    openApiMediaType.Schema.Properties["ContractTemplate"].Example = array;
                }

                if (openApiMediaType.Schema.Properties.ContainsKey("InvoiceAttachmentInfo"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new BM_FileInfo {  Id = "", FilePath = "", FileName = "", CreateDate = "" }, options)),
                    };
                    openApiMediaType.Schema.Properties["InvoiceAttachmentInfo"].Example = array;
                }

                if (openApiMediaType.Schema.Properties.ContainsKey("ContractFileInfo"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new BM_FileInfo {  Id = "", FilePath = "", FileName = "", CreateDate = "" }, options)),
                    };
                    openApiMediaType.Schema.Properties["ContractFileInfo"].Example = array;
                }

                if (openApiMediaType.Schema.Properties.ContainsKey("ContractSpecialAttachFileInfo"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new BM_FileInfo {  Id = "", FilePath = "", FileName = "", CreateDate = "" }, options)),
                    };
                    openApiMediaType.Schema.Properties["ContractSpecialAttachFileInfo"].Example = array;
                }

                if (openApiMediaType.Schema.Properties.ContainsKey("InvoiceInfo"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new Invoice {  InvoiceNo = Guid.NewGuid().ToString(), InvoicedAmount = 0 })),
                    };
                    openApiMediaType.Schema.Properties["InvoiceInfo"].Example = array;
                }
                if (openApiMediaType.Schema.Properties.ContainsKey("ReportCustomer"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new ReportCustomer {
                            Id="",
                            DailyWorkReportId="",
                            CompanyName="",
                            CreditCode="",
                            CustomerLevel=-1,
                            Contacts = "",
                            ContactWay= "",
                            TrackingStage=-1,
                            CustomerSource=-1
                        }, options)),
                    };
                    openApiMediaType.Schema.Properties["ReportCustomer"].Example = array;
                }
                if (openApiMediaType.Schema.Properties.ContainsKey("ReportExpectedCustomer"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new ReportExpectedCustomer {
                            Id="",
                            MonthReportID="",
                            CustomerId=""
                        }, options)),
                    };
                    openApiMediaType.Schema.Properties["ReportExpectedCustomer"].Example = array;
                }
                if (openApiMediaType.Schema.Properties.ContainsKey("ReportReceiver"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new ReportReceiver {
                            Id="",
                            ReceiverId=""
                        }, options)),
                    };
                    openApiMediaType.Schema.Properties["ReportReceiver"].Example = array;
                }
                if (openApiMediaType.Schema.Properties.ContainsKey("ReportProgress"))
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var array = new OpenApiArray {
                        new OpenApiString(JsonSerializer.Serialize(new ReportProgress {
                            Id="",
                            TrackingRecordId=""
                        }, options)),
                    };
                    openApiMediaType.Schema.Properties["ReportProgress"].Example = array;
                }
            }
        }
    }
}
