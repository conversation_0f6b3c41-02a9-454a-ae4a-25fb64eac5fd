﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_privateservice")]
    public class Db_crm_privateservice
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:年份
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? Year { get; set; }

        /// <summary>
        /// Desc:人员Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// Desc:个人服务天数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? PrivateServiceDays { get; set; }

        /// <summary>
        /// Desc:状态(0 正常 1 过期)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? State { get; set; }

        /// <summary>
        /// Desc:已使用天数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? UsedDays { get; set; }

        /// <summary>
        /// Desc:待确认天数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ConfirmedDays { get; set; }

        /// <summary>
        /// Desc:付费天数(可以为负数)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ChargeDays { get; set; }

        /// <summary>
        /// Desc:发放说明
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Remark { get; set; }

        /// <summary>
        /// Desc:退还备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RollbackRemark { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
