﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_message_ignore")]
    public class Db_sys_message_ignore
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:接收人Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:相关业务主键ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string IgnoreMessageTypeToId {get;set;}

           /// <summary>
           /// Desc:消息副类型(客户/合同/发票)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? IgnoreMessageLocalType {get;set;}

           /// <summary>
           /// Desc:忽略过期时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? IgnoreDate {get;set;}

           /// <summary>
           /// Desc:是否删除
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? IsDelete {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

    }
}
