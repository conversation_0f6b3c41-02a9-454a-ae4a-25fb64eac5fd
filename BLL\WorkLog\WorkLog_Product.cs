﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;

namespace CRM2_API.BLL.WorkLog
{
    public class WorkLog_Product : BaseWorkLog<WorkLog_Product>
    {
        /// <summary>
        /// 记录产品操作日志
        /// </summary>
        /// <param name="ProductId"></param>
        /// <param name="enumProductLogType"></param>
        /// <param name="log"></param>
        public void AddProductWorkLog(string ProductId, EnumProductLogType enumProductLogType, string log)
        {
            var workLog = Com_WorkLog.Instance;
            var sysOpeProductLog = new Db_sys_operation_product_log();
            sysOpeProductLog.Id = Guid.NewGuid().ToString();
            sysOpeProductLog.productId = ProductId;
            sysOpeProductLog.userId = UserTokenInfo.id;
            sysOpeProductLog.operateName = RedisCache.UserInfo.GetUserInfo(UserTokenInfo.id).Name;
            sysOpeProductLog.operateType = enumProductLogType;
            sysOpeProductLog.sysLogId = workLog.Id;
            sysOpeProductLog.opterateTime = DateTime.Now;
            sysOpeProductLog.content = log;
            DbOpe_sys_operation_product_log.Instance.InsertQueue(sysOpeProductLog);
        }



    }
}
