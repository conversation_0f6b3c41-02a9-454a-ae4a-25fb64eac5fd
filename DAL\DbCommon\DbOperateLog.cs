﻿using SqlSugar;

namespace CRM2_API.DAL.DbCommon
{
    /// <summary>
    /// gtis数据库单表通用操作基础类
    /// </summary>
    /// <typeparam name="DbModel">数据库类</typeparam>
    /// <typeparam name="DbOperate">数据库操作类</typeparam>
    public class DbOperateLog<DbModel, DbOperate> : IDbOperate<DbModel, DbOperate> where DbModel : class, new() where DbOperate : class, new()
    {
        /// <summary>
        /// 操作的数据库
        /// </summary>
        protected override SqlSugarScope Db => DbContext.LogDb;
    }
}
