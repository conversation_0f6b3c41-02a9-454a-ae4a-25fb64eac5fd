﻿using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.System;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using System.Data;
using System.Linq.Dynamic.Core;
using NPOI.POIFS.Properties;
using System.Linq;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_ProductRules;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml.VariantTypes;
using CRM2_API.Common.JWT;
using JiebaNet.Segmenter.Common;
using NPOI.HSSF.Record;
using NPOI.Util;

namespace CRM2_API.BLL.Common
{
    public class ProductRules
    {
        public static List<ProductRules_Out> GetProductRules(List<ProductInfo_In> productInfoList, int country, int currency, string firstparty, string parentContractId = null)
        {
            List<ProductRules_Out> result = new List<ProductRules_Out>();
            List<Db_crm_product_rules> rules = DbOpe_crm_product_rules.Instance.GetDataAllList().ToList();
            //Db_crm_customer_subcompany subcompany = DbOpe_crm_customer_subcompany.Instance.GetDataById(firstParty);
            List<ProductInfo_In> productInfoInFuc = new List<ProductInfo_In>();
            productInfoInFuc = productInfoList.Copy();

            if (parentContractId.IsNotNullOrEmpty())
            {
                var parentContractProudctList = DbOpe_crm_contract_productinfo.Instance.GetIncreaseContractProduct(parentContractId);

                if (parentContractProudctList.Count > 0)
                {
                    productInfoInFuc.AddRange(parentContractProudctList);
                }
            }

            List<string> productIds = productInfoInFuc.Select(r => r.ProductId.ToString()).ToList();
            List<Db_crm_product> productList = DbOpe_crm_product.Instance.GetAllProductList(productIds);
            string ParentId = Guid.Empty.ToString();

            foreach (ProductInfo_In productInfo in productInfoInFuc)
            {
                Db_crm_product product;
                if (productInfo.ProductId == Guid.Empty)
                {
                    product = new Db_crm_product() { ProductName = "超级子账号", ProductType = EnumProductType.Gtis.ToInt() };
                }
                else
                {
                    product = productList.Where(r => r.Id == productInfo.ProductId.ToString()).First();
                }
                int ProductType = productInfo.ProductType.ToInt();
                List<Db_crm_product_rules> rulelist = rules.Where(r => r.ParentId == ParentId && r.ProductType == ProductType).ToList();
                ProductRules_Out productRules = null;
                foreach (Db_crm_product_rules rule in rulelist)
                {
                    bool isCheck = ProductRulesConditionCheck(productInfo, productInfoInFuc, rule.RulesKey, rule.RulesValue);
                    if (isCheck)
                    {
                        ProductRules_Out productRulesNew = CreateProductRules(productInfo, productInfoInFuc, rule, country, currency, product, firstparty);
                        if (productRulesNew == null)
                        {
                            continue;
                        }
                        if (productRules == null)
                        {
                            productRules = new ProductRules_Out();
                            productRules.ProductId = productInfo.ProductId;
                            productRules = productRulesNew;
                        }
                        else
                        {
                            //throw new ApiException("产品价格规则配置有问题");
                            if (productRules.PriceList.IsNotNull())
                            {
                                List<ProductRulesPrice> productRulesPrices = new List<ProductRulesPrice>();
                                foreach (ProductRulesPrice p in productRules.PriceList)
                                {
                                    if (productRulesNew.PriceList.Where(r => r.Id == p.Id).Any())
                                    {
                                        productRulesPrices.Add(p);
                                    }
                                }
                                //productRules.PriceList = productRulesPrices;
                                //productRules.PriceList = productRules.PriceList.Intersect(productRulesNew.PriceList).ToList();
                                //productRules.PriceList.Intersect(productRulesNew.PriceList);
                                productRules.PriceList.Concat(productRulesNew.PriceList).Distinct();

                                List<G4DbNames> g4DbNames = new List<G4DbNames>();
                                foreach (G4DbNames g4DbName in productRules.G4DbNames)
                                {
                                    if (productRulesNew.G4DbNames.Where(r => r.SID == g4DbName.SID).Any())
                                    {
                                        g4DbNames.Add(g4DbName);
                                    }
                                }
                                //productRules.G4DbNames = g4DbNames;
                                //productRules.G4DbNames = productRules.G4DbNames.Intersect(productRulesNew.G4DbNames).ToList();
                                //productRules.G4DbNames.Intersect(productRulesNew.G4DbNames);
                                productRules.G4DbNames.Concat(productRulesNew.G4DbNames).Distinct();
                            }
                        }
                    }
                }
                if (productRules == null)
                {
                    //添加产品的价格和国家范围 按默认添加，因为没有在价格限定范围内
                    productRules = new ProductRules_Out();
                    productRules.ProductId = productInfo.ProductId;
                    if (productInfo.ProductType == EnumProductType.Global)
                    {
                        //switch (currency)
                        //{
                        //    case 1: 
                        //        productRules.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceListByPriceMode(productInfo.ProductId.ToString(), EnumPriceMode.MainAccount.ToInt());
                        //        break;
                        //    case 2:
                        //        productRules.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceListByPriceMode(productInfo.ProductId.ToString(), EnumPriceMode.MainAccountStarting.ToInt());
                        //        break;
                        //    case 3:
                        //        productRules.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceListByPriceMode(productInfo.ProductId.ToString(), EnumPriceMode.MainAccountStarting.ToInt());
                        //        break;
                        //    default:
                        //        productRules.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceListByPriceMode(productInfo.ProductId.ToString(), EnumPriceMode.MainAccount.ToInt());
                        //        break;

                        //}
                        productRules.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceListByPriceMode(productInfo.ProductId.ToString(), EnumPriceMode.FixedAndStaringSale.ToInt());//EnumPriceMode.MainAccount.ToInt());
                        productRules.SubaccountsPriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceListByPriceMode(productInfo.ProductId.ToString(), EnumPriceMode.SubAccount.ToInt());
                    }
                    else if (productInfo.ProductType == EnumProductType.Vip)
                    {
                        //获取国家
                        List<int> countrys = new List<int>();
                        if (productInfo.Countrys.IsNotNullOrEmpty())
                        {
                            countrys = productInfo.Countrys.Split(",").Select(s => Convert.ToInt32(s)).ToList();
                        }

                        int countrysCount = DbOpe_sys_g4_dbnames.Instance.GetDataList(r => countrys.Contains(r.SID.Value)).GroupBy(r => r.BelongToSid).Count();

                        //获取价格本身                                                                                                                                                                            
                        productRules.PriceList = DbOpe_crm_product_price.Instance.GetDataList(r => r.ProductId == productInfo.ProductId.ToString() && r.SalesFloor == 0).Select(r => new ProductRulesPrice { Id = r.Id, ProductName = product.ProductName, ProductType = product.ProductType, EventMark = product.Remark, Currency = r.Currency, TotalPrice = countrysCount == 0 ? 0 : (r.Price + (r.PricePart2.Value * (countrysCount - 1))), Price = r.Price, PricePart1 = r.Price, PricePart2 = r.PricePart2.Value, PriceMode = r.PriceMode }).ToList();
                    }
                    else
                    {
                        productRules.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceList(productInfo.ProductId.ToString(), firstparty, country == 337 ? false : true);


                    }
                }
                result.Add(productRules);
                //result = GetProductRules(productInfo, productInfoList, rules, ParentId);
            }
            return result;
        }

        //public static List<ProductRules_Out> GetProductRules(ProductInfo_In productInfo, List<ProductInfo_In> productInfoList, List<Db_crm_product_rules> rules, string parentId)
        //{
        //    List<ProductRules_Out> result = new List<ProductRules_Out>();
        //    int ProductType = productInfo.ProductType.ToInt();
        //    List<Db_crm_product_rules> list = rules.Where(r => r.ParentId == parentId && r.ProductType == ProductType).ToList();
        //    foreach (Db_crm_product_rules rule in list)
        //    {
        //        ProductRules_Out productRules = CreateProductRules(productInfo, productInfoList, rule.RulesKey, rule.RulesValue);
        //        //result = GetProductRules(productInfo, productInfoList, rules, rule.Id);
        //        result.Add(productRules);
        //    }
        //    return result;
        //}

        private static bool ProductRulesConditionCheck(ProductInfo_In productInfo, List<ProductInfo_In> productInfoList, string rulesKey, string rulesValue)
        {
            bool result = false;
            List<ProductInfo_In> datalist = new List<ProductInfo_In>() { productInfo };
            if (rulesKey == "CombinationSales")
            {
                if (productInfoList.AsQueryable().Where(r => r.ProductId != productInfo.ProductId).ToList().Count() > 0)
                {
                    if (productInfoList.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                    {
                        result = true;
                    }
                }
            }
            if (rulesKey == "CombinationSales_NotContain")
            {
                if (productInfoList.AsQueryable().Where(r => r.ProductId != productInfo.ProductId).ToList().Count() > 0)
                {
                    if (productInfoList.AsQueryable().Where(rulesValue).ToList().Count() == 0)
                    {
                        result = true;
                    }
                }
            }
            if (rulesKey == "DefaultPrice")
            {
                if (datalist.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    result = true;
                    //result.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceList(productInfo.ProductId.ToString());
                }
            }
            if (rulesKey == "ProductCondition")
            {
                if (datalist.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    result = true;
                }
            }
            if (rulesKey == "ProductCondition_NotContain")
            {
                if (productInfoList.AsQueryable().Where(rulesValue).ToList().Count() == 0)
                {
                    result = true;
                }
            }
            if (rulesKey == "ProductCount")
            {
                if (productInfoList.Count == rulesValue.ToInt())
                {
                    result = true;
                }
            }
            if (rulesKey == "SubAccountsNum")
            {

                if (datalist.AsQueryable().Where(rulesValue).ToList().Count() > 0)
                {
                    result = true;
                }
            }
            if (rulesKey == "SimilarProduct")
            {
                if (productInfoList.AsQueryable().Where(r => r.ProductType == productInfo.ProductType).ToList().Count() > 1)
                {
                    result = true;
                }
            }
            return result;
        }

        private static ProductRules_Out CreateProductRules(ProductInfo_In productInfo, List<ProductInfo_In> productInfoList, Db_crm_product_rules rule, int country, int currency, Db_crm_product product, string firstparty)
        {
            ProductRules_Out result = null;
            //价格信息
            if (productInfo.ProductType == EnumProductType.Gtis)
            {
                //超级子账号
                List<Db_crm_product_rules_supersubaccount> supersubaccount = DbOpe_crm_product_rules_supersubaccount.Instance.GetDataList(r => r.ProductRulesId == rule.Id);
                if (supersubaccount.Count > 0)
                {
                    result = new ProductRules_Out();
                    result.ProductId = productInfo.ProductId;
                    result.RuleId = rule.Id;
                    result.PriceList = supersubaccount.Select(r => new ProductRulesPrice { Id = Guid.Empty.ToString(), ProductName = product.ProductName, ProductType = product.ProductType, EventMark = product.Remark, Currency = r.Currency.Value, Price = r.Price.Value, PriceMode = r.PriceMode.Value }).ToList();

                    //超级子账号必须和gtis同时存在
                    if (productInfoList.Where(r => (r.ProductType == EnumProductType.Gtis && r.ProductId != Guid.Empty) || r.ProductType == EnumProductType.Combination).Count() == 0)
                    {
                        result.Deleted = true;
                    }
                }
            }
            if (productInfo.ProductType == EnumProductType.Vip)
            {
                ////零售4.8万以上显示的国家
                //List<Db_crm_product_rules_abovepricedsupport> abovepricedsupportList = DbOpe_crm_product_rules_abovepricedsupport.Instance.GetDataList(r => r.ProductRulesId == rule.Id);

                //获取国家
                List<int> countrys = new List<int>();
                if (productInfo.Countrys.IsNotNullOrEmpty())
                {
                    countrys = productInfo.Countrys.Split(",").Select(s => Convert.ToInt32(s)).ToList();
                }

                int countrysCount = DbOpe_sys_g4_dbnames.Instance.GetDataList(r => countrys.Contains(r.SID.Value)).GroupBy(r => r.BelongToSid).Count();

                //获取价格本身  //默认价格优先级最低
                List<ProductRulesPrice> PriceList = DbOpe_crm_product_price.Instance.GetDataList(r => r.ProductId == productInfo.ProductId.ToString() && r.SalesFloor == 0).Select(r => new ProductRulesPrice { Id = r.Id, ProductName = product.ProductName, ProductType = product.ProductType, EventMark = product.Remark, Currency = r.Currency, TotalPrice = countrysCount == 0 ? 0 : (r.Price + (r.PricePart2.Value * (countrysCount - 1))), Price = r.Price, PricePart1 = r.Price, PricePart2 = r.PricePart2.Value, PriceMode = r.PriceMode }).ToList();

                //特殊零售价格销售的数据 价格 18000 的 俄罗斯  //包含俄罗斯优先级中
                List<Db_crm_product_rules_specialretailprice> specialretailpriceList = DbOpe_crm_product_rules_specialretailprice.Instance.GetDataList(r => r.ProductRulesId == rule.Id && countrys.Contains(r.Sid.Value) && r.Currency == currency);
                if (specialretailpriceList.Count > 0)
                {
                    if (specialretailpriceList.First().MinCountryNum > countrysCount)//countrys.Count)
                    {
                        foreach (ProductRulesPrice p in PriceList)
                        {
                            p.Currency = specialretailpriceList.First().Currency.Value;
                            p.PricePart1 = specialretailpriceList.First().Price.Value;
                            p.Price = specialretailpriceList.First().Price.Value;
                            p.TotalPrice = p.PricePart1; //+  (p.PricePart2 * (countrys.Count - 1));
                        }
                    }
                }

                //组合售卖  //组合售卖优先级最高
                List<Db_crm_product_rules_combinationsales> combinationsalesList = DbOpe_crm_product_rules_combinationsales.Instance.GetDataList(r => r.ProductRulesId == rule.Id);
                if (combinationsalesList.Count > 0)
                {
                    Guid emptyGuid = Guid.Empty;
                    List<string> ProductIds = productInfoList.Where(r => r.ProductId != emptyGuid).Select(r => r.ProductId.ToString()).ToList();
                    bool isHaveCombinationSales = combinationsalesList.Where(r => ProductIds.Contains(r.CombinationSalesProductId)).Any();
                    if (isHaveCombinationSales)
                    {
                        foreach (ProductRulesPrice p in PriceList)
                        {
                            p.Currency = p.Currency;
                            p.PricePart1 = 0;
                            p.Price = 0;
                            p.TotalPrice = (p.PricePart2 * countrysCount);//countrys.Count);
                        }
                    }
                }

                if (PriceList.Count > 0)
                {
                    result = new ProductRules_Out();
                    result.ProductId = productInfo.ProductId;
                    result.RuleId = rule.Id;
                }

                result.PriceList = PriceList;

                //2024年3月7日 修改 增加产品特殊验证，验证用户是否允许同时添加gits进/出口产品和vip零售
                //修改 2024年3月11日 增加角色验证，只在角色是普通销售的人员进行权限验证
                //普通销售角色ID： 9fbe8362-b96f-46da-902a-aae767aa8b94
                //销售主管角色ID：313f9d6b-8feb-462c-9bdb-703436a37026
                string UserId = TokenModel.Instance.id;
                var IsSaler = DbOpe_sys_userinrole.Instance.GetData(r => r.UserId == UserId);
                //是当前是普通销售角色，做验证
                List<string> GtisProductIds = productInfoList.Where(r => r.ProductType == EnumProductType.Gtis).Select(r => r.ProductId.ToString()).ToList();
                List<string> CombinationProductIds = productInfoList.Where(r => r.ProductType == EnumProductType.Combination).Select(r => r.ProductId.ToString()).ToList();

                if (IsSaler != null && (IsSaler.RoleId == "9fbe8362-b96f-46da-902a-aae767aa8b94" || IsSaler.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026"))
                {
                    var specialRules = DbOpe_crm_product_rules_special.Instance.GetData(r => r.UserId == UserId);

                    //依据人员规则不为空，以规则为优先判断
                    if (specialRules.IsNotNull())
                    {
                        if (GtisProductIds.Count > 0 || CombinationProductIds.Count > 0)
                        {
                            //gtis专业版出口
                            if (GtisProductIds.Contains("716c3f51-afd0-4087-8cd3-9f9db8225cad"))
                            {
                                if (!specialRules.AllowGtisExportVIP)
                                {
                                    result.Deleted = true;
                                }
                            }
                            else
                            //gtis专业版进口
                            if (GtisProductIds.Contains("761c3f51-afd0-4087-8cd3-9f9db8225cad"))
                            {
                                if (!specialRules.AllowGtisImportVIP)
                                {
                                    result.Deleted = true;
                                }
                            }
                            //gtis高级版和套餐版产品也不能增加VIP零售
                            else
                            {
                                if (!CombinationProductIds.Contains("7f75e303-5e07-11f0-9097-c025a58cf040"))
                                {
                                    result.Deleted = true;
                                }
                            }
                        }
                    }
                    else
                    {
                        //依据人员的规则为空，则vip不可和任意gtis组合
                        if (GtisProductIds.Count > 0 || CombinationProductIds.Count > 0)
                        {
                            if (!CombinationProductIds.Contains("7f75e303-5e07-11f0-9097-c025a58cf040"))
                            {
                                result.Deleted = true;
                            }
                        }
                    }

                }
                else
                {
                    //依据人员的规则为空，则vip不可和任意gtis组合
                    if (GtisProductIds.Count > 0 || CombinationProductIds.Count > 0)
                    {
                        if (!GtisProductIds.Contains("761c3f51-afd0-4087-8cd3-9f9db8225cad") && !GtisProductIds.Contains("716c3f51-afd0-4087-8cd3-9f9db8225cad") && !CombinationProductIds.Contains("7f75e303-5e07-11f0-9097-c025a58cf040"))
                            result.Deleted = true;
                    }
                }



            }
            if (productInfo.ProductType == EnumProductType.Other)
            {
                if (rule.ProductId != productInfo.ProductId.ToString())
                {
                    return result;
                }
                result = new ProductRules_Out();
                result.ProductId = productInfo.ProductId;
                result.RuleId = rule.Id;
                //result.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceList(productInfo.ProductId.ToString(),firstparty);
                int ProductType = productInfo.ProductType.ToInt();
                string _productId = productInfo.ProductId.ToString();
                List<Db_crm_product_rules_globalsearchandcollegeprice> newOtherRulesPrice = DbOpe_crm_product_rules_globalsearchandcollegeprice.Instance.GetDataList(r => r.ProductRulesId == rule.Id && r.ProductType == ProductType && r.ProductId == _productId).OrderBy(i => i.Price).ToList();

                if (newOtherRulesPrice.Count > 0)
                {
                    int isGtis = -1;
                    //判断是否是哪种组合Rules
                    if (productInfoList.Where(r => ((r.ProductType == EnumProductType.Gtis || r.ProductType == EnumProductType.Combination) && r.ProductId != Guid.Empty)).Count() > 0)
                    {
                        isGtis = (int)EnumProductType.Gtis;
                    }
                    if (productInfoList.Where(r => (r.ProductType == EnumProductType.Vip && r.ProductId != Guid.Empty)).Count() > 0)
                    {
                        isGtis = (int)EnumProductType.Vip;
                        //vip零售+Gtis一起买 按GTIS算
                        if (productInfoList.Where(r => ((r.ProductType == EnumProductType.Gtis) && r.ProductId != Guid.Empty)).Count() > 0)
                        {
                            isGtis = (int)EnumProductType.Gtis;
                        }

                    }

                    result = new ProductRules_Out();
                    result.ProductId = productInfo.ProductId;
                    result.RuleId = rule.Id;
                    result.PriceList = newOtherRulesPrice.Select(r => new ProductRulesPrice
                    {
                        Id = r.ProductPriceId,
                        ProductName = product.ProductName,
                        ProductType = product.ProductType,
                        EventMark = product.Remark,
                        Currency = r.Currency.Value,
                        Price = isGtis == (int)EnumProductType.Gtis ? r.PricePart2.Value : r.Price.Value, //r.Price.Value,
                        PricePart1 = r.Price.Value,
                        PricePart2 = r.PricePart2.Value,
                        PriceMode = r.PriceMode.Value
                    }).ToList();
                }
                else
                {
                    result.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceList(productInfo.ProductId.ToString(), firstparty);
                }



                bool IsLocalHsCodeNotSupport = DbOpe_crm_product_rules_localhscodenotsupport.Instance.IsHaveLocalHsCodeNotSupport(rule.Id, country);
                if (IsLocalHsCodeNotSupport)
                {
                    result.Deleted = true;
                }
            }
            if (productInfo.ProductType == EnumProductType.Global || productInfo.ProductType == EnumProductType.GlobalWitsSchool || productInfo.ProductType == EnumProductType.Periodicals)
            {
                //环球搜或慧思学院价格
                int ProductType = productInfo.ProductType.ToInt();
                List<Db_crm_product_rules_globalsearchandcollegeprice> globalsearchAndCollegepriceList = DbOpe_crm_product_rules_globalsearchandcollegeprice.Instance.GetDataList(r => r.ProductRulesId == rule.Id && r.ProductType == ProductType && r.ProductId == productInfo.ProductId.ToString()).OrderBy(i => i.Price).ToList();
                if (globalsearchAndCollegepriceList.Count > 0)
                {
                    result = new ProductRules_Out();
                    result.ProductId = productInfo.ProductId;
                    result.RuleId = rule.Id;
                    result.PriceList = globalsearchAndCollegepriceList.Select(r => new ProductRulesPrice { Id = r.ProductPriceId, ProductName = product.ProductName, ProductType = product.ProductType, EventMark = product.Remark, Currency = r.Currency.Value, Price = r.Price.Value, PricePart1 = r.Price.Value, PricePart2 = r.PricePart2.Value, PriceMode = r.PriceMode.Value }).ToList();
                }
                if (productInfoList.Where(l => l.ProductId == new Guid("7f75e303-5e07-11f0-9097-c025a58cf040")).Any())
                {
                    result.Deleted = true;
                }


                //环球搜慧思学院价格子账号价格
                if (productInfo.ProductType == EnumProductType.Global)
                {
                    //List<Db_crm_product_rules_globalsearch_subaccounts> globalsearchSubaccountsList = DbOpe_crm_product_rules_globalsearch_subaccounts.Instance.GetProductRulesGlobalsearchSubaccountsByProductRulesId(rule.Id, productInfo.ProductId.ToString());
                    //if (globalsearchSubaccountsList.Count > 0)
                    //{
                    //    if (result == null)
                    //    {
                    //        result = new ProductRules_Out();
                    //        result.ProductId = productInfo.ProductId;
                    //        result.RuleId = rule.Id;
                    //    }
                    //    result.SubaccountsPriceList = globalsearchSubaccountsList.Select(r => new ProductRulesPrice { Id = r.ProductPriceId, ProductName = product.ProductName, ProductType = product.ProductType, EventMark = product.Remark, Currency = r.Currency.Value, Price = r.Price.Value, PricePart1 = r.Price.Value, PriceMode = r.PriceMode.Value }).ToList();
                    //}
                    if (rule.RulesKey != "ProductCount")
                    {
                        if (result == null)
                        {
                            result = new ProductRules_Out();
                            result.ProductId = productInfo.ProductId;
                            result.RuleId = rule.Id;
                        }
                        //result.SubaccountsPriceList = result.PriceList;
                        result.SubaccountsPriceList = null;
                    }
                }
                if (rule.RulesKey == "SimilarProduct")
                {
                    if (result == null)
                    {
                        result = new ProductRules_Out();
                        result.ProductId = productInfo.ProductId;
                        result.RuleId = rule.Id;
                    }
                    result.Deleted = true;
                }
                if (rule.RulesKey == "CombinationSales")
                {
                    if (productInfo.ProductId == new Guid("7adb9adb-678b-11f0-8d7d-c025a58cf040"))
                    {
                        if (result == null)
                        {
                            result = new ProductRules_Out();
                            result.ProductId = productInfo.ProductId;
                            result.RuleId = rule.Id;
                        }
                        result.Deleted = true;
                    }
                }

            }

            if (productInfo.ProductType == EnumProductType.Combination)
            {
                var containProduct = DbOpe_crm_product.Instance.GetProductById(productInfo.ProductId.ToString());
                if (containProduct.ProductCombination.Where(p => p.ProductType == (int)EnumProductType.SalesWits).Any())
                {
                    result = new ProductRules_Out();
                    result.ProductId = productInfo.ProductId;
                    result.RuleId = rule.Id;
                    result.PriceList = containProduct.ProductPrice.Select(cp => new ProductRulesPrice
                    {
                        Id = cp.Id,
                        ProductName = cp.ProductName,
                        ProductType = (int)cp.ProductType,
                        EventMark = cp.EventMark,
                        Currency = cp.Currency,
                        Price = cp.Price,
                        PriceMode = cp.PriceMode,
                        PriceInfo = cp.PriceInfo,
                        TotalPrice = cp.Price,

                    }).ToList();

                    if (productInfoList.Where(r => r.ProductType == EnumProductType.SalesWits).Any())
                    {
                        result.Deleted = true;
                    }
                }
            }
            if (productInfo.ProductType == EnumProductType.SalesWits)
            {
                int ProductType = productInfo.ProductType.ToInt();
                //2025年7月15日 旧有逻辑，加载价格
                List<Db_crm_product_rules_globalsearchandcollegeprice> globalsearchAndCollegepriceList = DbOpe_crm_product_rules_globalsearchandcollegeprice.Instance.GetDataList(r => r.ProductRulesId == rule.Id && r.ProductType == ProductType).OrderBy(i => i.Price).ToList();
                if (globalsearchAndCollegepriceList.Count > 0)
                {
                    result = new ProductRules_Out();
                    result.ProductId = productInfo.ProductId;
                    result.RuleId = rule.Id;
                    result.PriceList = globalsearchAndCollegepriceList.Select(r => new ProductRulesPrice { Id = r.ProductPriceId, ProductName = product.ProductName, ProductType = product.ProductType, EventMark = product.Remark, Currency = r.Currency.Value, Price = r.Price.Value, PricePart1 = r.Price.Value, PricePart2 = r.PricePart2.Value, PriceMode = r.PriceMode.Value, PriceInfo = r.PriceType.Value }).ToList();
                }

                if (productInfoList.Where(r => r.ProductId == new Guid("7f75e303-5e07-11f0-9097-c025a58cf040")).Any() || productInfoList.Where(r => r.ProductType == EnumProductType.SalesWits).Count() > 1)
                {
                    if (result == null)
                    {
                        result = new ProductRules_Out();
                        result.ProductId = productInfo.ProductId;
                        result.RuleId = rule.Id;
                    }
                    result.Deleted = true;
                }
            }

            //2025年7月15日 增加新增的产品类型 仿照超级子账号做法，必须与salewaits同时存在
            if (productInfo.ProductType == EnumProductType.AddCredit)
            {
                //充值产品(目前唯一)
                var chargeProduct = DbOpe_crm_product.Instance.GetProductById(productInfo.ProductId.ToString());
                if (chargeProduct.IsNotNull())
                {
                    result = new ProductRules_Out();
                    result.ProductId = productInfo.ProductId;
                    result.RuleId = rule.Id;
                    result.PriceList = chargeProduct.ProductPrice.Select(r => new ProductRulesPrice { Id = Guid.Empty.ToString(), ProductName = product.ProductName, ProductType = product.ProductType, EventMark = product.Remark, Currency = r.Currency, Price = r.Price, PriceMode = r.PriceMode }).ToList();

                    //充值产品必须和saleswits同时存在
                    if (productInfoList.Where(r => (r.ProductType == EnumProductType.SalesWits && r.ProductId != Guid.Empty) || (r.ProductType == EnumProductType.Combination && r.ProductId == new Guid("7f75e303-5e07-11f0-9097-c025a58cf040"))).Count() == 0)
                    {
                        result.Deleted = true;
                    }
                }
            }

            if (productInfo.ProductType == EnumProductType.AdditionalResource)
            {
                //新增资源产品
                var AdditionalProduct = DbOpe_crm_product.Instance.GetProductById(productInfo.ProductId.ToString());
                if (AdditionalProduct.IsNotNull())
                {
                    result = new ProductRules_Out();
                    result.ProductId = productInfo.ProductId;
                    result.RuleId = rule.Id;
                    result.PriceList = AdditionalProduct.ProductPrice.Select(r => new ProductRulesPrice { Id = Guid.Empty.ToString(), ProductName = product.ProductName, ProductType = product.ProductType, EventMark = product.Remark, Currency = r.Currency, Price = r.Price, PriceMode = r.PriceMode }).ToList();

                    //新增资源产品必须和saleswits同时存在
                    if (productInfoList.Where(r => (r.ProductType == EnumProductType.SalesWits && r.ProductId != Guid.Empty) || (r.ProductType == EnumProductType.Combination && r.ProductId == new Guid("7f75e303-5e07-11f0-9097-c025a58cf040"))).Count() == 0)
                    {
                        result.Deleted = true;
                    }
                }
            }

            //判断是否符合活动的条件，不符合则删除
            if (productInfo.ProductType == EnumProductType.Event)
            {
                //特惠活动 //有规则但是不符合当前规则
                List<ProductRulesActivityProduct> activityList = DbOpe_crm_product_rules_activity.Instance.GetProductRulesActivityByProductRulesId(rule.Id);
                if (activityList.Count > 0)
                {
                    DateTime day = DateTime.Now;
                    Guid emptyGuid = Guid.Empty;
                    List<string> ProductIds = productInfoList.Where(r => r.ProductId != emptyGuid).Select(r => r.ProductId.ToString()).ToList();
                    bool isHaveActivity = activityList.Where(r => r.StartTime <= day && r.EndTime >= day && ProductIds.Contains(r.ProductId)).Any();
                    if (!isHaveActivity)
                    {
                        if (result == null)
                        {
                            result = new ProductRules_Out();
                            result.ProductId = productInfo.ProductId;
                            result.RuleId = rule.Id;
                            result.PriceList = DbOpe_crm_product_price.Instance.GetProductRulesPriceList(productInfo.ProductId.ToString(), firstparty);
                        }
                        result.Deleted = true;
                    }
                }
            }

            //国家信息
            if (productInfo.ProductType == EnumProductType.Vip || productInfo.ProductType == EnumProductType.Other)
            {
                if (result == null)
                {
                    result = new ProductRules_Out();
                    result.ProductId = productInfo.ProductId;
                    result.RuleId = rule.Id;
                }

                List<G4DbNames> G4DbNamesList = BLL_G4DbNames.Instance.GetG4DbNames();

                //不支持零售的数据
                List<Db_crm_product_rules_notsupport> notsupport = DbOpe_crm_product_rules_notsupport.Instance.GetDataList(r => r.ProductRulesId == rule.Id);
                List<int> notsupportList = notsupport.Select(r => r.Sid.Value).ToList();
                result.G4DbNames = G4DbNamesList.Where(r => !notsupportList.Contains(r.SID.Value)).ToList();

                //不支持数据源当地销售及使用的数据
                List<Db_crm_product_rules_localdatasourcesnotsupport> localdatasourcesnotsupport = DbOpe_crm_product_rules_localdatasourcesnotsupport.Instance.GetDataList(r => r.ProductRulesId == rule.Id && r.SalesLocalCountryId == country);
                if (localdatasourcesnotsupport.Count > 0)
                {
                    List<int> localdatasourcesnotsupportList = localdatasourcesnotsupport.Select(r => r.Sid.Value).ToList();
                    result.G4DbNames = result.G4DbNames.Where(r => !localdatasourcesnotsupportList.Contains(r.SID.Value)).ToList();
                }

                if (country == 337)
                {
                    //需要判断  产品类型  不单独销售     专业性需要区分进出口  其他不需要区分
                    bool IsHaveGtis = productInfoList.Where(r => r.ProductType == EnumProductType.Gtis || r.ProductType == EnumProductType.Vip).Any();
                    if (IsHaveGtis)
                    {
                        List<string> ProductIds = productInfoList.Where(r => r.ProductType == EnumProductType.Gtis || r.ProductType == EnumProductType.Vip).Select(r => r.ProductId.ToString()).ToList();
                        List<Db_crm_product_rules_supportchina_producttype> supportChinaProductType = DbOpe_crm_product_rules_supportchina_producttype.Instance.GetDataList(r => r.ProductRulesId == rule.Id && ProductIds.Contains(r.ProductId));
                        List<int> CType = supportChinaProductType.Select(r => r.CType.Value).ToList();
                        //仅支持中国
                        List<Db_crm_product_rules_supportchina> supportchina = DbOpe_crm_product_rules_supportchina.Instance.GetSupportChinaByContractId(rule.Id, CType);// DbOpe_crm_product_rules_supportchina.Instance.GetDataList(r => r.ProductRulesId == rule.Id);
                        List<int> supportchinaList = supportchina.Select(r => r.Sid.Value).ToList();
                        List<G4DbNames> addList = G4DbNamesList.Where(r => supportchinaList.Contains(r.SID.Value)).ToList();
                        result.G4DbNames.AddRange(addList);
                    }
                }

                //零售4.8万以上显示的国家 gtis 高级版以上包含的国家
                //decimal Price = productInfoList.Sum(r => (r.ContractProductinfoPriceTotal + r.ParentContractProductinfoPriceTotal));
                //List<Db_crm_product_rules_abovepricedsupport> abovepricedsupport = DbOpe_crm_product_rules_abovepricedsupport.Instance.GetDataList(r => r.ProductRulesId == rule.Id && r.Price <= Price && r.Currency == currency);
                //if (abovepricedsupport.Count > 0)
                //{
                //    List<int> abovepricedsupportList = abovepricedsupport.Select(r => r.Sid.Value).ToList();
                //    List<G4DbNames> addList = G4DbNamesList.Where(r => abovepricedsupportList.Contains(r.SID.Value)).ToList();
                //    result.G4DbNames.AddRange(addList);
                //}

                //List<Db_crm_product_rules_abovepricedsupport> abovepricedsupport_del = DbOpe_crm_product_rules_abovepricedsupport.Instance.GetDataList(r => r.ProductRulesId == rule.Id && r.Price > Price && r.Currency == currency);
                //if (abovepricedsupport_del.Count > 0)
                //{
                //    List<int> abovepricedsupportDelList = abovepricedsupport_del.Select(r => r.Sid.Value).ToList();
                //    result.G4DbNames = result.G4DbNames.Where(r => !abovepricedsupportDelList.Contains(r.SID.Value)).ToList();
                //}

                Guid gtiszz = new Guid("22baeab3-6d67-4a30-8ac3-68df11771736");
                Guid gtisgj = new Guid("4a0ffc9a-6a44-4362-a76a-07492d0a81fa");
                List<string> GtisProductIds = productInfoList.Where(r => r.ProductType == EnumProductType.Gtis && (r.ProductId == gtiszz || r.ProductId == gtisgj)).Select(r => r.ProductId.ToString()).ToList();
                if (GtisProductIds.Count > 0)
                {
                    decimal Price = productInfoList.Sum(r => (r.ContractProductinfoPriceTotal + r.ParentContractProductinfoPriceTotal));
                    List<Db_crm_product_rules_abovepricedsupport> abovepricedsupport = DbOpe_crm_product_rules_abovepricedsupport.Instance.GetDataList(r => r.ProductRulesId == rule.Id && r.Price <= Price && r.Currency == currency);
                    if (abovepricedsupport.Count > 0)
                    {
                        List<int> abovepricedsupportList = abovepricedsupport.Select(r => r.Sid.Value).ToList();
                        List<G4DbNames> addList = G4DbNamesList.Where(r => abovepricedsupportList.Contains(r.SID.Value)).ToList();
                        result.G4DbNames.AddRange(addList);
                    }
                }

                if (GtisProductIds.Count == 0)
                {
                    List<Db_crm_product_rules_abovepricedsupport> abovepricedsupport_del = DbOpe_crm_product_rules_abovepricedsupport.Instance.GetDataList(r => r.ProductRulesId == rule.Id);
                    if (abovepricedsupport_del.Count > 0)
                    {
                        List<int> abovepricedsupportDelList = abovepricedsupport_del.Select(r => r.Sid.Value).ToList();
                        result.G4DbNames = result.G4DbNames.Where(r => !abovepricedsupportDelList.Contains(r.SID.Value)).ToList();
                    }
                }

                result.G4DbNames = result.G4DbNames.Distinct().ToList();
                ////特殊零售价格销售的数据 价格 18000 的 俄罗斯
                //List<Db_crm_product_rules_specialretailprice> specialretailprice = DbOpe_crm_product_rules_specialretailprice.Instance.GetDataList(r => r.ProductRulesId == rule.Id);
            }

            return result;
        }
    }
}
