using System;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.Enum;
using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.System;
using Newtonsoft.Json;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告周报月报业务逻辑类（数据库版本）
    /// </summary>
    public partial class BLL_WorkReportDatabase : BaseBLL<BLL_WorkReportDatabase>
    {
        #region 周报月报生成

        /// <summary>
        /// 生成周报（数据库版本）
        /// </summary>
        /// <param name="input">生成周报输入</param>
        /// <returns>生成结果</returns>
        public VM_WeeklyMonthlyResult_Out GenerateWeeklyReport(GenerateWeeklyReport_In input)
        {
            try
            {
                // 参数验证
                if (input == null)
                {
                    throw new ApiException("输入参数不能为空");
                }

                if (input.StartDate >= input.EndDate)
                {
                    throw new ApiException("开始日期必须小于结束日期");
                }

                // 获取当前用户信息
                var currentUserId = GetCurrentUserId();
                var currentUserName = GetCurrentUserName();
                var userTeamInfo = GetUserTeamInfo(currentUserId);

                // 计算周数
                var weekNumber = GetWeekOfYear(input.StartDate);
                var reportId = Guid.NewGuid().ToString();
                var reportTitle = $"{input.StartDate:yyyy}年第{weekNumber}周工作周报";

                // 查询该周期内的日报数据
                var dailyReports = GetDailyReportsInPeriod(currentUserId, input.StartDate, input.EndDate);
                
                if (dailyReports.Count == 0)
                {
                    return new VM_WeeklyMonthlyResult_Out
                    {
                        Success = false,
                        Message = "该时间段内没有找到日报数据，无法生成周报"
                    };
                }

                // 创建周报基础记录
                var reportBase = CreateWeeklyReportBase(reportId, reportTitle, currentUserId, currentUserName, 
                    userTeamInfo, input.StartDate, input.EndDate, weekNumber, input.IsAutoGenerate);

                // 保存周报基础信息
                DbOpe_crm_report_base.Instance.Insert(reportBase);

                // 生成周报内容
                if (input.IsAutoGenerate)
                {
                    // 自动汇总生成
                    GenerateWeeklyReportContent(reportId, dailyReports, reportBase);
                }
                else
                {
                    // 手动输入内容
                    SaveManualWeeklyContent(reportId, input, reportBase);
                }

                // 保存默认接收人和抄送人
                SaveDefaultReceiversAndCc(reportId, currentUserId);

                // 更新报告数量字段（包括附件、客户、点赞、评论、接收人、抄送人数量）
                UpdateReportCounts(reportId);

                return new VM_WeeklyMonthlyResult_Out
                {
                    Success = true,
                    Message = "周报生成成功",
                    ReportId = reportId,
                    ReportTitle = reportTitle,
                    SourceReportCount = dailyReports.Count,
                    GenerateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"生成周报失败：{ex.Message}", ex);
                throw new ApiException($"生成周报失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 生成月报（数据库版本）
        /// </summary>
        /// <param name="input">生成月报输入</param>
        /// <returns>生成结果</returns>
        public VM_WeeklyMonthlyResult_Out GenerateMonthlyReport(GenerateMonthlyReport_In input)
        {
            try
            {
                // 参数验证
                if (input == null)
                {
                    throw new ApiException("输入参数不能为空");
                }

                if (input.Month < 1 || input.Month > 12)
                {
                    throw new ApiException("月份必须在1-12之间");
                }

                // 获取当前用户信息
                var currentUserId = GetCurrentUserId();
                var currentUserName = GetCurrentUserName();
                var userTeamInfo = GetUserTeamInfo(currentUserId);

                var reportId = Guid.NewGuid().ToString();
                var reportTitle = $"{input.Year}年{input.Month}月工作月报";

                // 根据生成方式查询源数据
                List<Db_crm_report_base> sourceReports;
                if (input.GenerateType == 1)
                {
                    // 基于日报汇总
                    sourceReports = GetDailyReportsInMonth(currentUserId, input.Year, input.Month);
                }
                else
                {
                    // 基于周报汇总
                    sourceReports = GetWeeklyReportsInMonth(currentUserId, input.Year, input.Month);
                }

                if (sourceReports.Count == 0)
                {
                    var sourceType = input.GenerateType == 1 ? "日报" : "周报";
                    return new VM_WeeklyMonthlyResult_Out
                    {
                        Success = false,
                        Message = $"该月份内没有找到{sourceType}数据，无法生成月报"
                    };
                }

                // 创建月报基础记录
                var reportDate = new DateTime(input.Year, input.Month, 1);
                var reportBase = CreateMonthlyReportBase(reportId, reportTitle, currentUserId, currentUserName,
                    userTeamInfo, reportDate, input.IsAutoGenerate);

                // 保存月报基础信息
                DbOpe_crm_report_base.Instance.Insert(reportBase);

                // 生成月报内容
                if (input.IsAutoGenerate)
                {
                    // 自动汇总生成
                    GenerateMonthlyReportContent(reportId, sourceReports, reportBase, input.GenerateType);
                }
                else
                {
                    // 手动输入内容
                    SaveManualMonthlyContent(reportId, input, reportBase);
                }

                // 保存默认接收人和抄送人
                SaveDefaultReceiversAndCc(reportId, currentUserId);

                // 更新报告数量字段（包括附件、客户、点赞、评论、接收人、抄送人数量）
                UpdateReportCounts(reportId);

                return new VM_WeeklyMonthlyResult_Out
                {
                    Success = true,
                    Message = "月报生成成功",
                    ReportId = reportId,
                    ReportTitle = reportTitle,
                    SourceReportCount = sourceReports.Count,
                    GenerateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"生成月报失败：{ex.Message}", ex);
                throw new ApiException($"生成月报失败：{ex.Message}");
            }
        }

        #endregion

        #region 内容生成方法

        /// <summary>
        /// 生成周报内容（自动汇总）
        /// </summary>
        private void GenerateWeeklyReportContent(string reportId, List<Db_crm_report_base> dailyReports, Db_crm_report_base reportBase)
        {
            // 汇总工作量数据
            AggregateWeeklyWorkData(reportId, dailyReports, reportBase);

            // 汇总文本内容
            AggregateWeeklyTextContent(reportId, dailyReports, reportBase);

            // 汇总客户记录
            AggregateWeeklyCustomerRecords(reportId, dailyReports, reportBase);
        }

        /// <summary>
        /// 生成月报内容（自动汇总）
        /// </summary>
        private void GenerateMonthlyReportContent(string reportId, List<Db_crm_report_base> sourceReports,
            Db_crm_report_base reportBase, int generateType)
        {
            // 汇总工作量数据
            AggregateMonthlyWorkData(reportId, sourceReports, reportBase, generateType);

            // 汇总文本内容
            AggregateMonthlyTextContent(reportId, sourceReports, reportBase, generateType);

            // 汇总客户记录
            AggregateMonthlyCustomerRecords(reportId, sourceReports, reportBase, generateType);
        }

        /// <summary>
        /// 保存手动输入的周报内容
        /// </summary>
        private void SaveManualWeeklyContent(string reportId, GenerateWeeklyReport_In input, Db_crm_report_base reportBase)
        {
            var now = DateTime.Now;

            // 保存工作总结内容
            if (!string.IsNullOrEmpty(input.ManualWorkSummary))
            {
                var workSummaryContent = new Db_crm_report_content
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Weekly,
                    ReportTitle = reportBase.Title,
                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = "work_summary",
                    ModuleTitle = "工作总结",
                    ModuleOrder = 1,
                    SectionKey = "summary_content",
                    SectionTitle = "总结内容",
                    SectionOrder = 1,
                    Content = input.ManualWorkSummary,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_content.Instance.Insert(workSummaryContent);
            }

            // 保存下周计划内容
            if (!string.IsNullOrEmpty(input.ManualNextPlan))
            {
                var nextWeekPlanContent = new Db_crm_report_content
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Weekly,
                    ReportTitle = reportBase.Title,
                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = "next_week_plan",
                    ModuleTitle = "下周计划",
                    ModuleOrder = 2,
                    SectionKey = "plan_content",
                    SectionTitle = "计划内容",
                    SectionOrder = 1,
                    Content = input.ManualNextPlan,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_content.Instance.Insert(nextWeekPlanContent);
            }
        }

        /// <summary>
        /// 保存手动输入的月报内容
        /// </summary>
        private void SaveManualMonthlyContent(string reportId, GenerateMonthlyReport_In input, Db_crm_report_base reportBase)
        {
            var now = DateTime.Now;

            // 保存工作总结内容
            if (!string.IsNullOrEmpty(input.ManualWorkSummary))
            {
                var workSummaryContent = new Db_crm_report_content
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Monthly,
                    ReportTitle = reportBase.Title,
                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = "work_summary",
                    ModuleTitle = "工作总结",
                    ModuleOrder = 1,
                    SectionKey = "summary_content",
                    SectionTitle = "总结内容",
                    SectionOrder = 1,
                    Content = input.ManualWorkSummary,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_content.Instance.Insert(workSummaryContent);
            }

            // 保存下月计划内容
            if (!string.IsNullOrEmpty(input.ManualNextPlan))
            {
                var nextMonthPlanContent = new Db_crm_report_content
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Monthly,
                    ReportTitle = reportBase.Title,
                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = "next_month_plan",
                    ModuleTitle = "下月计划",
                    ModuleOrder = 2,
                    SectionKey = "plan_content",
                    SectionTitle = "计划内容",
                    SectionOrder = 1,
                    Content = input.ManualNextPlan,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_content.Instance.Insert(nextMonthPlanContent);
            }
        }

        #endregion

        #region 数据查询方法

        /// <summary>
        /// 获取指定时间段内的日报数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>日报列表</returns>
        private List<Db_crm_report_base> GetDailyReportsInPeriod(string userId, DateTime startDate, DateTime endDate)
        {
            return DbOpe_crm_report_base.Instance.GetDataList(x =>
                x.UserId == userId &&
                x.ReportType == (int)EnumReportType.Daily &&
                x.ReportDate >= startDate.Date &&
                x.ReportDate <= endDate.Date &&
                x.Status == (int)EnumReportStatus.Submitted &&
                x.Deleted == false
            ).OrderBy(x => x.ReportDate).ToList();
        }

        /// <summary>
        /// 获取指定月份的日报数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="year">年份</param>
        /// <param name="month">月份</param>
        /// <returns>日报列表</returns>
        private List<Db_crm_report_base> GetDailyReportsInMonth(string userId, int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);

            return DbOpe_crm_report_base.Instance.GetDataList(x =>
                x.UserId == userId &&
                x.ReportType == (int)EnumReportType.Daily &&
                x.ReportDate >= startDate &&
                x.ReportDate <= endDate &&
                x.Status == (int)EnumReportStatus.Submitted &&
                x.Deleted == false
            ).OrderBy(x => x.ReportDate).ToList();
        }

        /// <summary>
        /// 获取指定月份的周报数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="year">年份</param>
        /// <param name="month">月份</param>
        /// <returns>周报列表</returns>
        private List<Db_crm_report_base> GetWeeklyReportsInMonth(string userId, int year, int month)
        {
            return DbOpe_crm_report_base.Instance.GetDataList(x =>
                x.UserId == userId &&
                x.ReportType == (int)EnumReportType.Weekly &&
                x.ReportYear == year &&
                x.ReportMonth == month &&
                x.Status == (int)EnumReportStatus.Submitted &&
                x.Deleted == false
            ).OrderBy(x => x.ReportDate).ToList();
        }

        #endregion

        #region 报告基础记录创建

        /// <summary>
        /// 创建周报基础记录
        /// </summary>
        private Db_crm_report_base CreateWeeklyReportBase(string reportId, string reportTitle,
            string userId, string userName, (string TeamId, string TeamName) teamInfo, DateTime startDate, DateTime endDate,
            int weekNumber, bool isAutoGenerate)
        {
            var now = DateTime.Now;
            
            // 计算提交状态，如果计算失败则默认为准时
            EnumSubmitStatus submitStatus;
            try
            {
                submitStatus = CalculateSubmitStatus((int)EnumReportStatus.Submitted, now, startDate, EnumReportType.Weekly);
            }
            catch (Exception ex)
            {
                throw new ApiException($"计算周报提交状态失败，开始日期：{startDate}，错误：{ex.Message}");
            }

            return new Db_crm_report_base
            {
                Id = reportId,
                ReportType = (int)EnumReportType.Weekly,
                Title = reportTitle,
                UserId = userId,
                UserName = userName,
                TeamId = teamInfo.TeamId,
                TeamName = teamInfo.TeamName,
                Status = (int)EnumReportStatus.Submitted,
                SubmitStatus = (int)submitStatus,
                ReportDate = startDate,
                ReportYear = startDate.Year,
                ReportMonth = startDate.Month,
                ReportWeek = weekNumber,
                IsAutoGenerated = isAutoGenerate,
                SubmitTime = now,
                CreateUser = userId,
                CreateDate = now,
                UpdateUser = userId,
                UpdateDate = now,
                Deleted = false
            };
        }

        /// <summary>
        /// 创建月报基础记录
        /// </summary>
        private Db_crm_report_base CreateMonthlyReportBase(string reportId, string reportTitle,
            string userId, string userName, (string TeamId, string TeamName) teamInfo, DateTime reportDate, bool isAutoGenerate)
        {
            var now = DateTime.Now;
            
            // 计算提交状态，如果计算失败则默认为准时
            EnumSubmitStatus submitStatus;
            try
            {
                submitStatus = CalculateSubmitStatus((int)EnumReportStatus.Submitted, now, reportDate, EnumReportType.Monthly);
            }
            catch (Exception ex)
            {
                throw new ApiException($"计算月报提交状态失败，报告日期：{reportDate}，错误：{ex.Message}");
            }

            return new Db_crm_report_base
            {
                Id = reportId,
                ReportType = (int)EnumReportType.Monthly,
                Title = reportTitle,
                UserId = userId,
                UserName = userName,
                TeamId = teamInfo.TeamId,
                TeamName = teamInfo.TeamName,
                Status = (int)EnumReportStatus.Submitted,
                SubmitStatus = (int)submitStatus,
                ReportDate = reportDate,
                ReportYear = reportDate.Year,
                ReportMonth = reportDate.Month,
                ReportWeek = null,
                IsAutoGenerated = isAutoGenerate,
                SubmitTime = now,
                CreateUser = userId,
                CreateDate = now,
                UpdateUser = userId,
                UpdateDate = now,
                Deleted = false
            };
        }

        #endregion

        #region 数据汇总方法

        /// <summary>
        /// 汇总周报工作量数据
        /// </summary>
        private void AggregateWeeklyWorkData(string reportId, List<Db_crm_report_base> dailyReports, Db_crm_report_base reportBase)
        {
            var reportIds = dailyReports.Select(x => x.Id).ToList();
            if (reportIds.Count == 0) return;

            // 查询所有日报的工作量数据
            var workDataList = DbOpe_crm_report_work_data.Instance.GetDataList(x =>
                reportIds.Contains(x.ReportId) && x.Deleted == false);

            // 按数据类型分组汇总
            var aggregatedData = workDataList
                .GroupBy(x => new { x.ModuleKey, x.SectionKey, x.DataType })
                .Select(g => new
                {
                    g.Key.ModuleKey,
                    g.Key.SectionKey,
                    g.Key.DataType,
                    TotalValue = g.Sum(x => x.DataValue),
                    ModuleTitle = g.First().ModuleTitle,
                    SectionTitle = g.First().SectionTitle,
                    ModuleOrder = g.First().ModuleOrder,
                    SectionOrder = g.First().SectionOrder
                })
                .ToList();

            var now = DateTime.Now;

            // 保存汇总后的工作量数据
            foreach (var data in aggregatedData)
            {
                var weeklyWorkData = new Db_crm_report_work_data
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Weekly,

                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = data.ModuleKey,
                    ModuleTitle = data.ModuleTitle,
                    ModuleOrder = data.ModuleOrder,
                    SectionKey = data.SectionKey,
                    SectionTitle = data.SectionTitle,
                    SectionOrder = data.SectionOrder,
                    DataType = data.DataType,
                    DataValue = data.TotalValue,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_work_data.Instance.Insert(weeklyWorkData);
            }
        }

        /// <summary>
        /// 汇总月报工作量数据
        /// </summary>
        private void AggregateMonthlyWorkData(string reportId, List<Db_crm_report_base> sourceReports,
            Db_crm_report_base reportBase, int generateType)
        {
            var reportIds = sourceReports.Select(x => x.Id).ToList();
            if (reportIds.Count == 0) return;

            // 查询源报告的工作量数据
            var workDataList = DbOpe_crm_report_work_data.Instance.GetDataList(x =>
                reportIds.Contains(x.ReportId) && x.Deleted == false);

            // 按数据类型分组汇总
            var aggregatedData = workDataList
                .GroupBy(x => new { x.ModuleKey, x.SectionKey, x.DataType })
                .Select(g => new
                {
                    g.Key.ModuleKey,
                    g.Key.SectionKey,
                    g.Key.DataType,
                    TotalValue = g.Sum(x => x.DataValue),
                    ModuleTitle = g.First().ModuleTitle,
                    SectionTitle = g.First().SectionTitle,
                    ModuleOrder = g.First().ModuleOrder,
                    SectionOrder = g.First().SectionOrder
                })
                .ToList();

            var now = DateTime.Now;

            // 保存汇总后的工作量数据
            foreach (var data in aggregatedData)
            {
                var monthlyWorkData = new Db_crm_report_work_data
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Monthly,

                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = data.ModuleKey,
                    ModuleTitle = data.ModuleTitle,
                    ModuleOrder = data.ModuleOrder,
                    SectionKey = data.SectionKey,
                    SectionTitle = data.SectionTitle,
                    SectionOrder = data.SectionOrder,
                    DataType = data.DataType,
                    DataValue = data.TotalValue,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_work_data.Instance.Insert(monthlyWorkData);
            }
        }

        /// <summary>
        /// 汇总周报文本内容
        /// </summary>
        private void AggregateWeeklyTextContent(string reportId, List<Db_crm_report_base> dailyReports, Db_crm_report_base reportBase)
        {
            var reportIds = dailyReports.Select(x => x.Id).ToList();
            if (reportIds.Count == 0) return;

            // 查询所有日报的文本内容
            var contentList = DbOpe_crm_report_content.Instance.GetDataList(x =>
                reportIds.Contains(x.ReportId) && x.Deleted == false)
                .OrderBy(x => x.ReportDate).ThenBy(x => x.ModuleOrder).ThenBy(x => x.SectionOrder)
                .ToList();

            // 按模块和章节分组合并内容
            var groupedContent = contentList
                .GroupBy(x => new { x.ModuleKey, x.SectionKey })
                .ToList();

            var now = DateTime.Now;

            foreach (var group in groupedContent)
            {
                var contents = group.OrderBy(x => x.ReportDate).Select(x => x.Content).Where(x => !string.IsNullOrEmpty(x)).ToList();
                if (contents.Count == 0) continue;

                var mergedContent = string.Join("\n\n", contents);
                var firstItem = group.First();

                var weeklyContent = new Db_crm_report_content
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Weekly,
                    ReportTitle = reportBase.Title,
                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = firstItem.ModuleKey,
                    ModuleTitle = firstItem.ModuleTitle,
                    ModuleOrder = firstItem.ModuleOrder,
                    SectionKey = firstItem.SectionKey,
                    SectionTitle = firstItem.SectionTitle,
                    SectionOrder = firstItem.SectionOrder,
                    Content = mergedContent,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_content.Instance.Insert(weeklyContent);
            }
        }

        /// <summary>
        /// 汇总月报文本内容
        /// </summary>
        private void AggregateMonthlyTextContent(string reportId, List<Db_crm_report_base> sourceReports,
            Db_crm_report_base reportBase, int generateType)
        {
            var reportIds = sourceReports.Select(x => x.Id).ToList();
            if (reportIds.Count == 0) return;

            // 查询源报告的文本内容
            var contentList = DbOpe_crm_report_content.Instance.GetDataList(x =>
                reportIds.Contains(x.ReportId) && x.Deleted == false)
                .OrderBy(x => x.ReportDate).ThenBy(x => x.ModuleOrder).ThenBy(x => x.SectionOrder)
                .ToList();

            // 按模块和章节分组合并内容
            var groupedContent = contentList
                .GroupBy(x => new { x.ModuleKey, x.SectionKey })
                .ToList();

            var now = DateTime.Now;

            foreach (var group in groupedContent)
            {
                var contents = group.OrderBy(x => x.ReportDate).Select(x => x.Content).Where(x => !string.IsNullOrEmpty(x)).ToList();
                if (contents.Count == 0) continue;

                var mergedContent = string.Join("\n\n", contents);
                var firstItem = group.First();

                var monthlyContent = new Db_crm_report_content
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Monthly,
                    ReportTitle = reportBase.Title,
                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = firstItem.ModuleKey,
                    ModuleTitle = firstItem.ModuleTitle,
                    ModuleOrder = firstItem.ModuleOrder,
                    SectionKey = firstItem.SectionKey,
                    SectionTitle = firstItem.SectionTitle,
                    SectionOrder = firstItem.SectionOrder,
                    Content = mergedContent,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_content.Instance.Insert(monthlyContent);
            }
        }

        /// <summary>
        /// 汇总周报客户记录
        /// </summary>
        private void AggregateWeeklyCustomerRecords(string reportId, List<Db_crm_report_base> dailyReports, Db_crm_report_base reportBase)
        {
            var reportIds = dailyReports.Select(x => x.Id).ToList();
            if (reportIds.Count == 0) return;

            // 查询所有日报的客户记录
            var customerRecords = DbOpe_crm_report_customer.Instance.GetDataList(x =>
                reportIds.Contains(x.ReportId) && x.Deleted == false)
                .ToList();

            // 按客户去重（同一客户在多个日报中出现时只保留一条记录）
            var uniqueCustomers = customerRecords
                .GroupBy(x => new { x.CustomerId, x.ModuleKey, x.SectionKey })
                .Select(g => g.OrderByDescending(x => x.CreateDate).First())
                .ToList();

            var now = DateTime.Now;

            // 保存去重后的客户记录到周报
            foreach (var customer in uniqueCustomers)
            {
                var weeklyCustomer = new Db_crm_report_customer
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Weekly,
                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = customer.ModuleKey,
                    ModuleTitle = customer.ModuleTitle,
                    ModuleOrder = customer.ModuleOrder,
                    SectionKey = customer.SectionKey,
                    SectionTitle = customer.SectionTitle,
                    SectionOrder = customer.SectionOrder,
                    CustomerId = customer.CustomerId,
                    CustomerName = customer.CustomerName,
                    CustomerLevel = customer.CustomerLevel,
                    CustomerCountry = customer.CustomerCountry,
                    CustomerIndex = customer.CustomerIndex,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_customer.Instance.Insert(weeklyCustomer);
            }
        }

        /// <summary>
        /// 汇总月报客户记录
        /// </summary>
        private void AggregateMonthlyCustomerRecords(string reportId, List<Db_crm_report_base> sourceReports,
            Db_crm_report_base reportBase, int generateType)
        {
            var reportIds = sourceReports.Select(x => x.Id).ToList();
            if (reportIds.Count == 0) return;

            // 查询源报告的客户记录
            var customerRecords = DbOpe_crm_report_customer.Instance.GetDataList(x =>
                reportIds.Contains(x.ReportId) && x.Deleted == false)
                .ToList();

            // 按客户去重（同一客户在多个报告中出现时只保留一条记录）
            var uniqueCustomers = customerRecords
                .GroupBy(x => new { x.CustomerId, x.ModuleKey, x.SectionKey })
                .Select(g => g.OrderByDescending(x => x.CreateDate).First())
                .ToList();

            var now = DateTime.Now;

            // 保存去重后的客户记录到月报
            foreach (var customer in uniqueCustomers)
            {
                var monthlyCustomer = new Db_crm_report_customer
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportId = reportId,
                    ReportType = (int)EnumReportType.Monthly,
                    UserId = reportBase.UserId,
                    UserName = reportBase.UserName,
                    TeamId = reportBase.TeamId,
                    TeamName = reportBase.TeamName,
                    ReportDate = reportBase.ReportDate,
                    ReportYear = reportBase.ReportYear,
                    ReportMonth = reportBase.ReportMonth,
                    ReportWeek = reportBase.ReportWeek,
                    ModuleKey = customer.ModuleKey,
                    ModuleTitle = customer.ModuleTitle,
                    ModuleOrder = customer.ModuleOrder,
                    SectionKey = customer.SectionKey,
                    SectionTitle = customer.SectionTitle,
                    SectionOrder = customer.SectionOrder,
                    CustomerId = customer.CustomerId,
                    CustomerName = customer.CustomerName,
                    CustomerLevel = customer.CustomerLevel,
                    CustomerCountry = customer.CustomerCountry,
                    CustomerIndex = customer.CustomerIndex,
                    CreateUser = reportBase.UserId,
                    CreateDate = now,
                    UpdateUser = reportBase.UserId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_report_customer.Instance.Insert(monthlyCustomer);
            }
        }

        #endregion

        #region 工具方法



        /// <summary>
        /// 保存默认接收人和抄送人
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <param name="userId">用户ID</param>
        private void SaveDefaultReceiversAndCc(string reportId, string userId)
        {
            try
            {
                // 获取用户的默认接收人配置
                var defaultReceivers = GetDefaultReceiversForUser(userId);
                var now = DateTime.Now;

                // 保存接收人
                foreach (var receiver in defaultReceivers.Receivers)
                {
                    var reportReceiver = new Db_crm_report_receiver
                    {
                        Id = Guid.NewGuid().ToString(),
                        ReportId = reportId,
                        ReceiverId = receiver.UserId,
                        ReceiverName = receiver.UserName,
                        IsDefault = true,
                        Status = (int)EnumReceiverStatus.Unread,
                        CreateUser = userId,
                        CreateDate = now,
                        UpdateUser = userId,
                        UpdateDate = now,
                        Deleted = false
                    };
                    DbOpe_crm_report_receiver.Instance.Insert(reportReceiver);
                }

                // 保存抄送人
                foreach (var cc in defaultReceivers.CcUsers)
                {
                    var reportCc = new Db_crm_report_cc
                    {
                        Id = Guid.NewGuid().ToString(),
                        ReportId = reportId,
                        CcUserId = cc.UserId,
                        CcUserName = cc.UserName,
                        IsDefault = true,
                        Status = (int)EnumReceiverStatus.Unread,
                        CreateUser = userId,
                        CreateDate = now,
                        UpdateUser = userId,
                        UpdateDate = now,
                        Deleted = false
                    };
                    DbOpe_crm_report_cc.Instance.Insert(reportCc);
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"保存默认接收人和抄送人失败：{ex.Message}", ex);
                throw new ApiException($"保存默认接收人和抄送人失败：{ex.Message}");
                // 不抛出异常，避免影响主流程
            }
        }

        /// <summary>
        /// 获取用户的默认接收人配置
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>默认接收人配置</returns>
        private (List<UserInfo> Receivers, List<UserInfo> CcUsers) GetDefaultReceiversForUser(string userId)
        {
            var receivers = new List<UserInfo>();
            var ccUsers = new List<UserInfo>();

            try
            {
                // 只根据IsDefault标志查询用户的默认接收人设置
                var receiverRecords = DbOpe_crm_report_receiver.Instance.GetDataList(x => 
                    x.CreateUser == userId && 
                    x.IsDefault == true && 
                    x.Deleted == false);
                
                // 获取不重复的默认接收人
                var uniqueReceivers = receiverRecords
                    .GroupBy(x => x.ReceiverId)
                    .Select(g => g.First())
                    .ToList();
                
                foreach (var receiver in uniqueReceivers)
                {
                    receivers.Add(new UserInfo
                    {
                        UserId = receiver.ReceiverId,
                        UserName = receiver.ReceiverName
                    });
                }
                
                // 只根据IsDefault标志查询用户的默认抄送人设置
                var ccRecords = DbOpe_crm_report_cc.Instance.GetDataList(x => 
                    x.CreateUser == userId && 
                    x.IsDefault == true && 
                    x.Deleted == false);
                
                // 获取不重复的默认抄送人
                var uniqueCcs = ccRecords
                    .GroupBy(x => x.CcUserId)
                    .Select(g => g.First())
                    .ToList();
                
                foreach (var cc in uniqueCcs)
                {
                    ccUsers.Add(new UserInfo
                    {
                        UserId = cc.CcUserId,
                        UserName = cc.CcUserName
                    });
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取用户默认接收人配置失败：{ex.Message}", ex);
                throw new ApiException($"获取用户默认接收人配置失败：{ex.Message}");
            }

            return (receivers, ccUsers);
        }

        /// <summary>
        /// 用户信息类
        /// </summary>
        private class UserInfo
        {
            public string UserId { get; set; }
            public string UserName { get; set; }
        }



        /// <summary>
        /// 获取当前用户名
        /// </summary>
        /// <returns>用户名</returns>
        private string GetCurrentUserName()
        {
            try
            {
                var userId = GetCurrentUserId();
                var user = DbOpe_sys_user.Instance.GetUserById(userId);
                return user?.UserName ?? $"用户{userId}";
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取用户名失败：{ex.Message}", ex);
                throw new ApiException($"获取用户名失败：{ex.Message}");
            }
        }

        #endregion

        #region 工作日历

        /// <summary>
        /// 获取工作日历数据
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>工作日历数据</returns>
        public VM_WorkCalendar GetWorkCalendar(GetWorkCalendar_In input)
        {
            try
            {
                // 参数验证
                if (input == null)
                {
                    throw new ApiException("查询条件不能为空");
                }

                if (input.ReportDate == DateTime.MinValue)
                {
                    throw new ApiException("报告日期不能为空");
                }

                // 从报告日期获取年月
                int year = input.ReportDate.Year;
                int month = input.ReportDate.Month;
                string targetUserId = StringUtil.IsNotNullOrEmpty(input.TargetUserId) ? input.TargetUserId : UserId;
                // 生成日历数据
                var calendarDays = GenerateCalendarDaysFromDatabase(year, month, input.ReportTypeFilter, targetUserId);
                var monthlyStats = GenerateMonthlyStatisticsFromDatabase(year, month, targetUserId);

                return new VM_WorkCalendar
                {
                    Year = year,
                    Month = month,
                    CalendarDays = calendarDays,
                    MonthlyStatistics = monthlyStats
                };
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取工作日历失败：{ex.Message}", ex);
                throw new ApiException($"获取工作日历失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 从数据库生成日历数据
        /// </summary>
        private List<VM_CalendarDay> GenerateCalendarDaysFromDatabase(int year, int month, EnumReportType? reportTypeFilter,string targetUserId)
        {
            var calendarDays = new List<VM_CalendarDay>();
            var firstDayOfMonth = new DateTime(year, month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            var today = DateTime.Today;

            // 获取月份第一天是星期几，计算需要显示的上月日期
            var firstDayOfWeek = (int)firstDayOfMonth.DayOfWeek;
            if (firstDayOfWeek == 0) firstDayOfWeek = 7; // 周日为7

            // 计算日历显示的日期范围
            var startDate = firstDayOfMonth.AddDays(-(firstDayOfWeek - 1));
            var endDate = lastDayOfMonth.AddDays(42 - (firstDayOfWeek - 1 + lastDayOfMonth.Day));

            // 从数据库获取该时间范围内的所有报告
            var reports = GetReportsForCalendar(startDate, endDate, reportTypeFilter, targetUserId);

            // 添加上月的日期
            for (var i = firstDayOfWeek - 1; i > 0; i--)
            {
                var date = firstDayOfMonth.AddDays(-i);
                calendarDays.Add(CreateCalendarDayFromDatabase(date, false, today, reports));
            }

            // 添加当月的日期
            for (var day = 1; day <= lastDayOfMonth.Day; day++)
            {
                var date = new DateTime(year, month, day);
                calendarDays.Add(CreateCalendarDayFromDatabase(date, true, today, reports));
            }

            // 添加下月的日期，补齐42天（6周）
            var remainingDays = 42 - calendarDays.Count;
            for (var i = 1; i <= remainingDays; i++)
            {
                var date = lastDayOfMonth.AddDays(i);
                calendarDays.Add(CreateCalendarDayFromDatabase(date, false, today, reports));
            }

            return calendarDays;
        }

        /// <summary>
        /// 获取日历显示用的报告数据
        /// </summary>
        private Dictionary<string, List<VM_CalendarReport>> GetReportsForCalendar(DateTime startDate, DateTime endDate, EnumReportType? reportTypeFilter, string targetUserId)
        {
            var result = new Dictionary<string, List<VM_CalendarReport>>();

            try
            {
                // 获取当前用户ID
                var currentUserId = GetCurrentUserId();

                // 如果没有指定目标用户ID，则使用当前用户ID
                var userId = !string.IsNullOrEmpty(targetUserId) ? targetUserId : currentUserId;

                // 根据当前用户的权限过滤报告
                var filteredReports = GetReportsWithPermissionFilter(startDate, endDate, reportTypeFilter, currentUserId, userId);

                // 处理查询结果
                foreach (var report in filteredReports)
                {
                    var dateKey = report.ReportDate.ToString("yyyy-MM-dd");
                    if (!result.ContainsKey(dateKey))
                        result[dateKey] = new List<VM_CalendarReport>();

                    string reportTypeDescription = "";
                    string title = "";

                    switch ((EnumReportType)report.ReportType)
                    {
                        case EnumReportType.Daily:
                            reportTypeDescription = "日报";
                            title = $"{report.ReportDate:MM月dd日}工作日报";
                            break;
                        case EnumReportType.Weekly:
                            reportTypeDescription = "周报";
                            title = $"第{report.ReportWeek}周工作周报";
                            break;
                        case EnumReportType.Monthly:
                            reportTypeDescription = "月报";
                            title = $"{report.ReportYear}年{report.ReportMonth}月工作月报";
                            break;
                    }

                    result[dateKey].Add(new VM_CalendarReport
                    {
                        Id = report.Id,
                        ReportType = (EnumReportType)report.ReportType,
                        ReportTypeDescription = reportTypeDescription,
                        Title = title,
                        Status = (EnumReportStatus)report.Status,
                        StatusColor = GetReportStatusColorString((EnumReportStatus)report.Status),
                        SubmitTime = report.SubmitTime?.ToString("yyyy-MM-dd HH:mm") ?? report.CreateDate.ToString("yyyy-MM-dd HH:mm")
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取日历报告数据失败：{ex.Message}", ex);
                return result; // 返回空结果，不影响日历显示
            }
        }

        /// <summary>
        /// 根据权限过滤获取报告列表
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="reportTypeFilter">报告类型过滤</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="targetUserId">目标用户ID</param>
        /// <returns>过滤后的报告列表</returns>
        private List<Db_crm_report_base> GetReportsWithPermissionFilter(DateTime startDate, DateTime endDate, EnumReportType? reportTypeFilter, string currentUserId, string targetUserId)
        {
            try
            {
                // 1. 检查是否是系统管理员（后台人员）
                if (IsSystemAdmin(currentUserId))
                {
                    // 系统管理员可以查看所有报告
                    var adminQuery = DbOpe_crm_report_base.Instance.GetDataList(r =>
                        r.Deleted == false &&
                        r.UserId == targetUserId &&
                        r.ReportDate >= startDate &&
                        r.ReportDate <= endDate &&
                        r.Status == (int)EnumReportStatus.Submitted);

                    if (reportTypeFilter.HasValue)
                    {
                        adminQuery = adminQuery.Where(r => r.ReportType == (int)reportTypeFilter.Value).ToList();
                    }

                    return adminQuery;
                }

                // 2. 销售总监和普通用户都按照个人权限进行筛选
                return GetReportsWithPersonalPermissionFilter(startDate, endDate, reportTypeFilter, currentUserId, targetUserId);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"根据权限过滤报告失败：{ex.Message}", ex);
                return new List<Db_crm_report_base>();
            }
        }

        /// <summary>
        /// 根据个人权限过滤报告（销售总监和普通用户使用）
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="reportTypeFilter">报告类型过滤</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="targetUserId">目标用户ID</param>
        /// <returns>过滤后的报告列表</returns>
        private List<Db_crm_report_base> GetReportsWithPersonalPermissionFilter(DateTime startDate, DateTime endDate, EnumReportType? reportTypeFilter, string currentUserId, string targetUserId)
        {
            try
            {
                // 普通用户权限过滤
                var permissionExp = SqlSugar.Expressionable.Create<Db_crm_report_base>();

                // 基础条件
                permissionExp.And(r => r.Deleted == false &&
                                     r.ReportDate >= startDate &&
                                     r.ReportDate <= endDate &&
                                     r.Status == (int)EnumReportStatus.Submitted);

                // 如果指定了目标用户，则只查询该用户的报告
                if (!string.IsNullOrEmpty(targetUserId))
                {
                    permissionExp.And(r => r.UserId == targetUserId);
                }

                // 权限条件：当前用户可以查看的报告
                var permissionConditions = SqlSugar.Expressionable.Create<Db_crm_report_base>();

                // 1. 自己创建的报告
                permissionConditions.Or(r => r.CreateUser == currentUserId);

                // 2. 自己是接收人的报告
                var receivedReportIds = DbOpe_crm_report_receiver.Instance.GetDataList(x =>
                    x.ReceiverId == currentUserId && x.Deleted == false)
                    .Select(x => x.ReportId).ToList();
                if (receivedReportIds.Any())
                {
                    permissionConditions.Or(r => receivedReportIds.Contains(r.Id));
                }

                // 3. 自己是抄送人的报告
                var ccReportIds = DbOpe_crm_report_cc.Instance.GetDataList(x =>
                    x.CcUserId == currentUserId && x.Deleted == false)
                    .Select(x => x.ReportId).ToList();
                if (ccReportIds.Any())
                {
                    permissionConditions.Or(r => ccReportIds.Contains(r.Id));
                }

                permissionExp.And(permissionConditions.ToExpression());

                // 执行查询
                var query = DbOpe_crm_report_base.Instance.GetDataList(permissionExp.ToExpression());

                // 如果有报告类型过滤，则添加过滤条件
                if (reportTypeFilter.HasValue)
                {
                    query = query.Where(r => r.ReportType == (int)reportTypeFilter.Value).ToList();
                }

                return query;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"根据个人权限过滤报告失败：{ex.Message}", ex);
                return new List<Db_crm_report_base>();
            }
        }



        /// <summary>
        /// 创建日历日期数据
        /// </summary>
        private VM_CalendarDay CreateCalendarDayFromDatabase(DateTime date, bool isCurrentMonth, DateTime today, Dictionary<string, List<VM_CalendarReport>> reports)
        {
            var dateKey = date.ToString("yyyy-MM-dd");
            var dayReports = reports.ContainsKey(dateKey) ? reports[dateKey] : new List<VM_CalendarReport>();

            // 判断是否为工作日（使用数据库中的节假日数据）
            var holidayDbOpe = DbOpe_crm_holiday.Instance;
            var isWorkday = holidayDbOpe.IsWorkday(date);

            // 计算报告状态
            var reportStatus = CalculateReportStatus(dayReports);

            return new VM_CalendarDay
            {
                Date = date.ToString("yyyy-MM-dd"),
                Day = date.Day,
                DayOfWeek = (int)date.DayOfWeek == 0 ? 7 : (int)date.DayOfWeek,
                IsWorkday = isWorkday,
                IsToday = date.Date == today.Date,
                IsCurrentMonth = isCurrentMonth,
                Reports = dayReports,
                ReportStatus = reportStatus,
                StatusColor = GetCalendarStatusColor(reportStatus, isWorkday)
            };
        }

        /// <summary>
        /// 计算报告状态
        /// </summary>
        private int CalculateReportStatus(List<VM_CalendarReport> reports)
        {
            if (!reports.Any()) return 0; // 无报告

            var hasDaily = reports.Any(r => r.ReportType == EnumReportType.Daily);
            var hasWeekly = reports.Any(r => r.ReportType == EnumReportType.Weekly);
            var hasMonthly = reports.Any(r => r.ReportType == EnumReportType.Monthly);

            var typeCount = (hasDaily ? 1 : 0) + (hasWeekly ? 1 : 0) + (hasMonthly ? 1 : 0);

            if (typeCount > 1) return 4; // 多种报告
            if (hasDaily) return 1; // 有日报
            if (hasWeekly) return 2; // 有周报
            if (hasMonthly) return 3; // 有月报

            return 0;
        }

        /// <summary>
        /// 获取日历状态颜色
        /// </summary>
        private string GetCalendarStatusColor(int reportStatus, bool isWorkday)
        {
            if (!isWorkday) return "gray";

            return reportStatus switch
            {
                0 => "red",      // 无报告
                1 => "green",    // 有日报
                2 => "blue",     // 有周报
                3 => "purple",   // 有月报
                4 => "gold",     // 多种报告
                _ => "gray"
            };
        }

        /// <summary>
        /// 从数据库生成月度统计数据
        /// </summary>
        private VM_MonthlyStatistics GenerateMonthlyStatisticsFromDatabase(int year, int month, string targetUserId = null)
        {
            try
            {
                var startDate = new DateTime(year, month, 1);
                var endDate = startDate.AddMonths(1).AddDays(-1);

                // 计算工作日数量
                var holidayDbOpe = DbOpe_crm_holiday.Instance;
                var workdayCount = 0;
                for (var date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    if (holidayDbOpe.IsWorkday(date))
                        workdayCount++;
                }

                // 计算实际统计数据
                var currentUserId = GetCurrentUserId();
                var userId = !string.IsNullOrEmpty(targetUserId) ? targetUserId : currentUserId;
                var monthStart = new DateTime(year, month, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                // 根据权限过滤获取报告基础表中的数据
                var reportBaseList = GetReportsWithPermissionFilter(monthStart, monthEnd, null, currentUserId, userId);
                
                // 获取各类报告数量
                var dailyReportCount = reportBaseList.Count(r => r.ReportType == (int)EnumReportType.Daily && r.Status == (int)EnumReportStatus.Submitted);
                var weeklyReportCount = reportBaseList.Count(r => r.ReportType == (int)EnumReportType.Weekly && r.Status == (int)EnumReportStatus.Submitted);
                var monthlyReportCount = reportBaseList.Count(r => r.ReportType == (int)EnumReportType.Monthly && r.Status == (int)EnumReportStatus.Submitted);
                
                // 获取日报列表
                var dailyReports = reportBaseList.Where(r => r.ReportType == (int)EnumReportType.Daily).ToList();
                
                // 获取按时提交、迟交和未提交的报告数量
                var onTimeSubmissions = dailyReports.Count(r => r.SubmitStatus == (int)EnumSubmitStatus.OnTime);
                var lateSubmissions = dailyReports.Count(r => r.SubmitStatus == (int)EnumSubmitStatus.Late);
                
                // 计算未提交的报告数量（工作日总数减去已提交的报告数量）
                var missedSubmissions = Math.Max(0, workdayCount - dailyReportCount);
                
                // 计算按时提交率
                var onTimeRate = workdayCount > 0 ? (double)onTimeSubmissions / workdayCount * 100 : 0;
                
                // 计算总报告数
                var totalReports = dailyReportCount + weeklyReportCount + monthlyReportCount;

                return new VM_MonthlyStatistics
                {
                    TotalDailyReports = dailyReportCount,
                    TotalWeeklyReports = weeklyReportCount,
                    TotalMonthlyReports = monthlyReportCount,
                    OnTimeSubmissions = onTimeSubmissions,
                    LateSubmissions = lateSubmissions,
                    MissedSubmissions = missedSubmissions,
                    OnTimeRate = (decimal)onTimeRate,
                    OnTimeRateDisplay = $"{onTimeRate:F1}%"
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"生成月度统计数据失败：{ex.Message}", ex);

                // 返回默认统计数据
                return new VM_MonthlyStatistics
                {
                    TotalDailyReports = 0,
                    TotalWeeklyReports = 0,
                    TotalMonthlyReports = 0,
                    OnTimeSubmissions = 0,
                    LateSubmissions = 0,
                    MissedSubmissions = 0,
                    OnTimeRate = 0,
                    OnTimeRateDisplay = "0.0%"
                };
            }
        }

        /// <summary>
        /// 获取报告状态颜色（字符串版本，用于日历显示）
        /// </summary>
        private string GetReportStatusColorString(EnumReportStatus status)
        {
            return status switch
            {
                EnumReportStatus.Draft => "gray",
                EnumReportStatus.Submitted => "blue",
                _ => "gray"
            };
        }



        #endregion
    }
}
