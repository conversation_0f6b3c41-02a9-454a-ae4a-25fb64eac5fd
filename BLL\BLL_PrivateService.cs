﻿using Aspose.Cells;
using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.PerformanceObjectives;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Wordprocessing;
using LgyUtil;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Data;
using System.Diagnostics;
using System.IO;
using static CRM2_API.Model.BLLModel.Enum.PrivateServiceEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_PrivateService;

namespace CRM2_API.BLL
{
    public class BLL_PrivateService : BaseBLL<BLL_PrivateService>
    {
        /// <summary>
        /// 获取当前登陆人可用个人服务时间天数
        /// </summary>
        /// <returns></returns>
        public DurationInfo GetDuration()
        {
            return DbOpe_crm_privateservice.Instance.GetDuration();
        }


        /// <summary>
        /// 申请使用个人服务时间
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="applId">申请ID(可以为空)</param>
        /// <param name="privateServiceType">更新状态(使用中/已使用/作废)</param>
        /// <param name="useDays">免费天数(已使用和作废不填)</param>
        /// <param name="chargeDays">超额天数(已使用和作废不填)</param>
        public void UsingPrivateServiceForContractService(string contractId, EnumPrivateServiceType privateServiceType, string applId, int useDays = 0, int chargeDays = 0)
        {
            DbOpe_crm_privateservice_detail.Instance.UsingPrivateServiceForContractService(contractId, privateServiceType, applId, useDays, chargeDays);
        }


        /// <summary>
        /// 导入个人服务时间数据
        /// </summary>
        /// <param name="importPrivateServiceData_In"></param>
        /// <param name="operateType">导入后操作类型, 1 == 将导入数据作为JSON返回, 非1 == 直接入库</param>
        /// <returns></returns>
        public IActionResult ImportPrivateServiceData(ImportPrivateServiceData_In importPrivateServiceData_In, int operateType = 1)
        {
            IFormFile file = importPrivateServiceData_In.File;
            string fileName = file.FileName;
            string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
            Workbook workbook = null;
            if (fileExtension.Equals("xlsx") || fileExtension.Equals("xls"))
            {
                workbook = new Workbook(file.OpenReadStream());
            }
            else
            {
                return new JsonResult(new
                {
                    result = "Error",
                    Msg = "请检查文件是否选择正确!"
                });
            }
            int CheckYear = importPrivateServiceData_In.Year_In;
            if (DbOpe_crm_privateservice.Instance.GetYearDataCount(CheckYear) > 0)
            {
                return new JsonResult(new
                {
                    result = "Error",
                    Msg = "已存在所选年度的记录!"
                });
            }



            Worksheet worksheet = workbook.Worksheets[0];

            Cells cells = worksheet.Cells;
            List<ImportPrivateServiceData_Out> resultList = new();


            //主要使用列 : 姓名  身份证号  天数 备注
            DataTable dataTable = cells.ExportDataTableAsString(0, 0, cells.MaxDataRow + 1, cells.MaxDataColumn + 1, true);
            // 显示数据表中的数据
            foreach (DataRow row in dataTable.Rows)
            {
                ImportPrivateServiceData_Out result = new();
                result.Year = importPrivateServiceData_In.Year_In;
                if (row["姓名"].ToString().IsNullOrEmpty())
                {
                    //空行直接退出了
                    break;
                }
                result.UserId = DbOpe_sys_user.Instance.GetUserIdByIDno(row["身份证号"].ToString(), "");
                if (result.UserId.IsNullOrEmpty())
                {
                    continue;
                }
                result.State = 0;
                result.PrivateServiceDays = row["奖励天数"].ToIntNullable();
                result.UsedDays = row["已使用天数"].ToIntNullable() == null ? 0 : row["已使用天数"].ToIntNullable();
                result.ConfirmedDays = 0;
                result.ChargeDays = 0;
                if (row.Table.Columns.Contains("备注"))
                {
                    result.Remark = row["备注"].ToString();
                }
                else
                {
                    result.Remark = string.Empty;
                }
                //result.OrgName = row["所属组织"].ToString();

                resultList.Add(result);

                Db_crm_privateservice temp = result.MappingTo<Db_crm_privateservice>();


                DbOpe_crm_privateservice.Instance.InsertDataQueue(temp);


            }

            if (operateType != 1)
            {
                //执行直接入库流程
                int count = DbOpe_crm_privateservice.Instance.SaveQueues();
                //UpdatePerformanceObjectives(updatePerformanceObjectivesIn, true);
                return new JsonResult(new
                {
                    result = "Success",
                    Msg = "共计" + dataTable.Rows.Count + "条数据，插入" + count + "条数据成功!",
                });
            }
            else
            {
                return new JsonResult(new
                {
                    result = "Success",
                    Msg = resultList,
                });
            }
        }


        /// <summary>
        /// 导出个人服务情况使用详情
        /// </summary>
        /// <param name="privateServiceDetail_In">导出参数</param>
        /// <returns></returns>
        public Stream GetPrivateServiceDetailDataDownload(PrivateServiceDetail_In privateServiceDetail_In)
        {
            ExcelExporterNPOI exp = new ExcelExporterNPOI();
            var downloaddata = DbOpe_crm_privateservice_detail.Instance.GetPrivateServiceDetailDataDownload(privateServiceDetail_In);
            var expBytes = exp.ExportAsByteArray(downloaddata);

            return new MemoryStream(expBytes);
        }


        public void UpdateGetBackDetail()
        {
            List<Db_crm_privateservice_detail> BackDetailList = DbOpe_crm_privateservice_detail.Instance.GetBackDetailList();
            BackDetailList.ForEach(item =>
            {
                UsingPrivateServiceForContractService(item.ContractId, EnumPrivateServiceType.GiveBack, item.ContractServiceInfoGtisId, item.UsedDays.Value, item.ChargeDays.Value);
            });
        }

        public PrivcateServiceResult OperatedRollBackPrivateServiceDay(RollBackPrivateServiceDetail_In rollBackPrivateServiceDetail_In)
        {
            return DbOpe_crm_privateservice_detail.Instance.OperatedRollBackPrivateServiceDay(rollBackPrivateServiceDetail_In);
        }
    }
}
