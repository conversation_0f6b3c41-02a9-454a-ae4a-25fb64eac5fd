﻿using CRM2_API.Common.AppSetting;
using CSRedis;

namespace CRM2_API.Common.Cache
{
    /// <summary>
    /// redis缓存，在此文件中使用RedisHelper来操作，其它地方尽量不要使用，方便维护
    /// </summary>
    public partial class RedisCache
    {
        /// <summary>
        /// 初始化redis连接
        /// </summary>
        public static void InitReidsCache()
        {
            var client = new CSRedisClient(AppSettings.ConnectionStrings.Redis);
            RedisHelper.Initialization(client);
            
            // 初始化缓存数据
            CacheInit.InitAllCache();
        }
    }
}
