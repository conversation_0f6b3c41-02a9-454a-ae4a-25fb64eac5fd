﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.ContractTemplate;
using CRM2_API.Model.ControllersViewModel.ContractTemplateApply;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;
using static CRM2_API.Model.ControllersViewModel.VM_ContractCreatedTemplate;

namespace CRM2_API.Controllers
{
    [Description("合同创建模板控制器")]
    public class ContractCreateTemplateController : MyControllerBase
    {
        public ContractCreateTemplateController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        /// <summary>
        /// 获取模板选择列表(下拉菜单)
        /// </summary>
        /// <param name="searchCreateTemplateList_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<SearchCreateTemplateList_Out> SearchCreateTemplateList(SearchCreateTemplateList_In searchCreateTemplateList_In)
        {
            return DbOpe_crm_contract_createdtemplate.Instance.SearchCreateTemplateList(searchCreateTemplateList_In);
        }
        /// <summary>
        /// 根据ID获取模板
        /// </summary>
        /// <param name="createTemplateSimpleIn"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public CreateTemplateBase GetCreateTemplateById(CreateTemplateSimpleIn createTemplateSimpleIn)
        {
            return DbOpe_crm_contract_createdtemplate.Instance.GetCreateTemplateById(createTemplateSimpleIn);
        }
        /// <summary>
        /// 添加合同创建模板
        /// </summary>
        /// <param name="addCreateTemplate"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public AddCreaterTemplateResult AddCreateTemplate(AddOrUpdateCreateTemplate addCreateTemplate)
        {
            return BLL_CreateTemplate.Instance.AddCreateTemplate(addCreateTemplate);
        }

        /// <summary>
        /// 修改合同创建模板
        /// </summary>
        /// <param name="updateCreateTemplate"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public AddCreaterTemplateResult UpdateCreateTemplate(AddOrUpdateCreateTemplate updateCreateTemplate)
        {
            return BLL_CreateTemplate.Instance.UpdateCreateTemplate(updateCreateTemplate);
        }
        /// <summary>
        /// 删除合同创建模板
        /// </summary>
        /// <param name="parameter_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public CreaterTemplateResult DeleteCreateTemplate(Parameter_In parameter_In)
        {
            List<string> Ids = parameter_In.Ids.Split(',').ToList();
            return DbOpe_crm_contract_createdtemplate.Instance.DeleteCreateTemplate(Ids);
        }

        /// <summary>
        /// 获取合同创建模板完整列表
        /// </summary>
        /// <param name="searchCreateTemplateFullList_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public ApiTableOut<SearchCreateTemplateBase> SearchCreateTemplateFullList(SearchCreateTemplateFullList_In  searchCreateTemplateFullList_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_createdtemplate.Instance.SearchCreateTemplateFullList(searchCreateTemplateFullList_In, ref total);
            return GetApiTableOut(list, total);
        }
    }
}
