﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_contract_achievementreceiptregisterinfo")]
    public class Db_v_contract_achievementreceiptregisterinfo
    {
           /// <summary>
           /// Desc:合同表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractId {get;set;}

           /// <summary>
           /// Desc:到账情况(是否到账)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? IsReceipt {get;set;}
        public DateTime ReviewerDate { get; set; }
        public DateTime? BelongingMonth { get; set; }

    }
}
