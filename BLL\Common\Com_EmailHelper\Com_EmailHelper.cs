﻿using Aspose.Cells.Drawing;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using LumiSoft.Net.IMAP;
using LumiSoft.Net.IMAP.Client;
using LumiSoft.Net.Mail;
using Magicodes.ExporterAndImporter.Core.Extension;
using MailKit;
using MailKit.Net.Imap;
using MailKit.Net.Pop3;
using MailKit.Search;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Routing;
using Npoi.Mapper;
using NPOI.HPSF;
using QCloud;
using System.Collections.Concurrent;
using System.IO;
using System.Net.Mail;
using System.Web;
using CRM2_API.Model.BLLModel.Enum;

namespace CRM2_API.BLL.Common.Com_EmailHelper
{
    /// <summary>
    /// 发送邮件帮助类
    /// </summary>
    public class Com_EmailHelper
    {
        /// <summary>
        /// 发送登录验证码
        /// </summary>
        /// <param name="emailAddr">邮箱地址</param>
        /// <param name="code">验证码</param>
        public static void SendLoginCheckCode(string emailAddr, string code)
        {
            string template = GetEmailTemplate("Temp_LoginEmail.html");
            template = template.FormatTemplate(new { code });
            Send(emailAddr, null, "登录验证码", template);
        }

        /// <summary>
        /// 发送邓白氏开通邮件-仅模板内容
        /// </summary>
        /// <param name="emailAddr">收件地址</param>
        /// <param name="subject">主题</param>
        /// <param name="content">内容</param>
        /// <param name="filePathList"></param>
        public static void SendOpenDBServiceEmail(string emailAddr, string subject, string content, List<string> filePathList)
        {
            string template = GetEmailTemplate("Temp_DBEmail.html");
            template = template.FormatTemplate(new { content });
            Send(emailAddr, emailAddr, subject, template, filePathList);
        }

        /// <summary>
        /// 发送邓白氏开通邮件
        /// </summary>
        /// <param name="service">邮件参数</param>
        /// <param name="filePathList"></param>
        /// <param name="processingType"></param>
        public static void SendOpenDBServiceEmail(Db_crm_contract_serviceinfo_db service, List<string> filePathList, int processingType)
        {
            //string template = String.Empty;
            //string template = GetEmailTemplate("Temp_DBEmail.html");
            var content = string.IsNullOrEmpty(service.Content) ? " " : service.Content;
            var pmdId = string.IsNullOrEmpty(service.PMDID) ? " " : service.PMDID;
            var siteId = DbOpe_crm_contract_serviceinfo_db_siteid.Instance.GetSiteIDByServiceId(service.Id);
            var websiteContentSearchFlag = string.IsNullOrEmpty(service.WebsiteContentSearchFlag) ? " " : service.WebsiteContentSearchFlag;
            var newsFlag = string.IsNullOrEmpty(service.NewsFlag) ? " " : service.NewsFlag;
            var duns = string.IsNullOrEmpty(service.DUNS) ? " " : service.DUNS;
            var customerCompanyName = string.IsNullOrEmpty(service.CustomerCompanyName) ? " " : service.CustomerCompanyName;
            var customerContactName = string.IsNullOrEmpty(service.CustomerContactName) ? " " : service.CustomerContactName;
            var customerContactEmailAddress = string.IsNullOrEmpty(service.CustomerContactEmailAddress) ? " " : service.CustomerContactEmailAddress;
            var subscriptionType = string.IsNullOrEmpty(service.SubscriptionType) ? " " : service.SubscriptionType;
            var usersNo = service.UsersNo;
            var subscriptionPeriod = string.IsNullOrEmpty(service.SubscriptionPeriod) ? " " : service.SubscriptionPeriod;
            var salesRepEmailAddress = string.IsNullOrEmpty(service.SalesRepEmailAddress) ? " " : service.SalesRepEmailAddress;
            var DBHOOOVERSContractNo = string.IsNullOrEmpty(service.DBHOOOVERSContractNo) ? " " : service.DBHOOOVERSContractNo;

            //2024.12.12  改为申请和变更共用一个模板
            string template = GetEmailTemplate("Temp_SendToDBEmail.html");
            template = template.FormatTemplate(new
            {
                content,
                DBHOOOVERSContractNo,
                pmdId,
                siteId,
                websiteContentSearchFlag,
                newsFlag,
                duns,
                customerCompanyName,
                customerContactName,
                customerContactEmailAddress,
                subscriptionType,
                usersNo,
                subscriptionPeriod,
                salesRepEmailAddress
            });
            Send(service.Addressee, service.MakeCopy, service.Theme, template, filePathList);
            #region 2024.12.12  改为申请和变更共用一个模板，此段注释
            /*if (processingType == EnumProcessingType.Add.ToInt())
            {
                string template = GetEmailTemplate("Temp_SendToDBEmail.html");
                template = template.FormatTemplate(new
                {
                    content,
                    pmdId,
                    siteId,
                    websiteContentSearchFlag,
                    newsFlag,
                    duns,
                    customerCompanyName,
                    customerContactName,
                    customerContactEmailAddress,
                    subscriptionType,
                    usersNo,
                    subscriptionPeriod,
                    salesRepEmailAddress
                });
                Send(service.Addressee, service.MakeCopy, service.Theme, template, filePathList);
            }
            else if (processingType == EnumProcessingType.Change.ToInt())
            {
                string template = GetEmailTemplate("Temp_SendToDBEmail4Change.html");
                template = template.FormatTemplate(new
                {
                    content,
                    DBHOOOVERSContractNo,
                    customerCompanyName,
                    customerContactName,
                    customerContactEmailAddress,
                    subscriptionType,
                    usersNo,
                    subscriptionPeriod,
                    salesRepEmailAddress
                });
                Send(service.Addressee, service.MakeCopy, service.Theme, template, filePathList);
            }*/
            #endregion
        }

        public static void ReadDBEmail()
        {
            //验证是否存在确实Site的数据
            if (!DbOpe_crm_contract_serviceinfo_db.Instance.ExistsHaveNonSiteService())
                return;

            var mailServer = "imap.qiye.aliyun.com";//也可以使用其他邮箱服务器
            var username = AppSettings.Email.UserName;
            var password = AppSettings.Email.Pwd;

            using (var client = new ImapClient())
            {
                client.Connect(mailServer, 993, true);
                client.Authenticate(username, password);

                var inbox = client.Inbox;
                inbox.Open(FolderAccess.ReadWrite);

                var userIdList = DbOpe_sys_user.Instance.GetDBUserIdList();
                var readList = new List<ReadMailDBInner>();
                var yestoday = DateTime.Now.Date.AddDays(-1);
                var uids = inbox.Search(SearchQuery.DeliveredOn(yestoday)).ToList();
                foreach (var uid in uids)
                {
                    var message = inbox.GetMessage(uid);
                    var a = message.Date.ConvertToDateTime();
                    if (message.Subject.IndexOf("Your D&B Hoovers Subscription is Ready!") >= 0)
                    {
                        LogUtil.AddLog("DB_Email_1" + message.Subject);
                        //获取甲方公司名称
                        var companyName = message.Subject.Split("Your D&B Hoovers Subscription is Ready!")[1].ToString().TrimStart().TrimEnd();
                        LogUtil.AddLog("DB_Email_2" + companyName);
                        //获取产品名称  Product:&nbsp;
                        var productName = string.Empty;
                        if (message.TextBody.IndexOf("Product:&nbsp;") > 0)
                            productName = message.TextBody.Split("Product:&nbsp;")[1].Split("\r\n")[0].ToString();
                        else if (message.TextBody.IndexOf("Product: ") > 0)
                            productName = message.TextBody.Split("Product: ")[1].Split("\r\n")[0].ToString();
                        if (productName.Contains("&amp;"))
                            productName = productName.Replace("&amp;", "&");
                        LogUtil.AddLog("DB_Email_3" + productName);
                        //获取SiteID
                        var siteId = message.TextBody.Split("Your Site ID: ")[1].Split("\r\n")[0].ToString();
                        LogUtil.AddLog("DB_Email_4" + siteId);
                        var company = DbOpe_crm_customer_subcompany.Instance.GetSubCompanyByName(companyName);
                        if (company == null)
                        {
                            LogUtil.AddLog("DB_Email_Null_company");
                            foreach (var userId in userIdList)
                                DbOpe_sys_messages.Instance.AddServiceDBAbnormalMessage(userId, "未匹配到甲方公司信息");
                            continue;
                        }
                        var product = DbOpe_crm_product.Instance.GetProductByContrinsTrimAliss(productName);
                        if (product == null)
                        {
                            LogUtil.AddLog("DB_Email_Null_product" + siteId);
                            foreach (var userId in userIdList)
                                DbOpe_sys_messages.Instance.AddServiceDBAbnormalMessage(userId, "未匹配到产品信息");
                            continue;
                        }
                        var contractList = DbOpe_crm_contract.Instance.GetContractInfoByFirstParty(company.Id, product.Id);
                        if (contractList == null || contractList.Count == 0)
                        {
                            LogUtil.AddLog("DB_Email_Null_contract" + siteId);
                            foreach (var userId in userIdList)
                                DbOpe_sys_messages.Instance.AddServiceDBAbnormalMessage(userId, "未匹配到合同信息");
                            continue;
                        }
                        var serviceList = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByContractId(contractList.Select(e => e.Id).ToList());
                        if (serviceList == null || serviceList.Count == 0)
                        {
                            LogUtil.AddLog("DB_Email_Null_service" + siteId);
                            foreach (var userId in userIdList)
                                DbOpe_sys_messages.Instance.AddServiceDBAbnormalMessage(userId, "未匹配到服务信息");
                            continue;
                        }
                        serviceList.ForEach(service =>
                        {
                            var readMailDBInner = new ReadMailDBInner()
                            {
                                ServiceId = service.Id,
                                SiteId = siteId,
                                ReceiveDate = message.Date.ConvertToDateTime(),
                            };
                            readList.Add(readMailDBInner);
                        });
                        //DbOpe_crm_contract_serviceinfo_db_siteid.Instance.UpdateSiteId(siteId, service.Id);
                    }
                }
                //去重，相同serviceId的数据，取最新的一条
                var list = readList.GroupBy(e => e.ServiceId).Select(e => e.OrderByDescending(e => e.ReceiveDate).First()).ToList();
                //循环，修改siteId
                list.ForEach(it =>
                {
                    if (DbOpe_crm_contract_serviceinfo_db_siteid.Instance.IsExistsSiteIdData(it.ServiceId))
                        DbOpe_crm_contract_serviceinfo_db_siteid.Instance.UpdateSiteId(it.SiteId, it.ServiceId);
                    else
                    {
                        var siteIdData = new Db_crm_contract_serviceinfo_db_siteid();
                        siteIdData.ContractServiceInfoDBId = it.ServiceId;
                        siteIdData.SITEID = it.SiteId;
                        DbOpe_crm_contract_serviceinfo_db_siteid.Instance.InsertDataQueue(siteIdData);
                    }
                });
                //提交数据
                DbOpe_crm_contract_serviceinfo_db_siteid.Instance.SaveQueues();
                //标记已读
                //inbox.SetFlags(haveSeenUids, MessageFlags.Seen, true);
            }
            #region 注释不用
            /*using (IMAP_Client client = new IMAP_Client())
            {
                client.Connect(mailServer, 993, true);
                client.Login(username, password);
                // client.id_({ "name": "IMAPClient", "version": "2.1.0"});
                client.SelectFolder("INBOX");//打开收件箱
                var msgCount = client.FolderStatus("INBOX").First().MessagesCount;

                client.GetFolders(null).ToList().ForEach(f =>
                {
                    Console.WriteLine(f.FolderName);
                    var s = client.FolderStatus(f.FolderName);
                    s.ToList().ForEach(sIt =>
                    {
                        var unseen = sIt.UnseenCount;
                        Console.WriteLine("总数:{0},未读:{1},最近{2}", sIt.MessagesCount, sIt.MessagesCount, sIt.UnseenCount);
                    });
                });

                *//*int[] unseen_ids = client.Search(false, Encoding.UTF8, new IMAP_Search_Key_Unseen());
                if (unseen_ids.Count() == 0)
                {
                    return;
                }*//*


                for (int i = 1; i <= msgCount; i++)
                {
                    //首先确定取第x到第n封邮件，"1:*"表示第1封到最后一封
                    var seqSet = IMAP_t_SeqSet.Parse(i.ToString());
                    var items = new IMAP_t_Fetch_i[]
                    {
                        new IMAP_t_Fetch_i_Envelope(),  //邮件的标题、正文等信息
                        new IMAP_t_Fetch_i_Uid(),       //返回邮件的UID号，UID号是唯一标识邮件的一个号码
                        new IMAP_t_Fetch_i_Flags(),     //此邮件的标志，应该是已读未读标志
                        new IMAP_t_Fetch_i_InternalDate(),//貌似是收到的日期
                        new IMAP_t_Fetch_i_Rfc822()     //Rfc822是标准的邮件数据流，可以通过Lumisoft.Net.Mail.Mail_Message对象解析出邮件的所有信息
                    };

                    client.Fetch(false, seqSet, items, (s, e) =>
                    {
                        try
                        {
                            var email = e.Value as IMAP_r_u_Fetch;

                            if (email.Rfc822 != null)
                            {
                                email.Rfc822.Stream.Position = 0;
                                var mine = Mail_Message.ParseFromStream(email.Rfc822.Stream);
                                email.Rfc822.Stream.Close();

                                var files = mine.Attachments;
                                var fileurls = new List<KeyValuePair<string, string>>();
                                foreach (var item in files)
                                {
                                    var name = item.ContentType.Param_Name;
                                    using (MemoryStream stream = new MemoryStream(item.DataToByte()))
                                    {
                                        //附件可以上传到阿里云上或者其他地方
                                        var key = "";// AliOss.Upload(stream);
                                        fileurls.Add(new KeyValuePair<string, string>(name, key));//文件名，地址
                                    }
                                }
                                //处理自定义业务逻辑
                                var Title = email.Envelope.Subject;
                                var CreateTime = email.InternalDate.Date;
                                //var BodyText = mine.BodyText;
                                var BodyHtmlText = mine.BodyHtmlText;
                                var File = string.Join(",", fileurls.Select(x => x.Value));

                                //var abc = BodyText.Split("Your Site ID: ")[1];
                                //var bcd = abc.Split("\n")[0];

                                //处理完的邮件标记为已读
                                var sequenceSet = new IMAP_SequenceSet();
                                sequenceSet.Parse(string.Join(",", email.SeqNo));
                                client.StoreMessageFlags(false, sequenceSet, IMAP_Flags_SetType.Add, IMAP_MessageFlags.Seen);

                                //-------邮件内容入消息队列------------

                            }
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                    });
                }





                *//*unseen_ids = unseen_ids.Where(e => e != i).ToArray();
                seqSet = IMAP_t_SeqSet.Parse(string.Join(",", unseen_ids));*/
            /* });*//*



        }*/

            //using (var client = new Pop3Client("pop3.example.com", 995, true, "username", "password"))
            /* using (var client = new Pop3Client())
             {
                 client.Connect(); // 连接到邮件服务器
                 client.Authenticate(); // 进行认证

                 // 获取未读邮件数量
                 int unseenMessageCount = client.GetUnseenMessageCount();
                 Console.WriteLine($"You have {unseenMessageCount} unread messages.");
             }*/
            #endregion
        }

        public static void ReadDBEmail_Admin()
        {
            //验证是否存在确实Site的数据
            if (!DbOpe_crm_contract_serviceinfo_db.Instance.ExistsHaveNonSiteService())
                return;
            var mailServer = "imap.qiye.aliyun.com";//也可以使用其他邮箱服务器
            var username = AppSettings.Email.UserName;
            var password = AppSettings.Email.Pwd;
            using (var client = new ImapClient())
            {
                client.Connect(mailServer, 993, true);
                client.Authenticate(username, password);

                var inbox = client.Inbox;
                inbox.Open(FolderAccess.ReadWrite);

                var userIdList = DbOpe_sys_user.Instance.GetDBUserIdList();
                var readList = new List<ReadMailDBInner>();
                var startDate = new DateTime(2024, 7, 1).Date;
                var unSeenUids = inbox.Search(SearchQuery.DeliveredAfter(startDate)).ToList();
                foreach (var uid in unSeenUids)
                {
                    var message = inbox.GetMessage(uid);
                    var a = message.Date.ConvertToDateTime();
                    if (message.Subject.IndexOf("Your D&B Hoovers Subscription is Ready!") >= 0)
                    {
                        LogUtil.AddLog("DB_Email_1" + message.Subject);
                        //获取甲方公司名称
                        var companyName = message.Subject.Split("Your D&B Hoovers Subscription is Ready!")[1].ToString().TrimStart().TrimEnd();
                        LogUtil.AddLog("DB_Email_2" + companyName);
                        //获取产品名称  Product:&nbsp;
                        var productName = string.Empty;
                        if (message.TextBody.IndexOf("Product:&nbsp;") > 0)
                            productName = message.TextBody.Split("Product:&nbsp;")[1].Split("\r\n")[0].ToString();
                        else if (message.TextBody.IndexOf("Product: ") > 0)
                            productName = message.TextBody.Split("Product: ")[1].Split("\r\n")[0].ToString();
                        if (productName.Contains("&amp;"))
                            productName = productName.Replace("&amp;", "&");
                        LogUtil.AddLog("DB_Email_3" + productName);
                        //获取SiteID
                        var siteId = message.TextBody.Split("Your Site ID: ")[1].Split("\r\n")[0].ToString();
                        LogUtil.AddLog("DB_Email_4" + siteId);
                        var company = DbOpe_crm_customer_subcompany.Instance.GetSubCompanyByName(companyName);
                        if (company == null)
                        {
                            LogUtil.AddLog("DB_Email_Null_company");
                            foreach (var userId in userIdList)
                                DbOpe_sys_messages.Instance.AddServiceDBAbnormalMessage(userId, "未匹配到甲方公司信息");
                            continue;
                        }
                        var product = DbOpe_crm_product.Instance.GetProductByContrinsTrimAliss(productName);
                        if (product == null)
                        {
                            LogUtil.AddLog("DB_Email_Null_product" + siteId);
                            foreach (var userId in userIdList)
                                DbOpe_sys_messages.Instance.AddServiceDBAbnormalMessage(userId, "未匹配到产品信息");
                            continue;
                        }
                        var contractList = DbOpe_crm_contract.Instance.GetContractInfoByFirstParty(company.Id, product.Id);
                        if (contractList == null || contractList.Count == 0)
                        {
                            LogUtil.AddLog("DB_Email_Null_contract" + siteId);
                            foreach (var userId in userIdList)
                                DbOpe_sys_messages.Instance.AddServiceDBAbnormalMessage(userId, "未匹配到合同信息");
                            continue;
                        }
                        var serviceList = DbOpe_crm_contract_serviceinfo_db.Instance.GetServiceInfoByContractId(contractList.Select(e => e.Id).ToList());
                        if (serviceList == null || serviceList.Count == 0)
                        {
                            LogUtil.AddLog("DB_Email_Null_service" + siteId);
                            foreach (var userId in userIdList)
                                DbOpe_sys_messages.Instance.AddServiceDBAbnormalMessage(userId, "未匹配到服务信息");
                            continue;
                        }
                        serviceList.ForEach(service =>
                        {
                            var readMailDBInner = new ReadMailDBInner()
                            {
                                ServiceId = service.Id,
                                SiteId = siteId,
                                ReceiveDate = message.Date.ConvertToDateTime(),
                            };
                            readList.Add(readMailDBInner);
                        });
                        
                        //DbOpe_crm_contract_serviceinfo_db_siteid.Instance.UpdateSiteId(siteId, service.Id);
                    }
                }
                //去重，相同serviceId的数据，取最新的一条
                var list = readList.GroupBy(e => e.ServiceId).Select(e => e.OrderByDescending(e => e.ReceiveDate).First()).ToList();
                //循环，修改siteId
                list.ForEach(it =>
                {
                    if (DbOpe_crm_contract_serviceinfo_db_siteid.Instance.IsExistsSiteIdData(it.ServiceId))
                        DbOpe_crm_contract_serviceinfo_db_siteid.Instance.UpdateSiteId(it.SiteId, it.ServiceId);
                    else
                    {
                        var siteIdData = new Db_crm_contract_serviceinfo_db_siteid();
                        siteIdData.ContractServiceInfoDBId = it.ServiceId;
                        siteIdData.SITEID = it.SiteId;
                        DbOpe_crm_contract_serviceinfo_db_siteid.Instance.InsertDataQueue(siteIdData);
                    }
                });
                //提交数据
                DbOpe_crm_contract_serviceinfo_db_siteid.Instance.SaveQueues();
                //标记已读
                //inbox.SetFlags(haveSeenUids, MessageFlags.Seen, true);
            }
        }

        /// <summary>
        /// 发送电子发票-内容
        /// </summary>
        /// <param name="emailAddr">收件地址</param>
        /// <param name="subject">主题</param>
        /// <param name="fileList"></param>
        public static void SendElectronicInvoiceServiceEmail(string emailAddr, string subject, List<BM_FileInfo> fileList)
        {
            SendByFileInfo(emailAddr, emailAddr, subject, null, fileList);
        }

        #region 邮箱发送具体方法
        /// <summary>
        /// 邮件模板缓存
        /// </summary>
        private static ConcurrentDictionary<string, string> dicTemplate = new ConcurrentDictionary<string, string>();
        /// <summary>
        /// 获取邮件模板
        /// </summary>
        /// <param name="templateName"></param>
        /// <returns></returns>
        private static string GetEmailTemplate(string templateName)
        {
            return dicTemplate.GetOrAdd(templateName, val => FileUtil.ReadFileShare($"{AppDomain.CurrentDomain.BaseDirectory}BLL/Common/Com_EmailHelper/{templateName}"));
        }

        /// <summary>
        /// 发送邮件最终方法
        /// </summary>
        /// <param name="emailAddr">邮件地址</param>
        /// <param name="copyAddr">邮件抄送地址</param>
        /// <param name="subject">主题</param>
        /// <param name="content">邮件内容</param>
        /// <param name="filePathList">附件</param>
        private static void Send(string emailAddr, string copyAddr, string subject, string content, List<string> filePathList = null)
        {
            try
            {
                //主要处理发送邮件的内容（如：收发人地址、标题、主体、图片等等）
                using (MailMessage mMailMessage = new MailMessage())
                {
                    string RecieveEmail = string.IsNullOrEmpty(AppSettings.Email.RecieveEmail) ? emailAddr : AppSettings.Email.RecieveEmail;
                    string[] toList = RecieveEmail.Split(";");
                    foreach (var to in toList)
                    {
                        if (!string.IsNullOrWhiteSpace(to))
                        {
                            //目的邮箱
                            mMailMessage.To.Add(to);
                        }
                    }
                    ////目的邮箱
                    //mMailMessage.To.Add(string.IsNullOrEmpty(AppSettings.Email.RecieveEmail) ? emailAddr : AppSettings.Email.RecieveEmail);
                    //发件箱
                    mMailMessage.From = new MailAddress(AppSettings.Email.UserName);
                    //主题
                    mMailMessage.Subject = subject;
                    //邮件内容
                    mMailMessage.Body = content;
                    //邮件抄送
                    mMailMessage.CC.Add(copyAddr);
                    mMailMessage.IsBodyHtml = true;
                    mMailMessage.BodyEncoding = System.Text.Encoding.UTF8;
                    mMailMessage.Priority = MailPriority.Normal;
                    if (filePathList != null)
                    {
                        filePathList.ForEach(filePath =>
                        {
                            if (File.Exists(filePath))
                            {
                                var attachment = new Attachment(filePath);
                                mMailMessage.Attachments.Add(attachment);
                            }
                        });
                    }

                    //mMailMessage.Attachments.Add();
                    //主要处理用smtp方式发送此邮件的配置信息（如：邮件服务器、发送端口号、验证方式等等）
                    using (SmtpClient mSmtpClient = new SmtpClient())
                    {
                        //发件箱
                        mSmtpClient.Host = AppSettings.Email.Host;
                        //发件箱端口号
                        mSmtpClient.Port = AppSettings.Email.Port;
                        //不使用默认验证
                        mSmtpClient.UseDefaultCredentials = false;
                        //是否对邮件内容进行socket层加密传输
                        mSmtpClient.EnableSsl = false;
                        //配置发件箱地址，发件箱密码
                        mSmtpClient.Credentials = new System.Net.NetworkCredential(AppSettings.Email.UserName, AppSettings.Email.Pwd);
                        mSmtpClient.DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network;
                        mSmtpClient.Send(mMailMessage);
                    }
                }
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog(e.Message, e);
                throw new ApiException("邮件发送失败");
            }
        }


        /// <summary>
        /// 发送邮件最终方法
        /// </summary>
        /// <param name="emailAddr">邮件地址</param>
        /// <param name="copyAddr">邮件抄送地址</param>
        /// <param name="subject">主题</param>
        /// <param name="content">邮件内容</param>
        /// <param name="filePathList">附件</param>
        private static void SendByFileInfo(string emailAddr, string copyAddr, string subject, string content, List<BM_FileInfo> filePathList = null)
        {
            try
            {
                //主要处理发送邮件的内容（如：收发人地址、标题、主体、图片等等）
                using (MailMessage mMailMessage = new MailMessage())
                {
                    string RecieveEmail = string.IsNullOrEmpty(AppSettings.Email.RecieveEmail) ? emailAddr : AppSettings.Email.RecieveEmail;
                    string[] toList = RecieveEmail.Split(";");
                    foreach (var to in toList)
                    {
                        if (!string.IsNullOrWhiteSpace(to))
                        {
                            //目的邮箱
                            mMailMessage.To.Add(to);
                        }
                    }
                    ////目的邮箱
                    //mMailMessage.To.Add(string.IsNullOrEmpty(AppSettings.Email.RecieveEmail) ? emailAddr : AppSettings.Email.RecieveEmail);
                    //发件箱
                    mMailMessage.From = new MailAddress(AppSettings.Email.UserName);
                    //主题
                    mMailMessage.Subject = subject;
                    //邮件内容
                    mMailMessage.Body = content;
                    //邮件抄送
                    mMailMessage.CC.Add(copyAddr);
                    mMailMessage.IsBodyHtml = true;
                    mMailMessage.BodyEncoding = System.Text.Encoding.UTF8;
                    mMailMessage.Priority = MailPriority.Normal;
                    if (filePathList != null)
                    {
                        filePathList.ForEach(filePath =>
                        {
                            if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                            {
                                QCloudOperator qCloudOperator = new QCloudOperator();
                                if (qCloudOperator.Exists(filePath.FilePath))
                                {
                                    var data = qCloudOperator.OpenRead(filePath.FilePath);
                                    using (MemoryStream memoryStream = new MemoryStream(data))
                                    {
                                        string encodeFilename = HttpUtility.UrlEncode(filePath.FilePath, Encoding.GetEncoding("UTF-8"));
                                        MemoryStream resultStream = new MemoryStream(data);
                                        var attachment = new Attachment(resultStream, filePath.FileName);
                                        mMailMessage.Attachments.Add(attachment);
                                    }
                                }
                            }
                            else
                            {
                                if (File.Exists(filePath.FilePath))
                                {
                                    Stream stream = File.OpenRead(filePath.FilePath);
                                    var attachment = new Attachment(stream, filePath.FileName);
                                    mMailMessage.Attachments.Add(attachment);
                                }
                            }
                        });
                    }

                    //mMailMessage.Attachments.Add();
                    //主要处理用smtp方式发送此邮件的配置信息（如：邮件服务器、发送端口号、验证方式等等）
                    using (SmtpClient mSmtpClient = new SmtpClient())
                    {
                        //发件箱
                        mSmtpClient.Host = AppSettings.Email.Host;
                        //发件箱端口号
                        mSmtpClient.Port = AppSettings.Email.Port;
                        //不使用默认验证
                        mSmtpClient.UseDefaultCredentials = false;
                        //是否对邮件内容进行socket层加密传输
                        mSmtpClient.EnableSsl = false;
                        //配置发件箱地址，发件箱密码
                        mSmtpClient.Credentials = new System.Net.NetworkCredential(AppSettings.Email.UserName, AppSettings.Email.Pwd);
                        mSmtpClient.DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network;
                        mSmtpClient.Send(mMailMessage);
                    }
                }
            }
            catch (Exception e)
            {
                LogUtil.AddErrorLog(e.Message, e);
                throw new ApiException("邮件发送失败");
            }
        }
        #endregion
    }
}
