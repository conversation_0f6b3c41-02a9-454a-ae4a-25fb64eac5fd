﻿using System.IO;
using System.Web;
using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.TrackingRecord;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Spreadsheet;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.ObjectPool;
using SqlSugar;

namespace CRM2_API.BLL.TrackingRecord
{
    public class BLL_TrackingRecord : BaseBLL<BLL_TrackingRecord>
    {
        private readonly ObjectPool<StringBuilder> _stringBuilderPool = new DefaultObjectPoolProvider().CreateStringBuilderPool();

        /// <summary>
        /// 根据追踪记录表主键Id设置追踪记录为已读。
        /// </summary>
        /// <param name="setTrackingRecordReadByIdIn"></param>
        /// <returns></returns>
        public SetTrackingRecordReadById_Out SetTrackingRecordReadById(SetTrackingRecordReadById_In setTrackingRecordReadByIdIn)
        {
            if (setTrackingRecordReadByIdIn != null && !string.IsNullOrEmpty(setTrackingRecordReadByIdIn.Id))
            {
                bool existsResult = CheckTrackingRecordExists(setTrackingRecordReadByIdIn.Id);
                if (existsResult)
                {
                    return DbOpe_crm_trackingrecord_read.Instance
                        .SetTrackingRecordReadById(setTrackingRecordReadByIdIn);
                }
                throw new ApiException("追踪记录Id所对应的数据不存在");
            }
            return new SetTrackingRecordReadById_Out()
            {
                Data = 0,
            };
        }

        /// <summary>
        /// 添加跟踪记录信息
        /// </summary>
        /// <param name="addTrackingRecordIn"></param>
        /// <returns></returns>
        public AddTrackingRecord_Out AddTrackingRecord(AddTrackingRecord_In addTrackingRecordIn)
        {
            var result = new AddTrackingRecord_Out { Data = 0 };
            var checkResult = CheckTrackingRecordParams(addTrackingRecordIn);
            if (checkResult)
            {
                var insertId = Guid.NewGuid().ToString();
                var customerData = DbOpe_crm_trackingrecord.Instance.GetCustomerInfo(addTrackingRecordIn.CustomerId,
                    addTrackingRecordIn.CustomerDataSource);    //读取数据库中的客户信息并快照
                if (customerData is null)
                {
                    throw new ApiException("客户数据为空!");
                }

                //var newEntity = addTrackingRecordIn.MappingTo<Db_crm_trackingrecord>();
                var newEntity = new Db_crm_trackingrecord
                {
                    Id = insertId,
                    //UserId = addTrackingRecordIn.UserId,
                    UserId = TokenModel.Instance.id,
                    CustomerDataSource = addTrackingRecordIn.CustomerDataSource,
                    CustomerId = customerData.Id, //始终存入正式Id
                    CustomerTemporaryId = addTrackingRecordIn.CustomerDataSource.Equals(EnumCustomerDataSource.Temporary) ? customerData.CustomerTemporaryId : null,
                    ContractId = addTrackingRecordIn.ContractId,
                    TrackingType = addTrackingRecordIn.TrackingType,
                    TrackingPurpose = addTrackingRecordIn.TrackingPurpose,
                    TrackingStage = addTrackingRecordIn.TrackingStage,
                    Remark = addTrackingRecordIn.Remark,
                    IsVisible = addTrackingRecordIn.CustomerDataSource.Equals(EnumCustomerDataSource.Private) ? 1 : 0,
                    Deleted = false,
                    Hidden = false,
                    CanEdit = true,
                    CreateUser = TokenModel.Instance.id,
                    CreateDate = DateTime.Now,
                    //将客户快照信息存入到跟踪记录表中
                    CustomerSubCompanyId = customerData.CustomerSubCompanyId,
                    CustomerSubCompanyTemporaryId = addTrackingRecordIn.CustomerDataSource.Equals(EnumCustomerDataSource.Temporary) ? customerData.CustomerSubCompanyTemporaryId : null,
                    CustomerName = customerData.CustomerName,
                    CustomerLevel = customerData.CustomerLevel,
                    ServiceState = customerData.ServiceState,
                    CustomerOrderNum = customerData.CustomerOrderNum,
                    CustomerNum = customerData.CustomerNum,
                    CustomerNature = customerData.CustomerNature,
                    CustomerSize = customerData.CustomerSize,
                    CustomerBindUserId = customerData.CustomerBindUserId,
                    CustomerBindUserOrgId = customerData.CustomerBindUserOrgId,
                    CustomerBindUserOrgName = customerData.OrgName,
                    CustomerSource = customerData.CustomerSource,
                    CollectionTime = customerData.CollectionTime,
                    CustomerIndustrys = customerData.CustomerIndustrys,
                    RecBelongDate = DateTime.Now
                };

                if (!string.IsNullOrEmpty(addTrackingRecordIn.ContractId))
                {
                    var contractData = DbOpe_crm_trackingrecord.Instance.GetContractInfo(addTrackingRecordIn.ContractId);
                    //将合同信息快照到跟踪记录表中
                    newEntity.ContractName = contractData.ContractName;
                }

                DbOpe_crm_trackingrecord.Instance.InsertQueue(newEntity);
                //附件操作
                if (addTrackingRecordIn.AttachFiles is not null && addTrackingRecordIn.AttachFiles.Count > 0)
                {
                    if (addTrackingRecordIn.AttachFiles.Count > 9)
                    {
                        throw new ApiException("附件数量超限,附件最多只能上传9个!");
                    }
                    else
                    {
                        Util<DbOpe_crm_attachfile, BM_AttachFiles> util = new(DbOpe_crm_attachfile.Instance);
                        bool uploadResult = util.UploadFiles(insertId, addTrackingRecordIn.AttachFiles, AttachEnumOption.TrackingRecord);
                        if (!uploadResult)
                        {
                            throw new ApiException("文件上传异常!");
                        }
                    }
                }
                DbOpe_crm_trackingrecord.Instance.SaveQueues();
                //同步客户表跟踪状态[临时池暂时不管]
                if (addTrackingRecordIn.CustomerDataSource.Equals(EnumCustomerDataSource.Private))
                {
                    if (addTrackingRecordIn.TrackingStage is not null)
                    {
                        DbOpe_crm_customer.Instance.RefreshTrackingStage(customerData.Id);
                        // BLL_Customer.Instance.ModifyCustomerTrackingStageInfo(new Dictionary<string, EnumTrackingStage>()
                        // {
                        //     {addTrackingRecordIn.CustomerId,addTrackingRecordIn.TrackingStage.Value}
                        // }); 
                    }
                }
                result.Data = 1;
                result.Content = new
                {
                    insertId
                };
            }
            return result;
        }

        /// <summary>
        /// 修改跟踪记录信息，验证是否本人创建的跟踪记录。
        /// </summary>
        /// <param name="updateTrackingRecordIn"></param>
        /// <returns></returns>
        public UpdateTrackingRecord_Out UpdateTrackingRecord(UpdateTrackingRecord_In updateTrackingRecordIn)
        {
            var result = new UpdateTrackingRecord_Out { Data = 0 };
            var trackingRecord = DbOpe_crm_trackingrecord.Instance.QueryByPrimaryKey(updateTrackingRecordIn.Id) ?? throw new ApiException("追踪记录表主键所对应的数据不存在!");
            var checkCreatorResult = CheckTrackingRecordCreator(trackingRecord);
            if (!checkCreatorResult)
            {
                throw new ApiException("无法修改非本人创建的跟踪记录");
            }
            var checkResult = CheckTrackingRecordParams(updateTrackingRecordIn);
            if (checkCreatorResult && checkResult)
            {
                //拿一下客户数据
                var customerData = DbOpe_crm_trackingrecord.Instance.GetCustomerInfo(updateTrackingRecordIn.CustomerId,
                    updateTrackingRecordIn.CustomerDataSource);    //读取数据库中的客户信息并快照
                if (customerData is null)
                {
                    //throw new ApiException("非保护客户无法获取最新客户信息!");

                    customerData = DbOpe_crm_trackingrecord.Instance.GetCustomerSnapInfo(updateTrackingRecordIn.Id);

                }



                //将客户快照信息存入到跟踪记录表中
                DbOpe_crm_trackingrecord.Instance.UpdateTrackingRecord(updateTrackingRecordIn, customerData);
                //附件操作
                var attachFileList = DbOpe_crm_trackingrecord.Instance.GetTrackingRecordAttachFilesById(updateTrackingRecordIn.Id);
                var attachFileIdList = attachFileList.Select(w => w.Id).ToList();
                if (updateTrackingRecordIn.AttachFiles is null or { Count: 0 })
                {
                    if (attachFileIdList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_trackingrecord.Instance.DeleteAttachFileByIdList(attachFileIdList);
                    }
                }
                else
                {
                    if (updateTrackingRecordIn.AttachFiles.Count > 9)
                    {
                        throw new ApiException("附件数量超限,附件最多只能上传9个!");
                    }
                    else
                    {
                        var attachFileUpdateIdList = new List<string>();
                        FormFileCollection formFileCollection = new();
                        foreach (Update_TrackingRecordAttachFile updateTrackingRecordAttachFile in updateTrackingRecordIn.AttachFiles)
                        {
                            if (attachFileIdList.Contains(updateTrackingRecordAttachFile.Id))
                            {
                                attachFileUpdateIdList.Add(updateTrackingRecordAttachFile.Id);
                                continue;
                            }

                            if (updateTrackingRecordAttachFile.File != null)
                            {
                                formFileCollection.Add(updateTrackingRecordAttachFile.File);
                            }
                        }

                        if (formFileCollection.Count > 0)
                        {
                            Util<DbOpe_crm_attachfile, BM_AttachFiles> util = new(DbOpe_crm_attachfile.Instance);
                            bool uploadResult = util.UploadFiles(updateTrackingRecordIn.Id, formFileCollection, AttachEnumOption.TrackingRecord);
                            if (!uploadResult)
                            {
                                throw new ApiException("文件上传异常!");
                            }
                        }
                        var attachFileDeleteIdList = attachFileIdList.Except(attachFileUpdateIdList).ToList();
                        DbOpe_crm_trackingrecord.Instance.DeleteAttachFileByIdList(attachFileDeleteIdList);
                    }
                }

                //同步客户表跟踪状态[临时池暂时不管]
                if (updateTrackingRecordIn.CustomerDataSource.Equals(EnumCustomerDataSource.Private))
                {
                    if (updateTrackingRecordIn.TrackingStage is not null)
                    {
                        DbOpe_crm_customer.Instance.RefreshTrackingStage(customerData.Id);
                        // BLL_Customer.Instance.ModifyCustomerTrackingStageInfo(new Dictionary<string, EnumTrackingStage>()
                        // {
                        //     {updateTrackingRecordIn.CustomerId,updateTrackingRecordIn.TrackingStage.Value}
                        // }); 
                    }
                }
                int execCount = DbOpe_crm_trackingrecord.Instance.SaveQueues();
                if (execCount > 0)
                {
                    result.Data = 1;
                }
            }
            return result;
        }

        /// <summary>
        /// 根据查询条件获取跟踪记录汇总信息列表
        /// </summary>
        /// <param name="summaryTrackingRecordListIn"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<SummaryTrackingRecordList_Out> SummaryTrackingRecordList(SummaryTrackingRecordList_In summaryTrackingRecordListIn, ref int total)
        {
            if (summaryTrackingRecordListIn.CreateDateStart == null ||
                summaryTrackingRecordListIn.CreateDateEnd == null)
            {
                return new();
            }

            return DbOpe_crm_trackingrecord.Instance.SummaryTrackingRecordList(summaryTrackingRecordListIn, ref total);
            #region 废弃部分
            var sb = this._stringBuilderPool.Get();
            var countSb = this._stringBuilderPool.Get();

            countSb.Append(@$"SELECT
                          COUNT(su.Id) AS Count
                        FROM sys_user su
                          LEFT JOIN sys_organization so
                            ON su.OrganizationId = so.Id
                          LEFT JOIN (SELECT
                              cp.UserId AS UserId,
                              COUNT(cp.CustomerId) AS ReservedCustomersNum,
                              COUNT(tr.UserId) AS TrackedCustomersNum,
                              COUNT(cp.CustomerId) - COUNT(tr.UserId) AS NoTTrackedCustomersNum
                            FROM (SELECT
                                ccp.`UserId` AS `UserId`,
                                ccp.`CustomerId` AS `CustomerId`
                              FROM `crm_customer_privatepool` ccp
                                LEFT JOIN crm_customer cc
                                  ON ccp.CustomerId = cc.Id
                              WHERE ccp.State = 0
                              AND cc.IsMerge = 0
                              AND ccp.Deleted = 0
                              AND cc.Deleted = 0
                              UNION
                              SELECT
                                cct.`UserId` AS `UserId`,
                                cct.`CustomerId` AS `CustomerId`
                              FROM `crm_customer_temporarypool` cct
                                LEFT JOIN crm_customer_temporary cct1
                                  ON cct.CustomerTemporaryId = cct1.Id
                              WHERE cct.State = 0
                              AND cct1.IsMerge = 0
                              AND cct.Deleted = 0
                              AND cct1.Deleted = 0) cp
                              LEFT JOIN (SELECT
                                  UserId,
                                  CustomerId
                                FROM crm_trackingrecord
                                WHERE Deleted = 0");

            sb.Append(@$"SELECT
                          su.Id AS UserId,
                          su.UserName,
                          su.`Name`,
                          su.UserNum,
                          so.OrgName,
                          IFNULL(temp.ReservedCustomersNum, 0) AS ReservedCustomersNum,
                          IFNULL(temp.TrackedCustomersNum, 0) AS TrackedCustomersNum,
                          IFNULL(cct.ContractedCustomersNum, 0) AS ContractedCustomersNum,
                          IFNULL(temp.NoTTrackedCustomersNum, 0) AS NoTTrackedCustomersNum
                        FROM sys_user su
                          LEFT JOIN sys_organization so
                            ON su.OrganizationId = so.Id
                          LEFT JOIN (SELECT
                              cp.UserId AS UserId,
                              COUNT(cp.CustomerId) AS ReservedCustomersNum,
                              COUNT(tr.UserId) AS TrackedCustomersNum,
                              COUNT(cp.CustomerId) - COUNT(tr.UserId) AS NoTTrackedCustomersNum
                            FROM (SELECT
                                ccp.`UserId` AS `UserId`,
                                ccp.`CustomerId` AS `CustomerId`
                              FROM `crm_customer_privatepool` ccp
                                LEFT JOIN crm_customer cc
                                  ON ccp.CustomerId = cc.Id
                              WHERE ccp.State = 0
                              AND cc.IsMerge = 0
                              AND ccp.Deleted = 0
                              AND cc.Deleted = 0
                              UNION
                              SELECT
                                cct.`UserId` AS `UserId`,
                                cct.`CustomerId` AS `CustomerId`
                              FROM `crm_customer_temporarypool` cct
                                LEFT JOIN crm_customer_temporary cct1
                                  ON cct.CustomerTemporaryId = cct1.Id
                              WHERE cct.State = 0
                              AND cct1.IsMerge = 0
                              AND cct.Deleted = 0
                              AND cct1.Deleted = 0) cp
                              LEFT JOIN (SELECT
                                  UserId,
                                  CustomerId
                                FROM crm_trackingrecord
                                WHERE Deleted = 0");

            if (summaryTrackingRecordListIn.CreateDateStart != null)
            {
                sb.Append(@$" AND CreateDate >= '{summaryTrackingRecordListIn.CreateDateStart.Value.LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss")}'");
                countSb.Append(@$" AND CreateDate >= '{summaryTrackingRecordListIn.CreateDateStart.Value.LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss")}'");
            }

            if (summaryTrackingRecordListIn.CreateDateEnd != null)
            {
                sb.Append(@$" AND CreateDate < '{summaryTrackingRecordListIn.CreateDateEnd.Value.LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss")}'");
                countSb.Append(@$" AND CreateDate < '{summaryTrackingRecordListIn.CreateDateEnd.Value.LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss")}'");
            }

            sb.Append(@$"
                    GROUP BY UserId,
                    CustomerId) tr
                    ON cp.UserId = tr.UserId
                    AND cp.CustomerId = tr.CustomerId
                GROUP BY cp.UserId) temp
                ON temp.UserId = su.Id
              LEFT JOIN (SELECT
                  Issuer,
                  COUNT(Issuer) AS ContractedCustomersNum
                FROM crm_contract
                WHERE ContractStatus = 3
                AND Deleted = 0");

            countSb.Append(@$"
                    GROUP BY UserId,
                    CustomerId) tr
                    ON cp.UserId = tr.UserId
                    AND cp.CustomerId = tr.CustomerId
                GROUP BY cp.UserId) temp
                ON temp.UserId = su.Id
              LEFT JOIN (SELECT
                  Issuer,
                  COUNT(Issuer) AS ContractedCustomersNum
                FROM crm_contract
                WHERE ContractStatus = 3
                AND Deleted = 0");

            if (summaryTrackingRecordListIn.CreateDateStart != null)
            {
                sb.Append(@$" AND CreateDate >= '{summaryTrackingRecordListIn.CreateDateStart.Value.LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss")}'");
                countSb.Append(@$" AND CreateDate >= '{summaryTrackingRecordListIn.CreateDateStart.Value.LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss")}'");
            }

            if (summaryTrackingRecordListIn.CreateDateEnd != null)
            {
                sb.Append(@$" AND CreateDate < '{summaryTrackingRecordListIn.CreateDateEnd.Value.LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss")}'");
                countSb.Append(@$" AND CreateDate < '{summaryTrackingRecordListIn.CreateDateEnd.Value.LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss")}'");
            }

            sb.Append(@$"
                        GROUP BY Issuer) cct
                        ON cct.Issuer = su.Id
                    WHERE su.Deleted = 0
                    AND so.Deleted = 0");

            countSb.Append(@$"
                        GROUP BY Issuer) cct
                        ON cct.Issuer = su.Id
                    WHERE su.Deleted = 0
                    AND so.Deleted = 0");

            if (!string.IsNullOrEmpty(summaryTrackingRecordListIn.UserName))
            {
                sb.Append(@$" AND su.Name LIKE '%{summaryTrackingRecordListIn.UserName}%'");
                countSb.Append(@$" AND su.Name LIKE '%{summaryTrackingRecordListIn.UserName}%'");
            }

            if (!string.IsNullOrEmpty(summaryTrackingRecordListIn.OrganizationId))
            {
                sb.Append(@$" AND so.Id = '{summaryTrackingRecordListIn.OrganizationId}'");
                countSb.Append(@$" AND so.Id = '{summaryTrackingRecordListIn.OrganizationId}'");
            }

            sb.Append(@$" 
                    ORDER BY su.Id
                    LIMIT {(summaryTrackingRecordListIn.PageNumber - 1) * summaryTrackingRecordListIn.PageSize},{summaryTrackingRecordListIn.PageSize} ");

            List<SummaryTrackingRecordList_Out>
                result = DbOpe_crm_trackingrecord.Instance.SummaryTrackingRecordList(sb, countSb, ref total);
            _stringBuilderPool.Return(sb);
            _stringBuilderPool.Return(countSb);
            return result;
            #endregion
        }

        /// <summary>
        /// 删除跟踪记录信息，验证是否本人创建的跟踪记录，多条记录全部执行成功则返回成功，否则返回失败。只做逻辑删除，修改Deleted字段为1。
        /// </summary>
        /// <param name="deleteTrackingRecordIn"></param>
        /// <param name="canModifyCustomerTrackingStageInfo">是否回滚删除跟踪记录在客户表中的阶段状态</param>
        /// <returns></returns>
        public DeleteTrackingRecord_Out DeleteTrackingRecord(DeleteTrackingRecord_In deleteTrackingRecordIn, bool canModifyCustomerTrackingStageInfo = false)
        {
            DeleteTrackingRecord_Out result = new() { Data = 0 };
            if (string.IsNullOrEmpty(deleteTrackingRecordIn.Ids))
            {
                throw new ApiException("传入的跟踪记录Id为空!");
            }

            if (TokenModel.Instance == null || string.IsNullOrEmpty(TokenModel.Instance.id))
            {
                throw new ApiException("系统未登录!");
            }

            Dictionary<string, Db_crm_trackingrecord> datas = DbOpe_crm_trackingrecord.Instance.getTrackingRecordDictByIds(deleteTrackingRecordIn.Ids);
            if (datas == null || datas.Count == 0) return result;
            List<string> removeIdList = deleteTrackingRecordIn.Ids.Split(",").ToList();

            foreach (string id in removeIdList)
            {
                if (datas.ContainsKey(id))
                {
                    if (datas.TryGetValue(id, out var trackingrecord))
                    {
                        if (trackingrecord.TrackingStage == EnumTrackingStage.Received ||
                            trackingrecord.TrackingStage == EnumTrackingStage.SignedContract)
                        {
                            string errMsg = "签约成功及到账成功的数据由系统生成,无法执行删除操作!";
                            throw new ApiException(errMsg);
                        }

                        if (!trackingrecord.UserId.Equals(TokenModel.Instance.id))
                        {
                            string errMsg = "创建人非当前登录人，无法执行删除操作!";
                            throw new ApiException(errMsg);
                        }
                    }
                }
                else
                {
                    string errMsg = "创建人非当前登录人，无法执行删除操作!";
                    throw new ApiException(errMsg);
                }
            }

            if (canModifyCustomerTrackingStageInfo)
            {
                //批量修改状态
                // 1.先查询这些id要回退到什么状态
                List<Db_crm_trackingrecord> trackingrecords =
                    DbOpe_crm_trackingrecord.Instance.GetTrackingrecordListByIds(deleteTrackingRecordIn.Ids);
                List<string> customeridList = trackingrecords.Select(s => s.CustomerId).Distinct().ToList();

                // 调用刷新修改客户跟踪阶段
                foreach (string customerid in customeridList)
                {
                    DbOpe_crm_customer.Instance.RefreshTrackingStage(customerid);
                }

                // 2.查询这些客户在跟踪记录需要回滚的状态
                // Dictionary<string, EnumTrackingStage> customerWithTrackingStage =
                //     DbOpe_crm_trackingrecord.Instance.GetCustomerListTrackingStageStatus(customeridList,
                //         deleteTrackingRecordIn.Ids);

                // 3.调用方法执行回滚
                //BLL_Customer.Instance.ModifyCustomerTrackingStageInfo(customerWithTrackingStage); 

            }
            //执行批量删除
            DbOpe_crm_trackingrecord.Instance.DeleteTrackingRecord(deleteTrackingRecordIn.Ids);
            int execCount = DbOpe_crm_trackingrecord.Instance.SaveQueues();
            if (execCount > 0)
            {
                result.Data = 1;
            }

            return result;
        }

        /// <summary>
        /// 根据跟踪记录id判断是否存在
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public bool CheckTrackingRecordExists(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                throw new ArgumentNullException("跟踪记录id不可为空");
            }
            var dbData = DbOpe_crm_trackingrecord.Instance.QueryByPrimaryKey(id);
            return dbData is not null;
        }

        /// <summary>
        /// 校验跟踪记录的创建人是否为当前登陆人
        /// </summary>
        /// <param name="trackingRecord"></param>
        /// <returns>true == 校验通过,false == 校验不通过</returns>
        private bool CheckTrackingRecordCreator(Db_crm_trackingrecord trackingRecord)
        {
            if (TokenModel.Instance is null) return false;
            if (string.IsNullOrEmpty(trackingRecord.UserId)) return false;
            if (trackingRecord.UserId.Equals(TokenModel.Instance.id)) return true;
            return false;
        }

        /// <summary>
        /// 根据ids返回该id创建人及状态是否正常
        /// 签约成功及到账成功的将会返回false
        /// 创建人非当前用户的返回false
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ApiException"></exception>
        private Dictionary<string, bool> CheckTrackingRecordsCreatorAndState(string? ids)
        {
            if (string.IsNullOrEmpty(ids))
            {
                throw new ArgumentNullException("查找的跟踪记录id列表为空");
            }

            Dictionary<string, bool> result = ids.Split(",").ToDictionary(o => o, o => false);
            if (TokenModel.Instance == null || string.IsNullOrEmpty(TokenModel.Instance.id)) return result;

            Dictionary<string, Db_crm_trackingrecord> datas = DbOpe_crm_trackingrecord.Instance.getTrackingRecordDictByIds(ids);
            if (datas == null || datas.Count == 0) return result;
            foreach (string id in result.Keys)
            {
                if (datas.ContainsKey(id))
                {
                    if (datas.TryGetValue(id, out var trackingrecord))
                    {
                        if (trackingrecord.TrackingStage != EnumTrackingStage.Received &&
                            trackingrecord.TrackingStage != EnumTrackingStage.SignedContract)
                        {
                            result[id] = true;
                        }
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 校验新增和修改时的公共参数
        /// </summary>
        /// <returns></returns>
        private bool CheckTrackingRecordParams(TrackingRecordAddOrModifyBase param)
        {
            //对输入的参数进行校验
            // _ = DbOpe_sys_user.Instance.QueryByPrimaryKey(param.UserId) ?? throw new ApiException("用户主键所对应的数据不存在!");
            if (param.CustomerDataSource.Equals(EnumCustomerDataSource.Private))
            {
                //验证客户信息是否需要补录
                BLL_Customer.Instance.CheckCustomerInfoNeedSupplementary(new List<string>() { param.CustomerId });

                _ = DbOpe_crm_customer.Instance.QueryByPrimaryKey(param.CustomerId) ??
                    throw new ApiException("客户主键所对应的数据不存在!");
            }
            else if (param.CustomerDataSource.Equals(EnumCustomerDataSource.Temporary))
            {
                _ = DbOpe_crm_customer_temporary.Instance.QueryByPrimaryKey(param.CustomerId) ??
                    throw new ApiException("客户主键所对应的数据不存在!");
            }
            if (!string.IsNullOrEmpty(param.ContractId))
            {
                _ = DbOpe_crm_contract.Instance.QueryByPrimaryKey(param.ContractId) ??
                    throw new ApiException("合同主键所对应的数据不存在!");
            }
            return true;
        }

        /// <summary>
        /// 添加或更新跟踪记录[内部调用,非控制器接口调用]
        /// </summary>
        /// <param name="customerId">客户id</param>
        /// <param name="customerSubCompanyId">公司Id</param>
        /// <param name="contractId">合同Id</param>
        /// <param name="contractName">合同名称</param>
        /// <param name="trackingStage">跟踪阶段</param>
        /// <param name="type">1 == 新增, 2 == 更新</param>
        /// <param name="canHidden">是否隐藏</param>
        /// <param name="canModifyCustomerTrackingStageInfo">是否更新客户表中的阶段状态</param>
        /// <param name="userId">该条跟踪记录属于那个人,某些审核状态下错误的将跟踪记录记在了审核人员名下，跟踪记录的所有者某下状态下不一定是当前登录人</param>
        /// <param name="customerDataSource">客户来源</param>
        /// <param name="ignoreCustomerInfoCheck">忽略客户信息完整性验证</param>
        /// <param name="canEdit">是否允许编辑</param>
        /// <param name="ReceiptregisterId">业绩关联ID</param>
        /// <param name="BelongDate">业绩关联ID</param>
        /// <returns></returns>
        public bool InternalAddOrModifyTrackingRecord(string customerId, string customerSubCompanyId, string contractId, string contractName, EnumTrackingStage trackingStage, int type = 1, bool canHidden = false,
            bool canModifyCustomerTrackingStageInfo = false, string userId = "", EnumCustomerDataSource customerDataSource = EnumCustomerDataSource.Private, bool ignoreCustomerInfoCheck = false, bool canEdit = false, string ReceiptregisterId = "", DateTime? BelongDate = null)
        {
            if (!ignoreCustomerInfoCheck)
            {
                //验证客户信息是否需要补录
                BLL_Customer.Instance.CheckCustomerInfoNeedSupplementary(new List<string>() { customerId });
            }
            var customerData = DbOpe_crm_trackingrecord.Instance.GetCustomerInfo(customerId, customerDataSource, customerSubCompanyId);    //读取数据库中的客户信息并快照
            if (customerData is null && trackingStage != EnumTrackingStage.CollectFromPool)
            {
                LogUtil.AddErrorLog($"{customerDataSource.ToString()}中客户Id为{customerId}的客户信息不存在!");
                return false;
            }

            // 1.根据当前客户id获取最新1条
            var olddata = DbOpe_crm_trackingrecord.Instance.getTopTrackingrecordByCustomerId(customerId, customerDataSource, customerSubCompanyId);
            Db_crm_trackingrecord topDbData = new Db_crm_trackingrecord();
            if (olddata == null)
            {
                topDbData = new Db_crm_trackingrecord()
                {
                    CustomerDataSource = customerDataSource,
                    CustomerId = customerData.Id,
                    CustomerTemporaryId = customerDataSource == EnumCustomerDataSource.Temporary ? customerData.CustomerTemporaryId : null,
                    CustomerSubCompanyId = customerData.CustomerSubCompanyId,
                    CustomerSubCompanyTemporaryId = customerDataSource == EnumCustomerDataSource.Temporary ? customerData.CustomerSubCompanyTemporaryId : null,
                    IsVisible = customerDataSource.Equals(EnumCustomerDataSource.Private) ? 1 : 0,
                    Deleted = false,
                    TrackingType = null,
                    TrackingPurpose = null,
                    Hidden = true,
                };
            }
            else
            {
                topDbData = olddata.MappingTo<Db_crm_trackingrecord>();
                if (olddata.Hidden == true)
                {
                    topDbData.Hidden = false;
                }
            }

            //var topDbData = DbOpe_crm_trackingrecord.Instance.getTopTrackingrecordByCustomerId(customerId, customerDataSource, customerSubCompanyId) ?? new()
            //{
            //    CustomerDataSource = customerDataSource,
            //    CustomerId = customerData.Id,
            //    CustomerTemporaryId = customerDataSource == EnumCustomerDataSource.Temporary ? customerData.CustomerTemporaryId : null,
            //    CustomerSubCompanyId = customerData.CustomerSubCompanyId,
            //    CustomerSubCompanyTemporaryId = customerDataSource == EnumCustomerDataSource.Temporary ? customerData.CustomerSubCompanyTemporaryId : null,
            //    IsVisible = customerDataSource.Equals(EnumCustomerDataSource.Private) ? 1 : 0,
            //    Deleted = false,
            //    TrackingType = null,
            //    TrackingPurpose = null,
            //    Hidden = true,
            //};
            topDbData.UserId = string.IsNullOrEmpty(userId) ? TokenModel.Instance.id : userId;
            topDbData.TrackingStage = trackingStage; //赋值新的状态
            if (!string.IsNullOrEmpty(contractId))
            {
                topDbData.ContractId = contractId;
                topDbData.ContractName = string.IsNullOrEmpty(contractName)
                    ? DbOpe_crm_trackingrecord.Instance.GetContractInfo(contractId).ContractName
                    : contractName;
            }
            //2024年1月3日 增加不允许编辑标记
            topDbData.CanEdit = canEdit;
            //2024年1月5日 增加业绩关联ID 用于回滚记录
            topDbData.ReceiptregisterId = ReceiptregisterId.IsNotNullOrEmpty() ? ReceiptregisterId : null;
            //快照客户信息
            topDbData.CustomerName = customerData.CustomerName;
            topDbData.CustomerLevel = customerData.CustomerLevel;
            topDbData.ServiceState = customerData.ServiceState;
            topDbData.CustomerOrderNum = customerData.CustomerOrderNum;
            topDbData.CustomerNum = customerData.CustomerNum;
            topDbData.CustomerNature = customerData.CustomerNature;
            topDbData.CustomerSize = customerData.CustomerSize;
            topDbData.CustomerBindUserId = customerData.CustomerBindUserId;
            topDbData.CustomerBindUserOrgId = customerData.CustomerBindUserOrgId;
            topDbData.CustomerBindUserOrgName = customerData.OrgName;
            topDbData.CustomerSource = customerData.CustomerSource;
            topDbData.CollectionTime = customerData.CollectionTime;
            topDbData.CustomerIndustrys = customerData.CustomerIndustrys;
            if (BelongDate != null)
            {
                topDbData.RecBelongDate = BelongDate;
            }
            else
            {
                topDbData.RecBelongDate = DateTime.Now;
            }
            if (canHidden)
            {
                topDbData.Hidden = true;
            }
            else
            {
                topDbData.Hidden = false;
            }

            if (type == 1)
            {
                topDbData.Id = Guid.NewGuid().ToString();
                topDbData.CreateDate = DateTime.Now;
                topDbData.CreateUser = string.IsNullOrEmpty(userId) ? TokenModel.Instance.id : userId;
                topDbData.Remark = null;
                topDbData.UpdateUser = null;
                topDbData.UpdateDate = null;
                DbOpe_crm_trackingrecord.Instance.InsertQueue(topDbData);
            }
            else if (type == 2)
            {
                topDbData.UpdateUser = TokenModel.Instance.id;
                topDbData.UpdateDate = DateTime.Now;
                DbOpe_crm_trackingrecord.Instance.UpdateQueue(topDbData);
            }

            if (canModifyCustomerTrackingStageInfo)
            {
                //刷新客户的跟踪阶段
                DbOpe_crm_customer.Instance.RefreshTrackingStage(customerData.Id);
                // BLL_Customer.Instance.ModifyCustomerTrackingStageInfo(new Dictionary<string, EnumTrackingStage>()
                // {
                //     {customerId,trackingStage}
                // }); 
            }
            return DbOpe_crm_trackingrecord.Instance.SaveQueues() > 0;
        }

        /// <summary>
        /// 内部用于合同回滚跟踪记录
        /// </summary>
        /// <param name="customerId">客户Id</param>
        /// <param name="customerSubCompanyId">公司表Id</param>
        /// <param name="contractId">合同Id</param>
        /// <param name="userId">跟踪记录创建人Id</param>
        /// <param name="trackingStage">需要回退的跟踪阶段</param>
        /// <param name="canModifyCustomerTrackingStageInfo">是否同步客户跟踪阶段</param>
        /// <param name="ReceiptregisterId">到账相关ID</param>
        /// <returns></returns>
        public bool InternalUseForContractRollback(string customerId, string customerSubCompanyId, string contractId, string userId, EnumTrackingStage trackingStage, bool canModifyCustomerTrackingStageInfo = true, string ReceiptregisterId = "")
        {
            var result = DbOpe_crm_trackingrecord.Instance.InternalUseForContractRollback(customerId, customerSubCompanyId,
                contractId, userId, trackingStage);
            var schedule = DbOpe_crm_schedule.Instance.InternalUseForContractRollback(customerId, customerSubCompanyId,
                contractId, userId, trackingStage, ReceiptregisterId);
            if (canModifyCustomerTrackingStageInfo)
            {
                //刷新客户的跟踪阶段
                DbOpe_crm_customer.Instance.RefreshTrackingStage(customerId);
            }

            return result > 0;
        }

        /// <summary>
        /// 内部用于合同回滚跟踪记录
        /// </summary>
        /// <param name="customerId">客户Id</param>
        /// <param name="customerSubCompanyId">公司表Id</param>
        /// <param name="contractId">合同Id</param>
        /// <param name="trackingStage">需要回退的跟踪阶段</param>
        /// <param name="canModifyCustomerTrackingStageInfo">是否同步客户跟踪阶段</param>
        /// <param name="ReceiptregisterId">到账相关ID</param>
        /// <returns></returns>
        public bool InternalUseForContractRollback(string customerId, string customerSubCompanyId, string contractId, EnumTrackingStage trackingStage, bool canModifyCustomerTrackingStageInfo = true, string ReceiptregisterId = "")
        {
            var result = DbOpe_crm_trackingrecord.Instance.InternalUseForContractRollback(customerId, customerSubCompanyId,
                contractId, trackingStage, ReceiptregisterId);
            var schedule = DbOpe_crm_schedule.Instance.InternalUseForContractRollback(customerId, customerSubCompanyId,
                contractId, UserId, trackingStage, ReceiptregisterId);
            if (canModifyCustomerTrackingStageInfo)
            {
                //刷新客户的跟踪阶段
                DbOpe_crm_customer.Instance.RefreshTrackingStage(customerId);
            }

            return result > 0;
        }

        /// <summary>
        /// 导出跟踪记录汇总信息文件
        /// </summary>
        /// <param name="downloadSummaryTrackingRecordIn"></param>
        /// <param name="response"></param>
        /// <returns></returns>
        public IActionResult DownloadSummaryTrackingRecord(DownloadSummaryTrackingRecord_In downloadSummaryTrackingRecordIn, HttpResponse response)
        {
            var data = DbOpe_crm_trackingrecord.Instance.DownloadSummaryTrackingRecord(downloadSummaryTrackingRecordIn);
            //ExcelExporter exp = new();
            //var expBytes = exp.ExportAsByteArray(data).Result;            
            ExcelExporterNPOI exp = new();
            var expBytes = exp.ExportAsByteArray(data);
            var fileMemoryStream = new MemoryStream(expBytes);
            var fileName = "跟踪记录汇总.xlsx";
            string encodeFilename = HttpUtility.UrlEncode(fileName, Encoding.GetEncoding("UTF-8"));
            response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
            return new FileStreamResult(fileMemoryStream, "application/octet-stream");
        }
        /// <summary>
        /// 获取追踪记录发送人(树形结构）
        /// </summary>
        /// <returns></returns>
        public List<GetOrganizationWithUserTree_Out> GetTrackingReportPoster()
        {
            var baseData = DbOpe_crm_trackingrecord.Instance.GetTrackingReportPoster(UserId);
            List<GetOrganizationWithUserTree_Out> outData = new();
            if (baseData is not null and { Count: > 0 })
            {
                GetOrganizationWithUserTree_Out temp = default;
                foreach (GetBaseOrgWithUserTree item in baseData)
                {
                    temp = new GetOrganizationWithUserTree_Out()
                    {
                        Id = item.Id,
                        Name = item.OrgName,
                        Leaf = false,
                        Child = BLL_Organization.Instance.ConvertTreeOutModel(item.OrgChild, item.UserChild),
                    };
                    outData.Add(temp);
                }
            }
            return outData;
        }
    }
}