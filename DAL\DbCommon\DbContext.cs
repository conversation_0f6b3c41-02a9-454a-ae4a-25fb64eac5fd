﻿using CRM2_API.Common.AppSetting;
using SqlSugar;

namespace CRM2_API.DAL.DbCommon
{
    /// <summary>
    /// 数据库操作基础类
    /// </summary>
    public class DbContext
    {
        /// <summary>
        /// crm数据库(线程唯一)
        /// </summary>
        public static SqlSugarScope Crm2Db = new SqlSugarScope(new ConnectionConfig
        {
            ConnectionString = AppSettings.ConnectionStrings.CRM2 + ";charset=utf8mb4;",
            IsAutoCloseConnection = true,
            DbType = SqlSugar.DbType.MySql,
            InitKeyType = InitKeyType.Attribute,
            MoreSettings = new ConnMoreSettings()
            {
                EnableModelFuncMappingColumn = true//这一句
            },
        }, db =>
        {
            if (AppSettings.Env == Enum_SystemSettingEnv.Product)
                return;
            db.Aop.OnLogExecuting = (sql, pars) =>
            {
                var sqlstr = UtilMethods.GetSqlString(DbType.MySql, sql, pars);
                Console.WriteLine(sqlstr);
            };
        });
        /// <summary>
        /// Gtis数据库(线程唯一)
        /// </summary>
        public static SqlSugarScope GtisDb = new SqlSugarScope(new ConnectionConfig
        {
            ConnectionString = AppSettings.ConnectionStrings.GTIS,
            IsAutoCloseConnection = true,
            DbType = SqlSugar.DbType.MySql,
            InitKeyType = InitKeyType.Attribute
        }, db =>
        {
            if (AppSettings.Env == Enum_SystemSettingEnv.Product)
                return;
            db.Aop.OnLogExecuting = (sql, pars) =>
             {
                 Console.WriteLine(UtilMethods.GetSqlString(DbType.MySql, sql, pars));
             };
        });
        /// <summary>
        /// 接口日志数据库(线程唯一)
        /// </summary>
        public static SqlSugarScope LogDb = new SqlSugarScope(new ConnectionConfig
        {
            ConnectionString = AppSettings.ConnectionStrings.LOG,
            IsAutoCloseConnection = true,
            DbType = SqlSugar.DbType.SqlServer,
            InitKeyType = InitKeyType.Attribute
        }, db =>
        {
            if (AppSettings.Env == Enum_SystemSettingEnv.Product)
                return;
            db.Aop.OnLogExecuting = (sql, pars) =>
            {
                Console.WriteLine(UtilMethods.GetSqlString(DbType.SqlServer, sql, pars));
            };
        });
        
        /// <summary>
        /// ClickHouse日志数据库(线程唯一)
        /// </summary>
        public static SqlSugarScope ClickHouseDb = null;
        
        
        /// <summary>
        /// 初始化ClickHouse数据库
        /// </summary>
        public static void InitClickHouseDb()
        {
            try
            {
                var logConfig = AppSettings.LogConfig;
                if (logConfig != null && logConfig.EnableLogging)
                {
                    string clickHouseHost = logConfig.ClickHouseHost ?? "localhost";
                    int clickHousePort = logConfig.ClickHousePort > 0 ? logConfig.ClickHousePort : 8087;
                    string clickHouseUser = logConfig.ClickHouseUser ?? "default";
                    string clickHousePassword = logConfig.ClickHousePassword ?? "";
                    
                    // 注册ClickHouse提供程序
                    SqlSugar.InstanceFactory.CustomAssemblies = new System.Reflection.Assembly[] { typeof(SqlSugar.ClickHouse.ClickHouseProvider).Assembly };
                    
                    // 创建连接字符串
                    string connectionString = $"host={clickHouseHost};port={clickHousePort};user={clickHouseUser};password={clickHousePassword};database=logs;protocol=https;sslmode=none";
                    
                    Console.WriteLine($"初始化ClickHouse连接: {connectionString}");
                    
                    // 创建ClickHouse数据库连接
                    ClickHouseDb = new SqlSugarScope(new ConnectionConfig
                    {
                        ConnectionString = connectionString,
                        IsAutoCloseConnection = true,
                        DbType = SqlSugar.DbType.ClickHouse,
                        InitKeyType = InitKeyType.Attribute,
                        AopEvents = new AopEvents
                        {
                            OnLogExecuting = (sql, pars) =>
                            {
                                if (AppSettings.Env != Enum_SystemSettingEnv.Product)
                                {
                                    Console.WriteLine($"ClickHouse执行SQL: {UtilMethods.GetSqlString(DbType.ClickHouse, sql, pars)}");
                                }
                            }
                        }
                    });
                    
                    Console.WriteLine("ClickHouse连接初始化成功");
                    
                    // 确保数据库存在
                    ClickHouseDb.DbMaintenance.CreateDatabase("logs");
                    
                    // 检查表是否存在
                    var tables = ClickHouseDb.DbMaintenance.GetTableInfoList();
                    bool tableExists = tables.Any(t => t.Name.Equals("api_logs", StringComparison.OrdinalIgnoreCase));
                    
                    if (!tableExists)
                    {
                        Console.WriteLine("表api_logs不存在，准备创建...");
                        // 创建表结构
                        ClickHouseDb.CodeFirst.SetStringDefaultLength(200)
                                   .InitTables(typeof(DAL.DbModel.Log.Db_clickhouse_log));
                        Console.WriteLine("ClickHouse表结构创建完成");
                    }
                    else
                    {
                        Console.WriteLine("表api_logs已存在，跳过表结构创建");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化ClickHouse数据库失败: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
            }
        }
    }
}
