﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;

namespace CRM2_API.BLL.WorkLog
{
    public class WorkLog_User : BaseWorkLog<WorkLog_User>
    {

        /// <summary>
        /// 记录组织操作日志
        /// </summary>
        /// <param name="sysUserId"></param>
        /// <param name="enumUserLogType"></param>
        /// <param name="log"></param>
        public void AddOrgWorkLog(string sysUserId, EnumUserLogType enumUserLogType, string log)
        {
            var workLog = Com_WorkLog.Instance;
            var sysOpeUserLog = new Db_sys_operation_user_log();
            sysOpeUserLog.Id = Guid.NewGuid().ToString();
            sysOpeUserLog.userId = sysUserId;
            sysOpeUserLog.operateName = RedisCache.UserInfo.GetUserInfo(UserTokenInfo.id).Name;
            sysOpeUserLog.operateType = (int)enumUserLogType;
            sysOpeUserLog.sysLogId = workLog.Id;
            sysOpeUserLog.opterateTime = DateTime.Now;
            sysOpeUserLog.content = log;
            DbOpe_sys_operation_user_log.Instance.InsertQueue(sysOpeUserLog);
        }
    }
}
