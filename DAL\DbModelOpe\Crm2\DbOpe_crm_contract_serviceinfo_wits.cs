using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using SqlSugar;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_serviceinfo_wits表操作
    /// </summary>
    public class DbOpe_crm_contract_serviceinfo_wits : DbOperateCrm2Ex<Db_crm_contract_serviceinfo_wits, DbOpe_crm_contract_serviceinfo_wits>
    {
        /// <summary>
        /// 获取登记/复核信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public Db_crm_contract_serviceinfo_wits GetRegisterInfoByApplId(string applId)
        {
            return Queryable
                .Where(e => e.WitsApplId == applId)
                .Where(e => e.Deleted == false && e.IsChanged == false && e.IsHistory == false)
                .First();
        }

        /// <summary>
        /// 获取所有曾经开通过的Gtis服务数据
        /// </summary>
        /// <param name="contractNum"></param>
        /// <returns></returns>
        public Db_crm_contract_serviceinfo_wits GetOnceOpendServiceByContractNum(string contractNum)
        {
            return Queryable
                .Where(e => e.ContractNum == contractNum)
                .Where(e => e.Deleted == false && e.IsHistory == false && e.IsChanged == false)
                .Where(e => e.State == EnumContractServiceState.VALID || e.State == EnumContractServiceState.OUT || e.State == EnumContractServiceState.INVALID)
                .OrderByDescending(e => e.ReviewerDate)
                .First();
        }

        /// <summary>
        /// 根据申请Id获取慧思产品申请基本信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public GetWitsApplyInfo4Audit_Out_RegisterInfo GetWitsServiceBasicInfo(string applId)
        {
            return Queryable
               .LeftJoin<Db_crm_contract_productserviceinfo_wits_appl>((e, f) => e.WitsApplId == f.Id)
               .LeftJoin<Db_v_serviceinfo_wits_status>((e, f, g) => f.Id == g.Id)
               .Where(e => e.WitsApplId == applId)
               .Where(e => e.Deleted == false && e.IsChanged == false && e.IsHistory == false)
               .Select((e, f, g) => new GetWitsApplyInfo4Audit_Out_RegisterInfo_Replenish
               {
                   Id = e.Id.SelectAll(),
                   State = g.State,
                   ApplyRemark = f.Remark,
                   CounponDetailIds = f.CounponDetailIds,
                   OriContractNum = e.OldContractNum,
               })
               .Mapper(item =>
               {
                   //Gtis申请内容
                   if (item.IsGtisAudit)
                   {
                       item.GtisInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetServiceInfoByWitsApplyId(item.WitsApplId);
                       if (item.GtisInfo.ProductType == EnumProductType.Vip)
                           item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.Vip);
                       else
                           item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.Gtis);
                   }
                   //环球搜申请内容
                   if (item.IsGlobalSearchAudit)
                   {
                       item.GlobalSearchInfo = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetServiceInfoByWitsApplyId(item.WitsApplId);
                       item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.GlobalSearch);
                   }
                   //慧思学院申请内容
                   if (item.IsCollegeAudit)
                   {
                       item.CollegeInfo = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByWitsApplyId(item.WitsApplId);
                       item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.College);
                   }
                   //SalesWits申请内容
                   if (item.IsSalesWitsAudit)
                   {
                       item.SalesWitsInfo = DbOpe_crm_contract_serviceinfo_saleswits.Instance.GetServiceInfoByWitsApplyId(item.WitsApplId);
                       item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.SalesWits);
                   }
                   //账号信息
                   var userList = DbOpe_crm_contract_serviceinfo_wits_user.Instance.GetUserListByWitsId(item.Id);
                   item.UserList = userList.Where(e => !string.IsNullOrEmpty(e.AccountNumber)).OrderByDescending(e => e.AccountType).OrderBy(e => e.AccountNumber).Concat(userList.Where(e => string.IsNullOrEmpty(e.AccountNumber))).ToList();
                   //续约服务券使用数量
                   if (!string.IsNullOrEmpty(item.CounponDetailIds))
                       item.CouponCounts = item.CounponDetailIds.Split(',').ToList().Count;
               })
               .First()
               .MappingTo<GetWitsApplyInfo4Audit_Out_RegisterInfo>();
        }

        /// <summary>
        /// 更新所有业务服务的备注内容
        /// </summary>
        /// <param name="review_In"></param>
        /// <param name="serveData"></param>
        public void UpdAllServiceRemark(ReviewWitsApply_In review_In, Db_crm_contract_serviceinfo_wits serveData)
        {
            //wits表备注内容
            Updateable
                .SetColumns(e => new Db_crm_contract_serviceinfo_wits { RegisteredRemark = review_In.Remark, Remark = review_In.FeedBack })
                .Where(e => e.Id == serveData.Id)
                .ExecuteCommand();
            //如果存在gtis
            if (serveData.IsGtisAudit)
            {
                Db.Updateable<Db_crm_contract_serviceinfo_gtis>()
                    .SetColumns(e => new Db_crm_contract_serviceinfo_gtis { ReviewerRemark = review_In.Remark, Remark = review_In.FeedBack })
                    .Where(e => e.WitsApplId == review_In.WitsApplId && e.IsChanged == 0 && e.IsApplHistory == false && e.Deleted == false)
                    .ExecuteCommand();
            }
            //如果存在环球搜
            if (serveData.IsGlobalSearchAudit)
            {
                Db.Updateable<Db_crm_contract_serviceinfo_globalsearch>()
                    .SetColumns(e => new Db_crm_contract_serviceinfo_globalsearch { ReviewerRemark = review_In.GlobalSearchRemark, Remark = review_In.GlobalSearchRemark, FeedBack = review_In.FeedBack })
                    .Where(e => e.WitsApplId == review_In.WitsApplId && e.IsChanged == false && e.IsHistory == false && e.Deleted == false)
                    .ExecuteCommand();
            }
            //如果存在慧思学院
            if (serveData.IsCollegeAudit)
            {
                Db.Updateable<Db_crm_contract_serviceinfo_college>()
                    .SetColumns(e => new Db_crm_contract_serviceinfo_college { ReviewerRemark = review_In.CollegeRemark, Remark = review_In.CollegeRemark })
                    .Where(e => e.WitsApplId == review_In.WitsApplId && e.IsChanged == false && e.IsHistory == false && e.Deleted == false)
                    .ExecuteCommand();
            }
            //如果存在saleswits
            if (serveData.IsSalesWitsAudit)
            {
                Db.Updateable<Db_crm_contract_serviceinfo_saleswits>()
                    .SetColumns(e => new Db_crm_contract_serviceinfo_saleswits { ReviewerRemark = review_In.SalesWitsRemark, Remark = review_In.SalesWitsRemark })
                    .Where(e => e.WitsApplId == review_In.WitsApplId && e.IsChanged == false && e.IsHistory == false && e.Deleted == false)
                    .ExecuteCommand();
            }
        }

        /// <summary>
        /// 获取审核反馈&复核备注
        /// </summary>
        /// <param name="applyId"></param>
        /// <returns></returns>
        public List<ReviewFeedback> GetReviewFeedbacks(string applyId)
        {
            return Queryable
                .Where(e => e.WitsApplId == applyId)
                .Where(e => e.Deleted == false)
                .OrderByDescending(e => e.CreateDate)
                .Select(e => new ReviewFeedback
                {
                    RegisteredId = e.RegisteredId,
                    RegisteredDate = e.RegisteredDate,
                    ReviewerId = e.ReviewerId,
                    ReviewerdDate = e.ReviewerDate,
                    Remark = e.Remark,
                    FeedBack = e.FeedBack
                })
                .Mapper(item =>
                {
                    item.RegisteredName = Db.Queryable<Db_v_userwithorg>().Where(e => e.Id == item.RegisteredId).First()?.Name;
                    item.ReviewerName = Db.Queryable<Db_v_userwithorg>().Where(e => e.Id == item.ReviewerId).First()?.Name;
                })
                .ToList();
        }

        /// <summary>
        /// 根据申请Id获取账号信息
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <returns></returns>
        public List<AddOrAuditWitsApplUser> GetUserListByWitsApplId(string witsApplId)
        {
            Queryable
                .LeftJoin<Db_crm_contract_serviceinfo_wits_user>((serve, user) => user.WitsServeId == serve.Id && user.Deleted == false)
                .Where(serve => serve.Deleted == false && serve.IsHistory == false && serve.IsChanged == false)
                .Where(serve => serve.WitsApplId == witsApplId)
                .Select((serve, user) => new AddOrAuditWitsApplUser()
                {
                    SysUserId = user.SysUserId,
                    AccountType = user.AccountType,
                    AccountNumber = user.AccountNumber,
                    SharePeopleNum = user.SharePeopleNum,
                    GtisPermission = user.GtisPermission,
                    VipPermission = user.VipPermission,
                    GlobalSearchPermission = user.GlobalSearchPermission,
                    CollegePermission = user.CollegePermission,
                    SalesWitsPermission = user.SalesWitsPermission,
                    SalesWitsBindPhoneId = user.SalesWitsBindPhoneId,
                })
                .ToList();
            return null;
        }
    }
}
