-- 服务变更字段配置 - 其他 (ChangeReasonEnum = 8)
-- 执行日期: 2025-01-29

-- ================================
-- 其他 - 申请场景字段
-- ================================

-- GTIS服务申请时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 1, 8, 'GtisAccountNum', 'GTIS子账号数量', 10, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisServiceMonth', 'GTIS服务开通月数', 11, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisServiceStart', 'GTIS服务开始日期', 12, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisServiceEnd', 'GTIS服务结束日期', 13, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisDownloadPermission', '下载权限', 14, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisCustomReport', '定制报告', 15, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisCustomReportNum', '定制报告数', 16, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisSubAccountCountryPermission', '子账号国家权限', 17, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
-- (UUID(), 1, 8, 'GtisShareUsageTotal', '共享使用总数', 18, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisUserNum', '使用者数', 19, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisApplyCountry', '申请国家', 20, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0);

-- 环球搜服务申请时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 3, 8, 'GlobalSearchAccountNum', '环球搜账号数量', 30, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 8, 'GlobalSearchServiceMonth', '环球搜服务开通月数', 31, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 8, 'GlobalSearchServiceStart', '环球搜服务开始日期', 32, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 8, 'GlobalSearchServiceEnd', '环球搜服务结束日期', 33, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0);
-- (UUID(), 3, 8, 'GlobalSearchPaymentPeriod', '环球搜收款时段', 34, 1, 2, 'apply', NULL, '其他原因时申请仅展示', NOW(), 'system', NULL, 'system', 0),
-- (UUID(), 3, 8, 'GlobalSearchAccountLevel', '环球搜账户级别', 35, 1, 2, 'apply', NULL, '其他原因时申请仅展示', NOW(), 'system', NULL, 'system', 0);

-- 慧思学院服务申请时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 4, 8, 'CollegeAccountNum', '慧思学院账号数量', 40, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 8, 'CollegeServiceMonth', '慧思学院服务开通月数', 41, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 8, 'CollegeServiceStart', '慧思学院服务开始日期', 42, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 8, 'CollegeServiceEnd', '慧思学院服务结束日期', 43, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0);

-- SalesWits服务申请时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 2, 8, 'SalesWitsAccountNum', 'SalesWits账号数量', 50, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsServiceMonth', 'SalesWits服务开通月数', 51, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsServiceStart', 'SalesWits服务开始日期', 52, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsServiceEnd', 'SalesWits服务结束日期', 53, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0),
-- (UUID(), 2, 8, 'SalesWitsGiftResourceMonths', 'SalesWits赠送资源月份数', 54, 1, 2, 'apply', NULL, '其他原因时申请仅展示', NOW(), 'system', NULL, 'system', 0),
-- (UUID(), 2, 8, 'SalesWitsGiftTokenNum', 'SalesWits赠送Token数量', 55, 1, 2, 'apply', NULL, '其他原因时申请仅展示', NOW(), 'system', NULL, 'system', 0),
-- (UUID(), 2, 8, 'SalesWitsGiftEmailNum', 'SalesWits赠送邮件数量', 56, 1, 2, 'apply', NULL, '其他原因时申请仅展示', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsUsers', 'SalesWits使用者', 57, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0);

-- 慧思服务通用字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 8, 'AccountList', '账号列表', 60, 1, 1, 'apply', NULL, '其他原因时申请可修改', NOW(), 'system', NULL, 'system', 0);

-- ================================
-- 其他 - 审核场景字段
-- ================================

-- GTIS服务审核时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 1, 8, 'GtisAccountNum', 'GTIS子账号数量', 70, 1, 1, 'audit', 'AccountList,GtisAccountNum,GtisShareUsageTotal,GtisUserNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisServiceMonth', 'GTIS服务开通月数', 71, 1, 1, 'audit', 'GtisServiceStart,GtisServiceEnd,GtisServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisServiceStart', 'GTIS服务开始日期', 72, 1, 1, 'audit', 'GtisServiceStart,GtisServiceEnd,GtisServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisServiceEnd', 'GTIS服务结束日期', 73, 1, 1, 'audit', 'GtisServiceStart,GtisServiceEnd,GtisServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisDownloadPermission', '下载权限', 74, 1, 1, 'audit', 'GtisDownloadPermission', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisCustomReport', '定制报告', 75, 1, 1, 'audit', 'GtisCustomReport,GtisCustomReportNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisCustomReportNum', '定制报告数', 76, 1, 1, 'audit', 'GtisCustomReport,GtisCustomReportNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisSubAccountCountryPermission', '子账号国家权限', 77, 1, 1, 'audit', 'AccountList,GtisAccountNum,GtisShareUsageTotal,GtisUserNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisShareUsageTotal', '共享使用总数', 78, 1, 1, 'audit', 'AccountList,GtisAccountNum,GtisShareUsageTotal,GtisUserNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisUserNum', '使用者数', 79, 1, 1, 'audit', 'AccountList,GtisAccountNum,GtisShareUsageTotal,GtisUserNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 1, 8, 'GtisApplyCountry', '申请国家', 80, 1, 1, 'audit', 'GtisApplyCountry', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- 环球搜服务审核时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 3, 8, 'GlobalSearchAccountNum', '环球搜账号数量', 90, 1, 1, 'audit', 'AccountList,GlobalSearchAccountNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 8, 'GlobalSearchServiceMonth', '环球搜服务开通月数', 91, 1, 1, 'audit', 'GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 8, 'GlobalSearchServiceStart', '环球搜服务开始日期', 92, 1, 1, 'audit', 'GlobalSearchServiceEnd,GlobalSearchServiceMonth,GlobalSearchServiceStart,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 8, 'GlobalSearchServiceEnd', '环球搜服务结束日期', 93, 1, 1, 'audit', 'GlobalSearchServiceStart,GlobalSearchServiceMonth,GlobalSearchServiceEnd,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 8, 'GlobalSearchPaymentPeriod', '环球搜收款时段', 94, 1, 1, 'audit', 'GlobalSearchPaymentPeriod,GlobalSearchAccountLevel,GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 3, 8, 'GlobalSearchAccountLevel', '环球搜账户级别', 95, 1, 1, 'audit', 'GlobalSearchPaymentPeriod,GlobalSearchAccountLevel,GlobalSearchServiceStart,GlobalSearchServiceEnd,GlobalSearchServiceMonth', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- 慧思学院服务审核时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 4, 8, 'CollegeAccountNum', '慧思学院账号数量', 100, 1, 1, 'audit', 'AccountList,CollegeAccountNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 8, 'CollegeServiceMonth', '慧思学院服务开通月数', 101, 1, 1, 'audit', 'CollegeServiceStart,CollegeServiceEnd,CollegeServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 8, 'CollegeServiceStart', '慧思学院服务开始日期', 102, 1, 1, 'audit', 'CollegeServiceStart,CollegeServiceEnd,CollegeServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 4, 8, 'CollegeServiceEnd', '慧思学院服务结束日期', 103, 1, 1, 'audit', 'CollegeServiceStart,CollegeServiceEnd,CollegeServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- SalesWits服务审核时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 2, 8, 'SalesWitsAccountNum', 'SalesWits账号数量', 110, 1, 1, 'audit', 'AccountList,SalesWitsAccountNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsServiceMonth', 'SalesWits服务开通月数', 111, 1, 1, 'audit', 'SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsServiceStart', 'SalesWits服务开始日期', 112, 1, 1, 'audit', 'SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsServiceEnd', 'SalesWits服务结束日期', 113, 1, 1, 'audit', 'SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth,PersonalServiceDays,SelfPaidDays,CouponList', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsGiftResourceMonths', 'SalesWits赠送资源月份数', 114, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsGiftResourceMonths', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsGiftTokenNum', 'SalesWits赠送Token数量', 115, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsGiftTokenNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsGiftEmailNum', 'SalesWits赠送邮件数量', 116, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsGiftEmailNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsRecharge', 'SaleWits充值', 117, 1, 1, 'audit', 'SalesWitsRecharge', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 8, 'SalesWitsUsers', 'SalesWits使用者', 118, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsUsers', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- 慧思服务通用字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 8, 'AccountList', '账号列表', 120, 1, 1, 'audit', 'AccountList,GtisAccountNum,GtisShareUsageTotal,GtisUserNum,GlobalSearchAccountNum,CollegeAccountNum,SalesWitsAccountNum', '其他原因时审核可修改', NOW(), 'system', NULL, 'system', 0);

SELECT '其他原因配置完成！' AS message; 