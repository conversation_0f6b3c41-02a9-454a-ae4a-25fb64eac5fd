# CRM2 API 测试环境服务自动重启部署说明

本文档提供了如何在测试环境中使用systemd配置CRM2 API服务自动重启的完整指南。此配置已针对您提供的测试环境路径(`/home/<USER>/`)进行了优化。

## 1. 文件说明

本测试环境部署包含以下文件：

- **crm-api-test.service**: 测试环境主服务配置文件
- **install-crm-test.sh**: 测试环境安装脚本
- **monitor-crm-test.sh**: 健康监控脚本
- **crm-api-test-monitor.service**: 监控服务配置文件

## 2. 部署步骤

### 2.1 准备文件

先将所有配置文件上传到服务器，并设置执行权限：

```bash
# 确保文件行尾格式正确（避免Windows/Linux行尾问题）
dos2unix *.sh

# 设置执行权限
chmod +x install-crm-test.sh
chmod +x monitor-crm-test.sh
```

### 2.2 部署主服务

运行安装脚本安装主服务：

```bash
sudo ./install-crm-test.sh
```

这将执行以下操作：
- 停止现有的CRM2服务
- 复制服务文件到systemd目录
- 从publish目录复制最新文件到Release目录
- 创建日志目录
- 启用并启动服务

### 2.3 部署监控服务（推荐）

监控服务提供额外的保障，定期检查API的健康状态：

```bash
# 复制监控脚本到系统目录
sudo cp monitor-crm-test.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/monitor-crm-test.sh

# 安装监控服务
sudo cp crm-api-test-monitor.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable crm-api-test-monitor.service
sudo systemctl start crm-api-test-monitor.service
```

## 3. 服务管理命令

### 3.1 主服务管理

```bash
# 启动服务
sudo systemctl start crm-api-test.service

# 停止服务
sudo systemctl stop crm-api-test.service

# 重启服务
sudo systemctl restart crm-api-test.service

# 查看服务状态
sudo systemctl status crm-api-test.service

# 查看服务日志
sudo journalctl -u crm-api-test.service -f
```

### 3.2 监控服务管理

```bash
# 启动监控服务
sudo systemctl start crm-api-test-monitor.service

# 停止监控服务
sudo systemctl stop crm-api-test-monitor.service

# 查看监控服务状态
sudo systemctl status crm-api-test-monitor.service

# 查看监控日志
cat /home/<USER>/monitor.log
```

## 4. 更新应用程序

当您需要更新应用程序时，只需将新文件放入`/home/<USER>/publish`目录，然后运行：

```bash
# 重新运行安装脚本，它会复制新文件并重启服务
sudo ./install-crm-test.sh

# 或者手动执行：
sudo cp -rf /home/<USER>/publish/* /home/<USER>/api/Release/
sudo systemctl restart crm-api-test.service
```

## 5. 故障排查

如果服务无法启动或自动重启不正常，请检查：

1. 服务日志：
   ```bash
   sudo journalctl -u crm-api-test.service -f
   ```

2. 应用程序日志：
   ```bash
   cat /home/<USER>/log
   ```

3. 监控日志：
   ```bash
   cat /home/<USER>/monitor.log
   ```

4. 服务状态：
   ```bash
   sudo systemctl status crm-api-test.service
   sudo systemctl status crm-api-test-monitor.service
   ```

5. 检查OpenSSL配置：
   ```bash
   # 确认OpenSSL配置文件存在
   ls -la /etc/ssl/openssl_legacy.cnf
   ```

## 6. 与您原有脚本的对比

此部署方案是对您原有脚本的增强：

```bash
# 您原有的脚本
#! /bin/bash
# 设置 OpenSSL 配置
export OPENSSL_CONF=/etc/ssl/openssl_legacy.cnf
echo "正在停止CRM服务..."
kill -9 $(ps -ef | grep api/Release/CRM2_API | grep -v grep | awk '{print $2}')
echo "正在启动CRM服务..."
cd /home/<USER>/publish
ls
cp -rf * /home/<USER>/api/Release
nohup dotnet /home/<USER>/api/Release/CRM2_API.dll >/home/<USER>/log 2>&1 &
echo "CRM服务启动成功！"
```

新方案的优势：
1. 使用systemd管理服务，提供自动重启功能
2. 增加了健康检查监控，主动检测服务问题
3. 保留了原有脚本的所有功能（包括OpenSSL配置、文件复制等）
4. 提供了更详细的日志记录
5. 添加了开机自启功能 