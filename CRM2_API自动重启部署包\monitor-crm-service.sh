#!/bin/bash

# CRM2 API服务端口
PORT=6001
# 健康检查端点
HEALTH_ENDPOINT="/api/Home/HealthCheck"
# 健康检查间隔（秒）
CHECK_INTERVAL=10
# 连续失败次数阈值，超过此值则重启服务
FAILURE_THRESHOLD=6
# 成功日志记录间隔（分钟）
SUCCESS_LOG_INTERVAL=5

# 日志文件
LOG_FILE="/hqhs_data/00.API/01.CRM/log/monitor.log"

# 确保日志目录存在
mkdir -p $(dirname $LOG_FILE)

log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "启动CRM2 API服务监控"

failure_count=0
last_success_log_time=0

while true; do
  # 检查服务是否运行
  if ! systemctl is-active --quiet crm-api.service; then
    log "服务未运行，尝试启动..."
    systemctl start crm-api.service
    sleep 30  # 等待服务启动
    continue
  fi
  
  # 获取当前时间（Unix时间戳）
  current_time=$(date +%s)
  
  # 检查服务是否响应
  HEALTH_CHECK_RESULT=$(curl -s -o /dev/null -w "%{http_code}" -m 10 "http://localhost:$PORT$HEALTH_ENDPOINT")
  
  if [ "$HEALTH_CHECK_RESULT" == "200" ]; then
    # 计算距上次记录成功日志的时间（分钟）
    time_diff=$(( (current_time - last_success_log_time) / 60 ))
    
    # 仅当达到记录间隔或首次检查时记录成功日志
    if [ $time_diff -ge $SUCCESS_LOG_INTERVAL ] || [ $last_success_log_time -eq 0 ]; then
      log "服务健康检查通过，状态码：$HEALTH_CHECK_RESULT"
      last_success_log_time=$current_time
    fi
    
    failure_count=0
  else
    failure_count=$((failure_count + 1))
    log "服务健康检查失败，状态码：$HEALTH_CHECK_RESULT，连续失败次数: $failure_count/$FAILURE_THRESHOLD"
    
    if [ $failure_count -ge $FAILURE_THRESHOLD ]; then
      log "超过失败阈值，重启服务..."
      systemctl restart crm-api.service
      log "服务已重启"
      failure_count=0
      sleep 30  # 等待服务启动
    fi
  fi
  
  sleep $CHECK_INTERVAL
done 