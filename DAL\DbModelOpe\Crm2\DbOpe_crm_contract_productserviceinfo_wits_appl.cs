using CRM2_API.BLL;
using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using SqlSugar;
using System.Collections.Generic;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_productserviceinfo_appl表操作
    /// </summary>
    public class DbOpe_crm_contract_productserviceinfo_wits_appl : DbOperateCrm2Ex<Db_crm_contract_productserviceinfo_wits_appl, DbOpe_crm_contract_productserviceinfo_wits_appl>
    {
        /// <summary>
        /// 将相同contractProductInfoGropuId的数据置位失效
        /// </summary>
        /// <param name="ContractProductInfoSeriesId"></param>
        public void InvalidOldApply(string ContractProductInfoSeriesId)
        {
            Updateable
                .SetColumns(e => e.IsInvalid == EnumIsInvalid.Invalid)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .SetColumns(e => e.UpdateUser == UserId)
                .Where(e => e.ContractProductInfoSeriesId == ContractProductInfoSeriesId)
                .Where(e => e.Deleted == false)
                .Where(e => e.IsInvalid == EnumIsInvalid.Effective)
                .ExecuteCommand();
        }

        /// <summary>
        /// 把现在生效产品对应的申请,状态重新置为有效  (如果剩下也全是拒绝的那就全是无效也无所谓了)
        /// </summary>
        /// <param name="ContractProductInfoSeriesId"></param>
        public void ReActiveCurrentWitsAppl(string ContractProductInfoSeriesId)
        {
            //查找应该被恢复的申请Id
            var applId = Queryable
                .LeftJoin<Db_v_serviceinfo_wits_status>((e, f) => e.Id == f.Id)
                .Where(e => e.ContractProductInfoSeriesId == ContractProductInfoSeriesId)
                .Where(e => e.Deleted == false)
                .Where((e, f) => f.State == EnumContractServiceOpenState.Open || f.State == EnumContractServiceOpenState.NearlyEnd || f.State == EnumContractServiceOpenState.OverDue)
                .Select(e => e.Id)
                .First();
            if (!string.IsNullOrEmpty(applId))
            {
                //恢复申请的有效状态
                Updateable.SetColumns(e => e.IsInvalid == EnumIsInvalid.Effective).SetColumns(e => e.UpdateDate == DateTime.Now).SetColumns(e => e.UpdateUser == UserId)
                    .Where(e => e.Id == applId).Where(e => e.Deleted == false)
                    .ExecuteCommand();
            }
        }

        /// <summary>
        /// 根据合同产品系列Id判断是否禁止进行服务申请
        /// </summary>
        /// <param name="contractProductInfoSeiresId"></param>
        /// <param name="exceptAppl"></param>
        /// <returns></returns>
        public bool IsForbidApplyService(string contractProductInfoSeiresId, string exceptAppl = "")
        {
            return Queryable
                .Where(e => e.ContractProductInfoSeriesId == contractProductInfoSeiresId)
                .Where(e => e.Deleted == false)
                .Where(e => e.IsInvalid == EnumIsInvalid.Effective)
                .Where(e => e.Id != exceptAppl)
                .Where(e => e.State == EnumProcessStatus.Submit || e.State == EnumProcessStatus.Pass)
                .Any();
        }

        /// <summary>
        /// 判断甲方公司FirstParty是否存在审核中的服务申请
        /// </summary>
        /// <param name="firstParty"></param>
        /// <returns></returns>
        public bool IsExistInReviewApplyByFirstParty(string firstParty)
        {
            return Queryable
               .LeftJoin<Db_crm_contract>((appl, contract) => appl.ContractId == contract.Id && contract.Deleted == false)
               .Where((appl, contract) => appl.State == EnumProcessStatus.Submit)
               .Where((appl, contract) => appl.Deleted == false)
               .Where((appl, contract) => appl.IsInvalid == EnumIsInvalid.Effective)
               .Where((appl, contract) => contract.FirstParty == firstParty || appl.RenewFirstParty == firstParty)
               .Any();
        }

        /// <summary>
        /// 查看服务申请列表
        /// </summary>
        /// <param name="search_In"></param>
        /// <param name="total"></param>
        public List<SearchWitsApplList_Out> SearchWitsApplList(SearchWitsApplList_In search_In, ref int total)
        {
            #region 时间参数
            var dt = DateTime.Now;
            var dtDay = DateTime.Parse(dt.ToString("yyyy-MM-dd"));
            var weekEnd = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).AddDays(6).ToString("yyyy-MM-dd");
            var weekStart = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).ToString("yyyy-MM-dd");
            #endregion
            #region 当前操作人权限参数，不同权限可见的数据状态不同
            var roleId = DbOpe_sys_role.Instance.GetRoleIdByUserId(UserId);
            List<int> servOpenStateList = new List<int>();
            //获取当前人员可操作的按钮
            var buttonIds = RedisCache.UserButtonRight.GetUserButtonRight(UserId).Select(e => e.Id).ToList();
            if (buttonIds.Contains("fec2c764-a545-4a2c-8a8f-8593e05edcb3"))
            {//拥有初审功能按钮可查看数据：开通状态为待开通、待变更、被驳回、拒绝、待复核、已开通。
                servOpenStateList.AddRange(new List<int> { EnumContractServiceOpenState.ToBeOpened.ToInt(), EnumContractServiceOpenState.TobeChanged.ToInt(), EnumContractServiceOpenState.Returned.ToInt(), EnumContractServiceOpenState.Refuse.ToInt(), EnumContractServiceOpenState.ToBeReview.ToInt(), EnumContractServiceOpenState.Open.ToInt() });
            }
            if (buttonIds.Contains("cefc95c3-fa17-43ca-bb7e-e458e21b2e3b"))
            {//拥有复核功能按钮可查看数据：开通状态为：待复核、被驳回、已开通、即将到期、过期。
                servOpenStateList.AddRange(new List<int> { EnumContractServiceOpenState.ToBeReview.ToInt(), EnumContractServiceOpenState.Returned.ToInt(), EnumContractServiceOpenState.Open.ToInt(), EnumContractServiceOpenState.NearlyEnd.ToInt(), EnumContractServiceOpenState.OverDue.ToInt(), EnumContractServiceOpenState.Void.ToInt() });
            }
            servOpenStateList = servOpenStateList.Distinct().ToList();
            #endregion
            //验证超级权限（系统管理员或高级管理人员）。超级权限可以看到没有客户编码的申请数据
            bool superRole = BLL_Role.Instance.CheckSuperUser();
            //查询条件如果有通过时间，同时要筛选服务状态            
            var approvalStates = new List<EnumContractServiceState>() { EnumContractServiceState.VALID, EnumContractServiceState.OUT, EnumContractServiceState.INVALID };

            var retList = new List<SearchWitsApplList_Out>();
            retList = Queryable
                .LeftJoin<Db_crm_contract_serviceinfo_wits>((apply, service) => service.WitsApplId == apply.Id && service.Deleted == false && service.IsHistory == false)
                .LeftJoin<Db_crm_contract>((apply, service, contract) => apply.ContractId == contract.Id && contract.Deleted == false)
                .LeftJoin<Db_crm_customer_subcompany>((apply, service, contract, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_v_userwithorg>((apply, service, contract, company, applicant) => apply.ApplicantId == applicant.Id && applicant.Deleted == false)
                .LeftJoin<Db_v_userwithorg>((apply, service, contract, company, applicant, reviewer) => apply.ReviewerId == reviewer.Id && reviewer.Deleted == false)
                .LeftJoin<Db_v_serviceinfo_wits_status>((apply, service, contract, company, applicant, reviewer, view) => apply.Id == view.Id)
                .Where(apply => apply.Deleted == false && apply.IsInvalid == EnumIsInvalid.Effective)
                .WhereIF(!superRole, (apply, service, contract) => !SqlFunc.IsNullOrEmpty(contract.ContractNum))//超级权限可查看没有客户编码的申请
                .WhereIF(!string.IsNullOrEmpty(search_In.ContractNum), (apply, service, contract) => contract.ContractNum == search_In.ContractNum)//按客户编码筛选
                .WhereIF(!string.IsNullOrEmpty(search_In.FirstPartyName), (apply, service, contract, company) => company.CompanyName.Contains(search_In.FirstPartyName))//按甲方公司筛选
                .WhereIF(!string.IsNullOrEmpty(search_In.ContractName), (apply, service, contract) => contract.ContractName.Contains(search_In.ContractName))//按合同名称筛选
                .WhereIF(!ArrayUtil.IsNullOrEmpty(search_In.ApplicantId), (apply, service, contract, company, applicant) => SqlFunc.ContainsArray(search_In.ApplicantId, applicant.Id))//按申请人筛选
                .WhereIF(!string.IsNullOrEmpty(search_In.Remark), apply => SqlFunc.Contains(apply.Remark4List, search_In.Remark))//按备注筛选
                .WhereIF(search_In.ContractType != null, (apply, service, contract) => search_In.ContractType == contract.ContractType)//按签约类型筛选
                .WhereIF(search_In.StampReviewStatus != null, (apply, service, contract) => contract.StampReviewStatus == search_In.StampReviewStatus)//按盖章合同状态筛选
                .WhereIF(search_In.State != null, (apply, service, contract, company, applicant, reviewer, view) => search_In.State.Contains(view.State))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(search_In.ServiceType), apply => SqlFunc.ContainsArray(search_In.ServiceType, apply.ProcessingType))//按服务类型筛选
                .WhereIF(search_In.ServiceCycleStartBegin != null, (apply, service) => SqlFunc.ToDateShort(service.ServiceCycleStart == null ? apply.ServiceCycleStart : service.ServiceCycleStart) >= SqlFunc.ToDateShort(search_In.ServiceCycleStartBegin))//按服务开通时间筛选-开始
                .WhereIF(search_In.ServiceCycleStartOver != null, (apply, service) => SqlFunc.ToDateShort(service.ServiceCycleStart == null ? apply.ServiceCycleStart : service.ServiceCycleStart) <= SqlFunc.ToDateShort(search_In.ServiceCycleStartOver))//按服务开通时间筛选-结束
                .WhereIF(search_In.CreateDateStart != null, apply => SqlFunc.ToDateShort(search_In.CreateDateStart.Value) <= SqlFunc.ToDateShort(apply.CreateDate.Value))//按创建时间筛选-开始
                .WhereIF(search_In.CreateDateEnd != null, apply => SqlFunc.ToDateShort(search_In.CreateDateEnd.Value) >= SqlFunc.ToDateShort(apply.CreateDate.Value))//按创建时间筛选-结束
                .WhereIF(search_In.ApprovalStartDate != null, (apply, service) =>
                    SqlFunc.ToDateShort(search_In.ApprovalStartDate.Value) <= SqlFunc.ToDateShort(service.ReviewerDate.Value) && approvalStates.Contains(service.State.Value))//按服务复核通过时间筛选-开始
                .WhereIF(search_In.ApprovalEndDate != null, (apply, service) =>
                    SqlFunc.ToDateShort(search_In.ApprovalEndDate.Value) >= SqlFunc.ToDateShort(service.ReviewerDate.Value) && approvalStates.Contains(service.State.Value))//按服务复核通过时间筛选-结束
                .WhereIF(search_In.EnumQueryListType == EnumQueryListType.Today, apply => apply.ApplicantDate != null && SqlFunc.DateIsSame(apply.ApplicantDate.Value, dtDay))
                .WhereIF(search_In.EnumQueryListType == EnumQueryListType.Week, apply => apply.ApplicantDate != null && SqlFunc.Between(apply.ApplicantDate.Value, weekStart, weekEnd))
                //.WhereIF(search_In.AppleId.IsNotNullOrEmpty(), apply => apply.Id == search_In.AppleId)//2025年3月21日 修改 增加applid(跳转用)
                .OrderByPropertyName(search_In.SortField, search_In.IsDESC ? OrderByType.Desc : OrderByType.Asc)//有排序列根据排序列
                .OrderByIF(StringUtil.IsNullOrEmpty(search_In.SortField), (apply, service) => SqlFunc.IIF((apply.State == EnumProcessStatus.Submit || (service.State == EnumContractServiceState.VALID && (apply.SendAccount == null || apply.SendAccount == false))) && apply.IsUrgent == true, 1, 0), OrderByType.Desc)//加急、未开通的优先
                .OrderByIF(StringUtil.IsNullOrEmpty(search_In.SortField), apply => SqlFunc.IIF(apply.State == EnumProcessStatus.Submit && SqlFunc.ToDateShort(apply.ServiceCycleStart) <= SqlFunc.ToDateShort(DateTime.Today), 1, 0), OrderByType.Desc)//申请的服务开始时间临近或过了的优先
                .OrderByIF(StringUtil.IsNullOrEmpty(search_In.SortField), "ApplicantDate desc")//指定排序列
                .OrderByDescending(apply => apply.Id)
                .Select((apply, service, contract, company, applicant, reviewer, view) => new SearchWitsApplList_Out()
                {
                    Id = apply.Id,
                    ContractNum = contract.ContractNum,
                    FirstPartyName = company.CompanyName,
                    ContractName = contract.ContractName,
                    ContractType = contract.ContractType,
                    ApplicantId = apply.ApplicantId,
                    ApplicantName = applicant.UserWithOrgFullName,
                    ApplicantDate = apply.ApplicantDate == null ? "" : apply.ApplicantDate.Value.ToString("yyyy-MM-dd HH:mm"),
                    //需要mapper中写 ProductName = product.ProductName + ((apply.RetailCountry == (int)EnumGtisRetailCountry.Add && productInfo.ProductType != (int)EnumProductType.Vip) ? "（定制零售国家）" : ""),
                    ServiceCycle = (service.ServiceCycleStart == null || service.ServiceCycleEnd == null) ?
                    apply.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + apply.ServiceCycleEnd.Value.ToString("yyyy-MM-dd") :
                    service.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + service.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                    ReviewerId = apply.ReviewerId,
                    ReviewerName = reviewer.Name,
                    ReviewerDate = apply.ReviewerDate,
                    ServiceId = service.Id,
                    ServiceType = apply.ProcessingType,
                    IsInvalid = apply.IsInvalid == EnumIsInvalid.Effective,
                    ApplState = apply.State.Value,
                    ServiceState = SqlFunc.IsNull(service.State, EnumContractServiceState.TO_BE_OPENED),
                    ServiceCycleStart = SqlFunc.IsNull(service.ServiceCycleStart, apply.ServiceCycleStart),
                    ServiceCycleEnd = SqlFunc.IsNull(service.ServiceCycleEnd, apply.ServiceCycleEnd),
                    //IsFree = apply.IsFree,
                    SendAccount = apply.SendAccount.Value,
                    SendAccountTime = apply.SendAccountTime,
                    Remark = apply.Remark4List,
                    CustomerEmail = contract.Email,
                    CouponIds = apply.CounponDetailIds,
                    IsUrgent = apply.IsUrgent == true,
                    IsUrgentLable = false,
                    State = view.State
                }, true)
                .Mapper(it =>
                {
                    it.ContractTypeName = it.ContractType == null ? null : Dictionary.ContractType.First(e => e.Value == it.ContractType.ToString()).Name;
                    it.StateName = it.State.GetEnumDescription();
                    it.IsPendingToday = it.ApplState == EnumProcessStatus.Submit && SqlFunc.ToDateShort(it.ServiceCycleStart) <= SqlFunc.ToDateShort(DateTime.Today);
                    it.CouponCount = string.IsNullOrEmpty(it.CouponIds) ? 0 : it.CouponIds.Split(',').ToList().Count;
                    it.IsUrgentLable = it.IsUrgent && (it.ApplState == EnumProcessStatus.Submit || (it.ServiceState == EnumContractServiceState.VALID && !it.SendAccount));

                    var conproList = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoBySeiresId(it.ContractProductInfoSeriesId);
                    if (conproList.Any(e => e.ProductType == EnumProductType.Gtis))
                        it.ProductName = conproList.Find(e => e.ProductType == EnumProductType.Gtis).ProductName;
                    if (conproList.Any(e => e.ProductType == EnumProductType.Vip) && !string.IsNullOrEmpty(it.ProductName))
                        it.ProductName += "（零售定制国家）";
                    else if (conproList.Any(e => e.ProductType == EnumProductType.Vip) && string.IsNullOrEmpty(it.ProductName))
                        it.ProductName = conproList.Find(e => e.ProductType == EnumProductType.Vip).ProductName;
                    if (conproList.Any(e => e.ProductType == EnumProductType.Global))
                        it.ProductName = string.IsNullOrEmpty(it.ProductName) ? "环球搜" : it.ProductName + "、环球搜";
                    if (conproList.Any(e => e.ProductType == EnumProductType.GlobalWitsSchool))
                        it.ProductName = string.IsNullOrEmpty(it.ProductName) ? "慧思学院" : it.ProductName + "、慧思学院";
                    if (conproList.Any(e => e.ProductType == EnumProductType.SalesWits))
                        it.ProductName = string.IsNullOrEmpty(it.ProductName) ? "SalesWits" : it.ProductName + "、SalesWits";
                })
                .ToPageList(search_In.PageNumber, search_In.PageSize, ref total);
            //LogUtil.AddLog("searchend:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
            var svCodes = retList.FindAll(c => StringUtil.IsNotNullOrEmpty(c.ContractNum)).Select(c => c.ContractNum).Distinct().ToArray();
            Dictionary<string, string> accStates = new Dictionary<string, string>();
            if (svCodes.Length > 0)
            {
                accStates = BLL_GtisOpe.Instance.GetUserState(svCodes).Result;
                //LogUtil.AddLog("GetUserStateend:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
            }
            retList.ForEach(d =>
            {
                if (d.State == EnumContractServiceOpenState.Void)
                {
                    d.AccountStatusName = "";
                    d.AccountStatus = (int)EnumGtisAccountStatus.Exception;
                    d.AccountNum = 0;
                }
                else if (d.State != EnumContractServiceOpenState.Open && d.State != EnumContractServiceOpenState.NearlyEnd)
                {
                    d.AccountStatusName = "";
                    d.AccountStatus = (int)EnumGtisAccountStatus.INPROCESS;
                    d.AccountNum = 0;
                }
                else
                {
                    var gtisFound = (d.ContractNum != null && accStates != null && accStates.ContainsKey(d.ContractNum));
                    d.AccountStatusName = gtisFound ? accStates[d.ContractNum] : "";
                    d.AccountStatus = (int)TransGtisServiceAccountStates(d.AccountStatusName);
                    #region AccountNum统一改为0  注释
                    //if (gtisFound)
                    //{
                    //    LogUtil.AddLog("GetContractServiceInfoGtisUserByApplId start:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
                    //    //对于开通过的账户（在gtis系统有过记录） （******** 不刷新具体账号状态信息了）
                    //    var accounts = DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.
                    //    LogUtil.AddLog("GetContractServiceInfoGtisUserByApplId end:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
                    //    d.AccountNum = accounts.Count;
                    //}
                    //else
                    //{
                    //    d.AccountNum = 0;
                    //}
                    #endregion
                    d.AccountNum = 0;
                    #region 是否可以赠送 注释
                    ////LogUtil.AddLog("checkfree start:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
                    //if (d.IsFree == true || d.IsInvalid != (int)EnumIsInvalid.Effective || d.State != (int)EnumContractServiceOpenState.Open)
                    //{
                    //    //gtis赠送过了，所有都不能再送了
                    //    d.GTISFreeAble = false;
                    //    d.CollegeFreeAble = false;
                    //    d.GlobalFreeAble = false;
                    //}
                    //else
                    //{
                    //    d.GTISFreeAble = true;
                    //    if (DbOpe_crm_contract_productserviceinfo_college_appl.Instance.CheckContractHasCollegeProduct(d.ContractId))
                    //    {
                    //        d.CollegeFreeAble = false;
                    //    }
                    //    else
                    //    {
                    //        if (DbOpe_crm_contract_productserviceinfo_college_appl.Instance.CheckContractHasPresentedCollegeProduct(d.ContractId))
                    //        {
                    //            //慧思学院赠送过了，所有都不能再送了
                    //            d.GTISFreeAble = false;
                    //            d.CollegeFreeAble = false;
                    //            d.GlobalFreeAble = false;
                    //        }
                    //        else
                    //        {
                    //            d.CollegeFreeAble = true;
                    //        }
                    //    }
                    //    if (DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckContractHasGlobalSearchProduct(d.ContractId))
                    //    {
                    //        d.GlobalFreeAble = false;
                    //    }
                    //    else
                    //    {
                    //        if (DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckContractHasPresentedGlobalSearchProduct(d.ContractId))
                    //        {
                    //            //慧思学院赠送过了，所有都不能再送了
                    //            d.GTISFreeAble = false;
                    //            d.CollegeFreeAble = false;
                    //            d.GlobalFreeAble = false;
                    //        }
                    //        else
                    //        {
                    //            d.GlobalFreeAble = true;
                    //        }
                    //    }
                    //}
                    ////LogUtil.AddLog("checkfree end:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
                    #endregion

                }
            });
            //LogUtil.AddLog("end:" + DateTime.Now.ToString("mm-dd HH:mm:ss:fff"));
            return retList;
        }

        /// <summary>
        /// gtis系统返回的账号状态翻译成EnumGtisAccountStatus
        /// </summary>
        /// <param name="gtisStatesName"></param>
        /// <returns></returns>
        public EnumGtisAccountStatus TransGtisServiceAccountStates(string gtisStatesName)
        {
            switch (gtisStatesName)
            {
                case "正常":
                    return EnumGtisAccountStatus.Ok;
                case "异常":
                    return EnumGtisAccountStatus.Exception;
                case "停用":
                    return EnumGtisAccountStatus.Stop;
                case "过期":
                    return EnumGtisAccountStatus.Expired;
                default:
                    return EnumGtisAccountStatus.INPROCESS;
            }
        }

        /// <summary>
        /// 根据申请Id获取慧思产品申请基本信息
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public GetWitsApplyInfo4Audit_Out_ApplyInfo GetWitsApplyBasicInfo(string applId)
        {
            return Queryable
               .LeftJoin<Db_v_serviceinfo_wits_status>((e, f) => e.Id == f.Id)
               .LeftJoin<Db_v_userwithorg>((e, f, g) => e.ApplicantId == g.Id)
               .Where(e => e.Id == applId)
               .Where(e => e.Deleted == false)
               .Select((e, f, g) => new GetWitsApplyInfo4Audit_Out_ApplyInfo_Replenish
               {
                   Id = e.Id.SelectAll(),
                   State = f.State,
                   ApplicantName = g.UserWithOrgFullName,
                   ApplyRemark = e.Remark,
                   OriWitsServeId = e.OriWitsServiceId
               })
               .Mapper(item =>
               {
                   //Gtis申请内容
                   if (item.IsGtisApply)
                   {
                       item.GtisInfo = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetApplyInfoByWitsApplyId(item.Id);
                       if (item.GtisInfo.ProductType == EnumProductType.Vip)
                           item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.Vip);
                       else
                           item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.Gtis);
                   }
                   //环球搜申请内容
                   if (item.IsGlobalSearchApply)
                   {
                       item.GlobalSearchInfo = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetApplyInfoByWitsApplyId(item.Id);
                       item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.GlobalSearch);
                   }
                   //慧思学院申请内容
                   if (item.IsCollegeApply)
                   {
                       item.CollegeInfo = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetApplyInfoByWitsApplyId(item.Id);
                       item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.College);
                   }
                   //SalesWits申请内容
                   if (item.IsSalesWitsApply)
                   {
                       item.SalesWitsInfo = DbOpe_crm_contract_productserviceinfo_saleswits_appl.Instance.GetApplyInfoByWitsApplyId(item.Id);
                       item.GtisAccountPermissionList.Add(EnumGtisSeriesPermission.SalesWits);
                   }
                   //账号信息
                   var userList = DbOpe_crm_contract_productserviceinfo_wits_appl_user.Instance.GetUserListByWitsId(item.Id);
                   item.UserList = userList.Where(e => !string.IsNullOrEmpty(e.AccountNumber)).OrderByDescending(e => e.AccountType).OrderBy(e => e.AccountNumber).Concat(userList.Where(e => string.IsNullOrEmpty(e.AccountNumber))).ToList();
                   //续约服务券使用数量
                   if (!string.IsNullOrEmpty(item.CounponDetailIds))
                       item.CouponCounts = item.CounponDetailIds.Split(',').ToList().Count;
                   if (!string.IsNullOrEmpty(item.OriWitsServeId))
                       item.OriContractNum = DbOpe_crm_contract_serviceinfo_wits.Instance.QueryByPrimaryKey(item.OriWitsServeId).ContractNum;
               })
               .First()
               .MappingTo<GetWitsApplyInfo4Audit_Out_ApplyInfo>();
        }

    }
}
