#!/bin/bash

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
  echo "请使用sudo或root权限运行此脚本"
  exit 1
fi

echo "=== 开始安装CRM2 API服务 ==="

# 复制服务文件到systemd目录
cp crm-api.service /etc/systemd/system/

# 确保日志目录存在
mkdir -p /hqhs_data/00.API/01.CRM/log
chmod 755 /hqhs_data/00.API/01.CRM/log

# 重新加载systemd配置
systemctl daemon-reload

# 启用服务（开机自启）
systemctl enable crm-api.service
echo "已设置开机自启动"

# 启动服务
systemctl start crm-api.service
echo "服务已启动"

# 检查服务状态
echo "=== 服务状态 ==="
systemctl status crm-api.service

echo ""
echo "=== 安装完成 ==="
echo "您可以使用以下命令管理服务："
echo "  启动服务: sudo systemctl start crm-api.service"
echo "  停止服务: sudo systemctl stop crm-api.service"
echo "  重启服务: sudo systemctl restart crm-api.service"
echo "  查看状态: sudo systemctl status crm-api.service"
echo "  查看日志: sudo journalctl -u crm-api.service" 