using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.IO;
using System.Linq;
using CRM2_API.Common.AppSetting;
using Spire.Doc;
using CRM2_API.DAL.DbCommon;
using CRM2_API.BLL.Common;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 文件处理部分
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        /// <summary>
        /// 获取发票文件流
        /// </summary>
        /// <param name="invoiceApplId">发票申请ID</param>
        /// <param name="isProformaInvoice">是否显式指定为形式发票</param>
        /// <returns>文件流</returns>
        public Stream DownloadInvoice(string invoiceApplId, bool isProformaInvoice = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(invoiceApplId))
                {
                    throw new ApiException("发票申请ID不能为空");
                }
                    
                // 获取发票信息
                var invoiceappl = DbOpe_crm_invoice_application.Instance.GetData(i => i.Id == invoiceApplId && i.Deleted != true);
                if (invoiceappl == null)
                {
                    throw new ApiException("发票申请不存在或已被删除");
                }
                    
                // 验证合同权限
                var contract = DbOpe_crm_contract.Instance.GetContractById(invoiceappl.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("没有数据权限或合同数据不存在");
                }

                
                // 判断发票类型，只有形式发票需要生成文件，其他发票类型从附件中获取
                if (isProformaInvoice || invoiceappl.InvoiceType == (int)EnumInvoiceType.ProformaTicket)
                {
                    var proformaInvoice = DbOpe_crm_proforma_invoice.Instance.GetData(i => i.ApplicationId == invoiceApplId && i.Deleted != true);
                    if (proformaInvoice == null)
                    {
                        throw new ApiException("形式发票不存在");
                    }
                    // 形式发票，需要动态生成
                    return GenerateProformaInvoice(proformaInvoice, contract, invoiceappl);
                }
                else
                {
                    // 判断用户是否为后台管理人员
                    bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                    if(!isBackendManager){
                        // 不是后台管理人员的话，不能看审核中的发票
                        if(invoiceappl.AuditStatus != (int)EnumInvoiceApplicationStatus.Completed){
                            throw new ApiException("没有权限查看该发票信息");
                        }
                    }
                    // 其他类型发票，从附件中获取
                    // 使用DbContext直接查询
                    var db = DbContext.Crm2Db;
                    var attachments = db.Queryable<Db_crm_contract_invoice_attachment>()
                        .Where(a => a.ContractInvoiceId == invoiceappl.Id && a.Deleted != true)
                        .ToList();
                        
                    if (attachments == null || !attachments.Any())
                    {
                        throw new ApiException("未找到发票文件");
                    }
                    
                    // 获取第一个附件
                    var attachment = attachments.First();
                    string filePath = Path.Combine(Directory.GetCurrentDirectory(), attachment.FilePath, attachment.FileName);
                    
                    if (!File.Exists(filePath))
                    {
                        throw new ApiException("发票文件不存在");
                    }
                    
                    // 创建文件流返回
                    FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                    return fileStream;
                }
            }
            catch (ApiException)
            {
                throw; // 直接重新抛出业务异常
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"下载发票文件时发生错误: {ex.Message}, {ex.StackTrace}");
                throw new ApiException("下载发票文件失败，请联系管理员");
            }
        }

        /// <summary>
        /// 获取发票附件文件流
        /// </summary>
        /// <param name="id">附件ID</param>
        /// <param name="fileType">文件类型</param>
        /// <returns>文件流</returns>
        public Stream DownloadInvoiceAttachment(string id, string? fileType)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(id))
                {
                    throw new ApiException("附件ID不能为空");
                }
                
                // 获取附件信息
                var db = DbContext.Crm2Db;
                var attachment = db.Queryable<Db_crm_contract_invoice_attachment>()
                    .Where(a => a.Id == id && a.Deleted != true)
                    .First();
                    
                if (attachment == null)
                {
                    throw new ApiException("附件不存在或已被删除");
                }
                
                // 验证发票权限
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == attachment.ContractInvoiceId && i.Deleted != true);
                if (invoice == null)
                {
                    throw new ApiException("附件对应的发票不存在");
                }
                
                // 验证合同权限
                var contract = DbOpe_crm_contract.Instance.GetContractById(invoice.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("没有数据权限或合同数据不存在");
                }
                
                // 构建文件路径
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), attachment.FilePath, attachment.FileName);
                
                if (!File.Exists(filePath))
                {
                    throw new ApiException("附件文件不存在");
                }
                
                // 创建文件流返回
                FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                return fileStream;
            }
            catch (ApiException)
            {
                throw; // 直接重新抛出业务异常
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"下载发票附件时发生错误: {ex.Message}, {ex.StackTrace}");
                throw new ApiException("下载发票附件失败，请联系管理员");
            }
        }

        /// <summary>
        /// 生成形式发票
        /// </summary>
        /// <param name="invoice">发票信息</param>
        /// <param name="contract">合同信息</param>
        /// <param name="invoiceApp">发票申请信息</param>
        /// <returns>文件流</returns>
        private Stream GenerateProformaInvoice(Db_crm_proforma_invoice invoice, Db_crm_contract contract, Db_crm_invoice_application invoiceApp)
        {
            try
            {
                // 准备数据
                decimal contractAmount = 0;
                if (contract.Currency == (int)EnumCurrency.CNY)
                {
                    contractAmount = contract.ContractAmount ?? 0;
                }
                else
                {
                    contractAmount = contract.FCContractAmount ?? 0;
                }
                
                // 计算已开票金额和余额
                decimal invoicedAmountTotal = contractAmount;
                decimal amountApplied = DbOpe_crm_invoice.Instance.GetInvoicedAmountByContractAndDate(
                    contract.Id, invoice.InvoiceDate ?? DateTime.Now, invoice.Id);
                // 获取应付金额，如果未设置则使用开票金额
                decimal balanceDue = invoice.BalanceDue !=null ? decimal.Parse(invoice.BalanceDue) : invoice.Amount;
                
                // 格式化开票日期
                string expectedInvoicingTime = "";
                if (invoiceApp.ExpectedInvoicingDate.HasValue)
                {
                    expectedInvoicingTime = invoiceApp.ExpectedInvoicingDate.Value.Year.ToString() + "-" + 
                                        invoiceApp.ExpectedInvoicingDate.Value.Month.ToString("00") + "-" + 
                                        invoiceApp.ExpectedInvoicingDate.Value.Day.ToString("00");
                }
                else
                {
                    expectedInvoicingTime = invoice.InvoiceDate?.Year.ToString() + "-" + 
                                        invoice.InvoiceDate?.Month.ToString("00") + "-" + 
                                        invoice.InvoiceDate?.Day.ToString("00");
                }
                
                // 准备模板字段
                List<TemplateField> templateFields = new List<TemplateField>();
                templateFields.Add(new TemplateField { FieldName = "${BillingHeader}", FieldValue = invoiceApp.BillingHeader });
                templateFields.Add(new TemplateField { FieldName = "${InvoicingDate}", FieldValue = expectedInvoicingTime });
                templateFields.Add(new TemplateField { FieldName = "${InvoicedAmount}", FieldValue = invoice.Amount.ToString() });
                templateFields.Add(new TemplateField { FieldName = "${FormalInvoiceNumber}", FieldValue = invoice.InvoiceNumber });
                

                
                templateFields.Add(new TemplateField { FieldName = "${SigningDate}", FieldValue = expectedInvoicingTime });
                templateFields.Add(new TemplateField { FieldName = "${ContractAmount}", FieldValue = contractAmount.ToString() });
                templateFields.Add(new TemplateField { FieldName = "${InvoicedAmountTotal}", FieldValue = invoicedAmountTotal.ToString() });
                templateFields.Add(new TemplateField { FieldName = "${AmountApplied}", FieldValue = amountApplied.ToString() });
                templateFields.Add(new TemplateField { FieldName = "${BalanceDue}", FieldValue = balanceDue.ToString() });
                
                // 获取付款信息
                var paymentInfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByContractId(contract.Id);
                
                // 处理地址信息
                string addressAll = GetFormattedAddress(contract, paymentInfo);
                
                // 处理联系人信息
                string contacts = contract.Contacts ?? "";
                string email = contract.Email ?? "";
                string fax = contract.Fax ?? "";
                string postalCode = contract.PostalCode ?? "";
                string contactWay = contract.ContactWay ?? "";
                string telephone = string.IsNullOrEmpty(contract.Telephone) ? contactWay : contract.Telephone;
                
                // 如果是代付款，使用代付款方的联系信息
                if (paymentInfo != null && paymentInfo.IsBehalfPayment == true)
                {
                    contacts = paymentInfo.PaymentContacts ?? "";
                    email = paymentInfo.PaymentEmail ?? "";
                    fax = paymentInfo.PaymentFax ?? "";
                    postalCode = paymentInfo.PaymentPostalCode ?? "";
                    contactWay = paymentInfo.PaymentContactWay ?? "";
                    telephone = string.IsNullOrEmpty(paymentInfo.PaymentTelephone) ? contactWay : paymentInfo.PaymentTelephone;
                }
                
                // 添加联系信息字段
                templateFields.Add(new TemplateField { FieldName = "${Contacts}", FieldValue = contacts });
                templateFields.Add(new TemplateField { FieldName = "${Address}", FieldValue = addressAll });
                templateFields.Add(new TemplateField { FieldName = "${Telephone}", FieldValue = telephone });
                templateFields.Add(new TemplateField { FieldName = "${Fax}", FieldValue = fax });
                templateFields.Add(new TemplateField { FieldName = "${PostalCode}", FieldValue = postalCode });
                templateFields.Add(new TemplateField { FieldName = "${Email}", FieldValue = email });
                templateFields.Add(new TemplateField { FieldName = "${ContactWay}", FieldValue = contactWay });
                
                // 根据币种选择模板
                string currency = "";
                if (contract.Currency == (int)EnumCurrency.USD)
                {
                    currency = "Usd";
                }
                else if (contract.Currency == (int)EnumCurrency.EUR)
                {
                    currency = "Eur";
                }
                else if (contract.Currency == (int)EnumCurrency.CNY)
                {
                    currency = "Cny";
                }
                
                // 获取模板路径
                var collectingCompany = DbOpe_crm_collectingcompany.Instance.GetDataById(invoice.BillingCompany);
                if (collectingCompany == null)
                {
                    throw new ApiException("开票公司信息不存在");
                }
                
                string templatePath = collectingCompany.TemplatePath;
                
                // 构建模板文件路径
                templatePath = templatePath + "/NoAccount2023" + currency + ".docx";
                
                // 加载模板
                var filePath = Path.Combine(Directory.GetCurrentDirectory(), "InvoiceTemplate/" + templatePath);
                
                if (!File.Exists(filePath))
                {
                    throw new ApiException("当前乙方公司不存在合同币种对应的形式发票模板");
                }
                
                // 生成发票文档
                using (Stream stream = File.OpenRead(filePath))
                {
                    // 使用模板和字段生成文档
                    Stream result = SpireDocCreateDoc.SpireDocCreateDocStream(stream, templateFields);
                    result.Seek(0, SeekOrigin.Begin);
                    
                    // 转换为PDF格式
                    Document document = new Document();
                    document.LoadFromStream(result, FileFormat.Docx);
                    
                    // 处理Unicode字符字体问题（支持土耳其语、阿拉伯语、中文等）
                    UnicodeTextProcessor.ProcessDocumentForUnicodeText(document);
                    
                    Stream streamPDF = new MemoryStream();
                    document.SaveToStream(streamPDF, FileFormat.PDF);
                    
                    streamPDF.Seek(0, SeekOrigin.Begin);
                    return streamPDF;
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"生成形式发票时发生错误: {ex.Message}, {ex.StackTrace}");
                throw new ApiException("生成形式发票失败");
            }
        }
        
        /// <summary>
        /// 获取格式化的地址信息
        /// </summary>
        /// <param name="contract">合同信息</param>
        /// <param name="paymentInfo">付款信息</param>
        /// <returns>格式化后的地址</returns>
        private string GetFormattedAddress(Db_crm_contract contract, Db_crm_contract_paymentinfo paymentInfo)
        {
            string addressAll = "";
            
            // 使用代付款地址
            if (paymentInfo != null && paymentInfo.IsBehalfPayment == true)
            {
                string countryName = "";
                var country = DbOpe_sys_country.Instance.GetDataById(paymentInfo.PaymentCountry.ToString());
                if (country != null)
                {
                    // 外国国家后面加空格
                    countryName = country.NameEN + " ";
                }
                
                string address = paymentInfo.PaymentAddress ?? "";
                
                // 中国地址特殊处理
                if (paymentInfo.PaymentCountry == 337) // 337为中国
                {
                    countryName = "";
                    string provinceName = "";
                    var province = DbOpe_sys_province.Instance.GetDataById(paymentInfo.PaymentProvince.ToString());
                    if (province != null)
                    {
                        provinceName = province.Name;
                    }
                    
                    string cityName = "";
                    var city = DbOpe_sys_city.Instance.GetDataById(paymentInfo.PaymentCity.ToString());
                    if (city != null)
                    {
                        cityName = city.Name;
                    }
                    
                    // 如果省名和市名相同，只显示一个
                    if (provinceName == cityName)
                    {
                        cityName = "";
                    }
                    
                    countryName = countryName + provinceName + cityName;
                }
                
                addressAll = countryName + address;
            }
            // 使用合同地址
            else
            {
                string countryName = "";
                var country = DbOpe_sys_country.Instance.GetDataById(contract.Country.ToString());
                if (country != null)
                {
                    // 外国国家后面加空格
                    countryName = country.NameEN + " ";
                }
                
                string address = contract.Address ?? "";
                
                // 中国地址特殊处理
                if (contract.Country == 337) // 337为中国
                {
                    countryName = "";
                    string provinceName = "";
                    var province = DbOpe_sys_province.Instance.GetDataById(contract.Province.ToString());
                    if (province != null)
                    {
                        provinceName = province.Name;
                    }
                    
                    string cityName = "";
                    var city = DbOpe_sys_city.Instance.GetDataById(contract.City.ToString());
                    if (city != null)
                    {
                        cityName = city.Name;
                    }
                    
                    // 如果省名和市名相同，只显示一个
                    if (provinceName == cityName)
                    {
                        cityName = "";
                    }
                    
                    countryName = countryName + provinceName + cityName;
                }
                
                addressAll = countryName + address;
            }
            
            return addressAll;
        }

        /// <summary>
        /// 获取发票附件列表
        /// </summary>
        /// <param name="invoiceApplId">发票申请ID</param>
        /// <returns>附件列表</returns>
        public List<BM_FileInfo> GetInvoiceAttachments(string invoiceApplId)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(invoiceApplId))
                {
                    throw new ApiException("发票申请ID不能为空");
                }
                var invoiceappl = DbOpe_crm_invoice_application.Instance.GetData(i => i.Id == invoiceApplId && i.Deleted != true);
                if (invoiceappl == null)
                {
                    throw new ApiException("发票申请信息不存在");
                }
                // 验证合同权限
                var contract = DbOpe_crm_contract.Instance.GetContractById(invoiceappl.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("没有数据权限或合同数据不存在");
                }
                List<BM_FileInfo> result = new List<BM_FileInfo>();
                if (invoiceappl.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
                {
                    var proformaTicket = DbOpe_crm_proforma_invoice.Instance.GetData(i => i.ApplicationId == invoiceApplId && i.Deleted != true);
                    if (proformaTicket == null)
                    {
                        throw new ApiException("形式发票不存在");
                    }
                    result.Add(new BM_FileInfo { Id = invoiceApplId, FileName = contract.ContractName, FilePath = AppSettings.DownLoadFilePath + "/ProformaTicket/" });
                }
                else{
                    // 判断用户是否为后台管理人员
                    bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                    // 获取附件列表
                    var db = DbContext.Crm2Db;
                    var attachments = db.Queryable<Db_crm_contract_invoice_attachment>()
                        .Where(a => a.ContractInvoiceId == invoiceApplId && a.Deleted != true)
                        .ToList();
                    // 添加附件信息
                    foreach (var item in attachments)
                    {
                        if(!isBackendManager){
                            // 不是后台管理人员的话，不能看审核中的发票
                            if(invoiceappl.AuditStatus != (int)EnumInvoiceApplicationStatus.Completed){
                                continue;
                            }
                        }
                        result.Add(new BM_FileInfo 
                        { 
                            Id = item.Id, 
                            FileName = item.FileName, 
                            FilePath = item.FilePath
                        });
                    }
                
                    
                }
                return result;
            }
            catch (ApiException)
            {
                throw; // 直接重新抛出业务异常
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取发票附件列表时发生错误: {ex.Message}, {ex.StackTrace}");
                throw new ApiException("获取发票附件列表失败");
            }
        }
    }
} 