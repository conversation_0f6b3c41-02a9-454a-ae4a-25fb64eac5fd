﻿using CRM2_API.Model.BusinessModel;

namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class UserInfo
        {
            const string USERINFO = "userinfo_";
            public static bool CheckUserInfo(string userId)
            {
                var key = USERINFO + userId;
                return RedisHelper.Exists(key);
            }
            public static UserBasicInfo? GetUserInfo(string userId)
            {
                var key = USERINFO + userId;
                if (RedisHelper.Exists(key))
                    return RedisHelper.Get<UserBasicInfo>(key);
                else
                    return null;
            }
            public static void SaveUser(UserBasicInfo user)
            {
                RedisHelper.Set(USERINFO + user.Id, user/*, TimeSpan.FromMinutes(60)*/);
            }

            public static void DelUserInfo(string userId)
            {
                var key = USERINFO + userId;
                RedisHelper.Del(key);
            }
        }
        
    }
}
