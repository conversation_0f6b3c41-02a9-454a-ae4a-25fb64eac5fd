﻿using CRM2_API.BLL;
using CRM2_API.BLL.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System.Collections;
using System.IO;
using System.Threading.Tasks;

namespace CRM2_API.Common.Filter
{
    [AttributeUsage(AttributeTargets.Method, Inherited = false, AllowMultiple = true)]
    public class CustomerViewAuthCheckAttribute : Attribute, IAsyncAuthorizationFilter
    {
        protected String ParamKey;
        protected bool Multiple;
        public CustomerViewAuthCheckAttribute(String paramKey = "CustomerId", bool temp = false, bool multiple = false)
        {
            this.ParamKey = paramKey;
            this.Multiple = multiple;
        }
        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            var request = context.HttpContext.Request;

            //开启多次读取body流
            request.EnableBuffering();

            //leaveOpen:true标识StreamReader释放时不会自动关闭流        　　
            using var sr = new StreamReader(request.Body, leaveOpen: true, encoding: Encoding.UTF8);

            var paramJson = await sr.ReadToEndAsync();

            //Action中可再次读取流
            request.Body.Seek(0, SeekOrigin.Begin);

            var paramName = this.ParamKey;
            var paramObj = JsonConvert.DeserializeObject<JObject>(paramJson);
            List<string> checkIds = new List<string>();
            try
            {
                if (this.Multiple)
                {
                    var array = (paramObj[paramName] as JArray);
                    foreach (var id in array)
                    {
                        checkIds.Add(id.ToString());
                    }
                }
                else
                {
                    checkIds.Add(paramObj[paramName].ToString());
                }
            }
            catch (Exception ex)
            {
                throw new ApiException("客户权限验证异常");
            }
            //从TokenModel中获取用户主键userId
            var userId = TokenModel.Instance.id;
#if DEBUG
            //userId = "9406d3a2-0801-4195-95c9-88edc3eb8853";
#endif
            #region 判断客户权限（公有池/用户私有池/流程相关用户）
            List<string> leftCheckIds = new List<string>();
            if (!DbOpe_crm_customer.Instance.CheckCustomerViewAuth(checkIds, userId, ref leftCheckIds))
            {
                if (!BLL_WorkFlow.Instance.IsHaveWorkflowPending(leftCheckIds))
                {
                    throw new ApiException("客户权限验证失败");
                }
            }
            #endregion

        }
    }
}

