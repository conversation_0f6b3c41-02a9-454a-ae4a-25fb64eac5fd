using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 测试数据 BLL 类 - 合同相关测试数据
    /// </summary>
    public partial class BLL_TestData
    {
        /// <summary>
        /// 创建测试合同
        /// </summary>
        /// <param name="customerId">客户ID</param>
        /// <returns>合同创建结果，包含ID和名称</returns>
        public CreationResult CreateTestContract(string customerId)
        {
            try
            {
                // 获取客户的信息
                var customerInfo = BLL_Customer.Instance.GetCustomerById(customerId);
                if (customerInfo == null)
                {
                    return null;
                }
                // 创建合同名称
                string contractName = customerInfo.CompanyName + "资讯合同";

                // 创建合同
                var contractData = new AddContract_In
                {
                    CustomerId = new Guid(customerId),
                    ContactInformation = 2,
                    ContractMethod = 2,
                    ContractPaperEntityId = "",
                    FirstParty = new Guid(customerInfo.Id),
                    ContractName = contractName,
                    Contacts = "张测试",
                    Job = "经理",
                    ContactWay = "18900000000",
                    Email = "<EMAIL>",
                    Telephone = "010-12345678",
                    Fax = "010-12345678",
                    Country = 337,
                    Province = 2,
                    City = 2,
                    Address = "测试地址",
                    PostalCode = "100000",
                    IsOverseasCustomer = true,
                    ContractType = 1,
                    SigningDate = DateTime.Now,
                    SecondParty = new Guid("f27399d1-ceac-11ed-bc7b-30d042e24322"),
                    RenewalContractNum = "",
                    SalesCountry = 255,
                    IsMerged = false,
                    SigningAgentName = "测试代理人",
                    SigningAgentPhone = "13800000000",
                    SigningAgentEmail = "<EMAIL>",
                    SigningAgentFax = "010-10000000",
                    CompanyAddressId = null,
                    PaymentInfo = new AddPaymentInfo_In
                    {
                        IsReceipt = 2,
                        PaymentType = 3,
                        PlannedArrivalDate = null,
                        PlannedArrivalAmount = null,
                        PaymentCompany = new Guid(customerInfo.Id),
                        IsBehalfPayment = false,
                        CollectingCompany = new Guid("f27399d1-ceac-11ed-bc7b-30d042e24322"),
                        Currency = 1,
                        PaymentMethod = 1,
                        CashSourceRemarks = "",
                        IsStage = 0,
                        PaymentContacts = "",
                        PaymentJob = "",
                        PaymentContactWay = "",
                        PaymentEmail = "",
                        PaymentTelephone = "",
                        PaymentFax = "",
                        PaymentCountry = null,
                        PaymentProvince = null,
                        PaymentCity = null,
                        PaymentAddress = "",
                        PaymentPostalCode = "",
                        ArrivalDate = DateTime.Now,
                        ArrivalAmount = 10000,
                        BankPaymentAmount = null,
                        CashPaymentAmount = null
                    },
                    IsSpecial = 0,
                    SpecialDescription = "",
                    RelationshipDescription = "",
                    Remark = "",
                    ProductInfo = new List<AddProductInfo_In>
                    {
                        new AddProductInfo_In
                        {
                            ContractProductinfoPrice = 48800,
                            ParentProductId = Guid.Empty,
                            ProductId = new Guid("4a0ffc9a-6a44-4362-a76a-07492d0a81fa"),
                            ProductType = (EnumProductType)1,
                            SubAccountsNum = 1,
                            PrimaryAccountsNum = 1,
                            OpeningYears = 1,
                            OpeningMonths = 12,
                            FirstOpeningMonths = 12,
                            SubAccountsProductPrice = 0,
                            ContractProductinfoPriceTotal = 0
                        },
                        new AddProductInfo_In
                        {
                            ContractProductinfoPrice = 0,
                            ParentProductId = Guid.Empty,
                            ProductId = new Guid("a2f16437-e95f-4c3f-8e97-a22a1bbf0ab8"),
                            ProductType = (EnumProductType)5,
                            SubAccountsNum = 1,
                            OpeningMonths = 12,
                            OpeningYears = 1,
                            FirstOpeningMonths = 12,
                            PrimaryAccountsNum = 1,
                            SubAccountsProductPrice = 0,
                            ContractProductinfoPriceTotal = 0
                        }
                    },
                    Currency = 1,
                    ContractAmount = 48800,
                    FCContractAmount = null,
                    IsApplySuperSubAccount = false,
                    ContractStatus = (EnumContractStatus)1,
                    IsCustomContractDescription = 2
                };

                // 调用BLL创建合同
                string contractId = BLL_Contract.Instance.AddContract(contractData);

                if (string.IsNullOrEmpty(contractId))
                {
                    return null;
                }

                return new CreationResult
                {
                    Id = contractId,
                    Name = contractName
                };
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
} 