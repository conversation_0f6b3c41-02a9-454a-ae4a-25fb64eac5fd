﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///工作月报_预计签约客户表
    ///</summary>
    [SugarTable("crm_month_work_report_expected_customer")]
    public class Db_crm_month_work_report_expected_customer
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:工作周报表Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MonthWorkReportId { get; set; }

        /// <summary>
        /// Desc:客户表id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerId { get; set; }

        /// <summary>
        /// Desc:客户名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CompanyName { get; set; }

        /// <summary>
        /// Desc:信用代码
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreditCode { get; set; }

        /// <summary>
        /// Desc:客户级别
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? CustomerLevel { get; set; }

        /// <summary>
        /// Desc:联系人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Contacts { get; set; }

        /// <summary>
        /// Desc:联系方式
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ContactWay { get; set; }

        /// <summary>
        /// Desc:跟踪阶段
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? TrackingStage { get; set; }

        /// <summary>
        /// Desc:客户来源
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? CustomerSource { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
