﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Server.IIS.Core;
using static CRM2_API.Model.ControllersViewModel.VM_SalesRules;

namespace CRM2_API.BLL
{
    public class BLL_SalesRules : BaseBLL<BLL_SalesRules>
    {
        public SalesRulesResult AddSalesRules(AddOrUpdateSalesRules addsalesrules)
        {
            SalesRulesResult rulesResult = new SalesRulesResult();
            Db_sys_salesrules newdata = addsalesrules.MappingTo<Db_sys_salesrules>();
            bool IsHaveDB = DbOpe_sys_salesrules.Instance.CheckDBIsHave(newdata.CountryName, newdata.DBCode);
            if (IsHaveDB)
            {
                throw new ApiException("已经存在相同的数据库编码或数据库名");
            }

            string ctype = newdata.DBCode.Substring(0, 1);
            switch (ctype)
            {
                case "E": newdata.CType = 1; break;
                case "I": newdata.CType = 0; break;
                case "T": newdata.CType = 0; break;
                default:
                    throw new ApiException("数据库编码不符合规则，请检查");
                    break;
            }

            newdata.SID = DbOpe_sys_salesrules.Instance.GetGtisCountrySid(newdata.CountryName);

            DbOpe_sys_salesrules.Instance.InsertDataQueue(newdata);
            rulesResult.affectedRow = DbOpe_sys_salesrules.Instance.SaveQueues();
            rulesResult.state = 0;
            return rulesResult;

        }

        public SalesRulesResult UpdateSalesRules(AddOrUpdateSalesRules Updatesalesrules)
        {
            SalesRulesResult rulesResult = new SalesRulesResult();
            Db_sys_salesrules newdata = Updatesalesrules.MappingTo<Db_sys_salesrules>();
            DbOpe_sys_salesrules.Instance.UpdateRulesData(newdata);


            rulesResult.affectedRow = DbOpe_sys_salesrules.Instance.SaveQueues();
            rulesResult.state = 0;
            return rulesResult;


        }
    }
}
