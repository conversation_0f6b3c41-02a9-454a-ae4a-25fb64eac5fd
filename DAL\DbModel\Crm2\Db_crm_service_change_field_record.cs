using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    /// 服务变更字段记录表（所有服务类型通用）
    ///</summary>
    [SugarTable("crm_service_change_field_record")]
    public class Db_crm_service_change_field_record
    {
        /// <summary>
        /// Desc:主键ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:申请ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ApplyId { get; set; }

        /// <summary>
        /// Desc:服务类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ServiceType { get; set; }

        /// <summary>
        /// Desc:变更原因
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ChangeReasonEnum { get; set; }

        /// <summary>
        /// Desc:变更的字段键名
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string FieldKey { get; set; }

        /// <summary>
        /// Desc:变更的字段名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string FieldName { get; set; }

        /// <summary>
        /// Desc:是否是数组类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? IsArray { get; set; }

        /// <summary>
        /// Desc:原始值
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string OriginValue { get; set; }

        /// <summary>
        /// Desc:变更后的值
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ChangedValue { get; set; }

        /// <summary>
        /// Desc:是否删除
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// Desc:更新人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }
    }
}