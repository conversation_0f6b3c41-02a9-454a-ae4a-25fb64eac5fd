-- =====================================================
-- 更新 EnumGtisServiceChangeProject 枚举配置脚本
-- 修正 ChangeReasonEnum 字段值与系统枚举的对应关系
-- 创建时间: 2025-01-03
-- =====================================================

-- 1. 查看当前错误的配置
SELECT
    Id,
    ServiceType,
    ChangeReasonEnum,
    FieldKey,
    FieldName,
    CASE ChangeReasonEnum
        WHEN 1 THEN '尾款申请剩余服务'
        WHEN 2 THEN '变更服务内容'
        WHEN 3 THEN '个人服务天数延期'
        WHEN 4 THEN '优惠券延期'
        WHEN 5 THEN '其他'
        WHEN 6 THEN '开通SaleWits'
        WHEN 7 THEN 'SalesWits充值'
        WHEN 8 THEN 'SalesWits新增子账号'
        ELSE CONCAT('❌ 错误枚举值: ', ChangeReasonEnum)
    END AS ChangeReasonDescription
FROM crm_service_change_reason_field_config
WHERE ChangeReasonEnum IN (6, 7, 8, 9, 10, 11)  -- 查看SalesWits相关的配置
ORDER BY ChangeReasonEnum, ServiceType, FieldKey;

-- 2. 修正错误的枚举值
-- 将错误的枚举值 9 修正为 6 (开通SaleWits)
UPDATE crm_service_change_reason_field_config
SET ChangeReasonEnum = 6,
    UpdateDate = NOW(),
    UpdateUser = 'system_fix'
WHERE ChangeReasonEnum = 9;

-- 将错误的枚举值 10 修正为 7 (SalesWits充值)
UPDATE crm_service_change_reason_field_config
SET ChangeReasonEnum = 7,
    UpdateDate = NOW(),
    UpdateUser = 'system_fix'
WHERE ChangeReasonEnum = 10;

-- 将错误的枚举值 11 修正为 8 (SalesWits新增子账号)
UPDATE crm_service_change_reason_field_config
SET ChangeReasonEnum = 8,
    UpdateDate = NOW(),
    UpdateUser = 'system_fix'
WHERE ChangeReasonEnum = 11;

-- 3. 验证修正结果
SELECT
    Id,
    ServiceType,
    ChangeReasonEnum,
    FieldKey,
    FieldName,
    CASE ChangeReasonEnum
        WHEN 1 THEN '✅ 尾款申请剩余服务'
        WHEN 2 THEN '✅ 变更服务内容'
        WHEN 3 THEN '✅ 个人服务天数延期'
        WHEN 4 THEN '✅ 优惠券延期'
        WHEN 5 THEN '✅ 其他'
        WHEN 6 THEN '✅ 开通SaleWits'
        WHEN 7 THEN '✅ SalesWits充值'
        WHEN 8 THEN '✅ SalesWits新增子账号'
        ELSE CONCAT('❌ 仍有错误枚举值: ', ChangeReasonEnum)
    END AS ChangeReasonDescription,
    UpdateDate,
    UpdateUser
FROM crm_service_change_reason_field_config
WHERE ChangeReasonEnum IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11)
ORDER BY ChangeReasonEnum, ServiceType, FieldKey;

-- 4. 统计修正结果
SELECT
    '修正完成' AS Status,
    COUNT(*) AS TotalRecords,
    SUM(CASE WHEN ChangeReasonEnum BETWEEN 1 AND 8 THEN 1 ELSE 0 END) AS ValidRecords,
    SUM(CASE WHEN ChangeReasonEnum > 8 THEN 1 ELSE 0 END) AS InvalidRecords
FROM crm_service_change_reason_field_config;
