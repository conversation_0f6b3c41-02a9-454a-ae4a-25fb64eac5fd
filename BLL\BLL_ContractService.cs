﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.JWT;
using CRM2_API.Controllers;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using JiebaNet.Segmenter.Common;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SqlSugar;
using System.IO;
using System.Threading.Tasks;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.BLLModel.Enum.PrivateServiceEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;

namespace CRM2_API.BLL
{
    public partial class BLL_ContractService : BaseBLL<BLL_ContractService>
    {
        private string currentUser;
        public BLL_ContractService()
        {
            currentUser = UserId;
#if DEBUG
            //currentUser = "9406d3a2-0801-4195-95c9-88edc3eb8853";
            //currentUser = "02880719-b912-4df5-a5e0-acf0c010a505";
#endif
        }
    }
}
