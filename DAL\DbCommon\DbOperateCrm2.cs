﻿using CRM2_API.Common.JWT;
using SqlSugar;
using System.Reflection;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Xml.Linq;


namespace CRM2_API.DAL.DbCommon
{
    /// <summary>
    /// crm2数据库单表通用操作基础类
    /// </summary>
    /// <typeparam name="DbModel"></typeparam>
    /// <typeparam name="DbOperate">数据库操作类</typeparam>
    public class DbOperateCrm2<DbModel, DbOperate> : IDbOperate<DbModel, DbOperate> where DbModel : class, new() where DbOperate : class, new()
    {
        protected override SqlSugarScope Db => DbContext.Crm2Db;

        public string UserId => TokenModel.Instance.id;

        public void InsertData<T>(T addData)
        {
            var dt = new Dictionary<string, object>();
            dt.Add("Id", Guid.NewGuid().ToString());
            dt.Add("Deleted", false);
            dt.Add("CreateUser", TokenModel.Instance.id);
            dt.Add("CreateDate", DateTime.Now);
            Type type = addData.GetType();//获取类型
            Type typeDB = typeof(DbModel);//获取类型
            PropertyInfo[] properties = type.GetProperties();
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            for (int i = 0; i < properties.Length; i++)
            {
                bool isIgnore = false;
                object[] attributes = properties[i].GetCustomAttributes(true);
                foreach (object attribute in attributes)
                {
                    if (attribute.GetType().Name == "SugarColumn")
                    {
                        if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                        {
                            isIgnore = true;
                            break;
                        }
                    }
                }
                if (isIgnore == true)
                {
                    continue;
                }

                string name = properties[i].Name;
                object? value = properties[i].GetValue(addData);
                if (name != "Id" && name != "Deleted" && name != "CreateUser" && name != "CreateDate")
                {
                    if (typeDB.GetProperty(name) != null)
                    {
                        dt.Add(name, value);
                    }
                }
            }
            Db.Insertable(dt).AS(tableName).ExecuteCommand();
        }

        public Guid InsertDataReturnId<T>(T addData)
        {
            Guid id = Guid.NewGuid();
            var dt = new Dictionary<string, object>();
            dt.Add("Id", id.ToString());
            dt.Add("Deleted", false);
            dt.Add("CreateUser", TokenModel.Instance.id);
            dt.Add("CreateDate", DateTime.Now);
            Type type = addData.GetType();//获取类型
            Type typeDB = typeof(DbModel);//获取类型
            PropertyInfo[] properties = type.GetProperties();
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            for (int i = 0; i < properties.Length; i++)
            {
                bool isIgnore = false;
                object[] attributes = properties[i].GetCustomAttributes(true);
                foreach (object attribute in attributes)
                {
                    if (attribute.GetType().Name == "SugarColumn")
                    {
                        if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                        {
                            isIgnore = true;
                            break;
                        }
                    }
                }
                if (isIgnore == true)
                {
                    continue;
                }

                string name = properties[i].Name;
                object? value = properties[i].GetValue(addData);
                if (name != "Id" && name != "Deleted" && name != "CreateUser" && name != "CreateDate")
                {
                    if (typeDB.GetProperty(name) != null)
                    {
                        dt.Add(name, value);
                    }
                }
            }
            Db.Insertable(dt).AS(tableName).ExecuteCommand();
            return id;
        }

        public void InsertListData<T>(List<T> addData)
        {
            if (addData.Count > 0)
            {
                Type type = addData[0].GetType();//获取类型
                Type typeDB = typeof(DbModel);//获取类型
                PropertyInfo[] properties = type.GetProperties();
                string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
                List<Dictionary<string, object>> dtlist = new List<Dictionary<string, object>>();
                foreach (T item in addData)
                {
                    var dt = new Dictionary<string, object>();
                    dt.Add("Id", Guid.NewGuid().ToString());
                    dt.Add("Deleted", false);
                    dt.Add("CreateUser", TokenModel.Instance.id);
                    dt.Add("CreateDate", DateTime.Now);
                    for (int i = 0; i < properties.Length; i++)
                    {
                        bool isIgnore = false;
                        object[] attributes = properties[i].GetCustomAttributes(true);
                        foreach (object attribute in attributes)
                        {
                            if (attribute.GetType().Name == "SugarColumn")
                            {
                                if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                                {
                                    isIgnore = true;
                                    break;
                                }
                            }
                        }
                        if (isIgnore == true)
                        {
                            continue;
                        }

                        string name = properties[i].Name;
                        object? value = properties[i].GetValue(item);
                        if (name != "Id" && name != "Deleted" && name != "CreateUser" && name != "CreateDate")
                        {
                            if (typeDB.GetProperty(name) != null)
                            {
                                dt.Add(name, value);
                            }
                        }
                    }
                    dtlist.Add(dt);
                }
                Db.Insertable(dtlist).AS(tableName).ExecuteCommand();
            }
        }

        /// <summary>
        /// 批量插入list数据，只补充主键Id
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="addData"></param>
        public void InsertListDataOnlyId<T>(List<T> addData)
        {
            if (addData.Count > 0)
            {
                Type type = addData[0].GetType();//获取类型
                Type typeDB = typeof(DbModel);//获取类型
                PropertyInfo[] properties = type.GetProperties();
                string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
                List<Dictionary<string, object>> dtlist = new List<Dictionary<string, object>>();
                foreach (T item in addData)
                {
                    var dt = new Dictionary<string, object>();
                    dt.Add("Id", Guid.NewGuid().ToString());
                    for (int i = 0; i < properties.Length; i++)
                    {
                        bool isIgnore = false;
                        object[] attributes = properties[i].GetCustomAttributes(true);
                        foreach (object attribute in attributes)
                        {
                            if (attribute.GetType().Name == "SugarColumn")
                            {
                                if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                                {
                                    isIgnore = true;
                                    break;
                                }
                            }
                        }
                        if (isIgnore == true)
                        {
                            continue;
                        }

                        string name = properties[i].Name;
                        object? value = properties[i].GetValue(item);
                        if (name != "Id")
                        {
                            if (typeDB.GetProperty(name) != null)
                            {
                                dt.Add(name, value);
                            }
                        }
                    }
                    dtlist.Add(dt);
                }
                Db.Insertable(dtlist).AS(tableName).ExecuteCommand();
            }
        }

        public List<T> InsertListDataReturnData<T>(ref List<T> addData)
        {
            if (addData.Count > 0)
            {
                Type type = addData[0].GetType();//获取类型
                Type typeDB = typeof(DbModel);//获取类型
                PropertyInfo[] properties = type.GetProperties();
                string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
                List<Dictionary<string, object>> dtlist = new List<Dictionary<string, object>>();
                foreach (T item in addData)
                {
                    Guid id = Guid.NewGuid();
                    var dt = new Dictionary<string, object>();
                    dt.Add("Id", id.ToString());
                    dt.Add("Deleted", false);
                    dt.Add("CreateUser", TokenModel.Instance.id);
                    dt.Add("CreateDate", DateTime.Now);
                    for (int i = 0; i < properties.Length; i++)
                    {
                        bool isIgnore = false;
                        object[] attributes = properties[i].GetCustomAttributes(true);
                        foreach (object attribute in attributes)
                        {
                            if (attribute.GetType().Name == "SugarColumn")
                            {
                                if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                                {
                                    isIgnore = true;
                                    break;
                                }
                            }
                        }
                        if (isIgnore == true)
                        {
                            continue;
                        }

                        string name = properties[i].Name;
                        object? value = properties[i].GetValue(item);
                        if (name != "Id" && name != "Deleted" && name != "CreateUser" && name != "CreateDate")
                        {
                            if (typeDB.GetProperty(name) != null)
                            {
                                dt.Add(name, value);
                            }
                        }
                        else if (name == "Id")
                        {
                            properties[i].SetValue(item, id.ToString());
                        }
                    }
                    dtlist.Add(dt);
                }
                Db.Insertable(dtlist).AS(tableName).ExecuteCommand();
            }
            return addData;
        }


        public void InsertDataQueue<T>(T addData)
        {
            var dt = new Dictionary<string, object>();
            dt.Add("Id",Guid.NewGuid().ToString());
            dt.Add("Deleted", false);
            dt.Add("CreateUser", TokenModel.Instance.id);
            dt.Add("CreateDate", DateTime.Now);
            Type type = addData.GetType();//获取类型
            Type typeDB = typeof(DbModel);//获取类型
            PropertyInfo[] properties = type.GetProperties();
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            for (int i = 0; i < properties.Length; i++)
            {
                bool isIgnore = false;
                object[] attributes = properties[i].GetCustomAttributes(true);
                foreach (object attribute in attributes)
                {
                    if (attribute.GetType().Name == "SugarColumn")
                    {
                        if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                        {
                            isIgnore = true;
                            break;
                        }
                    }
                }
                if (isIgnore == true)
                {
                    continue;
                }

                string name = properties[i].Name;
                object? value = properties[i].GetValue(addData);
                if (name != "Id" && name != "Deleted" && name != "CreateUser" && name != "CreateDate")
                {
                    if (typeDB.GetProperty(name) != null)
                    {
                        dt.Add(name, value);
                    }
                }
            }
            Db.Insertable(dt).AS(tableName).AddQueue();
        }

        public string InsertDataQueueReturnId<T>(T addData)
        {
            var id = Guid.NewGuid().ToString();
            var dt = new Dictionary<string, object>();
            dt.Add("Id", id);
            dt.Add("Deleted", false);
            dt.Add("CreateUser", TokenModel.Instance.id);
            dt.Add("CreateDate", DateTime.Now);
            Type type = addData.GetType();//获取类型
            Type typeDB = typeof(DbModel);//获取类型
            PropertyInfo[] properties = type.GetProperties();
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            for (int i = 0; i < properties.Length; i++)
            {
                bool isIgnore = false;
                object[] attributes = properties[i].GetCustomAttributes(true);
                foreach (object attribute in attributes)
                {
                    if (attribute.GetType().Name == "SugarColumn")
                    {
                        if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                        {
                            isIgnore = true;
                            break;
                        }
                    }
                }
                if (isIgnore == true)
                {
                    continue;
                }

                string name = properties[i].Name;
                object? value = properties[i].GetValue(addData);
                if (name != "Id" && name != "Deleted" && name != "CreateUser" && name != "CreateDate")
                {
                    if (typeDB.GetProperty(name) != null)
                    {
                        dt.Add(name, value);
                    }
                }
            }
            Db.Insertable(dt).AS(tableName).AddQueue();
            return id;
        }

        public void InsertListDataQueue<T>(List<T> addData)
        {
            if (addData.Count > 0)
            {
                Type type = addData[0].GetType();//获取类型
                Type typeDB = typeof(DbModel);//获取类型
                PropertyInfo[] properties = type.GetProperties();
                string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
                List<Dictionary<string, object>> dtlist = new List<Dictionary<string, object>>();
                foreach (T item in addData)
                {
                    var dt = new Dictionary<string, object>();
                    dt.Add("Id", Guid.NewGuid().ToString());
                    dt.Add("Deleted", false);
                    dt.Add("CreateUser", TokenModel.Instance.id);
                    dt.Add("CreateDate", DateTime.Now);
                    for (int i = 0; i < properties.Length; i++)
                    {
                        bool isIgnore = false;
                        object[] attributes = properties[i].GetCustomAttributes(true);
                        foreach (object attribute in attributes)
                        {
                            if (attribute.GetType().Name == "SugarColumn")
                            {
                                if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                                {
                                    isIgnore = true;
                                    break;
                                }
                            }
                        }
                        if (isIgnore == true)
                        {
                            continue;
                        }

                        string name = properties[i].Name;
                        object? value = properties[i].GetValue(item);
                        if (name != "Id" && name != "Deleted" && name != "CreateUser" && name != "CreateDate")
                        {
                            if (typeDB.GetProperty(name) != null)
                            {
                                dt.Add(name, value);
                            }
                        }
                    }
                    dtlist.Add(dt);
                }
                Db.Insertable(dtlist).AS(tableName).AddQueue();
            }
        }

        public void UpdateData<T>(T updateData)
        {
            var dt = new Dictionary<string, object>();
            dt.Add("UpdateUser", TokenModel.Instance.id);
            dt.Add("UpdateDate", DateTime.Now);
            Type type = updateData.GetType();//获取类型
            Type typeDB = typeof(DbModel);//获取类型
            PropertyInfo[] properties = type.GetProperties();
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            for (int i = 0; i < properties.Length; i++)
            {
                bool isIgnore = false;
                object[] attributes = properties[i].GetCustomAttributes(true);
                foreach (object attribute in attributes)
                {
                    if (attribute.GetType().Name == "SugarColumn")
                    {
                        if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                        {
                            isIgnore = true;
                            break;
                        }
                    }
                }
                if (isIgnore == true)
                {
                    continue;
                }

                string name = properties[i].Name;
                object? value = properties[i].GetValue(updateData);
                if (name != "UpdateUser" && name != "UpdateDate")
                {
                    if (typeDB.GetProperty(name) != null)
                    {
                        dt.Add(name, value);
                    }
                }
            }
            Db.Updateable(dt).AS(tableName).IgnoreColumns("Deleted").IgnoreColumns("CreateUser").IgnoreColumns("CreateDate").WhereColumns("Id").ExecuteCommand();
        }

        public void UpdateDataQueue<T>(T updateData)
        {
            var dt = new Dictionary<string, object>();
            dt.Add("UpdateUser", TokenModel.Instance.id);
            dt.Add("UpdateDate", DateTime.Now);
            Type type = updateData.GetType();//获取类型
            Type typeDB = typeof(DbModel);//获取类型
            PropertyInfo[] properties = type.GetProperties();
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            for (int i = 0; i < properties.Length; i++)
            {
                bool isIgnore = false;
                object[] attributes = properties[i].GetCustomAttributes(true);
                foreach (object attribute in attributes)
                {
                    if (attribute.GetType().Name == "SugarColumn")
                    {
                        if (((SqlSugar.SugarColumn)attribute).IsIgnore == true)
                        {
                            isIgnore = true;
                            break;
                        }
                    }
                }
                if (isIgnore == true)
                {
                    continue;
                }

                string name = properties[i].Name;
                object? value = properties[i].GetValue(updateData);
                if (name != "UpdateUser" && name != "UpdateDate")
                {
                    if (typeDB.GetProperty(name) != null)
                    {
                        dt.Add(name, value);
                    }
                }
            }
            Db.Updateable(dt).AS(tableName).IgnoreColumns("Deleted").IgnoreColumns("CreateUser").IgnoreColumns("CreateDate").WhereColumns("Id").AddQueue();
        }

        public void UpdateData(Expression<Func<DbModel, DbModel>> predicate, string id)
        {
            Updateable
                .SetColumns(predicate)
                .SetColumns("UpdateUser", TokenModel.Instance.id)
                .SetColumns("UpdateDate", DateTime.Now)
                .IgnoreColumns("Id")
                .Where("Id = (@id)", new { id = id })
                .Where("Deleted = 0").ExecuteCommand();
        }

        public void UpdateData(Expression<Func<DbModel, DbModel>> predicate, Expression<Func<DbModel, bool>> where)
        {
            Updateable
                .SetColumns(predicate)
                .SetColumns("UpdateUser", TokenModel.Instance.id)
                .SetColumns("UpdateDate", DateTime.Now)
                .IgnoreColumns("Id")
                .Where(where)
                .Where("Deleted = 0").ExecuteCommand();
        }

        public void DeleteData(string Id)
        {
            Type typeDB = typeof(DbModel);//获取类型
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            var dt = new Dictionary<string, object>();
            dt.Add("UpdateUser", TokenModel.Instance.id);
            dt.Add("UpdateDate", DateTime.Now);
            dt.Add("Deleted", true);
            //dt.Add("Id", Id);
            Db.Updateable(dt).AS(tableName).Where("Id = (@id) ", new { id = Id }).Where("Deleted = 0").ExecuteCommand();
        }

        public void DeleteDataQueue(string Id)
        {
            Type typeDB = typeof(DbModel);//获取类型
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            var dt = new Dictionary<string, object>();
            dt.Add("UpdateUser", TokenModel.Instance.id);
            dt.Add("UpdateDate", DateTime.Now);
            dt.Add("Deleted", true);
            //dt.Add("Id", Id);
            Db.Updateable(dt).AS(tableName).Where("Id = (@id) ", new { id = Id }).Where("Deleted = 0").AddQueue();
        }

        public void DeleteData(List<string> Ids)
        {
            Type typeDB = typeof(DbModel);//获取类型
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            var dt = new Dictionary<string, object>();
            dt.Add("UpdateUser", TokenModel.Instance.id);
            dt.Add("UpdateDate", DateTime.Now);
            dt.Add("Deleted", true);
            Db.Updateable(dt).AS(tableName).Where("Id in (@id) ", new { id = Ids }).Where("Deleted = 0").ExecuteCommand();
        }

        public void DeleteDataQueue(List<string> Ids)
        {
            Type typeDB = typeof(DbModel);//获取类型
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            var dt = new Dictionary<string, object>();
            dt.Add("UpdateUser", TokenModel.Instance.id);
            dt.Add("UpdateDate", DateTime.Now);
            dt.Add("Deleted", true);
            Db.Updateable(dt).AS(tableName).Where("Id in (@id) ", new { id = Ids }).Where("Deleted = 0").AddQueue();
        }

        public void DeleteData(Expression<Func<DbModel, bool>> predicate)
        {
            Type typeDB = typeof(DbModel);//获取类型
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            var dt = new Dictionary<string, object>();
            dt.Add("UpdateUser", TokenModel.Instance.id);
            dt.Add("UpdateDate", DateTime.Now);
            dt.Add("Deleted", true);
            Db.Updateable<DbModel>(dt).AS(tableName).Where(predicate).Where("Deleted = 0").ExecuteCommand();
        }

        /// <summary>
        /// 同步数据集中的数据到数据库
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data">数据集</param>
        /// <param name="ids">数据库中的数据集主键</param>
        public void UpdateDataList<T>(List<T> data, List<string> ids)
        {
            if (data != null)
            {
                List<T> insertData = data.AsQueryable().Where("Id = \"" + Guid.Empty.ToString() + "\" or Id = null").ToList();
                insertData.ForEach(p =>
                {
                    InsertData(p);
                });
                List<T> updateData = data.AsQueryable().Where("Id <> \"" + Guid.Empty.ToString() + "\" and Id <> null").ToList();
                updateData.ForEach(p =>
                {
                    UpdateData(p);
                });
            }
            if (ids != null)
            {
                List<string> deleteData = new List<string>();
                ids.ForEach(p =>
                {
                    if (data == null)
                    {
                        deleteData.Add(p);
                    }
                    else if (!data.AsQueryable().Where("Id = \"" + p + "\"").Any())
                    {
                        deleteData.Add(p);
                    }
                });
                if (deleteData.Count() > 0)
                {
                    DeleteData(deleteData);
                }
            }
        }

        /// <summary>
        /// 删除不存数据集中不存在的数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <param name="ids"></param>
        public void DeleteDataList<T>(List<T> data, List<string> ids)
        {
            List<string> deleteData = new List<string>();
            ids.ForEach(p =>
            {
                if (data == null)
                {
                    deleteData.Add(p);
                }
                else if (!data.AsQueryable().Where("Id = \"" + p + "\"").Any())
                {
                    deleteData.Add(p);
                }
            });
            if (deleteData.Count() > 0)
            {
                DeleteData(deleteData);
            }
        }

        public DbModel GetDataById(string id)
        {
            return Queryable.Where("Id = (@id) ", new { id = id }).Where("Deleted = 0").First();
        }

        public DbModel GetData(Expression<Func<DbModel, bool>> predicate)
        {
            return Queryable.Where(predicate).Where("Deleted = 0").First();
        }

        public List<DbModel> GetDataList(Expression<Func<DbModel, bool>> predicate)
        {
            return Queryable.Where(predicate).Where("Deleted = 0").ToList();
        }

        public ISugarQueryable<DbModel> GetDataAllList()
        {
            return Queryable.Where("Deleted = 0");
        }
    }
}
