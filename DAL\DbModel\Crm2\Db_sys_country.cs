﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///国家信息表
    ///</summary>
    [SugarTable("sys_country")]
    public class Db_sys_country
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public int Id { get; set; }

        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Name { get; set; }

        /// <summary>
        /// Desc:编码
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Code { get; set; }

        /// <summary>
        /// Desc:英文名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string NameEN { get; set; }

        /// <summary>
        /// Desc:所属大洲
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Continent { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
