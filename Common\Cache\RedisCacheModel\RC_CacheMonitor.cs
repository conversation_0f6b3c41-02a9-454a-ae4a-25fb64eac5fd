using CRM2_API.Common.Cron;
using CRM2_API.Common.AppSetting;
using CRM2_API.Model.BLLModel.Enum;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        /// <summary>
        /// Redis缓存监控类
        /// 用于监控关键缓存的状态，如果缓存丢失则自动重新生成
        /// </summary>
        public class CacheMonitor
        {
            private const string MONITOR_JOB_NAME = "RedisCacheMonitor";
            private const string USER_ORG_CACHE_KEY = "userwithorg_all";
            
            /// <summary>
            /// 缓存监控配置
            /// </summary>
            public class CacheMonitorConfig
            {
                /// <summary>
                /// 是否启用缓存监控
                /// </summary>
                public bool EnableCacheMonitor { get; set; } = true;
                
                /// <summary>
                /// 监控间隔（分钟）
                /// </summary>
                public int MonitorIntervalMinutes { get; set; } = 30;
                
                /// <summary>
                /// 是否记录监控日志
                /// </summary>
                public bool EnableMonitorLog { get; set; } = true;
                
                /// <summary>
                /// 监控的缓存类型列表
                /// </summary>
                public List<string> MonitoredCacheTypes { get; set; } = new List<string>
                {
                    "UserWithOrg",      // 用户与组织缓存
                    "CollectingCompany", // 收款公司缓存
                    "PayingCompany",     // 付款公司缓存
                    "ExceptWords",       // 排除词缓存
                    "BlacklistUsers"     // 黑名单用户缓存
                };
            }
            
            /// <summary>
            /// 初始化缓存监控
            /// </summary>
            public static void InitializeCacheMonitor()
            {
                try
                {
                    var config = GetMonitorConfig();
                    
                    if (!config.EnableCacheMonitor)
                    {
                        LogUtil.AddLog("Redis缓存监控已禁用，跳过初始化");
                        return;
                    }
                    
                    // 停止已存在的监控任务
                    SafeCronUtil.StopJob(MONITOR_JOB_NAME);
                    
                    // 创建Cron表达式（每N分钟执行一次）
                    string cronExpression = $"0 */{config.MonitorIntervalMinutes} * * * ?";
                    
                    // 添加监控任务
                    bool success = SafeCronUtil.AddCronJob(MONITOR_JOB_NAME, () =>
                    {
                        MonitorAllCaches();
                    }, cronExpression);
                    
                    if (success)
                    {
                        LogUtil.AddLog($"Redis缓存监控已启动，监控间隔: {config.MonitorIntervalMinutes}分钟");
                    }
                    else
                    {
                        LogUtil.AddErrorLog("Redis缓存监控启动失败");
                    }
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"初始化Redis缓存监控失败: {ex.Message}");
                }
            }
            
            /// <summary>
            /// 监控所有缓存
            /// </summary>
            public static void MonitorAllCaches()
            {
                try
                {
                    var config = GetMonitorConfig();
                    
                    if (!config.EnableCacheMonitor)
                        return;
                    
                    var monitorResults = new List<CacheMonitorResult>();
                    
                    // 监控用户与组织缓存
                    if (config.MonitoredCacheTypes.Contains("UserWithOrg"))
                    {
                        var result = MonitorUserWithOrgCache();
                        monitorResults.Add(result);
                    }
                    
                    // 监控收款公司缓存
                    if (config.MonitoredCacheTypes.Contains("CollectingCompany"))
                    {
                        var result = MonitorCollectingCompanyCache();
                        monitorResults.Add(result);
                    }
                    
                    // 监控付款公司缓存
                    if (config.MonitoredCacheTypes.Contains("PayingCompany"))
                    {
                        var result = MonitorPayingCompanyCache();
                        monitorResults.Add(result);
                    }
                    
                    // 监控排除词缓存
                    if (config.MonitoredCacheTypes.Contains("ExceptWords"))
                    {
                        var result = MonitorExceptWordsCache();
                        monitorResults.Add(result);
                    }
                    
                    // 监控黑名单用户缓存
                    if (config.MonitoredCacheTypes.Contains("BlacklistUsers"))
                    {
                        var result = MonitorBlacklistUsersCache();
                        monitorResults.Add(result);
                    }
                    
                    // 记录监控结果
                    if (config.EnableMonitorLog)
                    {
                        LogMonitorResults(monitorResults);
                    }
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"缓存监控执行失败: {ex.Message}");
                }
            }
            
            /// <summary>
            /// 监控用户与组织缓存
            /// </summary>
            private static CacheMonitorResult MonitorUserWithOrgCache()
            {
                var result = new CacheMonitorResult
                {
                    CacheType = "UserWithOrg",
                    MonitorTime = DateTime.Now
                };
                
                try
                {
                    // 检查主要缓存键是否存在
                    bool allUsersExists = RedisHelper.Exists("userwithorg_all");
                    
                    if (!allUsersExists)
                    {
                        result.Status = CacheMonitorStatus.Missing;
                        result.Message = "用户与组织缓存丢失";
                        
                        // 自动重新生成缓存
                        LogUtil.AddLog("检测到用户与组织缓存丢失，正在重新生成...");
                        UserWithOrg.InitializeCache();
                        
                        result.ActionTaken = "重新生成缓存";
                        result.Success = true;
                    }
                    else
                    {
                        // 检查缓存数据是否有效
                        var allUsers = UserWithOrg.GetAllUsers();
                        if (allUsers == null || allUsers.Count == 0)
                        {
                            result.Status = CacheMonitorStatus.Invalid;
                            result.Message = "用户与组织缓存数据为空";
                            
                            // 重新生成缓存
                            LogUtil.AddLog("检测到用户与组织缓存数据为空，正在重新生成...");
                            UserWithOrg.InitializeCache();
                            
                            result.ActionTaken = "重新生成缓存";
                            result.Success = true;
                        }
                        else
                        {
                            result.Status = CacheMonitorStatus.Healthy;
                            result.Message = $"用户与组织缓存正常，共 {allUsers.Count} 条记录";
                            result.Success = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.Status = CacheMonitorStatus.Error;
                    result.Message = $"监控用户与组织缓存时发生错误: {ex.Message}";
                    result.Success = false;
                }
                
                return result;
            }
            
            /// <summary>
            /// 监控收款公司缓存
            /// </summary>
            private static CacheMonitorResult MonitorCollectingCompanyCache()
            {
                var result = new CacheMonitorResult
                {
                    CacheType = "CollectingCompany",
                    MonitorTime = DateTime.Now
                };
                
                try
                {
                    bool cacheExists = RedisHelper.Exists("collectingcompany_all");
                    
                    if (!cacheExists)
                    {
                        result.Status = CacheMonitorStatus.Missing;
                        result.Message = "收款公司缓存丢失";
                        
                        LogUtil.AddLog("检测到收款公司缓存丢失，正在重新生成...");
                        CollectingCompany.InitializeCache();
                        
                        result.ActionTaken = "重新生成缓存";
                        result.Success = true;
                    }
                    else
                    {
                        // 检查缓存数据是否有效（不仅检查key存在，还要检查实际取值）
                        var companies = CollectingCompany.GetAllCollectingCompanies();
                        if (companies == null || companies.Count == 0)
                        {
                            result.Status = CacheMonitorStatus.Invalid;
                            result.Message = "收款公司缓存数据为空";
                            
                            LogUtil.AddLog("检测到收款公司缓存数据为空，正在重新生成...");
                            CollectingCompany.InitializeCache();
                            
                            result.ActionTaken = "重新生成缓存";
                            result.Success = true;
                        }
                        else
                        {
                        result.Status = CacheMonitorStatus.Healthy;
                            result.Message = $"收款公司缓存正常，共 {companies.Count} 条记录";
                        result.Success = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.Status = CacheMonitorStatus.Error;
                    result.Message = $"监控收款公司缓存时发生错误: {ex.Message}";
                    result.Success = false;
                }
                
                return result;
            }
            
            /// <summary>
            /// 监控付款公司缓存
            /// </summary>
            private static CacheMonitorResult MonitorPayingCompanyCache()
            {
                var result = new CacheMonitorResult
                {
                    CacheType = "PayingCompany",
                    MonitorTime = DateTime.Now
                };
                
                try
                {
                    bool cacheExists = RedisHelper.Exists("payingcompany_all");
                    
                    if (!cacheExists)
                    {
                        result.Status = CacheMonitorStatus.Missing;
                        result.Message = "付款公司缓存丢失";
                        
                        LogUtil.AddLog("检测到付款公司缓存丢失，正在重新生成...");
                        PayingCompany.InitializeCache();
                        
                        result.ActionTaken = "重新生成缓存";
                        result.Success = true;
                    }
                    else
                    {
                        // 检查缓存数据是否有效
                        var companies = PayingCompany.GetAllPayingCompanies();
                        if (companies == null || companies.Count == 0)
                        {
                            result.Status = CacheMonitorStatus.Invalid;
                            result.Message = "付款公司缓存数据为空";
                            
                            LogUtil.AddLog("检测到付款公司缓存数据为空，正在重新生成...");
                        PayingCompany.InitializeCache();
                        
                        result.ActionTaken = "重新生成缓存";
                        result.Success = true;
                    }
                    else
                    {
                        result.Status = CacheMonitorStatus.Healthy;
                            result.Message = $"付款公司缓存正常，共 {companies.Count} 条记录";
                        result.Success = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.Status = CacheMonitorStatus.Error;
                    result.Message = $"监控付款公司缓存时发生错误: {ex.Message}";
                    result.Success = false;
                }
                
                return result;
            }
            
            /// <summary>
            /// 监控排除词缓存
            /// </summary>
            private static CacheMonitorResult MonitorExceptWordsCache()
            {
                var result = new CacheMonitorResult
                {
                    CacheType = "ExceptWords",
                    MonitorTime = DateTime.Now
                };
                
                try
                {
                    bool cacheExists = RedisHelper.Exists("exceptwords_all");
                    
                    if (!cacheExists)
                    {
                        result.Status = CacheMonitorStatus.Missing;
                        result.Message = "排除词缓存丢失";
                        
                        LogUtil.AddLog("检测到排除词缓存丢失，正在重新生成...");
                        ExceptWords.InitializeCache();
                        
                        result.ActionTaken = "重新生成缓存";
                        result.Success = true;
                    }
                    else
                    {
                        // 检查缓存数据是否有效
                        var exceptWords = ExceptWords.GetAllExceptWords();
                        if (exceptWords == null || exceptWords.Count == 0)
                        {
                            result.Status = CacheMonitorStatus.Invalid;
                            result.Message = "排除词缓存数据为空";
                            
                            LogUtil.AddLog("检测到排除词缓存数据为空，正在重新生成...");
                        ExceptWords.InitializeCache();
                        
                        result.ActionTaken = "重新生成缓存";
                        result.Success = true;
                    }
                    else
                    {
                        result.Status = CacheMonitorStatus.Healthy;
                            result.Message = $"排除词缓存正常，共 {exceptWords.Count} 条记录";
                        result.Success = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.Status = CacheMonitorStatus.Error;
                    result.Message = $"监控排除词缓存时发生错误: {ex.Message}";
                    result.Success = false;
                }
                
                return result;
            }
            
            /// <summary>
            /// 监控黑名单用户缓存
            /// </summary>
            private static CacheMonitorResult MonitorBlacklistUsersCache()
            {
                var result = new CacheMonitorResult
                {
                    CacheType = "BlacklistUsers",
                    MonitorTime = DateTime.Now
                };
                
                try
                {
                    bool cacheExists = RedisHelper.Exists("blacklist_users_all");
                    
                    if (!cacheExists)
                    {
                        result.Status = CacheMonitorStatus.Missing;
                        result.Message = "黑名单用户缓存丢失";
                        
                        LogUtil.AddLog("检测到黑名单用户缓存丢失，正在重新生成...");
                        BlacklistUsers.InitializeCache();
                        
                        result.ActionTaken = "重新生成缓存";
                        result.Success = true;
                    }
                    else
                    {
                        // 检查缓存数据是否有效
                        var blacklistUsers = BlacklistUsers.GetAllBlacklistUsers();
                        if (blacklistUsers == null)
                        {
                            result.Status = CacheMonitorStatus.Invalid;
                            result.Message = "黑名单用户缓存数据为空";
                            
                            LogUtil.AddLog("检测到黑名单用户缓存数据为空，正在重新生成...");
                        BlacklistUsers.InitializeCache();
                        
                        result.ActionTaken = "重新生成缓存";
                        result.Success = true;
                    }
                    else
                    {
                        result.Status = CacheMonitorStatus.Healthy;
                            result.Message = $"黑名单用户缓存正常，共 {blacklistUsers.Count} 条记录";
                        result.Success = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.Status = CacheMonitorStatus.Error;
                    result.Message = $"监控黑名单用户缓存时发生错误: {ex.Message}";
                    result.Success = false;
                }
                
                return result;
            }
            
            /// <summary>
            /// 记录监控结果
            /// </summary>
            private static void LogMonitorResults(List<CacheMonitorResult> results)
            {
                if (results == null || results.Count == 0)
                    return;
                
                var healthyCount = results.Count(r => r.Status == CacheMonitorStatus.Healthy);
                var missingCount = results.Count(r => r.Status == CacheMonitorStatus.Missing);
                var invalidCount = results.Count(r => r.Status == CacheMonitorStatus.Invalid);
                var errorCount = results.Count(r => r.Status == CacheMonitorStatus.Error);
                
                var logMessage = $"Redis缓存监控结果 - 正常: {healthyCount}, 丢失: {missingCount}, 无效: {invalidCount}, 错误: {errorCount}";
                
                if (missingCount > 0 || invalidCount > 0 || errorCount > 0)
                {
                    LogUtil.AddLog(logMessage);
                    
                    // 详细记录有问题的缓存
                    foreach (var result in results.Where(r => r.Status != CacheMonitorStatus.Healthy))
                    {
                        LogUtil.AddLog($"缓存问题 - {result.CacheType}: {result.Message}");
                        if (!string.IsNullOrEmpty(result.ActionTaken))
                        {
                            LogUtil.AddLog($"已采取行动: {result.ActionTaken}");
                        }
                        
                        // 对于错误状态，记录更详细的信息
                        if (result.Status == CacheMonitorStatus.Error)
                        {
                            LogUtil.AddErrorLog($"缓存监控错误详情 - {result.CacheType}: {result.Message}");
                        }
                    }
                }
                else
                {
                    // 只在调试模式下记录正常状态
                    if (AppSettings.Env == Enum_SystemSettingEnv.Debug)
                    {
                        LogUtil.AddLog(logMessage);
                    }
                }
            }
            
            /// <summary>
            /// 获取监控配置
            /// </summary>
            private static CacheMonitorConfig GetMonitorConfig()
            {
                return new CacheMonitorConfig
                {
                    EnableCacheMonitor = true,
                    MonitorIntervalMinutes = 5, // 开发环境和生产环境都5分钟监控一次
                    EnableMonitorLog = true,
                    MonitoredCacheTypes = new List<string>
                    {
                        "UserWithOrg",
                        "CollectingCompany", 
                        "PayingCompany",
                        "ExceptWords",
                        "BlacklistUsers"
                    }
                };
            }
            

        }
        
        /// <summary>
        /// 缓存监控结果
        /// </summary>
        public class CacheMonitorResult
        {
            /// <summary>
            /// 缓存类型
            /// </summary>
            public string CacheType { get; set; }
            
            /// <summary>
            /// 监控时间
            /// </summary>
            public DateTime MonitorTime { get; set; }
            
            /// <summary>
            /// 监控状态
            /// </summary>
            public CacheMonitorStatus Status { get; set; }
            
            /// <summary>
            /// 消息
            /// </summary>
            public string Message { get; set; }
            
            /// <summary>
            /// 是否成功
            /// </summary>
            public bool Success { get; set; }
            
            /// <summary>
            /// 采取的行动
            /// </summary>
            public string ActionTaken { get; set; }
        }
        
        /// <summary>
        /// 缓存监控状态
        /// </summary>
        public enum CacheMonitorStatus
        {
            /// <summary>
            /// 正常
            /// </summary>
            Healthy,
            
            /// <summary>
            /// 丢失
            /// </summary>
            Missing,
            
            /// <summary>
            /// 无效
            /// </summary>
            Invalid,
            
            /// <summary>
            /// 错误
            /// </summary>
            Error
        }
    }
} 