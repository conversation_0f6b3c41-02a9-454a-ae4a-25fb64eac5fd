﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///跟踪记录表
    ///</summary>
    [SugarTable("crm_work_report_trackingsnap")]
    public class Db_crm_work_report_trackingsnap
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:工作报表Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string WorkReportId { get; set; }

        /// <summary>
        /// Desc:分类
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? Type { get; set; }

        /// <summary>
        /// Desc:跟踪记录ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string TrackingRecordId { get; set; }

        /// <summary>
        /// Desc:追踪次数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? TrackingCount { get; set; }

        /// <summary>
        /// Desc:用户id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// Desc:1 == 私有池, 2 == 临时池
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? CustomerDataSource { get; set; }

        /// <summary>
        /// Desc:正式客户表id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerId { get; set; }

        /// <summary>
        /// Desc:临时客户表id,当客户来源为临时池时,此值存在
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerTemporaryId { get; set; }

        /// <summary>
        /// Desc:正式子公司表id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerSubCompanyId { get; set; }

        /// <summary>
        /// Desc:临时子公司表id,当客户来源为临时池时,此值存在
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerSubCompanyTemporaryId { get; set; }

        /// <summary>
        /// Desc:客户名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerName { get; set; }

        /// <summary>
        /// Desc:客户来源 0:搜索引擎 1:转介绍 2:广告推广 3:中国出口企业 4:广交会 5:其他
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? CustomerSource { get; set; }

        /// <summary>
        /// Desc:客户级别 0:未知 1:A类 2:B类 3:C类
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? CustomerLevel { get; set; }

        /// <summary>
        /// Desc:服务状态 0:无服务：全部开通 2：待开通 3：申请服务: 4：服务变更: 5：部分开通: 6：服务过期: 7：服务拒绝
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ServiceState { get; set; }

        /// <summary>
        /// Desc:(私有池)领取时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CollectionTime { get; set; }

        /// <summary>
        /// Desc:(临时池显示)客户临时序号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerOrderNum { get; set; }

        /// <summary>
        /// Desc:慧思ID/客户编号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerNum { get; set; }

        /// <summary>
        /// Desc:客户性质 0:未知 1:国企 2:私企 3:离岸 4:境外 5:其他
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? CustomerNature { get; set; }

        /// <summary>
        /// Desc:客户规模 0:未知 1:小型企业 2:中性企业 3:大型企业
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? CustomerSize { get; set; }

        /// <summary>
        /// Desc:快照时当前客户绑定的用户Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerBindUserId { get; set; }

        /// <summary>
        /// Desc:快照时当前客户绑定的用户所属的组织机构Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerBindUserOrgId { get; set; }

        /// <summary>
        /// Desc:快照时当前客户绑定的用户所属的组织机构名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerBindUserOrgName { get; set; }

        /// <summary>
        /// Desc:客户行业[聚合],以,分割
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerIndustrys { get; set; }

        /// <summary>
        /// Desc:合同表id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ContractId { get; set; }

        /// <summary>
        /// Desc:合同名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ContractName { get; set; }

        /// <summary>
        /// Desc:跟踪方式
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? TrackingType { get; set; }

        /// <summary>
        /// Desc:跟踪目的
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? TrackingPurpose { get; set; }

        /// <summary>
        /// Desc:跟踪阶段
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? TrackingStage { get; set; }

        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Remark { get; set; }

        /// <summary>
        /// Desc:是否可见
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? IsVisible { get; set; }

        /// <summary>
        /// Desc:是否隐藏
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Hidden { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
