using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.Enum;
using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.System;
using Newtonsoft.Json;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告节假日同步业务逻辑类（数据库版本）
    /// </summary>
    public partial class BLL_WorkReportDatabase : BaseBLL<BLL_WorkReportDatabase>
    {
        #region 节假日同步

        /// <summary>
        /// 同步节假日数据
        /// </summary>
        /// <param name="input">同步参数</param>
        /// <returns></returns>
        public VM_HolidaySync_Out SyncHolidayData(VM_HolidaySync_In input)
        {
            var syncLogDbOpe = DbOpe_crm_holiday_sync_log.Instance;
            var holidayDbOpe = DbOpe_crm_holiday.Instance;
            
            string logId = "";
            var startTime = DateTime.Now;

            try
            {
                // 创建同步日志
                logId = syncLogDbOpe.CreateSyncLog(input.SyncYear, "Timor Tech API", $"http://timor.tech/api/holiday/year/{input.SyncYear}");

                // 调用第三方API获取节假日数据
                var apiData = GetHolidayDataFromApi(input.SyncYear);
                
                if (apiData == null || !apiData.Any())
                {
                    throw new ApiException("未获取到节假日数据");
                }

                // 处理数据同步
                var syncResult = ProcessHolidaySync(apiData, input.SyncYear, input.ForceSync);

                // 更新同步日志
                syncLogDbOpe.UpdateSyncLogResult(
                    logId, 
                    EnumSyncStatus.Success, 
                    JsonConvert.SerializeObject(apiData.Take(5)), // 只保存前5条作为示例
                    syncResult.TotalAffected
                );

                return new VM_HolidaySync_Out
                {
                    Success = true,
                    SyncYear = input.SyncYear,
                    SyncStartTime = startTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    SyncEndTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    ApiSource = "Timor Tech API",
                    AddedCount = syncResult.AddedCount,
                    UpdatedCount = syncResult.UpdatedCount,
                    SkippedCount = syncResult.SkippedCount,
                    ErrorCount = syncResult.ErrorCount,
                    Message = $"同步完成，新增{syncResult.AddedCount}条，更新{syncResult.UpdatedCount}条，跳过{syncResult.SkippedCount}条",
                    SyncLogId = logId,
                    Test = "test"
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"同步节假日数据失败：{ex.Message}", ex);

                // 更新同步日志为失败状态
                if (!string.IsNullOrEmpty(logId))
                {
                    syncLogDbOpe.UpdateSyncLogResult(logId, EnumSyncStatus.Failed, null, 0, ex.Message);
                }

                return new VM_HolidaySync_Out
                {
                    Success = false,
                    SyncYear = input.SyncYear,
                    SyncStartTime = startTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    SyncEndTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    ApiSource = "Timor Tech API",
                    AddedCount = 0,
                    UpdatedCount = 0,
                    SkippedCount = 0,
                    ErrorCount = 1,
                    Message = "同步失败",
                    ErrorMessage = ex.Message,
                    SyncLogId = logId
                };
            }
        }

        /// <summary>
        /// 自动同步节假日数据（定时任务调用）
        /// </summary>
        /// <param name="syncYear">同步年份</param>
        /// <returns></returns>
        public bool AutoSyncHolidayData(int syncYear)
        {
            try
            {
                var syncLogDbOpe = DbOpe_crm_holiday_sync_log.Instance;
                
                // 检查是否需要同步（24小时内有成功记录则跳过）
                if (!syncLogDbOpe.NeedSync(syncYear, 24))
                {
                    LogUtil.AddLog($"年份{syncYear}的节假日数据无需同步");
                    return true;
                }

                // 执行同步
                var syncInput = new VM_HolidaySync_In
                {
                    SyncYear = syncYear,
                    ForceSync = false
                };

                var result = SyncHolidayData(syncInput);
                
                if (result.Success)
                {
                    LogUtil.AddLog($"自动同步年份{syncYear}的节假日数据成功：{result.Message}");
                    return true;
                }
                else
                {
                    LogUtil.AddErrorLog($"自动同步年份{syncYear}的节假日数据失败：{result.ErrorMessage}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"自动同步节假日数据异常，年份：{syncYear}，错误：{ex.Message}", ex);
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 从第三方API获取节假日数据
        /// </summary>
        /// <param name="year">年份</param>
        /// <returns></returns>
        private List<HolidayApiData> GetHolidayDataFromApi(int year)
        {
            try
            {
                // 调用 Timor Tech API 获取节假日数据
                var apiUrl = $"http://timor.tech/api/holiday/year/{year}";

                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(30);

                var response = httpClient.GetStringAsync(apiUrl).Result;

                if (string.IsNullOrEmpty(response))
                {
                    throw new ApiException("API返回数据为空");
                }

                // 解析 Timor Tech API 返回的数据
                var apiResult = JsonConvert.DeserializeObject<TimorApiResponse>(response);

                if (apiResult?.Code != 0)
                {
                    throw new ApiException($"API返回错误：{apiResult?.Type ?? "未知错误"}");
                }

                var holidayData = ParseTimorApiData(apiResult, year);

                LogUtil.AddLog($"从Timor Tech API获取到{year}年节假日数据{holidayData.Count}条");
                return holidayData;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"调用Timor Tech API失败：{ex.Message}", ex);
                
                // API调用失败时直接抛出异常，不使用Mock数据
                throw new ApiException($"节假日数据同步失败：无法从Timor Tech API获取{year}年节假日数据。错误信息：{ex.Message}");
            }
        }



        /// <summary>
        /// 处理节假日数据同步
        /// </summary>
        /// <param name="apiData">API数据</param>
        /// <param name="year">年份</param>
        /// <param name="forceSync">是否强制同步</param>
        /// <returns></returns>
        private (int AddedCount, int UpdatedCount, int SkippedCount, int ErrorCount, int TotalAffected) ProcessHolidaySync(List<HolidayApiData> apiData, int year, bool forceSync)
        {
            var holidayDbOpe = DbOpe_crm_holiday.Instance;
            int addedCount = 0, updatedCount = 0, skippedCount = 0, errorCount = 0;

            try
            {
                // 获取现有的节假日数据
                var existingHolidays = holidayDbOpe.GetHolidaysByDateRange(
                    new DateTime(year, 1, 1), 
                    new DateTime(year, 12, 31)
                );

                var holidaysToAdd = new List<Db_crm_holiday>();
                var holidaysToUpdate = new List<Db_crm_holiday>();

                foreach (var apiItem in apiData)
                {
                    try
                    {
                        var existing = existingHolidays.FirstOrDefault(h => h.HolidayDate.Date == apiItem.Date.Date);

                        if (existing == null)
                        {
                            // 新增
                            holidaysToAdd.Add(new Db_crm_holiday
                            {
                                Id = Guid.NewGuid().ToString(),
                                HolidayDate = apiItem.Date,
                                HolidayYear = apiItem.Date.Year,
                                HolidayMonth = apiItem.Date.Month,
                                HolidayDay = apiItem.Date.Day,
                                HolidayName = apiItem.Name,
                                HolidayType = apiItem.Type,
                                IsWorkday = apiItem.IsWorkday,
                                Remark = $"API同步于{DateTime.Now:yyyy-MM-dd}",
                                Source = "API",
                                Deleted = false
                            });
                            addedCount++;
                        }
                        else if (forceSync || existing.Source == "API")
                        {
                            // 更新（只有强制同步或原来就是API数据才更新）
                            existing.HolidayName = apiItem.Name;
                            existing.HolidayType = apiItem.Type;
                            existing.IsWorkday = apiItem.IsWorkday;
                            existing.Remark = $"API更新于{DateTime.Now:yyyy-MM-dd}";
                            existing.Source = "API";
                            holidaysToUpdate.Add(existing);
                            updatedCount++;
                        }
                        else
                        {
                            // 跳过（手动数据不覆盖）
                            skippedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddErrorLog($"处理节假日数据失败，日期：{apiItem.Date:yyyy-MM-dd}，错误：{ex.Message}", ex);
                        errorCount++;
                    }
                }

                // 批量操作
                if (holidaysToAdd.Any())
                {
                    holidayDbOpe.BatchInsertHolidays(holidaysToAdd);
                }

                if (holidaysToUpdate.Any())
                {
                    holidayDbOpe.BatchUpdateHolidays(holidaysToUpdate);
                }

                return (addedCount, updatedCount, skippedCount, errorCount, addedCount + updatedCount);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"批量处理节假日数据失败：{ex.Message}", ex);
                throw new ApiException($"批量处理节假日数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 解析 Timor Tech API 数据
        /// </summary>
        /// <param name="apiResult">API响应结果</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        private List<HolidayApiData> ParseTimorApiData(TimorApiResponse apiResult, int year)
        {
            var holidayData = new List<HolidayApiData>();

            if (apiResult?.Holiday == null)
            {
                return holidayData;
            }

            foreach (var item in apiResult.Holiday)
            {
                try
                {
                    // 解析日期格式 "MM-dd"
                    var dateParts = item.Key.Split('-');
                    if (dateParts.Length != 2 ||
                        !int.TryParse(dateParts[0], out int month) ||
                        !int.TryParse(dateParts[1], out int day))
                    {
                        continue;
                    }

                    var date = new DateTime(year, month, day);
                    var info = item.Value;

                    // 添加调试日志
                    if (date.Month == 2 && date.Day == 8)
                    {
                        LogUtil.AddLog($"2月8日数据：Holiday={info.Holiday}, Rest={info.Rest}, Name={info.Name}");
                    }

                    // 根据 Timor API 的数据结构转换
                    int holidayType;
                    bool isWorkday;

                    if (info.Holiday)
                    {
                        // 法定节假日
                        holidayType = 1;
                        isWorkday = false;
                    }
                    else if (!string.IsNullOrEmpty(info.Target))
                    {
                        // 调休工作日（补班日）- 有target字段说明是调休
                        holidayType = 3;
                        isWorkday = true;
                    }
                    else
                    {
                        // 普通周末
                        holidayType = 2;
                        isWorkday = false;
                    }

                    // 添加调试日志
                    if (date.Month == 2 && date.Day == 8)
                    {
                        LogUtil.AddLog($"2月8日转换后：HolidayType={holidayType}, IsWorkday={isWorkday}");
                    }

                    holidayData.Add(new HolidayApiData
                    {
                        Date = date,
                        Name = info.Name ?? (info.Holiday ? "节假日" : "周末"),
                        Type = holidayType,
                        IsWorkday = isWorkday
                    });
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"解析节假日数据失败，日期：{item.Key}，错误：{ex.Message}", ex);
                }
            }

            return holidayData;
        }

        #endregion

        #region 内部类

        /// <summary>
        /// 节假日API数据模型
        /// </summary>
        private class HolidayApiData
        {
            public DateTime Date { get; set; }
            public string Name { get; set; } = "";
            public int Type { get; set; } // 1=法定节假日, 2=周末, 3=调休工作日
            public bool IsWorkday { get; set; }
        }

        /// <summary>
        /// Timor Tech API 响应模型
        /// </summary>
        private class TimorApiResponse
        {
            public int Code { get; set; }
            public string? Type { get; set; }
            public Dictionary<string, TimorHolidayInfo>? Holiday { get; set; }
        }

        /// <summary>
        /// Timor Tech 节假日信息模型
        /// </summary>
        private class TimorHolidayInfo
        {
            public bool Holiday { get; set; }
            public string? Name { get; set; }
            public int Wage { get; set; }
            public string? Date { get; set; }
            public int Rest { get; set; }
            public string? Target { get; set; }  // 调休目标节日
            public bool After { get; set; }     // 是否为节后调休
        }

        #endregion
    }
}
