﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;
using static CRM2_API.Common.Filter.WorkLog;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;

namespace CRM2_API.Controllers
{
    [Description("合同服务管理-期刊邮寄")]
    public class ContractServiceInfoProjectController : MyControllerBase
    {
        public ContractServiceInfoProjectController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        /// <summary>
        /// 根据查询条件获取合同服务信息期刊邮寄申请信息列表
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchContractProductServiceInfoProjectApplList_Out> SearchContractProductServiceInfoProjectApplList(SearchContractProductServiceInfoProjectApplList_In search_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.SearchContractProductServiceInfoProjectApplList(search_In, ref total);
            return GetApiTableOut(list, total);
        }


        /// <summary>
        /// 根据申请id获取合同服务申请信息_期刊邮寄信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoApplyInfoProjectByApplId_Out GetContractServiceInfoApplyInfoProjectByApplId(string Id)
        {
            //获取申请详细信息
            var apply = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.GetContractServiceInfoApplyInfoProjectByApplId(Id);
            //获取合同详细信息
            apply.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
            //到账备注列表
            apply.ReceiptRemarks = DbOpe_crm_contract_receiptregister.Instance.GetRemarksByContractId(apply.ContractId);
            //合同的所有到账信息
            apply.ReceiptRegisterCollectionList = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(apply.ContractId, String.Empty);
            //获取产品详细信息
            apply.ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(apply.ContractProductInfoId).MappingTo<GetContractServiceApplInfoProjectByApplId_Out_ProductInfo>();
            apply.ProductInfo.OpeningMonths = apply.PeriodsNum;
            apply.ProductInfo.FirstOpeningMonths = apply.PeriodsNum;
            var retApply = apply.MappingTo<GetContractServiceInfoApplyInfoProjectByApplId_Out>();
            return retApply;
        }

        /// <summary>
        /// 作废合同服务期刊邮寄申请信息
        /// </summary>
        /// <param name="Id"></param>
        [HttpPost, PreLog]
        public void VoidContractProductServiceProjectInfo(string Id)
        {
            BLL_ContractServiceProject.Instance.VoidContractProductServiceProjectInfo(Id);
        }

        /// <summary>
        /// 删除合同服务期刊邮寄申请信息
        /// </summary>
        /// <param name="Id"></param>
        [HttpPost, PreLog]
        public void DeleteContractProductServiceProjectInfo(string Id)
        {
            BLL_ContractServiceProject.Instance.DeleteContractProductServiceProjectInfo(Id);
        }

        /// <summary>
        /// 修改合同服务期刊邮寄申请信息
        /// </summary>
        /// <param name="update_In"></param>
        [HttpPost, PreLog]
        public void UpdateContractProductServiceProjectInfo(UpdateContractProductServiceProjectInfo_In update_In)
        {
            BLL_ContractServiceProject.Instance.UpdateContractProductServiceProjectInfo(update_In);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息_期刊邮寄信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoProjectByApplId_Out GetContractServiceInfoProjectByApplId(string Id)
        {
            return DbOpe_crm_contract_productserviceinfo_project_appl.Instance.GetContractServiceInfoProjectByApplId(Id);
        }

        /// <summary>
        /// 根据合同Id和审核日期获取发刊信息列表
        /// </summary>
        [HttpPost, PreLog]
        public ApiTableOut<ContractProductServiceinfoProjectList_Out> GetContractProductServiceinfoProjectListByContractId(ContractProductServiceinfoProjectList_In contractProductServiceinfoProjectListIn)
        {
            return BLL_ContractServiceProject.Instance.GetContractProductServiceinfoProjectListByContractId(contractProductServiceinfoProjectListIn);
        }

        /// <summary>
        /// 根据客户Id和审核日期获取发刊信息列表
        /// </summary>
        [HttpPost, PreLog]
        public ApiTableOut<ContractProductServiceinfoProjectList_Out> GetContractProductServiceinfoProjectListByCustomerId(ContractProductServiceinfoProjectList_CustomerId_In contractProductServiceinfoProjectListCustomerIdIn)
        {
            return BLL_ContractServiceProject.Instance.GetContractProductServiceinfoProjectListByCustomerId(contractProductServiceinfoProjectListCustomerIdIn);
        }

        /// <summary>
        /// 下载期刊邮寄批量导入模板
        /// </summary>
        /// <returns></returns>
        [HttpPost, PreLog]
        public async Task<Stream> DownloadMailingExcelTemplate()
        {
            var filePath = BLL_ContractServiceProject.Instance.DownloadMailingExcelTemplate();
            FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
            SetDownloadFileName("期刊邮寄批量导入模板.xlsx");
            return fs;
        }

        /// <summary>
        /// 上传期刊邮寄批量导入模板
        /// </summary>
        /// <param name="uploadMailingTemplate_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public List<AnalysisMailingTemplate_Out> UploadMailingTemplate([FromForm]UploadMailingTemplate_In uploadMailingTemplate_In)//)
        {
            return BLL_ContractServiceProject.Instance.UploadMailingTemplate(uploadMailingTemplate_In);
        }

        /// <summary>
        /// 批量登记合同服务信息期刊邮寄的快递信息
        /// </summary>
        /// <param name="register_In"></param>
        [HttpPost, PreLog]
        public void BatchRegisterContractProductServiceInfoMailing(List<BatchRegisterContractProductServiceInfoMailing_In> register_In)
        {
            BLL_ContractServiceProject.Instance.BatchRegisterContractProductServiceInfoMailing(register_In);
        }
    }
}

