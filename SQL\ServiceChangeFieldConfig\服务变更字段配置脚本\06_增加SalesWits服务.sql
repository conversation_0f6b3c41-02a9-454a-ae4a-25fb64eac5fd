-- 服务变更字段配置 - 增加SalesWits服务 (ChangeReasonEnum = 5)
-- 执行日期: 2025-01-29

-- ================================
-- 增加SalesWits服务 - 申请场景字段
-- ================================

-- SalesWits服务申请时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 2, 5, 'SalesWitsAccountNum', 'SalesWits账号数量', 10, 1, 1, 'apply', NULL, '增加SalesWits服务时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsServiceMonth', 'SalesWits服务开通月数', 11, 1, 1, 'apply', NULL, '增加SalesWits服务时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsServiceStart', 'SalesWits服务开始日期', 12, 1, 1, 'apply', NULL, '增加SalesWits服务时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsServiceEnd', 'SalesWits服务结束日期', 13, 1, 1, 'apply', NULL, '增加SalesWits服务时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsUsers', 'SalesWits使用者', 14, 1, 1, 'apply', NULL, '增加SalesWits服务时申请可修改', NOW(), 'system', NULL, 'system', 0);

-- 慧思服务通用字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 5, 'AccountList', '账号列表', 20, 1, 1, 'apply', NULL, '增加SalesWits服务时申请可修改', NOW(), 'system', NULL, 'system', 0);

-- ================================
-- 增加SalesWits服务 - 审核场景字段
-- ================================

-- SalesWits服务审核时可修改字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 2, 5, 'SalesWitsAccountNum', 'SalesWits账号数量', 30, 1, 1, 'audit', 'AccountList,SalesWitsAccountNum', '增加SalesWits服务时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsServiceMonth', 'SalesWits服务开通月数', 31, 1, 1, 'audit', 'SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth', '增加SalesWits服务时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsServiceStart', 'SalesWits服务开始日期', 32, 1, 1, 'audit', 'SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth', '增加SalesWits服务时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsServiceEnd', 'SalesWits服务结束日期', 33, 1, 1, 'audit', 'SalesWitsServiceStart,SalesWitsServiceEnd,SalesWitsServiceMonth', '增加SalesWits服务时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsGiftResourceMonths', 'SalesWits赠送资源月份数', 34, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsGiftResourceMonths', '增加SalesWits服务时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsGiftTokenNum', 'SalesWits赠送Token数量', 35, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsGiftTokenNum', '增加SalesWits服务时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsGiftEmailNum', 'SalesWits赠送邮件数量', 36, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsGiftEmailNum', '增加SalesWits服务时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 5, 'SalesWitsUsers', 'SalesWits使用者', 37, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsUsers', '增加SalesWits服务时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- 慧思服务通用字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 5, 'AccountList', '账号列表', 40, 1, 1, 'audit', 'AccountList,SalesWitsAccountNum', '增加SalesWits服务时审核可修改', NOW(), 'system', NULL, 'system', 0);

SELECT '增加SalesWits服务配置完成！' AS message; 