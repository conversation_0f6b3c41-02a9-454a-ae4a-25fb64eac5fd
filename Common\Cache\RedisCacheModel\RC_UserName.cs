﻿namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class UserName
        {
            const string USERNAME = "username_";
            public static List<string> CheckUserName(List<string> nameList)
            {
                List<string> list = new List<string>();
                nameList.ForEach(name =>
                {
                    if (RedisHelper.Exists(USERNAME + name))
                        list.Add(name);
                });
                return list;
            }
            public static void SaveUser(string name)
            {
                RedisHelper.Set(USERNAME + name, name/*, TimeSpan.FromMinutes(60)*/);
            }
        }
        
    }
}
