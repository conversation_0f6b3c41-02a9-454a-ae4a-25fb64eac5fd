﻿using System.IO;
using IP2Region.Net.Abstractions;
using IP2Region.Net.XDB;

namespace CRM2_API.Common.Utils
{
    public class IPUtil
    {
        private static ISearcher _searcher = null;
        private static readonly object lockObj = new object();

        private IPUtil() { }

        public static ISearcher Searcher
        {
            get
            {
                if (_searcher == null)
                {
                    lock (lockObj)
                    {
                        if (_searcher == null)
                        {
                            _searcher = new Searcher(CachePolicy.Content, Path.Combine(AppContext.BaseDirectory, "Data/ip2region.xdb"));
                        }
                    }
                }
                return _searcher;
            }
        }
    }
}