using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 报告客户关联表
    /// </summary>
    [SugarTable("crm_report_customer")]
    public class Db_crm_report_customer
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 报告ID
        /// </summary>
        public string ReportId { get; set; }

        /// <summary>
        /// 报告类型：1-日报，2-周报，3-月报
        /// </summary>
        public int ReportType { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 团队ID
        /// </summary>
        public string TeamId { get; set; }

        /// <summary>
        /// 团队名称
        /// </summary>
        public string TeamName { get; set; }

        /// <summary>
        /// 报告日期
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// 报告年份
        /// </summary>
        public int ReportYear { get; set; }

        /// <summary>
        /// 报告月份
        /// </summary>
        public int? ReportMonth { get; set; }

        /// <summary>
        /// 报告周数
        /// </summary>
        public int? ReportWeek { get; set; }

        /// <summary>
        /// 模块键名
        /// </summary>
        public string ModuleKey { get; set; }

        /// <summary>
        /// 模块标题
        /// </summary>
        public string ModuleTitle { get; set; }

        /// <summary>
        /// 模块排序
        /// </summary>
        public int ModuleOrder { get; set; }

        /// <summary>
        /// 章节键名
        /// </summary>
        public string SectionKey { get; set; }

        /// <summary>
        /// 章节标题
        /// </summary>
        public string SectionTitle { get; set; }

        /// <summary>
        /// 子项排序
        /// </summary>
        public int SectionOrder { get; set; }

        /// <summary>
        /// 客户序号，用于区分同一模块下的不同客户记录
        /// </summary>
        public int CustomerIndex { get; set; }

        /// <summary>
        /// 客户ID，手动填写时可为空
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户级别：A/B/C
        /// </summary>
        public string CustomerLevel { get; set; }

        /// <summary>
        /// 客户国别
        /// </summary>
        public string CustomerCountry { get; set; }

        /// <summary>
        /// 主营产品
        /// </summary>
        public string CustomerProduct { get; set; }

        /// <summary>
        /// HS编码
        /// </summary>
        public string CustomerHsCode { get; set; }

        /// <summary>
        /// 跟踪记录ID，手动填写时可为空
        /// </summary>
        public string TrackingRecordId { get; set; }

        /// <summary>
        /// 是否来自跟踪记录：0-手动填写，1-跟踪记录
        /// </summary>
        public bool IsFromTracking { get; set; }

        /// <summary>
        /// 方案：客户跟进时为推荐方案，客户签约时为签约方案
        /// </summary>
        public string Solution { get; set; }

        /// <summary>
        /// 相关内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateDate { get; set; }
    }
} 