﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.VariantTypes;
using Microsoft.AspNetCore.Http;
using SqlSugar;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Text.RegularExpressions;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Collectioninfo;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.BLL
{
    public class BLL_Collectioninfo : BaseBLL<BLL_Collectioninfo>
    {
        private void IsDepositValidating(CollectionInfoIsDeposit_In collectionInfo)
        {
            if (collectionInfo.PaymentMethod == EnumPaymentMethod.Bank.ToInt())
            {
                if (collectionInfo.BankPaymentAmount == null)
                {
                    throw new ApiException("银行支付金额不可为空");
                }
            }
            if (collectionInfo.PaymentMethod == EnumPaymentMethod.Cash.ToInt())
            {
                if (collectionInfo.CashPaymentAmount == null)
                {
                    throw new ApiException("现金支付金额不可为空");
                }
            }
            if (collectionInfo.PaymentMethod == EnumPaymentMethod.BankAndCash.ToInt())
            {
                if (collectionInfo.BankPaymentAmount == null)
                {
                    throw new ApiException("银行支付金额不可为空");
                }
                if (collectionInfo.CashPaymentAmount == null)
                {
                    throw new ApiException("现金支付金额不可为空");
                }
            }
            if (collectionInfo.Currency != EnumCurrency.CNY.ToInt())
            {
                if (collectionInfo.FCArrivalAmount == null)
                {
                    throw new ApiException("外币到账金额不可为空");
                }
            }
        }

        private void Validating(List<CollectionInfo_In> addCollectionInfo)
        {
            //验证回款信息
            if (addCollectionInfo != null && addCollectionInfo.Count > 0)
            {
                addCollectionInfo.ForEach(collectionInfo =>
                {
                    if (collectionInfo.PaymentMethod == EnumPaymentMethod.Bank.ToInt())
                    {
                        if (collectionInfo.BankPaymentAmount == null)
                        {
                            throw new ApiException("银行支付金额不可为空");
                        }
                    }
                    if (collectionInfo.PaymentMethod == EnumPaymentMethod.Cash.ToInt())
                    {
                        if (collectionInfo.CashPaymentAmount == null)
                        {
                            throw new ApiException("现金支付金额不可为空");
                        }
                    }
                    if (collectionInfo.PaymentMethod == EnumPaymentMethod.BankAndCash.ToInt())
                    {
                        if (collectionInfo.BankPaymentAmount == null)
                        {
                            throw new ApiException("银行支付金额不可为空");
                        }
                        if (collectionInfo.CashPaymentAmount == null)
                        {
                            throw new ApiException("现金支付金额不可为空");
                        }
                    }
                    if (collectionInfo.Currency != EnumCurrency.CNY.ToInt())
                    {
                        if (collectionInfo.FCArrivalAmount == null)
                        {
                            throw new ApiException("外币到账金额不可为空");
                        }
                    }
                });
            }
            else
            {
                throw new ApiException("回款信息不可为空");
            }
        }

        /// <summary>
        /// 添加回款信息
        /// </summary>
        public List<Db_crm_collectioninfo> AddCollectionInfo(List<AddCollectionInfo_In> addCollectionInfo)
        {
            //验证输入项
            Validating(addCollectionInfo.ConvertAll<CollectionInfo_In>(r => r));

            //List<AddCollectionInfo_In> Receipts = addCollectionInfo.Where(r => r.TransactionReceipt != "" && r.TransactionReceipt != null).ToList();
            //if (Receipts.Count > Receipts.Select(r => r.TransactionReceipt).Distinct().Count())
            //{
            //    throw new ApiException("回款交易凭证信息存在重复信息");
            //}

            //List<string> result = new List<string>();
            //foreach (AddCollectionInfo_In l in addCollectionInfo)
            //{
            //    if (l.TransactionReceipt.IsNotNullOrEmpty())
            //    {
            //        List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetDataList(r => r.TransactionReceipt == l.TransactionReceipt).ToList();
            //        if (list.Count > 0)
            //        {
            //            result.Add("“" + l.TransactionReceipt + "”");
            //        }
            //    }
            //}
            //if (result.Count > 0)
            //{
            //    throw new ApiException("回款交易凭证信息已存在," + result.JoinToString(","));
            //}

            List<Db_crm_collectioninfo> collectioninfo = new List<Db_crm_collectioninfo>();
            DbOpe_crm_collectioninfo.Instance.TransDeal(() =>
            {

                List<string> CollectingCompanyList = addCollectionInfo.Select(r => r.CollectingCompanyName).ToList();
                List<Db_crm_collectingcompany> collectingcompany = DbOpe_crm_collectingcompany.Instance.GetDataList(r => CollectingCompanyList.Contains(r.CollectingCompanyName));
                List<string> PaymentCompanyList = addCollectionInfo.Select(r => r.PaymentCompanyName).ToList();
                List<Db_crm_customer_subcompany> customersubcompany = DbOpe_crm_customer_subcompany.Instance.GetDataList(r => PaymentCompanyList.Contains(r.CompanyName) && (r.IsValid == 1 || (r.IsValid == 0 && r.CustomerId == Guid.Empty.ToString())));


                foreach (AddCollectionInfo_In co in addCollectionInfo)
                {
                    Db_crm_collectioninfo item = new Db_crm_collectioninfo();
                    item = co.MappingTo<Db_crm_collectioninfo>();
                    item.VerifyConfirm = 1;
                    List<Db_crm_collectingcompany> cc = collectingcompany.Where(r => r.CollectingCompanyName == item.CollectingCompanyName).ToList();
                    if (cc.Count() > 0)
                    {
                        item.CollectingCompany = cc.First().Id;
                    }
                    List<Db_crm_customer_subcompany> csc = customersubcompany.Where(r => r.CompanyName == item.PaymentCompanyName).ToList();
                    if (csc.Count() > 0)
                    {
                        item.PaymentCompany = csc.First().Id;
                    }

                    //List<Db_crm_contract_paymentinfo> paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetDataList(r =>
                    //r.CollectingCompany == item.CollectingCompany &&
                    //r.PaymentCompany == item.PaymentCompany &&
                    //r.Currency == item.Currency &&
                    //r.ArrivalAmount == item.ArrivalAmount &&
                    //r.PaymentMethod == item.PaymentMethod &&
                    //r.BankPaymentAmount == item.BankPaymentAmount &&
                    //r.CashPaymentAmount == item.CashPaymentAmount);
                    List<Db_crm_contract_paymentinfo> paymentinfo = new List<Db_crm_contract_paymentinfo>();
                    if (item.Currency == EnumCurrency.CNY.ToInt())
                    {
                        paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfo(item.CollectingCompany, item.PaymentCompany, item.Currency, item.ArrivalAmount, item.PaymentMethod, item.BankPaymentAmount, item.CashPaymentAmount);
                    }
                    else
                    {
                        paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfo(item.CollectingCompany, item.PaymentCompany, item.Currency, item.FCArrivalAmount, item.PaymentMethod, item.BankPaymentAmount, item.CashPaymentAmount);
                    }

                    //if (paymentinfo.Count > 0)
                    //{
                    //    item.IsDeposit = false;
                    //    item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                    //    item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //    item.IsMatchingAll = EnumIsMatchingAll.All.ToInt() == 1 ? true : false;
                    //}
                    //else
                    //{
                    //    paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByPaymentCompany(item.PaymentCompany);
                    //    if (paymentinfo.Count > 0)
                    //    {
                    //        bool IsDeposit = false;
                    //        if (item.Currency == EnumCurrency.CNY.ToInt())
                    //        {
                    //            if ((item.ArrivalAmount.Value * 2) > paymentinfo.First().ArrivalAmount.Value || (item.ArrivalAmount.Value * 2) > paymentinfo.First().PlannedArrivalAmount.Value)
                    //            {
                    //                IsDeposit = false;
                    //            }
                    //            else
                    //            {
                    //                IsDeposit = true;
                    //            }
                    //        }
                    //        else
                    //        {
                    //            if ((item.FCArrivalAmount.Value * 2) > paymentinfo.First().ArrivalAmount.Value || (item.FCArrivalAmount.Value * 2) > paymentinfo.First().PlannedArrivalAmount.Value)
                    //            {
                    //                IsDeposit = false;
                    //            }
                    //            else
                    //            {
                    //                IsDeposit = true;
                    //            }
                    //        }
                    //        if (IsDeposit)
                    //        {
                    //            item.IsDeposit = true;
                    //            item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                    //            item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //            item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    //        }
                    //        else
                    //        {
                    //            item.IsDeposit = false;
                    //            item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                    //            item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //            item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    //        }
                    //    }
                    //    else
                    //    {
                    //        item.IsDeposit = false;
                    //        item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                    //        item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //        item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    //    }
                    //    //item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                    //    //item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //    //item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    //}
                    if (item.IsDeposit == true)
                    {
                        item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                        item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                        item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    }
                    else
                    {
                        if (paymentinfo.Count > 0)
                        {
                            item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                            item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                            item.IsMatchingAll = EnumIsMatchingAll.All.ToInt() == 1 ? true : false;
                        }
                        else
                        {
                            paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByPaymentCompany(item.PaymentCompany);
                            if (paymentinfo.Count > 0)
                            {
                                item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                            }
                            else
                            {
                                item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                            }
                            //item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                            //item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                            //item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                        }
                    }

                    item.MaintenanceStatus = EnumMaintenanceStatus.Pass.ToInt();

                    int Enable = EnumExchangeState.Enable.ToInt();
                    Db_sys_exchange exchange = DbOpe_sys_exchange.Instance.GetData(r => r.ExchangeDate == item.ArrivalDate && r.TargetCurrency == item.Currency && r.State == Enable);
                    if (exchange != null)
                    {
                        item.ExchangeRate = exchange.ExchangeRate;
                    }

                    //修改 2024年4月26日 增加到账银行及账号信息
                    item.BankAccountNumber = co.BankAccountNumber;
                    item.CollectingBankName = co.CollectingBankName;

                    //修改 2024年8月1日 增加对账中锁定信息
                    item.IsLocked = EnumLocked.UnLocked.ToInt();

                    collectioninfo.Add(item);
                    Guid collectionInfoId = DbOpe_crm_collectioninfo.Instance.InsertDataReturnId(item);
                    item.Id = collectionInfoId.ToString();

                    if (item.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt())
                    {
                        Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(paymentinfo.First().ContractId);
                        //获取用户信息
                        var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                        //获取组织信息树，从当前组织追溯出所有上级组织
                        var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                        string? OrgDivisionId = null;
                        string OrgDivisionName = "";
                        string? OrgBrigadeId = null;
                        string OrgBrigadeName = "";
                        string? OrgRegimentId = null;
                        string OrgRegimentName = "";
                        foreach (var org in orgList)
                        {
                            switch (org.OrgType)
                            {
                                case EnumOrgType.BattleTeam:
                                    OrgDivisionId = org.Id;
                                    OrgDivisionName = org.OrgName;
                                    break;
                                case EnumOrgType.Battalion:
                                    OrgBrigadeId = org.Id;
                                    OrgBrigadeName = org.OrgName;
                                    break;
                                case EnumOrgType.Squadron:
                                    OrgRegimentId = org.Id;
                                    OrgRegimentName = org.OrgName;
                                    break;
                            }
                        }
                        Db_crm_contract_collectioninfo_automatching automatching = new Db_crm_contract_collectioninfo_automatching();
                        automatching.ContractId = paymentinfo.First().ContractId;
                        automatching.ContractPaymentInfoId = paymentinfo.First().Id;
                        automatching.CollectionInfoId = collectionInfoId.ToString();
                        automatching.State = EnumAutoMatchingState.MatchedToBeConfirmed.ToInt();
                        automatching.IsAutoMatching = EnumIsAutoMatching.Auto.ToInt() == 1 ? true : false;
                        automatching.Issuer = contract.Issuer;
                        automatching.OrgDivisionId = OrgDivisionId;
                        automatching.OrgDivisionName = OrgDivisionName;
                        automatching.OrgBrigadeId = OrgBrigadeId;
                        automatching.OrgBrigadeName = OrgBrigadeName;
                        automatching.OrgRegimentId = OrgRegimentId;
                        automatching.OrgRegimentName = OrgRegimentName;

                        //240531 增加工作流
                        var autoMatchingId = DbOpe_crm_contract_collectioninfo_automatching.Instance.InsertDataReturnId(automatching);

                        //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", collectionInfoId.ToString(), item, "", EnumMatchingStatus.MatchingPendingRegistration.GetEnumDescription(), "银行到账自动匹配成功");
                    }
                    else
                    {
                        //240531 增加工作流
                        //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", collectionInfoId.ToString(), item, "", EnumMatchingStatus.ToBeMatched.GetEnumDescription(), "银行到账自动匹配失败");
                    }

                    if (co.CollectioninfoAttachFile != null && co.CollectioninfoAttachFile.Count > 0)
                    {
                        Util<DbOpe_crm_collectioninfo_attachfile, BM_AttachFile> contractAttachFile = new Util<DbOpe_crm_collectioninfo_attachfile, BM_AttachFile>(DbOpe_crm_collectioninfo_attachfile.Instance);
                        contractAttachFile.UploadFile(co.CollectioninfoAttachFile, EnumAttachFileType.TransactionReceipt.ToString(), UserId, collectionInfoId.ToString());
                    }
                }

                //DbOpe_crm_collectioninfo.Instance.InsertListData(collectioninfo);
            });
            return collectioninfo;
        }

        /// <summary>
        /// 修改回款信息
        /// </summary>
        public Db_crm_collectioninfo UpdateCollectionInfo(UpdateCollectionInfo_In updateCollectionInfo)
        {
            //if (updateCollectionInfo.TransactionReceipt != "" && updateCollectionInfo.TransactionReceipt != null)
            //{
            //    List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetDataList(r => r.TransactionReceipt == updateCollectionInfo.TransactionReceipt && r.Id != updateCollectionInfo.Id).ToList();
            //    if (list.Count > 0)
            //    {
            //        throw new ApiException("回款交易凭证信息已存在," + updateCollectionInfo.TransactionReceipt);
            //    }
            //}

            Db_crm_collectioninfo collectionInfo = DbOpe_crm_collectioninfo.Instance.GetDataById(updateCollectionInfo.Id);
            //if (collectionInfo.MatchingStatus == EnumMatchingStatus.Registered.ToInt())
            //{
            //    throw new ApiException("该回款信息已登记不可以修改");
            //}
            //银行到账列表中除待匹配状态或未维护的可以点击修改按钮； 其他都需要限制下：不允许修改
            if (!(collectionInfo.MatchingStatus == EnumMatchingStatus.ToBeMatched.ToInt() || collectionInfo.MaintenanceStatus == EnumMaintenanceStatus.Submit.ToInt()))
            {
                throw new ApiException("该回款信息已登记不可以修改");
            }
            if (collectionInfo.IsLocked == EnumLocked.Locked.ToInt())
            {
                throw new ApiException("该回款信息已锁定不可以修改");
            }

            Db_crm_collectioninfo item = new Db_crm_collectioninfo();
            //验证输入项
            List<UpdateCollectionInfo_In> updateCollectionInfo_Ins = new List<UpdateCollectionInfo_In>();
            updateCollectionInfo_Ins.Add(updateCollectionInfo);
            Validating(updateCollectionInfo_Ins.ConvertAll<CollectionInfo_In>(r => r));

            DbOpe_crm_collectioninfo.Instance.TransDeal(() =>
            {
                Db_crm_collectioninfo ol = DbOpe_crm_collectioninfo.Instance.GetDataById(updateCollectionInfo.Id);
                List<Db_crm_collectingcompany> collectingcompany = DbOpe_crm_collectingcompany.Instance.GetDataList(r => r.CollectingCompanyName == updateCollectionInfo.CollectingCompanyName);
                List<Db_crm_customer_subcompany> customersubcompany = DbOpe_crm_customer_subcompany.Instance.GetDataList(r => r.CompanyName == updateCollectionInfo.PaymentCompanyName && (r.IsValid == 1 || (r.IsValid == 0 && r.CustomerId == Guid.Empty.ToString())));


                item = updateCollectionInfo.MappingTo<Db_crm_collectioninfo>();
                item.SplitDate = ol.SplitDate;
                item.SplitRemark = ol.SplitRemark;
                item.VerifyConfirm = ol.VerifyConfirm;
                item.VerifyConfirmDate = ol.VerifyConfirmDate;
                item.UpdateRemark = ol.UpdateRemark;
                item.Remark = ol.Remark;

                List<Db_crm_collectingcompany> cc = collectingcompany.Where(r => r.CollectingCompanyName == item.CollectingCompanyName).ToList();
                if (cc.Count() > 0)
                {
                    item.CollectingCompany = cc.First().Id;
                }
                List<Db_crm_customer_subcompany> csc = customersubcompany.Where(r => r.CompanyName == item.PaymentCompanyName).ToList();
                if (csc.Count() > 0)
                {
                    item.PaymentCompany = csc.First().Id;
                }

                //List<Db_crm_contract_paymentinfo> paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetDataList(r =>
                //r.CollectingCompany == item.CollectingCompany &&
                //r.PaymentCompany == item.PaymentCompany &&
                //r.Currency == item.Currency &&
                //r.ArrivalAmount == item.ArrivalAmount &&
                //r.PaymentMethod == item.PaymentMethod &&
                //r.BankPaymentAmount == item.BankPaymentAmount &&
                //r.CashPaymentAmount == item.CashPaymentAmount);
                List<Db_crm_contract_paymentinfo> paymentinfo = new List<Db_crm_contract_paymentinfo>();
                if (item.Currency == EnumCurrency.CNY.ToInt())
                {
                    paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfoAndCollectioninfoID(updateCollectionInfo.Id, item.CollectingCompany, item.PaymentCompany, item.Currency, item.ArrivalAmount, item.PaymentMethod, item.BankPaymentAmount, item.CashPaymentAmount);
                }
                else
                {
                    paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfoAndCollectioninfoID(updateCollectionInfo.Id, item.CollectingCompany, item.PaymentCompany, item.Currency, item.FCArrivalAmount, item.PaymentMethod, item.BankPaymentAmount, item.CashPaymentAmount);
                }

                item.MatchingStatus = collectionInfo.MatchingStatus;
                item.IsManualMatching = collectionInfo.IsManualMatching;
                item.IsMatchingAll = collectionInfo.IsMatchingAll;

                //if (paymentinfo.Count > 0)
                //{
                //    item.IsDeposit = false;
                //    item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                //    item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //    item.IsMatchingAll = EnumIsMatchingAll.All.ToInt() == 1 ? true : false;
                //}
                //else
                //{
                //    if (!(collectionInfo.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt() && collectionInfo.IsManualMatching == true && collectionInfo.IsMatchingAll == false))
                //    {
                //        paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByPaymentCompanyAndCollectioninfoID(updateCollectionInfo.Id, item.PaymentCompany);
                //        if (paymentinfo.Count > 0)
                //        {
                //            bool IsDeposit = false;
                //            if (item.Currency == EnumCurrency.CNY.ToInt())
                //            {
                //                if ((item.ArrivalAmount.Value * 2) > paymentinfo.First().ArrivalAmount.Value || (item.ArrivalAmount.Value * 2) > paymentinfo.First().PlannedArrivalAmount.Value)
                //                {
                //                    IsDeposit = false;
                //                }
                //                else
                //                {
                //                    IsDeposit = true;
                //                }
                //            }
                //            else
                //            {
                //                if ((item.FCArrivalAmount.Value * 2) > paymentinfo.First().ArrivalAmount.Value || (item.FCArrivalAmount.Value * 2) > paymentinfo.First().PlannedArrivalAmount.Value)
                //                {
                //                    IsDeposit = false;
                //                }
                //                else
                //                {
                //                    IsDeposit = true;
                //                }
                //            }
                //            if (IsDeposit)
                //            {
                //                item.IsDeposit = true;
                //                item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                //                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                //            }
                //            else
                //            {
                //                item.IsDeposit = false;
                //                item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                //                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                //            }
                //        }
                //        else
                //        {
                //            item.IsDeposit = false;
                //            item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                //            item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //            item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                //        }
                //    }
                //    //item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                //    //item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //    //item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                //}
                if (item.IsDeposit == true)
                {
                    item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                    item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                }
                else
                {
                    if (paymentinfo.Count > 0)
                    {
                        item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                        item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                        item.IsMatchingAll = EnumIsMatchingAll.All.ToInt() == 1 ? true : false;
                    }
                    else
                    {
                        if (!(collectionInfo.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt() && collectionInfo.IsManualMatching == true && collectionInfo.IsMatchingAll == false))
                        {
                            paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByPaymentCompanyAndCollectioninfoID(updateCollectionInfo.Id, item.PaymentCompany);
                            if (paymentinfo.Count > 0)
                            {
                                item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                            }
                            else
                            {
                                item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                            }
                        }
                        //item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                        //item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                        //item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    }
                }

                item.MaintenanceStatus = EnumMaintenanceStatus.Pass.ToInt();

                int Enable = EnumExchangeState.Enable.ToInt();
                Db_sys_exchange exchange = DbOpe_sys_exchange.Instance.GetData(r => r.ExchangeDate == item.ArrivalDate && r.TargetCurrency == item.Currency && r.State == Enable);
                if (exchange != null)
                {
                    item.ExchangeRate = exchange.ExchangeRate;
                }
                item.IsLocked = EnumLocked.UnLocked.ToInt();

                DbOpe_crm_collectioninfo.Instance.UpdateData(item);

                //维护回款信息附件信息
                DbOpe_crm_collectioninfo_attachfile.Instance.DeleteCollectioninfoAttachFileList(updateCollectionInfo.CollectioninfoAttachFileInfo, updateCollectionInfo.Id);
                //上传回款信息附件
                if (updateCollectionInfo.CollectioninfoAttachFile != null && updateCollectionInfo.CollectioninfoAttachFile.Count > 0)
                {
                    Util<DbOpe_crm_collectioninfo_attachfile, BM_AttachFile> contractAttachFile = new Util<DbOpe_crm_collectioninfo_attachfile, BM_AttachFile>(DbOpe_crm_collectioninfo_attachfile.Instance);
                    contractAttachFile.UploadFile(updateCollectionInfo.CollectioninfoAttachFile, EnumAttachFileType.TransactionReceipt.ToString(), UserId, updateCollectionInfo.Id);
                }

                if (!(item.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt() && item.IsManualMatching == true && item.IsMatchingAll == false))
                {
                    //更新匹配信息
                    int MatchedToBeConfirmed = EnumAutoMatchingState.MatchedToBeConfirmed.ToInt();
                    Db_crm_contract_collectioninfo_automatching automatche = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetData(r => r.CollectionInfoId == item.Id && r.State == MatchedToBeConfirmed);
                    if (automatche != null)
                    {
                        //已匹配过，如果状态为不匹配则删除原有的，如果状态为匹配，则判断如果是新的匹配支付信息则删除原有的匹配信息在新增，如果不是新的则保持不变。
                        if (item.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt())
                        {
                            if (automatche.ContractPaymentInfoId != paymentinfo.First().Id)
                            {
                                DbOpe_crm_contract_collectioninfo_automatching.Instance.DeleteData(automatche.Id);

                                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(paymentinfo.First().ContractId);
                                //获取用户信息
                                var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                                //获取组织信息树，从当前组织追溯出所有上级组织
                                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                                string? OrgDivisionId = null;
                                string OrgDivisionName = "";
                                string? OrgBrigadeId = null;
                                string OrgBrigadeName = "";
                                string? OrgRegimentId = null;
                                string OrgRegimentName = "";
                                foreach (var org in orgList)
                                {
                                    switch (org.OrgType)
                                    {
                                        case EnumOrgType.BattleTeam:
                                            OrgDivisionId = org.Id;
                                            OrgDivisionName = org.OrgName;
                                            break;
                                        case EnumOrgType.Battalion:
                                            OrgBrigadeId = org.Id;
                                            OrgBrigadeName = org.OrgName;
                                            break;
                                        case EnumOrgType.Squadron:
                                            OrgRegimentId = org.Id;
                                            OrgRegimentName = org.OrgName;
                                            break;
                                    }
                                }

                                Db_crm_contract_collectioninfo_automatching automatching = new Db_crm_contract_collectioninfo_automatching();
                                automatching.ContractId = paymentinfo.First().ContractId;
                                automatching.ContractPaymentInfoId = paymentinfo.First().Id;
                                automatching.CollectionInfoId = item.Id.ToString();
                                automatching.State = EnumAutoMatchingState.MatchedToBeConfirmed.ToInt();
                                automatching.IsAutoMatching = EnumIsAutoMatching.Auto.ToInt() == 1 ? true : false;
                                automatching.Issuer = contract.Issuer;
                                automatching.OrgDivisionId = OrgDivisionId;
                                automatching.OrgDivisionName = OrgDivisionName;
                                automatching.OrgBrigadeId = OrgBrigadeId;
                                automatching.OrgBrigadeName = OrgBrigadeName;
                                automatching.OrgRegimentId = OrgRegimentId;
                                automatching.OrgRegimentName = OrgRegimentName;
                                DbOpe_crm_contract_collectioninfo_automatching.Instance.InsertData(automatching);
                            }
                        }
                        else
                        {
                            DbOpe_crm_contract_collectioninfo_automatching.Instance.DeleteData(automatche.Id);
                        }
                    }
                    else
                    {
                        //未匹配过，如果状态为匹配则添加
                        if (item.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt())
                        {
                            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(paymentinfo.First().ContractId);
                            //获取用户信息
                            var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                            //获取组织信息树，从当前组织追溯出所有上级组织
                            var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                            string? OrgDivisionId = null;
                            string OrgDivisionName = "";
                            string? OrgBrigadeId = null;
                            string OrgBrigadeName = "";
                            string? OrgRegimentId = null;
                            string OrgRegimentName = "";
                            foreach (var org in orgList)
                            {
                                switch (org.OrgType)
                                {
                                    case EnumOrgType.BattleTeam:
                                        OrgDivisionId = org.Id;
                                        OrgDivisionName = org.OrgName;
                                        break;
                                    case EnumOrgType.Battalion:
                                        OrgBrigadeId = org.Id;
                                        OrgBrigadeName = org.OrgName;
                                        break;
                                    case EnumOrgType.Squadron:
                                        OrgRegimentId = org.Id;
                                        OrgRegimentName = org.OrgName;
                                        break;
                                }
                            }
                            Db_crm_contract_collectioninfo_automatching automatching = new Db_crm_contract_collectioninfo_automatching();
                            automatching.ContractId = paymentinfo.First().ContractId;
                            automatching.ContractPaymentInfoId = paymentinfo.First().Id;
                            automatching.CollectionInfoId = item.Id.ToString();
                            automatching.State = EnumAutoMatchingState.MatchedToBeConfirmed.ToInt();
                            automatching.IsAutoMatching = EnumIsAutoMatching.Auto.ToInt() == 1 ? true : false;
                            automatching.Issuer = contract.Issuer;
                            automatching.OrgDivisionId = OrgDivisionId;
                            automatching.OrgDivisionName = OrgDivisionName;
                            automatching.OrgBrigadeId = OrgBrigadeId;
                            automatching.OrgBrigadeName = OrgBrigadeName;
                            automatching.OrgRegimentId = OrgRegimentId;
                            automatching.OrgRegimentName = OrgRegimentName;
                            DbOpe_crm_contract_collectioninfo_automatching.Instance.InsertData(automatching);
                            //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", item.Id, item, "", EnumMatchingStatus.MatchingPendingRegistration.GetEnumDescription(), "银行到账自动匹配成功");
                        }
                        else
                        {
                            //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", item.Id, item, "", EnumMatchingStatus.ToBeMatched.GetEnumDescription(), "银行到账自动匹配失败");
                        }
                    }
                }
            });
            return item;
        }

        /// <summary>
        /// 删除回款信息
        /// </summary>
        public void DeleteCollectionInfo(List<string> ids)
        {
            DbOpe_crm_contract.Instance.TransDeal(() =>
            {
                int Registered = EnumMatchingStatus.Registered.ToInt();
                int MatchingPendingRegistration = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                List<Db_crm_collectioninfo> collectioninfo = DbOpe_crm_collectioninfo.Instance.GetDataList(r => ids.Contains(r.Id) && (r.MatchingStatus == Registered || r.MatchingStatus == MatchingPendingRegistration));
                if (collectioninfo.Count() > 0)
                {
                    throw new ApiException("该回款信息已登记或已匹配待登记不可以删除");
                }
                DbOpe_crm_collectioninfo.Instance.DeleteData(ids);
                DbOpe_crm_contract_collectioninfo_automatching.Instance.DeleteData(r => ids.Contains(r.CollectionInfoId));
                //删除回款信息附件
                DbOpe_crm_collectioninfo_attachfile.Instance.DeleteCollectioninfoAttachFileByCollectionInfoId(ids);
            });
        }

        /// <summary>
        /// 根据回款信息Id获取回款信息
        /// </summary>
        public CollectionInfo_Out GetCollectionInfoById(string id)
        {
            return DbOpe_crm_collectioninfo.Instance.GetCollectionInfoById(id);
        }

        /// <summary>
        /// 根据查询条件获取回款信息列表
        /// </summary>
        public ApiTableOut<SearchCollectionInfoList_Out> SearchCollectionInfoList(SearchCollectionInfoList_In searchCollectionInfoListIn)
        {
            int total = 0;
            return new ApiTableOut<SearchCollectionInfoList_Out> { Data = DbOpe_crm_collectioninfo.Instance.SearchCollectionInfoList(searchCollectionInfoListIn, UserId, ref total, true), Total = total };
        }

        /// <summary>
        /// 根据查询条件获取回款信息统计
        /// </summary>
        public SearchCollectionInfoListSta_Out SearchCollectionInfoVerifyConfirmListSta(SearchCollectionInfoList_In searchCollectionInfoListIn)
        {
            return DbOpe_crm_collectioninfo.Instance.SearchCollectionInfoVerifyConfirmListSta(searchCollectionInfoListIn, UserId, true);
        }

        /// <summary>
        /// 根据查询条件获取回款信息列表
        /// </summary>
        public ApiTableOut<SearchCollectionInfoVerifyConfirmList_Out> SearchCollectionInfoVerifyConfirmList(SearchCollectionInfoList_In searchCollectionInfoListIn)
        {
            int total = 0;
            return new ApiTableOut<SearchCollectionInfoVerifyConfirmList_Out> { Data = DbOpe_crm_collectioninfo.Instance.SearchCollectionInfoVerifyConfirmList(searchCollectionInfoListIn, UserId, ref total, true), Total = total };
        }

        /// <summary>
        /// 识别回款信息
        /// </summary>
        public IdentifyCollectionInfo_Out IdentifyCollectionInfo(string transactionReceipt)
        {
            IdentifyCollectionInfo_Out result = new IdentifyCollectionInfo_Out();
            //浦发银行：23年1月13日08: 39贵公司帐户尾号1401转入人民币38,800.00余额xxxx[互联汇入**************]服务费,尾号8850山东奇康机械有限公司【浦发银行】
            //中国银行：您公司账户4541,于********收入(网银网上支付)人民币15000.00当前余额xxxx,对方为河北匠塑化工科技有限公司(账号*0650)（我行吸收的本外币存款依照《存款保险条例》受到保护）【中国银行】
            //稠州银行：【稠州银行】您尾号1582的账户于01月12日17:59转存收入USD7800.00汇款行:KASIKORNBANK PUBLIC COMPANY LIMITED,BANGKOK,汇款人:BAGS AND GLOVES CO.,LTD.. 备注:转存
            //【宁波银行】宁波银行短信通知：【温馨提示】您有一笔来自 (JW)STSBILISIMDANISMANLIKSAN.VETIGUNALSOK.ACIBADEMMAH.NO:21GTR/ISTANBULTR/********** 的汇入汇款USD8,500.00待确认，请在3日内登陆宁波银行企业APP进行汇入汇款确认。

            //string pfstring = "23年1月13日08: 39贵公司帐户尾号1401转入人民币38,800.00余额xxxx[互联汇入**************]服务费,尾号8850山东奇康机械有限公司【浦发银行】";
            Regex pfr = new Regex("【浦发银行】", RegexOptions.IgnoreCase);
            bool isMatchedPFR = pfr.IsMatch(transactionReceipt);
            if (isMatchedPFR)
            {
                Regex reg = new Regex("(?<key1>.*?)贵公司帐户尾号(?<key2>.*?)转入(?<key3>.*?)余额(?<key4>.*?),尾号(?<key5>.*?)【浦发银行】");
                Match match = reg.Match(transactionReceipt);
                string ArrivalDate = match.Groups["key1"].Value;
                string CollectingCompany = match.Groups["key2"].Value;
                string ArrivalAmount = match.Groups["key3"].Value;
                string PaymentCompanyName = match.Groups["key5"].Value;

                if (ArrivalDate != "")
                {
                    Match ma = new Regex(@"^((?<year>\d{2,4})年)?(?<month>\d{1,2})月((?<day>\d{1,2})日)?((?<hour>\d{1,2}))(时|点|:)?((?<minute>\d{1,2}))?$").Match(ArrivalDate);
                    if (ma.Success)
                    {
                        try
                        {
                            result.ArrivalDate = DateTime.Parse(ma.Groups[0].Value);
                        }
                        catch (Exception e)
                        {
                            throw new ApiException("到账日期不正确");
                        }
                    }
                }
                if (CollectingCompany != "")
                {
                    if (CollectingCompany == "1401")
                    {
                        result.CollectingCompanyName = "济南数据";
                    }
                    if (CollectingCompany == "1430")
                    {
                        result.CollectingCompanyName = "南京数据";
                    }
                    if (CollectingCompany == "1108")
                    {
                        result.CollectingCompanyName = "青岛数据";
                    }
                }
                if (ArrivalAmount != "")
                {
                    Match ma = new Regex(@"([\d\.]+)").Match(ArrivalAmount.Replace(",", ""));
                    if (ma.Success)
                    {
                        result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        result.Currency = EnumCurrency.CNY.ToInt();
                        result.PaymentMethod = EnumPaymentMethod.Bank.ToInt();
                        result.BankPaymentAmount = decimal.Parse(ma.Groups[0].Value);
                    }
                }
                if (PaymentCompanyName != "")
                {
                    //Match ma = new Regex(@"([\d]{4})[\w./]+").Match(PaymentCompanyName);
                    //if (ma.Success)
                    //{
                    //    result.PaymentCompanyName = ma.Groups[0].Value.Substring(4);
                    //}
                    result.PaymentCompanyName = PaymentCompanyName.Substring(4);
                }
            }

            //string zgrstring = "您公司账户4541,于********收入(网银网上支付)人民币15000.00当前余额xxxx,对方为河北匠塑化工科技有限公司(账号*0650)（我行吸收的本外币存款依照《存款保险条例》受到保护）【中国银行】";
            Regex zgr = new Regex("【中国银行】", RegexOptions.IgnoreCase);
            bool isMatchedZGR = zgr.IsMatch(transactionReceipt);
            if (isMatchedZGR)
            {
                Regex reg = new Regex("您公司账户(?<key1>.*?),于(?<key2>.*?)收入(?<key3>.*?)当前余额(?<key4>.*?),对方为(?<key5>.*?)（我行吸收的本外币存款依照《存款保险条例》受到保护）【中国银行】");
                Match match = reg.Match(transactionReceipt);
                string CollectingCompany = match.Groups["key1"].Value;
                string ArrivalDate = match.Groups["key2"].Value;
                string ArrivalAmount = match.Groups["key3"].Value;
                string PaymentCompanyName = match.Groups["key5"].Value;

                if (CollectingCompany != "")
                {
                    if (CollectingCompany == "4541")
                    {
                        result.CollectingCompanyName = "信息技术";
                    }
                    if (CollectingCompany == "4472")
                    {
                        result.CollectingCompanyName = "科技发展";
                    }
                    if (CollectingCompany == "4579")
                    {
                        result.CollectingCompanyName = "数据技术";
                    }
                    if (CollectingCompany == "3630")
                    {
                        result.CollectingCompanyName = "国际资讯";
                    }
                }
                if (ArrivalDate != "")
                {
                    Match ma = new Regex(@"^(?<month>\d{2})(?<day>\d{2})(?<hour>\d{2})(?<minute>\d{2})?$").Match(ArrivalDate);
                    if (ma.Success)
                    {
                        try
                        {
                            string date = DateTime.Now.Year.ToString() + "/" + ma.Groups[0].Value.Substring(0, 2) + "/" + ma.Groups[0].Value.Substring(2, 2) + " " + ma.Groups[0].Value.Substring(4, 2) + ":" + ma.Groups[0].Value.Substring(6, 2) + ":00";
                            result.ArrivalDate = DateTime.Parse(date);
                        }
                        catch (Exception e)
                        {
                            throw new ApiException("到账日期不正确");
                        }
                    }
                }
                if (ArrivalAmount != "")
                {
                    Match ma = new Regex(@"([\d\.]+)").Match(ArrivalAmount.Replace(",", ""));
                    if (ma.Success)
                    {
                        result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        result.Currency = EnumCurrency.CNY.ToInt();
                        result.PaymentMethod = EnumPaymentMethod.Bank.ToInt();
                        result.BankPaymentAmount = decimal.Parse(ma.Groups[0].Value);
                    }
                }
                if (PaymentCompanyName != "")
                {
                    result.PaymentCompanyName = PaymentCompanyName.Substring(0, PaymentCompanyName.LastIndexOf("("));
                }
            }

            //string czstring = "【稠州银行】您尾号1582的账户于01月12日17:59转存收入USD7800.00汇款行:KASIKORNBANK PUBLIC COMPANY LIMITED,BANGKOK,汇款人:BAGS AND GLOVES CO.,LTD.. 备注:转存";
            Regex cz = new Regex("【稠州银行】", RegexOptions.IgnoreCase);
            bool isMatchedCZR = cz.IsMatch(transactionReceipt);
            if (isMatchedCZR)
            {
                Regex reg = new Regex("【稠州银行】您尾号(?<key1>.*?)的账户于(?<key2>.*?)转存收入(?<key3>.*?)汇款行:(?<key4>.*?),汇款人:(?<key5>.*?) 备注:转存");
                Match match = reg.Match(transactionReceipt);
                string CollectingCompany = match.Groups["key1"].Value;
                string ArrivalDate = match.Groups["key2"].Value;
                string ArrivalAmount = match.Groups["key3"].Value;
                string PaymentCompanyName = match.Groups["key5"].Value;

                if (CollectingCompany != "")
                {
                    if (CollectingCompany == "1582")
                    {
                        result.CollectingCompanyName = "GLOBALWITS";
                    }
                    if (CollectingCompany == "0666")
                    {
                        result.CollectingCompanyName = "GLOBALWITS";
                    }
                }
                if (ArrivalDate != "")
                {
                    Match ma = new Regex(@"^(?<month>\d{1,2})月((?<day>\d{1,2})日)?((?<hour>\d{1,2}))(时|点|:)?((?<minute>\d{1,2}))?$").Match(ArrivalDate);
                    if (ma.Success)
                    {
                        try
                        {
                            result.ArrivalDate = DateTime.Parse(DateTime.Now.Year.ToString() + "年" + ma.Groups[0].Value);
                        }
                        catch (Exception e)
                        {
                            throw new ApiException("到账日期不正确");
                        }
                    }
                }
                if (ArrivalAmount != "")
                {
                    Match ma = new Regex(@"([\d\.]+)").Match(ArrivalAmount.Replace(",", ""));
                    if (ma.Success)
                    {
                        //result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        result.PaymentMethod = EnumPaymentMethod.Bank.ToInt();
                        result.BankPaymentAmount = decimal.Parse(ma.Groups[0].Value);
                        if (ArrivalAmount.IndexOf("CNY") > -1)
                        {
                            result.Currency = EnumCurrency.CNY.ToInt();
                            result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        }
                        if (ArrivalAmount.IndexOf("USD") > -1)
                        {
                            result.Currency = EnumCurrency.USD.ToInt();
                            result.FCArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        }
                        if (ArrivalAmount.IndexOf("EUR") > -1)
                        {
                            result.Currency = EnumCurrency.EUR.ToInt();
                            result.FCArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        }
                    }
                }
                if (PaymentCompanyName != "")
                {
                    result.PaymentCompanyName = PaymentCompanyName;
                }
            }

            //【宁波银行】宁波银行短信通知：【温馨提示】您有一笔来自 (JW)STSBILISIMDANISMANLIKSAN.VETIGUNALSOK.ACIBADEMMAH.NO:21GTR/ISTANBULTR/********** 的汇入汇款USD8,500.00待确认，请在3日内登陆宁波银行企业APP进行汇入汇款确认。
            //string nbstring = "【宁波银行】宁波银行短信通知：【温馨提示】您有一笔来自 (JW)STSBILISIMDANISMANLIKSAN.VETIGUNALSOK.ACIBADEMMAH.NO:21GTR/ISTANBULTR/********** 的汇入汇款USD8,500.00待确认，请在3日内登陆宁波银行企业APP进行汇入汇款确认。";
            Regex nb = new Regex("【宁波银行】", RegexOptions.IgnoreCase);
            bool isMatchedNBR = nb.IsMatch(transactionReceipt);
            if (isMatchedNBR)
            {
                Regex reg = new Regex("【宁波银行】宁波银行短信通知：【温馨提示】您有一笔来自(?<key1>.*?)的汇入汇款(?<key2>.*?)待确认，请在3日内登陆宁波银行企业APP进行汇入汇款确认。");
                Match match = reg.Match(transactionReceipt);
                string CollectingCompany = "数据技术(宁波银行)";
                DateTime ArrivalDate = DateTime.Now;
                string ArrivalAmount = match.Groups["key2"].Value;
                //string PaymentCompanyName = match.Groups["key5"].Value;

                //if (CollectingCompany != "")
                //{
                //    if (CollectingCompany == "1582")
                //    {
                //        result.CollectingCompanyName = "GLOBALWITS";
                //    }
                //    if (CollectingCompany == "0666")
                //    {
                //        result.CollectingCompanyName = "GLOBALWITS";
                //    }
                //}
                result.CollectingCompanyName = CollectingCompany;
                //if (ArrivalDate != "")
                //{
                //    Match ma = new Regex(@"^(?<month>\d{1,2})月((?<day>\d{1,2})日)?((?<hour>\d{1,2}))(时|点|:)?((?<minute>\d{1,2}))?$").Match(ArrivalDate);
                //    if (ma.Success)
                //    {
                //        try
                //        {
                //            result.ArrivalDate = DateTime.Parse(DateTime.Now.Year.ToString() + "年" + ma.Groups[0].Value);
                //        }
                //        catch (Exception e)
                //        {
                //            throw new ApiException("到账日期不正确");
                //        }
                //    }
                //}
                result.ArrivalDate = ArrivalDate;
                if (ArrivalAmount != "")
                {
                    Match ma = new Regex(@"([\d\.]+)").Match(ArrivalAmount.Replace(",", ""));
                    if (ma.Success)
                    {
                        //result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        result.PaymentMethod = EnumPaymentMethod.Bank.ToInt();
                        result.BankPaymentAmount = decimal.Parse(ma.Groups[0].Value);
                        if (ArrivalAmount.IndexOf("CNY") > -1)
                        {
                            result.Currency = EnumCurrency.CNY.ToInt();
                            result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        }
                        if (ArrivalAmount.IndexOf("USD") > -1)
                        {
                            result.Currency = EnumCurrency.USD.ToInt();
                            result.FCArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        }
                        if (ArrivalAmount.IndexOf("EUR") > -1)
                        {
                            result.Currency = EnumCurrency.EUR.ToInt();
                            result.FCArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                        }
                    }
                }
                //if (PaymentCompanyName != "")
                //{
                //    result.PaymentCompanyName = PaymentCompanyName;
                //}
            }

            if (result.ArrivalDate != null || result.CollectingCompanyName.IsNotNullOrEmpty() || result.ArrivalAmount != null || (isMatchedNBR || result.PaymentCompanyName.IsNotNullOrEmpty()))
            {
                result.IdentifyStatus = EnumIdentifyStatus.Success;
                CollectionInfoIsDeposit_In CollectionInfoIsDepositIn = new CollectionInfoIsDeposit_In
                {
                    ArrivalAmount = result.ArrivalAmount == null ? 0 : result.ArrivalAmount.Value,
                    BankPaymentAmount = result.BankPaymentAmount,
                    CashPaymentAmount = result.CashPaymentAmount,
                    CollectingCompanyName = result.CollectingCompanyName,
                    Currency = result.Currency.Value,
                    FCArrivalAmount = result.FCArrivalAmount,
                    PaymentCompanyName = result.PaymentCompanyName,
                    PaymentMethod = result.PaymentMethod.Value
                };
                result.IsDeposit = GetIsDeposit(CollectionInfoIsDepositIn);
            }
            else
            {
                result.IdentifyStatus = EnumIdentifyStatus.Fail;
            }

            return result;
        }

        /// <summary>
        /// 识别回款信息
        /// </summary>
        public IdentifyCollectionInfo_Out IdentifyCollectionInfoList(string transactionReceipt)
        {
            IdentifyCollectionInfo_Out result = new IdentifyCollectionInfo_Out();
            List<Db_sys_collectioninfo_template> list = DbOpe_sys_collectioninfo_template.Instance.GetDataAllList().ToList();
            foreach (Db_sys_collectioninfo_template item in list)
            {
                Regex reg = new Regex(item.IsMatch, RegexOptions.IgnoreCase);
                bool isMatchedREG = reg.IsMatch(transactionReceipt);
                if (isMatchedREG)
                {
                    Regex regitem = new Regex(item.ContentRegex);
                    List<Db_sys_collectioninfo_template_item> itemlist = DbOpe_sys_collectioninfo_template_item.Instance.GetDataList(r => r.CollectioninfoTemplateId == item.Id).ToList();
                    Match match = regitem.Match(transactionReceipt);
                    foreach (Db_sys_collectioninfo_template_item it in itemlist)
                    {
                        if (it.Item == "ArrivalDate")
                        {
                            string ArrivalDate = match.Groups[it.Key].Value;
                            if (ArrivalDate != "")
                            {
                                Match ma = new Regex(@"^((?<year>\d{2,4})年)?(?<month>\d{1,2})月((?<day>\d{1,2})日)?((?<hour>\d{1,2}))(时|点|:)?((?<minute>\d{1,2}))?$").Match(ArrivalDate);
                                if (ma.Success)
                                {
                                    try
                                    {
                                        if (ma.Groups[0].Value.Split("年")[0].Length == 2)
                                        {
                                            result.ArrivalDate = DateTime.Parse("20" + ma.Groups[0].Value);
                                        }
                                        else
                                        {
                                            result.ArrivalDate = DateTime.Parse(ma.Groups[0].Value);
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        throw new ApiException("到账日期不正确");
                                    }
                                }
                                ma = new Regex(@"^(?<month>\d{2})(?<day>\d{2})(?<hour>\d{2})(?<minute>\d{2})?$").Match(ArrivalDate);
                                if (ma.Success)
                                {
                                    try
                                    {
                                        string date = DateTime.Now.Year.ToString() + "/" + ma.Groups[0].Value.Substring(0, 2) + "/" + ma.Groups[0].Value.Substring(2, 2) + " " + ma.Groups[0].Value.Substring(4, 2) + ":" + ma.Groups[0].Value.Substring(6, 2) + ":00";
                                        result.ArrivalDate = DateTime.Parse(date);
                                    }
                                    catch (Exception e)
                                    {
                                        throw new ApiException("到账日期不正确");
                                    }
                                }
                                ma = new Regex(@"^(?<month>\d{1,2})月((?<day>\d{1,2})日)?((?<hour>\d{1,2}))(时|点|:)?((?<minute>\d{1,2}))?$").Match(ArrivalDate);
                                if (ma.Success)
                                {
                                    try
                                    {
                                        result.ArrivalDate = DateTime.Parse(DateTime.Now.Year.ToString() + "年" + ma.Groups[0].Value);
                                    }
                                    catch (Exception e)
                                    {
                                        throw new ApiException("到账日期不正确");
                                    }
                                }
                                ma = new Regex(@"^(?<month>\d{1,2})月((?<day>\d{1,2})日)?$").Match(ArrivalDate);
                                if (ma.Success)
                                {
                                    try
                                    {
                                        result.ArrivalDate = DateTime.Parse(DateTime.Now.Year.ToString() + "年" + ma.Groups[0].Value);
                                    }
                                    catch (Exception e)
                                    {
                                        throw new ApiException("到账日期不正确");
                                    }
                                }
                            }
                        }

                        if (it.Item == "CollectingCompany")
                        {
                            string CollectingCompany = match.Groups[it.Key].Value;
                            if (CollectingCompany != "")
                            {
                                #region 修改为数据库配置
                                //if (item.IsMatch == "【中国银行】")
                                //{
                                //    if (CollectingCompany == "4541")
                                //    {
                                //        result.CollectingCompanyName = "信息技术";
                                //    }
                                //    if (CollectingCompany == "4579")
                                //    {
                                //        result.CollectingCompanyName = "数据技术";
                                //    }
                                //    if (CollectingCompany == "3630")
                                //    {
                                //        result.CollectingCompanyName = "国际资讯";
                                //    }
                                //    if (CollectingCompany == "4472")
                                //    {
                                //        result.CollectingCompanyName = "科技发展";
                                //    }
                                //    //增加 2024年4月19日
                                //    if (CollectingCompany == "3493")
                                //    {
                                //        result.CollectingCompanyName = "科技发展";
                                //    }
                                //    if (CollectingCompany == "9686")
                                //    {
                                //        result.CollectingCompanyName = "信息技术";
                                //    }
                                //    if (CollectingCompany == "3147")
                                //    {
                                //        result.CollectingCompanyName = "国际资讯";
                                //    }
                                //}
                                //if (item.IsMatch == "【宁波银行】")
                                //{
                                //    if (CollectingCompany == "0431")
                                //    {
                                //        result.CollectingCompanyName = "数据技术";
                                //    }
                                //    if (CollectingCompany == "0236")
                                //    {
                                //        result.CollectingCompanyName = "数据技术";
                                //    }
                                //    if (CollectingCompany == "4170")
                                //    {
                                //        result.CollectingCompanyName = "数据技术";
                                //    }
                                //}
                                //if (item.IsMatch == "【浦发银行】")
                                //{
                                //    if (CollectingCompany == "1108")
                                //    {
                                //        result.CollectingCompanyName = "青岛数据";
                                //    }
                                //    if (CollectingCompany == "1430")
                                //    {
                                //        result.CollectingCompanyName = "南京数据";
                                //    }
                                //    if (CollectingCompany == "1401")
                                //    {
                                //        result.CollectingCompanyName = "济南数据";
                                //    }

                                //    //增加 2024年4月19日
                                //    if (CollectingCompany == "2371")
                                //    {
                                //        result.CollectingCompanyName = "青岛数据";
                                //    }
                                //    if (CollectingCompany == "2374")
                                //    {
                                //        result.CollectingCompanyName = "青岛数据";
                                //    }
                                //    if (CollectingCompany == "1993")
                                //    {
                                //        result.CollectingCompanyName = "南京数据";
                                //    }
                                //    if (CollectingCompany == "1994")
                                //    {
                                //        result.CollectingCompanyName = "南京数据";
                                //    }
                                //    if (CollectingCompany == "3718")
                                //    {
                                //        result.CollectingCompanyName = "济南数据";
                                //    }
                                //    if (CollectingCompany == "3717")
                                //    {
                                //        result.CollectingCompanyName = "济南数据";
                                //    }
                                //}
                                //if (item.IsMatch == "【稠州银行】")
                                //{
                                //    if (CollectingCompany == "1582")
                                //    {
                                //        result.CollectingCompanyName = "GLOBALWITS";
                                //    }
                                //    if (CollectingCompany == "0666")
                                //    {
                                //        result.CollectingCompanyName = "GLOBALWITS";
                                //    }
                                //}
                                #endregion
                                //if (item.IsMatch == "【中国银行】")
                                string CollectingBankName = item.IsMatch.Substring(1, 4);
                                result.CollectingBankName = CollectingBankName;
                                result.BankAccountNumber = CollectingCompany;
                                List<string> hasValue = DbOpe_crm_collectingcompany_bank.Instance.GetCollectingCompanyNameByBankNumber(CollectingBankName, CollectingCompany);
                                if (!hasValue.IsNullOrEmpty())
                                {
                                    result.CollectingCompanyName = hasValue.FirstOrDefault();
                                }
                                else
                                {
                                    result.CollectingCompanyName = null;
                                }

                            }
                        }

                        if (it.Item == "ArrivalAmount")
                        {
                            string ArrivalAmount = match.Groups["ArrivalAmount"].Value;
                            if (ArrivalAmount != "")
                            {
                                //Match ma = new Regex(@"([\d\.]+)").Match(ArrivalAmount.Replace(",", ""));
                                //if (ma.Success)
                                //{
                                //    result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                //    result.Currency = EnumCurrency.CNY.ToInt();
                                //    result.PaymentMethod = EnumPaymentMethod.Bank.ToInt();
                                //    result.BankPaymentAmount = decimal.Parse(ma.Groups[0].Value);
                                //}
                                //Match ma = new Regex(@"([\d\.]+)").Match(ArrivalAmount.Replace(",", ""));
                                //if (ma.Success)
                                //{
                                //    result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                //    result.Currency = EnumCurrency.CNY.ToInt();
                                //    result.PaymentMethod = EnumPaymentMethod.Bank.ToInt();
                                //    result.BankPaymentAmount = decimal.Parse(ma.Groups[0].Value);
                                //}
                                Match ma = new Regex(@"([\d\.]+)").Match(ArrivalAmount.Replace(",", ""));
                                if (ma.Success)
                                {
                                    //result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                    result.PaymentMethod = EnumPaymentMethod.Bank.ToInt();
                                    result.BankPaymentAmount = decimal.Parse(ma.Groups[0].Value);
                                    if (ArrivalAmount.IndexOf("CNY") > -1 || ArrivalAmount.IndexOf("人民币") > -1)
                                    {
                                        result.Currency = EnumCurrency.CNY.ToInt();
                                        result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                    }
                                    else if (ArrivalAmount.IndexOf("USD") > -1 || ArrivalAmount.IndexOf("美元") > -1)
                                    {
                                        result.Currency = EnumCurrency.USD.ToInt();
                                        result.FCArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                    }
                                    else if (ArrivalAmount.IndexOf("EUR") > -1 || ArrivalAmount.IndexOf("欧元") > -1)
                                    {
                                        result.Currency = EnumCurrency.EUR.ToInt();
                                        result.FCArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                    }
                                    else if (ArrivalAmount.IndexOf("元") > -1)
                                    {
                                        result.Currency = EnumCurrency.CNY.ToInt();
                                        result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                    }
                                }

                                //ma = new Regex(@"([\d\.]+)").Match(ArrivalAmount.Replace(",", ""));
                                //if (ma.Success)
                                //{
                                //    //result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                //    result.PaymentMethod = EnumPaymentMethod.Bank.ToInt();
                                //    result.BankPaymentAmount = decimal.Parse(ma.Groups[0].Value);
                                //    if (ArrivalAmount.IndexOf("CNY") > -1)
                                //    {
                                //        result.Currency = EnumCurrency.CNY.ToInt();
                                //        result.ArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                //    }
                                //    if (ArrivalAmount.IndexOf("USD") > -1)
                                //    {
                                //        result.Currency = EnumCurrency.USD.ToInt();
                                //        result.FCArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                //    }
                                //    if (ArrivalAmount.IndexOf("EUR") > -1)
                                //    {
                                //        result.Currency = EnumCurrency.EUR.ToInt();
                                //        result.FCArrivalAmount = decimal.Parse(ma.Groups[0].Value);
                                //    }
                                //}
                            }
                        }

                        if (it.Item == "PaymentCompanyName")
                        {
                            string PaymentCompanyName = match.Groups["PaymentCompanyName"].Value;
                            if (PaymentCompanyName != "")
                            {
                                //Match ma = new Regex(@"([\d]{4})[\w./]+").Match(PaymentCompanyName);
                                //if (ma.Success)
                                //{
                                //    result.PaymentCompanyName = ma.Groups[0].Value.Substring(4);
                                //}
                                if (item.IsMatch == "【浦发银行】")
                                {
                                    Match ma = new Regex(@"[\d]{4}[\w./]+").Match(PaymentCompanyName);
                                    if (ma.Success)
                                    {
                                        result.PaymentCompanyName = ma.Groups[0].Value.Substring(4);
                                    }
                                    else
                                    {
                                        result.PaymentCompanyName = PaymentCompanyName;
                                    }
                                }
                                else
                                {
                                    if (PaymentCompanyName.LastIndexOf("(") > -1)
                                    {
                                        if (PaymentCompanyName.LastIndexOf("(账号") > -1)
                                            result.PaymentCompanyName = PaymentCompanyName.Substring(0, PaymentCompanyName.LastIndexOf("(账号"));
                                        else
                                            if (PaymentCompanyName.LastIndexOf("（账号") > -1)
                                        {
                                            result.PaymentCompanyName = PaymentCompanyName.Substring(0, PaymentCompanyName.LastIndexOf("（账号"));
                                        }
                                        else
                                        {
                                            result.PaymentCompanyName = PaymentCompanyName;
                                        }
                                    }
                                    else if (PaymentCompanyName.LastIndexOf("（") > -1)
                                    {
                                        if (PaymentCompanyName.LastIndexOf("(账号") > -1)
                                            result.PaymentCompanyName = PaymentCompanyName.Substring(0, PaymentCompanyName.LastIndexOf("(账号"));
                                        else
                                            if (PaymentCompanyName.LastIndexOf("（账号") > -1)
                                        {
                                            result.PaymentCompanyName = PaymentCompanyName.Substring(0, PaymentCompanyName.LastIndexOf("（账号"));
                                        }
                                        else
                                        {
                                            result.PaymentCompanyName = PaymentCompanyName;
                                        }
                                    }
                                    else
                                    {
                                        result.PaymentCompanyName = PaymentCompanyName;
                                    }
                                }
                                result.PaymentCompany = Regex.IsMatch(result.PaymentCompanyName, @"[a-zA-Z]") ? result.PaymentCompanyName : result.PaymentCompanyName.Replace("(", "（").Replace(")", "）");
                                result.PaymentCompanyName = result.PaymentCompany;
                            }
                        }
                    }
                }
                if (result.ArrivalDate != null || result.CollectingCompanyName.IsNotNullOrEmpty() || result.ArrivalAmount != null || result.FCArrivalAmount != null || (result.PaymentCompanyName.IsNotNullOrEmpty()))
                {
                    break;
                }
            }

            if (result.ArrivalDate != null || result.CollectingCompanyName.IsNotNullOrEmpty() || result.ArrivalAmount != null || result.FCArrivalAmount != null || (result.PaymentCompanyName.IsNotNullOrEmpty()))
            {
                if (result.ArrivalDate != null && result.Currency != null && result.FCArrivalAmount != null)
                {
                    int Enable = EnumExchangeState.Enable.ToInt();
                    Db_sys_exchange exchange = DbOpe_sys_exchange.Instance.GetData(r => SqlFunc.ToDateShort(r.ExchangeDate) == SqlFunc.ToDateShort(result.ArrivalDate) && r.TargetCurrency == result.Currency && r.State == Enable);
                    if (exchange != null)
                    {
                        //result.ArrivalAmount = Math.Round(result.FCArrivalAmount.Value * exchange.ExchangeRate.Value, 2);
                        result.ArrivalAmount = Math.Floor(result.FCArrivalAmount.Value * exchange.ExchangeRate.Value);
                    }
                }
                result.IdentifyStatus = EnumIdentifyStatus.Success;

                CollectionInfoIsDeposit_In CollectionInfoIsDepositIn = new CollectionInfoIsDeposit_In
                {
                    ArrivalAmount = result.ArrivalAmount == null ? 0 : result.ArrivalAmount.Value,
                    BankPaymentAmount = result.BankPaymentAmount,
                    CashPaymentAmount = result.CashPaymentAmount,
                    CollectingCompanyName = result.CollectingCompanyName,
                    Currency = result.Currency.Value,
                    FCArrivalAmount = result.FCArrivalAmount,
                    PaymentCompanyName = result.PaymentCompanyName,
                    PaymentMethod = result.PaymentMethod.Value
                };
                result.IsDeposit = GetIsDeposit(CollectionInfoIsDepositIn);
            }
            else
            {
                result.IdentifyStatus = EnumIdentifyStatus.Fail;
            }

            return result;
        }

        /// <summary>
        /// 识别是否定金
        /// </summary>
        public bool GetIsDeposit(CollectionInfoIsDeposit_In isDepositIn)
        {
            IsDepositValidating(isDepositIn);

            string CollectingCompany = "";
            string PaymentCompany = "";
            List<Db_crm_collectingcompany> cc = DbOpe_crm_collectingcompany.Instance.GetDataList(r => r.CollectingCompanyName == isDepositIn.CollectingCompanyName).ToList();
            if (cc.Count() > 0)
            {
                CollectingCompany = cc.First().Id;
            }
            List<Db_crm_customer_subcompany> csc = DbOpe_crm_customer_subcompany.Instance.GetDataList(r => r.CompanyName == isDepositIn.PaymentCompanyName).ToList();
            if (csc.Count() > 0)
            {
                PaymentCompany = csc.First().Id;
            }

            if (CollectingCompany == "" || PaymentCompany == "")
            {
                return false;
            }

            List<Db_crm_contract_paymentinfo> paymentinfo = new List<Db_crm_contract_paymentinfo>();
            //if (isDepositIn.Currency == EnumCurrency.CNY.ToInt())
            //{
            //    paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfo(CollectingCompany, PaymentCompany, isDepositIn.Currency, isDepositIn.ArrivalAmount, isDepositIn.PaymentMethod, isDepositIn.BankPaymentAmount, isDepositIn.CashPaymentAmount);
            //}
            //else
            //{
            //    paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfo(CollectingCompany, PaymentCompany, isDepositIn.Currency, isDepositIn.FCArrivalAmount, isDepositIn.PaymentMethod, isDepositIn.BankPaymentAmount, isDepositIn.CashPaymentAmount);
            //}

            //if (paymentinfo.Count > 0)
            //{
            //    return false;
            //}
            //else
            //{
            paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByPaymentCompany(PaymentCompany);
            if (paymentinfo.Count > 0)
            {
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(paymentinfo.First().ContractId);
                bool IsDeposit = false;
                if (isDepositIn.Currency == EnumCurrency.CNY.ToInt())
                {
                    if ((isDepositIn.ArrivalAmount * 2) >= contract.ContractAmount)
                    {
                        IsDeposit = false;
                    }
                    else
                    {
                        IsDeposit = true;
                    }
                }
                else
                {
                    if ((isDepositIn.FCArrivalAmount.Value * 2) >= contract.FCContractAmount)
                    {
                        IsDeposit = false;
                    }
                    else
                    {
                        IsDeposit = true;
                    }
                }
                if (IsDeposit)
                {
                    return true;

                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
            //}
        }

        /// <summary>
        /// 根据查询条件获取匹配状态为待登记的回款信息列表
        /// </summary>
        public ApiTableOut<SearchUnRegisteredCollectionInfoList_Out> SearchUnRegisteredCollectionInfoList(SearchUnRegisteredCollectionInfoList_In searchUnRegisteredCollectionInfoListIn)
        {
            int total = 0;
            return new ApiTableOut<SearchUnRegisteredCollectionInfoList_Out> { Data = DbOpe_crm_collectioninfo.Instance.SearchUnRegisteredCollectionInfoList(searchUnRegisteredCollectionInfoListIn, ref total), Total = total };
        }

        private void ValidatingTransactionReceipt(List<AddTransactionReceipt_In> addTransactionReceiptIn)
        {
            //验证回款信息
            if (addTransactionReceiptIn != null && addTransactionReceiptIn.Count > 0)
            {
                addTransactionReceiptIn.ForEach(collectionInfo =>
                {
                    if (collectionInfo.TransactionReceipt.IsNullOrEmpty() && (collectionInfo.CollectioninfoAttachFile == null || collectionInfo.CollectioninfoAttachFile.Count == 0))
                    {
                        throw new ApiException("回款交易凭证信息不可为空");
                    }
                });
            }
            else
            {
                throw new ApiException("回款交易凭证信息不可为空");
            }
        }

        private void ValidatingTransactionReceiptUpdate(List<UpdateTransactionReceipt_In> addTransactionReceiptIn)
        {
            //验证回款信息
            if (addTransactionReceiptIn != null && addTransactionReceiptIn.Count > 0)
            {
                addTransactionReceiptIn.ForEach(collectionInfo =>
                {
                    List<Db_crm_collectioninfo_attachfile> attachfileList = DbOpe_crm_collectioninfo_attachfile.Instance.GetDataList(r => r.CollectionInfoId == collectionInfo.Id);
                    bool isnotdeleteattach = false;
                    if (attachfileList.Count > 0)
                    {
                        List<string> attachIds = attachfileList.Select(r => r.Id).ToList();
                        if (collectionInfo.CollectioninfoAttachFileInfo != null)
                        {
                            isnotdeleteattach = collectionInfo.CollectioninfoAttachFileInfo.Where(r => attachIds.Contains(r.Id)).Any();
                        }
                    }

                    //if (collectionInfo.TransactionReceipt.IsNullOrEmpty() && (attachfileList.Count == 0 || (attachfileList.Count > 0 && collectionInfo.CollectioninfoAttachFile.Count == 0 && !isnotdeleteattach)) && (collectionInfo.CollectioninfoAttachFile == null || collectionInfo.CollectioninfoAttachFile.Count == 0))
                    if (collectionInfo.TransactionReceipt.IsNullOrEmpty() && (attachfileList.Count == 0 || !isnotdeleteattach) && (collectionInfo.CollectioninfoAttachFile == null || collectionInfo.CollectioninfoAttachFile.Count == 0))
                    {
                        throw new ApiException("回款交易凭证信息不可为空");
                    }
                });
            }
            else
            {
                throw new ApiException("回款交易凭证信息不可为空");
            }
        }

        /// <summary>
        /// 添加回款交易凭证信息
        /// </summary>
        public List<Db_crm_collectioninfo> AddTransactionReceipt(List<AddTransactionReceipt_In> addTransactionReceiptIn)
        {
            //验证输入项
            ValidatingTransactionReceipt(addTransactionReceiptIn);

            //List<AddTransactionReceipt_In> Receipts = addTransactionReceiptIn.Where(r => r.TransactionReceipt != "" && r.TransactionReceipt != null).ToList();
            //if (Receipts.Count > Receipts.Select(r => r.TransactionReceipt).Distinct().Count())
            //{
            //    throw new ApiException("回款交易凭证信息存在重复信息");
            //}

            //List<string> result = new List<string>();
            //foreach (AddTransactionReceipt_In l in addTransactionReceiptIn)
            //{
            //    if (l.TransactionReceipt.IsNotNullOrEmpty())
            //    {
            //        List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetDataList(r => r.TransactionReceipt == l.TransactionReceipt).ToList();
            //        if (list.Count > 0)
            //        {
            //            result.Add("“" + l.TransactionReceipt + "”");
            //        }
            //    }
            //}
            //if (result.Count > 0)
            //{
            //    throw new ApiException("回款交易凭证信息已存在," + result.JoinToString(","));
            //}

            List<Db_crm_collectioninfo> collectioninfo = new List<Db_crm_collectioninfo>();
            DbOpe_crm_collectioninfo.Instance.TransDeal(() =>
            {

                List<string> CollectingCompanyList = addTransactionReceiptIn.Select(r => r.CollectingCompanyName).ToList();
                List<Db_crm_collectingcompany> collectingcompany = DbOpe_crm_collectingcompany.Instance.GetDataList(r => CollectingCompanyList.Contains(r.CollectingCompanyName));
                List<string> PaymentCompanyList = addTransactionReceiptIn.Select(r => r.PaymentCompanyName).ToList();
                List<Db_crm_customer_subcompany> customersubcompany = DbOpe_crm_customer_subcompany.Instance.GetDataList(r => PaymentCompanyList.Contains(r.CompanyName) && (r.IsValid == 1 || (r.IsValid == 0 && r.CustomerId == Guid.Empty.ToString())));

                foreach (AddTransactionReceipt_In co in addTransactionReceiptIn)
                {
                    Db_crm_collectioninfo item = new Db_crm_collectioninfo();
                    item = co.MappingTo<Db_crm_collectioninfo>();
                    item.VerifyConfirm = 1;
                    List<Db_crm_collectingcompany> cc = collectingcompany.Where(r => r.CollectingCompanyName == item.CollectingCompanyName).ToList();
                    if (cc.Count() > 0)
                    {
                        item.CollectingCompany = cc.First().Id;
                    }
                    List<Db_crm_customer_subcompany> csc = customersubcompany.Where(r => r.CompanyName == item.PaymentCompanyName).ToList();
                    if (csc.Count() > 0)
                    {
                        item.PaymentCompany = csc.First().Id;
                    }

                    //List<Db_crm_contract_paymentinfo> paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetDataList(r =>
                    //r.CollectingCompany == item.CollectingCompany &&
                    //r.PaymentCompany == item.PaymentCompany &&
                    //r.Currency == item.Currency &&
                    //r.ArrivalAmount == item.ArrivalAmount &&
                    //r.PaymentMethod == item.PaymentMethod &&
                    //r.BankPaymentAmount == item.BankPaymentAmount &&
                    //r.CashPaymentAmount == item.CashPaymentAmount);
                    //List<Db_crm_contract_paymentinfo> paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfo(item.CollectingCompany, item.PaymentCompany, item.Currency, item.ArrivalAmount, item.PaymentMethod, item.BankPaymentAmount, item.CashPaymentAmount);
                    List<Db_crm_contract_paymentinfo> paymentinfo = new List<Db_crm_contract_paymentinfo>();
                    if (item.Currency == EnumCurrency.CNY.ToInt())
                    {
                        paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfo(item.CollectingCompany, item.PaymentCompany, item.Currency, item.ArrivalAmount, item.PaymentMethod, item.BankPaymentAmount, item.CashPaymentAmount);
                    }
                    else
                    {
                        paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfo(item.CollectingCompany, item.PaymentCompany, item.Currency, item.FCArrivalAmount, item.PaymentMethod, item.BankPaymentAmount, item.CashPaymentAmount);
                    }

                    item.MaintenanceStatus = EnumMaintenanceStatus.Submit.ToInt();
                    //if (paymentinfo.Count > 0)
                    //{
                    //    item.IsDeposit = false;
                    //    item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                    //    item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //    item.IsMatchingAll = EnumIsMatchingAll.All.ToInt() == 1 ? true : false;
                    //    item.MaintenanceStatus = EnumMaintenanceStatus.Pass.ToInt();
                    //}
                    //else
                    //{
                    //    paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByPaymentCompany(item.PaymentCompany);
                    //    if (paymentinfo.Count > 0)
                    //    {
                    //        bool IsDeposit = false;
                    //        if (item.Currency == EnumCurrency.CNY.ToInt())
                    //        {
                    //            if ((item.ArrivalAmount.Value * 2) > paymentinfo.First().ArrivalAmount.Value || (item.ArrivalAmount.Value * 2) > paymentinfo.First().PlannedArrivalAmount.Value)
                    //            {
                    //                IsDeposit = false;
                    //            }
                    //            else
                    //            {
                    //                IsDeposit = true;
                    //            }
                    //        }
                    //        else
                    //        {
                    //            if ((item.FCArrivalAmount.Value * 2) > paymentinfo.First().ArrivalAmount.Value || (item.FCArrivalAmount.Value * 2) > paymentinfo.First().PlannedArrivalAmount.Value)
                    //            {
                    //                IsDeposit = false;
                    //            }
                    //            else
                    //            {
                    //                IsDeposit = true;
                    //            }
                    //        }
                    //        if (IsDeposit)
                    //        {
                    //            item.IsDeposit = true;
                    //            item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                    //            item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //            item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    //        }
                    //        else
                    //        {
                    //            item.IsDeposit = false;
                    //            item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                    //            item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //            item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    //        }
                    //    }
                    //    else
                    //    {
                    //        item.IsDeposit = false;
                    //        item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                    //        item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //        item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    //    }
                    //    //item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                    //    //item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    //    //item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    //}
                    if (item.IsDeposit == true)
                    {
                        item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                        item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                        item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    }
                    else
                    {
                        if (paymentinfo.Count > 0)
                        {
                            item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                            item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                            item.IsMatchingAll = EnumIsMatchingAll.All.ToInt() == 1 ? true : false;
                            item.MaintenanceStatus = EnumMaintenanceStatus.Pass.ToInt();
                        }
                        else
                        {
                            paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByPaymentCompany(item.PaymentCompany);
                            if (paymentinfo.Count > 0)
                            {
                                item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                            }
                            else
                            {
                                item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                            }
                            //item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                            //item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                            //item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                        }
                    }

                    //item.MaintenanceStatus = EnumMaintenanceStatus.Submit.ToInt();
                    //if (item.IdentifyStatus == EnumIdentifyStatus.Success.ToInt())
                    //{
                    //    item.MaintenanceStatus = EnumMaintenanceStatus.Pass.ToInt();
                    //}
                    //else
                    //{
                    //    item.MaintenanceStatus = EnumMaintenanceStatus.Submit.ToInt();
                    //}

                    if (String.IsNullOrEmpty(item.TransactionReceipt))
                    {
                        item.IdentifyStatus = EnumIdentifyStatus.Fail.ToInt();
                    }
                    if (item.IdentifyStatus == null || item.IdentifyStatus == 0)
                    {
                        item.IdentifyStatus = EnumIdentifyStatus.Fail.ToInt();
                    }

                    int Enable = EnumExchangeState.Enable.ToInt();
                    Db_sys_exchange exchange = DbOpe_sys_exchange.Instance.GetData(r => r.ExchangeDate == item.ArrivalDate && r.TargetCurrency == item.Currency && r.State == Enable);
                    if (exchange != null)
                    {
                        item.ExchangeRate = exchange.ExchangeRate;
                    }

                    //日期 2024年8月1日 增加锁定列
                    item.IsLocked = EnumLocked.UnLocked.ToInt();

                    collectioninfo.Add(item);
                    Guid collectionInfoId = DbOpe_crm_collectioninfo.Instance.InsertDataReturnId(item);

                    if (item.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt())
                    {
                        Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(paymentinfo.First().ContractId);
                        //获取用户信息
                        var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                        //获取组织信息树，从当前组织追溯出所有上级组织
                        var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                        string? OrgDivisionId = null;
                        string OrgDivisionName = "";
                        string? OrgBrigadeId = null;
                        string OrgBrigadeName = "";
                        string? OrgRegimentId = null;
                        string OrgRegimentName = "";
                        foreach (var org in orgList)
                        {
                            switch (org.OrgType)
                            {
                                case EnumOrgType.BattleTeam:
                                    OrgDivisionId = org.Id;
                                    OrgDivisionName = org.OrgName;
                                    break;
                                case EnumOrgType.Battalion:
                                    OrgBrigadeId = org.Id;
                                    OrgBrigadeName = org.OrgName;
                                    break;
                                case EnumOrgType.Squadron:
                                    OrgRegimentId = org.Id;
                                    OrgRegimentName = org.OrgName;
                                    break;
                            }
                        }
                        Db_crm_contract_collectioninfo_automatching automatching = new Db_crm_contract_collectioninfo_automatching();
                        automatching.ContractId = paymentinfo.First().ContractId;
                        automatching.ContractPaymentInfoId = paymentinfo.First().Id;
                        automatching.CollectionInfoId = collectionInfoId.ToString();
                        automatching.State = EnumAutoMatchingState.MatchedToBeConfirmed.ToInt();
                        automatching.IsAutoMatching = EnumIsAutoMatching.Auto.ToInt() == 1 ? true : false;
                        automatching.Issuer = contract.Issuer;
                        automatching.OrgDivisionId = OrgDivisionId;
                        automatching.OrgDivisionName = OrgDivisionName;
                        automatching.OrgBrigadeId = OrgBrigadeId;
                        automatching.OrgBrigadeName = OrgBrigadeName;
                        automatching.OrgRegimentId = OrgRegimentId;
                        automatching.OrgRegimentName = OrgRegimentName;
                        DbOpe_crm_contract_collectioninfo_automatching.Instance.InsertData(automatching);
                        //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", item.Id, item, "", EnumMatchingStatus.MatchingPendingRegistration.GetEnumDescription(), "回款信息自动匹配成功");
                    }
                    else
                    {
                        //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", item.Id, item, "", EnumMatchingStatus.ToBeMatched.GetEnumDescription(), "回款信息自动匹配失败");
                    }

                    if (co.CollectioninfoAttachFile != null && co.CollectioninfoAttachFile.Count > 0)
                    {
                        Util<DbOpe_crm_collectioninfo_attachfile, BM_AttachFile> contractAttachFile = new Util<DbOpe_crm_collectioninfo_attachfile, BM_AttachFile>(DbOpe_crm_collectioninfo_attachfile.Instance);
                        contractAttachFile.UploadFile(co.CollectioninfoAttachFile, EnumAttachFileType.TransactionReceipt.ToString(), UserId, collectionInfoId.ToString());
                    }
                }

                //DbOpe_crm_collectioninfo.Instance.InsertListData(collectioninfo);
            });
            return collectioninfo;
        }

        /// <summary>
        /// 修改回款信息
        /// </summary>
        public Db_crm_collectioninfo UpdateTransactionReceipt(UpdateTransactionReceipt_In updateTransactionReceiptIn)
        {
            //if (updateTransactionReceiptIn.TransactionReceipt != "" && updateTransactionReceiptIn.TransactionReceipt != null)
            //{
            //    List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetDataList(r => r.TransactionReceipt == updateTransactionReceiptIn.TransactionReceipt && r.Id != updateTransactionReceiptIn.Id).ToList();
            //    if (list.Count > 0)
            //    {
            //        throw new ApiException("回款交易凭证信息已存在," + updateTransactionReceiptIn.TransactionReceipt);
            //    }
            //}

            Db_crm_collectioninfo collectionInfo = DbOpe_crm_collectioninfo.Instance.GetDataById(updateTransactionReceiptIn.Id);
            //if (collectionInfo.MatchingStatus == EnumMatchingStatus.Registered.ToInt())
            //{
            //    throw new ApiException("该回款信息已登记不可以修改");
            //}
            //银行到账列表中除待匹配状态或未维护的可以点击修改按钮； 其他都需要限制下：不允许修改
            if (!(collectionInfo.MatchingStatus == EnumMatchingStatus.ToBeMatched.ToInt() || collectionInfo.MaintenanceStatus == EnumMaintenanceStatus.Submit.ToInt()))
            {
                throw new ApiException("该回款信息已登记不可以修改");
            }
            if (collectionInfo.IsLocked == EnumLocked.Locked.ToInt())
            {
                throw new ApiException("该回款信息已锁定不可以修改");
            }
            //验证输入项
            List<UpdateTransactionReceipt_In> updateTransactionReceipt_Ins = new List<UpdateTransactionReceipt_In>();
            updateTransactionReceipt_Ins.Add(updateTransactionReceiptIn);
            ValidatingTransactionReceiptUpdate(updateTransactionReceipt_Ins.ConvertAll<UpdateTransactionReceipt_In>(r => r));
            Db_crm_collectioninfo item = new Db_crm_collectioninfo();
            DbOpe_crm_collectioninfo.Instance.TransDeal(() =>
            {
                Db_crm_collectioninfo ol = DbOpe_crm_collectioninfo.Instance.GetDataById(updateTransactionReceiptIn.Id);
                List<Db_crm_collectingcompany> collectingcompany = DbOpe_crm_collectingcompany.Instance.GetDataList(r => r.CollectingCompanyName == updateTransactionReceiptIn.CollectingCompanyName);
                List<Db_crm_customer_subcompany> customersubcompany = DbOpe_crm_customer_subcompany.Instance.GetDataList(r => r.CompanyName == updateTransactionReceiptIn.PaymentCompanyName && (r.IsValid == 1 || (r.IsValid == 0 && r.CustomerId == Guid.Empty.ToString())));

                item = updateTransactionReceiptIn.MappingTo<Db_crm_collectioninfo>();

                item.SplitDate = ol.SplitDate;
                item.SplitRemark = ol.SplitRemark;
                item.VerifyConfirm = ol.VerifyConfirm;
                item.VerifyConfirmDate = ol.VerifyConfirmDate;
                item.UpdateRemark = ol.UpdateRemark;
                item.Remark = ol.Remark;

                List<Db_crm_collectingcompany> cc = collectingcompany.Where(r => r.CollectingCompanyName == item.CollectingCompanyName).ToList();
                if (cc.Count() > 0)
                {
                    item.CollectingCompany = cc.First().Id;
                }
                List<Db_crm_customer_subcompany> csc = customersubcompany.Where(r => r.CompanyName == item.PaymentCompanyName).ToList();
                if (csc.Count() > 0)
                {
                    item.PaymentCompany = csc.First().Id;
                }

                //List<Db_crm_contract_paymentinfo> paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetDataList(r =>
                //r.CollectingCompany == item.CollectingCompany &&
                //r.PaymentCompany == item.PaymentCompany &&
                //r.Currency == item.Currency &&
                //r.ArrivalAmount == item.ArrivalAmount &&
                //r.PaymentMethod == item.PaymentMethod &&
                //r.BankPaymentAmount == item.BankPaymentAmount &&
                //r.CashPaymentAmount == item.CashPaymentAmount);
                List<Db_crm_contract_paymentinfo> paymentinfo = new List<Db_crm_contract_paymentinfo>();
                if (item.Currency == EnumCurrency.CNY.ToInt())
                {
                    paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfoAndCollectioninfoID(updateTransactionReceiptIn.Id, item.CollectingCompany, item.PaymentCompany, item.Currency, item.ArrivalAmount, item.PaymentMethod, item.BankPaymentAmount, item.CashPaymentAmount);
                }
                else
                {
                    paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByCollectioninfoAndCollectioninfoID(updateTransactionReceiptIn.Id, item.CollectingCompany, item.PaymentCompany, item.Currency, item.FCArrivalAmount, item.PaymentMethod, item.BankPaymentAmount, item.CashPaymentAmount);
                }

                item.MatchingStatus = collectionInfo.MatchingStatus;
                item.IsManualMatching = collectionInfo.IsManualMatching;
                item.IsMatchingAll = collectionInfo.IsMatchingAll;

                item.MaintenanceStatus = EnumMaintenanceStatus.Submit.ToInt();
                //if (paymentinfo.Count > 0)
                //{
                //    item.IsDeposit = false;
                //    item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                //    item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //    item.IsMatchingAll = EnumIsMatchingAll.All.ToInt() == 1 ? true : false;
                //    item.MaintenanceStatus = EnumMaintenanceStatus.Pass.ToInt();
                //}
                //else
                //{
                //    if (!(collectionInfo.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt() && collectionInfo.IsManualMatching == true && collectionInfo.IsMatchingAll == false))
                //    {
                //        paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByPaymentCompanyAndCollectioninfoID(updateTransactionReceiptIn.Id, item.PaymentCompany);
                //        if (paymentinfo.Count > 0)
                //        {
                //            bool IsDeposit = false;
                //            if (item.Currency == EnumCurrency.CNY.ToInt())
                //            {
                //                if ((item.ArrivalAmount.Value * 2) > paymentinfo.First().ArrivalAmount.Value || (item.ArrivalAmount.Value * 2) > paymentinfo.First().PlannedArrivalAmount.Value)
                //                {
                //                    IsDeposit = false;
                //                }
                //                else
                //                {
                //                    IsDeposit = true;
                //                }
                //            }
                //            else
                //            {
                //                if ((item.FCArrivalAmount.Value * 2) > paymentinfo.First().ArrivalAmount.Value || (item.FCArrivalAmount.Value * 2) > paymentinfo.First().PlannedArrivalAmount.Value)
                //                {
                //                    IsDeposit = false;
                //                }
                //                else
                //                {
                //                    IsDeposit = true;
                //                }
                //            }
                //            if (IsDeposit)
                //            {
                //                item.IsDeposit = true;
                //                item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                //                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                //            }
                //            else
                //            {
                //                item.IsDeposit = false;
                //                item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                //                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                //            }
                //        }
                //        else
                //        {
                //            item.IsDeposit = false;
                //            item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                //            item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //            item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                //        }
                //    }
                //    //item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                //    //item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                //    //item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                //}
                if (item.IsDeposit == true)
                {
                    item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                    item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                    item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                }
                else
                {
                    if (paymentinfo.Count > 0)
                    {
                        item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                        item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                        item.IsMatchingAll = EnumIsMatchingAll.All.ToInt() == 1 ? true : false;
                        item.MaintenanceStatus = EnumMaintenanceStatus.Pass.ToInt();
                    }
                    else
                    {
                        if (!(collectionInfo.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt() && collectionInfo.IsManualMatching == true && collectionInfo.IsMatchingAll == false))
                        {
                            paymentinfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByPaymentCompanyAndCollectioninfoID(updateTransactionReceiptIn.Id, item.PaymentCompany);
                            if (paymentinfo.Count > 0)
                            {
                                item.MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                            }
                            else
                            {
                                item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                                item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                                item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                            }
                        }
                        //item.MatchingStatus = EnumMatchingStatus.ToBeMatched.ToInt();
                        //item.IsManualMatching = EnumIsManualMatching.Auto.ToInt() == 1 ? true : false;
                        //item.IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;
                    }
                }

                //item.MaintenanceStatus = EnumMaintenanceStatus.Submit.ToInt();

                int Enable = EnumExchangeState.Enable.ToInt();
                Db_sys_exchange exchange = DbOpe_sys_exchange.Instance.GetData(r => r.ExchangeDate == item.ArrivalDate && r.TargetCurrency == item.Currency && r.State == Enable);
                if (exchange != null)
                {
                    item.ExchangeRate = exchange.ExchangeRate;
                }
                //修改 2024年8月1日 增加锁定列
                item.IsLocked = EnumLocked.UnLocked.ToInt();

                DbOpe_crm_collectioninfo.Instance.UpdateData(item);

                //维护回款信息附件信息
                DbOpe_crm_collectioninfo_attachfile.Instance.DeleteCollectioninfoAttachFileList(updateTransactionReceiptIn.CollectioninfoAttachFileInfo, updateTransactionReceiptIn.Id);
                //上传回款信息附件
                if (updateTransactionReceiptIn.CollectioninfoAttachFile != null && updateTransactionReceiptIn.CollectioninfoAttachFile.Count > 0)
                {
                    Util<DbOpe_crm_collectioninfo_attachfile, BM_AttachFile> contractAttachFile = new Util<DbOpe_crm_collectioninfo_attachfile, BM_AttachFile>(DbOpe_crm_collectioninfo_attachfile.Instance);
                    contractAttachFile.UploadFile(updateTransactionReceiptIn.CollectioninfoAttachFile, EnumAttachFileType.TransactionReceipt.ToString(), UserId, updateTransactionReceiptIn.Id);
                }

                if (!(item.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt() && item.IsManualMatching == true && item.IsMatchingAll == false))
                {
                    //更新匹配信息
                    int MatchedToBeConfirmed = EnumAutoMatchingState.MatchedToBeConfirmed.ToInt();
                    Db_crm_contract_collectioninfo_automatching automatche = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetData(r => r.CollectionInfoId == item.Id && r.State == MatchedToBeConfirmed);
                    if (automatche != null)
                    {
                        //已匹配过，如果状态为不匹配则删除原有的，如果状态为匹配，则判断如果是新的匹配支付信息则删除原有的匹配信息在新增，如果不是新的则保持不变。
                        if (item.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt())
                        {
                            if (automatche.ContractPaymentInfoId != paymentinfo.First().Id)
                            {
                                DbOpe_crm_contract_collectioninfo_automatching.Instance.DeleteData(automatche.Id);

                                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(paymentinfo.First().ContractId);
                                //获取用户信息
                                var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                                //获取组织信息树，从当前组织追溯出所有上级组织
                                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                                string? OrgDivisionId = null;
                                string OrgDivisionName = "";
                                string? OrgBrigadeId = null;
                                string OrgBrigadeName = "";
                                string? OrgRegimentId = null;
                                string OrgRegimentName = "";
                                foreach (var org in orgList)
                                {
                                    switch (org.OrgType)
                                    {
                                        case EnumOrgType.BattleTeam:
                                            OrgDivisionId = org.Id;
                                            OrgDivisionName = org.OrgName;
                                            break;
                                        case EnumOrgType.Battalion:
                                            OrgBrigadeId = org.Id;
                                            OrgBrigadeName = org.OrgName;
                                            break;
                                        case EnumOrgType.Squadron:
                                            OrgRegimentId = org.Id;
                                            OrgRegimentName = org.OrgName;
                                            break;
                                    }
                                }

                                Db_crm_contract_collectioninfo_automatching automatching = new Db_crm_contract_collectioninfo_automatching();
                                automatching.ContractId = paymentinfo.First().ContractId;
                                automatching.ContractPaymentInfoId = paymentinfo.First().Id;
                                automatching.CollectionInfoId = item.Id.ToString();
                                automatching.State = EnumAutoMatchingState.MatchedToBeConfirmed.ToInt();
                                automatching.IsAutoMatching = EnumIsAutoMatching.Auto.ToInt() == 1 ? true : false;
                                automatching.Issuer = contract.Issuer;
                                automatching.OrgDivisionId = OrgDivisionId;
                                automatching.OrgDivisionName = OrgDivisionName;
                                automatching.OrgBrigadeId = OrgBrigadeId;
                                automatching.OrgBrigadeName = OrgBrigadeName;
                                automatching.OrgRegimentId = OrgRegimentId;
                                automatching.OrgRegimentName = OrgRegimentName;
                                DbOpe_crm_contract_collectioninfo_automatching.Instance.InsertData(automatching);
                            }
                            //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", item.Id, item, "", EnumMatchingStatus.MatchingPendingRegistration.GetEnumDescription(), "回款信息自动匹配成功");
                        }
                        else
                        {
                            //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", item.Id, item, "", EnumMatchingStatus.ToBeMatched.GetEnumDescription(), "回款信息自动匹配失败");
                            DbOpe_crm_contract_collectioninfo_automatching.Instance.DeleteData(automatche.Id);
                        }
                    }
                    else
                    {
                        //未匹配过，如果状态为匹配则添加
                        if (item.MatchingStatus == EnumMatchingStatus.MatchingPendingRegistration.ToInt())
                        {
                            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(paymentinfo.First().ContractId);
                            //获取用户信息
                            var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                            //获取组织信息树，从当前组织追溯出所有上级组织
                            var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                            string? OrgDivisionId = null;
                            string OrgDivisionName = "";
                            string? OrgBrigadeId = null;
                            string OrgBrigadeName = "";
                            string? OrgRegimentId = null;
                            string OrgRegimentName = "";
                            foreach (var org in orgList)
                            {
                                switch (org.OrgType)
                                {
                                    case EnumOrgType.BattleTeam:
                                        OrgDivisionId = org.Id;
                                        OrgDivisionName = org.OrgName;
                                        break;
                                    case EnumOrgType.Battalion:
                                        OrgBrigadeId = org.Id;
                                        OrgBrigadeName = org.OrgName;
                                        break;
                                    case EnumOrgType.Squadron:
                                        OrgRegimentId = org.Id;
                                        OrgRegimentName = org.OrgName;
                                        break;
                                }
                            }
                            Db_crm_contract_collectioninfo_automatching automatching = new Db_crm_contract_collectioninfo_automatching();
                            automatching.ContractId = paymentinfo.First().ContractId;
                            automatching.ContractPaymentInfoId = paymentinfo.First().Id;
                            automatching.CollectionInfoId = item.Id.ToString();
                            automatching.State = EnumAutoMatchingState.MatchedToBeConfirmed.ToInt();
                            automatching.IsAutoMatching = EnumIsAutoMatching.Auto.ToInt() == 1 ? true : false;
                            automatching.Issuer = contract.Issuer;
                            automatching.OrgDivisionId = OrgDivisionId;
                            automatching.OrgDivisionName = OrgDivisionName;
                            automatching.OrgBrigadeId = OrgBrigadeId;
                            automatching.OrgBrigadeName = OrgBrigadeName;
                            automatching.OrgRegimentId = OrgRegimentId;
                            automatching.OrgRegimentName = OrgRegimentName;
                            DbOpe_crm_contract_collectioninfo_automatching.Instance.InsertData(automatching);
                            //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", item.Id, item, "", EnumMatchingStatus.MatchingPendingRegistration.GetEnumDescription(), "回款信息自动匹配成功");
                        }
                        else
                        {
                            //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", item.Id, item, "", EnumMatchingStatus.ToBeMatched.GetEnumDescription(), "回款信息自动匹配失败");
                        }
                    }
                }
            });
            return item;
        }

        /// <summary>
        /// 添加回款交易凭证信息
        /// </summary>
        public void CheckTransactionReceiptRepeat(List<CheckTransactionReceiptRepeat_In> checkTransactionReceiptRepeatIn)
        {
            foreach (CheckTransactionReceiptRepeat_In l in checkTransactionReceiptRepeatIn)
            {
                if (l.TransactionReceipt.IsNullOrEmptyTrim())
                {
                    throw new ApiException("交易凭证（支付凭证）不可为空");
                }
            }

            if (checkTransactionReceiptRepeatIn.Count > checkTransactionReceiptRepeatIn.Select(r => r.TransactionReceipt).Distinct().Count())
            {
                throw new ApiException("回款交易凭证信息存在重复信息");
            }

            List<string> result = new List<string>();
            foreach (CheckTransactionReceiptRepeat_In l in checkTransactionReceiptRepeatIn)
            {
                List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetDataList(r => r.TransactionReceipt == l.TransactionReceipt).ToList();
                if (list.Count > 0)
                {
                    result.Add("“" + l.TransactionReceipt + "”");
                }
            }
            if (result.Count > 0)
            {
                throw new ApiException("回款交易凭证信息已存在," + result.JoinToString(","));
            }
        }

        /// <summary>
        /// 手动匹配回款交易凭证信息
        /// </summary>
        /// <param name="addManualMatchingIn"></param>
        public void AddManualMatching(AddManualMatching_In addManualMatchingIn)
        {
            DbOpe_crm_collectioninfo.Instance.TransDeal(() =>
            {
                Db_crm_collectioninfo collectioninfo = DbOpe_crm_collectioninfo.Instance.GetDataById(addManualMatchingIn.CollectionInfoId);
                if (!(collectioninfo.MaintenanceStatus == EnumMaintenanceStatus.Pass.ToInt() && collectioninfo.MatchingStatus == EnumMatchingStatus.ToBeMatched.ToInt()))
                {
                    throw new ApiException("当前匹配状态不可以进行手动匹配");
                }
                if (collectioninfo.IsDeposit.Value)
                {
                    throw new ApiException("当前是否定金状态不可以进行手动匹配");
                }

                int MatchingStatus = EnumMatchingStatus.MatchingPendingRegistration.ToInt();
                bool IsManualMatching = EnumIsManualMatching.Manual.ToInt() == 1 ? true : false;
                bool IsMatchingAll = EnumIsMatchingAll.Part.ToInt() == 1 ? true : false;

                if (addManualMatchingIn.ContractId.IsNullOrEmpty() || addManualMatchingIn.ContractId == Guid.Empty.ToString())
                {
                    //没有匹配的合同，只保存备注信息
                    DbOpe_crm_collectioninfo.Instance.UpdateData(r => new Db_crm_collectioninfo { IsManualMatching = IsManualMatching, Remark = addManualMatchingIn.Remark, MaintenanceDate = DateTime.Now }, addManualMatchingIn.CollectionInfoId);
                }
                else
                {
                    DbOpe_crm_collectioninfo.Instance.UpdateData(r => new Db_crm_collectioninfo { MatchingStatus = MatchingStatus, IsManualMatching = IsManualMatching, IsMatchingAll = IsMatchingAll, Remark = addManualMatchingIn.Remark, MaintenanceDate = DateTime.Now }, addManualMatchingIn.CollectionInfoId);

                    Db_crm_contract_paymentinfo PaymentInfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByContractId(addManualMatchingIn.ContractId);

                    Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(PaymentInfo.ContractId);
                    //获取用户信息
                    var user = DbOpe_sys_user.Instance.GetDbSysUserById(contract.Issuer);
                    //获取组织信息树，从当前组织追溯出所有上级组织
                    var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                    string? OrgDivisionId = null;
                    string OrgDivisionName = "";
                    string? OrgBrigadeId = null;
                    string OrgBrigadeName = "";
                    string? OrgRegimentId = null;
                    string OrgRegimentName = "";
                    foreach (var org in orgList)
                    {
                        switch (org.OrgType)
                        {
                            case EnumOrgType.BattleTeam:
                                OrgDivisionId = org.Id;
                                OrgDivisionName = org.OrgName;
                                break;
                            case EnumOrgType.Battalion:
                                OrgBrigadeId = org.Id;
                                OrgBrigadeName = org.OrgName;
                                break;
                            case EnumOrgType.Squadron:
                                OrgRegimentId = org.Id;
                                OrgRegimentName = org.OrgName;
                                break;
                        }
                    }

                    Db_crm_contract_collectioninfo_automatching automatching = new Db_crm_contract_collectioninfo_automatching();
                    automatching.ContractId = addManualMatchingIn.ContractId;
                    automatching.ContractPaymentInfoId = PaymentInfo.Id;
                    automatching.CollectionInfoId = addManualMatchingIn.CollectionInfoId;
                    automatching.State = EnumAutoMatchingState.MatchedToBeConfirmed.ToInt();
                    automatching.IsAutoMatching = EnumIsAutoMatching.Manual.ToInt() == 1 ? true : false;
                    automatching.Remark = addManualMatchingIn.Remark;
                    automatching.Issuer = contract.Issuer;
                    automatching.OrgDivisionId = OrgDivisionId;
                    automatching.OrgDivisionName = OrgDivisionName;
                    automatching.OrgBrigadeId = OrgBrigadeId;
                    automatching.OrgBrigadeName = OrgBrigadeName;
                    automatching.OrgRegimentId = OrgRegimentId;
                    automatching.OrgRegimentName = OrgRegimentName;
                    var autoMatchingId = DbOpe_crm_contract_collectioninfo_automatching.Instance.InsertDataReturnId(automatching);
                    collectioninfo = DbOpe_crm_collectioninfo.Instance.GetDataById(addManualMatchingIn.CollectionInfoId);
                    //BLL_WorkFlow.Instance.AddWorkFlow("银行到账匹配流程", addManualMatchingIn.CollectionInfoId, collectioninfo, "", EnumMatchingStatus.MatchingPendingRegistration.GetEnumDescription(), "银行到账手动匹配成功");
                }
            });
        }

        /// <summary>
        /// 获取回款信息状态字典
        /// </summary>
        public List<Dictionary_Out> GetCollectionInfoStateList()
        {
            List<Dictionary_Out> result = DbOpe_sys_dictionary.Instance.GetDictionaryByParentValueAndDataOwner("MatchingStatus", true).OrderBy(r => r.CreateDate).ToList();
            return result;
        }

        /// <summary>
        /// 验证回款信息是否重复
        /// </summary>
        public CheckCollectionInfoRepeat_Out CheckCollectionInfoRepeat(List<CheckCollectionInfoRepeat_In> checkCollectionInfoRepeatIn)
        {
            List<CheckCollectionInfoRepeat_In> Receipts = checkCollectionInfoRepeatIn.Where(r => r.TransactionReceipt != "" && r.TransactionReceipt != null).ToList();
            if (Receipts.Count > Receipts.Select(r => r.TransactionReceipt).Distinct().Count())
            {
                //throw new ApiException("回款交易凭证信息存在重复信息");
                return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息存在重复信息" };
            }
            bool RepeatInfo = checkCollectionInfoRepeatIn.GroupBy(r => new
            {
                r.CollectingBankName,
                //r.BankAccountNumber,
                r.CollectingCompanyName,
                r.ArrivalDate,
                r.PaymentCompanyName,
                r.Currency,
                r.ArrivalAmount,
                r.FCArrivalAmount,
                r.PaymentMethod,
                r.BankPaymentAmount,
                r.CashPaymentAmount,
                r.CashSourceRemarks
            }).Any(g => g.Count() > 1);
            if (RepeatInfo)
            {
                //throw new ApiException("回款交易凭证信息存在重复信息");
                return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息存在重复信息" };
            }

            List<string> result = new List<string>();
            foreach (CheckCollectionInfoRepeat_In l in checkCollectionInfoRepeatIn)
            {
                if (l.TransactionReceipt.IsNotNullOrEmpty())
                {
                    List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetDataList(r => r.TransactionReceipt == l.TransactionReceipt).ToList();
                    if (list.Count > 0)
                    {
                        result.Add("“" + l.TransactionReceipt + "”");
                    }
                }
                List<Db_crm_collectioninfo> collectioninfoList = DbOpe_crm_collectioninfo.Instance.GetDataList(r =>
                r.CollectingBankName == l.CollectingBankName &&
                //r.BankAccountNumber == l.BankAccountNumber &&
                r.CollectingCompanyName == l.CollectingCompanyName &&
                r.ArrivalDate == l.ArrivalDate &&
                r.PaymentCompanyName == l.PaymentCompanyName &&
                r.Currency == l.Currency &&
                r.ArrivalAmount == l.ArrivalAmount &&
                r.FCArrivalAmount == l.FCArrivalAmount &&
                r.PaymentMethod == l.PaymentMethod &&
                r.BankPaymentAmount == l.BankPaymentAmount &&
                r.CashPaymentAmount == l.CashPaymentAmount &&
                r.CashSourceRemarks == l.CashSourceRemarks
                ).ToList();
                if (collectioninfoList.Count > 0)
                {
                    result.Add("“" + l.PaymentCompanyName + "”");
                }
            }
            if (result.Count > 0)
            {
                //throw new ApiException("回款交易凭证信息已存在," + result.JoinToString(","));
                return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息已存在," + result.JoinToString(",") };
            }
            return new CheckCollectionInfoRepeat_Out();
        }

        public CheckCollectionInfoRepeat_Out CheckUpdateCollectionInfoRepeat(CheckUpdateCollectionInfoRepeat_In checkUpdateCollectionInfoRepeatIn)
        {
            if (checkUpdateCollectionInfoRepeatIn.TransactionReceipt != "" && checkUpdateCollectionInfoRepeatIn.TransactionReceipt != null)
            {
                List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetDataList(r => r.TransactionReceipt == checkUpdateCollectionInfoRepeatIn.TransactionReceipt && r.Id != checkUpdateCollectionInfoRepeatIn.Id).ToList();
                if (list.Count > 0)
                {
                    //throw new ApiException("回款交易凭证信息已存在," + checkUpdateCollectionInfoRepeatIn.TransactionReceipt);
                    return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息已存在," + checkUpdateCollectionInfoRepeatIn.TransactionReceipt };
                }
            }

            List<Db_crm_collectioninfo> collectioninfoList = DbOpe_crm_collectioninfo.Instance.GetDataList(r =>
            r.CollectingBankName == checkUpdateCollectionInfoRepeatIn.CollectingBankName &&
            //r.BankAccountNumber == checkUpdateCollectionInfoRepeatIn.BankAccountNumber &&
            r.CollectingCompanyName == checkUpdateCollectionInfoRepeatIn.CollectingCompanyName &&
            r.ArrivalDate == checkUpdateCollectionInfoRepeatIn.ArrivalDate &&
            r.PaymentCompanyName == checkUpdateCollectionInfoRepeatIn.PaymentCompanyName &&
            r.Currency == checkUpdateCollectionInfoRepeatIn.Currency &&
            r.ArrivalAmount == checkUpdateCollectionInfoRepeatIn.ArrivalAmount &&
            r.FCArrivalAmount == checkUpdateCollectionInfoRepeatIn.FCArrivalAmount &&
            r.PaymentMethod == checkUpdateCollectionInfoRepeatIn.PaymentMethod &&
            r.BankPaymentAmount == checkUpdateCollectionInfoRepeatIn.BankPaymentAmount &&
            r.CashPaymentAmount == checkUpdateCollectionInfoRepeatIn.CashPaymentAmount &&
            r.CashSourceRemarks == checkUpdateCollectionInfoRepeatIn.CashSourceRemarks &&
            r.Id != checkUpdateCollectionInfoRepeatIn.Id
            ).ToList();
            if (collectioninfoList.Count > 0)
            {
                //throw new ApiException("回款交易凭证信息已存在," + checkUpdateCollectionInfoRepeatIn.PaymentCompanyName);
                return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息已存在," + checkUpdateCollectionInfoRepeatIn.PaymentCompanyName };
            }
            return new CheckCollectionInfoRepeat_Out();
        }

        /// <summary>
        /// 验证回款信息是否重复
        /// </summary>
        public CheckCollectionInfoRepeat_Out CheckTransactionReceiptAndInfoRepeat(List<CheckTransactionReceiptAndInfoRepeat_In> checkTransactionReceiptAndInfoRepeatIn)
        {
            List<CheckTransactionReceiptAndInfoRepeat_In> Receipts = checkTransactionReceiptAndInfoRepeatIn.Where(r => r.TransactionReceipt != "" && r.TransactionReceipt != null).ToList();
            if (Receipts.Count > Receipts.Select(r => r.TransactionReceipt).Distinct().Count())
            {
                //throw new ApiException("回款交易凭证信息存在重复信息");
                return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息存在重复信息" };
            }
            List<CheckTransactionReceiptAndInfoRepeat_In> RepeatInfoList = checkTransactionReceiptAndInfoRepeatIn.Where(r => r.CollectingBankName != null
            && r.CollectingCompanyName != null && r.ArrivalDate != null &&
                r.PaymentCompanyName != null && r.Currency != null &&
                (r.ArrivalAmount != null || r.FCArrivalAmount != null) && r.PaymentMethod != null && (r.BankPaymentAmount != null || r.CashPaymentAmount != null)).ToList();
            if (RepeatInfoList.Count > 0)
            {
                bool RepeatInfo = RepeatInfoList.GroupBy(r => new
                {
                    r.CollectingBankName,
                    r.CollectingCompanyName,
                    r.ArrivalDate,
                    r.PaymentCompanyName,
                    r.Currency,
                    r.ArrivalAmount,
                    r.FCArrivalAmount,
                    r.PaymentMethod,
                    r.BankPaymentAmount,
                    r.CashPaymentAmount,
                    r.CashSourceRemarks
                }).Any(g => g.Count() > 1);
                if (RepeatInfo)
                {
                    //throw new ApiException("回款交易凭证信息存在重复信息");
                    return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息存在重复信息" };
                }
            }

            List<string> result = new List<string>();
            foreach (CheckTransactionReceiptAndInfoRepeat_In l in checkTransactionReceiptAndInfoRepeatIn)
            {
                if (l.TransactionReceipt.IsNotNullOrEmpty())
                {
                    List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetDataList(r => r.TransactionReceipt == l.TransactionReceipt).ToList();
                    if (list.Count > 0)
                    {
                        result.Add("“" + l.TransactionReceipt + "”");
                    }
                }

                if (l.CollectingBankName != null && l.CollectingCompanyName != null && l.ArrivalDate != null && l.PaymentCompanyName != null && l.Currency != null &&
                    (l.ArrivalAmount != null || l.FCArrivalAmount != null) && l.PaymentMethod != null && (l.BankPaymentAmount != null || l.CashPaymentAmount != null))
                {
                    List<Db_crm_collectioninfo> collectioninfoList = DbOpe_crm_collectioninfo.Instance.GetDataList(r =>
                        r.CollectingBankName == l.CollectingBankName &&
                        //r.BankAccountNumber == l.BankAccountNumber &&
                        r.CollectingCompanyName == l.CollectingCompanyName &&
                        r.ArrivalDate == l.ArrivalDate &&
                        r.PaymentCompanyName == l.PaymentCompanyName &&
                        r.Currency == l.Currency &&
                        r.ArrivalAmount == l.ArrivalAmount &&
                        r.FCArrivalAmount == l.FCArrivalAmount &&
                        r.PaymentMethod == l.PaymentMethod &&
                        r.BankPaymentAmount == l.BankPaymentAmount &&
                        r.CashPaymentAmount == l.CashPaymentAmount &&
                        r.CashSourceRemarks == l.CashSourceRemarks
                        ).ToList();
                    if (collectioninfoList.Count > 0)
                    {
                        result.Add("“" + l.PaymentCompanyName + "”");
                    }
                }
            }
            if (result.Count > 0)
            {
                //throw new ApiException("回款交易凭证信息已存在," + result.JoinToString(","));
                return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息已存在," + result.JoinToString(",") };
            }
            return new CheckCollectionInfoRepeat_Out();
        }

        public CheckCollectionInfoRepeat_Out CheckUpdateTransactionReceiptAndInfoRepeat(CheckUpdateTransactionReceiptAndInfoRepeat_In checkUpdateTransactionReceiptAndInfoRepeatIn)
        {
            if (checkUpdateTransactionReceiptAndInfoRepeatIn.TransactionReceipt != "" && checkUpdateTransactionReceiptAndInfoRepeatIn.TransactionReceipt != null)
            {
                List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetDataList(r => r.TransactionReceipt == checkUpdateTransactionReceiptAndInfoRepeatIn.TransactionReceipt && r.Id != checkUpdateTransactionReceiptAndInfoRepeatIn.Id).ToList();
                if (list.Count > 0)
                {
                    //throw new ApiException("回款交易凭证信息已存在," + checkUpdateTransactionReceiptAndInfoRepeatIn.TransactionReceipt);
                    return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息已存在,“" + checkUpdateTransactionReceiptAndInfoRepeatIn.TransactionReceipt + "”" };
                }
            }

            if (checkUpdateTransactionReceiptAndInfoRepeatIn.CollectingBankName != null && checkUpdateTransactionReceiptAndInfoRepeatIn.CollectingCompanyName != null && checkUpdateTransactionReceiptAndInfoRepeatIn.ArrivalDate != null && checkUpdateTransactionReceiptAndInfoRepeatIn.PaymentCompanyName != null && checkUpdateTransactionReceiptAndInfoRepeatIn.Currency != null &&
                    (checkUpdateTransactionReceiptAndInfoRepeatIn.ArrivalAmount != null || checkUpdateTransactionReceiptAndInfoRepeatIn.FCArrivalAmount != null) && checkUpdateTransactionReceiptAndInfoRepeatIn.PaymentMethod != null && (checkUpdateTransactionReceiptAndInfoRepeatIn.BankPaymentAmount != null || checkUpdateTransactionReceiptAndInfoRepeatIn.CashPaymentAmount != null))
            {
                List<Db_crm_collectioninfo> collectioninfoList = DbOpe_crm_collectioninfo.Instance.GetDataList(r =>
                   r.CollectingBankName == checkUpdateTransactionReceiptAndInfoRepeatIn.CollectingBankName &&
                   //r.BankAccountNumber == checkUpdateTransactionReceiptAndInfoRepeatIn.BankAccountNumber &&
                   r.CollectingCompanyName == checkUpdateTransactionReceiptAndInfoRepeatIn.CollectingCompanyName &&
                   r.ArrivalDate == checkUpdateTransactionReceiptAndInfoRepeatIn.ArrivalDate &&
                   r.PaymentCompanyName == checkUpdateTransactionReceiptAndInfoRepeatIn.PaymentCompanyName &&
                   r.Currency == checkUpdateTransactionReceiptAndInfoRepeatIn.Currency &&
                   r.ArrivalAmount == checkUpdateTransactionReceiptAndInfoRepeatIn.ArrivalAmount &&
                   r.FCArrivalAmount == checkUpdateTransactionReceiptAndInfoRepeatIn.FCArrivalAmount &&
                   r.PaymentMethod == checkUpdateTransactionReceiptAndInfoRepeatIn.PaymentMethod &&
                   r.BankPaymentAmount == checkUpdateTransactionReceiptAndInfoRepeatIn.BankPaymentAmount &&
                   r.CashPaymentAmount == checkUpdateTransactionReceiptAndInfoRepeatIn.CashPaymentAmount &&
                   r.CashSourceRemarks == checkUpdateTransactionReceiptAndInfoRepeatIn.CashSourceRemarks &&
                   r.Id != checkUpdateTransactionReceiptAndInfoRepeatIn.Id
                   ).ToList();
                if (collectioninfoList.Count > 0)
                {
                    //throw new ApiException("回款交易凭证信息已存在," + checkUpdateTransactionReceiptAndInfoRepeatIn.PaymentCompanyName);
                    return new CheckCollectionInfoRepeat_Out { Success = false, Message = "回款交易凭证信息已存在," + checkUpdateTransactionReceiptAndInfoRepeatIn.PaymentCompanyName };
                }
            }
            return new CheckCollectionInfoRepeat_Out();
        }

        /// <summary>
        /// 核对确认
        /// </summary>
        public void VerifyConfirmCollectionInfo(string id)
        {
            DbOpe_crm_contract.Instance.TransDeal(() =>
            {
                int VerifyConfirm = EnumVerifyConfirm.VerifyConfirm.ToInt();
                Db_crm_collectioninfo collectioninfo = DbOpe_crm_collectioninfo.Instance.GetDataById(id);
                if (collectioninfo == null)
                {
                    throw new ApiException("该回款信息不存在");
                }
                else if (collectioninfo.VerifyConfirm == VerifyConfirm)
                {
                    throw new ApiException("该回款信息已核对确认");
                }
                DbOpe_crm_collectioninfo.Instance.UpdateData(r => new Db_crm_collectioninfo { VerifyConfirm = VerifyConfirm, VerifyConfirmDate = DateTime.Now }, id);
            });
        }

        /// <summary>
        /// 撤销核对确认
        /// </summary>
        public void RevokeVerifyConfirmCollectionInfo(string id)
        {
            DbOpe_crm_contract.Instance.TransDeal(() =>
            {
                int NoVerifyConfirm = EnumVerifyConfirm.NoVerifyConfirm.ToInt();
                Db_crm_collectioninfo collectioninfo = DbOpe_crm_collectioninfo.Instance.GetDataById(id);
                if (collectioninfo == null)
                {
                    throw new ApiException("该回款信息不存在");
                }
                else if (collectioninfo.VerifyConfirm == NoVerifyConfirm)
                {
                    throw new ApiException("该回款信息未核对确认");
                }
                DbOpe_crm_collectioninfo.Instance.UpdateData(r => new Db_crm_collectioninfo { VerifyConfirm = NoVerifyConfirm, VerifyConfirmDate = null }, id);
            });
        }

        /// <summary>
        /// 修改回款信息
        /// </summary>
        public void UpdateCollectionArrivalDateInfo(UpdateCollectionArrivalDateInfo_In updateCollectionArrivalDateInfo)
        {
            string CollectingCompany = "";
            List<Db_crm_collectingcompany> collectingcompany = DbOpe_crm_collectingcompany.Instance.GetDataList(r => r.CollectingCompanyName == updateCollectionArrivalDateInfo.CollectingCompanyName);
            if (collectingcompany.Count() > 0)
            {
                CollectingCompany = collectingcompany.First().Id;
            }
            else
            {
                throw new ApiException("收款公司不存在");
            }

            DbOpe_crm_contract.Instance.TransDeal(() =>
            {
                Db_crm_collectioninfo collectioninfo = DbOpe_crm_collectioninfo.Instance.GetDataById(updateCollectionArrivalDateInfo.Id);
                if (collectioninfo.IsLocked == EnumLocked.Locked.ToInt())
                {
                    throw new ApiException("此回款信息已锁定，请先解锁后再操作");
                }
                Db_crm_collectioninfo_updateremark updateremark = new Db_crm_collectioninfo_updateremark();
                updateremark.CollectionInfoId = collectioninfo.Id;
                updateremark.CollectingCompany = CollectingCompany;
                updateremark.CollectingCompanyName = updateCollectionArrivalDateInfo.CollectingCompanyName;
                updateremark.ArrivalDate = updateCollectionArrivalDateInfo.ArrivalDate;
                updateremark.CollectingBankName = updateCollectionArrivalDateInfo.CollectingBankName;
                updateremark.HistoryCollectionInfoId = collectioninfo.Id;
                updateremark.HistoryCollectingCompany = collectioninfo.CollectingCompany;
                updateremark.HistoryCollectingCompanyName = collectioninfo.CollectingCompanyName;
                updateremark.HistoryArrivalDate = collectioninfo.ArrivalDate;
                updateremark.HistoryCollectingBankName = collectioninfo.CollectingBankName;
                updateremark.Remark = updateCollectionArrivalDateInfo.UpdateRemark;
                DbOpe_crm_collectioninfo_updateremark.Instance.InsertData(updateremark);
                DbOpe_crm_collectioninfo.Instance.UpdateData(r => new Db_crm_collectioninfo { CollectingCompany = CollectingCompany, CollectingCompanyName = updateCollectionArrivalDateInfo.CollectingCompanyName, ArrivalDate = updateCollectionArrivalDateInfo.ArrivalDate, CollectingBankName = updateCollectionArrivalDateInfo.CollectingBankName, UpdateRemark = updateCollectionArrivalDateInfo.UpdateRemark }, updateCollectionArrivalDateInfo.Id);
            });
        }

        public void LockCollectionLockStates(LockCollectionStates_In lockcollectionStates_In)
        {
            DbOpe_crm_collectioninfo.Instance.TransDeal(() =>
            {
                string ids = lockcollectionStates_In.Ids;
                List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetLockedCollectionInfos(ids);
                if (list.Count() > 0)
                {
                    throw new ApiException("包含已锁定到账登记信息,无法进行锁定操作");
                }

                DbOpe_crm_collectioninfo.Instance.UpdateCollectionLocked(ids);
            });
        }

        public void UnLockCollectionLockStates(LockCollectionStates_In lockcollectionStates_In)
        {
            DbOpe_crm_collectioninfo.Instance.TransDeal(() =>
            {
                string ids = lockcollectionStates_In.Ids;
                List<Db_crm_collectioninfo> list = DbOpe_crm_collectioninfo.Instance.GetUnLockedCollectionInfos(ids);
                if (list.Count() > 0)
                {
                    throw new ApiException("包含已锁定到账登记信息,无法进行锁定操作");
                }

                DbOpe_crm_collectioninfo.Instance.UpdateCollectionUnLocked(ids);
            });
        }
    }
}
