﻿// This file is auto-generated, don't edit it. Thanks.

using AlibabaCloud.OpenApiClient.Models;
using AlibabaCloud.SDK.Dingtalkoauth2_1_0;
using CRM2_API.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using DingTalk.Api.Request;
using DingTalk.Api.Response;
using DingTalk.Api;
using Tea;
using CRM2_API.Model.System;
using System.Text;

namespace CRM2_API.Common.DingTalk
{
    public class DingTalk
    {
        #region client
        // 单例对象锁
        private static readonly object _lock = new object();

        // 主Client单例
        private static Client _mainClient;
        public static Client MainClient
        {
            get
            {
                if (_mainClient == null)
                {
                    lock (_lock)
                    {
                        if (_mainClient == null)
                        {
                            var config = new Config { Protocol = "https", RegionId = "central" };
                            _mainClient = new Client(config);
                        }
                    }
                }
                return _mainClient;
            }
        }

        // HRM Client单例
        private static AlibabaCloud.SDK.Dingtalkhrm_1_0.Client _hrmClient;
        public static AlibabaCloud.SDK.Dingtalkhrm_1_0.Client HrmClient
        {
            get
            {
                if (_hrmClient == null)
                {
                    lock (_lock)
                    {
                        if (_hrmClient == null)
                        {
                            var config = new AlibabaCloud.OpenApiClient.Models.Config
                            {
                                Protocol = "https",
                                RegionId = "central"
                            };
                            _hrmClient = new AlibabaCloud.SDK.Dingtalkhrm_1_0.Client(config);
                        }
                    }
                }
                return _hrmClient;
            }
        }

        private static AlibabaCloud.SDK.Dingtalktodo_1_0.Client _todeClient;
        public static AlibabaCloud.SDK.Dingtalktodo_1_0.Client TodeClient{
        get
            {
                if (_todeClient == null)
                {
                    lock (_lock)
                    {
                        if (_todeClient == null)
                        {
                            var config = new AlibabaCloud.OpenApiClient.Models.Config
                            {
                                Protocol = "https",
                                RegionId = "central"
                            };
                            _todeClient = new AlibabaCloud.SDK.Dingtalktodo_1_0.Client(config);
                        }
                    }
                }
                return _todeClient;
            }
        }
        
        private static AlibabaCloud.SDK.Dingtalkcard_1_0.Client _cardClient;
        public static AlibabaCloud.SDK.Dingtalkcard_1_0.Client CardClient{
        get
            {
                if (_cardClient == null)
                {
                    lock (_lock)
                    {
                        if (_cardClient == null)
                        {
                            var config = new AlibabaCloud.OpenApiClient.Models.Config
                            {
                                Protocol = "https",
                                RegionId = "central"
                            };
                            _cardClient = new AlibabaCloud.SDK.Dingtalkcard_1_0.Client(config);
                        }
                    }
                }
                return _cardClient;
            }
        }
        
        #endregion


        public static string GetAcccessToken()
        {
            // 检查缓存是否存在有效token
            var token = RedisCache.DingTalkToken.Get();
            if (!string.IsNullOrEmpty(token))
            {
                return token;
            }
            var getAccessTokenRequest = new AlibabaCloud.SDK.Dingtalkoauth2_1_0.Models.GetAccessTokenRequest
            {
                AppKey = AppSettings.DingTalk.AppKey,
                AppSecret = AppSettings.DingTalk.AppSecret,
            };

            try
            {
                var response = MainClient.GetAccessToken(getAccessTokenRequest);
                var accessToken = response.Body.AccessToken;
                // 安全获取过期时间（处理空值情况）
                long expiresIn = response.Body.ExpireIn?? 0;

                // 添加保护性判断
                if (expiresIn <= 0)
                {
                    throw new InvalidOperationException("获取的token有效期无效");
                }

                // 带安全阈值的计算（保持原有逻辑）
                var safeExpireTime = expiresIn > 60 ? expiresIn - 60 : expiresIn;
                RedisCache.DingTalkToken.Set(accessToken, TimeSpan.FromSeconds(safeExpireTime));
                return accessToken;
            }
            catch (TeaException err)
            {
                // 处理异常，记录日志
                throw;
            }
            catch (Exception _err)
            {
                // 处理异常，记录日志
                throw;
            }
        }
        //public static string GetUserId(string code)
        //{
        //    try
        //    {
        //        IDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo");
        //        OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
        //        req.Code = code;
        //        OapiV2UserGetuserinfoResponse rsp = client.Execute(req, GetAcccessToken());
        //        if (rsp.Errcode == 0)
        //        {
        //            return rsp.Result.Userid;
        //        }
        //        else {
        //            throw new ApiException(rsp.ErrMsg);
        //        }
        //    }
        //    catch (TeaException err)
        //    {
        //        if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
        //        {
        //            // err 中含有 code 和 message 属性，可帮助开发定位问题
        //            throw new ApiException($"获取钉钉用户ID失败：{err.Message}");
        //        }
        //        throw new ApiException("获取钉钉用户ID失败");
        //    }
        //    catch (Exception _err)
        //    {
        //        TeaException err = new TeaException(new Dictionary<string, object>
        //        {
        //            { "message", _err.Message }
        //        });
        //        if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
        //        {
        //            // err 中含有 code 和 message 属性，可帮助开发定位问题
        //            throw new ApiException($"获取钉钉用户ID失败：{err.Message}");
        //        }
        //        throw new ApiException("获取钉钉用户ID失败");
        //    }
        //}

        /// <summary>
        /// 获取钉钉用户的unionid
        /// </summary>
        /// <param name="userId">钉钉用户ID</param>
        /// <returns>钉钉用户unionid</returns>
        public static string GetUserUnionId(string userId)
        {
            try
            {
                IDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/get");
                OapiUserGetRequest req = new OapiUserGetRequest();
                req.Userid = userId;
                OapiUserGetResponse rsp = client.Execute(req, GetAcccessToken());
                if (rsp.Errcode == 0)
                {
                    return rsp.Unionid;
                }
                else
                {
                    throw new ApiException(rsp.Errmsg);
                }
            }
            catch (TeaException err)
            {
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    throw new ApiException($"获取钉钉用户unionid失败：{err.Message}");
                }
                throw new ApiException("获取钉钉用户unionid失败");
            }
            catch (Exception _err)
            {
                TeaException err = new TeaException(new Dictionary<string, object>
                {
                    { "message", _err.Message }
                });
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    throw new ApiException($"获取钉钉用户unionid失败：{err.Message}");
                }
                throw new ApiException("获取钉钉用户unionid失败");
            }
        }

        public static string GetIDNo(string userId)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ApiException("钉钉用户ID不能为空");
                
            AlibabaCloud.SDK.Dingtalkhrm_1_0.Models.GetEmployeeRosterByFieldHeaders headers = new();
            string accessToken = GetAcccessToken();
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new ApiException("获取钉钉访问令牌失败");
            }
            headers.XAcsDingtalkAccessToken = accessToken; 
            
            // 从配置中获取AgentId
            long agentId = AppSettings.DingTalk.AgentId;
            
            // 如果配置中的AgentId为0，则使用默认值
            if (agentId == 0)
            {
                agentId = 3535939366; // 默认AgentId
                LogUtil.AddLog("警告：未配置钉钉应用的AgentId，使用默认值");
            }
            
            var request = new AlibabaCloud.SDK.Dingtalkhrm_1_0.Models.GetEmployeeRosterByFieldRequest
            {
                UserIdList = new List<string>
                {
                   userId
                },
                FieldFilterList = new List<string>
                {
                    "sys02-certNo"
                },
                AppAgentId = agentId,
                Text2SelectConvert = true,
            };
            try
            {
                var response = HrmClient.GetEmployeeRosterByFieldWithOptions(request, headers, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());

                // 检查响应是否为空
                if (response == null || response.Body == null)
                {
                    throw new ApiException("获取身份证信息失败：钉钉返回的响应为空");
                }
                
                // 检查结果列表是否为空
                if (response.Body.Result == null || !response.Body.Result.Any())
                {
                    throw new ApiException("获取身份证信息失败：钉钉返回的结果列表为空");
                }
                
                // 检查字段数据列表是否为空
                var firstResult = response.Body.Result.FirstOrDefault();
                if (firstResult.FieldDataList == null || !firstResult.FieldDataList.Any())
                {
                    throw new ApiException("未能获取到您的身份证信息，请联系管理员或HR确认您的钉钉智能人事花名册中是否已填写身份证号");
                }
                
                // 检查字段值列表是否为空
                var firstFieldData = firstResult.FieldDataList.FirstOrDefault();
                if (firstFieldData.FieldValueList == null || !firstFieldData.FieldValueList.Any())
                {
                    throw new ApiException("未能获取到您的身份证信息，请联系管理员或HR确认您的钉钉智能人事花名册中是否已填写身份证号");
                }

                // 添加空值检查防止空引用异常
                if (firstFieldData.FieldValueList.FirstOrDefault()?.Value is string value)
                {
                    if (string.IsNullOrEmpty(value))
                    {
                        throw new ApiException("未能获取到您的身份证信息，请联系管理员或HR确认您的钉钉智能人事花名册中是否已填写身份证号");
                    }
                    return value;
                }
                throw new ApiException("未能获取到您的身份证信息，请联系管理员或HR确认您的钉钉智能人事花名册中是否已填写身份证号");
            }
            catch (TeaException err)
            {
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    throw new ApiException($"获取身份证信息失败：{err.Message}，错误码：{err.Code}，请联系管理员");
                }
                throw new ApiException($"获取身份证信息失败：{err.Message}，请联系管理员");
            }
            catch (Exception _err)
            {
                TeaException err = new TeaException(new Dictionary<string, object>
                {
                    { "message", _err.Message }
                });
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    throw new ApiException($"获取身份证信息失败：{err.Message}，错误码：{err.Code}，请联系管理员");
                }
                throw new ApiException($"获取身份证信息失败：{_err.Message}，请联系管理员");
            }
        }

        /// <summary>
        /// 通过免登录code同时获取钉钉用户ID和unionId
        /// </summary>
        /// <param name="code">钉钉免登录code</param>
        /// <returns>包含userId和unionId的元组</returns>
        public static (string userId, string unionId) GetUserIdAndUnionId(string code)
        {
            if (string.IsNullOrEmpty(code))
                throw new ApiException("钉钉免登录code不能为空");

            // 获取钉钉用户ID
            string userId = string.Empty;
            string unionId = string.Empty;
            try
            {
                IDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo");
                OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
                req.Code = code;
                string accessToken = GetAcccessToken();
                if (string.IsNullOrEmpty(accessToken))
                {
                    throw new ApiException("获取钉钉访问令牌失败");
                }
                
                OapiV2UserGetuserinfoResponse rsp = client.Execute(req, accessToken);
                if (rsp.Errcode == 0)
                {
                    if (rsp.Result == null || string.IsNullOrEmpty(rsp.Result.Userid))
                    {
                        throw new ApiException("钉钉返回的用户ID为空");
                    }
                    userId = rsp.Result.Userid;
                    unionId = rsp.Result.Unionid;
                }
                else
                {
                    throw new ApiException($"获取钉钉用户信息失败：{rsp.ErrMsg}，错误码：{rsp.Errcode}");
                }
            }
            catch (TeaException err)
            {
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"获取钉钉用户信息失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"获取钉钉用户信息失败：{err.Message}");
            }
            catch (Exception _err)
            {
                TeaException err = new TeaException(new Dictionary<string, object>
                {
                    { "message", _err.Message }
                });
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"获取钉钉用户信息失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"获取钉钉用户信息失败：{_err.Message}");
            }

            return (userId, unionId);
        }


        public class TodoTaskUrlModel
        {
            public string appUrl { get; set; }
            public string pcUrl { get; set; }
        }

        /// <summary>
        /// 创建钉钉待办任务
        /// </summary>
        /// <param name="userId">钉钉用户ID</param>
        /// <param name="bizId">业务ID，用于唯一标识一个待办</param>
        /// <param name="subject">待办标题</param>
        /// <param name="description">待办详情</param>
        /// <param name="dueTime">截止时间，格式：yyyy-MM-dd HH:mm:ss</param>
        /// <param name="detailUrl">待办详情跳转链接</param>
        /// <param name="priority">优先级，取值：10-较低，20-普通，30-紧急，40-非常紧急</param>
        /// <param name="unionId">钉钉用户unionId，如果提供则直接使用，不再调用接口获取</param>
        /// <returns>待办任务ID</returns>
        public static string CreateTodoTask(string userId, string bizId, string subject, string description, DateTime? dueTime = null, TodoTaskUrlModel detailUrl = null, int priority = 20, string unionId = null)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ApiException("钉钉用户ID不能为空");
            
            if (string.IsNullOrEmpty(subject))
                throw new ApiException("待办标题不能为空");

            try
            {
                // 获取用户的unionId
                if (string.IsNullOrEmpty(unionId))
                {
                    // 如果没有提供unionId，则从接口获取
                    unionId = GetUserUnionId(userId);
                    if (string.IsNullOrEmpty(unionId))
                    {
                        throw new ApiException("获取钉钉用户unionId失败");
                    }
                }

                // 创建待办任务
                AlibabaCloud.SDK.Dingtalktodo_1_0.Client client = TodeClient;
                AlibabaCloud.SDK.Dingtalktodo_1_0.Models.CreateTodoTaskHeaders createTodoTaskHeaders = new AlibabaCloud.SDK.Dingtalktodo_1_0.Models.CreateTodoTaskHeaders();
                
                string accessToken = GetAcccessToken();
                if (string.IsNullOrEmpty(accessToken))
                {
                    throw new ApiException("获取钉钉访问令牌失败");
                }
                createTodoTaskHeaders.XAcsDingtalkAccessToken = accessToken;
                
                // 配置通知
                AlibabaCloud.SDK.Dingtalktodo_1_0.Models.CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs notifyConfigs = new AlibabaCloud.SDK.Dingtalktodo_1_0.Models.CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs
                {
                    DingNotify = "1", // 1-钉提醒，0-不提醒
                };
                
                // 配置详情链接
                AlibabaCloud.SDK.Dingtalktodo_1_0.Models.CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl detailUrlObj = null;
                if (detailUrl!=null)
                {
                    detailUrlObj = new AlibabaCloud.SDK.Dingtalktodo_1_0.Models.CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl
                    {
                        AppUrl = detailUrl.appUrl,
                        PcUrl = detailUrl.pcUrl,
                    };
                }
                
                
                // 创建待办请求
                AlibabaCloud.SDK.Dingtalktodo_1_0.Models.CreateTodoTaskRequest createTodoTaskRequest = new AlibabaCloud.SDK.Dingtalktodo_1_0.Models.CreateTodoTaskRequest
                {
                    OperatorId = unionId,
                    SourceId = bizId,
                    Subject = subject,
                    CreatorId = unionId,
                    Description = description,
                    ExecutorIds = new List<string> { unionId }, // 执行者
                    NotifyConfigs = notifyConfigs,
                    Priority = priority,
                    DetailUrl = detailUrlObj,
                    IsOnlyShowExecutor = true // 仅执行者可见
                };
                
                // 设置截止时间
                if (dueTime.HasValue)
                {
                    // 转换为时间戳（毫秒）
                    long timestamp = new DateTimeOffset(dueTime.Value).ToUnixTimeMilliseconds();
                    createTodoTaskRequest.DueTime = timestamp;
                }
                
                var response = client.CreateTodoTaskWithOptions(unionId, createTodoTaskRequest, createTodoTaskHeaders, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());
                
                if (response != null && response.Body != null && !string.IsNullOrEmpty(response.Body.Id))
                {
                    return response.Body.Id;
                }
                else
                {
                    throw new ApiException("创建钉钉待办任务失败：返回的任务ID为空");
                }
            }
            catch (TeaException err)
            {
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"创建钉钉待办任务失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"创建钉钉待办任务失败：{err.Message}");
            }
            catch (Exception _err)
            {
                TeaException err = new TeaException(new Dictionary<string, object>
                {
                    { "message", _err.Message }
                });
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"创建钉钉待办任务失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"创建钉钉待办任务失败：{_err.Message}");
            }
        }
        
        /// <summary>
        /// 删除钉钉待办任务
        /// </summary>
        /// <param name="userId">钉钉用户ID</param>
        /// <param name="taskId">待办任务ID</param>
        /// <param name="unionId">钉钉用户unionId，如果提供则直接使用，不再调用接口获取</param>
        /// <returns>是否删除成功</returns>
        public static bool DeleteTodoTask(string userId, string taskId, string unionId = null)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ApiException("钉钉用户ID不能为空");
            
            if (string.IsNullOrEmpty(taskId))
                throw new ApiException("待办任务ID不能为空");

            try
            {
                // 获取用户的unionId
                if (string.IsNullOrEmpty(unionId))
                {
                    // 如果没有提供unionId，则从接口获取
                    unionId = GetUserUnionId(userId);
                    if (string.IsNullOrEmpty(unionId))
                    {
                        throw new ApiException("获取钉钉用户unionId失败");
                    }
                }

                // 删除待办任务
                AlibabaCloud.SDK.Dingtalktodo_1_0.Client client = TodeClient;
                AlibabaCloud.SDK.Dingtalktodo_1_0.Models.DeleteTodoTaskHeaders deleteTodoTaskHeaders = new AlibabaCloud.SDK.Dingtalktodo_1_0.Models.DeleteTodoTaskHeaders();
                
                string accessToken = GetAcccessToken();
                if (string.IsNullOrEmpty(accessToken))
                {
                    throw new ApiException("获取钉钉访问令牌失败");
                }
                deleteTodoTaskHeaders.XAcsDingtalkAccessToken = accessToken;
                
                // 创建删除请求
                AlibabaCloud.SDK.Dingtalktodo_1_0.Models.DeleteTodoTaskRequest deleteTodoTaskRequest = new AlibabaCloud.SDK.Dingtalktodo_1_0.Models.DeleteTodoTaskRequest
                {
                    OperatorId = unionId,
                };
                
                var response = client.DeleteTodoTaskWithOptions(unionId, taskId, deleteTodoTaskRequest, deleteTodoTaskHeaders, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());
                
                return response != null && response.Body != null && response.Body.Result != null && response.Body.Result.Value;
            }
            catch (TeaException err)
            {
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"删除钉钉待办任务失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"删除钉钉待办任务失败：{err.Message}");
            }
            catch (Exception _err)
            {
                TeaException err = new TeaException(new Dictionary<string, object>
                {
                    { "message", _err.Message }
                });
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"删除钉钉待办任务失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"删除钉钉待办任务失败：{_err.Message}");
            }
        }
        
        /// <summary>
        /// 更新钉钉待办任务
        /// </summary>
        /// <param name="userId">钉钉用户ID</param>
        /// <param name="taskId">待办任务ID</param>
        /// <param name="isDone">是否完成</param>
        /// <param name="subject">待办标题（可选）</param>
        /// <param name="description">待办详情（可选）</param>
        /// <param name="dueTime">截止时间（可选）</param>
        /// <param name="unionId">钉钉用户unionId，如果提供则直接使用，不再调用接口获取</param>
        /// <returns>是否更新成功</returns>
        public static bool UpdateTodoTask(string userId, string taskId, bool isDone, string unionId , string subject = "", string description = "", DateTime? dueTime = null)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ApiException("钉钉用户ID不能为空");
            
            if (string.IsNullOrEmpty(taskId))
                throw new ApiException("待办任务ID不能为空");

            try
            {
                // 获取用户的unionId
                if (string.IsNullOrEmpty(unionId))
                {
                    // 如果没有提供unionId，则从接口获取
                    unionId = GetUserUnionId(userId);
                    if (string.IsNullOrEmpty(unionId))
                    {
                        throw new ApiException("获取钉钉用户unionId失败");
                    }
                }

                // 更新待办任务
                AlibabaCloud.SDK.Dingtalktodo_1_0.Client client = TodeClient;
                AlibabaCloud.SDK.Dingtalktodo_1_0.Models.UpdateTodoTaskHeaders updateTodoTaskHeaders = new AlibabaCloud.SDK.Dingtalktodo_1_0.Models.UpdateTodoTaskHeaders();
                
                string accessToken = GetAcccessToken();
                if (string.IsNullOrEmpty(accessToken))
                {
                    throw new ApiException("获取钉钉访问令牌失败");
                }
                updateTodoTaskHeaders.XAcsDingtalkAccessToken = accessToken;
                
                // 创建更新请求
                AlibabaCloud.SDK.Dingtalktodo_1_0.Models.UpdateTodoTaskRequest updateTodoTaskRequest = new AlibabaCloud.SDK.Dingtalktodo_1_0.Models.UpdateTodoTaskRequest
                {
                    OperatorId = unionId,
                    ExecutorIds = new List<string> { unionId },
                };
                
                // 设置可选参数
                if (!string.IsNullOrEmpty(subject))
                {
                    updateTodoTaskRequest.Subject = subject;
                }
                
                if (!string.IsNullOrEmpty(description))
                {
                    updateTodoTaskRequest.Description = description;
                }
                
                if (dueTime.HasValue)
                {
                    // 转换为时间戳（毫秒）
                    long timestamp = new DateTimeOffset(dueTime.Value).ToUnixTimeMilliseconds();
                    updateTodoTaskRequest.DueTime = timestamp;
                }
                updateTodoTaskRequest.Done = isDone;

                var response = client.UpdateTodoTaskWithOptions(unionId, taskId, updateTodoTaskRequest, updateTodoTaskHeaders, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());
                
                return response != null && response.Body != null && response.Body.Result != null && response.Body.Result.Value;
            }
            catch (TeaException err)
            {
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"更新钉钉待办任务失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"更新钉钉待办任务失败：{err.Message}");
            }
            catch (Exception _err)
            {
                TeaException err = new TeaException(new Dictionary<string, object>
                {
                    { "message", _err.Message }
                });
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"更新钉钉待办任务失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"更新钉钉待办任务失败：{_err.Message}");
            }
        }

        public class InfoCardModel { 
            public string title { get; set; } 
            public string data1 { get; set; } 
            public string data2 { get; set; } 
            public string cont { get; set; } 
            public string type { get; set; } 
            public string createTime { get; set; } 
            public string url { get; set; } 
            public string urlmobile { get; set; } 
        }

        /// <summary>
        /// 创建并发送钉钉信息卡片
        /// </summary>
        /// <param name="userId">接收卡片的用户ID</param>
        /// <param name="cardTemplateId">卡片模板ID</param>
        /// <param name="content">卡片内容</param>
        /// <param name="outTrackId">外部追踪ID，用于唯一标识一个卡片</param>
        /// <param name="cardParamMap">卡片参数映射，用于传递额外参数</param>
        /// <param name="robotCode">机器人编码，如果为空则使用配置中的默认值</param>
        /// <returns>卡片实例ID</returns>
        public static string CreateInfoCard(string userId, string cardTemplateId, InfoCardModel content, string outTrackId = null, Dictionary<string, string> cardParamMap = null, string robotCode = null)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ApiException("接收卡片的用户ID不能为空");
            
            if (string.IsNullOrEmpty(cardTemplateId))
                throw new ApiException("卡片模板ID不能为空");
            
            if (content == null)
                throw new ApiException("卡片标题不能为空");

            try
            {
                AlibabaCloud.SDK.Dingtalkcard_1_0.Client client = CardClient;
                AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverHeaders createAndDeliverHeaders = new AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverHeaders();
                
                string accessToken = GetAcccessToken();
                if (string.IsNullOrEmpty(accessToken))
                {
                    throw new ApiException("获取钉钉访问令牌失败");
                }
                createAndDeliverHeaders.XAcsDingtalkAccessToken = accessToken;
                
                // 如果没有提供outTrackId，则生成一个唯一ID
                if (string.IsNullOrEmpty(outTrackId))
                {
                    outTrackId = Guid.NewGuid().ToString("N");
                }
                
                // 如果没有提供cardParamMap，则创建一个新的字典
                if (cardParamMap == null)
                {
                    cardParamMap = new Dictionary<string, string>();
                }
                
                // 添加标题和内容到参数映射
                cardParamMap["title"] = content.title;
                cardParamMap["data1"] = content.data1;
                cardParamMap["data2"] = content.data2;
                cardParamMap["cont"] = content.cont;
                cardParamMap["type"] = content.type;
                cardParamMap["createTime"] = content.createTime;
                
                // 添加URL到参数映射（如果存在）
                if (!string.IsNullOrEmpty(content.url))
                {
                    cardParamMap["url"] = content.url;
                }
                
                // 添加移动URL到参数映射（如果存在）
                if (!string.IsNullOrEmpty(content.urlmobile))
                {
                    cardParamMap["urlmobile"] = content.urlmobile;
                }

                // 创建卡片数据
                AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest.CreateAndDeliverRequestCardData cardData = new AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest.CreateAndDeliverRequestCardData
                {
                    CardParamMap = cardParamMap,
                };
                
                // 创建单聊机器人空间模型
                AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModel.CreateAndDeliverRequestImRobotOpenSpaceModelNotification notification = new AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModel.CreateAndDeliverRequestImRobotOpenSpaceModelNotification
                {
                    AlertContent = content.title,
                    NotificationOff = false,
                };
                
                AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModel imRobotOpenSpaceModel = new AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModel
                {
                    SupportForward = true,
                    Notification = notification,
                };
                
                // 创建单聊机器人投递模型
                Dictionary<string, string> extension = new Dictionary<string, string>
                {
                    {"key", "value"},
                };
                
                // 如果没有提供robotCode，则从配置中获取
                if (string.IsNullOrEmpty(robotCode))
                {
                    // 从AppSettings中获取RobotCode
                    robotCode = AppSettings.DingTalk.RobotCode;
                    
                    // 如果配置中也没有设置，则使用默认值
                    if (string.IsNullOrEmpty(robotCode))
                    {
                        robotCode = "dingg3xmqdkpaojuakm8"; // 默认机器人编码
                        LogUtil.AddLog("警告：未配置钉钉机器人编码(RobotCode)，使用默认值");
                    }
                }
                
                AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenDeliverModel imRobotOpenDeliverModel = new AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenDeliverModel
                {
                    SpaceType = "IM_ROBOT",
                    RobotCode = robotCode,
                    Extension = extension,
                };
                
                // 创建请求
                AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest createAndDeliverRequest = new AlibabaCloud.SDK.Dingtalkcard_1_0.Models.CreateAndDeliverRequest
                {
                    UserId = userId,
                    CardTemplateId = cardTemplateId,
                    OutTrackId = outTrackId,
                    CardData = cardData,
                    ImRobotOpenSpaceModel = imRobotOpenSpaceModel,
                    ImRobotOpenDeliverModel = imRobotOpenDeliverModel,
                    OpenSpaceId = "dtv1.card//IM_ROBOT." + userId,
                    UserIdType = 1, // 1表示userId是钉钉userId
                };
                
                var response = client.CreateAndDeliverWithOptions(createAndDeliverRequest, createAndDeliverHeaders, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());
                
                if (response != null && response.Body != null && response.Body.Result != null)
                {
                    // 根据API文档，Result可能是一个对象，我们需要将其转换为字符串
                    // 这里假设Result对象有一个ToString()方法或者可以通过某种方式获取其字符串表示
                    return response.Body.Result.ToString();
                }
                else
                {
                    throw new ApiException("创建钉钉信息卡片失败：返回的卡片实例ID为空");
                }
            }
            catch (TeaException err)
            {
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    throw new ApiException($"创建钉钉信息卡片失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"创建钉钉信息卡片失败：{err.Message}");
            }
            catch (Exception _err)
            {
                TeaException err = new TeaException(new Dictionary<string, object>
                {
                    { "message", _err.Message }
                });
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    throw new ApiException($"创建钉钉信息卡片失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"创建钉钉信息卡片失败：{_err.Message}");
            }
        }

        /// <summary>
        /// 获取 JSAPI Ticket
        /// </summary>
        /// <returns>JSAPI Ticket</returns>
        public static string GetJsApiTicket()
        {
            // 检查缓存是否存在有效 ticket
            var ticket = RedisCache.DingTalkJsapiTicket.Get();
            if (!string.IsNullOrEmpty(ticket))
            {
                return ticket;
            }

            try
            {
                IDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/get_jsapi_ticket");
                OapiGetJsapiTicketRequest req = new OapiGetJsapiTicketRequest();
                req.SetHttpMethod("GET");
                OapiGetJsapiTicketResponse response = client.Execute(req, GetAcccessToken());
                
                if (response.Errcode != 0)
                {
                    throw new ApiException($"获取 JSAPI Ticket 失败：{response.Errmsg}，错误码：{response.Errcode}");
                }
                
                var jsapiTicket = response.Ticket;
                // 安全获取过期时间（处理空值情况）
                long expiresIn = response.ExpiresIn > 0 ? response.ExpiresIn : 7200; // 默认过期时间为 7200 秒

                // 带安全阈值的计算
                var safeExpireTime = expiresIn > 60 ? expiresIn - 60 : expiresIn;
                RedisCache.DingTalkJsapiTicket.Set(jsapiTicket, TimeSpan.FromSeconds(safeExpireTime));
                return jsapiTicket;
            }
            catch (TeaException err)
            {
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"获取 JSAPI Ticket 失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"获取 JSAPI Ticket 失败：{err.Message}");
            }
            catch (Exception _err)
            {
                TeaException err = new TeaException(new Dictionary<string, object>
                {
                    { "message", _err.Message }
                });
                if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
                {
                    throw new ApiException($"获取 JSAPI Ticket 失败：{err.Message}，错误码：{err.Code}");
                }
                throw new ApiException($"获取 JSAPI Ticket 失败：{_err.Message}");
            }
        }

        /// <summary>
        /// 生成 JSAPI 签名
        /// </summary>
        /// <param name="url">当前网页的URL，不包含#及其后面部分</param>
        /// <returns>JSAPI 签名相关信息</returns>
        public static JsapiSignature GetJsapiSignature(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                throw new ApiException("URL不能为空");
            }

            try
            {
                string nonceStr = Guid.NewGuid().ToString().Replace("-", "");
                long timeStamp = DateTimeOffset.Now.ToUnixTimeSeconds();
                string ticket = GetJsApiTicket();

                if (string.IsNullOrEmpty(ticket))
                {
                    throw new ApiException("获取 JSAPI Ticket 失败");
                }

                // 处理URL（参照Java版本的decodeUrl方法）
                url = DecodeUrl(url);

                // 按照钉钉 JSAPI 签名规则拼接字符串
                StringBuilder sb = new StringBuilder();
                sb.Append("jsapi_ticket=").Append(ticket).Append("&")
                  .Append("noncestr=").Append(nonceStr).Append("&")
                  .Append("timestamp=").Append(timeStamp).Append("&")
                  .Append("url=").Append(url);

                // 记录签名字符串，便于调试
                LogUtil.AddLog($"签名字符串: {sb.ToString()}");
                
                // 使用SHA-256算法计算签名
                string signature = ComputeSHA256Hash(sb.ToString());
                LogUtil.AddLog($"生成的签名: {signature}");

                return new JsapiSignature
                {
                    AgentId = AppSettings.DingTalk.AgentId,
                    CorpId = AppSettings.DingTalk.CorpId,
                    TimeStamp = timeStamp,
                    NonceStr = nonceStr,
                    Signature = signature,
                    JsApiTicket = ticket,
                    Url = url
                };
            }
            catch (Exception ex) when (!(ex is ApiException))
            {
                throw new ApiException($"生成 JSAPI 签名失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 参照Java代码实现URL处理，增强了错误处理
        /// </summary>
        /// <param name="url">原始URL</param>
        /// <returns>处理后的URL</returns>
        private static string DecodeUrl(string url)
        {
            try
            {
                // 记录输入URL
                LogUtil.AddLog($"原始URL: {url}");
                
                // 检查URL格式
                if (string.IsNullOrEmpty(url))
                {
                    LogUtil.AddLog("URL为空，返回空字符串");
                    return string.Empty;
                }
                
                // 检查URL是否是百分号编码形式，如果是则先解码
                if (url.Contains("%3A%2F%2F") || (url.StartsWith("http%3A") || url.StartsWith("https%3A")))
                {
                    try 
                    {
                        string decodedUrl = System.Web.HttpUtility.UrlDecode(url);
                        LogUtil.AddLog($"URL是编码形式，已解码: {decodedUrl}");
                        url = decodedUrl;
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddLog($"URL编码解码失败: {ex.Message}");
                        // 继续使用原始URL
                    }
                }
                
                // 修复常见URL错误：替换逗号为点号
                if (url.Contains(","))
                {
                    string fixedUrl = url.Replace(",", ".");
                    LogUtil.AddLog($"URL中存在逗号，已替换为点号: {fixedUrl}");
                    url = fixedUrl;
                }
                
                // 尝试创建URI对象
                Uri uri;
                if (!Uri.TryCreate(url, UriKind.Absolute, out uri))
                {
                    LogUtil.AddLog($"无法解析URL为绝对URI: {url}，返回处理后的URL");
                    
                    // 如果还是无法解析，但看起来像URL，手动规范化
                    if (url.Contains("://") || url.StartsWith("http:") || url.StartsWith("https:"))
                    {
                        // 移除URL中的井号(#)及其后面内容
                        int indexOfSharp = url.IndexOf('#');
                        if (indexOfSharp >= 0)
                        {
                            url = url.Substring(0, indexOfSharp);
                            LogUtil.AddLog($"手动移除#及后内容: {url}");
                        }
                        return url;
                    }
                    return url;
                }
                
                // 构建处理后的URL，精确保持原始URL格式
                StringBuilder urlBuffer = new StringBuilder();
                urlBuffer.Append(uri.Scheme);
                urlBuffer.Append(":");
                
                if (!string.IsNullOrEmpty(uri.Authority))
                {
                    urlBuffer.Append("//");
                    urlBuffer.Append(uri.Authority);
                }
                
                // 添加路径但保持原始URL的斜杠格式
                string originalPath = uri.AbsolutePath;
                
                // 如果原始URL没有以斜杠结尾，并且处理后路径添加了斜杠，则移除尾部斜杠
                if (!url.TrimEnd('/').Equals(url) || !originalPath.TrimEnd('/').Equals(originalPath))
                {
                    // 原始URL确实包含尾部斜杠，保留它
                    urlBuffer.Append(originalPath);
                }
                else
                {
                    // 原始URL不包含尾部斜杠，确保我们也不添加
                    if (originalPath.EndsWith("/") && originalPath.Length > 1)
                    {
                        urlBuffer.Append(originalPath.TrimEnd('/'));
                    }
                    else
                    {
                        urlBuffer.Append(originalPath);
                    }
                }
                
                // 检查原始URL是否有尾部斜杠
                bool originalHasTrailingSlash = url.EndsWith("/");
                
                // 如果路径部分为空或只有根路径"/"，并且原始URL没有尾部斜杠，则移除尾部斜杠
                if (string.IsNullOrEmpty(originalPath) || originalPath == "/")
                {
                    if (!originalHasTrailingSlash)
                    {
                        // 确保不添加尾部斜杠
                        LogUtil.AddLog("原始URL不包含尾部斜杠，确保处理后也不包含");
                    }
                }
                
                if (!string.IsNullOrEmpty(uri.Query))
                {
                    urlBuffer.Append('?');
                    try
                    {
                        // 解码查询参数
                        string decodedQuery = System.Web.HttpUtility.UrlDecode(uri.Query.TrimStart('?'), System.Text.Encoding.UTF8);
                        urlBuffer.Append(decodedQuery);
                    }
                    catch (Exception ex)
                    {
                        // 如果解码失败，使用原始查询字符串
                        LogUtil.AddLog($"查询参数解码失败: {ex.Message}，使用原始查询参数");
                        urlBuffer.Append(uri.Query.TrimStart('?'));
                    }
                }
                
                string result = urlBuffer.ToString();
                
                // 最终检查，确保处理前后URL的尾部斜杠情况保持一致
                bool resultHasTrailingSlash = result.EndsWith("/");
                if (originalHasTrailingSlash && !resultHasTrailingSlash)
                {
                    // 原始有斜杠，结果没有，添加斜杠
                    result += "/";
                }
                else if (!originalHasTrailingSlash && resultHasTrailingSlash && result.Length > 8) // "https://" 长度为8
                {
                    // 原始没有斜杠，结果有斜杠，移除斜杠（但不处理http://这种情况）
                    result = result.TrimEnd('/');
                }
                
                LogUtil.AddLog($"处理后URL: {result}");
                
                return result;
            }
            catch (Exception ex)
            {
                // 如果解析失败，记录异常并返回原始URL
                LogUtil.AddLog($"URL处理异常详情: {ex.GetType().Name} - {ex.Message}");
                if (ex.InnerException != null)
                {
                    LogUtil.AddLog($"内部异常: {ex.InnerException.Message}");
                }
                LogUtil.AddLog($"异常堆栈: {ex.StackTrace}");
                
                // 尝试进行紧急URL解码
                try
                {
                    // 先尝试URL解码
                    if (url.Contains("%"))
                    {
                        string decodedUrl = System.Web.HttpUtility.UrlDecode(url);
                        LogUtil.AddLog($"紧急URL解码: {decodedUrl}");
                        
                        // 再尝试移除#
                        int indexOfSharp = decodedUrl.IndexOf('#');
                        if (indexOfSharp >= 0)
                        {
                            string basicProcessedUrl = decodedUrl.Substring(0, indexOfSharp);
                            LogUtil.AddLog($"紧急处理: 解码并去除#号: {basicProcessedUrl}");
                            return basicProcessedUrl;
                        }
                        return decodedUrl;
                    }
                
                    // 尝试最基本的URL处理：移除URL中的井号(#)及其后面内容
                    int indexOfSharp2 = url.IndexOf('#');
                    if (indexOfSharp2 >= 0)
                    {
                        string basicProcessedUrl = url.Substring(0, indexOfSharp2);
                        LogUtil.AddLog($"基本URL处理(去除#号及后内容): {basicProcessedUrl}");
                        return basicProcessedUrl;
                    }
                }
                catch (Exception innerEx)
                {
                    LogUtil.AddLog($"紧急URL处理异常: {innerEx.Message}");
                    // 继续返回原始URL
                }
                
                return url;
            }
        }

        /// <summary>
        /// 使用SHA-256计算哈希
        /// </summary>
        /// <param name="text">待加密文本</param>
        /// <returns>SHA-256哈希值（小写十六进制）</returns>
        private static string ComputeSHA256Hash(string text)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(text));
                return BitConverter.ToString(hash).Replace("-", "").ToLower();
            }
        }

        /// <summary>
        /// JSAPI签名信息
        /// </summary>
        public class JsapiSignature
        {
            /// <summary>
            /// 企业ID
            /// </summary>
            public string CorpId { get; set; }

            /// <summary>
            /// 应用ID
            /// </summary>
            public long AgentId { get; set; }

            /// <summary>
            /// 时间戳
            /// </summary>
            public long TimeStamp { get; set; }

            /// <summary>
            /// 随机字符串
            /// </summary>
            public string NonceStr { get; set; }

            /// <summary>
            /// 签名
            /// </summary>
            public string Signature { get; set; }

            /// <summary>
            /// JSAPI票据
            /// </summary>
            public string JsApiTicket { get; set; }

            /// <summary>
            /// 当前URL
            /// </summary>
            public string Url { get; set; }
        }
    }
}