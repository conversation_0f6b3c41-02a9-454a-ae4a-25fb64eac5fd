﻿using CRM2_API.Common.AppSetting;
using CRM2_API.Model.System;
using Newtonsoft.Json.Linq;

namespace CRM2_API.BLL.Common
{
    /// <summary>
    /// 手机帮助类(发送的模板在数据库表中gtis4.SysSMSTemplate)
    /// </summary>
    public class Com_PhoneHelper
    {
        //申请模板的方法：https://help.aliyun.com/document_detail/108088.html?spm=a2c4g.11186623.0.0.22fd73fa3FeKqM

        /// <summary>
        /// 发送登录验证码，验证码内容：验证码${code}，您正在登录，若非本人操作，请勿泄露。
        /// </summary>
        /// <param name="phone">手机号</param>
        /// <param name="code">验证码</param>
        public static void SendLoginCheckCode(string phone, string code)
        {
            Send(phone, new JObject { { "code", code } }, EnumMessageTemplate.SMS_190505518);
        }

        /// <summary>
        /// 发送用户注册信息：您好！${name}，您的慧思CRM系统的登账账号：${username}，登录密码：${password}，请即时登录使用。若有疑问请联系管理员！
        /// </summary>
        /// <param name="phone"></param>
        /// <param name="name"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        public static void SendAddUserInfo(string phone, string name, string username, string password)
        {
            Send(phone, new JObject { { "name", name }, { "username", username }, { "password", password } }, EnumMessageTemplate.SMS_463785408);
        }

        /// <summary>
        /// 您好！${name}，您的慧思CRM系统的登录密码重置成功，新登录密码：${password}，请即时登录使用。若有疑问请联系管理员！
        /// </summary>
        /// <param name="phone"></param>
        /// <param name="name"></param>
        /// <param name="password"></param>
        public static void SendResetUserPwd(string phone, string name, string password)
        {
            //{ "phone", phone }, { "name", name }, 
            Send(phone, new JObject { { "password", password } }, EnumMessageTemplate.SMS_463720418);
        }
        /// <summary>
        /// 短信模板枚举，枚举模板在数据库中gtis4.SysSMSTemplate
        /// </summary>
        enum EnumMessageTemplate
        {
            /// <summary>
            /// 验证码${code}，您正在登录，若非本人操作，请勿泄露。
            /// </summary>
            SMS_190505518 = 0,
            /// <summary>
            /// 发送用户注册信息
            /// </summary>
            SMS_463785408 = 1,
            /// <summary>
            /// 重置密码信息
            /// </summary>
            SMS_463720418 = 2
        }


        #region 阿里发短信的方法
        /// <summary>
        /// 最终发送的方法，记录日志
        /// </summary>
        /// <param name="phone">手机号</param>
        /// <param name="jsonData">数据</param>
        /// <param name="template">短信模板枚举</param>
        private static void Send(string phone, JObject jsonData, EnumMessageTemplate template)
        {
            //创建发送对象
            AlibabaCloud.SDK.Dysmsapi20170525.Client client = CreateClient(AppSettings.AliPhoneMessage.AccessKeyId, AppSettings.AliPhoneMessage.AccessSecret);
            AlibabaCloud.SDK.Dysmsapi20170525.Models.SendSmsRequest sendSmsRequest = new AlibabaCloud.SDK.Dysmsapi20170525.Models.SendSmsRequest
            {
                //手机号
                PhoneNumbers = phone,
                //签名
                SignName = AppSettings.AliPhoneMessage.SignName,
                //模板名称
                TemplateCode = template.ToString(),
                //模板参数，json串
                TemplateParam = jsonData.ToString()
            };
            try
            {
                var ret = client.SendSmsWithOptions(sendSmsRequest, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());
                if (ret.Body.Code != "OK")
                    throw new ApiException(ret.Body.Message);
            }
            catch (Exception error)
            {
                LogUtil.AddErrorLog("短信发送失败：" + error.Message, error);
                throw new ApiException("短信发送失败");
            }
        }
        /// <summary>
        /// 使用AK、SK初始化账号Client
        /// </summary>
        /// <param name="accessKeyId"></param>
        /// <param name="accessKeySecret"></param>
        /// <returns></returns>
        private static AlibabaCloud.SDK.Dysmsapi20170525.Client CreateClient(string accessKeyId, string accessKeySecret)
        {
            AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config
            {
                // 必填，您的 AccessKey ID
                AccessKeyId = accessKeyId,
                // 必填，您的 AccessKey Secret
                AccessKeySecret = accessKeySecret,
            };
            // 访问的域名
            config.Endpoint = "dysmsapi.aliyuncs.com";
            return new AlibabaCloud.SDK.Dysmsapi20170525.Client(config);
        }
        #endregion
    }
}
