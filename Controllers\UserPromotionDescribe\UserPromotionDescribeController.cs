﻿using System.ComponentModel;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.UserPromotionDescribe;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.Controllers.UserPromotionDescribe
{
    [Description("员工职级描述控制器")]
    public class UserPromotionDescribeController : MyControllerBase
    {
        public UserPromotionDescribeController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        
        /// <summary>
        /// 获取员工职级描述列表
        /// </summary>
        /// <returns></returns>
        [HttpPost,SkipRightCheck]
        public List<GetUserPromotionDescribeList_Out> GetUserPromotionDescribeList()
        {
            return DbOpe_crm_user_promotion_describe.Instance.GetUserPromotionDescribeList();
        }
    }
}