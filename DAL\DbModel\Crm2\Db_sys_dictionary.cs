﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///数据字典表
    ///</summary>
    [SugarTable("sys_dictionary")]
    public class Db_sys_dictionary
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Name { get; set; }

        /// <summary>
        /// Desc:值
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Value { get; set; }

        /// <summary>
        /// Desc:父级节点
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ParentId { get; set; }

        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Type { get; set; }

        /// <summary>
        /// Desc:排序号
        /// Default:0
        /// Nullable:True
        /// </summary>           
        public int? Sort { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:0
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

        [SqlSugar.SugarColumn(IsIgnore = true)]
        public List<Db_sys_dictionary>? Children { get; set; }
    }
}
