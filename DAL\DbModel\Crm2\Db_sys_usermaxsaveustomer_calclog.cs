﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_usermaxsaveustomer_calclog")]
    public class Db_sys_usermaxsaveustomer_calclog
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:用户id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:年
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? Year {get;set;}

           /// <summary>
           /// Desc:月
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? Month {get;set;}

        /// <summary>
           /// Desc:计算业绩
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? EffectAchivement {get;set;}
           /// <summary>
           /// Desc:最大可保存客户数上次
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? MaxSaveCustomerOld {get;set;}

           /// <summary>
           /// Desc:本次计算结果
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? MaxSaveCustomerCalc {get;set;}

           /// <summary>
           /// Desc:最终保存客户数
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? MaxSaveCustomerFinal {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime CreateDate {get;set;}

    }
}
