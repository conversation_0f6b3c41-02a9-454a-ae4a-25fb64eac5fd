using CRM2_API.BLL.Common;
using CRM2_API.BLL.GlobalSearchOpe;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.Enum;
using CRM2_API.Model.BLLModel.ServiceOpening;
using CRM2_API.Model.System;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CRM2_API.Model.BLLModel.Enum;

namespace CRM2_API.BLL.ServiceOpening
{
    /// <summary>
    /// 组合服务开通业务逻辑类
    /// </summary>
    public partial class BLL_ServiceOpening : BaseBLL<BLL_ServiceOpening>
    {
        #region 私有字段

        private readonly BLL_GlobalSearch _globalSearchBll;
        private readonly BLL_SaleWits _saleWitsBll;
        private readonly BLL_GtisOpe _gtisBll;

        // 数据操作类
        private readonly DbOpe_crm_contract_serviceinfo_saleswits _dbOpe_crm_contract_serviceinfo_saleswits;
        private readonly DbOpe_crm_contract_serviceinfo_college _dbOpe_crm_contract_serviceinfo_college;
        private readonly DbOpe_crm_contract _dbOpe_crm_contract;
        private readonly DbOpe_crm_salewits_resource_distribution _dbOpe_crm_salewits_resource_distribution;

        #endregion
        
        #region 构造函数
        
        public BLL_ServiceOpening()
        {
            _globalSearchBll = new BLL_GlobalSearch();
            // 使用配置中的环境设置初始化SaleWits
            _saleWitsBll = new BLL_SaleWits();
            _gtisBll = new BLL_GtisOpe();

            // 初始化数据操作类
            _dbOpe_crm_contract_serviceinfo_saleswits = DbOpe_crm_contract_serviceinfo_saleswits.Instance;
            _dbOpe_crm_contract_serviceinfo_college = DbOpe_crm_contract_serviceinfo_college.Instance;
            _dbOpe_crm_contract = DbOpe_crm_contract.Instance;
            _dbOpe_crm_salewits_resource_distribution = DbOpe_crm_salewits_resource_distribution.Instance;
        }
        
        #endregion
        
        #region 主开通流程
        
        /// <summary>
        /// 执行组合服务开通
        /// </summary>
        /// <param name="serviceId">服务ID（crm_contract_serviceinfo_wits表ID）</param>
        /// <param name="isTestMode">是否为测试模式</param>
        /// <param name="operatorId">操作人ID（可选，用于人工审核下发）</param>
        /// <returns>开通结果</returns>
        public async Task<ServiceOpeningResult> ExecuteServiceOpening(string serviceId, bool isTestMode = false, string operatorId = null)
        {
            var result = new ServiceOpeningResult
            {
                ServiceId = serviceId,
                StartTime = DateTime.Now
            };

            try
            {
                LogUtil.AddLog($"开始执行组合服务开通，服务ID: {serviceId}, 测试模式: {isTestMode}");

                // 1. 先判断开通类型（基于基础信息）
                var openingType = DetermineOpeningTypeEarly(serviceId);

                // 2. 根据开通类型准备开通参数
                var openingParams = PrepareOpeningParams(serviceId, isTestMode, openingType, operatorId);
                if (!openingParams.IsValid)
                {
                    result.SetError($"开通参数准备失败: {openingParams.ErrorMessage}");
                    return result;
                }
                
                LogUtil.AddLog($"开通类型: {openingType}, 服务范围: {string.Join(",", openingParams.Apps)}");
                
                // 3. 处理环球搜服务（如果需要）
                if (openingParams.MainService.IsGlobalSearchAudit)
                {
                    var globalSearchResult = ProcessGlobalSearchService(openingParams);
                    if (!globalSearchResult.Success)
                    {
                        result.SetError($"环球搜开通失败: {globalSearchResult.Message}");
                        return result;
                    }
                    result.GlobalSearchResult = globalSearchResult;
                }
                
                // 4. 处理GTIS服务
                var gtisResult = ProcessGtisService(openingParams, result.GlobalSearchResult);
                if (!gtisResult.Success)
                {
                    result.SetError($"GTIS开通失败: {gtisResult.Message}");
                    return result;
                }
                result.GtisResult = gtisResult;
                
                // 5. 处理SaleWits资源下发（根据数据准备阶段的判断结果）
                if (openingParams.MainService.IsSalesWitsAudit &&
                    openingParams.SalesWitsSpecialConfig?.ResourceDataResult?.Success == true)
                {
                    var saleWitsResult = await ProcessSaleWitsService(openingParams);
                    if (!saleWitsResult.Success)
                    {
                        result.SetError($"SaleWits资源下发失败: {saleWitsResult.Message}");
                        return result;
                    }
                    result.SaleWitsResult = saleWitsResult;
                }
                else if (openingParams.MainService.IsSalesWitsAudit &&
                         openingParams.SalesWitsSpecialConfig?.ResourceDataResult?.Success == false)
                {
                    // 需要SaleWits但数据准备阶段判断不需要下发资源（如Token、Email和充值金额都为0）
                    LogUtil.AddLog($"SaleWits服务已配置但无需下发资源或充值: {openingParams.SalesWitsSpecialConfig.ResourceDataResult.Message}");

                    // 创建一个成功的SaleWits结果，表示跳过了资源下发和充值
                    result.SaleWitsResult = new SaleWitsResult
                    {
                        Success = true,
                        Message = "SaleWits服务配置完成，但无需下发资源或充值（Token、邮件数量和充值金额都为0）",
                        Code = 200,
                        ResourceManagementId = openingParams.SalesWitsSpecialConfig?.ServiceInfo?.ResourceManagementId
                    };
                }
                
                // 6. 处理慧思学院服务（如果需要）
                if (openingParams.MainService.IsCollegeAudit)
                {
                    var collegeResult = ProcessCollegeService(openingParams);
                    if (!collegeResult.Success)
                    {
                        result.SetError($"慧思学院开通失败: {collegeResult.Message}");
                        return result;
                    }
                    result.CollegeResult = collegeResult;
                }



                result.SetSuccess("组合服务开通成功");
                LogUtil.AddLog($"组合服务开通完成，服务ID: {serviceId}");

            }
            catch (Exception ex)
            {
                throw new ApiException($"服务开通异常，服务ID: {serviceId}, 错误: {ex.Message}");
            }
            finally
            {
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
            }
            
            return result;
        }
        
        #endregion
        
        #region 开通类型判断
        
        /// <summary>
        /// 提前判断开通类型（基于基础信息）
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <returns>开通类型</returns>
        private ServiceOpeningType DetermineOpeningTypeEarly(string serviceId)
        {
            try
            {
                LogUtil.AddLog($"开始提前判断开通类型，服务ID: {serviceId}");

                // 1. 获取主服务信息
                var mainService = DbOpe_crm_contract_serviceinfo_wits.Instance.GetData(x => x.Id == serviceId);
                if (mainService == null)
                {
                    LogUtil.AddLog($"未找到主服务信息，服务ID: {serviceId}，默认按新开账号处理");
                    return ServiceOpeningType.NewAccount;
                }

                // 2. 获取合同信息
                var contract = DbOpe_crm_contract.Instance.GetData(c => c.Id == mainService.ContractId);
                if (contract == null)
                {
                    LogUtil.AddLog($"未找到合同信息，合同ID: {mainService.ContractId}，默认按新开账号处理");
                    return ServiceOpeningType.NewAccount;
                }

                // 3. 根据ProcessingType和ContractType判断开通类型
                ServiceOpeningType openingType;

                if (mainService.ProcessingType == (int)EnumProcessingType.Change)
                {
                    openingType = ServiceOpeningType.Change;
                }
                else if (mainService.ProcessingType == (int)EnumProcessingType.Add)
                {
                    if (contract.ContractType == (int)EnumContractType.New ||
                        contract.ContractType == (int)EnumContractType.AddItem)
                    {
                        openingType = ServiceOpeningType.NewAccount;
                    }
                    else if (contract.ContractType == (int)EnumContractType.ReNew)
                    {
                        openingType = ServiceOpeningType.Renewal;
                    }
                    else
                    {
                        openingType = ServiceOpeningType.NewAccount; // 默认
                    }
                }
                else
                {
                    openingType = ServiceOpeningType.NewAccount; // 默认
                }

                LogUtil.AddLog($"开通类型判断完成: {openingType} (ProcessingType={mainService.ProcessingType}, ContractType={contract.ContractType})");
                return openingType;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提前判断开通类型异常: {ex.Message}，默认按新开账号处理");
                return ServiceOpeningType.NewAccount;
            }
        }

        /// <summary>
        /// 判断开通类型（保留原方法，用于兼容性）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>开通类型</returns>
        private ServiceOpeningType DetermineOpeningType(ServiceOpeningParams openingParams)
        {
            // ProcessingType: 1-服务申请, 2-服务变更
            if (openingParams.MainService.ProcessingType == (int)EnumProcessingType.Change)
            {
                return ServiceOpeningType.Change;
            }
            else if (openingParams.MainService.ProcessingType == (int)EnumProcessingType.Add)
            {
                // ContractType: 1-新增合同, 2-合同续约, 3-新增项目
                if (openingParams.Contract.ContractType == (int)EnumContractType.New || 
                    openingParams.Contract.ContractType == (int)EnumContractType.AddItem)
                {
                    return ServiceOpeningType.NewAccount;
                }
                else if (openingParams.Contract.ContractType == (int)EnumContractType.ReNew)
                {
                    return ServiceOpeningType.Renewal;
                }
            }
            
            return ServiceOpeningType.NewAccount; // 默认新开
        }
        
        #endregion

        #region SaleWits定时下发

        /// <summary>
        /// SaleWits定时下发任务
        /// 每天凌晨2点执行，检查需要进行年度资源下发的服务
        /// </summary>
        public static async Task ExecuteSaleWitsScheduledDistribution()
        {
            var startTime = DateTime.Now;
            DistributionSummary? summary = null;
            
            try
            {
                LogUtil.AddLog("开始执行SaleWits定时下发任务");

                var instance = new BLL_ServiceOpening();
                summary = await instance.ProcessScheduledDistribution();

                LogUtil.AddLog($"SaleWits定时下发任务执行完成: {summary.Message}");
                
                // 记录详细的执行结果
                if (summary.ServiceResults?.Any() == true)
                {
                    LogUtil.AddLog("详细执行结果:");
                    foreach (var result in summary.ServiceResults)
                    {
                        var status = result.Success ? "成功" : "失败";
                        LogUtil.AddLog($"  - 服务{result.ServiceId}({result.ResourceManagementId}): {status}, 耗时{result.Duration.TotalSeconds:F1}秒, {result.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog("SaleWits定时下发任务执行失败", ex);
                summary = new DistributionSummary 
                { 
                    Success = false, 
                    Message = $"任务执行异常: {ex.Message}",
                    Duration = DateTime.Now - startTime
                };
            }
            finally
            {
                var totalDuration = DateTime.Now - startTime;
                if (summary != null)
                {
                    LogUtil.AddLog($"SaleWits定时下发任务总结: 总耗时{totalDuration.TotalMinutes:F2}分钟, 处理{summary.TotalServices}个服务, 成功{summary.SuccessCount}个, 失败{summary.FailureCount}个");
                }
                else
                {
                    LogUtil.AddLog($"SaleWits定时下发任务总结: 总耗时{totalDuration.TotalMinutes:F2}分钟, 任务异常终止");
                }
            }
        }

        /// <summary>
        /// 处理定时下发逻辑
        /// </summary>
        private async Task<DistributionSummary> ProcessScheduledDistribution()
        {
            var summary = new DistributionSummary();
            var startTime = DateTime.Now;
            
            try
            {
                // 查询需要进行年度下发的SaleWits服务
                var servicesNeedDistribution = GetServicesNeedAnnualDistribution();
                summary.TotalServices = servicesNeedDistribution.Count;

                LogUtil.AddLog($"找到{servicesNeedDistribution.Count}个需要年度下发的SaleWits服务");

                if (servicesNeedDistribution.Count == 0)
                {
                    LogUtil.AddLog("没有需要年度下发的服务，任务结束");
                    summary.Message = "没有需要年度下发的服务";
                    return summary;
                }

                // 使用信号量控制并发数量，避免同时下发太多服务
                var semaphore = new System.Threading.SemaphoreSlim(3, 3); // 最多同时处理3个服务
                var distributionResults = new System.Collections.Concurrent.ConcurrentBag<ServiceDistributionResult>();
                var successCount = 0;
                var failureCount = 0;
                
                var distributionTasks = servicesNeedDistribution.Select(async service =>
                {
                    await semaphore.WaitAsync();
                    var serviceResult = new ServiceDistributionResult 
                    { 
                        ServiceId = service.Id, 
                        ResourceManagementId = service.ResourceManagementId,
                        ContractId = service.ContractId,
                        StartTime = DateTime.Now
                    };
                    
                    try
                    {
                        await ProcessSingleServiceAnnualDistribution(service);
                        serviceResult.Success = true;
                        serviceResult.Message = "年度下发成功";
                        System.Threading.Interlocked.Increment(ref successCount);
                    }
                    catch (Exception ex)
                    {
                        serviceResult.Success = false;
                        serviceResult.Message = ex.Message;
                        serviceResult.Exception = ex;
                        System.Threading.Interlocked.Increment(ref failureCount);
                        LogUtil.AddErrorLog($"处理服务{service.Id}年度下发失败: {ex.Message}", ex);
                    }
                    finally
                    {
                        serviceResult.EndTime = DateTime.Now;
                        serviceResult.Duration = serviceResult.EndTime - serviceResult.StartTime;
                        distributionResults.Add(serviceResult);
                        semaphore.Release();
                    }
                });

                // 等待所有下发任务完成
                await Task.WhenAll(distributionTasks);
                
                // 更新统计信息
                summary.SuccessCount = successCount;
                summary.FailureCount = failureCount;
                summary.ServiceResults = distributionResults.ToList();
                summary.Duration = DateTime.Now - startTime;
                summary.Message = $"年度下发完成: 成功{summary.SuccessCount}个, 失败{summary.FailureCount}个";
                
                LogUtil.AddLog($"所有年度下发任务处理完成: 成功{summary.SuccessCount}个, 失败{summary.FailureCount}个, 总耗时{summary.Duration.TotalMinutes:F2}分钟");
                
                return summary;
            }
            catch (Exception ex)
            {
                summary.Success = false;
                summary.Message = $"定时下发处理异常: {ex.Message}";
                summary.Duration = DateTime.Now - startTime;
                LogUtil.AddErrorLog($"定时下发处理异常: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取需要年度下发的SaleWits服务列表
        /// </summary>
        /// <returns></returns>
        private List<Db_crm_contract_serviceinfo_saleswits> GetServicesNeedAnnualDistribution()
        {
            var currentDate = DateTime.Now.Date;
            var servicesNeedDistribution = new List<Db_crm_contract_serviceinfo_saleswits>();

            try
            {
                // 获取所有有ResourceManagementId的已处理服务
                var allServices = _dbOpe_crm_contract_serviceinfo_saleswits
                    .GetDataList(x => x.IsProcessed == (int)EnumGtisServiceIsProcess.Processed &&
                                     x.Deleted == false &&
                                     !string.IsNullOrEmpty(x.ResourceManagementId));

                LogUtil.AddLog($"共找到{allServices.Count}个有ResourceManagementId的已处理SaleWits服务");

                // 按ResourceManagementId分组，每组只取最新的合同服务
                var groupedServices = allServices
                    .GroupBy(x => x.ResourceManagementId)
                    .Select(g => g.OrderByDescending(x => x.CreateDate).First())
                    .ToList();

                LogUtil.AddLog($"按ResourceManagementId分组后，共{groupedServices.Count}个独立的资源管理组");

                foreach (var service in groupedServices)
                {
                    // 检查是否为年度服务（服务时间≥12个月）
                    if (service.ServiceMonth < 12)
                    {
                        LogUtil.AddLog($"资源管理{service.ResourceManagementId}服务时间{service.ServiceMonth}个月，小于12个月，跳过");
                        continue;
                    }

                    // 检查服务有效期
                    if (service.ServiceCycleEnd.HasValue && service.ServiceCycleEnd.Value.Date < currentDate)
                    {
                        LogUtil.AddLog($"资源管理{service.ResourceManagementId}已过期（{service.ServiceCycleEnd.Value:yyyy-MM-dd}），跳过");
                        continue;
                    }

                    // 检查是否需要年度下发
                    if (ShouldPerformAnnualDistribution(service))
                    {
                        servicesNeedDistribution.Add(service);
                        LogUtil.AddLog($"资源管理{service.ResourceManagementId}需要年度下发");
                    }
                }

                LogUtil.AddLog($"最终确定{servicesNeedDistribution.Count}个资源管理需要年度下发");
                return servicesNeedDistribution;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取需要年度下发的服务列表异常: {ex.Message}", ex);
                return new List<Db_crm_contract_serviceinfo_saleswits>();
            }
        }

        /// <summary>
        /// 判断是否应该进行年度下发
        /// </summary>
        /// <param name="service">服务信息</param>
        /// <returns></returns>
        private bool ShouldPerformAnnualDistribution(Db_crm_contract_serviceinfo_saleswits service)
        {
            var currentDate = DateTime.Now.Date;

            // 如果没有ResourceManagementId，跳过
            if (string.IsNullOrEmpty(service.ResourceManagementId))
            {
                LogUtil.AddLog($"服务{service.Id}没有ResourceManagementId，跳过年度下发检查");
                return false;
            }

            // 基于ResourceManagementId获取最近一次年度下发记录
            var lastAnnualDistribution = _dbOpe_crm_salewits_resource_distribution
                .GetDataList(x => x.ResourceManagementId == service.ResourceManagementId &&
                                 x.DistributionType == "AnnualDistribution" &&
                                 x.IsSuccess == true)
                .OrderByDescending(x => x.DistributionTime)
                .FirstOrDefault();

            // 如果没有年度下发记录，检查是否距离服务开始已满一年
            if (lastAnnualDistribution == null)
            {
                // 获取首次下发记录作为服务开始时间参考
                var firstDistribution = _dbOpe_crm_salewits_resource_distribution
                    .GetDataList(x => x.ResourceManagementId == service.ResourceManagementId &&
                                     x.DistributionType == "FirstDistribution" &&
                                     x.IsSuccess == true)
                    .OrderBy(x => x.DistributionTime)
                    .FirstOrDefault();

                if (firstDistribution != null)
                {
                    var nextAnnualDateFromFirst = firstDistribution.DistributionTime.AddYears(1).Date;
                    LogUtil.AddLog($"资源管理{service.ResourceManagementId}首次下发时间{firstDistribution.DistributionTime:yyyy-MM-dd}，下次年度下发时间{nextAnnualDateFromFirst:yyyy-MM-dd}，当前时间{currentDate:yyyy-MM-dd}");
                    return currentDate >= nextAnnualDateFromFirst;
                }

                // 如果没有首次下发记录，使用服务开始时间
                if (service.ServiceCycleStart.HasValue)
                {
                    var nextAnnualDateFromStart = service.ServiceCycleStart.Value.AddYears(1).Date;
                    LogUtil.AddLog($"资源管理{service.ResourceManagementId}服务开始时间{service.ServiceCycleStart.Value:yyyy-MM-dd}，下次年度下发时间{nextAnnualDateFromStart:yyyy-MM-dd}，当前时间{currentDate:yyyy-MM-dd}");
                    return currentDate >= nextAnnualDateFromStart;
                }

                LogUtil.AddLog($"资源管理{service.ResourceManagementId}无下发记录且无服务开始时间，跳过年度下发");
                return false;
            }

            // 如果有年度下发记录，检查是否距离上次下发已满一年
            var nextAnnualDate = lastAnnualDistribution.DistributionTime.AddYears(1).Date;
            LogUtil.AddLog($"资源管理{service.ResourceManagementId}上次年度下发时间{lastAnnualDistribution.DistributionTime:yyyy-MM-dd}，下次年度下发时间{nextAnnualDate:yyyy-MM-dd}，当前时间{currentDate:yyyy-MM-dd}");
            return currentDate >= nextAnnualDate;
        }

        /// <summary>
        /// 处理单个服务的年度下发
        /// </summary>
        /// <param name="service">服务信息</param>
        private async Task ProcessSingleServiceAnnualDistribution(Db_crm_contract_serviceinfo_saleswits service)
        {
            LogUtil.AddLog($"开始处理服务{service.Id}的年度下发");

            try
            {
                // 创建年度下发的服务开通参数
                var openingParams = CreateAnnualDistributionParams(service);
                if (!openingParams.IsValid)
                {
                    LogUtil.AddLog($"服务{service.Id}年度下发参数创建失败: {openingParams.ErrorMessage}");
                    return;
                }

                // 年度下发跳过数据预处理，直接准备资源数据
                var resourceDataResult = PrepareAnnualDistributionResourceData(openingParams);
                openingParams.SalesWitsSpecialConfig.ResourceDataResult = resourceDataResult;

                // 执行SaleWits资源下发（年度下发不需要GTIS结果）
                var result = await ProcessSaleWitsService(openingParams);

                if (result.Success)
                {
                    LogUtil.AddLog($"服务{service.Id}年度下发成功");
                }
                else
                {
                    LogUtil.AddLog($"服务{service.Id}年度下发失败: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"服务{service.Id}年度下发异常", ex);
            }
        }

        /// <summary>
        /// 创建年度下发的服务开通参数
        /// </summary>
        /// <param name="service">SaleWits服务信息</param>
        /// <returns>服务开通参数</returns>
        private ServiceOpeningParams CreateAnnualDistributionParams(Db_crm_contract_serviceinfo_saleswits service)
        {
            var openingParams = new ServiceOpeningParams();

            try
            {
                // 获取合同信息
                var contract = DbOpe_crm_contract.Instance.GetData(c => c.Id == service.ContractId);
                //var contract = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(service.ContractId);
                if (contract == null)
                {
                    openingParams.SetError($"未找到合同信息，合同ID: {service.ContractId}");
                    return openingParams;
                }

                // 获取主服务信息（WitsAppl）
                var mainService = DbOpe_crm_contract_serviceinfo_wits.Instance.GetData(x => x.Id == service.WitsApplId);
                if (mainService == null)
                {
                    openingParams.SetError($"未找到主服务信息，WitsApplId: {service.WitsApplId}");
                    return openingParams;
                }

                // 设置基本信息
                openingParams.Contract = contract;
                openingParams.MainService = mainService;
                openingParams.MainService.IsSalesWitsAudit = true;

                // 设置定时下发的固定操作人信息
                openingParams.OperatorId = "SYSTEM";
                openingParams.OperatorName = "系统定时下发";

                // 设置SaleWits配置（年度下发使用历史配置）
                openingParams.SalesWitsBasicConfig = new SalesWitsBasicConfig
                {
                    ContractId = service.ContractId,
                    HasAppPermission = true,
                    MaxAccountsNum = service.AccountsNum ?? 0,
                    ServiceCycleStart = service.ServiceCycleStart,
                    ServiceCycleEnd = service.ServiceCycleEnd
                };

                // 为年度下发计算并设置资源量
                var annualResources = GetAnnualDistributionSaleWitsParams(service);
                
                // 创建临时的SaleWits特殊配置用于下发
                var tempSaleWitsService = new DAL.DbModel.Crm2.Db_crm_contract_serviceinfo_saleswits
                {
                    Id = service.Id,
                    ContractId = service.ContractId,
                    WitsApplId = service.WitsApplId,
                    AccountsNum = service.AccountsNum,
                    ServiceMonth = 12, // 年度下发固定12个月
                    ResourceManagementId = service.ResourceManagementId,
                    IsProcessed = service.IsProcessed,
                    Deleted = service.Deleted,
                    
                    // 设置计算出的年度下发资源量
                    CurrentGiftMonths = 12,
                    CurrentGiftTokens = annualResources.TokenCount, // 已经是万个单位
                    CurrentGiftEmails = annualResources.EmailCount,
                    RechargeAmount = 0 // 年度下发不充值
                };

                // 创建SaleWits特殊配置
                openingParams.SalesWitsSpecialConfig = new Model.BLLModel.ServiceOpening.SalesWitsSpecialConfig
                {
                    ServiceInfo = tempSaleWitsService
                };

                openingParams.SetValid();
                LogUtil.AddLog($"成功创建服务{service.Id}的年度下发参数");
            }
            catch (Exception ex)
            {
                openingParams.SetError($"创建年度下发参数异常: {ex.Message}");
                LogUtil.AddLog($"创建服务{service.Id}年度下发参数异常", ex);
            }

            return openingParams;
        }

        /// <summary>
        /// 为年度下发准备资源数据（跳过复杂的数据预处理）
        /// </summary>
        /// <param name="openingParams">开通参数</param>
        /// <returns>资源数据准备结果</returns>
        private SaleWitsResourceDataResult PrepareAnnualDistributionResourceData(ServiceOpeningParams openingParams)
        {
            var result = new SaleWitsResourceDataResult();

            try
            {
                LogUtil.AddLog("开始为年度下发准备资源数据");

                var salesWitsService = openingParams.SalesWitsSpecialConfig?.ServiceInfo;
                if (salesWitsService == null)
                {
                    result.SetError("年度下发缺少SaleWits服务配置");
                    return result;
                }

                // 验证资源配置
                var tokenCount = salesWitsService.CurrentGiftTokens ?? 0;
                var emailCount = salesWitsService.CurrentGiftEmails ?? 0;
                var rechargeAmount = salesWitsService.RechargeAmount ?? 0;

                if (tokenCount == 0 && emailCount == 0 && rechargeAmount == 0)
                {
                    result.SetError("年度下发资源量全部为0，无需下发");
                    LogUtil.AddLog("年度下发资源量全部为0，跳过下发");
                    return result;
                }

                // 构建资源数据准备结果
                result.SetSuccess("年度下发资源数据准备完成");
                result.DistributionType = EnumSaleWitsDistributionType.AnnualDistribution;
                result.PresetResources = new SaleWitsPresetResources
                {
                    MonthsCount = 12,
                    EmailCount = emailCount,
                    TokenCount = tokenCount,
                    RechargeAmount = rechargeAmount
                };

                LogUtil.AddLog($"年度下发资源数据准备完成: 邮件{emailCount}封, Token{tokenCount}万个, 充值{rechargeAmount}元");

            }
            catch (Exception ex)
            {
                var errorMsg = $"年度下发资源数据准备异常: {ex.Message}";
                LogUtil.AddLog($"{errorMsg}，详细异常: {ex}");
                result.SetError(errorMsg);
            }

            return result;
        }

        #endregion
    }
}