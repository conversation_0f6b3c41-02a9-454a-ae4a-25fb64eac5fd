﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.BLL.RemindInfo;
using CRM2_API.BLL.TrackingRecord;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Utils.IdentityCard;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.Services.PreInspection;
using Microsoft.AspNetCore.Http;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SqlSugar;
using System.ComponentModel;
using System.IO;
using System.Reflection;

namespace CRM2_API.BLL
{
    public partial class BLL_Customer : BaseBLL<BLL_Customer>
    {
        /// <summary>
        /// 获取信用代码(新)
        /// 自动修正名称中的括号问题（验证时，大小写括号都验证一下，确定是否是国内公司，如果是国内公司，则自动修正名称中的括号并返回正确括号的公司名称）
        /// </summary>
        /// <param name="getCreditCode_IN"></param>
        /// <returns></returns>
        public GetCreditCode_OUT GetCreditCodeNew(GetCreditCode_IN getCreditCode_IN)
        {
            List<string> exceptCustomerIds = new List<string>();
            if (!string.IsNullOrEmpty(getCreditCode_IN.CustomerId))
            {
                exceptCustomerIds.Add(getCreditCode_IN.CustomerId);
            }
            GetCreditCode_OUT getCreditCode_OUT = new GetCreditCode_OUT();

            #region 修正公司名称中的括号问题
            bool isCorrected = CorrectCompanyNameBrackets(getCreditCode_IN.CompanyName, out string correctedCompanyName);
            if (isCorrected)
            {
                getCreditCode_OUT.CorrectedCompanyName = correctedCompanyName;
            }
            else
            {
                getCreditCode_OUT.CorrectedCompanyName = getCreditCode_IN.CompanyName;
            }
            #endregion

            #region 天眼查验证公司归属信息是否正确，获取信用代码 
            var tycCreditCode = "";
            string msg = "";
            Db_crm_tianyancha_info tycInfo = null;
            var checkCompanyName = isCorrected ? correctedCompanyName : getCreditCode_IN.CompanyName;
            //在天眼查验证公司归属信息是否正确，获取信用代码 
            //如果修正过，则使用修正后的公司名称去验证
            if (GetTianYanChaInfo(checkCompanyName, ref tycCreditCode, ref tycInfo, ref msg))
            {
                //在天眼查验证通过，获取到了信用代码，证明是国内的
                getCreditCode_OUT.CreditCode = tycCreditCode;
                getCreditCode_OUT.Msg = msg;
                //顺便获取地址信息
                GetAddressFromTianYanChaInfo(tycInfo, ref getCreditCode_OUT);
                if (getCreditCode_IN.CreditType != (int)EnumCompanyType.CN)
                {
                    throw new ApiException("公司信息验证失败:国内公司名称不能选择其他客户归属类型");
                }
            }
            else
            {
                //在天眼查验证不通过，证明不是国内的
                if (getCreditCode_IN.CreditType == (int)EnumCompanyType.CN)
                {
                    throw new ApiException("公司信息验证失败:未找到公司的信用代码，请检查输入的公司名称是否正确且完整，并确认公司是否在国内注册");
                }
                else
                {
                    //不是国内的，则生成一个信用代码（个人客户不用生成信用代码）
                    if (getCreditCode_IN.CreditType == (int)EnumCompanyType.Other)
                    {
                        var code = DateTime.Now.ToString("yyyyMMddHHmmssff") + new Random().Next(10) + "X";
                        getCreditCode_OUT.CreditCode = code;
                        //如果是其他类型，则保存到redis中，后续保存客户进行验证时需要从redis中验证
                        RedisCache.CreditCode.Save(getCreditCode_IN.CompanyName, code);
                        //如果不是国内企业，不要返回修正括号后的公司名称，因为你redis里存的是原名称，所以返回也得还是原名称
                        getCreditCode_OUT.CorrectedCompanyName = getCreditCode_IN.CompanyName;
                    }
                    else
                    {
                        getCreditCode_OUT.CreditCode = "";
                    }
                }
            }
            #endregion

            #region 排重
            List<CheckCompanyUniqueInfo> checkCompanyUniqueInfos = new List<CheckCompanyUniqueInfo>();
            //排重
            checkCompanyUniqueInfos.Add(new CheckCompanyUniqueInfo()
            {
                //中文括号名称
                CompanyName = getCreditCode_IN.CompanyName,
                CreditCode = getCreditCode_OUT.CreditCode,
                CreditType = getCreditCode_IN.CreditType
            });
            //排重默认会忽略括号中的中英文问题
            var existCompanys = DbOpe_crm_customer.Instance.CheckDuplicateCustomer(checkCompanyUniqueInfos, exceptCustomerIds, getCreditCode_IN.IsRelated != 1, false);
            if (existCompanys.Count > 0)
            {
                if (string.IsNullOrEmpty(getCreditCode_OUT.Msg))
                {
                    getCreditCode_OUT.Msg = "(" + string.Join(", ", existCompanys) + ")是独立的保留公司，请确定是否添加采购主体关联";
                }
                else
                {
                    getCreditCode_OUT.Msg += "\r\n(" + string.Join(", ", existCompanys) + ")是独立的保留公司，请确定是否添加采购主体关联";
                }
            }
            var tempCustomerExist = DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(checkCompanyUniqueInfos, currentUser, null, getCreditCode_IN.IsRelated != 1);
            if (tempCustomerExist.Count > 0)
            {
                if (string.IsNullOrEmpty(getCreditCode_OUT.Msg))
                {
                    getCreditCode_OUT.Msg = "(" + string.Join(", ", tempCustomerExist) + ")是您的临时池客户，请确定是否添加采购主体关联";
                }
                else
                {
                    getCreditCode_OUT.Msg += "\r\n(" + string.Join(", ", tempCustomerExist) + ")是您的临时池客户，请确定是否添加采购主体关联";
                }
            }
            if (getCreditCode_IN.IsRelated != 1)
            {
                CheckAuditInProcess(checkCompanyUniqueInfos);
            }

            #endregion

            return getCreditCode_OUT;
        }
        /// <summary>
        /// 获取天眼查信息
        /// </summary>
        /// <param name="companyName"></param>
        /// <param name="creditCode"></param>
        /// <param name="info"></param>
        /// <param name="msg"></param>
        /// <returns>是否获取成功（有记录返回true，没有记录返回false）</returns>
        private bool GetTianYanChaInfo(string companyName, ref string creditCode, ref Db_crm_tianyancha_info info, ref string msg)
        {
            List<Db_crm_tianyancha_info> infos = new List<Db_crm_tianyancha_info>();
            string error_code = "";
            BLL_Tianyancha.Instance.QueryTianyancha(companyName, ref infos, ref error_code);
            if (infos.Count == 0)
            {
                return false;
            }
            var exactCompany = infos.Find(i => i.CompanyName == companyName);
            var historyCompany = infos.Find(i => i.HistoryNames != null && i.HistoryNames.Split("|").Contains(companyName));
            if (exactCompany != null)
            {
                //名称输入与天眼查一致
                creditCode = exactCompany.CreditCode;
                info = exactCompany;
                return true;
            }
            else if (historyCompany != null)
            {
                var index = historyCompany.HistoryNames.Split("|").ToList().IndexOf(companyName);
                //输入的是曾用名 特殊处理编码
                creditCode = "$$" + index + historyCompany.CreditCode;
                info = historyCompany;
                msg = "您输入的公司名称为公司曾用名，公司现用名称为：" + historyCompany.CompanyName;
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 获取信用代码（包括排重） 11.23排除审核中公司
        /// </summary>
        /// <param name="getCreditCode_IN"></param>
        /// <returns></returns>
        public GetCreditCode_OUT GetCreditCode(GetCreditCode_IN getCreditCode_IN)
        {
            GetCreditCode_OUT getCreditCode_OUT = new GetCreditCode_OUT();
            CheckCompanyUniqueInfo checkCompanyUniqueInfo = new CheckCompanyUniqueInfo();
            checkCompanyUniqueInfo.CompanyName = getCreditCode_IN.CompanyName;
            List<string> exceptCustomerIds = new List<string>();
            if (!string.IsNullOrEmpty(getCreditCode_IN.CustomerId))
            {
                exceptCustomerIds.Add(getCreditCode_IN.CustomerId);
            }
            if (getCreditCode_IN.CreditType == (int)EnumCompanyType.CN)
            {
                List<Db_crm_tianyancha_info> infos = new List<Db_crm_tianyancha_info>();
                string error_code = "";
                BLL_Tianyancha.Instance.QueryTianyancha(getCreditCode_IN.CompanyName, ref infos, ref error_code);
                if (infos == null || infos.Count == 0)
                {
                    throw new ApiException("未找到公司信息，请检查输入的公司名称是否正确且完整（error_code=" + error_code + "）");
                }
                else
                {
                    var exactCompany = infos.Find(i => i.CompanyName == checkCompanyUniqueInfo.CompanyName);
                    var historyCompany = infos.Find(i => i.HistoryNames != null && i.HistoryNames.Split("|").Contains(checkCompanyUniqueInfo.CompanyName));
                    if (exactCompany != null)
                    {
                        //名称输入与天眼查一致
                        checkCompanyUniqueInfo.CreditCode = exactCompany.CreditCode;
                        //获取地址信息
                        GetAddressFromTianYanChaInfo(exactCompany, ref getCreditCode_OUT);
                    }
                    else if (historyCompany != null)
                    {
                        var index = historyCompany.HistoryNames.Split("|").ToList().IndexOf(checkCompanyUniqueInfo.CompanyName);
                        //输入的是曾用名 特殊处理编码
                        checkCompanyUniqueInfo.CreditCode = "$$" + index + historyCompany.CreditCode;
                        getCreditCode_OUT.Msg = "您输入的公司名称为公司曾用名，公司现用名称为：" + historyCompany.CompanyName;
                        //获取地址信息
                        GetAddressFromTianYanChaInfo(historyCompany, ref getCreditCode_OUT);
                    }
                    else
                    {
                        throw new ApiException("未找到公司信息，请检查输入的公司名称是否正确且完整");
                    }
                    var existCompanys = DbOpe_crm_customer.Instance.CheckDuplicateCustomer(new List<CheckCompanyUniqueInfo>() { checkCompanyUniqueInfo }, exceptCustomerIds, getCreditCode_IN.IsRelated != 1);
                    if (existCompanys.Count > 0)
                    {
                        if (string.IsNullOrEmpty(getCreditCode_OUT.Msg))
                        {
                            getCreditCode_OUT.Msg = "(" + string.Join(", ", existCompanys) + ")是独立的保留公司，请确定是否添加采购主体关联";
                        }
                        else
                        {
                            getCreditCode_OUT.Msg += "\r\n(" + string.Join(", ", existCompanys) + ")是独立的保留公司，请确定是否添加采购主体关联";
                        }
                    }
                    var tempCustomerExist = DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(new List<CheckCompanyUniqueInfo>() { checkCompanyUniqueInfo }, currentUser, null, getCreditCode_IN.IsRelated != 1);
                    if (tempCustomerExist.Count > 0)
                    {
                        if (string.IsNullOrEmpty(getCreditCode_OUT.Msg))
                        {
                            getCreditCode_OUT.Msg = "(" + string.Join(", ", tempCustomerExist) + ")是您的临时池客户，请确定是否添加采购主体关联";
                        }
                        else
                        {
                            getCreditCode_OUT.Msg += "\r\n(" + string.Join(", ", tempCustomerExist) + ")是您的临时池客户，请确定是否添加采购主体关联";
                        }
                    }
                    if (getCreditCode_IN.IsRelated != 1)
                    {
                        CheckAuditInProcess(new List<CheckCompanyUniqueInfo>() { checkCompanyUniqueInfo });
                    }

                }
            }
            else if (getCreditCode_IN.CreditType == (int)EnumCompanyType.Other)
            {
                //其他也需要工商信息验证一下，避免注册的就国内企业但选择其他类型；
                // List<Db_crm_tianyancha_info> infos;
                bool checkNotIn = false;
                try
                {
                    checkNotIn = BLL_Tianyancha.Instance.QueryNotIn(getCreditCode_IN.CompanyName);
                }
                catch (Exception e)
                {
                    //#if DEBUG
                    throw new ApiException("公司信息验证失败:" + e.Message);
                    //#endif
                    //                    throw new ApiException("公司信息验证失败");
                }
                if (!checkNotIn)
                {
                    throw new ApiException("公司信息验证失败:请检查公司类型");
                }
                var code = DateTime.Now.ToString("yyyyMMddHHmmssff") + new Random().Next(10) + "X";
                checkCompanyUniqueInfo.CreditCode = code;
                var existCompanys = DbOpe_crm_customer.Instance.CheckDuplicateCustomer(new List<CheckCompanyUniqueInfo>() { checkCompanyUniqueInfo }, exceptCustomerIds, getCreditCode_IN.IsRelated != 1);
                if (existCompanys.Count > 0)
                {
                    if (string.IsNullOrEmpty(getCreditCode_OUT.Msg))
                    {
                        getCreditCode_OUT.Msg = "(" + string.Join(", ", existCompanys) + ")是独立的保留公司，请确定是否添加采购主体关联";
                    }
                    else
                    {
                        getCreditCode_OUT.Msg += "\r\n(" + string.Join(", ", existCompanys) + ")是独立的保留公司，请确定是否添加采购主体关联";
                    }
                }
                var tempCustomerExist = DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(new List<CheckCompanyUniqueInfo>() { checkCompanyUniqueInfo }, currentUser, null, getCreditCode_IN.IsRelated != 1);
                if (tempCustomerExist.Count > 0)
                {
                    if (string.IsNullOrEmpty(getCreditCode_OUT.Msg))
                    {
                        getCreditCode_OUT.Msg = "(" + string.Join(", ", tempCustomerExist) + ")是您的临时池客户，请确定是否添加采购主体关联";
                    }
                    else
                    {
                        getCreditCode_OUT.Msg += "\r\n(" + string.Join(", ", tempCustomerExist) + ")是您的临时池客户，请确定是否添加采购主体关联";
                    }
                }
                if (getCreditCode_IN.IsRelated != 1)
                {
                    CheckAuditInProcess(new List<CheckCompanyUniqueInfo>() { checkCompanyUniqueInfo });
                }
                RedisCache.CreditCode.Save(getCreditCode_IN.CompanyName, code);
            }
            else if (getCreditCode_IN.CreditType == (int)EnumCompanyType.Person)
            {
                //其他也需要工商信息验证一下，避免注册的就国内企业但选择其他类型；
                // List<Db_crm_tianyancha_info> infos;
                //bool checkNotIn = false;
                //try
                //{
                //    checkNotIn = BLL_Tianyancha.Instance.QueryNotIn(getCreditCode_IN.CompanyName);
                //}
                //catch (Exception e)
                //{
                //    //#if DEBUG
                //    throw new ApiException("个人信息验证失败:" + e.Message);
                //    //#endif
                //    //                    throw new ApiException("公司信息验证失败");
                //}
                //if (!checkNotIn)
                //{
                //    throw new ApiException("个人信息验证失败:请检查公司类型");
                //}
                checkCompanyUniqueInfo.CreditCode = "";
                var existCompanys = DbOpe_crm_customer.Instance.CheckDuplicateCustomer(new List<CheckCompanyUniqueInfo>() { checkCompanyUniqueInfo }, exceptCustomerIds, getCreditCode_IN.IsRelated != 1);
                if (existCompanys.Count > 0)
                {
                    if (string.IsNullOrEmpty(getCreditCode_OUT.Msg))
                    {
                        getCreditCode_OUT.Msg = "(" + string.Join(", ", existCompanys) + ")是独立的保留公司，请确定是否添加采购主体关联";
                    }
                    else
                    {
                        getCreditCode_OUT.Msg += "\r\n(" + string.Join(", ", existCompanys) + ")是独立的保留公司，请确定是否添加采购主体关联";
                    }
                }
                var tempCustomerExist = DbOpe_crm_customer_temporarypool.Instance.CheckCompanyExist(new List<CheckCompanyUniqueInfo>() { checkCompanyUniqueInfo }, currentUser, null, getCreditCode_IN.IsRelated != 1);
                if (tempCustomerExist.Count > 0)
                {
                    if (string.IsNullOrEmpty(getCreditCode_OUT.Msg))
                    {
                        getCreditCode_OUT.Msg = "(" + string.Join(", ", tempCustomerExist) + ")是您的临时池客户，请确定是否添加采购主体关联";
                    }
                    else
                    {
                        getCreditCode_OUT.Msg += "\r\n(" + string.Join(", ", tempCustomerExist) + ")是您的临时池客户，请确定是否添加采购主体关联";
                    }
                }
                if (getCreditCode_IN.IsRelated != 1)
                {
                    CheckAuditInProcess(new List<CheckCompanyUniqueInfo>() { checkCompanyUniqueInfo });
                }
            }
            else
            {
                throw new ApiException("类型错误");
            }
            getCreditCode_OUT.CreditCode = checkCompanyUniqueInfo.CreditCode;
            return getCreditCode_OUT;
        }

        /// <summary>
        /// 检查公司名称/代码的合法性 （只检查名称代码，不进行排重）
        /// </summary>
        /// <param name="checkCompanyUniqueInfos"></param>
        public void CheckCreditCode(List<CheckCompanyUniqueInfo> checkCompanyUniqueInfos)
        {
            foreach (var checkCompanyUniqueInfo in checkCompanyUniqueInfos)
            {
                if (string.IsNullOrEmpty(checkCompanyUniqueInfo.CompanyName))
                {
                    throw new ApiException("公司信息验证失败:公司名称不能为空");
                }
                if (string.IsNullOrEmpty(checkCompanyUniqueInfo.CreditCode) && checkCompanyUniqueInfo.CreditType != (int)EnumCompanyType.Person)
                {
                    throw new ApiException("公司信息验证失败:信用代码不能为空");
                }
                if (checkCompanyUniqueInfo.CreditType == (int)EnumCompanyType.CN)
                {
                    List<Db_crm_tianyancha_info> infos = new List<Db_crm_tianyancha_info>();
                    string error_code = "";
                    BLL_Tianyancha.Instance.QueryTianyancha(checkCompanyUniqueInfo.CompanyName, ref infos, ref error_code);
                    if (infos == null || infos.Count == 0)
                    {
                        throw new ApiException("未找到公司信息，请检查输入的公司名称(" + checkCompanyUniqueInfo.CompanyName + ")是否正确且完整（error_code=" + error_code + "）");
                    }
                    else
                    {
                        var exactCompany = infos.Find(i => i.CompanyName == checkCompanyUniqueInfo.CompanyName);
                        var historyCompany = infos.Find(i => i.HistoryNames != null && i.HistoryNames.Split("|").Contains(checkCompanyUniqueInfo.CompanyName));
                        if (exactCompany != null)
                        {
                            if (checkCompanyUniqueInfo.CreditCode != exactCompany.CreditCode)
                            {
                                throw new ApiException("公司信息(" + checkCompanyUniqueInfo.CompanyName + ")验证失败(2)");
                            }
                        }
                        else if (historyCompany != null)
                        {
                            var index = historyCompany.HistoryNames.Split("|").ToList().IndexOf(checkCompanyUniqueInfo.CompanyName);
                            //输入的是曾用名 特殊处理编码
                            var historyCreditCode = "$$" + index + historyCompany.CreditCode;
                            if (checkCompanyUniqueInfo.CreditCode != historyCreditCode)
                            {
                                throw new ApiException("公司信息(" + checkCompanyUniqueInfo.CompanyName + ")验证失败(3)");
                            }
                        }
                        else
                        {
                            throw new ApiException("未找到公司信息，请检查输入的公司名称是否正确且完整");
                        }
                    }
                }
                else if (checkCompanyUniqueInfo.CreditType == (int)EnumCompanyType.Other)
                {
                    //其他也需要工商信息验证一下，避免注册的就国内企业但选择其他类型；
                    // List<Db_crm_tianyancha_info> infos;
                    bool checkNotIn = false;
                    try
                    {
                        checkNotIn = BLL_Tianyancha.Instance.QueryNotIn(checkCompanyUniqueInfo.CompanyName);
                    }
                    catch (Exception e)
                    {
                        //#if DEBUG
                        throw new ApiException("公司信息验证失败:" + e.Message);
                        //#endif
                        //                        throw new ApiException("公司信息验证失败");
                    }
                    if (!checkNotIn)
                    {
                        throw new ApiException("公司信息验证失败:请检查公司类型");
                    }

                    var code = RedisCache.CreditCode.GetCreditCode(checkCompanyUniqueInfo.CompanyName);
                    if (code == null || code != checkCompanyUniqueInfo.CreditCode)
                    {
                        throw new ApiException("公司信息(" + checkCompanyUniqueInfo.CompanyName + ")验证失败");
                    }
                }
                else if (checkCompanyUniqueInfo.CreditType == (int)EnumCompanyType.Person)
                {
                    //国内客户名称不能设置为个人客户类型
                    bool checkNotIn = false;
                    try
                    {
                        checkNotIn = BLL_Tianyancha.Instance.QueryNotIn(checkCompanyUniqueInfo.CompanyName);
                    }
                    catch (Exception e)
                    {
                        //#if DEBUG
                        throw new ApiException("公司信息验证失败:" + e.Message);
                        //#endif
                        //                        throw new ApiException("公司信息验证失败");
                    }
                    if (!checkNotIn)
                    {
                        throw new ApiException("公司信息验证失败:请检查公司类型");
                    }
                    if (!string.IsNullOrEmpty(checkCompanyUniqueInfo.CreditCode))
                    {
                        IdValidator idValidator = new IdValidator(checkCompanyUniqueInfo.CreditCode);
                        if (!idValidator.Execute())
                        {
                            throw new ApiException("个人身份信息(" + checkCompanyUniqueInfo.CreditCode + ")验证失败");
                        }
                    }
                }
                else
                {
                    throw new ApiException("公司信息验证失败:公司类型错误");
                }

            }
        }

        /// <summary>
        /// 修正公司名称中的括号问题
        /// </summary>
        /// <param name="companyName">原始公司名称</param>
        /// <param name="correctedCompanyName">修正后的公司名称</param>
        /// <returns>是否修正</returns>
        private bool CorrectCompanyNameBrackets(string companyName, out string correctedCompanyName)
        {
            if (string.IsNullOrEmpty(companyName))
            {
                correctedCompanyName = companyName;
                return false;
            }

            // 检查是否包含括号
            if (!companyName.Contains('(') && !companyName.Contains(')'))
            {
                correctedCompanyName = companyName;
                return false;
            }

            // 创建两个版本的公司名称进行比较
            string nameWithChineseBrackets = companyName
                .Replace('(', '（')
                .Replace(')', '）');

            string nameWithEnglishBrackets = companyName
                .Replace('（', '(')
                .Replace('）', ')');

            // 中文公司应该使用中文括号
            correctedCompanyName = nameWithChineseBrackets;
            return true;
        }
        private void GetAddressFromTianYanChaInfo(Db_crm_tianyancha_info info, ref GetCreditCode_OUT getCreditCode_OUT)
        {
            if (info != null)
            {
                var tycCity = info.City;
                var existCity = LocalCache.LC_Address.CityCache.Find(c => c.Name == tycCity);
                if (existCity != null)
                {
                    getCreditCode_OUT.City = existCity.Id;
                    getCreditCode_OUT.Province = existCity.ProvinceID;
                    getCreditCode_OUT.Address = info.RegLocation.Replace(LocalCache.LC_Address.ProvinceCache.Find(p => p.Id == existCity.ProvinceID)?.Name ?? "", "").Replace(existCity.Name, "");
                }
                else
                {
                    getCreditCode_OUT.Address = info.RegLocation;
                }
            }
        }
    }

}

