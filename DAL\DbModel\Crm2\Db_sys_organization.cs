﻿using System;
using System.Linq;
using System.Text;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_organization")]
    public partial class Db_sys_organization
    {
           public Db_sys_organization(){


           }
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:组织编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string OrgNum {get;set;}

           /// <summary>
           /// Desc:组织名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string OrgName {get;set;}

           /// <summary>
           /// Desc:部门名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string DepartmentName {get;set;}

           /// <summary>
           /// Desc:团队类型:1-战队;2-大队;3-中队
           /// Default:
           /// Nullable:False
           /// </summary>           
           public EnumOrgType OrgType {get;set;}

           /// <summary>
           /// Desc:上级组织id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string ParentId {get;set;}

           /// <summary>
           /// Desc:是否启用:0-停用;1-启用
           /// Default:
           /// Nullable:False
           /// </summary>           
           public bool OrgStatus {get;set;}

           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Remark {get;set;}

           /// <summary>
           /// Desc:删除标识:0-未删除;1-已删除
           /// Default:
           /// Nullable:False
           /// </summary>           
           public bool Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:修改时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}


    }
}
