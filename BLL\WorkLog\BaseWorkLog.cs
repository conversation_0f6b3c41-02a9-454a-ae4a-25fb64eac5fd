﻿using CRM2_API.Common.JWT;

namespace CRM2_API.BLL.WorkLog
{
    public class BaseWorkLog<WorkLogClass> where WorkLogClass : class, new()
    {
        private static AsyncLocal<WorkLogClass> _Instance = new AsyncLocal<WorkLogClass>();

        /// <summary>
        /// 用户token信息
        /// </summary>
        public TokenModel UserTokenInfo => TokenModel.Instance;
        /// <summary>
        /// 实例化当前线程的BLL对象
        /// </summary>
        public static WorkLogClass Instance
        {
            get
            {
                if (_Instance.Value is null)
                    _Instance.Value = new WorkLogClass();
                return _Instance.Value;
            }
        }
    }
}
