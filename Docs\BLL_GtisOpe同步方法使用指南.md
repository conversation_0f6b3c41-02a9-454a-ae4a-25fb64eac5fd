# BLL_GtisOpe 同步方法使用指南

## 问题背景

在项目中使用 `BLL_GtisOpe` 的异步方法时，如果直接使用 `.Result` 属性等待异步任务完成，可能会遇到 `AggregateException` 包装的异常，导致程序提前退出或崩溃。

## 解决方案

我们已经在 `BLL_GtisOpe` 类中添加了同步包装方法，这些方法内部会正确处理 `AggregateException` 并提供友好的错误信息。

## 可用的同步方法

### 1. 用户信息相关
- `GetUserInfoSync(params string[] svCodes)` - 获取用户信息
- `GetUserStateSync(string[] svCodes)` - 获取用户状态
- `GetAccountUsersInfoSync(string sysuserid)` - 获取账号使用者信息

### 2. 公司信息相关
- `GetCompanyOtherInfoSync(string svcode)` - 获取公司其他信息

### 3. 用户操作相关
- `AddUserSync(BM_AddGtisUser crmAddUser)` - 添加用户
- `RenewalContactSync(BM_GtisOpe_RenewalContact changeInfo)` - 续约联系

### 4. 日志相关
- `GetAllUserOperateLogStaSync(string mainSysUserId, string[] svcodes)` - 获取所有用户操作日志统计
- `GetOpeLogDetailSync(BM_GtisOpeGetOpeLogDetal logDetail)` - 获取操作日志详情
- `GetExportLogDetailSync(BM_GtisOpeGetExportLogDetal expDetail)` - 获取导出日志详情

### 5. 演示账号相关
- `GetDemoInfoSync(int taskID)` - 获取演示账号信息
- `GetDemoLeftOpenTimesSync(string oprID)` - 获取演示账号剩余开通次数
- `GetAllDemoUserOperateLogStaSync(int taskID, string oprID)` - 获取所有演示用户操作日志统计
- `GetDemoBindWxImgSync(string id)` - 获取演示账号绑定微信图片

### 6. 其他功能
- `GetG5Phone_ListSync(int pageNum, int pageSize)` - 获取G5手机号列表
- `GetG5Phone_NewSync()` - 获取G5手机号最新信息
- `GetGtisAllSidsSync()` - 获取GTIS所有SID
- `GetAccountTenantIdSync(string svcode, string sysuserid = "", string phoneid = "")` - 获取租户ID

## 使用示例

### 错误的使用方式（不推荐）
```csharp
// 这种方式可能会抛出 AggregateException
var userInfo = BLL_GtisOpe.Instance.GetUserInfo(contractNum).Result;
```

### 正确的使用方式（推荐）
```csharp
// 使用同步包装方法，异常会被正确处理
var userInfo = BLL_GtisOpe.Instance.GetUserInfoSync(contractNum);
```

## 异常处理

同步包装方法会自动处理以下异常：
1. `AggregateException` - 包装的异步异常
2. `ApiException` - API调用异常
3. 其他一般异常

所有异常都会被重新包装为带有明确错误信息的 `Exception`，便于调试和错误处理。

## 迁移建议

1. **逐步替换**：在现有代码中逐步将 `.Result` 调用替换为对应的同步方法
2. **保持一致性**：在同一个方法中，建议统一使用同步方法或异步方法
3. **错误处理**：使用同步方法后，可以简化异常处理逻辑

## 注意事项

1. 同步方法会阻塞当前线程，在性能要求高的场景下，建议使用异步方法
2. 同步方法内部仍然使用 `.Result`，但异常处理已经优化
3. 如果需要更细粒度的异常处理，可以继续使用异步方法并手动处理异常

## 示例：完整的方法替换

### 替换前
```csharp
public OriginUserGroupInfo GetAndSynchroUserInfo(string contractNum)
{
    var retValue = new OriginUserGroupInfo();
    DbOpe_crm_contract.Instance.TransDeal(() =>
    {
        // 可能抛出 AggregateException
        var gtisOpeUserList = BLL_GtisOpe.Instance.GetUserInfo(new string[] { contractNum }).Result;
        // ... 其他代码
    });
    return retValue;
}
```

### 替换后
```csharp
public OriginUserGroupInfo GetAndSynchroUserInfo(string contractNum)
{
    var retValue = new OriginUserGroupInfo();
    DbOpe_crm_contract.Instance.TransDeal(() =>
    {
        // 异常会被正确处理
        var gtisOpeUserList = BLL_GtisOpe.Instance.GetUserInfoSync(new string[] { contractNum });
        // ... 其他代码
    });
    return retValue;
}
```

通过使用这些同步包装方法，可以有效避免 `AggregateException` 导致的程序提前退出问题，提高系统的稳定性和可维护性。 