using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 报告抄送人表
    /// </summary>
    [SugarTable("crm_report_cc")]
    public class Db_crm_report_cc
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 报告ID
        /// </summary>
        public string ReportId { get; set; }

        /// <summary>
        /// 抄送人ID
        /// </summary>
        public string CcUserId { get; set; }

        /// <summary>
        /// 抄送人姓名
        /// </summary>
        public string CcUserName { get; set; }

        /// <summary>
        /// 是否默认抄送人：0-否，1-是
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 状态：1-未读，2-已读
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateDate { get; set; }
    }
} 