using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CRM2_API.BLL.Common;
using SqlSugar;
using System.Linq.Expressions;
using CRM2_API.DAL.DbCommon;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using Spire.Doc;
using System.Text;
using System.Web;
using System.Text.RegularExpressions;
using CRM2_API.Common.AppSetting;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类
    /// 重构版本 - 使用partial class分离不同功能
    /// </summary>
    public partial class BLL_ContractInvoiceNew : BaseBLL<BLL_ContractInvoiceNew>
    {
    }
} 