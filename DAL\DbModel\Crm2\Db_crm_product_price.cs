﻿using System;
using System.Linq;
using System.Text;
using Microsoft.AspNetCore.Components.Forms;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_product_price")]
    public class Db_crm_product_price
    {
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:产品表Id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string ProductId { get; set; }
        
        /// <summary>
        /// Desc:区分售卖标识 0 国内/个人 1 国外
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int SalesFloor { get; set; }

        /// <summary>
        /// Desc:价格分类(固定/起售/区间/起购复购/(主账号/子账号)/(赠送/自费))
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int PriceMode { get; set; }

        /// <summary>
        /// Desc:币种
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int Currency { get; set; }

        /// <summary>
        /// Desc:区间上限/复购价/自费价
        /// Default:
        /// Nullable:True
        /// </summary>           
        public decimal? PricePart2 { get; set; }

        /// <summary>
        /// Desc:价格
        /// Default:
        /// Nullable:False
        /// </summary>           
        public decimal Price { get; set; }

        /// <summary>
        /// Desc:计费参数
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int ChargingParameters { get; set; }

        /// <summary>
        /// Desc:服务周期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ServiceCycle { get; set; }

        /// <summary>
        /// Desc:价格适用属性(如saleswits用户数)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? PriceInfo { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建日期
        /// Default:
        /// Nullable:False
        /// </summary>           
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
