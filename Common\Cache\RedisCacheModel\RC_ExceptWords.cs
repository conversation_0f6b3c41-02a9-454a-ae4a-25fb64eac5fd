using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        /// <summary>
        /// 排除词列表缓存
        /// </summary>
        public class ExceptWords
        {
            const string EXCEPTWORDS_ALL = "exceptwords_all";
            
            /// <summary>
            /// 初始化排除词缓存
            /// </summary>
            public static void InitializeCache()
            {
                // 从数据库获取并更新缓存
                var exceptWords = DbOpe_crm_tianyancha_othernames.Instance.Query_crm_tianyancha_othernames().Select(o => o.Names).ToList();
                
                // 清除缓存并保存
                ClearCache();
                SaveExceptWords(exceptWords);
            }
            
            /// <summary>
            /// 获取所有排除词
            /// </summary>
            /// <returns>排除词列表，如果缓存不存在返回null</returns>
            public static List<string> GetAllExceptWords()
            {
                if (RedisHelper.Exists(EXCEPTWORDS_ALL))
                    return RedisHelper.Get<List<string>>(EXCEPTWORDS_ALL);
                
                // 如果缓存不存在，初始化缓存
                InitializeCache();
                return RedisHelper.Get<List<string>>(EXCEPTWORDS_ALL);
            }
            
            /// <summary>
            /// 保存排除词列表到缓存
            /// </summary>
            /// <param name="exceptWords">排除词列表</param>
            public static void SaveExceptWords(List<string> exceptWords)
            {
                RedisHelper.Set(EXCEPTWORDS_ALL, exceptWords, TimeSpan.FromHours(26));
            }
            
            /// <summary>
            /// 检查词语是否在排除词列表中
            /// </summary>
            /// <param name="word">要检查的词语</param>
            /// <returns>如果是排除词返回true，否则返回false</returns>
            public static bool IsExceptWord(string word)
            {
                var exceptWords = GetAllExceptWords();
                if (exceptWords != null && exceptWords.Count > 0)
                {
                    return exceptWords.Contains(word);
                }
                return false;
            }
            
            /// <summary>
            /// 清除排除词缓存
            /// </summary>
            public static void ClearCache()
            {
                RedisHelper.Del(EXCEPTWORDS_ALL);
            }
        }
    }
} 