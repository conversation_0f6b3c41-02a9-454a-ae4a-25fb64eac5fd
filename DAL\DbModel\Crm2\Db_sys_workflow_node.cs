﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///审批流节点表
    ///</summary>
    [SugarTable("sys_workflow_node")]
    public class Db_sys_workflow_node
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:审批流表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string WorkFlowId {get;set;}

           /// <summary>
           /// Desc:节点名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string NodeName {get;set;}

           /// <summary>
           /// Desc:所属表单id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string FormId {get;set;}

           /// <summary>
           /// Desc:流出条件
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string FlowCondition {get;set;}

           /// <summary>
           /// Desc:接收类型(角色、用户、上级节点实际处理人id、流程发起人等)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? RecipientType { get; set; }

           /// <summary>
           /// Desc:接收组
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string RoleId {get;set;}

           /// <summary>
           /// Desc:接收人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId { get; set; }

           /// <summary>
           /// Desc:目标地址（目标表单）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string FlowUrl {get;set;}

           /// <summary>
           /// Desc:信息模板
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string InfoTemp {get;set;}

           /// <summary>
           /// Desc:下一个流程节点id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string NextFormId {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
