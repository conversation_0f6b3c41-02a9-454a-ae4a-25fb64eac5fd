using System.ComponentModel;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.Enum;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using CRM2_API.Common.Filter;
using CRM2_API.Common.JWT;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System;
using LgyUtil;


namespace CRM2_API.Controllers
{
    /// <summary>
    /// 工作报告管理控制器（简化版）
    /// </summary>
    [Description("工作报告管理控制器")]
    public class ReportController : MyControllerBase
    {
        public ReportController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }



        /// <summary>
        /// 获取报告列表
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>报告列表</returns>
        [HttpPost]
        public ApiTableOut<SearchReportList_Out> SearchReportList(SearchReportList_In input)
        {
            if (input == null)
            {
                throw new ApiException("查询条件不能为空");
            }

            
            

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.SearchReportList(input);
        }



        /// <summary>
        /// 获取报告详情（简化版，固定字段结构）
        /// 严格按照需求文档的日报/周报/月报模板字段返回
        /// </summary>
        /// <param name="input">报告ID输入</param>
        /// <returns>报告详情（简化版）</returns>
        [HttpPost]
        public GetReportDetailSimple_Out GetReportDetailSimple(GetReportDetail_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.Id))
            {
                throw new ApiException("报告ID不能为空");
            }

            
            

            // 使用数据库操作层实现
            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetReportDetailSimple(input.Id);
        }

        /// <summary>
        /// 创建报告（固定字段结构）
        /// 严格按照需求文档的字段结构，不使用动态字段列表
        /// </summary>
        /// <param name="input">创建报告输入（固定字段）</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public SaveReport_Out CreateReportFixed([FromForm] CreateReportFixed_In input)
        {
            if (input == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            // 同步JSON和List数据
            if (input.DailyContent != null)
            {
                input.DailyContent.SyncJsonAndListData();
            }
            
            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.CreateReportFixed(input);
        }

        /// <summary>
        /// 更新报告（固定字段结构）
        /// 严格按照需求文档的字段结构，不使用动态字段列表
        /// </summary>
        /// <param name="input">更新报告输入（固定字段）</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        public SaveReport_Out UpdateReportFixed([FromForm] UpdateReportFixed_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.Id))
            {
                throw new ApiException("输入参数或报告ID不能为空");
            }

            // 同步JSON和List数据
            if (input.DailyContent != null)
            {
                input.DailyContent.SyncJsonAndListData();
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.UpdateReportFixed(input);
        }





        /// <summary>
        /// 删除报告
        /// </summary>
        /// <param name="input">删除报告输入</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public OperationResult_Out DeleteReport(DeleteReport_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.Id))
            {
                throw new ApiException("报告ID不能为空");
            }

            
            

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.DeleteReport(input.Id);
        }

        #region 接收人和抄送人管理

        /// <summary>
        /// 获取可选的接收人列表（包括默认接收人）
        /// </summary>
        /// <returns>接收人列表</returns>
        [HttpGet]
        public System.Collections.Generic.List<VM_ReportReceiver> GetAvailableReceivers()
        {
            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetAvailableReceivers();
        }

        /// <summary>
        /// 设置默认接收人
        /// </summary>
        /// <param name="input">设置默认接收人输入</param>
        /// <returns>设置结果</returns>
        [HttpPost]
        public OperationResult_Out SetDefaultReceivers(SetDefaultReceivers_In input)
        {
            if (input == null || input.ReceiverIds == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.SetDefaultReceivers(input);
        }

        /// <summary>
        /// 获取可选的抄送人列表（包括默认抄送人）
        /// </summary>
        /// <returns>抄送人列表</returns>
        [HttpGet]
        public System.Collections.Generic.List<VM_ReportReceiver> GetAvailableCcs()
        {
            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetAvailableCcs();
        }

        /// <summary>
        /// 设置默认抄送人
        /// </summary>
        /// <param name="input">设置默认抄送人输入</param>
        /// <returns>设置结果</returns>
        [HttpPost]
        public OperationResult_Out SetDefaultCcs(SetDefaultCcs_In input)
        {
            if (input == null || input.CcIds == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.SetDefaultCcs(input);
        }

        #endregion

        #region 评论功能

        /// <summary>
        /// 获取报告评论列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public System.Collections.Generic.List<GetReportCommentList_Out> GetReportCommentList(GetReportCommentList_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.ReportId))
            {
                throw new ApiException("报告ID不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetReportCommentList(input);
        }

        /// <summary>
        /// 添加报告评论
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public AddReportComment_Out AddReportComment(AddReportComment_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.ReportId) || string.IsNullOrEmpty(input.CommentContent))
            {
                throw new ApiException("报告ID和评论内容不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.AddReportComment(input);
        }

        /// <summary>
        /// 更新报告评论
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public ReportInteractionResult_Out UpdateReportComment(UpdateReportComment_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.CommentId) || string.IsNullOrEmpty(input.CommentContent))
            {
                throw new ApiException("评论ID和评论内容不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.UpdateReportComment(input);
        }

        /// <summary>
        /// 删除报告评论
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public ReportInteractionResult_Out DeleteReportComment(DeleteReportComment_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.CommentId))
            {
                throw new ApiException("评论ID不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.DeleteReportComment(input);
        }

        #endregion

        #region 点赞功能

        /// <summary>
        /// 报告点赞
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public AddReportLike_Out AddReportLike(AddReportLike_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.ReportId))
            {
                throw new ApiException("报告ID不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.AddReportLike(input);
        }

        /// <summary>
        /// 取消报告点赞
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public CancelReportLike_Out CancelReportLike(CancelReportLike_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.ReportId))
            {
                throw new ApiException("报告ID不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.CancelReportLike(input);
        }

        /// <summary>
        /// 获取报告点赞列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public System.Collections.Generic.List<GetReportLikeList_Out> GetReportLikeList(GetReportLikeList_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.ReportId))
            {
                throw new ApiException("报告ID不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetReportLikeList(input);
        }

        #endregion

        #region 互动统计

        /// <summary>
        /// 获取报告互动统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public GetReportInteractionStats_Out GetReportInteractionStats(GetReportInteractionStats_In input)
        {
            if (input == null || string.IsNullOrEmpty(input.ReportId))
            {
                throw new ApiException("报告ID不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetReportInteractionStats(input);
        }

        #endregion

        // /// <summary>
        // /// 获取报告模板
        // /// </summary>
        // /// <param name="input">获取模板输入</param>
        // /// <returns>报告模板</returns>




        /// <summary>
        /// 获取简化版输入示例
        /// </summary>
        /// <returns>简化版输入示例</returns>
        [HttpGet]
        public CreateReportSimple_In GetSimpleInputExample()
        {
            return new CreateReportSimple_In
            {
                ReportType = EnumReportType.Daily,
                ReportDate = System.DateTime.Today,
                Title = "2024年6月25日工作日",
                Fields = new System.Collections.Generic.List<VM_ReportFieldInput>
                {
                    // 工作回顾模块 - 工作量统计
                    new VM_ReportFieldInput { ModuleKey = "work_review", SectionKey = "phone_count", FieldType = "number", FieldValue = "15" },
                    new VM_ReportFieldInput { ModuleKey = "work_review", SectionKey = "visit_count", FieldType = "number", FieldValue = "3" },
                    new VM_ReportFieldInput { ModuleKey = "work_review", SectionKey = "email_rough_count", FieldType = "number", FieldValue = "25" },
                    new VM_ReportFieldInput { ModuleKey = "work_review", SectionKey = "email_precise_count", FieldType = "number", FieldValue = "8" },
                    new VM_ReportFieldInput { ModuleKey = "work_review", SectionKey = "social_count", FieldType = "number", FieldValue = "12" },
                    new VM_ReportFieldInput { ModuleKey = "work_review", SectionKey = "reply_count", FieldType = "number", FieldValue = "6" },
                    new VM_ReportFieldInput { ModuleKey = "work_review", SectionKey = "demo_count", FieldType = "number", FieldValue = "2" },
                    new VM_ReportFieldInput { ModuleKey = "other_planning", SectionKey = "other_work", FieldType = "text", FieldValue = "整理客户资料，制作产品演示PPT" },

                    // 客户新增模块
                    new VM_ReportFieldInput { ModuleKey = "customer_new", SectionKey = "customer_level", FieldType = "select", FieldValue = "A", CustomerId = "cust001" },
                    new VM_ReportFieldInput { ModuleKey = "customer_new", SectionKey = "country", FieldType = "select", FieldValue = "美国", CustomerId = "cust001" },
                    new VM_ReportFieldInput { ModuleKey = "customer_new", SectionKey = "company_name", FieldType = "text", FieldValue = "ABC贸易公司", CustomerId = "cust001" },
                    new VM_ReportFieldInput { ModuleKey = "customer_new", SectionKey = "main_product", FieldType = "text", FieldValue = "电子产品", CustomerId = "cust001" },
                    new VM_ReportFieldInput { ModuleKey = "customer_new", SectionKey = "hs_code", FieldType = "text", FieldValue = "8517120000", CustomerId = "cust001" },
                    new VM_ReportFieldInput { ModuleKey = "customer_new", SectionKey = "communication_summary", FieldType = "text", FieldValue = "通过LinkedIn联系，对方表示有采购意向，计划下周进行产品演示", CustomerId = "cust001" },

                    // 客户跟进模块
                    new VM_ReportFieldInput { ModuleKey = "customer_follow", SectionKey = "company_name", FieldType = "text", FieldValue = "XYZ电子科技", CustomerId = "cust002" },
                    new VM_ReportFieldInput { ModuleKey = "customer_follow", SectionKey = "main_product", FieldType = "text", FieldValue = "智能设备", CustomerId = "cust002" },
                    new VM_ReportFieldInput { ModuleKey = "customer_follow", SectionKey = "hs_code", FieldType = "text", FieldValue = "8471300000", CustomerId = "cust002" },
                    new VM_ReportFieldInput { ModuleKey = "customer_follow", SectionKey = "recommended_solution", FieldType = "text", FieldValue = "定制化ODM方案", CustomerId = "cust002" },
                    new VM_ReportFieldInput { ModuleKey = "customer_follow", SectionKey = "follow_summary", FieldType = "text", FieldValue = "客户对价格满意，正在内部评估，预计本月底给答复", CustomerId = "cust002" },

                    // 客户签约模块
                    new VM_ReportFieldInput { ModuleKey = "customer_sign", SectionKey = "company_name", FieldType = "text", FieldValue = "DEF贸易有限公司", CustomerId = "cust003" },
                    new VM_ReportFieldInput { ModuleKey = "customer_sign", SectionKey = "main_product", FieldType = "text", FieldValue = "消费电子", CustomerId = "cust003" },
                    new VM_ReportFieldInput { ModuleKey = "customer_sign", SectionKey = "hs_code", FieldType = "text", FieldValue = "8518300000", CustomerId = "cust003" },
                    new VM_ReportFieldInput { ModuleKey = "customer_sign", SectionKey = "contract_solution", FieldType = "text", FieldValue = "年度采购协议", CustomerId = "cust003" },
                    new VM_ReportFieldInput { ModuleKey = "customer_sign", SectionKey = "contract_summary", FieldType = "text", FieldValue = "成功签订50万美金年度采购协议，首批订单计划下月发货", CustomerId = "cust003" },

                    // 客户服务模块
                    new VM_ReportFieldInput { ModuleKey = "customer_service", SectionKey = "company_name", FieldType = "text", FieldValue = "GHI国际贸易", CustomerId = "cust004" },
                    new VM_ReportFieldInput { ModuleKey = "customer_service", SectionKey = "service_content", FieldType = "text", FieldValue = "处理产品质量反馈，协调工厂改进生产工艺，客户表示满意" },

                    // 其他规划与建议模块
                    new VM_ReportFieldInput { ModuleKey = "other_planning", SectionKey = "product_planning", FieldType = "text", FieldValue = "重点推广新款智能音箱，目标客户为北美电子产品经销商" },
                    new VM_ReportFieldInput { ModuleKey = "other_planning", SectionKey = "region_planning", FieldType = "text", FieldValue = "加强欧洲市场开发，特别是德国和法国地区" },
                    new VM_ReportFieldInput { ModuleKey = "other_planning", SectionKey = "sales_planning", FieldType = "text", FieldValue = "制定Q2销售计划，目标新增10个A级客户" },
                    new VM_ReportFieldInput { ModuleKey = "other_planning", SectionKey = "difficulties", FieldType = "text", FieldValue = "部分客户对产品认证要求较高，需要技术部门支持" },
                    new VM_ReportFieldInput { ModuleKey = "other_planning", SectionKey = "support_needed", FieldType = "text", FieldValue = "希望市场部提供欧洲市场的竞争分析报告" },
                    new VM_ReportFieldInput { ModuleKey = "other_planning", SectionKey = "suggestions", FieldType = "text", FieldValue = "建议增加产品展示样品，提高客户体验" },

                    // 学习与思考模块
                    new VM_ReportFieldInput { ModuleKey = "learning_thinking", SectionKey = "learning_content", FieldType = "text", FieldValue = "学习了国际贸易新政策，了解了RCEP对出口业务的影响和机遇" },

                    // 附件模块 - 用于标识需要上传的附件类型
                    new VM_ReportFieldInput { ModuleKey = "attachments", SectionKey = "work_screenshots", FieldType = "attachment", FieldValue = "工作截图" },
                    new VM_ReportFieldInput { ModuleKey = "attachments", SectionKey = "customer_communications", FieldType = "attachment", FieldValue = "客户沟通记录" },
                    new VM_ReportFieldInput { ModuleKey = "attachments", SectionKey = "contract_documents", FieldType = "attachment", FieldValue = "合同相关文档" }

                    // 说明：
                    // 1. FieldValue 表示附件分类描述
                    // 2. 实际文件必须通过 CreateReport 接口files 参数一起提交
                }
            };
        }

        #region 时效管理功能

        /// <summary>
        /// 获取节假日分页列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<VM_Holiday_Out> GetHolidayPageList(VM_HolidayQuery_In input)
        {
            if (input == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetHolidayPageList(input);
        }

        /// <summary>
        /// 检查是否为工作日
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public VM_WorkdayCheck_Out CheckWorkday(VM_WorkdayCheck_In input)
        {
            if (input == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.CheckWorkday(input);
        }

        /// <summary>
        /// 获取下一个工作日
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public VM_NextWorkday_Out GetNextWorkday(VM_NextWorkday_In input)
        {
            if (input == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetNextWorkday(input);
        }

        /// <summary>
        /// 获取指定报告类型的生效时间配置
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public VM_TimeConfig_Out GetActiveTimeConfig(VM_ActiveTimeConfig_In input)
        {
            if (input == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetActiveTimeConfig(input);
        }

        /// <summary>
        /// 计算报告截止时间
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public VM_ReportDeadlineCalc_Out CalculateReportDeadline(VM_ReportDeadlineCalc_In input)
        {
            if (input == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.CalculateReportDeadline(input);
        }

        /// <summary>
        /// 同步节假日数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        [SkipIPCheck]
        [SkipAuthCheck]
        [SkipRSAKey]
        public VM_HolidaySync_Out SyncHolidayData(VM_HolidaySync_In input)
        {
            if (input == null)
            {
                throw new ApiException("输入参数不能为空");
            }




            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.SyncHolidayData(input);
        }

        #endregion

        #region 工作台接口

        /// <summary>
        /// 获取个人工作台数据
        /// </summary>
        /// <returns>个人工作台数据</returns>
        [HttpPost]
        public GetPersonalWorkbench_Out GetPersonalWorkbench(GetPersonalWorkbench_In input)
        {
            
            

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetPersonalWorkbench();
        }

        /// <summary>
        /// 获取团队管理区数据
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>团队管理区数据</returns>
        [HttpPost]
        public GetTeamManagement_Out GetTeamManagement(GetTeamManagement_In input)
        {
            if (input == null)
            {
                throw new ApiException("查询条件不能为空");
            }

            
            

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetTeamManagement(input);
        }

        /// <summary>
        /// 获取我的报告管理数据
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>我的报告数据</returns>
        [HttpPost]
        public GetMyReports_Out GetMyReports(GetMyReports_In input)
        {
            if (input == null)
            {
                throw new ApiException("查询条件不能为空");
            }

            
            

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetMyReports(input);
        }

        /// <summary>
        /// 获取团队成员状态数据
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>团队成员状态数据</returns>
        [HttpPost]
        public ApiTableOut<VM_TeamMember> GetTeamMembers(GetTeamMembers_In input)
        {
            if (input == null)
            {
                throw new ApiException("查询条件不能为空");
            }

            
            

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetTeamMembers(input);
        }

        #endregion

        #region 周报月报接口

        /// <summary>
        /// 生成周报
        /// </summary>
        /// <param name="input">生成周报输入</param>
        /// <returns>生成结果</returns>
        [HttpPost]
        public VM_WeeklyMonthlyResult_Out GenerateWeeklyReport(GenerateWeeklyReport_In input)
        {
            if (input == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            if (input.StartDate >= input.EndDate)
            {
                throw new ApiException("开始日期必须小于结束日期");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GenerateWeeklyReport(input);
        }



        /// <summary>
        /// 生成月报
        /// </summary>
        /// <param name="input">生成月报输入</param>
        /// <returns>生成结果</returns>
        [HttpPost]
        public VM_WeeklyMonthlyResult_Out GenerateMonthlyReport(GenerateMonthlyReport_In input)
        {
            if (input == null)
            {
                throw new ApiException("输入参数不能为空");
            }

            if (input.Month < 1 || input.Month > 12)
            {
                throw new ApiException("月份必须在1-12之间");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GenerateMonthlyReport(input);
        }



        /// <summary>
        /// 获取工作日历
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>工作日历数据</returns>
        [HttpPost]
        public VM_WorkCalendar GetWorkCalendar(GetWorkCalendar_In input)
        {
            if (input == null)
            {
                throw new ApiException("查询条件不能为空");
            }

            if (input.ReportDate == DateTime.MinValue)
            {
                throw new ApiException("报告日期不能为空");
            }

            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetWorkCalendar(input);
        }



        #endregion

        #region 调试方法

        /// <summary>
        /// 调试Token信息
        /// </summary>
        /// <param name="input">调试输入</param>
        /// <returns>调试信息</returns>
        [HttpPost]
        public object DebugToken(object input)
        {
            

            return new
            {
                TokenModelId = TokenModel.Instance?.id ?? "NULL",
                TokenModelName = TokenModel.Instance?.name ?? "NULL",
                HasAuthHeader = Request.Headers.ContainsKey("Authorization"),
                AuthHeader = Request.Headers.ContainsKey("Authorization") ? Request.Headers["Authorization"].ToString() : "NULL",
                Input = input
            };
        }

        /// <summary>
        /// 综合工作流程测试接口
        /// 完整测试：创建报告 -> 修改报告 -> 验证数据完整性
        /// </summary>
        /// <param name="input">测试输入参数</param>
        /// <returns>完整测试结果</returns>
        [HttpPost]
        public object ComprehensiveWorkflowTest(object input)
        {
            var testDate = DateTime.Parse("2025-08-01");
            

            var testResults = new List<object>();
            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            string createdReportId = null;

            try
            {
                // Step 1: 用户身份验证
                testResults.Add(new
                {
                    Step = "1. 用户身份验证",
                    Success = true,
                    UserId = TokenModel.Instance?.id ?? "NULL",
                    UserName = TokenModel.Instance?.name ?? "NULL",
                    Message = "身份验证成功"
                });

                // Step 2: 准备测试附件
                var testAttachments = new List<IFormFile>();
                
                // 创建测试附件1 - 文本文件
                byte[] textFileBytes = System.Text.Encoding.UTF8.GetBytes("这是一个测试文本文件内容");
                var textFile = new FormFile(
                    new MemoryStream(textFileBytes),
                    0,
                    textFileBytes.Length,
                    "text_file",
                    "测试文本文件.txt"
                )
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "text/plain"
                };
                testAttachments.Add(textFile);
                
                // 创建测试附件2 - 简单图片数据
                byte[] imageFileBytes = new byte[100]; // 模拟图片数据
                for (int i = 0; i < imageFileBytes.Length; i++)
                {
                    imageFileBytes[i] = (byte)(i % 256);
                }
                var imageFile = new FormFile(
                    new MemoryStream(imageFileBytes),
                    0,
                    imageFileBytes.Length,
                    "image_file",
                    "测试图片.png"
                )
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "image/png"
                };
                testAttachments.Add(imageFile);
                
                testResults.Add(new
                {
                    Step = "2. 准备测试附件",
                    Success = true,
                    AttachmentCount = testAttachments.Count,
                    Attachments = testAttachments.Select(a => new { a.FileName, a.Length, a.ContentType }).ToList(),
                    Message = "测试附件准备完成"
                });

                // Step 3: 创建综合测试报告
                var createInput = new CreateReportFixed_In
                {
                    ReportType = EnumReportType.Daily, // 日报
                    Title = "综合测试日报 - " + testDate.ToString("yyyy-MM-dd"),
                    ReportDate = testDate,
                    Status = EnumReportStatus.Draft,

                    // 日报内容
                    DailyContent = new VM_DailyReportInput
                    {
                        // 1.1.1 工作回顾
                        PhoneCount = 15,
                        VisitCount = 8,
                        EmailPreciseCount = 5,
                        EmailBulkCount = 12,
                        SocialMediaCount = 6,
                        ReplyCount = 18,
                        DemoCount = 2,
                        Other = "参加了部门会议，讨论了新产品推广策略",

                        // 1.1.2 客户新增
                        CustomerNew = new List<VM_CustomerNewInput>
                        {
                            new VM_CustomerNewInput
                            {
                                CustomerLevel = "A",
                                Country = "中国",
                                CompanyName = "测试科技有限公司",
                                MainProducts = "8517120000",
                                CommunicationSummary = "客户对我们的产品很感兴趣，初步洽谈产品需求",
                                CustomerId = "test-customer-001"
                            }
                        },

                        // 1.1.3 客户跟进
                        CustomerFollow = new List<VM_CustomerFollowInput>
                        {
                            new VM_CustomerFollowInput
                            {
                                CompanyName = "跟进科技公司",
                                MainProducts = "8517120000",
                                RecommendedSolution = "推荐标准版产品方案",
                                FollowUpSummary = "客户表示需要进一步评估，安排下周演示",
                                CustomerId = "test-customer-002"
                            }
                        },

                        // 1.1.6 其他规划与建议
                        ProductPlanning = "重点推广新产品线，关注客户反馈",
                        RegionPlanning = "下周重点开拓新市场区域",
                        SalesPlanning = "本月目标完成合同签约10个，回款100万",
                        Difficulties = "部分客户对价格有异议，需要进一步沟通",
                        SupportNeeded = "需要协调技术部门支持",
                        Suggestions = "建议增加产品演示环节，提高客户满意度",

                        // 1.1.7 学习与思考
                        LearningAndThinking = "今日完成客户拜访8次，电话沟通15次，新增潜在客户3个"
                    },
                    
                    // 附件集合
                    Files = new FormFileCollection()
                };
                
                // 添加测试附件到创建请求
                foreach (var attachment in testAttachments)
                {
                    ((FormFileCollection)createInput.Files).Add(attachment);
                }

                var createResult = bll.CreateReportFixed(createInput);
                createdReportId = createResult.Id;

                testResults.Add(new
                {
                    Step = "3. 创建综合测试报告",
                    Success = true,
                    ReportId = createdReportId,
                    Message = "报告创建成功"
                });

                // Step 4: 验证创建的数据
                var detailResult = bll.GetReportDetailSimple(createdReportId);

                // 验证附件数据
                var attachmentVerification = new
                {
                    ExpectedAttachmentCount = testAttachments.Count,
                    ActualAttachmentCount = detailResult.Attachments?.Count ?? 0,
                    AttachmentsMatch = (detailResult.Attachments?.Count ?? 0) == testAttachments.Count,
                    AttachmentDetails = detailResult.Attachments?.Select(a => new
                    {
                        a.Id,
                        a.FileName,
                        a.FileSize,
                        a.FileType,
                        a.ModuleKey,
                        a.SectionKey
                    }).ToList()
                };

                var createVerification = new
                {
                    Step = "4. 验证创建数据",
                    Success = true,
                    FieldVerification = new
                    {
                        // 工作回顾数据验证
                        PhoneCount = new { Expected = 15, Actual = detailResult.DailyContent?.PhoneCount, Match = detailResult.DailyContent?.PhoneCount == 15 },
                        VisitCount = new { Expected = 8, Actual = detailResult.DailyContent?.VisitCount, Match = detailResult.DailyContent?.VisitCount == 8 },
                        EmailPreciseCount = new { Expected = 5, Actual = detailResult.DailyContent?.EmailPreciseCount, Match = detailResult.DailyContent?.EmailPreciseCount == 5 },
                        EmailBulkCount = new { Expected = 12, Actual = detailResult.DailyContent?.EmailBulkCount, Match = detailResult.DailyContent?.EmailBulkCount == 12 },
                        SocialMediaCount = new { Expected = 6, Actual = detailResult.DailyContent?.SocialMediaCount, Match = detailResult.DailyContent?.SocialMediaCount == 6 },
                        ReplyCount = new { Expected = 18, Actual = detailResult.DailyContent?.ReplyCount, Match = detailResult.DailyContent?.ReplyCount == 18 },
                        DemoCount = new { Expected = 2, Actual = detailResult.DailyContent?.DemoCount, Match = detailResult.DailyContent?.DemoCount == 2 },
                        Other = new { Expected = "参加了部门会议，讨论了新产品推广策略", Actual = detailResult.DailyContent?.Other, Match = detailResult.DailyContent?.Other == "参加了部门会议，讨论了新产品推广策略" },

                        // 规划与建议字段验证
                        ProductPlanning = new { Expected = "重点推广新产品线，关注客户反馈", Actual = detailResult.DailyContent?.ProductPlanning, Match = detailResult.DailyContent?.ProductPlanning == "重点推广新产品线，关注客户反馈" },
                        RegionPlanning = new { Expected = "下周重点开拓新市场区域", Actual = detailResult.DailyContent?.RegionPlanning, Match = detailResult.DailyContent?.RegionPlanning == "下周重点开拓新市场区域" },
                        SalesPlanning = new { Expected = "本月目标完成合同签约10个，回款100万", Actual = detailResult.DailyContent?.SalesPlanning, Match = detailResult.DailyContent?.SalesPlanning == "本月目标完成合同签约10个，回款100万" },
                        Difficulties = new { Expected = "部分客户对价格有异议，需要进一步沟通", Actual = detailResult.DailyContent?.Difficulties, Match = detailResult.DailyContent?.Difficulties == "部分客户对价格有异议，需要进一步沟通" },
                        SupportNeeded = new { Expected = "需要协调技术部门支持", Actual = detailResult.DailyContent?.SupportNeeded, Match = detailResult.DailyContent?.SupportNeeded == "需要协调技术部门支持" },
                        Suggestions = new { Expected = "建议增加产品演示环节，提高客户满意度", Actual = detailResult.DailyContent?.Suggestions, Match = detailResult.DailyContent?.Suggestions == "建议增加产品演示环节，提高客户满意度" },
                        LearningAndThinking = new { Expected = "今日完成客户拜访8次，电话沟通15次，新增潜在客户3个", Actual = detailResult.DailyContent?.LearningAndThinking, Match = detailResult.DailyContent?.LearningAndThinking == "今日完成客户拜访8次，电话沟通15次，新增潜在客户3个" },

                        // 客户数据验证
                        CustomerNewCount = new { Expected = 1, Actual = detailResult.DailyContent?.CustomerNew?.Count ?? 0, Match = (detailResult.DailyContent?.CustomerNew?.Count ?? 0) == 1 },
                        CustomerFollowCount = new { Expected = 1, Actual = detailResult.DailyContent?.CustomerFollow?.Count ?? 0, Match = (detailResult.DailyContent?.CustomerFollow?.Count ?? 0) == 1 },
                        CustomerNewCompanyName = new { Expected = "测试科技有限公司", Actual = detailResult.DailyContent?.CustomerNew?.FirstOrDefault()?.CompanyName, Match = detailResult.DailyContent?.CustomerNew?.FirstOrDefault()?.CompanyName == "测试科技有限公司" }
                    },
                    AttachmentVerification = attachmentVerification,
                    Message = "创建数据验证完成"
                };

                testResults.Add(createVerification);

                // Step 5: 准备更新用的附件
                var updateAttachments = new List<IFormFile>();
                
                // 创建更新用附件1 - 更新后的文本文件
                byte[] updatedTextFileBytes = System.Text.Encoding.UTF8.GetBytes("这是更新后的测试文本文件内容，内容已修改");
                var updatedTextFile = new FormFile(
                    new MemoryStream(updatedTextFileBytes),
                    0,
                    updatedTextFileBytes.Length,
                    "updated_text_file",
                    "更新后的测试文本文件.txt"
                )
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "text/plain"
                };
                updateAttachments.Add(updatedTextFile);
                
                // 创建更新用附件2 - 新增的PDF文件
                byte[] pdfFileBytes = new byte[200]; // 模拟PDF数据
                for (int i = 0; i < pdfFileBytes.Length; i++)
                {
                    pdfFileBytes[i] = (byte)((i * 3) % 256);
                }
                var pdfFile = new FormFile(
                    new MemoryStream(pdfFileBytes),
                    0,
                    pdfFileBytes.Length,
                    "pdf_file",
                    "新增测试文档.pdf"
                )
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "application/pdf"
                };
                updateAttachments.Add(pdfFile);
                
                testResults.Add(new
                {
                    Step = "5. 准备更新用附件",
                    Success = true,
                    AttachmentCount = updateAttachments.Count,
                    Attachments = updateAttachments.Select(a => new { a.FileName, a.Length, a.ContentType }).ToList(),
                    Message = "更新用附件准备完成"
                });

                // Step 6: 修改报告数据
                var updateInput = new UpdateReportFixed_In
                {
                    Id = createdReportId,
                    ReportType = EnumReportType.Daily,
                    ReportDate = testDate,
                    Status = EnumReportStatus.Draft,
                    Title = "修改后的综合测试日报 - " + testDate.ToString("yyyy-MM-dd"),

                    // 修改后的日报内容
                    DailyContent = new VM_DailyReportInput
                    {
                        // 1.1.1 工作回顾 - 修改后的数据
                        PhoneCount = 25, // 修改：从15改为25
                        VisitCount = 12, // 修改：从8改为12
                        EmailPreciseCount = 8, // 修改：从5改为8
                        EmailBulkCount = 15, // 修改：从12改为15
                        SocialMediaCount = 9, // 修改：从6改为9
                        ReplyCount = 22, // 修改：从18改为22
                        DemoCount = 3, // 修改：从2改为3
                        Other = "参加了部门会议和客户培训，讨论了新产品推广策略", // 修改

                        // 1.1.2 客户新增 - 修改后的数据
                        CustomerNew = new List<VM_CustomerNewInput>
                        {
                            new VM_CustomerNewInput
                            {
                                CustomerLevel = "A",
                                Country = "美国", // 修改：从中国改为美国
                                CompanyName = "更新科技股份有限公司", // 修改
                                MainProducts = "8517120000",
                                CommunicationSummary = "客户对我们的产品非常感兴趣，已进入深度洽谈阶段", // 修改
                                CustomerId = "test-customer-001-updated" // 修改
                            }
                        },

                        // 1.1.3 客户跟进 - 修改后的数据
                        CustomerFollow = new List<VM_CustomerFollowInput>
                        {
                            new VM_CustomerFollowInput
                            {
                                CompanyName = "修改后的跟进科技公司", // 修改
                                MainProducts = "8517120000",
                                RecommendedSolution = "推荐高级版产品方案", // 修改
                                FollowUpSummary = "客户已确认需求，准备签约，安排本周最终演示", // 修改
                                CustomerId = "test-customer-002-updated" // 修改
                            }
                        },

                        // 1.1.6 其他规划与建议 - 修改后的数据
                        ProductPlanning = "重点推广新产品线和高端产品，关注客户反馈和市场趋势", // 修改
                        RegionPlanning = "下周重点开拓新市场区域，加强北美和欧洲市场", // 修改
                        SalesPlanning = "本月目标调整为合同签约15个，回款150万，超额完成任务", // 修改
                        Difficulties = "客户需求变化较快，需要灵活调整方案", // 修改
                        SupportNeeded = "需要市场部配合提供最新产品资料和技术支持", // 修改
                        Suggestions = "建议建立客户需求跟踪系统，提高响应速度和服务质量", // 修改

                        // 1.1.7 学习与思考 - 修改后的数据
                        LearningAndThinking = "今日完成客户拜访12次，电话沟通25次，新增潜在客户5个，学习了新的销售技巧和客户管理方法" // 修改
                    },
                    
                    // 更新附件集合
                    Files = new FormFileCollection()
                };
                
                // 添加更新用附件
                foreach (var attachment in updateAttachments)
                {
                    ((FormFileCollection)updateInput.Files).Add(attachment);
                }

                var updateResult = bll.UpdateReportFixed(updateInput);

                testResults.Add(new
                {
                    Step = "6. 修改报告数据",
                    Success = true,
                    Message = "报告修改成功"
                });

                // Step 7: 验证修改后的数据完整性
                var updatedDetailResult = bll.GetReportDetailSimple(createdReportId);

                // 验证更新后的附件
                var updatedAttachmentVerification = new
                {
                    ExpectedAttachmentCount = updateAttachments.Count,
                    ActualAttachmentCount = updatedDetailResult.Attachments?.Count ?? 0,
                    AttachmentsMatch = (updatedDetailResult.Attachments?.Count ?? 0) >= updateAttachments.Count,
                    AttachmentDetails = updatedDetailResult.Attachments?.Select(a => new
                    {
                        a.Id,
                        a.FileName,
                        a.FileSize,
                        a.FileType,
                        a.ModuleKey,
                        a.SectionKey
                    }).ToList()
                };

                var updateVerification = new
                {
                    Step = "7. 验证修改后数据完整性",
                    Success = true,
                    FieldVerification = new
                    {
                        // 工作回顾数据验证
                        PhoneCount = new { Expected = 25, Actual = updatedDetailResult.DailyContent?.PhoneCount, Match = updatedDetailResult.DailyContent?.PhoneCount == 25 },
                        VisitCount = new { Expected = 12, Actual = updatedDetailResult.DailyContent?.VisitCount, Match = updatedDetailResult.DailyContent?.VisitCount == 12 },
                        EmailPreciseCount = new { Expected = 8, Actual = updatedDetailResult.DailyContent?.EmailPreciseCount, Match = updatedDetailResult.DailyContent?.EmailPreciseCount == 8 },
                        EmailBulkCount = new { Expected = 15, Actual = updatedDetailResult.DailyContent?.EmailBulkCount, Match = updatedDetailResult.DailyContent?.EmailBulkCount == 15 },
                        SocialMediaCount = new { Expected = 9, Actual = updatedDetailResult.DailyContent?.SocialMediaCount, Match = updatedDetailResult.DailyContent?.SocialMediaCount == 9 },
                        ReplyCount = new { Expected = 22, Actual = updatedDetailResult.DailyContent?.ReplyCount, Match = updatedDetailResult.DailyContent?.ReplyCount == 22 },
                        DemoCount = new { Expected = 3, Actual = updatedDetailResult.DailyContent?.DemoCount, Match = updatedDetailResult.DailyContent?.DemoCount == 3 },
                        Other = new { Expected = "参加了部门会议和客户培训，讨论了新产品推广策略", Actual = updatedDetailResult.DailyContent?.Other, Match = updatedDetailResult.DailyContent?.Other == "参加了部门会议和客户培训，讨论了新产品推广策略" },

                        // 规划与建议字段验证
                        ProductPlanning = new { Expected = "重点推广新产品线和高端产品，关注客户反馈和市场趋势", Actual = updatedDetailResult.DailyContent?.ProductPlanning, Match = updatedDetailResult.DailyContent?.ProductPlanning == "重点推广新产品线和高端产品，关注客户反馈和市场趋势" },
                        RegionPlanning = new { Expected = "下周重点开拓新市场区域，加强北美和欧洲市场", Actual = updatedDetailResult.DailyContent?.RegionPlanning, Match = updatedDetailResult.DailyContent?.RegionPlanning == "下周重点开拓新市场区域，加强北美和欧洲市场" },
                        SalesPlanning = new { Expected = "本月目标调整为合同签约15个，回款150万，超额完成任务", Actual = updatedDetailResult.DailyContent?.SalesPlanning, Match = updatedDetailResult.DailyContent?.SalesPlanning == "本月目标调整为合同签约15个，回款150万，超额完成任务" },
                        Difficulties = new { Expected = "客户需求变化较快，需要灵活调整方案", Actual = updatedDetailResult.DailyContent?.Difficulties, Match = updatedDetailResult.DailyContent?.Difficulties == "客户需求变化较快，需要灵活调整方案" },
                        SupportNeeded = new { Expected = "需要市场部配合提供最新产品资料和技术支持", Actual = updatedDetailResult.DailyContent?.SupportNeeded, Match = updatedDetailResult.DailyContent?.SupportNeeded == "需要市场部配合提供最新产品资料和技术支持" },
                        Suggestions = new { Expected = "建议建立客户需求跟踪系统，提高响应速度和服务质量", Actual = updatedDetailResult.DailyContent?.Suggestions, Match = updatedDetailResult.DailyContent?.Suggestions == "建议建立客户需求跟踪系统，提高响应速度和服务质量" },
                        LearningAndThinking = new { Expected = "今日完成客户拜访12次，电话沟通25次，新增潜在客户5个，学习了新的销售技巧和客户管理方法", Actual = updatedDetailResult.DailyContent?.LearningAndThinking, Match = updatedDetailResult.DailyContent?.LearningAndThinking == "今日完成客户拜访12次，电话沟通25次，新增潜在客户5个，学习了新的销售技巧和客户管理方法" },

                        // 客户数据验证
                        CustomerNewCount = new { Expected = 1, Actual = updatedDetailResult.DailyContent?.CustomerNew?.Count ?? 0, Match = (updatedDetailResult.DailyContent?.CustomerNew?.Count ?? 0) == 1 },
                        CustomerFollowCount = new { Expected = 1, Actual = updatedDetailResult.DailyContent?.CustomerFollow?.Count ?? 0, Match = (updatedDetailResult.DailyContent?.CustomerFollow?.Count ?? 0) == 1 },
                        CustomerNewCompanyName = new { Expected = "更新科技股份有限公司", Actual = updatedDetailResult.DailyContent?.CustomerNew?.FirstOrDefault()?.CompanyName, Match = updatedDetailResult.DailyContent?.CustomerNew?.FirstOrDefault()?.CompanyName == "更新科技股份有限公司" },
                        CustomerNewCountry = new { Expected = "美国", Actual = updatedDetailResult.DailyContent?.CustomerNew?.FirstOrDefault()?.Country, Match =  updatedDetailResult.DailyContent?.CustomerNew?.FirstOrDefault()?.Country ==  1 }
                    },
                    AttachmentVerification = updatedAttachmentVerification,
                    Message = "修改数据验证完成"
                };

                testResults.Add(updateVerification);

                // 计算总体成功率
                var allFieldsFromCreate = createVerification.FieldVerification.GetType().GetProperties()
                    .Select(p => p.GetValue(createVerification.FieldVerification))
                    .Cast<dynamic>()
                    .Count(field => field.Match == true);

                var totalFieldsFromCreate = createVerification.FieldVerification.GetType().GetProperties().Length;

                var allFieldsFromUpdate = updateVerification.FieldVerification.GetType().GetProperties()
                    .Select(p => p.GetValue(updateVerification.FieldVerification))
                    .Cast<dynamic>()
                    .Count(field => field.Match == true);

                var totalFieldsFromUpdate = updateVerification.FieldVerification.GetType().GetProperties().Length;

                return new
                {
                    TestName = "综合工作流程测试",
                    OverallSuccess = true,
                    CreatedReportId = createdReportId,
                    Summary = new
                    {
                        CreateDataIntegrity = $"{allFieldsFromCreate}/{totalFieldsFromCreate} 字段验证成功",
                        UpdateDataIntegrity = $"{allFieldsFromUpdate}/{totalFieldsFromUpdate} 字段验证成功",
                        CreateSuccessRate = $"{(double)allFieldsFromCreate / totalFieldsFromCreate * 100:F1}%",
                        UpdateSuccessRate = $"{(double)allFieldsFromUpdate / totalFieldsFromUpdate * 100:F1}%",
                        AttachmentCreateSuccess = createVerification.AttachmentVerification.AttachmentsMatch,
                        AttachmentUpdateSuccess = updateVerification.AttachmentVerification.AttachmentsMatch,
                        InitialAttachmentCount = createVerification.AttachmentVerification.ActualAttachmentCount,
                        FinalAttachmentCount = updateVerification.AttachmentVerification.ActualAttachmentCount
                    },
                    DetailedResults = testResults,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            catch (Exception ex)
            {
                testResults.Add(new
                {
                    Step = "异常处理",
                    Success = false,
                    Error = ex.Message,
                    StackTrace = ex.StackTrace,
                    Message = "测试过程中发生异常"
                });

                return new
                {
                    TestName = "综合工作流程测试",
                    OverallSuccess = false,
                    CreatedReportId = createdReportId,
                    Summary = new
                    {
                        Error = ex.Message,
                        FailedAt = testResults.LastOrDefault()
                    },
                    DetailedResults = testResults,
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
        }

        #endregion

        #region 第六模块：跟踪记录查询接口

        /// <summary>
        /// 获取跟踪记录列表
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>跟踪记录列表</returns>
        [HttpPost]
        public ApiTableOut<VM_TrackingRecord> GetTrackingRecords(GetTrackingRecords_In input)
        {
            if (input == null)
            {
                throw new ApiException("查询条件不能为空");
            }

            if (input.QueryType < 1 || input.QueryType > 3)
            {
                throw new ApiException("查询类型必须在1-3之间");
            }
            var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
            return bll.GetTrackingRecords(input);
        }





        #endregion

        /// <summary>
        /// 测试模型绑定器
        /// </summary>
        [HttpPost]
        public object TestModelBinder([FromForm] CreateReportFixed_In input)
        {
            Console.WriteLine("ReportController.TestModelBinder: 方法被调用");
            
            if (input == null)
            {
                Console.WriteLine("ReportController.TestModelBinder: 输入为null");
                return new { Success = false, Message = "输入为null" };
            }
            
            Console.WriteLine($"ReportController.TestModelBinder: 输入类型 = {input.GetType().FullName}");
            Console.WriteLine($"ReportController.TestModelBinder: ReportType = {input.ReportType}");
            Console.WriteLine($"ReportController.TestModelBinder: Title = {input.Title}");
            
            // 检查JSON字符串字段和List对象
            if (input.DailyContent != null)
            {
                // 同步JSON和List数据
                input.DailyContent.SyncJsonAndListData();
                
                Console.WriteLine($"ReportController.TestModelBinder: CustomerNewJson = {input.DailyContent.CustomerNewJson}");
                Console.WriteLine($"ReportController.TestModelBinder: CustomerNew Count = {input.DailyContent.CustomerNew?.Count ?? 0}");
                
                if (input.DailyContent.CustomerNew?.Count > 0)
                {
                    Console.WriteLine($"ReportController.TestModelBinder: 第一个客户名称 = {input.DailyContent.CustomerNew[0].CompanyName}");
                }
                
                Console.WriteLine($"ReportController.TestModelBinder: CustomerFollowJson = {input.DailyContent.CustomerFollowJson}");
                Console.WriteLine($"ReportController.TestModelBinder: CustomerFollow Count = {input.DailyContent.CustomerFollow?.Count ?? 0}");
                
                Console.WriteLine($"ReportController.TestModelBinder: CustomerContractJson = {input.DailyContent.CustomerContractJson}");
                Console.WriteLine($"ReportController.TestModelBinder: CustomerContract Count = {input.DailyContent.CustomerContract?.Count ?? 0}");
                
                Console.WriteLine($"ReportController.TestModelBinder: CustomerServiceJson = {input.DailyContent.CustomerServiceJson}");
                Console.WriteLine($"ReportController.TestModelBinder: CustomerService Count = {input.DailyContent.CustomerService?.Count ?? 0}");
            }
            
            return new { 
                Success = true, 
                Message = "测试成功", 
                Input = new {
                    ReportType = input.ReportType,
                    Title = input.Title,
                    ReportDate = input.ReportDate,
                    DailyContent = input.DailyContent != null ? new {
                        PhoneCount = input.DailyContent.PhoneCount,
                        VisitCount = input.DailyContent.VisitCount,
                        CustomerNewCount = input.DailyContent.CustomerNew?.Count ?? 0,
                        CustomerFollowCount = input.DailyContent.CustomerFollow?.Count ?? 0,
                        CustomerContractCount = input.DailyContent.CustomerContract?.Count ?? 0,
                        CustomerServiceCount = input.DailyContent.CustomerService?.Count ?? 0
                    } : null
                }
            };
        }
    }
}
