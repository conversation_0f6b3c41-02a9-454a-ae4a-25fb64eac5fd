-- 服务变更字段配置 - 增加SalesWits账户数 (ChangeReasonEnum = 6)
-- 执行日期: 2025-01-29

-- ================================
-- 增加SalesWits账户数 - 申请场景字段
-- ================================

-- SalesWits账户数相关字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 2, 6, 'SalesWitsAccountNum', 'SalesWits账号数量', 10, 1, 1, 'apply', NULL, '增加SalesWits账户数时申请可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 6, 'SalesWitsUsers', 'SalesWits使用者', 11, 1, 1, 'apply', NULL, '增加SalesWits账户数时申请可修改', NOW(), 'system', NULL, 'system', 0);

-- 慧思服务通用字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 6, 'AccountList', '账号列表', 20, 1, 1, 'apply', NULL, '增加SalesWits账户数时申请可修改', NOW(), 'system', NULL, 'system', 0);

-- ================================
-- 增加SalesWits账户数 - 审核场景字段
-- ================================

-- SalesWits账户数相关字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 2, 6, 'SalesWitsAccountNum', 'SalesWits账号数量', 30, 1, 1, 'audit', 'AccountList,SalesWitsAccountNum,SalesWitsGiftResourceMonths,SalesWitsGiftTokenNum,SalesWitsGiftEmailNum,SalesWitsUsers', '增加SalesWits账户数时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 6, 'SalesWitsGiftResourceMonths', 'SalesWits赠送资源月份数', 31, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsGiftResourceMonths', '增加SalesWits账户数时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 6, 'SalesWitsGiftTokenNum', 'SalesWits赠送Token数量', 32, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsGiftTokenNum', '增加SalesWits账户数时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 6, 'SalesWitsGiftEmailNum', 'SalesWits赠送邮件数量', 33, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsGiftEmailNum', '增加SalesWits账户数时审核可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 2, 6, 'SalesWitsUsers', 'SalesWits使用者', 34, 1, 1, 'audit', 'SalesWitsAccountNum,SalesWitsUsers', '增加SalesWits账户数时审核可修改', NOW(), 'system', NULL, 'system', 0);

-- 慧思服务通用字段
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 6, 'AccountList', '账号列表', 40, 1, 1, 'audit', 'AccountList,SalesWitsAccountNum', '增加SalesWits账户数时审核可修改', NOW(), 'system', NULL, 'system', 0);

SELECT '增加SalesWits账户数配置完成！' AS message; 