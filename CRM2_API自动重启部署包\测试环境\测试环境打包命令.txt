# 测试环境部署快速命令参考

## 在Windows本地准备文件

```powershell
# 创建部署目录
mkdir test-deploy
cd test-deploy

# 复制测试环境相关文件
copy ..\CRM2_API自动重启部署包\crm-api-test.service .
copy ..\CRM2_API自动重启部署包\crm-api-test-monitor.service .
copy ..\CRM2_API自动重启部署包\install-crm-test.sh .
copy ..\CRM2_API自动重启部署包\monitor-crm-test.sh .
copy ..\CRM2_API自动重启部署包\测试环境部署说明.md .

# 压缩文件
Compress-Archive -Path * -DestinationPath crm-api-test-deploy.zip
```

## 在Linux测试服务器上部署

假设已将文件上传到/tmp目录：

```bash
# 创建部署目录
mkdir -p /tmp/test-deploy
cd /tmp/test-deploy

# 解压文件
unzip /tmp/crm-api-test-deploy.zip

# 确保脚本使用Unix行尾格式
dos2unix *.sh

# 设置执行权限
chmod +x install-crm-test.sh
chmod +x monitor-crm-test.sh

# 部署主服务
sudo ./install-crm-test.sh

# 部署监控服务
sudo cp monitor-crm-test.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/monitor-crm-test.sh
sudo cp crm-api-test-monitor.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable crm-api-test-monitor.service
sudo systemctl start crm-api-test-monitor.service

# 检查服务状态
sudo systemctl status crm-api-test.service
sudo systemctl status crm-api-test-monitor.service
```

## 常用维护命令

```bash
# 查看应用日志
cat /home/<USER>/log
tail -f /home/<USER>/log

# 查看监控日志
cat /home/<USER>/monitor.log
tail -f /home/<USER>/monitor.log

# 更新应用程序
# (将新文件放到/home/<USER>/publish目录后)
sudo ./install-crm-test.sh

# 手动重启服务
sudo systemctl restart crm-api-test.service
``` 