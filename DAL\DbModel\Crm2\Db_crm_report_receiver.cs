using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 报告接收人表
    /// </summary>
    [SugarTable("crm_report_receiver")]
    public class Db_crm_report_receiver
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 报告ID
        /// </summary>
        public string ReportId { get; set; }

        /// <summary>
        /// 接收人ID
        /// </summary>
        public string ReceiverId { get; set; }

        /// <summary>
        /// 接收人姓名
        /// </summary>
        public string ReceiverName { get; set; }

        /// <summary>
        /// 是否默认接收人：0-否，1-是
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 状态：1-未读，2-已读
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 阅读时间
        /// </summary>
        public DateTime? ReadTime { get; set; }

        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateDate { get; set; }
    }
} 