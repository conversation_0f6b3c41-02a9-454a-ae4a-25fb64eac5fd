using System;
using System.ComponentModel;
using System.Reflection;

namespace CRM2_API.Common
{
    /// <summary>
    /// 扩展方法类
    /// </summary>
    public static class ExtensionMethod
    {
        /// <summary>
        /// 获取枚举的描述信息
        /// </summary>
        /// <param name="enumValue">枚举值</param>
        /// <returns>描述信息</returns>
        public static string GetEnumDescription(this Enum enumValue)
        {
            if (enumValue == null)
            {
                return string.Empty;
            }

            string description = enumValue.ToString();
            FieldInfo field = enumValue.GetType().GetField(description);
            
            if (field != null)
            {
                DescriptionAttribute[] attributes = (DescriptionAttribute[])field.GetCustomAttributes(typeof(DescriptionAttribute), false);
                
                if (attributes != null && attributes.Length > 0)
                {
                    description = attributes[0].Description;
                }
            }
            
            return description;
        }
    }
} 