using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CRM2_API.BLL.Common;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbCommon;
using SqlSugar;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 发票申请相关功能
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        #region 发票申请相关方法

        /// <summary>
        /// 创建发票申请
        /// </summary>
        /// <param name="request">申请请求</param>
        /// <param name="testMode">测试情况不验证发票抬头</param>
        /// <returns>申请ID</returns>
        public string CreateInvoiceApplication(CreateInvoiceApplicationRequest request, bool testMode = false)
        {
            if (request == null)
            {
                throw new ApiException("请求参数不能为空");
            }

            // 从TokenModel获取用户ID
            string userId = TokenModel.Instance.id;

            // 1. 验证合同权限和状态
            var contract = DbOpe_crm_contract.Instance.GetContractById(request.ContractId, true);
            if (contract == null)
            {
                throw new ApiException("未找到合同或该用户没有合同权限");
            }

            if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
            {
                throw new ApiException("当前合同状态下，不可以申请发票");
            }
            //验证客户权限
            var customerAuth = DbContext.Crm2Db.Queryable<Db_v_customer_subcompany_private_user>()
                .Where(m => m.CompanyCurrentUser == userId && m.Id == contract.FirstParty)
                .Any();
            if (!customerAuth)
            {
                throw new ApiException("没有客户权限，不可以申请发票");
            }

            // 2. 计算合同可开票金额
            decimal availableInvoiceAmount = CalculateAvailableInvoiceAmount(request.ContractId);

            // 3. 验证申请金额是否超过可开票金额
            decimal requestedAmount = request.InvoiceInfo.InvoiceAmount ?? 0;
            if (requestedAmount > availableInvoiceAmount)
            {
                throw new ApiException($"开票金额 {requestedAmount} 大于可开票金额 {availableInvoiceAmount}，不可以申请发票");
            }

            if (availableInvoiceAmount <= 0)
            {
                throw new ApiException("合同已无可开票金额，不可以申请发票");
            }

            // 到账开票类型特殊处理：验证到账记录ID
            if (request.BillingType == EnumBillingType.InvoicingUponReceipt)
            {
                if (string.IsNullOrEmpty(request.ReceiptId))
                {
                    throw new ApiException("到账开票类型必须指定到账记录ID");
                }

                // 检查是否已有匹配记录
                bool hasMatchingRecord = DbOpe_crm_invoice_receipt_matching.Instance.GetDataList(m =>
                    m.ReceiptId == request.ReceiptId && m.Deleted != true).Any();

                if (hasMatchingRecord)
                {
                    throw new ApiException("该到账记录已有匹配关系，不能重复申请");
                }

                // 获取到账记录
                var receipt = DbOpe_crm_contract_receiptregister.Instance.GetData(r =>
                    r.Id == request.ReceiptId &&
                    r.AchievementState == (int)EnumAchievementState.Confirmed &&
                    r.Deleted != true);

                if (receipt == null)
                {
                    throw new ApiException("到账记录不存在或未确认");
                }

                // 获取到账详细信息（包括付款公司和到账金额等）
                var receiptDetails = DbContext.Crm2Db
                    .Queryable<Db_crm_contract_receiptregister, Db_crm_contract_receipt_details, Db_crm_collectioninfo>(
                        (r, d, c) => new JoinQueryInfos(
                            JoinType.Left, r.Id == d.ContractReceiptRegisterId,
                            JoinType.Left, d.CollectionInfoId == c.Id
                        ))
                    .Where((r, d, c) => r.Id == request.ReceiptId && r.Deleted != true && d.Deleted != true && c.Deleted != true)
                    .Select((r, d, c) => new
                    {
                        PaymentCompany = c.PaymentCompany,
                        PaymentCompanyName = c.PaymentCompanyName,
                        ArrivalAmount = c.ArrivalAmount
                    })
                    .First();

                if (receiptDetails == null)
                {
                    throw new ApiException("无法获取到账详细信息");
                }

                // 验证开票金额是否等于到账金额
                decimal requestAmount = request.InvoiceInfo.InvoiceAmount ?? 0;
                decimal receiptAmount = receiptDetails.ArrivalAmount.GetValueOrDefault(0);

                if (requestAmount != receiptAmount)
                {
                    throw new ApiException($"开票金额 {requestAmount} 必须等于到账金额 {receiptAmount}");
                }

                // 对于形式发票，直接使用到账付款方公司作为抬头，不需要验证
                if (request.InvoiceInfo.InvoiceType == EnumInvoiceType.ProformaTicket)
                {
                    request.ReceiveCompanyInfo.CompanyName = receiptDetails.PaymentCompanyName;
                    LogUtil.AddLog($"到账开票使用付款方作为形式发票抬头: {receiptDetails.PaymentCompanyName}");
                }
                else if (!testMode)
                {
                    // 普票和专票需要验证抬头
                    // 优先使用到账付款方公司作为抬头
                    string headerName = receiptDetails.PaymentCompanyName;
                    bool headerValid = false;

                    // 验证付款方公司抬头
                    int tempTotal = 0;
                    var headerInfos = new List<Db_crm_tianyancha_info>();
                    try
                    {
                        headerInfos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(headerName, false, ref tempTotal);
                    }
                    catch (Exception ex)
                    {
                    }

                    if (headerInfos.Count > 0)
                    {
                        // 付款方公司验证成功
                        headerValid = true;
                        request.ReceiveCompanyInfo.CompanyName = headerName;
                        LogUtil.AddLog($"到账开票使用付款方公司抬头: {headerName}");
                    }
                    else
                    {
                        // 查询是否是代付款
                        var paymentInfo = DbContext.Crm2Db
                            .Queryable<Db_crm_contract_receiptregister>()
                            .LeftJoin<Db_crm_contract_paymentinfo>((r, p) => r.ContractPaymentInfoId == p.Id)
                            .Where((r, p) => r.Id == request.ReceiptId &&
                                  r.Deleted != true && p.Deleted != true)
                            .Select((r, p) => new
                            {
                                IsBehalfPayment = p.IsBehalfPayment,
                                PaymentCompany = p.PaymentCompany
                            })
                            .First();

                        if (paymentInfo != null && paymentInfo.IsBehalfPayment.HasValue && paymentInfo.IsBehalfPayment.Value && !string.IsNullOrEmpty(paymentInfo.PaymentCompany))
                        {
                            // 获取代付款公司名称
                            var behalfCompany = DbOpe_crm_customer_subcompany.Instance.GetData(c =>
                                c.Id == paymentInfo.PaymentCompany && c.Deleted == 0);

                            if (behalfCompany != null && !string.IsNullOrEmpty(behalfCompany.CompanyName))
                            {
                                // 验证代付款公司抬头
                                headerName = behalfCompany.CompanyName;
                                try
                                {
                                    headerInfos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(headerName, false, ref tempTotal);
                                }
                                catch (Exception ex)
                                {
                                }

                                if (headerInfos.Count > 0)
                                {
                                    // 代付款公司验证成功
                                    headerValid = true;
                                    request.ReceiveCompanyInfo.CompanyName = headerName;
                                    LogUtil.AddLog($"到账开票使用代付款公司抬头: {headerName}");
                                }
                            }
                        }
                    }

                    // 如果付款方和代付款方都验证失败，尝试使用合同甲方
                    if (!headerValid)
                    {
                        // 获取合同甲方信息
                        if (!string.IsNullOrEmpty(contract.FirstParty))
                        {
                            var firstPartyCompany = DbOpe_crm_customer_subcompany.Instance.GetData(c =>
                                c.Id == contract.FirstParty && c.Deleted == 0);

                            if (firstPartyCompany != null && !string.IsNullOrEmpty(firstPartyCompany.CompanyName))
                            {
                                headerName = firstPartyCompany.CompanyName;
                                try
                                {
                                    headerInfos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(headerName, false, ref tempTotal);
                                }
                                catch (Exception ex)
                                {
                                }

                                if (headerInfos.Count > 0)
                                {
                                    // 合同甲方验证成功
                                    headerValid = true;
                                    request.ReceiveCompanyInfo.CompanyName = headerName;
                                    LogUtil.AddLog($"到账开票使用合同甲方抬头: {headerName}");
                                }
                            }
                        }
                    }

                    // 如果所有抬头验证都失败，则只能开形式发票
                    if (!headerValid)
                    {
                        LogUtil.AddErrorLog($"到账开票抬头验证全部失败，只能选择形式发票: {headerName}");
                        throw new ApiException("付款方、代付款方和合同甲方公司验证均失败，只能选择开具形式发票");
                    }
                }
            }



            // 添加发票抬头公司验证 - 参考旧代码中的验证逻辑
            string creditCode = string.Empty;
            string billingHeaderName = request.ReceiveCompanyInfo.CompanyName;

            int total = 0;
            var infos = new List<Db_crm_tianyancha_info>();

            // 根据发票类型决定验证方式
            if (request.InvoiceInfo.InvoiceType == EnumInvoiceType.ProformaTicket)
            {
                // 如果是形式发票，取消全英文的验证
                infos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(billingHeaderName, false, ref total);
                if (infos.Count == 0)
                {
                    // 形式发票且未找到公司信息时，生成临时信用代码
                    creditCode = DateTime.Now.ToString("yyyyMMddHHmmssff") + new Random().Next(10) + "X";
                    request.ReceiveCompanyInfo.CreditCode = creditCode;
                    LogUtil.AddLog($"形式发票生成临时信用代码: {billingHeaderName}, 临时代码: {creditCode}");
                }
            }
            else if (!testMode)
            {
                // 普票或专票，要限制开票抬头不可以是全英文
                infos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(billingHeaderName, false, ref total);
            }

            // creditCode要验证的
            if (!testMode)
            {
                var exactCompany = infos.Find(i => i.CompanyName == billingHeaderName);
                var historyCompany = infos.Find(i => i.HistoryNames != null && i.HistoryNames.Split("|").Contains(billingHeaderName));

                if (exactCompany != null)
                {
                    // 名称输入与天眼查一致
                    creditCode = exactCompany.CreditCode;
                    request.ReceiveCompanyInfo.CreditCode = creditCode;
                    LogUtil.AddLog($"发票抬头公司验证成功: {billingHeaderName}, 信用代码: {creditCode}");
                }
                else if (historyCompany != null)
                {
                    // 输入的是曾用名，特殊处理编码
                    var index = historyCompany.HistoryNames.Split("|").ToList().IndexOf(billingHeaderName);
                    creditCode = "$$" + index + historyCompany.CreditCode;
                    request.ReceiveCompanyInfo.CreditCode = creditCode;
                    LogUtil.AddLog($"发票抬头公司验证成功(曾用名): {billingHeaderName}, 当前名称: {historyCompany.CompanyName}, 信用代码: {creditCode}");
                }
                else
                {
                    // 如果不是形式发票，且未找到公司信息，则抛出异常
                    if (request.InvoiceInfo.InvoiceType != EnumInvoiceType.ProformaTicket)
                    {
                        LogUtil.AddErrorLog($"发票抬头公司验证失败: {billingHeaderName}, 未找到相关公司信息");
                        throw new ApiException("未找到发票抬头公司信息，请检查输入的公司名称是否正确且完整");
                    }
                }
            }

            if (testMode)
            {
                creditCode = DateTime.Now.ToString("yyyyMMddHHmmssff") + new Random().Next(10) + "X";
                request.ReceiveCompanyInfo.CreditCode = creditCode;
            }

            string invoiceApplicationId = string.Empty;
            try
            {
                // 使用事务创建发票申请记录
                DbOpe_crm_invoice_application.Instance.TransDeal(() =>
                {
                    // 创建发票申请记录 - 使用新的Db_crm_invoice_application模型
                    var invoiceApplication = new Db_crm_invoice_application
                    {
                        Id = Guid.NewGuid().ToString(),
                        ContractId = request.ContractId,
                        BillingType = (int)request.BillingType,
                        // 对于到账开票类型，设置到账记录ID
                        ReceiptId = request.BillingType == EnumBillingType.InvoicingUponReceipt ? request.ReceiptId : null,
                        InvoiceType = (int)request.InvoiceInfo.InvoiceType,
                        BillingCompany = request.BillingCompany,
                        BillingHeader = request.ReceiveCompanyInfo.CompanyName,
                        CreditCode = request.ReceiveCompanyInfo.CreditCode,
                        AppliedAmount = request.InvoiceInfo.InvoiceAmount ?? 0,
                        ExpectedInvoicingDate = request.ExpectedInvoiceDate,
                        InvoicingDetails = request.InvoiceInfo.InvoicingDetails,
                        Recipient = request.ReceiveCompanyInfo.Recipient,
                        Email = request.ReceiveCompanyInfo.Email,
                        AuditStatus = (int)EnumInvoiceApplicationStatus.Applied, // 新系统中，创建即为"申请中"状态
                        ApplicantId = userId,
                        ApplyTime = DateTime.Now,
                        Remark = request.ApplicationReason,
                        IsReminded = (int)EnumIsReminder.UnUrgedTicket,
                        Deleted = false,
                        CreateUser = userId,
                        CreateDate = DateTime.Now,
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now
                    };

                    // 插入发票申请记录
                    try
                    {
                        DbOpe_crm_invoice_application.Instance.Insert(invoiceApplication);
                        invoiceApplicationId = invoiceApplication.Id;
                        LogUtil.AddLog($"保存发票申请记录: {invoiceApplicationId}");
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddErrorLog($"创建发票申请异常: {ex.Message}");
                        throw new ApiException("创建发票申请失败: " + ex.Message);
                    }

                    // 如果是到账开票类型，则创建一条初始匹配记录，防止重复申请
                    if (request.BillingType == EnumBillingType.InvoicingUponReceipt && !string.IsNullOrEmpty(request.ReceiptId))
                    {
                        try
                        {
                            // 创建初始匹配记录
                            var initialMatching = new Db_crm_invoice_receipt_matching
                            {
                                Id = Guid.NewGuid().ToString(),
                                ReceiptId = request.ReceiptId,
                                // 此时还没有正式发票ID，先设为临时值，后面再更新
                                InvoiceId = "",
                                // 直接设置申请ID
                                InvoiceApplicationId = invoiceApplicationId,
                                // 使用"待复核"状态
                                MatchingStatus = (int)EnumInvoiceMatchingStatus.WaitingAudit, // 匹配待复核状态
                                MatchingTime = DateTime.Now,
                                MatchingUser = userId,
                                Remark = "",
                                Deleted = false,
                                CreateUser = userId,
                                CreateDate = DateTime.Now,
                                UpdateUser = userId,
                                UpdateDate = DateTime.Now
                            };

                            // 保存初始匹配记录
                            DbOpe_crm_invoice_receipt_matching.Instance.Insert(initialMatching);
                            LogUtil.AddLog($"创建到账开票初始匹配记录成功，ID: {initialMatching.Id}，到账ID: {request.ReceiptId}，申请ID: {invoiceApplicationId}");
                        }
                        catch (Exception ex)
                        {
                            LogUtil.AddErrorLog($"创建到账开票初始匹配记录异常: {ex.Message}");
                            // 不抛出异常，让申请正常创建，后续可以手动处理匹配
                        }
                    }
                });

                // 处理附件 (事务外处理，避免长事务)
                if (request.InvoiceAttachments != null && request.InvoiceAttachments.Count > 0)
                {
                    // 直接使用系统中的附件上传工具类处理多个附件
                    Util<DbOpe_crm_contract_invoiceappl_attachment, BM_AttachFile> invoiceApplAttachFile = new Util<DbOpe_crm_contract_invoiceappl_attachment, BM_AttachFile>(DbOpe_crm_contract_invoiceappl_attachment.Instance);
                    invoiceApplAttachFile.UploadFile(request.InvoiceAttachments, EnumAttachFileType.ContractInvoiceAppl.ToString(), userId, invoiceApplicationId);
                }

                // 如果是形式发票类型，直接创建形式发票，并且需要把到账匹配状态设置为已匹配
                if (request.InvoiceInfo.InvoiceType == EnumInvoiceType.ProformaTicket)
                {
                    try
                    {
                        LogUtil.AddLog($"开始创建形式发票，申请ID: {invoiceApplicationId}");

                        // 准备形式发票所需数据
                        var invoiceData = new Dictionary<string, object>
                        {
                            { "ContractId", request.ContractId },
                            { "Amount", request.InvoiceInfo.InvoiceAmount ?? 0 },
                            { "BillingHeader", request.ReceiveCompanyInfo.CompanyName },
                            { "BillingCompany", request.BillingCompany },
                            { "TaxpayerCode", request.ReceiveCompanyInfo.CreditCode },
                            { "ProjectName", contract.ContractName },
                            { "Recipient", request.ReceiveCompanyInfo.Recipient },
                            { "Email", request.ReceiveCompanyInfo.Email },
                            { "InvoiceDetails",request.InvoiceInfo.InvoicingDetails},
                            { "Remark", request.ApplicationReason }
                        };

                        // 直接创建或获取形式发票
                        var proformaInvoice = DbOpe_crm_proforma_invoice.Instance.GetOrCreateProformaInvoice(
                            invoiceApplicationId, invoiceData, userId);

                        if (proformaInvoice != null)
                        {
                            LogUtil.AddLog($"成功创建形式发票，ID: {proformaInvoice.Id}，申请ID: {invoiceApplicationId}");

                            // 自动审核通过形式发票申请
                            var application = DbOpe_crm_invoice_application.Instance.QueryByPrimaryKey(invoiceApplicationId);
                            if (application != null)
                            {
                                application.AuditStatus = (int)EnumInvoiceApplicationStatus.Completed;
                                application.UpdateUser = userId;
                                application.UpdateDate = DateTime.Now;
                                DbOpe_crm_invoice_application.Instance.Update(application);
                                LogUtil.AddLog($"形式发票申请自动审核通过，申请ID: {invoiceApplicationId}");
                            }
                            if (request.BillingType == EnumBillingType.InvoicingUponReceipt && !string.IsNullOrEmpty(request.ReceiptId))
                            {
                                // 更新到账匹配状态为已匹配
                                var matching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => m.InvoiceApplicationId == invoiceApplicationId && m.Deleted != true);
                                if (matching != null)
                                {
                                    matching.MatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccess;
                                    matching.MatchingTime = DateTime.Now;
                                    matching.MatchingUser = userId;
                                    matching.UpdateUser = userId;
                                    matching.UpdateDate = DateTime.Now;
                                    DbOpe_crm_invoice_receipt_matching.Instance.Update(matching);
                                }
                            }

                        }
                        else
                        {
                            LogUtil.AddErrorLog($"创建形式发票失败，申请ID: {invoiceApplicationId}");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddErrorLog($"创建形式发票过程中发生错误: {ex.Message}, {ex.StackTrace}");
                        // 不抛出异常，让申请正常创建，后续可以手动处理形式发票
                    }
                }

                // 更新发票显示状态
                UpdateInvoiceDisplayStatus(invoiceApplicationId);

                return invoiceApplicationId;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"创建发票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"创建发票申请系统异常: {ex.Message}");
                throw new ApiException("创建发票申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 计算合同可开票金额
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="invoiceApplicationId">发票申请ID(可选,更新时使用)</param>
        /// <returns>可开票金额</returns>
        public decimal CalculateAvailableInvoiceAmount(string contractId, string invoiceApplicationId = null)
        {
            // 获取合同信息
            var contract = DbOpe_crm_contract.Instance.GetContractById(contractId, true);
            if (contract == null)
            {
                throw new ApiException("未找到合同或您没有权限");
            }

            // 根据合同币种获取合同金额
            decimal contractAmount = 0;
            if (contract.Currency == EnumCurrency.CNY.ToInt())
            {
                contractAmount = contract.ContractAmount.ToDecimal();
            }
            else
            {
                contractAmount = contract.FCContractAmount.ToDecimal();
            }

            // 已开票金额 = 已开票金额 - 已退票金额
            decimal invoicedAmount = DbOpe_crm_invoice.Instance.GetDataList(i => i.ContractId == contractId && i.Deleted != true
            && i.TransactionType == 1)
            .Sum(i => i.InvoicedAmount);

            decimal proformaInvoiceAmount = DbOpe_crm_proforma_invoice.Instance.GetDataList(i => i.ContractId == contractId && i.Deleted != true)
            .Sum(i => i.Amount);

            decimal refundAmount = DbOpe_crm_invoice.Instance.GetDataList(i => i.ContractId == contractId && i.Deleted != true
            && i.TransactionType == 2)
            .Sum(i => i.InvoicedAmount);
            decimal actualInvoicedAmount = invoicedAmount + proformaInvoiceAmount - refundAmount;

            // 获取已申请未开票金额
            decimal pendingInvoiceAmount = 0;
            if (string.IsNullOrEmpty(invoiceApplicationId))
            {
                // 创建新申请时，计算所有未完成状态的申请金额
                // 需要计算的状态包括：申请中(Applied)、待复核(Processed)
                var applications = DbOpe_crm_invoice_application.Instance.GetDataList(a =>
                    a.ContractId == contractId &&
                    (a.AuditStatus == (int)EnumInvoiceApplicationStatus.Applied ||
                     a.AuditStatus == (int)EnumInvoiceApplicationStatus.Processed) &&
                    a.Deleted != true);

                pendingInvoiceAmount = applications.Sum(a => a.AppliedAmount);
            }
            else
            {
                // 更新申请时，排除当前申请的金额，但包括其他未完成状态的申请金额
                var applications = DbOpe_crm_invoice_application.Instance.GetDataList(a =>
                    a.ContractId == contractId &&
                    a.Id != invoiceApplicationId &&
                    (a.AuditStatus == (int)EnumInvoiceApplicationStatus.Applied ||
                     a.AuditStatus == (int)EnumInvoiceApplicationStatus.Processed) &&
                    a.Deleted != true);

                pendingInvoiceAmount = applications.Sum(a => a.AppliedAmount);
            }

            // 计算可开票金额
            decimal availableAmount = contractAmount - actualInvoicedAmount - pendingInvoiceAmount;

            // 可开票金额不能为负数
            return Math.Max(0, availableAmount);
        }

        /// <summary>
        /// 更新发票申请
        /// </summary>
        /// <param name="request">更新请求</param>
        /// <returns>是否更新成功</returns>
        public bool UpdateInvoiceApplication(UpdateInvoiceApplicationRequest request)
        {
            if (request == null)
            {
                throw new ApiException("请求参数不能为空");
            }

            // 从TokenModel获取用户ID
            string userId = TokenModel.Instance.id;

            // 1. 验证发票申请是否存在
            var existingApplication = DbOpe_crm_invoice_application.Instance.QueryByPrimaryKey(request.Id);
            if (existingApplication == null)
            {
                throw new ApiException("未找到发票申请记录");
            }

            // 2. 验证发票申请状态是否可以编辑
            // 只有已拒绝状态的发票申请才能编辑
            //形式发票开票了也可以编辑
            if (existingApplication.AuditStatus != (int)EnumInvoiceApplicationStatus.Rejected
            && existingApplication.InvoiceType != EnumInvoiceType.ProformaTicket.ToInt())
            {
                throw new ApiException("当前发票申请状态不可编辑");
            }

            // 3. 验证合同权限和状态
            var contract = DbOpe_crm_contract.Instance.GetContractById(request.ContractId, true);
            if (contract == null)
            {
                throw new ApiException("未找到合同或该用户没有合同权限");
            }

            if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
            {
                throw new ApiException("当前合同状态下，不可以申请发票");
            }
            //验证客户权限
            var customerAuth = DbContext.Crm2Db.Queryable<Db_v_customer_subcompany_private_user>()
                .Where(m => m.CompanyCurrentUser == userId && m.Id == contract.FirstParty)
                .Any();
            if (!customerAuth)
            {
                throw new ApiException("没有客户权限，不可以申请发票");
            }
            // 4. 计算合同可开票金额 (排除当前申请的金额)
            decimal availableInvoiceAmount = CalculateAvailableInvoiceAmount(request.ContractId, request.Id);

            // 5. 验证申请金额是否超过合同可开票金额
            decimal newAppliedAmount = request.InvoiceInfo.InvoiceAmount ?? 0;
            if (newAppliedAmount > availableInvoiceAmount)
            {
                throw new ApiException($"开票金额 {newAppliedAmount} 大于可开票金额 {availableInvoiceAmount}，不可以申请发票");
            }

            // 判断是否为到账开票
            if (request.BillingType == EnumBillingType.InvoicingUponReceipt)
            {
                // 到账开票类型特殊处理
                if (string.IsNullOrEmpty(request.ReceiptId))
                {
                    throw new ApiException("到账开票类型必须指定到账记录ID");
                }

                // 获取到账记录
                var receipt = DbOpe_crm_contract_receiptregister.Instance.GetData(r =>
                    r.Id == request.ReceiptId &&
                    r.ContractId == request.ContractId &&
                    r.AchievementState == (int)EnumAchievementState.Confirmed &&
                    r.Deleted != true);

                if (receipt == null)
                {
                    throw new ApiException("到账记录不存在或未确认");
                }

                // 获取到账详细信息
                var receiptDetails = DbContext.Crm2Db
                    .Queryable<Db_crm_contract_receiptregister, Db_crm_contract_receipt_details, Db_crm_collectioninfo>(
                        (r, d, c) => new JoinQueryInfos(
                            JoinType.Left, r.Id == d.ContractReceiptRegisterId,
                            JoinType.Left, d.CollectionInfoId == c.Id
                        ))
                    .Where((r, d, c) => r.Id == request.ReceiptId && r.Deleted != true)
                    .Select((r, d, c) => new
                    {
                        PaymentCompany = c.PaymentCompany,
                        PaymentCompanyName = c.PaymentCompanyName,
                        ArrivalAmount = c.ArrivalAmount
                    })
                    .First();

                if (receiptDetails == null)
                {
                    throw new ApiException("无法获取到账详细信息");
                }

                // 验证开票金额是否等于到账金额
                decimal requestAmount = request.InvoiceInfo.InvoiceAmount ?? 0;
                decimal receiptAmount = receiptDetails.ArrivalAmount.GetValueOrDefault(0);

                if (requestAmount != receiptAmount)
                {
                    throw new ApiException($"开票金额 {requestAmount} 必须等于到账金额 {receiptAmount}");
                }

                // 对于形式发票，直接使用到账付款方公司作为抬头，不需要验证
                if (request.InvoiceInfo.InvoiceType == EnumInvoiceType.ProformaTicket)
                {
                    request.ReceiveCompanyInfo.CompanyName = receiptDetails.PaymentCompanyName;
                    LogUtil.AddLog($"更新申请：到账开票使用付款方作为形式发票抬头: {receiptDetails.PaymentCompanyName}");
                }
                else
                {
                    // 普票和专票需要验证抬头
                    // 优先使用到账付款方公司作为抬头
                    string headerName = receiptDetails.PaymentCompanyName;
                    bool headerValid = false;

                    // 验证付款方公司抬头
                    int tempTotal = 0;
                    var headerInfos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(headerName, false, ref tempTotal);

                    if (headerInfos.Count > 0)
                    {
                        // 付款方公司验证成功
                        headerValid = true;
                        request.ReceiveCompanyInfo.CompanyName = headerName;
                        LogUtil.AddLog($"更新申请：到账开票使用付款方公司抬头: {headerName}");
                    }
                    else
                    {
                        // 查询是否是代付款
                        var paymentInfo = DbContext.Crm2Db
                            .Queryable<Db_crm_contract_receiptregister>()
                            .LeftJoin<Db_crm_contract_paymentinfo>((r, p) => r.ContractPaymentInfoId == p.Id)
                            .Where((r, p) => r.Id == request.ReceiptId &&
                                  r.Deleted != true && p.Deleted != true)
                            .Select((r, p) => new
                            {
                                IsBehalfPayment = p.IsBehalfPayment,
                                PaymentCompany = p.PaymentCompany
                            })
                            .First();

                        if (paymentInfo != null && paymentInfo.IsBehalfPayment.HasValue && paymentInfo.IsBehalfPayment.Value && !string.IsNullOrEmpty(paymentInfo.PaymentCompany))
                        {
                            // 获取代付款公司名称
                            var behalfCompany = DbOpe_crm_customer_subcompany.Instance.GetData(c =>
                                c.Id == paymentInfo.PaymentCompany && c.Deleted == 0);

                            if (behalfCompany != null && !string.IsNullOrEmpty(behalfCompany.CompanyName))
                            {
                                // 验证代付款公司抬头
                                headerName = behalfCompany.CompanyName;
                                headerInfos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(headerName, false, ref tempTotal);

                                if (headerInfos.Count > 0)
                                {
                                    // 代付款公司验证成功
                                    headerValid = true;
                                    request.ReceiveCompanyInfo.CompanyName = headerName;
                                    LogUtil.AddLog($"更新申请：到账开票使用代付款公司抬头: {headerName}");
                                }
                            }
                        }
                    }

                    // 如果付款方和代付款方都验证失败，尝试使用合同甲方
                    if (!headerValid)
                    {
                        // 获取合同甲方信息
                        if (!string.IsNullOrEmpty(contract.FirstParty))
                        {
                            var firstPartyCompany = DbOpe_crm_customer_subcompany.Instance.GetData(c =>
                                c.Id == contract.FirstParty && c.Deleted == 0);

                            if (firstPartyCompany != null && !string.IsNullOrEmpty(firstPartyCompany.CompanyName))
                            {
                                headerName = firstPartyCompany.CompanyName;
                                headerInfos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(headerName, false, ref tempTotal);

                                if (headerInfos.Count > 0)
                                {
                                    // 合同甲方验证成功
                                    headerValid = true;
                                    request.ReceiveCompanyInfo.CompanyName = headerName;
                                    LogUtil.AddLog($"更新申请：到账开票使用合同甲方抬头: {headerName}");
                                }
                            }
                        }
                    }

                    // 如果所有抬头验证都失败，则只能开形式发票
                    if (!headerValid)
                    {
                        LogUtil.AddErrorLog($"更新申请：到账开票抬头验证全部失败，只能选择形式发票: {headerName}");
                        throw new ApiException("付款方、代付款方和合同甲方公司验证均失败，只能选择开具形式发票");
                    }
                }
            }

            // 如果不是形式发票，且验证了抬头，则进行常规的抬头公司验证
            if (request.InvoiceInfo.InvoiceType != EnumInvoiceType.ProformaTicket && request.BillingType != EnumBillingType.InvoicingUponReceipt)
            {
                // 添加发票抬头公司验证 - 参考创建申请时的验证逻辑
                string creditCode = string.Empty;
                string billingHeaderName = request.ReceiveCompanyInfo.CompanyName;

                int total = 0;
                var infos = new List<Db_crm_tianyancha_info>();

                // 普票或专票，要限制开票抬头不可以是全英文
                infos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(billingHeaderName, false, ref total);

                // 如果未设置信用代码，则从查询结果中获取
                if (string.IsNullOrEmpty(creditCode))
                {
                    var exactCompany = infos.Find(i => i.CompanyName == billingHeaderName);
                    var historyCompany = infos.Find(i => i.HistoryNames != null && i.HistoryNames.Split("|").Contains(billingHeaderName));

                    if (exactCompany != null)
                    {
                        // 名称输入与天眼查一致
                        creditCode = exactCompany.CreditCode;
                        request.ReceiveCompanyInfo.CreditCode = creditCode;
                        LogUtil.AddLog($"发票抬头公司验证成功(更新): {billingHeaderName}, 信用代码: {creditCode}");
                    }
                    else if (historyCompany != null)
                    {
                        // 输入的是曾用名，特殊处理编码
                        var index = historyCompany.HistoryNames.Split("|").ToList().IndexOf(billingHeaderName);
                        creditCode = "$$" + index + historyCompany.CreditCode;
                        request.ReceiveCompanyInfo.CreditCode = creditCode;
                        LogUtil.AddLog($"发票抬头公司验证成功(更新-曾用名): {billingHeaderName}, 当前名称: {historyCompany.CompanyName}, 信用代码: {creditCode}");
                    }
                    else
                    {
                        // 未找到公司信息，则抛出异常
                        LogUtil.AddErrorLog($"发票抬头公司验证失败(更新): {billingHeaderName}, 未找到相关公司信息");
                        throw new ApiException("未找到发票抬头公司信息，请检查输入的公司名称是否正确且完整");
                    }
                }
            }
            else if (request.InvoiceInfo.InvoiceType == EnumInvoiceType.ProformaTicket && request.BillingType != EnumBillingType.InvoicingUponReceipt)
            {
                // 形式发票
                string billingHeaderName = request.ReceiveCompanyInfo.CompanyName;

                // 验证信用代码是否已设置
                if (string.IsNullOrEmpty(request.ReceiveCompanyInfo.CreditCode))
                {
                    // 形式发票，生成临时信用代码
                    string creditCode = DateTime.Now.ToString("yyyyMMddHHmmssff") + new Random().Next(10) + "X";
                    request.ReceiveCompanyInfo.CreditCode = creditCode;
                    LogUtil.AddLog($"形式发票生成临时信用代码(更新): {billingHeaderName}, 临时代码: {creditCode}");
                }
            }

            try
            {
                // 6. 使用事务更新发票申请记录
                DbOpe_crm_invoice_application.Instance.TransDeal(() =>
                {
                    // 获取原始申请记录，用于比较ReceiptId是否变更
                    var originalApplication = DbOpe_crm_invoice_application.Instance.QueryByPrimaryKey(request.Id);
                    string originalReceiptId = originalApplication?.ReceiptId;

                    // 更新发票申请记录
                    var applicationToUpdate = new Db_crm_invoice_application
                    {
                        Id = request.Id,
                        ContractId = request.ContractId,
                        BillingType = (int)request.BillingType,
                        InvoiceType = (int)request.InvoiceInfo.InvoiceType,
                        BillingCompany = request.BillingCompany,
                        BillingHeader = request.ReceiveCompanyInfo.CompanyName,
                        CreditCode = request.ReceiveCompanyInfo.CreditCode,
                        AppliedAmount = newAppliedAmount,
                        ExpectedInvoicingDate = request.ExpectedInvoiceDate,
                        InvoicingDetails = request.InvoiceInfo.InvoicingDetails,
                        Recipient = request.ReceiveCompanyInfo.Recipient,
                        Email = request.ReceiveCompanyInfo.Email,
                        // 被拒绝的申请，更新后将状态改为"申请中"
                        AuditStatus = (int)EnumInvoiceApplicationStatus.Applied,
                        Remark = request.ApplicationReason,
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now,
                        Deleted = false,
                        ApplicantId = userId,
                        ApplyTime = DateTime.Now
                    };

                    // 如果是到账开票类型，需要设置ReceiptId
                    if (request.BillingType == EnumBillingType.InvoicingUponReceipt && !string.IsNullOrEmpty(request.ReceiptId))
                    {
                        applicationToUpdate.ReceiptId = request.ReceiptId;
                    }
                    else
                    {
                        applicationToUpdate.ReceiptId = null;
                    }

                    try
                    {
                        // 更新发票申请记录
                        DbOpe_crm_invoice_application.Instance.UpdateData(applicationToUpdate);
                        LogUtil.AddLog($"更新发票申请记录: {request.Id}");

                        // 处理到账匹配关系
                        // 删除原有的匹配记录，重新创建新的匹配记录

                        // 如果原来有匹配记录，则删除
                        if (!string.IsNullOrEmpty(originalReceiptId))
                        {
                            // 删除旧的匹配记录
                            var matchingsToDelete = DbOpe_crm_invoice_receipt_matching.Instance
                                .GetDataList(m => m.InvoiceApplicationId == request.Id ||
                                                 m.ReceiptId == originalReceiptId);

                            if (matchingsToDelete != null && matchingsToDelete.Count > 0)
                            {
                                foreach (var matching in matchingsToDelete)
                                {
                                    DbOpe_crm_invoice_receipt_matching.Instance.Delete(matching);
                                    LogUtil.AddLog($"删除旧的到账匹配记录: ID={matching.Id}, 到账ID={matching.ReceiptId}");
                                }
                            }
                        }

                        // 3. 如果现在是到账开票且有新的到账记录，则创建新的匹配记录
                        if (request.BillingType == EnumBillingType.InvoicingUponReceipt && !string.IsNullOrEmpty(request.ReceiptId))
                        {
                            // 检查是否已有匹配记录
                            bool hasExistingMatching = DbOpe_crm_invoice_receipt_matching.Instance
                                .GetDataList(m => m.ReceiptId == request.ReceiptId && m.Deleted != true).Any();

                            if (hasExistingMatching)
                            {
                                throw new ApiException("该到账记录已有匹配关系，不能重复申请");
                            }

                            // 创建新的匹配记录
                            var newMatching = new Db_crm_invoice_receipt_matching
                            {
                                Id = Guid.NewGuid().ToString(),
                                ReceiptId = request.ReceiptId,
                                InvoiceId = "", // 暂时为空，后续更新
                                InvoiceApplicationId = request.Id,
                                MatchingStatus = (int)EnumInvoiceMatchingStatus.WaitingAudit,
                                MatchingTime = DateTime.Now,
                                MatchingUser = userId,
                                IsManualMatching = true, // 手动匹配
                                Remark = "更新发票申请时创建的匹配记录",
                                Deleted = false,
                                CreateUser = userId,
                                CreateDate = DateTime.Now,
                                UpdateUser = userId,
                                UpdateDate = DateTime.Now
                            };

                            DbOpe_crm_invoice_receipt_matching.Instance.Insert(newMatching);
                            LogUtil.AddLog($"创建新的到账匹配记录: ID={newMatching.Id}, 到账ID={request.ReceiptId}, 申请ID={request.Id}");
                        }
                        // 如果原来是形式发票类型，需要删除原形式发票
                        if (originalApplication.InvoiceType == (int)EnumInvoiceType.ProformaTicket)
                        {
                            var oldProformaInvoice = DbOpe_crm_proforma_invoice.Instance.GetData(p => p.ApplicationId == request.Id && p.Deleted != true);
                            if (oldProformaInvoice != null)
                            {
                                oldProformaInvoice.Deleted = true;
                                oldProformaInvoice.UpdateUser = userId;
                                oldProformaInvoice.UpdateDate = DateTime.Now;
                                DbOpe_crm_proforma_invoice.Instance.Update(oldProformaInvoice);
                            }
                        }
                        // 如果修改后是形式发票，需要直接创建新的形式发票
                        if (request.InvoiceInfo.InvoiceType == EnumInvoiceType.ProformaTicket)
                        {
                            try
                            {
                                // 准备形式发票所需数据
                                var invoiceData = new Dictionary<string, object>
                                {
                                    { "ContractId", request.ContractId },
                                    { "Amount", request.InvoiceInfo.InvoiceAmount ?? 0 },
                                    { "BillingHeader", request.ReceiveCompanyInfo.CompanyName },
                                    { "BillingCompany", request.BillingCompany },
                                    { "TaxpayerCode", request.ReceiveCompanyInfo.CreditCode },
                                    { "ProjectName", contract.ContractName },
                                    { "Recipient", request.ReceiveCompanyInfo.Recipient },
                                    { "Email", request.ReceiveCompanyInfo.Email },
                                    { "InvoiceDetails",request.InvoiceInfo.InvoicingDetails},
                                    { "Remark", request.ApplicationReason }
                                };

                                // 直接创建或获取形式发票
                                var proformaInvoice = DbOpe_crm_proforma_invoice.Instance.GetOrCreateProformaInvoice(
                                            request.Id, invoiceData, userId);

                                if (proformaInvoice != null)
                                {
                                    LogUtil.AddLog($"成功创建形式发票，ID: {proformaInvoice.Id}，申请ID: {request.Id}");

                                    // 自动审核通过形式发票申请
                                    var application = DbOpe_crm_invoice_application.Instance.QueryByPrimaryKey(request.Id);
                                    if (application != null)
                                    {
                                        application.AuditStatus = (int)EnumInvoiceApplicationStatus.Completed;
                                        application.UpdateUser = userId;
                                        application.UpdateDate = DateTime.Now;
                                        DbOpe_crm_invoice_application.Instance.Update(application);
                                        LogUtil.AddLog($"形式发票申请自动审核通过，申请ID: {request.Id}");
                                    }
                                    if (request.BillingType == EnumBillingType.InvoicingUponReceipt && !string.IsNullOrEmpty(request.ReceiptId))
                                    {
                                        // 更新到账匹配状态为已匹配
                                        var matching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => m.InvoiceApplicationId == request.Id && m.Deleted != true);
                                        if (matching != null)
                                        {
                                            matching.MatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccess;
                                            matching.MatchingTime = DateTime.Now;
                                            matching.MatchingUser = userId;
                                            matching.UpdateUser = userId;
                                            matching.UpdateDate = DateTime.Now;
                                            DbOpe_crm_invoice_receipt_matching.Instance.Update(matching);
                                        }
                                    }

                                }
                                else
                                {
                                    LogUtil.AddErrorLog($"创建形式发票失败，申请ID: {request.Id}");
                                }
                            }
                            catch (Exception ex)
                            {
                                LogUtil.AddErrorLog($"创建形式发票过程中发生错误: {ex.Message}, {ex.StackTrace}");
                                // 不抛出异常，让申请正常创建，后续可以手动处理形式发票
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddErrorLog($"更新发票申请异常: {ex.Message}");
                        throw new ApiException("更新发票申请失败: " + ex.Message);
                    }
                });

                // 7. 处理附件 (事务外处理，避免长事务)
                // 7.1 处理要删除的附件
                if (request.ExistingAttachments != null)
                {
                    // 获取原有附件列表
                    var existingAttachmentIds = DbOpe_crm_contract_invoiceappl_attachment.Instance
                        .GetDataList(a => a.ContractInvoiceApplId == request.Id && a.Deleted == false)
                        .Select(a => a.Id)
                        .ToList();

                    // 计算需要删除的附件
                    var attachmentsToRetain = request.ExistingAttachments.Select(a => a.Id).ToList();
                    var attachmentsToDelete = existingAttachmentIds
                        .Where(id => !attachmentsToRetain.Contains(id))
                        .Select(id => new BM_FileInfo { Id = id })
                        .ToList();

                    // 删除不再需要的附件
                    if (attachmentsToDelete.Any())
                    {
                        DbOpe_crm_contract_invoiceappl_attachment.Instance.DeleteInvoiceApplAttachFileList(
                            attachmentsToDelete,
                            request.Id);
                    }
                }

                // 7.2 处理新上传的附件
                if (request.InvoiceAttachments != null && request.InvoiceAttachments.Count > 0)
                {
                    // 直接使用系统中的附件上传工具类处理多个附件
                    Util<DbOpe_crm_contract_invoiceappl_attachment, BM_AttachFile> invoiceApplAttachFile =
                        new Util<DbOpe_crm_contract_invoiceappl_attachment, BM_AttachFile>(
                            DbOpe_crm_contract_invoiceappl_attachment.Instance);

                    invoiceApplAttachFile.UploadFile(
                        request.InvoiceAttachments,
                        EnumAttachFileType.ContractInvoiceAppl.ToString(),
                        userId,
                        request.Id);
                }

                // 更新发票显示状态
                UpdateInvoiceDisplayStatus(request.Id);

                return true;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"更新发票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新发票申请系统异常: {ex.Message}");
                throw new ApiException("更新发票申请失败，请联系系统管理员");
            }
        }

        #endregion


        #region  作废

        /// <summary>
        /// 作废发票申请
        /// 如果是形式发票，则删除形式发票
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <param name="isSystem">是否为系统自动作废</param>
        /// <returns>是否作废成功</returns>
        public bool InvalidateInvoiceApplication(string id, bool isSystem = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(id))
                {
                    throw new ApiException("发票申请ID不能为空");
                }

                // 获取发票申请信息
                var application = DbOpe_crm_invoice_application.Instance.GetData(a =>
                    a.Id == id && a.Deleted != true);

                if (application == null)
                {
                    throw new ApiException("发票申请信息不存在或已被删除");
                }

                // 获取合同信息以获取客户ID
                var contract = DbOpe_crm_contract.Instance.GetData(c =>
                    c.Id == application.ContractId && c.Deleted != true);

                if (contract == null && !isSystem)
                {
                    throw new ApiException("合同信息不存在或已被删除");
                }
                var isProformaTicket = application.InvoiceType == (int)EnumInvoiceType.ProformaTicket;
                // 从TokenModel获取当前用户ID
                string userId = TokenModel.Instance.id;
                if (!isSystem)
                {
                    // 检查权限
                    bool superRole = BLL_Role.Instance.CheckSuperUser();
                    if (!superRole)
                    {
                        // 获取当前用户信息
                        var currentUser = DbOpe_sys_user.Instance.GetUserById(userId);

                        // 判断用户是否为后台管理人员
                        bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(userId);

                        if (!isBackendManager)
                        {
                            // 检查是否为当前客户的客户经理
                            var hasPermission = DbContext.Crm2Db.Queryable<Db_v_customer_subcompany_private_user>()
                                .Any(cspu => cspu.Id == contract.FirstParty &&
                                        cspu.CompanyCurrentUser == userId);

                            if (!hasPermission)
                            {
                                throw new ApiException("没有权限作废该发票申请");
                            }
                        }
                    }

                    // 检查发票申请状态
                    if (application.AuditStatus != (int)EnumInvoiceApplicationStatus.Applied &&
                        application.AuditStatus != (int)EnumInvoiceApplicationStatus.Processed &&
                        application.AuditStatus != (int)EnumInvoiceApplicationStatus.Rejected && !isProformaTicket)
                    {
                        throw new ApiException("只有申请中、待复核或已拒绝状态的发票申请才能作废");
                    }
                }

                // 作废发票申请
                application.AuditStatus = (int)EnumInvoiceApplicationStatus.Invalidated;
                application.UpdateUser = userId;
                application.UpdateDate = DateTime.Now;
                application.IsReminded = (int)EnumIsReminder.UnUrgedTicket;

                DbOpe_crm_invoice_application.Instance.UpdateData(application);

                // 如果是到账开票类型，删除初始匹配记录
                if (application.BillingType == (int)EnumBillingType.InvoicingUponReceipt &&
                    !string.IsNullOrEmpty(application.ReceiptId))
                {
                    try
                    {
                        // 查找并删除初始匹配记录
                        var matchingRecord = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m =>
                            m.ReceiptId == application.ReceiptId &&
                            m.InvoiceApplicationId == application.Id &&
                            m.Deleted != true);

                        if (matchingRecord != null)
                        {
                            // 逻辑删除匹配记录
                            matchingRecord.Deleted = true;
                            matchingRecord.UpdateUser = userId;
                            matchingRecord.UpdateDate = DateTime.Now;
                            DbOpe_crm_invoice_receipt_matching.Instance.Update(matchingRecord);

                            LogUtil.AddLog($"作废发票申请：删除到账开票初始匹配记录，ID: {matchingRecord.Id}，到账ID: {application.ReceiptId}");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddErrorLog($"删除到账开票初始匹配记录异常: {ex.Message}");
                        // 不抛出异常，让作废流程正常完成
                    }
                }

                if (isProformaTicket)
                {
                    var performInvoice = DbOpe_crm_proforma_invoice.Instance.GetData(a =>
                        a.ApplicationId == id && a.Deleted != true);

                    if (performInvoice != null)
                    {
                        performInvoice.Deleted = true;
                        performInvoice.UpdateUser = userId;
                        performInvoice.UpdateDate = DateTime.Now;
                        DbOpe_crm_proforma_invoice.Instance.Update(performInvoice);
                    }
                }

                // 更新发票显示状态
                UpdateInvoiceDisplayStatus(id);

                return true;
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"作废发票申请时发生错误: {ex.Message}");
                throw new ApiException("作废发票申请失败");
            }
        }

        /// <summary>
        /// 修改合同后的发票处理
        /// 1、原合同下的已经开具的发票(包括退票)全部挂在新的合同下
        /// 2、原合同下审核中的发票(包括退票)全部作废
        /// 3、待复核、复核驳回时，已经上传过发票了，这时发票就应该算已经开票，应当按照已开票处理
        /// </summary>
        /// <param name="oldContractId">旧合同ID</param>
        /// <param name="newContractId">新合同ID</param>
        /// <returns></returns>
        public void ModifyContract(string oldContractId, string newContractId)
        {
            if (string.IsNullOrEmpty(oldContractId) || string.IsNullOrEmpty(newContractId))
            {
                throw new ApiException("合同ID不能为空");
            }
            var userId = TokenModel.Instance.id;
            DbOpe_crm_invoice.Instance.TransDeal(() =>
            {

                // 1、原合同下的已经开具的发票/形式发票/退票全部挂在新的合同下
                var invoicedList = DbOpe_crm_invoice.Instance.GetDataList(i =>
                    i.ContractId == oldContractId &&
                    i.Deleted != true);

                foreach (var invoice in invoicedList)
                {
                    invoice.ContractId = newContractId;
                    invoice.UpdateUser = userId;
                    invoice.UpdateDate = DateTime.Now;
                    DbOpe_crm_invoice.Instance.Update(invoice);
                }
                var performInvoiceList = DbOpe_crm_proforma_invoice.Instance.GetDataList(a =>
                    a.ContractId == oldContractId &&
                    a.Deleted != true);

                foreach (var performInvoice in performInvoiceList)
                {
                    performInvoice.ContractId = newContractId;
                    performInvoice.UpdateUser = userId;
                    performInvoice.UpdateDate = DateTime.Now;
                    DbOpe_crm_proforma_invoice.Instance.Update(performInvoice);
                }

                // 2、原合同下未完成审核的发票申请全部作废,通过的全部更新合同ID
                // 3、待复核、复核驳回时，已经上传过发票了，这时发票就应该算已经开票，应当按照已开票处理
                // 4、原合同下退票及其申请全部更新合同ID
                var invoiceAppls = DbOpe_crm_invoice_application.Instance.GetDataList(a =>
                    a.ContractId == oldContractId &&
                    a.Deleted != true);

                foreach (var invoiceAppl in invoiceAppls)
                {
                    var invalidateStates = new int[] {
                            (int)EnumInvoiceDisplayStatus.NoAudit,
                            (int)EnumInvoiceDisplayStatus.InvoiceApplied,
                            (int)EnumInvoiceDisplayStatus.InvoiceRejected
                        };
                    if (invalidateStates.Any(x => x == invoiceAppl.DisplayStatus))
                    {
                        //未完成审核的发票申请全部作废
                        InvalidateInvoiceApplication(invoiceAppl.Id, true);
                    }
                    else
                    {
                        invoiceAppl.ContractId = newContractId;
                        invoiceAppl.UpdateUser = userId;
                        invoiceAppl.UpdateDate = DateTime.Now;
                        DbOpe_crm_invoice_application.Instance.Update(invoiceAppl);
                        //更新发票审核记录的合同ID
                        var invoiceReviews = DbOpe_crm_invoice_review.Instance.GetDataList(a =>
                            a.InvoiceApplicationId == invoiceAppl.Id);

                        foreach (var invoiceReview in invoiceReviews)
                        {
                            invoiceReview.ContractId = newContractId;
                            invoiceReview.UpdateUser = userId;
                            invoiceReview.UpdateDate = DateTime.Now;
                            DbOpe_crm_invoice_review.Instance.Update(invoiceReview);
                        }
                        var invoiceId = DbOpe_crm_invoice.Instance.GetData(a =>
                            a.InvoiceApplicationId == invoiceAppl.Id &&
                            a.Deleted != true);
                        if (invoiceId != null)
                        {
                            //原合同下退票及其申请全部更新合同ID
                            var refundAppls = DbOpe_crm_invoice_refund_application.Instance.GetDataList(a =>
                                a.ContractId == oldContractId &&
                                a.InvoiceId == invoiceId.Id &&
                                a.Deleted != true);
                            foreach (var refundAppl in refundAppls)
                            {
                                    refundAppl.ContractId = newContractId;
                                    refundAppl.UpdateUser = userId;
                                    refundAppl.UpdateDate = DateTime.Now;
                                    DbOpe_crm_invoice_refund_application.Instance.Update(refundAppl);
                                    var invoiceRefundReviews = DbOpe_crm_invoice_refund_review.Instance.GetDataList(a =>
                                        a.RefundApplicationId == refundAppl.Id);

                                    foreach (var invoiceRefundReview in invoiceRefundReviews)
                                    {
                                        invoiceRefundReview.ContractId = newContractId;
                                        invoiceRefundReview.UpdateUser = userId;
                                        invoiceRefundReview.UpdateDate = DateTime.Now;
                                        DbOpe_crm_invoice_refund_review.Instance.Update(invoiceRefundReview);
                                    }
                            }
                        }
                    }
                }

            });
        }

        /// <summary>
        /// 检查合同是否存在有效发票(已开票未完成退票)
        /// 待复核/复核驳回时，已经上传过发票了，这时发票就应该算已经开票，应当按照已开票处理
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        public bool CheckContractHasValidInvoice(string contractId)
        {
            if (string.IsNullOrEmpty(contractId))
            {
                throw new ApiException("合同ID不能为空");
            }
            //检查合同是否存在有效发票(已开票未完成退票)
            var invoice = DbOpe_crm_invoice.Instance.GetDataList(a =>
                a.ContractId == contractId &&
                a.RefundStatus == (int)EnumInvoiceRefundStatus.Normal &&
                a.TransactionType == 1 &&
                a.Deleted != true);
            //检查合同是否存在待复核/复核驳回的发票申请
            var invoiceAppl = DbOpe_crm_invoice_application.Instance.GetDataList(a =>
                a.ContractId == contractId &&
                (a.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceReviewRejected ||
                a.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceProcessed) &&
                a.Deleted != true);
            return invoice.Any() || invoiceAppl.Any();
        }

        /// <summary>
        /// 合同作废/修改后的发票处理
        /// 1、如果有审核中的发票申请，则作废申请
        /// （此段注释不用）2、如果合同已开票，则取消和合同的关联，此发票变更为一张补充发票，等待经办人后续去做补充发票退票/补充发票作废
        /// 3、如果是形式发票，则删除形式发票
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        public bool InvalidateContract(string contractId)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(contractId))
                {
                    throw new ApiException("合同ID不能为空");
                }


                // 从TokenModel获取当前用户ID
                string userId = TokenModel.Instance.id;

                // 1. 获取并处理所有审核中的发票申请
                var pendingApplications = DbOpe_crm_invoice_application.Instance.GetDataList(a =>
                    a.ContractId == contractId &&
                    a.Deleted != true &&
                    (a.AuditStatus == (int)EnumInvoiceApplicationStatus.Applied ||
                     a.AuditStatus == (int)EnumInvoiceApplicationStatus.Processed ||
                     a.AuditStatus == (int)EnumInvoiceApplicationStatus.Rejected));

                // 作废所有审核中的发票申请
                foreach (var application in pendingApplications)
                {

                    // 作废发票申请
                    InvalidateInvoiceApplication(application.Id, true);

                }
                #region 获取并处理所有已开票的发票 注释不用
                /*// 2. 获取并处理所有已开票的发票
                var issuedInvoices = DbOpe_crm_invoice.Instance.GetDataList(i =>
                    i.ContractId == contractId &&
                    i.Deleted != true);

                foreach (var invoice in issuedInvoices)
                {

                    // 获取发票申请信息
                    var invoiceApplication = DbOpe_crm_invoice_application.Instance.GetData(a =>
                        a.Id == invoice.InvoiceApplicationId &&
                        a.Deleted != true);

                    if (invoiceApplication != null &&
                        invoiceApplication.AuditStatus == (int)EnumInvoiceApplicationStatus.Completed)
                    {
                        // 解除发票与合同的关联
                        invoice.ContractId = null;
                        invoice.IsSupplementInvoice = true; // 标记为补充发票
                        invoice.MatchingStatus = (int)EnumInvoiceMatchingStatus.NotReceived;
                        invoice.UpdateUser = userId;
                        invoice.UpdateDate = DateTime.Now;
                        invoice.BillingType = (int)EnumBillingType.SupplementInvoice;

                        DbOpe_crm_invoice.Instance.UpdateData(invoice);

                        // 同步更新发票申请信息
                        invoiceApplication.ContractId = null;
                        invoiceApplication.UpdateUser = userId;
                        invoiceApplication.UpdateDate = DateTime.Now;
                        invoiceApplication.BillingType = (int)EnumBillingType.SupplementInvoice;
                        invoiceApplication.ReceiptId = null;

                        DbOpe_crm_invoice_application.Instance.UpdateData(invoiceApplication);

                        // 更新发票显示状态
                        UpdateInvoiceDisplayStatus(invoiceApplication.Id);

                        //删除发票匹配记录
                        var matchingRecord = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m =>
                            m.InvoiceApplicationId == invoiceApplication.Id &&
                            m.Deleted != true);

                        if (matchingRecord != null)
                        {
                            // 逻辑删除匹配记录
                            matchingRecord.Deleted = true;
                            matchingRecord.UpdateUser = userId;
                            matchingRecord.UpdateDate = DateTime.Now;
                            DbOpe_crm_invoice_receipt_matching.Instance.Update(matchingRecord);

                            LogUtil.AddLog($"作废发票：删除到账开票初始匹配记录，ID: {matchingRecord.Id}，发票ID: {invoice.Id}");
                        }

                        LogUtil.AddLog($"合同作废时解除发票与合同的关联，将发票变更为补充发票，合同ID: {contractId}, 发票ID: {invoice.Id}");
                    }

                }*/
                #endregion
                var performInvoice = DbOpe_crm_proforma_invoice.Instance.GetDataList(a =>
                    a.ContractId == contractId &&
                    a.Deleted != true);


                foreach (var invoice in performInvoice)
                {
                    //作废形式发票，并删除形式发票
                    InvalidateInvoiceApplication(invoice.ApplicationId, true);
                }

                return true;
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"合同作废处理发票失败: {ex.Message}");
                throw new ApiException("合同作废处理发票失败，请联系系统管理员");
            }
        }

        #endregion
    }
}