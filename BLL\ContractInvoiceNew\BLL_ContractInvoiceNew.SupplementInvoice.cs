using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.Common.Utils;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BLLModel.InvoiceSystem;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using QCloud;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using CRM2_API.Common.JWT;
using static CRM2_API.Model.BLLModel.InvoiceSystem.VM_InvoiceSystem;
using CRM2_API.Common.Cache;
using AlibabaCloud.SDK.Dingtalkagoal_1_0.Models;

namespace CRM2_API.BLL
{
    public partial class BLL_ContractInvoiceNew : BaseBLL<BLL_ContractInvoiceNew>
    {
        #region 补充发票业务逻辑

        /// <summary>
        /// 上传并识别补充发票
        /// </summary>
        /// <param name="fileBytes">发票文件字节数组</param>
        /// <param name="applicationId">发票申请ID</param>
        /// <returns>OCR识别结果</returns>
        public async Task<RecognizeSupplementInvoice_Out> UploadAndRecognizeSupplementInvoice(byte[] fileBytes,string applicationId = null)
        {
            try
            {
                // 参数验证
                if (fileBytes == null)
                {
                    throw new ApiException("文件不能为空");
                }

                // 根据文件类型调用不同的OCR识别方法
                UmiOcrService.OcrInvoiceInfo invoiceInfo = null;
                //这里换一种实现方式，用pdf读取二维码信息
                var analyzePdfQRCodeResult = await InvoiceReadUtil.AnalyzePdfQRCode(fileBytes);

                //尝试从pdf中直接读取其他信息
                invoiceInfo = InvoiceReadUtil.ExtractInvoiceInfoByFixedCoordinates(fileBytes);
                if (invoiceInfo == null)
                {
                    throw new ApiException("发票OCR识别失败");
                }

                // 转换为OcrInvoiceResult
                var ocrResult = new OcrInvoiceResult
                {
                    InvoiceType = invoiceInfo.InvoiceType,
                    InvoiceCode = invoiceInfo.InvoiceCode,
                    InvoiceNumber = analyzePdfQRCodeResult.InvoiceNo == "?" ? invoiceInfo.InvoiceNumber : analyzePdfQRCodeResult.InvoiceNo,
                    InvoiceDate = analyzePdfQRCodeResult.InvoiceDate == "?" ? (invoiceInfo.InvoiceDate == null ? "" : invoiceInfo.InvoiceDate.Value.ToString("yyyy-MM-dd")) : analyzePdfQRCodeResult.InvoiceDate,
                    BuyerName = invoiceInfo.BuyerName,
                    BuyerTaxNumber = invoiceInfo.BuyerTaxNumber,
                    SellerName = invoiceInfo.SellerName,
                    SellerTaxNumber = invoiceInfo.SellerTaxNumber,
                    TotalAmount = invoiceInfo.TotalAmount,
                    TotalTax = invoiceInfo.TotalTax,
                    TotalAmountWithTax = analyzePdfQRCodeResult.InvoiceAmount == "?" ? invoiceInfo.TotalAmountWithTax.ToString("F2") : analyzePdfQRCodeResult.InvoiceAmount,
                    OriginalText = invoiceInfo.OriginalText,
                    Remark = invoiceInfo.Remark,
                    InvoiceDetails = analyzePdfQRCodeResult.InvoiceDetails == "?" ? "" : analyzePdfQRCodeResult.InvoiceDetails
                };
                ocrResult.CollectingCompanyId = RedisCache.CollectingCompany.GetCollectingCompanyBySellerName(ocrResult.SellerName)?.Id ?? string.Empty;
                
                

                // 临时保存识别结果，不关联申请ID
                applicationId = applicationId  ?? Guid.NewGuid().ToString();
                var ocrId = await SaveOcrResultAsync(applicationId, ocrResult);
                // 保存发票附件到crm_contract_invoice_attachment表
                string attachmentId = await SaveInvoiceAttachmentAsync(fileBytes, applicationId, ocrResult.InvoiceNumber);

                // 返回识别结果
                return new RecognizeSupplementInvoice_Out
                {
                    InvoiceType = ocrResult.InvoiceType,
                    RecognitionId = ocrId,
                    InvoiceNumber = ocrResult.InvoiceNumber,
                    InvoiceCode = ocrResult.InvoiceCode,
                    InvoiceDate =  ocrResult.InvoiceDate,
                    TotalAmount = ocrResult.TotalAmount,
                    TaxAmount = ocrResult.TotalTax,
                    TotalAmountWithTax = !string.IsNullOrEmpty(ocrResult.TotalAmountWithTax) && decimal.TryParse(ocrResult.TotalAmountWithTax, out var totalWithTax) ? totalWithTax : 0m,
                    BuyerName = ocrResult.BuyerName,
                    BuyerTaxCode = ocrResult.BuyerTaxNumber,
                    SellerName = ocrResult.SellerName,
                    SellerTaxCode = ocrResult.SellerTaxNumber,
                    InvoiceDetails = ocrResult.InvoiceDetails,
                    CollectingCompanyId = ocrResult.CollectingCompanyId
                };
            }
            catch (ApiException aex)
            {
                LogUtil.AddErrorLog($"上传识别补充发票业务异常: {aex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"上传识别补充发票系统异常: {ex.Message}");
                throw new ApiException("上传识别补充发票失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 提交补充发票（经办人提交到复核环节）
        /// </summary>
        /// <param name="request">提交请求</param>
        public async Task<SubmitSupplementInvoice_Out> SubmitSupplementInvoice(SubmitSupplementInvoice_In request)
        {
            try
            {
                // 参数验证
                if (request == null || string.IsNullOrEmpty(request.RecognitionId))
                {
                    throw new ApiException("识别ID不能为空");
                }

                // 获取识别结果
                var recognition = DbOpe_crm_invoice_recognition.Instance.GetData(r => r.Id == request.RecognitionId && r.Deleted != true);
                if (recognition == null)
                {
                    throw new ApiException("识别记录不存在或已被删除");
                }

                // 验证发票类型是否合法
                if (request.InvoiceType != (int)EnumInvoiceType.UniversalTicket &&
                    request.InvoiceType != (int)EnumInvoiceType.SpecialTicket)
                {
                    throw new ApiException("发票类型无效，补充发票只支持普票和专票");
                }

                string userId = TokenModel.Instance.id;
                // 事务处理
                SubmitSupplementInvoice_Out result = null;

                DbOpe_crm_invoice_application.Instance.TransDeal(() =>
                {
                    // 1. 创建申请记录
                    string applicationId = recognition.InvoiceApplicationId;

                    //这里要根据申请id，获取是否是驳回后再次提交的申请ID
                    //如果是驳回的，则需要更新信息；否则新建

                    var application = DbOpe_crm_invoice_application.Instance.GetData(a => a.Id == applicationId && a.Deleted != true);
                    if (application != null)
                    {
                        //是驳回的
                        //获取审核记录
                        var currentReview = DbOpe_crm_invoice_review.Instance.GetData(r => r.InvoiceApplicationId == applicationId && r.Deleted != true);
                        if (currentReview != null)
                        {
                            if(currentReview.AuditStatus == (int)EnumInvoiceReviewStatus.ReviewRejected)
                            {
                                //更新信息
                                application.AuditStatus = (int)EnumInvoiceApplicationStatus.Processed;
                                application.UpdateUser = userId;
                                application.UpdateDate = DateTime.Now;
                                application.InvoiceType = request.InvoiceType;
                                application.BillingType = (int)EnumBillingType.SupplementInvoice;
                                application.BillingCompany = request.BillingCompany;
                                application.BillingHeader = request.BillingHeader;
                                application.CreditCode = request.CreditCode;
                                application.AppliedAmount = request.TotalAmount;
                                application.ExpectedInvoicingDate = request.InvoiceDate;
                                application.InvoicingDetails = request.InvoiceDetails ?? recognition.InvoiceDetails;
                                application.Recipient = request.Recipient;
                                application.Email = request.Email;
                                application.Remark = request.Remark;
                                //补充发票的后台备注默认和remark一致
                                application.InvoiceBackgroundRemark = request.Remark;
                                application.DisplayStatus = (int)EnumInvoiceDisplayStatus.InvoiceProcessed;
                                application.DisplayStatusName = EnumInvoiceDisplayStatus.InvoiceProcessed.GetEnumDescription();
                                application.ApplicantId = userId;
                                application.ApplyTime = DateTime.Now;
                                DbOpe_crm_invoice_application.Instance.Update(application);

                                //删除旧的审核信息
                                currentReview.Deleted = true;
                                currentReview.UpdateUser = userId;
                                currentReview.UpdateDate = DateTime.Now;
                                DbOpe_crm_invoice_review.Instance.Update(currentReview);

                            }
                            else{
                                throw new ApiException("当前审核状态不允许提交");
                            }
                        }
                    }
                    else{
                        application = new Db_crm_invoice_application
                        {
                            Id = applicationId,
                            ContractId = null, // 补充发票无关联合同
                            ReceiptId = null, // 补充发票无关联到账记录
                            BillingType = (int)EnumBillingType.SupplementInvoice, // 补充发票类型
                            InvoiceType = request.InvoiceType,
                            BillingCompany = request.BillingCompany,
                            BillingHeader = request.BillingHeader,
                            CreditCode = request.CreditCode,
                            AppliedAmount = request.TotalAmount,
                            ExpectedInvoicingDate = recognition.InvoiceDate,
                            InvoicingDetails = request.InvoiceDetails ?? recognition.InvoiceDetails,
                            Recipient = request.Recipient,
                            Email = request.Email,
                            AuditStatus = (int)EnumInvoiceApplicationStatus.Processed, // 待复核状态
                            ApplicantId = userId,
                            ApplyTime = DateTime.Now,
                            Remark = request.Remark,
                            //补充发票的后台备注默认和remark一致
                            InvoiceBackgroundRemark = request.Remark,
                            DisplayStatus = (int)EnumInvoiceDisplayStatus.InvoiceProcessed, // 发票待复核
                            DisplayStatusName = EnumInvoiceDisplayStatus.InvoiceProcessed.GetEnumDescription(),
                            CreateUser = userId,
                            CreateDate = DateTime.Now,
                            UpdateUser = userId,
                            UpdateDate = DateTime.Now,
                            Deleted = false
                        };

                     
                        // 保存数据
                        DbOpe_crm_invoice_application.Instance.Insert(application);

                    }
                       // 2. 创建审核记录（直接到复核环节）
                        string reviewId = Guid.NewGuid().ToString();

                        Db_crm_invoice_review review = new Db_crm_invoice_review
                        {
                            Id = reviewId,
                            InvoiceApplicationId = applicationId,
                            ContractId = null, // 补充发票无合同
                            InvoiceNumber = request.InvoiceNumber,
                            InvoicingDate = request.InvoiceDate,
                            InvoicedAmount = request.TotalAmount,
                            BillingCompany = request.BillingCompany,
                            BillingHeader = request.BillingHeader,
                            CreditCode = request.CreditCode,
                            InvoicingDetails = request.InvoiceDetails ?? recognition.InvoiceDetails,
                            InvoiceType = request.InvoiceType,
                            AuditStatus = (int)EnumInvoiceReviewStatus.WaitingAudit, // 待复核
                            ProcessorId = userId,
                            ProcessTime = DateTime.Now,
                            ProcessorRemark = "",
                            CreateUser = userId,
                            CreateDate = DateTime.Now,
                            UpdateUser = userId,
                            UpdateDate = DateTime.Now,
                            Deleted = false
                        };
                        DbOpe_crm_invoice_review.Instance.Insert(review);
                    DbOpe_crm_invoice_recognition.Instance.Update(recognition);
                    // 从recognition对象获取数据创建OCR结果对象
                    var ocrResult = new OcrInvoiceResult
                    {
                        RecognitionId = recognition.Id,
                        InvoiceCode = recognition.InvoiceCode,
                        InvoiceNumber = recognition.InvoiceNumber,
                        InvoiceDate = recognition.InvoiceDate?.ToString("yyyy-MM-dd"),
                        BuyerName = recognition.BuyerName,
                        SellerName = recognition.SellerName,
                        BuyerTaxNumber = recognition.BuyerTaxCode,
                        SellerTaxNumber = recognition.SellerTaxCode,
                        TotalAmount = recognition.InvoiceAmount ?? 0,
                        TotalTax = recognition.TaxAmount ?? 0,
                        TotalAmountWithTax = (recognition.TotalAmount ?? 0).ToString("F2"),
                        InvoiceDetails = recognition.InvoiceDetails,
                        OriginalText = recognition.RecognitionResult,
                        InvoiceType = recognition.InvoiceTypeString
                    };
                    
                    // 从application对象创建应用信息对象
                    var applicationInfo = new InvoiceApplicationInfo
                    {
                        Id = application.Id,
                        ContractId = application.ContractId,
                        AppliedAmount = request.TotalAmount,
                        BillingType = (EnumBillingType)application.BillingType,
                        InvoiceType = (EnumInvoiceType)request.InvoiceType,
                        BillingCompany = request.BillingCompany,
                        BillingCompanyName = DbOpe_crm_collectingcompany.Instance.GetData(c => 
                            c.Id == request.BillingCompany && c.Deleted == false)?.SellerName ?? string.Empty,
                        ApplicantId = application.ApplicantId,
                        ApplyTime = application.ApplyTime,
                        AuditStatus = (EnumInvoiceApplicationStatus)application.AuditStatus,
                        DisplayStatus = (EnumInvoiceDisplayStatus)(application.DisplayStatus ?? 0),
                        BillingHeader = request.BillingHeader,
                        BillingHeaderName = request.BillingHeader,
                        CreditCode = request.CreditCode,
                        Recipient = request.Recipient,
                        Email = request.Email,
                        Remark = request.Remark,
                        ExpectedInvoicingDate = request.InvoiceDate,
                        InvoiceDetails = request.InvoiceDetails
                    };
                    
                    // 获取OCR记录ID
                    var ocrId = recognition.Id;

                    // 创建比对结果对象
                    var compareResult = new InvoiceOcrCompareResult
                    {
                        OcrInfo = ocrResult,
                        ApplicationInfo = applicationInfo,
                        ComparisonResults = new List<FieldComparisonResult>(),
                        IsMatch = false // 默认为false，后面根据比对结果设置
                    };

                    // 进行字段比对
                    CompareFields(compareResult);

                    // 计算是否匹配
                    bool isMatch = true;
                    foreach (var fieldResult in compareResult.ComparisonResults)
                    {
                        // 如果任何一个必要字段不匹配，则整体不匹配
                        if (fieldResult.IsRequired && !fieldResult.IsMatch)
                        {
                            isMatch = false;
                            break;
                        }
                    }

                    compareResult.IsMatch = isMatch;
                    
                    // 保存比对结果
                    SaveComparisonResult(ocrId, compareResult);


                    // 更新发票显示状态
                    UpdateInvoiceDisplayStatus(applicationId);

                    // 设置返回结果
                    result = new SubmitSupplementInvoice_Out
                    {
                        ApplicationId = applicationId,
                        ReviewId = reviewId
                    };
                });

                return result;
            }
            catch (ApiException aex)
            {
                LogUtil.AddErrorLog($"提交补充发票业务异常: {aex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提交补充发票系统异常: {ex.Message}");
                throw new ApiException("提交补充发票失败，请联系系统管理员");
            }
        }
        #endregion

        #region 补充发票退票业务逻辑
        
        /// <summary>
        /// 上传并识别补充发票退票凭证
        /// </summary>
        /// <param name="fileBytes">退票凭证文件字节数组</param>
        /// <param name="originalInvoiceId">原发票ID</param>
        /// <param name="refundApplicationId">退票申请ID(注意这是退票的)</param>
        /// <returns>OCR识别结果</returns>
        public async Task<RecognizeSupplementInvoice_Out> UploadAndRecognizeSupplementInvoiceRefund(byte[] fileBytes, string originalInvoiceId,string refundApplicationId = null)
        {
            try
            {
                // 参数验证
                if (fileBytes == null)
                {
                    throw new ApiException("文件不能为空");
                }
                
                if (string.IsNullOrEmpty(originalInvoiceId))
                {
                    throw new ApiException("原发票ID不能为空");
                }
                
                string userId = TokenModel.Instance.id;
                // 验证原发票是否存在
                var originalInvoice = DbOpe_crm_invoice.Instance.GetData(i => 
                    i.Id == originalInvoiceId && 
                    i.Deleted != true);
                
                if (originalInvoice == null)
                {
                    throw new ApiException("未找到原发票信息");
                }
                
                // 验证是否为补充发票
                if (!originalInvoice.IsSupplementInvoice)
                {
                    throw new ApiException("该发票不是补充发票，请使用正确的接口发起退票");
                }
                if(originalInvoice.RefundStatus != (int)EnumInvoiceRefundStatus.Normal)
                {
                    throw new ApiException("该发票已退票，不能重复退票");
                }

                // 验证此发票是否已经存在退票申请 存在退票申请的，需要传入退票申请ID 
                var currentRefundApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => r.InvoiceId == originalInvoiceId && r.Deleted != true);
                if(currentRefundApplication != null)
                {
                    if(StringUtil.IsNullOrEmpty(refundApplicationId))
                    {
                        throw new ApiException("该发票已存在退票申请,不能重复申请");
                    }
                }
                  // 根据文件类型调用不同的OCR识别方法
                UmiOcrService.OcrInvoiceInfo invoiceInfo = null;

                //这里换一种实现方式，用pdf读取二维码信息
                var analyzePdfQRCodeResult = await InvoiceReadUtil.AnalyzePdfQRCode(fileBytes);

                //尝试从pdf中直接读取其他信息
                invoiceInfo = InvoiceReadUtil.ExtractInvoiceInfoByFixedCoordinates(fileBytes);
                if (invoiceInfo == null)
                {
                    throw new ApiException("退票凭证OCR识别失败");
                }


                // 转换为OcrInvoiceResult
                var ocrResult = new OcrInvoiceResult
                {
                    InvoiceType = invoiceInfo.InvoiceType,
                    InvoiceCode = invoiceInfo.InvoiceCode,
                    InvoiceNumber = analyzePdfQRCodeResult.InvoiceNo == "?" ? invoiceInfo.InvoiceNumber : analyzePdfQRCodeResult.InvoiceNo,
                    InvoiceDate = analyzePdfQRCodeResult.InvoiceDate == "?" ? (invoiceInfo.InvoiceDate == null ? "" : invoiceInfo.InvoiceDate.Value.ToString("yyyy-MM-dd")) : analyzePdfQRCodeResult.InvoiceDate,
                    BuyerName = invoiceInfo.BuyerName,
                    BuyerTaxNumber = invoiceInfo.BuyerTaxNumber,
                    SellerName = invoiceInfo.SellerName,
                    SellerTaxNumber = invoiceInfo.SellerTaxNumber,
                    TotalAmount = invoiceInfo.TotalAmount,
                    TotalTax = invoiceInfo.TotalTax,
                    TotalAmountWithTax = analyzePdfQRCodeResult.InvoiceAmount == "?" ? invoiceInfo.TotalAmountWithTax.ToString("F2") : analyzePdfQRCodeResult.InvoiceAmount,
                    OriginalText = invoiceInfo.OriginalText,
                    Remark = invoiceInfo.Remark,
                    InvoiceDetails = analyzePdfQRCodeResult.InvoiceDetails == "?" ? "" : analyzePdfQRCodeResult.InvoiceDetails
                };

                ocrResult.CollectingCompanyId = RedisCache.CollectingCompany.GetCollectingCompanyBySellerName(ocrResult.SellerName)?.Id ?? string.Empty;

                

                // 保存识别结果，不关联申请ID
                refundApplicationId = refundApplicationId ?? Guid.NewGuid().ToString();
                var ocrId = await SaveRefundOcrResultAsync(refundApplicationId, ocrResult);
                string attachmentId = await SaveRefundAttachmentAsync(fileBytes, refundApplicationId, ocrResult.InvoiceNumber);
                // 返回识别结果
                return new RecognizeSupplementInvoice_Out
                {
                    InvoiceType = ocrResult.InvoiceType,
                    RecognitionId = ocrId,
                    InvoiceNumber = ocrResult.InvoiceNumber,
                    InvoiceCode = ocrResult.InvoiceCode,
                    InvoiceDate = ocrResult.InvoiceDate,
                    TotalAmount = ocrResult.TotalAmount,
                    TaxAmount = ocrResult.TotalTax,
                    TotalAmountWithTax = !string.IsNullOrEmpty(ocrResult.TotalAmountWithTax) && decimal.TryParse(ocrResult.TotalAmountWithTax, out var totalWithTax) ? totalWithTax : 0m,
                    BuyerName = ocrResult.BuyerName,
                    BuyerTaxCode = ocrResult.BuyerTaxNumber,
                    SellerName = ocrResult.SellerName,
                    SellerTaxCode = ocrResult.SellerTaxNumber,
                    InvoiceDetails = ocrResult.InvoiceDetails,
                    CollectingCompanyId = ocrResult.CollectingCompanyId
                };
            }
            catch (ApiException aex)
            {
                LogUtil.AddErrorLog($"上传识别补充发票退票凭证业务异常: {aex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"上传识别补充发票退票凭证系统异常: {ex.Message}");
                throw new ApiException("上传识别补充发票退票凭证失败，请联系系统管理员");
            }
        }
        
        /// <summary>
        /// 提交补充发票退票申请
        /// </summary>
        /// <param name="request">提交请求</param>
        /// <returns>申请结果</returns>
        public SubmitSupplementInvoiceRefund_Out SubmitSupplementInvoiceRefund(SubmitSupplementInvoiceRefund_In request)
        {
            try
            {
                // 参数验证
                if (request == null || string.IsNullOrEmpty(request.RecognitionId))
                {
                    throw new ApiException("识别ID不能为空");
                }
                
                if (string.IsNullOrEmpty(request.OriginalInvoiceId))
                {
                    throw new ApiException("原发票ID不能为空");
                }
                
                if (string.IsNullOrEmpty(request.RefundReason))
                {
                    throw new ApiException("备注不能为空");
                }
                
                string userId = TokenModel.Instance.id;
                // 验证是否为补充发票
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                    i.Id == request.OriginalInvoiceId && 
                    i.Deleted != true);
                    
                if (invoice == null)
                {
                    throw new ApiException("未找到原发票信息");
                }
                
                if (!invoice.IsSupplementInvoice)
                {
                    throw new ApiException("该发票不是补充发票，请使用正确的接口发起退票");
                }
                if(invoice.RefundStatus != (int)EnumInvoiceRefundStatus.Normal)
                {
                    throw new ApiException("该发票已退票，不能重复退票");
                }
                
                // 获取OCR识别结果
                var recognition = DbOpe_crm_invoice_recognition.Instance.GetData(r => 
                    r.Id == request.RecognitionId && 
                    r.Deleted != true);
                
                if (recognition == null)
                {
                    throw new ApiException("未找到退票OCR识别记录");
                }
                // 验证退票金额是否大于原发票金额
                if(request.RefundAmount > invoice.TotalAmount)
                {
                    throw new ApiException("退票金额不能大于原发票金额");
                }
                
                string reviewId = string.Empty;
                string refundApplicationId = recognition.InvoiceApplicationId;
                // 使用事务创建退票申请
                DbOpe_crm_invoice_refund_application.Instance.TransDeal(() =>
                {
                    //这里需要判断是否是驳回的，驳回的话，需要更新之前的申请信息； 否则 新建申请信息
                    var currentApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(a => a.Id == refundApplicationId && a.Deleted != true);
                    if(currentApplication != null)
                    {
                        //是驳回的
                        var currentReview = DbOpe_crm_invoice_refund_review.Instance.GetData(r =>  r.RefundApplicationId == refundApplicationId && r.Deleted != true); 
                        if(currentReview.AuditStatus == (int)EnumRefundReviewStatus.RefundReviewRejected)
                        {
                            // 更新退票申请记录
                            currentApplication.InvoiceId = request.OriginalInvoiceId;
                            currentApplication.ApplyTime = DateTime.Now;
                            currentApplication.RefundReason = request.RefundReason;
                            currentApplication.RefundBackgroundRemark = request.RefundReason;
                            currentApplication.RefundAmount = request.RefundAmount;
                            currentApplication.AuditStatus = (int)EnumRefundApplicationStatus.Processed; // 修改为已处理状态，等待复核
                            currentApplication.UpdateDate = DateTime.Now;
                            currentApplication.UpdateUser = userId;
                            DbOpe_crm_invoice_refund_application.Instance.Update(currentApplication);
                            currentReview.Deleted = true;
                            currentReview.UpdateDate = DateTime.Now;
                            currentReview.UpdateUser = userId;
                            DbOpe_crm_invoice_refund_review.Instance.Update(currentReview);
                        }
                        else{

                            throw new ApiException("当前审核状态不允许提交");
                        }
                    }
                    else{
                        // 验证发票是否已有退票申请或已退票
                        var anyRefundApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => 
                            r.InvoiceId == request.OriginalInvoiceId && 
                            r.Deleted != true &&
                            r.AuditStatus != (int)EnumRefundApplicationStatus.Invalidated);
                            
                        if (anyRefundApplication != null)
                        {
                            // 根据申请状态给出不同的错误提示
                            if (anyRefundApplication.AuditStatus == (int)EnumRefundApplicationStatus.Applied ||
                                anyRefundApplication.AuditStatus == (int)EnumRefundApplicationStatus.Processed)
                            {
                                throw new ApiException("该补充发票已有待处理的退票申请，不能重复申请");
                            }
                            else if (anyRefundApplication.AuditStatus == (int)EnumRefundApplicationStatus.Completed)
                            {
                                throw new ApiException("该补充发票已完成退票，不能重复申请");
                            }
                            else if (anyRefundApplication.AuditStatus == (int)EnumRefundApplicationStatus.Rejected)
                            {
                                throw new ApiException("该补充发票的退票申请已被拒绝，不能重复申请");
                            }
                        }
                        // 创建退票申请记录
                        var refundInvoice = new Db_crm_invoice_refund_application
                        {
                            Id = refundApplicationId,
                            ContractId = invoice.ContractId, // 补充发票可能没有合同ID
                            InvoiceId = request.OriginalInvoiceId,
                            ApplicantId = userId,
                            ApplyTime = DateTime.Now,
                            RefundReason = request.RefundReason,
                            RefundBackgroundRemark = request.RefundReason,
                            AuditStatus = (int)EnumRefundApplicationStatus.Processed, // 修改为已处理状态，等待复核
                            RefundAmount = request.RefundAmount, 
                            Deleted = false,
                            CreateUser = userId,
                            CreateDate = DateTime.Now,
                            UpdateUser = userId,
                            UpdateDate = DateTime.Now
                        };
                        
                        // 插入退票申请记录
                        DbOpe_crm_invoice_refund_application.Instance.Insert(refundInvoice);

                    }

                    
                    
                    // 创建退票审核记录（直接到复核环节）
                    reviewId = Guid.NewGuid().ToString();

                    Db_crm_invoice_refund_review review = new Db_crm_invoice_refund_review
                    {
                        Id = reviewId,
                        RefundApplicationId = refundApplicationId,
                        ContractId = invoice.ContractId,
                        InvoiceId = invoice.Id,
                        RefundNumber = request.RefundInvoiceNumber, // 使用原发票号作为退票号
                        RefundAmount = request.RefundAmount, // 补充发票退票通常是全额退票
                        RefundDate = request.RefundDate,
                        InvoiceType = request.InvoiceType,
                        AuditStatus = (int)EnumRefundReviewStatus.WaitingAudit, // 待复核
                        ProcessorId = userId,
                        ProcessTime = DateTime.Now,
                        ProcessorRemark = "",
                        CreateUser = userId,
                        CreateDate = DateTime.Now,
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now,
                        Deleted = false
                    };
                    
                    // 保存审核记录
                    DbOpe_crm_invoice_refund_review.Instance.Insert(review);
                    
                    // 更新发票的退票状态 - 设置为Normal状态，通过退票申请的AuditStatus来追踪退票进度
                    // 此处不使用RefundStatus表示退票中，而是通过退票申请记录中的状态来控制
                    invoice.RefundStatus = (int)EnumInvoiceRefundStatus.Normal;
                    invoice.UpdateUser = userId;
                    invoice.UpdateDate = DateTime.Now;
                    DbOpe_crm_invoice.Instance.Update(invoice);

                    // 从recognition对象获取OCR识别结果
                    var ocrResult = new OcrInvoiceResult
                    {
                        RecognitionId = recognition.Id,
                        InvoiceCode = recognition.InvoiceCode,
                        InvoiceNumber = recognition.InvoiceNumber,
                        InvoiceDate = recognition.InvoiceDate?.ToString("yyyy-MM-dd"),
                        BuyerName = recognition.BuyerName,
                        SellerName = recognition.SellerName,
                        BuyerTaxNumber = recognition.BuyerTaxCode,
                        SellerTaxNumber = recognition.SellerTaxCode,
                        TotalAmount = recognition.InvoiceAmount ?? 0,
                        TotalTax = recognition.TaxAmount ?? 0,
                        TotalAmountWithTax = (recognition.TotalAmount ?? 0).ToString("F2"),
                        InvoiceDetails = recognition.InvoiceDetails,
                        OriginalText = recognition.RecognitionResult,
                        InvoiceType = recognition.InvoiceTypeString
                    };
                    
                    // 创建申请信息对象
                    var applicationInfo = new InvoiceApplicationInfo
                    {
                        Id = refundApplicationId,
                        ContractId = invoice.ContractId,
                        AppliedAmount = request.RefundAmount,
                        BillingType = (EnumBillingType)invoice.BillingType,
                        InvoiceType = (EnumInvoiceType)(request.InvoiceType == null ? invoice.InvoiceType : request.InvoiceType),
                        BillingCompany = invoice.BillingCompany,
                        BillingCompanyName = DbOpe_crm_collectingcompany.Instance.GetData(c => 
                            c.Id == invoice.BillingCompany && c.Deleted == false)?.SellerName ?? string.Empty,
                        ApplicantId = userId,
                        ApplyTime = DateTime.Now,
                        AuditStatus = EnumInvoiceApplicationStatus.Processed,
                        DisplayStatus = EnumInvoiceDisplayStatus.InvoiceProcessed,
                        BillingHeader = invoice.BillingHeader,
                        BillingHeaderName = invoice.BillingHeader,
                        CreditCode = invoice.CreditCode,
                        Recipient = invoice.Recipient,
                        Email = invoice.Email,
                        Remark = request.RefundReason,
                        ExpectedInvoicingDate = request.RefundDate,
                        InvoiceDetails = invoice.InvoicingDetails
                    };
                    
                    // 获取OCR记录ID
                    var ocrId = recognition.Id;

                    // 创建比对结果对象
                    var compareResult = new InvoiceOcrCompareResult
                    {
                        OcrInfo = ocrResult,
                        ApplicationInfo = applicationInfo,
                        ComparisonResults = new List<FieldComparisonResult>(),
                        IsMatch = false // 默认为false，后面根据比对结果设置
                    };

                    // 进行字段比对
                    CompareRefundFields(compareResult, invoice);

                    // 计算是否匹配
                    bool isMatch = true;
                    foreach (var fieldResult in compareResult.ComparisonResults)
                    {
                        // 如果任何一个必要字段不匹配，则整体不匹配
                        if (fieldResult.IsRequired && !fieldResult.IsMatch)
                        {
                            isMatch = false;
                            break;
                        }
                    }

                    compareResult.IsMatch = isMatch;
                    
                    // 保存比对结果
                    SaveRefundComparisonResult(ocrId, compareResult);
                    // 更新发票显示状态
                    UpdateInvoiceDisplayStatus(invoice.InvoiceApplicationId);

                    LogUtil.AddLog($"创建补充发票退票申请成功，ID: {refundApplicationId}，发票ID: {request.OriginalInvoiceId}");

                });
                
                return new SubmitSupplementInvoiceRefund_Out
                {
                    RefundId = refundApplicationId,
                    ReviewId = reviewId
                };
            }
            catch (ApiException aex)
            {
                LogUtil.AddErrorLog($"提交补充发票退票申请业务异常: {aex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提交补充发票退票申请系统异常: {ex.Message}");
                throw new ApiException("提交补充发票退票申请失败，请联系系统管理员");
            }
        }
        
        #endregion

        #region 补充发票作废业务逻辑

        /// <summary>
        /// 作废补充发票
        /// 1、如果发票已退票，则同时作废退票（退票申请也作废）
        /// 2、申请表作废，正式发票表软删除
        /// </summary>
        /// <param name="invoiceId">补充发票ID</param>
        /// <param name="invoiceBackgroundRemark">发票备注</param>
        /// <returns>作废结果</returns>
        public bool InvalidateSupplementInvoice(string invoiceId, string invoiceBackgroundRemark)
        {
           try
           {
               // 参数验证
               if (string.IsNullOrEmpty(invoiceId))
               {
                   throw new ApiException("补充发票ID不能为空");
               }
            
               if (string.IsNullOrEmpty(invoiceBackgroundRemark))
               {
                   throw new ApiException("发票备注不能为空");
               }
               // 获取补充发票信息
               var invoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == invoiceId 
               && i.Deleted != true && i.IsSupplementInvoice == true);
               if (invoice == null)
               {
                    throw new ApiException("未找到补充发票信息");
               }   
               if(invoice.TransactionType != 1)
               {
                    throw new ApiException("只有补充发票正数票才可以进行作废");
               }
               DbOpe_crm_invoice.Instance.TransDeal(() =>
               {
                    // 验证发票是否已退票
                    var refundInvoiceApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => r.InvoiceId == invoiceId && r.Deleted != true);
                    if(refundInvoiceApplication != null)
                    {
                            if(refundInvoiceApplication.AuditStatus == (int)EnumRefundApplicationStatus.Completed)
                            {
                                //软删除退票的发票记录
                                var refundInvoice = DbOpe_crm_invoice.Instance.GetData(i => i.InvoiceApplicationId == refundInvoiceApplication.Id && i.Deleted != true);
                                if(refundInvoice != null)
                                {
                                    refundInvoice.Deleted = true;
                                    refundInvoice.UpdateUser = TokenModel.Instance.id;
                                    refundInvoice.UpdateDate = DateTime.Now;
                                    refundInvoice.Remark = invoiceBackgroundRemark;
                                    DbOpe_crm_invoice.Instance.Update(refundInvoice);
                                }
                            }
                            // 作废退票申请
                            refundInvoiceApplication.AuditStatus = (int)EnumRefundApplicationStatus.Invalidated;
                            refundInvoiceApplication.UpdateUser = TokenModel.Instance.id;
                            refundInvoiceApplication.UpdateDate = DateTime.Now;
                            refundInvoiceApplication.RefundBackgroundRemark = invoiceBackgroundRemark;
                            DbOpe_crm_invoice_refund_application.Instance.Update(refundInvoiceApplication);
                    } 
                    // 软删除发票
                    invoice.Deleted = true;
                    invoice.UpdateUser = TokenModel.Instance.id;
                    invoice.UpdateDate = DateTime.Now;
                    invoice.Remark = invoiceBackgroundRemark;
                    DbOpe_crm_invoice.Instance.Update(invoice);
                    // 作废申请表
                    var invoiceApplication = DbOpe_crm_invoice_application.Instance.GetData(a => a.Id == invoice.InvoiceApplicationId && a.Deleted != true);
                    invoiceApplication.AuditStatus = (int)EnumInvoiceApplicationStatus.Invalidated;
                    invoiceApplication.UpdateUser = TokenModel.Instance.id;
                    invoiceApplication.UpdateDate = DateTime.Now;
                    invoiceApplication.InvoiceBackgroundRemark = invoiceBackgroundRemark;
                    DbOpe_crm_invoice_application.Instance.Update(invoiceApplication);
                    UpdateInvoiceDisplayStatus(invoice.InvoiceApplicationId);
               });
               return true;
           }
           catch (ApiException aex)
           {
               LogUtil.AddErrorLog($"作废补充发票业务异常: {aex.Message}");
               throw;
           }
           catch (Exception ex)
           {
               LogUtil.AddErrorLog($"作废补充发票系统异常: {ex.Message}");
               throw new ApiException("作废补充发票失败，请联系系统管理员");
           }
        }    
        #endregion
    }
} 