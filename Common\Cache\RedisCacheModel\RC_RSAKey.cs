﻿namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class RSAKey
        {
            const string RSAKEY = "rsaKey_";
            /// <summary>
            /// 判断当前用户是否存在Rsa私钥缓存
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            /// <returns></returns>
            public static bool CheckRSAKey(string userId, bool IsMobile)
            {
                var key = RSAKEY + userId + (IsMobile ? "_Mobile" : "");
                return RedisHelper.Exists(key);
            }
            /// <summary>
            /// 获取当前用户的RSA私钥
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            /// <returns></returns>
            public static string GetPrivateKey(string userId, bool IsMobile)
            {
                var key = RSAKEY + userId + (IsMobile ? "_Mobile" : "");
                return RedisHelper.HGet(key, "privateKey");
            }
            /// <summary>
            /// 获取当前用户的RSA公钥
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            /// <returns></returns>
            public static string GetPublicKey(string userId, bool IsMobile)
            {
                var key = RSAKEY + userId + (IsMobile ? "_Mobile" : "");
                return RedisHelper.HGet(key, "publicKey");
            }
            /// <summary>
            /// 保存用户的Rsa私钥
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            /// <param name="publicKey"></param>
            /// <param name="privateKey"></param>
            public static void SavePrivateKey(string userId, bool IsMobile, string publicKey, string privateKey)
            {
                var key = RSAKEY + userId + (IsMobile ? "_Mobile" : "");
                RedisHelper.HMSet(key, "publicKey", publicKey, "privateKey",privateKey);
                //RedisHelper.Expire(key, TimeSpan.FromMinutes(60));
            }
        }

    }
}
