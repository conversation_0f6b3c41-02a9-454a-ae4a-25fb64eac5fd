﻿using System.IO;
using Spire.Doc;

namespace CRM2_API.BLL.Common
{
    public class SpireDocCreateDoc
    {
        public static Stream SpireDocCreateDocStream(Stream stream, List<TemplateField> item)
        {
            Document document = new Document();
            document.LoadFromStream(stream, FileFormat.Docx);

            foreach (TemplateField field in item)
            {
                document.Replace(field.FieldName, field.FieldValue, false, false);
            }

            Stream streamDocx = new MemoryStream();
            document.SaveToStream(streamDocx, FileFormat.Docx);
            return streamDocx;
        }
    }
}
