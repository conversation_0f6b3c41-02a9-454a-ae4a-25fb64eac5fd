﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///系统消息提醒已接收表
    ///</summary>
    [SugarTable("sys_messagereminder_received")]
    public class Db_sys_messagereminder_received
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:系统消息提醒类型表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string MessageReminderTypeId {get;set;}

           /// <summary>
           /// Desc:待办表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string WorkflowPendingId {get;set;}

           /// <summary>
           /// Desc:从属表主键
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string SubjectId {get;set;}

           /// <summary>
           /// Desc:接收人id(用户id)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? State {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
