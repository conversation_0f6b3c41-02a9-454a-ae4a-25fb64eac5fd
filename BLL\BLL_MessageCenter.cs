﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.RemindInfo;
using CRM2_API.BLL.WorkLog;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModel.Gtis;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using JiebaNet.Segmenter.Common;
using Microsoft.AspNetCore.Http;
using Namotion.Reflection;
using SqlSugar;
using System.Net.Http;
using System.Threading.Tasks;
using CRM2_API.Common.DingTalk;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using LgyUtil;
using Taobao.Top.Link.Endpoints;
using static CRM2_API.Common.DingTalk.DingTalk;
using System.Security.Policy;
using System.Web;
using System.Threading;

namespace CRM2_API.BLL
{
    public class BLL_MessageCenter : BaseBLL<BLL_MessageCenter>
    {
        public void InsertMessageCenterDetail(MessageCenterDetail_In messageCenterDetail_In)
        {
            //1.主表插入记录
            DbOpe_sys_messagecenterdetail.Instance.InsertMessageCenterDetail(messageCenterDetail_In);

            //2.检查是消息中心表记录，
            //如果当前为最新则删除旧的，插入当前，并计算消息数量

        }

        /// <summary>
        /// 插入消息记录
        /// </summary>
        /// <param name="mainInfo">主要内容</param>
        /// <param name="enumMessageLocalType">类型</param>
        /// <param name="enumMessageStep">具体步骤</param>
        /// <param name="enumMessageState">步骤状态</param>
        /// <param name="contractId">默认为空,当且仅当插入服务相干信息时使用，作为跳转的第二个参数</param>
        public MessageGiveBack InsertMessageCenterDetail(MessageMainInfo mainInfo, EnumMessageLocalType enumMessageLocalType, EnumMessageStepInfo enumMessageStep, EnumMessageStateInfo enumMessageState, string contractId = null)
        {
            //增加忽略，忽略期内不增加新的消息
            if (DbOpe_sys_message_ignore.Instance.CheckIsIgnore(mainInfo.Issuer, mainInfo.MessageTypeToId, enumMessageLocalType))
            {
                return new MessageGiveBack { MessageId = Guid.Empty.ToString(), IsRealTimeSend = false };
            }

            //2024年12月16日 修改 初始传入跳转链接及参数 

            //到账复核通过通知确认业绩
            MessageCenterDetail_In messageCenterDetail_In = new MessageCenterDetail_In();
            messageCenterDetail_In.MessageTypeToId = mainInfo.MessageTypeToId;
            messageCenterDetail_In.UserID = mainInfo.Issuer;
            messageCenterDetail_In.MessageLocalType = enumMessageLocalType;
            messageCenterDetail_In.CreateUser = UserId;

            messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + enumMessageStep.GetEnumDescription() + enumMessageState.GetEnumDescription();

            //2025年3月18日 增加审核信息 仅拒绝信息
            messageCenterDetail_In.LocalFeedBack = mainInfo.LocalFeedBack;

            bool realTimeSend = true;

            //获取基本跳转链接及参数
            var path = DbOpe_sys_message_typepath.Instance.GetMessageTypeOptionsList();


            switch (enumMessageLocalType)
            {
                case EnumMessageLocalType.SignCustomerNearlyRelease:
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName);
                    realTimeSend = false;
                    break;
                case EnumMessageLocalType.SaveCustomerNearlyRelease:
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName);
                    realTimeSend = false;
                    break;
                case EnumMessageLocalType.ServiceNearlyEnd:
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName);
                    realTimeSend = false;
                    break;
                case EnumMessageLocalType.ContractAbout:
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + enumMessageStep.GetEnumDescription() + enumMessageState.GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = EnumMessageLocalType.ContractAbout.GetEnumDescription() + enumMessageStep.GetEnumDescription() + enumMessageState.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(messageCenterDetail_In.MessageParms.ToLower().Contains("id") ? messageCenterDetail_In.MessageParms + "" + messageCenterDetail_In.MessageTypeToId + "" : messageCenterDetail_In.MessageParms);
                    break;
                case EnumMessageLocalType.ServiceAbout:
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + enumMessageStep.GetEnumDescription() + enumMessageState.GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = EnumMessageLocalType.ServiceAbout.GetEnumDescription() + enumMessageStep.GetEnumDescription() + enumMessageState.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = GetServiceAboutPath(enumMessageStep);
                    messageCenterDetail_In.MessageParms = GetServiceAboutParms(messageCenterDetail_In.MessageTypeToId, contractId);
                    break;
                case EnumMessageLocalType.AchievementAbout:
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + EnumMessageLocalType.AchievementAbout.GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = enumMessageStep.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(messageCenterDetail_In.MessageParms.ToLower().Contains("id") ? messageCenterDetail_In.MessageParms + "" + messageCenterDetail_In.MessageTypeToId + "" : messageCenterDetail_In.MessageParms);
                    break;
                case EnumMessageLocalType.InvoiceAbout:
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + EnumMessageLocalType.InvoiceAbout.GetEnumDescription() + enumMessageState.GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = EnumMessageLocalType.InvoiceAbout.GetEnumDescription() + enumMessageStep.GetEnumDescription() + enumMessageState.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(messageCenterDetail_In.MessageParms.ToLower().Contains("id") ? messageCenterDetail_In.MessageParms + "" + messageCenterDetail_In.MessageTypeToId + "" : messageCenterDetail_In.MessageParms);
                    break;
                case EnumMessageLocalType.NoHaveServiceAppl:
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(messageCenterDetail_In.MessageParms.ToLower().Contains("id") ? messageCenterDetail_In.MessageParms + "" + messageCenterDetail_In.MessageTypeToId + "" : messageCenterDetail_In.MessageParms);
                    realTimeSend = false;
                    break;

                case EnumMessageLocalType.ContractNeedService://2025年4月21日 新增
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes + ((int)enumMessageLocalType).ToEnum<EnumMessageLocalTypeShort>().GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(messageCenterDetail_In.MessageParms.ToLower().Contains("id") ? messageCenterDetail_In.MessageParms + "" + messageCenterDetail_In.MessageTypeToId + "" : messageCenterDetail_In.MessageParms);

                    break;
                case EnumMessageLocalType.TradeleadsAbout://2025年5月15日 新增
                    messageCenterDetail_In.MessageDetail = "您有一条标题为" + mainInfo.MessagemMainAboutDes + "供求信息" + enumMessageState.GetEnumDescription();
                    messageCenterDetail_In.MessageTitle = enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(messageCenterDetail_In.MessageParms.ToLower().Contains("id") ? messageCenterDetail_In.MessageParms + "" + messageCenterDetail_In.MessageTypeToId + "" : messageCenterDetail_In.MessageParms);
                    realTimeSend = false;
                    break;
                case EnumMessageLocalType.ServiceExceptionRemind_Handing://2025年5月29日 新增
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes;
                    messageCenterDetail_In.MessageTitle = enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(messageCenterDetail_In.MessageParms.ToLower().Contains("id") ? messageCenterDetail_In.MessageParms + "" + messageCenterDetail_In.MessageTypeToId + "" : messageCenterDetail_In.MessageParms);
                    break;
                case EnumMessageLocalType.ServiceExceptionRemind_Copy://2025年5月29日 新增
                    messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes;
                    messageCenterDetail_In.MessageTitle = enumMessageLocalType.GetEnumDescription();
                    messageCenterDetail_In.MessageToPath = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageToPath;
                    messageCenterDetail_In.MessageParms = path.Where(e => e.Type == (int)enumMessageLocalType).First().MessageFormName;
                    messageCenterDetail_In.MessageParms = DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(messageCenterDetail_In.MessageParms.ToLower().Contains("id") ? messageCenterDetail_In.MessageParms + "" + messageCenterDetail_In.MessageTypeToId + "" : messageCenterDetail_In.MessageParms);
                    break;
                default:
                    break;

            }
            string _messageId = DbOpe_sys_messagecenterdetail.Instance.InsertMessageCenterDetail(messageCenterDetail_In);

            MessageGiveBack messageGiveBack = new MessageGiveBack();
            messageGiveBack.MessageId = _messageId;
            messageGiveBack.IsRealTimeSend = realTimeSend;
            return messageGiveBack;


        }


        public MessageGiveBack InsertSysOrderMessageCenterDetail(MessageMainInfo mainInfo)
        {

            //2024年12月16日 修改 初始传入跳转链接及参数 

            //到账复核通过通知确认业绩
            MessageCenterDetail_In messageCenterDetail_In = new MessageCenterDetail_In();
            messageCenterDetail_In.MessageTypeToId = mainInfo.MessageTypeToId;
            messageCenterDetail_In.UserID = mainInfo.Issuer;
            messageCenterDetail_In.MessageLocalType = EnumMessageLocalType.System;
            messageCenterDetail_In.CreateUser = UserId;

            messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes;


            bool realTimeSend = true;


            messageCenterDetail_In.MessageDetail = mainInfo.MessagemMainAboutDes ;
            messageCenterDetail_In.MessageTitle = "系统通知";

            realTimeSend = false;

            string _messageId = DbOpe_sys_messagecenterdetail.Instance.InsertMessageCenterDetail(messageCenterDetail_In);

            MessageGiveBack messageGiveBack = new MessageGiveBack();
            messageGiveBack.MessageId = _messageId;
            messageGiveBack.IsRealTimeSend = realTimeSend;
            return messageGiveBack;


        }


        public async Task RealTimeSend(MessageGiveBack messageGiveBack)
        {
            if (messageGiveBack.IsRealTimeSend)
            {
                //调用G5发送消息接口 注意 服务过期等三个不直接发送消息
                var GetSendData = DbOpe_sys_messagecenterdetail.Instance.FindData(messageGiveBack.MessageId);
                if (GetSendData != null)
                {
                    // 并行执行两个异步任务
                    var dingTalkTask = sendDingTalkMessage(GetSendData.MessageType.Value.ToEnum<EnumMessageCenterType>(),
                        messageGiveBack.MessageId, GetSendData.MessageVXPath);

                    var gzhTask = sendGZHMessage(GetSendData.MessageVXPath);

                    // 等待两个任务完成
                    await Task.WhenAll(dingTalkTask, gzhTask);
                }
            }
        }

        public void AddMessageCenter()
        {

            //var SignCustomerNearlyRelease = DbOpe_sys_messagecenterdetail.Instance.ProtectNearlyReleaseQueryNotByUserId(EnumRemindType.SignCustomerNearlyRelease)
            //    .Select(it => new
            //    {
            //        Issuer = it.UserId,
            //        MessageTypeToId = it.CustomerId,
            //        MessagemMainAboutDes = it.CustomerName,
            //    }).ToList();
            //SignCustomerNearlyRelease.ForEach(it =>
            //{
            //    MessageMainInfo message = new MessageMainInfo();
            //    message.Issuer = it.Issuer;
            //    message.MessageTypeToId = it.MessageTypeToId;
            //    message.MessagemMainAboutDes = it.MessagemMainAboutDes;
            //    BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.SignCustomerNearlyRelease, EnumMessageStepInfo.Release, EnumMessageStateInfo.NearlyRelease);
            //});

            var SignCustomerNearlyReleaseCount = DbOpe_sys_messagecenterdetail.Instance.ProtectNearlyReleaseQueryNotByUserId(EnumRemindType.SignCustomerNearlyRelease)
                                                                                        .GroupBy(it => it.UserId)
                                                                                        .Select(it => new
                                                                                        {
                                                                                            Issuer = it.UserId,
                                                                                            MessagemMainAboutDes = SqlFunc.AggregateCount(it.CustomerId),
                                                                                        }).ToList();
            SignCustomerNearlyReleaseCount.ForEach(it =>
            {
                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = it.Issuer;
                message.MessageTypeToId = "";
                message.MessagemMainAboutDes = "您有" + it.MessagemMainAboutDes + "个";
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.SignCustomerNearlyRelease, EnumMessageStepInfo.Release, EnumMessageStateInfo.NearlyRelease);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            });


            //var SaveCustomerNearlyRelease = DbOpe_sys_messagecenterdetail.Instance.ProtectNearlyReleaseQueryNotByUserId(EnumRemindType.SaveCustomerNearlyRelease)
            //    .Select(it => new
            //    {
            //        Issuer = it.UserId,
            //        MessageTypeToId = it.CustomerId,
            //        MessagemMainAboutDes = it.CustomerName,
            //    }).ToList();
            //SaveCustomerNearlyRelease.ForEach(it =>
            //{
            //    MessageMainInfo message = new MessageMainInfo();
            //    message.Issuer = it.Issuer;
            //    message.MessageTypeToId = it.MessageTypeToId;
            //    message.MessagemMainAboutDes = it.MessagemMainAboutDes;
            //    BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.SaveCustomerNearlyRelease, EnumMessageStepInfo.Release, EnumMessageStateInfo.NearlyRelease);
            //});

            var SaveCustomerNearlyReleaseCount = DbOpe_sys_messagecenterdetail.Instance.ProtectNearlyReleaseQueryNotByUserId(EnumRemindType.SaveCustomerNearlyRelease)
                                                                                        .GroupBy(it => it.UserId)
                                                                                        .Select(it => new
                                                                                        {
                                                                                            Issuer = it.UserId,
                                                                                            MessagemMainAboutDes = SqlFunc.AggregateCount(it.CustomerId),
                                                                                        }).ToList();
            SaveCustomerNearlyReleaseCount.ForEach(it =>
            {
                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = it.Issuer;
                message.MessageTypeToId = "";
                message.MessagemMainAboutDes = "您有" + it.MessagemMainAboutDes + "个";
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.SaveCustomerNearlyRelease, EnumMessageStepInfo.Release, EnumMessageStateInfo.NearlyRelease);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            });

            //var ServiceNearlyEnd = DbOpe_sys_messagecenterdetail.Instance.ProtectNearlyReleaseQueryNotByUserId(EnumRemindType.ServiceNearlyEnd)
            //    .Select(it => new
            //    {
            //        Issuer = it.UserId,
            //        MessageTypeToId = it.CustomerId,
            //        MessagemMainAboutDes = it.CustomerName,
            //    }).ToList();
            //ServiceNearlyEnd.ForEach(it =>
            //{
            //    MessageMainInfo message = new MessageMainInfo();
            //    message.Issuer = it.Issuer;
            //    message.MessageTypeToId = it.MessageTypeToId;
            //    message.MessagemMainAboutDes = it.MessagemMainAboutDes;
            //    BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceNearlyEnd, EnumMessageStepInfo.Release, EnumMessageStateInfo.NearlyRelease);
            //});
            var ServiceNearlyEndCount = DbOpe_sys_messagecenterdetail.Instance.ProtectNearlyReleaseQueryNotByUserId(EnumRemindType.ServiceNearlyEnd)
                                                                                .GroupBy(it => it.UserId)
                                                                                .Select(it => new
                                                                                {
                                                                                    Issuer = it.UserId,
                                                                                    MessagemMainAboutDes = SqlFunc.AggregateCount(it.CustomerId),
                                                                                }).ToList();
            ServiceNearlyEndCount.ForEach(it =>
            {
                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = it.Issuer;
                message.MessageTypeToId = "";
                message.MessagemMainAboutDes = "您有" + it.MessagemMainAboutDes + "个";
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceNearlyEnd, EnumMessageStepInfo.Release, EnumMessageStateInfo.NearlyRelease);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            });


            var NoServiceContractApplCount = DbOpe_sys_messagecenterdetail.Instance.NoServiceApplQueryByUserId()
                                                                          .GroupBy(it => it.Issuer)
                                                                          .Select(it => new
                                                                          {
                                                                              Issuer = it.Issuer,
                                                                              MessagemMainAboutDes = SqlFunc.AggregateCount(it.ContractId),
                                                                          }).ToList();

            NoServiceContractApplCount.ForEach(it =>
            {
                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = it.Issuer;
                message.MessageTypeToId = "";
                message.MessagemMainAboutDes = "您有" + it.MessagemMainAboutDes + "个";
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.NoHaveServiceAppl, EnumMessageStepInfo.NoAppl, EnumMessageStateInfo.NoAppl);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            });

            DbOpe_sys_messagecenter.Instance.UpdateTodoMessageCount();
            DbOpe_sys_messagecenter.Instance.SaveQueues();
        }


        public async Task sendGZHMessage(string message)
        {
            // 默认的超时时间
            int timeoutSeconds = 30;
            try
            {
                // 记录开始发送消息的日志
                LogUtil.AddLog($"开始发送公众号消息: {message.Substring(0, Math.Min(message.Length, 100))}...");

                string url = AppSettings.WeChatConfig.message_url;
                string localid = AppSettings.WeChatConfig.message_id;

                using (HttpClient client = new HttpClient())
                {
                    // 设置超时时间
                    client.Timeout = TimeSpan.FromSeconds(timeoutSeconds);

                    MessageModel messageModel = message.DeserializeNewtonJson<MessageModel>();

                    if (messageModel == null)
                    {
                        LogUtil.AddLog($"公众号消息反序列化失败，消息内容无效: {message}");
                        return;
                    }

                    // 准备要发送的数据
                    var postData = new FormUrlEncodedContent(new[]
                    {
                        new KeyValuePair<string, string>("openid", messageModel.GZHOpenId ?? string.Empty),
                        new KeyValuePair<string, string>("title", "1"),
                        new KeyValuePair<string, string>("cname", messageModel.GZHTitle ?? string.Empty),
                        new KeyValuePair<string, string>("phone", messageModel.MessageTypeDesc ?? string.Empty),
                        new KeyValuePair<string, string>("mark", messageModel.Remark ?? string.Empty),
                        new KeyValuePair<string, string>("linkurl", messageModel.LinkUri ?? string.Empty),
                        new KeyValuePair<string, string>("times", messageModel.CreateDate ?? string.Empty),
                        new KeyValuePair<string, string>("id", localid)
                    });

                    // 记录API调用
                    LogUtil.AddLog($"调用公众号消息API: {url}");

                    // 设置取消令牌，确保任务在超时后可以被取消
                    using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds)))
                    {
                        // 发送请求
                        HttpResponseMessage response = await client.PostAsync(url, postData, cts.Token);

                        // 检查响应状态
                        if (response.IsSuccessStatusCode)
                        {
                            LogUtil.AddLog($"公众号消息发送成功，响应: {response.ReasonPhrase}");
                            // 成功后等待一段时间，避免请求过于频繁
                            await Task.Delay(2000);
                        }
                        else
                        {
                            string responseContent = await response.Content.ReadAsStringAsync();
                            LogUtil.AddLog($"公众号消息发送失败，状态码: {response.StatusCode}，响应内容: {responseContent}");
                        }
                    }
                }
            }
            catch (TaskCanceledException ex)
            {
                LogUtil.AddLog($"公众号消息发送超时 ({timeoutSeconds}秒): {ex.Message}", ex);
            }
            catch (HttpRequestException ex)
            {
                LogUtil.AddLog($"公众号消息HTTP请求异常: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"公众号消息发送发生未知异常: {ex.Message}", ex);
            }
        }


        public async Task sendDingTalkMessage(EnumMessageCenterType centerType, string msgId, string message)
        {
            try
            {
                LogUtil.AddLog($"开始发送钉钉消息，类型: {centerType}, 消息ID: {msgId}");

                // 设置超时时间为30秒
                int timeoutSeconds = 30;
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds)))
                {
                    // 添加取消令牌支持
                    var cancellationToken = cts.Token;

                    MessageModel messageModel = message.DeserializeNewtonJson<MessageModel>();
                    if (messageModel == null)
                    {
                        LogUtil.AddLog($"钉钉消息反序列化失败，消息内容无效: {message}");
                        return;
                    }

                    // 验证必要的参数
                    if (string.IsNullOrEmpty(messageModel.DingTalkUserId) || string.IsNullOrEmpty(messageModel.DingTalkUnionId))
                    {
                        LogUtil.AddLog($"钉钉用户ID或UnionID为空，无法发送消息，消息ID: {msgId}");
                        return;
                    }

                    // 根据消息类型处理
                    if (centerType == EnumMessageCenterType.Todo)
                    {
                        try
                        {
                            LogUtil.AddLog($"发送钉钉待办任务, 用户ID: {messageModel.DingTalkUserId}");

                            TodoTaskUrlModel urlModel = new TodoTaskUrlModel
                            {
                                pcUrl = messageModel.LinkUri,
                                appUrl = messageModel.LinkUri
                            };

                            // 使用Task.Run创建可取消的任务
                            var taskIdTask = Task.Run(() => CRM2_API.Common.DingTalk.DingTalk.CreateTodoTask(
                                messageModel.DingTalkUserId,
                                msgId,
                                messageModel.DingTalkTitle ?? string.Empty,
                                messageModel.MessageTypeDesc ?? string.Empty,
                                messageModel.CreateDate.ToDateTime("yyyy-MM-dd HH:mm"),
                                urlModel,
                                20,
                                messageModel.DingTalkUnionId
                            ), cancellationToken);

                            // 等待任务完成或超时
                            if (await Task.WhenAny(taskIdTask, Task.Delay(timeoutSeconds * 1000, cancellationToken)) == taskIdTask)
                            {
                                string taskId = await taskIdTask;

                                if (!string.IsNullOrEmpty(taskId))
                                {
                                    // 更新消息状态
                                    await Task.Run(() => DbOpe_sys_messagecenterdetail.Instance.UpdateDingMsgState(msgId, taskId), cancellationToken);
                                    LogUtil.AddLog($"钉钉待办任务创建成功，任务ID: {taskId}");
                                }
                                else
                                {
                                    LogUtil.AddLog($"钉钉待办任务创建返回空ID，可能失败");
                                }
                            }
                            else
                            {
                                LogUtil.AddLog($"钉钉待办任务创建超时 ({timeoutSeconds}秒)");
                                // 取消任务
                                cts.Cancel();
                            }
                        }
                        catch (Exception ex)
                        {
                            LogUtil.AddLog($"创建钉钉待办任务异常: {ex.Message}", ex);
                        }
                    }
                    else if (centerType == EnumMessageCenterType.Notice)
                    {
                        try
                        {
                            LogUtil.AddLog($"发送钉钉通知消息, 用户ID: {messageModel.DingTalkUserId}");

                            string DingcardTemId = string.Empty;
                            // 使用Task.Run创建可取消的任务获取模板ID
                            var getTemplateTask = Task.Run(() =>
                            {
                                var GetTempId = DbOpe_sys_dictionary.Instance.GetDictionaryByParentId("f6cd8a10-6a86-4cf2-a5d9-b0de0c3a3614");
                                return GetTempId.Count > 0 ? GetTempId.FirstOrDefault().Value : "816d316c-759f-409f-8e86-8d1bd30b090b.schema";
                            }, cancellationToken);

                            // 等待获取模板ID或超时
                            if (await Task.WhenAny(getTemplateTask, Task.Delay(timeoutSeconds * 1000 / 2, cancellationToken)) == getTemplateTask)
                            {
                                DingcardTemId = await getTemplateTask;
                            }
                            else
                            {
                                LogUtil.AddLog($"获取钉钉卡片模板ID超时，使用默认值");
                                DingcardTemId = "816d316c-759f-409f-8e86-8d1bd30b090b.schema";
                            }

                            InfoCardModel data = new InfoCardModel
                            {
                                title = messageModel.DingTalkTitle ?? string.Empty,
                                type = messageModel.DingTalkMessageTypeDesc ?? string.Empty,
                                data1 = messageModel.MessageDetail ?? string.Empty,
                                cont = messageModel.MessageDetail ?? string.Empty,
                                data2 = messageModel.DingTalkRemark.IsNullOrEmpty() ? (messageModel.Remark ?? string.Empty) : messageModel.DingTalkRemark,
                                createTime = messageModel.CreateDate ?? string.Empty,
                                urlmobile = messageModel.LinkUri ?? string.Empty,
                                url = !string.IsNullOrEmpty(messageModel.PCLinkUri) ?
                                      "dingtalk://dingtalkclient/action/openapp?corpid=ding6ac5463ef32c5776bc961a6cb783455b&container_type=work_platform&app_id=dinghlwojv7p62jtlmh9&redirect_type=jump&redirect_url=" +
                                      HttpUtility.UrlEncode(messageModel.PCLinkUri) : string.Empty,
                            };

                            // 使用Task.Run创建可取消的任务
                            var createCardTask = Task.Run(() => CRM2_API.Common.DingTalk.DingTalk.CreateInfoCard(
                                messageModel.DingTalkUserId,
                                DingcardTemId,
                                data
                            ), cancellationToken);

                            // 等待任务完成或超时
                            if (await Task.WhenAny(createCardTask, Task.Delay(timeoutSeconds * 1000, cancellationToken)) == createCardTask)
                            {
                                await createCardTask;
                                LogUtil.AddLog($"钉钉通知卡片创建成功");
                            }
                            else
                            {
                                LogUtil.AddLog($"钉钉通知卡片创建超时 ({timeoutSeconds}秒)");
                                // 取消任务
                                cts.Cancel();
                            }
                        }
                        catch (Exception ex)
                        {
                            LogUtil.AddLog($"创建钉钉通知卡片异常: {ex.Message}", ex);
                        }
                    }
                    else
                    {
                        LogUtil.AddLog($"不支持的钉钉消息类型: {centerType}");
                    }
                }
            }
            catch (TaskCanceledException ex)
            {
                LogUtil.AddLog($"钉钉消息发送被取消或超时: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"钉钉消息发送发生未知异常: {ex.Message}", ex);
            }
        }

        public List<RemindInfoOut> GetRemindInfosNoServiceAppl()
        {
            List<RemindInfoOut> remindInfoOuts = new List<RemindInfoOut>();
            var AchivementToConfirm = DbOpe_sys_messagecenterdetail.Instance.NoServiceApplQueryByUserId()
                .Where(it => it.Issuer == UserId)
                .Select(it => new ApiDictionary() { Value = it.ContractId, Name = it.ContractName }).ToList();

            if (AchivementToConfirm.Count > 0)
            {
                remindInfoOuts.Add(new RemindInfoOut()
                {
                    RemindDataNum = AchivementToConfirm.Count,
                    RemindDataIdAndNames = AchivementToConfirm,
                    RemindTypeDetail = EnumRemindType.ServiceNeedApply.GetEnumDescription(),
                    MaxShowNameNum = 5,
                    RemindNumUnit = "个",
                    RemindToPath = "/customer/serviceProducts?EnumRemindType=15",
                    RemindDataIdDbName = "Id",
                    RemindType = EnumRemindType.ServiceNeedApply
                });
            }
            return remindInfoOuts;
        }


        public string GetServiceAboutPath(EnumMessageStepInfo enumMessageStep)
        {
            string result = string.Empty;
            switch (enumMessageStep)
            {
                case EnumMessageStepInfo.GtisService:
                    result = "customServiceGtisDetail";
                    break;
                case EnumMessageStepInfo.DBService:
                    result = "customServiceDBDetail";
                    break;
                case EnumMessageStepInfo.SearchService:
                    result = "customServiceGlobalDetail";
                    break;
                case EnumMessageStepInfo.CollegeService:
                    result = "customServiceHsCollegeDetail";
                    break;
                case EnumMessageStepInfo.OtherService:
                    result = "customServiceOtherDetail";
                    break;
                default:
                    break;
            }
            return result;
        }

        public string GetServiceAboutParms(string messageTypeToId, string contractId)
        {
            string returnParms = "appleId:" + messageTypeToId + ",contractId:" + contractId;

            return DbOpe_sys_messagecenterdetail.Instance.MainPageJsonPrams(returnParms);
        }

        public void ScheduleSendGZHMessage()
        {
            try
            {
                LogUtil.AddLog("开始执行ScheduleSendGZHMessage");
                //调用G5发送消息接口 注意 服务过期等三个不直接发送消息
                var GetSendData = DbOpe_sys_messagecenter.Instance.FindToDoData();
                LogUtil.AddLog($"ScheduleSendGZHMessage获取到{GetSendData.Count}条待发送数据");

                if (GetSendData.Count > 0)
                {
                    // 使用正确的异步方式处理数据
                    foreach (var s in GetSendData)
                    {
                        try
                        {
                            // 同步执行，避免异步处理问题
                            sendDingTalkMessage(s.MessageType.Value.ToEnum<EnumMessageCenterType>(), s.Id, s.MessageVXPath).Wait();
                            sendGZHMessage(s.MessageVXPath).Wait();
                            LogUtil.AddLog($"成功发送消息ID: {s.Id}");
                        }
                        catch (Exception ex)
                        {
                            LogUtil.AddLog($"发送消息失败，ID: {s.Id}, 错误: {ex.Message}");
                            // 继续处理下一条，不让单个消息的失败影响整体
                        }
                    }
                    LogUtil.AddLog("ScheduleSendGZHMessage执行完成");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"ScheduleSendGZHMessage执行异常: {ex.Message}", ex);
            }
        }

        public void ScheduleSendSystemGZHMessage()
        {
            try
            {
                LogUtil.AddLog("开始执行ScheduleSendGZHMessage");
                //调用G5发送消息接口 注意 服务过期等三个不直接发送消息
                var GetSendData = DbOpe_sys_messagecenter.Instance.FindSystemSendData();
                LogUtil.AddLog($"ScheduleSendGZHMessage获取到{GetSendData.Count}条待发送数据");

                if (GetSendData.Count > 0)
                {
                    // 使用正确的异步方式处理数据
                    foreach (var s in GetSendData)
                    {
                        try
                        {
                            // 同步执行，避免异步处理问题
                            sendDingTalkMessage(s.MessageType.Value.ToEnum<EnumMessageCenterType>(), s.Id, s.MessageVXPath).Wait();
                            sendGZHMessage(s.MessageVXPath).Wait();
                            LogUtil.AddLog($"成功发送消息ID: {s.Id}");
                        }
                        catch (Exception ex)
                        {
                            LogUtil.AddLog($"发送消息失败，ID: {s.Id}, 错误: {ex.Message}");
                            // 继续处理下一条，不让单个消息的失败影响整体
                        }
                    }
                    LogUtil.AddLog("ScheduleSendGZHMessage执行完成");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"ScheduleSendGZHMessage执行异常: {ex.Message}", ex);
            }
        }
    }
}