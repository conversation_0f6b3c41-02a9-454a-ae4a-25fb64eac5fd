using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BLLModel.InvoiceSystem;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CRM2_API.DAL.DbModelOpe.Crm2;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_Collectioninfo;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;
using CRM2_API.Model.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System.Data;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using SqlSugar;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 测试数据控制器 - 用于准备测试数据
    /// </summary>
    [Description("测试数据控制器")]
    public class TestDataController : MyControllerBase
    {
        public TestDataController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }


        [HttpGet]
            [SkipRightCheck]
    [SkipIPCheck]
    [SkipRecordLog]
    [SkipAuthCheck]
    [SkipRSAKey]
        public void TestTran()
        {
            BLL_TestData.Instance.TestImprovedTran();
        }

        /// <summary>
        /// 比较两个视图的数据差异
        /// </summary>
        /// <remarks>
        /// 该方法比较v_searchcontractreceiptregister_sta和v_searchcontractreceiptregisterlist_join两个视图的数据差异。
        /// 查找两个视图中数据不一致的记录，并显示具体哪些列存在差异。
        /// </remarks>
        /// <param name="maxDiffRecords">最大返回的差异记录数量</param>
        /// <returns>两个视图的数据差异</returns>
        [HttpGet]
        [SkipRightCheck]
        [SkipIPCheck]
        [SkipRecordLog]
        [SkipAuthCheck]
        [SkipRSAKey]
        public IActionResult CompareViewsData(int maxDiffRecords = 100)
        {
            try
            {
                // 获取数据库连接
                var db = DbContext.Crm2Db;

                // 步骤1: 一次性获取视图1的有效数据
                var data1 = db.Queryable<Db_v_searchcontractreceiptregister_sta>()
                    .Where(v => v.State != null && v.State != 10)
                    .ToList();

                // 步骤2: 一次性获取视图2的所有数据
                var data2 = db.Queryable<Db_v_searchcontractreceiptregisterlist_join>()
                    .ToList();

                // 统计数据
                int view1Count = data1.Count;
                int view2Count = data2.Count;

                // 步骤3: 创建快速查询索引
                var data2Dict = new Dictionary<string, Db_v_searchcontractreceiptregisterlist_join>();
                var data2NullIdList = new List<Db_v_searchcontractreceiptregisterlist_join>();

                foreach (var item in data2)
                {
                    if (item.Id != null)
                    {
                        // 对于有ID的记录，使用ID作为索引
                        data2Dict[item.Id] = item;
                    }
                    else if (item.ContractId != null)
                    {
                        // 对于ID为null但ContractId不为null的记录，添加到特殊列表
                        data2NullIdList.Add(item);
                    }
                }

                // 步骤4: 查找只在视图1中存在的记录和共有但不同的记录
                var onlyInView1 = new List<Db_v_searchcontractreceiptregister_sta>();
                var differingRecords = new List<object>();

                foreach (var item1 in data1)
                {
                    bool found = false;
                    
                    if (item1.Id != null)
                    {
                        // 对于有ID的记录，检查在视图2中是否存在
                        if (data2Dict.TryGetValue(item1.Id, out var item2))
                        {
                            found = true;
                            // 比较所有属性是否有差异
                            var differences = CompareProperties(item1, item2);
                            if (differences.Count > 0 && differingRecords.Count < maxDiffRecords)
                            {
                                differingRecords.Add(new
                                {
                                    Id = item1.Id,
                                    ContractId = item1.ContractId,
                                    Differences = differences
                                });
                            }
                        }
                    }
                    else
                    {
                        // 对于ID为null的记录，使用ContractId比较
                        var matchingItem = data2NullIdList.FirstOrDefault(x => 
                            x.ContractId == item1.ContractId);
                        
                        if (matchingItem != null)
                        {
                            found = true;
                            // 比较所有属性是否有差异
                            var differences = CompareProperties(item1, matchingItem);
                            if (differences.Count > 0 && differingRecords.Count < maxDiffRecords)
                            {
                                differingRecords.Add(new
                                {
                                    Id = null as string,
                                    ContractId = item1.ContractId,
                                    Differences = differences
                                });
                            }
                        }
                    }
                    
                    if (!found)
                    {
                        onlyInView1.Add(item1);
                    }
                }

                // 步骤5: 查找只在视图2中存在的记录
                var onlyInView2 = new List<Db_v_searchcontractreceiptregisterlist_join>();
                
                // 检查ID不为null的记录
                foreach (var item2 in data2)
                {
                    bool found = false;
                    
                    if (item2.Id != null)
                    {
                        // 检查是否存在于视图1
                        found = data1.Any(x => x.Id == item2.Id);
                    }
                    else
                    {
                        // 对于ID为null的记录，使用ContractId比较
                        found = data1.Any(x => x.Id == null && x.ContractId == item2.ContractId);
                    }
                    
                    if (!found)
                    {
                        onlyInView2.Add(item2);
                    }
                }

                // 步骤6: 返回结果
                var result = new
                {
                    Statistics = new
                    {
                        View1Name = "v_searchcontractreceiptregister_sta (State IS NOT NULL AND State != 10)",
                        View1Count = view1Count,
                        View2Name = "v_searchcontractreceiptregisterlist_join",
                        View2Count = view2Count,
                        OnlyInView1Count = onlyInView1.Count,
                        OnlyInView2Count = onlyInView2.Count,
                        DifferingRecordsCount = differingRecords.Count,
                        Note = $"显示最多{maxDiffRecords}条差异记录"
                    },
                    DifferingRecords = differingRecords.Take(maxDiffRecords).ToList()
                };

                return new JsonResult(new
                {
                    Success = true,
                    Message = "视图数据比较完成",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Success = false,
                    Message = "比较视图数据失败",
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                }) { StatusCode = 500 };
            }
        }

        /// <summary>
        /// 比较两个对象的所有公共属性，返回不同的属性列表
        /// </summary>
        private List<object> CompareProperties(object obj1, object obj2)
        {
            var differences = new List<object>();
            var properties1 = obj1.GetType().GetProperties();
            var properties2 = obj2.GetType().GetProperties();
            
            // 获取公共属性名
            var commonPropertyNames = properties1.Select(p => p.Name)
                .Intersect(properties2.Select(p => p.Name))
                .ToList();
            
            foreach (var propName in commonPropertyNames)
            {
                // 跳过Id属性，因为我们用它来匹配记录
                if (propName == "Id")
                    continue;
                
                var prop1 = properties1.FirstOrDefault(p => p.Name == propName);
                var prop2 = properties2.FirstOrDefault(p => p.Name == propName);
                
                if (prop1 == null || prop2 == null)
                    continue;
                
                var value1 = prop1.GetValue(obj1);
                var value2 = prop2.GetValue(obj2);
                
                // 特殊处理: 日期比较只比较日期部分，不比较时间部分
                if (prop1.PropertyType == typeof(DateTime) || prop1.PropertyType == typeof(DateTime?))
                {
                    DateTime? dt1 = value1 as DateTime?;
                    DateTime? dt2 = value2 as DateTime?;
                    
                    if (dt1.HasValue != dt2.HasValue)
                    {
                        differences.Add(new
                        {
                            PropertyName = propName,
                            View1Value = dt1,
                            View2Value = dt2
                        });
                    }
                    else if (dt1.HasValue && dt2.HasValue)
                    {
                        // 比较日期部分
                        if (dt1.Value.Date != dt2.Value.Date)
                        {
                            differences.Add(new
                            {
                                PropertyName = propName,
                                View1Value = dt1,
                                View2Value = dt2
                            });
                        }
                    }
                    continue;
                }
                
                // 普通比较
                bool isDifferent = false;
                
                if ((value1 == null && value2 != null) || (value1 != null && value2 == null))
                {
                    isDifferent = true;
                }
                else if (value1 != null && value2 != null && !value1.Equals(value2))
                {
                    isDifferent = true;
                }
                
                if (isDifferent)
                {
                    differences.Add(new
                    {
                        PropertyName = propName,
                        View1Value = value1,
                        View2Value = value2
                    });
                }
            }
            
            return differences;
        }

        /// <summary>
        /// 合同审核通过的完整数据（创建客户+合同）
        /// </summary>
        /// <returns>生成的测试数据信息</returns>
        [HttpPost]
    [SkipRightCheck]
    [SkipIPCheck]
    [SkipRecordLog]
    [SkipAuthCheck]
    [SkipRSAKey]
        public IActionResult PrepareInvoiceTestData()
        {
            try
            {
                // 生成随机后缀，避免重名
                string randomSuffix = DateTime.Now.ToString("yyyyMMddHHmmss");
                
                // 1. 创建客户
                var customerResult = BLL_TestData.Instance.CreateTestCustomer(randomSuffix);
                if (customerResult == null)
                {
                    return new JsonResult(new { Message = "客户创建失败" }) { StatusCode = 400 };
                }
                
                // 2. 创建合同
                var contractResult = BLL_TestData.Instance.CreateTestContract(customerResult.Id);
                if (contractResult == null)
                {
                    return new JsonResult(new { Message = "合同创建失败", CustomerId = customerResult.Id }) { StatusCode = 400 };
                }

                // 直接返回创建的测试数据信息
                return new JsonResult(new
                {
                    Message = "测试数据创建成功",
                    Data = new
                    {
                        CustomerId = customerResult.Id,
                        CustomerName = customerResult.Name,
                        ContractId = contractResult.Id,
                        ContractName = contractResult.Name
                    }
                }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "创建测试数据失败",
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                }) { StatusCode = 500 };
            }
        }

        /// <summary>
        /// 创建测试客户
        /// </summary>
        /// <param name="randomSuffix">随机后缀，用于避免重名</param>
        /// <returns>客户创建结果</returns>
        [SkipRightCheck]
            [HttpPost]
        [SkipIPCheck]
        [SkipRecordLog]
        [SkipAuthCheck]
        [SkipRSAKey]
        public IActionResult CreateTestCustomerAction(string randomSuffix = null)
        {
            try
            {
                if (string.IsNullOrEmpty(randomSuffix))
                {
                    randomSuffix = DateTime.Now.ToString("yyyyMMddHHmmss");
                }

                var result = BLL_TestData.Instance.CreateTestCustomer(randomSuffix);
                if (result == null)
                {
                    return new JsonResult(new { Message = "客户创建失败" }) { StatusCode = 400 };
                }

                return new JsonResult(new
                {
                    Message = "测试客户创建成功",
                    Data = new
                    {
                        CustomerId = result.Id,
                        CustomerName = result.Name
                    }
                }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "创建测试客户失败",
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                }) { StatusCode = 500 };
            }
        }

        /// <summary>
        /// 创建测试合同
        /// </summary>
        /// <param name="customerId">客户ID</param>
        /// <returns>合同创建结果</returns>
        [SkipRightCheck]
        [HttpPost]
        [SkipIPCheck]
        [SkipRecordLog]
        [SkipAuthCheck]
        [SkipRSAKey]
        public IActionResult CreateTestContractAction(string customerId)
        {
            try
            {
                var result = BLL_TestData.Instance.CreateTestContract(customerId);
                if (result == null)
                {
                    return new JsonResult(new { Message = "合同创建失败", CustomerId = customerId }) { StatusCode = 400 };
                }

                return new JsonResult(new
                {
                    Message = "测试合同创建成功",
                    Data = new
                    {
                        CustomerId = customerId,
                        ContractId = result.Id,
                        ContractName = result.Name
                    }
                }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Message = "创建测试合同失败",
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                }) { StatusCode = 500 };
            }
        }

        /// <summary>
        /// 准备到账确认成功后的测试数据（包含创建客户、合同、银行到账和登记业绩）
        /// </summary>
        /// <returns>测试数据创建结果</returns>
        [HttpGet]
        [SkipRightCheck]
        [SkipIPCheck]
        [SkipRecordLog]
        public object PrepareReceiptTestData()
        {
            try
            {
                // 委托给 BLL 处理
                var result = BLL_TestData.Instance.PrepareReceiptTestData();
                if (result == null)
                {
                    return new JsonResult(new { Success = false, Message = "创建测试数据失败" }) { StatusCode = 500 };
                }
                
                // 返回成功结果和创建的数据ID
                return new JsonResult(new 
                { 
                    Success = true, 
                    Message = "创建测试数据成功", 
                    Data = new
                    {
                        CustomerId = result.CustomerId,
                        CustomerName = result.CustomerName,
                        ContractId = result.ContractId,
                        ContractName = result.ContractName,
                        CollectionInfoId = result.CollectionInfoId,
                        ReceiptRegisterId = result.ReceiptRegisterId
                    }
                }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                // 返回异常信息
                return new JsonResult(new { Success = false, Message = "创建测试数据失败: " + ex.Message }) { StatusCode = 500 };
            }
        }

        /// <summary>
        /// 为测试合同创建发票申请
        /// </summary>
        /// <returns>发票申请结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipIPCheck]
        [SkipRecordLog]
        [SkipAuthCheck]
        [SkipRSAKey]
        public IActionResult PrepareInvoiceApplication()
        {
            try
            {
                // 直接调用 BLL 层方法，查找未开发票的测试数据并创建发票申请
                var result = BLL_TestData.Instance.PrepareInvoiceApplication();
                if (result == null)
                {
                    return new JsonResult(new { Success = false, Message = "发票申请创建失败" }) { StatusCode = 500 };
                }

                // 返回成功结果
                return new JsonResult(new
                {
                    Success = true,
                    Message = "发票申请创建成功",
                    Data = new
                    {
                        InvoiceApplicationId = result.InvoiceApplicationId,
                        ContractId = result.ContractId,
                        ContractName = result.ContractName,
                        CustomerId = result.CustomerId,
                        CustomerName = result.CustomerName,
                        CollectionInfoId = result.CollectionInfoId,
                        InvoiceAmount = result.InvoiceAmount
                    }
                }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Success = false,
                    Message = "创建发票申请失败",
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                }) { StatusCode = 500 };
            }
        }

        /// <summary>
        /// 审核测试发票申请（包含上传发票和OCR识别流程）
        /// </summary>
        /// <remarks>
        /// 完整流程包括：
        /// 1. 获取待审核的发票申请
        /// 2. 上传测试发票文件
        /// 3. 进行OCR识别和比对
        /// 4. 使用OCR识别的发票信息进行审核
        /// 5. 返回审核结果
        /// </remarks>
        /// <param name="invoiceApplicationId">可选的发票申请ID，如不提供则自动查找一个待审核的申请</param>
        /// <returns>审核结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipIPCheck]
        [SkipRecordLog]
        [SkipAuthCheck]
        [SkipRSAKey]
        public IActionResult AuditInvoiceApplication(string invoiceApplicationId = null)
        {
            try
            {
                // 调用 BLL 层方法审核发票申请
                var result = BLL_TestData.Instance.AuditInvoiceApplication(invoiceApplicationId);
                
                // 返回成功结果
                return new JsonResult(new
                {
                    Success = true,
                    Message = result.SuccessMessage ?? "发票申请审核成功",
                    Data = new
                    {
                        InvoiceApplicationId = result.InvoiceApplicationId,
                        ContractId = result.ContractId,
                        ContractName = result.ContractName,
                        InvoiceAmount = result.InvoiceAmount
                    }
                }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Success = false,
                    Message = "审核发票申请失败",
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                }) { StatusCode = 500 };
            }
        }

        /// <summary>
        /// 测试补充发票识别和提交流程
        /// </summary>
        /// <remarks>
        /// 完整流程包括：
        /// 1. 准备测试发票文件
        /// 2. 上传并进行OCR识别
        /// 3. 提交补充发票申请
        /// 4. 返回操作结果
        /// </remarks>
        /// <returns>补充发票测试结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipIPCheck]
        [SkipRecordLog]
        [SkipAuthCheck]
        [SkipRSAKey]
        public IActionResult TestSupplementInvoice()
        {
            try
            {
                var result = BLL_TestData.Instance.TestSupplementInvoice();
                if (result == null)
                {
                    return new JsonResult(new
                    {
                        Success = false,
                        Message = "补充发票测试失败，未返回结果"
                    }) { StatusCode = 500 };
                }
                
                return new JsonResult(new
                {
                    Success = true,
                    Message = result.SuccessMessage ?? "补充发票测试成功",
                    Data = new
                    {
                        InvoiceApplicationId = result.InvoiceApplicationId,
                        InvoiceAmount = result.InvoiceAmount,
                        Instructions = "此申请ID可直接用于复核测试，请复制后调用TestReviewSupplementInvoice接口"
                    }
                }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Success = false,
                    Message = "补充发票测试失败",
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                }) { StatusCode = 500 };
            }
        }

        /// <summary>
        /// 测试补充发票复核流程
        /// </summary>
        /// <remarks>
        /// 完整流程包括：
        /// 1. 获取待复核的补充发票申请
        /// 2. 调用复核接口完成复核
        /// 3. 验证复核结果
        /// 4. 返回操作结果
        /// </remarks>
        /// <param name="invoiceApplicationId">可选的发票申请ID，如不提供则自动查找一个待复核的补充发票申请</param>
        /// <returns>补充发票复核测试结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipIPCheck]
        [SkipRecordLog]
        [SkipAuthCheck]
        [SkipRSAKey]
        public IActionResult TestReviewSupplementInvoice(string invoiceApplicationId = null)
        {
            try
            {
                var result = BLL_TestData.Instance.TestReviewSupplementInvoice(invoiceApplicationId);
                if (result == null)
                {
                    return new JsonResult(new
                    {
                        Success = false,
                        Message = "补充发票复核测试失败，未返回结果"
                    }) { StatusCode = 500 };
                }

                return new JsonResult(new
                {
                    Success = true,
                    Message = result.SuccessMessage ?? "补充发票复核测试成功",
                    Data = new
                    {
                        InvoiceApplicationId = result.InvoiceApplicationId,
                        InvoiceAmount = result.InvoiceAmount
                    }
                }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Success = false,
                    Message = "补充发票复核测试失败",
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                }) { StatusCode = 500 };
            }
        }
        
        /// <summary>
        /// 测试补充发票完整流程（创建+复核）
        /// </summary>
        /// <remarks>
        /// 完整流程包括：
        /// 1. 创建补充发票申请（上传OCR识别和提交）
        /// 2. 自动进行补充发票复核
        /// 3. 返回完整流程结果
        /// </remarks>
        /// <returns>补充发票完整流程测试结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipIPCheck]
        [SkipRecordLog]
        [SkipAuthCheck]
        [SkipRSAKey]
        public IActionResult TestSupplementInvoiceCompleteProcess()
        {
            try
            {
                var result = BLL_TestData.Instance.TestSupplementInvoiceCompleteProcess();
                if (result == null)
                {
                    return new JsonResult(new
                    {
                        Success = false,
                        Message = "补充发票完整流程测试失败，未返回结果"
                    }) { StatusCode = 500 };
                }

                return new JsonResult(new
                {
                    Success = true,
                    Message = result.SuccessMessage ?? "补充发票完整流程测试成功",
                    Data = new
                    {
                        InvoiceApplicationId = result.InvoiceApplicationId,
                        InvoiceAmount = result.InvoiceAmount,
                        Instructions = "已完成从申请到复核的完整流程测试"
                    }
                }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Success = false,
                    Message = "补充发票完整流程测试失败",
                    Error = ex.Message,
                    StackTrace = ex.StackTrace
                }) { StatusCode = 500 };
            }
        }
        
    }
} 