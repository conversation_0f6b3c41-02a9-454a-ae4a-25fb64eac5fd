﻿using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BusinessModel;

namespace CRM2_API.BLL.Common
{
    public class Com_SysForm
    {
        private static readonly AsyncLocal<FormInterfaceName> _dbSysFrom = new();

        public static FormInterfaceName Instance
        {
            get { return _dbSysFrom.Value ?? new(); }
        }

        public static void SetDbSysFrom(FormInterfaceName dbSysFrom)
        {
            _dbSysFrom.Value = dbSysFrom;
        }
    }
}
