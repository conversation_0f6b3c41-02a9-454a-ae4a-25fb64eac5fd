﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.IO;
using static CRM2_API.Model.BLLModel.Enum.PrivateServiceEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_Coupon;
using static CRM2_API.Model.ControllersViewModel.VM_PrivateService;

namespace CRM2_API.Controllers
{
    [Description("个人服务时间控制器")]
    public class PrivateServiceController : MyControllerBase
    {
        public PrivateServiceController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取当前登陆人可用个人服务时间天数
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public DurationInfo GetDuration()
        {
            return BLL_PrivateService.Instance.GetDuration();
        }

        [HttpPost]
        public IActionResult ImportExcelData([FromForm] ImportPrivateServiceData_In importPrivateServiceData_In)
        {
            return BLL_PrivateService.Instance.ImportPrivateServiceData(importPrivateServiceData_In, 0);
        }
        /// <summary>
        /// 服务天数管理查询
        /// </summary>
        /// <param name="searchPrivateServiceData_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchPrivateServiceData_Out> SearchPrivateServiceDataList(SearchPrivateServiceData_In searchPrivateServiceData_In)
        {
            int total = 0;
            var data = DbOpe_crm_privateservice.Instance.SearchPrivateServiceList(searchPrivateServiceData_In, ref total);
            return new ApiTableOut<SearchPrivateServiceData_Out> { Data = data, Total = total };
        }

        /// <summary>
        /// 查询服务天数使用详情
        /// </summary>
        /// <param name="privateServiceDetail_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<PrivateServiceDetail_Out> SearchPrivateServiceDetailDataList(PrivateServiceDetail_In privateServiceDetail_In)
        {
            int total = 0;
            var data = DbOpe_crm_privateservice_detail.Instance.SearchPrivateServiceDetailDataList(privateServiceDetail_In, ref total);
            return new ApiTableOut<PrivateServiceDetail_Out> { Data = data, Total = total };
        }
        /// <summary>
        /// 添加个人服务天数
        /// </summary>
        /// <param name="addPrivateService_In"></param>
        /// <returns></returns>
        [HttpPost]
        public PrivcateServiceResult AddPrivateService(AddPrivateService_In addPrivateService_In)
        {
            return DbOpe_crm_privateservice.Instance.AddPrivcateService(addPrivateService_In);
        }

        /// <summary>
        /// 更新个人服务天数
        /// </summary>
        /// <param name="updatePrivateService_In"></param>
        /// <returns></returns>
        [HttpPost]
        public PrivcateServiceResult UpdatePrivateService(UpdatePrivateService_In updatePrivateService_In)
        {
            return DbOpe_crm_privateservice.Instance.UpdatePrivcateService(updatePrivateService_In);
        }

        /// <summary>
        /// 根据ID获取个人服务天数记录
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        [HttpGet]
        public SearchPrivateServiceData_Out GetPrivateServiceDataById(string Ids)
        {
            return DbOpe_crm_privateservice.Instance.GetPrivateServiceDataById(Ids);
        }

        /// <summary>
        /// 导出结果
        /// </summary>
        /// <param name="privateServiceDetail_In"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetPrivateServiceDetailDataDownload(PrivateServiceDetail_In privateServiceDetail_In)
        {
            Stream result = BLL_PrivateService.Instance.GetPrivateServiceDetailDataDownload(privateServiceDetail_In);
            return new FileStreamResult(result, "application/octet-stream");
        }

        [HttpPost,SkipRightCheck]
        public void RollBackWrongData()
        {
            DbOpe_crm_privateservice_detail.Instance.CheckWrongData();
        }
        /// <summary>
        /// 手动操作退还服务天数，主要参数为 原记录主键ID 退还的发放天数 退还的自费天数 备注
        /// </summary>
        /// <param name="rollBackPrivateServiceDetail_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public PrivcateServiceResult OperatedRollBackPrivateServiceDay(RollBackPrivateServiceDetail_In rollBackPrivateServiceDetail_In)
        {
           return BLL_PrivateService.Instance.OperatedRollBackPrivateServiceDay(rollBackPrivateServiceDetail_In);
        }
    }
}
