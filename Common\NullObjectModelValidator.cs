﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace CRM2_API.Common
{
    /// <summary>
    /// 创建不会将任何字段标记为无效的 IObjectModelValidator 实现。
    /// </summary>
    public class NullObjectModelValidator : IObjectModelValidator
    {
        public void Validate(ActionContext actionContext, ValidationStateDictionary validationState, string prefix, object model)
        {
            //该方法故意为空
        }
    }
}
