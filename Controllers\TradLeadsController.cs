﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using System.IO;
using static CRM2_API.Model.ControllersViewModel.VM_TradeLeads;

namespace CRM2_API.Controllers
{
    [Description("供求关系信息")]
    public class TradLeadsController : MyControllerBase
    {
        public TradLeadsController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }


        /// <summary>
        /// 获取供求关系列表 G6调用
        /// </summary>
        /// <param name="searchTradeList_In"></param>
        /// <returns></returns>
        [Http<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ord<PERSON><PERSON>]
        public ApiTableOut<TradeLeadsBase> SearchTradeLeadsListForGtis(SearchTradeList_In searchTradeList_In)
        {
            int total = 0;
            var list = DbOpe_crm_tradeleads.Instance.SearchTradeLeadsListForGtis(searchTradeList_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据ID获取供求关系详情 G6调用
        /// </summary>
        /// <param name="tradeLeadsParameter_In"></param>
        /// <returns></returns>
        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipIPCheck, SkipRecordLog]
        public TradeLeadsDetail GetTradeLeadsDetailByIdForGtis(TradeLeadsParameter_In tradeLeadsParameter_In)
        {
            return DbOpe_crm_tradeleads.Instance.GetTradeLeadsDetailById(tradeLeadsParameter_In);
        }


        /// <summary>
        /// 下载求购信息附件 G6调用
        /// </summary>
        [HttpGet, SkipAuthCheck, SkipRSAKey, SkipIPCheck, SkipRecordLog]
        public IActionResult DownloadTradeFileForGtis(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误!"
                });
            }
            return BLL_Attachfile.Instance.Preview(id, "TradeLeadsFiles", Response);
        }

        /// <summary>
        /// 新增供求信息
        /// </summary>
        /// <param name="addTradeLeadsInfo"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public TradeLeadsResult AddTradeLeads([FromForm] AddTradeLeadsInfo addTradeLeadsInfo)
        {
            return DbOpe_crm_tradeleads.Instance.AddTradeLeads(addTradeLeadsInfo);
        }

        /// <summary>
        /// 修改供求信息
        /// </summary>
        /// <param name="updateTradeLeadsInfo"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public TradeLeadsResult UpdateTradeLeads([FromForm] UpdateTradeLeadsInfo updateTradeLeadsInfo)
        {
            return DbOpe_crm_tradeleads.Instance.UpdateTradeLeads(updateTradeLeadsInfo);
        }

        /// <summary>
        /// 删除供求信息
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public TradeLeadsResult DelTradeLeads(string Ids)
        {
            return DbOpe_crm_tradeleads.Instance.DelTradeLeads(Ids);
        }

        /// <summary>
        /// 根据ID获取供求关系详情
        /// </summary>
        /// <param name="tradeLeadsParameter_In"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public TradeLeadsDetail GetTradeLeadsDetailById(TradeLeadsParameter_In tradeLeadsParameter_In)
        {
            return DbOpe_crm_tradeleads.Instance.GetTradeLeadsDetailById(tradeLeadsParameter_In); ;
        }

        /// <summary>
        /// 修改供求关系状态（0 打开 1 关闭）
        /// </summary>
        /// <param name="tradeLeadsState_In"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public TradeLeadsResult SetTradeLeadsStatesOpenClosed(TradeLeadsState_In tradeLeadsState_In)
        {
            return DbOpe_crm_tradeleads.Instance.SetTradeLeadsStatesOpenClosed(tradeLeadsState_In); ;
        }

        /// <summary>
        /// 获取供求关系列表 
        /// </summary>
        /// <param name="searchTradeList_In"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public ApiTableOut<TradeLeadsBase> SearchTradeLeadsList(SearchTradeList_In searchTradeList_In)
        {
            int total = 0;
            var list = DbOpe_crm_tradeleads.Instance.SearchTradeLeadsList(searchTradeList_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 供求关系延期
        /// </summary>
        /// <param name="tradeLeadsParameter_In"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public TradeLeadsResult SetTradeLeadsExpiredDelay(TradeLeadsParameter_In tradeLeadsParameter_In)
        {
            return DbOpe_crm_tradeleads.Instance.DelayTradeLeads(tradeLeadsParameter_In.Id);
        }
    }
}
