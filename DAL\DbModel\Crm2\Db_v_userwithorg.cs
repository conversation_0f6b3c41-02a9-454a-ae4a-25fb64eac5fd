﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("v_userwithorg")]
    public partial class Db_v_userwithorg
    {
        public Db_v_userwithorg()
        {


        }
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:账号
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string UserName { get; set; }

        /// <summary>
        /// Desc:密码
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string PassWord { get; set; }

        /// <summary>
        /// Desc:员工编号
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string UserNum { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string Name { get; set; }

        /// <summary>
        /// Desc:部门id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string DepartmentId { get; set; }

        /// <summary>
        /// Desc:所属组织id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string OrganizationId { get; set; }

        /// <summary>
        /// Desc:手机号
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string Telephone { get; set; }

        /// <summary>
        /// Desc:电子邮箱
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Email { get; set; }

        /// <summary>
        /// Desc:1-管理者;2-职工
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int UserType { get; set; }

        /// <summary>
        /// Desc:最大可保存客户数
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int MaxSaveCustomer { get; set; }

        /// <summary>
        /// Desc:可保存最大客户数(手动输入)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int MaxSaveCustomerMannualInput { get; set; }

        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Remark { get; set; }

        /// <summary>
        /// Desc:是否启用
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool UserStatus { get; set; }

        /// <summary>
        /// Desc:微信绑定标识
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool WeChatIsBind { get; set; }

        /// <summary>
        /// Desc:微信openid
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string WeChatOpenID { get; set; }

        /// <summary>
        /// Desc:钉钉绑定标识
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool DingDingIsBind { get; set; }

        /// <summary>
        /// Desc:钉钉openid
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string DingDingOpenID { get; set; }

        /// <summary>
        /// Desc:是否允许用户名登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowUserNameLogin { get; set; }

        /// <summary>
        /// Desc:是否允许手机号登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowTelphoneLogin { get; set; }

        /// <summary>
        /// Desc:是否允许邮箱登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowEmailLogin { get; set; }

        /// <summary>
        /// Desc:是否允许微信登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowWechatLogin { get; set; }

        /// <summary>
        /// Desc:是否允许钉钉登录
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool AllowDingdingLogin { get; set; }

        /// <summary>
        /// Desc:最后登录时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// Desc:最后登录IP
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string LastLoginIP { get; set; }
        
        /// <summary>
        /// Desc:头像图片
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AvatarImage {get;set;}

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>           
        public bool Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:False
        /// </summary>           
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }
        /// <summary>
        /// Desc:完整组织名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string OrgFullName { get; set; }
        /// <summary>
        /// Desc:用户名称（完整组织名称）
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UserWithOrgFullName { get; set; }
        /// <summary>
        /// 组织机构类型
        /// </summary>
        public int? OrgType {get;set;}
    }
}
