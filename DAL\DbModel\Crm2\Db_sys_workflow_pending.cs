﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///待办表
    ///</summary>
    [SugarTable("sys_workflow_pending")]
    public class Db_sys_workflow_pending
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:审批流表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string WorkFlowId {get;set;}

           /// <summary>
           /// Desc:审批流节点id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string WorkFlowNodeId {get;set;}

           /// <summary>
           /// Desc:所属表单id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string FormId {get;set;}

           /// <summary>
           /// Desc:发送人id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:记录id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string DataId {get;set;}

           /// <summary>
           /// Desc:接收人角色id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string RecipientRoleId { get;set;}

           /// <summary>
           /// Desc:接收人id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string RecipientUserId { get; set; }

           /// <summary>
           /// Desc:待办链接
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PendingUrl {get;set;}

           /// <summary>
           /// Desc:待办描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PendingContent {get;set;}

           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? State {get;set;}

           /// <summary>
           /// Desc:发起时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? StartDate {get;set;}

           /// <summary>
           /// Desc:结束时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? EndDate {get;set;}

           /// <summary>
           /// Desc:实际处理人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OperationUserId {get;set;}

           /// <summary>
           /// Desc:实际处理结果
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OperationContent { get; set; }

           /// <summary>
           /// Desc:是否发送消息
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Sended { get; set; }

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
