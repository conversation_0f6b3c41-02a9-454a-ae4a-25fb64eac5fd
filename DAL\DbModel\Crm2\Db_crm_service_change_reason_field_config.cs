using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 服务变更原因可变更字段配置表
    /// </summary>
    [SugarTable("crm_service_change_reason_field_config")]
    public partial class Db_crm_service_change_reason_field_config
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, Length = 36)]
        public string Id { get; set; }

        /// <summary>
        /// 服务类型：1-GTIS，2-邓白氏，3-环球搜，4-慧思学院，5-其他数据
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ServiceType { get; set; }

        /// <summary>
        /// 变更原因枚举值
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ChangeReasonEnum { get; set; }

        /// <summary>
        /// 可变更的字段键名
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 100)]
        public string FieldKey { get; set; }

        /// <summary>
        /// 字段显示名称
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 200)]
        public string FieldName { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? DisplayOrder { get; set; }

        /// <summary>
        /// 是否启用：0-否，1-是
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? IsActive { get; set; }

        /// <summary>
        /// 备注说明
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 500)]
        public string Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string CreateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string UpdateUser { get; set; }

        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? Deleted { get; set; }

        /// <summary>
        /// 字段权限：1-可修改，2-仅展示
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? FieldPermission { get; set; }

        /// <summary>
        /// 适用场景：apply-申请，audit-审核，both-通用
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 10)]
        public string ApplyScenario { get; set; }

        /// <summary>
        /// 触发字段列表：JSON格式，当这些字段变更时本字段在审核时可修改
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDataType = "TEXT")]
        public string TriggerFields { get; set; }

        /// <summary>
        /// 是否为数组/列表类型字段：0-否，1-是
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? IsArray { get; set; }

        /// <summary>
        /// 字段类型：simple-简单类型，list-列表类型，object-对象类型
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string FieldType { get; set; }

        /// <summary>
        /// 比较方式：direct-直接比较，list_diff-列表差异，json_compare-JSON比较
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string ComparisonMethod { get; set; }
    }
}
