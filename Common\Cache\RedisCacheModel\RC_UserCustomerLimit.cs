﻿using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.System;

namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class UserCustomerLimit
        {
            const string USERCUSTOMERLIMIT = "usercustomerlimit_";
            /// <summary>
            /// 验证用户当日查看客户信息数量限制
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="customerId"></param>
            /// <returns></returns>
            public static void CheckLimit(string userId,string customerId)
            {
                var key = USERCUSTOMERLIMIT + userId;
                if (RedisHelper.Exists(key))
                {
                    var customerList = RedisHelper.Get<List<string>>(key);
                    if (customerList == null)
                    {
                        RedisHelper.Set(key, new List<string>() { customerId });
                        //今日后过期删除
                        RedisHelper.ExpireAt(key, DateTime.Today.GetDaysEnd());
                        //return true;
                    }
                    else
                    {
                        if (customerList.Contains(customerId))
                        {
                            //return true;
                        }
                        else
                        {
                            var userCustomerLimitNum = 100;
                            if (customerList.Count >= userCustomerLimitNum)
                            {
                                throw new ApiException("超出本日查看客户数据数量限制");
                            }
                            else {
                                customerList.Add(customerId);
                                RedisHelper.Set(key, customerList);
                                RedisHelper.ExpireAt(key, DateTime.Today.GetDaysEnd());
                                //return true;
                            }
                        }
                    }
                }
                else {
                    RedisHelper.Set(key, new List<string>() { customerId });
                    //今日后过期删除
                    RedisHelper.ExpireAt(key, DateTime.Today.GetDaysEnd());
                    //return true;
                }
            }
            public static void CheckLimit(string userId, List<string> customerIds)
            { 
                foreach(var customerId in customerIds) { 
                    CheckLimit(userId, customerId);
                }
            }
        }
        
    }
}
