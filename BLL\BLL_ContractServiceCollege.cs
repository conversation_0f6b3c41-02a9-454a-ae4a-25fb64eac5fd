﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.ContractOverseasIPRecord;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Metadata;
using SqlSugar;
using System.Security.Policy;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;

namespace CRM2_API.BLL
{
    public class BLL_ContractServiceCollege : BaseBLL<BLL_ContractServiceCollege>
    {

        /// <summary>
        /// 审核合同服务信息慧思学院申请信息
        /// </summary>
        /// <param name="audit_In"></param>
        public void AuditContractProductServiceInfoCollegeAppl(AuditContractProductServiceInfoCollegeAppl_In audit_In)
        {
            //获取申请的数据
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.QueryByPrimaryKey(audit_In.ProductServiceInfoCollegeApplId);
            //开通的workflow记录
            var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(apply.ContractId);
            #region 判断是否可以进行登记操作
            /*获取当前申请绑定的服务数据，如果没有绑定服务数据&&申请处在提交状态--可以登记; 如果绑定了服务数据且服务数据是被驳回状态--可以登记; 其余情况不可进行登记*/
            bool couldRegistered = false;
            var curService = DbOpe_crm_contract_serviceinfo_college.Instance.GetData(g => g.ProductServiceInfoCollegeApplId == apply.Id && g.IsHistory == false);
            if (curService == null && apply.State == EnumProcessStatus.Submit.ToInt())
                couldRegistered = true;
            else if (curService != null && (curService.State == EnumContractServiceState.RETURNED || curService.State == EnumContractServiceState.TO_BE_OPENED))
                couldRegistered = true;
            if (!couldRegistered)
                throw new ApiException("当前申请无法进行初审操作");
            #endregion
            //如果登记人员执行拒绝操作，修改申请数据apply的状态为拒绝，如果是服务变更数据，对已经锁定的旧数据放开锁定
            if (!audit_In.State)
            {
                apply.State = EnumProcessStatus.Refuse.ToInt();
                //放开被变更的服务数据可操作
                var curOpenService = DbOpe_crm_contract_serviceinfo_college.Instance.GetData(g => (g.State == EnumContractServiceState.VALID || g.State == EnumContractServiceState.OUT) && g.ContractProductInfoId == apply.ContractProductInfoId);
                if (curOpenService != null)
                {
                    var currentAppl = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetDataById(curOpenService.ProductServiceInfoCollegeApplId);
                    currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(currentAppl);
                }
                apply.Feedback = audit_In.FeedBack;
                apply.ReviewerDate = DateTime.Now;
                apply.ReviewerId = UserId;
                apply.UpdateDate = DateTime.Now;
                apply.UpdateUser = UserId;
                if (apply.ProcessingType == (int)EnumProcessingType.Change)
                    //拒绝后该数据状态为无效
                    apply.IsInvalid = (int)EnumIsInvalid.Invalid;
                DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(apply);
                


                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = apply.CreateUser;
                message.MessageTypeToId = apply.Id;
                message.MessagemMainAboutDes = contract.ContractName;
                message.LocalFeedBack = apply.Feedback;
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.CollegeService, EnumMessageStateInfo.Refus, apply.ContractId);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            }
            //如果初审登记通过，需要验证合同信息，判断合同是否到账; 需要验证账号开通数量是否小于可申请数量
            else
            {
                /* if (!string.IsNullOrEmpty(apply.ContractProductInfoId) && apply.PrimaryAccountsNum < audit_In.CollegeContacts.Count())
                     throw new ApiException("待开通账号数量超过可申请数量");*/
                var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
                if (contractInfo == null)
                    throw new ApiException("未找到合同信息");
                if (string.IsNullOrEmpty(contractInfo.ContractNum))
                    throw new ApiException("未到账合同不能进行登记操作");
            }
            //创建服务数据
            var now = DateTime.Now;
            var servCollegeInfo = audit_In.MappingTo<Db_crm_contract_serviceinfo_college>();
            servCollegeInfo.Id = Guid.NewGuid().ToString();
            servCollegeInfo.ContractId = apply.ContractId;
            servCollegeInfo.ProductId = apply.ProductId;
            servCollegeInfo.ContractProductInfoId = apply.ContractProductInfoId;
            servCollegeInfo.ServiceCycleStart = audit_In.ServiceCycleStart;
            servCollegeInfo.ServiceCycleEnd = audit_In.ServiceCycleEnd;
            servCollegeInfo.ServiceMonth = audit_In.ServiceMonth;
            servCollegeInfo.State = audit_In.State ? EnumContractServiceState.TO_BE_REVIEW : EnumContractServiceState.REFUSE;
            servCollegeInfo.Deleted = false;
            servCollegeInfo.IsChanged = false;
            servCollegeInfo.CreateDate = DateTime.Now;
            servCollegeInfo.CreateUser = UserId;
            servCollegeInfo.RegisteredId = UserId;
            servCollegeInfo.RegisteredTime = now;
            servCollegeInfo.IsHistory = false;
            servCollegeInfo.ProcessingType = apply.ProcessingType;
            servCollegeInfo.PrimaryAccountsNum = audit_In.CollegeContacts.Count();
            servCollegeInfo.RegisteredRemark = audit_In.FeedBack;
            //判断是否是被驳回数据
            if (curService != null)
            {
                //如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
                curService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(curService);
                //被驳回服务重新初审通过时，删除被驳回服务的到账绑定关系
                DbOpe_crm_contract_receiptregister_service.Instance.DeleteReturnServiceLinkDataQueue(curService.Id);
            }
            DbOpe_crm_contract_serviceinfo_college.Instance.InsertQueue(servCollegeInfo);
            //初审通过时保存人员信息
            if (audit_In.State)
            {
                var contacts = new List<Db_crm_contract_serviceinfo_college_contacts>();
                //循环插入慧思学院人员
                audit_In.CollegeContacts.ForEach(item =>
                {
                    Db_crm_contract_serviceinfo_college_contacts contact = new Db_crm_contract_serviceinfo_college_contacts();
                    contact.ContractServiceInfoCollegeId = servCollegeInfo.Id;
                    contact.Name = item.Name;
                    contact.Phone = item.Phone;
                    contact.State = EnumContractServiceCollegeContactsState.ToBeReview;
                    contacts.Add(contact);
                });
                DbOpe_crm_contract_serviceinfo_college_contacts.Instance.InsertListData(contacts);
                //绑定到账信息
                if (audit_In.ReceiptRegisterIds != null && audit_In.ReceiptRegisterIds.Count > 0)
                {
                    DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptDataQueue(audit_In.ReceiptRegisterIds, servCollegeInfo.Id, EnumProductType.GlobalWitsSchool);
                }
            }

            string dataState = String.Empty;
            if (audit_In.State)
                dataState = EnumContractServiceOpenState.ToBeReview.GetEnumDescription();
            else
                dataState = EnumContractServiceOpenState.Refuse.GetEnumDescription();
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_college, Db_crm_contract>("慧思学院服务审批流程", apply.Id, servCollegeInfo, contract, audit_In.FeedBack, dataState, "初审");
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.SaveQueues();
            /* 复核时用
             * //11.20 开通服务要刷新合同的保护截止日
            if (audit_In.State)
                DbOpe_crm_contract.Instance.RefreshContractProtectDate(apply.ContractId);*/
        }

        public void ReviewContractProductServiceInfoCollegeAppl(ReviewContractProductServiceInfoCollegeAppl_In review_In)
        {
            //根据ProductServiceInfoCollegeApplId获取申请表信息
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.QueryByPrimaryKey(review_In.ProductServiceInfoCollegeApplId);
            //根据ProductServiceInfoCollegeApplId获取服务表信息
            var curService = DbOpe_crm_contract_serviceinfo_college.Instance.GetData(g => g.ProductServiceInfoCollegeApplId == apply.Id && g.IsHistory == false);

            //开通的workflow记录
            var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(apply.ContractId);
            #region 判断是否可以进行复核操作
            /*如果存在绑定服务数据&&服务数据为待复核状态--可以复核;其余情况不可进行复核*/
            if (!(curService != null && curService.State == EnumContractServiceState.TO_BE_REVIEW))
                throw new ApiException("当前申请无法进行复核操作");
            #endregion
            var now = DateTime.Now;
            //驳回
            if (!review_In.State)
                curService.State = EnumContractServiceState.RETURNED;
            else
            {
                //合同信息验证
                var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
                if (contractInfo == null)
                    throw new ApiException("未找到合同信息");
                if (string.IsNullOrEmpty(contractInfo.ContractNum))
                    throw new ApiException("未到账合同不能开通服务产品");
                //处理申请信息
                apply.State = EnumProcessStatus.Pass.ToInt();
                apply.ReviewerDate = now;
                apply.ReviewerId = UserId;
                apply.UpdateDate = now;
                apply.UpdateUser = UserId;
                apply.Feedback = review_In.FeedBack;
                DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(apply);
                
                
                //服务状态置位生效
                curService.State = EnumContractServiceState.VALID;
                //判断 ProcessingType
                //var origContacts = new List<Db_crm_contract_serviceinfo_college_contacts>();
                //获取当前服务待开通的慧思学院账号信息
                var tobeReviewContacts = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetToBeReviewContactsByServId(curService.Id);
                if (curService.ProcessingType == EnumProcessingType.Add.ToInt())
                {
                    tobeReviewContacts.ForEach(item =>
                    {
                        item.State = EnumContractServiceCollegeContactsState.Normal;
                        item.UpdateDate = now;
                        item.UpdateUser = UserId;
                        DbOpe_crm_contract_serviceinfo_college_contacts.Instance.UpdateQueue(item);
                    });
                }
                if (curService.ProcessingType == EnumProcessingType.Change.ToInt())
                {//服务申请修改 需要找到当前的服务记录数据(根据ContractProductInfoId)，修改isChanged=true，补充ChangedId,当前数据补充HistoryId
                 //获取原服务数据进行修改
                    var originalServe = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByConProInfoId(apply.ContractProductInfoId);
                    originalServe.IsChanged = true;
                    originalServe.ChangedId = curService.Id;
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    originalServe.State = EnumContractServiceState.INVALID;
                    DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(originalServe);
                    //变更服务补充HistoryId
                    curService.HistoryId = originalServe.Id;
                    //获取被变更服务的已开通&异常的慧思学院账号信息
                    var origContacts = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetServiceContactsByServId(originalServe.Id);
                    tobeReviewContacts.ForEach(item =>
                    {
                        //如果不是新增的账户，账户状态维持原来的状态
                        if (origContacts.Any(e => e.Phone == item.Phone))
                            item.State = origContacts.Where(e => e.Phone == item.Phone).First().State;
                        else
                            item.State = EnumContractServiceCollegeContactsState.Normal;
                        DbOpe_crm_contract_serviceinfo_college_contacts.Instance.UpdateQueue(item);
                    });
                    //原账号列表对传入的账号列表取差集，得到待停用的账号列表
                    var tobeStopContacts = origContacts.ExceptBy(tobeReviewContacts.Select(e => e.Phone), e => e.Phone).ToList();
                    //停用账号删除的账号
                    DbOpe_crm_contract_serviceinfo_college_contacts.Instance.StopContacts(tobeStopContacts.Select(e => e.Phone).ToList());
                }

                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = apply.CreateUser;
                message.MessageTypeToId = apply.Id;
                message.MessagemMainAboutDes = contract.ContractName;
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.CollegeService, EnumMessageStateInfo.Pass, apply.ContractId);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            }

            //记录  复核备注&审核反馈
            curService.ReviewerId = UserId;
            curService.ReviewerTime = now;
            curService.ReviewerRemark = review_In.ReviewerRemark;
            curService.UpdateDate = now;
            curService.UpdateUser = UserId;
            curService.Remark = review_In.FeedBack;
            DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(curService);
            string dataState = String.Empty;
            if (review_In.State)
                dataState = EnumContractServiceOpenState.Open.GetEnumDescription();
            else
                dataState = EnumContractServiceOpenState.Returned.GetEnumDescription();
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_college, Db_crm_contract>("慧思学院服务审批流程", apply.Id, curService, contract, apply.ApplicantId, curService.Remark, dataState, "复核");

            #region 2024.3.19改为所有流转记录都保存审核反馈，这段内容注释
            /*string dataState = String.Empty;
            string remark = String.Empty;
            if (review_In.State)
            {
                dataState = EnumContractServiceOpenState.Open.GetEnumDescription();
                remark = curService.Remark;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_college, Db_crm_contract>("慧思学院服务审批流程", apply.Id, curService, contract, apply.ApplicantId, remark, dataState, "复核");
            }
            else
            {
                dataState = EnumContractServiceOpenState.Returned.GetEnumDescription();
                remark = curService.ReviewerRemark;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_college, Db_crm_contract>("慧思学院服务审批流程", apply.Id, curService, contract, remark, dataState, "复核");
            }*/
            #endregion
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_db_appl.Instance.SaveQueues();
            //11.20 开通服务要刷新合同的保护截止日
            if (review_In.State)
                DbOpe_crm_contract.Instance.RefreshContractProtectDate(apply.ContractId);
        }


        /// <summary>
        /// 根据申请id获取合同服务申请信息_慧思学院信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public GetContractServiceInfoApplyInfoCollegeByApplId_Out GetContractServiceInfoApplyInfoCollegeByApplId(string Id)
        {
            //获取申请详细信息
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.QueryByPrimaryKey(Id)
                .MappingTo<GetContractServiceInfoApplyInfoCollegeByApplId_Out_Mid>();
            apply.IsFree = StringUtil.IsNullOrEmpty(apply.ContractProductInfoId);
            //获取当前申请的慧思学院账号信息
            var contacts = DbOpe_crm_contract_productserviceinfo_college_appl_contacts.Instance.GetApplyContacts(apply.Id);
            //获取当前已开通服务的账号信息
            if (!string.IsNullOrEmpty(apply.ContractProductInfoId))
            {
                var oriContacts = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetOriginalServiceContacts(apply.ContractProductInfoId);
                if (oriContacts.Count > 0)
                {
                    //取申请的账号和当前开通服务账号的并集
                    var unionContacts = contacts.Union(oriContacts).ToList();
                    //var distributeIdList_ToBeDelete = distributeIdList_Before.ExceptBy(distributeIdList_Rest.Select(e => e.Id), e => e.Id).ToList()
                    unionContacts.ForEach(contact =>
                    {
                        //同时出现在 contacts 和 oriContacts，状态为保留
                        if (contacts.Any(e => e.Phone == contact.Phone) && oriContacts.Any(e => e.Phone == contact.Phone))
                            contact.UpdateState = EnumCollegeContactsUpdateState.Original;
                        //出现在 contacts，但未出现在 oriContacts，状态为新增
                        if (contacts.Any(e => e.Phone == contact.Phone) && !oriContacts.Any(e => e.Phone == contact.Phone))
                            contact.UpdateState = EnumCollegeContactsUpdateState.Insert;
                        //未出现在 contacts，但出现在 oriContacts，状态为删除
                        if (!contacts.Any(e => e.Phone == contact.Phone) && oriContacts.Any(e => e.Phone == contact.Phone))
                            contact.UpdateState = EnumCollegeContactsUpdateState.Delete;
                        if (apply.Contacts == null)
                            apply.Contacts = new List<GetContractServiceInfoApplyInfoCollegeByApplId_Out_Sub>();
                        apply.Contacts.Add(contact);
                    });
                }
                else
                    apply.Contacts = contacts;
            }
            else
                apply.Contacts = contacts;

            apply.Contacts = apply.Contacts.OrderBy(e => e.Phone).ToList();
            //获取合同详细信息
            apply.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
            //获取产品详细信息,如果是普通服务，从合同产品表中获取信息，如果是赠送服务，从产品表中获取信息
            if (!string.IsNullOrEmpty(apply.ContractProductInfoId))
                apply.ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(apply.ContractProductInfoId).MappingTo<GetContractServiceApplInfoCollegeByApplId_Out_ProductInfo>();
            else
            {
                apply.ProductInfo = DbOpe_crm_product.Instance.QueryByPrimaryKey(apply.ProductId).MappingTo<GetContractServiceApplInfoCollegeByApplId_Out_ProductInfo>();
                apply.ProductInfo.OpeningMonths = apply.ServiceMonth.Value;
                apply.ProductInfo.FirstOpeningMonths = apply.ServiceMonth.Value;
            }
            apply.ProductInfo.PrimaryAccountsNum = apply.PrimaryAccountsNum;

            //apply.RegisteredRemark = DbOpe_crm_contract_serviceinfo_college.Instance.GetRegistereRemark(apply.Id);
            apply.ReviewInfo = DbOpe_crm_contract_serviceinfo_college.Instance.GetReviewInfo(Id);
            apply.ReviewFeedbacks = DbOpe_crm_contract_serviceinfo_college.Instance.GetReviewFeedbacks(Id);
            //到账备注列表
            apply.ReceiptRemarks = DbOpe_crm_contract_receiptregister.Instance.GetRemarksByContractId(apply.ContractId);
            //合同的所有到账信息
            apply.ReceiptRegisterCollectionList = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(apply.ContractId, String.Empty, EnumProductType.GlobalWitsSchool, apply.ReviewInfo == null ? "" : apply.ReviewInfo.Id);
            //当前服务已绑定的到账信息
            if (apply.ReviewInfo != null)
                apply.LinkedReceiptRegisterIds = DbOpe_crm_contract_receiptregister_service.Instance.GetReceiptRegisterIdsByServiceId(apply.ReviewInfo.Id);
            var retApply = apply.MappingTo<GetContractServiceInfoApplyInfoCollegeByApplId_Out>();
            return retApply;
        }

        /// <summary>
        /// 撤销合同服务信息慧思学院申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        /// <exception cref="ApiException"></exception>
        public void RevokeContractProductServiceInfoCollegeAudit(OperateContractProductServiceInfoCollegeAudit_In operate_In)
        {

            //声明可撤销状态的集合, 待复核、被驳回、拒绝、已开通状态的数据可以进行撤销操作
            var couldRevokeOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.ToBeReview, EnumContractServiceOpenState.Returned, EnumContractServiceOpenState.Refuse, EnumContractServiceOpenState.Open };
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetServiceCollegeApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以撤销
            if (!couldRevokeOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可撤销");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByApplyId(operate_In);
            var now = DateTime.Now;

            //待复核状态撤销到待开通/待变更状态：申请数据不变，服务数据状态改为TO_BE_OPENED(10)
            if (apply.OpenState == EnumContractServiceOpenState.ToBeReview)
            {
                //服务数据状态改为TO_BE_OPENED(10)
                service.State = EnumContractServiceState.TO_BE_OPENED;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(service);
            }
            //被驳回状态撤销到待复核状态：申请数据不变，服务数据状态改为TO_BE_REVIEW(20)
            else if (apply.OpenState == EnumContractServiceOpenState.Returned)
            {
                //服务数据状态改为TO_BE_REVIEW(20)
                service.State = EnumContractServiceState.TO_BE_REVIEW;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(service);
            }
            //拒绝状态撤销到待开通/待变更状态：申请数据状态改为EnumProcessStatus.Submit(1),其他申请置位失效
            else if (apply.OpenState == EnumContractServiceOpenState.Refuse)
            {
                //申请数据状态改为EnumProcessStatus.Submit(1)
                apply.State = EnumProcessStatus.Submit.ToInt();
                apply.UpdateDate = now;
                apply.UpdateUser = UserId;
                DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_college_appl>());
                
                //服务数据状态改为TO_BE_OPENED(10)
                service.State = EnumContractServiceState.TO_BE_OPENED;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(service);
                //其他的申请要置为无效，只保留当前操作的这个申请
                var otherAppls = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetDataList(g => g.ContractProductInfoId == apply.ContractProductInfoId && g.Id != operate_In.ApplyId);
                otherAppls.ForEach(a =>
                {
                    DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateData(r => new Db_crm_contract_productserviceinfo_college_appl { IsInvalid = (int)EnumIsInvalid.Invalid }, r => a.Id == r.Id);
                });
            }
            //已开通状态撤销到待复核状态
            else if (apply.OpenState == EnumContractServiceOpenState.Open)
            {
                //申请数据状态改为EnumProcessStatus.Submit(1)
                apply.State = EnumProcessStatus.Submit.ToInt();
                apply.UpdateDate = now;
                apply.UpdateUser = UserId;
                //服务状态变更为待复核EnumContractServiceState.TO_BE_REVIEW(20)
                DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_college_appl>());
                
                service.State = EnumContractServiceState.TO_BE_REVIEW;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(service);
                //其他的申请要置为无效，只保留当前操作的这个申请
                var otherAppls = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetDataList(g => g.ContractProductInfoId == apply.ContractProductInfoId && g.Id != operate_In.ApplyId);
                otherAppls.ForEach(a =>
                {
                    DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateData(r => new Db_crm_contract_productserviceinfo_college_appl { IsInvalid = (int)EnumIsInvalid.Invalid }, r => a.Id == r.Id);
                });
                //如果是服务变更，原服务恢复正常
                if (apply.ProcessingType == EnumProcessingType.Change.ToInt())
                {
                    var originalServe = DbOpe_crm_contract_serviceinfo_college.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    originalServe.IsChanged = false;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(originalServe);
                }

            }
            //workflow撤销
            string state = ((EnumContractServiceOpenState)apply.OpenState).GetEnumDescription();
            BLL_WorkFlow.Instance.CancelWorkflowPending("慧思学院服务审批流程", apply.Id, state, apply);
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.SaveQueues();

            #region 原撤销逻辑 2023.12.13注释
            /*
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
            //待开通或待变更(申请为Submit)状态的数据不可撤销
            if (apply.State == EnumProcessStatus.Submit.ToInt())
                throw new ApiException("所选的申请不可撤销");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByApplyId(operate_In);
            //若存在服务，过期(服务为过期)、作废(服务为作废或失效)状态的数据不可撤销
            if (service != null && (service.State == EnumContractServiceState.INVALID || service.State == EnumContractServiceState.VOID || service.State == EnumContractServiceState.OUT))
                throw new ApiException("所选的申请不可撤销");
            //申请的状态改为待开通
            apply.State = EnumProcessStatus.Submit.ToInt();
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(apply);
            //如果存在服务，则为开通-->待开通
            if (service != null)
            {
                //服务的状态改为作废
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(service);
                //删除服务开通的账号
                DbOpe_crm_contract_serviceinfo_college_contacts.Instance.DeleteRevokeContacts(service.Id, UserId);
                //如果是服务变更，原服务恢复正常
                if (!string.IsNullOrEmpty(service.HistoryId))
                {
                    var originalServe = DbOpe_crm_contract_serviceinfo_college.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.IsChanged = false;
                    originalServe.ChangedId = null;
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                    {
                        originalServe.State = EnumContractServiceState.VALID;
                        DbOpe_crm_contract_serviceinfo_college_contacts.Instance.RecoverContacts(originalServe.Id, UserId);
                    }

                    DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(originalServe);
                }
                //workflow撤销
                BLL_WorkFlow.Instance.CancelWorkflowPending("慧思学院服务审批流程", service.Id);
            }

            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.SaveQueues();
            //旧的申请要置为无效，只保留最新的这个申请
            var otherAppls = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetDataList(g => g.ContractProductInfoId == apply.ContractProductInfoId && g.Id != operate_In.ApplyId);
            otherAppls.ForEach(a =>
            {
                //a.IsInvalid = (int)EnumIsInvalid.Invalid;
                DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateData(r => new Db_crm_contract_productserviceinfo_college_appl { IsInvalid = (int)EnumIsInvalid.Invalid }, r => a.Id == r.Id);
            });
*/
            #endregion
        }

        /// <summary>
        /// 作废合同服务信息慧思学院申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        /// <exception cref="ApiException"></exception>
        public void VoidContractProductServiceInfoCollegeAudit(OperateContractProductServiceInfoCollegeAudit_In operate_In)
        {
            //声明可作废状态的集合, 过期、作废状态的数据不可以进行作废操作
            var couldRevokeOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.OverDue, EnumContractServiceOpenState.Void };
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetServiceCollegeApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以撤销
            if (couldRevokeOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可撤销");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByApplyId(operate_In);
            //当前时间
            var now = DateTime.Now;
            //申请的状态改为作废
            apply.State = EnumProcessStatus.Void.ToInt();
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_college_appl>());
            
            //如果存在服务，处理服务后续内容
            if (service != null)
            {
                //服务的状态改为作废
                service.State = EnumContractServiceState.VOID;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(service);
            }
            //服务变更处理
            if (apply.ProcessingType == EnumProcessingType.Change.ToInt())
            {
                var originalServe = new Db_crm_contract_serviceinfo_college();
                //已开通服务撤销，原服务恢复正常
                if (apply.OpenState == EnumContractServiceOpenState.Open)
                {
                    originalServe = DbOpe_crm_contract_serviceinfo_college.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(originalServe);
                }
                //未开通服务的撤销，获取原服务数据
                else
                    originalServe = DbOpe_crm_contract_serviceinfo_college.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false && g.IsHistory == false);
                //将原服务数据置位生效状态
                if (originalServe.ProductServiceInfoCollegeApplId.IsNotNullOrEmpty())
                {
                    var originalAppl = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetDataById(originalServe.ProductServiceInfoCollegeApplId);
                    originalAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(originalAppl);
                }
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.SaveQueues();
            #region 原作废逻辑 2023.12.13注释
            /*
                //获取申请数据 
                var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
                *//* 2023.10.7 改为拒绝状态可作废
                //申请为拒绝状态的数据不可以作废
                if (apply.State == EnumProcessStatus.Refuse.ToInt())
                    throw new ApiException("所选的申请不可作废");
                *//*
                //获取服务数据
                var service = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByApplyId(operate_In);
                //若存在服务，过期(服务为过期)、作废(服务为作废或失效)状态的数据不可以作废
                if (service != null && (service.State == EnumContractServiceState.INVALID || service.State == EnumContractServiceState.VOID || service.State == EnumContractServiceState.OUT))
                    throw new ApiException("所选的申请不可作废");


                *//*var currentService = DbOpe_crm_contract_serviceinfo_college.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId);
                    if (currentService != null)
                    {
                        var currentAppl = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetDataById(currentService.ProductServiceInfoCollegeApplId);
                        currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                        DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(currentAppl);
                    }*//*

                //申请的状态改为作废
                apply.State = EnumProcessStatus.Void.ToInt();
                apply.UpdateUser = UserId;
                apply.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(apply);
                //如果存在服务，处理服务后续内容
                if (service != null)
                {
                    //服务的状态改为作废
                    service.State = EnumContractServiceState.VOID;
                    service.UpdateUser = UserId;
                    service.UpdateDate = DateTime.Now;
                    DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(service);
                    //删除服务开通的账号
                    DbOpe_crm_contract_serviceinfo_college_contacts.Instance.DeleteRevokeContacts(service.Id, UserId);
                    //如果是服务变更，原服务恢复正常
                    if (!string.IsNullOrEmpty(service.HistoryId))
                    {
                        var originalServe = DbOpe_crm_contract_serviceinfo_college.Instance.QueryByPrimaryKey(service.HistoryId);
                        originalServe.ChangedId = null;
                        originalServe.IsChanged = false;
                        originalServe.UpdateDate = DateTime.Now;
                        originalServe.UpdateUser = UserId;
                        if (originalServe.ServiceCycleEnd < DateTime.Now)
                            originalServe.State = EnumContractServiceState.OUT;
                        else
                        {
                            originalServe.State = EnumContractServiceState.VALID;
                            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.RecoverContacts(originalServe.Id, UserId);
                        }
                        DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(originalServe);
                        var originalAppl = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetDataById(originalServe.ProductServiceInfoCollegeApplId);
                        originalAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                        DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(originalAppl);
                    }
                }
                //如果作废的是服务变更申请，且没有开通服务，则解除被变更的服务锁定状态
                else if (apply.ProcessingType == (int)EnumProcessingType.Change)
                {
                    //获取被变更的服务数据
                    var currentService = DbOpe_crm_contract_serviceinfo_college.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false);
                    if (currentService != null)
                    {
                        var currentAppl = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetDataById(currentService.ProductServiceInfoCollegeApplId);
                        currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                        DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(currentAppl);
                    }
                }

                //提交sql队列
                DbOpe_crm_contract_productserviceinfo_college_appl.Instance.SaveQueues();
            */
            #endregion
        }


        /// <summary>
        /// 删除合同服务信息慧思学院申请信息
        /// 作废的可以删除，拒绝的可以删除
        /// </summary>
        /// <param name="operate_In"></param>
        public void DeleteContractProductServiceInfoCollegeAudit(OperateContractProductServiceInfoCollegeAudit_In operate_In)
        {

            //声明可删除状态的集合, 拒绝、作废状态的数据可以进行删除操作
            var couldDeleteOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.Refuse, EnumContractServiceOpenState.Void };
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetServiceCollegeApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以删除
            if (!couldDeleteOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可删除");
            //删除申请
            apply.Deleted = true;
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_college_appl>());
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByApplyId(operate_In);
            if (service != null)
            {
                //删除服务
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(service);
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.SaveQueues();
            #region 原删除逻辑 2023.12.13注释
            /*
            //获取申请数据 
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
            //申请待开通状态的数据不可以删除
            if (apply.State == EnumProcessStatus.Submit.ToInt())
                throw new ApiException("所选的申请不可删除");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByApplyId(operate_In);
            //若存在服务，正常状态的数据不可以作废
            if (service != null && service.State == EnumContractServiceState.VALID)
                throw new ApiException("所选的申请不可删除");
            //删除申请
            apply.Deleted = true;
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(apply);
            if (service != null)
            {
                //删除服务
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_college.Instance.UpdateQueue(service);
            }
            //如果作废的是服务变更申请，则解除被变更的服务锁定状态
            if (apply.ProcessingType == (int)EnumProcessingType.Change)
            {
                //获取被变更的服务数据
                var currentService = DbOpe_crm_contract_serviceinfo_college.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false);
                if (currentService != null)
                {
                    var currentAppl = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetDataById(currentService.ProductServiceInfoCollegeApplId);
                    currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_college_appl.Instance.UpdateQueue(currentAppl);
                }
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_college_appl.Instance.SaveQueues();
            */
            #endregion
        }


        /// <summary>
        /// 根据申请Id批量修改服务用户状态
        /// </summary>
        /// <param name="applyList_In"></param>
        public void SetContractServiceInfoCollegeContactsByApplyIds(SetContractServiceInfoCollegeContactsByApplyIds_In applyList_In)
        {
            var applyIdList = applyList_In.Ids.Split(',').ToList();
            var serviceIds = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoIdByApplyIds(applyIdList);
            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.SetContractServiceInfoCollegeContactsByApplyId(serviceIds, applyList_In.State, UserId);
        }

        /// <summary>
        /// 设置慧思学院用户状态信息
        /// </summary>
        /// <param name="contacts_In"></param>
        public void SetCollegeContacts(SetCollegeContacts_In contacts_In)
        {
            var ContactsList = new List<Db_crm_contract_serviceinfo_college_contacts>();
            foreach (var contact in contacts_In.Contacts)
            {
                if (string.IsNullOrEmpty(contact.ContactId))//新增
                {
                    var addContact = contact.MappingTo<Db_crm_contract_serviceinfo_college_contacts>();
                    addContact.ContractServiceInfoCollegeId = contacts_In.ServiceId;
                    ContactsList.Add(addContact);
                }
                else //修改
                    DbOpe_crm_contract_serviceinfo_college_contacts.Instance.SetCollegeContacts(contact, UserId);

            }
            //批量新增
            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.InsertListData(ContactsList);
            //提交sql队列
            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.SaveQueues();
        }


        /// <summary>
        /// 验证开通账号是否存在重复
        /// </summary>
        /// <param name="check_In"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public CheckHasSameValidContacts_Out CheckHasSameValidContacts(CheckHasSameValidContacts_In check_In)
        {
            //定义返回对象
            var retObj = new CheckHasSameValidContacts_Out();
            retObj.SameType = 0;
            retObj.contacts = new List<CheckHasSameValidContacts_Out_Sub>();
            //将申请的账号列表转置，增加IsSame属性
            var applyContacts = check_In.contacts.MappingTo<List<CheckHasSameValidContacts_Out_Sub>>();
            //获取申请账号列表中的重复手机号
            var applySamePhoneList = check_In.contacts.GroupBy(e => e.Phone).Where(e => e.Count() > 1).Select(e => e.Key).ToList();
            //申请的账号中存在重复手机号
            if (applySamePhoneList.Count() > 0)
            {
                retObj.SameType = 1;
                foreach (var contact in applyContacts)
                {
                    if (applySamePhoneList.Contains(contact.Phone))
                        contact.IsSame = true;
                    else
                        contact.IsSame = false;
                    retObj.contacts.Add(contact);
                }
            }
            else
            {
                //获取传入的账号Phone列表
                var phones = check_In.contacts.Select(e => e.Phone).ToList();
                //定义重复Phone列表
                var sameContactPhones = new List<string>();
                if (!string.IsNullOrEmpty(check_In.ProductServiceInfoCollegeApplId))
                {//存在appId,是开通时候验证，使用ContractProductInfoId来排除当前合同已开通服务的账号
                    var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.QueryByPrimaryKey(check_In.ProductServiceInfoCollegeApplId);
                    sameContactPhones = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetSameContacts(phones, apply.ContractProductInfoId);
                }
                else if (check_In.ProcessType == EnumProcessingType.Change.ToInt())
                {
                    sameContactPhones = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetSameContacts(phones, check_In.ContractProductInfoId);
                }
                else
                {//不存在applyId,是申请时候验证，比较当前开通的所有服务的启用/异常账号
                    sameContactPhones = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetSameContacts(phones);
                }
                //如果有重复
                if (sameContactPhones.Count > 0)
                {
                    retObj.SameType = 2;
                    foreach (var contact in applyContacts)
                    {
                        if (sameContactPhones.Contains(contact.Phone))
                            contact.IsSame = true;
                        else
                            contact.IsSame = false;

                        retObj.contacts.Add(contact);
                    }
                }
                else
                    retObj.contacts = applyContacts;
            }
            return retObj;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="present_In"></param>
        public void PresentedCollegeService(PresentedCollegeService_In present_In)
        {
            DateTime startDate = present_In.ServiceCycleStart.Value;
            DateTime endDate = present_In.ServiceCycleEnd.Value;
            int monthsDifference = ((endDate.Year - startDate.Year) * 12) + endDate.Month - startDate.Month;
            int ServiceMonth = present_In.ServiceMonth.Value;
            if (monthsDifference != ServiceMonth)
                throw new ApiException("服务月份设置不正确");
            Db_crm_contract contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(present_In.ContractId);
            //合同审核通过后，才可申请服务，并只可申请合同内签约产品的服务项目。
            if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
                throw new ApiException("当前合同状态下，不可以添加合同服务信息-慧思学院申请信息");
            //判断当前合同是否存在慧思学院产品，如果存在则无法赠送 慧思学院产品类型-6
            //if (DbOpe_crm_contract_productserviceinfo_college_appl.Instance.CheckContractHasCollegeProduct(present_In.ContractId))
            if (DbOpe_crm_contract_productinfo.Instance.CheckContractHasCollegeProduct(present_In.ContractId))
                throw new ApiException("当前合同已经存在慧思学院产品，无法赠送");
            if (DbOpe_crm_contract_productserviceinfo_college_appl.Instance.CheckContractHasPresentedCollegeProduct(present_In.ContractId))
                throw new ApiException("当前合同已经赠送过慧思学院产品，无法再次赠送");
            if (present_In.CollegeApplContacts.Count == 0)
                throw new ApiException("慧思学院申请联系人不可为空");
            /*if (addContractProductServiceInfoCollegeApplIn.CollegeApplContacts.Select(r => r.Phone).Distinct().Count() < addContractProductServiceInfoCollegeApplIn.CollegeApplContacts.Count())
            {
                throw new ApiException("慧思学院申请联系人电话号码重复");
            }*/
            Db_crm_contract_productserviceinfo_college_appl college_appl = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.PresentedGlobalSearchService(present_In);
            string dataState = Dictionary.ProcessStatus.First(e => e.Value == college_appl.State.ToInt().ToString()).Name;
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_college_appl, Db_crm_contract>("慧思学院服务审批流程", college_appl.Id, college_appl, contract, present_In.Remark, "待开通", "申请");//dataState, "申请");
            /*初审流程*/
            var now = DateTime.Now;
            //创建服务数据
            var servCollegeInfo = college_appl.MappingTo<Db_crm_contract_serviceinfo_college>();
            servCollegeInfo.Id = Guid.NewGuid().ToString();
            servCollegeInfo.ProductServiceInfoCollegeApplId = college_appl.Id;
            servCollegeInfo.State = EnumContractServiceState.TO_BE_REVIEW;
            servCollegeInfo.IsChanged = false;
            servCollegeInfo.RegisteredId = UserId;
            servCollegeInfo.RegisteredTime = now;
            servCollegeInfo.IsHistory = false;
            DbOpe_crm_contract_serviceinfo_college.Instance.Insert(servCollegeInfo);
            var contacts = new List<Db_crm_contract_serviceinfo_college_contacts>();
            //循环插入慧思学院人员
            present_In.CollegeApplContacts.ForEach(item =>
            {
                Db_crm_contract_serviceinfo_college_contacts contact = new Db_crm_contract_serviceinfo_college_contacts();
                contact.ContractServiceInfoCollegeId = servCollegeInfo.Id;
                contact.Name = item.Name;
                contact.Phone = item.Phone;
                contact.State = EnumContractServiceCollegeContactsState.ToBeReview;
                contacts.Add(contact);
            });
            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.InsertListData(contacts);
            //开通的workflow记录
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_college, Db_crm_contract>("慧思学院服务审批流程", college_appl.Id, servCollegeInfo, contract, college_appl.Feedback, EnumContractServiceOpenState.ToBeReview.GetEnumDescription(), "初审");

        }

        /// <summary>
        /// 慧思学院服务自动通过
        /// </summary>
        /// <param name="contractId"></param>
        /// <param name="gtisServId"></param>
        public void AutoAuditAndReviewCollegeAppl(string contractId, string gtisServId)
        {
            //检查合同中是否包含慧思学院
            //if (DbOpe_crm_contract_productserviceinfo_college_appl.Instance.CheckContractHaveCollegeService(contractId))
            //{
            //获取可以自动通过的申请数据(待开通、待变更、待复核、被驳回数据)
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetAutoPassApplyInfoByContractId(contractId);
            //如果存在申请数据，执行自动开通
            if (apply != null)
            {
                Db_crm_contract contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(contractId);
                var now = DateTime.Now;
                var college_appl = apply.MappingTo<Db_crm_contract_productserviceinfo_college_appl>();
                college_appl.State = EnumProcessStatus.Pass.ToInt();
                college_appl.ReviewerDate = now;
                college_appl.ReviewerId = UserId;
                college_appl.UpdateDate = now;
                college_appl.UpdateUser = UserId;
                DbOpe_crm_contract_productserviceinfo_college_appl.Instance.Update(college_appl);

                //待开通和待变更
                if (apply.ServiceOpenState == EnumContractServiceOpenState.ToBeOpened || apply.ServiceOpenState == EnumContractServiceOpenState.TobeChanged)
                {
                    //私有方法：补充初审数据、复核数据、人员数据
                    AutoAuditAndReviewCollegeAppl_Private(college_appl, contract, gtisServId);
                }
                //待复核
                if (apply.ServiceOpenState == EnumContractServiceOpenState.ToBeReview)
                {
                    //当前的服务数据
                    var curService = DbOpe_crm_contract_serviceinfo_college.Instance.GetData(g => g.ProductServiceInfoCollegeApplId == apply.Id && g.IsHistory == false);
                    if (curService.ProcessingType == EnumProcessingType.Add.ToInt())
                    {
                        //修改人员状态
                        var contacts = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetToBeReviewContactsByServId(curService.Id);
                        //循环修改慧思学院人员
                        contacts.ForEach(item =>
                        {
                            item.State = EnumContractServiceCollegeContactsState.Normal;
                            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.Update(contacts);
                        });
                    }
                    else if (curService.ProcessingType == EnumProcessingType.Change.ToInt())
                    {//服务申请修改 需要找到当前的服务记录数据(根据ContractProductInfoId)，修改isChanged=true，补充ChangedId,当前数据补充HistoryId
                     //获取原服务数据进行修改
                        var originalServe = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByConProInfoId(apply.ContractProductInfoId);
                        originalServe.IsChanged = true;
                        originalServe.ChangedId = curService.Id;
                        originalServe.State = EnumContractServiceState.INVALID;
                        DbOpe_crm_contract_serviceinfo_college.Instance.Update(originalServe);
                        //变更服务补充HistoryId
                        curService.HistoryId = originalServe.Id;
                        //获取被变更服务的已开通&异常的慧思学院账号信息
                        var origContacts = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetServiceContactsByServId(originalServe.Id);
                        var tobeReviewContacts = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetToBeReviewContactsByServId(curService.Id);
                        tobeReviewContacts.ForEach(item =>
                        {
                            //如果不是新增的账户，账户状态维持原来的状态
                            if (origContacts.Any(e => e.Phone == item.Phone))
                                item.State = origContacts.Where(e => e.Phone == item.Phone).First().State;
                            else
                                item.State = EnumContractServiceCollegeContactsState.Normal;
                            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.Update(item);
                        });
                        //原账号列表对传入的账号列表取差集，得到待停用的账号列表
                        var tobeStopContacts = origContacts.ExceptBy(tobeReviewContacts.Select(e => e.Phone), e => e.Phone).ToList();
                        //停用账号删除的账号
                        DbOpe_crm_contract_serviceinfo_college_contacts.Instance.StopContacts(tobeStopContacts.Select(e => e.Phone).ToList());
                    }
                    //补充数据
                    curService.ReviewerId = UserId;
                    curService.ReviewerTime = now;
                    curService.ReviewerRemark = "自动通过";
                    curService.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_college.Instance.Update(curService);
                    //补充服务到账绑定关系
                    var rrIds = DbOpe_crm_contract_receiptregister_service.Instance.GetReceiptRegisterIdsByServiceId(gtisServId);
                    if (rrIds != null && rrIds.Count > 0)
                    {
                        DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptData(rrIds, curService.Id, EnumProductType.GlobalWitsSchool);
                    }
                    //开通的workflow记录-复核
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_college, Db_crm_contract>("慧思学院服务审批流程", apply.Id, curService, contract, apply.ApplicantId, curService.ReviewerRemark, EnumContractServiceOpenState.Open.GetEnumDescription(), "复核");
                }
                //被驳回
                if (apply.ServiceOpenState == EnumContractServiceOpenState.Returned)
                {
                    //将当前的服务数据置位历史
                    var curService = DbOpe_crm_contract_serviceinfo_college.Instance.GetData(g => g.ProductServiceInfoCollegeApplId == apply.Id && g.IsHistory == false);
                    curService.IsHistory = true;
                    DbOpe_crm_contract_serviceinfo_college.Instance.Update(curService);
                    //私有方法：补充初审数据、复核数据、人员数据
                    AutoAuditAndReviewCollegeAppl_Private(college_appl, contract, gtisServId);
                }
            }
            //}
        }

        /// <summary>
        /// 私有方法：补充初审数据、复核数据、人员数据
        /// </summary>
        /// <param name="college_appl"></param>
        /// <param name="contract"></param>
        /// <param name="gtisServId"></param>
        private void AutoAuditAndReviewCollegeAppl_Private(Db_crm_contract_productserviceinfo_college_appl college_appl, Db_crm_contract contract, string gtisServId)
        {
            var now = DateTime.Now;
            //初审数据
            var servCollegeInfo = college_appl.MappingTo<Db_crm_contract_serviceinfo_college>();
            servCollegeInfo.Id = Guid.NewGuid().ToString();
            servCollegeInfo.ProductServiceInfoCollegeApplId = college_appl.Id;
            servCollegeInfo.State = EnumContractServiceState.VALID;
            servCollegeInfo.IsChanged = false;
            servCollegeInfo.RegisteredId = UserId;
            servCollegeInfo.RegisteredTime = now;
            servCollegeInfo.IsHistory = false;
            servCollegeInfo.ServiceCycleEnd = servCollegeInfo.ServiceCycleEnd.Value.AddDays(1);
            //复核数据
            servCollegeInfo.ReviewerId = UserId;
            servCollegeInfo.ReviewerTime = now;
            servCollegeInfo.ReviewerRemark = "自动通过";
            //人员数据
            var applyContacts = DbOpe_crm_contract_productserviceinfo_college_appl_contacts.Instance.GetApplyContactsOriModel(college_appl.Id);
            var contacts = new List<Db_crm_contract_serviceinfo_college_contacts>();
            //循环插入慧思学院人员
            applyContacts.ForEach(item =>
            {
                Db_crm_contract_serviceinfo_college_contacts contact = new Db_crm_contract_serviceinfo_college_contacts();
                contact.ContractServiceInfoCollegeId = servCollegeInfo.Id;
                contact.Name = item.Name;
                contact.Phone = item.Phone;
                contact.State = EnumContractServiceCollegeContactsState.Normal;
                contacts.Add(contact);
            });
            if (college_appl.ProcessingType == EnumProcessingType.Change.ToInt())
            {
                //获取原服务数据进行修改
                var originalServe = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoByConProInfoId(college_appl.ContractProductInfoId);
                originalServe.IsChanged = true;
                originalServe.ChangedId = servCollegeInfo.Id;
                originalServe.State = EnumContractServiceState.INVALID;
                DbOpe_crm_contract_serviceinfo_college.Instance.Update(originalServe);
                //变更服务补充HistoryId
                servCollegeInfo.HistoryId = originalServe.Id;

                //获取被变更服务的已开通&异常的慧思学院账号信息
                var origContacts = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetServiceContactsByServId(originalServe.Id);

                contacts.ForEach(item =>
                {
                    //如果不是新增的账户，账户状态维持原来的状态
                    if (origContacts.Any(e => e.Phone == item.Phone))
                        item.State = origContacts.Where(e => e.Phone == item.Phone).First().State;
                    DbOpe_crm_contract_serviceinfo_college_contacts.Instance.Update(item);
                });
                //原账号列表对传入的账号列表取差集，得到待停用的账号列表
                var tobeStopContacts = origContacts.ExceptBy(contacts.Select(e => e.Phone), e => e.Phone).ToList();
                //停用账号删除的账号
                DbOpe_crm_contract_serviceinfo_college_contacts.Instance.StopContacts(tobeStopContacts.Select(e => e.Phone).ToList());

            }
            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.InsertListData(contacts);
            servCollegeInfo.PrimaryAccountsNum = contacts.Count;
            DbOpe_crm_contract_serviceinfo_college.Instance.Insert(servCollegeInfo);

            //补充服务到账绑定关系
            var rrIds = DbOpe_crm_contract_receiptregister_service.Instance.GetReceiptRegisterIdsByServiceId(gtisServId);
            if (rrIds != null && rrIds.Count > 0)
            {
                DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptData(rrIds, servCollegeInfo.Id, EnumProductType.GlobalWitsSchool);
            }
            //开通的workflow记录-初审
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_college, Db_crm_contract>("慧思学院服务审批流程", college_appl.Id, servCollegeInfo, contract, college_appl.Feedback, EnumContractServiceOpenState.ToBeReview.GetEnumDescription(), "初审");
            //开通的workflow记录-复核
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_college, Db_crm_contract>("慧思学院服务审批流程", college_appl.Id, servCollegeInfo, contract, college_appl.ApplicantId, servCollegeInfo.ReviewerRemark, EnumContractServiceOpenState.Open.GetEnumDescription(), "复核");
        }

    }
}
