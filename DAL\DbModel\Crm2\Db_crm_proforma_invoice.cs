using System;
using System.ComponentModel.DataAnnotations;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 形式发票表 - 专用于存储形式发票信息，无需审核流程
    /// </summary>
    [SugarTable("crm_proforma_invoice")]
    public class Db_crm_proforma_invoice
    {
        /// <summary>
        /// Desc:形式发票ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:关联合同ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ContractId { get; set; }

        /// <summary>
        /// Desc:关联申请ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ApplicationId { get; set; }

        /// <summary>
        /// Desc:形式发票编号
        /// Default:
        /// Nullable:True
        /// </summary>
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// Desc:开票日期
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? InvoiceDate { get; set; }

        /// <summary>
        /// Desc:开票金额
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnDataType = "decimal(18, 2)", IsNullable = false)]
        public decimal Amount { get; set; } = 0;

        /// <summary>
        /// Desc:应付金额
        /// Default:0
        /// Nullable:False
        /// </summary>
        public string BalanceDue { get; set; }

        /// <summary>
        /// Desc:开票抬头
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BillingHeader { get; set; }

        /// <summary>
        /// Desc:开票公司名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BillingCompany { get; set; }

        /// <summary>
        /// Desc:纳税人识别号/信用代码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string TaxpayerCode { get; set; }

        /// <summary>
        /// Desc:项目名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// Desc:收件人信息
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Recipient { get; set; }

        /// <summary>
        /// Desc:邮箱地址
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Desc:开票内容明细
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnDataType = "nvarchar(max)")]
        public string InvoiceDetails { get; set; }

        /// <summary>
        /// Desc:PDF文件路径
        /// Default:
        /// Nullable:True
        /// </summary>
        public string PdfFilePath { get; set; }

        /// <summary>
        /// Desc:是否已下载
        /// Default:False
        /// Nullable:True
        /// </summary>
        public bool? IsDownloaded { get; set; } = false;

        /// <summary>
        /// Desc:最近下载时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? LastDownloadTime { get; set; }

        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:False
        /// Nullable:True
        /// </summary>
        public bool? Deleted { get; set; } = false;

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? UpdateDate { get; set; }
    }
} 