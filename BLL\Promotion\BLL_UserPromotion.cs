﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;

namespace CRM2_API.BLL.Promotion
{
    public class BLL_UserPromotion : BaseBLL<BLL_UserPromotion>
    {
        /// <summary>
        /// 中队领导、大队领导默认职级为“区域总监”
        /// 传入用户列表完成对中队领导、大队领导默认职级初始化
        /// 【未对用户列表的角色进行校验】
        /// </summary>
        /// <param name="userIdList">用户列表</param>
        /// <returns></returns>
        public bool LeadUserPromotionInit(List<string> userIdList)
        {
            return UserPromotionInit(userIdList, EnumUserPromotionType.QYZJ);
        }

        /// <summary>
        /// 普通职员默认职级为“职员”
        /// 传入用户列表完成对职员默认职级初始化
        /// 【未对用户列表的角色进行校验】
        /// </summary>
        /// <param name="userIdList">用户列表</param>
        /// <returns></returns>
        public bool ClerkUserPromotionInit(List<string> userIdList)
        {
            return UserPromotionInit(userIdList, EnumUserPromotionType.ZY);
        }


        /// <summary>
        /// 管理员默认职级北京后台
        /// 传入用户列表完成对职员默认职级初始化
        /// 【未对用户列表的角色进行校验】
        /// </summary>
        /// <param name="userIdList">用户列表</param>
        /// <returns></returns>
        public bool BackgroundUserPromotionInit(List<string> userIdList)
        {
            return UserPromotionInit(userIdList, EnumUserPromotionType.BJHT);
        }

        /// <summary>
        /// 根据传入的职称Code完成对用户列表的职称初始化
        /// </summary>
        /// <param name="userIdList">用户列表</param>
        /// <param name="promotionType">初始化的职称</param>
        /// <returns></returns>
        public bool UserPromotionInit(List<string> userIdList, EnumUserPromotionType promotionType)
        {
            if (userIdList is not null and { Count: > 0 })
            {
                var userPromotionDescribe =
                    DbOpe_crm_user_promotion_describe.Instance.GetUserPromotionDescribeByCode(
                        promotionType);
                var userPromotionList = new List<Db_crm_user_promotion>(userIdList.Count);
                Db_crm_user_promotion temp;
                foreach (string userId in userIdList)
                {
                    temp = new ()
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserId = userId,
                        PromotionDescribeId = userPromotionDescribe.Id,
                        PromotionTime = DateTime.Now,
                        TakingEffectTime = DateTime.Now,
                        Deleted = false,
                        CreateUser = UserTokenInfo.id,
                        CreateDate = DateTime.Now,
                    };
                    userPromotionList.Add(temp);
                }

                if (userPromotionList is not null and { Count: > 0 })
                {
                    DbOpe_crm_user_promotion.Instance.InsertQueue(userPromotionList);
                    return true;
                }
            }
            return false;
        }
    }
}