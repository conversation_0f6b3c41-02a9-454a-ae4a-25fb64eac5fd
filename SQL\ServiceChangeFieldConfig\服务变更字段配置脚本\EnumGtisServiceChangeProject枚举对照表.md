# EnumGtisServiceChangeProject 枚举对照表

## 📋 系统枚举定义

根据 `Model/Enum/EnumUtil.cs` 中的定义：

| 枚举值 | 枚举名称 | 中文描述 | 说明 |
|--------|----------|----------|------|
| 1 | ApplyResidualService | 尾款申请剩余服务 | 原有枚举 |
| 2 | ChangeServiceContent | 变更服务内容 | 原有枚举 |
| 3 | DelayPersonalServiceDays | 个人服务天数延期 | 原有枚举 |
| 4 | DelayCoupons | 优惠券延期 | 原有枚举 |
| 5 | OtherReason | 其他 | 原有枚举 |
| 6 | OpenSaleWits | 开通SaleWits | 新增枚举 |
| 7 | SalesWitsRecharge | SalesWits充值 | 新增枚举 |
| 8 | SalesWitsAddAccount | SalesWits新增子账号 | 新增枚举 |

## 🔧 数据库配置对应关系

在 `crm_service_change_field_permission` 表中，`ChangeReasonEnum` 字段应该使用上述枚举值：

### SalesWits 相关配置 (ProductType = 5)

| 字段名 | 显示名称 | ChangeReasonEnum | 对应枚举 |
|--------|----------|------------------|----------|
| OpenSaleWits | 开通SaleWits | 6 | OpenSaleWits |
| SalesWitsRecharge | SalesWits充值 | 7 | SalesWitsRecharge |
| SalesWitsAddAccount | SalesWits新增子账号 | 8 | SalesWitsAddAccount |

## ⚠️ 常见错误

1. **错误的枚举值**：使用了不存在的枚举值（如 9、10 等）
2. **枚举值错位**：将 SalesWitsRecharge 配置为 6，应该是 7
3. **产品类型错误**：SalesWits 的 ProductType 应该是 5

## 🔍 验证查询

```sql
-- 查看当前配置
SELECT 
    [FieldName],
    [FieldDisplayName],
    [ChangeReasonEnum],
    CASE [ChangeReasonEnum]
        WHEN 1 THEN '尾款申请剩余服务'
        WHEN 2 THEN '变更服务内容'
        WHEN 3 THEN '个人服务天数延期'
        WHEN 4 THEN '优惠券延期'
        WHEN 5 THEN '其他'
        WHEN 6 THEN '开通SaleWits'
        WHEN 7 THEN 'SalesWits充值'
        WHEN 8 THEN 'SalesWits新增子账号'
        ELSE '❌ 未知枚举值'
    END AS [ChangeReasonDescription]
FROM [dbo].[crm_service_change_field_permission] 
WHERE [ProductType] = 5
ORDER BY [ChangeReasonEnum];
```

## 📝 更新说明

- 原有的 1-5 枚举值保持不变
- 新增的 6-8 枚举值对应 SalesWits 相关功能
- 确保数据库配置与代码枚举定义完全一致
