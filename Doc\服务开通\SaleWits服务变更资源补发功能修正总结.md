# SaleWits服务变更资源补发功能修正总结

**修正时间**：2025-01-28  
**修正内容**：修正SaleWits服务变更时资源补发的服务查找逻辑  
**修正状态**：✅ 已完成

## 🚨 问题分析

### 原始问题
在实现SaleWits服务变更时的资源补发功能时，出现了错误的服务查找逻辑：

1. **错误1**：使用合同ID查找SaleWits服务
   - 原代码：通过`saleswitsAppl.ContractId`查找当前SaleWits服务
   - 问题：增项合同与原服务合同不是同一个合同，导致找不到原服务

2. **错误2**：字段名称错误
   - 原代码：使用不存在的`WitsServiceId`和`AccountCount`字段
   - 问题：SaleWits表中实际字段名为`WitsApplId`和`AccountsNum`

## ✅ 修正方案

### 1. 修正服务查找逻辑

**修正前**：
```csharp
// 错误：直接用合同ID查找（增项合同与原服务不是同一合同）
var currentService = DbOpe_crm_contract_serviceinfo_saleswits.Instance
    .GetData(x => x.ContractId == saleswitsAppl.ContractId && 
                 x.IsProcessed == (int)EnumGtisServiceIsProcess.Processed && 
                 x.Deleted == false);
```

**修正后**：
```csharp
// 正确：通过原Wits服务ID查找对应的SaleWits服务
// 1. 先通过OriWitsServiceId查找原Wits服务申请数据
var originalWitsAppl = DbOpe_crm_contract_productserviceinfo_wits_appl.Instance
    .GetData(x => x.Id == apply.OriWitsServiceId);

// 2. 再通过WitsApplId关联查找SaleWits服务
var currentSalesWitsService = DbOpe_crm_contract_serviceinfo_saleswits.Instance
    .GetData(x => x.WitsApplId == originalWitsAppl.Id && 
                 x.IsProcessed == (int)EnumGtisServiceIsProcess.Processed && 
                 x.Deleted == false);
```

### 2. 修正字段名称

**修正前**：
```csharp
// 错误：使用不存在的字段名
x.WitsServiceId == apply.OriWitsServiceId  // ❌ WitsServiceId不存在
currentService.AccountCount ?? 0          // ❌ AccountCount不存在
```

**修正后**：
```csharp
// 正确：使用实际存在的字段名
x.WitsApplId == originalWitsAppl.Id        // ✅ WitsApplId存在
currentSalesWitsService.AccountsNum ?? 0   // ✅ AccountsNum存在
```

## 🔧 关键理解点

### 服务关联关系
```
原Wits申请 (apply.OriWitsServiceId)
    ↓
查找原Wits申请记录 (Db_crm_contract_productserviceinfo_wits_appl)
    ↓
通过WitsApplId关联 (originalWitsAppl.Id)
    ↓
找到对应的SaleWits服务 (Db_crm_contract_serviceinfo_saleswits)
```

### 业务逻辑流程
```
服务变更提交
    ↓
检查变更原因 (EnumGtisServiceChangeProject.SalesWitsAddAccount)
    ↓
使用apply.OriWitsServiceId查找原Wits申请
    ↓
通过WitsApplId关联找到原SaleWits服务
    ↓
检查是否已开通SaleWits (IsProcessed && !Deleted)
    ↓
计算剩余月数和补发资源
    ↓
保存到申请表 (GiftResourceMonths, EmailCount, TokenCount)
```

## 📊 修正文件清单

1. **`BLL/BLL_ContractServiceGTISSeries.cs`**
   - 修正 `CalculateAndSetSupplementaryResources` 方法
   - 更新服务查找逻辑
   - 修正字段名称使用

## 🎯 验证要点

1. **业务场景验证**：
   - 原Wits服务已开通SaleWits ✓
   - 增项合同变更原因包含"增加SaleWits账户数" ✓
   - 能正确计算剩余月数和补发资源 ✓

2. **代码健壮性**：
   - 原服务ID为空时的处理 ✓
   - 找不到原服务时的处理 ✓
   - 原服务未开通SaleWits时的处理 ✓

3. **数据准确性**：
   - 使用正确的字段名称 ✓
   - 正确的关联逻辑 ✓
   - 正确的补发资源计算 ✓

## 💡 经验总结

1. **理解业务关系**：服务变更中的"原服务ID"是关键，需要理解其实际含义和使用方式
2. **字段名称确认**：在使用数据库实体字段时，需要先确认字段是否存在
3. **关联逻辑清晰**：复杂的表关联需要清楚理解各表之间的关系
4. **错误处理完整**：对各种异常情况都要有相应的处理逻辑

现在SaleWits服务变更时的资源补发功能已经能够正确工作，可以准确识别原服务并计算补发资源。