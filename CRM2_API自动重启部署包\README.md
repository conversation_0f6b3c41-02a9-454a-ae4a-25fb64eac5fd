# CRM2 API 服务自动重启部署包

本部署包包含了所有必要的配置文件，用于实现CRM2 API服务在Ubuntu系统上的自动重启功能。

## 文件清单

1. `crm-api.service` - systemd主服务配置文件
2. `install-crm-service.sh` - 主服务安装脚本
3. `monitor-crm-service.sh` - 健康监控脚本
4. `crm-api-monitor.service` - 监控服务配置文件
5. `部署说明.md` - 完整的部署指南

## 快速部署步骤

1. 将所有文件复制到服务器上的临时目录
2. 设置执行权限：
   ```bash
   chmod +x install-crm-service.sh
   chmod +x monitor-crm-service.sh
   ```
3. 运行安装脚本安装主服务：
   ```bash
   sudo ./install-crm-service.sh
   ```
4. 部署监控服务：
   ```bash
   # 复制监控脚本到系统目录
   sudo cp monitor-crm-service.sh /usr/local/bin/
   sudo chmod +x /usr/local/bin/monitor-crm-service.sh
   
   # 安装监控服务
   sudo cp crm-api-monitor.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable crm-api-monitor.service
   sudo systemctl start crm-api-monitor.service
   ```

更多详细信息请参阅 `部署说明.md` 文件。 