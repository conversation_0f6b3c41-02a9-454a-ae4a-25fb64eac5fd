using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.Linq;
using SqlSugar;
using CRM2_API.DAL.DbCommon;

namespace CRM2_API.BLL
{
        /// <summary>
    /// 合同发票业务类 - 查询部分
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        /// <summary>
        /// 获取匹配详情(必须是已经开票的发票)
        /// </summary>
        /// <param name="matchingId">匹配ID</param>
        /// <returns>匹配详情</returns>
        private MatchingDetailResponse GetMatchingDetail(string matchingId)
        {
            if (string.IsNullOrEmpty(matchingId))
            {
                throw new ApiException("匹配ID不能为空");
            }

            try
            {
                // 获取匹配记录
                var matching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => m.Id == matchingId && m.Deleted != true);
                if (matching == null)
                {
                    throw new ApiException("未找到匹配记录");
                }

                // 获取发票信息
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == matching.InvoiceId && i.Deleted != true);
                if (invoice == null)
                {
                    throw new ApiException("未找到相关发票信息");
                }

                // 获取到账信息
                var receiptInfo = BLL_ContractReceiptRegister.Instance.GetReceiptRegisterById(matching.ReceiptId);
                if (receiptInfo == null)
                {
                    throw new ApiException("未找到相关到账信息");
                }

                // 获取合同信息
                var contract = DbOpe_crm_contract.Instance.GetData(c => c.Id == invoice.ContractId && c.Deleted != true);
                if (contract == null)
                {
                    throw new ApiException("未找到相关合同信息");
                }

                // 获取审核人信息
                string auditorName = string.Empty;
                if (!string.IsNullOrEmpty(matching.AuditorId))
                {
                    var auditor = DbOpe_sys_user.Instance.GetData(u => u.Id == matching.AuditorId);
                    auditorName = auditor?.Name ?? string.Empty;
                }

                // 获取匹配操作人信息
                string matchingUserName = string.Empty;
                if (!string.IsNullOrEmpty(matching.MatchingUser))
                {
                    var matchingUser = DbOpe_sys_user.Instance.GetData(u => u.Id == matching.MatchingUser);
                    matchingUserName = matchingUser?.Name ?? string.Empty;
                }

                // 获取开票公司信息
                string billingCompanyName = string.Empty;
                if (!string.IsNullOrEmpty(invoice.BillingCompany))
                {
                    // 尝试查询公司信息
                    var company = DbOpe_crm_collectingcompany.Instance.GetData(c => c.Id == invoice.BillingCompany && c.Deleted == false);
                    if (company != null)
                    {
                        // 如果能找到对应的公司记录，使用公司名称
                        billingCompanyName = company.SellerName;
                    }
                    else
                    {
                        // 如果找不到公司记录，直接使用BillingCompany的值作为公司名称
                        billingCompanyName = invoice.BillingCompany;
                    }
                }

                // 构建发票匹配信息 - 根据正式发票表内容
                var invoiceInfo = GetInvoiceInfo(invoice.InvoiceApplicationId);
                // 判断用户是否为后台管理人员
                bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                // 构建匹配详情响应
                var response = new MatchingDetailResponse
                {
                    MatchingId = matching.Id,
                    ContractId = contract.Id,
                    ContractNumber = contract.ContractNo,
                    ContractName = contract.ContractName,
                    FirstPartyName = GetFirstPartyName(contract.FirstParty),
                    InvoiceInfo = invoiceInfo,
                    ReceiptInfo = receiptInfo,
                    MatchingStatus = (EnumInvoiceMatchingStatus)matching.MatchingStatus,
                    MatchingTime = matching.MatchingTime,
                    MatchingMethod = !matching.IsRecommended ? (int)EnumIsManualMatching.Manual : (int)EnumIsManualMatching.Auto, // 使用IsRecommended判断匹配类型
                    MatchingRemark = isBackendManager ? matching.Remark : "",
                    ReviewerId = matching.AuditorId,
                    ReviewerName = auditorName,
                    ReviewTime = matching.AuditTime,
                    ReviewComments = matching.AuditRemark,
                    MatchingUser = matching.MatchingUser,
                    MatchingUserName = matchingUserName
                };

                return response;
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取匹配详情异常: {ex.Message}");
                throw new ApiException("获取匹配详情失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 通过发票申请ID获取匹配详情
        /// </summary>
        /// <param name="applicationId">发票申请ID</param>
        /// <returns>匹配详情</returns>
        public MatchingDetailResponse GetMatchingDetailByApplicationId(string applicationId)
        {
            if (string.IsNullOrEmpty(applicationId))
            {
                throw new ApiException("发票申请ID不能为空");
            }

            try
            {
                // 直接通过ApplicationId查询匹配记录
                var matching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => 
                    m.InvoiceApplicationId == applicationId && 
                    m.Deleted != true);
                
                // 如果找到匹配记录，检查是否可以直接获取详情
                if (matching != null)
                {
                    // 只有当匹配记录包含有效的InvoiceId时才使用GetMatchingDetail
                    if (!string.IsNullOrEmpty(matching.InvoiceId) && !matching.InvoiceId.StartsWith("temp_"))
                    {
                        return GetMatchingDetail(matching.Id);
                    }
                    else
                    {
                        // 如果匹配记录没有有效的InvoiceId，说明是申请阶段的匹配
                        // 获取申请信息并构建响应
                        var applicationData = DbOpe_crm_invoice_application.Instance.GetData(a => 
                            a.Id == applicationId && a.Deleted != true);
                            
                        if (applicationData == null)
                        {
                            throw new ApiException("未找到相关发票申请信息");
                        }
                        
                        // 获取合同信息
                        var contractInfo = DbOpe_crm_contract.Instance.GetContractById(applicationData.ContractId, true);
                        if (contractInfo == null)
                        {
                            throw new ApiException("未找到相关合同信息");
                        }
                        
                        // 获取开票公司名称
                        string billingCompanyName = string.Empty;
                        if (!string.IsNullOrEmpty(applicationData.BillingCompany))
                        {
                            var company = DbOpe_crm_collectingcompany.Instance.GetData(c => 
                                c.Id == applicationData.BillingCompany && c.Deleted == false);
                            billingCompanyName = company?.SellerName ?? applicationData.BillingCompany;
                        }
                        
                        // 获取到账信息
                        var receiptInfo = matching.ReceiptId != null 
                            ? BLL_ContractReceiptRegister.Instance.GetReceiptRegisterById(matching.ReceiptId) 
                            : null;
                        
                        // 获取匹配操作人信息
                        string matchingUserName = string.Empty;
                        if (!string.IsNullOrEmpty(matching.MatchingUser))
                        {
                            var matchingUser = DbOpe_sys_user.Instance.GetData(u => u.Id == matching.MatchingUser);
                            matchingUserName = matchingUser?.Name ?? string.Empty;
                        }
                        // 判断用户是否为后台管理人员
                        bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                        // 填充发票申请阶段的匹配详情
                        var applicationResponse = new MatchingDetailResponse
                        {
                            MatchingId = matching.Id,
                            ContractId = contractInfo.Id,
                            ContractNumber = contractInfo.ContractNo,
                            ContractName = contractInfo.ContractName,
                            FirstPartyName = GetFirstPartyName(contractInfo.FirstParty),
                            ReceiptInfo = receiptInfo,
                            MatchingStatus = (EnumInvoiceMatchingStatus)matching.MatchingStatus,
                            MatchingTime = matching.MatchingTime,
                            MatchingMethod = !matching.IsRecommended ? (int)EnumIsManualMatching.Manual : (int)EnumIsManualMatching.Auto,
                            MatchingRemark = isBackendManager ? matching.Remark : "",
                            MatchingUser = matching.MatchingUser,
                            MatchingUserName = matchingUserName,
                            
                            // 填充发票信息 (申请阶段)
                            InvoiceInfo = new InvoiceInfo
                            {
                                Id = "申请中", // 标记为申请中
                                InvoiceNumber = "申请中",
                                InvoiceCode = "申请中",
                                InvoiceAmount = applicationData.AppliedAmount,
                                BillingType = (int)applicationData.BillingType,
                                InvoiceType = (EnumInvoiceType)applicationData.InvoiceType,
                                BillingCompanyName = billingCompanyName,
                                BillingHeader = applicationData.BillingHeader
                            }
                        };
                        
                        return applicationResponse;
                    }
                }
                
                // 如果没有找到直接匹配，尝试查看是否有发票信息
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                    i.InvoiceApplicationId == applicationId && 
                    i.Deleted != true);
                
                // 如果有发票，尝试查询基于发票ID的匹配
                if (invoice != null)
                {
                    // 通过发票ID查询匹配记录
                    matching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => 
                        m.InvoiceId == invoice.Id && 
                        m.Deleted != true);
                    
                    // 如果找到匹配记录，获取详情
                    if (matching != null)
                    {
                        return GetMatchingDetail(matching.Id);
                    }
                    
                    // 如果没有匹配记录，构建基本发票信息的响应
                    // 获取合同信息
                    var contract = DbOpe_crm_contract.Instance.GetData(c => 
                        c.Id == invoice.ContractId && c.Deleted != true);
                    var isSupplementInvoice = invoice.BillingType == (int)EnumBillingType.SupplementInvoice;
                    if (contract == null && !isSupplementInvoice)
                    {
                        throw new ApiException("未找到相关合同信息");
                    }
                    
                    // 获取开票公司信息
                    string billingCompanyName = string.Empty;
                    if (!string.IsNullOrEmpty(invoice.BillingCompany))
                    {
                        var company = DbOpe_crm_collectingcompany.Instance.GetData(c => 
                            c.Id == invoice.BillingCompany && c.Deleted == false);
                        billingCompanyName = company?.CollectingCompanyName ?? invoice.BillingCompany;
                    }
                    
                    // 创建基本的发票匹配信息
                    var invoiceInfo = GetInvoiceInfo(invoice.InvoiceApplicationId);
                    
                    // 创建匹配详情响应（尚无匹配信息）
                    var response = new MatchingDetailResponse
                    {
                        MatchingId = string.Empty,  // 尚未匹配
                        ContractId = isSupplementInvoice ? string.Empty : contract.Id,
                        ContractNumber = isSupplementInvoice ? string.Empty : contract.ContractNo,
                        ContractName = isSupplementInvoice ? string.Empty : contract.ContractName,
                        FirstPartyName = isSupplementInvoice ? string.Empty : GetFirstPartyName(contract.FirstParty),
                        InvoiceInfo = invoiceInfo,
                        MatchingStatus = (EnumInvoiceMatchingStatus)invoice.MatchingStatus,
                        MatchingMethod = (int)EnumIsManualMatching.Manual
                    };
                    
                    return response;
                }
                
                // 如果没有发票信息，则尝试从申请信息构建响应
                var application = DbOpe_crm_invoice_application.Instance.GetData(a => 
                    a.Id == applicationId && a.Deleted != true);
                
                if (application == null)
                {
                    throw new ApiException("未找到相关发票申请信息");
                }
                
                // 获取合同信息
                var contractData = DbOpe_crm_contract.Instance.GetContractById(application.ContractId, true);
                if (application.BillingType != (int)EnumBillingType.SupplementInvoice&&contractData == null)
                {
                    throw new ApiException("未找到相关合同信息");
                }
                
                // 获取开票公司名称
                string companyName = string.Empty;
                if (!string.IsNullOrEmpty(application.BillingCompany))
                {
                    var company = DbOpe_crm_collectingcompany.Instance.GetData(c => 
                        c.Id == application.BillingCompany && c.Deleted == false);
                    companyName = company?.CollectingCompanyName ?? application.BillingCompany;
                }
                
                // 创建基本的匹配详情响应（大部分字段为空或默认值）
                var appResponse = new MatchingDetailResponse
                {
                    MatchingId = string.Empty,  // 尚未匹配
                    ContractId = contractData == null?"":contractData.Id,
                    ContractNumber = contractData == null ? "" : contractData.ContractNo,
                    ContractName = contractData == null ? "" : contractData.ContractName,
                    FirstPartyName = contractData == null ? "" : GetFirstPartyName(contractData.FirstParty),
                    
                    // 填充发票信息
                    InvoiceInfo = new InvoiceInfo
                    {
                        Id = string.Empty,  // 尚未生成发票
                        InvoiceAmount = application.AppliedAmount,
                        BillingType = (int)application.BillingType,
                        InvoiceType = (EnumInvoiceType)application.InvoiceType,
                        BillingCompanyName = companyName,
                        BillingHeader = application.BillingHeader
                    },
                    
                    // 默认匹配状态
                    MatchingStatus = EnumInvoiceMatchingStatus.NotReceived,
                    MatchingMethod = (int)EnumIsManualMatching.Manual
                };
                
                return appResponse;
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"通过发票申请ID获取匹配详情异常: {ex.Message}");
                throw new ApiException("通过发票申请ID获取匹配详情失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 通过到账ID获取匹配详情
        /// </summary>
        /// <param name="receiptId">到账ID</param>
        /// <returns>匹配详情</returns>
        /// <exception cref="ApiException">异常</exception>
        public MatchingDetailResponse GetMatchingDetailByReceiptId(string receiptId)
        {
            if (string.IsNullOrEmpty(receiptId))
            {
                throw new ApiException("到账ID不能为空");
            }
              try
            {
                // 直接通过ApplicationId查询匹配记录
                var matching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => 
                    m.ReceiptId == receiptId && 
                    m.Deleted != true);
                
                // 如果找到匹配记录，检查是否可以直接获取详情
                if (matching != null)
                {
                    // 只有当匹配记录包含有效的InvoiceId时才使用GetMatchingDetail
                    if (!string.IsNullOrEmpty(matching.InvoiceId))
                    {
                        return GetMatchingDetail(matching.Id);
                    }
                    else if(!string.IsNullOrEmpty(matching.InvoiceApplicationId))
                    {
                        return GetMatchingDetailByApplicationId(matching.InvoiceApplicationId);
                    }
                }
                //没有匹配记录，获取其他到账基本信息
                 // 获取到账信息
                var receiptInfo = BLL_ContractReceiptRegister.Instance.GetReceiptRegisterById(receiptId);
                if (receiptInfo == null)
                {
                    throw new ApiException("未找到相关到账信息");
                }

                // 获取合同信息
                var contractData = DbOpe_crm_contract.Instance.GetData(c => c.Id == receiptInfo.ContractId && c.Deleted != true);
                if (contractData == null)
                {
                    throw new ApiException("未找到相关合同信息");
                }

                // 如果没有发票信息，创建基本的匹配详情响应（大部分字段为空或默认值）
                var appResponse = new MatchingDetailResponse
                {
                    MatchingId = string.Empty,  // 尚未匹配
                    ContractId = contractData == null?"":contractData.Id,
                    ContractNumber = contractData == null ? "" : contractData.ContractNo,
                    ContractName = contractData == null ? "" : contractData.ContractName,
                    FirstPartyName = contractData == null ? "" : GetFirstPartyName(contractData.FirstParty),
                    
                    // 填充发票信息
                    InvoiceInfo = null,
                    
                    // 默认匹配状态
                    MatchingStatus = EnumInvoiceMatchingStatus.NotReceived,
                    MatchingMethod = (int)EnumIsManualMatching.Manual,

                    // 填充到账信息
                    ReceiptInfo = receiptInfo
                };
                
                return appResponse;
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"通过到账获取匹配详情异常: {ex.Message}");
                throw new ApiException("通过到账获取匹配详情失败：" + ex.Message);
            }
        
        
        
        }
        
    
} }