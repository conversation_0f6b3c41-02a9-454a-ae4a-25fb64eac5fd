using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.Enum;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Common;
using CRM2_API.Common.Cache;
using CRM2_API.Model.System;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;
using CRM2_API.DAL.DbCommon;
using CRM2_API.Model.ControllersViewModel.Common;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告数据库操作业务逻辑类 - 工作台功能
    /// 实现真实的数据库操作，替换Mock实现
    /// </summary>
    public partial class BLL_WorkReportDatabase
    {
        #region 用户身份验证

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private string GetCurrentUserId()
        {
            if (string.IsNullOrEmpty(UserId))
            {
                throw new ApiException("用户身份验证失败，请重新登录");
            }
            return UserId;
        }

        #endregion

        #region 个人工作台

        /// <summary>
        /// 获取个人工作台数据
        /// </summary>
        /// <returns></returns>
        public GetPersonalWorkbench_Out GetPersonalWorkbench()
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                
                var result = new GetPersonalWorkbench_Out
                {
                    TodayTasks = GetTodayTasksFromDatabase(currentUserId),
                    WorkbenchSettings = GetWorkbenchSettingsFromDatabase(currentUserId),
                    LatestFeedback = GetLatestFeedbackFromDatabase(currentUserId),
                    UserInfo = GetUserInfoFromDatabase(currentUserId)
                };

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取个人工作台数据失败：{ex.Message}", ex);
                throw new ApiException($"获取个人工作台数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取今日任务提醒数据（从数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        private VM_TodayTasks GetTodayTasksFromDatabase(string userId)
        {
            try
            {
                var currentTime = DateTime.Now;
                var taskReminders = new List<VM_TaskReminder>();
                var holidayDbOpe = DbOpe_crm_holiday.Instance;

                // 日报提醒逻辑
                var showDualReports = ShouldShowDualReports(currentTime);
                if (showDualReports)
                {
                    // 修复：昨天日报应该是昨天的日期，今天日报应该是今天的日期
                    // 不要因为节假日而改变日期，让AddDailyReportReminder内部处理节假日逻辑
                    var yesterdayDate = currentTime.AddDays(-1).Date;
                    var todayDate = currentTime.Date;
                    
                    // 添加昨天日报提醒
                    AddDailyReportReminder(taskReminders, userId, yesterdayDate, "昨天日报", holidayDbOpe);
                    // 添加今天日报提醒
                    AddDailyReportReminder(taskReminders, userId, todayDate, "今天日报", holidayDbOpe);
                }
                else
                {
                    var todayDate = currentTime.Date;
                    AddDailyReportReminder(taskReminders, userId, todayDate, "今天日报", holidayDbOpe);
                }

                // ========== 周报提醒 ===========
                // 只要在区间内就提醒，可以多条
                var weekReportDate = GetLastWorkdayOfWeek(currentTime);
                var lastWeekReportDate = GetLastWorkdayOfWeek(currentTime.AddDays(-7));
                // 上周周报
                AddWeeklyReportReminderWithTimeCheck(taskReminders, userId, lastWeekReportDate, "上周周报", currentTime);
                // 本周周报
                AddWeeklyReportReminderWithTimeCheck(taskReminders, userId, weekReportDate, "本周周报", currentTime);

                // ========== 月报提醒 ===========
                var monthReportDate = GetLastWorkdayOfMonth(currentTime);
                var lastMonthReportDate = GetLastWorkdayOfMonth(currentTime.AddMonths(-1));
                // 上月月报
                AddMonthlyReportReminderWithTimeCheck(taskReminders, userId, lastMonthReportDate, $"{lastMonthReportDate:MM}月报", currentTime);
                // 本月月报
                AddMonthlyReportReminderWithTimeCheck(taskReminders, userId, monthReportDate, $"{monthReportDate:MM}月报", currentTime);

                return new VM_TodayTasks
                {
                    CurrentPeriodText = GetCurrentPeriodText(EnumReportType.Daily),
                    TaskReminders = taskReminders,
                    ShowDualReports = showDualReports
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取今日任务提醒数据失败：{ex.Message}", ex);
                throw new ApiException($"获取今日任务提醒数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据时效配置判断是否应该显示双报告
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <returns>是否显示双报告</returns>
        private bool ShouldShowDualReports(DateTime currentTime)
        {
            try
            {
                // 获取今天日报的时效配置
                var dailyDeadlineInfo = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                {
                    ReportType = EnumReportType.Daily,
                    ReportDate = currentTime.Date
                });

                var regularTime = DateTime.Parse(dailyDeadlineInfo.RegularTime);
                
                // 如果当前时间在今天日报规定时间之前，显示双报告（昨天+今天）
                // 如果当前时间在今天日报规定时间之后，只显示今天的报告
                return currentTime < regularTime;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"判断是否显示双报告失败：{ex.Message}", ex);
                // 默认行为：17:00前显示双报告
                return currentTime.Hour < 17;
            }
        }

        /// <summary>
        /// 获取本周最后一个工作日
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <returns>本周最后一个工作日</returns>
        private DateTime GetLastWorkdayOfWeek(DateTime currentTime)
        {
            var holidayDbOpe = DbOpe_crm_holiday.Instance;
            
            // 从本周日开始，向前查找最近的工作日
            // 计算本周日：一周从周一开始到周日结束，所以本周日是本周的最后一天
            // 如果今天是周日(0)则是今天，否则计算到本周日还有几天
            var currentDayOfWeek = (int)currentTime.DayOfWeek;
            var daysToSunday = currentDayOfWeek == 0 ? 0 : (7 - currentDayOfWeek);
            var weekEnd = currentTime.AddDays(daysToSunday).Date; // 本周日（本周结束）
            
            LogUtil.AddLog($"GetLastWorkdayOfWeek - 当前时间：{currentTime:yyyy-MM-dd}，本周日：{weekEnd:yyyy-MM-dd}");
            
            // 从周日开始向前查找，最多查找7天
            for (int i = 0; i < 7; i++)
            {
                var checkDate = weekEnd.AddDays(-i);
                var isWorkday = holidayDbOpe.IsWorkday(checkDate);
                LogUtil.AddLog($"GetLastWorkdayOfWeek - 检查日期：{checkDate:yyyy-MM-dd}，是否工作日：{isWorkday}");
                
                if (isWorkday)
                {
                    LogUtil.AddLog($"GetLastWorkdayOfWeek - 找到本周最后一个工作日：{checkDate:yyyy-MM-dd}");
                    return checkDate;
                }
            }
            
            // 如果没找到工作日，返回本周日（理论上不应该发生）
            LogUtil.AddLog($"GetLastWorkdayOfWeek - 未找到工作日，返回本周日：{weekEnd:yyyy-MM-dd}");
            return weekEnd;
        }

        /// <summary>
        /// 获取本月最后一个工作日
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <returns>本月最后一个工作日</returns>
        private DateTime GetLastWorkdayOfMonth(DateTime currentTime)
        {
            var holidayDbOpe = DbOpe_crm_holiday.Instance;
            
            // 获取本月最后一天
            var monthEnd = new DateTime(currentTime.Year, currentTime.Month, 1).AddMonths(1).AddDays(-1);
            
            // 从月末开始向前查找，最多查找31天
            for (int i = 0; i < 31; i++)
            {
                var checkDate = monthEnd.AddDays(-i);
                if (holidayDbOpe.IsWorkday(checkDate))
                {
                    return checkDate;
                }
            }
            
            // 如果没找到工作日，返回月末（理论上不应该发生）
            return monthEnd;
        }

        /// <summary>
        /// 获取指定日期之前的最近工作日
        /// </summary>
        /// <param name="date">指定日期</param>
        /// <returns>最近的工作日</returns>
        private DateTime GetPreviousWorkday(DateTime date)
        {
            var holidayDbOpe = DbOpe_crm_holiday.Instance;
            var checkDate = date.AddDays(-1);
            
            // 最多向前查找30天，避免无限循环
            for (int i = 0; i < 30; i++)
            {
                if (holidayDbOpe.IsWorkday(checkDate))
                {
                    return checkDate;
                }
                checkDate = checkDate.AddDays(-1);
            }
            
            // 如果30天内都没找到工作日，返回原日期
            return date;
        }

        /// <summary>
        /// 获取工作台设置数据（从数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        private VM_WorkbenchSettings GetWorkbenchSettingsFromDatabase(string userId)
        {
            try
            {
                // 使用BLL_WorkReport中的接口获取默认接收人和抄送人
                var defaultReceivers = GetDefaultReceiversForWorkbench(userId);
                var defaultCcs = GetDefaultCcsForWorkbench(userId);
                
                return new VM_WorkbenchSettings
                {
                    DefaultReceivers = defaultReceivers,
                    DefaultCcs = defaultCcs
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取工作台设置数据失败：{ex.Message}", ex);
                throw new ApiException($"获取工作台设置数据失败：{ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取默认接收人（工作台专用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>默认接收人列表</returns>
        private List<VM_DefaultUser> GetDefaultReceiversForWorkbench(string userId)
        {
            try
            {
                // 查询用户默认接收人配置
                var defaultReceivers = new List<VM_DefaultUser>();
                
                // 只根据IsDefault标志查询用户的默认接收人设置
                var receiverRecords = DbOpe_crm_report_receiver.Instance.GetDataList(x => 
                    x.CreateUser == userId && 
                    x.IsDefault == true && 
                    x.Deleted == false);
                
                // 获取不重复的默认接收人
                var uniqueReceivers = receiverRecords
                    .GroupBy(x => x.ReceiverId)
                    .Select(g => g.First())
                    .Take(5)
                    .ToList();
                
                if (uniqueReceivers.Any())
                {
                    // 获取接收人的部门信息
                    var receiverIds = uniqueReceivers.Select(x => x.ReceiverId).Distinct().ToList();
                    var receiverUsers = DbOpe_sys_user.Instance.GetDataList(x => 
                        receiverIds.Contains(x.Id) && x.Deleted == false);
                    var userDict = receiverUsers.ToDictionary(x => x.Id, x => x);
                    
                    // 构建返回结果
                    foreach (var receiver in uniqueReceivers)
                    {
                        string department = "";
                        if (userDict.TryGetValue(receiver.ReceiverId, out var user) && !string.IsNullOrEmpty(user.OrganizationId) && user.OrganizationId != Guid.Empty.ToString())
                        {
                            var org = DbOpe_sys_organization.Instance.GetData(x => x.Id == user.OrganizationId);
                            department = org?.OrgName ?? "";
                        }
                        
                        defaultReceivers.Add(new VM_DefaultUser
                        {
                            UserId = receiver.ReceiverId,
                            UserName = userDict.TryGetValue(receiver.ReceiverId, out var u) ? u.Name : receiver.ReceiverName,
                            Department = department,
                            IsDefault = true
                        });
                    }
                }
                
                return defaultReceivers;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取默认接收人失败：{ex.Message}", ex);
                return new List<VM_DefaultUser>();
            }
        }
        
        /// <summary>
        /// 获取默认抄送人（工作台专用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>默认抄送人列表</returns>
        private List<VM_DefaultUser> GetDefaultCcsForWorkbench(string userId)
        {
            try
            {
                // 查询用户默认抄送人配置
                var defaultCcs = new List<VM_DefaultUser>();
                
                // 只根据IsDefault标志查询用户的默认抄送人设置
                var ccRecords = DbOpe_crm_report_cc.Instance.GetDataList(x => 
                    x.CreateUser == userId && 
                    x.IsDefault == true && 
                    x.Deleted == false);
                
                // 获取不重复的默认抄送人
                var uniqueCcs = ccRecords
                    .GroupBy(x => x.CcUserId)
                    .Select(g => g.First())
                    .Take(5)
                    .ToList();
                
                if (uniqueCcs.Any())
                {
                    // 获取抄送人的部门信息
                    var ccUserIds = uniqueCcs.Select(x => x.CcUserId).Distinct().ToList();
                    var ccUsers = DbOpe_sys_user.Instance.GetDataList(x => 
                        ccUserIds.Contains(x.Id) && x.Deleted == false);
                    var userDict = ccUsers.ToDictionary(x => x.Id, x => x);
                    
                    // 构建返回结果
                    foreach (var cc in uniqueCcs)
                    {
                        string department = "";
                        if (userDict.TryGetValue(cc.CcUserId, out var user) && !string.IsNullOrEmpty(user.OrganizationId) && user.OrganizationId != Guid.Empty.ToString())
                        {
                            var org = DbOpe_sys_organization.Instance.GetData(x => x.Id == user.OrganizationId);
                            department = org?.OrgName ?? "";
                        }
                        
                        defaultCcs.Add(new VM_DefaultUser
                        {
                            UserId = cc.CcUserId,
                            UserName = userDict.TryGetValue(cc.CcUserId, out var u) ? u.Name : cc.CcUserName,
                            Department = department,
                            IsDefault = true
                        });
                    }
                }
                
                return defaultCcs;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取默认抄送人失败：{ex.Message}", ex);
                return new List<VM_DefaultUser>();
            }
        }

        /// <summary>
        /// 获取最新反馈数据（从数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        private VM_LatestFeedback GetLatestFeedbackFromDatabase(string userId)
        {
            try
            {
                var result = new VM_LatestFeedback
                {
                    LatestComments = new List<VM_LatestComment>(),
                    LatestLikes = new List<VM_LatestLike>()
                };
                // 1. 查询用户创建的报告ID
                var userReportIds = DbOpe_crm_report_base.Instance.GetDataList(x => 
                    x.UserId == userId && 
                    x.Status == (int)EnumReportStatus.Submitted && // 只查询已提交的报告
                    x.Deleted == false)
                    .Select(x => x.Id)
                    .ToList();
                
                if (userReportIds.Any())
                {
                    // 2. 查询这些报告的最新评论
                    var comments = DbOpe_crm_report_comment.Instance.GetDataList(x => 
                        userReportIds.Contains(x.ReportId) && 
                        x.Deleted == false)
                        .OrderByDescending(x => x.CreateDate)
                        .Take(5) // 最多取5条
                        .ToList();
                    
                    if (comments.Any())
                    {
                        // 3. 获取评论用户信息
                        var commentUserIds = comments.Select(x => x.CreateUser).Distinct().ToList();
                        var commentUsers = DbOpe_sys_user.Instance.GetDataList(x => 
                            commentUserIds.Contains(x.Id) && x.Deleted == false);
                        var userDict = commentUsers.ToDictionary(x => x.Id, x => x);
                        
                        // 4. 获取报告信息
                        var reportIds = comments.Select(x => x.ReportId).Distinct().ToList();
                        var reports = DbOpe_crm_report_base.Instance.GetDataList(x => 
                            reportIds.Contains(x.Id) && x.Deleted == false);
                        var reportDict = reports.ToDictionary(x => x.Id, x => x);
                        
                        // 5. 构建评论列表
                        foreach (var comment in comments)
                        {
                            string userName = "未知用户";
                            string userDepartment = "";
                            
                            if (userDict.TryGetValue(comment.CreateUser, out var user))
                            {
                                userName = user.Name;
                                
                                if (!string.IsNullOrEmpty(user.OrganizationId) && user.OrganizationId != Guid.Empty.ToString())
                                {
                                    var org = DbOpe_sys_organization.Instance.GetData(x => x.Id == user.OrganizationId);
                                    userDepartment = org?.OrgName ?? "";
                                }
                            }
                            
                            string reportTitle = "未知报告";
                            if (reportDict.TryGetValue(comment.ReportId, out var report))
                            {
                                reportTitle = report.Title;
                            }
                            
                            result.LatestComments.Add(new VM_LatestComment
                            {
                                CommentId = comment.Id,
                                ReportId = comment.ReportId,
                                ReportTitle = reportTitle,
                                CommentText = comment.CommentText,
                                CommentTime = comment.CreateDate,
                                TimeAgo = GetTimeAgoText(comment.CreateDate),
                                UserId = comment.CreateUser,
                                UserName = userName,
                                Department = userDepartment
                            });
                        }
                    }
                    
                    // 6. 查询这些报告的最新点赞
                    var likes = DbOpe_crm_report_like.Instance.GetDataList(x => 
                        userReportIds.Contains(x.ReportId) && 
                        x.Deleted == false)
                        .OrderByDescending(x => x.CreateDate)
                        .Take(5) // 最多取5条
                        .ToList();
                    
                    if (likes.Any())
                    {
                        // 7. 获取点赞用户信息
                        var likeUserIds = likes.Select(x => x.CreateUser).Distinct().ToList();
                        var likeUsers = DbOpe_sys_user.Instance.GetDataList(x => 
                            likeUserIds.Contains(x.Id) && x.Deleted == false);
                        var userDict = likeUsers.ToDictionary(x => x.Id, x => x);
                        
                        // 8. 获取报告信息
                        var reportIds = likes.Select(x => x.ReportId).Distinct().ToList();
                        var reports = DbOpe_crm_report_base.Instance.GetDataList(x => 
                            reportIds.Contains(x.Id) && x.Deleted == false);
                        var reportDict = reports.ToDictionary(x => x.Id, x => x);
                        
                        // 9. 构建点赞列表
                        foreach (var like in likes)
                        {
                            string userName = "未知用户";
                            string userDepartment = "";
                            
                            if (userDict.TryGetValue(like.CreateUser, out var user))
                            {
                                userName = user.Name;
                                
                                if (!string.IsNullOrEmpty(user.OrganizationId) && user.OrganizationId != Guid.Empty.ToString())
                                {
                                    var org = DbOpe_sys_organization.Instance.GetData(x => x.Id == user.OrganizationId);
                                    userDepartment = org?.OrgName ?? "";
                                }
                            }
                            
                            string reportTitle = "未知报告";
                            if (reportDict.TryGetValue(like.ReportId, out var report))
                            {
                                reportTitle = report.Title;
                            }
                            
                            result.LatestLikes.Add(new VM_LatestLike
                            {
                                LikeId = like.Id,
                                ReportId = like.ReportId,
                                ReportTitle = reportTitle,
                                LikeTime = like.CreateDate,
                                TimeAgo = GetTimeAgoText(like.CreateDate),
                                UserId = like.CreateUser,
                                UserName = userName,
                                Department = userDepartment
                            });
                        }
                    }
                }
                
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取最新反馈数据失败：{ex.Message}", ex);
                throw new ApiException($"获取最新反馈数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户基本信息（从数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        private VM_UserInfo GetUserInfoFromDatabase(string userId)
        {
            try
            {
                // 从用户表查询用户基本信息
                var user = DbOpe_sys_user.Instance.GetData(x => x.Id == userId && x.Deleted == false);
                if (user == null)
                {
                    throw new ApiException($"用户不存在：{userId}");
                }
                
                // 查询用户是否为部门负责人
                var isLeader = false;
                if (!string.IsNullOrEmpty(user.OrganizationId))
                {
                    var org = DbOpe_sys_organization.Instance.GetData(x => 
                        x.Id == user.OrganizationId && x.Deleted == false);
                    
                    // 根据组织架构判断是否为领导，这里简化处理，实际可能需要更复杂的逻辑
                    if (org != null)
                    {
                        // 假设组织结构中没有直接的领导字段，可以通过其他方式判断
                        // 比如用户类型、角色等
                        isLeader = user.UserType == 1; // 假设1表示管理者
                    }
                }
                var isAdmin = DbOpe_sys_user.Instance.CheckUserIsManager(userId);
                return new VM_UserInfo
                {
                    UserId = userId,
                    UserName = user.Name,
                    IsLeader = isLeader,
                    IsAdmin = isAdmin
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取用户信息失败：{ex.Message}", ex);
                throw new ApiException($"获取用户信息失败：{ex.Message}");
            }
        }

        #endregion

        #region 团队管理区

        /// <summary>
        /// 获取团队管理区数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public GetTeamManagement_Out GetTeamManagement(GetTeamManagement_In input)
        {
            try
            {
                var currentUserId = GetCurrentUserId();

                var result = new GetTeamManagement_Out
                {
                    TeamOverview = GetTeamOverviewFromDatabase(currentUserId, input.ReportType),
                    PendingReports = GetPendingReportsFromDatabase(currentUserId, input.ReportType)
                };

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取团队管理区数据失败：{ex.Message}", ex);
                throw new ApiException($"获取团队管理区数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取团队状态总览数据（从数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="reportType">报告类型</param>
        /// <returns></returns>
        private VM_TeamOverview GetTeamOverviewFromDatabase(string userId, EnumReportType reportType)
        {
            try
            {
                // 获取用户的团队信息
                var userTeamInfo = GetUserTeamInfoForWorkbench(userId);
                if (userTeamInfo == null)
                {
                    // 如果没有团队信息，返回空统计
                    return new VM_TeamOverview
                    {
                        StatsCards = new VM_StatsCards
                        {
                            PendingCard = new VM_StatsCard { Title = "未提交", Value = 0, Total = 0, CardColor = EnumStatusColor.Danger.GetEnumDescription() },
                            ToReviewCard = new VM_StatsCard { Title = "待查看", Value = 0, Total = 0, CardColor = "blue" },
                            OnTimeCard = new VM_StatsCard { Title = "按时提交", Value = 0, Total = 0, CardColor = EnumStatusColor.Success.GetEnumDescription() },
                            LateCard = new VM_StatsCard { Title = "迟交", Value = 0, Total = 0, CardColor = EnumStatusColor.Warning.GetEnumDescription() }
                        },
                        CurrentPeriodText = GetCurrentPeriodText(reportType)
                    };
                }

                // 获取团队报告查看范围内的用户列表
                var teamMembers = GetTeamReportViewScopeUserIds(userTeamInfo.TeamId);
                var totalMembers = teamMembers.Count;

                if (totalMembers == 0)
                {
                    return new VM_TeamOverview
                    {
                        StatsCards = new VM_StatsCards
                        {
                            PendingCard = new VM_StatsCard { Title = "未提交", Value = 0, Total = 0, CardColor = EnumStatusColor.Danger.GetEnumDescription() },
                            ToReviewCard = new VM_StatsCard { Title = "待查看", Value = 0, Total = 0, CardColor = EnumStatusColor.Info.GetEnumDescription() },
                            OnTimeCard = new VM_StatsCard { Title = "按时提交", Value = 0, Total = 0, CardColor = EnumStatusColor.Success.GetEnumDescription() },
                            LateCard = new VM_StatsCard { Title = "迟交", Value = 0, Total = 0, CardColor = EnumStatusColor.Warning.GetEnumDescription() }
                        },
                        CurrentPeriodText = GetCurrentPeriodText(reportType)
                    };
                }

                // 根据报告类型确定查询的日期范围
                var (startDate, endDate) = GetReportDateRange(reportType);

                // 查询团队成员在当前报告周期的报告
                var teamReports = DbOpe_crm_report_base.Instance.GetDataList(x => 
                    teamMembers.Contains(x.CreateUser) && 
                    x.ReportType == (int)reportType && 
                    x.ReportDate >= startDate && x.ReportDate <= endDate &&
                    x.Deleted == false);
                
                // 初始化统计数据
                int pendingCount = 0;
                int toReviewCount = 0;
                int onTimeCount = 0;
                int lateCount = 0;
                
                if (teamReports.Any())
                {
                    // 计算已提交报告的用户ID列表
                    var submittedUserIds = teamReports
                        .Where(x => x.Status == (int)EnumReportStatus.Submitted)
                        .Select(x => x.CreateUser)
                        .Distinct()
                        .ToList();
                    
                    // 未提交的人数 = 总人数 - 已提交的人数
                    pendingCount = totalMembers - submittedUserIds.Count;
                    
                    // 对于已提交的报告，根据SubmitStatus统计按时和迟交的数量
                    var submittedReports = teamReports.Where(x => x.Status == (int)EnumReportStatus.Submitted).ToList();
                    onTimeCount = submittedReports.Count(x => x.SubmitStatus == (int)EnumSubmitStatus.OnTime);
                    lateCount = submittedReports.Count(x => x.SubmitStatus == (int)EnumSubmitStatus.Late);
                }
                else
                {
                    // 如果没有找到报告，则所有成员都未提交
                    pendingCount = totalMembers;
                }
                
                // 待查看的报告：查询需要当前用户查看但未查看的报告（仅限团队成员的报告）
                if (teamReports.Any())
                {
                    var reportIds = teamReports.Select(x => x.Id).ToList();
                    
                    // 作为接收人的未读报告
                    var receiverRecords = DbOpe_crm_report_receiver.Instance.GetDataList(x => 
                        reportIds.Contains(x.ReportId) && 
                        x.ReceiverId == userId && 
                        x.Status == (int)EnumReceiverStatus.Unread && // 使用枚举
                        x.Deleted == false);
                    
                    // 作为抄送人的未读报告
                    var ccRecords = DbOpe_crm_report_cc.Instance.GetDataList(x => 
                        reportIds.Contains(x.ReportId) && 
                        x.CcUserId == userId && 
                        x.Status == (int)EnumReceiverStatus.Unread && // 使用枚举
                        x.Deleted == false);
                    
                    // 统计需要查看的报告数量（去重）
                    var allUnreadReportIds = receiverRecords.Select(x => x.ReportId)
                        .Union(ccRecords.Select(x => x.ReportId))
                        .Distinct()
                        .ToList();
                    
                    toReviewCount = allUnreadReportIds.Count;
                }

                return new VM_TeamOverview
                {
                    StatsCards = new VM_StatsCards
                    {
                        PendingCard = new VM_StatsCard
                        {
                            Title = "未提交",
                            Value = pendingCount,
                            Total = totalMembers,
                            CardColor = EnumStatusColor.Danger.GetEnumDescription()
                        },
                        ToReviewCard = new VM_StatsCard
                        {
                            Title = "待查看",
                            Value = toReviewCount,
                            Total = totalMembers,
                            CardColor = EnumStatusColor.Info.GetEnumDescription()
                        },
                        OnTimeCard = new VM_StatsCard
                        {
                            Title = "按时提交",
                            Value = onTimeCount,
                            Total = totalMembers,
                            CardColor = EnumStatusColor.Success.GetEnumDescription()
                        },
                        LateCard = new VM_StatsCard
                        {
                            Title = "迟交",
                            Value = lateCount,
                            Total = totalMembers,
                            CardColor = EnumStatusColor.Warning.GetEnumDescription()
                        }
                    },
                    CurrentPeriodText = GetCurrentPeriodText(reportType)
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取团队状态总览失败：{ex.Message}", ex);
                // 返回默认数据，避免整个工作台崩溃
                return new VM_TeamOverview
                {
                    StatsCards = new VM_StatsCards
                    {
                        PendingCard = new VM_StatsCard { Title = "未提交", Value = 0, Total = 0, CardColor = EnumStatusColor.Danger.GetEnumDescription() },
                        ToReviewCard = new VM_StatsCard { Title = "待查看", Value = 0, Total = 0, CardColor = EnumStatusColor.Info.GetEnumDescription() },
                        OnTimeCard = new VM_StatsCard { Title = "按时提交", Value = 0, Total = 0, CardColor = EnumStatusColor.Success.GetEnumDescription() },
                        LateCard = new VM_StatsCard { Title = "迟交", Value = 0, Total = 0, CardColor = EnumStatusColor.Warning.GetEnumDescription() }
                    },
                    CurrentPeriodText = GetCurrentPeriodText(reportType)
                };
            }
        }

        /// <summary>
        /// 获取当前报告周期文本
        /// </summary>
        /// <param name="reportType"></param>
        /// <returns></returns>
        private string GetCurrentPeriodText(EnumReportType reportType)
        {
            try
            {
                var now = DateTime.Now;
                switch (reportType)
                {
                    case EnumReportType.Daily:
                        // 计算日报的时效周期
                        var dailyDeadlineInfo = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                        {
                            ReportType = EnumReportType.Daily,
                            ReportDate = now.Date
                        });
                        var regularTime = DateTime.Parse(dailyDeadlineInfo.RegularTime);
                        var finalTime = DateTime.Parse(dailyDeadlineInfo.FinalTime);
                        return $"{regularTime:MM月dd日 HH:mm} - {finalTime:MM月dd日 HH:mm}";
                        
                    case EnumReportType.Weekly:
                        // 计算周报的时效周期
                        var weekStart = now.AddDays(-(int)now.DayOfWeek + 1).Date;
                        var weeklyDeadlineInfo = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                        {
                            ReportType = EnumReportType.Weekly,
                            ReportDate = weekStart
                        });
                        var weekRegularTime = DateTime.Parse(weeklyDeadlineInfo.RegularTime);
                        var weekFinalTime = DateTime.Parse(weeklyDeadlineInfo.FinalTime);
                        return $"{weekRegularTime:MM月dd日 HH:mm} - {weekFinalTime:MM月dd日 HH:mm}";
                        
                    case EnumReportType.Monthly:
                        // 计算月报的时效周期
                        var monthStart = new DateTime(now.Year, now.Month, 1);
                        var monthlyDeadlineInfo = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                        {
                            ReportType = EnumReportType.Monthly,
                            ReportDate = monthStart
                        });
                        var monthRegularTime = DateTime.Parse(monthlyDeadlineInfo.RegularTime);
                        var monthFinalTime = DateTime.Parse(monthlyDeadlineInfo.FinalTime);
                        return $"{monthRegularTime:MM月dd日 HH:mm} - {monthFinalTime:MM月dd日 HH:mm}";
                        
                    default:
                        return "报告统计";
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取报告周期文本失败：{ex.Message}", ex);
                return "报告统计";
            }
        }

        /// <summary>
        /// 获取待处理报告数据（从数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="reportType">报告类型</param>
        /// <returns></returns>
        private List<VM_PendingReport> GetPendingReportsFromDatabase(string userId, EnumReportType reportType)
        {
            try
            {
                // 根据报告类型确定查询的日期范围
                var (startDate, endDate) = GetReportDateRange(reportType);

                // 1. 查询当前用户作为接收人的未读报告
                var unreadReceiverRecords = DbOpe_crm_report_receiver.Instance.GetDataList(x => 
                    x.ReceiverId == userId && 
                    x.Status == (int)EnumReceiverStatus.Unread && // 使用枚举
                    x.Deleted == false);
                
                // 2. 查询当前用户作为抄送人的未读报告
                var unreadCcRecords = DbOpe_crm_report_cc.Instance.GetDataList(x => 
                    x.CcUserId == userId && 
                    x.Status == (int)EnumReceiverStatus.Unread && // 使用枚举
                    x.Deleted == false);
                
                // 3. 合并所有未读报告的ID
                var allUnreadReportIds = unreadReceiverRecords.Select(x => x.ReportId)
                    .Union(unreadCcRecords.Select(x => x.ReportId))
                    .Distinct()
                    .ToList();
                
                if (!allUnreadReportIds.Any())
                {
                    return new List<VM_PendingReport>();
                }
                
                // 4. 查询这些未读报告的详细信息，不限制团队范围，只要接收人或抄送人是当前用户的都显示
                var unreadReports = DbOpe_crm_report_base.Instance.GetDataList(x => 
                    allUnreadReportIds.Contains(x.Id) && 
                    x.Status == (int)EnumReportStatus.Submitted && // 已提交的报告
                    x.Deleted == false);
                
                // 4. 构建待处理报告列表
                var pendingReports = unreadReports
                    .Select(x => new VM_PendingReport
                    {
                        ReportId = x.Id,
                        ReportType = (EnumReportType)x.ReportType,
                        Title = x.Title,
                        SubmitterUserId = x.CreateUser,
                        SubmitterUserName = GetUserNameByIdForWorkbench(x.CreateUser),
                        SubmitterDepartment = GetUserDepartment(x.CreateUser),
                        SubmitTime = x.SubmitTime ?? DateTime.Now,
                        SubmitStatus = x.SubmitStatus.HasValue ? EnumHelper.GetEnumDescription<EnumSubmitStatus>((EnumSubmitStatus)x.SubmitStatus.Value) : "未提交",
                        SubmitStatusColor = x.SubmitStatus.HasValue ? (x.SubmitStatus.Value == (int)EnumSubmitStatus.OnTime ? EnumStatusColor.Success : 
                                                                      x.SubmitStatus.Value == (int)EnumSubmitStatus.Late ? EnumStatusColor.Warning : 
                                                                      EnumStatusColor.Danger) : EnumStatusColor.Danger,
                        IsUrgent = IsReportUrgent(x.SubmitTime ?? x.CreateDate)
                    })
                    .OrderByDescending(x => x.SubmitTime) // 按提交时间倒序
                    .Take(5) // 最多取5条
                    .ToList();

                return pendingReports;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取待处理报告数据失败：{ex.Message}", ex);
                return new List<VM_PendingReport>();
            }
        }

        #endregion

        #region 模块化接口

        /// <summary>
        /// 获取我的报告管理数据（最近3条）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public GetMyReports_Out GetMyReports(GetMyReports_In input)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var reports = GetMyReportsFromDatabase(currentUserId, input.ReportType, input.StartDate, input.EndDate);

                return new GetMyReports_Out
                {
                    Reports = reports
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取我的报告数据失败：{ex.Message}", ex);
                throw new ApiException($"获取我的报告数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取团队成员状态数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public ApiTableOut<VM_TeamMember> GetTeamMembers(GetTeamMembers_In input)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var (members, totalCount) = GetTeamMembersFromDatabase(currentUserId, input.ReportType, input.PageNumber, input.PageSize);

                return new ApiTableOut<VM_TeamMember>
                {
                    Data = members,
                    Total = totalCount
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取团队成员状态失败：{ex.Message}", ex);
                throw new ApiException($"获取团队成员状态失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取我的报告列表数据（最近3条，从数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="reportType">报告类型</param>
        /// <param name="startDate">开始日期（可选）</param>
        /// <param name="endDate">结束日期（可选）</param>
        /// <returns></returns>
        private List<VM_ReportSummary> GetMyReportsFromDatabase(string userId, EnumReportType reportType, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                // 获取报告数据
                var reports = GetReportsWithDateRange(userId, reportType, startDate, endDate);

                // 构建结果 - 使用存储的数量字段，避免实时查询
                var result = reports.Select(x => new VM_ReportSummary
                {
                    ReportId = x.Id,
                    Title = x.Title,
                    ReportDate = x.ReportDate,
                    Status = (EnumReportStatus)x.Status,
                    CommentCount = x.CommentCount,
                    LikeCount = x.LikeCount
                }).ToList();

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取我的报告列表失败：{ex.Message}", ex);
                return new List<VM_ReportSummary>();
            }
        }
        
        /// <summary>
        /// 根据日期范围获取报告数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="reportType">报告类型</param>
        /// <param name="startDate">开始日期（可选）</param>
        /// <param name="endDate">结束日期（可选）</param>
        /// <returns>报告数据列表</returns>
        private List<DAL.DbModel.Crm2.Db_crm_report_base> GetReportsWithDateRange(string userId, EnumReportType reportType, DateTime? startDate = null, DateTime? endDate = null)
        {
            // 使用 DbContext.Crm2Db.Queryable 构建查询
            var query = DbContext.Crm2Db.Queryable<DAL.DbModel.Crm2.Db_crm_report_base>()
                .Where(x => x.UserId == userId)
                .Where(x => x.ReportType == (int)reportType)
                .Where(x => x.Deleted == false);

            // 使用 WhereIF 添加条件查询
            query = query.WhereIF(startDate.HasValue, x => x.ReportDate >= startDate.Value);
            query = query.WhereIF(endDate.HasValue, x => x.ReportDate <= endDate.Value);

            // 排序并获取结果
            return query.OrderByDescending(x => x.ReportDate)
                .Take(3)
                .ToList();
        }

        /// <summary>
        /// 获取团队成员数据（从数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="reportType">报告类型</param>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>(成员列表, 总记录数)</returns>
        private (List<VM_TeamMember> members, int totalCount) GetTeamMembersFromDatabase(string userId, EnumReportType reportType, int pageNumber, int pageSize)
        {
            try
            {
                // 获取用户的团队信息
                var userTeamInfo = GetUserTeamInfoForWorkbench(userId);
                if (userTeamInfo == null)
                {
                    return (new List<VM_TeamMember>(), 0);
                }

                // 获取团队报告查看范围内的用户列表
                var teamMemberIds = GetTeamReportViewScopeUserIds(userTeamInfo.TeamId);
                if (teamMemberIds.Count == 0)
                {
                    return (new List<VM_TeamMember>(), 0);
                }

                // 排除当前用户
                teamMemberIds = teamMemberIds.Where(id => id != userId).ToList();

                // 获取当前和上一个报告周期的日期范围
                var (currentStart, currentEnd) = GetReportDateRange(reportType);
                var (lastStart, lastEnd) = GetLastReportDateRange(reportType);

                // 使用数据库分页查询团队成员
                var query = DbContext.Crm2Db.Queryable<DAL.DbModel.Crm2.Db_sys_user>()
                    .Where(u => teamMemberIds.Contains(u.Id))
                    .Where(u => u.Deleted == false)
                    .Select(u => new
                    {
                        u.Id,
                        u.Name,
                        u.OrganizationId
                    });

                // 应用分页
                int totalCount = 0;
                var pagedUsers = query.ToPageList(pageNumber, pageSize, ref totalCount);

                // 获取用户部门信息
                var allUsers = RedisCache.UserWithOrg.GetAllUsers();
                var userDict = allUsers?.Where(x => teamMemberIds.Contains(x.Id))
                                      .ToDictionary(x => x.Id, x => x) ?? new Dictionary<string, RedisCache.UserWithOrg.UserWithOrgSimple>();

                var result = new List<VM_TeamMember>();

                foreach (var user in pagedUsers)
                {
                    var memberName = user.Name;
                    var memberDepartment = userDict.ContainsKey(user.Id) ? userDict[user.Id].OrgFullName ?? "" : "";

                    // 查询上一期报告
                    var lastReport = DbOpe_crm_report_base.Instance.GetData(x =>
                        x.UserId == user.Id &&
                        x.ReportType == (int)reportType &&
                        x.ReportDate >= lastStart && x.ReportDate <= lastEnd &&
                        x.Deleted != true);

                    // 查询当前期报告
                    var currentReport = DbOpe_crm_report_base.Instance.GetData(x =>
                        x.UserId == user.Id &&
                        x.ReportType == (int)reportType &&
                        x.ReportDate >= currentStart && x.ReportDate <= currentEnd &&
                        x.Deleted != true);

                    result.Add(new VM_TeamMember
                    {
                        UserId = user.Id,
                        UserName = memberName,
                        Department = memberDepartment,
                        LastReport = GetMemberReportInfo(lastReport, "上期"),
                        CurrentReport = GetMemberReportInfo(currentReport, "本期")
                    });
                }

                return (result, totalCount);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取团队成员数据失败：{ex.Message}", ex);
                return (new List<VM_TeamMember>(), 0);
            }
        }

        /// <summary>
        /// 获取报告周期数据
        /// </summary>
        /// <param name="reportType"></param>
        /// <returns></returns>
        private VM_ReportPeriod GetReportPeriodData(EnumReportType reportType)
        {
            var now = DateTime.Now;
            string periodText;

            switch (reportType)
            {
                case EnumReportType.Daily:
                    periodText = "昨天日报";
                    break;
                case EnumReportType.Weekly:
                    periodText = "上周周报";
                    break;
                case EnumReportType.Monthly:
                    periodText = "上月月报";
                    break;
                default:
                    periodText = "报告";
                    break;
            }

            return new VM_ReportPeriod
            {
                PeriodText = periodText,
                ReportType = reportType
            };
        }

        #endregion

        #region 接收人和抄送人管理

        /// <summary>
        /// 获取可选的接收人列表（包括默认接收人）
        /// 接收人只能是：当前用户的所有上级 + 销售总监 + 当前团队队长
        /// </summary>
        /// <returns>接收人列表</returns>
        public List<VM_ReportReceiver> GetAvailableReceivers()
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = new List<VM_ReportReceiver>();
                
                // 获取当前用户信息
                var currentUser = DbOpe_sys_user.Instance.GetData(x => x.Id == currentUserId && x.Deleted == false);
                if (currentUser == null)
                {
                    throw new ApiException("当前用户不存在");
                }
                
                // 获取用户的部门信息
                var orgDict = new Dictionary<string, string>();
                
                // 1. 获取当前用户的所有上级
                var superiors = GetUserSuperiors(currentUserId);
                
                // 2. 获取所有销售总监
                var salesManagers = GetSalesManagerUsers();
                
                // 3. 获取当前团队的队长
                var teamLeaders = GetCurrentTeamLeaders(currentUserId);
                
                // 合并上级、销售总监和团队队长，去重
                var availableUserIds = new HashSet<string>();
                availableUserIds.UnionWith(superiors.Select(x => x.Id));
                availableUserIds.UnionWith(salesManagers.Select(x => x.Id));
                availableUserIds.UnionWith(teamLeaders.Select(x => x.Id));
                
                // 从Redis缓存获取用户信息，包含部门信息
                var allUsers = RedisCache.UserWithOrg.GetAllUsers();
                var userDict = new Dictionary<string, RedisCache.UserWithOrg.UserWithOrgSimple>();
                if (allUsers != null && allUsers.Count > 0)
                {
                    userDict = allUsers.Where(x => availableUserIds.Contains(x.Id))
                                      .ToDictionary(x => x.Id, x => x);
                }
                // 查询默认接收人
                var defaultReceiverIds = new HashSet<string>();
                var receiverRecords = DbOpe_crm_report_receiver.Instance.GetDataList(x => 
                    x.CreateUser == currentUserId &&
                    x.IsDefault == true && 
                    x.Deleted == false);
                defaultReceiverIds = new HashSet<string>(receiverRecords.Select(x => x.ReceiverId).Distinct());
                // 构建返回结果
                foreach (var userId in availableUserIds)
                {
                    if (!userDict.ContainsKey(userId))
                        continue;
                    var user = userDict[userId];
                    string department = user.OrgFullName ?? "";
                    result.Add(new VM_ReportReceiver
                    {
                        UserId = user.Id,
                        UserName = string.IsNullOrEmpty(department) ? user.Name : $"{user.Name}({department})",
                        IsDefault = defaultReceiverIds.Contains(user.Id)
                    });
                }
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取可选接收人列表失败：{ex.Message}", ex);
                throw new ApiException($"获取可选接收人列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 设置默认接收人
        /// </summary>
        /// <param name="input">设置参数</param>
        /// <returns>设置结果</returns>
        public OperationResult_Out SetDefaultReceivers(SetDefaultReceivers_In input)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                string targetUserId = string.IsNullOrEmpty(input.UserId) ? currentUserId : input.UserId;
                
                // 调用更新默认接收人方法，不传递reportId，使用虚拟reportId
                UpdateDefaultReceivers(targetUserId, input.ReceiverIds, input.ReportType);
                
                // 如果有抄送人设置，也一并更新
                if (input.CcIds != null && input.CcIds.Any())
                {
                    UpdateDefaultCcUsers(targetUserId, input.CcIds, input.ReportType);
                }
                
                return new OperationResult_Out
                {
                    Success = true,
                    RowsAffected = input.ReceiverIds.Count + (input.CcIds?.Count ?? 0),
                    Message = "默认接收人设置成功"
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"设置默认接收人失败：{ex.Message}", ex);
                throw new ApiException($"设置默认接收人失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取可选的抄送人列表（包括默认抄送人）
        /// 抄送人只能是：当前用户的所有上级 + 销售总监 + 当前团队队长
        /// </summary>
        /// <returns>抄送人列表</returns>
        public List<VM_ReportReceiver> GetAvailableCcs()
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = new List<VM_ReportReceiver>();
                
                // 获取当前用户信息
                var currentUser = DbOpe_sys_user.Instance.GetData(x => x.Id == currentUserId && x.Deleted == false);
                if (currentUser == null)
                {
                    throw new ApiException("当前用户不存在");
                }
                
                // 获取用户的部门信息
                var orgDict = new Dictionary<string, string>();
                
                // 1. 获取当前用户的所有上级
                var superiors = GetUserSuperiors(currentUserId);
                
                // 2. 获取所有销售总监
                var salesManagers = GetSalesManagerUsers();
                
                // 3. 获取当前团队的队长
                var teamLeaders = GetCurrentTeamLeaders(currentUserId);
                
                // 合并上级、销售总监和团队队长，去重
                var availableUserIds = new HashSet<string>();
                availableUserIds.UnionWith(superiors.Select(x => x.Id));
                availableUserIds.UnionWith(salesManagers.Select(x => x.Id));
                availableUserIds.UnionWith(teamLeaders.Select(x => x.Id));
                
                // 从Redis缓存获取用户信息，包含部门信息
                var allUsers = RedisCache.UserWithOrg.GetAllUsers();
                var userDict = new Dictionary<string, RedisCache.UserWithOrg.UserWithOrgSimple>();
                if (allUsers != null && allUsers.Count > 0)
                {
                    userDict = allUsers.Where(x => availableUserIds.Contains(x.Id))
                                      .ToDictionary(x => x.Id, x => x);
                }
                // 查询默认抄送人
                var defaultCcIds = new HashSet<string>();
                var ccRecords = DbOpe_crm_report_cc.Instance.GetDataList(x => 
                    x.CreateUser == currentUserId &&
                    x.IsDefault == true && 
                    x.Deleted == false);
                defaultCcIds = new HashSet<string>(ccRecords.Select(x => x.CcUserId).Distinct());
                // 构建返回结果
                foreach (var userId in availableUserIds)
                {
                    if (!userDict.ContainsKey(userId))
                        continue;
                    var user = userDict[userId];
                    string department = user.OrgFullName ?? "";
                    result.Add(new VM_ReportReceiver
                    {
                        UserId = user.Id,
                        UserName = string.IsNullOrEmpty(department) ? user.Name : $"{user.Name}({department})",
                        IsDefault = defaultCcIds.Contains(user.Id)
                    });
                }
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取可选抄送人列表失败：{ex.Message}", ex);
                throw new ApiException($"获取可选抄送人列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户的所有上级
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>上级用户列表</returns>
        private List<DAL.DbModel.Crm2.Db_sys_user> GetUserSuperiors(string userId)
        {
            try
            {
                var result = new List<DAL.DbModel.Crm2.Db_sys_user>();
                
                //需要排除的后台管理人员组织
                const string BACKEND_ADMIN_ORG_ID = "00000000-0000-0000-0000-000000000000";
                
                // 从Redis缓存获取当前用户信息
                var currentUser = RedisCache.UserWithOrg.GetUserById(userId);
                if (currentUser == null)
                    return result;
                
                // 获取当前用户的组织信息
                if (string.IsNullOrEmpty(currentUser.OrganizationId))
                    return result;
                
                var currentOrg = DbOpe_sys_organization.Instance.GetData(x => x.Id == currentUser.OrganizationId && x.Deleted == false);
                if (currentOrg == null)
                    return result;
                
                // 获取当前组织的所有上级组织
                var superiorOrgIds = GetSuperiorOrganizationIds(currentOrg.Id);
                
                // 获取这些上级组织中的队长（使用Redis缓存）
                if (superiorOrgIds.Any())
                {
                    var allUsers = RedisCache.UserWithOrg.GetAllUsers();
                    if (allUsers != null && allUsers.Count > 0)
                    {
                        // 获取销售总监角色ID
                        const string SALES_MANAGER_ROLE_ID = "0cafa3b1-1d14-4047-ae52-2feb0bf2c1cc";
                        
                        // 获取所有销售总监用户ID
                        var salesManagerUserIds = DbOpe_sys_userinrole.Instance.GetDataList(x => 
                            x.RoleId == SALES_MANAGER_ROLE_ID)
                            .Select(x => x.UserId)
                            .Distinct()
                            .ToList();
                        
                        var superiorLeaders = allUsers.Where(x => 
                            superiorOrgIds.Contains(x.OrganizationId) && 
                            x.UserStatus == true &&
                            (x.OrganizationId != BACKEND_ADMIN_ORG_ID || salesManagerUserIds.Contains(x.Id)) && // 排除后台人员，但包含销售总监
                            x.UserType == (int)EnumUserType.Manager) // 只获取队长（管理者）
                            .Select(x => new DAL.DbModel.Crm2.Db_sys_user
                            {
                                Id = x.Id,
                                Name = x.Name,
                                OrganizationId = x.OrganizationId,
                                UserStatus = x.UserStatus
                            })
                            .ToList();
                        result.AddRange(superiorLeaders);
                    }
                }
                
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取用户上级失败，用户ID：{userId}，错误：{ex.Message}");
                return new List<DAL.DbModel.Crm2.Db_sys_user>();
            }
        }
        
        /// <summary>
        /// 获取组织的所有上级组织ID
        /// </summary>
        /// <param name="orgId">组织ID</param>
        /// <returns>上级组织ID列表</returns>
        private List<string> GetSuperiorOrganizationIds(string orgId)
        {
            var result = new List<string>();
            
            try
            {
                var currentOrg = DbOpe_sys_organization.Instance.GetData(x => x.Id == orgId && x.Deleted == false);
                if (currentOrg == null)
                    return result;
                
                // 递归获取所有上级组织
                var parentId = currentOrg.ParentId;
                while (!string.IsNullOrEmpty(parentId))
                {
                    result.Add(parentId);
                    var parentOrg = DbOpe_sys_organization.Instance.GetData(x => x.Id == parentId && x.Deleted == false);
                    if (parentOrg == null)
                        break;
                    parentId = parentOrg.ParentId;
                }
                
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取组织上级失败，组织ID：{orgId}，错误：{ex.Message}");
                return result;
            }
        }
        
        /// <summary>
        /// 获取所有销售总监用户
        /// </summary>
        /// <returns>销售总监用户列表</returns>
        private List<DAL.DbModel.Crm2.Db_sys_user> GetSalesManagerUsers()
        {
            try
            {
                var result = new List<DAL.DbModel.Crm2.Db_sys_user>();
                
                // 销售总监角色ID
                const string SALES_MANAGER_ROLE_ID = "0cafa3b1-1d14-4047-ae52-2feb0bf2c1cc";
                
                // 获取所有具有销售总监角色的用户
                var salesManagerUserIds = DbOpe_sys_userinrole.Instance.GetDataList(x => 
                    x.RoleId == SALES_MANAGER_ROLE_ID)
                    .Select(x => x.UserId)
                    .Distinct()
                    .ToList();
                
                if (salesManagerUserIds.Any())
                {
                    // 从Redis缓存获取用户信息
                    var allUsers = RedisCache.UserWithOrg.GetAllUsers();
                    if (allUsers != null && allUsers.Count > 0)
                    {
                        var salesManagerUsers = allUsers.Where(x => 
                            salesManagerUserIds.Contains(x.Id) && 
                            x.UserStatus == true)
                            .Select(x => new DAL.DbModel.Crm2.Db_sys_user
                            {
                                Id = x.Id,
                                Name = x.Name,
                                OrganizationId = x.OrganizationId,
                                UserStatus = x.UserStatus
                            })
                            .ToList();
                        result.AddRange(salesManagerUsers);
                    }
                }
                
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取销售总监用户失败：{ex.Message}");
                return new List<DAL.DbModel.Crm2.Db_sys_user>();
            }
        }

        /// <summary>
        /// 设置默认抄送人
        /// </summary>
        /// <param name="input">设置参数</param>
        /// <returns>设置结果</returns>
        public OperationResult_Out SetDefaultCcs(SetDefaultCcs_In input)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                string targetUserId = string.IsNullOrEmpty(input.UserId) ? currentUserId : input.UserId;
                
                // 调用更新默认抄送人方法，不传递reportId，使用虚拟reportId
                UpdateDefaultCcUsers(targetUserId, input.CcIds, input.ReportType);
                
                return new OperationResult_Out
                {
                    Success = true,
                    RowsAffected = input.CcIds.Count,
                    Message = "默认抄送人设置成功"
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"设置默认抄送人失败：{ex.Message}", ex);
                throw new ApiException($"设置默认抄送人失败：{ex.Message}");
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取时间前文本（多久前）
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        private string GetTimeAgoText(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "刚刚";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}分钟前";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}小时前";
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays}天前";

            return dateTime.ToString("MM-dd");
        }

        /// <summary>
        /// 根据用户ID获取用户名（工作台专用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户名</returns>
        private string GetUserNameByIdForWorkbench(string userId)
        {
            try
            {
                // 从用户表中查询用户名
                var user = DbOpe_sys_user.Instance.GetData(x => x.Id == userId && x.Deleted == false);
                return user?.Name ?? $"未知用户({userId})";
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取用户名失败，用户ID：{userId}，错误：{ex.Message}", ex);
                return $"未知用户({userId})";
            }
        }

        /// <summary>
        /// 检查用户是否为领导（工作台专用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否为领导</returns>
        private bool CheckUserIsLeaderForWorkbench(string userId)
        {
            try
            {
                // 从用户表中查询用户类型，UserType=1表示管理者
                var user = DbOpe_sys_user.Instance.GetData(x => 
                    x.Id == userId && 
                    x.Deleted == false && 
                    x.UserStatus == true);
                
                if (user == null)
                    return false;
                
                // 检查用户是否为管理者类型
                return user.UserType == (int)EnumUserType.Manager;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"检查用户是否为领导失败，用户ID：{userId}，错误：{ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取用户团队信息（工作台专用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户团队信息</returns>
        private VM_UserTeamInfo GetUserTeamInfoForWorkbench(string userId)
        {
            try
            {
                // 从用户表中查询用户所属组织ID
                var user = DbOpe_sys_user.Instance.GetData(x => 
                    x.Id == userId && 
                    x.Deleted == false && 
                    x.UserStatus == true);
                
                if (user == null || string.IsNullOrEmpty(user.OrganizationId))
                {
                    return new VM_UserTeamInfo
                    {
                        TeamId = string.Empty,
                        TeamName = "未分配团队",
                        IsLeader = false
                    };
                }
                
                // 从组织表中查询团队信息
                var team = DbOpe_sys_organization.Instance.GetData(x => 
                    x.Id == user.OrganizationId && 
                    x.Deleted == false && 
                    x.OrgStatus == true);
                
                if (team == null)
                {
                    return new VM_UserTeamInfo
                    {
                        TeamId = user.OrganizationId,
                        TeamName = "未知团队",
                        IsLeader = false
                    };
                }
                
                // 检查用户是否为团队领导
                bool isLeader = CheckUserIsLeaderForWorkbench(userId);
                
                return new VM_UserTeamInfo
                {
                    TeamId = team.Id,
                    TeamName = team.OrgName,
                    IsLeader = isLeader
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取用户团队信息失败，用户ID：{userId}，错误：{ex.Message}", ex);
                return new VM_UserTeamInfo
                {
                    TeamId = string.Empty,
                    TeamName = "获取团队信息失败",
                    IsLeader = false
                };
            }
        }

        /// <summary>
        /// 获取团队报告查看范围的用户ID列表
        /// </summary>
        /// <param name="teamId">团队ID</param>
        /// <returns>团队报告查看范围内的用户ID列表，包含团队里所有成员和所有直属下级团队的队长</returns>
        private List<string> GetTeamReportViewScopeUserIds(string teamId)
        {
            try
            {
                // 从组织架构表中查询团队成员
                if (string.IsNullOrEmpty(teamId))
                    return new List<string>();

                var result = new List<string>();
                var currentUserId = GetCurrentUserId();
                
                // 需要排除的后台管理人员组织
                const string BACKEND_ADMIN_ORG_ID = "00000000-0000-0000-0000-000000000000";
                
                // 获取销售总监角色ID
                const string SALES_MANAGER_ROLE_ID = "0cafa3b1-1d14-4047-ae52-2feb0bf2c1cc";
                
                // 获取所有销售总监用户ID
                var salesManagerUserIds = DbOpe_sys_userinrole.Instance.GetDataList(x => 
                    x.RoleId == SALES_MANAGER_ROLE_ID)
                    .Select(x => x.UserId)
                    .Distinct()
                    .ToList();
                
                // 1. 查询指定团队的所有成员（排除后台管理人员，但包含销售总监）
                var teamMembers = DbOpe_sys_user.Instance.GetDataList(x =>
                    x.OrganizationId == teamId && 
                    x.Deleted == false && 
                    x.UserStatus == true &&
                    (x.OrganizationId != BACKEND_ADMIN_ORG_ID || salesManagerUserIds.Contains(x.Id)));
                
                result.AddRange(teamMembers.Select(x => x.Id));
                
                // 2. 查询当前团队信息，确定是什么级别的团队
                var currentTeam = DbOpe_sys_organization.Instance.GetData(x => 
                    x.Id == teamId && x.Deleted == false && x.OrgStatus == true);
                
                if (currentTeam != null)
                {
                    // 3. 查询直属下级团队
                    var subTeams = DbOpe_sys_organization.Instance.GetDataList(x => 
                        x.ParentId == teamId && x.Deleted == false && x.OrgStatus == true);
                    
                    if (subTeams.Any())
                    {
                        // 4. 查询每个直属团队的队长（排除后台管理人员，但包含销售总监）
                        foreach (var subTeam in subTeams)
                        {
                            // 查询该团队的队长（假设队长是UserType=1的用户）
                            var teamLeaders = DbOpe_sys_user.Instance.GetDataList(x =>
                                x.OrganizationId == subTeam.Id && 
                                x.UserType == (int)EnumUserType.Manager && 
                                x.Deleted == false && 
                                x.UserStatus == true &&
                                (x.OrganizationId != BACKEND_ADMIN_ORG_ID || salesManagerUserIds.Contains(x.Id)));
                            
                            result.AddRange(teamLeaders.Select(x => x.Id));
                        }
                    }
                }
                
                // 排除当前用户自己
                result = result.Where(x => x != currentUserId).Distinct().ToList();
                
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取团队成员失败，团队ID：{teamId}，错误：{ex.Message}", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取报告日期范围
        /// </summary>
        /// <param name="reportType"></param>
        /// <returns></returns>
        private (DateTime startDate, DateTime endDate) GetReportDateRange(EnumReportType reportType)
        {
            var now = DateTime.Now;
            switch (reportType)
            {
                case EnumReportType.Daily:
                    return (now.Date, now.Date);
                case EnumReportType.Weekly:
                    var weekStart = now.AddDays(-(int)now.DayOfWeek + 1).Date;
                    return (weekStart, weekStart.AddDays(6));
                case EnumReportType.Monthly:
                    var monthStart = new DateTime(now.Year, now.Month, 1);
                    return (monthStart, monthStart.AddMonths(1).AddDays(-1));
                default:
                    return (now.Date, now.Date);
            }
        }

        /// <summary>
        /// 获取上一个报告周期的日期范围
        /// </summary>
        /// <param name="reportType"></param>
        /// <returns></returns>
        private (DateTime startDate, DateTime endDate) GetLastReportDateRange(EnumReportType reportType)
        {
            try
            {
                var now = DateTime.Now;
                var holidayDbOpe = DbOpe_crm_holiday.Instance;
                
                switch (reportType)
                {
                    case EnumReportType.Daily:
                        // 日报：上一个周期是前一个工作日的日报
                        var previousWorkday = GetPreviousWorkday(now.Date);
                        return (previousWorkday, previousWorkday);
                        
                    case EnumReportType.Weekly:
                        // 周报：上一个周期是上一周的报告
                        // 获取当前周的开始日期（周一）
                        var currentWeekStart = now.AddDays(-(int)now.DayOfWeek + 1).Date;
                        // 上一周的开始日期
                        var lastWeekStart = currentWeekStart.AddDays(-7);
                        // 上一周的结束日期
                        var lastWeekEnd = lastWeekStart.AddDays(6);
                        return (lastWeekStart, lastWeekEnd);
                        
                    case EnumReportType.Monthly:
                        // 月报：上一个周期是上一个月的报告
                        // 获取当前月的第一天
                        var currentMonthStart = new DateTime(now.Year, now.Month, 1);
                        // 上个月的第一天
                        var lastMonthStart = currentMonthStart.AddMonths(-1);
                        // 上个月的最后一天
                        var lastMonthEnd = currentMonthStart.AddDays(-1);
                        return (lastMonthStart, lastMonthEnd);
                        
                    default:
                        var yesterday = GetPreviousWorkday(now.Date);
                        return (yesterday, yesterday);
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取上一个报告周期日期范围失败：{ex.Message}", ex);
                // 返回默认值
                var yesterday = DateTime.Now.AddDays(-1).Date;
                return (yesterday, yesterday);
            }
        }

        /// <summary>
        /// 获取年份中的周数（工作台专用）
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        private int GetWeekOfYearForWorkbench(DateTime date)
        {
            var jan1 = new DateTime(date.Year, 1, 1);
            var daysOffset = (int)jan1.DayOfWeek - 1;
            var firstWeekDay = jan1.AddDays(-daysOffset);
            var weekNum = ((date - firstWeekDay).Days / 7) + 1;
            return weekNum;
        }

        

        /// <summary>
        /// 判断报告是否紧急
        /// </summary>
        /// <param name="submitTime"></param>
        /// <returns></returns>
        private bool IsReportUrgent(DateTime submitTime)
        {
            // 如果提交时间超过2天，认为是紧急的
            return (DateTime.Now - submitTime).TotalDays > 2;
        }

        /// <summary>
        /// 获取用户部门
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户所属部门名称</returns>
        private string GetUserDepartment(string userId)
        {
            try
            {
                // 兜底查数据库
                var dbUser = DbOpe_sys_user.Instance.GetData(x => 
                    x.Id == userId && 
                    x.Deleted == false && 
                    x.UserStatus == true);
                if (dbUser == null || string.IsNullOrEmpty(dbUser.OrganizationId))
                {
                    return "未分配部门";
                }
                
                // 如果是非销售组织（后台人员），不显示部门信息
                if (dbUser.OrganizationId ==    Guid.Empty.ToString())
                {
                    return "";
                }
                
                // 优先从Redis缓存获取
                var user = RedisCache.UserWithOrg.GetUserById(userId);
                if (user != null && !string.IsNullOrEmpty(user.OrgFullName))
                {
                    return user.OrgFullName;
                }
                
                var org = DbOpe_sys_organization.Instance.GetData(x => 
                    x.Id == dbUser.OrganizationId && 
                    x.Deleted == false && 
                    x.OrgStatus == true);
                if (org != null)
                {
                    if (!string.IsNullOrEmpty(org.DepartmentName))
                        return org.DepartmentName;
                    return org.OrgName;
                }
                return "未知部门";
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取用户部门失败，用户ID：{userId}，错误：{ex.Message}", ex);
                return "未知部门";
            }
        }

        /// <summary>
        /// 获取成员报告信息
        /// </summary>
        /// <param name="report"></param>
        /// <param name="periodText"></param>
        /// <returns></returns>
        private VM_MemberReportInfo GetMemberReportInfo(dynamic report, string periodText)
        {
            if (report == null)
            {
                return new VM_MemberReportInfo
                {
                    ReportId = "",
                    StatusText = "未提交",
                    StatusColor = EnumStatusColor.Danger,
                    SubmitTimeText = "",
                    CanView = false
                };
            }

            var status = (EnumReportStatus)report.Status;
            
            // 如果是草稿状态，对其他人显示为"未提交"
            if (status == EnumReportStatus.Draft)
            {
                return new VM_MemberReportInfo
                {
                    ReportId = "",
                    StatusText = "未提交",
                    StatusColor = EnumStatusColor.Danger,
                    SubmitTimeText = "",
                    CanView = false
                };
            }
            
            return new VM_MemberReportInfo
            {
                ReportId = report.Id,
                StatusText = GetReportStatusText(status),
                StatusColor = GetReportStatusColor(status),
                SubmitTimeText = report.SubmitTime?.ToString("MM-dd HH:mm") ?? "",
                CanView = true
            };
        }

        /// <summary>
        /// 获取报告状态文本
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private string GetReportStatusText(EnumReportStatus status)
        {
            return status switch
            {
                EnumReportStatus.Draft => "草稿",
                EnumReportStatus.Submitted => "已提交",
                _ => "未知"
            };
        }

        /// <summary>
        /// 获取报告状态颜色
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private EnumStatusColor GetReportStatusColor(EnumReportStatus status)
        {
            return status switch
            {
                EnumReportStatus.Draft => EnumStatusColor.Warning,
                EnumReportStatus.Submitted => EnumStatusColor.Success,
                _ => EnumStatusColor.Danger
            };
        }

        #endregion

        #region 日报提醒辅助方法

        /// <summary>
        /// 添加日报提醒
        /// </summary>
        /// <param name="taskReminders">任务提醒列表</param>
        /// <param name="userId">用户ID</param>
        /// <param name="reportDate">报告日期</param>
        /// <param name="reportDateText">报告日期文本</param>
        /// <param name="holidayDbOpe">节假日操作类</param>
        private void AddDailyReportReminder(List<VM_TaskReminder> taskReminders, string userId, DateTime reportDate, string reportDateText, DbOpe_crm_holiday holidayDbOpe)
        {
            try
            {
                var currentTime = DateTime.Now;
                
                // 处理节假日逻辑
                var actualReportDate = reportDate;
                if (!holidayDbOpe.IsWorkday(reportDate))
                {
                    // 判断是"今天日报"还是"昨天日报"
                    var today = DateTime.Now.Date;
                    if (reportDate == today)
                    {
                        // 今天是节假日，今天日报应该指向下一个工作日
                        actualReportDate = holidayDbOpe.GetNextWorkday(reportDate, 1);
                    }
                    else
                    {
                        // 昨天是节假日，昨天日报应该指向前一个工作日
                        actualReportDate = GetPreviousWorkday(reportDate);
                    }
                }
                
                // 查询该日期的日报（使用调整后的日期）
                var dailyReport = DbOpe_crm_report_base.Instance.GetData(x => 
                    x.UserId == userId && 
                    x.ReportType == (int)EnumReportType.Daily && 
                    x.ReportDate.Date == actualReportDate &&
                    x.Deleted != true);

                // 计算日报时效信息（使用调整后的日期）
                var dailyDeadlineInfo = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                {
                    ReportType = EnumReportType.Daily,
                    ReportDate = actualReportDate
                });
                
                // 日报提醒逻辑：从报告日期当天开始显示，而不是等到规定时间
                var reportDateStart = actualReportDate.Date; // 报告日期的开始时间（00:00）
                
                // 如果当前时间在报告日期当天或之后，就显示日报提醒
                if (currentTime >= reportDateStart)
                {
                    string typeName = "日报";
                    var regularTime = DateTime.Parse(dailyDeadlineInfo.RegularTime);
                    var lateTimeValue = DateTime.Parse(dailyDeadlineInfo.LateTime);
                    var finalTime = DateTime.Parse(dailyDeadlineInfo.FinalTime);
                    string dailyReminderText;
                    
                    // 根据报告状态和SubmitStatus字段来决定提示文字和状态
                    if (dailyReport != null)
                    {
                        // 有报告数据
                        if (dailyReport.Status == (int)EnumReportStatus.Submitted)
                        {
                            // 已提交的报告，根据SubmitStatus显示
                            if (dailyReport.SubmitStatus.HasValue)
                            {
                                switch ((EnumSubmitStatus)dailyReport.SubmitStatus.Value)
                                {
                                    case EnumSubmitStatus.OnTime:
                                        dailyReminderText = $"本{typeName}已按时提交。";
                                        break;
                                    case EnumSubmitStatus.Late:
                                        if (dailyReport.SubmitTime.HasValue)
                                        {
                                            var lateTime = dailyReport.SubmitTime.Value - regularTime;
                                            dailyReminderText = $"本{typeName}已提交（迟交【{FormatTimeRemaining(lateTime)}】）。";
                                        }
                                        else
                                        {
                                            dailyReminderText = $"本{typeName}已提交（迟交）。";
                                        }
                                        break;
                                    case EnumSubmitStatus.NotSubmitted:
                                        // 超过最终截止时间提交的，但仍然算作已提交
                                        dailyReminderText = $"本{typeName}已提交（超过最终截止时间）。";
                                        break;
                                    default:
                                        dailyReminderText = $"本{typeName}已提交。";
                                        break;
                                }
                            }
                            else
                            {
                                dailyReminderText = $"本{typeName}已提交。";
                            }
                        }
                        else if (dailyReport.Status == (int)EnumReportStatus.Draft)
                        {
                            // 草稿状态，根据时效状态显示提醒
                            switch (dailyDeadlineInfo.CurrentStatus)
                            {
                                case EnumReportTimeStatus.BeforeRegularTime:
                                case EnumReportTimeStatus.BetweenRegularAndLateTime:
                                    dailyReminderText = $"请于【{lateTimeValue:MM月dd日 HH:mm}】前完成本{typeName}提交。";
                                    break;
                                case EnumReportTimeStatus.BetweenLateAndFinalTime:
                                    // 修正迟交时长计算逻辑，用当前时间减迟交时间点
                                    var lateTimeValue2 = DateTime.Parse(dailyDeadlineInfo.LateTime);
                                    var lateTime2 = DateTime.Now - lateTimeValue2;
                                    string lateText2 = lateTime2.TotalMinutes > 0 ? $"（已迟交【{FormatTimeRemaining(lateTime2)}】）" : "";
                                    dailyReminderText = $"本{typeName}已进入迟交阶段，请尽快于【{finalTime:MM月dd日 HH:mm}】前补交！{lateText2}";
                                    break;
                                case EnumReportTimeStatus.AfterFinalTime:
                                    dailyReminderText = $"本{typeName}已超过最终截止时间，无法再提交。";
                                    break;
                                default:
                                    dailyReminderText = $"请及时完成本{typeName}提交。";
                                    break;
                            }
                        }
                        else
                        {
                            // 其他状态
                            dailyReminderText = $"本{typeName}状态异常。";
                        }
                    }
                    else
                    {
                        // 没有报告数据，根据时效状态显示提醒
                        switch (dailyDeadlineInfo.CurrentStatus)
                        {
                            case EnumReportTimeStatus.BeforeRegularTime:
                            case EnumReportTimeStatus.BetweenRegularAndLateTime:
                                dailyReminderText = $"请于【{lateTimeValue:MM月dd日 HH:mm}】前完成本{typeName}提交。";
                                break;
                            case EnumReportTimeStatus.BetweenLateAndFinalTime:
                                // 修正迟交时长计算逻辑，用当前时间减迟交时间点
                                var lateTimeValue3 = DateTime.Parse(dailyDeadlineInfo.LateTime);
                                var lateTime3 = DateTime.Now - lateTimeValue3;
                                string lateText3 = lateTime3.TotalMinutes > 0 ? $"（已迟交【{FormatTimeRemaining(lateTime3)}】）" : "";
                                dailyReminderText = $"本{typeName}已进入迟交阶段，请尽快于【{finalTime:MM月dd日 HH:mm}】前补交！{lateText3}";
                                break;
                            case EnumReportTimeStatus.AfterFinalTime:
                                dailyReminderText = $"本{typeName}已超过最终截止时间，无法再提交。";
                                break;
                            default:
                                dailyReminderText = $"请及时完成本{typeName}提交。";
                                break;
                        }
                    }

                    // 根据SubmitStatus判断有效提交状态
                    bool isEffectivelySubmitted = false;
                    if (dailyReport != null)
                    {
                        // 如果报告状态是已提交，就认为有效提交
                        // 不管SubmitStatus是什么值，只要Status是Submitted就算已提交
                        isEffectivelySubmitted = dailyReport.Status == (int)EnumReportStatus.Submitted;
                    }

                    taskReminders.Add(new VM_TaskReminder
                    {
                        ReportType = EnumReportType.Daily,
                        ReportDateText = reportDateText,
                        ReportDate = actualReportDate, // 使用调整后的日期
                        StatusColor = isEffectivelySubmitted ? EnumStatusColor.Success : EnumStatusColor.Danger,
                        StatusDescription = isEffectivelySubmitted ? EnumHelper.GetEnumDescription<EnumReportStatus>(EnumReportStatus.Submitted) : EnumHelper.GetEnumDescription<EnumReportStatus>(EnumReportStatus.NotSubmitted),
                        ActionText = dailyReport == null 
                            ? EnumHelper.GetEnumDescription<ReportActionText>(ReportActionText.Write) + "日报"
                            : (dailyReport.Status == (int)EnumReportStatus.Draft 
                                ? EnumHelper.GetEnumDescription<ReportActionText>(ReportActionText.Edit) + "日报" 
                                : EnumHelper.GetEnumDescription<ReportActionText>(ReportActionText.View) + "日报"),
                        ActionType = dailyReport == null 
                            ? ReportActionText.Write 
                            : (dailyReport.Status == (int)EnumReportStatus.Draft 
                                ? ReportActionText.Edit 
                                : ReportActionText.View),
                        ReportId = dailyReport?.Id,
                        ReminderText = dailyReminderText,
                        TaskStatus = isEffectivelySubmitted ? EnumReportStatus.Submitted : EnumReportStatus.NotSubmitted
                    });
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"添加日报提醒失败：{ex.Message}", ex);
            }
        }

        #endregion

        #region 团队队长查询

        /// <summary>
        /// 获取当前团队的队长
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>团队队长列表</returns>
        private List<DAL.DbModel.Crm2.Db_sys_user> GetCurrentTeamLeaders(string userId)
        {
            try
            {
                var result = new List<DAL.DbModel.Crm2.Db_sys_user>();
                
                // 从Redis缓存获取当前用户信息
                var currentUser = RedisCache.UserWithOrg.GetUserById(userId);
                if (currentUser == null || string.IsNullOrEmpty(currentUser.OrganizationId))
                    return result;
                
                // 判断当前用户是否为队长（UserType=1表示管理者）
                bool isCurrentUserLeader = currentUser.UserType == (int)EnumUserType.Manager;
                
                // 获取当前用户所在组织的队长
                var allUsers = RedisCache.UserWithOrg.GetAllUsers();
                if (allUsers != null && allUsers.Count > 0)
                {
                    // 查询条件：
                    // 1. 必须是当前用户所在组织
                    // 2. 必须是队长（UserType=1）
                    // 3. 必须是有效用户（UserStatus=true）
                    // 4. 排除自己（避免自己选择自己作为接收人）
                    var teamLeaders = allUsers.Where(x => 
                        x.OrganizationId == currentUser.OrganizationId && 
                        x.UserType == (int)EnumUserType.Manager && 
                        x.UserStatus == true &&
                        x.Id != userId)
                        .Select(x => new DAL.DbModel.Crm2.Db_sys_user
                        {
                            Id = x.Id,
                            Name = x.Name,
                            OrganizationId = x.OrganizationId,
                            UserStatus = x.UserStatus
                        })
                        .ToList();
                    result.AddRange(teamLeaders);
                }
                
                // 逻辑说明：
                // - 如果当前用户是普通销售：会包含当前团队的队长（排除自己后）
                // - 如果当前用户是队长：不会包含自己，但会包含同团队的其他队长（如果有的话）
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取当前团队队长失败，用户ID：{userId}，错误：{ex.Message}");
                return new List<DAL.DbModel.Crm2.Db_sys_user>();
            }
        }

        #endregion

        // 新增：带区间判断的周报提醒
        private void AddWeeklyReportReminderWithTimeCheck(List<VM_TaskReminder> taskReminders, string userId, DateTime reportDate, string reportDateText, DateTime currentTime)
        {
            // 周报提醒逻辑：从报告日期当天开始显示，直到最终截止时间
            var reportDateStart = reportDate.Date; // 报告日期的开始时间（00:00）
            
            // 如果当前时间在报告日期当天或之后，就显示周报提醒
            if (currentTime >= reportDateStart)
            {
                // 先查询该日期的周报
                var weeklyReport = DbOpe_crm_report_base.Instance.GetData(x =>
                    x.UserId == userId &&
                    x.ReportType == (int)EnumReportType.Weekly &&
                    x.ReportDate == reportDate &&
                    x.Deleted != true);

                var weeklyDeadlineInfo = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                {
                    ReportType = EnumReportType.Weekly,
                    ReportDate = reportDate
                });
                
                var finalTime = DateTime.Parse(weeklyDeadlineInfo.FinalTime);
                
                // 只有在最终截止时间之前才显示提醒
                if (currentTime <= finalTime)
                {
                    string reminderText;
                    
                    // 如果报告已经提交，显示已提交状态
                    if (weeklyReport != null && weeklyReport.Status == (int)EnumReportStatus.Submitted)
                    {
                        reminderText = "本周报已提交。";
                    }
                    else
                    {
                        // 报告未提交，根据时效状态显示提醒
                        var regularTime = DateTime.Parse(weeklyDeadlineInfo.RegularTime);
                        var lateTimeValue = DateTime.Parse(weeklyDeadlineInfo.LateTime);
                        
                        switch (weeklyDeadlineInfo.CurrentStatus)
                        {
                            case EnumReportTimeStatus.BeforeRegularTime:
                            case EnumReportTimeStatus.BetweenRegularAndLateTime:
                                reminderText = $"请于【{lateTimeValue:MM月dd日 HH:mm}】前完成本周报提交。";
                                break;
                            case EnumReportTimeStatus.BetweenLateAndFinalTime:
                                // 修正迟交时长计算逻辑，用当前时间减迟交时间点
                                var lateTime2 = DateTime.Now - lateTimeValue;
                                string lateText2 = lateTime2.TotalMinutes > 0 ? $"（已迟交【{FormatTimeRemaining(lateTime2)}】）" : "";
                                reminderText = $"本周报已进入迟交阶段，请尽快于【{finalTime:MM月dd日 HH:mm}】前补交！{lateText2}";
                                break;
                            case EnumReportTimeStatus.AfterFinalTime:
                                reminderText = $"本周报已超过最终截止时间，无法再提交。";
                                break;
                            default:
                                reminderText = $"请及时完成本周报提交。";
                                break;
                        }
                    }
                    
                    AddWeeklyReportReminder(taskReminders, userId, reportDate, reportDateText, reminderText);
                }
            }
        }
        // 新增：带区间判断的月报提醒
        private void AddMonthlyReportReminderWithTimeCheck(List<VM_TaskReminder> taskReminders, string userId, DateTime reportDate, string reportDateText, DateTime currentTime)
        {
            // 月报提醒逻辑：从报告日期当天开始显示，直到最终截止时间
            var reportDateStart = reportDate.Date; // 报告日期的开始时间（00:00）
            
            // 如果当前时间在报告日期当天或之后，就显示月报提醒
            if (currentTime >= reportDateStart)
            {
                // 先查询该日期的月报
                var monthlyReport = DbOpe_crm_report_base.Instance.GetData(x =>
                    x.UserId == userId &&
                    x.ReportType == (int)EnumReportType.Monthly &&
                    x.ReportDate == reportDate &&
                    x.Deleted != true);

                var monthlyDeadlineInfo = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                {
                    ReportType = EnumReportType.Monthly,
                    ReportDate = reportDate
                });
                
                var finalTime = DateTime.Parse(monthlyDeadlineInfo.FinalTime);
                
                // 只有在最终截止时间之前才显示提醒
                if (currentTime <= finalTime)
                {
                    string reminderText;
                    
                    // 如果报告已经提交，显示已提交状态
                    if (monthlyReport != null && monthlyReport.Status == (int)EnumReportStatus.Submitted)
                    {
                        reminderText = "本月报已提交。";
                    }
                    else
                    {
                        // 报告未提交，根据时效状态显示提醒
                        var regularTime = DateTime.Parse(monthlyDeadlineInfo.RegularTime);
                        var lateTimeValue = DateTime.Parse(monthlyDeadlineInfo.LateTime);
                        
                        switch (monthlyDeadlineInfo.CurrentStatus)
                        {
                            case EnumReportTimeStatus.BeforeRegularTime:
                            case EnumReportTimeStatus.BetweenRegularAndLateTime:
                                reminderText = $"请于【{lateTimeValue:MM月dd日 HH:mm}】前完成本月报提交。";
                                break;
                            case EnumReportTimeStatus.BetweenLateAndFinalTime:
                                // 修正迟交时长计算逻辑，用当前时间减迟交时间点
                                var lateTime2 = DateTime.Now - lateTimeValue;
                                string lateText2 = lateTime2.TotalMinutes > 0 ? $"（已迟交【{FormatTimeRemaining(lateTime2)}】）" : "";
                                reminderText = $"本月报已进入迟交阶段，请尽快于【{finalTime:MM月dd日 HH:mm}】前补交！{lateText2}";
                                break;
                            case EnumReportTimeStatus.AfterFinalTime:
                                reminderText = $"本月报已超过最终截止时间，无法再提交。";
                                break;
                            default:
                                reminderText = $"请及时完成本月报提交。";
                                break;
                        }
                    }
                    
                    AddMonthlyReportReminder(taskReminders, userId, reportDate, reportDateText, reminderText);
                }
            }
        }
        // 重载：带自定义提醒文字的周报提醒
        private void AddWeeklyReportReminder(List<VM_TaskReminder> taskReminders, string userId, DateTime reportDate, string reportDateText, string reminderText)
        {
            // ...原AddWeeklyReportReminder逻辑，最后把reminderText赋值给ReminderText...
            var weeklyReport = DbOpe_crm_report_base.Instance.GetData(x =>
                x.UserId == userId &&
                x.ReportType == (int)EnumReportType.Weekly &&
                x.ReportDate == reportDate &&
                x.Deleted != true);
            var status = weeklyReport == null ? EnumReportStatus.NotSubmitted : (EnumReportStatus)weeklyReport.Status;
            taskReminders.Add(new VM_TaskReminder
            {
                ReportType = EnumReportType.Weekly,
                ReportDateText = reportDateText + $"（{reportDate:MM月dd日}）",
                ReportDate = reportDate,
                StatusColor = weeklyReport == null ? EnumStatusColor.Danger : (status == EnumReportStatus.Submitted ? EnumStatusColor.Success : EnumStatusColor.Warning),
                StatusDescription = weeklyReport == null ? EnumHelper.GetEnumDescription<EnumReportStatus>(EnumReportStatus.NotSubmitted) : EnumHelper.GetEnumDescription<EnumReportStatus>(status),
                ActionText = weeklyReport == null
                    ? EnumHelper.GetEnumDescription<ReportActionText>(ReportActionText.Write) + "周报"
                    : (status == EnumReportStatus.Draft
                        ? EnumHelper.GetEnumDescription<ReportActionText>(ReportActionText.Edit) + "周报"
                        : EnumHelper.GetEnumDescription<ReportActionText>(ReportActionText.View) + "周报"),
                ActionType = weeklyReport == null
                    ? ReportActionText.Write
                    : (status == EnumReportStatus.Draft
                        ? ReportActionText.Edit
                        : ReportActionText.View),
                ReportId = weeklyReport?.Id,
                ReminderText = reminderText,
                TaskStatus = status
            });
        }
        // 重载：带自定义提醒文字的月报提醒
        private void AddMonthlyReportReminder(List<VM_TaskReminder> taskReminders, string userId, DateTime reportDate, string reportDateText, string reminderText)
        {
            var monthlyReport = DbOpe_crm_report_base.Instance.GetData(x =>
                x.UserId == userId &&
                x.ReportType == (int)EnumReportType.Monthly &&
                x.ReportDate == reportDate &&
                x.Deleted != true);
            var status = monthlyReport == null ? EnumReportStatus.NotSubmitted : (EnumReportStatus)monthlyReport.Status;
            taskReminders.Add(new VM_TaskReminder
            {
                ReportType = EnumReportType.Monthly,
                ReportDateText = reportDateText + $"（{reportDate:MM月dd日}）",
                ReportDate = reportDate,
                StatusColor = monthlyReport == null ? EnumStatusColor.Danger : (status == EnumReportStatus.Submitted ? EnumStatusColor.Success : EnumStatusColor.Warning),
                StatusDescription = monthlyReport == null ? EnumHelper.GetEnumDescription<EnumReportStatus>(EnumReportStatus.NotSubmitted) : EnumHelper.GetEnumDescription<EnumReportStatus>(status),
                ActionText = monthlyReport == null
                    ? EnumHelper.GetEnumDescription<ReportActionText>(ReportActionText.Write) + "月报"
                    : (status == EnumReportStatus.Draft
                        ? EnumHelper.GetEnumDescription<ReportActionText>(ReportActionText.Edit) + "月报"
                        : EnumHelper.GetEnumDescription<ReportActionText>(ReportActionText.View) + "月报"),
                ActionType = monthlyReport == null
                    ? ReportActionText.Write
                    : (status == EnumReportStatus.Draft
                        ? ReportActionText.Edit
                        : ReportActionText.View),
                ReportId = monthlyReport?.Id,
                ReminderText = reminderText,
                TaskStatus = status
            });
        }
    }

    /// <summary>
    /// 用户团队信息
    /// </summary>
    public class VM_UserTeamInfo
    {
        public string TeamId { get; set; }
        public string TeamName { get; set; }
        public bool IsLeader { get; set; }
    }
}
