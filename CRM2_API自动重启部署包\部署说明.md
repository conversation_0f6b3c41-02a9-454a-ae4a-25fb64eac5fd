# CRM2 API 服务自动重启方案部署说明

本文档提供了如何使用systemd配置CRM2 API服务自动重启的完整指南，并包含了健康检查和监控服务的配置方法。

## 1. 文件说明

本方案包含以下文件：

- **crm-api.service**: 主服务配置文件，用于管理CRM2 API服务
- **install-crm-service.sh**: 安装主服务的脚本
- **monitor-crm-service.sh**: 健康监控脚本，定期检查API服务健康状态
- **crm-api-monitor.service**: 监控服务配置文件
- **健康检查API**: 在HomeController中添加的健康检查端点（Home/HealthCheck）

## 2. 部署步骤

### 2.1 准备文件

将所有配置文件放在同一个目录下，并设置执行权限：

```bash
chmod +x install-crm-service.sh
chmod +x monitor-crm-service.sh
```

### 2.2 部署主服务

运行安装脚本安装主服务：

```bash
sudo ./install-crm-service.sh
```

这将执行以下操作：
- 复制服务文件到systemd目录
- 创建日志目录
- 启用并启动服务

### 2.3 部署监控服务（可选但推荐）

监控服务提供额外的保障，定期检查API的健康状态：

```bash
# 复制监控脚本到系统目录
sudo cp monitor-crm-service.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/monitor-crm-service.sh

# 安装监控服务
sudo cp crm-api-monitor.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable crm-api-monitor.service
sudo systemctl start crm-api-monitor.service
```

## 3. 服务管理命令

### 3.1 主服务管理

```bash
# 启动服务
sudo systemctl start crm-api.service

# 停止服务
sudo systemctl stop crm-api.service

# 重启服务
sudo systemctl restart crm-api.service

# 查看服务状态
sudo systemctl status crm-api.service

# 查看服务日志
sudo journalctl -u crm-api.service -f
```

### 3.2 监控服务管理

```bash
# 启动监控服务
sudo systemctl start crm-api-monitor.service

# 停止监控服务
sudo systemctl stop crm-api-monitor.service

# 查看监控服务状态
sudo systemctl status crm-api-monitor.service

# 查看监控日志
cat /hqhs_data/00.API/01.CRM/log/monitor.log
```

## 4. 健康检查API

系统已配置了一个健康检查API端点：

```
GET /Home/HealthCheck
```

该端点返回以下JSON信息：

```json
{
  "status": "healthy",
  "timestamp": "2023-04-16T12:34:56.789Z",
  "version": "2.0"
}
```

## 5. 故障排查

如果服务无法启动或自动重启不正常，请检查：

1. 服务日志：
   ```bash
   sudo journalctl -u crm-api.service -f
   ```

2. 应用程序日志：
   ```bash
   cat /hqhs_data/00.API/01.CRM/log/log
   ```

3. 监控日志：
   ```bash
   cat /hqhs_data/00.API/01.CRM/log/monitor.log
   ```

4. 服务状态：
   ```bash
   sudo systemctl status crm-api.service
   sudo systemctl status crm-api-monitor.service
   ```

## 6. 自定义配置

如需调整配置，可以修改以下参数：

- **crm-api.service**:
  - `RestartSec`: 服务重启前等待的秒数
  - `StandardOutput`/`StandardError`: 日志输出位置

- **monitor-crm-service.sh**:
  - `CHECK_INTERVAL`: 健康检查间隔（秒）
  - `FAILURE_THRESHOLD`: 连续失败阈值
  - `PORT`: API服务端口

更改配置后，需要重新加载systemd:
```bash
sudo systemctl daemon-reload
``` 