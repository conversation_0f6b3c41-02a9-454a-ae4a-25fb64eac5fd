using Spire.Doc;
using Spire.Doc.Documents;
using System;
using System.Text.RegularExpressions;

namespace CRM2_API.Common.Utils
{
    /// <summary>
    /// Unicode文本处理器 - 解决PDF转换中特殊字符显示问题
    /// 支持土耳其语、阿拉伯语、俄语、中文等Unicode字符
    /// </summary>
    public static class UnicodeTextProcessor
    {
        /// <summary>
        /// 处理文档中的Unicode字符，确保在PDF转换中正确显示
        /// </summary>
        /// <param name="document">Spire.Doc文档对象</param>
        public static void ProcessDocumentForUnicodeText(Document document)
        {
            try
            {
                // 遍历所有节
                foreach (Section section in document.Sections)
                {
                    ProcessSectionForUnicodeText(section);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，避免影响正常流程
                LogUtil.AddErrorLog("UnicodeTextProcessor处理异常: " + ex.Message, "UnicodeTextProcessor");
            }
        }

        /// <summary>
        /// 处理节中的Unicode字符
        /// </summary>
        /// <param name="section">文档节</param>
        private static void ProcessSectionForUnicodeText(Section section)
        {
            // 遍历节中的所有段落
            foreach (Paragraph paragraph in section.Paragraphs)
            {
                ProcessParagraphForUnicodeText(paragraph);
            }

            // 处理表格
            foreach (Table table in section.Tables)
            {
                ProcessTableForUnicodeText(table);
            }

            // 处理页眉页脚 - 使用更兼容的方式
            try
            {
                var headerFooter = section.HeadersFooters;
                ProcessHeaderFootersForUnicodeText(headerFooter);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理页眉页脚异常: {ex.Message}", "UnicodeTextProcessor");
            }
        }

        /// <summary>
        /// 处理段落中的Unicode字符
        /// </summary>
        /// <param name="paragraph">段落对象</param>
        private static void ProcessParagraphForUnicodeText(Paragraph paragraph)
        {
            try
            {
                for (int i = 0; i < paragraph.ChildObjects.Count; i++)
                {
                    var childObject = paragraph.ChildObjects[i];
                    
                    // 使用动态类型检查，避免类型引用问题
                    if (childObject != null && childObject.GetType().Name == "TextRange")
                    {
                        ProcessTextRangeForUnicodeText(childObject);
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理段落异常: {ex.Message}", "UnicodeTextProcessor");
            }
        }

        /// <summary>
        /// 处理表格中的Unicode字符
        /// </summary>
        /// <param name="table">表格对象</param>
        private static void ProcessTableForUnicodeText(Table table)
        {
            try
            {
                foreach (TableRow row in table.Rows)
                {
                    foreach (TableCell cell in row.Cells)
                    {
                        foreach (Paragraph paragraph in cell.Paragraphs)
                        {
                            ProcessParagraphForUnicodeText(paragraph);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理表格异常: {ex.Message}", "UnicodeTextProcessor");
            }
        }

        /// <summary>
        /// 处理页眉页脚中的Unicode字符
        /// </summary>
        /// <param name="headersFooters">页眉页脚集合</param>
        private static void ProcessHeaderFootersForUnicodeText(object headersFooters)
        {
            try
            {
                // 使用反射方式处理，避免类型定义问题
                var enumerable = headersFooters as System.Collections.IEnumerable;
                if (enumerable != null)
                {
                    foreach (var headerFooter in enumerable)
                    {
                        // 获取Paragraphs属性
                        var paragraphsProperty = headerFooter.GetType().GetProperty("Paragraphs");
                        if (paragraphsProperty != null)
                        {
                            var paragraphs = paragraphsProperty.GetValue(headerFooter) as System.Collections.IEnumerable;
                            if (paragraphs != null)
                            {
                                foreach (Paragraph paragraph in paragraphs)
                                {
                                    ProcessParagraphForUnicodeText(paragraph);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理页眉页脚异常: {ex.Message}", "UnicodeTextProcessor");
            }
        }

        /// <summary>
        /// 处理文本区域中的Unicode字符
        /// </summary>
        /// <param name="textRangeObj">文本区域对象</param>
        private static void ProcessTextRangeForUnicodeText(object textRangeObj)
        {
            try
            {
                // 使用反射获取Text属性
                var textProperty = textRangeObj.GetType().GetProperty("Text");
                if (textProperty != null)
                {
                    string originalText = textProperty.GetValue(textRangeObj) as string;
                    
                    // 检查是否包含需要特殊处理的Unicode特殊字符
                    if (!string.IsNullOrEmpty(originalText) && ContainsUnicodeCharacters(originalText))
                    {
                        // 设置支持Unicode的字体
                        SetUnicodeSupportFont(textRangeObj, originalText);
                        
                        // 记录处理的文本（用于调试）
                        LogUtil.AddLog($"处理Unicode文本: {originalText}", "UnicodeTextProcessor");
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理文本区域异常: {ex.Message}", "UnicodeTextProcessor");
            }
        }

        /// <summary>
        /// 检查文本是否包含需要特殊处理的Unicode字符
        /// </summary>
        /// <param name="text">要检查的文本</param>
        /// <returns>是否包含需要特殊处理的Unicode字符</returns>
        private static bool ContainsUnicodeCharacters(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;
                
            // 如果文本只包含数字、小数点、逗号和空格，则不需要特殊处理
            if (Regex.IsMatch(text, @"^[\d\s\.,]+$"))
                return false;
                
            // 如果文本只包含ASCII字符，则不需要特殊处理
            if (Regex.IsMatch(text, @"^[\x00-\x7F]+$"))
                return false;

            // 检查土耳其语特殊字符
            if (Regex.IsMatch(text, @"[İıĞğÜüŞşÖöÇç]"))
                return true;

            // 检查阿拉伯语字符
            if (Regex.IsMatch(text, @"[\u0600-\u06FF\u0750-\u077F]"))
                return true;

            // 检查俄语字符（西里尔字母）
            if (Regex.IsMatch(text, @"[\u0400-\u04FF]"))
                return true;

            // 检查其他Unicode扩展字符（但排除中文，因为中文通常不需要特殊处理）
            if (Regex.IsMatch(text, @"[\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF]"))
                return true;

            return false;
        }

        /// <summary>
        /// 为文本区域设置支持Unicode的字体
        /// </summary>
        /// <param name="textRangeObj">文本区域对象</param>
        /// <param name="text">文本内容</param>
        private static void SetUnicodeSupportFont(object textRangeObj, string text)
        {
            try
            {
                // 使用反射获取CharacterFormat属性
                var characterFormatProperty = textRangeObj.GetType().GetProperty("CharacterFormat");
                if (characterFormatProperty != null)
                {
                    var characterFormat = characterFormatProperty.GetValue(textRangeObj);
                    if (characterFormat != null)
                    {
                        // 获取字体属性
                        var fontNameProperty = characterFormat.GetType().GetProperty("FontName");
                        var fontSizeProperty = characterFormat.GetType().GetProperty("FontSize");
                        var fontNameBidiProperty = characterFormat.GetType().GetProperty("FontNameBidi");
                        var bidiProperty = characterFormat.GetType().GetProperty("Bidi");
                        
                        string originalFontName = "";
                        float originalFontSize = 12f;
                        
                        if (fontNameProperty != null)
                        {
                            originalFontName = fontNameProperty.GetValue(characterFormat) as string ?? "";
                        }
                        
                        if (fontSizeProperty != null)
                        {
                            var sizeValue = fontSizeProperty.GetValue(characterFormat);
                            if (sizeValue != null)
                            {
                                originalFontSize = Convert.ToSingle(sizeValue);
                            }
                        }

                        // 检查是否为阿拉伯语文本
                        bool isArabicText = !string.IsNullOrEmpty(text) && 
                                          Regex.IsMatch(text, @"[\u0600-\u06FF\u0750-\u077F]");

                        // 检查是否为混合语言文本（包含阿拉伯语和其他文字）
                        bool isMixedLanguageText = isArabicText && HasNonArabicText(text);

                        // 只有在需要特殊处理的情况下才设置支持Unicode的字体
                        if (isArabicText || Regex.IsMatch(text, @"[İıĞğÜüŞşÖöÇç]") || 
                            Regex.IsMatch(text, @"[\u0400-\u04FF]") || 
                            Regex.IsMatch(text, @"[\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF]"))
                        {
                            if (fontNameProperty != null)
                            {
                                fontNameProperty.SetValue(characterFormat, "DejaVu Sans");
                            }
                        }

                        // 为阿拉伯语文本设置RTL专用字体和方向
                        if (isArabicText)
                        {
                            // 设置阿拉伯语专用字体
                            if (fontNameBidiProperty != null)
                            {
                                fontNameBidiProperty.SetValue(characterFormat, "DejaVu Sans");
                            }
                            
                            // 只在纯阿拉伯语文本时启用完整的RTL处理
                            // 在混合语言文档中，只启用字符级的Bidi处理，保持整体排版美观
                            if (bidiProperty != null)
                            {
                                if (isMixedLanguageText)
                                {
                                    // 混合语言：只启用字符级的双向文本处理，不影响整体排版
                                    bidiProperty.SetValue(characterFormat, true);
                                    LogUtil.AddLog($"设置阿拉伯语字符级Bidi属性（混合语言）: {text}", "UnicodeTextProcessor");
                                }
                                else
                                {
                                    // 纯阿拉伯语：启用完整的RTL处理
                                    bidiProperty.SetValue(characterFormat, true);
                                    LogUtil.AddLog($"设置阿拉伯语完整RTL属性（纯阿拉伯语）: {text}", "UnicodeTextProcessor");
                                }
                            }
                        }
                        
                        // 如果原始字体大小合理，保持不变
                        if (fontSizeProperty != null && originalFontSize > 0 && originalFontSize < 100)
                        {
                            fontSizeProperty.SetValue(characterFormat, originalFontSize);
                        }

                        // 只在纯阿拉伯语文本时设置段落级别的RTL属性
                        if (isArabicText && !isMixedLanguageText)
                        {
                            SetParagraphBidiDirection(textRangeObj);
                        }

                        string logInfo = $"字体设置: {originalFontName} -> DejaVu Sans, 大小: {originalFontSize}";
                        if (isArabicText)
                        {
                            logInfo += isMixedLanguageText ? ", 字符级Bidi: 已启用" : ", 完整RTL: 已启用";
                        }
                        LogUtil.AddLog(logInfo, "UnicodeTextProcessor");
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"设置Unicode字体失败: {ex.Message}", "UnicodeTextProcessor");
                
                // 尝试备选字体
                TrySetAlternativeFont(textRangeObj, "Arial Unicode MS");
            }
        }

        /// <summary>
        /// 检查文本是否包含非阿拉伯语字符（用于判断是否为混合语言文本）
        /// </summary>
        /// <param name="text">要检查的文本</param>
        /// <returns>是否包含非阿拉伯语字符</returns>
        private static bool HasNonArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // 检查是否包含中文字符
            if (Regex.IsMatch(text, @"[\u4e00-\u9fff]"))
                return true;

            // 检查是否包含拉丁字母
            if (Regex.IsMatch(text, @"[a-zA-Z]"))
                return true;

            // 检查是否包含数字
            if (Regex.IsMatch(text, @"[0-9]"))
                return true;

            // 检查是否包含常见标点符号
            if (Regex.IsMatch(text, @"[，。、；：""''（）【】《》〈〉「」『』\s]"))
                return true;

            return false;
        }

        /// <summary>
        /// 为阿拉伯语文本设置段落级别的RTL方向
        /// </summary>
        /// <param name="textRangeObj">文本区域对象</param>
        private static void SetParagraphBidiDirection(object textRangeObj)
        {
            try
            {
                // 获取TextRange的Owner属性（通常是Paragraph）
                var ownerProperty = textRangeObj.GetType().GetProperty("Owner");
                if (ownerProperty != null)
                {
                    var paragraph = ownerProperty.GetValue(textRangeObj);
                    if (paragraph != null)
                    {
                        // 获取段落的Format属性
                        var formatProperty = paragraph.GetType().GetProperty("Format");
                        if (formatProperty != null)
                        {
                            var paragraphFormat = formatProperty.GetValue(paragraph);
                            if (paragraphFormat != null)
                            {
                                // 设置段落的IsBidi属性
                                var isBidiProperty = paragraphFormat.GetType().GetProperty("IsBidi");
                                if (isBidiProperty != null)
                                {
                                    isBidiProperty.SetValue(paragraphFormat, true);
                                    LogUtil.AddLog("已设置段落RTL方向", "UnicodeTextProcessor");
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"设置段落RTL方向失败: {ex.Message}", "UnicodeTextProcessor");
            }
        }

        /// <summary>
        /// 尝试设置备选字体
        /// </summary>
        /// <param name="textRangeObj">文本区域对象</param>
        /// <param name="fontName">字体名称</param>
        private static void TrySetAlternativeFont(object textRangeObj, string fontName)
        {
            try
            {
                var characterFormatProperty = textRangeObj.GetType().GetProperty("CharacterFormat");
                if (characterFormatProperty != null)
                {
                    var characterFormat = characterFormatProperty.GetValue(textRangeObj);
                    if (characterFormat != null)
                    {
                        var fontNameProperty = characterFormat.GetType().GetProperty("FontName");
                        if (fontNameProperty != null)
                        {
                            fontNameProperty.SetValue(characterFormat, fontName);
                        }
                    }
                }
            }
            catch
            {
                // 如果设置失败，尝试下一个备选字体
                if (fontName == "Arial Unicode MS")
                {
                    TrySetAlternativeFont(textRangeObj, "Times New Roman");
                }
                else if (fontName == "Times New Roman")
                {
                    TrySetAlternativeFont(textRangeObj, "Arial");
                }
            }
        }

        /// <summary>
        /// 获取文本中的Unicode字符统计信息
        /// </summary>
        /// <param name="text">要分析的文本</param>
        /// <returns>Unicode字符统计信息</returns>
        public static string GetUnicodeCharacterInfo(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "无文本内容";

            var info = new System.Text.StringBuilder();
            info.AppendLine($"文本内容: {text}");
            info.AppendLine($"字符长度: {text.Length}");
            info.AppendLine($"字节长度: {System.Text.Encoding.UTF8.GetByteCount(text)}");

            // 分析每个字符
            for (int i = 0; i < text.Length; i++)
            {
                char c = text[i];
                int unicode = (int)c;
                
                if (unicode > 127) // 非ASCII字符
                {
                    info.AppendLine($"字符: '{c}' Unicode: U+{unicode:X4} ({unicode})");
                }
            }

            return info.ToString();
        }
    }
} 