using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.PreInspection;
using CRM2_API.Model.System;
using CRM2_API.Services.PreInspection;
using SqlSugar;

namespace CRM2_API.BLL.Customer
{
    /// <summary>
    /// 客户预验业务逻辑类
    /// </summary>
    public class CustomerPreInspectionBLL : BaseBLL<CustomerPreInspectionBLL>
    {
        private readonly OptimizedPreInspectionService _optimizedPreInspectionService;
        private readonly CompanyNamePreprocessService _companyNamePreprocessService;
        private readonly SqlSugarScope _db;

        /// <summary>
        /// 构造函数
        /// </summary>
        public CustomerPreInspectionBLL()
        {
            _db = DbContext.Crm2Db;
            _optimizedPreInspectionService = new OptimizedPreInspectionService(null);
            _companyNamePreprocessService = new CompanyNamePreprocessService(null);
        }

        /// <summary>
        /// 执行客户预验
        /// </summary>
        /// <param name="input">用户输入</param>
        /// <param name="userId">用户ID</param>
        /// <param name="msg">返回消息</param>
        /// <param name="keyWords">返回关键词列表</param>
        /// <returns>预验结果</returns>
        public List<PreInspectionCustomer> PreInspectionCustomer(string input, string userId, ref string msg, ref List<string> keyWords)
        {
            try
            {
                // 使用优化后的预验服务
                return _optimizedPreInspectionService.PreInspectionCustomer(input, userId, ref msg, ref keyWords);
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException(ex.Message);
            }
        }

        /// <summary>
        /// 预处理所有公司名称
        /// </summary>
        /// <param name="batchSize">批处理大小</param>
        /// <returns>处理的公司数量</returns>
        public int PreprocessAllCompanyNames(int batchSize = 100)
        {
            try
            {
                return _companyNamePreprocessService.PreprocessAllCompanyNames(batchSize);
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"预处理公司名称失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 预处理单个公司名称
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="companyName">公司名称</param>
        /// <returns>是否成功</returns>
        public bool PreprocessCompanyName(string companyId, string companyName)
        {
            try
            {
                return _companyNamePreprocessService.PreprocessCompanyName(companyId, companyName);
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"预处理公司名称失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 预处理子公司名称
        /// </summary>
        /// <param name="customerId">客户ID</param>
        /// <returns>是否成功</returns>
        public bool PreprocessSubCompanyNames(string customerId)
        {
            try
            {
                return _companyNamePreprocessService.PreprocessSubCompanyNames(customerId);
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"预处理子公司名称失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 预处理关联公司名称
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <returns>是否成功</returns>
        public bool PreprocessRelatedCompanyNames(string companyId)
        {
            try
            {
                return _companyNamePreprocessService.PreprocessRelatedCompanyNames(companyId);
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"预处理关联公司名称失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 完整预处理公司及其关联信息
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="companyName">公司名称</param>
        /// <param name="customerId">客户ID</param>
        /// <returns>是否成功</returns>
        public bool PreprocessCompanyComplete(string companyId, string companyName, string customerId)
        {
            try
            {
                // 预处理主公司名称
                bool mainResult = PreprocessCompanyName(companyId, companyName);
                
                // 预处理子公司名称
                bool subResult = PreprocessSubCompanyNames(customerId);
                
                // 预处理关联公司名称
                bool relatedResult = PreprocessRelatedCompanyNames(companyId);
                
                return mainResult && subResult && relatedResult;
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"完整预处理公司及其关联信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除预验缓存
        /// </summary>
        public void ClearPreInspectionCache()
        {
            try
            {
                _optimizedPreInspectionService.ClearCache();
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"清除预验缓存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除预处理数据
        /// </summary>
        /// <param name="companyId">公司ID</param>
        public void DeletePreprocessedData(string companyId)
        {
            try
            {
                _companyNamePreprocessService.DeletePreprocessedData(companyId);
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"删除预处理数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量删除预处理数据
        /// </summary>
        /// <param name="companyIds">公司ID列表</param>
        public void BatchDeletePreprocessedData(List<string> companyIds)
        {
            try
            {
                _companyNamePreprocessService.BatchDeletePreprocessedData(companyIds);
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"批量删除预处理数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取预验统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public PreInspectionStats GetPreInspectionStats()
        {
            try
            {
                return _optimizedPreInspectionService.GetPreInspectionStats();
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"获取预验统计信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除过期的预验证缓存
        /// </summary>
        public void RemoveExpiredPreInspectionCache()
        {
            PreInspectionCache.RemoveExpiredCache();
        }

        /// <summary>
        /// 删除公司预处理数据
        /// </summary>
        /// <param name="companyId">公司ID</param>
        public void DeleteCompanyPreprocessData(string companyId)
        {
            BatchDeletePreprocessedData(new List<string> { companyId });
        }

        /// <summary>
        /// 批量删除公司预处理数据
        /// </summary>
        /// <param name="companyIds">公司ID列表</param>
        public void BatchDeleteCompanyPreprocessData(List<string> companyIds)
        {
            BatchDeletePreprocessedData(companyIds);
        }

        /// <summary>
        /// 获取并修复未经过预处理的公司数据
        /// </summary>
        /// <param name="batchSize">批处理大小</param>
        /// <returns>修复的公司数量</returns>
        public int GetAndFixUnprocessedCompanies(int batchSize = 100)
        {
            try
            {
                return _companyNamePreprocessService.GetAndFixUnprocessedCompanies(batchSize);
            }
            catch (Exception ex)
            {
                throw new CRM2_API.Model.System.ApiException($"获取并修复未预处理的公司数据失败: {ex.Message}");
            }
        }
    }
} 