using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.Enum;
using CRM2_API.Model.System;
using NPOI.SS.Formula.Functions;
using NStandard;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 测试数据 BLL 类 - 发票申请相关测试数据
    /// </summary>
    public partial class BLL_TestData
    {

        public void TestImprovedTran()
        {
            try
            {
                Console.WriteLine("开始测试改进后的嵌套事务...");
                
                // 记录初始数据状态
                var initialTip = DbOpe_crm_article_articleintips.Instance.GetDataAllList().First();
                var initialArticle = DbOpe_crm_article.Instance.GetData(d => d.Id == initialTip.ArticleId);
                
                string originalTipUpdateUser = initialTip.UpdateUser;
                string originalArticleUpdateUser = initialArticle.UpdateUser;
                
                Console.WriteLine($"初始状态 - Tip UpdateUser: {originalTipUpdateUser}, Article UpdateUser: {originalArticleUpdateUser}");
                
                // 执行嵌套事务测试
                try
                {
                    DbOpe_crm_article.Instance.TransDeal(() =>
                    {
                        // 1. 内部事务
                        DbOpe_crm_article_articleintips.Instance.TransDeal(() =>
                        {
                            var tip = DbOpe_crm_article_articleintips.Instance.GetDataAllList().First();
                            tip.UpdateUser = "INNER_TRAN_USER";
                            DbOpe_crm_article_articleintips.Instance.Update(tip);
                            Console.WriteLine("内部事务执行完成，已更新Tip记录");
                        });
                        
                        // 2. 外部事务继续执行
                        var article = DbOpe_crm_article.Instance.GetData(d => d.Id == initialArticle.Id);
                        article.UpdateUser = "OUTER_TRAN_USER";
                        DbOpe_crm_article.Instance.Update(article);
                        Console.WriteLine("外部事务继续执行，已更新Article记录");
                        
                        // 3. 抛出异常，测试事务回滚
                        throw new Exception("测试外部事务异常");
                    });
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"事务执行异常: {ex.Message}");
                }
                
                // 检查事务后的数据状态
                var finalTip = DbOpe_crm_article_articleintips.Instance.GetDataAllList().First();
                var finalArticle = DbOpe_crm_article.Instance.GetData(d => d.Id == initialArticle.Id);
                
                Console.WriteLine($"最终状态 - Tip UpdateUser: {finalTip.UpdateUser}, Article UpdateUser: {finalArticle.UpdateUser}");
                
                // 验证事务回滚情况
                if (finalTip.UpdateUser == originalTipUpdateUser && finalArticle.UpdateUser == originalArticleUpdateUser)
                {
                    Console.WriteLine("测试成功：嵌套事务回滚正常，所有更新已回滚");
                }
                else
                {
                    Console.WriteLine("测试失败：事务回滚异常！");
                    if (finalTip.UpdateUser != originalTipUpdateUser)
                        Console.WriteLine($"- 内部事务更新未回滚: {finalTip.UpdateUser}");
                    if (finalArticle.UpdateUser != originalArticleUpdateUser)
                        Console.WriteLine($"- 外部事务更新未回滚: {finalArticle.UpdateUser}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试执行异常: {ex.Message}");
            }
        }

        public GetReceiptRegisterResult GetUnInvoicedReceiptId(string receiptRegisterId = null){

            return DbContext.Crm2Db.Queryable<Db_crm_contract_receiptregister>()
            .LeftJoin<Db_crm_contract>((r,c)=>r.ContractId==c.Id)
            .LeftJoin<Db_crm_invoice_receipt_matching>((r,c,m)=>m.ReceiptId==r.Id)  
            .Where((r,c,m)=>
            r.AchievementState==(int)EnumAchievementState.Confirmed 
            && c.ContractName.StartsWith("测试客户")
             && (m.Id==null))
            .WhereIF(!string.IsNullOrEmpty(receiptRegisterId), (r,c,m)=>r.Id==receiptRegisterId)
            .Select((r,c)=>new GetReceiptRegisterResult{
                Id= r.Id,
                ContractId = c.Id
            })
            .First();
        }
        /// <summary>
        /// 准备发票申请测试数据
        /// </summary>
        /// <returns>发票申请结果</returns>
        public CompleteTestDataResult PrepareInvoiceApplication(string receiptRegisterId = null)
        {
            try
            {
                // 1. 查询所有已识别的到账记录（筛选测试数据）
                var receiptRegisterResult = GetUnInvoicedReceiptId(receiptRegisterId);
                if(receiptRegisterResult==null){
                    throw new ApiException("未找到到账未开票的测试数据");
                }
                
                var receiptDetails  = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterById(receiptRegisterResult.Id);

                string contractId = receiptDetails.ContractId;
                string customerName = receiptDetails.ContractReceiptDetails.First()?.PaymentCompanyName??"";

                decimal arrivalAmount = receiptDetails.ContractReceiptDetails.First()?.ArrivalAmount ?? 10000;

                var billingCompany = receiptDetails.ContractReceiptDetails.First()?.CollectingCompany ?? "";

                var contractInfo = DbOpe_crm_contract.Instance.GetData(c=>c.Id==contractId);

                string contractName = contractInfo.ContractName;
                // 7. 创建发票申请请求
                var applicationRequest = new CreateInvoiceApplicationRequest
                {
                    ContractId = contractId,
                    ReceiptId = receiptDetails.Id,
                    BillingType = EnumBillingType.InvoicingUponReceipt, // 到账开票
                    InvoiceInfo = new InvoiceBaseInfo
                    {
                        InvoiceType = EnumInvoiceType.SpecialTicket, // 增值税专用发票
                        InvoiceForm = EnumInvoicingForm.ElectronicInvoice, // 纸质发票
                        InvoiceAmount = arrivalAmount, // 使用到账金额
                        InvoicingDetails = "测试产品服务费"
                    },
                    ReceiveCompanyInfo = new ReceiveCompanyInfo
                    {
                        CompanyName = customerName,
                        CreditCode = "",
                        Recipient = "张测试",
                        Email = "<EMAIL>"
                    },
                    ExpectedInvoiceDate = DateTime.Now.AddDays(7),
                    ApplicationReason = "测试发票申请",
                    InvoiceDetails = "产品服务费",
                    BillingCompany = billingCompany
                };

                // 8. 调用发票申请接口
                var invoiceApplicationId = BLL_ContractInvoiceNew.Instance.CreateInvoiceApplication(applicationRequest,true);
                
                if (string.IsNullOrEmpty(invoiceApplicationId))
                {
                    return null;
                }

                // 9. 返回成功结果
                return new CompleteTestDataResult
                {
                    InvoiceApplicationId = invoiceApplicationId,
                    ContractId = contractId,
                    ContractName = contractName,
                    CustomerId = "",
                    CustomerName = customerName,
                    CollectionInfoId = "",
                    InvoiceAmount = arrivalAmount
                };
            }
            catch (Exception ex)
            {
                // 记录异常信息，方便调试
                Console.WriteLine($"发票申请创建失败: {ex.Message}");
                return null;
            }
        }
       
        
        /// <summary>
        /// 审核之前添加的测试发票申请（到账开票）
        /// </summary>
        /// <param name="invoiceApplicationId">指定要审核的发票申请ID，如果为空则自动查找一个待审核的申请</param>
        /// <returns>审核结果</returns>
        public CompleteTestDataResult AuditInvoiceApplication(string invoiceApplicationId = null)
        {
            try
            {
                // 1. 查找一个待审核的发票申请（状态为申请中-Applied的发票申请）
                Db_crm_invoice_application application = null;
                
                if (!string.IsNullOrEmpty(invoiceApplicationId))
                {
                    // 如果指定了发票申请ID，则直接获取该申请
                    application = DbOpe_crm_invoice_application.Instance.GetData(a => 
                        a.Id == invoiceApplicationId && 
                        a.BillingType == (int)EnumBillingType.InvoicingUponReceipt && 
                        a.AuditStatus == (int)EnumInvoiceApplicationStatus.Applied && 
                        a.Deleted != true);
                }
                else
                {
                    // 如果未指定发票申请ID，则查找一个符合条件的申请
                    application = DbContext.Crm2Db.Queryable<Db_crm_invoice_application>()
                        .LeftJoin<Db_crm_contract>((a, c) => a.ContractId == c.Id)
                        .Where((a, c) => 
                            a.BillingType == (int)EnumBillingType.InvoicingUponReceipt && 
                            a.AuditStatus == (int)EnumInvoiceApplicationStatus.Applied && 
                            c.ContractName.StartsWith("测试客户") && 
                            a.Deleted != true)
                        .OrderBy((a, c) => a.CreateDate, SqlSugar.OrderByType.Desc)
                        .Select((a, c) => a)
                        .First();
                }
                
                if (application == null)
                {
                    throw new ApiException("未找到符合条件的待审核发票申请");
                }
                
                // 2. 获取合同信息
                var contract = DbOpe_crm_contract.Instance.GetData(c => c.Id == application.ContractId);
                if (contract == null)
                {
                    throw new ApiException($"未找到合同信息，合同ID: {application.ContractId}");
                }
                
                // 3. 先上传发票文件并进行OCR识别
                // 获取测试用的发票PDF文件字节数组
                byte[] invoiceFileBytes = GetTestInvoicePdfBytes();
                if (invoiceFileBytes == null)
                {
                    throw new ApiException("获取测试发票文件失败");
                }
                
                // 调用OCR识别接口
                var ocrResult = BLL_ContractInvoiceNew.Instance.RecognizeAndCompareInvoiceOcrAsync(
                    invoiceFileBytes, application.Id).GetAwaiter().GetResult();
                
                if (ocrResult == null || !ocrResult.IsMatch)
                {
                    // 如果OCR识别失败或比对不匹配，记录错误信息
                    string errorMsg = ocrResult == null ? "OCR识别失败" : $"OCR识别结果与申请信息不匹配: {ocrResult.Remark}";
                    Console.WriteLine(errorMsg);
                    // 在测试环境中，继续执行审核流程
                }
                
                // 4. 准备审核请求
                var request = new AuditInvoiceApplicationRequest
                {
                    Id = application.Id,
                    AuditResult = 1, // 1表示通过
                    AuditComments = "测试审核通过",
                    ActualInvoiceInfo = new ActualInvoiceInfo
                    {
                        InvoiceNumber = "TEST" + DateTime.Now.ToString("yyyyMMddHHmmss"),
                        InvoiceDate = DateTime.Now,
                        InvoiceAmount = application.AppliedAmount,
                        BillingCompanyId = application.BillingCompany

                    }
                };
                
                // 如果OCR识别成功，使用OCR识别的发票信息
                if (ocrResult != null && ocrResult.OcrInfo != null)
                {
                    request.ActualInvoiceInfo.InvoiceNumber = ocrResult.OcrInfo.InvoiceNumber ?? request.ActualInvoiceInfo.InvoiceNumber;
                    request.ActualInvoiceInfo.InvoiceDate = DateTime.TryParse(ocrResult.OcrInfo.InvoiceDate, out DateTime invoiceDate) 
                        ? invoiceDate : request.ActualInvoiceInfo.InvoiceDate;
                    request.ActualInvoiceInfo.InvoiceAmount = ocrResult.OcrInfo.TotalAmount > 0 
                        ? ocrResult.OcrInfo.TotalAmount : request.ActualInvoiceInfo.InvoiceAmount;
                    request.ActualInvoiceInfo.BillingHeader = ocrResult.OcrInfo.BuyerName ?? "";
                    request.ActualInvoiceInfo.InvoicingDetails = ocrResult.OcrInfo.InvoiceDetails ?? "";
                }
                
                // 5. 调用审核接口
                bool result = BLL_ContractInvoiceNew.Instance.AuditInvoiceApplication(request);
                
                if (!result)
                {
                    throw new ApiException("发票审核失败");
                }
                
                // 6. 返回审核结果
                return new CompleteTestDataResult
                {
                    InvoiceApplicationId = application.Id,
                    ContractId = application.ContractId,
                    ContractName = contract.ContractName,
                    InvoiceAmount = application.AppliedAmount,
                    SuccessMessage = $"发票申请审核成功，发票申请ID: {application.Id}"
                };
            }
            catch (Exception ex)
            {
                // 记录异常信息，方便调试
                Console.WriteLine($"发票审核失败: {ex.Message}");
                throw;
            }
        }
    }
} 