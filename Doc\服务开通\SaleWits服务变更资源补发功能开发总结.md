# SaleWits服务变更资源补发功能开发总结

**开发时间**：2025-01-28  
**功能范围**：服务变更提交时检查增加SaleWits子账号的变更原因并自动计算补发资源  
**开发状态**：✅ 已完成

## 📋 需求分析

### 业务需求
当用户在服务变更时选择"增加SalesWits账户数"的变更原因时，系统需要：
1. **自动检测**变更原因中是否包含`EnumGtisServiceChangeProject.SalesWitsAddAccount`（枚举值=8）
2. **自动计算**需要补发的资源（邮件数量和Token数量）
3. **保存到申请表**，等待后续审核通过后实际下发

### 技术实现要点
- **时机**：服务变更申请提交时，而非实际下发时
- **计算逻辑**：基于剩余服务周期按比例补发资源
- **数据存储**：补发资源信息保存在SalesWits申请表中

## 🔧 技术实现

### 1. 获取下次下发时间方法

在 `BLL/ServiceOpening/BLL_ServiceOpening.Helpers.cs` 中新增方法：

```csharp
/// <summary>
/// 获取SaleWits服务的下次年度下发时间
/// </summary>
/// <param name="serviceId">SaleWits服务ID</param>
/// <returns>下次下发时间，如果无法确定则返回null</returns>
public DateTime? GetNextDistributionDate(string serviceId)
```

**功能**：
- 根据SaleWits服务ID查找对应的资源管理记录
- 获取最近一次年度下发记录或首次下发记录
- 计算下次下发时间（上次下发时间+1年）

### 2. 服务变更检查与计算

在 `BLL/BLL_ContractServiceGTISSeries.cs` 的 `UpdateSalesWitsAppl` 方法中新增逻辑：

```csharp
// 检查是否有增加SalesWits子账号的变更原因，计算补发资源
var changeReasonList = new List<EnumGtisServiceChangeProject>();
try
{
    // 尝试解析JSON格式
    var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<dynamic>(apply.ChangeReasonEnums);
    if (changeReasonData != null && changeReasonData.GetProperty("ChangeReasons").GetArrayLength() > 0)
    {
        var changeReasons = changeReasonData.GetProperty("ChangeReasons");
        foreach (var reason in changeReasons.EnumerateArray())
        {
            var changeReason = reason.GetProperty("ChangeReason").GetInt32();
            changeReasonList.Add(changeReason.ToEnum<EnumGtisServiceChangeProject>());
        }
    }
}
catch (System.Text.Json.JsonException)
{
    // JSON解析失败，尝试解析旧格式（兼容性处理）
    changeReasonList = apply.ChangeReasonEnums?.Split(',')
        .Select(e => e.ToEnum<EnumGtisServiceChangeProject>()).ToList() ?? new List<EnumGtisServiceChangeProject>();
}

if (changeReasonList.Contains(EnumGtisServiceChangeProject.SalesWitsAddAccount))
{
    LogUtil.AddLog($"检测到增加SalesWits子账号变更，开始计算补发资源");
    CalculateAndSetSupplementaryResources(saleswits, saleswitsAppl);
}
```

### 3. 补发资源计算方法

新增 `CalculateAndSetSupplementaryResources` 方法：

```csharp
/// <summary>
/// 计算并设置增加子账号的补发资源
/// </summary>
private void CalculateAndSetSupplementaryResources(
    VM_ContractServiceChange.UpdateGtisSeriesAppl_In_SalesWits saleswits, 
    Db_crm_contract_productserviceinfo_saleswits_appl saleswitsAppl)
```

**计算逻辑**：
1. **获取当前服务**：查找合同下的在服SalesWits服务
2. **获取下次下发时间**：调用 `GetNextDistributionDate` 方法
3. **计算剩余月数**：从当前时间到下次下发时间的月数
4. **计算新增账号数**：申请账号数 - 当前账号数  
5. **计算补发资源**：
   - 补发邮件 = 新增账号数 × 300 × 剩余月数
   - 补发Token = 新增账号数 × 10 × 剩余月数（万个单位）

### 4. 数据存储

补发资源保存到 `Db_crm_contract_productserviceinfo_saleswits_appl` 表的以下字段：
- `GiftResourceMonths` - 赠送月数（剩余月数）
- `EmailCount` - 邮件数量（补发邮件总数）  
- `TokenCount` - Token数量（补发Token总数，万个单位）

## 🔄 业务流程

```
服务变更申请提交
        ↓
检查变更原因中是否包含"增加SalesWits账户数"
        ↓
获取当前服务信息 → 获取下次下发时间
        ↓
计算剩余月数 → 计算新增账号数
        ↓
计算补发资源 = 新增账号数 × 每月标准 × 剩余月数
        ↓
保存到申请表 → 等待审核
        ↓
审核通过后实际下发资源
```

## 📊 计算示例

### 场景示例
- **当前账号数**：5个
- **申请账号数**：8个  
- **新增账号数**：3个
- **当前时间**：2025-01-28
- **下次下发时间**：2025-07-15
- **剩余月数**：6个月

### 计算结果
- **补发邮件**：3 × 300 × 6 = 5,400封
- **补发Token**：3 × 10 × 6 = 180万个
- **赠送月数**：6个月

## 🛡️ 异常处理

### 1. 数据验证
- 检查服务是否存在
- 验证下次下发时间是否有效
- 确保新增账号数大于0

### 2. 边界情况处理
- 剩余月数小于等于0时不补发
- 新增账号数小于等于0时不补发
- 无法获取下次下发时间时跳过计算

### 3. 日志记录
- 记录补发资源计算的详细过程
- 记录异常情况和跳过原因
- 便于问题排查和业务监控

## ✅ 测试验证要点

### 1. 功能测试
- ✅ 验证变更原因检查是否正确
- ✅ 验证补发资源计算是否准确
- ✅ 验证数据保存是否正确

### 2. 边界测试  
- ✅ 测试剩余月数为0的情况
- ✅ 测试新增账号数为0的情况
- ✅ 测试无法获取下次下发时间的情况

### 3. 集成测试
- ✅ 编译通过，无语法错误
- ✅ 与现有服务变更流程无冲突
- ✅ 日志记录完整

## 📝 总结

本次开发成功实现了SaleWits服务变更时的自动资源补发计算功能：

### ✅ 主要成果
1. **完整的业务逻辑**：从变更原因检查到资源计算到数据保存
2. **准确的计算方法**：基于剩余服务周期按比例补发
3. **完善的异常处理**：多种边界情况和异常情况的处理
4. **详细的日志记录**：便于监控和问题排查

### 🔧 技术特点
- **无侵入性**：不影响现有服务变更流程
- **高可靠性**：充分的数据验证和异常处理
- **易维护性**：清晰的代码结构和详细注释
- **可扩展性**：为后续类似功能提供参考

### 🚀 后续建议
1. **审核通过处理**：在审核通过后实际执行资源下发
2. **统计报表**：添加补发资源的统计和报表功能
3. **配置化**：将每月标准资源数量改为可配置参数
4. **监控告警**：添加异常情况的监控和告警机制

此功能为SaleWits服务变更业务提供了完整的资源补发解决方案，确保客户在增加子账号时能够获得应有的资源配额。