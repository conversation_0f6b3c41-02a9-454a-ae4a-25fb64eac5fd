﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.WorkLog;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModel.Gtis;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using JiebaNet.Segmenter.Common;
using Microsoft.AspNetCore.Http;
using Namotion.Reflection;

namespace CRM2_API.BLL
{
    public class BLL_Product : BaseBLL<BLL_Product>
    {
        /// <summary>
        /// 修改产品状态信息，变更产品状态为启用、停用，多条记录全部执行成功则返回成功，否则返回失败。
        /// </summary>
        /// <param name="Ids"></param>
        /// <param name="State"></param>
        /// <param name="Remark"></param>
        /// <exception cref="ApiException"></exception>
        public void UpdateProductState(string Ids, int State, string Remark)
        {
            //验证传入的Ids是否为空
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("产品主键不可为空");
            //转换Ids为list
            var idList = Ids.Split(",").ToList();
            idList.ForEach(item =>
            {
                if (item.IsNotNullOrEmpty())
                    WorkLog_Product.Instance.AddProductWorkLog(item, State == 0 ? EnumProductLogType.Stop : EnumProductLogType.Start, Remark);
            });
            //执行启用停用更新
            DbOpe_crm_product.Instance.UpdateProductState(idList, State, Remark);
        }

        /// <summary>
        /// 删除产品信息，当产品状态为停用、草稿时，可删除。验证数据权限。只做逻辑删除，修改Deleted字段为1。
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public void DeleteProduct(string Ids)
        {
            //验证传入的Ids是否为空
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("产品主键不可为空");
            //转换Ids为list
            var idList = Ids.Split(",").ToList();

            //执行启用停用更新
            DbOpe_crm_product.Instance.DeleteProduct(idList);
        }

        /// <summary>
        /// 添加产品信息。保存草稿：添加产品信息，状态为草稿。新建：添加产品信息，状态为启用。
        /// 表名:Crm_Product 产品表。表名:Crm_Product_Price 产品价格表。表名:Crm_Product_Combination 产品组合表。
        /// </summary>
        /// <param name="addProductIn"></param>
        /// <returns></returns>
        public void AddProduct(AddProduct_In addProductIn)
        {
            //增加判断：价格不能为同一种货币
            var hasSameMoney = addProductIn.ProductPrice.GroupBy(e => new { e.ServiceCycle, e.Currency }).Where(g => g.Count() > 1).Count();
            if (hasSameMoney > 0)
            {
                throw new ApiException("产品价格不能有同一币种的多种价格，请检查");
            }

            if (DbOpe_crm_product.Instance.ProductNameValidating(addProductIn.ProductName))
            {
                throw new ApiException("产品名称已存在,请检查");
            }

            if (addProductIn.SubmitState == EStatus.Start && addProductIn.ProductType == (int)EnumProductType.Combination && addProductIn.ProductCombination.Count() == 0)
            {
                throw new ApiException("组合产品需要选择子产品");
            }
            if (addProductIn.ProductNameEN.IsNullOrEmpty())
            {
                throw new ApiException("产品英文名称为必填项");
            }
            if (addProductIn.ProductType == null)
            {
                throw new ApiException("未选择产品类型");
            }
            //if (addProductIn.ServiceCycle == null)
            //{
            //    throw new ApiException("未选择服务周期");
            //}
            //if (addProductIn.ServiceCycleEnd == null)
            //{
            //    throw new ApiException("未选择服务周期月份");
            //}
            //if (addProductIn.IsRecommend == null)
            //{
            //    throw new ApiException("未选择是否推荐");
            //}

            Db_crm_product crm_Product = addProductIn.MappingTo<Db_crm_product>();
            //生成主键
            crm_Product.Id = Guid.NewGuid().ToString();
            //产品编码（后台赋值）以3位堆积数字按顺序生成，如：001
            //改成五位 2023年11月13日
            crm_Product.ProductNum = (DbOpe_crm_product.Instance.GetQueryCount(crm_Product) + 1).ToString().PadLeft(5, '0');
            //状态，根据保存按钮的选择，分别为草稿和启用
            crm_Product.ProductState = (int)addProductIn.SubmitState;
            //创建人 当前登录人员
            crm_Product.CreateUser = UserTokenInfo.id;
            crm_Product.CreateDate = DateTime.Now;
            crm_Product.UpdateUser = UserTokenInfo.id;
            crm_Product.UpdateDate = DateTime.Now;

            ////草稿问题考量
            crm_Product.ServiceCycleStart = addProductIn.ServiceCycleStart != null ? (int)addProductIn.ServiceCycleStart : 0;
            crm_Product.ServiceCycleEnd = addProductIn.ServiceCycleEnd != null ? (int)addProductIn.ServiceCycleEnd : 0;
            crm_Product.ServiceCycle = addProductIn.ServiceCycle != null ? (int)addProductIn.ServiceCycle : 1;
            //价格表增加
            List<Db_crm_product_price> PriceList = new List<Db_crm_product_price>();

            addProductIn.ProductPrice.ForEach(item =>
            {
                Db_crm_product_price price = item.MappingTo<Db_crm_product_price>();
                price.Id = Guid.NewGuid().ToString();
                price.ProductId = crm_Product.Id;

                //2024年1月18日 修改 服务周期挪入价格中
                //2024年2月21日 修改 此字段已不在有效，为防止报错默认为1
                //price.ServiceCycle = item.ServiceCycle != null ? (int)item.ServiceCycle : 1;
                price.ServiceCycle = 1;
                //price.PerPrice = price.Price / price.ServiceCycle;
                //2024年2月21日 增加
                //是否区分国内外售卖 传入空则默认为0 不区分/国内价格 传入1则为国外价格
                price.SalesFloor = item.SalesFloor != null ? (int)item.SalesFloor : 0; 
                //计价方式 详见 EnumPriceMode
                price.PriceMode = item.PriceMode;
                //区间上限/复购价/自费价
                price.PricePart2 = item.PricePart2;

                price.Deleted = false;
                price.CreateUser = UserTokenInfo.id;
                price.CreateDate = DateTime.Now;
                price.UpdateUser = UserTokenInfo.id;
                price.UpdateDate = DateTime.Now;
                PriceList.Add(price);
            });

            //组合表增加
            List<Db_crm_product_combination> CombinationList = new List<Db_crm_product_combination>();
            addProductIn.ProductCombination.ForEach(item =>
            {
                Db_crm_product_combination Combination = item.MappingTo<Db_crm_product_combination>();
                Combination.Id = Guid.NewGuid().ToString();
                Combination.ProductId = crm_Product.Id;
                Combination.Deleted = false;
                Combination.CreateUser = UserTokenInfo.id;
                Combination.CreateDate = DateTime.Now;
                Combination.UpdateUser = UserTokenInfo.id;
                Combination.UpdateDate = DateTime.Now;
                CombinationList.Add(Combination);
            });

            DbOpe_crm_product.Instance.AddProductInTrans(crm_Product, PriceList, CombinationList);


        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="updateProduct_In"></param>
        /// <returns></returns>
        public void UpdateProduct(UpdateProduct_In updateProduct_In)
        {
            #region 废弃部分
            //int Data = 0;

            //Db_crm_product UpdateProduct = updateProduct_In.MappingTo<Db_crm_product>();
            //UpdateProduct.ProductState = (int)updateProduct_In.SubmitState;
            //UpdateProduct.UpdateUser = UserTokenInfo.id;
            //UpdateProduct.UpdateDate = DateTime.Now;



            ////价格表更新
            //List<Db_crm_product_price> PriceList = new List<Db_crm_product_price>();

            //updateProduct_In.ProductPrice.ForEach(item =>
            //{
            //    Db_crm_product_price price = item.MappingTo<Db_crm_product_price>();
            //    price.Id = price.Id == null ? Guid.NewGuid().ToString() : price.Id;
            //    price.ProductId = UpdateProduct.Id;
            //    price.Deleted = false;
            //    price.CreateUser = price.Id == null ? UserTokenInfo.id : price.CreateUser;
            //    price.CreateDate = price.Id == null ? DateTime.Now : price.CreateDate;

            //    price.UpdateUser = UserTokenInfo.id;
            //    price.UpdateDate = DateTime.Now;
            //    PriceList.Add(price);
            //});
            ////价格表也应考虑删除 以及新增的问题


            ////组合表更新
            //List<Db_crm_product_combination> CombinationList = new List<Db_crm_product_combination>();
            //updateProduct_In.ProductCombination.ForEach(item =>
            //{
            //    Db_crm_product_combination Combination = item.MappingTo<Db_crm_product_combination>();
            //    Combination.Id = item.Id == null ? Guid.NewGuid().ToString() : item.Id;
            //    Combination.ProductId = UpdateProduct.Id;
            //    Combination.SubProductId = item.SubProductId;
            //    Combination.Deleted = false;
            //    Combination.CreateUser = UserTokenInfo.id;
            //    Combination.CreateDate = DateTime.Now;
            //    Combination.UpdateUser = UserTokenInfo.id;
            //    Combination.UpdateDate = DateTime.Now;
            //    CombinationList.Add(Combination);
            //});
            ////暂时不确定如何处理组合产品的更新如何获取移除的
            ////使用简单粗暴的办法：逻辑删除原来的，然后新增
            //string ProductId = UpdateProduct.Id.ToString();


            //DbOpe_crm_product.Instance.UpdateProductInTrans(UpdateProduct, PriceList, ProductId, CombinationList);
            #endregion

            DbOpe_crm_product.Instance.UpdateProductInTrans(updateProduct_In, UserId);
            //DbOpe_crm_product.Instance.SaveQueues();
        }

        /// <summary>
        /// 根据当前登录人ID，非管理者返回自身，管理者返回自身及下属机构所有人员id
        /// 未按特定规则排序，仅包含人员id，无其他信息
        /// </summary>
        /// <returns></returns>
        public List<string> GetOrgUserByTokenUserId()
        {
            return DbOpe_crm_product.Instance.GetOrgUserByRoleUser(UserTokenInfo.id);
        }

        public List<ProductOperateLog> GetProductOperateLog(string ProductId)
        {
            return DbOpe_crm_product.Instance.GetProductOperateLog(ProductId);

        }

        public bool GetIsAllowSuperSubAccount()
        {
            bool isAllow = true;
            //修改 2024年3月11日 增加角色验证，只在角色是普通销售的人员进行权限验证
            //普通销售角色ID： 9fbe8362-b96f-46da-902a-aae767aa8b94
            //销售主管角色ID：313f9d6b-8feb-462c-9bdb-703436a37026
            string UserId = TokenModel.Instance.id;
            var IsSaler = DbOpe_sys_userinrole.Instance.GetData(r => r.UserId == UserId);
            //是当前是普通销售角色，做验证
            if (IsSaler != null && (IsSaler.RoleId == "9fbe8362-b96f-46da-902a-aae767aa8b94" || IsSaler.RoleId == "313f9d6b-8feb-462c-9bdb-703436a37026"))
            {
                var GetRuleData = DbOpe_crm_product_rules_special.Instance.GetData(r => r.UserId == UserId);
                if (GetRuleData.IsNotNull())
                {
                    isAllow = GetRuleData.AllowSuperSubAccount;
                }
                else
                {
                    isAllow = false;
                }
            }
            return isAllow;

        }

        public List<SearchSpecialProductRules_Out> SearchSpecialProductRules(SearchSpecialProductRules_In searchSpecialProductRules_In, ref int total)
        {
            return DbOpe_crm_product.Instance.SearchSpecialProductRules(searchSpecialProductRules_In,ref total);
        }
    }
}
