﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///产品规则表_不支持海关编码当地销售及使用的数据
    ///</summary>
    [SugarTable("crm_product_rules_localhscodenotsupport")]
    public class Db_crm_product_rules_localhscodenotsupport
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:产品规则表Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ProductRulesId {get;set;}

           /// <summary>
           /// Desc:销售地
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string SalesLocal {get;set;}

           /// <summary>
           /// Desc:销售地Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? SalesLocalCountryId {get;set;}

           /// <summary>
           /// Desc:海关编码
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string HsCode {get;set;}

           /// <summary>
           /// Desc:海关编码Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string HsCodeId {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
