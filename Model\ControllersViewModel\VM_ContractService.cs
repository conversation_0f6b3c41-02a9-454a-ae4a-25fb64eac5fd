﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.RemindInfo;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel.Common;
using DocumentFormat.OpenXml.Drawing.Charts;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;
using System.Security.Cryptography;
using static CRM2_API.Common.Cache.LocalCache;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Collectioninfo;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_Coupon;
using CRM2_API.Model.BLLModel.ServiceOpening;
using CRM2_API.Model.BLLModel.ServiceChange;

namespace CRM2_API.Model.ControllersViewModel
{
    public class SearchServicesList_OUT
    {
        /// <summary>
        /// 服务Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 申请Id
        /// </summary>
        public string ApplId { get; set; }
        /// <summary>
        /// 合同主键
        /// </summary>
        public string ContractId { get; set; }
        /// <summary>
        /// 客户主键
        /// </summary>
        public string CustomerId { get; set; }
        /// <summary>
        /// 产品类型
        /// </summary>
        public int? ProductType { get; set; }
        /// <summary>
        /// 产品类型名称
        /// </summary>
        public string ProductTypeName { get; set; }
        /// <summary>
        /// 客户编号
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 甲方公司
        /// </summary>
        public string FirstPartyName { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }
        /// <summary>
        /// 合同名称
        /// </summary>
        public string ContractName { get; set; }
        /// <summary>
        /// 签约日期
        /// </summary>
        public string SigningDate { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 服务周期
        /// </summary>
        public string ServiceCycle { get; set; }
        /// <summary>
        /// 保护期
        /// </summary>
        public string ProtectionDeadline { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public EnumSalesServiceState SalesServiceState { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public string SalesServiceStateName { get; set; }
        /// <summary>
        /// 服务截止日
        /// </summary>
        public string ServiceEnd { get; set; }
        /// <summary>
        /// 服务开始日
        /// </summary>
        public string ServiceStart { get; set; }
        /// <summary>
        /// 保护期截止
        /// </summary>
        public DateTime ProtectionDeadlineDt { get; set; }
        /// <summary>
        /// 服务期截止
        /// </summary>
        public DateTime ServiceCycleEndDt { get; set; }
        /// <summary>
        /// 客户账号发送时间
        /// </summary>
        public string SendAccountTime { get; set; }
        public DateTime? SendAccountTimeDt { get; set; }
        /// <summary>
        /// 是否即将到期释放
        /// </summary>
        public bool NearlyRealese { get; set; }
        /// <summary>
        /// 是否服务即将到期
        /// </summary>
        public bool NearlyServiceEnd { get; set; }
        public int? ApplState { get; set; }
        public int? State { get; set; }
        public int? ServiceType { get; set; }
        public int? IsReceipt { get; set; }
        /// <summary>
        /// 能否申请服务
        /// </summary>
        public bool CouldApplyService { get; set; }
        /// <summary>
        /// 能否申请服务变更
        /// </summary>
        public bool CouldUpdateService { get; set; }
        /// <summary>
        /// 能否延期
        /// </summary>
        public bool CouldUrgent { get; set; }
    }

    /// <summary>
    /// 客户服务情况列表最终返回列
    /// 因存在不属于数据表的属性，sql会报错
    /// </summary>
    public class SearchServicesList_OUT_Return : SearchServicesList_OUT
    {
        /// <summary>
        /// 能否申请服务
        /// </summary>
        public bool CouldApplyService { get; set; }
        /// <summary>
        /// 能否申请服务变更
        /// </summary>
        public bool CouldUpdateService { get; set; }
    }
    public class SearchServicesList_IN : ApiTableIn
    {
        /// <summary>
        /// 关键字(合同编码/甲方公司名称/合同名称)
        /// </summary>
        public string KeyWords { get; set; }
        /// <summary>
        /// 合同编码
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string FirstPartyName { get; set; }
        /// <summary>
        /// 合同名称
        /// </summary>
        public string ContractName { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public List<int> State { get; set; }
        /// <summary>
        /// 产品
        /// </summary>
        public List<string> Products { get; set; }
        /// <summary>
        /// 产品类型
        /// </summary>
        public List<string> ProductTypes { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime? ServiceStartDateStart { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime? ServiceStartDateEnd { get; set; }
        /// <summary>
        /// 服务截止时间
        /// </summary>
        public DateTime? ServiceEndDateStart { get; set; }
        /// <summary>
        /// 服务截止时间
        /// </summary>
        public DateTime? ServiceEndDateEnd { get; set; }
        /// <summary>
        /// 保护截止时间
        /// </summary>
        public DateTime? ProtectDateStart { get; set; }
        /// <summary>
        /// 保护截止时间
        /// </summary>
        public DateTime? ProtectDateEnd { get; set; }
        /// <summary>
        /// 提醒跳转搜索类型
        /// </summary>
        public EnumRemindType EnumRemindType { get; set; }
        //public EnumCustomerSort EnumCustomerSort { get; set; }

        public string? appleId { get; set; }

        public string? ContractId { get; set; }
    }
    public class SearchContractProductServiceInfoGtisAppl_IN : SafeApiTableIn
    {
        /// <summary>
        /// 合同编码
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 甲方公司名称
        /// </summary>
        public string FirstPartyName { get; set; }
        /// <summary>
        /// 合同名称
        /// </summary>
        public string ContractName { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public List<string> ApplicantId { get; set; }
        /// <summary>
        /// 签约类型
        /// </summary>
        public int? ContractType { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public List<int> State { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public List<int> ServiceType { get; set; }
        /// <summary>
        /// 账号状态
        /// </summary>
        public List<int> AccountStatus { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDateStart { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDateEnd { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime? ServiceDateStart { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime? ServiceDateEnd { get; set; }
        public EnumQueryListType EnumQueryListType { get; set; }
        public string Remark { get; set; }

        /// <summary>
        /// 优惠类型
        /// </summary>
        public EnumGtisDiscountType? DiscountType { get; set; }

        public string? AppleId { get; set; }
        /// <summary>
        /// 通过时间-开始
        /// </summary>
        public DateTime? ApprovalStartDate { get; set; }
        /// <summary>
        /// 通过时间-结束
        /// </summary>
        public DateTime? ApprovalEndDate { get; set; }
        /// <summary>
        /// 双方盖章合同状态
        /// </summary>
        public int? StampReviewStatus { get; set; }
    }
    public class CheckGtisFreeAble_OUT
    {
        /// <summary>
        /// 是否可以赠送gtis
        /// </summary>
        public bool GTISFreeAble { get; set; }
        /// <summary>
        /// 是否可以赠送慧思学院
        /// </summary>
        public bool CollegeFreeAble { get; set; }
        /// <summary>
        /// 是否可以赠送环球搜
        /// </summary>
        public bool GlobalFreeAble { get; set; }
    }
    public class SearchContractProductServiceInfoGtisAppl_OUT
    {
        public string Id { get; set; }
        public string ContractId { get; set; }
        /// <summary>
        /// 客户编号
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 甲方公司
        /// </summary>
        public string FirstPartyName { get; set; }
        /// <summary>
        /// 合同名称
        /// </summary>
        public string ContractName { get; set; }
        /// <summary>
        /// 签约类型
        /// </summary>
        public int? ContractType { get; set; }
        /// <summary>
        /// 签约类型
        /// </summary>
        public string ContractTypeName { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplicantId { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplicantName { get; set; }
        /// <summary>
        /// 申请日期
        /// </summary>
        public string ApplicantDate { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 申请服务周期
        /// </summary>
        public string ServiceCycle { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public int? State { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public string StateName { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public int? ServiceType { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public string ServiceTypeName { get; set; }
        /// <summary>
        /// 账号数量
        /// </summary>
        public int AccountNum { get; set; }
        /// <summary>
        /// 账号状态
        /// </summary>
        public int? AccountStatus { get; set; }
        /// <summary>
        /// 账号状态
        /// </summary>
        public string AccountStatusName { get; set; }
        /// <summary>
        /// 开通人
        /// </summary>
        public string ReviewerId { get; set; }
        /// <summary>
        /// 开通人
        /// </summary>
        public string ReviewerName { get; set; }
        /// <summary>
        /// 开通时间
        /// </summary>
        public DateTime? ReviewerDate { get; set; }
        /// <summary>
        /// 合同服务信息环球慧思表id
        /// </summary>
        public string ContractServiceInfoGtisId { get; set; }
        /// <summary>
        /// 是否已经失效
        /// </summary>
        public int? IsInvalid { get; set; }
        /// <summary>
        /// 免费延长时间变更标记
        /// </summary>
        public bool IsFree { get; set; }
        public int applState { get; set; }
        public int processingType { get; set; }
        public int? serviceState { get; set; }
        public DateTime? ServiceCycleStart { get; set; }
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 是否可以赠送gtis
        /// </summary>
        public bool GTISFreeAble { get; set; }
        /// <summary>
        /// 是否可以赠送慧思学院
        /// </summary>
        public bool CollegeFreeAble { get; set; }
        /// <summary>
        /// 是否可以赠送环球搜
        /// </summary>
        public bool GlobalFreeAble { get; set; }
        /// <summary>
        /// Desc:账号信息是否发送
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool SendAccount { get; set; }

        /// <summary>
        /// Desc:账号信息发送时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? SendAccountTime { get; set; }

        /// <summary>
        /// 申请备注
        /// </summary>
        public string ApplyRemark { get; set; }
        /// <summary>
        /// 初审备注
        /// </summary>
        public string RegisteredRemark { get; set; }
        /// <summary>
        /// 审核反馈
        /// </summary>
        public string FeedBack { get; set; }
        /// <summary>
        /// 复核备注
        /// </summary>
        public string ReviewerRemark { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string CustomerEmail { get; set; }
        /// <summary>
        /// 是否需要今天处理
        /// </summary>
        public bool IsPendingToday { get; set; }
        /// <summary>
        /// 优惠类型
        /// </summary>
        public EnumGtisDiscountType DiscountType { get; set; }
        /// <summary>
        /// 优惠券数量
        /// </summary>
        public int CouponCount { get; set; }
        /// <summary>
        /// 优惠券Ids
        /// </summary>
        public string CouponIds { get; set; }
        /// <summary>
        /// 加急处理
        /// </summary>
        public bool IsUrgent { get; set; }
        /// <summary>
        /// 加急处理标签
        /// </summary>
        public bool IsUrgentLable { get; set; }
        /// <summary>
        /// 双方盖章合同状态
        /// </summary>
        public int? StampReviewStatus { get; set; }
        /// <summary>
        /// 双方盖章合同状态名称
        /// </summary>
        public string StampReviewStatusName { get { return StampReviewStatus == null ? "" : ((EnumStampReviewStatus)StampReviewStatus).GetEnumDescription().ToString(); } }
    }
    public class ServiceInfoGtisUser_OUT
    {
        public string Id { get; set; }

        /// <summary>
        /// 合同服务信息环球慧思表id
        /// </summary>           
        public string ContractServiceInfoGtisId { get; set; }

        /// <summary>
        /// 开通状态
        /// </summary>           
        public int? OpeningStatus { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public string OpeningStatusName
        {
            get
            {
                return OpeningStatus != null ? ((EnumGtisUserOpeningStatus)OpeningStatus).GetEnumDescription() : "";
            }
        }
        /// <summary>
        /// Desc:账号Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// Desc:账号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AccountNumber { get; set; }
        /// <summary>
        /// 密码
        /// </summary>
        public string PassWord { get; set; }

        /// <summary>
        /// Desc:账号类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? AccountType { get; set; }
        /// <summary>
        /// Desc:账号类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AccountTypeName
        {
            get
            {
                return AccountType != null ? ((EnumGtisAccountType)AccountType).GetEnumDescription() : "";
            }
        }
        /// <summary>
        /// Desc:启用日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string StartDate { get; set; }
        /// <summary>
        /// Desc:停用日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string EndDate { get; set; }
        /// <summary>
        /// Desc:登录IP
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string LoginIP { get; set; }
        /// <summary>
        /// Desc:最近登录时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string LastLoginTime { get; set; }
        /// <summary>
        /// 每个账户共享人数
        /// </summary>
        public int SharePeopleNum { get; set; }
        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int AuthorizationNum { get; set; }
        /// <summary>
        /// Desc:使用者
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string PhoneUser { get; set; }
        /// <summary>
        /// Desc:使用者数量
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? PhoneUserNum { get; set; }
        /// <summary>
        /// 所有使用者
        /// </summary>
        public List<BM_GtisOpe_UserPhoneInfo> AllPhoneUserInfo { get; set; }
        /// <summary>
        /// 环球搜码
        /// </summary>
        public string GlobalSearchCode { get; set; }
    }
    public class AddFreeProductServices_In
    {
        /// <summary>
        /// 合同服务信息环球慧思申请表id
        /// </summary>
        [Required(ErrorMessage = "合同服务信息环球慧思申请表id不可为空")]
        public string ProductServiceInfoGtisApplId { get; set; }
        public AddFreeProductServices_GTIS_In AddFreeProductServices_GTIS { get; set; }
        public PresentedGlobalSearchService_In PresentedGlobalSearchService_In { get; set; }
        public PresentedCollegeService_In PresentedCollegeService_In { get; set; }
    }
    public class AddFreeProductServices_GTIS_In
    {
        /// <summary>
        /// 赠送月份
        /// </summary>
        public int? AddServiceMonth { get; set; }
        /// <summary>
        /// 赠送后服务截止日期
        /// </summary>
        public DateTime? NewServiceCycleEnd { get; set; }
    }
    public class AuditContractProductServiceInfoGtisAppl_IN
    {
        /// <summary>
        /// 合同服务信息环球慧思申请表id
        /// </summary>
        [Required(ErrorMessage = "合同服务信息环球慧思申请表id不可为空")]
        public string ProductServiceInfoGtisApplId { get; set; }

        /// <summary>
        /// 账号生成方式
        /// </summary>
        public int AccountGenerationMethod { get; set; }

        /// <summary>
        /// 客户编码
        /// </summary>
        public string ContractNum { get; set; }

        /// <summary>
        /// 主账号数量
        /// </summary>
        public int PrimaryAccountsNum { get; set; }

        /// <summary>
        /// 子账号数量
        /// </summary>
        public int SubAccountsNum { get; set; }

        /// <summary>
        /// 每个账户共享人数
        /// </summary>
        public int SharePeopleNum { get; set; }

        /// <summary>
        /// 共享使用总数
        /// </summary>
        public int? ShareUsageNum { get; set; }

        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int? AuthorizationNum { get; set; }
        /// <summary>
        /// 禁用下载/导出权限
        /// </summary>
        public bool ForbidSearchExport { get; set; }
        /// <summary>
        /// 是否开通定制报告
        /// </summary>
        public bool WordRptPermissions { get; set; }
        /// <summary>
        /// 定制报告每年总次数
        /// </summary>
        public int? WordRptMaxTimes { get; set; }
        ///// <summary>
        ///// 是否老客户
        ///// </summary>
        //public bool IsGtisOldCustomer { get; set; }
        /// <summary>
        /// 子账号自动分配全部国家
        /// </summary>
        public bool AllCountrySubUser { get; set; }
        /// <summary>
        /// 服务周期-开始时间
        /// </summary>
        public DateTime? ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务周期-结束时间
        /// </summary>
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 服务月份
        /// </summary>
        public int? ServiceMonth { get; set; }
        /// <summary>
        /// 常驻国家
        /// </summary>
        public List<int> ResidentCountries { get; set; }

        /// <summary>
        /// 常驻城市
        /// </summary>
        public List<int> ResidentCitys { get; set; }

        /// <summary>
        /// 零售国家
        /// </summary>
        public int? RetailCountry { get; set; }

        /// <summary>
        /// 环球慧思用户表
        /// </summary>
        public List<GtisUser_IN>? GtisUser { get; set; }

        /// <summary>
        /// 环球慧思用户使用者
        /// </summary>
        public List<MainSysUserPhone>? MainSysUserPhones { get; set; }
        /// <summary>
        /// 环球慧思零售国家表
        /// </summary>
        public List<GtisRetailCountry_IN> GtisRetailCountry { get; set; }

        /// <summary>
        /// 状态(12.5 true：保存、false：拒绝)
        /// </summary>
        [Required(ErrorMessage = "审核结果不可为空")]
        public bool Pass { get; set; }
        /// <summary>
        /// 本次备注
        /// </summary>
        public string CurrentRemark { get; set; }
        /// <summary>
        /// 到账信息Id列表
        /// </summary>
        public List<string>? ReceiptRegisterIds { get; set; }
        /// <summary>
        /// 需要发给环球搜的备注
        /// </summary>
        public string GlobalSearchRemark { get; set; }
        /// <summary>
        /// 环球搜账号数量(主+子)
        /// </summary>           
        public int? GlobalSearchAccountCount { get; set; }
        /// <summary>
        /// 环球搜结算级别(主账号数量+子账号数量)
        /// </summary>
        public string GlobalSearchSettlementLevel { get; set; }
        /// <summary>
        /// 环球搜收款时段
        /// </summary>
        public decimal? GlobalSearchSettlementMonth { get; set; }
        /// <summary>
        /// 环球搜此次执行的服务开始时间
        /// </summary>           
        public DateTime? GlobalSearchExecuteServiceCycleStart { get; set; }
        /// <summary>
        /// 环球搜此次执行的服务结束时间
        /// </summary>           
        public DateTime? GlobalSearchExecuteServiceCycleEnd { get; set; }
        /// <summary>
        /// 环球搜此次执行的服务月份
        /// </summary>           
        public int? GlobalSearchExecuteServiceMonth { get; set; }
        /// <summary>
        /// 是否开通Crm系统
        /// </summary>
        public int? IsOpenCrm { get; set; }
        /// <summary>
        /// 最大Crm账户数量
        /// </summary>
        public int MaxCrmAccount { get; set; }
        /// <summary>
        /// 是否删除子账号授权国家次数
        /// </summary>
        public bool? DelAuthorizationNum { get; set; }
    }

    public class GtisUser_IN
    {
        /// <summary>
        /// 账号
        /// </summary>
        public string? AccountNumber { get; set; }

        /// <summary>
        /// 账号类型
        /// </summary>
        public int AccountType { get; set; }

        /// <summary>
        /// 每个账户共享人数
        /// </summary>
        public int? SharePeopleNum { get; set; }

        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int? AuthorizationNum { get; set; }

        /// <summary>
        /// 账号状态
        /// </summary>
        public int OpeningStatus { get; set; }
    }
    public class GtisRetailCountry_IN
    {
        /// <summary>
        /// SID
        /// </summary>
        public int Sid { get; set; }
    }
    public class ContractServiceGtisInfo_OUT
    {
        /// <summary>
        /// 境内零售单国家 /境外零售  子账号授权国家次数需要写0
        /// </summary>
        public bool IsVIPSingle { get; set; }
        /// <summary>
        /// 是否含VIP零售
        /// </summary>
        public bool IsContainsVIP { get; set; }
        /// <summary>
        /// 签约产品_VIP零售
        /// </summary>
        public ProductInfo_Out ProductInfo_VIP { get; set; }
        /// <summary>
        /// 是否含VIP零售
        /// </summary>
        public bool IsContainsGlobalSearch { get; set; }
        /// <summary>
        /// 签约产品_环球搜
        /// </summary>
        public ProductInfo_Out ProductInfo_GlobalSearch { get; set; }

        /// <summary>
        /// 免费延长时间变更标记
        /// </summary>
        public bool IsFree { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public int? State { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public string StateName { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public int? ServiceType { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public string ServiceTypeName { get; set; }
        /// <summary>
        /// 账号
        /// </summary>
        public List<ServiceInfoGtisUser_OUT> Accounts { get; set; }
        /// <summary>
        /// 账号状态
        /// </summary>
        public int? AccountStatus { get; set; }
        /// <summary>
        /// 账号状态
        /// </summary>
        public string AccountStatusName { get; set; }
        /// <summary>
        /// 合同和到账信息
        /// </summary>
        public ContractInfoAndReceipt_Out ContractInfoAndReceipt { get; set; }
        /// <summary>
        /// 签约产品
        /// </summary>
        public ProductInfo_Out ProductInfo { get; set; }
        /// <summary>
        /// 签约产品_超级子账号
        /// </summary>
        public ProductInfo_Out ProductInfo_SuperSubAccount { get; set; }
        /// <summary>
        /// 申请内容
        /// </summary>
        public ApplGtisInfo_OUT ApplGtisInfo { get; set; }
        /// <summary>
        /// 超级子账号总个数（产品内包含的+合同额外购买的）
        /// </summary>
        public int SuperSubAccountTotalNum { get; set; }
        /// <summary>
        /// 定制报告默认下载次数
        /// </summary>
        public int Appl_ReportTotalNum { get; set; }
        /// <summary>
        /// 在服内容（如果是续约合同的话，这里是之前合同的服务产品信息）
        /// </summary>
        public GtisInfo_OUT GtisInfo { get; set; }
        /// <summary>
        /// 变更信息（最终开通审批时更改的内容）
        /// </summary>
        public List<GtisChangeItems> GtisChangeItems { get; set; }
        /// <summary>
        /// 开通项目
        /// </summary>
        public List<GtisRetailCountry_OUT> OpenItems { get; set; }
        /// <summary>
        /// 登记复核信息
        /// </summary>
        public RegisterInfo_OUT RegisterInfo { get; set; }
        /// <summary>
        /// G5备注
        /// </summary>
        public string G5Remark { get; set; }
        /// <summary>
        /// 本次备注
        /// </summary>
        public string CurrentRemark { get; set; }
        /// <summary>
        /// 赠送信息(gtis)
        /// </summary>
        public GetFreeGtisServiceInfo_Out GetFreeGtisServiceInfo { get; set; }
        /// <summary>
        /// 赠送信息(环球搜)
        /// </summary>
        public GetPresentedGlobalSearchService_Out GetPresentedGlobalSearchService { get; set; }
        /// <summary>
        /// 赠送信息(慧思学院)
        /// </summary>
        public PresentedCollegeService_Out PresentedCollegeService { get; set; }

        /// <summary>
        /// 到账备注列表
        /// </summary>
        public List<string> ReceiptRemarks { get; set; }
        /// <summary>
        /// 合同的所有到账信息
        /// </summary>
        public List<ReceiptRegisterCollectionInfoItem_Out> ReceiptRegisterCollectionList { get; set; }
        /// <summary>
        /// 历史服务信息
        /// </summary>
        public List<HistoryGtisServiceInfo> HistoryGtisServiceList { get; set; }
        /* /// <summary>
         /// 合同最后到账年月
         /// </summary>
         public string LastReceiptMonth { get; set; }*/
        /// <summary>
        /// 优惠类型
        /// </summary>
        public EnumGtisDiscountType DiscountType { get; set; }
        /// <summary>
        /// 优惠券列表
        /// </summary>
        public List<CounponDetail> CouponList { get; set; }
        /// <summary>
        /// 开通状态-销售视角
        /// </summary>
        public EnumSalesServiceState SalesServiceState { get; set; }
        /// <summary>
        /// 开通状态名称-销售视角
        /// </summary>
        public string SalesServiceStateName { get; set; }
        /// <summary>
        /// 客户Id-准确版
        /// </summary>
        public string CustomerId { get; set; }
        /// <summary>
        /// 能否使用个人服务天数
        /// </summary>
        public bool CouldUsePriServDays { get; set; }
        /// <summary>
        /// 当前服务已绑定的到账信息Ids
        /// </summary>
        public List<string> LinkedReceiptRegisterIds { get; set; }
        /// <summary>
        /// 在服情况
        /// </summary>
        public EnumInServiceState? InSerivceState { get; set; }
        /// <summary>
        /// 在服情况
        /// </summary>
        public string InSerivceStateName { get; set; }
        /// <summary>
        /// 合同保护情况
        /// </summary>
        public EnumProtectionDeadlineState? ProtectionDeadlineState { get; set; }
        /// <summary>
        /// 合同保护情况
        /// </summary>
        public string ProtectionDeadlineStateName { get; set; }

        /// <summary>
        /// 服务变更时服务月份可选的上限增量（需要再叠加OpeningMonth计算出下拉框的最大值）
        /// </summary>
        public int ServiceMonthAddUpperLimit4Update { get; set; }
        /// <summary>
        /// 能否申请服务(移动端客户服务产品进入)
        /// </summary>
        public bool CouldApplyService { get; set; }
        /// <summary>
        /// 能否变更服务(移动端客户服务产品进入)
        /// </summary>
        public bool CouldUpdateService { get; set; }
        /// <summary>
        /// 产品类型
        /// </summary>
        public int ProductType { get; set; }
        /// <summary>
        /// 加急处理状态
        /// </summary>
        public bool IsUrgent { get; set; }
        /// <summary>
        /// 能否加急处理
        /// </summary>
        public bool CouldUrgent { get; set; }
        /// <summary>
        /// 服务变更初审能否允许修改(Json字符串集合)
        /// </summary>
        public JObject? AllowChangeProperty { get; set; }
        /// <summary>
        /// 在服的环球搜服务信息
        /// </summary>
        public Db_crm_contract_serviceinfo_globalsearch? GlobalSearchInfo { get; set; }
    }
    public class GetFreeGtisServiceInfo_Out
    {
        /// <summary>
        /// 服务周期
        /// </summary>
        public string ServiceCycle { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public EnumContractServiceOpenState? State { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public string StateName
        {
            get
            {
                if (State != null)
                    return State.GetEnumDescription();
                else
                    return null;
            }
        }
    }
    public class ApplGtisInfo_OUT
    {

        public string Id { get; set; }
        /// <summary>
        /// 合同表id
        /// </summary>           
        public string ContractId { get; set; }
        /// <summary>
        /// 产品表id
        /// </summary>           
        public string ProductId { get; set; }
        /// <summary>
        /// 合同产品信息表id
        /// </summary>           
        public string ContractProductInfoId { get; set; }
        /// <summary>
        /// 免费延长时间变更标记
        /// </summary>
        public bool IsFree { get; set; }
        /// <summary>
        /// 主账号数量
        /// </summary>           
        public int? PrimaryAccountsNum { get; set; }
        /// <summary>
        /// 子账号数量
        /// </summary>           
        public int? SubAccountsNum { get; set; }
        /// <summary>
        /// 每个账户共享人数
        /// </summary>           
        public int? SharePeopleNum { get; set; }
        /// <summary>
        /// 共享使用总数
        /// </summary>           
        public int? ShareUsageNum { get; set; }
        /// <summary>
        /// 子账号授权国家次数
        /// </summary>           
        public int? AuthorizationNum { get; set; }
        /// <summary>
        /// 服务周期-开始时间
        /// </summary>           
        public DateTime? ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务周期-结束时间
        /// </summary>           
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 零售国家
        /// </summary>           
        public int? RetailCountry { get; set; }
        /// <summary>
        /// 备注
        /// </summary>           
        public string Remark { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>           
        public string ApplicantId { get; set; }
        /// <summary>
        /// 申请时间
        /// </summary>           
        public DateTime? ApplicantDate { get; set; }
        /// <summary>
        /// 审核人
        /// </summary>           
        public string ReviewerId { get; set; }
        /// <summary>
        /// 审核时间
        /// </summary>           
        public DateTime? ReviewerDate { get; set; }
        /// <summary>
        /// 审核反馈
        /// </summary>           
        public string Feedback { get; set; }
        /// <summary>
        /// 审核状态
        /// </summary>           
        public int? State { get; set; }
        /// <summary>
        /// 处理类型
        /// </summary>           
        public int? ProcessingType { get; set; }
        /// <summary>
        /// 无效的
        /// </summary>           
        public int? IsInvalid { get; set; }
        /// <summary>
        /// 服务月份
        /// </summary>     
        public int? ServiceMonth { get; set; }
        /// <summary>
        /// 删除标识
        /// </summary>           
        public bool? Deleted { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>           
        public string CreateUser { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>           
        public DateTime? CreateDate { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>           
        public string UpdateUser { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>           
        public DateTime? UpdateDate { get; set; }
        public string ServiceCycle { get; set; }

        public List<GtisResidentCountry> ResidentCountries { get; set; }
        public List<GtisResidentCity> ResidentCitys { get; set; }

        public string RetailCountryName
        {
            get
            {
                return RetailCountry == null ? null : Dictionary.RetailCountry.First(e => e.Value == RetailCountry.ToString())?.Name;
            }
        }

        public string ApplicantName { get; set; }
        /// <summary>
        /// 审核人
        /// </summary>           
        public string ReviewerName { get; set; }
        public List<GtisRetailCountry_OUT> GtisRetailCountry { get; set; }
        public List<ApplGtisUserInfo_OUT> GtisUserInfo { get; set; }
        /// <summary>
        /// 账号信息是否发送
        /// </summary>           
        public bool SendAccount { get; set; }
        /// <summary>
        /// 账号信息发送时间
        /// </summary>           
        public DateTime? SendAccountTime { get; set; }
        /// <summary>
        /// （续约）甲方公司
        /// </summary>
        public string RenewFirstParty { get; set; }
        /// <summary>
        /// （续约）客户编码
        /// </summary>
        public string RenewContractNum { get; set; }
        /// <summary>
        /// 服务变更修改项目
        /// </summary>
        public List<Db_crm_contract_serviceinfo_changetype> ChangeProjectTypeList { get; set; }
        /// <summary>
        /// 优惠类型
        /// </summary>
        public EnumGtisDiscountType? DiscountType { get; set; }
        /// <summary>
        /// 优惠类型名称
        /// </summary>
        //public string? DiscountTypeName => DiscountType?.GetEnumDescription().ToString();
        /// <summary>
        /// 优惠券Id列表
        /// </summary>
        public string CouponIds { get; set; }
        /// <summary>
        /// 优惠券数量
        /// </summary>
        public int? CouponCount { get { return string.IsNullOrEmpty(CouponIds) ? 0 : CouponIds.Split(',').ToList().Count; } }
        /// <summary>
        /// 优惠后服务月份
        /// </summary>     
        public int? ServiceMonthAfterDiscount { get; set; }
        /// <summary>
        /// 优惠后服务周期-开始时间
        /// </summary>           
        public DateTime? ServiceCycleStartAfterDiscount { get; set; }
        /// <summary>
        /// 优惠后服务周期-结束时间
        /// </summary>           
        public DateTime? ServiceCycleEndAfterDiscount { get; set; }
        /// <summary>
        /// 优惠后服务周期
        /// </summary>
        public string ServiceCycleAfterDiscount { get; set; }
        /// <summary>
        /// 总延长服务天数
        /// </summary>
        public int PerlongServiceDays { get; set; } = 0;
        /// <summary>
        /// 个人服务天数
        /// </summary>
        public int PrivateServiceDays { get; set; } = 0;
        /// <summary>
        /// 超额服务天数
        /// </summary>
        public int OverServiceDays { get; set; } = 0;
        /// <summary>
        /// 赠送服务月份
        /// </summary>
        public int? ServiceAddMonth { get; set; }
        /// <summary>
        /// 环球搜账号数量(主+子)
        /// </summary>
        public int? GlobalSearchAccountCount { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 加急处理状态
        /// </summary>
        public bool IsUrgent { get; set; }
        /// <summary>
        /// 服务变更原因
        /// </summary>
        public string ChangeReasonEnums { get; set; }
        /// <summary>
        /// 服务变更原因
        /// </summary>
        public List<ApplGtisInfo_OUT_ChangeReason> ChangeReasons { get; set; }
        /// <summary>
        /// 禁用下载/导出权限
        /// </summary>      
        public bool ForbidSearchExport { get; set; }
        /// <summary>
        /// 是否开通定制报告
        /// </summary>
        public bool WordRptPermissions { get; set; }
        /// <summary>
        /// 定制报告每年总次数
        /// </summary>
        public int? WordRptMaxTimes { get; set; }
        /// <summary>
        /// 此次执行的服务开始时间
        /// </summary>
        public DateTime? GlobalSearchExecuteServiceCycleStart { get; set; }
        /// <summary>
        /// 此次执行的服务开始时间
        /// </summary>
        public DateTime? GlobalSearchExecuteServiceCycleEnd { get; set; }
        /// <summary>
        /// 此次执行的服务月份
        /// </summary>
        public int? GlobalSearchExecuteServiceMonth { get; set; }
    }
    public class ApplGtisInfo_OUT_ChangeReason
    {
        /// <summary>
        /// 变更原因枚举值
        /// </summary>
        public EnumGtisServiceChangeProject ChangeReasonEnum { get; set; }
        /// <summary>
        /// 变更原因枚举描述
        /// </summary>
        public string ChangeReasonEnumName { get; set; }
        /// <summary>
        /// 合同产品ID (新增字段)
        /// </summary>
        public string ContractProductId { get; set; }
        /// <summary>
        /// 产品名称 (新增字段)
        /// </summary>
        public string ProductName { get; set; }
    }
    public class GtisResidentCountry
    {
        public int ResidentCountry { get; set; }
        public string ResidentCountryName
        {
            get
            {
                return (ResidentCountry == null || ResidentCountry == -1 || ResidentCountry == 0) ? "" : LC_Address.CountryAndAreaCache.First(e => e.Id == ResidentCountry)?.Name;
            }
        }
    }
    public class GtisResidentCity
    {
        public int ResidentCity { get; set; }
        public string ResidentCityName
        {
            get
            {
                return (ResidentCity == null || ResidentCity == -1 || ResidentCity == 0) ? "" : LC_Address.CityCache.First(e => e.Id == ResidentCity)?.Name;
            }
        }
    }
    public class ApplGtisUserInfo_OUT
    {
        public string Id { get; set; }
        /// <summary>
        /// 申请表ID
        /// </summary>
        public string ContractProductServiceInfoGtisApplId { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>           
        public int? OpeningStatus { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public string OpeningStatusName
        {
            get
            {
                return OpeningStatus != null ? ((EnumGtisUserOpeningStatus)OpeningStatus).GetEnumDescription() : "";
            }
        }
        /// <summary>
        /// Desc:账号Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// Desc:账号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AccountNumber { get; set; }

        /// <summary>
        /// Desc:账号类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? AccountType { get; set; }
        /// <summary>
        /// 账号类型
        /// </summary>
        public string AccountTypeName
        {
            get
            {
                return AccountType != null ? ((EnumGtisAccountType)AccountType).GetEnumDescription() : "";
            }
        }
        /// <summary>
        /// Desc:每个账户共享人数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int SharePeopleNum { get; set; }

        /// <summary>
        /// Desc:子账号授权国家次数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int AuthorizationNum { get; set; }
    }
    public class GetOldContractGtisInfo_In
    {
        /// <summary>
        /// 合同Id
        /// </summary>
        public string ContractId { get; set; }
        /// <summary>
        /// 甲方公司Id
        /// </summary>
        public string FirstParty { get; set; }
        /// <summary>
        /// 客户编码
        /// </summary>
        public string ContractNum { get; set; }
    }
    public class GtisInfo_OUT
    {
        /// <summary>
        /// id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 申请ID
        /// </summary>
        public string ProductServiceInfoGtisApplId { get; set; }
        /// <summary>
        /// 账号生成方式
        /// </summary>
        public int? AccountGenerationMethod { get; set; }
        /// <summary>
        /// 账号生成方式
        /// </summary>
        public string AccountGenerationMethodName
        {
            get
            {
                return AccountGenerationMethod != null ? ((EnumGtisAccountGenerationMethod)AccountGenerationMethod).GetEnumDescription() : "";
            }
        }
        /// <summary>
        /// 客户编码
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 合同产品Id
        /// </summary>
        public string ContractProductInfoId { get; set; }
        /// <summary>
        /// 合同Id
        /// </summary>
        public string ContractId { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProductId { get; set; }
        /// <summary>
        /// 服务状态
        /// </summary>
        public int? State { get; set; }
        /// <summary>
        /// 主账号数量
        /// </summary>
        public int? PrimaryAccountsNum { get; set; }
        /// <summary>
        /// 子账号数量
        /// </summary>
        public int? SubAccountsNum { get; set; }
        /// <summary>
        /// 每个账号共享人数
        /// </summary>
        public int? SharePeopleNum { get; set; }
        /// <summary>
        /// 账号使用总数
        /// </summary>
        public int? ShareUsageNum { get; set; }
        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int? AuthorizationNum { get; set; }
        /// <summary>
        /// 服务周期-开始时间
        /// </summary>
        public DateTime? ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务周期-结束时间
        /// </summary>
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 服务月份
        /// </summary>
        public int? ServiceMonth { get; set; }
        public bool? ForbidSearchExport { get; set; }
        public bool? WordRptPermissions { get; set; }
        public int? WordRptMaxTimes { get; set; }
        public bool? IsGtisOldCustomer { get; set; }
        /// <summary>
        /// 子账号自动分配全部国家(子账号数量为1时有效)
        /// </summary>
        public bool? AllCountrySubUser { get; set; }
        public string ServiceCycle { get; set; }
        public List<GtisResidentCountry> ResidentCountries { get; set; }
        public List<GtisResidentCity> ResidentCitys { get; set; }
        public int? RetailCountry { get; set; }
        public string RetailCountryName
        {
            get
            {
                return RetailCountry == null ? null : Dictionary.RetailCountry.First(e => e.Value == RetailCountry.ToString()).Name;
            }
        }

        public string Remark { get; set; }

        public List<GtisUserInfo_OUT> GtisUserInfo { get; set; }
        /// <summary>
        /// 服务变更申请时显示的账号列表，只包括正常状态账号
        /// </summary>
        public List<GtisUserInfo_OUT> GtisUserInfo4ChangeApply { get; set; }
        public List<GtisRetailCountry_OUT> GtisRetailCountry { get; set; }
        /// <summary>
        /// Desc:账号信息是否发送
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool SendAccount { get; set; }

        /// <summary>
        /// Desc:账号信息发送时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? SendAccountTime { get; set; }
        /// <summary>
        /// 优惠券Id
        /// </summary>
        public string CouponIds { get; set; }
        /// <summary>
        /// 优惠券数量
        /// </summary>
        public int CouponCount { get; set; }
        /// <summary>
        /// 优惠类型
        /// </summary>
        public EnumGtisDiscountType? DiscountType { get; set; }
        /// <summary>
        /// 优惠类型描述
        /// </summary>
        //public string DiscountTypeName => DiscountType?.GetEnumDescription().ToString();
        /// <summary>
        /// 优惠券总数量
        /// </summary>
        public int CouponCountTotal { get; set; }
        /// <summary>
        /// 总延长服务天数
        /// </summary>
        public int PerlongServiceDays { get; set; } = 0;
        /// <summary>
        /// 个人服务天数
        /// </summary>
        public int PrivateServiceDays { get; set; } = 0;
        /// <summary>
        /// 超额服务天数
        /// </summary>
        public int OverServiceDays { get; set; } = 0;
        /// <summary>
        /// 需要发给环球搜的备注
        /// </summary>           
        public string GlobalSearchRemark { get; set; }
        /// <summary>
        /// 环球搜结算单位
        /// </summary>           
        public decimal? GlobalSearchSettlementCount { get; set; }
        /// <summary>
        /// 环球搜收款时段
        /// </summary>           
        public decimal? GlobalSearchSettlementMonth { get; set; }
        /// <summary>
        /// 环球搜账号数量(主+子)
        /// </summary>           
        public int? GlobalSearchAccountCount { get; set; }
        /// <summary>
        /// 服务变更原因
        /// </summary>
        public string ChangeReasonEnums { get; set; }
        /// <summary>
        /// 服务变更原因
        /// </summary>
        public List<ApplGtisInfo_OUT_ChangeReason> ChangeReasons { get; set; }
        /// <summary>
        /// 是否开通Crm
        /// </summary>
        public int? IsOpenCrm { get; set; }
        /// <summary>
        /// 最大Crm账户数量
        /// </summary>
        public int? MaxCrmAccount { get; set; }

        public List<MainSysUserPhone> MainSysUserPhones { get; set; }

    }
    public class MainSysUserPhone
    {
        /// <summary>
        /// 使用者Id
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string LinkMan { get; set; }
        /// <summary>
        /// 是否开通CRM
        /// </summary>
        public bool IsOpenCrm { get; set; }
    }
    public class RegisterInfo_OUT : RegisterRemarks_OUT
    {
        /// <summary>
        /// 最新登记id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 申请ID
        /// </summary>
        public string ProductServiceInfoGtisApplId { get; set; }
        /// <summary>
        /// 账号生成方式
        /// </summary>
        public int AccountGenerationMethod { get; set; }
        /// <summary>
        /// 账号生成方式
        /// </summary>
        public string AccountGenerationMethodName
        {
            get
            {
                return AccountGenerationMethod != null ? ((EnumGtisAccountGenerationMethod)AccountGenerationMethod).GetEnumDescription() : "";
            }
        }
        /// <summary>
        /// 客户编码
        /// </summary>
        public string ContractNum { get; set; }
        public string ContractProductInfoId { get; set; }
        public string ContractId { get; set; }
        public string ProductId { get; set; }

        public int PrimaryAccountsNum { get; set; }

        public int SubAccountsNum { get; set; }

        public int SharePeopleNum { get; set; }

        public int ShareUsageNum { get; set; }

        public int AuthorizationNum { get; set; }

        public DateTime? ServiceCycleStart { get; set; }

        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 服务月份
        /// </summary>
        public int? ServiceMonth { get; set; }
        /// <summary>
        /// 优惠后服务开始时间
        /// </summary>
        public DateTime? ServiceCycleStartAfterDiscount { get; set; }
        /// <summary>
        /// 优惠后服务结束时间
        /// </summary>
        public DateTime? ServiceCycleEndAfterDiscount { get; set; }
        /// <summary>
        /// 优惠后服务月份
        /// </summary>
        public int? ServiceMonthAfterDiscount { get; set; }

        public bool ForbidSearchExport { get; set; }
        public bool WordRptPermissions { get; set; }
        public int? WordRptMaxTimes { get; set; }
        public bool IsGtisOldCustomer { get; set; }
        /// <summary>
        /// 子账号自动分配全部国家(子账号数量为1时有效)
        /// </summary>
        public bool AllCountrySubUser { get; set; }
        public string ServiceCycle { get; set; }
        public List<GtisResidentCountry> ResidentCountries { get; set; }
        public List<GtisResidentCity> ResidentCitys { get; set; }
        public int? RetailCountry { get; set; }
        public string RetailCountryName
        {
            get
            {
                return RetailCountry == null ? null : Dictionary.RetailCountry.First(e => e.Value == RetailCountry.ToString()).Name;
            }
        }
        public List<GtisUserInfo_OUT> GtisUserInfo { get; set; }
        public List<GtisRetailCountry_OUT> GtisRetailCountry { get; set; }
        /// <summary>
        /// 审核记录
        /// </summary>
        public List<RegisterRemarks_OUT> RegisterRemarks { get; set; }
        /// <summary>
        /// 总延长服务天数
        /// </summary>
        public int PerlongServiceDays { get; set; } = 0;
        /// <summary>
        /// 个人服务天数
        /// </summary>
        public int PrivateServiceDays { get; set; } = 0;
        /// <summary>
        /// 超额服务天数
        /// </summary>
        public int OverServiceDays { get; set; } = 0;

        /// <summary>
        /// 需要发给环球搜的备注
        /// </summary>           
        public string GlobalSearchRemark { get; set; }
        /// <summary>
        /// 环球搜账号数量(主+子)
        /// </summary>           
        public int? GlobalSearchAccountCount { get; set; }
        /// <summary>
        /// 环球搜结算级别(主账号数量+子账号数量)
        /// </summary>
        public string GlobalSearchSettlementLevel { get; set; }
        /// <summary>
        /// 环球搜收款时段
        /// </summary>
        public decimal GlobalSearchSettlementMonth { get; set; }
        /// <summary>
        /// 环球搜此次执行的服务开始时间
        /// </summary>           
        public DateTime? GlobalSearchExecuteServiceCycleStart { get; set; }
        /// <summary>
        /// 环球搜此次执行的服务结束时间
        /// </summary>           
        public DateTime? GlobalSearchExecuteServiceCycleEnd { get; set; }
        /// <summary>
        /// 环球搜此次执行的服务月份
        /// </summary>           
        public int? GlobalSearchExecuteServiceMonth { get; set; }
        /// <summary>
        /// 是否开通Crm
        /// </summary>
        public int? IsOpenCrm { get; set; }
        /// <summary>
        /// 最大Crm账户数量
        /// </summary>
        public int? MaxCrmAccount { get; set; }
        /// <summary>
        /// 主账号使用者绑定saleswits情况
        /// </summary>
        public List<MainSysUserPhone> MainSysUserPhones { get; set; }

    }
    public class RegisterRemarks_OUT
    {
        public string Id { get; set; }
        public int? State { get; set; }
        /// <summary>
        /// Desc:登记人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RegisteredId { get; set; }
        public string RegisteredName { get; set; }
        public bool IsApplHistory { get; set; }
        /// <summary>
        /// Desc:登记时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? RegisteredDate { get; set; }

        /// <summary>
        /// Desc:初审备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string? RegisteredRemark { get; set; }

        /// <summary>
        /// Desc:复核人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string? ReviewerId { get; set; }
        public string? ReviewerName { get; set; }

        /// <summary>
        /// Desc:复核时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? ReviewerDate { get; set; }

        /// <summary>
        /// Desc:复核备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string? ReviewerRemark { get; set; }
        /// <summary>
        /// Desc:审核备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string? Remark { get; set; }
        /// <summary>
        /// 计算出来的最新更新时间，用来排序看哪个审核是最新的
        /// </summary>
        public DateTime? AuditDate { get; set; }
    }
    public class GtisUserInfo_OUT
    {
        public string Id { get; set; }
        /// <summary>
        /// 合同服务信息环球慧思表id
        /// </summary>
        public string ContractServiceInfoGtisId { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>           
        public int? OpeningStatus { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public string OpeningStatusName
        {
            get
            {
                return OpeningStatus != null ? ((EnumGtisUserOpeningStatus)OpeningStatus).GetEnumDescription() : "";
            }
        }
        /// <summary>
        /// 账号Id
        /// </summary>           
        public string UserId { get; set; }
        /// <summary>
        /// 账号
        /// </summary>           
        public string AccountNumber { get; set; }
        /// <summary>
        /// 账号类型
        /// </summary>           
        public int? AccountType { get; set; }
        /// <summary>
        /// 账号类型
        /// </summary>
        public string AccountTypeName
        {
            get
            {
                return AccountType != null ? ((EnumGtisAccountType)AccountType).GetEnumDescription() : "";
            }
        }
        /// <summary>
        /// 每个账户共享人数
        /// </summary>           
        public int SharePeopleNum { get; set; }
        /// <summary>
        /// 子账号授权国家次数
        /// </summary>           
        public int AuthorizationNum { get; set; }
    }
    public class GtisRetailCountry_OUT
    {
        public string Id { get; set; }
        /// <summary>
        /// SID
        /// </summary>           
        public int Sid { get; set; }
        /// <summary>
        /// 国家
        /// </summary>           
        public string SidName { get; set; }
        /// <summary>
        /// 归属国家ID
        /// </summary>
        public int BelongToSid { get; set; }
    }
    public class GtisChangeItems
    {
        public string ApplyId { get; set; }
        public string PropKey { get; set; }
        public string PropName { get; set; }
        public bool IsArray { get; set; }
        public string ChangeValue { get; set; }
        public string OriValue { get; set; }
    }
    public class DBChangeItems
    {
        public string PropKey { get; set; }
        public string PropName { get; set; }
        public bool IsArray { get; set; }
        public string ChangeValue { get; set; }
    }
    public class GtisUserUseLog_OUT : BM_AllUserOperateLog
    {
        public string Id { get; set; }

        public string Svcode { get; set; }
        /// <summary>
        /// 合同服务信息环球慧思表id
        /// </summary>           
        public string ContractServiceInfoGtisId { get; set; }

        /// <summary>
        /// Desc:账号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AccountNumber { get; set; }

        /// <summary>
        /// Desc:账号类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? AccountType { get; set; }

        /// <summary>
        /// Desc:账号类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AccountTypeName
        {
            get
            {
                return AccountType != null ? ((EnumGtisAccountType)AccountType).GetEnumDescription() : "";
            }
        }

        /// <summary>
        /// Desc:使用者
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string PhoneUser { get; set; }

        /// <summary>
        /// Desc:使用者数量
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? PhoneUserNum { get; set; }
        /// <summary>
        /// 所有使用者
        /// </summary>
        public List<BM_GtisOpe_UserPhoneInfo> AllPhoneUserInfo { get; set; }


        /// <summary>
        /// 账户状态/使用者状态描述()
        /// </summary>
        public string StateDesc { get; set; }
    }

    public class GtisAllUserUseLog_OUT : BM_AllUserOperateLog
    {
        /// <summary>
        /// 合同服务信息环球慧思表id
        /// </summary>           
        public string ContractNum { get; set; }

        /// <summary>
        /// Desc:客户ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerId
        {
            get; set;
        }
        /// <summary>
        /// Desc:客户名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CustomerName { get; set; }

        /// <summary>
        /// Desc:gtis到期日
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string GtisServiceEndDate { get; set; }

        /// <summary>
        /// Desc:applID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ApplId { get; set; }

        /// <summary>
        /// 账户状态/使用者状态描述()
        /// </summary>
        public string StateDesc { get; set; }

    }


    public class GtisUserLog_IN : ApiTableIn
    {
        [Required(ErrorMessage = "必须填写id")]
        public string ApplId { get; set; }
    }
    public class GtisUserMonthLog_IN : ApiTableIn
    {
        [Required(ErrorMessage = "必须填写id")]
        public string GtisUserId { get; set; }
    }
    public class GtisUserMonthLog_OUT : BM_OneUserOperateLogByMonth
    {
        public string Id { get; set; }

        /// <summary>
        /// 合同服务信息环球慧思表id
        /// </summary>           
        public string ContractServiceInfoGtisId { get; set; }

        /// <summary>
        /// Desc:账号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AccountNumber { get; set; }

        /// <summary>
        /// Desc:账号类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? AccountType { get; set; }

        /// <summary>
        /// Desc:账号类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AccountTypeName
        {
            get
            {
                return AccountType != null ? ((EnumGtisAccountType)AccountType).GetEnumDescription() : "";
            }
        }
    }
    public class GtisUserLogDetail_IN
    {
        /// <summary>
        /// id（必填）
        /// </summary>
        [Required(ErrorMessage = "必须填写id")]
        public string GtisUserId { get; set; }
        /// <summary>
        /// 操作类型，null：查询所有类型
        /// </summary>
        public BM_GtisOpe_LogOpeType? OpeType { get; set; }
        /// <summary>
        /// 统计年份，0:不使用
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 统计月份，0：不使用
        /// </summary>
        public int Months { get; set; }
        /// <summary>
        /// 页码
        /// </summary>
        [Required(ErrorMessage = "必须填写页码")]
        public int PageNumber { get; set; }

        /// <summary>
        /// 每页条数
        /// </summary>
        [Required(ErrorMessage = "必须填写每页条数")]
        public int PageSize { get; set; }

    }
    /// <summary>
    /// 导出日志详情的参数
    /// </summary>
    public class GtisUserDownloadLogDetail_IN
    {
        /// <summary>
        /// 账户id（必填）
        /// </summary>
        [Required(ErrorMessage = "必须填写账户id")]
        public string GtisUserId { get; set; }

        /// <summary>
        /// 使用者id(查询某个使用者时，填写)
        /// </summary>
        public string PhoneID { get; set; }

        /// <summary>
        /// 统计年份，0:不使用
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 统计月份，0：不使用
        /// </summary>
        public int Months { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        [Required(ErrorMessage = "必须填写页码")]
        public int PageNumber { get; set; }

        /// <summary>
        /// 每页条数
        /// </summary>
        [Required(ErrorMessage = "必须填写每页条数")]
        public int PageSize { get; set; }
    }

    public class GetG5PhoneList_In : ApiTableIn
    {

    }

    public class SetContractServiceInfoGtisUserAll_IN
    {
        /// <summary>
        /// 合同服务信息环球慧思表id
        /// </summary>
        public string ContractServiceInfoGtisId { get; set; }
        /// <summary>
        /// 开通/停用
        /// </summary>
        public bool Open { get; set; }
    }
    public class SetContractServiceInfoGtisUser_IN
    {
        /// <summary>
        /// 合同服务信息环球慧思表id
        /// </summary>
        public string ContractServiceInfoGtisId { get; set; }
        public List<SetGtisUserStatus_IN> setGtisUserStatuses { get; set; }
    }
    public class SetGtisUserStatus_IN
    {
        /// <summary>
        /// gtis userid
        /// </summary>
        public string Id { get; set; }
        public bool Open { get; set; }

    }

    public class ContractServiceInfoTemporaryAccount_IN
    {
        /// <summary>
        /// 关联公司
        /// </summary>
        [Required(ErrorMessage = "关联公司不可为空")]
        public string CustomerId { get; set; }

        /// <summary>
        /// 开通账号数
        /// </summary>
        [Required(ErrorMessage = "开通账号数不可为空")]
        public int AccountsOpenedNum { get; set; }

        /// <summary>
        /// 启用日期
        /// </summary>
        [Required(ErrorMessage = "启用日期不可为空")]
        public DateTime OpenedDate { get; set; }

        /// <summary>
        /// 使用时长
        /// </summary>
        [Required(ErrorMessage = "使用时长不可为空")]
        public int Duration { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        //[Required(ErrorMessage = "备注不可为空")]
        public string Remark { get; set; }

        /// <summary>
        /// 是否开通子账号
        /// </summary>           
        [Required(ErrorMessage = "是否开通子账号")]
        public int IsOpenSubAccount { get; set; }

        /// <summary>
        /// 是否开通环球搜
        /// </summary>       
        [Required(ErrorMessage = "是否开通环球搜")]
        public int IsOpenGlobalSearch { get; set; }

        /// <summary>
        /// 临时账号_国家
        /// </summary>
        [Required(ErrorMessage = "临时账号国家不可为空")]
        public List<AccountCountry> AccountCountry { get; set; }
    }

    public class AccountCountry
    {
        /// <summary>
        /// 国家id
        /// </summary>
        [Required(ErrorMessage = "国家id不可为空")]
        public int Sid { get; set; }
    }

    public class AccountCountry_Out
    {
        public string Id { get; set; }

        public string Name { get; set; }

        /// <summary>
        /// 国家id
        /// </summary>
        public List<Country_Out> Countrys { get; set; }
    }

    public class Country_Out
    {
        /// <summary>
        /// 国家id
        /// </summary>
        public int Sid { get; set; }

        public string CountryName { get; set; }

    }

    public class DelayContractServiceInfoTemporaryAccountList_Out
    {
        public string Id { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string AccountNumber { get; set; }

        /// <summary>
        /// 账号类型
        /// </summary>
        public int? TemporaryAccountType { get; set; }

        /// <summary>
        /// 账号类型
        /// </summary>
        public string TemporaryAccountTypeName
        {
            get
            {
                return Dictionary.TemporaryAccountType.First(e => e.Value == TemporaryAccountType.ToString()).Name;
            }
        }

        /// <summary>
        /// 状态
        /// </summary>
        public int? TemporaryAccountUserState { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string TemporaryAccountUserStateName
        {
            get
            {
                return Dictionary.TemporaryAccountUserState.First(e => e.Value == TemporaryAccountUserState.ToString()).Name;
            }
        }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 停止时间
        /// </summary>
        public DateTime EndTime { get; set; }

    }

    public class SearchContractServiceInfoTemporaryAccountList_In : ApiTableIn
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskNum { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 组织架构(创建人所属组织)
        /// </summary>
        public string OrganizationId { get; set; }

        /// <summary>
        /// G5账号状态： 0= 全部账号，1=今日有效，2=全部有效，3=已过期 默认 全部有效
        /// </summary>
        public int StatePara { get; set; } = 0;
        /// <summary>
        /// 账号状态
        /// </summary>
        public int? State { get; set; }

        /// <summary>
        /// 使用时长
        /// </summary>
        public int? Duration { get; set; }

        /// <summary>
        /// 开通账号数
        /// </summary>
        public int? AccountsOpenedNum { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public List<string> CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDateStart { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDateEnd { get; set; }

        /// <summary>
        /// 快捷搜索选项
        /// </summary>
        public EnumQueryListType enumQueryListType { get; set; }

    }

    public class SearchContractServiceInfoTemporaryAccountList_Out
    {
        /// <summary>
        /// 主键
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskNum { get; set; }

        /// <summary>
        /// 组织架构
        /// </summary>
        public string OrgName { get; set; }

        /// <summary>
        /// 关联客户
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 账号状态
        /// </summary>
        public int? TemporaryAccountState { get; set; }

        /// <summary>
        /// 账号状态
        /// </summary>
        public string TemporaryAccountStateName { get; set; }
        //{
        //    get
        //    {
        //        return Dictionary.TemporaryAccountState.First(e => e.Value == TemporaryAccountState.ToString()).Name;
        //    }
        //}

        /// <summary>
        /// 开通账号数量
        /// </summary>
        public int AccountsOpenedNum { get; set; }

        /// <summary>
        /// 开通账号数量
        /// </summary>
        public string AccountsOpenedNumName
        {
            get
            {
                return Dictionary.AccountsOpenedNum.First(e => e.Value == AccountsOpenedNum.ToString()).Name;
            }
        }

        /// <summary>
        /// 使用时长
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 使用时长
        /// </summary>
        public string DurationName
        {
            get
            {
                return Dictionary.Duration.First(e => e.Value == Duration.ToString()).Name;
            }
        }

        /// <summary>
        /// 创建人
        /// </summary>
        public string ApplicantId { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string ApplicantName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        private string _CreateDate;
        public string CreateDate
        {
            //get; set;
            get { return Convert.ToDateTime(_CreateDate).ToString("yyyy-MM-dd HH:mm"); }
            set { _CreateDate = value; }
        }

        /// <summary>
        /// 是否开通子账号
        /// </summary>
        public int IsOpenSubAccount { get; set; }

        /// <summary>
        /// 是否开通环球搜
        /// </summary>
        public int IsOpenGlobalSearch { get; set; }

        /// <summary>
        /// 账号数
        /// </summary>
        public int AccountNums { get; set; }
    }

    public class ContractServiceInfoTemporaryAccount_Out
    {
        /// <summary>
        /// 主键
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskNum { get; set; }

        /// <summary>
        /// 关联公司
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 开通账号数
        /// </summary>
        public int AccountsOpenedNum { get; set; }

        /// <summary>
        /// 开通账号数量
        /// </summary>
        public string AccountsOpenedNumName
        {
            get
            {
                return Dictionary.AccountsOpenedNum.First(e => e.Value == AccountsOpenedNum.ToString()).Name;
            }
        }

        /// <summary>
        /// 启用日期
        /// </summary>
        public string OpenedDate { get; set; }

        /// <summary>
        /// 使用时长
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 使用时长
        /// </summary>
        public string DurationName
        {
            get
            {
                return Dictionary.Duration.First(e => e.Value == Duration.ToString()).Name;
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int State { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string StateName { get; set; }
        //{
        //    get
        //    {
        //        return Dictionary.TemporaryAccountState.First(e => e.Value == State.ToString()).Name;
        //    }
        //}

        /// <summary>
        /// 创建人所属组织架构id
        /// </summary>
        public string OrganizationId { get; set; }

        /// <summary>
        /// 组织名称
        /// </summary>
        public string OrgName { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUserName { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public List<TemporaryAccountCountry> AccountCountry { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public List<TemporaryAccountUser> AccountUser { get; set; }

        /// <summary>
        /// 是否开通子账号
        /// </summary>
        public int IsOpenSubAccount { get; set; }

        /// <summary>
        /// 是否开通环球搜
        /// </summary>
        public int IsOpenGlobalSearch { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        private string _CreateDate;
        public string CreateDate
        {
            //get; set;
            get { return Convert.ToDateTime(_CreateDate).ToString("yyyy-MM-dd HH:mm"); }
            set { _CreateDate = value; }
        }
    }

    public class TemporaryAccountCountry
    {
        /// <summary>
        /// 合同服务信息_临时账号_国家表Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 合同服务信息_临时账号Id
        /// </summary>
        public string ServiceInfoTemporaryAccountID { get; set; }

        /// <summary>
        /// 国家id
        /// </summary>
        public int? Sid { get; set; }

        /// <summary>
        /// 国家id
        /// </summary>
        public string SidName { get; set; }
    }

    public class TemporaryAccountUser
    {
        /// <summary>
        /// 合同服务信息_临时账号_用户表Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 合同服务信息_临时账号Id
        /// </summary>
        public string ServiceInfoTemporaryAccountID { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string AccountNumber { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string PassWord { get; set; }

        /// <summary>
        /// 账号类型
        /// </summary>
        public int? TemporaryAccountType { get; set; }

        /// <summary>
        /// 账号类型
        /// </summary>
        public string TemporaryAccountTypeName
        {
            get
            {
                return Dictionary.TemporaryAccountType.First(e => e.Value == TemporaryAccountType.ToString()).Name;
            }
        }

        /// <summary>
        /// 状态
        /// </summary>
        public int? TemporaryAccountUserState { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string TemporaryAccountUserStateName { get; set; }
        //{
        //    get
        //    {
        //        return Dictionary.TemporaryAccountUserState.First(e => e.Value == TemporaryAccountUserState.ToString()).Name;
        //    }
        //}

        /// <summary>
        /// 开始时间
        /// </summary>
        public string StartTime { get; set; }

        /// <summary>
        /// 停止时间
        /// </summary>
        public string EndTime { get; set; }

        /// <summary>
        /// 登录IP
        /// </summary>
        public string LoginIP { get; set; }

        /// <summary>
        /// 剩余使用时间
        /// </summary>
        public string RemainingUsageTime { get; set; }

        /// <summary>
        /// 延期次数
        /// </summary>
        public int? DelayTimes { get; set; }

        /// <summary>
        /// gtis用户ID
        /// </summary>           
        public string SysUserID { get; set; }
    }

    public class TemporaryAccountUserLogDetail_IN
    {
        /// <summary>
        /// id（必填）
        /// </summary>
        [Required(ErrorMessage = "必须填写id")]
        public string SysUserID { get; set; }
        /// <summary>
        /// 操作类型，null：查询所有类型
        /// </summary>
        public BM_GtisOpe_LogOpeType? OpeType { get; set; }
        /// <summary>
        /// 统计年份，0:不使用
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 统计月份，0：不使用
        /// </summary>
        public int Months { get; set; }
        /// <summary>
        /// 页码
        /// </summary>
        [Required(ErrorMessage = "必须填写页码")]
        public int PageNumber { get; set; }

        /// <summary>
        /// 每页条数
        /// </summary>
        [Required(ErrorMessage = "必须填写每页条数")]
        public int PageSize { get; set; }

    }


    public class DemoUserLogDetail_IN : TemporaryAccountUserLogDetail_IN
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }
    }
    public class GtisOldUser_OUT
    {
        public int index { get; set; }
        public string Id { get; set; }
        /// <summary>
        /// Desc:合同服务信息环球慧思表id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ContractServiceInfoGtisId { get; set; }

        /// <summary>
        /// Desc:账号Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// Desc:账号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AccountNumber { get; set; }

        /// <summary>
        /// Desc:账号类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? AccountType { get; set; }

        /// <summary>
        /// Desc:开通状态
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? OpeningStatus { get; set; }

        /// <summary>
        /// Desc:启用日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Desc:停用日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Desc:登录IP
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string LoginIP { get; set; }

        /// <summary>
        /// Desc:最近登录时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? LastLoginTime { get; set; }
        /// <summary>
        /// Desc:每个账户共享人数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? SharePeopleNum { get; set; }
        /// <summary>
        /// Desc:使用者
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string PhoneUser { get; set; }

        /// <summary>
        /// Desc:使用者数量
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? PhoneUserNum { get; set; }

        /// <summary>
        /// Desc:子账号授权国家次数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? AuthorizationNum { get; set; }

        /// <summary>
        /// Desc:是否执行成功
        /// Default:0
        /// Nullable:True
        /// </summary>           
        public int? IsProcessed { get; set; }

        /// <summary>
        /// Desc:最后执行时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? ProcessedTime { get; set; }

        /// <summary>
        /// Desc:环球搜码
        /// Default:
        /// Nullable:True
        /// </summary>        
        public string GlobalSearchCode { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }
    }

    public class TemporaryAccountUserExportLogDetail_IN
    {
        /// <summary>
        /// 账户id（必填）
        /// </summary>
        [Required(ErrorMessage = "必须填写账户id")]
        public string SysUserID { get; set; }

        /// <summary>
        /// 使用者id(查询某个使用者时，填写)
        /// </summary>
        public string PhoneID { get; set; }

        /// <summary>
        /// 统计年份，0:不使用
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 统计月份，0：不使用
        /// </summary>
        public int Months { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        [Required(ErrorMessage = "必须填写页码")]
        public int PageNumber { get; set; }

        /// <summary>
        /// 每页条数
        /// </summary>
        [Required(ErrorMessage = "必须填写每页条数")]
        public int PageSize { get; set; }
    }
    public class ReviewContractProductServiceInfoGtisAppl_IN
    {
        /// <summary>
        /// 合同服务信息环球慧思申请表id
        /// </summary>
        [Required(ErrorMessage = "合同服务信息环球慧思申请表id不可为空")]
        public string ProductServiceInfoGtisApplId { get; set; }
        /// <summary>
        /// 审核反馈
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 本次备注
        /// </summary>
        public string CurrentRemark { get; set; }

        /// <summary>
        /// 状态(12.5 true：开通、false：拒绝)
        /// </summary>
        [Required(ErrorMessage = "复核结果不可为空")]
        public bool Pass { get; set; }

        /// <summary>
        /// 发给环球搜的备注
        /// </summary>
        public string GlobalSearchRemark { get; set; }

    }

    public class Tscountry
    {
        public string Id { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>           
        public string Name { get; set; }

    }

    public class GetOldContractGtisInfoByCompanyIdList_Out
    {
        /// <summary>
        /// 合同公司Id
        /// </summary>
        public string CompanyId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 客户编码
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CreateDate { get; set; }
        /// <summary>
        /// Gtis主账号
        /// </summary>
        public string UserId { get; set; }

    }

    public class HistoryGtisServiceInfo
    {
        /// <summary>
        /// 服务Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 合同Id
        /// </summary>
        public string ContractId { get; set; }
        /// <summary>
        /// 客户编码
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 主账号数量
        /// </summary>
        public int? PrimaryAccountsNum { get; set; }
        /// <summary>
        /// 子账号数量
        /// </summary>
        public int? SubAccountsNum { get; set; }
        /// <summary>
        /// 每个账号绑定人数
        /// </summary>
        public int? SharePeopleNum { get; set; }
        /// <summary>
        /// 绑定使用总数
        /// </summary>
        public int? ShareUsageNum { get; set; }
        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int? AuthorizationNum { get; set; }
        /// <summary>
        /// 服务周期
        /// </summary>
        public string ServiceCycle { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 优惠券数量
        /// </summary>
        public int CouponCount { get; set; }
        /// <summary>
        /// 优惠券
        /// </summary>
        public string CouponIds { get; set; }
        /// <summary>
        /// 赠送服务时长
        /// </summary>
        public int AddFreeGitsServiceMonth { get; set; }
        /// <summary>
        /// 需要发给环球搜的备注
        /// </summary>           
        public string GlobalSearchRemark { get; set; }
        /// <summary>
        /// 环球搜结算单位
        /// </summary>           
        public decimal? GlobalSearchSettlementCount { get; set; }
        /// <summary>
        /// 环球搜账号数量(主+子)
        /// </summary>           
        public int? GlobalSearchAccountCount { get; set; }
        /// <summary>
        /// 服务变更原因
        /// </summary>
        public List<ApplGtisInfo_OUT_ChangeReason> ChangeReasons { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 是否开通Crm
        /// </summary>
        public int? IsOpenCrm { get; set; }
    }

    public class HistoryGtisServiceInfo_Mid : HistoryGtisServiceInfo
    {
        /// <summary>
        /// 旧合同客户编码
        /// </summary>
        public string OldContractNum { get; set; }
        /// <summary>
        /// 合同类型
        /// </summary>
        public int? ProcessingType { get; set; }
        /// <summary>
        /// 合同Id
        /// </summary>
        public string ContractId { get; set; }
        /// <summary>
        /// 申请Id
        /// </summary>
        public string ApplyId { get; set; }
        /// <summary>
        /// 服务变更原因
        /// </summary>
        public string ChangeReasonEnums { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProductId { get; set; }

    }

    public class TempContractReleaseColumn
    {
        /// <summary>
        /// 出担人ID
        /// </summary>
        public string Issuer { get; set; }
        /// <summary>
        /// 合同编号/svcode
        /// </summary>
        public string ContractNum { get; set; }
    }

    public class GtisOldInfo_OUT
    {
        /// <summary>
        /// 服务数据主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 客户编码
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProductId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 主账号数量
        /// </summary>
        public int? PrimaryAccountsNum { get; set; }
        /// <summary>
        /// 子账号数量
        /// </summary>
        public int? SubAccountsNum { get; set; }
        /// <summary>
        /// 每个账户绑定人数
        /// </summary>
        public int? SharePeopleNum { get; set; }
        /// <summary>
        /// 服务周期-开始
        /// </summary>
        public DateTime? ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务周期-结束
        /// </summary>
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 服务月份
        /// </summary>
        public int? ServiceMonth { get; set; }
        /// <summary>
        /// 服务周期
        /// </summary>
        public string ServiceCycle { get; set; }
        /// <summary>
        /// 被续约的甲方公司
        /// </summary>
        public string RenewFirstParty { get; set; }
        /// <summary>
        /// 能否使用个人服务天数
        /// </summary>
        public bool CouldUsePriServDays { get; set; }
    }

    public class AddUserTSCountry_In
    {
        /// <summary>
        /// 名称
        /// </summary>           
        [Required(ErrorMessage = "名称不可为空")]
        public string Name { get; set; }

        /// <summary>
        /// 备注
        /// </summary>           
        public string Remark { get; set; }

        /// <summary>
        /// 备注
        /// </summary>           
        public List<int> SID { get; set; }

    }

    public class UpdateUserTSCountry_In : AddUserTSCountry_In
    {
        /// <summary>
        /// 名称
        /// </summary>           
        [Required(ErrorMessage = "用户临时账户国家模版表Id不可为空")]
        public string Id { get; set; }

    }

    public class SearchUserTSCountryList_Out
    {
        /// <summary>
        /// 主键
        /// </summary>           
        public string Id { get; set; }

        /// <summary>
        /// 用户表id
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>           
        public string Name { get; set; }

        /// <summary>
        /// 备注
        /// </summary>           
        public string Remark { get; set; }

    }

    public class SearchUserTSCountryList_In : ApiTableIn
    {

    }

    public class GetUserTSCountryList_Out
    {
        /// <summary>
        /// 主键
        /// </summary>           
        public string Id { get; set; }

        /// <summary>
        /// 用户表id
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>           
        public string Name { get; set; }

        /// <summary>
        /// 备注
        /// </summary>           
        public string Remark { get; set; }

        /// <summary>
        /// SID
        /// </summary>
        public List<int?> SID { get; set; }

    }

    public class CustomersvCodesInfo
    {
        public string svcodes { get; set; }
        public string CompanyName { get; set; }
    }

    public class SearchServicesList4ServeManage_OUT : SearchServicesList_OUT
    {
        /// <summary>
        /// 到账情况
        /// </summary>           
        public int? IsReceipt { get; set; }
        /// <summary>
        /// 是否到账
        /// </summary>
        public string IsReceiptName { get; set; }
        /*{
            get
            {
                return IsReceipt == null ? null : Dictionary.IsReceipt.First(e => e.Value == IsReceipt.ToString()).Name;
            }
        }*/
        /// <summary>
        /// 归属月份
        /// </summary>           
        public DateTime? BelongingMonth { get; set; }
        /// <summary>
        /// 人员
        /// </summary>
        public string UserName { get; set; }
    }


    public class SearchServicesList4ServeManage_OUT_IN : SearchServicesList_IN
    {
        /// <summary>
        /// 到账情况
        /// </summary>           
        public List<int>? IsReceipt { get; set; }
        /// <summary>
        /// 归属月份-开始
        /// </summary>           
        public DateTime? BelongingMonthStart { get; set; }
        /// <summary>
        /// 归属月份-结束
        /// </summary>
        public DateTime? BelongingMonthEnd { get; set; }
        /// <summary>
        /// 人员Id
        /// </summary>
        public string UserId { get; set; }
    }


    public class SearchServiceContractList_Out
    {
        public string ContractId { get; set; }
        public string ContractName { get; set; }
        public string ContractNum { get; set; }
        public string FirstParty { get; set; }
        public EnumContractType Contracttype { get; set; }
        public DateTime? SigningDate { get; set; }
        public string? Issuer { get; set; }
        public DateTime? ArrivalDate { get; set; }
        public string ContractReceiptRegisterId { get; set; }
        public bool IsService { get; set; }
        public string IsServiceName { get; set; }
        public string ProductTypes { get; set; }
        public string ProductNames { get; set; }
        public string FirstPartyName { get; set; }
        public string UserName { get; set; }
    }
    public class SearchServiceContractSta_Out
    {
        public int ArrivalTotal { get; set; }
        public int ServiceTotal { get; set; }
        public int NotServiceTotal { get; set; }

    }
    public class SearchServiceContractList_In : ApiTableIn
    {
        /// <summary>
        /// 合同名称（模糊查询）
        /// </summary>
        public string ContractName { get; set; }

        /// <summary>
        /// 客户编码（精确匹配）
        /// </summary>
        public string ContractNum { get; set; }

        /// <summary>
        /// 到账时间查询起始范围
        /// </summary>
        public DateTime? ArrivalTimeStart { get; set; }

        /// <summary>
        /// 到账时间查询结束范围
        /// </summary>
        public DateTime? ArrivalTimeEnd { get; set; }

        /// <summary>
        /// 是否已服务
        /// </summary>
        public EnumIsService ServiceStatus { get; set; }

        /// <summary>
        /// 负责人或所属团队
        /// </summary>
        public List<string>? OrgOrUserIds { get; set; }
    }

    public class DeleteAmericaCountryData_In
    {
        /// <summary>
        /// 
        /// </summary>
        public string AccountId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<string> SysUserIds { get; set; }
    }

    public class CheckAuthorizationNumReduce_In
    {
        /// <summary>
        /// 客户编码
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int AuthorizationNum { get; set; }
    }

    public class SearchWitsApplList_In : SafeApiTableIn
    {
        /// <summary>
        /// 合同编码
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 甲方公司名称
        /// </summary>
        public string FirstPartyName { get; set; }
        /// <summary>
        /// 合同名称
        /// </summary>
        public string ContractName { get; set; }
        /// <summary>
        /// 备注-模糊查询
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public List<string> ApplicantId { get; set; }
        /// <summary>
        /// 签约类型
        /// </summary>
        public int? ContractType { get; set; }
        /// <summary>
        /// 双方盖章合同状态
        /// </summary>
        public int? StampReviewStatus { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public List<EnumContractServiceOpenState> State { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public List<int> ServiceType { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime? ServiceCycleStartBegin { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime? ServiceCycleStartOver { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDateStart { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDateEnd { get; set; }
        /// <summary>
        /// 通过时间-开始
        /// </summary>
        public DateTime? ApprovalStartDate { get; set; }
        /// <summary>
        /// 通过时间-结束
        /// </summary>
        public DateTime? ApprovalEndDate { get; set; }
        /// <summary>
        /// 查询类型：全部 今日新增 本周新增
        /// </summary>
        public EnumQueryListType EnumQueryListType { get; set; }
        /// <summary>
        /// 账号状态
        /// </summary>
        public List<int> AccountStatus { get; set; }
        /// <summary>
        /// 优惠类型
        /// </summary>
        public EnumGtisDiscountType? DiscountType { get; set; }
        /// <summary>
        /// 申请Id，应为ApplId，误写为AppleId
        /// </summary>
        public string? AppleId { get; set; }
    }

    public class SearchWitsApplList_Out
    {
        /// <summary>
        /// 申请表Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 合同表Id
        /// </summary>
        public string ContractId { get; set; }
        /// <summary>
        /// 慧思产品系列Id
        /// </summary>
        public string ContractProductInfoSeriesId { get; set; }
        /// <summary>
        /// 客户编号
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 甲方公司
        /// </summary>
        public string FirstPartyName { get; set; }
        /// <summary>
        /// 合同名称
        /// </summary>
        public string ContractName { get; set; }
        /// <summary>
        /// 签约类型
        /// </summary>
        public int? ContractType { get; set; }
        /// <summary>
        /// 签约类型
        /// </summary>
        public string ContractTypeName { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplicantId { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplicantName { get; set; }
        /// <summary>
        /// 申请日期
        /// </summary>
        public string ApplicantDate { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 申请服务周期
        /// </summary>
        public string ServiceCycle { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public EnumContractServiceOpenState State { get; set; }
        /// <summary>
        /// 开通状态-名称
        /// </summary>
        public string StateName { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public EnumProcessingType? ServiceType { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public string ServiceTypeName { get { return ServiceType == null ? "" : ServiceType.GetEnumDescription().ToString(); } }
        /// <summary>
        /// 账号数量
        /// </summary>
        public int AccountNum { get; set; }
        /// <summary>
        /// 账号状态
        /// </summary>
        public int? AccountStatus { get; set; }
        /// <summary>
        /// 账号状态
        /// </summary>
        public string AccountStatusName { get; set; }
        /// <summary>
        /// 开通人
        /// </summary>
        public string ReviewerId { get; set; }
        /// <summary>
        /// 开通人--姓名
        /// </summary>
        public string ReviewerName { get; set; }
        /// <summary>
        /// 开通时间
        /// </summary>
        public DateTime? ReviewerDate { get; set; }
        /// <summary>
        /// 合同服务信息环球慧思表id
        /// </summary>
        public string ServiceId { get; set; }
        /// <summary>
        /// 是否已经失效
        /// </summary>
        public bool? IsInvalid { get; set; }
        /// <summary>
        /// 免费延长时间变更标记
        /// </summary>
        public bool IsFree { get; set; }
        /// <summary>
        /// 申请表数据状态
        /// </summary>
        public EnumProcessStatus ApplState { get; set; }
        /// <summary>
        /// 服务表数据状态
        /// </summary>
        public EnumContractServiceState? ServiceState { get; set; }
        /// <summary>
        /// 服务周期-开始
        /// </summary>
        public DateTime? ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务周期-结束
        /// </summary>
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 是否可以赠送gtis
        /// </summary>
        public bool GTISFreeAble { get; set; }
        /// <summary>
        /// 是否可以赠送慧思学院
        /// </summary>
        public bool CollegeFreeAble { get; set; }
        /// <summary>
        /// 是否可以赠送环球搜
        /// </summary>
        public bool GlobalFreeAble { get; set; }
        /// <summary>
        /// 账号信息是否发送
        /// </summary>
        public bool SendAccount { get; set; }
        /// <summary>
        /// 账号信息发送时间
        /// </summary>
        public DateTime? SendAccountTime { get; set; }
        /// <summary>
        /// 申请备注
        /// </summary>
        public string ApplyRemark { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 客户邮箱
        /// </summary>
        public string CustomerEmail { get; set; }
        /// <summary>
        /// 是否需要今天处理
        /// </summary>
        public bool IsPendingToday { get; set; }
        /// <summary>
        /// 优惠类型
        /// </summary>
        public EnumGtisDiscountType DiscountType { get; set; }
        /// <summary>
        /// 优惠券数量
        /// </summary>
        public int CouponCount { get; set; }
        /// <summary>
        /// 优惠券Ids
        /// </summary>
        public string CouponIds { get; set; }
        /// <summary>
        /// 加急处理
        /// </summary>
        public bool IsUrgent { get; set; }
        /// <summary>
        /// 加急处理标签
        /// </summary>
        public bool IsUrgentLable { get; set; }
        /// <summary>
        /// 双方盖章合同状态
        /// </summary>
        public int? StampReviewStatus { get; set; }
        /// <summary>
        /// 双方盖章合同状态名称
        /// </summary>
        public string StampReviewStatusName { get { return StampReviewStatus == null ? "" : ((EnumStampReviewStatus)StampReviewStatus).GetEnumDescription().ToString(); } }
    }

    public class GetContractProductInfoBySeiresId_Out : Db_crm_contract_productinfo
    {
        public EnumProductType ProductType { get; set; }
    }

    public class GetWitsApplyInfo4Audit_Out_ProductInfo
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 服务周期
        /// </summary>
        public int OpeningMonths { get; set; }
        /// <summary>
        /// 首次开通月数
        /// </summary>
        public int? FirstOpeningMonths { get; set; }
        /// <summary>
        /// 产品价格
        /// </summary>           
        public string ProductPrice { get; set; }
    }

    public class GetWitsApplyInfo4Audit_Out
    {
        // 1.申请信息；2.初审登记信息；3.合同信息；4.产品信息；5.到账信息 6.在服信息
        /// <summary>
        /// 合同信息
        /// </summary>
        public ContractInfoAndReceipt_Out ContractInfo { get; set; }
        /// <summary>
        /// 产品信息
        /// </summary>
        public List<GetWitsApplyInfo4Audit_Out_ProductInfo> ProductInfo { get; set; }
        /// <summary>
        /// 申请信息（初审时查看信息）
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_ApplyInfo ApplyInfo { get; set; }
        /// <summary>
        /// 复核时查看信息(或复核驳回后初审查看的信息)
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_RegisterInfo RegisterInfo { get; set; }
        /// <summary>
        /// 合同的所有到账信息
        /// </summary>
        public List<ReceiptRegisterCollectionInfoItem_Out> ReceiptRegisterCollectionList { get; set; }
        /// <summary>
        /// 当前服务已绑定的到账信息Ids
        /// </summary>
        public List<string> LinkedReceiptRegisterIds { get; set; }
        /// <summary>
        /// 复核备注&审核反馈列表
        /// </summary>
        public List<ReviewFeedback> ReviewFeedbacks { get; set; }

        /// <summary>
        /// 在服信息
        /// </summary>
        public WitsServiceInfo WitsServiceInfo {get;set;}   


        /// <summary>
        /// 字段权限列表
        /// </summary>
        public List<FieldPermissionInfo> FieldPermissions { get; set; } = new List<FieldPermissionInfo>();

        #region 备用字段
        /*
                /// <summary>
                /// 申请表Id
                /// </summary>
                public string Id { get; set; }
                /// <summary>
                /// 合同表Id
                /// </summary>
                public string ContractId { get; set; }
                /// <summary>
                /// 产品表主键
                /// </summary>
                public string ProductId { get; set; }
                /// <summary>
                /// 合同产品信息表Id
                /// </summary>
                public string ContractProductInfoId { get; set; }
                /// <summary>
                /// 处理类型
                /// </summary>           
                public int? ProcessingType { get; set; }
                /// <summary>
                /// 主账号数量
                /// </summary>
                public int? PrimaryAccountsNum { get; set; }
                /// <summary>
                /// 子账号数量
                /// </summary>
                public int? SubAccountsNum { get; set; }
                /// <summary>
                /// 服务周期
                /// </summary>
                public DateTime? ServiceCycleStart { get; set; }
                /// <summary>
                /// 服务周期
                /// </summary>
                public DateTime? ServiceCycleEnd { get; set; }
                /// <summary>
                /// 服务月份
                /// </summary>
                public int? ServiceMonth { get; set; }
                /// <summary>
                /// 服务周期
                /// </summary>
                public string ServiceCycle { get; set; }
                /// <summary>
                /// 备注
                /// </summary>
                public string Remark { get; set; }
                /// <summary>
                /// 合同中主账号数量
                /// </summary>
                public int? ContractPrimaryAccountsNum { get; set; }
                /// <summary>
                /// 合同中子账号数量
                /// </summary>
                public int? ContractSubAccountsNum { get; set; }
                /// <summary>
                /// 复核备注&审核反馈列表
                /// </summary>
                public List<ReviewFeedback> ReviewFeedbacks { get; set; }
                /// <summary>
                /// 赠送标记
                /// </summary>
                public bool IsFree { get; set; }
                /// <summary>
                /// 当前合同对应gtis账号及环球搜编码
                /// </summary>
                public List<GetContractServiceInfoApplyInfoGlobalSearchByApplId_Out_CurContractGTISUserInfo> CurContractGTISUserList { get; set; } = new();
                /// <summary>
                /// 原合同的环球搜服务开通的编码
                /// </summary>
                public List<GetContractServiceInfoApplyInfoGlobalSearchByApplId_Out_OldContractGlobalSearchUserInfo> OldContractGlobalSearchUserList { get; set; }
                /// <summary>
                /// 当前合同申请的环球搜编码个数
                /// </summary>
                public int? CurContractGlobalSearchAccountTotalNum { get; set; }
                /// <summary>
                /// gtis账号生成时是否使用原有
                /// </summary>
                public bool IsGtisUseOrgAccount { get; set; } = false;
                /// <summary>
                /// 能否增减Gtis账号
                /// </summary>
                public bool CouldChangeGtisAccountNum { get; set; } = false;
                /// <summary>
                /// 续约合同环球搜账号是否小于原合同
                /// </summary>
                public bool IsGlobalSearchAccountNumReduce { get; set; } = false;
                /// <summary>
                /// 是否存在Gtis服务
                /// </summary>
                public bool IsHaveGtisService { get; set; } = false;
                /// <summary>
                /// Gtis备注
                /// </summary>
                public string GtisRemark { get; set; }
                /// <summary>
                /// 环球搜是否是赠送的
                /// </summary>
                public bool IsGlobalSearchFree { get; set; }
                /// <summary>
                /// 被续约合同的环球搜服务信息
                /// </summary>
                public GetContractServiceInfoApplyInfoGlobalSearchByApplId_Out_OldServiceInfo OldServiceInfo { get; set; }
                /// <summary>
                /// 被续约的甲方公司
                /// </summary>
                public string OldFirstParty { get; set; }
                /// <summary>
                /// 被续约的客户编码
                /// </summary>
                public string RenewContractNum { get; set; }
                /// <summary>
                /// 到账备注列表
                /// </summary>
                public List<string> ReceiptRemarks { get; set; }
                /// <summary>
                /// 历史服务信息
                /// </summary>
                public List<HistoryGlobalsearchServiceInfo> HistoryGlobalSearchServiceList { get; set; }
                /// <summary>
                /// 结算级别(主账号数量+子账号数量)
                /// </summary>      
                public string SettlementLevel { get; set; }
                /// <summary>
                /// 结算月份
                /// </summary>     
                public decimal? SettlementMonth { get; set; }
                /// <summary>
                /// 当前服务已绑定的到账信息Ids
                /// </summary>
                public List<string> LinkedReceiptRegisterIds { get; set; }
                /// <summary>
                /// 当前合同在服数据信息
                /// </summary>
                public Db_crm_contract_serviceinfo_globalsearch? OnServiceInfo { get; set; }*/
        #endregion
    }

    #region Wits初审时查看的申请信息
    public class GetWitsApplyInfo4Audit_Out_ApplyInfo
    {
        /// <summary>
        /// 申请表Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplicantId { get; set; }
        /// <summary>
        /// 申请人姓名
        /// </summary>
        public string ApplicantName { get; set; }
        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime ApplicantDate { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public EnumContractServiceOpenState State { get; set; }
        /// <summary>
        /// 是否加急
        /// </summary>
        public bool IsUrgent { get; set; }
        /// <summary>
        /// 共享使用总数
        /// </summary>
        public int ShareUsageNum { get; set; }
        /// <summary>
        /// 账号生成方式
        /// </summary>
        public EnumGtisAccountGenerationMethod AccountGenerationMethod { get; set; }
        /// <summary>
        /// 延长的总服务天数
        /// </summary>
        public int PerlongServiceDays { get; set; }
        /// <summary>
        /// 个人服务天数
        /// </summary>
        public int PrivateServiceDays { get; set; }
        /// <summary>
        /// 续约服务券使用数量
        /// </summary>
        public int CouponCounts { get; set; }
        /// <summary>
        /// 超额服务天数
        /// </summary>
        public int OverServiceDays { get; set; }
        /// <summary>
        /// 客户经理申请备注
        /// </summary>
        public string ApplyRemark { get; set; }
        /// <summary>
        /// Gtis备注
        /// </summary>
        public string GtisRemark { get; set; }
        /// <summary>
        /// Gtis服务申请内容
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_ApplyInfo_Gtis GtisInfo { get; set; }
        /// <summary>
        /// 环球搜服务申请内容
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_ApplyInfo_GlobalSearch GlobalSearchInfo { get; set; }
        /// <summary>
        /// 慧思学院服务申请内容
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_ApplyInfo_College CollegeInfo { get; set; }
        /// <summary>
        /// 慧销云服务申请内容
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_ApplyInfo_SalesWits SalesWitsInfo { get; set; }
        /// <summary>
        /// 账号列表
        /// </summary>
        public List<GetWitsApplyInfo4Audit_Out_ApplyInfo_UserList> UserList { get; set; }
        /// <summary>
        /// 账号可选权限列表
        /// </summary>
        public List<EnumGtisSeriesPermission> GtisAccountPermissionList { get; set; } = new();
        /// <summary>
        /// 服务变更原因列表
        /// </summary>
        public List<EnumGtisServiceChangeProject> ChangeReasons { get; set; } = new();
        /// <summary>
        /// 服务变更原因描述列表
        /// </summary>
        public List<string> ChangeReasonNames { get; set; } = new();
        /// <summary>
        /// 原Wits服务Id
        /// </summary>
        public string OriWitsServeId { get; set; }
        /// <summary>
        /// 原服务的客户编码
        /// </summary>
        public string OriContractNum { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_ApplyInfo_Replenish : GetWitsApplyInfo4Audit_Out_ApplyInfo
    {
        /// <summary>
        /// 优惠券实体Ids
        /// </summary>           
        public string CounponDetailIds { get; set; }
        /// <summary>
        /// 是否申请了Gtis服务
        /// </summary>
        public bool IsGtisApply { get; set; }
        /// <summary>
        /// 是否申请了环球搜服务
        /// </summary>
        public bool IsGlobalSearchApply { get; set; }
        /// <summary>
        /// 是否申请了慧思学院服务
        /// </summary>
        public bool IsCollegeApply { get; set; }
        /// <summary>
        /// 是否申请了SalesWits服务
        /// </summary>
        public bool IsSalesWitsApply { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_ApplyInfo_Gtis
    {
        /// <summary>
        /// 申请表主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProducttId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 产品类型
        /// </summary>
        public EnumProductType ProductType { get; set; }
        /// <summary>
        /// 子账号数量
        /// </summary>
        public int SubAccountNum { get; set; }
        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int AuthorizationNum { get; set; }
        /// <summary>
        /// 禁止下载/导出
        /// </summary>
        public bool ForbidSearchExport { get; set; }
        /// <summary>
        /// 是否拥有定制报告权限
        /// </summary>
        public bool WordRptPermissions { get; set; }
        /// <summary>
        /// 定制报告每年总次数
        /// </summary>
        public int WordRptMaxTimes { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime ServiceCycleEnd { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int ServiceMonth { get; set; }
        /// <summary>
        /// 添加零售国家（1-添加；2-未添加）
        /// </summary>
        public int RetailCountry { get; set; }
        /// <summary>
        /// 零售国家列表
        /// </summary>
        public List<GtisRetailCountry_OUT> RetailCountryList { get; set; } = new();
    }
    public class GetWitsApplyInfo4Audit_Out_ApplyInfo_GlobalSearch
    {
        /// <summary>
        /// 申请表主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProducttId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 账号数量
        /// </summary>
        public int AccountNum { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime ServiceCycleEnd { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int ServiceMonth { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_ApplyInfo_College
    {
        /// <summary>
        /// 申请表主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProducttId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 账号数量
        /// </summary>
        public int AccountNum { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime ServiceCycleEnd { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int ServiceMonth { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_ApplyInfo_SalesWits
    {
        /// <summary>
        /// 申请表主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProducttId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 账号数量
        /// </summary>
        public int AccountNum { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime ServiceCycleEnd { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int ServiceMonth { get; set; }
        /// <summary>
        /// 充值金额
        /// </summary>
        public decimal SalesWitsAddCredit { get; set; }
        /// <summary>
        /// 赠送邮件数量
        /// </summary>
        public int EmailCount { get; set; }
        /// <summary>
        /// 赠送Token数量
        /// </summary>
        public int TokenCount { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_ApplyInfo_UserList
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// Gtis系统UserId
        /// </summary>
        public string SysUserId { get; set; }
        /// <summary>
        /// 账号
        /// </summary>
        public string AccountNumber { get; set; }
        /// <summary>
        /// 账号类型
        /// </summary>
        public int AccountType { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public int OpeningStatus { get; set; }
        /// <summary>
        /// 账户共享人数
        /// </summary>
        public int SharePeopleNum { get; set; }
        /// <summary>
        /// Gtis权限
        /// </summary>
        public bool GtisPermission { get; set; }
        /// <summary>
        /// Vip零售权限
        /// </summary>
        public bool VipPermission { get; set; }
        /// <summary>
        /// 环球搜权限
        /// </summary>
        public bool GlobalSearchPermission { get; set; }
        /// <summary>
        /// 慧思学院权限
        /// </summary>
        public bool CollegePermission { get; set; }
        /// <summary>
        /// SalesWits权限
        /// </summary>
        public bool SalesWitsPermission { get; set; }
        /// <summary>
        /// Sales绑定的使用者Id
        /// </summary>
        public string SalesWitsBindPhoneId { get; set; }
    }
    #endregion

    #region Wits复核时查看信息 或 复核驳回后初审查看的信息
    public class GetWitsApplyInfo4Audit_Out_RegisterInfo
    {
        /// <summary>
        /// 服务数据主键ID
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 客户编码
        /// </summary>
        public string ContractNum { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public EnumContractServiceOpenState State { get; set; }
        /// <summary>
        /// 共享使用总数
        /// </summary>
        public int ShareUsageNum { get; set; }
        /// <summary>
        /// 账号生成方式
        /// </summary>
        public EnumGtisAccountGenerationMethod AccountGenerationMethod { get; set; }
        /// <summary>
        /// 延长的总服务天数
        /// </summary>
        public int PerlongServiceDays { get; set; }
        /// <summary>
        /// 个人服务天数
        /// </summary>
        public int PrivateServiceDays { get; set; }
        /// <summary>
        /// 超额服务天数
        /// </summary>
        public int OverServiceDays { get; set; }
        /// <summary>
        /// 续约服务券使用数量
        /// </summary>
        public int CouponCounts { get; set; }
        /// <summary>
        /// 客户经理申请备注
        /// </summary>
        public string ApplyRemark { get; set; }
        /// <summary>
        /// Gtis备注
        /// </summary>
        public string GtisRemark { get; set; }
        /// <summary>
        /// 本次备注(初审备注)
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// Gtis服务申请内容
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_RegisterInfo_Gtis GtisInfo { get; set; }
        /// <summary>
        /// 环球搜服务申请内容
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_RegisterInfo_GlobalSearch GlobalSearchInfo { get; set; }
        /// <summary>
        /// 慧思学院服务申请内容
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_RegisterInfo_College CollegeInfo { get; set; }
        /// <summary>
        /// 慧销云服务申请内容
        /// </summary>
        public GetWitsApplyInfo4Audit_Out_RegisterInfo_SalesWits SalesWitsInfo { get; set; }
        /// <summary>
        /// 账号列表
        /// </summary>
        public List<GetWitsApplyInfo4Audit_Out_RegisterInfo_User> UserList { get; set; }
        /// <summary>
        /// 账号可选权限列表
        /// </summary>
        public List<EnumGtisSeriesPermission> GtisAccountPermissionList { get; set; } = new();
        /// <summary>
        /// 被续约的客户编码
        /// </summary>
        public string OriContractNum { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_RegisterInfo_Replenish : GetWitsApplyInfo4Audit_Out_RegisterInfo
    {
        /// <summary>
        /// 优惠券实体Ids
        /// </summary>           
        public string CounponDetailIds { get; set; }
        /// <summary>
        /// 是否申请了Gtis服务
        /// </summary>
        public bool IsGtisAudit { get; set; }
        /// <summary>
        /// 是否申请了环球搜服务
        /// </summary>
        public bool IsGlobalSearchAudit { get; set; }
        /// <summary>
        /// 是否申请了慧思学院服务
        /// </summary>
        public bool IsCollegeAudit { get; set; }
        /// <summary>
        /// 是否申请了SalesWits服务
        /// </summary>
        public bool IsSalesWitsAudit { get; set; }
        /// <summary>
        /// 慧思产品申请表ID
        /// </summary>
        public string WitsApplId { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_RegisterInfo_Gtis
    {
        /// <summary>
        /// 服务表主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProducttId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 产品类型
        /// </summary>
        public EnumProductType ProductType { get; set; }
        /// <summary>
        /// 子账号数量
        /// </summary>
        public int SubAccountNum { get; set; }
        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int AuthorizationNum { get; set; }
        /// <summary>
        /// 禁止下载/导出
        /// </summary>
        public bool ForbidSearchExport { get; set; }
        /// <summary>
        /// 是否拥有定制报告权限
        /// </summary>
        public bool WordRptPermissions { get; set; }
        /// <summary>
        /// 定制报告每年总次数
        /// </summary>
        public int WordRptMaxTimes { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime ServiceCycleEnd { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int ServiceMonth { get; set; }
        /// <summary>
        /// 添加零售国家（1-添加；2-未添加）
        /// </summary>
        public int RetailCountry { get; set; }
        /// <summary>
        /// 零售国家列表
        /// </summary>
        public List<GtisRetailCountry_OUT> RetailCountryList { get; set; } = new();
        /// <summary>
        /// 子账号是否同步国家权限
        /// </summary>
        public bool AllCountrySubUser { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_RegisterInfo_GlobalSearch
    {
        /// <summary>
        /// 申请表主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProducttId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 账号数量
        /// </summary>
        public int AccountNum { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime ServiceCycleEnd { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int ServiceMonth { get; set; }
        /// <summary>
        /// 收款时段
        /// </summary>
        public decimal SettlementCount { get; set; }
        /// <summary>
        /// 账户级别
        /// </summary>
        public string SettlementLevel { get; set; }
        /// <summary>
        /// 初审备注
        /// </summary>
        public string Remark { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_RegisterInfo_College
    {
        /// <summary>
        /// 申请表主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProducttId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 账号数量
        /// </summary>
        public int AccountNum { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime ServiceCycleEnd { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int ServiceMonth { get; set; }
        /// <summary>
        /// 初审备注
        /// </summary>
        public string Remark { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_RegisterInfo_SalesWits
    {
        /// <summary>
        /// 申请表主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        public string ProducttId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 账号数量
        /// </summary>
        public int AccountNum { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime ServiceCycleEnd { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int ServiceMonth { get; set; }
        /// <summary>
        /// 赠送邮件数量
        /// </summary>
        public int EmailCount { get; set; }
        /// <summary>
        /// 赠送Token数量
        /// </summary>
        public int TokenCount { get; set; }
        /// <summary>
        /// 充值金额
        /// </summary>
        public decimal AddCredit { get; set; }
        /// <summary>
        /// 初审备注
        /// </summary>
        public string Remark { get; set; }
    }
    public class GetWitsApplyInfo4Audit_Out_RegisterInfo_User
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 账号
        /// </summary>
        public string AccountNumber { get; set; }
        /// <summary>
        /// 账号类型
        /// </summary>
        public int AccountType { get; set; }
        /// <summary>
        /// 开通状态
        /// </summary>
        public int OpeningStatus { get; set; }
        /// <summary>
        /// 账户共享人数
        /// </summary>
        public int SharePeopleNum { get; set; }
        /// <summary>
        /// Gtis权限
        /// </summary>
        public bool GtisPermission { get; set; }
        /// <summary>
        /// Vip零售权限
        /// </summary>
        public bool VipPermission { get; set; }
        /// <summary>
        /// 环球搜权限
        /// </summary>
        public bool GlobalSearchPermission { get; set; }
        /// <summary>
        /// 慧思学院权限
        /// </summary>
        public bool CollegePermission { get; set; }
        /// <summary>
        /// SalesWits权限
        /// </summary>
        public bool SalesWitsPermission { get; set; }
        /// <summary>
        /// SalesWits绑定的使用者
        /// </summary>
        public string SalesWitsBindPhoneId { get; set; }
    }
    #endregion
    public class AuditWitsAppl_In
    {
        /// <summary>
        /// 是否通过
        /// </summary>
        public bool Pass { get; set; }
        /// <summary>
        /// 申请Id
        /// </summary>
        public string ApplyId { get; set; }
        /// <summary>
        /// 审核备注（公共位置和Gtis共用此项备注）
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// （续约）甲方公司
        /// </summary>
        public string RenewFirstParty { get; set; }
        /// <summary>
        /// (续约)客户编码
        /// </summary>
        public string RenewContractNum { get; set; }
        /// <summary>
        /// 申请时选择的客户编码对应的WitsService表主键
        /// </summary>
        public string OriWitsServeId { get; set; }
        /// <summary>
        /// 共享使用总数
        /// </summary>
        public int? ShareUsageNum { get; set; }
        /// <summary>
        /// 绑定到账列表
        /// </summary>
        public List<string> ReceiptRegisterIds { get; set; }
        /// <summary>
        /// Gtis申请数据
        /// </summary>
        public AuditWitsAppl_In_Gtis? GtisAudit { get; set; }
        /// <summary>
        /// 环球搜申请数据
        /// </summary>
        public AuditWitsAppl_In_GlobalSearch? GlobalSearchAudit { get; set; }
        /// <summary>
        /// 慧思学院申请数据
        /// </summary>
        public AuditWitsAppl_In_College? CollegeAudit { get; set; }
        /// <summary>
        /// SalesWits申请数据
        /// </summary>
        public AuditWitsAppl_In_SalesWits? SalesWitsAudit { get; set; }
        /// <summary>
        /// 账号生成方式
        /// </summary>
        public int AccountGenerationMethod { get; set; }
        /// <summary>
        /// 账号信息列表
        /// </summary>
        public List<AddOrAuditWitsApplUser> UserList { get; set; }
    }

    public class AuditWitsAppl_In_Gtis
    {
        /// <summary>
        /// 申请表Id
        /// </summary>
        public string ApplyId { get; set; }
/*        /// <summary>
        /// 合同产品信息表id
        /// </summary>           
        public string ContractProductInfoId { get; set; }
        /// <summary>
        /// 产品表id
        /// </summary>           
        public string ProductId { get; set; }*/
        /// <summary>
        /// 子账号数量
        /// </summary>
        [Required(ErrorMessage = "子账号数量不可为空")]
        public int SubAccountsNum { get; set; }
        /// <summary>
        /// 子账号授权国家次数不可为空
        /// </summary>
        public int? AuthorizationNum { get; set; }
        /// <summary>
        /// 子账号是否同步国家权限
        /// </summary>
        public bool AllCountrySubUser { get; set; }
        /// <summary>
        /// 服务月份
        /// </summary>
        public int? ServiceMonth { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime? ServiceCycleStart { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 是否添加零售国家
        /// 1-添加；2-不添加
        /// </summary>
        public int? RetailCountry { get; set; }
        /// <summary>
        /// 开通国家
        /// </summary>
        public List<GtisApplCountry>? GtisApplCountry { get; set; }
        /// <summary>
        /// 禁用下载/导出权限
        /// </summary>
        public bool ForbidSearchExport { get; set; }
        /// <summary>
        /// 是否开通定制报告
        /// </summary>
        public bool WordRptPermissions { get; set; }
        /// <summary>
        /// 定制报告每年总次数
        /// </summary>
        public int? WordRptMaxTimes { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
    public class AuditWitsAppl_In_GlobalSearch
    {
        /// <summary>
        /// 申请表Id
        /// </summary>
        public string ApplyId { get; set; }
        /// <summary>
        /// 环球搜账号数量(主+子)
        /// </summary>
        public int? AccountCount { get; set; }
        /// <summary>
        /// 环球搜服务月份
        /// </summary>
        public int? ServiceMonth { get; set; }
        /// <summary>
        /// 环球搜服务开始时间
        /// </summary>
        public DateTime? ServiceCycleStart { get; set; }
        /// <summary>
        /// 环球搜服务结束时间
        /// </summary>
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 结算级别(主账号数量+子账号数量)
        /// </summary>           
        public string SettlementLevel { get; set; }
        /// <summary>
        /// 收款时段
        /// </summary>           
        public decimal? SettlementCount { get; set; }

    }
    public class AuditWitsAppl_In_College
    {
        /// <summary>
        /// 申请表Id
        /// </summary>
        public string ApplyId { get; set; }
        /// <summary>
        /// 慧思学院账号数量
        /// </summary>
        public int? AccountCount { get; set; }
        /// <summary>
        /// 慧思学院服务月份
        /// </summary>
        public int? ServiceMonth { get; set; }
        /// <summary>
        /// 慧思学院服务开始时间
        /// </summary>
        public DateTime? ServiceCycleStart { get; set; }
        /// <summary>
        /// 慧思学院服务结束时间
        /// </summary>
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
    public class AuditWitsAppl_In_SalesWits
    {
        /// <summary>
        /// 申请表Id
        /// </summary>
        public string ApplyId { get; set; }
        /// <summary>
        /// SalesWits账号数量
        /// </summary>
        public int? AccountCount { get; set; }
        /// <summary>
        /// SalesWits服务月份
        /// </summary>
        public int? ServiceMonth { get; set; }
        /// <summary>
        /// SalesWits服务开始时间
        /// </summary>
        public DateTime? ServiceCycleStart { get; set; }
        /// <summary>
        /// SalesWits服务结束时间
        /// </summary>
        public DateTime? ServiceCycleEnd { get; set; }
        /// <summary>
        /// SalesWtis充值金额
        /// </summary>
        public decimal? AddCredit { get; set; }
        /// <summary>
        /// 赠送邮件数量
        /// </summary>
        public int EmailCount { get; set; }
        /// <summary>
        /// 赠送Token数量
        /// </summary>
        public int TokenCount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
    /// <summary>
    /// 提交服务申请和初审时共用的账号模型
    /// </summary>
    public class AddOrAuditWitsApplUser
    {
        /// <summary>
        /// Gtis系统的UserID
        /// </summary>
        public string SysUserId { get; set; }
        /// <summary>
        /// 账号类型
        /// </summary>
        public int? AccountType { get; set; }
        /// <summary>
        /// 账号
        /// </summary>
        public string AccountNumber { get; set; }
        /// <summary>
        /// 每个账户共享人数
        /// </summary>
        public int? SharePeopleNum { get; set; }
        /// <summary>
        /// Gtis权限
        /// </summary>
        public bool? GtisPermission { get; set; } = false;
        /// <summary>
        /// Vip零售国家权限
        /// </summary>
        public bool? VipPermission { get; set; } = false;
        /// <summary>
        /// 环球搜权限
        /// </summary>
        public bool? GlobalSearchPermission { get; set; } = false;
        /// <summary>
        /// 慧思学院权限
        /// </summary>
        public bool? CollegePermission { get; set; } = false;
        /// <summary>
        /// SalesWits权限
        /// </summary>
        public bool? SalesWitsPermission { get; set; } = false;
        /// <summary>
        /// SalesWits绑定的使用者Id
        /// </summary>
        public string SalesWitsBindPhoneId { get; set; }
    }


    public class ReviewWitsApply_In
    {
        /// <summary>
        /// 合同服务信息环球慧思申请表id
        /// </summary>
        [Required(ErrorMessage = "合同服务信息环球慧思申请表id不可为空")]
        public string WitsApplId { get; set; }
        /// <summary>
        /// 状态(12.5 true：开通、false：拒绝)
        /// </summary>
        [Required(ErrorMessage = "复核结果不可为空")]
        public bool Pass { get; set; }
        /// <summary>
        /// 审核反馈
        /// </summary>
        public string FeedBack { get; set; }
        /// <summary>
        /// 本次备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 环球搜备注
        /// </summary>
        public string GlobalSearchRemark { get; set; }
        /// <summary>
        /// 慧思学院备注
        /// </summary>
        public string CollegeRemark { get; set; }
        /// <summary>
        /// SalesWits备注
        /// </summary>
        public string SalesWitsRemark { get; set; }
    }
}
