﻿using System.Diagnostics;
using CRM2_API.BLL.Common;
using CRM2_API.Common.JWT;
using CRM2_API.Common.Cron;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using Mapster;
using Quartz;
using CRM2_API.Model.ControllersViewModel.ComParam;

namespace CRM2_API.BLL.ComParam
{
    public class BLL_ComParam : BaseBLL<BLL_ComParam>
    {
        /// <summary>
        /// 修改公共参数信息
        /// </summary>
        /// <param name="updateComParamDictionaryIn">请求参数</param>
        /// <param name="isTimingRefresh">是否定时刷新</param>
        /// <returns></returns>
        public UpdateComParamDictionary_Out UpdateComParamDictionary(UpdateComParamDictionary_In updateComParamDictionaryIn, bool isTimingRefresh = false)
        {
            UpdateComParamDictionary_Out result = new() { Data = 0 };
            //将接口参数以字典的形式保存
            Dictionary<string, object> updateComParamDictionaryInDict = updateComParamDictionaryIn.Adapt<Dictionary<string, Object>>();
            //参数类型,都是这个,是写死的
            string type = ComParamCommon.COM_PARAM_TYPE;

            List<Db_sys_comparam> allComparams = DbOpe_sys_comparam.Instance.GetAllComparams(type); //数据库中所有的参数列表
            List<Db_sys_comparam> takeEffectComparams = allComparams.Where(i => i.WaitEffec is 0 or null).ToList(); //目前正在生效的参数列表
            List<Db_sys_comparam> waitEffecComparams = allComparams.Except(takeEffectComparams).ToList();   //待生效的参数列表
            //创建字典
            //数据库中所有的参数列表做成的字典
            Dictionary<string, Db_sys_comparam> allIdDictComparams = allComparams.ToDictionary(k => k.Id, v => v);
            //目前正在生效的参数列表字典
            Dictionary<string, Db_sys_comparam> takeEffectDictComparams = takeEffectComparams.ToDictionary(k => k.ParamKey, v => v);
            //待生效的参数列表字典
            Dictionary<string, Db_sys_comparam> waitEffecDictComparams = waitEffecComparams.ToDictionary(k => k.ParamKey, v => v);


            //准备的数据
            List<Db_sys_comparam> insertList = new();   //插入的数据列表
            List<Db_sys_comparam> updateList = new();   //要更新的数据列表
            string remark = string.Empty;   //备注
            Db_sys_comparam temp = null;    //临时的实体
            bool isOpenJob = false; //是否开启定时任务

            ///
            /// 添加的本地方法
            /// operateType 添加类型
            /// paramKey 通用参数的key
            ///
            Action<string, string> addParam = (operateType, paramKey) =>
            {
                temp = new()
                {
                    Id = Guid.NewGuid().ToString(),
                    ParamKey = paramKey,
                    ParamValue = updateComParamDictionaryInDict[paramKey].ToInt().ToString(), // 从接口参数字典中读取到值
                    Remark = remark,  //备注是从枚举中读取的，也是写死的，通过不同的ParamKey拿到不同的备注
                    Type = type, //类型是写死的
                    Deleted = false,
                    CreateUser = UserTokenInfo.id,
                    CreateDate = DateTime.Now
                };
                if (operateType.Equals("add"))   //如果添加类型是 add则有生效的上一条，rootAdd则是完全的新增，数据库中不存在生效的上一条
                {
                    temp.Inherit = takeEffectDictComparams.ContainsKey(paramKey)    //如果这个key在当前生效的参数中存在，将正在生效的id复制给新建的Inherit属性，表明新建的这条是继承的当前生效的这条
                        ? takeEffectDictComparams[paramKey].Id
                        : null;
                }
                if (isTimingRefresh)    //如果开启定时任务
                {
                    temp.WaitEffec = 1;     //定时的标记，表明该条数据将在定时任务后生效
                    isOpenJob = true;   //开启定时任务标记

                }
                else
                {
                    //如果是立即生效则直接把生效时间写进去
                    temp.TakingEffectTime = DateTime.Now;
                }
                insertList.Add(temp);

                if (operateType.Equals("add") && !isTimingRefresh)
                {  // 如果类型是 "add" 且没有开启定时任务,那么就需要立即生效，需要把上一条设置为删除的状态并添加到更新中
                    if (!string.IsNullOrEmpty(temp.Inherit) && allIdDictComparams.ContainsKey(temp.Inherit))    //查看上一条是否存在
                    {
                        temp = allIdDictComparams[temp.Inherit];
                        temp.Deleted = true;
                        temp.WaitEffec = null;
                        temp.UpdateUser = UserTokenInfo.id;
                        temp.UpdateDate = DateTime.Now;
                        temp.ExpirationTime = DateTime.Now;
                        updateList.Add(temp);
                    }
                }
            };

            ///
            /// 更新的本地方法
            /// paramKey 通用参数的key
            ///
            Action<string> modifyParam = paramKey =>
            {
                temp.ParamValue = updateComParamDictionaryInDict[paramKey].ToInt().ToString();
                temp.UpdateUser = UserTokenInfo.id;
                temp.UpdateDate = DateTime.Now;

                if (isTimingRefresh) //如果开启定时任务
                {
                    updateList.Add(temp);
                    isOpenJob = true;   //定时任务标记为true
                }
                else
                {
                    temp.WaitEffec = null;
                    updateList.Add(temp);
                    if (!string.IsNullOrEmpty(temp.Inherit) && allIdDictComparams.ContainsKey(temp.Inherit))    //立即生效，将相同参数的上一条更新为删除状态
                    {
                        temp = allIdDictComparams[temp.Inherit];
                        temp.Deleted = true;
                        temp.WaitEffec = null;
                        temp.UpdateUser = UserTokenInfo.id;
                        temp.UpdateDate = DateTime.Now;
                        updateList.Add(temp);
                    }
                    // CronUtil.StopJob("refreshParamTask");   //停止定时任务
                }
            };

            foreach (string updateComParamDictionaryInDictKey in updateComParamDictionaryInDict.Keys)
            {
                // 根据参数Key去获取枚举中的备注
                remark = string.Empty;
                if (type.Equals(ComParamCommon.COM_PARAM_TYPE))
                {
                    remark = Enum.TryParse(updateComParamDictionaryInDictKey, out EnumComParamKey key)
                        ? key.GetEnumDescription()
                        : String.Empty;
                }

                //这个key在正在生效的字典中和待生效的字典中都不存在，则为 rootAdd 新增，即 新建的这条Inherit属性为null，表明新建的不存在继承关系
                if (!takeEffectDictComparams.ContainsKey(updateComParamDictionaryInDictKey) && !waitEffecDictComparams.ContainsKey(updateComParamDictionaryInDictKey))
                {
                    addParam("rootAdd", updateComParamDictionaryInDictKey);
                }
                //这里是更新
                else
                {
                    //如果是开启定时任务的话
                    if (isTimingRefresh)
                    {
                        //待更新的字典中存在相同key的数据
                        if (waitEffecDictComparams.ContainsKey(updateComParamDictionaryInDictKey))
                        {
                            //拿到这条数据
                            temp = waitEffecDictComparams[updateComParamDictionaryInDictKey];
                            //修改这条数据即可
                            modifyParam(updateComParamDictionaryInDictKey);
                        }
                        //待更新的字典中不存在相同key的数据
                        else
                        {
                            //说明这个key的数据已经生效了，新建1条
                            addParam("add", updateComParamDictionaryInDictKey);
                        }
                    }
                    // 不开启定时任务的情况，和上面的业务逻辑相同，在具体的本地方法中进行了判断
                    else
                    {
                        //待更新的字典中存在相同key的数据
                        if (waitEffecDictComparams.ContainsKey(updateComParamDictionaryInDictKey))
                        {
                            //拿到这条数据
                            temp = waitEffecDictComparams[updateComParamDictionaryInDictKey];
                            //修改这条数据即可
                            modifyParam(updateComParamDictionaryInDictKey);
                        }
                        //待更新的字典中不存在相同key的数据
                        else
                        {
                            //说明这个key的数据已经生效了，新建1条
                            addParam("add", updateComParamDictionaryInDictKey);
                        }
                    }
                }
            }

            if (insertList is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam.Instance.Insert(insertList);
            }

            if (updateList is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam.Instance.Update(updateList);
            }
            DbOpe_sys_comparam.Instance.SaveQueues();
            result.Data = 1;
            if (isTimingRefresh && isOpenJob) //根据开启任务的参数和定时标记去决定是否开启定时任务
            {
                AddTimingRefreshParamTask(type);
            }
            return result;
        }

        /// <summary>
        /// 修改公共参数信息[备份]
        /// </summary>
        /// <param name="updateComParamDictionaryIn">请求参数</param>
        /// <param name="isTimingRefresh">是否定时刷新</param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [Obsolete]
        public UpdateComParamDictionary_Out UpdateComParamDictionaryOld(
            List<UpdateComParamDictionaryOld_In> updateComParamDictionaryIn, bool isTimingRefresh = false)
        {
            UpdateComParamDictionary_Out result = new() { Data = 0 };
            var checkParamKeyresult = CheckParamKeyRepeat(updateComParamDictionaryIn.Select(s => s.ParamKey).ToList());
            if (!checkParamKeyresult)
            {
                throw new ApiException("key值重复，请移除重复项后重新尝试！");
            }
            string type = ComParamCommon.COM_PARAM_TYPE;

            List<Db_sys_comparam> allComparams = DbOpe_sys_comparam.Instance.GetAllComparams(type); //所有的参数列表
            List<Db_sys_comparam> takeEffectComparams =
                allComparams.Where(i => i.WaitEffec is 0 or null).ToList(); //目前正在生效的参数列表
            List<Db_sys_comparam> waitEffecComparams = allComparams.Except(takeEffectComparams).ToList();   //待生效的参数列表

            Dictionary<string, Db_sys_comparam> takeEffectDictComparams = takeEffectComparams.ToDictionary(k => k.ParamKey, v => v);
            Dictionary<string, Db_sys_comparam> waitEffecDictComparams = waitEffecComparams.ToDictionary(k => k.ParamKey, v => v);

            Dictionary<string, Db_sys_comparam> allIdDictComparams = allComparams.ToDictionary(k => k.Id, v => v);

            //准备的数据
            List<Db_sys_comparam> insertList = new();
            List<Db_sys_comparam> updateList = new();
            Db_sys_comparam temp = null;
            bool isOpenJob = false;
            string remark = string.Empty;

            Action<string, UpdateComParamDictionaryOld_In> addParam = (operateType, paramItem) =>
            {
                temp = new()
                {
                    Id = Guid.NewGuid().ToString(),
                    ParamKey = paramItem.ParamKey,
                    ParamValue = paramItem.ParamValue,
                    Remark = remark,
                    Type = type,
                    Deleted = false,
                    CreateUser = UserTokenInfo.id,
                    CreateDate = DateTime.Now
                };
                if (operateType.Equals("add"))
                {
                    temp.Inherit = takeEffectDictComparams.ContainsKey(paramItem.ParamKey)
                        ? takeEffectDictComparams[paramItem.ParamKey].Id
                        : null;
                }
                if (isTimingRefresh)
                {
                    temp.WaitEffec = 1; //设置该数据项是定时开启
                    isOpenJob = true;

                }
                else
                {
                    //如果是立即生效则直接把生效时间写进去
                    temp.TakingEffectTime = DateTime.Now;
                }
                insertList.Add(temp);

                if (operateType.Equals("add") && !isTimingRefresh)
                {
                    if (!string.IsNullOrEmpty(temp.Inherit) && allIdDictComparams.ContainsKey(temp.Inherit))
                    {
                        temp = allIdDictComparams[temp.Inherit];
                        temp.Deleted = true;
                        temp.WaitEffec = null;
                        temp.UpdateUser = UserTokenInfo.id;
                        temp.UpdateDate = DateTime.Now;
                        temp.ExpirationTime = DateTime.Now;
                        updateList.Add(temp);
                    }
                }
            };

            Action<UpdateComParamDictionaryOld_In> modifyParam = paramItem =>
            {
                temp.ParamValue = paramItem.ParamValue;
                temp.UpdateUser = UserTokenInfo.id;
                temp.UpdateDate = DateTime.Now;

                if (isTimingRefresh)
                {
                    updateList.Add(temp);
                }
                else
                {
                    temp.WaitEffec = null;
                    updateList.Add(temp);
                    if (!string.IsNullOrEmpty(temp.Inherit) && allIdDictComparams.ContainsKey(temp.Inherit))
                    {
                        temp = allIdDictComparams[temp.Inherit];
                        temp.Deleted = true;
                        temp.WaitEffec = null;
                        temp.UpdateUser = UserTokenInfo.id;
                        temp.UpdateDate = DateTime.Now;
                        updateList.Add(temp);
                    }
                    // CronUtil.StopJob("refreshParamTask");
                }
            };

            foreach (UpdateComParamDictionaryOld_In paramItem in updateComParamDictionaryIn)
            {
                //key是否存在
                if (string.IsNullOrEmpty(paramItem.ParamKey))
                {
                    throw new ApiException("公共参数key值不可为空!");
                }

                //判断key在列表中是否存在
                if (!Enum.GetNames<EnumComParamKey>().ToList().Contains(paramItem.ParamKey))
                {
                    throw new ApiException($"{paramItem.ParamKey}不是通用参数的key值!");
                }

                remark = string.Empty;
                if (type.Equals(ComParamCommon.COM_PARAM_TYPE))
                {
                    remark = Enum.TryParse(paramItem.ParamKey, out EnumComParamKey key)
                        ? key.GetEnumDescription()
                        : String.Empty;
                }

                //id不存在并且key在数据库中也不存在则为新增
                if (string.IsNullOrEmpty(paramItem.Id) && !takeEffectDictComparams.ContainsKey(paramItem.ParamKey) && !waitEffecDictComparams.ContainsKey(paramItem.ParamKey))
                {
                    addParam("rootAdd", paramItem);
                }
                //这里是更新
                else
                {
                    if (isTimingRefresh)
                    {
                        //开启定时任务的情况下
                        //先判断id是否是待更新的数据[---]
                        //是的话直接修改这条数据[---]
                        //不是的话[---]
                        //首先判断是否有待更新的数据[即父id是当前生效的id并且定时标记是1]
                        //存在则更新那条数据 覆盖value即可。
                        //不存在则新增一条数据，父id 是当前正在生效的id，定时标记设置为1【删除操作是由定时器去完成】
                        if (waitEffecDictComparams.ContainsKey(paramItem.ParamKey))
                        {
                            temp = waitEffecDictComparams[paramItem.ParamKey];

                            modifyParam(paramItem);
                        }
                        else
                        {
                            addParam("add", paramItem);
                        }
                    }
                    else
                    {
                        //不开启定时任务的情况
                        //先判断id是否是待更新的数据[---]
                        //是的话 覆盖value，定时标记设置为null，更新一条数据，将父数据更新为删除[---]
                        //不是的话[---]
                        //首先判断是否有待更新的数据[即父id是当前生效的id并且定时标记是1]
                        //存在则更新那条数据 覆盖value，定时标记设置为null，更新一条数据，将父数据更新为删除
                        //不存在则新增一条数据，父id 是当前正在生效的id。定时标记设置为null，更新一条数据，将父数据更新为删除
                        if (waitEffecDictComparams.ContainsKey(paramItem.ParamKey))
                        {
                            temp = waitEffecDictComparams[paramItem.ParamKey];

                            modifyParam(paramItem);
                        }
                        else
                        {
                            addParam("add", paramItem);
                        }
                    }
                }
            }

            if (insertList is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam.Instance.Insert(insertList);
            }

            if (updateList is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam.Instance.Update(updateList);
            }
            DbOpe_sys_comparam.Instance.SaveQueues();
            result.Data = 1;
            if (isTimingRefresh && isOpenJob)
            {
                AddTimingRefreshParamTask(type);
            }
            return result;
        }


        /// <summary>
        /// 修改公共参数业绩规则信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updateComParamAchiIn"></param>
        /// <param name="isTimingRefresh"></param>
        /// <returns></returns>
        public UpdateComParamAchi_Out UpdateComParamAchi(List<UpdateComParamAchi_In> updateComParamAchiIn, bool isTimingRefresh = false)
        {
            CheckComParamAchiOrgRepeat(updateComParamAchiIn);
            UpdateComParamAchi_Out result = new() { Data = 0 };
            List<Db_sys_comparam_achi> allComparamAchis = DbOpe_sys_comparam_achi.Instance.GetAllComparamAchis();
            Dictionary<string, Db_sys_comparam_achi> allInheritDictAchis = allComparamAchis.Where(i => i.Inherit != null).ToDictionary(k => k.Inherit, v => v);
            Dictionary<string, Db_sys_comparam_achi> allIdDictAchis = allComparamAchis.ToDictionary(k => k.Id, v => v);

            List<Db_sys_comparam_achi> insertList = new();
            List<Db_sys_comparam_achi> updateList = new();
            List<Db_sys_comparam_achi_org> insertOrgList = new();
            Db_sys_comparam_achi temp = null;
            Db_sys_comparam_achi_org aoTemp = null;
            string tempGuid = string.Empty;
            bool isOpenJob = false;
            List<string> notRemoveIdList = new();
            int sortNum = 0;
            int orgSortNum = 0;
            #region 搞事情的复杂方式
            Action<string, UpdateComParamAchi_In> addAchi = (type, achiItem) =>
            {
                tempGuid = Guid.NewGuid().ToString();
                temp = new()
                {
                    Id = tempGuid,
                    ConfimYear = achiItem.ConfimYear,
                    PerformanceCoefficient = achiItem.PerformanceCoefficient,
                    Deleted = false,
                    CreateUser = TokenModel.Instance.id,
                    CreateDate = DateTime.Now,
                    SortNum = ++sortNum
                };
                if (type.Equals("add"))
                {
                    temp.Inherit = achiItem.Id;
                }
                if (type.Equals("add") && isTimingRefresh)
                {
                    //notRemoveIdList.Add(achiItem.Id);
                }
                if (isTimingRefresh)
                {
                    temp.WaitEffec = true;
                    isOpenJob = true;
                }
                else
                {
                    temp.TakingEffectTime = DateTime.Now;
                    // CronUtil.StopJob("refreshAchiTask"); // 不再需要手动停止任务，SafeCronUtil会自动管理
                }

                insertList.Add(temp);

                //保存子表
                foreach (string orgId in achiItem.OrgIds)
                {
                    aoTemp = new()
                    {
                        Id = Guid.NewGuid().ToString(),
                        ComParamAchiId = tempGuid,
                        OrgId = orgId,
                        Deleted = false,
                        CreateUser = TokenModel.Instance.id,
                        CreateDate = DateTime.Now,
                        SortNum = ++orgSortNum
                    };
                    insertOrgList.Add(aoTemp);
                }

                orgSortNum = 0; //重置排序码
            };

            Action<UpdateComParamAchi_In> modifyAchi = achiItem =>
            {
                temp.PerformanceCoefficient = achiItem.PerformanceCoefficient;
                temp.ConfimYear = achiItem.ConfimYear;
                temp.UpdateUser = TokenModel.Instance.id;
                temp.UpdateDate = DateTime.Now;
                temp.SortNum = ++sortNum;
                if (isTimingRefresh)
                {
                    notRemoveIdList.Add(temp.Id);
                    //notRemoveIdList.Add(temp.Inherit);
                    isOpenJob = true;
                }
                else
                {
                    temp.WaitEffec = null;
                    temp.TakingEffectTime = DateTime.Now;
                    // CronUtil.StopJob("refreshAchiTask"); // 不再需要手动停止任务，SafeCronUtil会自动管理
                }

                updateList.Add(temp);
                //notRemoveIdList.Add(temp.Id);

                DbOpe_sys_comparam_achi_org.Instance.PhysicsDeleteByAchiId(new List<string>()
                    { temp.Id }); //先删除
                //保存子表
                foreach (string orgId in achiItem.OrgIds)
                {
                    aoTemp = new()
                    {
                        Id = Guid.NewGuid().ToString(),
                        ComParamAchiId = temp.Id,
                        OrgId = orgId,
                        Deleted = false,
                        CreateUser = TokenModel.Instance.id,
                        CreateDate = DateTime.Now,
                        SortNum = ++orgSortNum
                    };
                    insertOrgList.Add(aoTemp);
                }
                orgSortNum = 0; //重置排序码
            };
            #endregion
            foreach (UpdateComParamAchi_In achiItem in updateComParamAchiIn)
            {
                string PerformanceCoefficient = achiItem.PerformanceCoefficient;
                int ConfimYear = achiItem.ConfimYear;
                //移除也放在这个里面了
                string achiID = DbOpe_sys_comparam_achi.Instance.InsertOrGetAchiId(ConfimYear, PerformanceCoefficient, achiItem.OrgIds);
                if (achiID != string.Empty)
                {
                    foreach (string orgId in achiItem.OrgIds)
                    {
                        aoTemp = new()
                        {
                            Id = Guid.NewGuid().ToString(),
                            ComParamAchiId = achiID,
                            OrgId = orgId,
                            Deleted = false,
                            CreateUser = TokenModel.Instance.id,
                            CreateDate = DateTime.Now,
                            SortNum = 0
                        };
                        insertOrgList.Add(aoTemp);
                    }
                }

            }
            if (insertList is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam_achi.Instance.Insert(insertList);
            }

            if (updateList is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam_achi.Instance.Update(updateList);
            }

            if (insertOrgList is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam_achi_org.Instance.Insert(insertOrgList);
            }

            //if (isTimingRefresh)
            //{
            //    List<string> removeIdList = allComparamAchis.Where(s => s.WaitEffec == true).Select(s => s.Id).ToList().Except(notRemoveIdList).ToList();

            //    //重写删除逻辑，目的：删除旧的参数记录
            //    //插入记录里的机构，旧记录无条件删除
            //    //非插入记录里的机构，如果存在关联旧参数的记录的 参数相同的update，不同的delete（不同的状况大概率没有）
            //    removeIdList = DbOpe_sys_comparam_achi.Instance.doNeedRemove(insertList, updateList, insertOrgList);
            //    insertList.ToList().ForEach(e =>
            //        {

            //        });




            //    DbOpe_sys_comparam_achi.Instance.RemoveByIdList(removeIdList);
            //}
            //else
            //{
            //    List<string> removeIdList = allComparamAchis.Select(s => s.Id).ToList().Except(notRemoveIdList).ToList();
            //    DbOpe_sys_comparam_achi.Instance.RemoveByIdList(removeIdList);
            //}


            DbOpe_sys_comparam_achi.Instance.SaveQueues();

            result.Data = 1;
            return result;
        }

        /// <summary>
        /// 修改公共参数保留客户数规则信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updateComParamRetainIn"></param>
        /// <param name="isTimingRefresh"></param>
        /// <returns></returns>
        public UpdateComParamRetain_Out UpdateComParamRetain(List<UpdateComParamRetain_In> updateComParamRetainIn, bool isTimingRefresh = false)
        {
            UpdateComParamRetain_Out result = new() { Data = 0 };
            List<Db_sys_comparam_retain> allComparamRetains = DbOpe_sys_comparam_retain.Instance.GetAllComparamRetains();
            Dictionary<string, Db_sys_comparam_retain> allInheritDictRetains = allComparamRetains.Where(i => i.Inherit != null).ToDictionary(k => k.Inherit, v => v);
            Dictionary<string, Db_sys_comparam_retain> allIdDictRetains = allComparamRetains.ToDictionary(k => k.Id, v => v);

            List<Db_sys_comparam_retain> insertList = new();
            List<Db_sys_comparam_retain> updateList = new();
            Db_sys_comparam_retain temp = null;
            string tempGuid = string.Empty;
            bool isOpenJob = false;
            List<string> notRemoveIdList = new();
            int sortNum = 0;

            Action<string, UpdateComParamRetain_In> addRetain = (type, retainItem) =>
            {
                tempGuid = Guid.NewGuid().ToString();
                temp = new()
                {
                    Id = tempGuid,
                    PerformanceAmountStart = retainItem.PerformanceAmountStart.ToDecimal(true),
                    PerformanceAmountEnd = retainItem.PerformanceAmountEnd.ToDecimal(true),
                    RetainCustomers = retainItem.RetainCustomers,
                    Deleted = false,
                    CreateUser = TokenModel.Instance.id,
                    CreateDate = DateTime.Now,
                    SortNum = ++sortNum
                };
                if (type.Equals("add"))
                {
                    temp.Inherit = retainItem.Id;
                }
                if (type.Equals("add") && isTimingRefresh)
                {
                    //notRemoveIdList.Add(achiItem.Id);
                }
                if (isTimingRefresh)
                {
                    temp.WaitEffec = true;
                    isOpenJob = true;
                }
                else
                {
                    temp.TakingEffectTime = DateTime.Now;
                    // CronUtil.StopJob("refreshRetainTask"); // 不再需要手动停止任务，SafeCronUtil会自动管理
                }

                insertList.Add(temp);
            };

            Action<UpdateComParamRetain_In> modifyRetain = retainItem =>
            {
                temp.PerformanceAmountStart = retainItem.PerformanceAmountStart.ToDecimal(true);
                temp.PerformanceAmountEnd = retainItem.PerformanceAmountEnd.ToDecimal(true);
                temp.RetainCustomers = retainItem.RetainCustomers;
                temp.UpdateUser = TokenModel.Instance.id;
                temp.UpdateDate = DateTime.Now;
                temp.SortNum = ++sortNum;
                if (isTimingRefresh)
                {
                    notRemoveIdList.Add(temp.Id);
                    //notRemoveIdList.Add(temp.Inherit);
                    isOpenJob = true;
                }
                else
                {
                    temp.WaitEffec = null;
                    temp.TakingEffectTime = DateTime.Now;
                    // CronUtil.StopJob("refreshRetainTask"); // 不再需要手动停止任务，SafeCronUtil会自动管理
                }

                updateList.Add(temp);
                //notRemoveIdList.Add(temp.Id);

            };

            foreach (UpdateComParamRetain_In retainItem in updateComParamRetainIn)
            {
                //新增
                if (string.IsNullOrEmpty(retainItem.Id))
                {
                    addRetain("rootAdd", retainItem);
                }
                //更新
                else
                {
                    if (!allIdDictRetains.ContainsKey(retainItem.Id))
                    {
                        throw new ApiException("id所对应的数据项不存在");
                    }

                    if (isTimingRefresh)
                    {
                        temp = allIdDictRetains[retainItem.Id];
                        bool isWaitEffec = temp.WaitEffec == true;
                        if (isWaitEffec)
                        {
                            modifyRetain(retainItem);
                        }
                        else
                        {
                            if (allInheritDictRetains.ContainsKey(retainItem.Id))
                            {
                                temp = allInheritDictRetains[retainItem.Id];
                                modifyRetain(retainItem);
                            }
                            else
                            {
                                addRetain("add", retainItem);
                            }
                        }
                    }

                    else
                    {
                        if (allInheritDictRetains.ContainsKey(retainItem.Id))
                        {
                            temp = allInheritDictRetains[retainItem.Id];
                            modifyRetain(retainItem);
                        }
                        else
                        {
                            temp = allIdDictRetains[retainItem.Id];
                            if (temp.WaitEffec == true)
                            {
                                modifyRetain(retainItem);
                            }
                            else
                            {
                                addRetain("add", retainItem);
                            }
                        }
                    }
                }
            }
            if (insertList is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam_retain.Instance.Insert(insertList);
            }

            if (updateList is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam_retain.Instance.Update(updateList);
            }

            if (isTimingRefresh)
            {
                List<string> removeIdList = allComparamRetains.Where(s => s.WaitEffec == true).Select(s => s.Id).ToList().Except(notRemoveIdList).ToList();
                DbOpe_sys_comparam_retain.Instance.RemoveByIdList(removeIdList);
            }
            else
            {
                List<string> removeIdList = allComparamRetains.Select(s => s.Id).ToList().Except(notRemoveIdList).ToList();
                DbOpe_sys_comparam_retain.Instance.RemoveByIdList(removeIdList);
            }
            DbOpe_sys_comparam_retain.Instance.SaveQueues();
            result.Data = 1;
            if (isTimingRefresh && isOpenJob)
            {
                AddTimingRefreshRetainTask();
            }
            return result;
        }

        /// <summary>
        /// 修改公共参数信息，参数分为三个数组，分别为参数数组字典、业绩规则和保留客户数，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updateComParamIn"></param>
        /// <param name="isTimingRefresh"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public UpdateComParam_Out UpdateComParam(UpdateComParam_In updateComParamIn, bool isTimingRefresh = false)
        {
            UpdateComParam_Out result = new() { Data = 0 };

            /*if (updateComParamIn.Dictionary != null && updateComParamIn.Dictionary.Count > 0)
            {
                var dictResult = UpdateComParamDictionaryOld(updateComParamIn.Dictionary,isTimingRefresh);
                if (dictResult.Data == 0) throw new ApiException("Dict exception");
            }*/
            if (updateComParamIn.Dictionary != null)
            {
                var dictResult = UpdateComParamDictionary(updateComParamIn.Dictionary, isTimingRefresh);
                if (dictResult.Data == 0) throw new ApiException("Dict exception");
            }

            if (updateComParamIn.Achi != null && updateComParamIn.Achi.Count > 0)
            {
                var achiResult = UpdateComParamAchi(updateComParamIn.Achi, isTimingRefresh);
                if (achiResult.Data == 0) throw new ApiException("Achi exception");
            }

            if (updateComParamIn.Retain != null && updateComParamIn.Retain.Count > 0)
            {
                var retainResult = UpdateComParamRetain(updateComParamIn.Retain, isTimingRefresh);
                if (retainResult.Data == 0) throw new ApiException("Retain exception");
            }

            result.Data = 1;
            return result;
        }

        /// <summary>
        /// Retain定时任务
        /// </summary>
        public void AddTimingRefreshRetainTask()
        {
            string jobName = $"refreshRetainTask";
            string cronExpression = "*/25 * * * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                RefreshRetainParam();
                Debug.WriteLine($"{jobName}任务执行完成！ \t {DateTime.Now}");
            }, cronExpression);
        }

        /// <summary>
        /// 刷新Retain操作Action
        /// </summary>
        private void RefreshRetainParam()
        {
            List<Db_sys_comparam_retain> allData = DbOpe_sys_comparam_retain.Instance.GetAllComparamRetains();
            List<Db_sys_comparam_retain> newData = allData.Where(w => w.WaitEffec == true).ToList();
            if (newData is null or { Count: 0 }) return;
            List<Db_sys_comparam_retain> oldData = allData.Except(newData).ToList();
            //移除旧的参数并修改新的参数
            DbOpe_sys_comparam_retain.Instance.RemoveByIdList(oldData.Select(s => s.Id).ToList());
            foreach (Db_sys_comparam_retain newItem in newData)
            {
                newItem.WaitEffec = null;
                newItem.TakingEffectTime = DateTime.Now;
                newItem.UpdateUser = TokenModel.Instance.id;
                newItem.UpdateDate = DateTime.Now;
            }
            DbOpe_sys_comparam_retain.Instance.Update(newData);
            DbOpe_sys_comparam_retain.Instance.SaveQueues();

            //定时任务执行时刷新用户表的可保留用户数据
            DbOpe_sys_user.Instance.RefreshUserMaxSaveCustomer();

        }

        /// <summary>
        /// Achi定时任务
        /// </summary>
        public void AddTimingRefreshAchiTask()
        {
            string jobName = $"refreshAchiTask";
            string cronExpression = "*/25 * * * * ?";
            
            SafeCronUtil.AddCronJob(jobName, () =>
            {
                RefreshAchiParam();
                Debug.WriteLine($"{jobName}任务执行完成！ \t {DateTime.Now}");
            }, cronExpression);
        }

        /// <summary>
        /// 刷新Achi操作Action
        /// </summary>
        private void RefreshAchiParam()
        {
            List<Db_sys_comparam_achi> allComparamAchis = DbOpe_sys_comparam_achi.Instance.GetAllComparamAchis();
            List<Db_sys_comparam_achi> newData = allComparamAchis.Where(w => w.WaitEffec == true).ToList();
            if (newData is null or { Count: 0 }) return;
            List<Db_sys_comparam_achi> oldData = allComparamAchis.Except(newData).ToList();
            //移除旧的参数并修改新的参数
            DbOpe_sys_comparam_achi.Instance.RemoveByIdList(oldData.Select(s => s.Id).ToList());
            foreach (Db_sys_comparam_achi newItem in newData)
            {
                newItem.WaitEffec = null;
                newItem.TakingEffectTime = DateTime.Now;
                newItem.UpdateUser = TokenModel.Instance.id;
                newItem.UpdateDate = DateTime.Now;
            }
            DbOpe_sys_comparam_achi.Instance.Update(newData);
            DbOpe_sys_organization_params.Instance.ChangeOrgParams(newData);
            DbOpe_sys_comparam_achi.Instance.SaveQueues();
        }

        /// <summary>
        /// 添加定时任务
        /// </summary>
        public void AddTimingRefreshParamTask(string type = "comparam")
        {
            string jobName = $"refreshParamTask";
            string cronExpression = "*/25 * * * * ?";
            
            SafeCronUtil.AddCronJob(jobName, () =>
            {
                RefreshComParam(type);
                Debug.WriteLine($"{jobName}任务执行完成！ \t {DateTime.Now}");
            }, cronExpression);
        }

        /// <summary>
        /// 刷新ComParam操作Action
        /// </summary>
        /// <param name="type"></param>
        private void RefreshComParam(string type = "comparam")
        {
            List<Db_sys_comparam> allData = DbOpe_sys_comparam.Instance.GetAllComparams(type);
            List<Db_sys_comparam> newData = allData.Where(w => w.WaitEffec == 1).ToList();
            if (newData is null or { Count: 0 }) return;
            List<string> newKeys = newData.Select(s => s.ParamKey).ToList();
            List<string> oldIds = allData.Where(w => (w.WaitEffec == 0 || w.WaitEffec == null) && newKeys.Contains(w.ParamKey)).Select(s => s.Id).ToList();
            //移除旧的参数并修改新的参数
            if (oldIds is not null and { Count: > 0 })
            {
                DbOpe_sys_comparam.Instance.DeleteComparamsByKeyAndType(type, oldIds);
            }
            foreach (Db_sys_comparam newItem in newData)
            {
                newItem.WaitEffec = null;
                newItem.TakingEffectTime = DateTime.Now;
                newItem.UpdateUser = TokenModel.Instance.id;
                newItem.UpdateDate = DateTime.Now;
            }
            DbOpe_sys_comparam.Instance.Update(newData);
            DbOpe_sys_comparam.Instance.SaveQueues();
        }

        /// <summary>
        /// 校验传入的参数值中key是否重复
        /// </summary>
        /// <param name="keyList"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        private bool CheckParamKeyRepeat(List<string> keyList)
        {
            if (keyList is not null and { Count: > 0 })
            {
                var groupParam = keyList.GroupBy(i => i)
                    .Select(g => (new Tuple<string, int>(g.Key, g.Count()))).ToList();
                foreach ((string item, int count) in groupParam)
                {
                    if (count > 1)
                    {
                        throw new ApiException($"key值：{item}在参数中重复,请移除重复项后重新添加");
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// 获取公共参数信息。
        /// </summary>
        /// <param name="type">指生效类型，0 == 获取全部，由前端进行处理。 1 == 获取当前生效值， 2 == 获取最新值</param>
        /// <returns></returns>
        public GetComParamList_Out GetComParamList(int type = 2)
        {
            string paramType = ComParamCommon.COM_PARAM_TYPE;
            List<Db_sys_comparam> allComparams = DbOpe_sys_comparam.Instance.GetAllComparams(paramType);
            List<Db_sys_comparam_achi> allComparamAchis = DbOpe_sys_comparam_achi.Instance.GetAllComparamAchis();
            Dictionary<string, List<Db_sys_comparam_achi_org>> comparamAchiOrgsDict =
                DbOpe_sys_comparam_achi_org.Instance.GetAchiOrgDictByAchiIdList(
                    allComparamAchis.Select(s => s.Id).ToList());
            List<Db_sys_comparam_retain> allComparamRetains =
                DbOpe_sys_comparam_retain.Instance.GetAllComparamRetains();

            bool eachOrgDich = comparamAchiOrgsDict is not null;

            GetComParamList_Out result = new();
            //1.对key进行分离
            List<Db_sys_comparam> comparams = null;
            List<Db_sys_comparam_achi> comparamAchis = null;
            List<Db_sys_comparam_retain> comparamRetains = null;
            switch (type)
            {
                // case 0:
                //     comparams = allComparams;
                //     break;
                case 1:
                    comparams = allComparams.Where(w => w.WaitEffec is null or 0).ToList();
                    comparamAchis = allComparamAchis.Where(w => w.WaitEffec is null or false).ToList();
                    comparamRetains = allComparamRetains.Where(w => w.WaitEffec is null or false).ToList();
                    break;
                case 2:
                    //comparams处理
                    List<Db_sys_comparam> newData = allComparams.Where(w => w.WaitEffec is 1).ToList();
                    List<string> newDataExistsKey = newData.Select(s => s.ParamKey).Distinct().ToList();
                    List<Db_sys_comparam> oldData = allComparams.Except(newData).ToList();
                    List<Db_sys_comparam> oldDataExists =
                        oldData.Where(w => !newDataExistsKey.Contains(w.ParamKey)).ToList();
                    newData.AddRange(oldDataExists);
                    comparams = newData;
                    //comparamAchis处理
                    comparamAchis = allComparamAchis.Where(w => w.WaitEffec is true).ToList();
                    if (comparamAchis is null or { Count: 0 })
                    {
                        comparamAchis = allComparamAchis.Where(w => w.WaitEffec is null or false).ToList();
                    }
                    //comparamRetains处理
                    comparamRetains = allComparamRetains.Where(w => w.WaitEffec is true).ToList();
                    if (comparamRetains is null or { Count: 0 })
                    {
                        comparamRetains = allComparamRetains.Where(w => w.WaitEffec is null or false).ToList();
                    }
                    break;
                default:
                    comparams = new();
                    comparamAchis = new();
                    comparamRetains = new();
                    break;
            };
            //1.组装参数
            // List<UpdateComParamDictionaryOld_In> dicts =  comparams.Select(s => new UpdateComParamDictionaryOld_In
            // {
            //     Id = s.Id,
            //     ParamKey = s.ParamKey,
            //     ParamValue = s.ParamValue
            // }).ToList();
            //result.Dictionary = dicts;

            //1.组装参数
            ComParamDictionary_Out dicts = comparams
                .ToDictionary(k => k.ParamKey, v => v.ParamValue)
                .Adapt<ComParamDictionary_Out>();
            result.Dictionary = dicts;
            //2.组装参数
            List<UpdateComParamAchi_In> achiOutput = comparamAchis.Select(s => new UpdateComParamAchi_In
            {
                Id = s.Id,
                PerformanceCoefficient = s.PerformanceCoefficient.ToString(),
                ConfimYear = s.ConfimYear != null ? (int)s.ConfimYear : 2023,
                OrgIds = eachOrgDich ? (comparamAchiOrgsDict.ContainsKey(s.Id) ? comparamAchiOrgsDict[s.Id].Select(s => s.OrgId).ToList() : new List<string>()) : new List<string>()
            }).ToList();
            result.Achi = achiOutput;

            //3.组装参数
            List<UpdateComParamRetain_In> retainOutput = comparamRetains.Select(s => new UpdateComParamRetain_In
            {
                Id = s.Id,
                PerformanceAmountStart = s.PerformanceAmountStart.ToString(),
                PerformanceAmountEnd = s.PerformanceAmountEnd.ToString(),
                RetainCustomers = s.RetainCustomers.ToInt()
            }).ToList();
            result.Retain = retainOutput;
            return result;
        }

        /// <summary>
        /// 校验需要更新的公共参数业绩规则信息中是否存在重复的组织机构
        /// </summary>
        /// <returns></returns>
        public bool CheckComParamAchiOrgRepeat(List<UpdateComParamAchi_In> updateComParamAchiIn)
        {
            var duplicates = updateComParamAchiIn.SelectMany((list, index) => list.OrgIds.Select(item => new { item, index }))
                .GroupBy(x => x.item)
                .Where(g => g.Count() > 1)
                .Select(g => new { Item = g.Key, Indexes = g.Select(x => x.index + 1) });

            foreach (var duplicate in duplicates)
            {
                Db_sys_organization org = DbOpe_sys_organization.Instance.GetDataById(duplicate.Item);
                throw new ApiException($"{org.OrgName}在第{string.Join(", ", duplicate.Indexes)}行中重复！");
            }

            return true;
        }
    }
}