﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;
using System.Text.RegularExpressions;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_OriginalInvoice;

namespace CRM2_API.BLL
{
    public class BLL_OriginalInvoice : BaseBLL<BLL_OriginalInvoice>
    {
        /// <summary>
        /// 添加发票原件信息
        /// </summary>
        /// <param name="addInfo_In"></param>
        /// <exception cref="ApiException"></exception>
        public void AddOriginalInvoice(AddOriginalInvoice_In addInfo_In)
        {
            //声明发票原件表主键Id
            var originalInvoiceId = Guid.NewGuid().ToString();
            //验证发票编号格式是否正确（1-8位字母或数字字符串，结尾字符必须是数字）
            var checkNoFormat = addInfo_In.OriginalInvoiceDetails.All(e => Regex.IsMatch(e.InvoiceNoStart, "^[0-9a-zA-Z]{0,7}[0-9]$") && string.IsNullOrEmpty(e.InvoiceNoEnd) ? true : Regex.IsMatch(e.InvoiceNoEnd, "^[0-9a-zA-Z]{0,7}[0-9]$"));
            if (!checkNoFormat)
                throw new ApiException("输入发票编号格式不正确");
            //普票编号列表
            List<string> uniInvoiceNoList = new List<string>();
            //专票编号列表
            List<string> speInvoiceNoList = new List<string>();
            //验证发票编号及发放数量对应是否有误
            addInfo_In.OriginalInvoiceDetails.ForEach(detail =>
            {
                //声明发票原件明细信息表主键Id
                var originalInvoiceDetailsId = Guid.NewGuid().ToString();
                //声明发票开始编号的前缀字符串和结尾序列号
                string prefix = string.Empty;
                string startSerialNo = string.Empty;
                //获取startPrefix和startSerialNo的值
                GetInvoiceNoPrefixAndSerial(detail.InvoiceNoStart, out prefix, out startSerialNo);
                //根据发放份数，计算发票结束编号的结尾序列号
                string endSerialNo = (startSerialNo.ToInt() + detail.IssuedNum - 1).ToString().PadLeft(startSerialNo.Length, '0');
                //若用户填写发票结束编号，验证结束编号是否正确
                if (!string.IsNullOrEmpty(detail.InvoiceNoEnd))
                {
                    //声明用户输入的发票结束编号的前缀字符串和结尾序列号
                    string endPrefix_In = string.Empty;
                    string endSerialNo_In = string.Empty;
                    //获取 endPrefix_In 和 endSerialNo_In 的值
                    GetInvoiceNoPrefixAndSerial(detail.InvoiceNoEnd, out endPrefix_In, out endSerialNo_In);
                    if (endPrefix_In != prefix || endSerialNo_In != endSerialNo)
                        throw new ApiException("输入的发票编号中的结尾编号不正确");
                }
                else
                    detail.InvoiceNoEnd = prefix + endSerialNo;
                //循环向发票实体列表插入发票
                for (int i = startSerialNo.ToInt(); i <= endSerialNo.ToInt(); i++)
                {
                    var invoiceNo = prefix + i.ToString().PadLeft(startSerialNo.Length, '0');
                    if (detail.InvoiceType == EnumInvoiceType.SpecialTicket)
                    {
                        //验证当前记录的发票编号是否存在重复
                        if (speInvoiceNoList.Any(e => e == invoiceNo))
                            throw new ApiException("存在重复输入的发票编号,请重新核对");
                        speInvoiceNoList.Add(invoiceNo);
                    }
                    else if (detail.InvoiceType == EnumInvoiceType.UniversalTicket)
                    {
                        //验证当前记录的发票编号是否存在重复
                        if (uniInvoiceNoList.Any(e => e == invoiceNo))
                            throw new ApiException("存在重复输入的发票编号,请重新核对");
                        uniInvoiceNoList.Add(invoiceNo);
                    }
                    //entity的insert队列
                    var entity = AssemblyEntity(originalInvoiceId, originalInvoiceDetailsId, invoiceNo);
                    DbOpe_crm_original_invoice_details_entity.Instance.InsertQueue(entity);
                }
                //detail的insert队列
                var insDetail = detail.MappingTo<Db_crm_original_invoice_details>();
                insDetail.Id = originalInvoiceDetailsId;
                insDetail.OriginalInvoiceId = originalInvoiceId;
                insDetail.Deleted = false;
                insDetail.CreateUser = UserId;
                insDetail.CreateDate = DateTime.Now;
                DbOpe_crm_original_invoice_details.Instance.InsertQueue(insDetail);
            });
            //验证专票发票编号是否重复
            if (DbOpe_crm_original_invoice_details_entity.Instance.CheckRepeatInvoiceNo(speInvoiceNoList, EnumInvoiceType.SpecialTicket))
                throw new ApiException("输入的发票号与现有发票号重复，请重新核对");
            //验证普票发票编号是否重复
            if (DbOpe_crm_original_invoice_details_entity.Instance.CheckRepeatInvoiceNo(uniInvoiceNoList, EnumInvoiceType.UniversalTicket))
                throw new ApiException("输入的发票号与现有发票号重复，请重新核对");
            lock (this)
            {
                var invoice = addInfo_In.MappingTo<Db_crm_original_invoice>();
                invoice.Id = originalInvoiceId;
                invoice.OriginalInvoiceNum = (DbOpe_crm_original_invoice.Instance.GetQueryCount(invoice) + 1).ToString().PadLeft(5, '0');
                invoice.DistributionTime = DateTime.Now;
                invoice.Distributor = UserId;
                invoice.Deleted = false;
                invoice.CreateDate = DateTime.Now;
                invoice.CreateUser = UserId;
                DbOpe_crm_original_invoice.Instance.InsertQueue(invoice);
                //提交sql队列
                DbOpe_crm_original_invoice.Instance.SaveQueues();
            }
        }


        /// <summary>
        /// 修改发票原件信息
        /// </summary>
        /// <param name="updInfo_In"></param>
        /// <exception cref="ApiException"></exception>
        public void UpdateOriginalInvoice(UpdateOriginalInvoice_In updInfo_In)
        {
            //验证发票编号格式是否正确（1-8位字母或数字字符串，结尾字符必须是数字）
            var checkNoFormat = updInfo_In.OriginalInvoiceDetails.All(e => Regex.IsMatch(e.InvoiceNoStart, "^[0-9a-zA-Z]{0,7}[0-9]$") && string.IsNullOrEmpty(e.InvoiceNoEnd) ? true : Regex.IsMatch(e.InvoiceNoEnd, "^[0-9a-zA-Z]{0,7}[0-9]$"));
            if (!checkNoFormat)
                throw new ApiException("输入发票编号格式不正确");
            //获取插入部分的detail信息列表
            var tobeInsDetails = updInfo_In.OriginalInvoiceDetails.Where(e => string.IsNullOrEmpty(e.Id)).ToList();
            //获取修改部分的detail信息列表
            var tobeUpdDetails = updInfo_In.OriginalInvoiceDetails.Where(e => !string.IsNullOrEmpty(e.Id)).ToList();
            //获取修改部分的明细Id集合
            var tobeUpdDetailIds = tobeUpdDetails.Select(e => e.Id).ToList();
            //获取修改部分的detail对应当前数据库中的信息列表
            var tobeUpdCurDetails = DbOpe_crm_original_invoice_details.Instance.GetDetailsByIds(tobeUpdDetailIds);
            //获取传入的Id对应的当前数据库中的明细detail
            var curDetails = DbOpe_crm_original_invoice_details.Instance.GetDetailsByOriInvId(updInfo_In.Id);
            //计算待删除的detail主键Id
            var tobeDelDetailIds = curDetails.Where(e => !tobeUpdDetailIds.Contains(e.Id)).Select(e => e.Id).ToList();
            //添加删除details任务队列
            DbOpe_crm_original_invoice_details.Instance.DeleteDetailByIds(tobeDelDetailIds, UserId);

            #region 整理需要删除的发票实体
            //待删除的发票实体主键列表
            var tobeDelEntityIds = new List<string>();
            //整理待删除的普票发票原件实体的发票编号
            var tobeDelUniEntities = curDetails.Where(e => tobeDelDetailIds.Contains(e.Id) && e.InvoiceType == EnumInvoiceType.UniversalTicket).Select(e => e.entities).ToList();
            //待删除的普票发票号列表
            var tobeDelUniInvoiceNoList = new List<string>();
            //填充待删除的普票发票号列表
            tobeDelUniEntities.ForEach(item =>
            {
                tobeDelUniInvoiceNoList.AddRange(item.Select(e => e.InvoiceNo));
                tobeDelEntityIds.AddRange(item.Select(e => e.Id));
            });
            //整理待删除的专票发票原件实体的发票编号
            var tobeDelSpeEntities = curDetails.Where(e => tobeDelDetailIds.Contains(e.Id) && e.InvoiceType == EnumInvoiceType.SpecialTicket).Select(e => e.entities).ToList();
            //待删除的专票发票号列表
            var tobeDelSpeInvoiceNoList = new List<string>();
            //填充待删除的专票发票号列表
            tobeDelSpeEntities.ForEach(item =>
            {
                tobeDelSpeInvoiceNoList.AddRange(item.Select(e => e.InvoiceNo));
                tobeDelEntityIds.AddRange(item.Select(e => e.Id));
            });
            //添加待删除Entity任务队列
            DbOpe_crm_original_invoice_details_entity.Instance.DeleteEntitiesByIds(tobeDelEntityIds, UserId);
            #endregion

            //普票编号列表
            List<string> uniInvoiceNoList = new List<string>();
            //专票编号列表
            List<string> speInvoiceNoList = new List<string>();

            //需要在修改中删除的普票发票号列表
            var tobeDelInUpdUniInvoiceNoList = new List<string>();
            //需要在修改中删除的专票发票号列表
            var tobeDelInUpdSpeInvoiceNoList = new List<string>();

            //处理发票原件明细修改
            tobeUpdDetails.ForEach(detail =>
            {
                //获取detail对应的当前数据库数据信息
                var curDetail = tobeUpdCurDetails.Where(e => e.Id == detail.Id).First();
                //判断开始发票号是否修改
                if (curDetail.InvoiceNoStart != detail.InvoiceNoStart)
                    throw new ApiException("不允许更改发票开始编号");
                /*2023.11.02注销，此处验证规则改为：发放份数不能小于已使用发票号中的最大号
                 * //判断传入的数量是否少于已使用的数量，少于不符合要求
                var usedCount = curDetail.entities.Count(e => e.State == EnumInvoiceEntityState.Used || e.State == EnumInvoiceEntityState.Void);
                if (curDetail.IssuedNum < usedCount)
                    throw new ApiException("发放份数不能小于已使用发票数");*/
                //声明发票开始编号的前缀字符串和结尾序列号
                string prefix = string.Empty;
                string startSerialNo = string.Empty;
                //获取startPrefix和startSerialNo的值
                GetInvoiceNoPrefixAndSerial(detail.InvoiceNoStart, out prefix, out startSerialNo);
                //根据发放份数，计算发票结束编号的结尾序列号
                string endSerialNo = (startSerialNo.ToInt() + detail.IssuedNum - 1).ToString().PadLeft(startSerialNo.Length, '0');
                //若用户填写发票结束编号，验证结束编号是否正确
                if (!string.IsNullOrEmpty(detail.InvoiceNoEnd))
                {/*用户输入发票结束编号*/
                    //声明用户输入的发票结束编号的前缀字符串和结尾序列号
                    string endPrefix_In = string.Empty;
                    string endSerialNo_In = string.Empty;
                    //获取 endPrefix_In 和 endSerialNo_In 的值
                    GetInvoiceNoPrefixAndSerial(detail.InvoiceNoEnd, out endPrefix_In, out endSerialNo_In);
                    if (endPrefix_In != prefix || endSerialNo_In != endSerialNo)
                        throw new ApiException("输入的发票编号不正确");
                }
                else/*用户未输入发票结束编号，使用计算的发票结尾编号填充到detail中*/
                    detail.InvoiceNoEnd = prefix + endSerialNo;
                //验证发放份数不能小于已使用发票号中的最大号
                var usedInvoiceNoList = curDetail.entities.Where(e => e.State == EnumInvoiceEntityState.Used || e.State == EnumInvoiceEntityState.Void).Select(e => e.InvoiceNo).ToList();
                if (usedInvoiceNoList.Count > 0)
                {
                    var usedInvoiceSerialNoList = new List<int>();
                    usedInvoiceNoList.ForEach(invNo =>
                    {
                        if (string.IsNullOrEmpty(prefix))
                            usedInvoiceSerialNoList.Add(invNo.ToInt());
                        else 
                        {
                            var serial = invNo.Replace(prefix, "");
                            if (Regex.IsMatch(serial, @"^[0-9]+$"))
                                usedInvoiceSerialNoList.Add(serial.ToInt());
                            else
                                throw new ApiException("输入的发票编号不正确");
                        }
                    });
                    if (usedInvoiceSerialNoList.Max() > endSerialNo.ToInt())
                        throw new ApiException("发放份数不能小于已使用发票号中的最大号");
                }
                
                //获取当前数据库中发票结束编号的序列号
                var curEndSerialNo = curDetail.InvoiceNoEnd.Remove(0, prefix.Length);
                //判断修改后的发票份数和原发票份数的大小
                if (curDetail.IssuedNum < detail.IssuedNum)
                {/*增加了发票原件实体*/
                    //循环向发票实体列表插入发票
                    for (int i = curEndSerialNo.ToInt() + 1; i <= endSerialNo.ToInt(); i++)
                    {
                        //新增的发票编号
                        var invoiceNo = prefix + i.ToString().PadLeft(startSerialNo.Length, '0');
                        //验证当前记录的发票编号是否存在重复
                        if (detail.InvoiceType == EnumInvoiceType.SpecialTicket)
                        {
                            //验证当前记录的发票编号是否存在重复
                            if (speInvoiceNoList.Any(e => e == invoiceNo))
                                throw new ApiException("存在重复输入的发票编号,请重新核对");
                            speInvoiceNoList.Add(invoiceNo);
                        }
                        else if (detail.InvoiceType == EnumInvoiceType.UniversalTicket)
                        {
                            //验证当前记录的发票编号是否存在重复
                            if (uniInvoiceNoList.Any(e => e == invoiceNo))
                                throw new ApiException("存在重复输入的发票编号,请重新核对");
                            uniInvoiceNoList.Add(invoiceNo);
                        }
                        //拼装插入的entity对象
                        var entity = AssemblyEntity(updInfo_In.Id, detail.Id, invoiceNo);
                        //entity的insert队列
                        DbOpe_crm_original_invoice_details_entity.Instance.InsertQueue(entity);
                    }
                }
                else if (curDetail.IssuedNum > detail.IssuedNum)
                {/*减少了发票原件实体*/
                    //循环向发票实体列表插入发票
                    for (int i = endSerialNo.ToInt() + 1; i <= curEndSerialNo.ToInt(); i++)
                    {
                        //待删除的发票编号
                        var invoiceNo = prefix + i.ToString().PadLeft(startSerialNo.Length, '0');
                        //判断当前明细是专票还是普票
                        if (detail.InvoiceType == EnumInvoiceType.UniversalTicket)
                            tobeDelInUpdUniInvoiceNoList.Add(invoiceNo);
                        else if (detail.InvoiceType == EnumInvoiceType.SpecialTicket)
                            tobeDelInUpdSpeInvoiceNoList.Add(invoiceNo);
                        //获取待删除的发票原件实体Id
                        var entityId = curDetail.entities.Where(e => e.InvoiceNo == invoiceNo).Select(e => e.Id).First();
                        //添加到删除队列
                        DbOpe_crm_original_invoice_details_entity.Instance.DeleteById(entityId, UserId);
                    }
                }
                //根据Id修改发票原件明细数据
                DbOpe_crm_original_invoice_details.Instance.UpdateDetailById(detail, UserId);
            });

            //处理发票原件明细新增
            tobeInsDetails.ForEach(detail =>
            {
                detail.Id = Guid.NewGuid().ToString();
                //声明发票开始编号的前缀字符串和结尾序列号
                string prefix = string.Empty;
                string startSerialNo = string.Empty;
                //获取startPrefix和startSerialNo的值
                GetInvoiceNoPrefixAndSerial(detail.InvoiceNoStart, out prefix, out startSerialNo);
                //根据发放份数，计算发票结束编号的结尾序列号
                string endSerialNo = (startSerialNo.ToInt() + detail.IssuedNum - 1).ToString().PadLeft(startSerialNo.Length, '0');
                //若用户填写发票结束编号，验证结束编号是否正确
                if (!string.IsNullOrEmpty(detail.InvoiceNoEnd))
                {/*用户输入发票结束编号*/
                    //声明用户输入的发票结束编号的前缀字符串和结尾序列号
                    string endPrefix_In = string.Empty;
                    string endSerialNo_In = string.Empty;
                    //获取 endPrefix_In 和 endSerialNo_In 的值
                    GetInvoiceNoPrefixAndSerial(detail.InvoiceNoEnd, out endPrefix_In, out endSerialNo_In);
                    if (endPrefix_In != prefix || endSerialNo_In != endSerialNo)
                        throw new ApiException("输入的发票编号不正确");
                }
                else/*用户未输入发票结束编号，使用计算的发票结尾编号填充到detail中*/
                    detail.InvoiceNoEnd = prefix + endSerialNo;
                //循环向发票实体列表插入发票
                for (int i = startSerialNo.ToInt(); i <= endSerialNo.ToInt(); i++)
                {
                    var invoiceNo = prefix + i.ToString().PadLeft(startSerialNo.Length, '0');
                    if (detail.InvoiceType == EnumInvoiceType.SpecialTicket)
                    {
                        //验证当前记录的发票编号是否存在重复
                        if (speInvoiceNoList.Any(e => e == invoiceNo))
                            throw new ApiException("存在重复输入的发票编号,请重新核对");
                        speInvoiceNoList.Add(invoiceNo);
                    }
                    else if (detail.InvoiceType == EnumInvoiceType.UniversalTicket)
                    {
                        //验证当前记录的发票编号是否存在重复
                        if (uniInvoiceNoList.Any(e => e == invoiceNo))
                            throw new ApiException("存在重复输入的发票编号,请重新核对");
                        uniInvoiceNoList.Add(invoiceNo);
                    }
                    //拼装插入的entity对象
                    var entity = AssemblyEntity(updInfo_In.Id, detail.Id, invoiceNo);
                    //entity的insert队列
                    DbOpe_crm_original_invoice_details_entity.Instance.InsertQueue(entity);
                }
                //detail的insert队列
                var insDetail = detail.MappingTo<Db_crm_original_invoice_details>();
                insDetail.OriginalInvoiceId = updInfo_In.Id;
                insDetail.Deleted = false;
                insDetail.CreateUser = UserId;
                insDetail.CreateDate = DateTime.Now;
                DbOpe_crm_original_invoice_details.Instance.InsertQueue(insDetail);
            });
            //验证专票发票编号是否重复
            if (DbOpe_crm_original_invoice_details_entity.Instance.CheckRepeatInvoiceNo(speInvoiceNoList, EnumInvoiceType.SpecialTicket, tobeDelSpeInvoiceNoList, tobeDelInUpdSpeInvoiceNoList))
                throw new ApiException("输入的发票号与现有发票号重复，请重新核对");
            //验证普票发票编号是否重复
            if (DbOpe_crm_original_invoice_details_entity.Instance.CheckRepeatInvoiceNo(uniInvoiceNoList, EnumInvoiceType.UniversalTicket, tobeDelUniInvoiceNoList, tobeDelInUpdUniInvoiceNoList))
                throw new ApiException("输入的发票号与现有发票号重复，请重新核对");
            //根据Id修改发票原件的接收公司和备注
            DbOpe_crm_original_invoice.Instance.UpdateOriginalInvoiceById(updInfo_In, UserId);
            //提交sql队列
            DbOpe_crm_original_invoice.Instance.SaveQueues();
        }

        /// <summary>
        /// 拼装插入的entity对象
        /// </summary>
        /// <param name="oriInvId"></param>
        /// <param name="oriInvDetailsId"></param>
        /// <param name="invoiceNo"></param>
        /// <returns></returns>
        private Db_crm_original_invoice_details_entity AssemblyEntity(string oriInvId, string oriInvDetailsId, string invoiceNo)
        {
            var entity = new Db_crm_original_invoice_details_entity();
            entity.Id = Guid.NewGuid().ToString();
            entity.OriginalInvoiceId = oriInvId;
            entity.OriginalInvoiceDetailsId = oriInvDetailsId;
            entity.InvoiceNo = invoiceNo;
            entity.State = EnumInvoiceEntityState.UnUsed;
            entity.Deleted = false;
            entity.CreateUser = UserId;
            entity.CreateDate = DateTime.Now;
            return entity;
        }

        /// <summary>
        /// 根据传入发票号，分离出发票号前缀及流水号
        /// </summary>
        /// <param name="invoiceNo"></param>
        /// <param name="prefix">前缀</param>
        /// <param name="serialNo">流水号</param>
        private void GetInvoiceNoPrefixAndSerial(string invoiceNo, out string prefix, out string serialNo)
        {
            //获取发票号长度
            var length = invoiceNo.Length;
            //将返回值暂时赋空值
            prefix = "";
            serialNo = "";
            //从最后一个字符开始循环发票号字符，进行拆解
            for (int i = length - 1; i >= 0; i--)
            {
                //判断当前字符是否是字母类型，如果是，则在此截断
                if (Regex.IsMatch(invoiceNo.Substring(i, 1), "[a-zA-Z]+$"))
                {
                    //当前字符及之前所有字符组成发票号前缀
                    prefix = invoiceNo.Substring(0, (i + 1));
                    //当前字符之后的所有字符组成发票号流水号
                    serialNo = invoiceNo.Substring(i + 1);
                    //跳出循环
                    break;
                }
            }
            //如果循环结束后，发票号前缀仍为空，则整个发票号均为流水号
            if (string.IsNullOrEmpty(prefix))
                serialNo = invoiceNo;
        }

        /// <summary>
        /// 删除发票原件信息
        /// </summary>
        /// <param name="Ids"></param>
        /// <exception cref="ApiException"></exception>
        public void DeleteOriginalInvoice(string Ids)
        {
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("未选择要删除的发票原件");
            var idList = Ids.Split(',').ToList();
            //验证每个Id关联的发票实体是否是已使用状态，如果有则不可以删除
            if (DbOpe_crm_original_invoice.Instance.CheckAllowDelete(idList))
                throw new ApiException("所选数据存在已经使用的发票，无法删除");
            //删除发票原件实体
            DbOpe_crm_original_invoice_details_entity.Instance.DeleteByOriInvIds(idList, UserId);
            //删除发票原件明细
            DbOpe_crm_original_invoice_details.Instance.DeleteByOriInvIds(idList, UserId);
            //删除发票原件
            DbOpe_crm_original_invoice.Instance.DeleteByOriInvIds(idList, UserId);
            //提交sql队列
            DbOpe_crm_original_invoice.Instance.SaveQueues();

        }
    }


}

