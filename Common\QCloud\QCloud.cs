﻿using COSXML.Model.Service;
using COSXML.Model.Tag;
using System.Threading.Tasks;
using COSXML.Model.Bucket;
using COSXML.Model.Object;
using COSXML.Transfer;
using CRM2_API.Common.AppSetting;
using System.IO;

namespace QCloud
{
    public class QCloudOperator
    {
        //private string BUCKETNAME = "CRM-1330617026";
        private string BUCKETNAME = "hqhs-1330617026";
        private string APPID = "1330617026";
        private string PREFIX = AppSettings.Env == Enum_SystemSettingEnv.Product ? "CRM/" : "CRMTEST/";
        #region 测试
        public string GetPreSignUrl(string dbFilePath)
        {
            String key = GetKey(dbFilePath);
            PreSignatureStruct preSignatureStruct = new PreSignatureStruct();
            preSignatureStruct.appid = APPID;//"1250000000"; //腾讯云账号 APPID
            preSignatureStruct.region = AppSettings.QCloud.Region;//"COS_REGION"; //存储桶地域
            preSignatureStruct.bucket = BUCKETNAME;// "examplebucket-1250000000"; //存储桶
            preSignatureStruct.key = key; //对象键
            preSignatureStruct.httpMethod = "GET"; //HTTP 请求方法
            preSignatureStruct.isHttps = true; //生成 HTTPS 请求 URL
            preSignatureStruct.signDurationSecond = 600; //请求签名时间为600s
            preSignatureStruct.headers = null; //签名中需要校验的 header
            preSignatureStruct.queryParameters = null; //签名中需要校验的 URL 中请求参数
            string requestSignURL = CustomQCloudCredentialProvider.getInstance().GenerateSignURL(preSignatureStruct);
            Console.WriteLine(requestSignURL);
            return requestSignURL;
        }
        public void GetBuckets()
        {
            try
            {
                GetServiceRequest request = new GetServiceRequest();
                //执行请求
                GetServiceResult result = CustomQCloudCredentialProvider.getInstance().GetService(request);
                //得到所有的 buckets
                List<ListAllMyBuckets.Bucket> allBuckets = result.listAllMyBuckets.buckets;
            }
            catch (COSXML.CosException.CosClientException clientEx)
            {
                //请求失败
                Console.WriteLine("CosClientException: " + clientEx);
            }
            catch (COSXML.CosException.CosServerException serverEx)
            {
                //请求失败
                Console.WriteLine("CosServerException: " + serverEx.GetInfo());
            }
        }
        /// 获取存储桶信息
        public void HeadBucket()
        {
            try
            {
                HeadBucketRequest request = new HeadBucketRequest(BUCKETNAME);
                //执行请求
                HeadBucketResult result = CustomQCloudCredentialProvider.getInstance().HeadBucket(request);
                //请求成功
                Console.WriteLine(result.GetResultInfo());
            }
            catch (COSXML.CosException.CosClientException clientEx)
            {
                //请求失败
                Console.WriteLine("CosClientException: " + clientEx);
            }
            catch (COSXML.CosException.CosServerException serverEx)
            {
                //请求失败
                Console.WriteLine("CosServerException: " + serverEx.GetInfo());
            }

            //.cssg-snippet-body-end
        }
        public async Task<string> UploadFromMemory(string fileName = "test.png", string fileType = "TestContract")
        {
            // 初始化 TransferConfig
            TransferConfig transferConfig = new TransferConfig();

            // 初始化 TransferManager
            TransferManager transferManager = new TransferManager(CustomQCloudCredentialProvider.getInstance(), transferConfig);

            String cosPath = PREFIX + fileType + "/" + fileName; //对象在存储桶中的位置标识符，即称对象键
            String srcPath = @"C:\Users\<USER>\Desktop\test.png";//本地文件绝对路径

            // 上传对象
            COSXMLUploadTask uploadTask = new COSXMLUploadTask(BUCKETNAME, cosPath);
            uploadTask.SetSrcPath(srcPath);

            uploadTask.progressCallback = delegate (long completed, long total)
            {
                Console.WriteLine(String.Format("progress = {0:##.##}%", completed * 100.0 / total));
            };

            try
            {
                COSXML.Transfer.COSXMLUploadTask.UploadTaskResult result = await
                  transferManager.UploadAsync(uploadTask);
                Console.WriteLine(result.GetResultInfo());
                string eTag = result.eTag;
                return eTag;
            }
            catch (Exception e)
            {
                Console.WriteLine("CosException: " + e);
                return "error";
            }
        }
        public void DownloadToMemory(string fileName = "test.png", string fileType = "TestContract")
        {
            try
            {
                String key = PREFIX + fileType + "/" + fileName; //对象在存储桶中的位置标识符，即称对象键
                GetObjectBytesRequest request = new GetObjectBytesRequest(BUCKETNAME, key);
                //设置进度回调
                request.SetCosProgressCallback(delegate (long completed, long total)
                {
                    Console.WriteLine(String.Format("progress = {0:##.##}%", completed * 100.0 / total));
                });
                //执行请求
                GetObjectBytesResult result = CustomQCloudCredentialProvider.getInstance().GetObject(request);
                //获取内容到 byte 数组中
                byte[] content = result.content;
                //请求成功
                Console.WriteLine(result.GetResultInfo());
            }
            catch (COSXML.CosException.CosClientException clientEx)
            {
                Console.WriteLine("CosClientException: " + clientEx);
            }
            catch (COSXML.CosException.CosServerException serverEx)
            {
                Console.WriteLine("CosServerException: " + serverEx.GetInfo());
            }
        }
        public void UploadBytes(string dbFilePath, byte[] data)
        {
            String key = GetKey(dbFilePath);
            PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKETNAME, key, data);
            // 发起上传
            PutObjectResult result = CustomQCloudCredentialProvider.getInstance().PutObject(putObjectRequest);
            Console.WriteLine(result.GetResultInfo());
        }
        #endregion




        public void UploadStream(string dbFilePath, Stream stream,string contentType)
        {

            String key = GetKey(dbFilePath);
            // 组装上传请求，其中 offset sendLength 为可选参数
            long offset = 0L;
            long sendLength = stream.Length;

            PutObjectRequest request = new PutObjectRequest(BUCKETNAME, key, stream, offset, sendLength);
            //request.SetRequestHeader("Content-Disposition", "inline");
            //request.SetRequestHeader("Content-Type", contentType);
            //设置进度回调
            request.SetCosProgressCallback(delegate (long completed, long total)
            {
                Console.WriteLine(String.Format("progress = {0:##.##}%", completed * 100.0 / total));
            });
            //执行请求
            PutObjectResult result = CustomQCloudCredentialProvider.getInstance().PutObject(request);
            //关闭文件流
            stream.Close();
            //打印请求结果
            Console.WriteLine(result.GetResultInfo());
        }



        public byte[] OpenRead(string filePath)
        {
            if (Exists(filePath))
            {
                String key = GetKey(filePath);

                //#region 下载文件测试
                //string localDir = "C:\\Users\\<USER>\\Desktop\\桌面文件";//本地文件夹
                //string localFileName = "test"; //指定本地保存的文件名
                //GetObjectRequest frequest = new GetObjectRequest(BUCKETNAME, key, localDir, localFileName);
                //frequest.SetCosProgressCallback(delegate (long completed, long total)
                //{
                //    Console.WriteLine(String.Format("progress = {0:##.##}%", completed * 100.0 / total));
                //});
                ////执行请求
                //GetObjectResult fresult = CustomQCloudCredentialProvider.getInstance().GetObject(frequest);
                //Console.WriteLine(fresult.GetResultInfo());
                //#endregion




                GetObjectBytesRequest request = new GetObjectBytesRequest(BUCKETNAME, key);
                //设置进度回调
                request.SetCosProgressCallback(delegate (long completed, long total)
                {
                    Console.WriteLine(String.Format("progress = {0:##.##}%", completed * 100.0 / total));
                });
                //执行请求
                GetObjectBytesResult result = CustomQCloudCredentialProvider.getInstance().GetObject(request);
                //获取内容到 byte 数组中
                byte[] content = result.content;

                // 设置文件流结果
                return content;

            }
            else
            {
                throw new Exception("文件不存在");
            }
        }
        public bool Exists(string filePath)
        {
            string key = GetKey(filePath);
            DoesObjectExistRequest request = new DoesObjectExistRequest(BUCKETNAME, key);
            //执行请求
            bool exist = CustomQCloudCredentialProvider.getInstance().DoesObjectExist(request);
            //请求成功
            return exist;
        }

        /// <summary>
        /// 删除对象
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        public bool DeleteFile(string filePath)
        {
            try
            {
                string key = GetKey(filePath);
                DeleteObjectRequest request = new DeleteObjectRequest(BUCKETNAME, key);
                //执行请求
                DeleteObjectResult result = CustomQCloudCredentialProvider.getInstance().DeleteObject(request);
                //请求成功
                Console.WriteLine(result.GetResultInfo());
                return true;
            }
            catch (COSXML.CosException.CosClientException clientEx)
            {
                //请求失败
                Console.WriteLine("CosClientException: " + clientEx);
                return false;
            }
            catch (COSXML.CosException.CosServerException serverEx)
            {
                //请求失败
                Console.WriteLine("CosServerException: " + serverEx.GetInfo());
                return false;
            }
        }
        private string GetKey(string dbFilePath)
        {
            if (string.IsNullOrEmpty(dbFilePath))
            {
                throw new Exception("路径为空");
            }
            else
            {
                var filePath = dbFilePath;
                if (dbFilePath.StartsWith("/"))
                {
                    filePath = dbFilePath.Substring(1);
                }
                return PREFIX + filePath; //对象键
            }
        }
    }
}
