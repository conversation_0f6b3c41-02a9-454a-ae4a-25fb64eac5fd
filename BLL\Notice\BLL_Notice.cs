﻿using System.IO;
using System.Text.RegularExpressions;
using System.Web;
using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Notice;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Drawing.Charts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.BLL.Notice
{
    public class BLL_Notice : BaseBLL<BLL_Notice>
    {
        private static readonly object lockobj = new();

        private static readonly string NoticeTextKey = "NOTICE_TEXT_";

        /// <summary>
        /// 添加公告通知信息
        /// </summary>
        /// <param name="addNoticeIn"></param>
        /// <returns></returns>
        public AddNotice_Out AddNotice(AddNotice_In addNoticeIn)
        {
            AddNotice_Out result = new() { Data = 0 };
            var insertId = addNoticeIn.Id ?? Guid.NewGuid().ToString();
            var newEntity = new Db_crm_notice
            {
                Id = insertId,
                UserId = UserTokenInfo.id,
                Title = addNoticeIn.Title,
                Type = addNoticeIn.Type,
                SendRange = addNoticeIn.SendRange,
                Content = addNoticeIn.Content,
                State = addNoticeIn.SubmitState,
                Deleted = false,
                CreateUser = UserTokenInfo.id,
                CreateDate = DateTime.Now,
            };
            //接收人处理
            if (addNoticeIn.SendRange == EnumNoticeSendRange.Part)
            {
                if (string.IsNullOrEmpty(addNoticeIn.NoticeReceiver))
                {
                    throw new ApiException("公告通知接收人为空");
                }
                List<string> userIdList = addNoticeIn.NoticeReceiver.Split(',').Distinct().ToList();
                //校验用户列表是否存在
                bool checkUserResult = CheckUserListExists(userIdList);
                if (!checkUserResult)
                {
                    throw new ApiException("指定的通知接收人不存在!");
                }

                List<Db_crm_notice_receiver> insertReceiver = new(userIdList.Count);
                Db_crm_notice_receiver temp = null;
                foreach (string userId in userIdList)
                {
                    temp = new()
                    {
                        Id = Guid.NewGuid().ToString(),
                        NoticeId = insertId,
                        UserId = userId,
                        State = 0,
                        Deleted = false,
                        CreateUser = UserTokenInfo.id,
                        CreateDate = DateTime.Now,
                    };
                    insertReceiver.Add(temp);
                }
                DbOpe_crm_notice_receiver.Instance.InsertQueue(insertReceiver);
            }
            //文件处理
            if (addNoticeIn.NoticeAttachFile is not null and { Count: > 0 })
            {
                Util<DbOpe_crm_notice_attachfile, BM_AttachFiles> util = new(DbOpe_crm_notice_attachfile.Instance);
                bool uploadResult = util.UploadFiles(insertId, addNoticeIn.NoticeAttachFile, AttachEnumOption.Notice);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
            }
            lock (lockobj)
            {
                newEntity.NoticeNo = (DbOpe_crm_notice.Instance.GetQueryCount(newEntity) + 1).ToString().PadLeft(5, '0');
                DbOpe_crm_notice.Instance.InsertQueue(newEntity);
                DbOpe_crm_notice.Instance.SaveQueues();
            }
            result.Data = 1;
            return result;
        }

        /// <summary>
        /// 修改公告通知信息
        /// </summary>
        /// <param name="updateNoticeIn"></param>
        /// <returns></returns>
        public UpdateNotice_Out UpdateNotice(UpdateNotice_In updateNoticeIn)
        {
            UpdateNotice_Out result = new() { Data = 0 };
            //判断数据是否存在
            var dbData = DbOpe_crm_notice.Instance.QueryByPrimaryKey(updateNoticeIn.Id) ??
                         throw new ApiException("公告通知表Id所对应的数据不存在!");
            //如果新的是全员通知的话，
            //判断以前的通知是全员还是部分
            //全员的不动
            //以前是部分通知则将以前的移除-
            //如果是部分通知-
            //判断以前的通知是全员还是部分
            //全员则直接新增
            //部分则两个集合进行比较 交集不动 修改更新时间
            //  左边的热删除      右边的新增

            //对接收人的处理
            if (updateNoticeIn.SendRange == EnumNoticeSendRange.All)
            {
                if (dbData.SendRange == EnumNoticeSendRange.Part)
                {
                    //将通知表的数据进行移除
                    List<Db_crm_notice_receiver> noticeReceiverList = DbOpe_crm_notice_receiver.Instance.GetNoticeReceiverByNoticeId(updateNoticeIn.Id);
                    foreach (Db_crm_notice_receiver noticeReceiver in noticeReceiverList)
                    {
                        noticeReceiver.Deleted = true;
                        noticeReceiver.UpdateDate = DateTime.Now;
                        noticeReceiver.UpdateUser = UserTokenInfo.id;
                    }
                    DbOpe_crm_notice_receiver.Instance.UpdateQueue(noticeReceiverList);
                }
            }
            else if (updateNoticeIn.SendRange == EnumNoticeSendRange.Part)
            {
                if (string.IsNullOrEmpty(updateNoticeIn.NoticeReceiver))
                {
                    throw new ApiException("公告通知接收人为空");
                }
                List<string> userIdList = updateNoticeIn.NoticeReceiver.Split(',').Distinct().ToList();
                //校验用户列表是否存在
                bool checkUserResult = CheckUserListExists(userIdList);
                if (!checkUserResult)
                {
                    throw new ApiException("指定的通知接收人不存在!");
                }
                if (dbData.SendRange == EnumNoticeSendRange.All)
                {
                    //直接新增
                    List<Db_crm_notice_receiver> insertReceiver = new(userIdList.Count);
                    Db_crm_notice_receiver temp = null;
                    foreach (string userId in userIdList)
                    {
                        temp = new()
                        {
                            Id = Guid.NewGuid().ToString(),
                            NoticeId = updateNoticeIn.Id,
                            UserId = userId,
                            State = 0,
                            Deleted = false,
                            CreateUser = UserTokenInfo.id,
                            CreateDate = DateTime.Now,
                        };
                        insertReceiver.Add(temp);
                    }
                    DbOpe_crm_notice_receiver.Instance.InsertQueue(insertReceiver);
                }
                else if (dbData.SendRange == EnumNoticeSendRange.Part)
                {
                    //部分则两个集合进行比较 交集不动 修改更新时间 左边的热删除 右边的新增
                    //1.获取原有的通知人列表
                    List<Db_crm_notice_receiver> noticeReceiverSourceList = DbOpe_crm_notice_receiver.Instance.GetNoticeReceiverByNoticeId(updateNoticeIn.Id);
                    //2.获取已有的[不动，修改下更新时间]
                    List<Db_crm_notice_receiver> noticeReceiverSourceUpdateList = noticeReceiverSourceList.IntersectBy(userIdList, i => i.UserId).ToList();
                    //3.获取差集[标记删除]
                    List<Db_crm_notice_receiver> noticeReceiverSourceRemoveList =
                        noticeReceiverSourceList.Except(noticeReceiverSourceUpdateList).ToList();
                    //4.获取新增的用户[插入]
                    var userIdInsertList = userIdList.ExceptBy(noticeReceiverSourceList.Select(s => s.UserId).ToList(), i => i).ToList();
                    //5.标记更新
                    foreach (Db_crm_notice_receiver noticeReceiver in noticeReceiverSourceUpdateList)
                    {
                        noticeReceiver.UpdateDate = DateTime.Now;
                        noticeReceiver.UpdateUser = UserTokenInfo.id;
                    }

                    if (noticeReceiverSourceUpdateList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_notice_receiver.Instance.UpdateQueue(noticeReceiverSourceUpdateList);
                    }

                    //6.标记删除
                    foreach (Db_crm_notice_receiver noticeReceiver in noticeReceiverSourceRemoveList)
                    {
                        noticeReceiver.Deleted = true;
                        noticeReceiver.UpdateDate = DateTime.Now;
                        noticeReceiver.UpdateUser = UserTokenInfo.id;
                    }

                    if (noticeReceiverSourceRemoveList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_notice_receiver.Instance.UpdateQueue(noticeReceiverSourceRemoveList);
                    }

                    //7.标记新增
                    List<Db_crm_notice_receiver> insertReceiver = new(userIdInsertList.Count);
                    Db_crm_notice_receiver temp = null;
                    foreach (string userId in userIdInsertList)
                    {
                        temp = new()
                        {
                            Id = Guid.NewGuid().ToString(),
                            NoticeId = updateNoticeIn.Id,
                            UserId = userId,
                            State = 0,
                            Deleted = false,
                            CreateUser = UserTokenInfo.id,
                            CreateDate = DateTime.Now,
                        };
                        insertReceiver.Add(temp);
                    }

                    if (insertReceiver is not null and { Count: > 0 })
                    {
                        DbOpe_crm_notice_receiver.Instance.InsertQueue(insertReceiver);
                    }
                }
            }

            //修改通知数据
            dbData.Title = updateNoticeIn.Title;
            dbData.Type = updateNoticeIn.Type;
            dbData.SendRange = updateNoticeIn.SendRange;
            dbData.Content = updateNoticeIn.Content;
            dbData.State = updateNoticeIn.SubmitState;
            dbData.UpdateUser = UserTokenInfo.id;
            dbData.UpdateDate = DateTime.Now;
            //end
            DbOpe_crm_notice.Instance.UpdateQueue(dbData);

            /*
            //文件处理
            //对以前的文件进行移除
            DbOpe_crm_notice_attachfile.Instance.RemoveNoticeAttachfileByNoticeId(updateNoticeIn.Id);
            if (updateNoticeIn.NoticeAttachFile is not null and { Count: > 0 })
            {
                Util<DbOpe_crm_notice_attachfile, BM_AttachFiles> util = new (DbOpe_crm_notice_attachfile.Instance);
                bool uploadResult = util.UploadFiles(updateNoticeIn.Id, updateNoticeIn.NoticeAttachFile, AttachEnumOption.Notice);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
            }*/

            //附件操作
            var attachFileList = DbOpe_crm_notice_attachfile.Instance.GetNoticeAttachFilesByNoticeid(updateNoticeIn.Id);
            var attachFileIdList = attachFileList.Select(w => w.Id).ToList();
            if (updateNoticeIn.NoticeAttachFile is null or { Count: 0 })
            {
                if (attachFileIdList is not null and { Count: > 0 })
                {
                    DbOpe_crm_notice_attachfile.Instance.DeleteAttachFileByIdList(attachFileIdList);
                }
            }
            else
            {
                var attachFileUpdateIdList = new List<string>();
                FormFileCollection formFileCollection = new();
                foreach (Update_NoticeAttachFile updateNoticeAttachFile in updateNoticeIn.NoticeAttachFile)
                {
                    if (attachFileIdList.Contains(updateNoticeAttachFile.Id))
                    {
                        attachFileUpdateIdList.Add(updateNoticeAttachFile.Id);
                        continue;
                    }

                    if (updateNoticeAttachFile.File != null)
                    {
                        formFileCollection.Add(updateNoticeAttachFile.File);
                    }

                }

                if (formFileCollection.Count > 0)
                {
                    Util<DbOpe_crm_notice_attachfile, BM_AttachFiles> util = new(DbOpe_crm_notice_attachfile.Instance);
                    bool uploadResult = util.UploadFiles(updateNoticeIn.Id, formFileCollection, AttachEnumOption.Notice);
                    if (!uploadResult)
                    {
                        throw new ApiException("文件上传异常!");
                    }
                }
                var attachFileDeleteIdList = attachFileIdList.Except(attachFileUpdateIdList).ToList();
                DbOpe_crm_notice_attachfile.Instance.DeleteAttachFileByIdList(attachFileDeleteIdList);
            }

            int execCount = DbOpe_crm_notice.Instance.SaveQueues();
            if (execCount > 0)
            {
                result.Data = 1;
            }

            return result;
        }

        /// <summary>
        /// 删除公告通知信息
        /// </summary>
        /// <param name="noticeId"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public DeleteNotice_Out DeleteNotice(string noticeId)
        {
            DeleteNotice_Out result = new() { Data = 0 };
            string[] noticeList = noticeId.Split(',');
            foreach (string oneID in noticeList)
            {
                //判断数据是否存在
                var dbData = DbOpe_crm_notice.Instance.QueryByPrimaryKey(oneID) ??
                             throw new ApiException("公告通知表Id所对应的数据不存在!");
                if (dbData.State != EnumNoticeState.Draft)
                {
                    throw new ApiException("非草稿状态下的公告通知禁止删除!");
                }
                DbOpe_crm_notice.Instance.DeleteNotice(oneID);
                DbOpe_crm_notice_receiver.Instance.DeleteNoticeReceiverByNoticeId(oneID);
                DbOpe_crm_notice_attachfile.Instance.RemoveNoticeAttachfileByNoticeId(oneID);
            }
            int exec = DbOpe_crm_notice.Instance.SaveQueues();
            if (exec > 0)
            {
                result.Data = 1;
            }
            return result;
        }

        /// <summary>
        /// 撤销公告通知信息，当发布状态为发布状态，可操作，撤销后状态变更为撤销
        /// </summary>
        /// <param name="noticeId"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public RevokeNotice_Out RevokeNotice(string noticeId)
        {
            RevokeNotice_Out result = new() { Data = 0 };
            string[] noticeList = noticeId.Split(',');
            foreach (string oneID in noticeList)
            {
                //判断数据是否存在
                var dbData = DbOpe_crm_notice.Instance.QueryByPrimaryKey(oneID) ??
                         throw new ApiException("公告通知表Id所对应的数据不存在!");
                if (dbData.State != EnumNoticeState.Release)
                {
                    throw new ApiException("当前公告通知状态禁止撤销!");
                }
                result = DbOpe_crm_notice.Instance.RevokeNotice(oneID);
            }
            return result;
        }

        /// <summary>
        /// 校验用户列表是否存在
        /// </summary>
        /// <param name="userIdList"></param>
        /// <returns>全部存在则返回true,有一个不存在直接异常处理[想不准确就返回false]。</returns>
        private bool CheckUserListExists(List<string> userIdList)
        {
            bool result = true;
            if (userIdList is null or { Count: 0 })
            {
                throw new ArgumentNullException("用户id列表不可为空!");
            }

            Dictionary<string, Db_sys_user> userDict = DbOpe_crm_notice.Instance.getUserDictByIdList(userIdList);
            foreach (string userId in userIdList)
            {
                if (userDict.ContainsKey(userId)) continue;
                result = !result;
                throw new ApiException($"对应的用户信息不存在!");
            }
            return result;
        }



        /// <summary>
        /// 方案1 - 预览
        /// </summary>
        /// <param name="attId"></param>
        /// <param name="response"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public IActionResult DownLoad(string attId, HttpResponse response)
        {
            Db_crm_notice_attachfile att = DbOpe_crm_notice_attachfile.Instance.QueryByPrimaryKey(attId);
            if (att is not null)
            {
                if (!File.Exists(att.FilePath))
                {
                    return new JsonResult(new
                    {
                        Msg = "文件不存在!"
                    });
                }
                var stream = File.OpenRead(att.FilePath);
                /*string fileExt = att.FileExtension;
                var provider = new FileExtensionContentTypeProvider();
                var contentType = provider.Mappings[$".{fileExt}"];*/
                string encodeFilename = HttpUtility.UrlEncode(att.FileName, Encoding.GetEncoding("UTF-8"));
                response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                return new FileStreamResult(stream, "application/octet-stream");
            }
            return new JsonResult(new
            {
                Msg = "文件不存在!"
            });
        }

        /// <summary>
        /// 方案1 - 上传
        /// </summary>
        /// <param name="upload"></param>
        /// <returns></returns>
        public IActionResult UploadImage(NoticeTextImageUpload upload)
        {
            string insertId = Guid.NewGuid().ToString();
            SnowflakeId s = new(2, 2);
            //string fileSavePath = AppSettings.fileSavePath ?? "/FileUpload";
            string fileSavePath = "/FileUpload";
            string fileHead = $"{fileSavePath}/NoticeText/{DateTime.Now:yyyyMMdd}";
            //设置文件上传路径
            string fileName = upload.File.FileName;
            string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
            string saveFileName = $"{fileName.Substring(0, fileName.LastIndexOf("."))}_{s.NextId()}.{fileExtension}";
            string fullSaveFilePath = $"{fileHead}/{saveFileName}";

            ////创建文件夹，保存文件
            string? path = Path.GetDirectoryName(fullSaveFilePath);
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            //将流写入文件
            using var stream = File.OpenWrite(fullSaveFilePath);
            upload.File.CopyTo(stream);

            Db_crm_notice_attachfile attachfile = new()
            {
                Id = insertId,
                NoticeId = upload.NoticeId,
                Source = 2,
                FileName = fileName,
                FilePath = fullSaveFilePath,
                FileExtension = fileExtension,
                FileSize = (int)upload.File.Length,
                Deleted = false,
                CreateUser = UserTokenInfo.id,
                CreateDate = DateTime.Now
            };
            DbOpe_crm_notice_attachfile.Instance.Insert(attachfile);

            return new OkObjectResult(new
            {
                location = $"/Notice/DownLoad?attId={insertId}",
            });
        }

        /// <summary>
        /// 获取文件【方案2】
        /// </summary>
        /// <param name="attId"></param>
        /// <param name="noticeId"></param>
        /// <param name="response"></param>
        /// <returns></returns>
        public IActionResult DownLoadNew(string attId, string noticeId, HttpResponse response)
        {
            string cacheKey = $"{NoticeTextKey}{noticeId}";
            Db_crm_notice_attachfile att = default;
            if (RedisHelper.Exists(cacheKey))
            {
                List<Db_crm_notice_attachfile> cacheAttList = RedisHelper.LRange<Db_crm_notice_attachfile>(cacheKey, 0, -1).ToList();
                if (cacheAttList is not null and { Count: > 0 })
                {
                    att = cacheAttList.FirstOrDefault(w => w.Id.Equals(attId));
                }
            }

            if (att is null)
            {
                att = DbOpe_crm_notice_attachfile.Instance.QueryByPrimaryKey(attId);
            }

            if (att is not null)
            {
                if (!File.Exists(att.FilePath))
                {
                    return new JsonResult(new
                    {
                        Msg = "文件不存在!"
                    });
                }
                var stream = File.OpenRead(att.FilePath);
                /*string fileExt = att.FileExtension;
                var provider = new FileExtensionContentTypeProvider();
                var contentType = provider.Mappings[$".{fileExt}"];*/
                string encodeFilename = HttpUtility.UrlEncode(att.FileName, Encoding.GetEncoding("UTF-8"));
                response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                return new FileStreamResult(stream, "application/octet-stream");
            }
            return new JsonResult(new
            {
                Msg = "文件不存在!"
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="upload"></param>
        /// <returns></returns>
        public IActionResult UploadImageNew(NoticeTextImageUpload upload)
        {
            upload.NoticeId ??= Guid.NewGuid().ToString();
            string insertId = Guid.NewGuid().ToString();
            SnowflakeId s = new(2, 2);
            //string fileSavePath = AppSettings.fileSavePath ?? "/FileUpload";
            string fileSavePath = "/FileUpload";
            string fileHead = $"{fileSavePath}/NoticeText/{DateTime.Now:yyyyMMdd}";
            //设置文件上传路径
            string fileName = upload.File.FileName;
            string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
            string saveFileName = $"{fileName.Substring(0, fileName.LastIndexOf("."))}_{s.NextId()}.{fileExtension}";
            string fullSaveFilePath = $"{fileHead}/{saveFileName}";

            ////创建文件夹，保存文件
            string? path = Path.GetDirectoryName(fullSaveFilePath);
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            //将流写入文件
            using var stream = File.OpenWrite(fullSaveFilePath);
            upload.File.CopyTo(stream);

            Db_crm_notice_attachfile attachfile = new()
            {
                Id = insertId,
                NoticeId = upload.NoticeId,
                Source = 2,
                FileName = fileName,
                FilePath = fullSaveFilePath,
                FileExtension = fileExtension,
                FileSize = (int)upload.File.Length,
                Deleted = false,
                CreateUser = UserTokenInfo.id,
                CreateDate = DateTime.Now
            };

            string cacheKey = $"{NoticeTextKey}{upload.NoticeId}";
            RedisHelper.RPush(cacheKey, attachfile);
            RedisHelper.Expire(cacheKey, 60 * 60 * 24);    //1天过期

            return new OkObjectResult(new
            {
                code = StatusCodes.Status200OK,
                noticeId = upload.NoticeId,
                location = $"/Notice/DownLoadNew?attId={insertId}&noticeId={upload.NoticeId}",
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="noticeId"></param>
        /// <returns></returns>
        public IActionResult NoticeTextHeartbeat(string noticeId)
        {
            string cacheKey = $"{NoticeTextKey}{noticeId}";
            if (RedisHelper.Exists(cacheKey))
            {
                RedisHelper.Expire(cacheKey, 60 * 60 * 24);    //1天过期
                return new OkObjectResult(new
                {
                    code = StatusCodes.Status200OK,
                    noticeId,
                });
            }
            return new JsonResult(new
            {
                code = StatusCodes.Status404NotFound,
                Msg = "缓存key不存在!"
            });
        }

        /// <summary>
        /// 添加公告通知信息[方案2专属]
        /// </summary>
        /// <param name="addNoticeIn"></param>
        /// <returns></returns>
        public AddNotice_Out AddNoticeNew(AddNotice_In addNoticeIn)
        {
            if (string.IsNullOrEmpty(addNoticeIn.Id))
            {
                string cacheKey = $"{NoticeTextKey}{addNoticeIn.Id}";
                if (!string.IsNullOrEmpty(addNoticeIn.Content) && RedisHelper.Exists(cacheKey))
                {
                    List<Db_crm_notice_attachfile> cacheAttList = RedisHelper.LRange<Db_crm_notice_attachfile>(cacheKey, 0, -1).ToList();
                    List<string> saveIdList = GetAttIdListByContent(addNoticeIn.Content);
                    var saveAttList = cacheAttList.Where(w => saveIdList.Contains(w.Id)).ToList();
                    if (saveAttList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_notice_attachfile.Instance.InsertQueue(saveAttList);
                    }
                    RedisHelper.Del(cacheKey);
                }
                else
                {
                    throw new ApiException("添加公告信息Id非报备值"); //方案2的id非前端生成，若携带id并且缓存中不存在则该id为伪造。
                }
            }

            AddNotice_Out result = new() { Data = 0 };
            var insertId = addNoticeIn.Id ?? Guid.NewGuid().ToString();
            var newEntity = new Db_crm_notice
            {
                Id = insertId,
                UserId = UserTokenInfo.id,
                Title = addNoticeIn.Title,
                Type = addNoticeIn.Type,
                SendRange = addNoticeIn.SendRange,
                Content = addNoticeIn.Content,
                State = addNoticeIn.SubmitState,
                Deleted = false,
                CreateUser = UserTokenInfo.id,
                CreateDate = DateTime.Now,
            };
            //接收人处理
            if (addNoticeIn.SendRange == EnumNoticeSendRange.Part)
            {
                if (string.IsNullOrEmpty(addNoticeIn.NoticeReceiver))
                {
                    throw new ApiException("公告通知接收人为空");
                }
                List<string> userIdList = addNoticeIn.NoticeReceiver.Split(',').Distinct().ToList();
                //校验用户列表是否存在
                bool checkUserResult = CheckUserListExists(userIdList);
                if (!checkUserResult)
                {
                    throw new ApiException("指定的通知接收人不存在!");
                }

                List<Db_crm_notice_receiver> insertReceiver = new(userIdList.Count);
                Db_crm_notice_receiver temp = null;
                foreach (string userId in userIdList)
                {
                    temp = new()
                    {
                        Id = Guid.NewGuid().ToString(),
                        NoticeId = insertId,
                        UserId = userId,
                        State = 0,
                        Deleted = false,
                        CreateUser = UserTokenInfo.id,
                        CreateDate = DateTime.Now,
                    };
                    insertReceiver.Add(temp);
                }
                DbOpe_crm_notice_receiver.Instance.InsertQueue(insertReceiver);
            }
            //文件处理
            if (addNoticeIn.NoticeAttachFile is not null and { Count: > 0 })
            {
                Util<DbOpe_crm_notice_attachfile, BM_AttachFiles> util = new(DbOpe_crm_notice_attachfile.Instance);
                bool uploadResult = util.UploadFiles(insertId, addNoticeIn.NoticeAttachFile, AttachEnumOption.Notice);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
            }
            lock (lockobj)
            {
                newEntity.NoticeNo = (DbOpe_crm_notice.Instance.GetQueryCount(newEntity) + 1).ToString().PadLeft(5, '0');
                DbOpe_crm_notice.Instance.InsertQueue(newEntity);
                DbOpe_crm_notice.Instance.SaveQueues();
            }
            result.Data = 1;
            return result;
        }

        /// <summary>
        /// 通过内容筛选出图片附件id列表
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        private List<string> GetAttIdListByContent(string content)
        {
            var result = new List<string>();
            Regex r = new("(api/Notice/DownLoad)[A-Za-z]*\\?attId=[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}(&noticeId=[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12})?(&Authorization=(\\w)+)?");
            var matches = r.Matches(content);
            if (matches.Count > 0)
            {
                var urlList = matches.Select(s => s.Value).ToList();
                foreach (string url in urlList)
                {
                    string queryStr = url.Substring(url.IndexOf("?") + 1);
                    var coll = HttpUtility.ParseQueryString(queryStr);
                    if (coll.Count > 0 && coll.AllKeys.Contains("attId"))
                    {
                        result.Add(coll["attId"]);
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 获取当前登录人未读的公告或通知【消息通知使用】
        /// </summary>
        /// <param name="type">是公告还是通知又或者所有?</param>
        /// <returns></returns>
        public List<VM_Messages.MessagesList_Out> GetUNReadNotice(EnumNoticeType type = EnumNoticeType.All)
        {
            return DbOpe_crm_notice.Instance.GetUNReadNotice(type);
        }

        /// <summary>
        /// 获取未读的消息通知列表
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessageReminder> GetUNReadNoticeMessage()
        {
            return DbOpe_crm_notice.Instance.GetUNReadNoticeMessage();
        }

        /// <summary>
        /// 当前用户是否拥有公告通知权限
        /// 通过是否拥有GetById权限判断
        /// </summary>
        /// <returns></returns>
        public bool CurrentHasNoticeDataAuth()
        {
            string noticeDataFunctionId = "077ce963-21f7-11ee-b512-30d042e24322";  //根据Id获取详情FormId,根据此Id判断用户是否有权限
            var interfaces = RedisCache.UserInterfaceRight.GetUserInterfaceRight(UserId);   // 当前用户拥有的接口权限
            return interfaces.FirstOrDefault(w => w.FormId.Equals(noticeDataFunctionId)) != null;
        }
    }
}