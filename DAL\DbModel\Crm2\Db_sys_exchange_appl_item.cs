﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///汇率申请明细表
    ///</summary>
    [SugarTable("sys_exchange_appl_item")]
    public class Db_sys_exchange_appl_item
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:汇率申请表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ExchangeApplId {get;set;}

           /// <summary>
           /// Desc:汇率日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ExchangeDate {get;set;}

           /// <summary>
           /// Desc:本位币种
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? StandardCurrency {get;set;}

           /// <summary>
           /// Desc:目标币种
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? TargetCurrency {get;set;}

           /// <summary>
           /// Desc:汇率
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? ExchangeRate {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
