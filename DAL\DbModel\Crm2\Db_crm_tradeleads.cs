﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_tradeleads")]
    public class Db_crm_tradeleads
    {
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:供求信息标题
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string TradeTitle { get; set; }

        /// <summary>
        /// Desc:产品
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string TradeItem { get; set; }

        /// <summary>
        /// Desc:中英文标识(0 中文 1 英文)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? TradeVersion { get; set; }

        /// <summary>
        /// Desc:供求类型(0 求 1供)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? TradeLeadsType { get; set; }

        /// <summary>
        /// Desc:数量
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? Quantity { get; set; }

        /// <summary>
        /// Desc:规格型号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Specifications { get; set; }

        /// <summary>
        /// Desc:工艺要求
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Requirements { get; set; }

        /// <summary>
        /// Desc:补充说明
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ReMark { get; set; }

        /// <summary>
        /// Desc:联系邮箱
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ContractEmail { get; set; }

        /// <summary>
        /// Desc:采购国
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Procurement { get; set; }

        /// <summary>
        /// Desc:状态(0 开启 1 关闭)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? State { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// Desc:过期时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? ExpiredDate { get; set; }

    }
}
