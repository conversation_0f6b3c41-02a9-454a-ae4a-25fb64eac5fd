﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///业务消息种类表
    ///</summary>
    [SugarTable("sys_business_messagetype")]
    public class Db_sys_business_messagetype
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:种类名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Name {get;set;}

           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? Type {get;set;}

           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Describe {get;set;}

           /// <summary>
           /// Desc:跳转路径
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PathUrl {get;set;}

           /// <summary>
           /// Desc:移动端图标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PathImg { get; set; }

           /// <summary>
           /// Desc:移动端跳转路径
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PathMobileUrl { get; set; }

           /// <summary>
           /// Desc:资源id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string FormId {get;set;}

           /// <summary>
           /// Desc:类型id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string TypeId { get; set; }

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
