using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.DAL.DbModel.Crm2;
using SqlSugar;
using CRM2_API.Common;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Diagnostics;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbCommon;

namespace CRM2_API.BLL.InvoiceSystem
{
    /// <summary>
    /// 发票系统历史数据迁移服务
    /// </summary>
    public class InvoiceMigrationService : BaseBLL<InvoiceMigrationService>
    {
        private int _batchSize = 100; // 默认批处理大小
        // 缓存字典数据
        private Dictionary<string, string> _invoicingDetailsCache;
        private Dictionary<string, string> _contractNameCache;
        private Dictionary<string, Db_crm_contract_invoiceappl> _invoiceApplCache;
        private DateTime _migrationCutoffDate = new DateTime(2025, 4, 23, 23, 59, 59); // 默认截止日期

        public InvoiceMigrationService()
        {
            _invoicingDetailsCache = new Dictionary<string, string>();
            _contractNameCache = new Dictionary<string, string>();
            _invoiceApplCache = new Dictionary<string, Db_crm_contract_invoiceappl>();
        }

        /// <summary>
        /// 生成新ID
        /// </summary>
        private string GenerateNewId()
        {
            return Guid.NewGuid().ToString("N");
        }

        /// <summary>
        /// 执行发票系统历史数据迁移
        /// </summary>
        /// <param name="batchSize">每批处理的数据量</param>
        /// <param name="migrationCutoffDate">迁移数据的截止日期，精确到秒</param>
        /// <returns>迁移结果</returns>
        public (bool Success, string Message, int TotalCount, int SuccessCount) MigrateInvoiceData(int batchSize = 100, DateTime? migrationCutoffDate = null)
        {
            _batchSize = batchSize;
            // 保存截止日期
            if (migrationCutoffDate.HasValue)
            {
                _migrationCutoffDate = migrationCutoffDate.Value;
            }
            
            var stopwatch = Stopwatch.StartNew();
            int totalCount = 0;
            int successCount = 0;

            try
            {
                LogUtil.AddLog("发票系统历史数据迁移开始");
                
                // 第一步：删除新发票相关表的记录
                LogUtil.AddLog("开始删除新发票相关表的记录...");
                DeleteInvoiceRelatedRecords();
                LogUtil.AddLog("新发票相关表的记录已清空");

                // 预加载字典数据
                PreloadDictionaryData();

                // 获取所有已开具的发票信息（完整数据）
                var allInvoiceApplData = GetAllInvoiceApplData();
                totalCount = allInvoiceApplData.Count;

                if (totalCount == 0)
                {
                    return (true, "没有需要迁移的发票数据", 0, 0);
                }

                LogUtil.AddLog($"共找到{totalCount}条需要迁移的发票记录");

                // 缓存所有发票申请数据
                foreach (var applData in allInvoiceApplData)
                {
                    if (!string.IsNullOrEmpty(applData.Id) && !_invoiceApplCache.ContainsKey(applData.Id))
                    {
                        _invoiceApplCache.Add(applData.Id, applData);
                    }
                }

                // 分批处理
                for (int i = 0; i < totalCount; i += _batchSize)
                {
                    var currentBatch = allInvoiceApplData.Skip(i).Take(_batchSize).ToList();
                    LogUtil.AddLog($"正在处理第{i + 1}至{Math.Min(i + _batchSize, totalCount)}条数据");

                    var batchResult = ProcessBatch(currentBatch);
                    successCount += batchResult;
                }

                stopwatch.Stop();
                string resultMessage = $"发票系统历史数据迁移完成。总记录数:{totalCount}, 成功:{successCount}, 失败:{totalCount - successCount}, 耗时:{stopwatch.ElapsedMilliseconds}ms";
                LogUtil.AddLog(resultMessage);

                return (true, resultMessage, totalCount, successCount);
            }
            catch (Exception ex)
            {
                LogUtil.AddLog("发票系统历史数据迁移过程中发生异常: " + ex.Message);
                stopwatch.Stop();
                return (false, $"迁移过程中发生异常: {ex.Message}", totalCount, successCount);
            }
        }

        /// <summary>
        /// 预加载字典数据到内存
        /// </summary>
        private void PreloadDictionaryData()
        {
            LogUtil.AddLog("开始预加载字典数据...");
            
            // 1. 加载开票明细项目字典
            var invoicingDetailsItems = DbContext.Crm2Db.Queryable<Db_crm_contract_invoice_invoicingdetails_item>().ToList();
            foreach (var item in invoicingDetailsItems)
            {
                if (!string.IsNullOrEmpty(item.Id) && !_invoicingDetailsCache.ContainsKey(item.Id))
                {
                    _invoicingDetailsCache.Add(item.Id, item.Name ?? string.Empty);
                }
            }
            LogUtil.AddLog($"已加载 {_invoicingDetailsCache.Count} 条开票明细项目数据");
            
            // 2. 预加载合同名称
            var contracts = DbContext.Crm2Db.Queryable<Db_crm_contract>()
                .Where(c => c.Deleted !=true)
                .Select(c => new { c.Id, c.ContractName })
                .ToList();
                
            foreach (var contract in contracts)
            {
                if (!string.IsNullOrEmpty(contract.Id) && !_contractNameCache.ContainsKey(contract.Id))
                {
                    _contractNameCache.Add(contract.Id, contract.ContractName ?? string.Empty);
                }
            }
            
            LogUtil.AddLog($"已加载 {_contractNameCache.Count} 条合同名称数据");
        }

        /// <summary>
        /// 获取所有需要迁移的发票申请完整数据
        /// </summary>
        private List<Db_crm_contract_invoiceappl> GetAllInvoiceApplData()
        {
            return DbContext.Crm2Db.Queryable<Db_crm_contract_invoiceappl>()
                .Where(a => a.AuditStatus == 2) // 审核状态为"通过"
                .Where(a => a.Deleted != true) // 未删除
                .ToList();
        }

        /// <summary>
        /// 处理一批发票数据
        /// </summary>
        private int ProcessBatch(List<Db_crm_contract_invoiceappl> applDataBatch)
        {
            int successCount = 0;

            try
            {
                // 获取这批数据的ID列表
                var invoiceApplIds = applDataBatch.Select(a => a.Id).ToList();
                
                // 获取对应的审核数据
                var auditDataList = DbContext.Crm2Db.Queryable<Db_crm_contract_invoiceaudit>()
                    .Where(a => invoiceApplIds.Contains(a.ContractInvoiceApplId))
                    .OrderByDescending(a => a.CreateDate)
                    .ToList();
                
                // 获取对应的发票数据
                var invoiceDataList = DbContext.Crm2Db.Queryable<Db_crm_contract_invoice>()
                    .Where(i => invoiceApplIds.Contains(i.ContractInvoiceApplId))
                    .ToList();

                // 创建审核数据和申请ID的映射关系
                var auditDataMap = auditDataList
                    .GroupBy(a => a.ContractInvoiceApplId)
                    .ToDictionary(g => g.Key, g => g.First());

                // 创建发票数据和申请ID的映射关系
                var invoiceDataMap = invoiceDataList
                    .GroupBy(i => i.ContractInvoiceApplId)
                    .ToDictionary(g => g.Key, g => g.First());

                // 准备批量插入的数据
                var applicationsToInsert = new List<Db_crm_invoice_application>();
                var reviewsToInsert = new List<Db_crm_invoice_review>();
                var invoicesToInsert = new List<Db_crm_invoice>();
                var proformaInvoicesToInsert = new List<Db_crm_proforma_invoice>();

                // 处理每条数据
                foreach (var applData in applDataBatch)
                {
                    try
                    {
                        // 检查是否有对应的审核和发票数据
                        if (!auditDataMap.TryGetValue(applData.Id, out var auditData) ||
                            !invoiceDataMap.TryGetValue(applData.Id, out var invoiceData))
                        {
                            LogUtil.AddLog($"申请ID {applData.Id} 的审核数据或发票数据不存在");
                            continue;
                        }

                        // 生成迁移的申请数据 - 使用发票表的ID
                        var application = CreateInvoiceApplication(applData, auditData);
                        applicationsToInsert.Add(application);

                        // 生成迁移的审核数据
                        var review = CreateInvoiceReview(auditData, invoiceData.Id, invoiceData, applData);
                        reviewsToInsert.Add(review);

                        // 根据发票类型生成相应的发票数据
                        if (applData.InvoiceType == 3) // 形式发票
                        {
                            var proformaInvoice = CreateProformaInvoice(applData, invoiceData, invoiceData.Id);
                            proformaInvoicesToInsert.Add(proformaInvoice);
                        }
                        else // 普票和专票
                        {
                            var invoice = CreateInvoice(invoiceData, invoiceData.Id, review.Id, applData);
                            invoicesToInsert.Add(invoice);
                        }

                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddLog($"处理申请ID {applData.Id} 时发生异常: {ex.Message}");
                    }
                }

                // 批量插入数据
                DbContext.Crm2Db.Ado.BeginTran();
                try
                {
                    // 批量插入申请数据
                    if (applicationsToInsert.Count > 0)
                    {
                        DbContext.Crm2Db.Insertable(applicationsToInsert).ExecuteCommand();
                        LogUtil.AddLog($"成功插入 {applicationsToInsert.Count} 条申请数据");
                    }

                    // 批量插入审核数据
                    if (reviewsToInsert.Count > 0)
                    {
                        DbContext.Crm2Db.Insertable(reviewsToInsert).ExecuteCommand();
                        LogUtil.AddLog($"成功插入 {reviewsToInsert.Count} 条审核数据");
                    }

                    // 批量插入发票数据
                    if (invoicesToInsert.Count > 0)
                    {
                        DbContext.Crm2Db.Insertable(invoicesToInsert).ExecuteCommand();
                        LogUtil.AddLog($"成功插入 {invoicesToInsert.Count} 条发票数据");
                    }

                    // 批量插入形式发票数据
                    if (proformaInvoicesToInsert.Count > 0)
                    {
                        DbContext.Crm2Db.Insertable(proformaInvoicesToInsert).ExecuteCommand();
                        LogUtil.AddLog($"成功插入 {proformaInvoicesToInsert.Count} 条形式发票数据");
                    }

                    DbContext.Crm2Db.Ado.CommitTran();
                }
                catch (Exception ex)
                {
                    DbContext.Crm2Db.Ado.RollbackTran();
                    LogUtil.AddLog($"批处理过程中发生异常，已回滚事务: {ex.Message}");
                    throw;
                }

                return successCount;
            }
            catch (Exception ex)
            {
                LogUtil.AddLog($"批处理过程中发生异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 创建发票申请记录对象
        /// </summary>
        private Db_crm_invoice_application CreateInvoiceApplication(Db_crm_contract_invoiceappl applData, Db_crm_contract_invoiceaudit auditData)
        {
            // 获取发票数据
            var invoiceData = DbContext.Crm2Db.Queryable<Db_crm_contract_invoice>()
                .Where(i => i.ContractInvoiceApplId == applData.Id)
                .First();
                
            if (invoiceData == null)
            {
                throw new Exception($"无法找到申请ID {applData.Id} 的发票数据");
            }
            
            // 获取开票明细名称(从缓存中)
            string invoicingDetailsName = GetInvoicingDetailsName(invoiceData.InvoicingDetails);

            return new Db_crm_invoice_application
            {
                Id = invoiceData.Id, // 使用invoice表的ID
                ContractId = invoiceData.ContractId, // 使用invoice表的ContractId
                ReceiptId = "",
                BillingType = applData.BillingType ?? 1,
                InvoiceType = applData.InvoiceType ?? 1,
                BillingCompany = invoiceData.BillingCompany, // 使用invoice表的BillingCompany
                BillingHeader = applData.BillingHeaderName,
                CreditCode = applData.BillingHeaderCreditCode,
                AppliedAmount = applData.InvoicedAmount ?? 0,
                ExpectedInvoicingDate = applData.ExpectedInvoicingTime,
                InvoicingDetails = invoicingDetailsName,
                Recipient = applData.Recipient,
                Email = applData.ReceiveEmail,
                AuditStatus = (int)EnumInvoiceApplicationStatus.Completed, // 已完成
                ApplicantId = applData.CreateUser, // 根据实际字段使用，如果模型中没有ApplicantId字段，则保留使用CreateUser
                ApplyTime = auditData.ApplicantDate,
                Remark = applData.InvoiceRemark,
                Deleted = false,
                CreateUser = applData.CreateUser,
                CreateDate = applData.CreateDate,
                UpdateUser = applData.UpdateUser,
                UpdateDate = applData.UpdateDate,
                DisplayStatus = (int)EnumInvoiceDisplayStatus.InvoiceCompleted, // 对应于已开票状态
                DisplayStatusName = GetEnumDescription(EnumInvoiceDisplayStatus.InvoiceCompleted), // 获取枚举描述
                IsReminded = applData.IsReminder ?? (int)EnumIsReminder.UnUrgedTicket,
                RemindedDate = null,
                RemindedUser = applData.IsReminder == (int)EnumIsReminder.UrgedTicket ? applData.CreateUser : null, // 使用CreateUser，因为模型中可能没有ApplicantId字段
                RefundStatus = (int)EnumInvoiceRefundStatus.Normal // 正常状态
            };
        }

        /// <summary>
        /// 创建发票审核记录对象
        /// </summary>
        private Db_crm_invoice_review CreateInvoiceReview(Db_crm_contract_invoiceaudit auditData, string newApplicationId, Db_crm_contract_invoice invoiceData, Db_crm_contract_invoiceappl applData)
        {
            // 获取开票明细名称(从缓存中)
            string invoicingDetailsName = GetInvoicingDetailsName(invoiceData.InvoicingDetails);

            return new Db_crm_invoice_review
            {
                Id = auditData.Id,
                InvoiceApplicationId = invoiceData.Id, // 使用invoice表的ID
                ContractId = auditData.Id, // 使用audit表的Id，不是ContractId
                InvoiceNumber = invoiceData.ElectronicInvoiceNumber,
                InvoicingDate = invoiceData.InvoicingDate,
                InvoicedAmount = invoiceData.InvoicedAmount ?? 0,
                BillingCompany = invoiceData.BillingCompany,
                BillingHeader = applData.BillingHeaderName,
                CreditCode = applData.BillingHeaderCreditCode, // 注意：迁移方案中是CreditCode，但模型可能实际是BillingHeaderCreditCode
                InvoicingDetails = invoicingDetailsName,
                AuditStatus = (int)EnumInvoiceReviewStatus.Approved, // 已审批
                ProcessorId = auditData.ReviewerId,
                ProcessTime = auditData.ReviewerDate,
                AuditorId = auditData.ReviewerId,
                AuditTime = auditData.ReviewerDate,
                AuditFeedback = auditData.Feedback,
                ProcessorRemark = null,
                ModifiedFields = null,
                Deleted = false,
                CreateUser = auditData.CreateUser,
                CreateDate = auditData.CreateDate,
                UpdateUser = auditData.UpdateUser,
                UpdateDate = auditData.UpdateDate
            };
        }

        /// <summary>
        /// 创建发票记录对象
        /// </summary>
        private Db_crm_invoice CreateInvoice(Db_crm_contract_invoice invoiceData, string newApplicationId, string newReviewId, Db_crm_contract_invoiceappl applData)
        {
            // 获取开票明细名称(从缓存中)
            string invoicingDetailsName = GetInvoicingDetailsName(invoiceData.InvoicingDetails);

            return new Db_crm_invoice
            {
                Id = GenerateNewId(), // 使用GUID生成新ID
                ContractId = invoiceData.ContractId,
                ReceiptId = null,
                InvoiceApplicationId = invoiceData.Id, // 使用invoice表的ID
                InvoiceReviewId = newReviewId,
                InvoiceNumber = invoiceData.ElectronicInvoiceNumber,
                InvoiceCode = null,
                InvoicingDate = invoiceData.InvoicingDate,
                InvoicedAmount = invoiceData.InvoicedAmount ?? 0,
                TotalAmount = invoiceData.InvoicedAmount ?? 0,
                TaxAmount = 0,
                BillingCompany = invoiceData.BillingCompany,
                BillingHeader = applData.BillingHeaderName,
                CreditCode = applData.BillingHeaderCreditCode,
                BillingType = applData.BillingType ?? 1,
                InvoiceType = applData.InvoiceType ?? 1,
                InvoicingDetails = invoicingDetailsName,
                MatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccess, // 老数据全设置成到账匹配成功
                RefundStatus = (int)EnumInvoiceRefundStatus.Normal,
                BalanceDue = null,
                Recipient = applData.Recipient,
                Email = applData.ReceiveEmail,
                IsSupplementInvoice = false, // 默认为false，旧数据不是补充发票
                InvoiceLink = invoiceData.InvoiceLink,
                TransactionType = 1, // 开票
                RelatedInvoiceId = null,
                Remark = null,
                Deleted = false,
                CreateUser = invoiceData.CreateUser,
                CreateDate = invoiceData.CreateDate,
                UpdateUser = invoiceData.UpdateUser,
                UpdateDate = invoiceData.UpdateDate
            };
        }

        /// <summary>
        /// 创建形式发票对象
        /// </summary>
        private Db_crm_proforma_invoice CreateProformaInvoice(Db_crm_contract_invoiceappl applData, Db_crm_contract_invoice invoiceData, string newApplicationId)
        {
            // 获取开票明细名称(从缓存中)
            string invoicingDetailsName = GetInvoicingDetailsName(invoiceData.InvoicingDetails);
            
            // 获取合同名称作为项目名称(从缓存中)
            string projectName = GetContractName(applData.ContractId);

            return new Db_crm_proforma_invoice
            {
                Id = GenerateNewId(), // 使用GUID生成新ID
                ContractId = invoiceData.ContractId, // 使用invoice表的ContractId
                ApplicationId = invoiceData.Id, // 使用invoice表的Id
                InvoiceNumber = invoiceData.FormalInvoiceNumber,
                InvoiceDate = invoiceData.InvoicingDate,
                Amount = invoiceData.InvoicedAmount ?? 0,
                BillingHeader = applData.BillingHeaderName,
                BillingCompany = invoiceData.BillingCompany,
                TaxpayerCode = applData.BillingHeaderCreditCode, // 注意：迁移方案中是CreditCode，但模型可能实际是BillingHeaderCreditCode
                ProjectName = projectName,
                Recipient = applData.Recipient,
                Email = applData.ReceiveEmail,
                InvoiceDetails = invoicingDetailsName,
                PdfFilePath = null,
                IsDownloaded = true,
                LastDownloadTime = null,
                Remark = null,
                Deleted = false,
                CreateUser = invoiceData.CreateUser,
                CreateDate = invoiceData.CreateDate,
                UpdateUser = invoiceData.UpdateUser,
                UpdateDate = invoiceData.UpdateDate
            };
        }

        #region 辅助方法

        /// <summary>
        /// 从缓存获取合同名称
        /// </summary>
        private string GetContractName(string contractId)
        {
            if (string.IsNullOrEmpty(contractId))
            {
                return string.Empty;
            }

            // 从缓存中获取
            if (_contractNameCache.TryGetValue(contractId, out var name))
            {
                return name;
            }

            return string.Empty;
        }

        /// <summary>
        /// 从缓存获取开票明细名称
        /// </summary>
        private string GetInvoicingDetailsName(string invoicingDetailsId)
        {
            if (string.IsNullOrEmpty(invoicingDetailsId))
            {
                return string.Empty;
            }

            // 从缓存中获取
            if (_invoicingDetailsCache.TryGetValue(invoicingDetailsId, out var name))
            {
                return name;
            }

            return string.Empty;
        }

        /// <summary>
        /// 获取枚举描述
        /// </summary>
        private string GetEnumDescription(Enum value)
        {
            var fieldInfo = value.GetType().GetField(value.ToString());
            var attributes = (System.ComponentModel.DescriptionAttribute[])fieldInfo.GetCustomAttributes(
                typeof(System.ComponentModel.DescriptionAttribute), false);

            return attributes.Length > 0 ? attributes[0].Description : value.ToString();
        }

        /// <summary>
        /// 删除新发票相关表的记录
        /// </summary>
        private void DeleteInvoiceRelatedRecords()
        {
            try
            {
                // 获取迁移数据的日期界限
                DateTime migrationCutoffDate = GetMigrationCutoffDate();
                LogUtil.AddLog($"仅删除 {migrationCutoffDate} 之前的历史迁移数据");
                
                // 开启事务
                DbContext.Crm2Db.Ado.BeginTran();
                
                // 按照依赖关系顺序删除，先删除引用了其他表主键的表，仅删除迁移时间点之前的数据
                int deletedCount1 = DbContext.Crm2Db.Deleteable<Db_crm_invoice>()
                    .Where(i => i.CreateDate <= migrationCutoffDate)
                    .ExecuteCommand();
                LogUtil.AddLog($"已删除 {deletedCount1} 条 crm_invoice 表历史记录");
                
                int deletedCount2 = DbContext.Crm2Db.Deleteable<Db_crm_proforma_invoice>()
                    .Where(p => p.CreateDate <= migrationCutoffDate)
                    .ExecuteCommand();
                LogUtil.AddLog($"已删除 {deletedCount2} 条 crm_proforma_invoice 表历史记录");
                
                int deletedCount3 = DbContext.Crm2Db.Deleteable<Db_crm_invoice_review>()
                    .Where(r => r.CreateDate <= migrationCutoffDate)
                    .ExecuteCommand();
                LogUtil.AddLog($"已删除 {deletedCount3} 条 crm_invoice_review 表历史记录");
                
                int deletedCount4 = DbContext.Crm2Db.Deleteable<Db_crm_invoice_application>()
                    .Where(a => a.CreateDate <= migrationCutoffDate)
                    .ExecuteCommand();
                LogUtil.AddLog($"已删除 {deletedCount4} 条 crm_invoice_application 表历史记录");
                
                // 提交事务
                DbContext.Crm2Db.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                // 回滚事务
                DbContext.Crm2Db.Ado.RollbackTran();
                LogUtil.AddLog($"删除新发票相关表记录时发生异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取迁移数据的截止日期
        /// </summary>
        /// <returns>迁移数据的截止日期（精确到秒）</returns>
        private DateTime GetMigrationCutoffDate()
        {
            // 直接返回字段值，该值可以从迁移接口传入
            return _migrationCutoffDate;
            
            // 如果需要动态查询，可以保留这部分代码
            // var lastInvoiceAppl = DbContext.Crm2Db.Queryable<Db_crm_contract_invoiceappl>()
            //    .Where(a => a.Deleted != true && a.AuditStatus == 2)
            //    .OrderByDescending(a => a.CreateDate)
            //    .First();
            // return lastInvoiceAppl?.CreateDate ?? DateTime.Now;
        }

        #endregion
    }
} 