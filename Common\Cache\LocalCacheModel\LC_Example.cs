﻿using CRM2_API.Common.AppSetting;

namespace CRM2_API.Common.Cache
{
    public partial class LocalCache//最外层必须用partial包着
    {
        /// <summary>
        /// 缓存例子
        /// </summary>
        public class LC_Example : ILocalCache
        {
            #region 设置缓存例子
            /// <summary>
            /// 缓存例子，不要使用DataTable这个对象，多线程读取DataTable会报错
            /// set方法必须为private，外部只读
            /// </summary>
            public static List<string> ExampleCache { private set; get; } = new List<string>(0);//需要先实例化，否则下面lock报错

            public void SetCache()
            {
                if (AppSettings.Env != Enum_SystemSettingEnv.Debug)
                    return;
                var list = new List<string> { "!", "2" };
                lock (ExampleCache)//赋值之前，要锁住这个对象，数据处理可以不锁
                {
                    ExampleCache = list;
                }
            }
            #endregion
        }
    }
}
