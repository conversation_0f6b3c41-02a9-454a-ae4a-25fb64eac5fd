﻿using System.IO;
using System.Data;
using Spire.Doc;
using static Aspose.Pdf.Operator;
using Spire.Doc.Documents;
using Spire.Doc.Collections;
using Spire.Pdf;
using Spire.Pdf.Texts;
using Aspose.Pdf.Generator;
using System.Drawing;
using System.Drawing.Imaging;

namespace CRM2_API.BLL.Common
{
    public class DownLoadPdfTemplate
    {
        //        public static void ConvertPdfToImages(string pdfFilePath, string outputFolder)
        //        {
        //            using (var document = PdfDocument.Load(pdfFilePath))
        //            {
        //                for (int pageIndex = 0; pageIndex < document.PageCount; pageIndex++)
        //                {
        //                    var pageImage = document.Render(pageIndex, 300, 300, true); // 渲染图片，可以调整缩放比例
        //                    string imageOutputPath = Path.Combine(outputFolder, $"page_{pageIndex + 1}.png");
        //                    pageImage.Save(imageOutputPath, ImageFormat.Png);
        //                }
        //            }
        //        }

        public SixLabors.ImageSharp.Image StreamToImage(Stream stream)
        {
            // 将流转换为字节数组
            byte[] bytes = new byte[stream.Length];
            stream.Read(bytes, 0, bytes.Length);

            // 使用MemoryStream和BitmapFactory将字节数组转换为Image对象
            using (MemoryStream memoryStream = new MemoryStream(bytes))
            {
                return SixLabors.ImageSharp.Image.Load(memoryStream);
            }
        }

        //        public Bitmap CombineImages(IEnumerable<Bitmap> images)
        //        {
        //            // 获取所有图片的宽度之和
        //            int totalWidth = images.Sum(x => x.Width);
        //            // 获取所有图片中的最大高度
        //            int maxHeight = images.Max(x => x.Height);

        //            // 创建一个新的Bitmap，用于合并后的图片
        //            Bitmap combinedBitmap = new Bitmap(totalWidth, maxHeight);

        //            using (Graphics graphics = Graphics.FromImage(combinedBitmap))
        //            {
        //                int currentX = 0;
        //                int currentY = 0;

        //                foreach (Bitmap image in images)
        //                {
        //                    // 计算当前图片的位置
        //                    currentX = image.Width * images.IndexOf(image);
        //                    currentY = (maxHeight - image.Height) / 2;

        //                    // 将图片绘制到合并后的Bitmap上
        //                    graphics.DrawImage(image, new Point(currentX, currentY));
        //                }
        //            }

        //            return combinedBitmap;
        //        }

        //        public static Stream SaveToImageStream(Stream stream)
        //        {
        //            using (PdfDocument pdfDocument = new PdfDocument(stream))
        //            {
        //                //for (int i = 0; i < pdfDocument.Pages.Count; i++)
        //                //{
        //                //    System.Drawing.Image bmp = pdfDocument.SaveAsImage(i, Spire.Pdf.Graphics.PdfImageType.Bitmap);
        //                //    string fileName = string.Format("Page-{0}.png", i + 1);
        //                //    bmp.Save(fileName, System.Drawing.Imaging.ImageFormat.Png);
        //                //}

        //                // 创建一个内存流来保存图片
        //MemoryStream imageStream = new MemoryStream();

        //                // 将PDF的每一页保存为PNG图片，并写入到内存流中
        //                foreach (PdfPageBase page in pdfDocument.Pages)
        //                {
        //                    pdfDocument.SaveAsImage(0);//imageStream);
        //                    // 在这里可以添加代码处理图片流，例如保存图片到文件等
        //                    imageStream.Position = 0; // 重置内存流的位置，以便进行下一次读取
        //                }


        //                Stream streamPNG = new MemoryStream();
        //                for (int i = 0; i < pdfDocument.Pages.Count; i++)
        //                {
        //                    //Stream img = pdfDocument.SaveAsImage(i);
        //                    //img.streamPNG

        //                    pdfDocument.SaveToImageStream(i, streamPNG, "image/png");

        //                    streamPNG.Position = 0;
        //                }

        //                //pdfDocument.SaveToImageStream(i, streamPDF, "image/png");
        //                return streamPNG;
        //            }
        //        }

        public static Stream DownLoadTemplate(Stream stream, List<TemplateField> item)
        {
            using (PdfDocument pdfDocument = new PdfDocument(stream))
            {
                foreach (PdfPageBase page in pdfDocument.Pages)
                {
                    PdfTextReplacer ptr = new PdfTextReplacer(page);
                    //替换文本
                    foreach (TemplateField i in item)
                    {
                        string FieldName = i.FieldName;
                        string FieldValue = i.FieldValue;
                        ptr.ReplaceAllText(FieldName, FieldValue);
                    }
                }
                Stream streamPDF = new MemoryStream();
                pdfDocument.SaveToStream(streamPDF);
                return streamPDF;
            }
        }


        public static Stream DownLoadTemplate(byte[] file, List<TemplateField> item, List<TemplateZhTableField> rows, string contractDescription, string picturePath)
        {
            return null;
            //using (PdfDocument pdfDocument = new PdfDocument(file))
            //{
            //    foreach (PdfPageBase page in pdfDocument.Pages)
            //    {
            //        PdfTextReplacer ptr = new PdfTextReplacer(page);
            //        //替换文本
            //        foreach (TemplateField i in item)
            //        {
            //            string FieldName = i.FieldName;
            //            string FieldValue = i.FieldValue;
            //            ptr.ReplaceAllText(FieldName, FieldValue);
            //        }

            //        //Table table = pdfDocument.Sections[0].Tables[0] as Spire.Doc.Table;

            //        //PdfTable table = new PdfTable();
            //        //table.Style.CellPadding = 2;
            //        //table.Style.HeaderSource = PdfHeaderSource.Rows;
            //        //table.Style.HeaderRowCount = 1;
            //        //table.Style.ShowHeader = true;
            //        //table.DataSource = dataSource;


            //        ////抽取表格
            //        //PdfTableExtractor extractor = new PdfTableExtractor(pdfDocument);
            //        //PdfTable[] tableLists = null;
            //        //for (int pageIndex = 0; pageIndex < pdfDocument.Pages.Count; pageIndex++)
            //        //{
            //        //    tableLists = extractor.ExtractTable(pageIndex);
            //        //    if (tableLists != null && tableLists.Length > 0)
            //        //    {
            //        //        foreach (PdfTable table in tableLists)
            //        //        {
            //        //            //table.Dat

            //        //            //TableRow row = table.AddRow();

            //        //            int row = table.GetRowCount();
            //        //            int column = table.GetColumnCount();
            //        //            for (int i = 0; i < row; i++)
            //        //            {
            //        //                for (int j = 0; j < column; j++)
            //        //                {
            //        //                    string text = table.GetText(i, j);

            //        //                }
            //        //            }
            //        //        }
            //        //    }
            //        //}



            //        //创建一个PdfTable的对象
            //        PdfTable table = new PdfTable();

            //        //设置表头以及其他单元格的字体
            //        //table.Style.DefaultStyle.Font = new PdfTrueTypeFont(new Font("HarmonyOS Sans SC", 12f, FontStyle.Regular), true);
            //        //table.Style.HeaderStyle.Font = new PdfTrueTypeFont(new Font("HarmonyOS Sans SC Medium", 12f, FontStyle.Bold), true);

            //        //创建一个DataTable的对象
            //        DataTable dataTable = new DataTable();
            //        dataTable.Columns.Add("编号");
            //        dataTable.Columns.Add("姓名");
            //        dataTable.Columns.Add("部门");
            //        dataTable.Columns.Add("职位");
            //        dataTable.Columns.Add("等级");
            //        dataTable.Rows.Add(new string[] { "1", "大卫", "信息部", "经理", "1" });
            //        dataTable.Rows.Add(new string[] { "3", "朱颖", "人事部", "经理", "1" });
            //        dataTable.Rows.Add(new string[] { "4", "苏菲", "市场部", "经理", "1" });
            //        dataTable.Rows.Add(new string[] { "7", "维奇", "市场部", "销售代表", "2" });
            //        dataTable.Rows.Add(new string[] { "9", "韦恩", "人事部", "人力资源主管", "2" });
            //        dataTable.Rows.Add(new string[] { "11", "米雅", "开发部", "开发人员", "2" });

            //        //将数据表设置为表格的数据源
            //        table.DataSource = dataTable;

            //        //显示表头（表头默认隐藏）
            //        table.Style.ShowHeader = true;

            //        //设置表头的字体颜色和背景色
            //        table.Style.HeaderStyle.BackgroundBrush = PdfBrushes.Gray;
            //        table.Style.HeaderStyle.TextBrush = PdfBrushes.White;

            //        //设置表头的文本对齐方式
            //        table.Style.HeaderStyle.StringFormat = new PdfStringFormat(PdfTextAlignment.Center, PdfVerticalAlignment.Middle);

            //        //设置其他单元格的文本对齐方式
            //        for (int i = 0; i < table.Columns.Count; i++)
            //        {
            //            table.Columns[i].StringFormat = new PdfStringFormat(PdfTextAlignment.Center, PdfVerticalAlignment.Middle);
            //        }

            //        //订阅事件
            //        table.BeginRowLayout += Table_BeginRowLayout;

            //        //将表格绘制在页面上
            //        table.Draw(page, new PointF(0, 30));
            //    }



            //    MemoryStream result = new MemoryStream();
            //    pdfDocument.SaveToStream(result);
            //    return result;
            //}


            //using (MemoryStream stream = new MemoryStream())
            //{
            //    stream.Write(file, 0, file.Length);
            //    stream.Position = 0;

            //    Document document = new Document();
            //    document.LoadFromStream(stream, Spire.Doc.FileFormat.Docx);

            //    //替换模板标签
            //    foreach (TemplateField i in item)
            //    {
            //        string FieldName = i.FieldName;
            //        string FieldValue = i.FieldValue;
            //        document.Replace(FieldName, FieldValue, false, false);
            //    }

            //    //获取第一个table
            //    Table table = document.Sections[0].Tables[0] as Spire.Doc.Table;
            //    // 添加数据
            //    foreach (TemplateTableField tableField in rows)
            //    {
            //        //在指定位置新插入一行作为第三行

            //        TableRow row = table.AddRow();
            //        table.Rows.Insert(1, row);

            //        CellCollection cell = ;


            //        row.Height = 30;
            //        row.Cells = 
            //        //设置自定义样式
            //        ParagraphStyle style = new ParagraphStyle(document);
            //        style.Name = "TableStyle";
            //        style.CharacterFormat.FontSize = 18;
            //        style.CharacterFormat.TextColor = Color.SeaGreen;
            //        style.CharacterFormat.h = Color.Yellow;
            //        //将自定义样式添加到文档
            //        document.Styles.Add(style);

            //    }

            //    //动态添加产品信息
            //    Table table = wordDoc.MainDocumentPart.Document.Body.Elements<Table>().First();

            //        TableRow row_d = table.Elements<TableRow>().ElementAt(1);
            //        TableCell cell_d = row_d.Elements<TableCell>().ElementAt(1);
            //        Paragraph p_d = cell_d.Elements<Paragraph>().First();
            //        Run r = p_d.Elements<Run>().First();
            //        Text t = r.Elements<Text>().First();
            //        t.Text = contractDescription;

            //        // 添加数据
            //        foreach (TemplateTableField tableField in rows)
            //        {
            //            TableRow row = new TableRow();
            //            TableRowProperties tabRowProps = row.AppendChild(new TableRowProperties(new TableRowHeight { Val = 600, HeightType = HeightRuleValues.Exact }));

            //            TableCell CellTitel = new TableCell();

            //            //垂直居中显示
            //            var CellTitelTcProperties = new TableCellProperties();
            //            CellTitelTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
            //            var CellTitelPproperties = new ParagraphProperties();
            //            CellTitelPproperties.AppendChild(new Justification { Val = JustificationValues.Center });
            //            //设置字体样式
            //            RunProperties CellTitelRunProperties = new RunProperties();//属性
            //            CellTitelRunProperties.Append(new Bold());
            //            CellTitelRunProperties.Append(new BoldComplexScript());
            //            CellTitelRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
            //            CellTitelRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

            //            Run CellTitelRun = new Run();
            //            CellTitelRun.Append(CellTitelRunProperties);
            //            CellTitelRun.Append(new Text(tableField.CellTitel));
            //            var CellTitelRunText = new Paragraph();
            //            CellTitelRunText.Append(CellTitelRun);
            //            CellTitelRunText.Append(CellTitelPproperties);
            //            CellTitel.AppendChild(CellTitelTcProperties);
            //            CellTitel.AppendChild(CellTitelRunText);
            //            //CellTitel.Append(new Paragraph(new Run(new Text(tableField.CellTitel))));
            //            row.Append(CellTitel);

            //            TableCell CellContent = new TableCell();
            //            //垂直居中显示
            //            var CellContentTcProperties = new TableCellProperties();
            //            CellContentTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
            //            var CellContentPproperties = new ParagraphProperties();
            //            CellContentPproperties.AppendChild(new Justification { Val = JustificationValues.Left });
            //            //设置字体样式
            //            RunProperties CellContentRunProperties = new RunProperties();//属性
            //            CellContentRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
            //            CellContentRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

            //            Run CellContentRun = new Run();
            //            CellContentRun.Append(CellContentRunProperties);
            //            CellContentRun.Append(new Text(tableField.CellContent));
            //            var CellContentRunText = new Paragraph();
            //            CellContentRunText.Append(CellContentRun);
            //            CellContentRunText.Append(CellContentPproperties);
            //            CellContent.AppendChild(CellContentTcProperties);
            //            CellContent.AppendChild(CellContentRunText);

            //            //CellContent.Append(new Paragraph(new Run(new Text(tableField.CellContent))));
            //            row.Append(CellContent);

            //            TableCell CellType = new TableCell();
            //            //垂直居中显示
            //            var CellTypeTcProperties = new TableCellProperties();
            //            CellTypeTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
            //            var CellTypePproperties = new ParagraphProperties();
            //            CellTypePproperties.AppendChild(new Justification { Val = JustificationValues.Center });
            //            //设置字体样式
            //            RunProperties CellTypeRunProperties = new RunProperties();//属性
            //            CellTypeRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
            //            CellTypeRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

            //            Run CellTypeRun = new Run();
            //            CellTypeRun.Append(CellTypeRunProperties);
            //            CellTypeRun.Append(new Text(tableField.CellType));
            //            var CellTypeRunText = new Paragraph();
            //            CellTypeRunText.Append(CellTypeRun);
            //            CellTypeRunText.Append(CellTypePproperties);
            //            CellType.AppendChild(CellTypeTcProperties);
            //            CellType.AppendChild(CellTypeRunText);

            //            //CellType.Append(new Paragraph(new Run(new Text(tableField.CellType))));
            //            row.Append(CellType);

            //            table.Elements<TableRow>().First().InsertAfterSelf(row);
            //        }

            //        //string picturePath = "F:\\workGit\\crm2\\crm2_api\\CRM2_API\\Template\\图章.png";
            //        string picType = "png";
            //        ImagePartType imagePartType;
            //        ImagePart imagePart = null;
            //        // 通过后缀名判断图片类型, true 表示忽视大小写
            //        if (Enum.TryParse<ImagePartType>(picType, true, out imagePartType))
            //        {
            //            imagePart = wordDoc.MainDocumentPart.AddImagePart(imagePartType);
            //        }
            //        imagePart.FeedData(File.Open(picturePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite)); // 读取图片二进制流

            //        MainDocumentPart mainPart = wordDoc.MainDocumentPart;
            //        var bookmarks = from bm in mainPart.Document.Body.Descendants<BookmarkStart>()
            //                        where bm.Name == "stamp"
            //                        select bm;
            //        var bookmark = bookmarks.SingleOrDefault();
            //        if (bookmark != null)
            //        {
            //            OpenXmlElement elem = bookmark.NextSibling();
            //            while (elem != null && !(elem is BookmarkEnd))
            //            {
            //                OpenXmlElement nextElem = elem.NextSibling();
            //                elem.Remove();
            //                elem = nextElem;
            //            }
            //            var parent = bookmark.Parent;
            //            Run run = new Run(new RunProperties());
            //            Run stamp = CreateImageParagraph2(wordDoc.MainDocumentPart.GetIdOfPart(imagePart), Path.GetFileNameWithoutExtension(picturePath), 1, 230, 230, 6, false);
            //            parent.InsertAfter<Run>(new Run(new Run(stamp)), bookmark);
            //        }

            //        wordDoc.Save();
            //    }

            //    MemoryStream result = new MemoryStream();
            //    stream.WriteTo(result);
            //    result.Position = 0;
            //    return result;
            //}
        }

        //事件处理器
        //private static void Table_BeginRowLayout(object sender, BeginRowLayoutEventArgs args)
        //{
        //    //设置行高
        //    args.MinimalHeight = 20f;

        //    //交替行的背景色
        //    if (args.RowIndex < 0)
        //    {
        //        return;
        //    }
        //    if (args.RowIndex % 2 == 1)
        //    {
        //        args.CellStyle.BackgroundBrush = PdfBrushes.LightGray;
        //    }
        //    else
        //    {
        //        args.CellStyle.BackgroundBrush = PdfBrushes.White;
        //    }
        //}
    }
}
