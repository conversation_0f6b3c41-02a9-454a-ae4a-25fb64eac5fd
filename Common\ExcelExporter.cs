﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.IO;
using Npoi.Mapper;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Collections.Generic;
using System.Reflection;
using System.ComponentModel;
using System;
using System.Linq;
using CRM2_API.Model.BLLModel.InvoiceSystem;
using Npoi.Mapper.Attributes;

namespace CRM2_API.Common
{
    /// <summary>
    /// Excel导出工具类
    /// </summary>
    public class ExcelExporterNPOI
    {
        /// <summary>
        /// 导出数据到Excel字节数组
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="dataItems">数据集合</param>
        /// <returns>Excel文件字节数组</returns>
        public virtual byte[] ExportAsByteArray<T>(IEnumerable<T> dataItems) where T : class, new()
        {
            var mapper = new Mapper(); // 假设Mapper是一个能够处理Excel导出的类
            MemoryStream stream = new MemoryStream();
            // 将dataItems集合生成的Excel直接放置到Stream中
            mapper.Save<T>(stream, dataItems, "sheet1", false, overwrite: true, xlsx: true);
            return stream.ToArray();
        }
        
        /// <summary>
        /// 导出Excel并应用样式
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="dataItems">数据集合</param>
        /// <param name="sheetName">工作表名称</param>
        /// <returns>Excel数据流</returns>
        public virtual Stream ExportWithStyle<T>(IEnumerable<T> dataItems, string sheetName = "数据导出") where T : class, new()
        {
            try
            {
                // 创建工作簿
                XSSFWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet(sheetName);
                
                // 创建标题行样式
                ICellStyle headerStyle = workbook.CreateCellStyle();
                IFont headerFont = workbook.CreateFont();
                headerFont.Boldweight = 700; // 设置加粗
                headerFont.FontHeightInPoints = 12; // 设置字体大小
                
                headerStyle.SetFont(headerFont);
                headerStyle.Alignment = HorizontalAlignment.Center; // 水平居中
                headerStyle.VerticalAlignment = VerticalAlignment.Center; // 垂直居中
                
                // 设置背景色
                headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Grey25Percent.Index;
                headerStyle.FillPattern = FillPattern.SolidForeground;
                
                // 设置边框
                headerStyle.BorderBottom = BorderStyle.Thin;
                headerStyle.BorderLeft = BorderStyle.Thin;
                headerStyle.BorderRight = BorderStyle.Thin;
                headerStyle.BorderTop = BorderStyle.Thin;
                
                // 创建数据行样式
                ICellStyle dataStyle = workbook.CreateCellStyle();
                dataStyle.BorderBottom = BorderStyle.Thin;
                dataStyle.BorderLeft = BorderStyle.Thin;
                dataStyle.BorderRight = BorderStyle.Thin;
                dataStyle.BorderTop = BorderStyle.Thin;
                
                // 数字格式
                ICellStyle numberStyle = workbook.CreateCellStyle();
                numberStyle.CloneStyleFrom(dataStyle);
                numberStyle.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.00");
                
                // 日期格式
                ICellStyle dateStyle = workbook.CreateCellStyle();
                dateStyle.CloneStyleFrom(dataStyle);
                dateStyle.DataFormat = workbook.CreateDataFormat().GetFormat("yyyy-MM-dd");
                
                // 获取属性信息
                var properties = typeof(T).GetProperties();
                
                // 创建标题行
                IRow headerRow = sheet.CreateRow(0);
                int colIndex = 0;
                
                // 获取属性信息并设置列标题和列宽
                foreach (var prop in properties)
                {
                    // 获取列标题
                    var columnAttr = prop.GetCustomAttributes(typeof(ColumnAttribute), true)
                                        .FirstOrDefault() as ColumnAttribute;
                    var displayAttr = prop.GetCustomAttributes(typeof(DisplayNameAttribute), true)
                                        .FirstOrDefault() as DisplayNameAttribute;
                    
                    // 确定列标题：优先使用Column特性，其次使用DisplayName特性，最后使用属性名
                    string columnName = columnAttr != null ? columnAttr.Name : 
                                      (displayAttr != null ? displayAttr.DisplayName : prop.Name);
                    
                    // 创建标题单元格并设置样式
                    ICell cell = headerRow.CreateCell(colIndex);
                    cell.SetCellValue(columnName);
                    cell.CellStyle = headerStyle;
                    
                    // 设置列宽
                    // 获取自定义列宽特性
                    var widthAttr = prop.GetCustomAttributes(typeof(ColumnWidthAttribute), true)
                                       .FirstOrDefault() as ColumnWidthAttribute;
                    
                    if (widthAttr != null)
                    {
                        // 使用自定义列宽
                        sheet.SetColumnWidth(colIndex, widthAttr.Width * 256);
                    }
                    else
                    {
                        // 根据属性类型设置默认列宽
                        if (prop.PropertyType == typeof(string))
                        {
                            // 根据列名和属性名判断可能的内容长度
                            if (columnName.Contains("名称") || columnName.Contains("抬头") || columnName.Contains("公司"))
                            {
                                sheet.SetColumnWidth(colIndex, 30 * 256); // 名称类列宽
                            }
                            else if (columnName.Contains("备注") || columnName.Contains("说明"))
                            {
                                sheet.SetColumnWidth(colIndex, 35 * 256); // 备注类列宽
                            }
                            else if (columnName.Contains("号码") || columnName.Contains("识别号") || columnName.Contains("编码"))
                            {
                                sheet.SetColumnWidth(colIndex, 25 * 256); // 编码类列宽
                            }
                            else if (prop.Name.Contains("Name") || prop.Name.Contains("Title"))
                            {
                                sheet.SetColumnWidth(colIndex, 20 * 256); // 名称类字符串
                            }
                            else if (prop.Name.Contains("Code") || prop.Name.Contains("Number") || prop.Name.Contains("Id"))
                            {
                                sheet.SetColumnWidth(colIndex, 18 * 256); // 编码类字符串
                            }
                            else if (prop.Name.Contains("Remark") || prop.Name.Contains("Description"))
                            {
                                sheet.SetColumnWidth(colIndex, 30 * 256); // 描述类字符串
                            }
                            else
                            {
                                sheet.SetColumnWidth(colIndex, 15 * 256); // 默认字符串宽度
                            }
                        }
                        else if (prop.PropertyType == typeof(DateTime) || prop.PropertyType == typeof(DateTime?))
                        {
                            sheet.SetColumnWidth(colIndex, 15 * 256); // 日期列宽
                        }
                        else if (prop.PropertyType == typeof(decimal) || prop.PropertyType == typeof(decimal?) ||
                                prop.PropertyType == typeof(double) || prop.PropertyType == typeof(double?))
                        {
                            sheet.SetColumnWidth(colIndex, 15 * 256); // 数字列宽
                        }
                        else if (prop.PropertyType == typeof(int) || prop.PropertyType == typeof(int?))
                        {
                            sheet.SetColumnWidth(colIndex, 10 * 256); // 整数列宽
                        }
                        else if (prop.PropertyType == typeof(bool) || prop.PropertyType == typeof(bool?))
                        {
                            sheet.SetColumnWidth(colIndex, 8 * 256); // 布尔值列宽
                        }
                        else
                        {
                            sheet.SetColumnWidth(colIndex, 12 * 256); // 默认列宽
                        }
                    }
                    
                    colIndex++;
                }
                
                // 填充数据行
                var list = dataItems.ToList();
                int rowIndex = 1;
                
                foreach (var item in list)
                {
                    IRow dataRow = sheet.CreateRow(rowIndex);
                    colIndex = 0;
                    
                    foreach (var prop in properties)
                    {
                        ICell cell = dataRow.CreateCell(colIndex);
                        
                        // 获取属性值
                        var value = prop.GetValue(item);
                        
                        if (value != null)
                        {
                            // 根据属性类型设置单元格值和样式
                            if (prop.PropertyType == typeof(string))
                            {
                                cell.SetCellValue(value.ToString());
                                cell.CellStyle = dataStyle;
                            }
                            else if (prop.PropertyType == typeof(DateTime) || prop.PropertyType == typeof(DateTime?))
                            {
                                cell.SetCellValue((DateTime)value);
                                cell.CellStyle = dateStyle;
                            }
                            else if (prop.PropertyType == typeof(decimal) || prop.PropertyType == typeof(decimal?))
                            {
                                cell.SetCellValue(Convert.ToDouble(value));
                                cell.CellStyle = numberStyle;
                            }
                            else if (prop.PropertyType == typeof(double) || prop.PropertyType == typeof(double?))
                            {
                                cell.SetCellValue(Convert.ToDouble(value));
                                cell.CellStyle = numberStyle;
                            }
                            else if (prop.PropertyType == typeof(int) || prop.PropertyType == typeof(int?))
                            {
                                cell.SetCellValue(Convert.ToDouble(value));
                                cell.CellStyle = dataStyle;
                            }
                            else if (prop.PropertyType == typeof(bool) || prop.PropertyType == typeof(bool?))
                            {
                                cell.SetCellValue((bool)value ? "是" : "否");
                                cell.CellStyle = dataStyle;
                            }
                            else
                            {
                                cell.SetCellValue(value.ToString());
                                cell.CellStyle = dataStyle;
                            }
                        }
                        else
                        {
                            cell.SetCellValue("");
                            cell.CellStyle = dataStyle;
                        }
                        
                        colIndex++;
                    }
                    
                    rowIndex++;
                }
                
                // 冻结首行
                sheet.CreateFreezePane(0, 1, 0, 1);
                
                // 将工作簿写入字节数组，然后创建新的内存流返回
                using (MemoryStream tempStream = new MemoryStream())
                {
                    workbook.Write(tempStream);
                    return new MemoryStream(tempStream.ToArray());
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"导出Excel时发生错误: {ex.Message}", ex);
            }
        }
    }
}
