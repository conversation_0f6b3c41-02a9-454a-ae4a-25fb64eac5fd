using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.IO;
using System.Xml.Linq;
using CRM2_API.Model.System;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using SqlSugar;
using static CRM2_API.Common.Cache.LocalCache;
using System.Collections.Generic;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using static CRM2_API.Model.ControllersViewModel.VM_ContractTerm;
using static CRM2_API.Model.ControllersViewModel.VM_ContractCreatedTemplate;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_ContractServiceChange;

namespace CRM2_API.Controllers
{
    [Description("合同管理")]
    //[SkipAuthCheck]
    //[SkipRSAKey]
    public class ContractController : MyControllerBase
    {
        public ContractController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 复制合同信息
        /// </summary>
        [HttpPost]
        public string CopyContract([FromForm] CopyContract_In addContractIn)
        {
            return BLL_Contract.Instance.CopyContract(addContractIn);
        }

        /// <summary>
        /// 添加合同信息
        /// </summary>
        [HttpPost]
        public string AddContract([FromForm] AddContract_In addContractIn)
        {
            return BLL_Contract.Instance.AddContract(addContractIn);
        }

        /// <summary>
        /// 审核超级子账号信息
        /// </summary>
        [HttpPost]
        public void AuditContractSuperSubAccount(AuditContractSuperSubAccount_In auditContractSuperSubAccountIn)
        {
            BLL_Contract.Instance.AuditContractSuperSubAccount(auditContractSuperSubAccountIn);
        }

        /// <summary>
        /// 中队条款审核提交合同信息
        /// </summary>
        [HttpPost]
        public void RegimentSubmitAuditContract(RegimentSubmitAuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.RegimentSubmitAuditContract(auditContractIn);
        }

        /// <summary>
        /// 大队条款审核提交合同信息
        /// </summary>
        [HttpPost]
        public void BrigadeSubmitAuditContract(BrigadeSubmitAuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.BrigadeSubmitAuditContract(auditContractIn);
        }

        /// <summary>
        /// 战队条款审核提交合同信息
        /// </summary>
        [HttpPost]
        public void DivisionSubmitAuditContract(DivisionSubmitAuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.DivisionSubmitAuditContract(auditContractIn);
        }

        /// <summary>
        /// 初审审核合同信息
        /// </summary>
        [HttpPost]
        public void InitialAuditContract(AuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.InitialAuditContract(auditContractIn);
        }

        /// <summary>
        /// 中队条款审核合同信息
        /// </summary>
        [HttpPost]
        public void RegimentAuditContract(RegimentAuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.RegimentAuditContract(auditContractIn);
        }

        /// <summary>
        /// 大队条款审核合同信息
        /// </summary>
        [HttpPost]
        public void BrigadeAuditContract(BrigadeAuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.BrigadeAuditContract(auditContractIn);
        }

        /// <summary>
        /// 战队条款审核合同信息
        /// </summary>
        [HttpPost]
        public void DivisionAuditContract(DivisionAuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.DivisionAuditContract(auditContractIn);
        }

        /// <summary>
        /// 条款审核合同信息
        /// </summary>
        [HttpPost]
        public void TeamAuditContract(TeamAuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.TeamAuditContract(auditContractIn);
        }

        /// <summary>
        /// 复核审核合同信息
        /// </summary>
        [HttpPost]
        public void ReviewAuditContract(ReviewAuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.ReviewAuditContract(auditContractIn);
        }

        /// <summary>
        /// 审核合同信息
        /// </summary>
        [HttpPost]
        public void AuditContract(AuditContract_In auditContractIn)
        {
            BLL_Contract.Instance.AuditContract(auditContractIn);
        }

        /// <summary>
        /// 审核合同盖章
        /// </summary>
        [HttpPost]
        public void AuditContractStampreViewAudit(AuditContractStampreViewAudit_In auditContractStampreViewAuditIn)
        {
            BLL_Contract.Instance.AuditContractStampreViewAudit(auditContractStampreViewAuditIn);
        }

        /// <summary>
        /// 审核合同其他盖章
        /// </summary>
        [HttpPost]
        public void AuditContractOtherStampreViewAudit(AuditContractOtherStampreViewAudit_In auditContractOtherStampreViewAuditIn)
        {
            BLL_Contract.Instance.AuditContractOtherStampreViewAudit(auditContractOtherStampreViewAuditIn);
        }

        /// <summary>
        /// 修改合同信息
        /// </summary>
        [HttpPost]
        public void UpdateContract([FromForm] UpdateContract_In updateContractIn)
        {
            BLL_Contract.Instance.UpdateContract(updateContractIn);
        }

        /// <summary>
        /// 删除合同信息，在合同状态为草稿、拒绝、作废时，合同可删除。
        /// </summary>
        [HttpPost]
        public void DeleteContract(string ids)
        {
            BLL_Contract.Instance.DeleteContract(ids);
        }

        /// <summary>
        /// 作废合同信息，在合同状态为待审核、通过时，可作废。
        /// </summary>
        [HttpPost]
        public void VoidContract(string id)
        {
            BLL_Contract.Instance.VoidContract(id);
        }

        /// <summary>
        /// 撤销合同信息，在合同状态为待审核、通过时，才可撤销。
        /// </summary>
        [HttpPost]
        public void RevokeContract(string id)
        {
            BLL_Contract.Instance.RevokeAdminContract(id);
        }

        /// <summary>
        /// 根据合同Id获取合同基本信息
        /// </summary>
        [HttpPost]
        public ContractBasicInfo_Out GetContractBasicInfoById(string id)
        {
            return BLL_Contract.Instance.GetContractBasicInfoById(id);
        }

        /// <summary>
        /// 根据客户编码获取合同基本信息
        /// </summary>
        [HttpPost]
        public ContractBasicInfo_Out GetContractBasicInfoByContractNum(string contractNum)
        {
            return BLL_Contract.Instance.GetContractBasicInfoByContractNum(contractNum);
        }

        /// <summary>
        /// 根据合同Id获取合同审核信息
        /// </summary>
        [HttpPost]
        public ContractAuditInfo_Out GetContractAuditInfoByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractAuditInfoByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id获取合同审核历史信息
        /// </summary>
        [HttpPost]
        public ContractAuditInfo_Out GetContractAuditHistoryInfoByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractAuditHistoryInfoByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id获取合同审核信息
        /// </summary>
        [HttpPost]
        public ContractInitialAuditInfo_Out GetContractInitialAuditInfoByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractInitialAuditInfoByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id获取合同审核历史信息
        /// </summary>
        [HttpPost]
        public ContractInitialAuditInfo_Out GetContractInitialAuditHistoryInfoByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractInitialAuditHistoryInfoByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id获取合同审核历史信息
        /// </summary>
        [HttpPost]
        public List<ContractAuditInfo_Out> GetContractAuditInfoListByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractAuditInfoListByContractId(contractId);
        }

        /// <summary>
        /// 根据查询条件获取销售合同列表。
        /// </summary>
        /// <param name="searchContractListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractList_Out> SearchContractList(SearchContractList_In searchContractListIn)
        {
            return BLL_Contract.Instance.SearchContractList(searchContractListIn);
        }

        /// <summary>
        /// 根据查询条件获取销售合同审核列表。
        /// </summary>
        /// <param name="searchContractListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractList_Out> SearchContractAuditList(SearchContractList_In searchContractListIn)
        {
            return BLL_Contract.Instance.SearchContractAuditList(searchContractListIn);
        }

        /// <summary>
        /// 根据查询条件获取销售合同盖章审核列表。
        /// </summary>
        /// <param name="searchContractListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractStampreViewList_Out> SearchContractStampreViewList(SearchContractStampreViewList_In searchContractListIn)
        {
            return BLL_Contract.Instance.SearchContractStampreViewList(searchContractListIn);
        }

        /// <summary>
        /// 根据查询条件获取销售合同其他盖章审核列表。
        /// </summary>
        /// <param name="searchContractListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractOtherStampreViewList_Out> SearchContractOtherStampreViewList(SearchContractOtherStampreViewList_In searchContractListIn)
        {
            return BLL_Contract.Instance.SearchContractOtherStampreViewList(searchContractListIn);
        }

        /// <summary>
        /// 回收合同信息，在合同状态为（跟踪状态）签约、作废、退款时，才可回收合同。验证数据权限（合同创建者处理）。合同类型为纸质合同才存在回收，电子合同不需要回收。
        /// </summary>
        [HttpPost]
        public void RecoveryContract(RecoveryContract_In recoveryContractIn)
        {
            BLL_Contract.Instance.RecoveryContract(recoveryContractIn);
        }

        /// <summary>
        /// 确认合同信息
        /// </summary>
        [HttpPost]
        public void ConfirmContract(ConfirmContract_In confirmContractIn)
        {
            BLL_Contract.Instance.ConfirmContract(confirmContractIn);
        }



        /// <summary>
        /// 添加合同服务信息环球慧思申请信息
        /// </summary>
        [HttpPost]
        public void AddContractProductServiceInfoGtisAppl(AddContractProductServiceInfoGtisAppl_In addContractProductServiceInfoGtisApplIn)
        {
            BLL_Contract.Instance.AddContractProductServiceInfoGtisAppl(addContractProductServiceInfoGtisApplIn);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息环球慧思申请信息
        /// </summary>
        [HttpPost]
        public ContractProductServiceInfoGtisAppl_Out GetContractProductServiceInfoGtisApplById(string id)
        {
            return BLL_Contract.Instance.GetContractProductServiceInfoGtisApplById(id);
        }

        /// <summary>
        /// 添加合同服务信息邓白氏申请信息
        /// </summary>
        [HttpPost]
        public void AddContractProductServiceInfoDBAppl(AddContractProductServiceInfoDBAppl_In addContractProductServiceInfoDBApplIn)
        {
            BLL_Contract.Instance.AddContractProductServiceInfoDBAppl(addContractProductServiceInfoDBApplIn);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息邓白氏申请信息
        /// </summary>
        [HttpPost]
        public ContractProductServiceInfoDBAppl_Out GetContractProductServiceInfoDBApplById(string id)
        {
            return BLL_Contract.Instance.GetContractProductServiceInfoDBApplById(id);
        }

        /// <summary>
        /// 添加合同服务信息慧思学院申请信息
        /// </summary>
        [HttpPost]
        public void AddContractProductServiceInfoCollegeAppl(AddContractProductServiceInfoCollegeAppl_In addContractProductServiceInfoCollegeApplIn)
        {
            BLL_Contract.Instance.AddContractProductServiceInfoCollegeAppl(addContractProductServiceInfoCollegeApplIn);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息慧思学院申请信息
        /// </summary>
        [HttpPost]
        public ContractProductServiceInfoCollegeAppl_Out GetContractProductServiceInfoCollegeApplById(string id)
        {
            return BLL_Contract.Instance.GetContractProductServiceInfoCollegeApplById(id);
        }

        /// <summary>
        /// 添加合同服务信息环球搜申请信息
        /// </summary>
        [HttpPost]
        public void AddContractProductServiceInfoGlobalSearchAppl(AddContractProductServiceInfoGlobalSearchAppl_In addContractProductServiceInfoGlobalSearchApplIn)
        {
            BLL_Contract.Instance.AddContractProductServiceInfoGlobalSearchAppl(addContractProductServiceInfoGlobalSearchApplIn);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息环球搜申请信息
        /// </summary>
        [HttpPost]
        public ContractProductServiceInfoGlobalSearchAppl_Out GetContractProductServiceInfoGlobalSearchApplById(string id)
        {
            return BLL_Contract.Instance.GetContractProductServiceInfoGlobalSearchApplById(id);
        }

        /// <summary>
        /// 添加合同服务信息其他数据申请信息
        /// </summary>
        [HttpPost]
        public void AddContractProductServiceInfoOtherDataAppl(AddContractProductServiceInfoOtherDataAppl_In addContractProductServiceInfoOtherDataApplIn)
        {
            BLL_Contract.Instance.AddContractProductServiceInfoOtherDataAppl(addContractProductServiceInfoOtherDataApplIn);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息其他数据申请信息
        /// </summary>
        [HttpPost]
        public ContractProductServiceInfoOtherDataAppl_Out GetContractProductServiceInfoOtherDataApplById(string id)
        {
            return BLL_Contract.Instance.GetContractProductServiceInfoOtherDataApplById(id);
        }

        /// <summary>
        /// 添加合同服务信息项目信息申请信息
        /// </summary>
        [HttpPost]
        public void AddContractProductServiceInfoProjectAppl(AddContractProductServiceInfoProjectAppl_In addContractProductServiceInfoProjectApplIn)
        {
            BLL_Contract.Instance.AddContractProductServiceInfoProjectAppl(addContractProductServiceInfoProjectApplIn);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息项目信息申请信息
        /// </summary>
        [HttpPost]
        public ContractProductServiceInfoProjectAppl_Out GetContractProductServiceInfoProjectApplById(string id)
        {
            return BLL_Contract.Instance.GetContractProductServiceInfoProjectApplById(id);
        }

        /// <summary>
        /// 根据合同Id获取合同支付信息
        /// </summary>
        [HttpPost]
        public ContractPaymentInfo_Out GetContractPaymentInfoByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractPaymentInfoByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id获取合同产品信息
        /// </summary>
        [HttpPost]
        public List<ProductInfo_Out> GetContractProductInfoByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractProductInfoByContractId(contractId);
        }

        /// <summary>
        /// 根据客户编码获取合同产品信息
        /// </summary>
        [HttpPost]
        public List<ProductInfo_Out> GetContractProductInfoByContractNum(string contractNum)
        {
            return BLL_Contract.Instance.GetContractProductInfoByContractNum(contractNum);
        }

        /// <summary>
        /// 根据合同Id获取合同产品信息下载样式和产品价格
        /// </summary>
        [HttpPost]
        public List<ProductInfoDownloadStyleAndPrice_Out> GetContractProductInfoDownloadStyleAndPriceByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractProductInfoDownloadStyleAndPriceByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id获取合同产品信息和产品价格
        /// </summary>
        [HttpPost]
        public List<ProductInfoAndPrice_Out> GetContractProductInfoAndPriceByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractProductInfoAndPriceByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id获取合同项目信息
        /// </summary>
        [HttpPost]
        public List<ContractProjectInfo_Out> GetContractProjectInfoByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractProjectInfoByContractId(contractId);
        }

        /// <summary>
        /// 添加合同服务信息
        /// 2025.7.15检查目前没有前端调用此接口
        /// </summary>
        [HttpPost]
        public void AddContractProductServiceInfoAppl(AddContractProductServiceInfoAppl_In addContractProductServiceInfoApplIn)
        {
            BLL_Contract.Instance.AddContractProductServiceInfoAppl(addContractProductServiceInfoApplIn);
        }

        /// <summary>
        /// 根据合同Id获取合同已开通服务信息
        /// </summary>
        [HttpPost]
        public List<ProductServiceInfo_Out> GetContractServiceInfo(string contractId)
        {
            return BLL_Contract.Instance.GetContractServiceInfo(contractId);
        }

        ///// <summary>
        ///// 合同服务变更申请
        ///// </summary>
        //[HttpPost]
        //public void UpdateContractProductServiceInfoAppl(UpdateContractProductServiceInfoAppl_In updateContractProductServiceInfoAppl_In)
        //{
        //    BLL_Contract.Instance.UpdateContractProductServiceInfoAppl(updateContractProductServiceInfoAppl_In);
        //}
        /// <summary>
        /// 变更合同服务信息环球慧思申请信息
        /// </summary>
        [HttpPost]
        public void UpdateContractProductServiceInfoGtisAppl(UpdateContractProductServiceInfoGtisAppl_In updateContractProductServiceInfoGtisApplIn)
        {
            BLL_Contract.Instance.UpdateContractProductServiceInfoGtisAppl(updateContractProductServiceInfoGtisApplIn);
        }

        /// <summary>
        /// 变更合同服务信息环球搜申请信息
        /// </summary>
        [HttpPost]
        public void UpdateContractProductServiceInfoGlobalSearchAppl(UpdateContractProductServiceInfoGlobalSearchAppl_In updateContractProductServiceInfoGlobalSearchAppl_In)
        {
            BLL_Contract.Instance.UpdateContractProductServiceInfoGlobalSearchAppl(updateContractProductServiceInfoGlobalSearchAppl_In);
        }
        /// <summary>
        /// 变更合同服务信息项目信息申请信息
        /// </summary>
        [HttpPost]
        public void UpdateContractProductServiceInfoProjectAppl(UpdateContractProductServiceInfoProjectAppl_In updateContractProductServiceInfoProjectAppl_In)
        {
            BLL_Contract.Instance.UpdateContractProductServiceInfoProjectAppl(updateContractProductServiceInfoProjectAppl_In);
        }
        /// <summary>
        /// 变更合同服务信息其他数据申请信息
        /// </summary>
        [HttpPost]
        public void UpdateContractProductServiceInfoOtherDataAppl(UpdateContractProductServiceInfoOtherDataAppl_In updateContractProductServiceInfoOtherDataAppl_In)
        {
            BLL_Contract.Instance.UpdateContractProductServiceInfoOtherDataAppl(updateContractProductServiceInfoOtherDataAppl_In);
        }
        /// <summary>
        /// 变更合同服务信息慧思学院申请信息
        /// </summary>
        [HttpPost]
        public void UpdateContractProductServiceInfoCollegeAppl(UpdateContractProductServiceInfoCollegeAppl_In updateContractProductServiceInfoCollegeAppl_In)
        {
            BLL_Contract.Instance.UpdateContractProductServiceInfoCollegeAppl(updateContractProductServiceInfoCollegeAppl_In);
        }
        /// <summary>
        /// 变更合同服务信息邓白氏申请信息
        /// </summary>
        [HttpPost]
        public void UpdateContractProductServiceInfoDBAppl(UpdateContractProductServiceInfoDBAppl_In updateContractProductServiceInfoDBAppl_In)
        {
            BLL_Contract.Instance.UpdateContractProductServiceInfoDBAppl(updateContractProductServiceInfoDBAppl_In);
        }
        /// <summary>
        /// 根据合同Id获取合同产品审核信息
        /// </summary>
        [HttpPost]
        public List<ProductInfoAudit_Out> GetContractProductInfoAuditByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractProductInfoAuditByContractId(contractId);
        }
        /// <summary>
        /// 添加合同信息变更申请信息
        /// </summary>
        [HttpPost]
        public void AddContractChangeAppl([FromForm] AddContractChangeAppl_In addContractChangeAppl_In)
        {
            BLL_Contract.Instance.AddContractChangeAppl(addContractChangeAppl_In);
        }
        /// <summary>
        /// 根据查询条件获取销售合同基本信息列表。
        /// </summary>
        /// <param name="searchContractBasicInfoListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractBasicInfoList_Out> SearchContractBasicInfoList(SearchContractBasicInfoList_In searchContractBasicInfoListIn)
        {
            return BLL_Contract.Instance.SearchContractBasicInfoList(searchContractBasicInfoListIn);
        }

        /// <summary>
        /// 根据查询条件获取到账登记销售合同基本信息列表
        /// </summary>
        /// <param name="searchRegisterContractBasicInfoListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractBasicInfoList_Out> SearchRegisterContractBasicInfoList(SearchRegisterContractBasicInfoList_In searchRegisterContractBasicInfoListIn)
        {
            return BLL_Contract.Instance.SearchRegisterContractBasicInfoList(searchRegisterContractBasicInfoListIn);
        }

        /// <summary>
        /// 根据查询条件获取到账登记销售合同基本信息列表(不包含全部到账的合同信息)
        /// </summary>
        /// <param name="searchRegisterContractBasicInfoListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractBasicInfoList_Out> SearchRegisterContractBasicInfoNotAllReceivedList(SearchRegisterContractBasicInfoList_In searchRegisterContractBasicInfoListIn)
        {
            return BLL_Contract.Instance.SearchRegisterContractBasicInfoNotAllReceivedList(searchRegisterContractBasicInfoListIn);
        }

        /// <summary>
        /// 根据查询条件获取合同变更申请信息列表
        /// </summary>
        /// <param name="searchContractChangeAppl_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractChangeAppl_OUT> SearchContractChangeApplList(SearchContractChangeAppl_IN searchContractChangeAppl_IN)
        {
            return BLL_Contract.Instance.SearchContractChangeApplList(searchContractChangeAppl_IN);
        }
        /// <summary>
        /// 根据合同变更Id获取合同变更信息
        /// </summary>
        /// <param name="auditId"></param>
        /// <returns></returns>
        [HttpPost]
        public ContractChangeAppl_OUT GetContractChangeApplById(string auditId)
        {
            return BLL_Contract.Instance.GetContractChangeApplById(auditId);
        }
        /// <summary>
        /// 审核合同变更申请信息
        /// </summary>
        /// <param name="auditContractChange"></param>
        /// <returns></returns>
        [HttpPost]
        public void AuditContractChangeAppl(AuditContractChangeAppl_IN auditContractChange)
        {
            BLL_Contract.Instance.AuditContractChangeAppl(auditContractChange);
        }
        /// <summary>
        /// 撤销合同变更申请审核信息
        /// </summary>
        /// <param name="revokeContractChangeAudit"></param>
        /// <returns></returns>
        [HttpPost]
        public void RevokeContractChangeAudit(RevokeContractChangeAudit_IN revokeContractChangeAudit)
        {
            BLL_Contract.Instance.RevokeContractChangeAudit(revokeContractChangeAudit);
        }
        /// <summary>
        /// 获取合同可变更字段列表
        /// </summary>
        [HttpPost]
        public List<GetContractChangeColumns_OUT> GetContractChangeColumns(bool containsChildsCol = false)
        {
            var d = LC_ContractChangeType.ChangeTypeCache;
            List<GetContractChangeColumns_OUT> li = new List<GetContractChangeColumns_OUT>();
            foreach (var item in d)
            {
                if (containsChildsCol || string.IsNullOrEmpty(item.GroupId))
                {
                    li.Add(new GetContractChangeColumns_OUT()
                    {
                        Value = item.Value,
                        Name = item.Name,
                        Id = item.Id,
                        GroupId = item.GroupId
                    });
                }
            }
            return li;
        }
        /// <summary>
        /// 验证合同状态为通过
        /// </summary>
        /// <param name="contractId"></param>
        [HttpPost]
        public void CheckContractState(string contractId)
        {
            BLL_Contract.Instance.CheckContractState(contractId);
        }

        /// <summary>
        /// 设置合同催单
        /// </summary>
        [HttpPost]
        public void AddUrgeRegistration([FromForm] AddUrgeRegistration_In addUrgeRegistrationIn)
        {
            BLL_Contract.Instance.AddUrgeRegistration(addUrgeRegistrationIn);
        }

        /// <summary>
        /// 检测上传已盖章合同
        /// </summary>
        [HttpPost]
        public CheckUploadContract_Out CheckUploadContract(string contractId)
        {
            return BLL_Contract.Instance.CheckUploadContract(contractId);
        }

        /// <summary>
        /// 上传已盖章合同
        /// </summary>
        [HttpPost]
        public void UploadContract([FromForm] UploadContract_In uploadContractIn)
        {
            BLL_Contract.Instance.UploadContract(uploadContractIn);
        }

        /// <summary>
        /// 获取合同开通服务数量
        /// </summary>
        /// <param name="contractId"></param>
        [HttpPost]
        public int ContractProductServiceInfoOpenedNum(string contractId)
        {
            return BLL_Contract.Instance.ContractProductServiceInfoOpenedNum(contractId);
        }

        /// <summary>
        /// 根据客户id和签约日期获取合同信息（240906 这里数据权限调整一下，这个接口按客户的权限走，和发票/到账统一）
        /// </summary>
        /// <param name="getContractByCustomerIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetContractByCustomerId_Out> GetContractByCustomerId(GetContractByCustomerId_In getContractByCustomerIdIn)
        {
            return BLL_Contract.Instance.GetContractByCustomerId(getContractByCustomerIdIn);
        }

        /// <summary>
        /// 下载合同
        /// </summary>
        [HttpPost]
        public IActionResult DownloadContract(string contractId, int type, int stampPosition, int DownloadFileType = 1)
        {
            ContractIsstage_Out ContractIsstage = BLL_Contract.Instance.GetContractIsstage(contractId);
            Stream result = BLL_Contract.Instance.DownloadContract(contractId, type, true, false, stampPosition, new ContractTemplateAddRow_Out(), ContractIsstage, Response, DownloadFileType);
            string ContentType = "application/octet-stream";
            if (DownloadFileType == EnumDownloadFileType.PDF.ToInt())
            {
                ContentType = "application/pdf";
            }
            else if (DownloadFileType == EnumDownloadFileType.WORD.ToInt())
            {
                ContentType = "application/msword";
            }
            else if (DownloadFileType == EnumDownloadFileType.PNG.ToInt())
            {
                ContentType = "image/png";
            }
            return new FileStreamResult(result, ContentType);
        }

        /// <summary>
        /// 下载合同
        /// </summary>
        [HttpPost]
        public IActionResult DownloadContractNoStamp(string contractId, int type, int stampPosition, int DownloadFileType = 1)
        {
            ContractIsstage_Out ContractIsstage = BLL_Contract.Instance.GetContractIsstage(contractId);
            Stream result = BLL_Contract.Instance.DownloadContract(contractId, type, false, false, stampPosition, new ContractTemplateAddRow_Out(), ContractIsstage, Response, DownloadFileType);
            string ContentType = "application/octet-stream";
            if (DownloadFileType == EnumDownloadFileType.PDF.ToInt())
            {
                ContentType = "application/pdf";
            }
            else if (DownloadFileType == EnumDownloadFileType.WORD.ToInt())
            {
                ContentType = "application/msword";
            }
            else if (DownloadFileType == EnumDownloadFileType.PNG.ToInt())
            {
                ContentType = "image/png";
            }
            return new FileStreamResult(result, ContentType);
        }

        /// <summary>
        /// 下载合同
        /// </summary>
        [HttpPost]
        public IActionResult DownloadContractPreview(DownContract_In addContractIn, int type, int ProductTable, int Item, int stampPosition, int DownloadFileType = 1)
        {
            Stream result = BLL_Contract.Instance.DownloadContract(addContractIn, type, false, true, stampPosition, new ContractTemplateAddRow_Out() { ProductTable = ProductTable, Item = Item }, addContractIn.ContractIsstage, Response, DownloadFileType);
            return new FileStreamResult(result, "application/octet-stream");
        }

        /// <summary>
        /// 合同详情 - 相关记录
        /// </summary>
        /// <param name="getContractInfoRecordsIn"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetContractInfoRecords_Out> GetContractInfoRecords(GetContractInfoRecords_In getContractInfoRecordsIn)
        {
            return BLL_Contract.Instance.GetContractInfoRecords(getContractInfoRecordsIn);
        }

        /// <summary>
        /// 根据合同id获取合同审核信息
        /// </summary>
        /// <param name="getContractAuditByContractIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetContractAuditByContractId_Out> GetContractAuditByContractId(GetContractAuditByContractIdIn getContractAuditByContractIdIn)
        {
            return BLL_Contract.Instance.GetContractAuditByContractId(getContractAuditByContractIdIn);
        }

        /// <summary>
        /// 补充上传已盖章合同
        /// </summary>
        [HttpPost]
        public void SupplementaryUploadContract([FromForm] SupplementaryUploadContract_In supplementaryUploadContractIn)
        {
            BLL_Contract.Instance.SupplementaryUploadContract(supplementaryUploadContractIn);
        }

        /// <summary>
        /// 补充上传已盖章合同
        /// </summary>
        [HttpPost]
        public void SupplementarySealedAndElectronicUploadContract([FromForm] SupplementarySealedAndElectronicUploadContract_In supplementarySealedAndElectronicUploadContractIn)
        {
            BLL_Contract.Instance.SupplementarySealedAndElectronicUploadContract(supplementarySealedAndElectronicUploadContractIn);
        }

        /// <summary>
        /// 补充上传其他已盖章合同
        /// </summary>
        [HttpPost]
        public void OtherStampReviewUploadContract([FromForm] OtherStampReviewUploadContract_In otherStampReviewUploadContractIn)
        {
            BLL_Contract.Instance.OtherStampReviewUploadContract(otherStampReviewUploadContractIn);
        }

        /// <summary>
        /// 根据其他合同附件审核id获取附件信息
        /// </summary>
        /// <param name="contractOtherStampReviewId"></param>
        /// <returns></returns>
        [HttpPost]
        public List<BM_FileInfo> GetOtherContractAttachfileByContractOtherStampReviewId(string contractOtherStampReviewId)
        {
            return BLL_Contract.Instance.GetOtherContractAttachfileByContractOtherStampReviewId(contractOtherStampReviewId);
        }

        /// <summary>
        /// 获取产品月份年限表
        /// </summary>
        [HttpPost]
        public List<ProductMonthYear_Out> GetProductMonthYear()
        {
            return BLL_Contract.Instance.GetProductMonthYear();
        }

        /// <summary>
        /// 获取产品规则信息
        /// </summary>
        [HttpPost]
        public List<ProductRules_Out> GetProductRules(ProductRules_In productRulesIn)
        {
            return BLL_Contract.Instance.GetProductRules(productRulesIn);
        }

        /// <summary>
        /// 检测合同状态
        /// </summary>
        [HttpPost]
        public CheckContractStatus_Out CheckContractStatus(string contractId)
        {
            return BLL_Contract.Instance.CheckContractStatus(contractId);
        }

        /// <summary>
        /// 检查合同是否可以进行增项
        /// </summary>
        [HttpPost]
        public CheckContractCanAddItem_Out CheckContractCanAddItem(CheckContractCanAddItem_In input)
        {
            string msg = "";
            bool canAddItem = BLL_Contract.Instance.CheckContractCanAddItem(input.ContractId, ref msg);
            if (!canAddItem)
            {
                throw new ApiException(msg);
            }
            return new CheckContractCanAddItem_Out
            {
                CanAddItem = canAddItem,
                Message = msg,
                Success = true
            };
        }

        /// <summary>
        /// 检测合同产品价格
        /// </summary>
        [HttpPost]
        public CheckContractProductPrice_Out CheckContractProductPrice([FromForm] AddContract_In addContractIn)
        {
            return BLL_Contract.Instance.CheckContractProductPrice(addContractIn, addContractIn.PaymentInfo, addContractIn.ProductInfo);
        }

        /// <summary>
        /// 检测合同产品价格
        /// </summary>
        [HttpPost]
        public CheckContractProductPrice_Out CheckContractProductPriceByCreateTemplateBase([FromForm] AddOrUpdateCreateTemplate addOrUpdateCreateTemplate)
        {
            Contract_In ContractIn = new Contract_In();
            ContractIn.SalesCountry = 337;
            ContractIn.Currency = addOrUpdateCreateTemplate.Currency;
            ContractIn.FCContractAmount = addOrUpdateCreateTemplate.FCContractAmount;
            ContractIn.ContractAmount = addOrUpdateCreateTemplate.ContractAmount;
            return BLL_Contract.Instance.CheckContractProductPrice(ContractIn, new PaymentInfo_In(), addOrUpdateCreateTemplate.ProductInfo.Cast<AddProductInfo_In>().ToList());
        }

        /// <summary>
        /// 检测合同状态
        /// </summary>
        [HttpPost]
        public List<AutoAuditReturnMessage_Out> CheckIsAutoAuditAdd([FromForm] AddContract_In addContractIn)
        {
            List<AutoAuditReturnMessage_Out> result = new List<AutoAuditReturnMessage_Out>();
            AutoAuditReturnMessage_Out IsAutoAudit = BLL_Contract.Instance.CheckIsAutoAuditAdd(Guid.Empty.ToString(), addContractIn, addContractIn.PaymentInfo, addContractIn.ProductInfo, addContractIn.ContractTermTemplate);
            result.Add(IsAutoAudit);
            AutoAuditReturnMessage_Out RenewalContractNumProductInfo = BLL_Contract.Instance.CheckRenewalContractNumProductInfo(Guid.Empty.ToString(), addContractIn, addContractIn.ProductInfo.Cast<ProductInfo_In>().ToList());
            result.Add(RenewalContractNumProductInfo);
            AutoAuditReturnMessage_Out IsCustomerHaveGtis = BLL_Contract.Instance.CheckCustomerHaveGtis(Guid.Empty.ToString(), addContractIn, addContractIn.ProductInfo.Cast<ProductInfo_In>().ToList());
            result.Add(IsCustomerHaveGtis);
            return result;
        }

        [HttpPost]
        public List<AutoAuditReturnMessage_Out> CheckIsAutoAuditUpdate([FromForm] UpdateContract_In updateContractIn)
        {
            List<AutoAuditReturnMessage_Out> result = new List<AutoAuditReturnMessage_Out>();
            AutoAuditReturnMessage_Out IsAutoAudit = BLL_Contract.Instance.CheckIsAutoAuditUpdate(updateContractIn.Id, updateContractIn, updateContractIn.PaymentInfo, updateContractIn.ProductInfo, updateContractIn.ContractTermTemplate);
            result.Add(IsAutoAudit);
            AutoAuditReturnMessage_Out RenewalContractNumProductInfo = BLL_Contract.Instance.CheckRenewalContractNumProductInfo(updateContractIn.Id, updateContractIn, updateContractIn.ProductInfo.Cast<ProductInfo_In>().ToList());
            result.Add(RenewalContractNumProductInfo);
            AutoAuditReturnMessage_Out IsCustomerHaveGtis = BLL_Contract.Instance.CheckCustomerHaveGtis(updateContractIn.Id, updateContractIn, updateContractIn.ProductInfo.Cast<ProductInfo_In>().ToList());
            result.Add(IsCustomerHaveGtis);
            return result;
        }

        /// <summary>
        /// 审核检测合同产品信息
        /// </summary>
        [HttpPost]
        public List<AutoAuditReturnMessage_Out> InitialAuditCheckRenewalContractNumProductInfo(AuditContract_In auditContractIn)
        {
            List<AutoAuditReturnMessage_Out> result = new List<AutoAuditReturnMessage_Out>();
            AutoAuditReturnMessage_Out RenewalContractNumProductInfo = BLL_Contract.Instance.InitialAuditCheckRenewalContractNumProductInfo(auditContractIn);
            result.Add(RenewalContractNumProductInfo);
            return result;
        }

        /// <summary>
        /// 根据合同Id获取合同盖章审核信息
        /// </summary>
        [HttpPost]
        public List<ContractStampreViewList_Out> GetContractStampreViewAuditByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractStampreViewAuditByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id获取合同其他附件审核信息121
        /// </summary>
        [HttpPost]
        public List<ContractOtherStampreViewList_Out> GetContractOtherStampreViewAuditByContractId(string contractId)
        {
            return BLL_Contract.Instance.GetContractOtherStampreViewAuditByContractId(contractId);
        }

        ///// <summary>
        ///// 根据合同Id获取合同盖章审核信息
        ///// </summary>
        //[HttpPost, SkipAuthCheck, UseDefaultRSAKey, SkipRecordLog]
        //public bool Temp()
        //{
        //    return BLL_Login.Instance.OperateAfterLoginTemp();
        //}

        /// <summary>
        /// 公司地址列表
        /// </summary>
        [HttpPost]
        public List<CompanyAddress> GetCompanyAddressList()
        {
            return BLL_Contract.Instance.GetCompanyAddressList();
        }



        /// <summary>
        /// 切换合同保密状态
        /// </summary>
        /// <param name="ContractId"></param>
        /// <param name="IsSecret"></param>
        [HttpPost]
        public void SwitchContractSecretState(string ContractId, bool IsSecret)
        {
            BLL_Contract.Instance.SwitchContractSecretState(ContractId, IsSecret);
        }


        /// <summary>
        /// 续约客户查看Gtis系统到账日期
        /// </summary>
        /// <param name="ContractId"></param>
        [HttpPost]
        public List<GetRenewGtisExpireDate_Out> GetRenewGtisExpireDate(string ContractId)
        {
            return BLL_Contract.Instance.GetRenewGtisExpireDate(ContractId);
        }

        /// <summary>
        /// 维护合同模板补行
        /// </summary>
        /// <param name="modifyContractTemplateAddRow"></param>
        [HttpPost]
        public void ModifyContractTemplateAddRow(ModifyContractTemplateAddRow_In modifyContractTemplateAddRow)
        {
            BLL_Contract.Instance.ModifyContractTemplateAddRow(modifyContractTemplateAddRow);
        }

        /// <summary>
        /// 修改合同模板补行
        /// </summary>
        /// <param name="updateContractTemplateAddRow"></param>
        [HttpPost]
        public void UpdateContractTemplateAddRow(UpdateContractTemplateAddRow_In updateContractTemplateAddRow)
        {
            BLL_Contract.Instance.UpdateContractTemplateAddRow(updateContractTemplateAddRow);
        }

        /// <summary>
        /// 获取合同模板补行
        /// </summary>
        /// <param name="ContractId"></param>
        /// <param name="TemplateType"></param>
        /// <param name="Currency"></param>
        [HttpPost]
        public ContractTemplateAddRow_Out GetContractTemplateAddRowByContractId(string ContractId, int TemplateType, int Currency)
        {
            return BLL_Contract.Instance.GetContractTemplateAddRowByContractId(ContractId, TemplateType, Currency);
        }

        /// <summary>
        /// 根据客户id获取合同的客户编码
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<GetContractNumList_Out> GetContractNumList(string ContractId)
        {
            if (ContractId == null)
            {
                return BLL_Contract.Instance.GetContractNumList();
            }
            else
            {
                return BLL_Contract.Instance.GetContractNumListNoSelf(ContractId);
            }
        }

        ///// <summary>
        ///// 根据客户id获取合同的客户编码审核
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //public List<GetContractNumList_Out> GetContractNumAuditList(string ContractId)
        //{
        //    return BLL_Contract.Instance.GetContractNumAuditListNoSelf(ContractId);
        //}

        /// <summary>
        /// 根据收款公司id获取合同模板信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public GetContractTemplate_Out GetContractTemplate(string id)
        {
            return BLL_Contract.Instance.GetContractTemplate(id);
        }

        /// <summary>
        /// 添加乙方联系信息
        /// </summary>
        /// <param name="addSecondPartyContactsIn"></param>
        [HttpPost]
        public void AddSecondPartyContacts(AddSecondPartyContacts_In addSecondPartyContactsIn)
        {
            BLL_Contract.Instance.AddSecondPartyContacts(addSecondPartyContactsIn);
        }

        /// <summary>
        /// 修改乙方联系信息
        /// </summary>
        /// <param name="updateSecondPartyContactsIn"></param>
        [HttpPost]
        public void UpdateSecondPartyContacts(UpdateSecondPartyContacts_In updateSecondPartyContactsIn)
        {
            BLL_Contract.Instance.UpdateSecondPartyContacts(updateSecondPartyContactsIn);
        }

        /// <summary>
        /// 删除乙方联系信息
        /// </summary>
        /// <param name="id"></param>
        [HttpPost]
        public void DeleteSecondPartyContactsById(string id)
        {
            BLL_Contract.Instance.DeleteSecondPartyContactsById(id);
        }

        /// <summary>
        /// 查询乙方联系信息
        /// </summary>
        /// <param name="searchSecondPartyContactsListIn"></param>
        [HttpPost]
        public ApiTableOut<SearchSecondPartyContactsList_Out> SearchSecondPartyContactsList(SearchSecondPartyContactsList_In searchSecondPartyContactsListIn)
        {
            return BLL_Contract.Instance.SearchSecondPartyContactsList(searchSecondPartyContactsListIn);
        }

        /// <summary>
        /// 根据id获取乙方联系信息
        /// </summary>
        /// <param name="id"></param>
        [HttpPost]
        public GetSecondPartyContactsList_Out GetSecondPartyContactsById(string id)
        {
            return BLL_Contract.Instance.GetSecondPartyContactsById(id);
        }

        /// <summary>
        /// 获取合同可变更字段列表
        /// </summary>
        [HttpPost]
        public List<ServiceChangeTypeTree> GetContractServiceChangeColumns(EnumServiceType ServiceType)
        {
            return LC_ServiceChangeType.ServiceChangeTypeCache.Where(e => e.ServiceType == (int)ServiceType).Select(e => e.Child).First();
        }

        /// <summary>
        /// 获取分期信息
        /// </summary>
        [HttpPost]
        public Isstage_Out GetIsstage()
        {
            return BLL_Contract.Instance.GetIsstage();
        }

        /// <summary>
        /// 根据合同id获取分期信息
        /// </summary>
        [HttpPost]
        public ContractIsstage_Out GetContractIsstage(string contractId)
        {
            return BLL_Contract.Instance.GetContractIsstage(contractId);
        }

        /// <summary>
        /// 添加合同分期信息
        /// </summary>
        /// <param name="addContractIsstage"></param>
        [HttpPost]
        public void AddContractIsstage(AddContractIsstage_In addContractIsstage)
        {
            BLL_Contract.Instance.AddContractIsstage(addContractIsstage);
        }

        /// <summary>
        /// 修改合同分期信息
        /// </summary>
        /// <param name="updateContractIsstage"></param>
        [HttpPost]
        public void UpdateContractIsstage(UpdateContractIsstage_In updateContractIsstage)
        {
            BLL_Contract.Instance.UpdateContractIsstage(updateContractIsstage);
        }

        /// <summary>
        /// 删除合同分期信息
        /// </summary>
        /// <param name="contractId"></param>
        [HttpPost]
        public void DeleteContractIsstage(string contractId)
        {
            BLL_Contract.Instance.DeleteContractIsstage(contractId);
        }

        /// <summary>
        /// 根据合同Id获取合同产品信息
        /// 服务申请时使用,原方法是 GetContractProductInfoByContractId
        /// </summary>
        /// <param name="getProductInfoByContractId4ServeApply_In"></param>
        /// <returns></returns>
        [HttpPost]
        public GetProductInfoByContractId4ServeApply_Out GetProductInfoByContractId4ServeApply(GetProductInfoByContractId4ServeApply_In getProductInfoByContractId4ServeApply_In)
        {
            return BLL_ContractService.Instance.GetProductInfoByContractId4ServeApply(getProductInfoByContractId4ServeApply_In.ContractId, getProductInfoByContractId4ServeApply_In.IsForServiceChange);
        }

        /// <summary>
        /// 查看当前合同是否可以申请服务
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        [HttpPost]
        public void CheckContractCanApply(string contractId)
        {
            DbOpe_crm_contract_productinfo.Instance.CheckContractCanApply(contractId, true);
        }
        /// <summary>
        /// 添加慧思产品申请信息
        /// </summary>
        /// <param name="addApply_In"></param>
        [HttpPost]
        public void AddWitsAppl(AddWitsAppl_In addApply_In)
        {
            BLL_Contract.Instance.AddWitsAppl(addApply_In);
        }
        /// <summary>
        /// 慧思产品服务变更
        /// </summary>
        /// <param name="updateApply_In"></param>
        [HttpPost]
        public void UpdateWitsAppl(AddWitsUpdateAppl_In updateApply_In)
        {
            BLL_Contract.Instance.UpdateWitsAppl(updateApply_In);
        }
    }
}
