﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_receiptregister_projectinfo_achievement_refunditems")]
    public class Db_v_receiptregister_projectinfo_achievement_refunditems
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string Id {get;set;}

           /// <summary>
           /// Desc:合同表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractId {get;set;}

           /// <summary>
           /// Desc:合同到账登记表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractReceiptRegisterId {get;set;}

           /// <summary>
           /// Desc:合同项目信息表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractProjectInfoId {get;set;}

           /// <summary>
           /// Desc:业绩扣除
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? PerformanceDeduction {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
