﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using System.Security.Policy;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using static CRM2_API.Model.ControllersViewModel.VM_ServiceExceptionRemind;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.BLL.RemindInfo;
using System.IO;
using CRM2_API.Common;

namespace CRM2_API.BLL
{
    public class BLL_ServiceExceptionRemind : BaseBLL<BLL_ServiceExceptionRemind>
    {
        public AddExceptionRemind_Search_Out AddExceptionRemind_Search(AddExceptionRemind_Search_In condition_In)
        {
            var retObj = new AddExceptionRemind_Search_Out();
            //如果检索条件参数全为空,则返回空集合
            if (string.IsNullOrEmpty(condition_In.ContractNum) && string.IsNullOrEmpty(condition_In.CompanyName) && string.IsNullOrEmpty(condition_In.AccountNumber))
                return retObj;
            //整理的客户编码列表，用于向Gtis系统请求得到对应的账号信息
            var svCodeList = new List<string>();
            //整理搜索条件中输入的客户编码
            if (!string.IsNullOrEmpty(condition_In.ContractNum))
                svCodeList.Add(condition_In.ContractNum);
            //整理搜索条件中输入的公司名称对应的客户编码
            if (!string.IsNullOrEmpty(condition_In.CompanyName) && DbOpe_crm_contract.Instance.exceptWords(condition_In.CompanyName))
            {
                //获取公司名称对应到的在服Gtis服务的客户编码
                var svCodeList_ByCompanyName = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetValidGtisServiceByCompanyName(condition_In.CompanyName);
                if (svCodeList_ByCompanyName != null && svCodeList_ByCompanyName.Count > 0)
                {
                    //如果传入条件没有ContractNum，直接使用 svCodeList_ByCompanyName；如果传入条件有ContractNum且svCodeList_ByCompanyName中不包含传入的ContractNum，则返回空；若传入条件有ContractNum且svCodeList_ByCompanyName中包含传入的ContractNum，则不对svCodeList进行处理
                    if (string.IsNullOrEmpty(condition_In.ContractNum))
                        svCodeList.AddRange(svCodeList_ByCompanyName);
                    else if (!svCodeList_ByCompanyName.Contains(condition_In.ContractNum))
                        return retObj;
                }
            }
            //整理搜索条件中输入的账号对应的客户编码
            if (!string.IsNullOrEmpty(condition_In.AccountNumber))
            {
                var svCodeList_ByAccountNum = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetValidGtisServiceByAccountNumber(condition_In.AccountNumber);
                if (svCodeList.Count > 0)
                {
                    //若此时svCodeList存在元素，则取svCodeList和svCodeList_ByAccountNum的交集intersect；若intersect的元素个数!=1，则返回空；若元素个数=1，不做处理
                    var intersect = svCodeList.Intersect(svCodeList_ByAccountNum).ToList();
                    if (intersect == null || intersect.Count != 1)
                        return retObj;
                    else
                        svCodeList = intersect;
                }
                else
                    svCodeList.AddRange(svCodeList_ByAccountNum);
                /*if (svCodeList_ByAccountNum != null*//* && svCodeList_ByAccountNum.Count > 0*//*)
                {
                    
                }*/
            }
            svCodeList = svCodeList.Distinct().ToList();
            if (svCodeList != null && svCodeList.Count > 1)
                throw new ApiException("当前条件检索出多个客户编码：" + string.Join(",", svCodeList) + ";请选择其中一个进行检索");

            //若此时svCode不为空,则可以通过Gtis查询方法填充返回内容
            if (svCodeList != null && svCodeList.Count == 1)
            {
                var svCode = svCodeList.First();
                var service = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetValidGtisServiceInfoByContractNum(svCode);
                if (service != null)
                {
                    var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(service.ContractId);
                    if (contract != null)
                    {
                        var userData = DbOpe_sys_user.Instance.GetFullUserInfo(contract.Issuer);
                        //客户服务信息
                        retObj.CustomerServiceInfo = new CustomerServiceInfo();
                        retObj.CustomerServiceInfo.ContractNum = svCode;
                        retObj.CustomerServiceInfo.ServiceId = service.Id;
                        retObj.CustomerServiceInfo.ProductType = DbOpe_crm_product.Instance.QueryByPrimaryKey(service.ProductId)?.ProductName;
                        retObj.CustomerServiceInfo.CompanyName = DbOpe_crm_customer_subcompany.Instance.GetCustomerMainCompany(contract.CustomerId)?.CompanyName;
                        retObj.CustomerServiceInfo.CustomerManager = userData.Id;
                        retObj.CustomerServiceInfo.CustomerManagerName = userData.UserWithOrgFullName;
                        retObj.CustomerServiceInfo.ServiceCycle = service.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + service.ServiceCycleEnd.Value.ToString("yyyy-MM-dd");
                        //账号及使用者列表
                        retObj.AccountAndPhoneUserInfos = new List<AccountAndPhoneUserInfo>();
                        var userList = BLL_GtisOpe.Instance.GetUserInfo(svCode).Result;
                        foreach (var user in userList)
                        {
                            var retValue = new AccountAndPhoneUserInfo();
                            retValue.Id = Guid.NewGuid().ToString();
                            retValue.ContractNum = svCode;
                            retValue.ServiceId = service.Id;
                            retValue.AccountUserId = user.SysUserID;
                            retValue.AccountType = user.AccountType == 0 ? EnumServiceExceptionRemind_AccountType.Main : EnumServiceExceptionRemind_AccountType.Sub;
                            retValue.AccountName = user.AccountNumber;
                            retValue.LastLoginTime = user.LastLoginDate;
                            retValue.LastLoginIp = user.LastLoginPlace;
                            retObj.AccountAndPhoneUserInfos.Add(retValue);
                            foreach (var phoneUser in user.AllPhoneUserInfo)
                            {
                                if (!string.IsNullOrEmpty(phoneUser.LinkMan))
                                {
                                    var retPhoneUser = new AccountAndPhoneUserInfo();
                                    retPhoneUser.Id = Guid.NewGuid().ToString();
                                    retPhoneUser.ContractNum = svCode;
                                    retPhoneUser.ServiceId = service.Id;
                                    retPhoneUser.AccountUserId = user.SysUserID;
                                    retPhoneUser.AccountType = EnumServiceExceptionRemind_AccountType.PhoneUser;
                                    retPhoneUser.AccountName = user.AccountNumber;
                                    retPhoneUser.PhoneUser = phoneUser.LinkMan;
                                    retPhoneUser.PhoneUserId = phoneUser.SysUserPhoneID;
                                    retPhoneUser.LastLoginTime = phoneUser.LastLoginDate;
                                    retPhoneUser.LastLoginIp = phoneUser.LastLoginPlace;
                                    retObj.AccountAndPhoneUserInfos.Add(retPhoneUser);

                                }
                            }
                        }
                        //客户异常历史记录
                        retObj.CustomerExceptionHistories = new List<CustomerExceptionHistory>();
                        retObj.CustomerExceptionHistories = DbOpe_crm_contract_serviceinfo_exception_remind.Instance.GetCustomerExceptionHistories(svCode);
                        //获取抄送人列表
                        retObj.CopyPersons = new List<CopyPersonInfo>();
                        //获取所有销售总监信息//改为所有后台人员，排除高级管理员和系统管理员
                        var salesDirectors = DbOpe_sys_user.Instance.GetSalesDirector().Select(e => new CopyPersonInfo() { UserId = e.Id, UserWithOrgFullName = e.Name }).ToList();
                        retObj.CopyPersons.AddRange(salesDirectors);
                        //获取所有上级组织的管理员信息
                        var OrgIds = DbOpe_sys_organization.Instance.GetParentOrgList(userData.OrganizationId).Select(e => e.Id).ToList();
                        var parentOrgManagers = DbOpe_sys_user.Instance.GetOrganizationManager(OrgIds, userData.Id).Select(e => new CopyPersonInfo() { UserId = e.Id, UserWithOrgFullName = e.UserWithOrgFullName }).ToList();
                        retObj.CopyPersons.AddRange(parentOrgManagers);
                    }
                }
            }
            return retObj;
        }

        public void AddExceptionRemind(AddExceptionRemind_In addExceptionRemind_In)
        {
            DbOpe_crm_contract_serviceinfo_exception_remind.Instance.TransDeal(() =>
            {
                //服务信息
                var service = DbOpe_crm_contract_serviceinfo_gtis.Instance.QueryByPrimaryKey(addExceptionRemind_In.ServiceId);
                //合同信息
                var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(service.ContractId);
                //产品信息
                var product = DbOpe_crm_product.Instance.QueryByPrimaryKey(service.ProductId);
                //公司信息
                var subcompany = DbOpe_crm_customer_subcompany.Instance.QueryByPrimaryKey(contract.FirstParty);
                //客户经理信息
                var user = DbOpe_sys_user.Instance.GetFullUserInfo(contract.Issuer);


                var remind = new Db_crm_contract_serviceinfo_exception_remind();
                remind.Id = Guid.NewGuid().ToString();
                remind.ContractNum = addExceptionRemind_In.ContractNum;
                remind.ExceptionContent = addExceptionRemind_In.RemindContent;
                remind.RemindType = String.Join(",", addExceptionRemind_In.RemindType.Select(e => (int)e).ToList());
                remind.CopyPerson = String.Join(",", addExceptionRemind_In.CopyPerson);
                remind.HandingPerson = addExceptionRemind_In.HandingPerson;
                remind.CustomerId = contract.FirstParty;
                remind.CustomerName = subcompany.CompanyName;
                remind.IssueUser = contract.Issuer;
                remind.OrgId = user.OrganizationId;
                remind.OrgName = user.OrgFullName;
                remind.ProductTypeName = product.ProductName;
                remind.ServiceCycle = service.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + service.ServiceCycleEnd.Value.ToString("yyyy-MM-dd");
                remind.State = EnumServiceExceptionRemindStatus.Pending;
                remind.Deleted = false;
                remind.CreateUser = TokenModel.Instance.id;
                remind.CreateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_exception_remind.Instance.Insert(remind);


                var cusSta = DbOpe_crm_contract_serviceinfo_exception_remind_statistics.Instance.GetExceptionCount(EnumServiceExceptionRemind_QueryObject.Customer, remind.CustomerId);
                if (cusSta == null)
                {
                    cusSta = new Db_crm_contract_serviceinfo_exception_remind_statistics()
                    {
                        StatisticsType = EnumServiceExceptionRemind_QueryObject.Customer,
                        StatisticsData = remind.CustomerId,
                        ExceptionRemindCount = 1
                    };
                    DbOpe_crm_contract_serviceinfo_exception_remind_statistics.Instance.InsertData(cusSta);
                }
                else
                {
                    cusSta.ExceptionRemindCount += 1;
                    DbOpe_crm_contract_serviceinfo_exception_remind_statistics.Instance.UpdateData(cusSta);
                }



                foreach (var account in addExceptionRemind_In.ExceptionRemindData)
                {
                    var remind_account = account.MappingTo<Db_crm_contract_serviceinfo_exception_remind_account>();
                    remind_account.ExceptionRemindId = remind.Id;
                    if (remind_account.AccountType == EnumServiceExceptionRemind_AccountType.PhoneUser)
                        remind_account.AccountUserId = "";
                    DbOpe_crm_contract_serviceinfo_exception_remind_account.Instance.InsertData(remind_account);

                    var qObject = remind_account.AccountType == EnumServiceExceptionRemind_AccountType.PhoneUser ? EnumServiceExceptionRemind_QueryObject.PhoneUser : EnumServiceExceptionRemind_QueryObject.Account;
                    var data = remind_account.AccountType == EnumServiceExceptionRemind_AccountType.PhoneUser ? remind_account.PhoneUserId : remind_account.AccountUserId;
                    var staData = DbOpe_crm_contract_serviceinfo_exception_remind_statistics.Instance.GetExceptionCount(qObject, data);
                    if (staData == null)
                    {
                        staData = new Db_crm_contract_serviceinfo_exception_remind_statistics()
                        {
                            StatisticsType = qObject,
                            StatisticsData = data,
                            ExceptionRemindCount = 1
                        };
                        DbOpe_crm_contract_serviceinfo_exception_remind_statistics.Instance.InsertData(staData);
                    }
                    else
                    {
                        staData.ExceptionRemindCount += 1;
                        DbOpe_crm_contract_serviceinfo_exception_remind_statistics.Instance.UpdateData(staData);
                    }
                }

                if (remind.RemindType.Contains(EnumServiceExceptionRemindType.DingDingRemind.ToInt().ToString()))
                {
                    //处理人钉钉信息发送
                    MessageMainInfo message = new MessageMainInfo();
                    message.Issuer = contract.Issuer;
                    message.MessageTypeToId = remind.Id;
                    message.MessagemMainAboutDes = subcompany.CompanyName + "客户账号异常";
                    MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceExceptionRemind_Handing, EnumMessageStepInfo.Review, EnumMessageStateInfo.Refus);
                    BLL_MessageCenter.Instance.RealTimeSend(giveBack);
                    //抄送人钉钉信息发送
                    foreach (var copyPerson in addExceptionRemind_In.CopyPerson)
                    {
                        //处理人钉钉信息发送
                        message = new MessageMainInfo();
                        message.Issuer = copyPerson;
                        message.MessageTypeToId = remind.Id;
                        message.MessagemMainAboutDes = user.UserWithOrgFullName + " 客户账号异常";
                        giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceExceptionRemind_Copy, EnumMessageStepInfo.Review, EnumMessageStateInfo.Refus);
                        BLL_MessageCenter.Instance.RealTimeSend(giveBack);
                    }
                }


            });
        }

        /// <summary>
        /// 飘红信息提醒
        /// </summary>
        /// <returns></returns>
        public List<RemindInfoOut> GetRemindInfos()
        {
            List<RemindInfoOut> remindInfoOuts = new List<RemindInfoOut>();
            //处理人飘红提醒
            var exceptionRemindHanding = DbOpe_crm_contract_serviceinfo_exception_remind.Instance.GetExceptionRemindByHandingPerson(UserId)
                .Select(it => new ApiDictionary() { Value = it.CustomerId, Name = it.CustomerName }).ToList();
            if (exceptionRemindHanding.Count > 0)
            {
                remindInfoOuts.Add(new RemindInfoOut()
                {
                    RemindDataNum = exceptionRemindHanding.Count,
                    RemindDataIdAndNames = exceptionRemindHanding,
                    RemindTypeDetail = EnumRemindType.ServiceExceptionRemind_Handing.GetEnumDescription(),
                    MaxShowNameNum = 5,
                    RemindNumUnit = "个",
                    RemindToPath = "/anomalousReminder?EnumRemindType=16",
                    //RemindDataIdDbName = "CustomerId",
                    //RemindFormName = "私有池",
                    RemindType = EnumRemindType.ServiceExceptionRemind_Handing
                });
            }
            //抄送人飘红提醒
            var exceptionRemindCopy = DbOpe_crm_contract_serviceinfo_exception_remind.Instance.GetExceptionRemindByCopyPerson(UserId)
                .Select(it => new ApiDictionary() { Value = it.HandingPerson, Name = it.HandingPersonName }).ToList();
            if (exceptionRemindCopy.Count > 0)
            {
                remindInfoOuts.Add(new RemindInfoOut()
                {
                    RemindDataNum = exceptionRemindCopy.Count,
                    RemindDataIdAndNames = exceptionRemindCopy,
                    RemindTypeDetail = EnumRemindType.ServiceExceptionRemind_Copy.GetEnumDescription(),
                    MaxShowNameNum = 5,
                    RemindNumUnit = "个",
                    RemindToPath = "/anomalousReminder?EnumRemindType=17",
                    //RemindDataIdDbName = "CustomerId",
                    //RemindFormName = "私有池",
                    RemindType = EnumRemindType.ServiceExceptionRemind_Copy
                });
            }
            return remindInfoOuts;
        }

        public void DealExceptionRemind(DealExceptionRemind_In deal_In)
        {
            DbOpe_crm_contract_serviceinfo_exception_remind.Instance.TransDeal(() =>
            {
                //标记已处理
                DbOpe_crm_contract_serviceinfo_exception_remind.Instance.DealExceptionRemind(deal_In);
                //如果数据有钉钉待办，修改待办消息状态
                if (DbOpe_crm_contract_serviceinfo_exception_remind.Instance.CheckRemindHaveDingdingRemind(deal_In.Remind_Id))
                    DbOpe_sys_messagecenterdetail.Instance.GetUserToDoRelate_SingleData(EnumRemindType.ServiceExceptionRemind_Handing, deal_In.Remind_Id);
            });

        }

        /// <summary>
        /// 下载异常提醒信息列表内容
        /// </summary>
        /// <param name="condition_In"></param>
        /// <returns></returns>
        public Stream DownloadExceptionRemindInfo(SearchExceptionRemindList_In condition_In)
        {
            var list = DbOpe_crm_contract_serviceinfo_exception_remind.Instance.GetDownloadExceptionRemindList(condition_In);
            var exporter = new ExcelExporterNPOI();
            var result = exporter.ExportAsByteArray(list);
            var fs = new MemoryStream(result);
            return fs;
        }
    }
}


