using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 测试数据 BLL 类 - 客户相关测试数据
    /// </summary>
    public partial class BLL_TestData
    {
        /// <summary>
        /// 创建测试客户
        /// </summary>
        /// <param name="randomSuffix">随机后缀，用于避免重名</param>
        /// <returns>客户创建结果，包含ID和名称</returns>
        public CreationResult CreateTestCustomer(string randomSuffix)
        {
            try
            {
                // 通过系统接口获取信用代码
                var getCreditCodeParams = new GetCreditCode_IN
                {
                    CompanyName = "测试客户" + randomSuffix,
                    CreditType = 0, // 默认值，可能需要根据实际情况调整
                    IsRelated = 0 // 不相关
                };
                
                var creditCodeResult = BLL_Customer.Instance.GetCreditCodeNew(getCreditCodeParams);
                string creditCode = creditCodeResult?.CreditCode;
                
                if (string.IsNullOrEmpty(creditCode))
                {
                    return null;
                }
                
                // 创建客户
                string customerName = "测试客户" + randomSuffix;
                var customerData = new AddCustomer_IN<AddSubCompany_IN>
                {
                    CreditType = 0,
                    CompanyName = customerName,
                    CreditCode = creditCode,
                    PeerData = 2,
                    TrackingStage = 10,
                    CustomerSource = 5,
                    DataDescription = "测试数据",
                    CustomerLevel = 3,
                    ContactInfos = new List<Contact_IN>
                    {
                        new Contact_IN
                        {
                            Contacts = "张测试",
                            Job = "经理",
                            ContactWay = "18900000000",
                            Email = "<EMAIL>",
                            Telephone = "010-12345678",
                            Fax = "010-12345678",
                            IsDefault = 1,
                            Index = 0
                        }
                    },
                    RelatedCompanies = new List<SubTempCompanyRelated_IN>(),
                    Country = 337,
                    Province = 2,
                    City = 2,
                    Address = "测试地址",
                    CustomerNature = 1,
                    CustomerSize = 3,
                    CustomerIndustry = new[] { "c4706f1b-c7ca-11ed-bc7b-30d042e24322" },
                    MainProducts = new[] { "4876c19c-c85d-11ed-bc7b-30d042e24322" },
                    IsMain = 1,
                    SubCompanys = new List<AddSubCompany_IN>()
                };
                
                // 调用BLL创建客户
                string customerId = BLL_Customer.Instance.AddPrivatePoolCustomer(customerData);

                if (string.IsNullOrEmpty(customerId))
                {
                    return null;
                }

                return new CreationResult
                {
                    Id = customerId,
                    Name = customerName
                };
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
} 