using System.ComponentModel;

namespace CRM2_API.Model.BLLModel.Enum
{
    /// <summary>
    /// SaleWits资源下发类型枚举
    /// </summary>
    public enum EnumSaleWitsDistributionType
    {
        /// <summary>
        /// 首次下发
        /// </summary>
        [Description("首次下发")]
        FirstDistribution,

        /// <summary>
        /// 年度下发
        /// </summary>
        [Description("年度下发")]
        AnnualDistribution,

        /// <summary>
        /// 增账号下发
        /// </summary>
        [Description("增账号下发")]
        AddAccountDistribution,

        /// <summary>
        /// 充值操作
        /// </summary>
        [Description("充值操作")]
        RechargeOperation
    }

    /// <summary>
    /// 外部系统/服务类型枚举
    /// </summary>
    public enum EnumSystemServiceType
    {
        /// <summary>
        /// GTIS
        /// </summary>
        [Description("GTIS")]
        GTIS,
        
        /// <summary>
        /// 环球搜
        /// </summary>
        [Description("环球搜")]
        GlobalSearch,
        
        /// <summary>
        /// SalesWits
        /// </summary>
        [Description("SalesWits")]
        SalesWits,
        
        /// <summary>
        /// 慧思学院
        /// </summary>
        [Description("慧思学院")]
        College
    }

    /// <summary>
    /// 业务类型枚举
    /// </summary>
    public enum EnumExternalCallBusinessType
    {
        /// <summary>
        /// 服务开通
        /// </summary>
        [Description("服务开通")]
        ServiceOpening,
        
        /// <summary>
        /// 资源下发
        /// </summary>
        [Description("资源下发")]
        ResourceDistribution,
        
        /// <summary>
        /// 定时任务
        /// </summary>
        [Description("定时任务")]
        ScheduledTask,
        
        /// <summary>
        /// 手动调用
        /// </summary>
        [Description("手动调用")]
        Manual
    }

    /// <summary>
    /// 调用状态枚举
    /// </summary>
    public enum EnumExternalCallStatus
    {
        /// <summary>
        /// 成功
        /// </summary>
        [Description("成功")]
        Success,
        
        /// <summary>
        /// 失败
        /// </summary>
        [Description("失败")]
        Failed,
        
        /// <summary>
        /// 超时
        /// </summary>
        [Description("超时")]
        Timeout,
        
        /// <summary>
        /// 异常
        /// </summary>
        [Description("异常")]
        Exception
    }

    /// <summary>
    /// 服务开通类型枚举
    /// </summary>
    public enum EnumServiceOpeningType
    {
        /// <summary>
        /// 新开账号
        /// </summary>
        [Description("新开账号")]
        NewAccount,
        
        /// <summary>
        /// 续约
        /// </summary>
        [Description("续约")]
        Renewal,
        
        /// <summary>
        /// 变更
        /// </summary>
        [Description("变更")]
        Change
    }


} 