﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.RemindInfo;
using CRM2_API.BLL.Schedule;
using CRM2_API.BLL.TrackingRecord;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using LgyUtil;
using SqlSugar;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.Contracts;
using System.Linq;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using static Lucene.Net.Util.Fst.Util;

namespace CRM2_API.BLL
{
    public class BLL_ContractAchievement : BaseBLL<BLL_ContractAchievement>
    {
        private string currentUser;

        public BLL_ContractAchievement()
        {
            currentUser = UserId;
#if DEBUG
            //currentUser = "02880719-b912-4df5-a5e0-acf0c010a505";
#endif
        }
        public List<RemindInfoOut> GetRemindInfos()
        {
            List<RemindInfoOut> remindInfoOuts = new List<RemindInfoOut>();
            var AchivementToConfirm = DbOpe_crm_contract_receiptregister.Instance.AchivementToConfirmQuery(currentUser).ToList();
            if (AchivementToConfirm.Count > 0)
            {
                remindInfoOuts.Add(new RemindInfoOut()
                {
                    RemindDataNum = AchivementToConfirm.Count,
                    RemindDataIdAndNames = AchivementToConfirm,
                    RemindTypeDetail = EnumRemindType.AchivementToConfirm.GetEnumDescription(),
                    MaxShowNameNum = 5,
                    RemindNumUnit = "笔",
                    RemindToPath = "/contract/performance?EnumRemindType=14",
                    RemindDataIdDbName = "Id",
                    RemindType = EnumRemindType.AchivementToConfirm
                });
            }
            return remindInfoOuts;
        }


        public ApiTableOut<QueryContractAchievement_OUT> SearchContractReceiptRegisterConfirmList(QueryContractAchievement_IN queryContractAchievement_IN)
        {
            int total = 0;
            return new ApiTableOut<QueryContractAchievement_OUT> { Data = DbOpe_crm_contract_receiptregister.Instance.SearchContractReceiptRegisterConfirmList(queryContractAchievement_IN, currentUser, ref total), Total = total };
        }

        /// <summary>
        /// 确认合同到账登记业绩审核信息
        /// </summary>
        public void AuditContractReceiptRegisterAchievement(AuditContractReceiptRegisterAchievement_IN auditContractReceiptRegisterAchievement_IN)
        {
            //参数验证
            if (auditContractReceiptRegisterAchievement_IN.AchievementState != EnumAchievementState.Confirmed &&
                auditContractReceiptRegisterAchievement_IN.AchievementState != EnumAchievementState.Questionable
            )
            {
                throw new ApiException("参数错误");
            }
            if (auditContractReceiptRegisterAchievement_IN.AchievementState == EnumAchievementState.Questionable && string.IsNullOrEmpty(auditContractReceiptRegisterAchievement_IN.Remark))
            {
                throw new ApiException("备注不可为空");
            }
            string receiptId = string.Empty;
            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                DateTime dt = DateTime.Now;
                var audit = DbOpe_crm_contract_receiptregister_achievement_audit.Instance.GetDataById(auditContractReceiptRegisterAchievement_IN.Id);
                if (audit == null || audit.Deleted.Value || audit.AchievementState != (int)EnumAchievementState.UnConfirmed)
                {
                    throw new ApiException("未找到对应的待审核信息");
                }
                var ca = DbOpe_crm_contract_receiptregister.Instance.GetDataById(audit.ContractReceiptRegisterId);
                if (ca == null || ca.Deleted.Value || ca.AchievementState != (int)EnumAchievementState.UnConfirmed)
                {
                    throw new ApiException("未找到对应的业绩确认信息");
                }
                else
                {
                    //验证数据权限（24.4.9 未确认的看合同所有者，已经确认的看确认人; 这里应该都是未确认的）
                    //var contracta = DbOpe_crm_contract.Instance.GetDataById(ca.ContractId);
                    //if (contracta.Issuer != currentUser)
                    //{
                    //    throw new ApiException("该用户没有数据权限");
                    //}
                    //240912 合同、发票、到账权限更改
                    Db_crm_contract contracta = DbOpe_crm_contract.Instance.GetContractById(ca.ContractId, true);
                    if (contracta == null)
                    {
                        throw new ApiException("未找到合同或该用户没有合同权限");
                    }
                }
                audit.AchievementState = (int)auditContractReceiptRegisterAchievement_IN.AchievementState;
                audit.Remark = auditContractReceiptRegisterAchievement_IN.Remark;
                audit.ReviewerDate = dt;
                audit.ReviewerId = currentUser;
                ca.AchievementState = (int)auditContractReceiptRegisterAchievement_IN.AchievementState;
                ca.Issuer = currentUser;
                DbOpe_crm_contract_receiptregister_achievement_audit.Instance.Update(audit);

                //获取用户信息
                var user = DbOpe_sys_user.Instance.GetDbSysUserById(currentUser);
                //获取组织信息树，从当前组织追溯出所有上级组织
                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                ca.OrgDivisionId = null;
                ca.OrgDivisionName = "";
                ca.OrgBrigadeId = null;
                ca.OrgBrigadeName = "";
                ca.OrgRegimentId = null;
                ca.OrgRegimentName = "";
                foreach (var org in orgList)
                {
                    switch (org.OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            ca.OrgDivisionId = org.Id;
                            ca.OrgDivisionName = org.OrgName;
                            break;
                        case EnumOrgType.Battalion:
                            ca.OrgBrigadeId = org.Id;
                            ca.OrgBrigadeName = org.OrgName;
                            break;
                        case EnumOrgType.Squadron:
                            ca.OrgRegimentId = org.Id;
                            ca.OrgRegimentName = org.OrgName;
                            break;
                    }
                }

                DbOpe_crm_contract_receiptregister.Instance.Update(ca);
                receiptId = ca.Id;
                //判断合同的客户编码是否为空，如果为空则添加客户编码信息
                //240912 合同、发票、到账权限更改
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(ca.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("未找到合同或该用户没有合同权限");
                }

                //如果选择的状态为确认，则需要判断合同的客户编码是否为空，如果为空则添加客户编码信息
                if (auditContractReceiptRegisterAchievement_IN.AchievementState == EnumAchievementState.Confirmed)
                {
                    if (contract.ContractNum.IsNullOrEmpty())
                    {
                        int Num = DbOpe_crm_contract.Instance.GetContractNumCount();
                        string ContractNum = (Num + 1).ToString().PadLeft(5, '0');
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ContractNum = ContractNum }, ca.ContractId);
                    }

                    string colId = DbOpe_crm_contract_receipt_details.Instance.GetCollectionInfoIdListByContractReceiptRegisterId(ca.Id).First();
                    //修改 补发合并客户，优惠券要将旧的未使用的移到新的里
                    BLL_Coupon.Instance.AuditRegisterUpdateCouponStates(ca.Id, colId, ca.ContractId);
                    if (contract.ContractType == (int)EnumContractType.ReNew)
                    {
                        BLL_Coupon.Instance.UpdateCouponCompany(contract.FirstParty, contract.RenewalContractNum, ca.Id);
                    }

                    

                    //日程计划 start
                    //读取配置项
                    var receivedScheduleDays = DbOpe_sys_comparam.Instance.GetComparams(EnumComParamKey.ReceivedScheduleDays.ToString());
                    var nowAfterreceivedSchedule = dt.AddDays(receivedScheduleDays);
                    TimeSpan time = new TimeSpan(9, 0, 0);
                    nowAfterreceivedSchedule = nowAfterreceivedSchedule.Date + time;
                    Tuple<DateTime, DateTime> visitorTime = BLL_Schedule.Instance.GenerateVisitorTime(
                        Tuple.Create(nowAfterreceivedSchedule, nowAfterreceivedSchedule.AddHours(1)));
                    var firstPartyCompany = DbOpe_crm_customer_subcompany.Instance.GetData(d => d.Id == contract.FirstParty);
                    BLL_Schedule.Instance.AddSchedule(new()
                    {
                        TrackingType = EnumTrackingType.TelephoneCommunication,
                        TrackingPurpose = EnumTrackingPurpose.ClientReview,
                        TrackingStage = EnumTrackingStage.Received,
                        TrackingEvents = EnumTrackingEvents.SignedCustomerVisit,
                        Remark = "合同到账自动生成的日程计划",
                        UserId = contract.Issuer,
                        CustomerDataSource = EnumCustomerDataSource.Private,
                        CustomerId = firstPartyCompany.CustomerId,
                        ContractId = ca.ContractId,
                        VisitorTimeStart = visitorTime.Item1,
                        VisitorTimeEnd = visitorTime.Item2
                    }, true, true, contract.FirstParty, false, ca.Id);
                    //日程计划 end
                    
                    //更新合同保护截止日
                    int Confirmed = EnumAchievementState.Confirmed.ToInt();
                    DateTime? ProtectionDeadline = DbOpe_crm_contract_receiptregister.Instance.GetDataList(r => r.ContractId == ca.ContractId && r.AchievementState == Confirmed).Max(r => r.ProtectionDeadline);
                    DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ProtectionDeadline = ProtectionDeadline }, r => r.Id == ca.ContractId);
                    //DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ProtectionDeadline = ca.ProtectionDeadline }, r=> r.Id == ca.ContractId && (r.ProtectionDeadline < ca.ProtectionDeadline || r.ProtectionDeadline == null));
                    //更新客户保护截止日
                    int Not = EnumCustomerPrivateRelease.Not.ToInt();
                    DbOpe_crm_customer_privatepool.Instance.UpdateData(r => new Db_crm_customer_privatepool { ProtectionDeadline = ca.ProtectionDeadline }, r => r.UserId == UserId && r.CustomerId == firstPartyCompany.CustomerId && r.State == Not && r.ProtectionDeadline < ca.ProtectionDeadline);

                    //更新跟踪记录
                    BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(firstPartyCompany.CustomerId, contract.FirstParty, contract.Id, contract.ContractName, EnumTrackingStage.Received, 1, false, true, contract.Issuer, EnumCustomerDataSource.Private, false, false, ca.Id, ca.BelongingMonth);

                    //确认业绩后通知申请服务

                    MessageMainInfo message = new MessageMainInfo();
                    message.Issuer = contract.Issuer;
                    message.MessageTypeToId = contract.Id;
                    message.MessagemMainAboutDes = contract.ContractName;
                    MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ContractNeedService, EnumMessageStepInfo.NoAppl, EnumMessageStateInfo.NoAppl);
                    BLL_MessageCenter.Instance.RealTimeSend(giveBack);

                }

                //2025年3月18日 更新钉钉业绩确认待办为完成 
                //2025年3月26日 有疑问也要设置已完成
                DbOpe_sys_messagecenterdetail.Instance.UpdateDingAchievementDone(ca.Id);

                string dataState = Dictionary.RegisterAchievementState.First(e => e.Value == auditContractReceiptRegisterAchievement_IN.AchievementState.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_receiptregister, Db_crm_contract>("到账业绩审核流程", ca.Id.ToString(), ca, contract, auditContractReceiptRegisterAchievement_IN.Remark, dataState, "业绩确认");
                //自动匹配发票和到账
                BLL_ContractInvoiceNew.Instance.AutoSetAuthorityAndMatching(receiptId);
            });
            //DbOpe_crm_contract_receiptregister.Instance.SaveQueues();
        }

        /// <summary>
        /// 批量确认合同到账登记业绩审核信息
        /// </summary>
        public void BatchAuditContractReceiptRegisterAchievement(BatchAuditContractReceiptRegisterAchievement_IN batchAuditContractReceiptRegisterAchievement_IN)
        {
            //参数验证
            if (batchAuditContractReceiptRegisterAchievement_IN.AchievementState != EnumAchievementState.Confirmed &&
                batchAuditContractReceiptRegisterAchievement_IN.AchievementState != EnumAchievementState.Questionable
            )
            {
                throw new ApiException("参数错误");
            }
            if (batchAuditContractReceiptRegisterAchievement_IN.AchievementState == EnumAchievementState.Questionable && string.IsNullOrEmpty(batchAuditContractReceiptRegisterAchievement_IN.Remark))
            {
                throw new ApiException("备注不可为空");
            }
            //到账登记表ids
            List<string> caIds = new List<string>();
            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                DateTime dt = DateTime.Now;
                var audits = DbOpe_crm_contract_receiptregister_achievement_audit.Instance.GetByIds(batchAuditContractReceiptRegisterAchievement_IN.Ids);
                foreach (var audit in audits)
                {
                    if (audit == null || audit.Deleted.Value || audit.AchievementState != (int)EnumAchievementState.UnConfirmed)
                    {
                        throw new ApiException("未找到对应的待审核信息");
                    }
                    caIds.Add(audit.ContractReceiptRegisterId);
                }
                var cas = DbOpe_crm_contract_receiptregister.Instance.GetByIds(caIds);
                //验证数据权限（合同所有者可确认登记）
                List<string> ids = cas.Select(r => r.ContractId).ToList();
                List<Db_crm_contract> otherIssuerContracts = DbOpe_crm_contract.Instance.GetDataList(r => ids.Contains(r.Id) && r.Issuer != UserId);
                if (otherIssuerContracts.Count() > 0)
                {
                    throw new ApiException("该用户没有数据权限");
                }

                //获取用户信息
                var user = DbOpe_sys_user.Instance.GetDbSysUserById(UserId);
                //获取组织信息树，从当前组织追溯出所有上级组织
                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);

                foreach (var audit in audits)
                {
                    var ca = cas.Find(c => c.Id == audit.ContractReceiptRegisterId);
                    if (ca == null || ca.Deleted.Value || ca.AchievementState != (int)EnumAchievementState.UnConfirmed)
                    {
                        throw new ApiException("未找到对应的业绩确认信息");
                    }
                    audit.AchievementState = (int)batchAuditContractReceiptRegisterAchievement_IN.AchievementState;
                    audit.Remark = batchAuditContractReceiptRegisterAchievement_IN.Remark;
                    audit.ReviewerDate = dt;
                    audit.ReviewerId = currentUser;
                    ca.AchievementState = (int)batchAuditContractReceiptRegisterAchievement_IN.AchievementState;
                    ca.Issuer = currentUser;
                    ca.OrgDivisionId = null;
                    ca.OrgDivisionName = "";
                    ca.OrgBrigadeId = null;
                    ca.OrgBrigadeName = "";
                    ca.OrgRegimentId = null;
                    ca.OrgRegimentName = "";
                    foreach (var org in orgList)
                    {
                        switch (org.OrgType)
                        {
                            case EnumOrgType.BattleTeam:
                                ca.OrgDivisionId = org.Id;
                                ca.OrgDivisionName = org.OrgName;
                                break;
                            case EnumOrgType.Battalion:
                                ca.OrgBrigadeId = org.Id;
                                ca.OrgBrigadeName = org.OrgName;
                                break;
                            case EnumOrgType.Squadron:
                                ca.OrgRegimentId = org.Id;
                                ca.OrgRegimentName = org.OrgName;
                                break;
                        }
                    }
                }
                DbOpe_crm_contract_receiptregister_achievement_audit.Instance.Update(audits);
                DbOpe_crm_contract_receiptregister.Instance.Update(cas);

                List<string> contractIds = cas.Select(r => r.ContractId).ToList();
                List<Db_crm_contract> contracts = DbOpe_crm_contract.Instance.GetDataList(r => contractIds.Contains(r.Id));

                //如果选择的状态为确认，则需要循环判断每个合同的客户编码是否为空，如果为空则添加客户编码信息
                if (batchAuditContractReceiptRegisterAchievement_IN.AchievementState == EnumAchievementState.Confirmed)
                {
                    int Num = DbOpe_crm_contract.Instance.GetContractNumCount();
                    int i = 0;
                    foreach (Db_crm_contract contract in contracts)
                    {
                        //判断合同的客户编码是否为空，如果为空则添加客户编码信息
                        if (contract.ContractNum.IsNullOrEmpty())
                        {
                            i++;
                            string ContractNum = (Num + i).ToString().PadLeft(5, '0');
                            DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ContractNum = ContractNum }, contract.Id);
                        }
                    }

                    //BLL_Coupon.Instance.BatchAuditRegisterUpdateCouponStates(cas.Select(c => c.Id).ToList());




                    foreach (var ca in cas)
                    {
                        var contract = contracts.Find(c => c.Id == ca.ContractId);

                        string colId = DbOpe_crm_contract_receipt_details.Instance.GetCollectionInfoIdListByContractReceiptRegisterId(ca.Id).First();

                        BLL_Coupon.Instance.AuditRegisterUpdateCouponStates(ca.Id, colId, ca.ContractId);
                        //修改 补发合并客户，优惠券要将旧的未使用的移到新的里
                        if (contract.ContractType == (int)EnumContractType.ReNew)
                        {
                            BLL_Coupon.Instance.UpdateCouponCompany(contract.FirstParty, contract.RenewalContractNum, ca.Id);
                        }

                        //日程计划 start
                        //读取配置项
                        var receivedScheduleDays = DbOpe_sys_comparam.Instance.GetComparams(EnumComParamKey.ReceivedScheduleDays.ToString());
                        var nowAfterreceivedSchedule = dt.AddDays(receivedScheduleDays);
                        TimeSpan time = new TimeSpan(9, 0, 0);
                        nowAfterreceivedSchedule = nowAfterreceivedSchedule.Date + time;
                        Tuple<DateTime, DateTime> visitorTime = BLL_Schedule.Instance.GenerateVisitorTime(
                            Tuple.Create(nowAfterreceivedSchedule, nowAfterreceivedSchedule.AddHours(1)));
                        BLL_Schedule.Instance.AddSchedule(new()
                        {
                            TrackingType = EnumTrackingType.TelephoneCommunication,
                            TrackingPurpose = EnumTrackingPurpose.ClientReview,
                            TrackingStage = EnumTrackingStage.Received,
                            TrackingEvents = EnumTrackingEvents.SignedCustomerVisit,
                            Remark = "合同到账自动生成的日程计划",
                            UserId = contract.Issuer,
                            CustomerDataSource = EnumCustomerDataSource.Private,
                            CustomerId = contract.CustomerId,
                            ContractId = ca.ContractId,
                            VisitorTimeStart = visitorTime.Item1,
                            VisitorTimeEnd = visitorTime.Item2
                        }, true, true, contract.FirstParty, false, ca.Id);
                        //日程计划 end
                        //更新跟踪记录
                        BLL_TrackingRecord.Instance.InternalAddOrModifyTrackingRecord(contract.CustomerId, contract.FirstParty, contract.Id, contract.ContractName, EnumTrackingStage.Received, 1, false, true, contract.Issuer, EnumCustomerDataSource.Private, false, false, ca.Id, ca.BelongingMonth);

                        //更新合同保护截止日
                        int Confirmed = EnumAchievementState.Confirmed.ToInt();
                        DateTime? ProtectionDeadline = DbOpe_crm_contract_receiptregister.Instance.GetDataList(r => r.ContractId == ca.ContractId && r.AchievementState == Confirmed).Max(r => r.ProtectionDeadline);
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ProtectionDeadline = ProtectionDeadline }, r => r.Id == ca.ContractId);
                        //DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ProtectionDeadline = ca.ProtectionDeadline }, r => r.Id == ca.ContractId && (r.ProtectionDeadline < ca.ProtectionDeadline || r.ProtectionDeadline == null));
                        //更新客户保护截止日
                        int Not = EnumCustomerPrivateRelease.Not.ToInt();
                        var firstPartyCompany = DbOpe_crm_customer_subcompany.Instance.GetData(d => d.Id == contract.FirstParty);
                        DbOpe_crm_customer_privatepool.Instance.UpdateData(r => new Db_crm_customer_privatepool { ProtectionDeadline = ca.ProtectionDeadline }, r => r.UserId == UserId && r.CustomerId == firstPartyCompany.CustomerId && r.State == Not && r.ProtectionDeadline < ca.ProtectionDeadline);

                        //自动判断并生成开票权限，自动匹配发票和到账
                        BLL_ContractInvoiceNew.Instance.AutoSetAuthorityAndMatching(ca.Id);
                    }
                }

                foreach (var ca in cas)
                {
                    //2025年3月18日 更新钉钉业绩确认待办为完成 
                    //2025年3月26日 有疑问也要设置已完成
                    DbOpe_sys_messagecenterdetail.Instance.UpdateDingAchievementDone(ca.Id);
                }

                Db_sys_form form = DbOpe_sys_form.Instance.GetData(r => r.MethodName == "AuditContractReceiptRegisterAchievement" && r.ControllerName == "ContractAchievement");
                foreach (var audit in audits)
                {
                    var ca = cas.Find(c => c.Id == audit.ContractReceiptRegisterId);
                    var contract = contracts.Find(c => c.Id == ca.ContractId);
                    string dataState = Dictionary.RegisterAchievementState.First(e => e.Value == batchAuditContractReceiptRegisterAchievement_IN.AchievementState.ToInt().ToString()).Name;
                    Com_SysForm.Instance.FormId = form.Id;
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_receiptregister, Db_crm_contract>("到账业绩审核流程", ca.Id.ToString(), ca, contract, batchAuditContractReceiptRegisterAchievement_IN.Remark, dataState, "业绩确认");
                }
            });
            //DbOpe_crm_contract_receiptregister.Instance.SaveQueues();
        }

        /// <summary>
        /// 撤销合同到账登记业绩审核信息(状态为确认、有疑问才可以)
        /// </summary>
        public void RevokeContractReceiptRegisterAchievement(RevokeAuditContractReceiptRegisterAchievement_IN revokeAuditContractReceiptRegisterAchievement_IN)
        {
            DbOpe_crm_contract_receiptregister.Instance.TransDeal(() =>
            {
                DateTime dt = DateTime.Now;
                revokeAuditContractReceiptRegisterAchievement_IN.Ids.ForEach(id =>
                {
                    var audit = DbOpe_crm_contract_receiptregister_achievement_audit.Instance.GetDataById(id);
                    if (audit == null || audit.Deleted.Value)
                    {
                        throw new ApiException("未找到对应的业绩确认信息");
                    }
                    var ca = DbOpe_crm_contract_receiptregister.Instance.GetContractInfoByContractReceiptRegisterId(audit.ContractReceiptRegisterId);
                    if (ca == null)
                    {
                        throw new ApiException("未找到对应的业绩确认信息");
                    }
                    if (audit.AchievementState != (int)EnumAchievementState.Confirmed && audit.AchievementState != (int)EnumAchievementState.Questionable)
                    {
                        throw new ApiException("只能撤销已确认或者有疑问的业绩确认信息(错误合同号：" + ca.ContractNo + ")");
                    }
                    //验证数据权限（业绩确认人可撤销登记）
                    if (ca.AchievementState != (int)EnumAchievementState.Confirmed && ca.AchievementState != (int)EnumAchievementState.Questionable)
                    {
                        throw new ApiException("只能撤销已确认或者有疑问的业绩确认信息(错误合同号：" + ca.ContractNo + ")");
                    }
                    if (ca.Issuer != UserId)
                    {
                        throw new ApiException("该用户没有数据权限(错误合同号：" + ca.ContractNo + ")");
                    }
                    if (ca.Locked == (int)EnumLocked.Locked)
                    {
                        throw new ApiException("业绩已锁定无法撤销(错误合同号：" + ca.ContractNo + ")");
                    }
                    int AchievementState = ca.AchievementState.Value;
                    audit.AchievementState = (int)EnumAchievementState.UnConfirmed;
                    audit.Remark = "";
                    var caData = DbOpe_crm_contract_receiptregister.Instance.GetDataById(audit.ContractReceiptRegisterId);
                    caData.AchievementState = (int)EnumAchievementState.UnConfirmed;
                    DbOpe_crm_contract_receiptregister_achievement_audit.Instance.Update(audit);
                    DbOpe_crm_contract_receiptregister.Instance.Update(caData);
                    ////如果撤销前是确认状态，需要判断该合同下是否还有其他已确认的登记信息，如果没有登记信息，则清空合同的客户编码
                    //if (AchievementState == (int)EnumAchievementState.Confirmed)
                    //{
                    //    List<Db_crm_contract_receiptregister> receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetConfirmedContractReceiptRegisterByContractId(ca.ContractId);
                    //    if (receiptregister.Count() == 0)
                    //    {
                    //        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ContractNum = String.Empty }, ca.ContractId);
                    //    }
                    //}

                    //撤销已发送的消息
                    string dataState = Dictionary.RegisterAchievementState.First(e => e.Value == audit.AchievementState.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.CancelWorkflowPending("到账业绩审核流程", audit.ContractReceiptRegisterId, dataState, caData);
                });
            });
            //DbOpe_crm_contract_receiptregister.Instance.SaveQueues();
        }

        /// <summary>
        /// 根据合同Id获取到账登记的合同信息
        /// </summary>
        /// <param name="contractId"></param>
        public ContractSimple_Out GetContractInfoByContractId(string contractId)
        {
            ContractSimple_Out result = DbOpe_crm_contract.Instance.GetContractInfoByContractId(contractId, true);
            if (result == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return result;
        }

        /// <summary>
        /// 根据合同登记Id获取合同到账登记的合同信息和合同到账登记信息
        /// </summary>
        /// <param name="contractReceiptRegisterId"></param>
        /// <returns></returns>
        public ContractWithReceiptRegister_Out GetContractInfoByContractReceiptRegisterId(string contractReceiptRegisterId)
        {
            ContractWithReceiptRegister_Out result = DbOpe_crm_contract_receiptregister.Instance.GetContractInfoByContractReceiptRegisterId(contractReceiptRegisterId, true);
            if (result == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return result;
        }

        /// <summary>
        /// 根据合同登记Id获取合同到账登记业绩审核确认信息
        /// </summary>
        /// <param name="contractReceiptRegisterAuditId"></param>
        public ContractReceiptRegisterAchievementAudit_Out GetContractReceiptRegisterAchievementAuditById(string contractReceiptRegisterAuditId)
        {
            ContractReceiptRegisterAchievementAudit_Out result = DbOpe_crm_contract_receiptregister_achievement_audit.Instance.GetContractReceiptRegisterAchievementAuditById(contractReceiptRegisterAuditId);
            if (result == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return result;
        }

        /// <summary>
        /// 根据到账登记表Id获取业绩确认详情信息
        /// </summary>
        /// <param name="contractReceiptRegisterId"></param>
        /// <param name="isSales"></param>
        /// <returns></returns>
        public ContractReceiptRegisterInfo_Out GetInfoByContractReceiptRegisterId(string contractReceiptRegisterId, bool isSales = false)
        {
            ContractReceiptRegisterInfo_Out contractReceiptRegisterInfo_Out = new ContractReceiptRegisterInfo_Out();

            contractReceiptRegisterInfo_Out.ReceiptRegisterCongratulations = DbOpe_crm_contract_receiptregister_congratulations.Instance.GetContractReceiptRegisterCongratulationsByContractReceiptRegisterId(contractReceiptRegisterId);
            contractReceiptRegisterInfo_Out.ReceiptRegisterCoupon = DbOpe_crm_customer_coupon.Instance.GetContractReceiptRegisterCouponByContractReceiptRegisterId(contractReceiptRegisterId);

            //9.26 这里要把历史的审核记录也拉出来
            var achievementAuditInfos = DbOpe_crm_contract_receiptregister_achievement_audit.Instance.GetContractReceiptRegisterAchievementAuditsByContractReceiptRegisterId(contractReceiptRegisterId);
            var receiptregisterAuditInfos = DbOpe_crm_contract_receiptregister_audit.Instance.GetContractReceiptRegisterAuditsByContractReceiptRegisterId(contractReceiptRegisterId, isSales);
            var auditInfos = achievementAuditInfos.Concat(receiptregisterAuditInfos).ToList().OrderByDescending(a => a.CreateDate).ToList();
            contractReceiptRegisterInfo_Out.ReceiptRegisterAuditInfo = auditInfos;
            contractReceiptRegisterInfo_Out.ContractReceiptRegisterId = contractReceiptRegisterId;
            var ci = DbOpe_crm_contract_receiptregister.Instance.GetContractInfoByContractReceiptRegisterId(contractReceiptRegisterId, isSales);
            if (ci == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo = ci.MappingTo<ReceiptRegisterContractInfo_Out>();
            var contractId = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ContractId;
            var colItems = DbOpe_crm_contract_receipt_details.Instance.GetCollectionInfoItemsByContractReceiptRegisterId(contractReceiptRegisterId);
            var historyItems = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(contractId, contractReceiptRegisterId);
            var leftAmount = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ContractAmount - colItems.Sum(c => c.ArrivalAmount) - historyItems.Sum(c => c.ArrivalAmount);
            contractReceiptRegisterInfo_Out.CollectionInfo = new ReceiptRegisterCollectionInfo_Out()
            {
                ContractId = contractId,
                ContractReceiptRegisterId = contractReceiptRegisterId,
                IsReceipt = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.IsReceipt,
                ProtectionDeadline = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ProtectionDeadline,
                //ProtectionDeadline = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ProtectionDeadline.Value == null? "": contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ProtectionDeadline.Value.ToString("yyyy-MM-dd"),
                CollectionInfoItem = colItems,
                LeftAmount = leftAmount,
            };
            contractReceiptRegisterInfo_Out.HistoryCollectionInfo = historyItems;

            contractReceiptRegisterInfo_Out.ProductInfo = DbOpe_crm_contract_receiptregister_achievement.Instance.GetReceiptRegisterContractProductInfo(contractReceiptRegisterId);
            List<ReceiptRegisterProductInfo_Out> superSubAccountResult = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountReceiptRegisterByContractId(contractId, true);
            contractReceiptRegisterInfo_Out.ProductInfo.AddRange(superSubAccountResult);

            contractReceiptRegisterInfo_Out.ProjectInfo = DbOpe_crm_contract_receiptregister_projectinfo_achievement.Instance.GetReceiptRegisterContractProjectInfo(contractReceiptRegisterId);
            var bankFlag = colItems.Find(c => c.PaymentMethod != (int)EnumPaymentMethod.Cash) != null;
            var cashFlag = colItems.Find(c => c.PaymentMethod != (int)EnumPaymentMethod.Bank) != null;
            contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.BankPaymentAmount = colItems.Sum(c => c.BankPaymentAmount);
            contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.CashPaymentAmount = colItems.Sum(c => c.CashPaymentAmount);
            contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.Honour = colItems.Sum(c => c.Honour);
            contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.PaymentMethod = (int)(bankFlag ? (cashFlag ? EnumPaymentMethod.BankAndCash : EnumPaymentMethod.Bank) : EnumPaymentMethod.Cash);
            contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.PaymentMethodName = ((EnumPaymentMethod)contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.PaymentMethod).GetEnumDescription();
            //contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.IsInvoice = DbOpe_crm_contract_invoice.Instance.GetIsInvoicedByContractId(contractId);
            contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.IsInvoice = DbOpe_crm_invoice.Instance.GetIsInvoicedByContractId(contractId);

            contractReceiptRegisterInfo_Out.NonPerformanceAmount = ci.NonPerformanceAmount;
            contractReceiptRegisterInfo_Out.RechargeAmount = ci.RechargeAmount;
            return contractReceiptRegisterInfo_Out;
        }
        /// <summary>
        /// 根据到账登记表Id获取业绩确认详情信息(销售)
        /// </summary>
        /// <param name="contractReceiptRegisterId"></param>
        /// <returns></returns>
        public ContractReceiptRegisterInfoSales_Out GetInfoByContractReceiptRegisterId_Sales(string contractReceiptRegisterId)
        {
            ContractReceiptRegisterInfoSales_Out contractReceiptRegisterInfo_Out = new ContractReceiptRegisterInfoSales_Out();

            //contractReceiptRegisterInfo_Out.ReceiptRegisterCongratulations = DbOpe_crm_contract_receiptregister_congratulations.Instance.GetContractReceiptRegisterCongratulationsByContractReceiptRegisterId(contractReceiptRegisterId);

            //9.26 这里要把历史的审核记录也拉出来
            var achievementAuditInfos = DbOpe_crm_contract_receiptregister_achievement_audit.Instance.GetContractReceiptRegisterAchievementAuditsByContractReceiptRegisterId(contractReceiptRegisterId);
            var receiptregisterAuditInfos = DbOpe_crm_contract_receiptregister_audit.Instance.GetContractReceiptRegisterAuditsByContractReceiptRegisterId(contractReceiptRegisterId, true);
            var auditInfos = achievementAuditInfos.Concat(receiptregisterAuditInfos).ToList().OrderByDescending(a => a.CreateDate).ToList();
            contractReceiptRegisterInfo_Out.ReceiptRegisterAuditInfo = auditInfos;
            contractReceiptRegisterInfo_Out.ContractReceiptRegisterId = contractReceiptRegisterId;
            var ci = DbOpe_crm_contract_receiptregister.Instance.GetContractInfoByContractReceiptRegisterId(contractReceiptRegisterId, true);
            if (ci == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo = ci.MappingTo<ContractWithReceiptRegisterSales_Out>();
            var contractId = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ContractId;
            var colItems = DbOpe_crm_contract_receipt_details.Instance.GetCollectionInfoItemsByContractReceiptRegisterId(contractReceiptRegisterId).MappingTo<List<ReceiptRegisterCollectionInfoItemSales_Out>>();
            var historyItems = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(contractId, contractReceiptRegisterId).MappingTo<List<ReceiptRegisterCollectionInfoItemSales_Out>>();
            var leftAmount = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ContractAmount - colItems.Sum(c => c.ArrivalAmount) - historyItems.Sum(c => c.ArrivalAmount);
            contractReceiptRegisterInfo_Out.CollectionInfo = new ReceiptRegisterCollectionInfoSales_Out()
            {
                ContractId = contractId,
                ContractReceiptRegisterId = contractReceiptRegisterId,
                IsReceipt = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.IsReceipt,
                ProtectionDeadline = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ProtectionDeadline,
                //ProtectionDeadline = contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ProtectionDeadline.Value == null? "": contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.ProtectionDeadline.Value.ToString("yyyy-MM-dd"),
                CollectionInfoItem = colItems,
                LeftAmount = leftAmount,
            };
            contractReceiptRegisterInfo_Out.HistoryCollectionInfo = historyItems;

            contractReceiptRegisterInfo_Out.ProductInfo = DbOpe_crm_contract_receiptregister_achievement.Instance.GetReceiptRegisterContractProductInfo(contractReceiptRegisterId);
            List<ReceiptRegisterProductInfo_Out> superSubAccountResult = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountReceiptRegisterByContractId(contractId, true);
            contractReceiptRegisterInfo_Out.ProductInfo.AddRange(superSubAccountResult);

            contractReceiptRegisterInfo_Out.ProjectInfo = DbOpe_crm_contract_receiptregister_projectinfo_achievement.Instance.GetReceiptRegisterContractProjectInfo(contractReceiptRegisterId);
            var bankFlag = colItems.Find(c => c.PaymentMethod != (int)EnumPaymentMethod.Cash) != null;
            var cashFlag = colItems.Find(c => c.PaymentMethod != (int)EnumPaymentMethod.Bank) != null;
            //contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.IsInvoice = DbOpe_crm_contract_invoice.Instance.GetIsInvoicedByContractId(contractId);
            contractReceiptRegisterInfo_Out.ReceiptRegisterAndContractInfo.IsInvoice = DbOpe_crm_invoice.Instance.GetIsInvoicedByContractId(contractId);
            // 新增字段赋值 - 直接从ci对象获取
            contractReceiptRegisterInfo_Out.NonPerformanceAmount = ci.NonPerformanceAmount;
            contractReceiptRegisterInfo_Out.RechargeAmount = ci.RechargeAmount;
            return contractReceiptRegisterInfo_Out;
        }

        /// <summary>
        /// 获取客户业绩摘要
        /// </summary>
        /// <param name="customerId"></param>
        /// <returns></returns>
        public CustomerAchievementAbstract_OUT GetCustomerAchievementAbstract(string customerId)
        {
            CustomerAchievementAbstract_OUT customerAchievementAbstract_OUT = new CustomerAchievementAbstract_OUT();
            customerAchievementAbstract_OUT.ContractAmountTotal = DbOpe_crm_contract.Instance.GetContractAmountByCustomerId(customerId);
            customerAchievementAbstract_OUT.AchievementTotal = DbOpe_crm_contract_receiptregister.Instance.GetAchivementByCustomerId(customerId);
            customerAchievementAbstract_OUT.RefundTotal = DbOpe_crm_contract_receiptregister.Instance.GetRefundAmountByCustomerId(customerId);
            customerAchievementAbstract_OUT.UnReceivedTotal = customerAchievementAbstract_OUT.ContractAmountTotal - DbOpe_crm_contract_receipt_details.Instance.GetArrivalAmountByCustomerId(customerId);
            return customerAchievementAbstract_OUT;
        }
        #region 数据统计 
        /// <summary>
        /// 获取个人业绩统计列表
        /// </summary>
        /// <param name="dateSpanParams"></param>
        /// <param name="userIds"></param>
        /// <returns></returns>
        public List<UserSalesAchivementAmount> GetUserSalesAchivementAmountList(DateSpanParams dateSpanParams, List<string> userIds)
        {
            return DbOpe_crm_contract_receiptregister.Instance.GetUserSalesAchivementAmountList(dateSpanParams, userIds);
        }

        /// <summary>
        /// （组织）客户数据-销售目标统计
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        public SalesTargetStatistics_OUT SalesTargetStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            SalesDataStatisticsParams salesDataStatistics = salesDataStatistics_IN.MappingTo<SalesDataStatisticsParams>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesDataStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesDataStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesDataStatistics_IN.OrgId);
            }
            switch (orgInfo.OrgType)
            {
                case EnumOrgType.Squadron:
                    salesDataStatistics.OrgRegimentId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.Battalion:
                    salesDataStatistics.OrgBrigadeId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.BattleTeam:
                    salesDataStatistics.OrgDivisionId = new List<string>() { orgInfo.Id };
                    break;
            }
            //进行统计
            SalesTargetStatistics_OUT salesTargetStatistics_OUT = new SalesTargetStatistics_OUT();
            var contractData = DbOpe_crm_contract.Instance.SalesTargetStatistics(salesDataStatistics);
            var achieveData = DbOpe_crm_contract_receiptregister.Instance.SalesTargetStatistics(salesDataStatistics);
            //获取该组织每年的目标
            var years = achieveData.Select(d => d.Key.Year).Distinct().ToList();
            List<Db_sys_performance_objectives> performanceList = DbOpe_sys_performance_objectives.Instance.GetDataList(d =>
                SqlFunc.ContainsArray(years, d.ObjectivesYear)
                && (d.WaitEffec == null || d.WaitEffec == 0)
                && d.OrgId == salesDataStatistics_IN.OrgId
            );
            salesTargetStatistics_OUT.TargetStatisticsItems = new List<SalesTargetStatisticsItem_OUT>();
            foreach (StatisticsDoubleItem<DateTime> ssi in achieveData)
            {
                salesTargetStatistics_OUT.TargetStatisticsItems.Add(new SalesTargetStatisticsItem_OUT()
                {
                    Name = ssi.Name,
                    //ContractAmountTotal = contractData.Find(d => d.Name == ssi.Name).Value == null ? 0 : contractData.Find(d => d.Name == ssi.Name).Value.Value,
                    PerformanceObjectiveTotal = DbOpe_sys_performance_objectives.Instance.GetMonthObjFromRowData(ssi.Key, performanceList) * 10000,
                    AchievementTotal = ssi.Value == null ? 0 : ssi.Value.Value,
                    EffectiveAchievementTotal = ssi.SecondValue == null ? 0 : ssi.SecondValue.Value,
                });
            }
            return salesTargetStatistics_OUT;
        }

        /// <summary>
        /// （个人-组织）客户数据-销售业绩 (包含同比环比)
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        public SalesDataStatistics_OUT SalesDataStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            SalesDataStatisticsParams salesDataStatistics = salesDataStatistics_IN.MappingTo<SalesDataStatisticsParams>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesDataStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesDataStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesDataStatistics_IN.OrgId);
            }
            switch (orgInfo.OrgType)
            {
                case EnumOrgType.Squadron:
                    salesDataStatistics.OrgRegimentId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.Battalion:
                    salesDataStatistics.OrgBrigadeId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.BattleTeam:
                    salesDataStatistics.OrgDivisionId = new List<string>() { orgInfo.Id };
                    break;
            }
            var dt = DateTime.Now;
            //进行统计 
            SalesDataStatistics_OUT salesDataStatistics_OUT = new SalesDataStatistics_OUT();
            //个人当前统计 （环比要向前多统计一个月）
            SalesDataStatisticsParams userTempCon = salesDataStatistics.MappingTo<SalesDataStatisticsParams>();
            userTempCon.UserId = UserId;
            userTempCon.OrgRegimentId = new List<string>();
            userTempCon.OrgBrigadeId = new List<string>();
            userTempCon.OrgDivisionId = new List<string>();
            var userAchieveData = DbOpe_crm_contract_receiptregister.Instance.SalesDateStatistics(userTempCon, EnumDateStatisticsType.HuanBi);
            //团队当前统计 （环比要向前多统计一个月）
            var orgAchieveData = DbOpe_crm_contract_receiptregister.Instance.SalesDateStatistics(salesDataStatistics, EnumDateStatisticsType.HuanBi);
            //个人同比
            var usertongbiAchieveData = DbOpe_crm_contract_receiptregister.Instance.SalesDateStatistics(userTempCon, EnumDateStatisticsType.TongBi);
            //团队同比统计
            var orgtongbiAchieveData = DbOpe_crm_contract_receiptregister.Instance.SalesDateStatistics(salesDataStatistics, EnumDateStatisticsType.TongBi);
            //统计结果
            salesDataStatistics_OUT.StatisticsItems = new List<SalesDataStatisticsItem_OUT>();
            //var first = userAchieveData.First();
            var compare = userAchieveData.First();
            var orgCompare = orgAchieveData.First();
            foreach (StatisticsSimpleItem<DateTime> ssi in userAchieveData)
            {
                if (ssi.Index >= 0)
                {
                    var userTongbi = usertongbiAchieveData.Find(d => d.Index == ssi.Index)?.Value;
                    var userHuanbi = compare?.Value;
                    var orgData = orgAchieveData.Find(d => d.Index == ssi.Index)?.Value;
                    var orgTongbi = orgtongbiAchieveData.Find(d => d.Index == ssi.Index)?.Value;
                    var orgHuanbi = orgCompare?.Value;
                    salesDataStatistics_OUT.StatisticsItems.Add(new SalesDataStatisticsItem_OUT()
                    {
                        Index = ssi.Index,
                        Name = ssi.Name,
                        UserAchievementTotal = ssi.Value == null ? 0 : ssi.Value.Value,
                        UserAchievementTotal_TongBi = userTongbi == null ? 0 : userTongbi.Value,
                        UserAchievementTotal_HuanBi = userHuanbi == null ? 0 : userHuanbi.Value,
                        OrgAchievementTotal = orgData == null ? 0 : orgData.Value,
                        OrgAchievementTotal_TongBi = orgTongbi == null ? 0 : orgTongbi.Value,
                        OrgAchievementTotal_HuanBi = orgHuanbi == null ? 0 : orgHuanbi.Value,
                    });
                    compare = ssi;
                    orgCompare = orgAchieveData.Find(d => d.Index == ssi.Index);
                }
            }
            return salesDataStatistics_OUT;
        }

        /// <summary>
        /// （组织）客户数据-业绩排行
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        public SalesRankStatistics_OUT SalesOrgRankStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            SalesRankStatisticsParmas salesRankStatisticsParmas = salesDataStatistics_IN.MappingTo<SalesRankStatisticsParmas>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesDataStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesDataStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesDataStatistics_IN.OrgId);
                salesRankStatisticsParmas.QueryOrgId = salesDataStatistics_IN.OrgId;
            }
            salesRankStatisticsParmas.EnumRankOrgType = orgInfo.OrgType;
            salesRankStatisticsParmas.FindOrgId = userOrg;
            salesRankStatisticsParmas.FindUserId = UserId;
            //进行统计
            SalesRankStatistics_OUT salesRankStatistics_OUT = new SalesRankStatistics_OUT();
            var rankData = DbOpe_crm_contract_receiptregister.Instance.SalesOrgRankStatistics(salesRankStatisticsParmas);
            return rankData;
        }
        /// <summary>
        /// （个人）客户数据-业绩排行
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        public SalesRankStatistics_OUT SalesUserRankStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            SalesRankStatisticsParmas salesRankStatisticsParmas = salesDataStatistics_IN.MappingTo<SalesRankStatisticsParmas>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesDataStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesDataStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesDataStatistics_IN.OrgId);
                salesRankStatisticsParmas.QueryOrgId = salesDataStatistics_IN.OrgId;
            }
            salesRankStatisticsParmas.EnumRankOrgType = orgInfo.OrgType;
            salesRankStatisticsParmas.FindOrgId = userOrg;
            salesRankStatisticsParmas.FindUserId = UserId;
            //进行统计
            SalesRankStatistics_OUT salesRankStatistics_OUT = new SalesRankStatistics_OUT();
            var rankData = DbOpe_crm_contract_receiptregister.Instance.SalesUserRankStatistics(salesRankStatisticsParmas);
            return rankData;
        }
        /// <summary>
        /// 客户数据-产品业绩
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        public SalesProductStatistics_OUT SalesProductStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            SalesDataStatisticsParams salesDataStatisticsParams = salesDataStatistics_IN.MappingTo<SalesDataStatisticsParams>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesDataStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesDataStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesDataStatistics_IN.OrgId);
            }
            switch (orgInfo.OrgType)
            {
                case EnumOrgType.Squadron:
                    salesDataStatisticsParams.OrgRegimentId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.Battalion:
                    salesDataStatisticsParams.OrgBrigadeId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.BattleTeam:
                    salesDataStatisticsParams.OrgDivisionId = new List<string>() { orgInfo.Id };
                    break;
            }
            return DbOpe_crm_contract_receiptregister_achievement.Instance.SalesProductStatistics(salesDataStatisticsParams);
        }
        /// <summary>
        /// 客户数据-合同统计
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        public SalesContractStatistics_OUT SalesContractStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            SalesDataStatisticsParams salesDataStatisticsParams = salesDataStatistics_IN.MappingTo<SalesDataStatisticsParams>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesDataStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesDataStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesDataStatistics_IN.OrgId);
            }
            switch (orgInfo.OrgType)
            {
                case EnumOrgType.Squadron:
                    salesDataStatisticsParams.OrgRegimentId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.Battalion:
                    salesDataStatisticsParams.OrgBrigadeId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.BattleTeam:
                    salesDataStatisticsParams.OrgDivisionId = new List<string>() { orgInfo.Id };
                    break;
            }
            return DbOpe_crm_contract_productinfo.Instance.SalesContractStatistics(salesDataStatisticsParams);

        }
        /// <summary>
        /// 客户数据-客户统计
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        public CustomerStatistics_OUT CustomerStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            CustomerStatistics_OUT customerStatistics_OUT = new CustomerStatistics_OUT();
            CustomerStatisticsParams customerStatisticsParams = salesDataStatistics_IN.MappingTo<CustomerStatisticsParams>();
            SalesDataStatisticsParams salesDataStatisticsParams = salesDataStatistics_IN.MappingTo<SalesDataStatisticsParams>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesDataStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesDataStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesDataStatistics_IN.OrgId);
                customerStatisticsParams.OrgIds = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(salesDataStatistics_IN.OrgId).Select(o => o.Id).ToList();
                //customerStatisticsParams.OrgIds.Add(salesDataStatistics_IN.OrgId);
            }
            else
            {
                customerStatisticsParams.OrgIds = userOrgList;
            }
            switch (orgInfo.OrgType)
            {
                case EnumOrgType.Squadron:
                    salesDataStatisticsParams.OrgRegimentId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.Battalion:
                    salesDataStatisticsParams.OrgBrigadeId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.BattleTeam:
                    salesDataStatisticsParams.OrgDivisionId = new List<string>() { orgInfo.Id };
                    break;
            }
            customerStatistics_OUT.NewCustomer = DbOpe_crm_customer_privatepool.Instance.CustomerStatistics(salesDataStatisticsParams);
            customerStatistics_OUT.SignCustomer = DbOpe_crm_contract.Instance.CustomerStatistics(salesDataStatisticsParams);
            return customerStatistics_OUT;

        }
        /// <summary>
        /// 客户数据-跟踪统计
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        public TrackRecordStatistics_OUT TrackRecordStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            CustomerStatisticsParams customerStatisticsParams = salesDataStatistics_IN.MappingTo<CustomerStatisticsParams>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesDataStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesDataStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                customerStatisticsParams.OrgIds = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(salesDataStatistics_IN.OrgId).Select(o => o.Id).ToList();
                //customerStatisticsParams.OrgIds.Add(salesDataStatistics_IN.OrgId);
            }
            else
            {
                customerStatisticsParams.OrgIds = userOrgList;
            }
            customerStatisticsParams.UserId = currentUser;
            return DbOpe_crm_trackingrecord.Instance.TrackRecordStatistics(customerStatisticsParams);

        }
        /// <summary>
        /// 客户数据-跟踪排行
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        public TrackingRecordRankStatistics_OUT TrackingRecordRankStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            SalesRankStatisticsParmas salesRankStatisticsParmas = salesDataStatistics_IN.MappingTo<SalesRankStatisticsParmas>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesDataStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesDataStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesDataStatistics_IN.OrgId);
                salesRankStatisticsParmas.QueryOrgId = salesDataStatistics_IN.OrgId;
            }
            salesRankStatisticsParmas.EnumRankOrgType = orgInfo.OrgType;
            salesRankStatisticsParmas.FindOrgId = userOrg;
            salesRankStatisticsParmas.FindUserId = UserId;
            //进行统计
            SalesRankStatistics_OUT salesRankStatistics_OUT = new SalesRankStatistics_OUT();
            var rankData = DbOpe_crm_trackingrecord.Instance.TrackingRecordRankStatistics(salesRankStatisticsParmas);
            return rankData;
        }
        /// <summary>
        /// 销售分析-客户（按合同计算：新增合同和续约合同都各自算一个新客户）地域分布
        /// </summary>
        /// <param name="salesAnalyseStatistics_IN"></param>
        /// <returns></returns>
        public SalesCitysStatistics_OUT SalesCitysStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {

            SalesAnalyseStatisticsParams salesAnalyseStatisticsParams = GetSalesAnalyseStatisticsParams(salesAnalyseStatistics_IN);
            var r = DbOpe_crm_contract.Instance.SignContractStatistics(salesAnalyseStatisticsParams);
            return r;
        }
        /// <summary>
        /// 销售分析-客户（签过合同且未作废的算签约客户）地域分布-国家详情
        /// </summary>
        /// <param name="salesCountryAnalyseStatistics_IN"></param>
        /// <returns></returns>
        public SalesCountryDetailAnalyseStatistics_OUT SalesCountryDetailAnalyseStatistics(SalesCountryAnalyseStatistics_IN salesCountryAnalyseStatistics_IN)
        {
            SalesAnalyseCountryStatisticsParams salesAnalyseStatisticsParams = GetSalesAnalyseCountryStatisticsParams(salesCountryAnalyseStatistics_IN);
            var r = DbOpe_crm_contract.Instance.SalesCountryDetailAnalyseStatistics(salesAnalyseStatisticsParams);
            return r;
        }
        /// <summary>
        /// 销售分析-客户（签过合同且未作废的算签约客户）地域分布-国家排行
        /// </summary>
        /// <param name="salesCountryAnalyseStatistics_IN"></param>
        /// <returns></returns>
        public SalesCountryRankAnalyseStatistics_OUT SalesCountryRankAnalyseStatistics(SalesCountryAnalyseStatistics_IN salesCountryAnalyseStatistics_IN)
        {
            SalesAnalyseCountryStatisticsParams salesAnalyseStatisticsParams = GetSalesAnalyseCountryStatisticsParams(salesCountryAnalyseStatistics_IN);
            var r = DbOpe_crm_contract.Instance.SalesCountryRankAnalyseStatistics(salesAnalyseStatisticsParams);
            return r;
        }
        ///// <summary>
        ///// 销售分析-销售漏斗
        ///// </summary>
        ///// <param name="salesAnalyseStatistics_IN"></param>
        ///// <returns></returns>
        //public SalesFunnelStatistics_OUT SalesFunnelStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        //{
        //    SalesFunnelStatisticsParams salesAnalyseStatisticsParams = GetSalesFunnelStatisticsParams(salesAnalyseStatistics_IN);
        //    var r = DbOpe_crm_trackingrecord.Instance.SalesFunnelStatistics(salesAnalyseStatisticsParams);
        //    return r;
        //}
        /// <summary>
        /// 销售分析-到账客户（）来源
        /// </summary>
        /// <param name="salesAnalyseStatistics_IN"></param>
        /// <returns></returns>
        public SalesReciveCustomerSourceStatistics_OUT SalesReciveCustomerSourceStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {
            SalesAnalyseStatisticsParams salesAnalyseStatisticsParams = GetSalesAnalyseStatisticsParams(salesAnalyseStatistics_IN);
            var r = DbOpe_crm_contract.Instance.SalesReciveCustomerSourceStatistics(salesAnalyseStatisticsParams);
            return r;
        }
        /// <summary>
        /// 销售分析-到账客户（）合同转化率
        /// </summary>
        /// <param name="salesAnalyseStatistics_IN"></param>
        /// <returns></returns>
        public SalesReciveContractTypeTransStatistics_OUT SalesReciveContractTypeTransStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {
            SalesAnalyseStatisticsParams salesAnalyseStatisticsParams = GetSalesAnalyseStatisticsParams(salesAnalyseStatistics_IN);
            var r = DbOpe_crm_contract.Instance.SalesReciveContractTypeTransStatistics(salesAnalyseStatisticsParams);
            return r;
        }
        /// <summary>
        /// 销售分析-到账客户（）来源转化率
        /// </summary>
        /// <param name="salesAnalyseStatistics_IN"></param>
        /// <returns></returns>
        public SalesReciveCustomerSourceTransStatistics_OUT SalesReciveCustomerSourceTransStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {
            SalesAnalyseStatisticsParams salesAnalyseStatisticsParams = GetSalesAnalyseStatisticsParams(salesAnalyseStatistics_IN);
            var r = DbOpe_crm_contract.Instance.SalesReciveCustomerSourceTransStatistics(salesAnalyseStatisticsParams);
            return r;
        }
        private SalesAnalyseStatisticsParams GetSalesAnalyseStatisticsParams(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {
            SalesAnalyseStatisticsParams salesAnalyseStatisticsParams = salesAnalyseStatistics_IN.MappingTo<SalesAnalyseStatisticsParams>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesAnalyseStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesAnalyseStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesAnalyseStatistics_IN.OrgId);
            }
            switch (orgInfo.OrgType)
            {
                case EnumOrgType.Squadron:
                    salesAnalyseStatisticsParams.OrgRegimentId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.Battalion:
                    salesAnalyseStatisticsParams.OrgBrigadeId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.BattleTeam:
                    salesAnalyseStatisticsParams.OrgDivisionId = new List<string>() { orgInfo.Id };
                    break;
            }
            return salesAnalyseStatisticsParams;
        }
        private SalesAnalyseCountryStatisticsParams GetSalesAnalyseCountryStatisticsParams(SalesCountryAnalyseStatistics_IN salesCountryAnalyseStatistics_IN)
        {
            SalesAnalyseCountryStatisticsParams salesAnalyseCountryStatisticsParams = salesCountryAnalyseStatistics_IN.MappingTo<SalesAnalyseCountryStatisticsParams>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            //判断参数里面的组织有没有权限
            if (!StringUtil.IsNullOrEmpty(salesCountryAnalyseStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesCountryAnalyseStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == salesCountryAnalyseStatistics_IN.OrgId);
            }
            switch (orgInfo.OrgType)
            {
                case EnumOrgType.Squadron:
                    salesAnalyseCountryStatisticsParams.OrgRegimentId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.Battalion:
                    salesAnalyseCountryStatisticsParams.OrgBrigadeId = new List<string>() { orgInfo.Id };
                    break;
                case EnumOrgType.BattleTeam:
                    salesAnalyseCountryStatisticsParams.OrgDivisionId = new List<string>() { orgInfo.Id };
                    break;
            }
            return salesAnalyseCountryStatisticsParams;
        }
        private SalesFunnelStatisticsParams GetSalesFunnelStatisticsParams(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {
            SalesFunnelStatisticsParams salesFunnelStatisticsParams = salesAnalyseStatistics_IN.MappingTo<SalesFunnelStatisticsParams>();
            //参数需要处理。组织没填默认按当前用户的组织统计
            //填了的话按组织统计，这时要筛选一下，只能统计当前用户所属组织及下属组织
            //这里需要注意，由于权限判断是按照当前所在组织进行的，所以过去在其他组织产生的一些业绩，可能会无法再去通过组织查到（现在没权限了）
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
            var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
            //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
            var userOrgList = orgObjList.Select(o => o.Id).ToList();
            //userOrgList.Add(userOrg);
            if (!StringUtil.IsNullOrEmpty(salesAnalyseStatistics_IN.OrgId))
            {
                if (!userOrgList.Contains(salesAnalyseStatistics_IN.OrgId))
                {
                    throw new ApiException("没有组织权限");
                }
                salesFunnelStatisticsParams.OrgIds = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(salesAnalyseStatistics_IN.OrgId).Select(o => o.Id).ToList();
                //salesFunnelStatisticsParams.OrgIds.Add(salesAnalyseStatistics_IN.OrgId);
            }
            else
            {
                salesFunnelStatisticsParams.OrgIds = userOrgList;
            }
            return salesFunnelStatisticsParams;
        }
        #endregion

        #region 首页
        /// <summary>
        /// 用户主页相关统计值
        /// </summary>
        /// <returns></returns>
        public UserIndexStatistics_OUT GetUserIndexStatistics()
        {
            UserIndexStatistics_OUT userIndexStatistics_OUT = new UserIndexStatistics_OUT();

            return userIndexStatistics_OUT;
        }
        #endregion
    }
}
