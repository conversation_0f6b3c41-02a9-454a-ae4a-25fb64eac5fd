﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_g4_dbnames")]
    public class Db_sys_g4_dbnames
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:国家ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? SID { get; set; }

        /// <summary>
        /// Desc:中文名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CountryName { get; set; }

        /// <summary>
        /// Desc:英文名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CountryEName { get; set; }

        /// <summary>
        /// Desc:洲的缩写
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string BFState { get; set; }

        /// <summary>
        /// Desc:洲的名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Continent { get; set; }

        /// <summary>
        /// Desc:国家类型 0：进口  1：出口
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? CType { get; set; }

        /// <summary>
        /// Desc:国家英文名称，不分进出口类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ImgName { get; set; }

        /// <summary>
        /// Desc:其他类型类型 0：正常 1：快板  2：统计
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? OtherType { get; set; }

        /// <summary>
        /// Desc:归属国家ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? BelongToSid { get; set; }

        /// <summary>
        /// Desc:Gtis系统删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? IsGtisDeleted { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
