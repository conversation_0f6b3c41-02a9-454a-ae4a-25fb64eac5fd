﻿using System.IO;
using System.Web;
using Aspose.Words;
using Aspose.Words.Saving;
using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.AppSetting;
using QCloud;
using Microsoft.Net.Http.Headers;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.DAL.DbCommon;
using CRM2_API.Common.JWT;

namespace CRM2_API.BLL
{
    public class BLL_Attachfile : BaseBLL<BLL_Attachfile>
    {
        /// <summary>
        /// 检查文件权限(根据合同id检查文件权限)
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        public bool CheckFileAuth(string contractId)
        {
            var userId = TokenModel.Instance.id;
            //检查是否是后台人员
            bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(userId);
            if (isBackendManager)
            {
                return true;
            }
            else {
                if (string.IsNullOrEmpty(contractId))
                {
                    return false;
                }
                // 检查合同是否存在
                var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(contractId);
                if (contract == null)
                {
                    return false;
                }
                var firstParty = contract.FirstParty;
                var isAuth = CheckFileAuthByFirstParty(firstParty);
                return isAuth;
            }
        }
        /// <summary>
        /// 检查文件权限(根据甲方公司检查文件权限)
        /// </summary>
        /// <param name="firstParty"></param>
        /// <returns></returns>
        public bool CheckFileAuthByFirstParty(string firstParty)
        {
            var userId = TokenModel.Instance.id;
            //检查是否是后台人员
            bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(userId);
            if (isBackendManager)
            {
                return true;
            }
            else {
                if (string.IsNullOrEmpty(firstParty))
                {
                    return false;
                }
                // 检查甲方公司是否存在
                var firstPartyCompany = DbOpe_crm_customer_subcompany.Instance.QueryByPrimaryKey(firstParty);
                if (firstPartyCompany == null)
                {
                    return false;
                }
                //验证客户权限
                var customerAuth = DbContext.Crm2Db.Queryable<Db_v_customer_subcompany_private_user>()
                    .Where(m => m.CompanyCurrentUser == userId && m.Id == firstParty)
                    .Any();
                if (!customerAuth)
                {
                    return false;
                }

                return true;
            }


        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileType"></param>
        /// <param name="response"></param>
        /// <returns></returns>
        public IActionResult Preview(string id, string fileType, HttpResponse response)
        {
            string fileName = string.Empty;
            string filePath = string.Empty;
            string fileExtension = string.Empty;
            string contractId = string.Empty;
            switch (fileType)
            {
                case "MergeRelation":
                    var mergerelationAttEntity = DbOpe_crm_mergecompany_attachfile.Instance.QueryByPrimaryKey(id);
                    if (mergerelationAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = mergerelationAttEntity.FileName;
                    filePath = mergerelationAttEntity.FilePath;
                    fileExtension = mergerelationAttEntity.FileExtension;
                    break;
                case "SubRelation":
                    var subrelationAttEntity = DbOpe_crm_addsubcompany_attachfile.Instance.QueryByPrimaryKey(id);
                    if (subrelationAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = subrelationAttEntity.FileName;
                    filePath = subrelationAttEntity.FilePath;
                    fileExtension = subrelationAttEntity.FileExtension;
                    break;
                case "ArticleText" or "Article":
                    var articleAttEntity = DbOpe_crm_article_attachfile.Instance.QueryByPrimaryKey(id);
                    if (articleAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = articleAttEntity.FileName;
                    filePath = articleAttEntity.FilePath;
                    fileExtension = articleAttEntity.FileExtension;
                    break;
                case "ArticleComment":
                    var articleCommentAttEntity = DbOpe_crm_article_comment_attachfile.Instance.QueryByPrimaryKey(id);
                    if (articleCommentAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = articleCommentAttEntity.FileName;
                    filePath = articleCommentAttEntity.FilePath;
                    fileExtension = articleCommentAttEntity.FileExtension;
                    break;
                case "Base" or "TrackingRecord":
                    var commonAttEntity = DbOpe_crm_attachfile.Instance.QueryByPrimaryKey(id);
                    if (commonAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = commonAttEntity.FileName;
                    filePath = commonAttEntity.FilePath;
                    fileExtension = commonAttEntity.FileExtension;
                    break;
                case "Schedule" or "ScheduleAfter":
                    var scheduleAttEntity = DbOpe_crm_schedule_attachfile.Instance.QueryByPrimaryKey(id);
                    if (scheduleAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = scheduleAttEntity.FileName;
                    filePath = scheduleAttEntity.FilePath;
                    fileExtension = scheduleAttEntity.FileExtension;
                    break;
                case "Contract":
                    //var contractAttEntity = DbOpe_crm_contract_attachfile.Instance.QueryByPrimaryKey(id);
                    var contractAttEntity = DbOpe_crm_contract_attachfile.Instance.GetContractAttachfileById(id, true);
                    if (contractAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    if(!CheckFileAuth(contractAttEntity.ContractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = contractAttEntity.FileName;
                    filePath = contractAttEntity.FilePath;
                    fileExtension = contractAttEntity.FileExtension;
                    break;
                case "ContractChangeAppl":
                    //var contractChangeApplAttEntity = DbOpe_crm_contract_change_appl_attachfile.Instance.QueryByPrimaryKey(id);
                    var contractChangeApplAttEntity = DbOpe_crm_contract_change_appl_attachfile.Instance.GetContractChangeApplAttachfileById(id);
                    if (contractChangeApplAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    contractId = DbOpe_crm_contract_change_appl.Instance.GetDataById(contractChangeApplAttEntity.ContractChangeApplId)?.ContractId ?? "";
                    if(!CheckFileAuth(contractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = contractChangeApplAttEntity.FileName;
                    filePath = contractChangeApplAttEntity.FilePath;
                    fileExtension = contractChangeApplAttEntity.FileExtension;
                    break;
                case "DBOriginalContract":
                    //var dBOriginalContractAttEntity = DbOpe_crm_contract_dboriginalcontract_attachfile.Instance.QueryByPrimaryKey(id);
                    var dBOriginalContractAttEntity = DbOpe_crm_contract_dboriginalcontract_attachfile.Instance.GetContractDboriginalContractAttachfileById(id, true);
                    if (dBOriginalContractAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    if(!CheckFileAuth(dBOriginalContractAttEntity.ContractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = dBOriginalContractAttEntity.FileName;
                    filePath = dBOriginalContractAttEntity.FilePath;
                    fileExtension = dBOriginalContractAttEntity.FileExtension;
                    break;
                case "ElectronicContract":
                    //var electronicContractAttEntity = DbOpe_crm_contract_electroniccontract_attachfile.Instance.QueryByPrimaryKey(id);
                    var electronicContractAttEntity = DbOpe_crm_contract_electroniccontract_attachfile.Instance.GetContractElectronicContractAttachfileById(id, true);
                    if (electronicContractAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    if(!CheckFileAuth(electronicContractAttEntity.ContractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = electronicContractAttEntity.FileName;
                    filePath = electronicContractAttEntity.FilePath;
                    fileExtension = electronicContractAttEntity.FileExtension;
                    break;
                case "ContractInvoice":
                    //var contractInvoiceAttEntity = DbOpe_crm_contract_invoice_attachment.Instance.QueryByPrimaryKey(id);
                    var contractInvoiceAttEntity = DbOpe_crm_contract_invoice_attachment.Instance.GetContractInvoiceAttachmentById(id);
                    if (contractInvoiceAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    contractId = DbOpe_crm_invoice_application.Instance.GetDataById(contractInvoiceAttEntity.ContractInvoiceId)?.ContractId ?? "";
                    if(!CheckFileAuth(contractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = contractInvoiceAttEntity.FileName;
                    filePath = contractInvoiceAttEntity.FilePath;
                    fileExtension = contractInvoiceAttEntity.FileExtension;
                    break;
                case "ContractInvoiceAppl":
                    //var contractInvoiceApplAttEntity = DbOpe_crm_contract_invoiceappl_attachment.Instance.QueryByPrimaryKey(id);
                    var contractInvoiceApplAttEntity = DbOpe_crm_contract_invoiceappl_attachment.Instance.GetContractInvoiceapplAttachmentById(id);
                    if (contractInvoiceApplAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    contractId = DbOpe_crm_invoice_application.Instance.GetDataById(contractInvoiceApplAttEntity.ContractInvoiceApplId)?.ContractId ?? "";
                    if(!CheckFileAuth(contractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = contractInvoiceApplAttEntity.FileName;
                    filePath = contractInvoiceApplAttEntity.FilePath;
                    fileExtension = contractInvoiceApplAttEntity.FileExtension;
                    break;
                case "ContractPaymentInfo":
                    //var contractPaymentInfoAttEntity = DbOpe_crm_contract_paymentinfo_attachfile.Instance.QueryByPrimaryKey(id);
                    var contractPaymentInfoAttEntity = DbOpe_crm_contract_paymentinfo_attachfile.Instance.GetContractPaymentinfoAttachfileById(id);
                    if (contractPaymentInfoAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    contractId = DbOpe_crm_contract_paymentinfo.Instance.GetDataById(contractPaymentInfoAttEntity.ContractPaymentInfoId)?.ContractId ?? "";
                    if(!CheckFileAuth(contractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = contractPaymentInfoAttEntity.FileName;
                    filePath = contractPaymentInfoAttEntity.FilePath;
                    fileExtension = contractPaymentInfoAttEntity.FileExtension;
                    break;
                case "RefundContractInvoice":
                    //var refundContractInvoiceAttEntity = DbOpe_crm_contract_refundinvoice_attachment.Instance.QueryByPrimaryKey(id);
                    var refundContractInvoiceAttEntity = DbOpe_crm_contract_refundinvoice_attachment.Instance.GetContractRefundinvoiceAttachmentById(id);
                    if (refundContractInvoiceAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    contractId = DbOpe_crm_contract_refundinvoice.Instance.GetDataById(refundContractInvoiceAttEntity.ContractRefundInvoiceId)?.ContractId ?? "";
                    if(!CheckFileAuth(contractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = refundContractInvoiceAttEntity.FileName;
                    filePath = refundContractInvoiceAttEntity.FilePath;
                    fileExtension = refundContractInvoiceAttEntity.FileExtension;
                    break;
                case "SealedContract":
                    //var sealedContractAttEntity = DbOpe_crm_contract_sealedcontract_attachfile.Instance.QueryByPrimaryKey(id);
                    var sealedContractAttEntity = DbOpe_crm_contract_sealedcontract_attachfile.Instance.GetContractSealedContractAttachfileById(id, true);
                    if (sealedContractAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    if(!CheckFileAuth(sealedContractAttEntity.ContractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = sealedContractAttEntity.FileName;
                    filePath = sealedContractAttEntity.FilePath;
                    fileExtension = sealedContractAttEntity.FileExtension;
                    break;
                case "OtherStampReview":
                    //var sealedContractAttEntity = DbOpe_crm_contract_sealedcontract_attachfile.Instance.QueryByPrimaryKey(id);
                    var otherStampReviewAttEntity = DbOpe_crm_contract_othercontract_attachfile.Instance.GetContractOtherContractAttachfileById(id, true);
                    if (otherStampReviewAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    if(!CheckFileAuth(otherStampReviewAttEntity.ContractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = otherStampReviewAttEntity.FileName;
                    filePath = otherStampReviewAttEntity.FilePath;
                    fileExtension = otherStampReviewAttEntity.FileExtension;
                    break;
                case "DBEmailTemplate": //两个表都是这个Type
                    var dBEmailTemplate = DbOpe_crm_contract_serviceinfo_db_attachfile.Instance.QueryByPrimaryKey(id);
                    var dbAttEntity = DbOpe_crm_db_emailtemplate_attachfile.Instance.QueryByPrimaryKey(id);
                    if (dBEmailTemplate == null && dbAttEntity == null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    if (dBEmailTemplate != null)
                    {
                        fileName = dBEmailTemplate.FileName;
                        filePath = dBEmailTemplate.FilePath;
                        fileExtension = dBEmailTemplate.FileExtension;
                    }
                    else
                    {
                        fileName = dbAttEntity.FileName;
                        filePath = dbAttEntity.FilePath;
                        fileExtension = dbAttEntity.FileExtension;
                    }
                    break;
                case "MailingFiles":
                    var mailingFilesAttEntity =
                        DbOpe_crm_contract_serviceinfo_mailing_attachfile.Instance.QueryByPrimaryKey(id);
                    if (mailingFilesAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = mailingFilesAttEntity.FileName;
                    filePath = mailingFilesAttEntity.FilePath;
                    fileExtension = mailingFilesAttEntity.FileExtension;
                    break;
                case "ContractSpecial":
                    //var contractSpecialAttEntity = DbOpe_crm_contract_special_attachfile.Instance.QueryByPrimaryKey(id);
                    var contractSpecialAttEntity = DbOpe_crm_contract_special_attachfile.Instance.GetContractSpecialAttachfileById(id, true);
                    if (contractSpecialAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    if(!CheckFileAuth(contractSpecialAttEntity.ContractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = contractSpecialAttEntity.FileName;
                    filePath = contractSpecialAttEntity.FilePath;
                    fileExtension = contractSpecialAttEntity.FileExtension;
                    break;
                case "ContractIpKeepRecord":
                    var contractIpKeepRecordAttEntity =
                        DbOpe_crm_ipkeeprecord_attachfile.Instance.QueryByPrimaryKey(id);
                    if (contractIpKeepRecordAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = contractIpKeepRecordAttEntity.FileName;
                    filePath = contractIpKeepRecordAttEntity.FilePath;
                    fileExtension = contractIpKeepRecordAttEntity.FileExtension;
                    break;
                case "Notice" or "NoticeText":
                    var noticeAttEntity = DbOpe_crm_notice_attachfile.Instance.QueryByPrimaryKey(id);
                    if (noticeAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = noticeAttEntity.FileName;
                    filePath = noticeAttEntity.FilePath;
                    fileExtension = noticeAttEntity.FileExtension;
                    break;
                case "Daily" or "Week" or "Month" or "Manager" or "Brief" or "WorkReports":
                    var reportAttEntity = DbOpe_crm_work_report_attachfile.Instance.QueryByPrimaryKey(id);
                    if (reportAttEntity is null)
                    {
                        var reportImgEntity = DbOpe_crm_work_report_img.Instance.QueryByPrimaryKey(id);
                        if (reportImgEntity is null)
                        {
                            return new JsonResult(new
                            {
                                Msg = "文件不存在!"
                            });
                        }
                        fileName = reportImgEntity.ImgName;
                        filePath = reportImgEntity.ImgPath;
                        // fileExtension = reportImgEntity.FileExtension;
                        break;
                    }
                    fileName = reportAttEntity.FileName;
                    filePath = reportAttEntity.FilePath;
                    fileExtension = reportAttEntity.FileExtension;
                    break;
                // 用户头像 id为用户表Id
                case "AvatarImage":
                    var userInfo = DbOpe_sys_user.Instance.GetDataById(id);
                    if (userInfo.AvatarImage.IsNullOrEmpty())
                    {
                        return new JsonResult(new
                        {
                            Msg = "当前用户还没有上传头像!"
                        });
                    }
                    fileName = "AvatarImage";
                    filePath = userInfo.AvatarImage;
                    break;
                case "TransactionReceipt":
                    //var collectioninfoAttachFile = DbOpe_crm_collectioninfo_attachfile.Instance.QueryByPrimaryKey(id);
                    var collectioninfoAttachFile = DbOpe_crm_collectioninfo_attachfile.Instance.GetCollectioninfoAttachfileById(id);
                    if (collectioninfoAttachFile is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    contractId = DbContext.Crm2Db.Queryable<Db_crm_contract_receipt_details>()
                        .Where(r => r.Id == collectioninfoAttachFile.CollectionInfoId && r.Deleted == false)
                        .Select(r => r.ContractId)
                        .First();
                    if(!CheckFileAuth(contractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = collectioninfoAttachFile.FileName;
                    filePath = collectioninfoAttachFile.FilePath;
                    fileExtension = collectioninfoAttachFile.FileExtension;
                    break;
                case "CompanyFiles":
                    //var companyFiles = DbOpe_crm_attachfile.Instance.GetOldDateloadCompanyFilesById(id);
                    var companyFiles = DbOpe_crm_attachfile.Instance.GetOldDateloadCompanyFilesById(id);
                    if (companyFiles is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    if(!CheckFileAuth(companyFiles.ContractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = companyFiles.FileName;
                    filePath = companyFiles.FilePath;
                    fileExtension = companyFiles.FileExtension;
                    break;
                case "TradeLeadsFiles":
                    //var companyFiles = DbOpe_crm_attachfile.Instance.GetOldDateloadCompanyFilesById(id);
                    var tradeLeadsFiles = DbOpe_crm_tradeleads_attachfile.Instance.QueryByPrimaryKey(id);
                    if (tradeLeadsFiles is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = tradeLeadsFiles.FileName;
                    filePath = tradeLeadsFiles.FilePath;
                    fileExtension = tradeLeadsFiles.FileExtension;
                    break;
                case "InvoiceRefund":
                    //var contractInvoiceAttEntity = DbOpe_crm_contract_invoice_attachment.Instance.QueryByPrimaryKey(id);
                    var invoiceRefundAttEntity = DbOpe_crm_invoice_refund_attachment.Instance.GetInvoiceRefundAttachmentById(id);
                    if (invoiceRefundAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    contractId = DbOpe_crm_invoice_refund_application.Instance.GetDataById(invoiceRefundAttEntity.RefundApplicationId)?.ContractId ?? "";
                    if(!CheckFileAuth(contractId))
                    {
                        return new JsonResult(new
                        {
                            Msg = "无访问权限!"
                        });
                    }
                    fileName = invoiceRefundAttEntity.FileName;
                    filePath = invoiceRefundAttEntity.FilePath;
                    fileExtension = invoiceRefundAttEntity.FileExtension;
                    break;
                case "Report":
                    var newReportAttEntity = DbOpe_crm_report_attachment.Instance.QueryByPrimaryKey(id);
                    if (newReportAttEntity is null)
                    {
                        return new JsonResult(new
                        {
                            Msg = "文件不存在!"
                        });
                    }
                    fileName = newReportAttEntity.FileName;
                    filePath = newReportAttEntity.FilePath;
                    fileExtension = newReportAttEntity.FileType;
                    break;
                default:
                    break;
            }

            string ContentType = "application/octet-stream";
            ContentType = ContentTypeHelper.GetContentType("." + fileExtension);


            if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
            {
                QCloudOperator qCloudOperator = new QCloudOperator();
                if (!qCloudOperator.Exists(filePath))
                {
                    return new JsonResult(new
                    {
                        Msg = "文件不存在!"
                    });

                }
                var data = qCloudOperator.OpenRead(filePath);
                using (MemoryStream memoryStream = new MemoryStream(data))
                {
                    string encodeFilename = HttpUtility.UrlEncode(fileName, Encoding.GetEncoding("UTF-8"));
                    MemoryStream resultStream = new MemoryStream(data);
                    response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);

                    //var contentDisposition = new ContentDispositionHeaderValue("inline");
                    //contentDisposition.FileName = encodeFilename;
                    //contentDisposition.SetHttpFileName(contentDisposition.FileName);
                    //response.Headers[HeaderNames.ContentDisposition] = contentDisposition.ToString();

                    //return new FileStreamResult(resultStream, "application/pdf");
                    return new FileStreamResult(resultStream, ContentType);
                }
            }
            else
            {
                if (!File.Exists(filePath))
                {
                    return new JsonResult(new
                    {
                        Msg = "文件不存在!"
                    });
                }
                var stream = File.OpenRead(filePath);
                string encodeFilename = HttpUtility.UrlEncode(fileName, Encoding.GetEncoding("UTF-8"));
                response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);

                //var contentDisposition = new ContentDispositionHeaderValue("inline");
                //contentDisposition.SetHttpFileName(contentDisposition.FileName);
                //response.Headers[HeaderNames.ContentDisposition] = contentDisposition.ToString();


                //return new FileStreamResult(stream, "application/pdf");
                return new FileStreamResult(stream, ContentType);
            }


            /*Dictionary<string, Type> mapper = new();
            mapper.Add("NoticeText",typeof(DbOpe_crm_notice_attachfile));
            if (!mapper.ContainsKey(fileType))
            {
                return null;
            }
            Type type = mapper[fileType];
            var propertyInfo = type.BaseType.BaseType.GetProperty("Instance");
            Object typeObj = propertyInfo.GetValue(propertyInfo, null);
            var method = type.GetMethod("QueryByPrimaryKey");
            Object obj = method.Invoke(typeObj,new[] { id });
            if (obj is not null)
            {
                var result = method.ReturnType;
                var filePathInfo = result.GetProperty("FilePath");
                string filePath = filePathInfo.GetValue(obj, null) as string;
                if (!File.Exists(filePath))
                {
                    return new JsonResult(new
                    {
                        Msg = "文件不存在!"
                    });
                }
                var stream = File.OpenRead(filePath);
                var fileNameInfo = result.GetProperty("FileName");
                string fileName = fileNameInfo.GetValue(obj, null) as string;
                string encodeFilename = HttpUtility.UrlEncode(fileName, Encoding.GetEncoding("UTF-8"));
                Response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                return new FileStreamResult(stream, "application/octet-stream");  
            }
            return new JsonResult(new
            {
                Msg = "文件不存在!"
            });*/
        }

        /// <summary>
        /// 将上传的Word文件转换为Html返回
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public IActionResult WordToHtml(IFormFile file)
        {
            string fileName = file.FileName;
            string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);

            Document document = null;
            if (fileExtension.Equals("doc"))
            {
                document = new Document(file.OpenReadStream(), new LoadOptions() { LoadFormat = LoadFormat.Doc });
            }
            else if (fileExtension.Equals("docx"))
            {
                document = new Document(file.OpenReadStream(), new LoadOptions() { LoadFormat = LoadFormat.Docx });
            }
            else
            {
                return new JsonResult(new
                {
                    Msg = "不被支持的文档类型!"
                });
            }

            HtmlSaveOptions options = new(SaveFormat.Html);
            options.ExportImagesAsBase64 = true;
            MemoryStream stream = new();
            document.Save(stream, options);
            stream.Position = 0;

            StreamReader reader = new(stream);
            string html = reader.ReadToEnd();

            // Document document = new Document();
            // if (fileExtension.Equals("doc"))
            // {
            //     document.LoadFromStream(file.OpenReadStream(),FileFormat.Doc);
            // }
            // else if (fileExtension.Equals("docx"))
            // {
            //     document.LoadFromStream(file.OpenReadStream(),FileFormat.Docx);
            // }
            // else
            // {
            //     return new JsonResult(new
            //     {
            //         Msg = "不被支持的文档类型!"
            //     });
            // }
            // document.HtmlExportOptions.ImageEmbedded = true;
            // document.HtmlExportOptions.IsExportDocumentStyles = true;
            // document.HtmlExportOptions.HasHeadersFooters = true;
            // MemoryStream stream = new();
            // document.SaveToStream(stream, FileFormat.Html);
            // stream.Position = 0;
            //
            // StreamReader reader = new(stream);
            // string html = reader.ReadToEnd();
            return new JsonResult(new
            {
                Data = html,
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileType"></param>
        /// <returns></returns>
        public void DeleteAttachFile(string id, string fileType)
        {
            switch (fileType)
            {
                //case "Contract":
                //    var contractAttEntity = DbOpe_crm_contract_attachfile.Instance.GetContractAttachfileById(id, true);
                //    if (contractAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_attachfile.Instance.DeleteData(id);
                //    break;
                //case "ContractChangeAppl":
                //    var contractChangeApplAttEntity = DbOpe_crm_contract_change_appl_attachfile.Instance.GetContractChangeApplAttachfileById(id, true);
                //    if (contractChangeApplAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_change_appl_attachfile.Instance.DeleteData(id);
                //    break;
                //case "DBOriginalContract":
                //    var dBOriginalContractAttEntity = DbOpe_crm_contract_dboriginalcontract_attachfile.Instance.GetContractDboriginalContractAttachfileById(id, true);
                //    if (dBOriginalContractAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_dboriginalcontract_attachfile.Instance.DeleteData(id);
                //    break;
                case "ElectronicContract":
                    var electronicContractAttEntity = DbOpe_crm_contract_electroniccontract_attachfile.Instance.GetContractElectronicContractAttachfileById(id, true);
                    if (electronicContractAttEntity is null)
                    {
                        throw new ApiException("没有数据权限或者数据不存在");
                    }
                    bool IsHaveContractCollectionInfoAutoMatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(electronicContractAttEntity.ContractId);
                    if (IsHaveContractCollectionInfoAutoMatching)
                    {
                        throw new ApiException("当前合同存在到账登记审核中,无法删除合同附件信息");
                    }
                    //盖章合同在盖章审核之前可以进行删除，然后重新上传  验证盖章审核
                    Db_crm_contract electronicontract = DbOpe_crm_contract.Instance.GetDataById(electronicContractAttEntity.ContractId);
                    //if (electronicontract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt())
                    //if (electronicontract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt() || electronicontract.StampReviewStatus == EnumStampReviewStatus.Refuse.ToInt())
                    if (electronicContractAttEntity.Status == EnumStampReviewStatus.Approved.ToInt() || electronicContractAttEntity.Status == EnumStampReviewStatus.Refuse.ToInt())
                    {
                        throw new ApiException("当前合同的盖章合同已审核，不可以删除");
                    }
                    DbOpe_crm_contract_electroniccontract_attachfile.Instance.DeleteData(id);
                    List<Db_crm_contract_electroniccontract_attachfile> electroniccontractattachfiles = DbOpe_crm_contract_electroniccontract_attachfile.Instance.GetDataList(r => r.ContractId == electronicContractAttEntity.ContractId);
                    if (electroniccontractattachfiles.Count == 0)
                    {
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { StampReviewStatus = 1 }, electronicContractAttEntity.ContractId);
                        DbOpe_crm_contract_stampreview.Instance.DeleteIsHistoryByContractId(electronicContractAttEntity.ContractId);
                    }
                    break;
                //case "ContractInvoice":
                //    var contractInvoiceAttEntity = DbOpe_crm_contract_invoice_attachment.Instance.GetContractInvoiceAttachmentById(id, true);
                //    if (contractInvoiceAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_invoice_attachment.Instance.DeleteData(id);
                //    break;
                //case "ContractInvoiceAppl":
                //    var contractInvoiceApplAttEntity = DbOpe_crm_contract_invoiceappl_attachment.Instance.GetContractInvoiceapplAttachmentById(id, true);
                //    if (contractInvoiceApplAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_invoiceappl_attachment.Instance.DeleteData(id);
                //    break;
                //case "ContractPaymentInfo":
                //    var contractPaymentInfoAttEntity = DbOpe_crm_contract_paymentinfo_attachfile.Instance.GetContractPaymentinfoAttachfileById(id, true);
                //    if (contractPaymentInfoAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_paymentinfo_attachfile.Instance.DeleteData(id);
                //    break;
                //case "RefundContractInvoice":
                //    var refundContractInvoiceAttEntity = DbOpe_crm_contract_refundinvoice_attachment.Instance.GetContractRefundinvoiceAttachmentById(id, true);
                //    if (refundContractInvoiceAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_refundinvoice_attachment.Instance.DeleteData(id);
                //    break;
                case "SealedContract":
                    var sealedContractAttEntity = DbOpe_crm_contract_sealedcontract_attachfile.Instance.GetContractSealedContractAttachfileById(id, true);
                    if (sealedContractAttEntity is null)
                    {
                        throw new ApiException("没有数据权限或者数据不存在");
                    }
                    bool IsHaveContractCollectionInfoAutoMatchingS = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(sealedContractAttEntity.ContractId);
                    if (IsHaveContractCollectionInfoAutoMatchingS)
                    {
                        throw new ApiException("当前合同存在到账登记审核中,无法删除合同附件信息");
                    }
                    //盖章合同在盖章审核之前可以进行删除，然后重新上传  验证盖章审核
                    Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(sealedContractAttEntity.ContractId);
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt())
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt() || contract.StampReviewStatus == EnumStampReviewStatus.Refuse.ToInt())
                    if (sealedContractAttEntity.Status == EnumStampReviewStatus.Approved.ToInt() || sealedContractAttEntity.Status == EnumStampReviewStatus.Refuse.ToInt())
                    {
                        throw new ApiException("当前合同的盖章合同已审核，不可以删除");
                    }
                    DbOpe_crm_contract_sealedcontract_attachfile.Instance.DeleteData(id);
                    List<Db_crm_contract_sealedcontract_attachfile> sealedcontractattachfiles = DbOpe_crm_contract_sealedcontract_attachfile.Instance.GetDataList(r => r.ContractId == sealedContractAttEntity.ContractId);
                    if (sealedcontractattachfiles.Count == 0)
                    {
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { StampReviewStatus = 1 }, sealedContractAttEntity.ContractId);
                        DbOpe_crm_contract_stampreview.Instance.DeleteIsHistoryByContractId(sealedContractAttEntity.ContractId);
                    }
                    break;
                //case "ContractSpecial":
                //    var contractSpecialAttEntity = DbOpe_crm_contract_special_attachfile.Instance.GetContractSpecialAttachfileById(id, true);
                //    if (contractSpecialAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_special_attachfile.Instance.DeleteData(id);
                //    break;
                //case "TransactionReceipt":
                //    var collectioninfoAttachFile = DbOpe_crm_collectioninfo_attachfile.Instance.GetCollectioninfoAttachfileById(id, true);
                //    if (collectioninfoAttachFile is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_collectioninfo_attachfile.Instance.DeleteData(id);
                //    break;
                case "OtherStampReview":
                    var otherStampReviewAttEntity = DbOpe_crm_contract_othercontract_attachfile.Instance.GetContractOtherContractAttachfileById(id, true);
                    if (otherStampReviewAttEntity is null)
                    {
                        throw new ApiException("没有数据权限或者数据不存在");
                    }
                    //bool IsHaveContractCollectionInfoAutoMatchingS = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(otherStampReviewAttEntity.ContractId);
                    //if (IsHaveContractCollectionInfoAutoMatchingS)
                    //{
                    //    throw new ApiException("当前合同存在到账登记审核中,无法删除合同附件信息");
                    //}
                    //盖章合同在盖章审核之前可以进行删除，然后重新上传  验证盖章审核
                    //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(otherStampReviewAttEntity.ContractId);
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt())
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt() || contract.StampReviewStatus == EnumStampReviewStatus.Refuse.ToInt())
                    if (otherStampReviewAttEntity.Status == EnumStampReviewStatus.Approved.ToInt() || otherStampReviewAttEntity.Status == EnumStampReviewStatus.Refuse.ToInt())
                    {
                        throw new ApiException("当前附件已审核，不可以删除");
                    }
                    DbOpe_crm_contract_othercontract_attachfile.Instance.DeleteData(id);
                    DbOpe_crm_contract_othercontract_attachfile_review.Instance.DeleteData(r => r.ContractOtherContractAttachFileId == id);
                    List<Db_crm_contract_othercontract_attachfile> othercontractattachfiles = DbOpe_crm_contract_othercontract_attachfile.Instance.GetDataList(r => r.ContractId == otherStampReviewAttEntity.ContractId);
                    if (othercontractattachfiles.Count == 0)
                    {
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { OtherStampReviewStatus = 1 }, otherStampReviewAttEntity.ContractId);
                        DbOpe_crm_contract_otherstampreview.Instance.DeleteIsHistoryByContractId(otherStampReviewAttEntity.ContractId);
                    }
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileType"></param>
        /// <returns></returns>
        public void DeleteAttachFileBySelf(string id, string fileType)
        {
            switch (fileType)
            {
                //case "Contract":
                //    var contractAttEntity = DbOpe_crm_contract_attachfile.Instance.GetContractAttachfileById(id, true);
                //    if (contractAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_attachfile.Instance.DeleteData(id);
                //    break;
                //case "ContractChangeAppl":
                //    var contractChangeApplAttEntity = DbOpe_crm_contract_change_appl_attachfile.Instance.GetContractChangeApplAttachfileById(id, true);
                //    if (contractChangeApplAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_change_appl_attachfile.Instance.DeleteData(id);
                //    break;
                //case "DBOriginalContract":
                //    var dBOriginalContractAttEntity = DbOpe_crm_contract_dboriginalcontract_attachfile.Instance.GetContractDboriginalContractAttachfileById(id, true);
                //    if (dBOriginalContractAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_dboriginalcontract_attachfile.Instance.DeleteData(id);
                //    break;
                case "ElectronicContract":
                    var electronicContractAttEntity = DbOpe_crm_contract_electroniccontract_attachfile.Instance.GetContractElectronicContractAttachfileSelfById(id);
                    if (electronicContractAttEntity is null)
                    {
                        throw new ApiException("无法删除其他用户上传的文件");
                    }
                    bool IsHaveContractCollectionInfoAutoMatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(electronicContractAttEntity.ContractId);
                    if (IsHaveContractCollectionInfoAutoMatching)
                    {
                        throw new ApiException("当前合同存在到账登记审核中,无法删除合同附件信息");
                    }
                    //盖章合同在盖章审核之前可以进行删除，然后重新上传  验证盖章审核
                    Db_crm_contract electronicontract = DbOpe_crm_contract.Instance.GetDataById(electronicContractAttEntity.ContractId);
                    //if (electronicontract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt())
                    //if (electronicontract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt() || electronicontract.StampReviewStatus == EnumStampReviewStatus.Refuse.ToInt())
                    if (electronicContractAttEntity.Status == EnumStampReviewStatus.Approved.ToInt() || electronicContractAttEntity.Status == EnumStampReviewStatus.Refuse.ToInt())
                    {
                        throw new ApiException("当前合同的盖章合同已审核，不可以删除");
                    }
                    DbOpe_crm_contract_electroniccontract_attachfile.Instance.DeleteData(id);
                    List<Db_crm_contract_electroniccontract_attachfile> electroniccontractattachfiles = DbOpe_crm_contract_electroniccontract_attachfile.Instance.GetDataList(r => r.ContractId == electronicContractAttEntity.ContractId);
                    if (electroniccontractattachfiles.Count == 0)
                    {
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { StampReviewStatus = 1 }, electronicContractAttEntity.ContractId);
                        DbOpe_crm_contract_stampreview.Instance.DeleteIsHistoryByContractId(electronicContractAttEntity.ContractId);
                    }
                    break;
                //case "ContractInvoice":
                //    var contractInvoiceAttEntity = DbOpe_crm_contract_invoice_attachment.Instance.GetContractInvoiceAttachmentById(id, true);
                //    if (contractInvoiceAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_invoice_attachment.Instance.DeleteData(id);
                //    break;
                //case "ContractInvoiceAppl":
                //    var contractInvoiceApplAttEntity = DbOpe_crm_contract_invoiceappl_attachment.Instance.GetContractInvoiceapplAttachmentById(id, true);
                //    if (contractInvoiceApplAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_invoiceappl_attachment.Instance.DeleteData(id);
                //    break;
                //case "ContractPaymentInfo":
                //    var contractPaymentInfoAttEntity = DbOpe_crm_contract_paymentinfo_attachfile.Instance.GetContractPaymentinfoAttachfileById(id, true);
                //    if (contractPaymentInfoAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_paymentinfo_attachfile.Instance.DeleteData(id);
                //    break;
                //case "RefundContractInvoice":
                //    var refundContractInvoiceAttEntity = DbOpe_crm_contract_refundinvoice_attachment.Instance.GetContractRefundinvoiceAttachmentById(id, true);
                //    if (refundContractInvoiceAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_refundinvoice_attachment.Instance.DeleteData(id);
                //    break;
                case "SealedContract":
                    var sealedContractAttEntity = DbOpe_crm_contract_sealedcontract_attachfile.Instance.GetContractSealedContractAttachfileSelfById(id);
                    if (sealedContractAttEntity is null)
                    {
                        throw new ApiException("无法删除其他用户上传的文件");
                    }
                    bool IsHaveContractCollectionInfoAutoMatchingS = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(sealedContractAttEntity.ContractId);
                    if (IsHaveContractCollectionInfoAutoMatchingS)
                    {
                        throw new ApiException("当前合同存在到账登记审核中,无法删除合同附件信息");
                    }
                    //盖章合同在盖章审核之前可以进行删除，然后重新上传  验证盖章审核
                    Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(sealedContractAttEntity.ContractId);
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt())
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt() || contract.StampReviewStatus == EnumStampReviewStatus.Refuse.ToInt())
                    if (sealedContractAttEntity.Status == EnumStampReviewStatus.Approved.ToInt() || sealedContractAttEntity.Status == EnumStampReviewStatus.Refuse.ToInt())
                    {
                        throw new ApiException("当前合同的盖章合同已审核，不可以删除");
                    }
                    DbOpe_crm_contract_sealedcontract_attachfile.Instance.DeleteData(id);
                    List<Db_crm_contract_sealedcontract_attachfile> sealedcontractattachfiles = DbOpe_crm_contract_sealedcontract_attachfile.Instance.GetDataList(r => r.ContractId == sealedContractAttEntity.ContractId);
                    if (sealedcontractattachfiles.Count == 0)
                    {
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { StampReviewStatus = 1 }, sealedContractAttEntity.ContractId);
                        DbOpe_crm_contract_stampreview.Instance.DeleteIsHistoryByContractId(sealedContractAttEntity.ContractId);
                    }
                    break;
                //case "ContractSpecial":
                //    var contractSpecialAttEntity = DbOpe_crm_contract_special_attachfile.Instance.GetContractSpecialAttachfileById(id, true);
                //    if (contractSpecialAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_special_attachfile.Instance.DeleteData(id);
                //    break;
                //case "TransactionReceipt":
                //    var collectioninfoAttachFile = DbOpe_crm_collectioninfo_attachfile.Instance.GetCollectioninfoAttachfileById(id, true);
                //    if (collectioninfoAttachFile is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_collectioninfo_attachfile.Instance.DeleteData(id);
                //    break;
                case "OtherStampReview":
                    var otherStampReviewAttEntity = DbOpe_crm_contract_othercontract_attachfile.Instance.GetContractOtherContractAttachfileById(id, true);
                    if (otherStampReviewAttEntity is null)
                    {
                        throw new ApiException("没有数据权限或者数据不存在");
                    }
                    //bool IsHaveContractCollectionInfoAutoMatchingS = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(otherStampReviewAttEntity.ContractId);
                    //if (IsHaveContractCollectionInfoAutoMatchingS)
                    //{
                    //    throw new ApiException("当前合同存在到账登记审核中,无法删除合同附件信息");
                    //}
                    //盖章合同在盖章审核之前可以进行删除，然后重新上传  验证盖章审核
                    //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(otherStampReviewAttEntity.ContractId);
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt())
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt() || contract.StampReviewStatus == EnumStampReviewStatus.Refuse.ToInt())
                    if (otherStampReviewAttEntity.Status == EnumStampReviewStatus.Approved.ToInt() || otherStampReviewAttEntity.Status == EnumStampReviewStatus.Refuse.ToInt())
                    {
                        throw new ApiException("当前附件已审核，不可以删除");
                    }
                    DbOpe_crm_contract_othercontract_attachfile.Instance.DeleteData(id);
                    DbOpe_crm_contract_othercontract_attachfile_review.Instance.DeleteData(r => r.ContractOtherContractAttachFileId == id);
                    List<Db_crm_contract_othercontract_attachfile> othercontractattachfiles = DbOpe_crm_contract_othercontract_attachfile.Instance.GetDataList(r => r.ContractId == otherStampReviewAttEntity.ContractId);
                    if (othercontractattachfiles.Count == 0)
                    {
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { OtherStampReviewStatus = 1 }, otherStampReviewAttEntity.ContractId);
                        DbOpe_crm_contract_otherstampreview.Instance.DeleteIsHistoryByContractId(otherStampReviewAttEntity.ContractId);
                    }
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileType"></param>
        /// <returns></returns>
        public void DeleteAttachFileAll(string id, string fileType)
        {
            var UserOrg = DbOpe_sys_user.Instance.GetOrgIdByUserId(UserId);
            switch (fileType)
            {
                //case "Contract":
                //    var contractAttEntity = DbOpe_crm_contract_attachfile.Instance.GetContractAttachfileById(id, true);
                //    if (contractAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_attachfile.Instance.DeleteData(id);
                //    break;
                //case "ContractChangeAppl":
                //    var contractChangeApplAttEntity = DbOpe_crm_contract_change_appl_attachfile.Instance.GetContractChangeApplAttachfileById(id, true);
                //    if (contractChangeApplAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_change_appl_attachfile.Instance.DeleteData(id);
                //    break;
                //case "DBOriginalContract":
                //    var dBOriginalContractAttEntity = DbOpe_crm_contract_dboriginalcontract_attachfile.Instance.GetContractDboriginalContractAttachfileById(id, true);
                //    if (dBOriginalContractAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_dboriginalcontract_attachfile.Instance.DeleteData(id);
                //    break;
                case "ElectronicContract":
                    var electronicContractAttEntity = DbOpe_crm_contract_electroniccontract_attachfile.Instance.GetContractElectronicContractAttachfileById(id, true);
                    if (UserOrg != Guid.Empty.ToString())
                    {
                        if (electronicContractAttEntity is null)
                        {
                            throw new ApiException("没有数据权限或者数据不存在");
                        }
                        bool IsHaveContractCollectionInfoAutoMatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(electronicContractAttEntity.ContractId);
                        if (IsHaveContractCollectionInfoAutoMatching)
                        {
                            throw new ApiException("当前合同存在到账登记审核中,无法删除合同附件信息");
                        }
                    }
                    //盖章合同在盖章审核之前可以进行删除，然后重新上传  验证盖章审核
                    Db_crm_contract electronicontract = DbOpe_crm_contract.Instance.GetDataById(electronicContractAttEntity.ContractId);
                    //if (electronicontract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt())
                    //if (electronicontract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt() || electronicontract.StampReviewStatus == EnumStampReviewStatus.Refuse.ToInt())
                    //if (electronicContractAttEntity.Status == EnumStampReviewStatus.Approved.ToInt() || electronicContractAttEntity.Status == EnumStampReviewStatus.Refuse.ToInt())
                    //{
                    //    throw new ApiException("当前合同的盖章合同已审核，不可以删除");
                    //}
                    DbOpe_crm_contract_electroniccontract_attachfile.Instance.DeleteData(id);
                    List<Db_crm_contract_electroniccontract_attachfile> electroniccontractattachfiles = DbOpe_crm_contract_electroniccontract_attachfile.Instance.GetDataList(r => r.ContractId == electronicContractAttEntity.ContractId);
                    if (electroniccontractattachfiles.Count == 0)
                    {
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { StampReviewStatus = 1 }, electronicContractAttEntity.ContractId);
                        DbOpe_crm_contract_stampreview.Instance.DeleteIsHistoryByContractId(electronicContractAttEntity.ContractId);
                    }
                    break;
                //case "ContractInvoice":
                //    var contractInvoiceAttEntity = DbOpe_crm_contract_invoice_attachment.Instance.GetContractInvoiceAttachmentById(id, true);
                //    if (contractInvoiceAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_invoice_attachment.Instance.DeleteData(id);
                //    break;
                //case "ContractInvoiceAppl":
                //    var contractInvoiceApplAttEntity = DbOpe_crm_contract_invoiceappl_attachment.Instance.GetContractInvoiceapplAttachmentById(id, true);
                //    if (contractInvoiceApplAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_invoiceappl_attachment.Instance.DeleteData(id);
                //    break;
                //case "ContractPaymentInfo":
                //    var contractPaymentInfoAttEntity = DbOpe_crm_contract_paymentinfo_attachfile.Instance.GetContractPaymentinfoAttachfileById(id, true);
                //    if (contractPaymentInfoAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_paymentinfo_attachfile.Instance.DeleteData(id);
                //    break;
                //case "RefundContractInvoice":
                //    var refundContractInvoiceAttEntity = DbOpe_crm_contract_refundinvoice_attachment.Instance.GetContractRefundinvoiceAttachmentById(id, true);
                //    if (refundContractInvoiceAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_refundinvoice_attachment.Instance.DeleteData(id);
                //    break;
                case "SealedContract":

                    var sealedContractAttEntity = DbOpe_crm_contract_sealedcontract_attachfile.Instance.GetContractSealedContractAttachfileById(id, true);
                    if (UserOrg != Guid.Empty.ToString())
                    {
                        if (sealedContractAttEntity is null)
                        {
                            throw new ApiException("没有数据权限或者数据不存在");
                        }
                        bool IsHaveContractCollectionInfoAutoMatchingS = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(sealedContractAttEntity.ContractId);
                        if (IsHaveContractCollectionInfoAutoMatchingS)
                        {
                            throw new ApiException("当前合同存在到账登记审核中,无法删除合同附件信息");
                        }
                    }
                    //盖章合同在盖章审核之前可以进行删除，然后重新上传  验证盖章审核
                    Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(sealedContractAttEntity.ContractId);
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt())
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt() || contract.StampReviewStatus == EnumStampReviewStatus.Refuse.ToInt())
                    //if (sealedContractAttEntity.Status == EnumStampReviewStatus.Approved.ToInt() || sealedContractAttEntity.Status == EnumStampReviewStatus.Refuse.ToInt())
                    //{
                    //    throw new ApiException("当前合同的盖章合同已审核，不可以删除");
                    //}
                    DbOpe_crm_contract_sealedcontract_attachfile.Instance.DeleteData(id);
                    List<Db_crm_contract_sealedcontract_attachfile> sealedcontractattachfiles = DbOpe_crm_contract_sealedcontract_attachfile.Instance.GetDataList(r => r.ContractId == sealedContractAttEntity.ContractId);
                    if (sealedcontractattachfiles.Count == 0)
                    {
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { StampReviewStatus = 1 }, sealedContractAttEntity.ContractId);
                        DbOpe_crm_contract_stampreview.Instance.DeleteIsHistoryByContractId(sealedContractAttEntity.ContractId);
                    }
                    break;
                //case "ContractSpecial":
                //    var contractSpecialAttEntity = DbOpe_crm_contract_special_attachfile.Instance.GetContractSpecialAttachfileById(id, true);
                //    if (contractSpecialAttEntity is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_contract_special_attachfile.Instance.DeleteData(id);
                //    break;
                //case "TransactionReceipt":
                //    var collectioninfoAttachFile = DbOpe_crm_collectioninfo_attachfile.Instance.GetCollectioninfoAttachfileById(id, true);
                //    if (collectioninfoAttachFile is null)
                //    {
                //        throw new ApiException("没有数据权限或者数据不存在");
                //    }
                //    DbOpe_crm_collectioninfo_attachfile.Instance.DeleteData(id);
                //    break;
                case "OtherStampReview":
                    var otherStampReviewAttEntity = DbOpe_crm_contract_othercontract_attachfile.Instance.GetContractOtherContractAttachfileById(id, true);
                    if (otherStampReviewAttEntity is null)
                    {
                        throw new ApiException("没有数据权限或者数据不存在");
                    }
                    //bool IsHaveContractCollectionInfoAutoMatchingS = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(otherStampReviewAttEntity.ContractId);
                    //if (IsHaveContractCollectionInfoAutoMatchingS)
                    //{
                    //    throw new ApiException("当前合同存在到账登记审核中,无法删除合同附件信息");
                    //}
                    //盖章合同在盖章审核之前可以进行删除，然后重新上传  验证盖章审核
                    //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(otherStampReviewAttEntity.ContractId);
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt())
                    //if (contract.StampReviewStatus == EnumStampReviewStatus.Approved.ToInt() || contract.StampReviewStatus == EnumStampReviewStatus.Refuse.ToInt())
                    //if (otherStampReviewAttEntity.Status == EnumStampReviewStatus.Approved.ToInt() || otherStampReviewAttEntity.Status == EnumStampReviewStatus.Refuse.ToInt())
                    //{
                    //    throw new ApiException("当前合同的盖章合同已审核，不可以删除");
                    //}
                    DbOpe_crm_contract_othercontract_attachfile.Instance.DeleteData(id);
                    DbOpe_crm_contract_othercontract_attachfile_review.Instance.DeleteData(r => r.ContractOtherContractAttachFileId == id);
                    List<Db_crm_contract_othercontract_attachfile> othercontractattachfiles = DbOpe_crm_contract_othercontract_attachfile.Instance.GetDataList(r => r.ContractId == otherStampReviewAttEntity.ContractId);
                    if (othercontractattachfiles.Count == 0)
                    {
                        DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { OtherStampReviewStatus = 1 }, otherStampReviewAttEntity.ContractId);
                        DbOpe_crm_contract_otherstampreview.Instance.DeleteIsHistoryByContractId(otherStampReviewAttEntity.ContractId);
                    }
                    break;
                default:
                    break;
            }
        }
    }
}