﻿using System.ComponentModel;
using System.Reflection;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Namotion.Reflection;
using NJsonSchema;

namespace CRM2_API.Common.Filter
{
    public class EnumSchemaFilter: ISchemaFilter
    {
        /// <summary>
        /// 处理过程
        /// </summary>
        /// <param name="model"></param>
        /// <param name="context"></param>
        public void Apply(OpenApiSchema model, SchemaFilterContext context)
        {
            if (context.Type.IsEnum)
            {
                model.Enum.Clear();
                var varnameArray = new OpenApiArray();
                var descriptionArray = new OpenApiArray();
                var names = Enum.GetNames(context.Type).ToList();
                string desc = $"{context.Type.Name}: \n";
                names.ForEach(name =>
                {
                    var enumIntValue = GetEnumIntegerValue(name, context);
                    //枚举注释，从Description标签或者summary注释获取
                    string nameDesc = GetDescription(context.Type, enumIntValue);
                    if (nameDesc.IsNullOrEmpty())
                        nameDesc = GetEnumSummary(context.Type, name);
                    desc += $"* `{enumIntValue}` - {name}_{nameDesc}\n";
                    varnameArray.Add(new OpenApiString(name));
                    descriptionArray.Add(new OpenApiString(nameDesc));
                    model.Enum.Add(
                        new OpenApiInteger(enumIntValue));
                });
                model.Example = new OpenApiInteger(GetEnumIntegerValue(names.First(), context));
                model.Extensions.Add("x-enum-descriptions",descriptionArray);
                model.Extensions.Add("x-enum-varnames",varnameArray);
                model.Description = desc;
            }
            //修改泛型模型，生成的名称，改成与生成ts模型一致
            if (context.Type.IsClass&&context.Type.IsGenericType)
            {
                var schema = JsonSchema.FromType(context.Type);
                model.Title = schema.Title;
            }
        }
        
        /// <summary>
        /// 获取枚举的int值
        /// </summary>
        /// <param name="name"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        private int GetEnumIntegerValue(string name, SchemaFilterContext context) => Convert.ToInt32(Enum.Parse(context.Type, name));
        
        /// <summary>
        /// 获取枚举值的描述信息
        /// </summary>
        /// <param name="t"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        private string GetDescription(Type t, object value)
        {
            if (t is null || value is null) return string.Empty;
            foreach (MemberInfo mInfo in t.GetMembers())
            {
                if (mInfo.Name == t.GetEnumName(value))
                {
                    foreach (Attribute attr in Attribute.GetCustomAttributes(mInfo))
                    {
                        if (attr.GetType() == typeof(DescriptionAttribute))
                        {
                            return ((DescriptionAttribute)attr).Description;
                        }
                    }
                }
            }
            return string.Empty;
        }
        /// <summary>
        /// 获取枚举的summary注释
        /// </summary>
        /// <param name="t"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        private string GetEnumSummary(Type t, string name)
        {
            string desc=t.GetField(name).GetXmlDocsSummary();
            if(desc.IsNullOrEmpty())
                return string.Empty;
            return desc;
        }
    }
}