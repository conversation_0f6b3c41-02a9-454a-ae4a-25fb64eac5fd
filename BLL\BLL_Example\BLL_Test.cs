﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Promotion;
using System.Diagnostics;

namespace CRM2_API.BLL.BLL_Example
{
    /// <summary>
    /// 样例BLL
    /// </summary>
    public class BLL_Test : BaseBLL<BLL_Test>
    {
        private readonly string[] Summaries = new[]
        {
            "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
        };
        /// <summary>
        /// 测试例子，其实代码过于简单，不需要创建业务处理模块，直接写在Controller中即可
        /// </summary>
        /// <returns></returns>
        public WeatherForecast_Out[] GetWeahterForcast()
        {
            return Enumerable.Range(1, 5).Select(index => new WeatherForecast_Out
            {
                Date = DateTime.Now.AddDays(index),
                TemperatureC = Random.Shared.Next(-20, 55),
                Summary = Summaries[Random.Shared.Next(Summaries.Length)]
            })
            .ToArray();
        }

        public int DoPromotionJob(DateTime dateTime)
        {
            bool isDebugger = false;
            #if DEBUG
            isDebugger = true;
            #endif
            if (AppSettings.Env == Enum_SystemSettingEnv.Debug)
            {
                isDebugger = true;
            }
            //时间值
            DateTime now = dateTime;//DateTime.Now;

            TimeSpan dayStartTime = new(0, 0, 0);
            TimeSpan dayEndTime = new(23, 59, 59);

            List<UserSalesAchivementAmount> accumulateUserSalesAchivementAmountList = BLL_ContractAchievement.Instance.GetUserSalesAchivementAmountList(new DateSpanParams(), null); //员工的累计业绩
            List<UserSalesAchivementAmount> quarterUserSalesAchivementAmountList = new();    //员工的季度业绩
            List<UserSalesAchivementAmount> halfYearUserSalesAchivementAmountList = new();    //员工的半年业绩
            List<UserSalesAchivementAmount> yearUserSalesAchivementAmountList = new();   //员工的年度业绩

            //工号处理部分 优先于其他部分
            foreach (var item in accumulateUserSalesAchivementAmountList)
            {
                string needJobNum = item.UserId;
                DbOpe_sys_user.Instance.CheckAndInsertJobNum(needJobNum);
            }

            List<PromotionInfoPlus> allPromotions = DbOpe_sys_promotion.Instance.GetAllPromotionWithDesc(1); //系统生效的晋升信息
            List<PromotionInfoPlus> brigadePromotions = allPromotions.Where(w => w.Type == EnumPromotionType.BrigadeLeader).ToList();  //大队领导晋升条件
            List<PromotionInfoPlus> squadronPromotions = allPromotions.Where(w => w.Type == EnumPromotionType.SquadronLeader).ToList();  //中队领导晋升条件
            List<PromotionInfoPlus> workersPromotions = allPromotions.Where(w => w.Type == EnumPromotionType.Worker).ToList();  //职工晋升条件

            var userPromotionInfoList = DbOpe_crm_user_promotion.Instance.GetUserPromotionInfo();    //用户信息

            var allUserPromotionDescribeList = DbOpe_crm_user_promotion_describe.Instance.GetUserPromotionDescribeArrayList(); //所有的职级描述信息
            var allUserPromotionDescribeDict = allUserPromotionDescribeList.ToDictionary(k => k.RankCode, v => v);    //所有的职级描述字段
            var regionCommissioner = allUserPromotionDescribeDict[EnumUserPromotionType.QYZJ.ToString()];//获取区域总监职级描述信息

            List<Db_crm_user_promotion> insertUserPromotionList = new();  //最终保存的荣誉
            Db_crm_user_promotion temp = null;  //临时实体

            // 如果是 3、6、9、12月份，则计算员工的季度业绩
            if (now is { Month: 3 or 6 or 9 or 12 } || isDebugger)
            {
                DateTime startQuarter = now.AddMonths(0 - (now.Month - 1) % 3).AddDays(1 - now.Day).Date + dayStartTime; // 本季度初
                DateTime endQuarter = startQuarter.AddMonths(3).AddDays(-1).Date + dayEndTime; // 本季度末
                DateSpanParams dateSpanParams = new DateSpanParams()
                {
                    DateStart = startQuarter,
                    DateEnd = endQuarter,
                };
                quarterUserSalesAchivementAmountList = BLL_ContractAchievement.Instance.GetUserSalesAchivementAmountList(dateSpanParams, null); //员工的季度业绩
            }
            // 如果是6月份，则计算员工的半年度业绩
            if (now.Month == 6 || isDebugger)
            {
                DateSpanParams dateSpanParams = new DateSpanParams()
                {
                    DateStart = new DateTime(now.Year, 1, 1, 0, 0, 0),
                    DateEnd = new DateTime(now.Year, 6, 30, 23, 59, 59),
                };
                halfYearUserSalesAchivementAmountList = BLL_ContractAchievement.Instance.GetUserSalesAchivementAmountList(dateSpanParams, null); //员工的半年度业绩
            }

            // 如果是12月份，则计算员工的年度业绩
            if (now.Month == 12 || isDebugger)
            {
                DateSpanParams dateSpanParams = new DateSpanParams()
                {
                    DateStart = new DateTime(now.Year, 1, 1, 0, 0, 0),
                    DateEnd = new DateTime(now.Year, 12, 31, 23, 59, 59),
                };
                yearUserSalesAchivementAmountList = BLL_ContractAchievement.Instance.GetUserSalesAchivementAmountList(dateSpanParams, null); //员工的年度业绩
            }

            Dictionary<string, UserSalesAchivementAmount> accumulateUserSalesAchivementAmountDict =
                accumulateUserSalesAchivementAmountList.ToDictionary(k => k.UserId, v => v);    //累计业绩字典

            Dictionary<string, UserSalesAchivementAmount> quarterUserSalesAchivementAmountDict =
                quarterUserSalesAchivementAmountList.ToDictionary(k => k.UserId, v => v); //员工的季度字典

            Dictionary<string, UserSalesAchivementAmount> halfYearUserSalesAchivementAmountDict =
                halfYearUserSalesAchivementAmountList.ToDictionary(k => k.UserId, v => v);   //员工的半年度字典

            Dictionary<string, UserSalesAchivementAmount> yearUserSalesAchivementAmountDict =
                yearUserSalesAchivementAmountList.ToDictionary(k => k.UserId, v => v);  //员工的年度字典

            // 遍历所有用户
            foreach (UserPromotionInfo userPromotionInfo in userPromotionInfoList)
            {
                //如果员工没有累计业绩则跳过, 累计业绩没有则不会存在任何业绩，该用户不会有晋升的条件
                if (!accumulateUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                {
                    continue;
                }
                //获取用户当前职称
                Db_crm_user_promotion_describe currentUserPromotion = null;
                //如果客户没有职称则初始化默认值[注：领导一般是有初始值的，没有则为用户创建时未添加]
                if (string.IsNullOrEmpty(userPromotionInfo.PromotionDescribeId) || !allUserPromotionDescribeDict.ContainsKey(userPromotionInfo.RankCode))
                {
                    if (userPromotionInfo.UserType == 1)
                    {
                        currentUserPromotion = allUserPromotionDescribeDict[EnumUserPromotionType.QYZJ.ToString()]; //管理者初始化为区域总监的ID
                    }
                    else if (userPromotionInfo.UserType == 2)
                    {
                        currentUserPromotion = allUserPromotionDescribeDict[EnumUserPromotionType.ZY.ToString()]; //职工初始化为普通职工 -> 实习生
                    }
                }
                else
                {
                    // 员工有职称则赋值为用户的当前职称
                    currentUserPromotion = allUserPromotionDescribeDict[userPromotionInfo.RankCode];
                }
                //获取员工可以晋级的职称列表
                List<PromotionInfoPlus> canUpgradePromotions = null;
                //若员工的职级大于等于区域总监
                if (currentUserPromotion.RankSortCode >= regionCommissioner.RankSortCode)
                {
                    int currentUserPromotionIndex = allUserPromotionDescribeList.IndexOf(currentUserPromotion); //用户当前职级所在索引
                                                                                                                // 用户当前职称所在的索引 + 1 超出了所有的职称信息之和，则代表已经升级到了最高的职称，因此跳过
                    if ((currentUserPromotionIndex + 1) >= allUserPromotionDescribeList.Count)  //已经升级到头了
                    {
                        continue;
                    }

                    // 因为区域总监可以跳级，此处固定晋升到下一级作废
                    /*var nextUserPromotion = allUserPromotionDescribeList[(currentUserPromotionIndex + 1)];
                    if (userPromotionInfo.UserType == 1)
                    {
                        if (userPromotionInfo is { OrgType: EnumOrgType.BattleTeam or EnumOrgType.Battalion })     //适用于大队领导晋升规则
                        {
                            canUpgradePromotions = brigadePromotions.Where(w => w.PromotionEndKey.Equals(nextUserPromotion.Id)).ToList();
                        }
                        else if (userPromotionInfo is { OrgType: EnumOrgType.Squadron })      //适用于中队领导晋升规则
                        {
                            canUpgradePromotions = squadronPromotions.Where(w => w.PromotionEndKey.Equals(nextUserPromotion.Id)).ToList();
                        }
                    }
                    else if (userPromotionInfo.UserType == 2)
                    {
                        canUpgradePromotions = workersPromotions.Where(w => w.PromotionEndKey.Equals(nextUserPromotion.Id)).ToList();
                    }*/

                    //区域总监之后的可以跳级，拿到可以跳级到的职位
                    int nextUserPromotionIndex = currentUserPromotionIndex + 1; //当前职称的索引加1
                                                                                // 以当前职称的索引加1为开始拿到之后的所有职称，这些都是可以跳级到的
                    var canUpgradePromotionDescribeList = allUserPromotionDescribeList.GetRange(nextUserPromotionIndex,
                        allUserPromotionDescribeList.Count - nextUserPromotionIndex).Select(s => s.Id).ToList();
                    //如果用户类型是管理员
                    if (userPromotionInfo.UserType == 1)
                    {
                        // 判断用户的组织机构类型,战队和大队 则适用于大队领导晋升规则
                        if (userPromotionInfo is { OrgType: EnumOrgType.BattleTeam or EnumOrgType.Battalion })     //适用于大队领导晋升规则
                        {
                            canUpgradePromotions = brigadePromotions.Where(w => canUpgradePromotionDescribeList.Contains(w.PromotionEndKey)).ToList();
                        }
                        // 中队 则适用于中队领导晋升规则
                        else if (userPromotionInfo is { OrgType: EnumOrgType.Squadron })      //适用于中队领导晋升规则
                        {
                            canUpgradePromotions = squadronPromotions.Where(w => canUpgradePromotionDescribeList.Contains(w.PromotionEndKey)).ToList();
                        }
                    }
                    // 如果用户类型类型是职员
                    else if (userPromotionInfo.UserType == 2)
                    {
                        // 则适用于职员晋升规则
                        canUpgradePromotions = workersPromotions.Where(w => canUpgradePromotionDescribeList.Contains(w.PromotionEndKey)).ToList();
                    }
                }
                // 如果用户当前职称在区域总监之下，目前不存在跳级的可能性
                // 如果用户的职称在区域总监之下，则只适用于职员晋升规则，因为管理员一般初始化就是区域总监
                else
                {
                    // 以用户当前职称的Id作为开始,查询可以晋升到的职称列表
                    canUpgradePromotions = workersPromotions.Where(w => w.PromotionStartKey.Equals(currentUserPromotion.Id)).ToList();
                }

                // 如果可以晋升到的职称列表不为空
                if (canUpgradePromotions is not null and { Count: > 0 })
                {
                    canUpgradePromotions.Reverse(); //反过来一下,能升级到大的肯定要升级到大的
                    foreach (PromotionInfoPlus promotionInfo in canUpgradePromotions)
                    {
                        // 下面则根据条件去判断，注意最多的时候会有两个条件。
                        if (promotionInfo.PromotionConditionType1 == EnumPromotionConditionType.GrandTotal)
                        {
                            var accumulate = accumulateUserSalesAchivementAmountDict[userPromotionInfo.Id];
                            if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue1 * 10000)
                            {
                                if (promotionInfo.PromotionConditionType2 != null)
                                {
                                    if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.GrandTotal)
                                    {
                                        if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                        {
                                            //通过，需要晋升,下个月生效
                                            temp = new()
                                            {
                                                Id = Guid.NewGuid().ToString(),
                                                UserId = userPromotionInfo.Id,
                                                PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                PromotionTime = now,
                                                TakingEffectTime = isDebugger ? now : new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                Deleted = false,
                                                CreateUser = null,
                                                CreateDate = now,
                                            };
                                            insertUserPromotionList.Add(temp);
                                            break;
                                        }
                                    }
                                    else if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.Annual)
                                    {
                                        if (now.Month == 6 || isDebugger)
                                        {
                                            if (halfYearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                            {
                                                var halfYear = halfYearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                if (halfYear.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                {
                                                    //通过，需要晋升
                                                    //通过，需要晋升,下个月生效
                                                    temp = new()
                                                    {
                                                        Id = Guid.NewGuid().ToString(),
                                                        UserId = userPromotionInfo.Id,
                                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                        PromotionTime = now,
                                                        TakingEffectTime = isDebugger ? now : new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                        Deleted = false,
                                                        CreateUser = null,
                                                        CreateDate = now,
                                                    };
                                                    insertUserPromotionList.Add(temp);
                                                    break;
                                                }
                                            }

                                        }
                                        if (now.Month == 12 || isDebugger)
                                        {
                                            if (yearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                            {
                                                var year = yearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                if (year.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                {
                                                    //通过，需要晋升,下个月生效
                                                    temp = new()
                                                    {
                                                        Id = Guid.NewGuid().ToString(),
                                                        UserId = userPromotionInfo.Id,
                                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                        PromotionTime = now,
                                                        TakingEffectTime = isDebugger ? now : new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                        Deleted = false,
                                                        CreateUser = null,
                                                        CreateDate = now,
                                                    };
                                                    insertUserPromotionList.Add(temp);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    else if (promotionInfo.PromotionConditionType2 is EnumPromotionConditionType.Quarter && (now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                                    {
                                        if (quarterUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                        {
                                            var quarter = quarterUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                            if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                            {
                                                //通过，需要晋升,下个月生效
                                                temp = new()
                                                {
                                                    Id = Guid.NewGuid().ToString(),
                                                    UserId = userPromotionInfo.Id,
                                                    PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                    PromotionTime = now,
                                                    TakingEffectTime = isDebugger ? now : new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                    Deleted = false,
                                                    CreateUser = null,
                                                    CreateDate = now,
                                                };
                                                insertUserPromotionList.Add(temp);
                                                break;
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    //通过，需要晋升,下个月生效
                                    temp = new()
                                    {
                                        Id = Guid.NewGuid().ToString(),
                                        UserId = userPromotionInfo.Id,
                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                        PromotionTime = now,
                                        TakingEffectTime = isDebugger ? now : new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                        Deleted = false,
                                        CreateUser = null,
                                        CreateDate = now,
                                    };
                                    insertUserPromotionList.Add(temp);
                                    break;
                                }
                            }
                        }
                        else if (promotionInfo.PromotionConditionType1 == EnumPromotionConditionType.Annual)
                        {
                            //看看有没有提前晋升的机会
                            if (now.Month == 6 || isDebugger)
                            {
                                if (halfYearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                {
                                    var halfYear = halfYearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                    if (halfYear.AchivementAmount >= promotionInfo.PromotionConditionValue1 * 10000)
                                    {
                                        if (promotionInfo.PromotionConditionType2 != null)
                                        {
                                            if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.GrandTotal)
                                            {
                                                var accumulate = accumulateUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                {
                                                    //通过，需要晋升,下个月生效
                                                    temp = new()
                                                    {
                                                        Id = Guid.NewGuid().ToString(),
                                                        UserId = userPromotionInfo.Id,
                                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                        PromotionTime = now,
                                                        TakingEffectTime = isDebugger ? now : new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                        Deleted = false,
                                                        CreateUser = null,
                                                        CreateDate = now,
                                                    };
                                                    insertUserPromotionList.Add(temp);
                                                    break;
                                                }
                                            }
                                            else if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.Annual)
                                            {
                                                if (halfYear.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                {
                                                    //通过，需要晋升,下个月生效
                                                    temp = new()
                                                    {
                                                        Id = Guid.NewGuid().ToString(),
                                                        UserId = userPromotionInfo.Id,
                                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                        PromotionTime = now,
                                                        TakingEffectTime = isDebugger ? now : new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                        Deleted = false,
                                                        CreateUser = null,
                                                        CreateDate = now,
                                                    };
                                                    insertUserPromotionList.Add(temp);
                                                    break;
                                                }
                                            }
                                            else if (promotionInfo.PromotionConditionType2 is EnumPromotionConditionType.Quarter && (now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                                            {
                                                if (quarterUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                {
                                                    var quarter = quarterUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                    if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = now,
                                                            TakingEffectTime = isDebugger ? now : new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //通过，需要晋升,下个月生效
                                            temp = new()
                                            {
                                                Id = Guid.NewGuid().ToString(),
                                                UserId = userPromotionInfo.Id,
                                                PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                PromotionTime = now,
                                                TakingEffectTime = isDebugger ? now :
                                                     new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                Deleted = false,
                                                CreateUser = null,
                                                CreateDate = now,
                                            };
                                            insertUserPromotionList.Add(temp);
                                            break;
                                        }
                                    }
                                }
                            }
                            if (now.Month == 12 || isDebugger)
                            {
                                if (yearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                {
                                    var year = yearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                    if (year.AchivementAmount >= promotionInfo.PromotionConditionValue1 * 10000)
                                    {
                                        if (promotionInfo.PromotionConditionType2 != null)
                                        {
                                            if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.GrandTotal)
                                            {
                                                var accumulate = accumulateUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                {
                                                    //通过，需要晋升,下个月生效
                                                    temp = new()
                                                    {
                                                        Id = Guid.NewGuid().ToString(),
                                                        UserId = userPromotionInfo.Id,
                                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                        PromotionTime = now,
                                                        TakingEffectTime = isDebugger ? now :
                                                             new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                        Deleted = false,
                                                        CreateUser = null,
                                                        CreateDate = now,
                                                    };
                                                    insertUserPromotionList.Add(temp);
                                                    break;
                                                }
                                            }
                                            else if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.Annual)
                                            {
                                                if (year.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                {
                                                    //通过，需要晋升,下个月生效
                                                    temp = new()
                                                    {
                                                        Id = Guid.NewGuid().ToString(),
                                                        UserId = userPromotionInfo.Id,
                                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                        PromotionTime = now,
                                                        TakingEffectTime = isDebugger ? now :
                                                             new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                        Deleted = false,
                                                        CreateUser = null,
                                                        CreateDate = now,
                                                    };
                                                    insertUserPromotionList.Add(temp);
                                                    break;
                                                }
                                            }
                                            else if (promotionInfo.PromotionConditionType2 is EnumPromotionConditionType.Quarter && (now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                                            {
                                                if (quarterUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                {
                                                    var quarter = quarterUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                    if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = now,
                                                            TakingEffectTime = isDebugger ? now :
                                                                 new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //通过，需要晋升,下个月生效
                                            temp = new()
                                            {
                                                Id = Guid.NewGuid().ToString(),
                                                UserId = userPromotionInfo.Id,
                                                PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                PromotionTime = now,
                                                TakingEffectTime = isDebugger ? now :
                                                     new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                Deleted = false,
                                                CreateUser = null,
                                                CreateDate = now,
                                            };
                                            insertUserPromotionList.Add(temp);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        else if (promotionInfo.PromotionConditionType1 is EnumPromotionConditionType.Quarter && (now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                        {
                            if (quarterUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                            {
                                var quarter = quarterUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue1 * 10000)
                                {
                                    if (promotionInfo.PromotionConditionType2 != null)
                                    {
                                        if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.GrandTotal)
                                        {
                                            var accumulate = accumulateUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                            if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                            {
                                                //通过，需要晋升,下个月生效
                                                temp = new()
                                                {
                                                    Id = Guid.NewGuid().ToString(),
                                                    UserId = userPromotionInfo.Id,
                                                    PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                    PromotionTime = now,
                                                    TakingEffectTime = isDebugger ? now :
                                                         new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                    Deleted = false,
                                                    CreateUser = null,
                                                    CreateDate = now,
                                                };
                                                insertUserPromotionList.Add(temp);
                                                break;
                                            }
                                        }
                                        else if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.Annual)
                                        {
                                            if (now.Month == 6 || isDebugger)
                                            {
                                                if (halfYearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                {
                                                    var halfYear = halfYearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                    if (halfYear.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = now,
                                                            TakingEffectTime = isDebugger ? now :
                                                                 new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }

                                            }
                                            if (now.Month == 12 || isDebugger)
                                            {
                                                if (yearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                {
                                                    var year = yearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                    if (year.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = now,
                                                            TakingEffectTime = isDebugger ? now :
                                                                 new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        else if (promotionInfo.PromotionConditionType2 is EnumPromotionConditionType.Quarter && (now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                                        {
                                            if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                            {
                                                //通过，需要晋升,下个月生效
                                                temp = new()
                                                {
                                                    Id = Guid.NewGuid().ToString(),
                                                    UserId = userPromotionInfo.Id,
                                                    PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                    PromotionTime = now,
                                                    TakingEffectTime = isDebugger ? now :
                                                         new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                                    Deleted = false,
                                                    CreateUser = null,
                                                    CreateDate = now,
                                                };
                                                insertUserPromotionList.Add(temp);
                                                break;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        //通过，需要晋升,下个月生效
                                        temp = new()
                                        {
                                            Id = Guid.NewGuid().ToString(),
                                            UserId = userPromotionInfo.Id,
                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                            PromotionTime = now,
                                            TakingEffectTime = isDebugger ? now :
                                                 new DateTime(now.Year, now.Month, 1).AddMonths(1),
                                            Deleted = false,
                                            CreateUser = null,
                                            CreateDate = now,
                                        };
                                        insertUserPromotionList.Add(temp);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (insertUserPromotionList is not null and { Count: > 0 })
            {
                int result = DbOpe_crm_user_promotion.Instance.Insert(insertUserPromotionList);
                Debug.WriteLine($"职工晋升{result}条执行完成!");
                return result;
            }
            return -1;
        }
    }
}
