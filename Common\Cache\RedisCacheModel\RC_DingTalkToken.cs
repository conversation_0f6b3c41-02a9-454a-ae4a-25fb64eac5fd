﻿using CRM2_API.Common.AppSetting;

namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class DingTalkToken
        {
            const string SILENCETIMEKEY = "DingTalkToken";
            /// <summary>
            /// 获取
            /// </summary>
            /// <returns></returns>
            public static string Get()
            {
                var key = SILENCETIMEKEY;
                return RedisHelper.Get(key);
            }
            /// <summary>
            /// 保存
            /// </summary>
            /// <param name="accessToken"></param>
            /// <param name="ts"></param>
            public static void Set(string accessToken,TimeSpan ts)
            {
                var key = SILENCETIMEKEY;
                RedisHelper.Set(key, accessToken, ts);
            }
        }

    }
}
