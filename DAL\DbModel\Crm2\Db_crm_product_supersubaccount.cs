﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///超级子账号
    ///</summary>
    [SugarTable("crm_product_supersubaccount")]
    public class Db_crm_product_supersubaccount
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:合同表Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractId {get;set;}

           /// <summary>
           /// Desc:产品价格
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ProductPrice { get; set; }

           /// <summary>
           /// Desc:价格
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal ContractProductinfoPrice { get; set; }

           /// <summary>
           /// Desc:价格
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal ContractProductinfoPriceTotal { get; set; }

           /// <summary>
           /// Desc:子账号授权国家次数
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? AuthorizationNum {get;set;}

           /// <summary>
           /// Desc:开通月数
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? OpeningMonths {get;set;}

           /// <summary>
           /// Desc:首次开通月数
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? FirstOpeningMonths { get; set; }

           /// <summary>
           /// Desc:开通年数
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? OpeningYears { get; set; }

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
