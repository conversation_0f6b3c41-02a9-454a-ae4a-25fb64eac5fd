﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_salesrules")]
    public class Db_sys_salesrules
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:国家ID
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? SID { get; set; }

        /// <summary>
        /// Desc:国家类型 0：进口  1：出口
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? CType { get; set; }

        /// <summary>
        /// Desc:中文名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CountryName { get; set; }

        /// <summary>
        /// Desc:搜索关键词
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string SearchCode { get; set; }

        /// <summary>
        /// Desc:国家代码
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CountryCode { get; set; }

        /// <summary>
        /// Desc:国家代码补充
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CountryCodeAdd { get; set; }

        /// <summary>
        /// Desc:数据库编码
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string DBCode { get; set; }

        /// <summary>
        /// Desc:区域销售规则
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string SalesCountry { get; set; }

        /// <summary>
        /// Desc:零售销售规则
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string VipSales { get; set; }

        /// <summary>
        /// Desc:套餐规则
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string AbovePricedsupport { get; set; }

        /// <summary>
        /// Desc:删除标记
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
