﻿using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;

namespace CRM2_API.Common.Cache
{
    public partial class LocalCache
    {
        /// <summary>
        /// 字典表缓存
        /// </summary>
        public class LC_SysDictionary : ILocalCache
        {
            
            public static List<Db_sys_dictionary> SysDictionaryCache { private set; get; } = new();

            public void SetCache()
            {
                lock (SysDictionaryCache)//一定要锁这个对象，否则出现使用脏数据的情况
                {
                    SysDictionaryCache = DbOpe_sys_dictionary.Instance.GetDictionaryTree();
                }
            }
        }
    }
}
