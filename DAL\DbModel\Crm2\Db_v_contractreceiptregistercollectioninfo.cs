﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_contractreceiptregistercollectioninfo")]
    public class Db_v_contractreceiptregistercollectioninfo
    {
           /// <summary>
           /// Desc:合同到账登记表id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractReceiptRegisterId {get;set;}
           public string CollectingCompany { get;set;}
           public string PaymentCompanyName { get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int PaymentMethod{get;set;}
        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>           
        public int Count { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>           
        public decimal? BankPaymentAmount {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? CashPaymentAmount {get;set;}

           /// <summary>
           /// Desc:到账日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? ArrivalDate {get;set;}
        /// <summary>
        /// 业绩归属日期
        /// </summary>
        public DateTime? BelongingMonth { get; set; }
        public int Currency { get; set; }
        public decimal? ArrivalAmount { get; set; }
        public decimal? FCArrivalAmount { get; set; }

    }
}
