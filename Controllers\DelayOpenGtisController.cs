﻿using CRM2_API.Common.Filter;
using CRM2_API.Common.JWT;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using TaskScheduler.Interfaces;
using TaskScheduler.Models;
using static CRM2_API.Common.GtisTask;

namespace CRM2_API.Controllers
{
    public class DelayOpenGtisController : MyControllerBase
    {
        private readonly TaskSchedulerService _taskScheduler;
        private readonly ITaskService _taskService;
        private readonly ILogger<DelayOpenGtisController> _logger;
        public DelayOpenGtisController(
           TaskSchedulerService taskScheduler,
        ITaskService taskService,
           ILogger<DelayOpenGtisController> logger)
        {
            _taskScheduler = taskScheduler;
            _taskService = taskService;
            _logger = logger;
        }


        //public async Task CreateSingleGtisService(string ApplId, DateTime dataForStart, BM_GtisOpe_RenewalContact bM_GtisOpe_RenewalContact, List<Db_crm_contract_serviceinfo_gtis_user> addUser, List<Db_crm_contract_serviceinfo_gtis_user> updateUser, List<Db_crm_contract_serviceinfo_gtis_user> delUser)
        //{

        //    ITaskService taskService = GetRequiredService<ITaskService>();
        //    // 提交首要任务
        //    var firstId = await taskService.SubmitTaskAsync<BM_GtisOpe_RenewalContact, BM_GtisReturnResult>(
        //        "GtisRenewalContact",
        //        bM_GtisOpe_RenewalContact,
        //        new TaskOptions
        //        {
        //            ScheduledTime = dataForStart,
        //        }//,                typeof(GtisRenewalContactProcessor)
        //         );

        //    var secondId = await taskService.SubmitTaskAsync<List<Db_crm_contract_serviceinfo_gtis_user>, BM_GtisReturnResult>(
        //                    "GtisAddUser",
        //                    addUser,
        //                    new TaskOptions
        //                    {
        //                        Dependencies = new List<TaskDependency> { new TaskDependency { TaskId = firstId, } },
        //                        ScheduledTime = dataForStart,
        //                    }
        //                    //,typeof(GtisAddUserProcessor)
        //                    );
        //    var thirdId = await taskService.SubmitTaskAsync<List<Db_crm_contract_serviceinfo_gtis_user>, BM_GtisReturnResult>(
        //                    "GtisUpdateUser",
        //                    updateUser,
        //                    new TaskOptions
        //                    {
        //                        Dependencies = new List<TaskDependency> { new TaskDependency { TaskId = secondId, } },
        //                        ScheduledTime = dataForStart,
        //                    }
        //                    //,typeof(GtisUpdateUserProcessor)
        //                    );
        //    var fourthId = await taskService.SubmitTaskAsync<List<Db_crm_contract_serviceinfo_gtis_user>, BM_GtisReturnResult>(
        //                    "GtisdelUser",
        //                    delUser,
        //                    new TaskOptions
        //                    {
        //                        Dependencies = new List<TaskDependency> { new TaskDependency { TaskId = secondId, } },
        //                        ScheduledTime = dataForStart,
        //                    }
        //                    //,typeof(GtisdelUserProcessor)
        //                    );
        //}

        //public  async Task TestT()
        //{
        //    ITaskService taskService = GetRequiredService<ITaskService>();
        //    DateTime dataForStart = DateTime.Now;
        //    string UserId = TokenModel.Instance.id;
        //    // 获取任务服务
        //    Db_sys_user user = DbOpe_sys_user.Instance.GetDbSysUserById(UserId);

        //    Db_crm_contract_serviceinfo_temporary_account account = new Db_crm_contract_serviceinfo_temporary_account();
        //    string TaskNum = (DbOpe_crm_contract_serviceinfo_temporary_account.Instance.GetQueryCount(account) + 1).ToString().PadLeft(5, '0');

        //    BM_AddGtisUserDemo AddGtisUser = new BM_AddGtisUserDemo();
        //    AddGtisUser.OprID = UserId;
        //    AddGtisUser.OprName = user.Name;
        //    AddGtisUser.TaskID = TaskNum.ToInt();
        //    AddGtisUser.Tel = user.Telephone;
        //    AddGtisUser.Email = user.Email;
        //    AddGtisUser.OpenID = user.WeChatOpenIDGZH;
        //    AddGtisUser.UnionID = user.WeChatOpenID;
        //    AddGtisUser.Nickname = user.Name;
        //    AddGtisUser.crm_country = "中国";
        //    AddGtisUser.crm_city = "";
        //    AddGtisUser.Sids = new int[] { 109, 425 };
        //    AddGtisUser.DemoCount = 1;
        //    AddGtisUser.UseHours = 1;
        //    AddGtisUser.IsOpenSubAccount = false;
        //    AddGtisUser.IsOpenHqs = false;
        //    AddGtisUser.AccountStartDate = DateTime.Now;

        //    // 提交首要任务
        //    var firstId = await taskService.SubmitTaskAsync<BM_AddGtisUserDemo, BM_GtisReturnResult>(
        //        "GtisTask",
        //        AddGtisUser,
        //        new TaskOptions
        //        {
        //            //ScheduledTime = dataForStart,
        //            Description = "第一步"
        //        }
        //        //,                typeof(GtisTaskProcessor)
        //        );

        //    var secondId = await taskService.SubmitTaskAsync<BM_AddGtisUserDemo, BM_GtisReturnResult>(
        //                        "GtisTask",
        //                        AddGtisUser,
        //                        new TaskOptions
        //                        {
        //                            Dependencies = new List<TaskDependency> { new TaskDependency { TaskId = firstId, } },
        //                            //ScheduledTime = dataForStart,
        //                            Description = "第二步"
        //                        }
        //                        //,                typeof(GtisTaskProcessor)
        //                        );
        //    var thirdId = await taskService.SubmitTaskAsync<BM_AddGtisUserDemo, BM_GtisReturnResult>(
        //                        "GtisTask",
        //                        AddGtisUser,
        //                        new TaskOptions
        //                        {
        //                            Dependencies = new List<TaskDependency> { new TaskDependency { TaskId = firstId, } },
        //                            //ScheduledTime = dataForStart,
        //                            Description = "第仨步"
        //                        }
        //                        //,                typeof(GtisTaskProcessor)
        //                        );

        //}

    }
}
