using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///预验缓存表
    ///</summary>
    [SugarTable("crm_preinspection_cache")]
    public class Db_crm_preinspection_cache
    {
        /// <summary>
        /// Desc:主键ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey=true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:输入关键词
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string InputKey { get; set; }

        /// <summary>
        /// Desc:结果JSON
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ResultJson { get; set; }

        /// <summary>
        /// Desc:命中次数
        /// Default:0
        /// Nullable:True
        /// </summary>           
        public int? HitCount { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:0
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:过期时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? ExpireDate { get; set; }
    }
} 