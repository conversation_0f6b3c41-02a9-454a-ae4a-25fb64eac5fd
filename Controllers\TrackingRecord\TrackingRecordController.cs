﻿using System.ComponentModel;
using CRM2_API.BLL;
using CRM2_API.BLL.TrackingRecord;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.TrackingRecord;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 跟踪记录控制器
    /// </summary>
    [Description("跟踪记录控制器")]
    public class TrackingRecordController : MyControllerBase
    {
        public TrackingRecordController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        
        /// <summary>
        /// 根据查询条件获取跟踪记录信息列表
        /// </summary>
        /// <param name="searchTrackingRecordList_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchTrackingRecordList_Out> SearchTrackingRecordList(SearchTrackingRecordList_In searchTrackingRecordList_In)
        {
            int total = 0;
            var list = DbOpe_crm_trackingrecord.Instance.SearchTrackingRecordList(searchTrackingRecordList_In, ref total);
            return GetApiTableOut(list, total);
        }
        
        /// <summary>
        /// 根据跟踪Id获取跟踪记录信息。
        /// </summary>
        /// <param name="getTrackingRecordByIdIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public GetTrackingRecordById_Out GetTrackingRecordById(GetTrackingRecordById_In getTrackingRecordByIdIn)
        {
            //验证Id是否为空
            if (getTrackingRecordByIdIn == null || string.IsNullOrEmpty(getTrackingRecordByIdIn.Id))
                throw new ApiException("跟踪信息主键不可为空");
            //查询跟踪信息
            return DbOpe_crm_trackingrecord.Instance.GetTrackingRecordById(getTrackingRecordByIdIn.Id);
        }
        
        /// <summary>
        /// 根据客户表主键Id获取跟踪记录信息。
        /// </summary>
        /// <param name="getTrackingRecordByCustomerIdIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        [Obsolete]
        public ApiTableOut<GetTrackingRecordByCustomerId_Out> GetTrackingRecordByCustomerId(GetTrackingRecordByCustomerId_In getTrackingRecordByCustomerIdIn)
        {
            //验证Id是否为空
            if (getTrackingRecordByCustomerIdIn == null || string.IsNullOrEmpty(getTrackingRecordByCustomerIdIn.CustomerId))
                throw new ApiException("客户表主键不可为空");
            int total = 0;
            var list = DbOpe_crm_trackingrecord.Instance.GetTrackingRecordByCustomerId(getTrackingRecordByCustomerIdIn, ref total);
            return GetApiTableOut(list, total);
        }
        
        /// <summary>
        /// 根据客户表主键Id获取客户追踪记录数量。
        /// </summary>
        /// <param name="getTrackingRecordNumberByCustomerIdIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        [Obsolete]
        public GetTrackingRecordNumberByCustomerId_Out GetTrackingRecordNumberByCustomerId(GetTrackingRecordNumberByCustomerId_In getTrackingRecordNumberByCustomerIdIn)
        {
            //验证Id是否为空
            if (getTrackingRecordNumberByCustomerIdIn == null || string.IsNullOrEmpty(getTrackingRecordNumberByCustomerIdIn.CustomerId))
                throw new ApiException("客户表主键不可为空");
            //查询跟踪信息数量
            return DbOpe_crm_trackingrecord.Instance.GetTrackingRecordNumberByCustomerId(getTrackingRecordNumberByCustomerIdIn.CustomerId);
        }
        
        /// <summary>
        /// 根据追踪记录表主键Id设置追踪记录为已读。
        /// </summary>
        /// <param name="setTrackingRecordReadByIdIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public SetTrackingRecordReadById_Out SetTrackingRecordReadById(SetTrackingRecordReadById_In setTrackingRecordReadByIdIn)
        {
            //验证Id是否为空
            if (setTrackingRecordReadByIdIn == null || string.IsNullOrEmpty(setTrackingRecordReadByIdIn.Id))
                throw new ApiException("追踪记录表主键不可为空");
            setTrackingRecordReadByIdIn.UserId = UserTokenInfo.id;  //防止越权
            return BLL_TrackingRecord.Instance.SetTrackingRecordReadById(setTrackingRecordReadByIdIn);
        }
        
        /// <summary>
        /// 添加跟踪记录信息
        /// </summary>
        /// <param name="addTrackingRecordIn"></param>
        /// <returns></returns>
        [HttpPost]
        public AddTrackingRecord_Out AddTrackingRecord([FromForm] AddTrackingRecord_In addTrackingRecordIn)
        {
            return BLL_TrackingRecord.Instance.AddTrackingRecord(addTrackingRecordIn);
        }

        /// <summary>
        /// 修改跟踪记录信息，验证是否本人创建的跟踪记录。
        /// </summary>
        /// <param name="updateTrackingRecordIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdateTrackingRecord_Out UpdateTrackingRecord([FromForm] UpdateTrackingRecord_In updateTrackingRecordIn)
        {
            //验证Id是否为空
            if (updateTrackingRecordIn == null || string.IsNullOrEmpty(updateTrackingRecordIn.Id))
                throw new ApiException("主键不可为空");
            return BLL_TrackingRecord.Instance.UpdateTrackingRecord(updateTrackingRecordIn);
        }
        
        /// <summary>
        /// 删除跟踪记录信息，验证是否本人创建的跟踪记录，多条记录全部执行成功则返回成功，否则返回失败。只做逻辑删除，修改Deleted字段为1。
        /// </summary>
        /// <param name="deleteTrackingRecordIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public DeleteTrackingRecord_Out DeleteTrackingRecord(DeleteTrackingRecord_In deleteTrackingRecordIn)
        {
            if (deleteTrackingRecordIn == null || string.IsNullOrEmpty(deleteTrackingRecordIn.Ids))
                throw new ApiException("主键不可为空");
            return BLL_TrackingRecord.Instance.DeleteTrackingRecord(deleteTrackingRecordIn);
        }

        /// <summary>
        /// 根据查询条件获取跟踪记录汇总信息列表
        /// </summary>
        /// <param name="summaryTrackingRecordListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SummaryTrackingRecordList_Out> SummaryTrackingRecordList(
            SummaryTrackingRecordList_In summaryTrackingRecordListIn)
        {
            int total = 0;
            var list = BLL_TrackingRecord.Instance.SummaryTrackingRecordList(summaryTrackingRecordListIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 导出跟踪记录汇总信息文件
        /// </summary>
        /// <param name="downloadSummaryTrackingRecordIn"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DownloadSummaryTrackingRecord(DownloadSummaryTrackingRecord_In downloadSummaryTrackingRecordIn)
        {
            return BLL_TrackingRecord.Instance.DownloadSummaryTrackingRecord(downloadSummaryTrackingRecordIn,Response);
        }
        
        /// <summary>
        /// 根据员工（用户）表主键Id获取跟踪记录信息
        /// </summary>
        /// <param name="getTrackingRecordByUserIdIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public List<GetTrackingRecordByUserId_Out> GetTrackingRecordByUserId(GetTrackingRecordByUserId_In getTrackingRecordByUserIdIn)
        {
            if (getTrackingRecordByUserIdIn == null || string.IsNullOrEmpty(getTrackingRecordByUserIdIn.UserId))
                throw new ApiException("主键不可为空");
            return DbOpe_crm_trackingrecord.Instance.GetTrackingRecordByUserId(getTrackingRecordByUserIdIn);
        }
        
        /// <summary>
        /// 根据查询条件查询合同列表
        /// </summary>
        /// <param name="searchContractInfoIn"></param>
        /// <returns></returns>
        [HttpPost]
        public List<TrackingRecord_ContractInfo> SearchContractInfo(TrackingRecord_SearchContractInfoIn searchContractInfoIn)
        {
            if (searchContractInfoIn is null)
            {
                throw new ApiException("查询条件不可为空!"); 
            }
            return DbOpe_crm_trackingrecord.Instance.SearchContractInfo(searchContractInfoIn);
        }

        /// <summary>
        /// 获取追踪记录发送人(树形结构)
        /// </summary>
        /// <returns></returns>
        [HttpGet, SkipRightCheck]
        public List<GetOrganizationWithUserTree_Out> GetTrackingReportPoster()
        {
            return BLL_TrackingRecord.Instance.GetTrackingReportPoster();
        }
    }
}