﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using CRM2_API.Common.Filter;
using static CRM2_API.Common.Filter.WorkLog;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Common.Cache;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 组织控制器
    /// </summary>
    [Description("组织控制器")]
    public class OrganizationController : MyControllerBase
    {
        public OrganizationController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 添加组织信息
        /// </summary>
        /// <param name="addOrg_In"></param>
        [HttpPost, PreLog]
        public void AddOrganization(AddOrganization_In addOrg_In)
        {
            BLL_Organization.Instance.AddOrganization(addOrg_In);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }

        /// <summary>
        /// 修改组织信息
        /// </summary>
        /// <param name="updOrg_In"></param>
        [HttpPost, PreLog]
        public void UpdateOrganization(UpdateOrganization_In updOrg_In)
        {
            BLL_Organization.Instance.UpdateOrganization(updOrg_In);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }
        /// <summary>
        /// 删除组织信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost, PreLog]
        public void DeleteOrganization(string Ids)
        {
            BLL_Organization.Instance.DeleteOrganization(Ids);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }
        /// <summary>
        /// 修改组织状态信息
        /// </summary>
        /// <param name="updOrgStatus_In"></param>
        [HttpPost, PreLog]
        public void UpdateOrganizationStatus(UpdateOrganizationStatus_In updOrgStatus_In)
        {
            BLL_Organization.Instance.UpdateOrgStatus(updOrgStatus_In);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }
        /// <summary>
        /// 检查组织下是否存在启用状态的员工信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost]
        public List<CheckOrgEnableUser_Out> CheckOrganizationEnableUser(string Ids)
        {
            return BLL_Organization.Instance.CheckOrganizationEnableUser(Ids);
        }

        /// <summary>
        /// 组织合并
        /// </summary>
        /// <param name="mergeOrg_In"></param>
        [HttpPost, PreLog]
        public void MergeOrganization(MergeOrganization_In mergeOrg_In)
        {
            BLL_Organization.Instance.MergeOrganization(mergeOrg_In.Id, mergeOrg_In.mergeId);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }

        /// <summary>
        /// 组织插入
        /// </summary>
        /// <param name="insOrg_In"></param>
        [HttpPost]
        public void InsertOrganization(InsertOrganization_In insOrg_In)
        {
            BLL_Organization.Instance.InsertOrganization(insOrg_In.Id, insOrg_In.parentId);
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }

        /// <summary>
        /// 根据组织Id获取组织信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        public GetOrganizationById_Out GetOrganizationById(string Id)
        {
            if (string.IsNullOrEmpty(Id))
                throw new ApiException("未选择组织");
            return DbOpe_sys_organization.Instance.GetOrganizationById(Id);
        }

        /// <summary>
        /// 根据查询条件获取组织信息列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchOrganizationList_Out> SearchOrganizationList(SearchOrganizationList_In searchSysOrg_In)
        {
            int total = 0;
            var list = DbOpe_sys_organization.Instance.SearchOrganizationList(searchSysOrg_In, ref total);
            return GetApiTableOut(list, total);
        }


        /// <summary>
        /// 根据组织Id获取下级组织信息列表
        /// </summary>
        /// <param name="getSubOrg_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetSubOrganizationById_Out> GetSubOrganizationById(GetSubOrganizationById_In getSubOrg_In)
        {
            int total = 0;
            var list = DbOpe_sys_organization.Instance.GetSubOrganizationById(getSubOrg_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据团队类型获取组织信息列表
        /// </summary>
        /// <param name="getOrgByType_In"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetOrganizationByOrgType_Out> GetOrganizationByOrgType(GetOrganizationByOrgType_In getOrgByType_In)
        {
            return BLL_Organization.Instance.GetOrganizationByOrgType(getOrgByType_In);
        }

        /// <summary>
        /// 根据组织Id获取当前组织下员工信息列表
        /// </summary>
        /// <param name="getOrgUserById_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetOrganizationUserById_Out> GetOrganizationUserById(GetOrganizationUserById_In getOrgUserById_In)
        {
            int total = 0;
            var list = DbOpe_sys_user.Instance.GetOrganizationUserById(getOrgUserById_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 获取系统中的组织机构树
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<GetOrganizationTree_Out> GetOrganizationTree()
        {
            return DbOpe_sys_organization.Instance.GetOrganizationTree();
        }

        /// <summary>
        /// 获取系统中的组织机构[携带用户]树
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<GetOrganizationWithUserTree_Out> GetOrganizationWithUserTree(GetOrganizationWithUserTree_In? organizationWithUserTree_In)
        {
            if (organizationWithUserTree_In is null)
            {
                organizationWithUserTree_In = new GetOrganizationWithUserTree_In();
            }
            return BLL_Organization.Instance.GetOrganizationWithUserTree(organizationWithUserTree_In.useUserStatus, organizationWithUserTree_In.allowAllUser);
        }

        /// <summary>
        /// 获取传入组织Id所在分支的树结构
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        public List<SysOrganizationTree> GetChildOrgList(string Id)
        {
            return DbOpe_sys_organization.Instance.GetBranchOrgList(Id);
        }

        /// <summary>
        /// 获取登录人的当前组织及下级组织树结构
        /// </summary>
        /// <returns></returns>
        [HttpPost, PreLog]
        public List<SysOrganizationTree> GetSelfAndChildOrgList()
        {
            return BLL_Organization.Instance.GetSelfAndChildOrgList();
        }

        /// <summary>
        /// 获取录人的当前组织及下级组织[携带用户]树
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<GetOrganizationWithUserTree_Out> GetSelfAndChildOrgWithUserTree()
        {
            return BLL_Organization.Instance.GetSelfAndChildOrgWithUserTree();
        }


        /// <summary>
        /// 获取录人的当前组织[携带用户]及下级组织[仅携带队长]树
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<GetOrganizationWithUserTree_Out> GetSelfOrgWithUserAndChildOrgWithManagerTree()
        {
            return BLL_Organization.Instance.GetSelfOrgWithUserAndChildOrgWithManagerTree();
        }

        /// <summary>
        /// 员工管理页面用 获取系统中的组织机构[携带用户(含组织名称及可保留客户数)]树
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<GetOrganizationWithUserTree_Out> GetOrgTreeWithUserMaxSaveCustomer()
        {
            return BLL_Organization.Instance.GetOrgTreeWithUserMaxSaveCustomer();
        }

        /// <summary>
        /// 获取当前登录用户所在分支的树结构
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<SysOrganizationTree> GetCurrentUserChildOrgList()
        {
            return DbOpe_sys_organization.Instance.GetCurrentUserChildOrgList();
        }

        /// <summary>
        /// 根据组织Id获取日志记录
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetOrganizationLogById_Out> GetOrganizationLogById(string orgId)
        {
            return DbOpe_sys_operation_org_log.Instance.GetOrganizationLogById(orgId);
        }
        [HttpPost, SkipAuthCheck, SkipRecordLog, SkipRSAKey, SkipIPCheck]
        public void ExcuteOrgChange()
        {
            BLL_Organization.Instance.ExcuteOrgChange();
            // 清除用户与组织缓存
            RedisCache.UserWithOrg.InitializeCache();
        }
    }
}
