﻿using Aspose.Pdf;
using SqlSugar;

namespace CRM2_API.DAL.DbCommon
{
    /// <summary>
    /// 通用模型操作文件
    /// </summary>
    /// <typeparam name="DbModel"></typeparam>
    /// <typeparam name="Db<PERSON>perate"></typeparam>
    public class IDbOperate<DbModel, DbOperate> where DbModel : class, new() where DbOperate : class, new()
    {
        private static AsyncLocal<DbOperate> _Instance = new AsyncLocal<DbOperate>();
        /// <summary>
        /// 获取操作数据库实例
        /// </summary>
        public static DbOperate Instance
        {
            get
            {
                if (_Instance.Value is null)
                    _Instance.Value = new DbOperate();
                return _Instance.Value;
            }
        }
        /// <summary>
        /// 操作的数据库
        /// </summary>
        protected virtual SqlSugarScope Db { get; }
        /// <summary>
        /// 查询对象
        /// </summary>
        protected ISugarQueryable<DbModel> Queryable => Db.Queryable<DbModel>();
        /// <summary>
        /// 更新对象
        /// </summary>
        protected IUpdateable<DbModel> Updateable => Db.Updateable<DbModel>();

        /// <summary>
        /// 批量更新对象
        /// </summary>
        protected IUpdateable<List<DbModel>> UpdateableList => Db.Updateable<List<DbModel>>();

        #region Query查询操作
        /// <summary>
        /// 根据主键查询
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public DbModel QueryByPrimaryKey(string id) => Db.Queryable<DbModel>().InSingle(id);
        /// <summary>
        /// 获取表总行数
        /// </summary>
        /// <param name="model"></param>
        /// <returns>总行数</returns>
        public int GetQueryCount(DbModel model) => Db.Queryable<DbModel>().Count();
        #endregion

        #region Insert通用操作
        #region Insert执行
        /// <summary>
        /// 单个对象插入
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int Insert(DbModel model) => Db.Insertable(model).ExecuteCommand();
        //public int Insert(DbModel model) { return 1; }
        /// <summary>
        /// 批量插入对象
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public int Insert(List<DbModel> models) => Db.Insertable(models).ExecuteCommand();
        /// <summary>
        /// 批量插入对象
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public int Insert(DbModel[] models) => Db.Insertable(models).ExecuteCommand();
        #endregion
        #region Insert加入队列
        /// <summary>
        /// 单个对象插入
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public void InsertQueue(DbModel model) => Db.Insertable(model).AddQueue();
        /// <summary>
        /// 批量插入对象
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public void InsertQueue(List<DbModel> models) => Db.Insertable(models).AddQueue();
        /// <summary>
        /// 批量插入对象
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public void InsertQueue(DbModel[] models) => Db.Insertable(models).AddQueue();
        #endregion
        #endregion

        #region Update通用操作
        #region Update执行
        /// <summary>
        /// 单个对象更新(根据主键)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int Update(DbModel model) => Db.Updateable(model).ExecuteCommand();
        /// <summary>
        /// 批量更新(根据主键)
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public int Update(List<DbModel> models) => Db.Updateable(models).ExecuteCommand();
        /// <summary>
        /// 批量更新(根据主键)
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public int Update(DbModel[] models) => Db.Updateable(models).ExecuteCommand();
        #endregion
        #region Update加入队列
        /// <summary>
        /// 单个对象更新(根据主键)
        /// </summary>
        /// <param name="model"></param>
        public void UpdateQueue(DbModel model) => Db.Updateable(model).AddQueue();
        /// <summary>
        /// 批量更新(根据主键)
        /// </summary>
        /// <param name="models"></param>
        public void UpdateQueue(List<DbModel> models) => Db.Updateable(models).AddQueue();
        /// <summary>
        /// 批量更新(根据主键)
        /// </summary>
        /// <param name="models"></param>
        public void UpdateQueue(DbModel[] models) => Db.Updateable(models).AddQueue();
        #endregion
        #endregion

        #region Delete通用方法
        /// <summary>
        /// 删除对象(根据主键)
        /// </summary>
        /// <param name="model"></param>
        /// <returns>受影响行数</returns>
        public int Delete(DbModel model) => Db.Deleteable(model).ExecuteCommand();
        /// <summary>
        /// 
        /// </summary>
        /// <param name="models"></param>
        /// <returns>受影响行数</returns>
        public int Delete(List<DbModel> models) => Db.Deleteable(models).ExecuteCommand();
        #endregion



        #region 其他通用方法
        /// <summary>
        /// 事务处理
        /// </summary>
        /// <param name="transDoing">事务中要做的操作,其中的数据库操作，不需要调用加入队列的方法</param>
        public void TransDeal(Action transDoing)
        {
            using (var uow = Db.CreateContext(Db.Ado.IsNoTran()))
            {
                try
                {
                    transDoing();
                    uow.Commit();
                }
                catch
                {
                    // 工作单元会自动回滚，无需显式调用
                    throw;
                }
            }
        }
        /// <summary>
        /// <para>执行队列中所有sql</para>
        /// <para>多个表的队列，只调用一次，所有队列都会一起提交</para>
        /// </summary>
        /// <returns>受影响行数</returns>
        public int SaveQueues() => Db.SaveQueues();
        #endregion
    }
}
