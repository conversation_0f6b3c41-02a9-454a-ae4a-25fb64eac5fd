﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using System.Security.Policy;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;

namespace CRM2_API.BLL
{
    public class BLL_ContractServiceOtherData : BaseBLL<BLL_ContractServiceOtherData>
    {

        /// <summary>
        /// 审核合同服务信息其他数据申请信息
        /// </summary>
        /// <param name="audit_In"></param>
        public void AuditContractProductServiceInfoOtherDataAppl(AuditContractProductServiceInfoOtherDataAppl_In audit_In)
        {
            //获取申请的数据
            var apply = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.QueryByPrimaryKey(audit_In.ProductServiceInfoOtherDataApplId);
            #region 判断是否可以进行登记操作
            /*获取当前申请绑定的服务数据，如果没有绑定服务数据&&申请处在提交状态--可以登记; 如果绑定了服务数据且服务数据是被驳回状态--可以登记; 其余情况不可进行登记*/
            bool couldRegistered = false;
            var curService = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetData(g => g.ProductServiceInfoOtherDataApplId == apply.Id && g.IsHistory == false);
            if (curService == null && apply.State == EnumProcessStatus.Submit.ToInt())
                couldRegistered = true;
            else if (curService != null && (curService.State == EnumContractServiceState.RETURNED || curService.State == EnumContractServiceState.TO_BE_OPENED))
                couldRegistered = true;
            if (!couldRegistered)
                throw new ApiException("当前申请无法进行初审操作");
            #endregion

            //开通的workflow记录
            var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(apply.ContractId);

            //如果登记人员执行拒绝操作，修改申请数据apply的状态为拒绝，如果是服务变更数据，对已经锁定的旧数据放开锁定
            if (!audit_In.State)
            {

                //放开被变更的服务数据可操作
                var curOpenService = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetData(g => (g.State == EnumContractServiceState.VALID || g.State == EnumContractServiceState.OUT) && g.ContractProductInfoId == apply.ContractProductInfoId);
                if (curOpenService != null)
                {
                    var currentAppl = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetDataById(curOpenService.ProductServiceInfoOtherDataApplId);
                    currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(currentAppl);
                }
                apply.State = EnumProcessStatus.Refuse.ToInt();
                apply.ReviewerDate = DateTime.Now;
                apply.ReviewerId = UserId;
                apply.UpdateDate = DateTime.Now;
                apply.UpdateUser = UserId;
                apply.Feedback = audit_In.FeedBack;
                if (apply.ProcessingType == (int)EnumProcessingType.Change)
                    //拒绝后该数据状态为无效
                    apply.IsInvalid = (int)EnumIsInvalid.Invalid;
                //apply.CodesNum = audit_In.CodesNum;
                DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply);

                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = apply.CreateUser;
                message.MessageTypeToId = apply.Id;
                message.MessagemMainAboutDes = contract.ContractName;
                message.LocalFeedBack = apply.Feedback;
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.OtherService, EnumMessageStateInfo.Refus, apply.ContractId);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            }
            //如果初审登记通过，需要验证合同信息，判断合同是否到账
            else
            {
                var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
                if (contractInfo == null)
                    throw new ApiException("未找到合同信息");
                if (string.IsNullOrEmpty(contractInfo.ContractNum))
                    throw new ApiException("未到账合同不能进行登记操作");
            }

            var now = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply);
            var servOtherDataInfo = audit_In.MappingTo<Db_crm_contract_serviceinfo_otherdata>();
            servOtherDataInfo.Id = Guid.NewGuid().ToString();
            servOtherDataInfo.ContractId = apply.ContractId;
            servOtherDataInfo.ProductId = apply.ProductId;
            servOtherDataInfo.ContractProductInfoId = apply.ContractProductInfoId;
            servOtherDataInfo.State = audit_In.State ? EnumContractServiceState.TO_BE_REVIEW : EnumContractServiceState.REFUSE;
            servOtherDataInfo.Deleted = false;
            servOtherDataInfo.IsChanged = false;
            servOtherDataInfo.CreateDate = DateTime.Now;
            servOtherDataInfo.CreateUser = UserId;
            servOtherDataInfo.RegisteredId = UserId;
            servOtherDataInfo.RegisteredTime = now;
            servOtherDataInfo.RegisteredRemark = audit_In.FeedBack;
            servOtherDataInfo.IsHistory = false;
            servOtherDataInfo.ProcessingType = apply.ProcessingType;
            servOtherDataInfo.ServiceMonth = audit_In.ServiceMonth;
            //判断是否是被驳回数据
            if (curService != null)
            {
                //如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
                curService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(curService);
                //被驳回服务重新初审通过时，删除被驳回服务的到账绑定关系
                DbOpe_crm_contract_receiptregister_service.Instance.DeleteReturnServiceLinkDataQueue(curService.Id);
            }
            DbOpe_crm_contract_serviceinfo_otherdata.Instance.InsertQueue(servOtherDataInfo);

            if (audit_In.State)
            {
                var itemList = new List<Db_crm_contract_serviceinfo_otherdata_items>();
                //循环插入其他数据代码
                audit_In.HSCodeItems.ForEach(item =>
                {
                    var otherDataItem = item.MappingTo<Db_crm_contract_serviceinfo_otherdata_items>();
                    otherDataItem.Id = Guid.NewGuid().ToString();
                    otherDataItem.ContractServiceInfoOtherDataId = servOtherDataInfo.Id;
                    otherDataItem.Deleted = false;
                    otherDataItem.CreateUser = UserId;
                    otherDataItem.CreateDate = now;
                    itemList.Add(otherDataItem);
                });
                DbOpe_crm_contract_serviceinfo_otherdata_items.Instance.InsertListData(itemList);
                //绑定到账信息
                if (audit_In.ReceiptRegisterIds != null && audit_In.ReceiptRegisterIds.Count > 0)
                {
                    DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptDataQueue(audit_In.ReceiptRegisterIds, servOtherDataInfo.Id, EnumProductType.Other);
                }
            }
            string dataState = String.Empty;
            if (audit_In.State)
                dataState = EnumContractServiceOpenState.ToBeReview.GetEnumDescription();
            else
                dataState = EnumContractServiceOpenState.Refuse.GetEnumDescription();
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_otherdata, Db_crm_contract>("其他数据服务审批流程", apply.Id, servOtherDataInfo, contract, audit_In.FeedBack, dataState, "初审");
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.SaveQueues();
        }

        public void ReviewContractProductServiceInfoOtherDataAppl(ReviewContractProductServiceInfoOtherDataAppl_In review_In)
        {
            //根据ProductServiceInfoDBApplId获取申请表信息
            var apply = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.QueryByPrimaryKey(review_In.ProductServiceInfoOtherDataApplId);
            //根据ProductServiceInfoDBApplId获取服务表信息
            var curService = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetData(g => g.ProductServiceInfoOtherDataApplId == apply.Id && g.IsHistory == false);

            //开通的workflow记录
            var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(apply.ContractId);
            #region 判断是否可以进行复核操作
            /*如果存在绑定服务数据&&服务数据为待复核状态--可以复核;其余情况不可进行复核*/
            if (!(curService != null && curService.State == EnumContractServiceState.TO_BE_REVIEW))
                throw new ApiException("当前申请无法进行复核操作");
            #endregion
            var now = DateTime.Now;
            //驳回
            if (!review_In.State)
                curService.State = EnumContractServiceState.RETURNED;
            else
            {
                //合同信息验证
                var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
                if (contractInfo == null)
                    throw new ApiException("未找到合同信息");
                if (string.IsNullOrEmpty(contractInfo.ContractNum))
                    throw new ApiException("未到账合同不能开通服务产品");
                //处理申请信息
                apply.State = EnumProcessStatus.Pass.ToInt();
                apply.ReviewerDate = now;
                apply.ReviewerId = UserId;
                apply.UpdateDate = now;
                apply.UpdateUser = UserId;
                apply.Feedback = review_In.FeedBack;
                DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply);
                //服务状态置位生效
                curService.State = EnumContractServiceState.VALID;
                //判断 ProcessingType
                //var origContacts = new List<Db_crm_contract_serviceinfo_college_contacts>();
                if (curService.ProcessingType == EnumProcessingType.Change.ToInt())
                {//服务申请修改 需要找到当前的服务记录数据(根据ContractProductInfoId)，修改isChanged=true，补充ChangedId,当前数据补充HistoryId
                 //获取原服务数据进行修改
                    var originalServe = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetServiceInfoByConProInfoId(apply.ContractProductInfoId);
                    originalServe.IsChanged = true;
                    originalServe.ChangedId = curService.Id;
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    originalServe.State = EnumContractServiceState.INVALID;
                    DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(originalServe);
                    //变更服务补充HistoryId
                    curService.HistoryId = originalServe.Id;
                }

                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = apply.CreateUser;
                message.MessageTypeToId = apply.Id;
                message.MessagemMainAboutDes = contract.ContractName;
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.OtherService, EnumMessageStateInfo.Pass, apply.ContractId);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            }
            curService.ReviewerId = UserId;
            curService.ReviewerTime = now;
            curService.ReviewerRemark = review_In.ReviewerRemark;
            curService.UpdateDate = now;
            curService.UpdateUser = UserId;
            curService.Remark = review_In.FeedBack;
            DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(curService);
            string dataState = String.Empty;
            if (review_In.State)
                dataState = EnumContractServiceOpenState.Open.GetEnumDescription();
            else
                dataState = EnumContractServiceOpenState.Returned.GetEnumDescription();
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_otherdata, Db_crm_contract>("其他数据服务审批流程", apply.Id, curService, contract, apply.ApplicantId, curService.Remark, dataState, "复核");
            #region 2024.3.19改为所有流转记录都保存审核反馈，这段内容注释
            /*string dataState = String.Empty;
            string remark = String.Empty;
            if (review_In.State)
            {
                dataState = EnumContractServiceOpenState.Open.GetEnumDescription();
                remark = curService.Remark;

                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_otherdata, Db_crm_contract>("其他数据服务审批流程", apply.Id, curService, contract, apply.ApplicantId, remark, dataState, "复核");
            }
            else
            {
                dataState = EnumContractServiceOpenState.Returned.GetEnumDescription();
                remark = curService.ReviewerRemark;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_otherdata, Db_crm_contract>("其他数据服务审批流程", apply.Id, curService, contract, remark, dataState, "复核");
            }*/
            #endregion
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.SaveQueues();
            //11.20 开通服务要刷新合同的保护截止日 复核用
            if (review_In.State)
                DbOpe_crm_contract.Instance.RefreshContractProtectDate(apply.ContractId);
        }

        /// <summary>
        /// 根据申请id获取合同服务申请信息_其他数据信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public GetContractServiceInfoApplyInfoOtherDataByApplId_Out GetContractServiceInfoApplyInfoOtherDataByApplId(string Id)
        {
            //获取申请详细信息
            var apply = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.QueryByPrimaryKey(Id).MappingTo<GetContractServiceInfoApplyInfoOtherDataByApplId_Out_Mid>();
            //获取合同详细信息
            apply.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
            //到账备注列表
            apply.ReceiptRemarks = DbOpe_crm_contract_receiptregister.Instance.GetRemarksByContractId(apply.ContractId);

            //获取产品详细信息
            apply.ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(apply.ContractProductInfoId).MappingTo<GetContractServiceApplInfoOtherDataByApplId_Out_ProductInfo>();
            //获取复核时或非首次初审时查看的信息
            apply.ReviewInfo = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetReviewInfo(Id);
            //复核备注&审核反馈
            apply.ReviewFeedbacks = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetReviewFeedbacks(Id);
            //编码数据
            var hsCodesInfo = DbOpe_crm_contract_productserviceinfo_otherdata_appl_hscode.Instance.GetHSCodeListByApplyId(Id);
            hsCodesInfo.ForEach(item =>
            {
                var matchData = GetMatchingData(new GetOtherDataMatchingData_In
                {
                    SID = item.SID,
                    HSCode = item.HSCode,
                    IAEType = item.IAEType,
                    TimePeriodStart = item.TimePeriodStart,
                    TimePeriodEnd = item.TimePeriodEnd
                });
                item.ActualTimePeriod = matchData.ActualTimePeriod;
                item.ActualMatchingNum = matchData.ActualMatchingNum;
                item.PlannedTimePeriod = matchData.PlannedTimePeriod;
                item.PlannedMatchingNum = matchData.PlannedMatchingNum;
            });
            apply.HSCodesInfo = hsCodesInfo;
            //合同的所有到账信息
            apply.ReceiptRegisterCollectionList = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(apply.ContractId, String.Empty, EnumProductType.Other, apply.ReviewInfo == null ? null : apply.ReviewInfo.Id);
            //当前服务已绑定的到账信息
            if (apply.ReviewInfo != null)
                apply.LinkedReceiptRegisterIds = DbOpe_crm_contract_receiptregister_service.Instance.GetReceiptRegisterIdsByServiceId(apply.ReviewInfo.Id);
            var retApply = apply.MappingTo<GetContractServiceInfoApplyInfoOtherDataByApplId_Out>();
            return retApply;
        }

        public GetOtherDataMatchingData_Out GetMatchingData(GetOtherDataMatchingData_In match_In)
        {
            var retObj = new GetOtherDataMatchingData_Out();
            var TimePeriods = new List<string>();
            if (match_In.SID == null || match_In.HSCode.IsNullOrEmpty() || match_In.IAEType == null || match_In.TimePeriodStart == null || match_In.TimePeriodEnd == null)
                return retObj;
            for (int i = match_In.TimePeriodStart.Value.ToString("yyyyMM").ToInt(); i <= match_In.TimePeriodEnd.Value.ToString("yyyyMM").ToInt(); i++)
                TimePeriods.Add(i.ToString());
            var list = DbOpe_sys_hscode.Instance.GetMatchingData(match_In.SID.Value, match_In.HSCode, match_In.IAEType.Value, TimePeriods);
            var ActualTimePeriodList = list.Where(e => e.ActualTimePeriod.IsNotNullOrEmpty()).Select(e => e.ActualTimePeriod).ToList();
            var PlannedTimePeriodList = list.Where(e => e.PlannedTimePeriod.IsNotNullOrEmpty()).Select(e => e.PlannedTimePeriod).ToList();

            retObj.ActualTimePeriod = SplitTimePeriodByList(ActualTimePeriodList);
            retObj.PlannedTimePeriod = SplitTimePeriodByList(PlannedTimePeriodList);
            retObj.ActualMatchingNum = ActualTimePeriodList.Count;
            retObj.PlannedMatchingNum = PlannedTimePeriodList.Count;
            return retObj;
        }

        private string SplitTimePeriodByList(List<string> TimePeriodList)
        {
            List<string> retPeriodList = new List<string>();
            if (TimePeriodList != null && TimePeriodList.Count > 0)
            {
                List<DateTime> ActualTimePeriods = TimePeriodList.Select(r => DateTime.ParseExact(r, "yyyyMM", System.Globalization.CultureInfo.CurrentCulture)).OrderBy(r => r).ToList();
                int ActualTimePeriodsCount = ActualTimePeriods.Count;
                if (ActualTimePeriodsCount == 1)
                {
                    return TimePeriodList.JoinToString(",");
                }
                for (int i = 0; i < ActualTimePeriodsCount; i++)
                {
                    DateTime ActualTimePeriodStart = ActualTimePeriods[i];
                    DateTime CurrentTimePeriod = ActualTimePeriods[i];
                    for (int j = i + 1; j < ActualTimePeriodsCount; j++)
                    {
                        if (CurrentTimePeriod.AddMonths(1) == ActualTimePeriods[j])
                        {
                            CurrentTimePeriod = ActualTimePeriods[j];
                            if (ActualTimePeriodsCount == (j + 1))
                            {
                                if (ActualTimePeriodStart.AddMonths(1) == CurrentTimePeriod)
                                {
                                    retPeriodList.Add(ActualTimePeriodStart.ToString("yyyyMM"));
                                    retPeriodList.Add(CurrentTimePeriod.ToString("yyyyMM"));
                                }
                                else
                                {
                                    if (ActualTimePeriodStart == CurrentTimePeriod)
                                    {
                                        retPeriodList.Add(ActualTimePeriodStart.ToString("yyyyMM"));
                                        if (ActualTimePeriodsCount == (j + 1))
                                        {
                                            retPeriodList.Add(ActualTimePeriods[j].ToString("yyyyMM"));
                                        }
                                    }
                                    else
                                    {
                                        retPeriodList.Add(ActualTimePeriodStart.ToString("yyyyMM") + "-" + CurrentTimePeriod.ToString("yyyyMM"));
                                    }
                                }
                            }
                            i = j - 1;
                        }
                        else
                        {
                            if (ActualTimePeriodStart.AddMonths(1) == CurrentTimePeriod)
                            {
                                retPeriodList.Add(ActualTimePeriodStart.ToString("yyyyMM"));
                                retPeriodList.Add(CurrentTimePeriod.ToString("yyyyMM"));
                            }
                            else
                            {
                                if (ActualTimePeriodStart == CurrentTimePeriod)
                                {
                                    retPeriodList.Add(ActualTimePeriodStart.ToString("yyyyMM"));
                                    if (ActualTimePeriodsCount == (j + 1))
                                    {
                                        retPeriodList.Add(ActualTimePeriods[j].ToString("yyyyMM"));
                                    }
                                }
                                else
                                {
                                    retPeriodList.Add(ActualTimePeriodStart.ToString("yyyyMM") + "-" + CurrentTimePeriod.ToString("yyyyMM"));
                                    if (ActualTimePeriodsCount == (j + 1))
                                    {
                                        retPeriodList.Add(ActualTimePeriods[j].ToString("yyyyMM"));
                                    }
                                }
                            }
                            i = j - 1;
                            break;
                        }
                    }
                }
            }
            return retPeriodList.JoinToString(",");
        }



        /// <summary>
        /// 撤销合同服务信息其他数据申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        /// <exception cref="ApiException"></exception>
        public void RevokeContractProductServiceInfoOtherDataAudit(OperateContractProductServiceInfoOtherDataAudit operate_In)
        {
            //声明可撤销状态的集合, 待复核、被驳回、拒绝、已开通状态的数据可以进行撤销操作
            var couldRevokeOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.ToBeReview, EnumContractServiceOpenState.Returned, EnumContractServiceOpenState.Refuse, EnumContractServiceOpenState.Open };
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetServiceOtherDataApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以撤销
            if (!couldRevokeOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可撤销");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetServiceInfoByApplyId(operate_In);
            var now = DateTime.Now;
            //待复核状态撤销到待开通/待变更状态：申请数据不变，服务数据状态改为TO_BE_OPENED(10)
            if (apply.OpenState == EnumContractServiceOpenState.ToBeReview)
            {
                //服务数据状态改为TO_BE_OPENED(10)
                service.State = EnumContractServiceState.TO_BE_OPENED;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(service);
            }
            //被驳回状态撤销到待复核状态：申请数据不变，服务数据状态改为TO_BE_REVIEW(20)
            else if (apply.OpenState == EnumContractServiceOpenState.Returned)
            {
                //服务数据状态改为TO_BE_REVIEW(20)
                service.State = EnumContractServiceState.TO_BE_REVIEW;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(service);
            }
            //拒绝状态撤销到待开通/待变更状态：申请数据状态改为EnumProcessStatus.Submit(1),其他申请置位失效
            else if (apply.OpenState == EnumContractServiceOpenState.Refuse)
            {
                //申请数据状态改为EnumProcessStatus.Submit(1)
                apply.State = EnumProcessStatus.Submit.ToInt();
                apply.UpdateDate = now;
                apply.UpdateUser = UserId;
                DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_otherdata_appl>());
                //服务数据状态改为TO_BE_OPENED(10)
                service.State = EnumContractServiceState.TO_BE_OPENED;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(service);
                //其他的申请要置为无效，只保留当前操作的这个申请
                var otherAppls = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetDataList(g => g.ContractProductInfoId == apply.ContractProductInfoId && g.Id != operate_In.ApplyId);
                otherAppls.ForEach(a =>
                {
                    DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateData(r => new Db_crm_contract_productserviceinfo_otherdata_appl { IsInvalid = (int)EnumIsInvalid.Invalid }, r => a.Id == r.Id);
                });
            }
            //已开通状态撤销到待复核状态
            else if (apply.OpenState == EnumContractServiceOpenState.Open)
            {
                //申请数据状态改为EnumProcessStatus.Submit(1)
                apply.State = EnumProcessStatus.Submit.ToInt();
                apply.UpdateDate = now;
                apply.UpdateUser = UserId;
                //服务状态变更为待复核EnumContractServiceState.TO_BE_REVIEW(20)
                DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_otherdata_appl>());
                service.State = EnumContractServiceState.TO_BE_REVIEW;
                service.UpdateDate = now;
                service.UpdateUser = UserId;
                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(service);
                //其他的申请要置为无效，只保留当前操作的这个申请
                var otherAppls = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetDataList(g => g.ContractProductInfoId == apply.ContractProductInfoId && g.Id != operate_In.ApplyId);
                otherAppls.ForEach(a =>
                {
                    DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateData(r => new Db_crm_contract_productserviceinfo_otherdata_appl { IsInvalid = (int)EnumIsInvalid.Invalid }, r => a.Id == r.Id);
                });
                //如果是服务变更，原服务恢复正常
                if (apply.ProcessingType == EnumProcessingType.Change.ToInt())
                {
                    var originalServe = DbOpe_crm_contract_serviceinfo_otherdata.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    originalServe.IsChanged = false;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(originalServe);
                }
            }
            //workflow撤销
            string state = ((EnumContractServiceOpenState)apply.OpenState).GetEnumDescription();
            BLL_WorkFlow.Instance.CancelWorkflowPending("其他数据服务审批流程", apply.Id, state, apply);
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.SaveQueues();
            #region 原撤销流程 2023.12.13注释

            /*
                        //获取申请数据
                        var apply = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
                        //待开通(申请为Submit)状态的数据不可撤销
                        if (apply.State == EnumProcessStatus.Submit.ToInt())
                            throw new ApiException("所选的申请不可撤销");
                        //获取服务数据
                        var service = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetServiceInfoByApplyId(operate_In);
                        //若存在服务，过期(服务为过期)、作废(服务为作废或失效)状态的数据不可撤销
                        if (service != null && (service.State == EnumContractServiceState.INVALID || service.State == EnumContractServiceState.VOID || service.State == EnumContractServiceState.OUT))
                            throw new ApiException("所选的申请不可撤销");
                        //申请的状态改为待开通
                        apply.State = EnumProcessStatus.Submit.ToInt();
                        apply.UpdateUser = UserId;
                        apply.UpdateDate = DateTime.Now;
                        DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply);
                        if (service != null)
                        {
                            //服务的状态改为作废
                            //service.State = EnumContractServiceState.VOID;
                            service.Deleted = true;
                            service.UpdateUser = UserId;
                            service.UpdateDate = DateTime.Now;
                            DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(service);
                            //如果是服务变更，原服务恢复正常
                            if (!string.IsNullOrEmpty(service.HistoryId))
                            {
                                var originalServe = DbOpe_crm_contract_serviceinfo_otherdata.Instance.QueryByPrimaryKey(service.HistoryId);
                                originalServe.ChangedId = "";
                                originalServe.IsChanged = false;
                                originalServe.UpdateDate = DateTime.Now;
                                originalServe.UpdateUser = UserId;
                                if (originalServe.ServiceCycleEnd < DateTime.Now)
                                    originalServe.State = EnumContractServiceState.OUT;
                                else
                                    originalServe.State = EnumContractServiceState.VALID;
                                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(originalServe);
                            }
                            //workflow撤销
                            BLL_WorkFlow.Instance.CancelWorkflowPending("其他数据服务审批流程", service.Id);
                        }

                        //提交sql队列
                        DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.SaveQueues();

                        //旧的申请要置为无效，只保留最新的这个申请
                        var otherAppls = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetDataList(g => g.ContractProductInfoId == apply.ContractProductInfoId && g.Id != operate_In.ApplyId);
                        otherAppls.ForEach(a =>
                        {
                            //a.IsInvalid = (int)EnumIsInvalid.Invalid;
                            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateData(r => new Db_crm_contract_productserviceinfo_otherdata_appl { IsInvalid = (int)EnumIsInvalid.Invalid }, r => a.Id == r.Id);
                        });*/
            #endregion 
        }

        /// <summary>
        /// 作废合同服务信息其他数据申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        /// <exception cref="ApiException"></exception>
        public void VoidContractProductServiceInfoOtherDataAudit(OperateContractProductServiceInfoOtherDataAudit operate_In)
        {

            //声明可作废状态的集合, 过期、作废状态的数据不可以进行作废操作
            var couldRevokeOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.OverDue, EnumContractServiceOpenState.Void };
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetServiceOtherDataApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以撤销
            if (couldRevokeOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可撤销");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetServiceInfoByApplyId(operate_In);
            //当前时间
            var now = DateTime.Now;
            //申请的状态改为作废
            apply.State = EnumProcessStatus.Void.ToInt();
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_otherdata_appl>());
            //如果存在服务，处理服务后续内容
            if (service != null)
            {
                //服务的状态改为作废
                service.State = EnumContractServiceState.VOID;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(service);
            }
            //服务变更处理
            if (apply.ProcessingType == EnumProcessingType.Change.ToInt())
            {
                var originalServe = new Db_crm_contract_serviceinfo_otherdata();
                //已开通服务撤销，原服务恢复正常
                if (apply.OpenState == EnumContractServiceOpenState.Open)
                {
                    originalServe = DbOpe_crm_contract_serviceinfo_otherdata.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(originalServe);
                }
                //未开通服务的撤销，获取原服务数据
                else
                    originalServe = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false && g.IsHistory == false);
                //将原服务数据置位生效状态
                if (originalServe.ProductServiceInfoOtherDataApplId.IsNotNullOrEmpty())
                {
                    var originalAppl = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetDataById(originalServe.ProductServiceInfoOtherDataApplId);
                    originalAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(originalAppl);
                }
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.SaveQueues();
            #region 原作废逻辑 2023.12.13注释
            /*
                        //获取申请数据 
                        var apply = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
                        *//* 2023.10.8 拒绝状态可以作废
                        //申请为拒绝状态的数据不可以作废
                        if (apply.State == EnumProcessStatus.Refuse.ToInt())
                            throw new ApiException("所选的申请不可作废");
                        *//*
                        //获取服务数据
                        var service = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetServiceInfoByApplyId(operate_In);
                        //若存在服务，过期(服务为过期)、作废(服务为作废或失效)状态的数据不可以作废
                        if (service != null && (service.State == EnumContractServiceState.INVALID || service.State == EnumContractServiceState.VOID || service.State == EnumContractServiceState.OUT))
                            throw new ApiException("所选的申请不可作废");
                        //申请的状态改为作废
                        apply.State = EnumProcessStatus.Void.ToInt();
                        apply.UpdateUser = UserId;
                        apply.UpdateDate = DateTime.Now;
                        DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply);
                        //如果存在服务，处理服务后续内容
                        if (service != null)
                        {
                            //服务的状态改为作废
                            service.State = EnumContractServiceState.VOID;
                            service.UpdateUser = UserId;
                            service.UpdateDate = DateTime.Now;
                            DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(service);
                            //如果是服务变更，原服务恢复正常
                            if (!string.IsNullOrEmpty(service.HistoryId))
                            {
                                var originalServe = DbOpe_crm_contract_serviceinfo_otherdata.Instance.QueryByPrimaryKey(service.HistoryId);
                                originalServe.IsChanged = false;

                                originalServe.ChangedId = "";
                                originalServe.UpdateDate = DateTime.Now;
                                originalServe.UpdateUser = UserId;
                                if (originalServe.ServiceCycleEnd < DateTime.Now)
                                    originalServe.State = EnumContractServiceState.OUT;
                                else
                                    originalServe.State = EnumContractServiceState.VALID;
                                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(originalServe);
                                var originalAppl = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetDataById(originalServe.ProductServiceInfoOtherDataApplId);
                                originalAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                                DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(originalAppl);
                            }
                        }
                        //如果作废的是服务变更申请，且没有开通服务，则解除被变更的服务锁定状态
                        else if (apply.ProcessingType == (int)EnumProcessingType.Change)
                        {
                            //获取被变更的服务数据
                            var currentService = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetData(g => g.State == EnumContractServiceState.VALID && g.ContractProductInfoId == apply.ContractProductInfoId && g.Deleted == false);
                            if (currentService != null)
                            {
                                var currentAppl = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetDataById(currentService.ProductServiceInfoOtherDataApplId);
                                currentAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                                DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(currentAppl);
                            }
                        }
                        //提交sql队列
                        DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.SaveQueues();
            */
            #endregion
        }


        /// <summary>
        /// 删除合同服务信息其他数据申请信息
        /// </summary>
        /// <param name="operate_In"></param>
        public void DeleteContractProductServiceInfoOtherDataAudit(OperateContractProductServiceInfoOtherDataAudit operate_In)
        {

            //声明可删除状态的集合, 拒绝、作废状态的数据可以进行删除操作
            var couldDeleteOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.Refuse, EnumContractServiceOpenState.Void };
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetServiceOtherDataApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以删除
            if (!couldDeleteOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可删除");
            //删除申请
            apply.Deleted = true;
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_otherdata_appl>());
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetServiceInfoByApplyId(operate_In);
            if (service != null)
            {
                //删除服务
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(service);
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.SaveQueues();
            #region 原删除逻辑 2023.12.13注释
            /*//获取申请数据 
            var apply = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
            //申请待开通状态的数据不可以删除
            if (apply.State == EnumProcessStatus.Submit.ToInt())
                throw new ApiException("所选的申请不可删除");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetServiceInfoByApplyId(operate_In);
            //若存在服务，正常状态的数据不可以作废
            if (service != null && service.State == EnumContractServiceState.VALID)
                throw new ApiException("所选的申请不可删除");
            //删除申请
            apply.Deleted = true;
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.UpdateQueue(apply);
            if (service != null)
            {
                //删除服务
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_otherdata.Instance.UpdateQueue(service);
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.SaveQueues();*/
            #endregion
        }


    }
}


