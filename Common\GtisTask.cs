﻿using CRM2_API.BLL.GtisOpe;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using TaskScheduler.Interfaces;
using TaskScheduler.Models;
using TaskScheduler.Services;
using static CRM2_API.Model.BLLModel.Enum.EnumUtil;

namespace CRM2_API.Common
{
    public class GtisTask
    {
        public class GtisTaskProcessor : ITaskProcessor<BM_AddGtisUserDemo, BM_GtisReturnResult>
        {
            public async Task<BM_GtisReturnResult> ProcessAsync(BM_AddGtisUserDemo parameter, TaskContext task, CancellationToken cancellationToken = default)
            {
                //Console.WriteLine($"处理任务: {parameter.Name}, 值: {parameter.Value}");
                Db_crm_contract_serviceinfo_temporary_account account = new();
                account.TaskNum = parameter.TaskID.ToString().PadLeft(5, '0');
                account.State = EnumTemporaryAccountState.Normal.ToInt();
                account.AccountsOpenedNum = 1;
                account.Duration = 1;
                account.IsOpenGlobalSearch = 0;
                account.IsOpenSubAccount = 0;
                account.OpenedDate = DateTime.Now;
                account.CustomerId = Guid.Empty.ToString();
                string id = DbOpe_crm_contract_serviceinfo_temporary_account.Instance.InsertDataReturnId(account).ToString();

                List<BM_AddGtisUserDemoRetModel> AccountUser = BLL_GtisOpe.Instance.AddUserDemo(parameter).Result;

                DbOpe_crm_contract_serviceinfo_temporary_account_user.Instance.AddContractServiceInfoTemporaryAccountUser(AccountUser, id, 1);
                List<AccountCountry> l = new List<AccountCountry> { new AccountCountry
                {
                    Sid =   109
                },new AccountCountry { Sid = 425} };
                DbOpe_crm_contract_serviceinfo_temporary_account_country.Instance.AddContractServiceInfoTemporaryAccountCountry(id, l);




                // 实现任务处理逻辑
                await Task.Delay(2000, cancellationToken);



                // 返回结果
                return new BM_GtisReturnResult
                {
                    //account = AccountUser,
                    Status = TaskExecutionStatus.Completed,
                    Result = $"任务  已完成",
                    Error = ""
                };

            }

        }


        public class GtisRenewalContactProcessor : ITaskProcessor<BM_GtisOpe_RenewalContact, BM_GtisReturnResult>
        {
            public async Task<BM_GtisReturnResult> ProcessAsync(BM_GtisOpe_RenewalContact parameter, TaskContext task, CancellationToken cancellationToken = default)
            {
                //Console.WriteLine($"处理任务: {parameter.Name}, 值: {parameter.Value}");
                List<BM_AddGtisUserRetModel> bM_AddGtisUserRetModels = new List<BM_AddGtisUserRetModel>();
                bM_AddGtisUserRetModels = await BLL_GtisOpe.Instance.RenewalContact(parameter);

                // 实现任务处理逻辑
                //await Task.Delay(2000, cancellationToken);

                // 返回结果
                return new BM_GtisReturnResult
                {
                    account = bM_AddGtisUserRetModels,
                    Status = TaskExecutionStatus.Completed,
                    Result = $"Gtis开通已完成",
                    Error = ""
                };

            }

        }

        public class GtisAddUserProcessor : ITaskProcessor<List<Db_crm_contract_serviceinfo_gtis_user>, BM_GtisReturnResult>
        {
            public async Task<BM_GtisReturnResult> ProcessAsync(List<Db_crm_contract_serviceinfo_gtis_user> parameter, TaskContext task, CancellationToken cancellationToken = default)
            {
                //Console.WriteLine($"处理任务: {parameter.Name}, 值: {parameter.Value}");
                // 获取任务结果
                // var execution = await taskService.GetTaskResultAsync<BM_GtisReturnResult>(executionId);
                var fatherResult = task.GetParentResult<BM_GtisReturnResult>();
                DateTime dt = DateTime.Now;
                parameter.ForEach(user =>
                {
                    //新加的账号
                    var addUserRet = fatherResult.account.Find(u => u.CrmId == user.Id);
                    if (addUserRet != null)
                    {
                        user.AccountNumber = addUserRet.Uid;
                        user.UserId = addUserRet.SysUserID;
                        user.PassWord = addUserRet.Pwd;
                        user.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        user.ProcessedTime = dt;
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                        user.StartDate = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(user);
                    }
                });

                // 实现任务处理逻辑
                //await Task.Delay(2000, cancellationToken);

                // 返回结果
                return new BM_GtisReturnResult
                {
                    account = null,
                    Status = TaskExecutionStatus.Completed,
                    Result = $"新增Gtis账号已完成",
                    Error = ""
                };

            }

        }

        public class GtisUpdateUserProcessor : ITaskProcessor<List<Db_crm_contract_serviceinfo_gtis_user>, BM_GtisReturnResult>
        {
            public async Task<BM_GtisReturnResult> ProcessAsync(List<Db_crm_contract_serviceinfo_gtis_user> parameter, TaskContext task, CancellationToken cancellationToken = default)
            {
                //Console.WriteLine($"处理任务: {parameter.Name}, 值: {parameter.Value}");
                // 获取任务结果
                // var execution = await taskService.GetTaskResultAsync<BM_GtisReturnResult>(executionId);
                DateTime dt = DateTime.Now;
                foreach (var updateUser in parameter)
                {
                    //沿用的账号
                    updateUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                    updateUser.ProcessedTime = dt;
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(updateUser);
                }

                // 实现任务处理逻辑
                //await Task.Delay(2000, cancellationToken);

                // 返回结果
                return new BM_GtisReturnResult
                {
                    account = null,
                    Status = TaskExecutionStatus.Completed,
                    Result = $"更新Gits账号已完成",
                    Error = ""
                };

            }

        }

        public class GtisdelUserProcessor : ITaskProcessor<List<Db_crm_contract_serviceinfo_gtis_user>, BM_GtisReturnResult>
        {
            public async Task<BM_GtisReturnResult> ProcessAsync(List<Db_crm_contract_serviceinfo_gtis_user> parameter, TaskContext task, CancellationToken cancellationToken = default)
            {
                //Console.WriteLine($"处理任务: {parameter.Name}, 值: {parameter.Value}");
                // 获取任务结果
                // var execution = await taskService.GetTaskResultAsync<BM_GtisReturnResult>(executionId);
                DateTime dt = DateTime.Now;
                foreach (var delUser in parameter)
                {
                    //停用的账号
                    delUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                    delUser.ProcessedTime = dt;
                    delUser.OpeningStatus = (int)EnumGtisUserOpeningStatus.UserManagerStop;
                    delUser.EndDate = dt;
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(delUser);
                }

                // 实现任务处理逻辑
                //await Task.Delay(2000, cancellationToken);

                // 返回结果
                return new BM_GtisReturnResult
                {
                    account = null,
                    Status = TaskExecutionStatus.Completed,
                    Result = $"删除Gtis账号已完成",
                    Error = ""
                };

            }

        }
    }

    public class TaskHandlerRegistrationService : IHostedService
    {
        private readonly TaskHandlerRegistry _taskHandlerRegistry;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TaskHandlerRegistrationService> _logger;

        public TaskHandlerRegistrationService(
            TaskHandlerRegistry taskHandlerRegistry,
            IServiceProvider serviceProvider,
            ILogger<TaskHandlerRegistrationService> logger)
        {
            _taskHandlerRegistry = taskHandlerRegistry;
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("正在注册任务处理器");

            // 注册任务处理器
            var emailTaskHandler = _serviceProvider.GetRequiredService<GtisTaskHandler>();
            _taskHandlerRegistry.RegisterHandler(emailTaskHandler);

            _logger.LogInformation("任务处理器注册完成");

            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }
    }

}
