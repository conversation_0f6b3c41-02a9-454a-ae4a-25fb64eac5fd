﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Data.Common;
using System.Net;
using System.Net.Sockets;

public class CheckFormAccessAttribute : ActionFilterAttribute
{


    public CheckFormAccessAttribute()
    {
    }

    /// <summary>
    /// 验证接口使用规则（到sys_form_rule表里设置白名单才可以使用）
    /// </summary>
    /// <param name="context"></param>
    /// <exception cref="ApiException"></exception>
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        var accessFlag = false;
        //获取请求的ip
        var ip = GetRequestIP(context);
        //获取formId
        var formId = Com_SysForm.Instance.FormId;
        //获取接口的ControllerName
        var controllerName = context.ActionDescriptor.DisplayName.Split('.')[2].ToString().Replace("Controller", "");
        //获取接口的ActionName
        var methodName = context.ActionDescriptor.DisplayName.Split('.')[3].Replace(" (CRM2_API)", "");
        //获取权限
        accessFlag = DbOpe_sys_form_rule.Instance.GetAccess(formId, controllerName, methodName, ip);
        if (!accessFlag)
        {
            context.Result = new UnauthorizedResult();
            throw new ApiException("管理权限验证失败");
            //return;
        }
        base.OnActionExecuting(context);
    }

    private string GetRequestIP(ActionExecutingContext context)
    {
        LogUtil.AddLog("ip验证");
        var request = context.HttpContext.Request;
        string ip;
        var ipAddress = request.HttpContext.Connection.RemoteIpAddress;
        LogUtil.AddLog("RemoteIpAddress：" + ipAddress.MapToIPv4().ToString());
        IPAddress ipv4Address = GetIPv4Address(ipAddress);
        ip = ipv4Address.ToString();
        string forwardedHeader = "X-Forwarded-For";
        if (request.HttpContext.Request.Headers.ContainsKey(forwardedHeader))
        {
            LogUtil.AddLog("X-Forwarded-For");
            ip = request.HttpContext.Request.Headers[forwardedHeader].ToString().Split(new char[] { ',' }).Last().Trim();
            LogUtil.AddLog("X-Forwarded-For-ip:" + ip);
        }
        return ip;
    }

    private IPAddress GetIPv4Address(IPAddress ipv6Address)
    {
        if (ipv6Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6)
        {
            // 尝试映射IPv4兼容的IPv6地址
            var ipv4Address = ipv6Address.MapToIPv4();
            if (!ipv4Address.Equals(IPAddress.Any))
            {
                return ipv4Address; // 返回转换后的IPv4地址
            }
        }
        return ipv6Address;
    }
}