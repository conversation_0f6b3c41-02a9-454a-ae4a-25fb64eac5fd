﻿using Microsoft.AspNetCore.Http;
using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using System.IO;
using System.Web;
using static CRM2_API.Common.Cache.LocalCache;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using Spire.Doc;
using CRM2_API.BLL.Schedule;
using CRM2_API.Model.ControllersViewModel.Schedule;
using CRM2_API.Model.ControllersViewModel.TrackingRecord;
using CRM2_API.BLL.TrackingRecord;
using NPOI.POIFS.Properties;
using System.Diagnostics.Contracts;
using DocumentFormat.OpenXml.VariantTypes;
using Lucene.Net.Support;
using System.Linq;
using LgyUtil;
using DocumentFormat.OpenXml.EMMA;
using static CRM2_API.Common.Cache.RedisCache;
using Microsoft.IdentityModel.Tokens;
using static Lucene.Net.Util.Fst.Util;
using System.Reflection.PortableExecutable;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;
using DocumentFormat.OpenXml.Presentation;
using System.Runtime.Intrinsics.X86;
using SqlSugar;
using SqlSugar.Extensions;
using CRM2_API.BLL.GtisOpe;
using SkiaSharp;
using LumiSoft.Net.Media;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.JWT;
using DocumentFormat.OpenXml.Bibliography;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using Microsoft.AspNetCore.Mvc;
using DocumentFormat.OpenXml.ExtendedProperties;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Utils;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;
using CRM2_API.DAL.DbCommon;
//using DocumentFormat.OpenXml.Spreadsheet;

namespace CRM2_API.BLL
{
    public partial class BLL_Contract : BaseBLL<BLL_Contract>
    {


        /// <summary>
        /// 获取合同中的SaleWits充值金额
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>充值金额</returns>
        public decimal GetContractRechargeAmount(string contractId)
        {
            // 通过关联查询获取充值产品的金额
            var rechargeAmount = DbContext.Crm2Db.Queryable<Db_crm_contract_productinfo>()
                .LeftJoin<Db_crm_product>((cp, p) => cp.ProductId == p.Id)
                .Where((cp, p) => cp.ContractId == contractId && cp.Deleted == false && p.ProductType == (int)EnumProductType.AddCredit)
                .Sum((cp, p) => cp.ContractProductinfoPriceTotal);

            return rechargeAmount;
        }
    }
}
