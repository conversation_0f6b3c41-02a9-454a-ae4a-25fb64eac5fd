﻿using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using DocumentFormat.OpenXml.Office2013.Drawing.ChartStyle;
using SqlSugar;
using System.Linq;
using System.Linq.Expressions;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_FormDataCondition;

namespace CRM2_API.BLL.Common
{
    public class DataPermission
    {
        public static string GetDataPermission(EnumDataSourceRelationShip dataSourceRelationShip, string associatedFieldName, string dataIdFieldName)
        {
            string formId = Com_SysForm.Instance.FormId;
            string userId = TokenModel.Instance.id;
            List<string> ConditionsList = new List<string>();
            List<FormDataCondition> UserDataRight = RedisCache.UserDataRight.GetUserDataRight(userId);
            string NtocontainFormDataConditionDataSource = "";
            List<FormDataCondition> formDataConditions = new List<FormDataCondition>();
            if (dataSourceRelationShip == EnumDataSourceRelationShip.NoAssociated)
            {
                NtocontainFormDataConditionDataSource = "Associated";
                formDataConditions = UserDataRight.Where(r => r.FormId == formId && (r.FormDataConditionDataSource == "Self" || r.FormDataConditionDataSource == "All")).ToList();// && (r.FormDataConditionDataSource != NtocontainFormDataConditionDataSource)).ToList();
            }
            else if (dataSourceRelationShip == EnumDataSourceRelationShip.Associated)
            {
                NtocontainFormDataConditionDataSource = "Self";
                formDataConditions = UserDataRight.Where(r => r.FormId == formId && (r.FormDataConditionDataSource != NtocontainFormDataConditionDataSource)).ToList();
            }
            //DbOpe_sys_form_datacondition.Instance.GetFormDataConditionByFormId(formId, userId);
            foreach (FormDataCondition dc in formDataConditions)
            {
                string Conditions = "";
                if (dc.FormDataConditionDataSource == "All")
                {
                    Conditions = " 1 = 1 ";
                }
                else if (dc.FormDataConditionDataSource == "Self")
                {
                    string ConditionsValue = dc.Condition.Replace("{#Associated}", associatedFieldName).Replace("{#dataIdFieldName}", dataIdFieldName).Replace("{#UserId}", userId);
                    Conditions = ConditionsValue;
                }
                else
                {
                    string ConditionsValue = dc.Condition.Replace("{#Associated}", associatedFieldName).Replace("{#dataIdFieldName}", dataIdFieldName).Replace("{#UserId}", userId);
                    Conditions = string.Format("EXISTS(SELECT Conditions.* FROM {0} Conditions WHERE ({1}))", dc.FormDataConditionDataSource, ConditionsValue);
                }
                ConditionsList.Add(Conditions);
            }
            string result = string.Join(" or ", ConditionsList);
            if (result == "")
            {
                return "( 1 = 1 )";
            }
            else
            {
                return "(" + result + ")";
            }
        }

        public static string GetDataPermission<T>(EnumDataSourceRelationShip dataSourceRelationShip)
        {
            Type typeDB = typeof(T);
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;
            return GetDataPermission(dataSourceRelationShip, tableName + ".CreateUser", tableName + ".Id");
        }

        public static string GetDataPermission<T>(EnumDataSourceRelationShip dataSourceRelationShip, Expression<Func<T, string>> associatedFieldName, Expression<Func<T, string>> dataIdFieldName)
        {
            Type typeDB = typeof(T);
            string tableName = ((SugarTable)(typeDB.GetCustomAttributes(true)[2])).TableName;

            string associatedFieldNameString = "";
            if (associatedFieldName != null)
            {
                associatedFieldNameString = tableName + "." + (associatedFieldName.Body as MemberExpression).Member.Name;
            }

            string dataIdFieldNameString = "";
            if (dataIdFieldName != null)
            {
                dataIdFieldNameString = tableName + "." + (dataIdFieldName.Body as MemberExpression).Member.Name;
            }
            
            return GetDataPermission(dataSourceRelationShip,  associatedFieldNameString,  dataIdFieldNameString);
        }

        public static string GetDataPermission<T, F>(EnumDataSourceRelationShip dataSourceRelationShip, Expression<Func<T, string>> associatedFieldName, Expression<Func<F, string>> dataIdFieldName)
        {
            Type typeAssociatedDB = typeof(T);
            string tableAssociatedName = ((SugarTable)(typeAssociatedDB.GetCustomAttributes(true)[2])).TableName;
            Type typeDataIdDB = typeof(F);
            string tableDataIdName = ((SugarTable)(typeDataIdDB.GetCustomAttributes(true)[2])).TableName;

            string associatedFieldNameString = "";
            if (associatedFieldName != null)
            {
                associatedFieldNameString = tableAssociatedName + "." + (associatedFieldName.Body as MemberExpression).Member.Name;
            }
            string dataIdFieldNameString = "";
            if (dataIdFieldName != null)
            {
                dataIdFieldNameString = tableDataIdName + "." + (dataIdFieldName.Body as MemberExpression).Member.Name;
            }

            return GetDataPermission(dataSourceRelationShip, associatedFieldNameString,  dataIdFieldNameString);
        }
    }
}
