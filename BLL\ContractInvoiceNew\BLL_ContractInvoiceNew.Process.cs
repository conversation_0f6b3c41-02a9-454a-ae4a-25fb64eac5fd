using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.Linq;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 处理部分
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        /// <summary>
        /// 催办发票申请（同时处理关联的退票申请）
        /// </summary>
        /// <param name="requestId">发票申请ID</param>
        /// <param name="userId">用户ID</param>
        /// <returns>催办结果</returns>
        public bool ReminderInvoiceApplication(string requestId, string userId)
        {
            try
            {
                // 查询发票申请
                var invoiceApp = DbOpe_crm_invoice_application.Instance.GetData(x => x.Id == requestId && x.Deleted != true);
                if (invoiceApp == null)
                {
                    return false; // 未找到发票申请
                }
                
                // 判断发票状态是否需要催办
                // 只有以下状态需要催办：发票申请中、发票待复核、发票复核驳回、退票申请中、退票待复核、退票复核驳回
                var displayStatus = invoiceApp.DisplayStatus ?? 0;
                bool canRemind = displayStatus == (int)EnumInvoiceDisplayStatus.InvoiceApplied || 
                                 displayStatus == (int)EnumInvoiceDisplayStatus.InvoiceProcessed || 
                                 displayStatus == (int)EnumInvoiceDisplayStatus.InvoiceReviewRejected ||
                                 displayStatus == (int)EnumInvoiceDisplayStatus.RefundApplied ||
                                 displayStatus == (int)EnumInvoiceDisplayStatus.RefundProcessed ||
                                 displayStatus == (int)EnumInvoiceDisplayStatus.RefundReviewRejected;
                
                if (!canRemind)
                {
                    return false; // 当前状态不需要催办
                }
                
                // 更新发票申请的催办标记
                invoiceApp.IsReminded = (int)EnumIsReminder.UrgedTicket;
                invoiceApp.RemindedDate = DateTime.Now;
                invoiceApp.RemindedUser = userId;
                invoiceApp.UpdateUser = userId;
                invoiceApp.UpdateDate = DateTime.Now;
                DbOpe_crm_invoice_application.Instance.UpdateData(invoiceApp);
                
                // 查找该发票申请对应的发票
                var invoices = DbOpe_crm_invoice.Instance.GetDataList(i => i.InvoiceApplicationId == requestId && i.Deleted != true);
                foreach (var invoice in invoices)
                {
                    // 查找每个发票关联的退票申请
                    var refundApps = DbOpe_crm_invoice_refund_application.Instance.GetDataList(r => r.InvoiceId == invoice.Id && r.Deleted != true);
                    foreach (var refundApp in refundApps)
                    {
                        // 更新退票申请的催办标记
                        refundApp.IsReminded = 1;
                        refundApp.RemindedDate = DateTime.Now;
                        refundApp.RemindedUser = userId;
                        refundApp.UpdateUser = userId;
                        refundApp.UpdateDate = DateTime.Now;
                        DbOpe_crm_invoice_refund_application.Instance.UpdateData(refundApp);
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"催办发票申请时发生错误: {ex.Message}, {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 更新发票应付金额
        /// </summary>
        /// <param name="request">更新请求</param>
        /// <param name="userId">用户ID</param>
        /// <returns>更新结果</returns>
        public bool UpdateBalanceDue(UpdateBalanceDueRequest request, string userId)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(request.Id))
                {
                    throw new ApiException("发票ID不能为空");
                }
                
                if (request.BalanceDue < 0)
                {
                    throw new ApiException("应付金额不能小于0");
                }
                var invoiceApp = DbOpe_crm_invoice_application.Instance.GetData(i => i.Id == request.Id && i.Deleted != true);
                if (invoiceApp == null)
                {
                    throw new ApiException("发票申请不存在或已被删除");
                }
                if (invoiceApp.AuditStatus != (int)EnumInvoiceApplicationStatus.Completed)
                {
                    throw new ApiException("只有已开票的发票，才能更新应付金额");
                }


                // 获取形式发票信息
                var invoice = DbOpe_crm_proforma_invoice.Instance.GetData(i => i.ApplicationId == request.Id && i.Deleted != true);
                if (invoice == null)
                {
                    throw new ApiException("发票不存在或已被删除");
                }
                
                // 验证合同权限
                var contract = DbOpe_crm_contract.Instance.GetContractById(invoice.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("没有数据权限或合同数据不存在");
                }
                
                // 更新应付金额
                DbOpe_crm_proforma_invoice.Instance.UpdateData(
                    i => new Db_crm_proforma_invoice {
                        BalanceDue = request.BalanceDue.ToString(),
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now
                    },
                    i => i.ApplicationId == request.Id
                );
                return true;
            }
            catch (ApiException)
            {
                throw; // 直接重新抛出业务异常
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新发票应付金额时发生错误: {ex.Message}, {ex.StackTrace}");
                throw new ApiException("更新发票应付金额失败");
            }
        }

        /// <summary>
        /// 获取发票应付金额
        /// </summary>
        /// <param name="id">发票ID</param>
        /// <returns>应付金额信息</returns>
        public BalanceDueResponse GetBalanceDue(string id)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(id))
                {
                    throw new ApiException("发票ID不能为空");
                }
                // 获取形式发票信息
                var invoice = DbOpe_crm_proforma_invoice.Instance.GetData(i => i.ApplicationId == id && i.Deleted != true);
                if (invoice == null)
                {
                    throw new ApiException("发票不存在或已被删除");
                }
                
                // 验证合同权限
                var contract = DbOpe_crm_contract.Instance.GetContractById(invoice.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("没有数据权限或合同数据不存在");
                }
                
                // 构建返回结果
                var response = new BalanceDueResponse
                {
                    Id = invoice.Id,
                    InvoicedAmount = invoice.Amount,
                    BalanceDue = invoice.BalanceDue
                };
                
                return response;
            }
            catch (ApiException)
            {
                throw; // 直接重新抛出业务异常
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取发票应付金额时发生错误: {ex.Message}, {ex.StackTrace}");
                throw new ApiException("获取发票应付金额失败");
            }
        }
    }
} 