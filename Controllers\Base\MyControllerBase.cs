﻿using CRM2_API.BLL.BLL_Example;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Filter;
using CRM2_API.Common.JWT;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using System.Web;

namespace CRM2_API.Controllers.Base
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class MyControllerBase
    {
        /// <summary>
        /// 当前请求中的对象
        /// </summary>
        public IHttpContextAccessor ContextAccessor { get; }
        /// <summary>
        /// HttpContext对象
        /// </summary>
        public HttpContext HttpContext => ContextAccessor?.HttpContext!;
        /// <summary>
        /// 响应
        /// </summary>
        public HttpResponse Response => ContextAccessor.HttpContext.Response;
        /// <summary>
        /// 请求
        /// </summary>
        public HttpRequest Request => ContextAccessor.HttpContext.Request;

        /// <summary>
        /// 用户token信息
        /// </summary>
        public TokenModel UserTokenInfo => TokenModel.Instance;

        /// <summary>
        /// 服务提供者
        /// </summary>
        protected IServiceProvider ServiceProvider => HttpContext?.RequestServices;

        /// <summary>
        /// controller构造函数，注入httpcontext对象
        /// </summary>
        /// <param name="_httpContextAccessor"></param>
        public MyControllerBase(IHttpContextAccessor _httpContextAccessor)
        {
            ContextAccessor = _httpContextAccessor;
        }
        public MyControllerBase()
        {
        }

        /// <summary>
        /// 获取注入的服务
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        protected T GetService<T>() where T : class
        {
            return ServiceProvider?.GetService<T>();
        }

        /// <summary>
        /// 获取必需的注入服务（如果服务未注册则抛出异常）
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        protected ITaskService GetRequiredService<ITaskService>() where ITaskService : class
        {
            return ServiceProvider?.GetRequiredService<ITaskService>();
        }

        /// <summary>
        /// 列表查询公共返回类
        /// </summary>
        /// <typeparam name="T">数组类型</typeparam>
        /// <param name="list">数据</param>
        /// <param name="total">总数</param>
        /// <returns></returns>
        [NonAction]
        public ApiTableOut<T> GetApiTableOut<T>(IEnumerable<T> list, int total)
        {
            return new ApiTableOut<T>() { Data = list, Total = total };
        }

        /// <summary>
        /// 设置下载文件名
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [NonAction]
        public void SetDownloadFileName(string fileName)
        {
            Response.ContentType = "application/octet-stream";
            Response.Headers.Add("download-name", HttpUtility.UrlEncode(fileName));
            Response.Headers.Add("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode(fileName));
            Response.Headers.Add("Access-Control-Expose-Headers", "download-name");//设置这个，前端axios才能读到downloadfilename这个header
        }

        /// <summary>
        /// 生成当前controller所有TypeScript模型
        /// </summary>
        /// <returns></returns>
        [HttpPut, SkipAuthCheck, SkipRecordLog, SkipIPCheck]
        public Stream GenerateThisControllerModelToTs()
        {
            //生产环境不能使用
            if (AppSettings.Env == Enum_SystemSettingEnv.Product)
                return null;
            var thisType = this.GetType();
            Response.ContentType = "application/octet-stream";
            Response.Headers.Add("Content-Disposition", "attachment;filename=" + thisType.Name + "_Ts" + ".zip");
            return BLL_GenerateModelToTS.Instance.GenerateController(thisType);
        }
    }
}
