using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Quartz;
using Quartz.Impl;

namespace CRM2_API.Common.Cron
{
    /// <summary>
    /// 安全的定时任务工具类，避免TokenModel等上下文依赖问题
    /// </summary>
    public static class SafeCronUtil
    {
        private static readonly Lazy<IScheduler> _scheduler = new Lazy<IScheduler>(() =>
        {
            var scheduler = StdSchedulerFactory.GetDefaultScheduler().Result;
            scheduler.Start().Wait();
            return scheduler;
        });

        private static IScheduler Scheduler => _scheduler.Value;
        
        // 记录所有已创建的Job，用于管理
        private static readonly ConcurrentDictionary<string, JobKey> _jobKeys = new ConcurrentDictionary<string, JobKey>();
        
        // 所有操作都在静态构造函数中初始化
        static SafeCronUtil()
        {
            try
            {
                Debug.WriteLine("[SafeCronUtil] 初始化调度器...");
                var scheduler = Scheduler; // 触发懒加载初始化
                Debug.WriteLine("[SafeCronUtil] 调度器初始化完成");
                
                // 注册应用程序退出时的清理工作
                AppDomain.CurrentDomain.ProcessExit += (sender, e) =>
                {
                    try
                    {
                        if (Scheduler != null && !Scheduler.IsShutdown)
                        {
                            Scheduler.Shutdown(true).Wait();
                            Debug.WriteLine("[SafeCronUtil] 调度器已安全关闭");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[SafeCronUtil] 关闭调度器时发生错误: {ex.Message}");
                    }
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SafeCronUtil] 初始化调度器失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 添加一个简单的执行一次的任务
        /// </summary>
        /// <param name="jobName">任务名称</param>
        /// <param name="action">要执行的操作</param>
        /// <param name="startTimeSpan">多久后执行，默认5秒</param>
        /// <returns>是否添加成功</returns>
        public static bool AddOneTimeJob(string jobName, Action action, TimeSpan? startTimeSpan = null)
        {
            if (string.IsNullOrEmpty(jobName) || action == null)
                return false;
                
            try
            {
                // 先停止同名任务
                StopJob(jobName);
                
                // 包装操作到安全的执行环境
                var wrappedAction = WrapAction(action);
                
                // 设置触发时间
                var startTime = DateTime.Now.Add(startTimeSpan ?? TimeSpan.FromSeconds(5));
                
                // 创建Job
                IJobDetail job = JobBuilder.Create<SafeActionJob>()
                    .WithIdentity(jobName, "SafeJobGroup")
                    .UsingJobData("jobName", jobName)
                    .Build();
                
                // 将Action存储到静态字典，避免序列化问题
                SafeActionJob.RegisterAction(jobName, wrappedAction);
                
                // 创建触发器
                ITrigger trigger = TriggerBuilder.Create()
                    .WithIdentity($"{jobName}_trigger", "SafeTriggerGroup")
                    .StartAt(startTime)
                    .Build();
                
                // 调度任务
                Scheduler.ScheduleJob(job, trigger).Wait();
                
                // 记录Job
                _jobKeys[jobName] = job.Key;
                
                Debug.WriteLine($"[SafeCronUtil] 成功添加一次性任务: {jobName}, 执行时间: {startTime}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SafeCronUtil] 添加一次性任务失败: {jobName}, 错误: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 添加一个基于Cron表达式的周期性任务
        /// </summary>
        /// <param name="jobName">任务名称</param>
        /// <param name="action">要执行的操作</param>
        /// <param name="cronExpression">Cron表达式</param>
        /// <returns>是否添加成功</returns>
        public static bool AddCronJob(string jobName, Action action, string cronExpression)
        {
            if (string.IsNullOrEmpty(jobName) || action == null || string.IsNullOrEmpty(cronExpression))
                return false;
                
            try
            {
                // 先停止同名任务
                StopJob(jobName);
                
                // 包装操作到安全的执行环境
                var wrappedAction = WrapAction(action);
                
                // 创建Job
                IJobDetail job = JobBuilder.Create<SafeActionJob>()
                    .WithIdentity(jobName, "SafeJobGroup")
                    .UsingJobData("jobName", jobName)
                    .Build();
                
                // 将Action存储到静态字典，避免序列化问题
                SafeActionJob.RegisterAction(jobName, wrappedAction);
                
                // 创建触发器
                ITrigger trigger = TriggerBuilder.Create()
                    .WithIdentity($"{jobName}_trigger", "SafeTriggerGroup")
                    .WithCronSchedule(cronExpression)
                    .Build();
                
                // 调度任务
                Scheduler.ScheduleJob(job, trigger).Wait();
                
                // 记录Job
                _jobKeys[jobName] = job.Key;
                
                Debug.WriteLine($"[SafeCronUtil] 成功添加Cron任务: {jobName}, Cron表达式: {cronExpression}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SafeCronUtil] 添加Cron任务失败: {jobName}, 错误: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 添加一个定时重复执行的任务
        /// </summary>
        /// <param name="jobName">任务名称</param>
        /// <param name="action">要执行的操作</param>
        /// <param name="intervalInSeconds">间隔秒数</param>
        /// <param name="repeatCount">重复次数，-1表示无限重复</param>
        /// <returns>是否添加成功</returns>
        public static bool AddIntervalJob(string jobName, Action action, int intervalInSeconds, int repeatCount = -1)
        {
            if (string.IsNullOrEmpty(jobName) || action == null || intervalInSeconds <= 0)
                return false;
                
            try
            {
                // 先停止同名任务
                StopJob(jobName);
                
                // 包装操作到安全的执行环境
                var wrappedAction = WrapAction(action);
                
                // 创建Job
                IJobDetail job = JobBuilder.Create<SafeActionJob>()
                    .WithIdentity(jobName, "SafeJobGroup")
                    .UsingJobData("jobName", jobName)
                    .Build();
                
                // 将Action存储到静态字典，避免序列化问题
                SafeActionJob.RegisterAction(jobName, wrappedAction);
                
                // 创建触发器
                TriggerBuilder triggerBuilder = TriggerBuilder.Create()
                    .WithIdentity($"{jobName}_trigger", "SafeTriggerGroup")
                    .StartNow();
                    
                // 设置重复间隔
                if (repeatCount < 0)
                {
                    triggerBuilder = triggerBuilder.WithSimpleSchedule(x => 
                        x.WithIntervalInSeconds(intervalInSeconds).RepeatForever());
                }
                else
                {
                    triggerBuilder = triggerBuilder.WithSimpleSchedule(x => 
                        x.WithIntervalInSeconds(intervalInSeconds).WithRepeatCount(repeatCount));
                }
                
                ITrigger trigger = triggerBuilder.Build();
                
                // 调度任务
                Scheduler.ScheduleJob(job, trigger).Wait();
                
                // 记录Job
                _jobKeys[jobName] = job.Key;
                
                Debug.WriteLine($"[SafeCronUtil] 成功添加间隔任务: {jobName}, 间隔: {intervalInSeconds}秒, 重复: {(repeatCount < 0 ? "无限" : repeatCount.ToString())}次");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SafeCronUtil] 添加间隔任务失败: {jobName}, 错误: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 停止并删除一个任务
        /// </summary>
        /// <param name="jobName">任务名称</param>
        /// <returns>是否成功</returns>
        public static bool StopJob(string jobName)
        {
            if (string.IsNullOrEmpty(jobName))
                return false;
                
            try
            {
                // 从记录中获取JobKey
                if (_jobKeys.TryGetValue(jobName, out JobKey jobKey))
                {
                    // 删除任务
                    Scheduler.DeleteJob(jobKey).Wait();
                    _jobKeys.TryRemove(jobName, out _);
                    
                    // 清理注册的Action
                    SafeActionJob.UnregisterAction(jobName);
                    
                    Debug.WriteLine($"[SafeCronUtil] 成功停止任务: {jobName}");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SafeCronUtil] 停止任务失败: {jobName}, 错误: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 停止所有任务
        /// </summary>
        public static void StopAllJobs()
        {
            try
            {
                // 获取所有任务名称
                var jobNames = new List<string>(_jobKeys.Keys);
                
                // 逐个停止
                foreach (var jobName in jobNames)
                {
                    StopJob(jobName);
                }
                
                Debug.WriteLine($"[SafeCronUtil] 已停止所有任务, 总数: {jobNames.Count}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SafeCronUtil] 停止所有任务失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 包装Action以防止异常逃逸
        /// </summary>
        private static Action WrapAction(Action action)
        {
            return () =>
            {
                try
                {
                    action();
                }
                catch (Exception ex)
                {
                    // 捕获所有异常，防止任务失败
                    Debug.WriteLine($"[SafeCronUtil] 任务执行异常: {ex.Message}");
                    Debug.WriteLine($"[SafeCronUtil] 异常堆栈: {ex.StackTrace}");
                }
            };
        }
        
        /// <summary>
        /// 安全执行Action的Job
        /// </summary>
        public class SafeActionJob : IJob
        {
            // 使用静态ConcurrentDictionary存储Action，避免序列化问题
            private static readonly ConcurrentDictionary<string, Action> _actions = new ConcurrentDictionary<string, Action>();
            
            public static void RegisterAction(string jobName, Action action)
            {
                _actions[jobName] = action;
            }
            
            public static void UnregisterAction(string jobName)
            {
                _actions.TryRemove(jobName, out _);
            }
            
            public Task Execute(IJobExecutionContext context)
            {
                // 从上下文中获取jobName
                string jobName = context.JobDetail.JobDataMap.GetString("jobName");
                
                if (string.IsNullOrEmpty(jobName))
                {
                    Debug.WriteLine("[SafeActionJob] 无法执行任务: jobName为空");
                    return Task.CompletedTask;
                }
                
                // 获取并执行对应的Action
                if (_actions.TryGetValue(jobName, out Action action))
                {
                    try
                    {
                        action();
                    }
                    catch (Exception ex)
                    {
                        // 再次捕获异常
                        Debug.WriteLine($"[SafeActionJob] 执行任务 {jobName} 异常: {ex.Message}");
                    }
                }
                else
                {
                    Debug.WriteLine($"[SafeActionJob] 找不到任务 {jobName} 的执行方法");
                }
                
                return Task.CompletedTask;
            }
        }
    }
} 