﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using System.Text.Json;

namespace CRM2_API.BLL
{
    public class BLL_WorkReports : BaseBLL<BLL_WorkReports>
    {
        #region 日报部分
        /// <summary>
        /// 接口名称：AddDailyWorkReport
        /// 协议类型：http/post
        /// 接口描述：添加工作日报信息。保存草稿：添加工作日报信息，状态为草稿。新建：添加工作日报信息，状态为已发布。
        /// </summary>
        /// <param name="addDailyWorkReportIn"></param>
        public WorkReportActionResult AddDailyWorkReport(AddDailyWorkReport_In addDailyWorkReportIn)
        {
            Db_crm_daily_work_report DailyWorkReport = addDailyWorkReportIn.MappingTo<Db_crm_daily_work_report>();
            DailyWorkReport.Id = Guid.NewGuid().ToString();
            DailyWorkReport.UserId = UserTokenInfo.id;
            DailyWorkReport.State = addDailyWorkReportIn.SubmitState;
            DailyWorkReport.Deleted = false;
            DailyWorkReport.CreateUser = UserTokenInfo.id;
            DailyWorkReport.CreateDate = DateTime.Now;
            DailyWorkReport.UpdateUser = UserTokenInfo.id;
            DailyWorkReport.UpdateDate = DateTime.Now;

            if (DailyWorkReport.State != 0)
            {
                if (string.IsNullOrEmpty(DailyWorkReport.PremiumCustomers))
                {
                    throw new ApiException("今日优质客户为必填项");
                }
                if (string.IsNullOrEmpty(DailyWorkReport.CompletionStatus))
                {
                    throw new ApiException("今日完成情况为必填项");
                }
                if (string.IsNullOrEmpty(DailyWorkReport.ThinkingPoints))
                {
                    throw new ApiException("今日思考点为必填项");
                }

            }
            //2024年12月20日 去掉换行符
            DailyWorkReport.PremiumCustomers = string.IsNullOrEmpty(DailyWorkReport.PremiumCustomers) ? null : DailyWorkReport.PremiumCustomers.Replace("\r", "");
            DailyWorkReport.CompletionStatus = string.IsNullOrEmpty(DailyWorkReport.CompletionStatus) ? null : DailyWorkReport.CompletionStatus.Replace("\r", "");
            DailyWorkReport.ThinkingPoints = string.IsNullOrEmpty(DailyWorkReport.ThinkingPoints) ? null : DailyWorkReport.ThinkingPoints.Replace("\r", "");

            List<Db_crm_daily_work_report_customer> reportCustomers = new List<Db_crm_daily_work_report_customer>();
            //if (addDailyWorkReportIn.ReportCustomer != null)
            if (!string.IsNullOrEmpty(addDailyWorkReportIn.ReportCustomerJsonStr) && (addDailyWorkReportIn.ReportCustomer == null || addDailyWorkReportIn.ReportCustomer.Count == 0))
            {
                addDailyWorkReportIn.ReportCustomer = JsonSerializer.Deserialize<List<ReportCustomer>>(addDailyWorkReportIn.ReportCustomerJsonStr);
                if (addDailyWorkReportIn.ReportCustomer.Count != 0)
                {
                    addDailyWorkReportIn.ReportCustomer.ForEach(item =>
                    {
                        Db_crm_daily_work_report_customer customer = item.MappingTo<Db_crm_daily_work_report_customer>();
                        customer.Id = Guid.NewGuid().ToString();
                        customer.DailyWorkReportId = DailyWorkReport.Id;
                        customer.CustomerId = item.CustomerId;
                        customer.CompanyName = item.CustomerName;
                        customer.Deleted = false;
                        customer.CreateUser = UserTokenInfo.id;
                        customer.CreateDate = DateTime.Now;
                        customer.UpdateUser = UserTokenInfo.id;
                        customer.UpdateDate = DateTime.Now;
                        reportCustomers.Add(customer);
                    });
                }
                else
                if (DailyWorkReport.State != 0)
                {
                    throw new ApiException("未选择客户");
                }
            }
            else
            {
                throw new ApiException("未选择客户");
            }

            List<Db_crm_daily_work_report_progress> reportProgresses = new List<Db_crm_daily_work_report_progress>();
            //if (addDailyWorkReportIn.ReportProgress != null)
            if (!string.IsNullOrEmpty(addDailyWorkReportIn.ReportProgressJsonStr) && (addDailyWorkReportIn.ReportProgress == null || addDailyWorkReportIn.ReportProgress.Count == 0))
            {
                addDailyWorkReportIn.ReportProgress = JsonSerializer.Deserialize<List<ReportProgress>>(addDailyWorkReportIn.ReportProgressJsonStr);
                addDailyWorkReportIn.ReportProgress.ForEach(item =>
                {
                    Db_crm_daily_work_report_progress progress = item.MappingTo<Db_crm_daily_work_report_progress>();

                    progress.Id = Guid.NewGuid().ToString();
                    progress.DailyWorkReportId = DailyWorkReport.Id;
                    progress.Deleted = false;
                    progress.CreateUser = UserTokenInfo.id;
                    progress.CreateDate = DateTime.Now;
                    progress.UpdateUser = UserTokenInfo.id;
                    progress.UpdateDate = DateTime.Now;

                    reportProgresses.Add(progress);
                });
            }
            List<Db_crm_work_report_receiver> receiverList = new List<Db_crm_work_report_receiver>();
            if (!string.IsNullOrEmpty(addDailyWorkReportIn.ReportReceiverJsonStr) && (addDailyWorkReportIn.ReportReceiver == null || addDailyWorkReportIn.ReportReceiver.Count == 0))
            {
                addDailyWorkReportIn.ReportReceiver = JsonSerializer.Deserialize<List<ReportReceiver>>(addDailyWorkReportIn.ReportReceiverJsonStr);

                addDailyWorkReportIn.ReportReceiver.ForEach(item =>
                {
                    Db_crm_work_report_receiver receiver = new Db_crm_work_report_receiver();
                    receiver.Id = Guid.NewGuid().ToString();
                    receiver.WorkReportId = DailyWorkReport.Id;
                    receiver.Type = (int)EnumWorkReportsType.Daily;
                    receiver.ReceiverId = item.ReceiverId;
                    receiver.State = (int)EnumReportsReceiverState.IsNotRead;
                    receiver.Deleted = false;
                    receiver.CreateUser = UserTokenInfo.id;
                    receiver.CreateDate = DateTime.Now;
                    receiver.UpdateUser = UserTokenInfo.id;
                    receiver.UpdateDate = DateTime.Now;
                    receiverList.Add(receiver);

                });
            }



            return DbOpe_crm_daily_work_report.Instance.AddDailyWorkReport(DailyWorkReport, reportCustomers, reportProgresses, receiverList, addDailyWorkReportIn.ReportAttachs, addDailyWorkReportIn.ReportImgs);

        }

        /// <summary>
        /// 接口名称：UpdateDailyWorkReport        
        /// 协议类型：http/post        
        /// 接口描述：修改工作日报信息。保存草稿：添加工作日报信息，状态为草稿。新建：添加工作日报信息，状态为已发布。
        /// </summary>
        /// <param name="updateDailyWorkReportIn"></param>
        public WorkReportActionResult UpdateDailyWorkReport(UpdateDailyWorkReport_In updateDailyWorkReportIn)
        {
            #region 废弃部分
            //Db_crm_daily_work_report updateDaily = updateDailyWorkReportIn.MappingTo<Db_crm_daily_work_report>();

            //updateDaily.State = updateDailyWorkReportIn.SubmitState;
            //updateDaily.UpdateUser = UserTokenInfo.id;
            //updateDaily.UpdateDate = DateTime.Now;

            //List<Db_crm_daily_work_report_customer> reportCustomers = new List<Db_crm_daily_work_report_customer>();
            //updateDailyWorkReportIn.ReportCustomer.ForEach(item =>
            //{
            //    Db_crm_daily_work_report_customer customer = item.MappingTo<Db_crm_daily_work_report_customer>();
            //    customer.Id = customer.Id == null ? Guid.NewGuid().ToString() : customer.Id;
            //    customer.DailyWorkReportId = updateDailyWorkReportIn.Id;
            //    customer.Deleted = false;
            //    customer.CreateUser = customer.CreateUser == null ? UserTokenInfo.id : customer.CreateUser;
            //    customer.CreateDate = customer.CreateDate == null ? DateTime.Now : customer.CreateDate;
            //    customer.UpdateUser = UserTokenInfo.id;
            //    customer.UpdateDate = DateTime.Now;
            //    reportCustomers.Add(customer);
            //});

            //List<Db_crm_daily_work_report_progress> reportProgresses = new List<Db_crm_daily_work_report_progress>();
            //updateDailyWorkReportIn.ReportProgress.ForEach(item =>
            //{
            //    Db_crm_daily_work_report_progress progress = item.MappingTo<Db_crm_daily_work_report_progress>();

            //    progress.Id = progress.Id == null ? Guid.NewGuid().ToString() : progress.Id;
            //    progress.DailyWorkReportId = updateDailyWorkReportIn.Id;
            //    progress.Deleted = false;
            //    progress.CreateUser = progress.CreateUser == null ? UserTokenInfo.id : progress.CreateUser;
            //    progress.CreateDate = progress.CreateDate == null ? DateTime.Now : progress.CreateDate;
            //    progress.UpdateUser = UserTokenInfo.id;
            //    progress.UpdateDate = DateTime.Now;
            //    reportProgresses.Add(progress);
            //});

            //List<Db_crm_work_report_receiver> receiverList = new List<Db_crm_work_report_receiver>();
            //updateDailyWorkReportIn.ReportReceiver.ForEach(item =>
            //{
            //    Db_crm_work_report_receiver receiver = new Db_crm_work_report_receiver();
            //    receiver.Id = receiver.Id == null ? Guid.NewGuid().ToString() : receiver.Id;
            //    receiver.WorkReportId = updateDailyWorkReportIn.Id;
            //    receiver.Type = (int)EnumWorkReportsType.Daily;
            //    receiver.ReceiverId = item.ReceiverId;
            //    receiver.State = (int)EnumReportsReceiverState.IsNotRead;
            //    receiver.Deleted = false;
            //    receiver.CreateUser = receiver.CreateUser == null ? UserTokenInfo.id : receiver.CreateUser;
            //    receiver.CreateDate = receiver.CreateDate == null ? DateTime.Now : receiver.CreateDate;
            //    receiver.UpdateUser = UserTokenInfo.id;
            //    receiver.UpdateDate = DateTime.Now;
            //    receiverList.Add(receiver);

            //});

            //DbOpe_crm_daily_work_report.Instance.UpdateDailyWorkReport(updateDaily, reportCustomers, reportProgresses, receiverList, updateDailyWorkReportIn.ReportAttachs, updateDailyWorkReportIn.ReportImgs);
            #endregion

            return DbOpe_crm_daily_work_report.Instance.UpdateDailyWorkReport(updateDailyWorkReportIn, UserId);

        }
        #endregion
        #region 周报部分
        /// <summary>
        /// 接口名称：AddWeekWorkReport    
        /// 协议类型：http/post       
        /// 接口描述：添加工作周报信息。保存草稿：添加工作周报信息，状态为草稿。新建：添加工作周报信息，状态为已发布。
        /// </summary>
        /// <param name="addWeekWorkReportIn"></param>
        public WorkReportActionResult AddWeekWorkReport(AddWeekWorkReport_In addWeekWorkReportIn)
        {
            Db_crm_week_work_report WeekWorkReport = addWeekWorkReportIn.MappingTo<Db_crm_week_work_report>();
            WeekWorkReport.Id = Guid.NewGuid().ToString();
            WeekWorkReport.UserId = UserTokenInfo.id;
            WeekWorkReport.State = addWeekWorkReportIn.SubmitState;
            WeekWorkReport.Deleted = false;
            WeekWorkReport.CreateUser = UserTokenInfo.id;
            WeekWorkReport.CreateDate = DateTime.Now;
            WeekWorkReport.UpdateUser = UserTokenInfo.id;
            WeekWorkReport.UpdateDate = DateTime.Now;

            if (WeekWorkReport.State != 0)
            {
                if (string.IsNullOrEmpty(WeekWorkReport.WorkSummary))
                {
                    throw new ApiException("本周工作总结未填写");
                }
                if (string.IsNullOrEmpty(WeekWorkReport.PremiumCustomersSummary))
                {
                    throw new ApiException("本周优质客户总结未填写");
                }
                if (string.IsNullOrEmpty(WeekWorkReport.PerformanceProgress))
                {
                    throw new ApiException("本周业绩进展情况未填写");
                }
                if (string.IsNullOrEmpty(WeekWorkReport.ProblemsEncountered))
                {
                    throw new ApiException("本周遇到的问题未填写");
                }
                if (string.IsNullOrEmpty(WeekWorkReport.PerformanceForecast))
                {
                    throw new ApiException("下周业绩预计和计划未填写");
                }
            }
            List<Db_crm_week_work_report_progress> reportProgresses = new List<Db_crm_week_work_report_progress>();
            //if (addWeekWorkReportIn.ReportProgress != null)
            if (!string.IsNullOrEmpty(addWeekWorkReportIn.ReportProgressJsonStr) && (addWeekWorkReportIn.ReportProgress == null || addWeekWorkReportIn.ReportProgress.Count == 0))
            {
                addWeekWorkReportIn.ReportProgress = JsonSerializer.Deserialize<List<ReportProgress>>(addWeekWorkReportIn.ReportProgressJsonStr);

                addWeekWorkReportIn.ReportProgress.ForEach(item =>
                {
                    Db_crm_week_work_report_progress progress = item.MappingTo<Db_crm_week_work_report_progress>();

                    progress.Id = Guid.NewGuid().ToString();
                    progress.WeekWorkReportId = WeekWorkReport.Id;
                    progress.Deleted = false;
                    progress.CreateUser = UserTokenInfo.id;
                    progress.CreateDate = DateTime.Now;
                    progress.UpdateUser = UserTokenInfo.id;
                    progress.UpdateDate = DateTime.Now;
                    reportProgresses.Add(progress);
                });
            }
            List<Db_crm_work_report_receiver> receiverList = new List<Db_crm_work_report_receiver>();
            if (!string.IsNullOrEmpty(addWeekWorkReportIn.ReportReceiverJsonStr) && (addWeekWorkReportIn.ReportReceiver == null || addWeekWorkReportIn.ReportReceiver.Count == 0))
            {
                addWeekWorkReportIn.ReportReceiver = JsonSerializer.Deserialize<List<ReportReceiver>>(addWeekWorkReportIn.ReportReceiverJsonStr);
                addWeekWorkReportIn.ReportReceiver.ForEach(item =>
                {
                    Db_crm_work_report_receiver receiver = new Db_crm_work_report_receiver();
                    receiver.Id = Guid.NewGuid().ToString();
                    receiver.WorkReportId = WeekWorkReport.Id;
                    receiver.Type = (int)EnumWorkReportsType.Week;
                    receiver.ReceiverId = item.ReceiverId;
                    receiver.State = (int)EnumReportsReceiverState.IsNotRead;
                    receiver.Deleted = false;
                    receiver.CreateUser = UserTokenInfo.id;
                    receiver.CreateDate = DateTime.Now;
                    receiver.UpdateUser = UserTokenInfo.id;
                    receiver.UpdateDate = DateTime.Now;
                    receiverList.Add(receiver);

                });
            }

            return DbOpe_crm_week_work_report.Instance.AddWeekWorkReport(WeekWorkReport, reportProgresses, receiverList, addWeekWorkReportIn.ReportAttachs, addWeekWorkReportIn.ReportImgs);

        }
        /// <summary>
        /// 接口名称：UpdateWeekWorkReport        
        /// 协议类型：http/post        
        /// 接口描述：修改工作周报信息。保存草稿：添加工作周报信息，状态为草稿。新建：添加工作周报信息，状态为已发布。
        /// </summary>
        /// <param name="updateWeekWorkReportIn"></param>
        public WorkReportActionResult UpdateWeekWorkReport(UpdateWeekWorkReport_In updateWeekWorkReportIn)
        {
            return DbOpe_crm_week_work_report.Instance.UpdateWeekWorkReport(updateWeekWorkReportIn, UserId);
        }



        #endregion
        #region 月报部分
        /// <summary>
        /// 接口名称：AddMonthWorkReport
        /// 协议类型：http/post
        /// 接口描述：添加工作月报信息。保存草稿：添加工作月报信息，状态为草稿。新建：添加工作月报信息，状态为已发布。
        /// </summary>
        /// <param name="addMonthWorkReportIn"></param>
        public WorkReportActionResult AddMonthWorkReport(AddMonthWorkReport_In addMonthWorkReportIn)
        {
            Db_crm_month_work_report MonthWorkReport = addMonthWorkReportIn.MappingTo<Db_crm_month_work_report>();
            MonthWorkReport.Id = Guid.NewGuid().ToString();
            MonthWorkReport.UserId = UserTokenInfo.id;
            MonthWorkReport.State = addMonthWorkReportIn.SubmitState;
            MonthWorkReport.Deleted = false;
            MonthWorkReport.CreateUser = UserTokenInfo.id;
            MonthWorkReport.CreateDate = DateTime.Now;
            MonthWorkReport.UpdateUser = UserTokenInfo.id;
            MonthWorkReport.UpdateDate = DateTime.Now;
            if (MonthWorkReport.State != 0)
            {
                if (string.IsNullOrEmpty(MonthWorkReport.WorkSummary))
                {
                    throw new ApiException("本月工作总结未填写");
                }
                if (string.IsNullOrEmpty(MonthWorkReport.PremiumCustomersSummary))
                {
                    throw new ApiException("本月优质客户情况总结未填写");
                }
                if (string.IsNullOrEmpty(MonthWorkReport.PerformanceProgress))
                {
                    throw new ApiException("本月业绩完成情况未填写");
                }
                if (string.IsNullOrEmpty(MonthWorkReport.PerformanceForecast))
                {
                    throw new ApiException("下月业绩预计和计划未填写");
                }
                if (string.IsNullOrEmpty(MonthWorkReport.ProblemsEncountered))
                {
                    throw new ApiException("本月遇到的问题未填写");
                }
            }



            List<Db_crm_month_work_report_expected_customer> customerList = new List<Db_crm_month_work_report_expected_customer>();

            if (!string.IsNullOrEmpty(addMonthWorkReportIn.ReportExpectedCustomerJsonStr) && (addMonthWorkReportIn.ReportExpectedCustomer == null || addMonthWorkReportIn.ReportExpectedCustomer.Count == 0))
            {
                addMonthWorkReportIn.ReportExpectedCustomer = JsonSerializer.Deserialize<List<ReportExpectedCustomer>>(addMonthWorkReportIn.ReportExpectedCustomerJsonStr);


                addMonthWorkReportIn.ReportExpectedCustomer.ForEach(item =>
                {
                    Db_crm_month_work_report_expected_customer customer = item.CustomerInfo.MappingTo<Db_crm_month_work_report_expected_customer>();

                    customer.Id = Guid.NewGuid().ToString();
                    customer.CustomerId = item.CustomerInfo.Id;
                    customer.MonthWorkReportId = MonthWorkReport.Id;
                    customer.Deleted = false;
                    customer.CreateUser = UserTokenInfo.id;
                    customer.CreateDate = DateTime.Now;
                    customer.UpdateUser = UserTokenInfo.id;
                    customer.UpdateDate = DateTime.Now;
                    customerList.Add(customer);
                });
            }

            List<Db_crm_work_report_receiver> receiverList = new List<Db_crm_work_report_receiver>();
            if (!string.IsNullOrEmpty(addMonthWorkReportIn.ReportReceiverJsonStr) && (addMonthWorkReportIn.ReportReceiver == null || addMonthWorkReportIn.ReportReceiver.Count == 0))
            {
                addMonthWorkReportIn.ReportReceiver = JsonSerializer.Deserialize<List<ReportReceiver>>(addMonthWorkReportIn.ReportReceiverJsonStr);
                addMonthWorkReportIn.ReportReceiver.ForEach(item =>
                {
                    Db_crm_work_report_receiver receiver = new Db_crm_work_report_receiver();
                    receiver.Id = Guid.NewGuid().ToString();
                    receiver.WorkReportId = MonthWorkReport.Id;
                    receiver.Type = (int)EnumWorkReportsType.Month;
                    receiver.ReceiverId = item.ReceiverId;
                    receiver.State = (int)EnumReportsReceiverState.IsNotRead;
                    receiver.Deleted = false;
                    receiver.CreateUser = UserTokenInfo.id;
                    receiver.CreateDate = DateTime.Now;
                    receiver.UpdateUser = UserTokenInfo.id;
                    receiver.UpdateDate = DateTime.Now;
                    receiverList.Add(receiver);

                });
            }

            return DbOpe_crm_month_work_report.Instance.AddMonthWorkReport(MonthWorkReport, customerList, receiverList, addMonthWorkReportIn.ReportAttachs, addMonthWorkReportIn.ReportImgs);

        }

        public WorkReportActionResult UpdateMonthWorkReport(UpdateMonthWorkReport_In updateMonthWorkReportIn)
        {
            return DbOpe_crm_month_work_report.Instance.UpdateMonthWorkReport(updateMonthWorkReportIn, UserId);
        }
        #endregion
        #region 管理日报
        /// <summary>
        /// 接口名称：AddManagerDailyWorkReport        
        /// 协议类型：http/post        
        /// 接口描述：添加工作管理日报信息。保存草稿：添加工作管理日报信息，状态为草稿。新建：添加工作管理日报信息，状态为已发布。
        /// </summary>
        /// <param name="addManagerDailyWorkReportIn"></param>
        public WorkReportActionResult AddManagerDailyWorkReport(AddManagerDailyWorkReport_In addManagerDailyWorkReportIn)
        {
            Db_crm_manager_daily_work_report ManagerDailyWorkReport = addManagerDailyWorkReportIn.MappingTo<Db_crm_manager_daily_work_report>();
            ManagerDailyWorkReport.Id = Guid.NewGuid().ToString();
            ManagerDailyWorkReport.UserId = UserTokenInfo.id;
            ManagerDailyWorkReport.State = addManagerDailyWorkReportIn.SubmitState;
            ManagerDailyWorkReport.Deleted = false;
            ManagerDailyWorkReport.CreateUser = UserTokenInfo.id;
            ManagerDailyWorkReport.CreateDate = DateTime.Now;
            ManagerDailyWorkReport.UpdateUser = UserTokenInfo.id;
            ManagerDailyWorkReport.UpdateDate = DateTime.Now;

            if (ManagerDailyWorkReport.State != 0)
            {
                if (string.IsNullOrEmpty(ManagerDailyWorkReport.CommunicationSummary))
                {
                    throw new ApiException("当日晨夕会与其他沟通摘要未填写");
                }
                if (string.IsNullOrEmpty(ManagerDailyWorkReport.ResumptionSummary))
                {
                    throw new ApiException("当日对员工赋能以及复盘摘要未填写");
                }
                if (string.IsNullOrEmpty(ManagerDailyWorkReport.AppraisalSummary))
                {
                    throw new ApiException("当日对员工的工作考核摘要未填写");
                }
                if (string.IsNullOrEmpty(ManagerDailyWorkReport.Problems))
                {
                    throw new ApiException("团队遇到的问题及挑战未填写");
                }
                if (string.IsNullOrEmpty(ManagerDailyWorkReport.Support))
                {
                    throw new ApiException("团队需要的支持与协助未填写");
                }
            }

            List<Db_crm_manager_daily_work_report_progress> reportProgresses = new List<Db_crm_manager_daily_work_report_progress>();
            if (!string.IsNullOrEmpty(addManagerDailyWorkReportIn.ReportProgressJsonStr) && (addManagerDailyWorkReportIn.ReportProgress == null || addManagerDailyWorkReportIn.ReportProgress.Count == 0))
            {
                addManagerDailyWorkReportIn.ReportProgress = JsonSerializer.Deserialize<List<ReportProgress>>(addManagerDailyWorkReportIn.ReportProgressJsonStr);
                addManagerDailyWorkReportIn.ReportProgress.ForEach(item =>
                {
                    Db_crm_manager_daily_work_report_progress progress = item.MappingTo<Db_crm_manager_daily_work_report_progress>();

                    progress.Id = Guid.NewGuid().ToString();
                    progress.ManagerDailyWorkReportId = ManagerDailyWorkReport.Id;
                    progress.Deleted = false;
                    progress.CreateUser = UserTokenInfo.id;
                    progress.CreateDate = DateTime.Now;
                    progress.UpdateUser = UserTokenInfo.id;
                    progress.UpdateDate = DateTime.Now;
                    reportProgresses.Add(progress);
                });
            }
            List<Db_crm_work_report_receiver> receiverList = new List<Db_crm_work_report_receiver>();
            if (!string.IsNullOrEmpty(addManagerDailyWorkReportIn.ReportReceiverJsonStr) && (addManagerDailyWorkReportIn.ReportReceiver == null || addManagerDailyWorkReportIn.ReportReceiver.Count == 0))
            {
                addManagerDailyWorkReportIn.ReportReceiver = JsonSerializer.Deserialize<List<ReportReceiver>>(addManagerDailyWorkReportIn.ReportReceiverJsonStr);

                addManagerDailyWorkReportIn.ReportReceiver.ForEach(item =>
                {
                    Db_crm_work_report_receiver receiver = new Db_crm_work_report_receiver();
                    receiver.Id = Guid.NewGuid().ToString();
                    receiver.WorkReportId = ManagerDailyWorkReport.Id;
                    receiver.Type = (int)EnumWorkReportsType.Manager;
                    receiver.ReceiverId = item.ReceiverId;
                    receiver.State = (int)EnumReportsReceiverState.IsNotRead;
                    receiver.Deleted = false;
                    receiver.CreateUser = UserTokenInfo.id;
                    receiver.CreateDate = DateTime.Now;
                    receiver.UpdateUser = UserTokenInfo.id;
                    receiver.UpdateDate = DateTime.Now;
                    receiverList.Add(receiver);

                });

            }
            return DbOpe_crm_manager_daily_work_report.Instance.AddManagerDailyWorkReport(ManagerDailyWorkReport, reportProgresses, receiverList, addManagerDailyWorkReportIn.ReportAttachs, addManagerDailyWorkReportIn.ReportImgs);

        }
        /// <summary>
        /// 接口名称：UpdateManagerDailyWorkReport        
        /// 协议类型：http/post
        /// 接口描述：修改工作管理日报信息。保存草稿：添加工作管理日报信息，状态为草稿。新建：添加工作管理日报信息，状态为已发布。
        /// </summary>
        /// <param name="updateManagerDailyWorkReportIn"></param>
        public WorkReportActionResult UpdateManagerDailyWorkReport(UpdateManagerDailyWorkReport_In updateManagerDailyWorkReportIn)
        {
            return DbOpe_crm_manager_daily_work_report.Instance.UpdateManagerDailyWorkReport(updateManagerDailyWorkReportIn, UserId);
        }
        #endregion
        #region 简报部分
        /// <summary>
        /// 接口名称：AddBriefReport
        /// 协议类型：http/post
        /// 接口描述：添加工作月报信息。保存草稿：添加工作月报信息，状态为草稿。新建：添加工作月报信息，状态为已发布。
        /// </summary>
        /// <param name="addBriefReportIn"></param>
        public WorkReportActionResult AddBriefReport(AddBriefReport_In addBriefReportIn)
        {
            Db_crm_brief_report BriefReport = addBriefReportIn.MappingTo<Db_crm_brief_report>();
            BriefReport.Id = Guid.NewGuid().ToString();
            BriefReport.UserId = UserTokenInfo.id;
            BriefReport.State = addBriefReportIn.SubmitState;
            BriefReport.Deleted = false;
            BriefReport.CreateUser = UserTokenInfo.id;
            BriefReport.CreateDate = DateTime.Now;
            BriefReport.UpdateUser = UserTokenInfo.id;
            BriefReport.UpdateDate = DateTime.Now;

            if (BriefReport.State != 0)
            {
                if (string.IsNullOrEmpty(BriefReport.SupportTransactions))
                {
                    throw new ApiException("营销支持事务未填写");
                }
                if (string.IsNullOrEmpty(BriefReport.Support))
                {
                    throw new ApiException("所需的支持与协助未填写");
                }
                if (string.IsNullOrEmpty(BriefReport.Solutions))
                {
                    throw new ApiException("合理化建议与方案未填写");
                }
                if (string.IsNullOrEmpty(BriefReport.SummaryReport))
                {
                    throw new ApiException("本人工作总结报告未填写");
                }
            }
            List<Db_crm_work_report_receiver> receiverList = new List<Db_crm_work_report_receiver>();
            if (!string.IsNullOrEmpty(addBriefReportIn.ReportReceiverJsonStr) && (addBriefReportIn.ReportReceiver == null || addBriefReportIn.ReportReceiver.Count == 0))
            {
                addBriefReportIn.ReportReceiver = JsonSerializer.Deserialize<List<ReportReceiver>>(addBriefReportIn.ReportReceiverJsonStr);

                addBriefReportIn.ReportReceiver.ForEach(item =>
                {
                    Db_crm_work_report_receiver receiver = new Db_crm_work_report_receiver();
                    receiver.Id = Guid.NewGuid().ToString();
                    receiver.WorkReportId = BriefReport.Id;
                    receiver.Type = (int)EnumWorkReportsType.Brief;
                    receiver.ReceiverId = item.ReceiverId;
                    receiver.State = (int)EnumReportsReceiverState.IsNotRead;
                    receiver.Deleted = false;
                    receiver.CreateUser = UserTokenInfo.id;
                    receiver.CreateDate = DateTime.Now;
                    receiver.UpdateUser = UserTokenInfo.id;
                    receiver.UpdateDate = DateTime.Now;
                    receiverList.Add(receiver);

                });

            }
            return DbOpe_crm_brief_report.Instance.AddBriefReport(BriefReport, receiverList, addBriefReportIn.ReportAttachs, addBriefReportIn.ReportImgs);

        }

        public WorkReportActionResult UpdateBriefReport(UpdateBriefReport_In updateBriefReportIn)
        {
            return DbOpe_crm_brief_report.Instance.UpdateBriefReport(updateBriefReportIn, UserId);
        }
        #endregion

        #region 报告通用型接口 跟踪信息获取/管理者获取
        public List<CustomerTraceRecord_Out> GetCustomerTraceRecord(string userId, EnumWorkReportsType type)
        {
            //根据当前人员获取追踪记录中，及相关的customerID
            //根据CostomerID获取用户信息
            return DbOpe_crm_daily_work_report.Instance.GetTrackingRecordByUserId(userId, type);
        }

        public WeekWorkReport WeekWorkReportStatistics()
        {
            WeekWorkReportStatistics_OUT wws = BLL_Statistics.Instance.WeekWorkReportStatistics();
            WeekWorkReport result = new WeekWorkReport();
            if (wws is not null)
            {

                result.SalesAchivement = wws.SalesAchivement_Week;
                result.EffectiveAchivement = wws.EffectiveAchivement_Week;
                result.ContractAmount = wws.ContractAmount_Week;
                result.CustomerTypeA = wws.NewCustomerTypeStatistics.Where(e => e.Name.Contains('A')).ToList().Select(e => e.Count).First();
                result.CustomerTypeB = wws.NewCustomerTypeStatistics.Where(e => e.Name.Contains('B')).ToList().Select(e => e.Count).First();
                result.CustomerTypeC = wws.NewCustomerTypeStatistics.Where(e => e.Name.Contains('C')).ToList().Select(e => e.Count).First();
            }
            else
            {
                result.SalesAchivement = "0";
                result.EffectiveAchivement = "0";
                result.ContractAmount = "0";
                result.CustomerTypeA = 0;
                result.CustomerTypeB = 0;
                result.CustomerTypeC = 0;
            }

            return result;
        }

        public MonthWorkReport MonthWorkReportStatistics()
        {
            MonthWorkReportStatistics_OUT mws = BLL_Statistics.Instance.MonthWorkReportStatistics();
            MonthWorkReport result = new MonthWorkReport();
            if (mws is not null)
            {
                result.SalesAchivement = mws.SalesAchivement_Month;
                result.SalesAchivementTarget = mws.SalesAchivementTarget_Month;
                result.ContractAmount = mws.ContractAmount_Month;
                result.CustomerTypeA = mws.NewCustomerTypeStatistics.Where(e => e.Name.Contains('A')).ToList().Select(e => e.Count).First();
                result.CustomerTypeB = mws.NewCustomerTypeStatistics.Where(e => e.Name.Contains('B')).ToList().Select(e => e.Count).First();
                result.CustomerTypeC = mws.NewCustomerTypeStatistics.Where(e => e.Name.Contains('C')).ToList().Select(e => e.Count).First();
                result.SignCustomerNum = mws.SignCustomerNum_Month;
            }
            else
            {
                result.SalesAchivement = "0";
                result.SalesAchivementTarget = "0";
                result.ContractAmount = "0";
                result.CustomerTypeA = 0;
                result.CustomerTypeB = 0;
                result.CustomerTypeC = 0;
                result.SignCustomerNum = 0;
            }
            return result;
        }

        public ManagerDailyWorkReport ManagerReportStatistics()
        {
            ManagerReportStatistics_OUT mrs = BLL_Statistics.Instance.ManagerReportStatistics();
            ManagerDailyWorkReport result = new ManagerDailyWorkReport();
            if (mrs is not null)
            {
                result.User_SalesAchivement = mrs.User_SalesAchivement_Today;
                result.Org_SalesAchivement = mrs.Org_SalesAchivement_Today;
                result.Org_SalesAchivement_Month = mrs.Org_SalesAchivement_Month;
                result.Org_SalesAchivementTarget_Month = mrs.Org_SalesAchivementTarget_Month;
                result.Org_ContractAmount_Month = mrs.Org_ContractAmount_Month;
                result.Org_ToBeRecivedCustomer_Month = mrs.Org_ToBeRecivedCustomer_Month;
                result.Org_ToBeRecivedAmount_Month = mrs.Org_ToBeRecivedAmount_Month;
            }
            else
            {
                result.User_SalesAchivement = "0";
                result.Org_SalesAchivement = "0";
                result.Org_SalesAchivement_Month = "0";
                result.Org_SalesAchivementTarget_Month = "0";
                result.Org_ContractAmount_Month = "0";
                result.Org_ToBeRecivedCustomer_Month = 0;
                result.Org_ToBeRecivedAmount_Month = "0";
            }
            return result;
        }
        /// <summary>
        /// 获取工作报告接收人(树形结构)
        /// </summary>
        /// <param name="workReportsType"></param>
        /// <returns></returns>
        public List<GetOrganizationWithUserTree_Out> GetReportReciver(EnumWorkReportsType workReportsType)
        {
            var baseData = DbOpe_crm_daily_work_report.Instance.GetReportReciver(UserId, workReportsType);
            List<GetOrganizationWithUserTree_Out> outData = new();
            if (baseData is not null and { Count: > 0 })
            {
                GetOrganizationWithUserTree_Out temp = default;
                foreach (GetBaseOrgWithUserTree item in baseData)
                {
                    temp = new GetOrganizationWithUserTree_Out()
                    {
                        Id = item.Id,
                        Name = item.OrgName,
                        Leaf = false,
                        Child = BLL_Organization.Instance.ConvertTreeOutModel(item.OrgChild, item.UserChild),
                    };
                    outData.Add(temp);
                }
            }
            return outData;
        }
        #endregion



    }

}
