using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Log;
using CRM2_API.Model.BLLModel.Enum;
using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace CRM2_API.Common.Log
{
    /// <summary>
    /// ClickHouse日志服务
    /// </summary>
    public class LogService
    {
        private static readonly LogService _instance = new LogService();
        private readonly ConcurrentQueue<Db_clickhouse_log> _logQueue = new ConcurrentQueue<Db_clickhouse_log>();
        private readonly Timer _flushTimer;
        private readonly SemaphoreSlim _syncLock = new SemaphoreSlim(1, 1);
        private bool _disposed;
        private int _batchSize = 50;

        /// <summary>
        /// 获取日志服务实例
        /// </summary>
        public static LogService Instance => _instance;

        /// <summary>
        /// 初始化日志服务
        /// </summary>
        private LogService()
        {
            // 每500毫秒刷新一次日志队列
            _flushTimer = new Timer(FlushLogs, null, TimeSpan.FromMilliseconds(500), TimeSpan.FromMilliseconds(500));
            
            // 注册应用程序退出事件
            AppDomain.CurrentDomain.ProcessExit += (sender, args) => 
            {
                Console.WriteLine("应用程序退出，正在刷新日志队列...");
                _disposed = true;
                ForceFlush();
            };
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public void Debug(string message, string controller = null, string action = null, string requestId = null, int? statusCode = null)
        {
            WriteLog("Debug", message, controller, action, requestId, statusCode);
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        public void Info(string message, string controller = null, string action = null, string requestId = null, int? statusCode = null)
        {
            WriteLog("Info", message, controller, action, requestId, statusCode);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        public void Warn(string message, string controller = null, string action = null, string requestId = null, int? statusCode = null)
        {
            WriteLog("Warn", message, controller, action, requestId, statusCode);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        public void Error(string message, Exception ex = null, string controller = null, string action = null, string requestId = null, int? statusCode = null)
        {
            string errorMessage = message;
            if (ex != null)
            {
                errorMessage += $" 异常: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $" 内部异常: {ex.InnerException.Message}";
                }
            }
            
            WriteLog("Error", errorMessage, controller, action, requestId, statusCode, ex?.ToString());
        }

        /// <summary>
        /// 记录API请求日志
        /// </summary>
        public void LogApiRequest(
            string requestId,
            string controller,
            string action,
            int duration,
            int statusCode,
            string username,
            string userId,
            string ipAddress,
            string httpMethod,
            string requestUrl,
            byte successValue,
            string errorMessage,
            int environment,
            string paramsContent)
        {
            try
            {
                if (!IsLoggingEnabled())
                    return;

                var log = new Db_clickhouse_log
                {
                    Timestamp = DateTime.Now,
                    Level = successValue == 1 ? "Info" : "Error",
                    Message = $"[API请求] {controller}.{action} 耗时:{duration}ms",
                    RequestId = requestId,
                    Controller = controller,
                    Action = action,
                    Duration = duration,
                    StatusCode = statusCode,
                    Username = username ?? "Unknown",
                    UserId = userId ?? "Unknown",
                    IpAddress = ipAddress,
                    HttpMethod = httpMethod,
                    RequestUrl = requestUrl,
                    Success = successValue,
                    ErrorMessage = errorMessage,
                    Environment = environment,
                    Params = paramsContent
                };

                _logQueue.Enqueue(log);
                
                // 如果队列大小达到批处理大小，立即刷新
                if (_logQueue.Count >= _batchSize)
                {
                    Task.Run(() => FlushLogs(null));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入API日志时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        private void WriteLog(string level, string message, string controller = null, string action = null, string requestId = null, int? statusCode = null, string errorMessage = null)
        {
            try
            {
                if (!IsLoggingEnabled())
                    return;

                var log = new Db_clickhouse_log
                {
                    Timestamp = DateTime.Now,
                    Level = level,
                    Message = message,
                    RequestId = requestId ?? Guid.NewGuid().ToString(),
                    Controller = controller,
                    Action = action,
                    StatusCode = statusCode ?? 0,
                    Success = level != "Error" ? (byte)1 : (byte)0,
                    ErrorMessage = errorMessage
                };

                _logQueue.Enqueue(log);
                
                // 如果队列大小达到批处理大小，立即刷新
                if (_logQueue.Count >= _batchSize)
                {
                    Task.Run(() => FlushLogs(null));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否启用日志
        /// </summary>
        private bool IsLoggingEnabled()
        {
            return AppSettings.LogConfig != null && AppSettings.LogConfig.EnableLogging && DbContext.ClickHouseDb != null;
        }

        /// <summary>
        /// 强制刷新日志队列
        /// </summary>
        public void ForceFlush()
        {
            FlushLogs(null);
        }

        /// <summary>
        /// 刷新日志队列到ClickHouse
        /// </summary>
        private async void FlushLogs(object state)
        {
            if (_disposed || _logQueue.IsEmpty || DbContext.ClickHouseDb == null)
                return;

            // 使用信号量确保同一时间只有一个线程在刷新日志
            if (!await _syncLock.WaitAsync(0))
                return;

            try
            {
                // 准备批量日志
                var batchLogs = new List<Db_clickhouse_log>();
                int count = Math.Min(_logQueue.Count, _batchSize);

                for (int i = 0; i < count; i++)
                {
                    if (_logQueue.TryDequeue(out Db_clickhouse_log log))
                    {
                        batchLogs.Add(log);
                    }
                }

                if (batchLogs.Count > 0)
                {
                    try
                    {
                        Console.WriteLine($"正在写入{batchLogs.Count}条日志到ClickHouse...");
                        
                        // 使用SqlSugar批量插入
                        var result = DbContext.ClickHouseDb.Fastest<Db_clickhouse_log>().BulkCopy(batchLogs);
                        
                        Console.WriteLine($"成功写入日志，影响行数: {batchLogs.Count}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"写入日志到ClickHouse失败: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                        }
                        
                        // 发生异常时，将日志重新入队
                        foreach (var log in batchLogs)
                        {
                            _logQueue.Enqueue(log);
                        }
                    }
                }
            }
            finally
            {
                _syncLock.Release();
            }
        }
    }
} 