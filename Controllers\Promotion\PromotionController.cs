﻿using System.ComponentModel;
using CRM2_API.BLL.Promotion;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.Promotion;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 晋升设置控制器
    /// </summary>
    [Description("晋升设置控制器")]
    public class PromotionController : MyControllerBase
    {
        public PromotionController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 修改晋升信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updatePromotionIns"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdatePromotion_Out UpdatePromotion(UpdatePromotion_In updatePromotionIns)
        {
            return BLL_Promotion.Instance.UpdatePromotion(updatePromotionIns, true);
        }

        /// <summary>
        /// 根据类别获取晋升信息
        /// </summary>
        /// <param name="getPromotionListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public GetPromotionList_Out GetPromotionList(GetPromotionList_In getPromotionListIn)
        {
            return DbOpe_sys_promotion.Instance.GetPromotionListByType(getPromotionListIn);
        }

        /// <summary>
        /// 根据当前时间自动计算上一周期客户保留数变化
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public CustomerMaxSaverResult CustomerSaverCountAutoUpdate()
        {
            DateTime dateTime_in = DateTime.Now;
            return BLL_Promotion.Instance.CustomerSaverCountAutoUpdate(dateTime_in);
        }

        /// <summary>
        /// 测试保留数接口
        /// </summary>
        /// <param name="testCustomer"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public CustomerMaxSaverResult CustomerSaverCountAutoUpdateResultTest(CustomerMaxSaverResult_In testCustomer)
        {
            DateTime dateTime_in = new DateTime(testCustomer.year, testCustomer.month, 1, 0, 0, 0); ;
            return BLL_Promotion.Instance.CustomerSaverCountAutoUpdate(dateTime_in);
        }
        /// <summary>
        /// 执行传入月份的保留数更新
        /// </summary>
        /// <param name="testCustomer"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public CustomerMaxSaverResult AcceptUpdateCustomerMaxSaverResult(CustomerMaxSaverResult_In testCustomer)
        {
            return BLL_Promotion.Instance.AcceptUpdateCustomerMaxSaverResult(testCustomer);
        }
        /// <summary>
        /// 查询传入月份用户保留数计算结果(默认所有)
        /// </summary>
        /// <param name="searchMaxSaverResult_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public ApiTableOut<CustomerMaxSaver_Out> SearchCustomerMaxSaverResult(SearchMaxSaverResult_In searchMaxSaverResult_In)
        {
            int total = 0;
            var list = DbOpe_sys_usermaxsaveustomer_calclog.Instance.SearchCustomerMaxSaverResult(searchMaxSaverResult_In, ref total);
            return GetApiTableOut(list, total);
        }
    }
}