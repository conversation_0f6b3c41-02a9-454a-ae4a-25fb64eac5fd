using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CRM2_API.Common.JWT;
using CRM2_API.Common.Utils;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SqlSugar;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 历史匹配功能部分
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        /// <summary>
        /// 历史发票到账匹配模型
        /// </summary>
        public class HistoryMatchingModel
        {
            /// <summary>
            /// 客户编码
            /// </summary>
            public string CustomerCode { get; set; }

            /// <summary>
            /// 付款公司
            /// </summary>
            public string PaymentCompany { get; set; }

            /// <summary>
            /// 收款公司
            /// </summary>
            public string ReceivingCompany { get; set; }

            /// <summary>
            /// 到账金额
            /// </summary>
            public decimal ReceiptAmount { get; set; }

            /// <summary>
            /// 支付方式
            /// </summary>
            public string PaymentMethod { get; set; }

            /// <summary>
            /// 到账日期
            /// </summary>
            public DateTime? ReceiptDate { get; set; }

            /// <summary>
            /// 是否开票
            /// </summary>
            public bool HasInvoice { get; set; }

            /// <summary>
            /// 开票金额
            /// </summary>
            public decimal? InvoiceAmount { get; set; }

            /// <summary>
            /// 开票日期
            /// </summary>
            public DateTime? InvoiceDate { get; set; }
        }

        /// <summary>
        /// 历史匹配结果
        /// </summary>
        public class HistoryMatchingResult
        {
            /// <summary>
            /// 处理的客户总数
            /// </summary>
            public int TotalCustomers { get; set; }

            /// <summary>
            /// 成功匹配的记录数
            /// </summary>
            public int SuccessMatchingCount { get; set; }

            /// <summary>
            /// 未找到到账信息的记录数
            /// </summary>
            public int MissingReceiptCount { get; set; }

            /// <summary>
            /// 未找到发票信息的记录数
            /// </summary>
            public int MissingInvoiceCount { get; set; }

            /// <summary>
            /// 处理的总记录数
            /// </summary>
            public int TotalRecords { get; set; }

            /// <summary>
            /// 问题数据列表
            /// </summary>
            public List<string> ProblemRecords { get; set; } = new List<string>();
        }

        /// <summary>
        /// 导入Excel并进行历史发票到账匹配
        /// </summary>
        /// <param name="excelFile">Excel文件流</param>
        /// <returns>匹配结果</returns>
        public HistoryMatchingResult ImportAndMatchHistoryData(Stream excelFile)
        {
            if (excelFile == null)
            {
                throw new ApiException("Excel文件不能为空");
            }

            try
            {
                // 获取当前用户ID
                string userId = TokenModel.Instance.id;
                if (string.IsNullOrEmpty(userId))
                {
                    throw new ApiException("无法获取当前用户信息");
                }

                // 创建结果对象
                var result = new HistoryMatchingResult();

                // 读取Excel文件
                var dataList = ReadExcelData(excelFile);
                result.TotalRecords = dataList.Count;

                // 按客户编码分组
                var customerGroups = dataList.GroupBy(d => d.CustomerCode).ToList();
                result.TotalCustomers = customerGroups.Count;

                // 逐个客户处理
                foreach (var customerGroup in customerGroups)
                {
                    string customerCode = customerGroup.Key;
                    var records = customerGroup.ToList();

                    try
                    {
                        ProcessCustomerRecords(customerCode, records, userId, result);
                    }
                    catch (Exception ex)
                    {
                        // 记录处理客户数据时的异常
                        LogUtil.AddErrorLog($"处理客户[{customerCode}]的历史匹配数据时发生异常: {ex.Message}");
                        result.ProblemRecords.Add($"客户[{customerCode}]处理失败: {ex.Message}");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"导入历史发票到账匹配数据失败: {ex.Message}");
                throw new ApiException("导入历史发票到账匹配数据失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 从Excel文件读取数据
        /// </summary>
        /// <param name="excelFile">Excel文件流</param>
        /// <returns>历史匹配数据列表</returns>
        private List<HistoryMatchingModel> ReadExcelData(Stream excelFile)
        {
            var dataList = new List<HistoryMatchingModel>();

            try
            {
                // 使用NPOI读取Excel
                IWorkbook workbook = new XSSFWorkbook(excelFile);
                ISheet sheet = workbook.GetSheetAt(0); // 获取第一个工作表

                // 从第二行开始读取（第一行是标题）
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null) continue;

                    try
                    {
                        var model = new HistoryMatchingModel
                        {
                            CustomerCode = GetCellStringValue(row.GetCell(0)),
                            PaymentCompany = GetCellStringValue(row.GetCell(1)),
                            ReceivingCompany = GetCellStringValue(row.GetCell(2)),
                            ReceiptAmount = GetCellDecimalValue(row.GetCell(3)),
                            PaymentMethod = GetCellStringValue(row.GetCell(4)),
                            ReceiptDate = GetCellDateValue(row.GetCell(5)),
                            HasInvoice = GetCellBoolValue(row.GetCell(6)),
                            InvoiceAmount = row.GetCell(7) != null ? GetCellDecimalValue(row.GetCell(7)) : null,
                            InvoiceDate = GetCellDateValue(row.GetCell(8))
                        };

                        // 只有客户编码、收款公司、付款公司和到账金额都不为空的记录才有效
                        if (!string.IsNullOrEmpty(model.CustomerCode) && 
                            !string.IsNullOrEmpty(model.PaymentCompany) && 
                            !string.IsNullOrEmpty(model.ReceivingCompany) && 
                            model.ReceiptAmount > 0)
                        {
                            dataList.Add(model);
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录单行数据读取异常但继续处理
                        LogUtil.AddErrorLog($"读取Excel第{i + 1}行数据异常: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"读取Excel文件失败: {ex.Message}");
                throw new ApiException("读取Excel文件失败，请确保文件格式正确");
            }

            return dataList;
        }

        /// <summary>
        /// 处理单个客户的所有记录
        /// </summary>
        private void ProcessCustomerRecords(string customerCode, List<HistoryMatchingModel> records, string userId, HistoryMatchingResult result)
        {
            // 根据客户编码查找合同
            var contracts = DbOpe_crm_contract.Instance.GetDataList(c => 
                c.ContractNum == customerCode && 
                c.Deleted == false).ToList();
                
            if (contracts == null || contracts.Count == 0)
            {
                result.ProblemRecords.Add($"未找到客户编码为[{customerCode}]的合同");
                return;
            }
            
            // 获取第一个合同的甲方ID作为客户信息
            string firstPartyId = contracts.First().FirstParty;
            if (string.IsNullOrEmpty(firstPartyId))
            {
                result.ProblemRecords.Add($"合同[{customerCode}]未关联甲方信息");
                return;
            }

            // 使用事务处理
            DbOpe_crm_invoice_receipt_matching.Instance.TransDeal(() =>
            {
                try
                {
                    // 逐条处理客户的记录
                    foreach (var record in records)
                    {
                        try
                        {
                            // 根据到账信息查找系统中的到账记录
                            var receipt = FindReceiptByHistory(record, firstPartyId);
                            if (receipt == null)
                            {
                                result.MissingReceiptCount++;
                                result.ProblemRecords.Add($"客户[{customerCode}]未找到到账记录: 付款公司[{record.PaymentCompany}], 收款公司[{record.ReceivingCompany}], 金额[{record.ReceiptAmount}]");
                                continue;
                            }

                            // 如果Excel中标记了已开票
                            if (record.HasInvoice && record.InvoiceAmount.HasValue && record.InvoiceAmount.Value > 0)
                            {
                                // 查找系统中的发票
                                var invoice = FindInvoiceByHistory(record, receipt.ContractId);
                                if (invoice == null)
                                {
                                    result.MissingInvoiceCount++;
                                    result.ProblemRecords.Add($"客户[{customerCode}]未找到发票记录: 收款公司[{record.ReceivingCompany}], 金额[{record.InvoiceAmount}]");
                                    continue;
                                }

                                // 创建匹配记录
                                CreateHistoryMatchingRecord(invoice, receipt, userId);
                                result.SuccessMatchingCount++;
                            }
                        }
                        catch (Exception ex)
                        {
                            // 记录单条记录处理异常但继续处理
                            LogUtil.AddErrorLog($"处理客户[{customerCode}]的单条记录时发生异常: {ex.Message}");
                            result.ProblemRecords.Add($"客户[{customerCode}]单条记录处理失败: {ex.Message}");
                        }
                    }

                    // 处理完当前客户的所有记录后，检查是否有发票需要更新状态
                    UpdateUnmatchedInvoiceStatus(firstPartyId, userId);
                }
                catch (Exception ex)
                {
                    // 记录事务处理异常
                    LogUtil.AddErrorLog($"处理客户[{customerCode}]的记录事务失败: {ex.Message}");
                    throw;
                }
            });
        }

        /// <summary>
        /// 根据历史数据查找系统中的到账记录
        /// </summary>
        private Db_crm_contract_receiptregister FindReceiptByHistory(HistoryMatchingModel historyData, string firstPartyId)
        {
            // 先查找甲方相关的所有合同
            var contracts = DbOpe_crm_contract.Instance.GetDataList(c => 
                c.FirstParty == firstPartyId && 
                c.Deleted != true).ToList();
                
            if (contracts == null || contracts.Count == 0)
            {
                return null;
            }
            
            // 获取合同ID列表
            var contractIds = contracts.Select(c => c.Id).ToList();
            
            // 查询匹配的到账记录
            var matchingReceipts = DbContext.Crm2Db
                .Queryable<Db_crm_contract_receiptregister, Db_crm_contract_receipt_details, Db_crm_collectioninfo>((r, d, c) => 
                    new JoinQueryInfos(
                        JoinType.Left, r.Id == d.ContractReceiptRegisterId,
                        JoinType.Left, d.CollectionInfoId == c.Id
                    ))
                .Where((r, d, c) => 
                    contractIds.Contains(r.ContractId) && 
                    r.AchievementState == (int)EnumAchievementState.Confirmed && 
                    r.Deleted != true &&
                    c.Deleted != true)
                .WhereIF(historyData.ReceiptAmount > 0, (r, d, c) => 
                    Math.Abs(c.ArrivalAmount.Value - historyData.ReceiptAmount) < 0.01m)
                .WhereIF(!string.IsNullOrEmpty(historyData.PaymentCompany), (r, d, c) => 
                    c.PaymentCompanyName.Contains(historyData.PaymentCompany) || 
                    c.PaymentCompany.Contains(historyData.PaymentCompany))
                .WhereIF(!string.IsNullOrEmpty(historyData.ReceivingCompany), (r, d, c) => 
                    c.CollectingCompanyName.Contains(historyData.ReceivingCompany) || 
                    c.CollectingCompany.Contains(historyData.ReceivingCompany))
                .WhereIF(historyData.ReceiptDate.HasValue, (r, d, c) => 
                    c.ArrivalDate.Value.Year == historyData.ReceiptDate.Value.Year &&
                    c.ArrivalDate.Value.Month == historyData.ReceiptDate.Value.Month &&
                    c.ArrivalDate.Value.Day == historyData.ReceiptDate.Value.Day)
                .Select((r, d, c) => r)
                .Distinct()
                .ToList();
                
            // 如果找到多条记录，优先取金额完全匹配的
            if (matchingReceipts.Count > 1)
            {
                var exactMatch = matchingReceipts.FirstOrDefault(r =>
                {
                    // 获取收款信息
                    var collectionInfos = DbOpe_crm_collectioninfo.Instance.GetDataList(
                        c => DbOpe_crm_contract_receipt_details.Instance.GetDataList(
                            d => d.ContractReceiptRegisterId == r.Id && d.CollectionInfoId == c.Id && d.Deleted != true
                        ).Any()
                    ).ToList();
                    
                    var collectionInfo = collectionInfos.FirstOrDefault();
                    return collectionInfo != null && Math.Abs(collectionInfo.ArrivalAmount.GetValueOrDefault() - historyData.ReceiptAmount) < 0.01m;
                });
                
                if (exactMatch != null)
                {
                    return exactMatch;
                }
            }
            
            // 返回第一条匹配记录，如果没有则返回null
            return matchingReceipts.FirstOrDefault();
        }

        /// <summary>
        /// 根据历史数据查找系统中的发票记录
        /// </summary>
        private Db_crm_invoice FindInvoiceByHistory(HistoryMatchingModel historyData, string contractId)
        {
            if (string.IsNullOrEmpty(contractId))
            {
                return null;
            }
            
            // 查询匹配的发票记录
            var matchingInvoices = DbOpe_crm_invoice.Instance.GetDataList(i => 
                i.ContractId == contractId && 
                i.RefundStatus == (int)EnumInvoiceRefundStatus.Normal && 
                i.Deleted != true)
                .WhereIF(historyData.InvoiceAmount.HasValue, i => 
                    Math.Abs(i.InvoicedAmount - historyData.InvoiceAmount.Value) < 0.01m)
                .WhereIF(historyData.InvoiceDate.HasValue, i => 
                    i.InvoicingDate.Value.Year == historyData.InvoiceDate.Value.Year &&
                    i.InvoicingDate.Value.Month == historyData.InvoiceDate.Value.Month &&
                    i.InvoicingDate.Value.Day == historyData.InvoiceDate.Value.Day)
                .WhereIF(!string.IsNullOrEmpty(historyData.ReceivingCompany), i => 
                    i.BillingCompany.Contains(historyData.ReceivingCompany))
                .ToList();
                
            // 如果找到多条记录，优先取金额完全匹配的
            if (matchingInvoices.Count > 1 && historyData.InvoiceAmount.HasValue)
            {
                var exactMatch = matchingInvoices.FirstOrDefault(i => 
                    Math.Abs(i.InvoicedAmount - historyData.InvoiceAmount.Value) < 0.01m);
                    
                if (exactMatch != null)
                {
                    return exactMatch;
                }
            }
            
            // 返回第一条匹配记录，如果没有则返回null
            return matchingInvoices.FirstOrDefault();
        }

        /// <summary>
        /// 创建历史匹配记录
        /// </summary>
        private void CreateHistoryMatchingRecord(Db_crm_invoice invoice, Db_crm_contract_receiptregister receipt, string userId)
        {
            // 检查是否已经存在匹配记录
            var existingMatching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => 
                m.InvoiceId == invoice.Id && 
                m.ReceiptId == receipt.Id && 
                m.Deleted != true);
                
            if (existingMatching != null)
            {
                // 已存在匹配记录，无需重复创建
                return;
            }
            
            // 创建新的匹配记录
            string matchingId = Guid.NewGuid().ToString();
            var matching = new Db_crm_invoice_receipt_matching
            {
                Id = matchingId,
                InvoiceId = invoice.Id,
                ReceiptId = receipt.Id,
                InvoiceApplicationId = invoice.InvoiceApplicationId,
                MatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccess, // 直接设置为匹配成功
                MatchingTime = DateTime.Now,
                MatchingUser = userId,
                Remark = "历史数据导入自动匹配",
                CreateUser = userId,
                CreateDate = DateTime.Now,
                UpdateUser = userId,
                UpdateDate = DateTime.Now,
                Deleted = false
            };
            
            // 插入匹配记录
            DbOpe_crm_invoice_receipt_matching.Instance.Insert(matching);
            
            // 确保发票的匹配状态是"匹配成功"
            DbOpe_crm_invoice.Instance.UpdateData(i => new Db_crm_invoice
            {
                MatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccess,
                UpdateUser = userId,
                UpdateDate = DateTime.Now
            }, invoice.Id);
            
            // 记录日志
            LogUtil.AddErrorLog($"历史数据导入: 创建了发票[{invoice.InvoiceNumber}]与到账记录[{receipt.Id}]的匹配关系");
        }

        /// <summary>
        /// 更新未匹配发票的状态
        /// </summary>
        private void UpdateUnmatchedInvoiceStatus(string firstPartyId, string userId)
        {
            // 获取甲方的所有合同
            var contracts = DbOpe_crm_contract.Instance.GetDataList(c => 
                c.FirstParty == firstPartyId && 
                c.Deleted != true).ToList();
                
            if (contracts == null || contracts.Count == 0)
            {
                return;
            }
            
            // 获取合同ID列表
            var contractIds = contracts.Select(c => c.Id).ToList();
            
            // 查询所有标记为"匹配成功"但在matching表中没有记录的发票
            var unmatchedInvoices = DbContext.Crm2Db.Queryable<Db_crm_invoice>()
                .LeftJoin<Db_crm_invoice_receipt_matching>((i, m) => i.Id == m.InvoiceId && m.Deleted != true)
                .Where((i, m) => 
                    contractIds.Contains(i.ContractId) && 
                    i.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess && 
                    i.RefundStatus == (int)EnumInvoiceRefundStatus.Normal && 
                    i.Deleted != true &&
                    m.Id == null) // 没有匹配记录
                .Select((i, m) => i)
                .ToList();
                
            foreach (var invoice in unmatchedInvoices)
            {
                // 判断合同中是否还有未匹配的到账记录
                bool hasUnmatchedReceipts = DbContext.Crm2Db.Queryable<Db_crm_contract_receiptregister>()
                    .LeftJoin<Db_crm_invoice_receipt_matching>((r, m) => r.Id == m.ReceiptId && m.Deleted != true)
                    .Any((r, m) => 
                        r.ContractId == invoice.ContractId && 
                        r.AchievementState == (int)EnumAchievementState.Confirmed && 
                        r.Deleted != true && 
                        m.Id == null); // 没有匹配记录
                
                // 确定发票的新状态
                int newInvoiceStatus = hasUnmatchedReceipts ? 
                    (int)EnumInvoiceMatchingStatus.WaitingConfirm : // 匹配待确认
                    (int)EnumInvoiceMatchingStatus.NotReceived;    // 未到账
                
                // 更新发票的匹配状态
                DbOpe_crm_invoice.Instance.UpdateData(i => new Db_crm_invoice
                {
                    MatchingStatus = newInvoiceStatus,
                    UpdateUser = userId,
                    UpdateDate = DateTime.Now
                }, invoice.Id);
                
                // 记录日志
                string statusName = newInvoiceStatus == (int)EnumInvoiceMatchingStatus.WaitingConfirm ? 
                    "匹配待确认" : "未到账";
                    
                LogUtil.AddErrorLog($"历史数据导入: 发票[{invoice.InvoiceNumber}]状态从'匹配成功'更新为'{statusName}'");
            }
        }

        /// <summary>
        /// 获取单元格字符串值
        /// </summary>
        private string GetCellStringValue(ICell cell)
        {
            if (cell == null) return string.Empty;
            
            switch (cell.CellType)
            {
                case CellType.String:
                    return cell.StringCellValue?.Trim() ?? string.Empty;
                case CellType.Numeric:
                    return cell.NumericCellValue.ToString();
                case CellType.Boolean:
                    return cell.BooleanCellValue.ToString();
                case CellType.Formula:
                    try
                    {
                        return cell.StringCellValue?.Trim() ?? string.Empty;
                    }
                    catch
                    {
                        try
                        {
                            return cell.NumericCellValue.ToString();
                        }
                        catch
                        {
                            return string.Empty;
                        }
                    }
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// 获取单元格decimal值
        /// </summary>
        private decimal GetCellDecimalValue(ICell cell)
        {
            if (cell == null) return 0;
            
            switch (cell.CellType)
            {
                case CellType.Numeric:
                    return (decimal)cell.NumericCellValue;
                case CellType.String:
                    if (decimal.TryParse(cell.StringCellValue, out decimal result))
                    {
                        return result;
                    }
                    return 0;
                case CellType.Formula:
                    try
                    {
                        return (decimal)cell.NumericCellValue;
                    }
                    catch
                    {
                        return 0;
                    }
                default:
                    return 0;
            }
        }

        /// <summary>
        /// 获取单元格日期值
        /// </summary>
        private DateTime? GetCellDateValue(ICell cell)
        {
            if (cell == null) return null;
            
            try
            {
                switch (cell.CellType)
                {
                    case CellType.Numeric:
                        if (DateUtil.IsCellDateFormatted(cell))
                        {
                            return cell.DateCellValue;
                        }
                        return null;
                    case CellType.String:
                        if (DateTime.TryParse(cell.StringCellValue, out DateTime result))
                        {
                            return result;
                        }
                        return null;
                    default:
                        return null;
                }
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取单元格布尔值
        /// </summary>
        private bool GetCellBoolValue(ICell cell)
        {
            if (cell == null) return false;
            
            switch (cell.CellType)
            {
                case CellType.Boolean:
                    return cell.BooleanCellValue;
                case CellType.Numeric:
                    return cell.NumericCellValue > 0;
                case CellType.String:
                    string value = cell.StringCellValue?.ToLower().Trim() ?? string.Empty;
                    return value == "true" || value == "1" || value == "yes" || value == "是" || value == "y";
                default:
                    return false;
            }
        }

        /// <summary>
        /// 生成历史发票到账匹配的Excel模板
        /// </summary>
        /// <returns>Excel文件流</returns>
        public MemoryStream GenerateHistoryMatchingTemplate()
        {
            try
            {
                // 创建工作簿和工作表
                IWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("历史发票到账匹配");

                // 创建标题样式
                ICellStyle headerStyle = workbook.CreateCellStyle();
                IFont headerFont = workbook.CreateFont();
                headerFont.IsBold = true;
                headerStyle.SetFont(headerFont);
                headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightBlue.Index;
                headerStyle.FillPattern = FillPattern.SolidForeground;

                // 创建标题行
                IRow headerRow = sheet.CreateRow(0);
                string[] headers = new string[] 
                { 
                    "客户编码", "付款公司", "收款公司", "到账金额", "支付方式", 
                    "到账日期", "是否开票", "开票金额", "开票日期", "备注" 
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    ICell cell = headerRow.CreateCell(i);
                    cell.SetCellValue(headers[i]);
                    cell.CellStyle = headerStyle;
                    // 设置列宽
                    sheet.SetColumnWidth(i, 20 * 256);
                }

                // 添加示例数据
                IRow sampleRow = sheet.CreateRow(1);
                sampleRow.CreateCell(0).SetCellValue("CUST001"); // 客户编码
                sampleRow.CreateCell(1).SetCellValue("示例付款公司"); // 付款公司
                sampleRow.CreateCell(2).SetCellValue("示例收款公司"); // 收款公司
                sampleRow.CreateCell(3).SetCellValue(10000.00); // 到账金额
                sampleRow.CreateCell(4).SetCellValue("电汇"); // 支付方式
                
                // 设置日期单元格格式
                ICellStyle dateStyle = workbook.CreateCellStyle();
                IDataFormat dataFormat = workbook.CreateDataFormat();
                dateStyle.DataFormat = dataFormat.GetFormat("yyyy-MM-dd");
                
                // 设置到账日期
                ICell receiptDateCell = sampleRow.CreateCell(5);
                receiptDateCell.SetCellValue(DateTime.Now);
                receiptDateCell.CellStyle = dateStyle;
                
                sampleRow.CreateCell(6).SetCellValue("是"); // 是否开票
                sampleRow.CreateCell(7).SetCellValue(10000.00); // 开票金额
                
                // 设置开票日期
                ICell invoiceDateCell = sampleRow.CreateCell(8);
                invoiceDateCell.SetCellValue(DateTime.Now);
                invoiceDateCell.CellStyle = dateStyle;
                
                sampleRow.CreateCell(9).SetCellValue("示例数据"); // 备注

                // 冻结首行
                sheet.CreateFreezePane(0, 1, 0, 1);

                // 保存到内存流
                MemoryStream ms = new MemoryStream();
                workbook.Write(ms, true);
                ms.Seek(0, SeekOrigin.Begin);
                
                return ms;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"生成历史发票到账匹配Excel模板失败: {ex.Message}");
                throw new ApiException("生成Excel模板失败");
            }
        }

        /// <summary>
        /// 查找并自动匹配系统中已开票但未正确匹配到账的发票
        /// </summary>
        /// <param name="executeUpdate">是否执行实际的添加/更新操作，false则只统计数量不执行操作</param>
        /// <returns>匹配结果统计</returns>
        public Dictionary<string, int> FindAndAutoMatchInvoices(bool executeUpdate = true)
        {
            // 统计结果
            var result = new Dictionary<string, int>
            {
                { "总数", 0 },
                { "成功", 0 },
                { "失败", 0 }
            };

            try
            {
                // 获取当前用户ID
                string userId = TokenModel.Instance.id;
                if (string.IsNullOrEmpty(userId))
                {
                    throw new ApiException("无法获取当前用户信息");
                }

                // 查找所有未正确匹配的发票（已开票，匹配状态为匹配成功，但匹配表中没有记录）
                var unmatchedInvoices = DbContext.Crm2Db.Queryable<Db_crm_invoice>()
                    .LeftJoin<Db_crm_invoice_receipt_matching>((i, m) => i.Id == m.InvoiceId && m.Deleted != true)
                    .Where((i, m) => 
                        i.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess &&  // 发票状态为匹配成功
                        i.RefundStatus == (int)EnumInvoiceRefundStatus.Normal &&            // 发票正常，未退票
                        i.Deleted != true && 
                        m.Id == null)                                                       // 匹配表中没有记录
                    .Select((i, m) => new { 
                        InvoiceId = i.Id,
                        ContractId = i.ContractId,
                        InvoiceNumber = i.InvoiceNumber,
                        InvoicedAmount = i.InvoicedAmount,
                        BillingCompany = i.BillingCompany,
                        BillingHeader = i.BillingHeader
                    })
                    .ToList();

                if (unmatchedInvoices.Count == 0)
                {
                    LogUtil.AddErrorLog("没有找到未匹配的发票记录");
                    return result;
                }

                result["总数"] = unmatchedInvoices.Count;
                
                // 按合同ID分组
                var invoicesByContract = unmatchedInvoices
                    .Where(i => !string.IsNullOrEmpty(i.ContractId))
                    .GroupBy(i => i.ContractId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 先一次性获取所有需要查询的合同ID
                var contractIds = invoicesByContract.Keys.ToList();
                
                // 一次性查询所有这些合同下未匹配的到账记录
                var allUnmatchedReceipts = DbContext.Crm2Db.Queryable<Db_crm_contract_receiptregister>()
                    .LeftJoin<Db_crm_invoice_receipt_matching>((r, m) => r.Id == m.ReceiptId && m.Deleted != true)
                    .LeftJoin<Db_crm_contract_receipt_details>((r, m, d) => r.Id == d.ContractReceiptRegisterId && d.Deleted != true)
                    .LeftJoin<Db_crm_collectioninfo>((r, m, d, c) => d.CollectionInfoId == c.Id && c.Deleted != true)
                    .Where((r, m, d, c) => 
                        contractIds.Contains(r.ContractId) && 
                        r.AchievementState == (int)EnumAchievementState.Confirmed && 
                        r.Deleted != true && 
                        (m.Id == null || string.IsNullOrEmpty(m.InvoiceApplicationId))) // 匹配表中没有记录或者是手动添加的记录(InvoiceApplicationId为空)
                    .Select((r, m, d, c) => new {
                        ReceiptId = r.Id,
                        ContractId = r.ContractId,
                        CollectingCompany = c.CollectingCompany,
                        CollectingCompanyName = c.CollectingCompanyName,
                        PaymentCompany = c.PaymentCompany,
                        PaymentCompanyName = c.PaymentCompanyName,
                        ArrivalAmount = c.ArrivalAmount
                    })
                    .ToList();
                
                // 按合同ID分组到账记录，以便快速查找每个合同的到账记录
                var receiptsByContract = allUnmatchedReceipts
                    .GroupBy(r => r.ContractId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // 遍历每个合同的发票
                foreach (var contractInvoices in invoicesByContract)
                {
                    string contractId = contractInvoices.Key;
                    if (string.IsNullOrEmpty(contractId)) continue;

                    // 获取合同下所有已确认但未匹配的到账记录
                    if (!receiptsByContract.TryGetValue(contractId, out var unmatchedReceipts) || unmatchedReceipts.Count == 0)
                    {
                        continue; // 没有找到对应的到账记录，跳过此合同
                    }

                    // 尝试为每个发票找到匹配的到账记录
                    foreach (var invoice in contractInvoices.Value)
                    {
                        // 找到完全匹配的到账记录（金额、公司、抬头都匹配）
                        var exactMatches = unmatchedReceipts.Where(r =>
                            // 金额匹配（保留两位小数）
                            Math.Round(invoice.InvoicedAmount, 2) == Math.Round(r.ArrivalAmount.GetValueOrDefault(), 2) &&
                            // 开票公司对应收款公司
                            invoice.BillingCompany == r.CollectingCompany &&
                            // 收票抬头对应付款公司名称
                            invoice.BillingHeader == r.PaymentCompanyName
                        ).ToList();

                        if (exactMatches.Count == 1)
                        {
                            // 唯一完全匹配，创建匹配记录
                            try
                            {
                                // 获取完整的发票和到账对象 - 这两个查询无法避免，因为需要完整对象来创建匹配记录
                                var fullInvoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == invoice.InvoiceId);
                                var fullReceipt = DbOpe_crm_contract_receiptregister.Instance.GetData(r => r.Id == exactMatches[0].ReceiptId);

                                if (fullInvoice != null && fullReceipt != null)
                                {
                                    // 先检查是否存在手动添加的匹配记录(InvoiceApplicationId为空但ReceiptId不为空)
                                    var existingManualMatching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m => 
                                        m.ReceiptId == exactMatches[0].ReceiptId && 
                                        string.IsNullOrEmpty(m.InvoiceApplicationId) && 
                                        m.Deleted != true);
                                    
                                    if (existingManualMatching != null)
                                    {
                                        // 如果存在手动添加的匹配记录，则更新该记录
                                        if (executeUpdate)
                                        {
                                            DbOpe_crm_invoice_receipt_matching.Instance.UpdateData(m => new Db_crm_invoice_receipt_matching
                                            {
                                                InvoiceId = fullInvoice.Id,
                                                InvoiceApplicationId = fullInvoice.InvoiceApplicationId,
                                                UpdateUser = userId,
                                                UpdateDate = DateTime.Now,
                                                Remark = "自动修复匹配：更新手动添加的匹配记录"
                                            }, existingManualMatching.Id);
                                            
                                            LogUtil.AddErrorLog($"自动修复匹配：更新发票[{invoice.InvoiceNumber}]与到账记录[{exactMatches[0].ReceiptId}]的手动匹配记录");
                                        }
                                        
                                        result["成功"]++;
                                    }
                                    else
                                    {
                                        // 创建匹配记录
                                        if (executeUpdate)
                                        {
                                            CreateMatchingRecord(fullInvoice, fullReceipt, userId, true);
                                            LogUtil.AddErrorLog($"自动修复匹配：发票[{invoice.InvoiceNumber}]成功匹配到账记录[{exactMatches[0].ReceiptId}]");
                                        }
                                        
                                        result["成功"]++;
                                    }
                                }
                                else
                                {
                                    result["失败"]++;
                                }
                            }
                            catch (Exception ex)
                            {
                                if (executeUpdate)
                                {
                                    LogUtil.AddErrorLog($"自动修复匹配失败: {ex.Message}");
                                }
                                
                                result["失败"]++;
                            }
                        }
                        else
                        {
                            // 没有找到唯一匹配，标记为失败
                            result["失败"]++;
                            
                            if (executeUpdate)
                            {
                                LogUtil.AddErrorLog($"发票[{invoice.InvoiceNumber}]无法找到唯一匹配的到账记录（找到{exactMatches.Count}个可能匹配）");
                            }
                        }
                    }
                }

                // 记录统计结果到日志
                LogUtil.AddLog($"发票自动匹配执行完成 - 总数: {result["总数"]}, 成功: {result["成功"]}, 失败: {result["失败"]}, 执行更新: {executeUpdate}");

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"查找和自动匹配发票异常: {ex.Message}");
                throw new ApiException("查找和自动匹配发票失败: " + ex.Message);
            }
        }
    }
} 