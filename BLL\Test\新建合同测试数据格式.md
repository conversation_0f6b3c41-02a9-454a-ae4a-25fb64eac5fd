AddContract



CustomerId: 8024a34f-d5c2-4b95-b616-d9ed4d4963a3
ContactInformation: 2
ContractMethod: 2
ContractPaperEntityId: 
FirstParty: 90eeb30a-25cb-4fc9-9389-7fcb30715198
ContractName: 自动生成测试客户1资讯合同
Contacts: 张三
Job: 经理
ContactWay: 18902021010
Email: <EMAIL>
Telephone: 27814785
Fax: 27814785
Country: 337
Province: 2
City: 2
Address: 天津金融中心1011
PostalCode: 300000
IsOverseasCustomer: true
ContractType: 1
SigningDate: 2025-04-15
SecondParty: f27399d1-ceac-11ed-bc7b-30d042e24322
RenewalContractNum: 
SalesCountry: 255
IsMerged: false
SigningAgentName: 王冬
SigningAgentPhone: 18902021032
SigningAgentEmail: <EMAIL>
SigningAgentFax: 010-84802310
CompanyAddressId: 
PaymentInfo.IsReceipt: 2
PaymentInfo.PaymentType: 3
PaymentInfo.PlannedArrivalDate: 
PaymentInfo.PlannedArrivalAmount: 
PaymentInfo.PaymentCompany: 90eeb30a-25cb-4fc9-9389-7fcb30715198
PaymentInfo.IsBehalfPayment: false
PaymentInfo.CollectingCompany: f27399d1-ceac-11ed-bc7b-30d042e24322
PaymentInfo.Currency: 1
PaymentInfo.PaymentMethod: 1
PaymentInfo.CashSourceRemarks: 
PaymentInfo.IsStage: 0
PaymentInfo.PaymentContacts: 
PaymentInfo.PaymentJob: 
PaymentInfo.PaymentContactWay: 
PaymentInfo.PaymentEmail: 
PaymentInfo.PaymentTelephone: 
PaymentInfo.PaymentFax: 
PaymentInfo.PaymentCountry: 
PaymentInfo.PaymentProvince: 
PaymentInfo.PaymentCity: 
PaymentInfo.PaymentAddress: 
PaymentInfo.PaymentPostalCode: 
PaymentInfo.ArrivalDate: 2025-04-15
PaymentInfo.ArrivalAmount: 10000
PaymentInfo.BankPaymentAmount: 
PaymentInfo.CashPaymentAmount: 
IsSpecial: 0
SpecialDescription: 
RelationshipDescription: 
Remark: 
ProductInfo[0]: {"ContractProductinfoPrice":48800,"ParentProductId":"********-0000-0000-0000-************","ProductName":"GTIS高级版","ProductId":"4a0ffc9a-6a44-4362-a76a-07492d0a81fa","ProductType":1,"SubAccountsNum":1,"PrimaryAccountsNum":1,"OpeningYears":1,"OpeningMonths":12,"FirstOpeningMonths":12,"SubAccountsProductPrice":0,"ContractProductinfoPriceTotal":0}
ProductInfo[1]: {"ContractProductinfoPrice":0,"ParentProductId":"********-0000-0000-0000-************","ProductName":"环球搜","ProductId":"a2f16437-e95f-4c3f-8e97-a22a1bbf0ab8","ProductType":5,"SubAccountsNum":1,"OpeningMonths":12,"OpeningYears":1,"FirstOpeningMonths":12,"PrimaryAccountsNum":1,"SubAccountsProductPrice":0,"ContractProductinfoPriceTotal":0}
Currency: 1
ContractAmount: 48800
FCContractAmount: 
IsApplySuperSubAccount: false
ProductInfoDownloadStyle[0]: {"ProductNum":"00006","ProductName":"GTIS高级版","ProductType":1,"ProductTypeName":"GTIS 5.0","ServiceCycle":1,"ServiceCycleName":"1年","ServiceCycleStart":12,"ServiceCycleEnd":14,"ProductCombination":[],"IsRecommend":0,"SubCount":1,"IsRecommendName":"否","StandardPriceCNY":48800,"StandardPriceUSD":6000,"StandardPriceEUR":null,"ReMark":null,"ProductId":"4a0ffc9a-6a44-4362-a76a-07492d0a81fa","disabled":true,"ContractProductinfoPrice":48800,"SubAccountsProductPrice":null,"isChecked":true}
ProductInfoDownloadStyle[1]: {"ProductNum":"00026","ProductName":"环球搜","ProductType":5,"ProductTypeName":"环球搜","ServiceCycle":1,"ServiceCycleName":"1年","ServiceCycleStart":12,"ServiceCycleEnd":14,"ProductCombination":[],"IsRecommend":0,"SubCount":1,"IsRecommendName":"否","StandardPriceCNY":6800,"StandardPriceUSD":1000,"StandardPriceEUR":null,"ReMark":null,"ProductId":"a2f16437-e95f-4c3f-8e97-a22a1bbf0ab8","disabled":true,"isChecked":true,"ContractProductinfoPrice":0,"SubAccountsProductPrice":null}
ModifyContractTemplateAddRow[0]: {"TemplateType":1,"Item":0,"ProductTable":0}
ModifyContractTemplateAddRow[1]: {"TemplateType":2,"Item":0,"ProductTable":0}
ContractStatus: 1
IsCustomContractDescription: 2