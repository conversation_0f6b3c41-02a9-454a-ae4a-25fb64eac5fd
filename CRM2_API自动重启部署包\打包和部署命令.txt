# 快速打包和部署命令参考

## 在Windows本地打包（使用PowerShell）

```powershell
# 进入部署包目录
cd .\CRM2_API自动重启部署包\

# 使用PowerShell压缩文件
Compress-Archive -Path * -DestinationPath crm-api-restart-deploy.zip

# 或者右键点击所有文件，选择"发送到 -> 压缩(zipped)文件夹"
```

## 在Linux服务器上部署

```bash
# 假设已经将文件上传到了/tmp目录

# 创建并进入部署目录
mkdir -p /tmp/crm-deploy
cd /tmp/crm-deploy

# 解压文件（如果是zip文件）
unzip /tmp/crm-api-restart-deploy.zip

# 或者解压tar.gz文件
# tar -xzvf /tmp/crm-api-restart-deploy.tar.gz

# 设置脚本执行权限
chmod +x install-crm-service.sh
chmod +x monitor-crm-service.sh

# 部署主服务
sudo ./install-crm-service.sh

# 部署监控服务
sudo cp monitor-crm-service.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/monitor-crm-service.sh
sudo cp crm-api-monitor.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable crm-api-monitor.service
sudo systemctl start crm-api-monitor.service

# 检查服务状态
sudo systemctl status crm-api.service
sudo systemctl status crm-api-monitor.service
```

## 常用管理命令

```bash
# 重启主服务
sudo systemctl restart crm-api.service

# 查看主服务日志
sudo journalctl -u crm-api.service -f

# 查看监控服务日志
cat /hqhs_data/00.API/01.CRM/log/monitor.log

# 停止服务
sudo systemctl stop crm-api.service
sudo systemctl stop crm-api-monitor.service
``` 