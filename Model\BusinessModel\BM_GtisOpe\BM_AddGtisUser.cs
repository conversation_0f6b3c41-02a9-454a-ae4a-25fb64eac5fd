using System.ComponentModel.DataAnnotations;

namespace CRM2_API.Model.BusinessModel.BM_GtisOpe
{
    /// <summary>
    /// 添加g5用户
    /// </summary>
    public class BM_AddGtisUser
    {
        /// <summary>
        /// 客户类型 1:零售  2:专业版  4:高级版  8:至尊版 9:新套餐1 10:新套餐2 11:新套餐3
        /// </summary>
        [Required(ErrorMessage = "必须填写客户类型")]
        public int CustomerType { get; set; }

        /// <summary>
        /// 客户编码
        /// </summary>
        [Required(ErrorMessage = "必须填写客户编码")]
        public string SvCode { get; set; }

        /// <summary>
        /// 姓名，公司名
        /// </summary>
        [Required(ErrorMessage = "必须填写公司名称")]
        public string Company { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [Required(ErrorMessage = "必须填写公司地址")]
        public string Addr { get; set; }

        /// <summary>
        /// 环球搜码
        /// </summary>
        public string HqsCode { get; set; }

        /// <summary>
        /// 主账号管理员姓名
        /// </summary>
        [Required(ErrorMessage = "必须填写主账号使用者姓名")]
        public string UserName { get; set; }

        /// <summary>
        /// 其他信息
        /// </summary>
        public string OtherInfo { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 最大子账号数量
        /// </summary>
        public int MaxChildrens { get; set; }

        /// <summary>
        /// 服务开始时间
        /// </summary>
        [Required(ErrorMessage = "必须填写服务开始日期")]
        public DateTime StartServerDate { get; set; }

        /// <summary>
        /// 服务结束时间
        /// </summary>
        [Required(ErrorMessage = "必须填写服务结束日期")]
        public DateTime EndServerDate { get; set; }

        /// <summary>
        /// 子账号授权国家次数
        /// </summary>
        public int MaxCountry { get; set; }

        /// <summary>
        /// 合同金额
        /// </summary>
        public string ContractNum { get; set; }

        /// <summary>
        /// 付款年月
        /// </summary>
        public string PayDate { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        [Required(ErrorMessage = "手机号不能为空")]
        public string Tel { get; set; }

        /// <summary>
        /// 是否历史用户  1:是历史客户    0：新客户
        /// </summary>
        public int IsHistory { get; set; }

        /// <summary>
        /// 贸易搜索，禁止导出(默认false) true:禁止导出  false:允许导出
        /// </summary>
        public bool ForbidSearchExport { get; set; }

        /// <summary>
        /// 定制报告，当前公司每年总次数
        /// </summary>
        public int WordRptMaxTimes { get; set; }

        /// <summary>
        /// 是否拥有定制报告权限 
        /// </summary>
        public bool WordRptPermissions { get; set; }

        /// <summary>
        /// 共享账号的总数量
        /// </summary>
        [Required(ErrorMessage = "必须填写共享使用总数量")]
        [Range(1, 2000, ErrorMessage = "共享使用总数量最多填写2000")]
        public int SharingTimes { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        [Required(ErrorMessage = "必须填写国家")]
        public string crm_country { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        public string crm_city { get; set; }

        /// <summary>
        /// 专业版客户类型
        /// 0：进口数据
        /// 1：出口数据
        /// </summary>
        public string CustomerType2 { get; set; }

        /// <summary>
        /// 所选的国家sid
        /// </summary>
        public int[] Sids { get; set; }

        /// <summary>
        /// 排除的国家sid
        /// </summary>
        public int[] SidsRemove { get; set; }

        /// <summary>
        /// 每个账号拥有共享次数，有几个账号，填写几个，数组第一项必须是主账号
        /// </summary>
        [Required(ErrorMessage = "必须填写每个账号共享次数")]
        [MinLength(1, ErrorMessage = "最少填写一个账号共享次数(就是主账号的共享次数)")]
        public List<Crm2UserAndSharing> AllUserSharingTimes { get; set; }

        /// <summary>
        /// 老合同号，填写，代表本次是续约（弃用）
        /// </summary>
        public string SvCodeOld { get; set; }

        /// <summary>
        /// 是否开通了Crm系统（弃用）
        /// </summary>
        public bool IsOpenCrm { get; set; }
        /// <summary>
        /// 最大Crm账户数量（弃用）
        /// </summary>
        public int MaxCrmAccount { get; set; }

        /// <summary>
        /// crm2系统中的用户id和共享账号数
        /// </summary>
        public class Crm2UserAndSharing
        {
            /// <summary>
            /// crm中的id
            /// </summary>
            [Required(ErrorMessage = "crmID必填")]
            [MinLength(1, ErrorMessage = "crmID必填")]
            public string CrmId { get; set; }

            /// <summary>
            /// 共享账号数
            /// </summary>
            [Required(ErrorMessage = "共享账号数必填")]
            [Range(1, 999, ErrorMessage = "共享账号数，最小为1")]
            public int SharingTimes { get; set; }
            /// <summary>
            /// 应用名称
            /// </summary>
            public string[] Apps { get; set; }
            /// <summary>
            /// 应用选项
            /// </summary>
            public List<AppOptionModel> AppOptions { get; set; }

            /// <summary>
            /// gits用户id
            /// </summary>
            public string SysUserID { get; set; }

            /// <summary>
            /// 账户id
            /// </summary>
            public string Uid { get; set; }

            /// <summary>
            /// 账户密码
            /// </summary>
            public string Pwd { get; set; }

            /// <summary>
            /// 是否是超级子账号，会将所有国家再分配给这个子账号
            /// </summary>
            public bool IsSupperSubUser { get; set; }

            /// <summary>
            /// 环球搜码
            /// </summary>
            public string HqsCode { get; set; }
        }
        /// <summary>
        /// 全部环球搜码
        /// </summary>
        public List<string> HqsCodeList { get; set; }
    }
} 
