using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.Linq;
using SqlSugar;
using CRM2_API.DAL.DbCommon;
using Newtonsoft.Json;
using CRM2_API.Common.Cache;
using static CRM2_API.Common.Cache.RedisCache;
using Aspose.Pdf;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using CRM2_API.Common.AppSetting;
using System.Linq.Expressions;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 查询部分
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        /// <summary>
        /// 查询发票列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>发票列表</returns>
        public ApiTableOut<InvoiceApplicationListItem> QueryInvoiceAndRefundList(QueryInvoiceApplicationListRequest query)
        {
            // 初始化响应对象
            var apiResponse = new ApiTableOut<InvoiceApplicationListItem>();
            
            try
            {
                // 参数验证
                if (query == null)
                {
                    return new ApiTableOut<InvoiceApplicationListItem> { Total = 0, Data = new List<InvoiceApplicationListItem>() };
                }
                
                // 创建查询对象 - 直接使用数据库级别的查询
                var db = DbContext.Crm2Db;
                
                // 1. 先连接主要表（约9-10个表）查询基本字段
                var query1 = db.Queryable<Db_crm_invoice_application,Db_crm_contract,Db_crm_invoice,Db_crm_invoice_review,Db_crm_invoice_refund_application,Db_v_customer_subcompany_private_user,Db_crm_proforma_invoice,Db_crm_invoice_receipt_matching,Db_crm_contract_receiptregister,Db_crm_contract_receipt_details,Db_crm_collectioninfo,Db_crm_invoice>
                    (
                        (invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation,collection,refundInvoice) => 
                        new JoinQueryInfos(
                            JoinType.Left, invoiceAppl.ContractId == contract.Id && contract.Deleted != true,
                            JoinType.Left, invoiceAppl.Id == invoice.InvoiceApplicationId && invoice.Deleted != true,
                            JoinType.Left, invoiceAppl.Id == invoiceAudit.InvoiceApplicationId && invoiceAudit.Deleted != true,
                            JoinType.Left, invoice.Id == invoiceRefundAppl.InvoiceId,
                            JoinType.Left, contract.FirstParty == cspu.Id,
                            JoinType.Left, invoiceAppl.Id == proInvoice.ApplicationId && proInvoice.Deleted != true,
                            JoinType.Left, invoiceAppl.Id == matching.InvoiceApplicationId && matching.Deleted != true,
                            JoinType.Left, matching.ReceiptId == receipt.Id && receipt.Deleted != true,
                            JoinType.Left, receiptRelation.ContractReceiptRegisterId == receipt.Id && receiptRelation.Deleted != true,
                            JoinType.Left, collection.Id == receiptRelation.CollectionInfoId && collection.Deleted != true,
                            JoinType.Left, invoice.Id == refundInvoice.RelatedInvoiceId && refundInvoice.Deleted != true
                        )
                    )
                    .Where(invoiceAppl => invoiceAppl.Deleted != true);

                // 应用数据权限控制 - 根据用户角色确定可查看的数据范围
                // 检查超级管理员权限 - 超级管理员可以查看所有数据
                bool superRole = BLL_Role.Instance.CheckSuperUser();
                // 判断用户是否为后台管理人员
                bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                // 非超级管理员需要应用权限过滤

                //未到账但是超期的，是需要显示出来的，未超期的，要看搜索条件，但是是否超期实在select语句中判断的
                Expression<Func<InvoiceApplicationListItem, bool>> isShowNotReceived = t => 1==1;

                if (!superRole)
                {
                    // 获取当前用户信息，包括角色
                    var currentUser = DbOpe_v_userwithorg.Instance.GetUserByIdFromCache(UserId);
                    
                    // 获取用户表单权限
                    var userFormIds = DbOpe_sys_form.Instance.GetFormIdsByUserId(UserId);
                    
                    // 检查是否有开票权限（经办人权限）
                    bool hasInvoiceOperatorPermission = userFormIds.Any(f => 
                        f.ControllerName == "InvoiceSystem" && 
                        (f.MethodName == "AuditInvoiceApplication" || 
                         f.MethodName == "AutoMatching" ||
                         f.MethodName == "ManualMatching"));
                    
                    // 检查是否有复核权限
                    bool hasInvoiceReviewPermission = userFormIds.Any(f => 
                        f.ControllerName == "InvoiceSystem" && 
                        (f.MethodName == "ReviewInvoiceApplication" || 
                         f.MethodName == "ReviewMatching" ||
                         f.MethodName == "ConfirmInvoiceRefundReview"));
                    //检查是否有调整应付金额权限
                    bool hasAdjustInvoiceAmountPermission = userFormIds.Any(f => 
                        f.ControllerName == "InvoiceSystem" && 
                        f.MethodName == "UpdateBalanceDue");

                    
                    // 判断是否有特定筛选条件
                    bool hasSpecificFilter = 
                                           !string.IsNullOrEmpty(query.ContractName) || 
                                           !string.IsNullOrEmpty(query.FirstPartyName) ||
                                           !string.IsNullOrEmpty(query.CustomerNum) ||
                                           !string.IsNullOrEmpty(query.IsBehalfPaymentCompanyName) ||
                                           !string.IsNullOrEmpty(query.InvoiceNumber) ;

                    // 客户经理只能看自己客户的发票
                    if (!isBackendManager)
                    {
                        query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                              matching, receipt, receiptRelation, collection) => 
                            cspu.CompanyCurrentUser == UserId // 客户当前所属人是自己
                        );
                    }
                    else
                    {
                        if (hasSpecificFilter)
                        {
                            // 有特定筛选条件时，后台人员可以查看所有发票
                        }
                        else
                        {
                            //排除强制忽略的发票
                            query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) =>
                                    invoiceAppl.ForcedProcessed == false
                            );
                            // 如果同时拥有两种权限
                            if (hasInvoiceOperatorPermission && hasInvoiceReviewPermission)
                            {
                                // 合并两种权限的条件
                                query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) =>
                                    // 开票人员能看到的
                                    (
                                        (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceApplied ||
                                        invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceReviewRejected ||
                                        invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.RefundApplied ||
                                        invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.RefundReviewRejected ||
                                        (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted &&
                                        !SqlFunc.IsNullOrEmpty(invoice.MatchingStatus) &&
                                        (invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.NotReceived ||
                                        invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.WaitingConfirm ||
                                        invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.Rejected)) ||
                                        (invoice.Id != null && invoice.IsSupplementInvoice == true &&
                                        invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted)) &&
                                        invoiceAppl.InvoiceType != (int)EnumInvoiceType.ProformaTicket
                                    ) ||
                                    // 复核人员能看到的
                                    (
                                        (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceProcessed ||
                                        invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.RefundProcessed ||
                                        (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted &&
                                        !SqlFunc.IsNullOrEmpty(invoice.MatchingStatus) &&
                                        invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.WaitingAudit)) &&
                                        invoiceAppl.InvoiceType != (int)EnumInvoiceType.ProformaTicket
                                    )
                                );
                                //二次筛选，对于 已开票 的数据，只有搜索条件中匹配状态里指定了未到账,或者超时到账的数据才显示，默认隐藏,其他状态数据不管
                                isShowNotReceived = t =>
                                 // 不是已开票状态，直接通过
                                 t.DisplayStatus != EnumInvoiceDisplayStatus.InvoiceCompleted ||
                                 // 已开票但有匹配状态且不是未到账，直接通过
                                 (!SqlFunc.IsNullOrEmpty(t.MatchingStatus) &&
                                 t.MatchingStatus != EnumInvoiceMatchingStatus.NotReceived) ||
                                 // 已开票且是未到账(空状态或NotReceived)，只有在以下条件下才通过：
                                 // 1. 超期未到账 或 2. 用户主动查询了未到账状态
                                 ((SqlFunc.IsNullOrEmpty(t.MatchingStatus) ||
                                 t.MatchingStatus == EnumInvoiceMatchingStatus.NotReceived) &&
                                 (t.IsReceiptOverdue == true ||
                                 query.ReceiptLinkStatus.Contains(EnumInvoiceMatchingStatus.NotReceived)));
                            }
                            else if (hasInvoiceOperatorPermission)
                            {
                                query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) =>
                                    invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceApplied ||
                                    invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceReviewRejected ||
                                    invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.RefundApplied ||
                                    invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.RefundReviewRejected ||
                                    (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted &&
                                    !SqlFunc.IsNullOrEmpty(invoice.MatchingStatus) &&
                                    invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.NotReceived) ||
                                    (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted &&
                                    !SqlFunc.IsNullOrEmpty(invoice.MatchingStatus) &&
                                    invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.WaitingConfirm) ||
                                    (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted &&
                                    !SqlFunc.IsNullOrEmpty(invoice.MatchingStatus) &&
                                    invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.Rejected)
                                    ||
                                    //补充发票已开票且还未申请退票的（发票状态是已开票）
                                    (invoice.Id != null &&
                                        invoice.IsSupplementInvoice == true &&
                                        invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted)
                                )
                                .Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection)
                                => invoiceAppl.InvoiceType != (int)EnumInvoiceType.ProformaTicket);
                                //二次筛选，对于 已开票 的数据，只有搜索条件中匹配状态里指定了未到账,或者超时到账的数据才显示，默认隐藏,其他状态数据不管
                                isShowNotReceived = t =>
                                    // 不是已开票状态，直接通过
                                    t.DisplayStatus != EnumInvoiceDisplayStatus.InvoiceCompleted ||
                                    // 已开票但有匹配状态且不是未到账，直接通过
                                    (!SqlFunc.IsNullOrEmpty(t.MatchingStatus) &&
                                    t.MatchingStatus != EnumInvoiceMatchingStatus.NotReceived) ||
                                    // 已开票且是未到账(空状态或NotReceived)，只有在以下条件下才通过：
                                    // 1. 超期未到账 或 2. 用户主动查询了未到账状态
                                    ((SqlFunc.IsNullOrEmpty(t.MatchingStatus) ||
                                    t.MatchingStatus == EnumInvoiceMatchingStatus.NotReceived) &&
                                    (t.IsReceiptOverdue == true ||
                                    query.ReceiptLinkStatus.Contains(EnumInvoiceMatchingStatus.NotReceived)));
                            }
                            else if (hasInvoiceReviewPermission)
                            {
                                query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) =>
                                    invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceProcessed ||
                                    invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.RefundProcessed ||
                                    (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted &&
                                    !SqlFunc.IsNullOrEmpty(invoice.MatchingStatus) &&
                                    invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.WaitingAudit)
                                ).Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection)
                                    => invoiceAppl.InvoiceType != (int)EnumInvoiceType.ProformaTicket);

                            }
                            else if (hasAdjustInvoiceAmountPermission && !hasInvoiceOperatorPermission && !hasInvoiceReviewPermission)
                            {
                                //只有调整应付金额权限，但是没有开票权限和复核权限，默认显示所有形式发票
                                query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) =>
                                    invoiceAppl.InvoiceType == (int)EnumInvoiceType.ProformaTicket
                                );
                            }
                            else
                            {
                                //后台人员没有权限，不能显示
                                query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) =>
                                    1 == 2
                                );
                            }
                        }
                       
                    }
                }

                // 应用查询条件
                #region 合同相关参数
                if (!string.IsNullOrEmpty(query.ContractId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.ContractId == query.ContractId);
                }

                if (!string.IsNullOrEmpty(query.ContractName))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        contract.ContractName.Contains(query.ContractName));
                }

                if (!string.IsNullOrEmpty(query.FirstPartyName))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        cspu.CompanyName.Contains(query.FirstPartyName));
                }

                if (query.ContractStatus != null && query.ContractStatus.Count > 0)
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        contract.ContractStatus.HasValue && 
                        query.ContractStatus.Contains(contract.ContractStatus.Value));
                }

                if (query.StampReviewStatus != null && query.StampReviewStatus.Count > 0)
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        contract.StampReviewStatus.HasValue && 
                        query.StampReviewStatus.Contains(contract.StampReviewStatus.Value));
                }

                if (!string.IsNullOrEmpty(query.CustomerNum))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        contract.ContractNum.Contains(query.CustomerNum));
                }

                if (!string.IsNullOrEmpty(query.IsBehalfPaymentCompanyName))
                {
                    var isBehalfContractIds = RedisCache.PayingCompany.SearchCompaniesByNameKeyword(query.IsBehalfPaymentCompanyName).Select(c=>c.ContractId).ToList();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        isBehalfContractIds.Contains(contract.Id));
                }
                #endregion

                #region 发票申请相关参数
                if (query.DisplayStatus != null && query.DisplayStatus.Count > 0)
                {
                    var statusList = query.DisplayStatus.Select(status => (int)status).ToList();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        statusList.Contains(invoiceAppl.DisplayStatus ?? 0));
                }

                if (!string.IsNullOrEmpty(query.ApplicantId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.ApplicantId == query.ApplicantId);
                }

                if (query.ApplicationStartTime.HasValue)
                {
                    var startDate = query.ApplicationStartTime.Value.GetDaysStart();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.ApplyTime >= startDate);
                }

                if (query.ApplicationEndTime.HasValue)
                {
                    // 添加一天减一秒，使查询包含结束当天的所有记录
                    var endTime = query.ApplicationEndTime.Value.AddDays(1).AddSeconds(-1);
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.ApplyTime <= endTime);
                }

                if (!string.IsNullOrEmpty(query.ApplicationInvoiceCompanyId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.BillingCompany == query.ApplicationInvoiceCompanyId);
                }

                if (!string.IsNullOrEmpty(query.ApplicationInvoiceTitle))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.BillingHeader.Contains(query.ApplicationInvoiceTitle));
                }

                if (!string.IsNullOrEmpty(query.ApplicationInvoiceDetails))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.InvoicingDetails.Contains(query.ApplicationInvoiceDetails));
                }
                if(query.ReceiptLinkStatus != null && query.ReceiptLinkStatus.Count > 0)
                {
                    var statusList = query.ReceiptLinkStatus.Select(status => (int)status).ToList();
                    // 开票前以matching的MatchingStatus为准，开票后以invoice的MatchingStatus为准
                    // 另一种情况，开票前matching表和invoice表都没有数据 这种算作未开票
                    // 形式发票只能看matching的状态
                    // 形式发票没有matching的话，算作未到账
                        query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                              matching, receipt, receiptRelation, collection) => 
                            (invoice.Id == null && statusList.Contains((int)matching.MatchingStatus))
                            || (invoice.Id != null && statusList.Contains((int)invoice.MatchingStatus))
                            || (invoice.Id == null && matching.Id == null && statusList.Contains((int)EnumInvoiceMatchingStatus.NotInvoiced))
                            );
                }
                #endregion

                #region 发票开具相关参数
                if (query.BillingType != null && query.BillingType.Count > 0)
                {
                    var typeList = query.BillingType.Select(type => (int)type).ToList();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice.Id == null && proInvoice.Id == null && typeList.Contains(invoiceAppl.BillingType)) ||
                        (invoice.Id != null && typeList.Contains(invoice.BillingType)) ||
                        (proInvoice.Id != null && typeList.Contains(invoiceAppl.BillingType)));
                }

                if (query.InvoiceType != null && query.InvoiceType.Count > 0)
                {
                    var typeList = query.InvoiceType.Select(type => (int)type).ToList();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice.Id == null && proInvoice.Id == null && typeList.Contains(invoiceAppl.InvoiceType)) ||
                        (invoice.Id != null && typeList.Contains(invoice.InvoiceType)) ||
                        (proInvoice.Id != null && typeList.Contains(invoiceAppl.InvoiceType)));
                }

                if (query.RefundStatus != null && query.RefundStatus.Count > 0)
                {
                    var statusList = query.RefundStatus.Select(status => (int)status).ToList();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (!SqlFunc.IsNullOrEmpty(invoice.RefundStatus) && statusList.Contains(invoice.RefundStatus))
                        || (!SqlFunc.IsNullOrEmpty(proInvoice.Id) && statusList.Contains((int)EnumInvoiceRefundStatus.Normal))
                        );
                }

                if (!string.IsNullOrEmpty(query.InvoiceCompanyId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice.Id == null && proInvoice.Id == null && invoiceAppl.BillingCompany == query.InvoiceCompanyId) ||
                        (invoice.Id != null && invoice.BillingCompany == query.InvoiceCompanyId) ||
                        (proInvoice.Id != null && proInvoice.BillingCompany == query.InvoiceCompanyId));
                }

                if (!string.IsNullOrEmpty(query.InvoiceHearder))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice.Id == null && proInvoice.Id == null && invoiceAppl.BillingHeader.Contains(query.InvoiceHearder)) ||
                        (invoice.Id != null && invoice.BillingHeader.Contains(query.InvoiceHearder)) ||
                        (proInvoice.Id != null && proInvoice.BillingHeader.Contains(query.InvoiceHearder)));
                }

                if (!string.IsNullOrEmpty(query.InvoiceDetails))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice.Id == null && proInvoice.Id == null && invoiceAppl.InvoicingDetails.Contains(query.InvoiceDetails)) ||
                        (invoice.Id != null && invoice.InvoicingDetails.Contains(query.InvoiceDetails)) ||
                        (proInvoice.Id != null && proInvoice.InvoiceDetails.Contains(query.InvoiceDetails)));
                }

                if (!string.IsNullOrEmpty(query.InvoiceNumber))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (!SqlFunc.IsNullOrEmpty(invoice.InvoiceNumber) && invoice.InvoiceNumber.Contains(query.InvoiceNumber)) || 
                        (!SqlFunc.IsNullOrEmpty(proInvoice.InvoiceNumber) && proInvoice.InvoiceNumber.Contains(query.InvoiceNumber)));
                }

                if (query.InvoicingDateStart.HasValue)
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice.InvoicingDate != null && invoice.InvoicingDate >= query.InvoicingDateStart.Value) || 
                        (proInvoice.InvoiceDate != null && proInvoice.InvoiceDate >= query.InvoicingDateStart.Value));
                }

                if (query.InvoicingDateEnd.HasValue)
                {
                    DateTime endDate = query.InvoicingDateEnd.Value.AddDays(1).AddSeconds(-1);
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice.InvoicingDate != null && invoice.InvoicingDate <= endDate) || 
                        (proInvoice.InvoiceDate != null && proInvoice.InvoiceDate <= endDate));
                }

                if (query.IsPersonalInvoice != null)
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                       (!SqlFunc.IsNullOrEmpty(invoice.Id) && invoice.IsPersonalInvoice == query.IsPersonalInvoice)
                       || (SqlFunc.IsNullOrEmpty(invoice.Id) && invoiceAudit.IsPersonalInvoice == query.IsPersonalInvoice)
                       || (SqlFunc.IsNullOrEmpty(invoice.Id) && SqlFunc.IsNullOrEmpty(invoiceAudit.Id) && query.IsPersonalInvoice == false)
                       || (!SqlFunc.IsNullOrEmpty(proInvoice.Id) && query.IsPersonalInvoice == false)
                       );
                }
                #endregion

                #region 到账相关参数
                if (!string.IsNullOrEmpty(query.ReceiptCompanyId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                       !SqlFunc.IsNullOrEmpty(collection.CollectingCompany) && collection.CollectingCompany == query.ReceiptCompanyId);
                }

                if (!string.IsNullOrEmpty(query.PaymentCompanyName))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        !SqlFunc.IsNullOrEmpty(collection.PaymentCompanyName) && collection.PaymentCompanyName.Contains(query.PaymentCompanyName));
                }

                if (query.ReceiptDateStart.HasValue)
                {
                    var startDate = query.ReceiptDateStart.Value.GetDaysStart();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        collection.ArrivalDate!=null && collection.ArrivalDate >= startDate);
                }

                if (query.ReceiptDateEnd.HasValue)
                {
                    var endDate = query.ReceiptDateEnd.Value.GetDaysEnd();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        collection.ArrivalDate!=null && collection.ArrivalDate <= endDate);
                }

                #endregion

                #region 经办人相关参数
                if (!string.IsNullOrEmpty(query.OperatorId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) =>    
                        invoiceAudit.ProcessorId == query.OperatorId);
                }

                if (query.OperateDateStart.HasValue)
                {
                    var startDate = query.OperateDateStart.Value.GetDaysStart();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) =>
                        invoiceAudit.ProcessTime != null && invoiceAudit.ProcessTime >= startDate);
                }

                if (query.OperateDateEnd.HasValue)
                {
                    var endDate = query.OperateDateEnd.Value.GetDaysEnd();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) =>
                        invoiceAudit.ProcessTime != null && invoiceAudit.ProcessTime <= endDate);
                }
                #endregion
                #region 复核人相关参数
                if (!string.IsNullOrEmpty(query.VerifierId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) =>
                        invoiceAudit.AuditorId == query.VerifierId);
                }

                if (query.VerifyDateStart.HasValue)
                {
                    var startDate = query.VerifyDateStart.Value.GetDaysStart();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) =>
                        invoiceAudit.AuditTime != null && invoiceAudit.AuditTime >= startDate);
                }

                if (query.VerifyDateEnd.HasValue)
                {
                    var endDate = query.VerifyDateEnd.Value.GetDaysEnd();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) =>
                        invoiceAudit.AuditTime != null && invoiceAudit.AuditTime <= endDate);
                }
                #endregion
                

                // // 预加载所有需要的缓存数据
                // var userCache = new Dictionary<string, RedisCache.UserWithOrg.UserWithOrgSimple>();
                // var companyCache = new Dictionary<string, RedisCache.CollectingCompany.CollectingCompanySimple>();
                int Days = AppSettings.AutoVoidContractDays;
                var dt = DateTime.Now;
                int total = 0;
                // 使用SqlSugar的Select和ToPageList方法简化分页和数据获取
                var result = query1
                .Select(
                    (invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice,
                                          matching, receipt, receiptRelation, collection, refundInvoice) => new InvoiceApplicationListItem()
                                          {
                                              // 基本信息
                                              Id = invoiceAppl.Id,
                                              InvoiceId = SqlFunc.IsNull(invoice.Id, proInvoice.Id),
                                              RefundApplId = SqlFunc.IsNull(invoiceRefundAppl.Id, null),

                                              // 合同相关信息
                                              ContractId = contract.Id,
                                              ContractNum = contract.ContractNum,
                                              ContractNumber = contract.ContractNo,
                                              ContractName = contract.ContractName,
                                              FirstPartyName = cspu.CompanyName,
                                              ContractAmount = contract.ContractAmount,
                                              ContractStatus = contract.ContractStatus ?? -1,
                                              StampReviewStatus = contract.StampReviewStatus ?? -1,

                                              Currency = (EnumCurrency)(contract.Currency ?? 1),
                                              // 发票申请信息
                                              ApplicationStatus = (EnumInvoiceApplicationStatus)invoiceAppl.AuditStatus,
                                              ApplicationTime = invoiceAppl.ApplyTime,
                                              DisplayStatus = (EnumInvoiceDisplayStatus)(invoiceAppl.DisplayStatus ?? 0),
                                              IsReminded = invoiceAppl.IsReminded ?? (int)EnumIsReminder.UnUrgedTicket,
                                              RemindedDate = invoiceAppl.RemindedDate,
                                              ApplicantId = invoiceAppl.ApplicantId,

                                              ApplicationInvoiceHeader = invoiceAppl.BillingHeader,
                                              ApplicationInvoiceDetails = invoiceAppl.InvoicingDetails,
                                              ApplicationInvoiceAmount = invoiceAppl.AppliedAmount,
                                              BillingType = (EnumBillingType)invoiceAppl.BillingType,
                                              InvoiceType = SqlFunc.IsNull((EnumInvoiceType)invoice.InvoiceType, (EnumInvoiceType)invoiceAppl.InvoiceType),
                                              IsProformaInvoice = invoiceAppl.InvoiceType == (int)EnumInvoiceType.ProformaTicket,

                                             ForcedProcessed = invoiceAppl.ForcedProcessed,

                                              // 发票信息
                                              InvoiceAmount = SqlFunc.IsNull(invoice.TotalAmount, proInvoice.Amount),
                                              InvoiceNumber = SqlFunc.IsNull(invoice.InvoiceNumber, proInvoice.InvoiceNumber),
                                              InvoiceDate = SqlFunc.IIF(invoice.InvoicingDate != null, invoice.InvoicingDate.Value.ToString("yyyy-MM-dd"),
                        proInvoice.InvoiceDate != null ? proInvoice.InvoiceDate.Value.ToString("yyyy-MM-dd") : null),
                                              InvoiceCompanyId = SqlFunc.IsNull(invoice.BillingCompany, proInvoice.BillingCompany),
                                              InvoiceHearder = SqlFunc.IsNull(invoice.BillingHeader, proInvoice.BillingHeader),
                                              InvoiceDetails = SqlFunc.IsNull(invoice.InvoicingDetails, proInvoice.InvoiceDetails),
                                              RefundStatus = !SqlFunc.IsNullOrEmpty(invoice.RefundStatus) ? invoice.RefundStatus :
                                       (proInvoice.Id != null ? (int?)EnumInvoiceRefundStatus.Normal : null),
                                              IsPersonalInvoice = SqlFunc.IsNull(invoice.IsPersonalInvoice, invoiceAudit.IsPersonalInvoice),


                                              // 退票信息
                                              RefundAmount = SqlFunc.IsNull(refundInvoice.TotalAmount, invoiceRefundAppl.RefundAmount),
                                              RefundApplicationStatus = (EnumRefundApplicationStatus)invoiceRefundAppl.AuditStatus,

                                              // 到账与匹配信息
                                              MatchingStatus = SqlFunc.IIF(invoiceAppl.InvoiceType == (int)EnumInvoiceType.ProformaTicket && SqlFunc.IsNullOrEmpty(matching.MatchingStatus),
                            (EnumInvoiceMatchingStatus)EnumInvoiceMatchingStatus.NotInvoiced,
                            SqlFunc.IsNull((EnumInvoiceMatchingStatus)invoice.MatchingStatus, (EnumInvoiceMatchingStatus)matching.MatchingStatus)),
                                              ReceiptDate = collection.ArrivalDate,
                                              ReceiptAmount = collection.ArrivalAmount,
                                              ReceiptCompanyName = collection.CollectingCompanyName,
                                              CollectionBank = collection.CollectingBankName,
                                              PaymentMethod = collection.PaymentMethod == null ? null : (EnumPaymentMethod)collection.PaymentMethod,

                                              // 处理人信息
                                              ProcessorId = invoiceAudit.ProcessorId,
                                              ProcessDate = invoiceAudit.ProcessTime,
                                              AuditorId = invoiceAudit.AuditorId,
                                              AuditDate = invoiceAudit.AuditTime,
                                              AuditFeedback = invoiceAudit.AuditFeedback,
                                              ProcessorRemark = invoiceAudit.ProcessorRemark,

                                              //备注信息
                                              InvoiceBackgroundRemark_Appl = invoiceAppl.InvoiceBackgroundRemark,
                                              InvoiceBackgroundRemark_Invoice = invoice.Remark,
                                              InvoiceBackgroundRemark_RefundAppl = invoiceRefundAppl.RefundBackgroundRemark,
                                              InvoiceBackgroundRemark_Refund = refundInvoice.Remark,
                                              //其他

                                              //是否到账已逾期(已开票，正常未退票，先开票后到账，从签约日期算已经超过60天，还未到账)
                                              IsReceiptOverdue = invoice.Id != null &&
                                               invoice.RefundStatus == (int)EnumInvoiceRefundStatus.Normal &&
                                               SqlFunc.DateDiff(DateType.Day, contract.SigningDate.Value, dt) > Days &&
                                               invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.NotReceived &&
                                               invoice.BillingType == (int)EnumBillingType.InvoicingBeforePayment
                                          })
                    .MergeTable()
                    .Where(isShowNotReceived)
                    .OrderBy(t => t.IsReminded)
                    .OrderBy(t => t.IsReceiptOverdue, OrderByType.Desc)
                    .OrderBy(t => t.ApplicationTime, OrderByType.Desc)
                    .OrderBy(t => t.Id, OrderByType.Desc)
                    .Mapper(t => {
                        t.InvoiceCompanyName = t.InvoiceCompanyId != null ? RedisCache.CollectingCompany.GetCollectingCompanyById(t.InvoiceCompanyId)?.CollectingCompanyName ?? "" : "";
                        t.ApplicantName = t.ApplicantId != null ? RedisCache.UserWithOrg.GetUserById(t.ApplicantId)?.UserWithOrgFullName ?? "" : ""; 
                        t.ProcessorName = t.ProcessorId != null ? RedisCache.UserWithOrg.GetUserById(t.ProcessorId)?.Name ?? "" : "";
                        t.AuditorName = t.AuditorId != null ? RedisCache.UserWithOrg.GetUserById(t.AuditorId)?.Name ?? "" : "";
                        t.IsBehalfPaymentCompanyName = RedisCache.PayingCompany.GetPayingCompanyByContractId(t.ContractId)?.PayingCompanyName ?? "";  //后面通过缓存再做处理
                        t.ApplicationInvoiceCompany = t.ApplicationInvoiceCompany != null ? RedisCache.CollectingCompany.GetCollectingCompanyById(t.ApplicationInvoiceCompany)?.CollectingCompanyName ?? "" : "";


                        // 判断权限，如果是后台人员 获取发票后台备注 (还没开票的取申请表的发票备注，已经开票的，如果已经开始退票，取退票备注，否则取发票备注；)
                        if (isBackendManager)
                        {
                            if (string.IsNullOrEmpty(t.InvoiceId))
                            {
                                //如果还没有开票，取申请表的发票备注
                                t.InvoiceBackgroundRemark = t.InvoiceBackgroundRemark_Appl;
                            }
                            else
                            {
                                if (!string.IsNullOrEmpty(t.RefundApplId))
                                {
                                    if (t.RefundStatus != (int)EnumInvoiceRefundStatus.Normal)
                                    {
                                        //如果退票成功，取退票发票备注 （invoice的remark）
                                        t.InvoiceBackgroundRemark = t.InvoiceBackgroundRemark_Refund;
                                    }
                                    else
                                    {
                                        //如果退票未成功，取退票申请表的发票备注 （refund_application的refund_background_remark）
                                        t.InvoiceBackgroundRemark = t.InvoiceBackgroundRemark_RefundAppl;
                                    }
                                }
                                else
                                {
                                    //如果还没有退票，取发票备注 （invoice的remark）
                                    t.InvoiceBackgroundRemark = t.InvoiceBackgroundRemark_Invoice;
                                }
                            }
                        }
                        else
                        {
                            //如果不是后台人员，不显示发票后台备注
                            t.InvoiceBackgroundRemark = "--";
                            t.InvoiceBackgroundRemark_Appl = "--";
                            t.InvoiceBackgroundRemark_Invoice = "--";
                            t.InvoiceBackgroundRemark_RefundAppl = "--";
                            t.InvoiceBackgroundRemark_Refund = "--";
                        }




                    })
                    .ToPageList(query.PageNumber, query.PageSize,ref total);
                
                

                var apiResult = new ApiTableOut<InvoiceApplicationListItem>()
                {
                    Data = result,
                    Total = total
                };
                
                return apiResult;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"查询发票列表时发生错误: {ex.Message}");
                throw new ApiException("查询发票列表失败");
            }
        }

       
    }}