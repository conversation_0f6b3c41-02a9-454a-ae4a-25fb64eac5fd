-- 服务变更原因可变更字段配置表创建和数据迁移脚本
-- 将原有硬编码的字段权限配置迁移到数据库表中
-- 执行日期: 2025-01-29

-- 1. 创建配置表
CREATE TABLE IF NOT EXISTS crm_service_change_reason_field_config (
    Id VARCHAR(36) PRIMARY KEY COMMENT '主键ID',
    ServiceType INT NOT NULL COMMENT '服务类型：1-GTIS，2-邓白氏，3-环球搜，4-慧思学院，5-其他数据',
    ChangeReasonEnum INT NOT NULL COMMENT '变更原因枚举值',
    FieldKey VARCHAR(100) NOT NULL COMMENT '可变更的字段键名',
    FieldName VARCHAR(200) NOT NULL COMMENT '字段显示名称',
    DisplayOrder INT DEFAULT 0 COMMENT '显示顺序',
    IsActive TINYINT DEFAULT 1 COMMENT '是否启用：0-否，1-是',
    Remark VARCHAR(500) NULL COMMENT '备注说明',
    CreateDate DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    CreateUser VARCHAR(50) NULL COMMENT '创建人',
    UpdateDate DATETIME NULL COMMENT '更新时间',
    UpdateUser VARCHAR(50) NULL COMMENT '更新人',
    Deleted TINYINT DEFAULT 0 COMMENT '是否删除：0-否，1-是'
) COMMENT='服务变更原因可变更字段配置表';

-- 2. 创建索引
CREATE INDEX IX_ServiceChangeReasonFieldConfig_ServiceType_ChangeReason 
ON crm_service_change_reason_field_config (ServiceType, ChangeReasonEnum, IsActive, Deleted);

CREATE INDEX  IX_ServiceChangeReasonFieldConfig_FieldKey 
ON crm_service_change_reason_field_config (FieldKey);
