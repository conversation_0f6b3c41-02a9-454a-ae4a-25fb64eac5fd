﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;  // 确保这行存在
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;

using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.Controllers
{
    [Description("Gtis国家信息")]
    public class G4DbNamesController : MyControllerBase
    {
        public G4DbNamesController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取Gtis国家信息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public List<G4DbNames> GetG4DbNames(string productId = "")
        {
            return BLL_G4DbNames.Instance.GetG4DbNames(productId);
        }
        [Htt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
        public void RefreshG4DbNames()
        {
            BLL_G4DbNames.Instance.TryGetGtisG4DbNames();
        }
    }
}
