﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Model.ControllersViewModel.VM_Exchange;

namespace CRM2_API.Controllers
{
    [Description("汇率")]
    public class ExchangeController : MyControllerBase
    {
        public ExchangeController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 添加汇率申请信息
        /// </summary>
        [HttpPost]
        public void AddExchangeAppl(AddExchangeAppl_In addExchangeAppl)
        {
            BLL_Exchange.Instance.AddExchangeAppl(addExchangeAppl);
        }

        /// <summary>
        /// 修改汇率申请信息
        /// </summary>
        [HttpPost]
        public void UpdateExchangeAppl(UpdateExchangeAppl_In updateExchangeAppl)
        {
            BLL_Exchange.Instance.UpdateExchangeAppl(updateExchangeAppl);
        }

        /// <summary>
        /// 审核汇率申请信息
        /// </summary>
        [HttpPost]
        public void AuditExchangeAppl(AuditExchangeAppl_In auditExchangeApplIns)
        {
            BLL_Exchange.Instance.AuditExchangeAppl(auditExchangeApplIns);
        }

        /// <summary>
        /// 删除汇率申请信息
        /// </summary>
        [HttpPost]
        public void DeleteExchangeAppl(List<string> ids)
        {
            BLL_Exchange.Instance.DeleteExchangeAppl(ids);
        }

        /// <summary>
        /// 根据汇率申请信息Id获取汇率申请信息
        /// </summary>
        [HttpPost]
        public ExchangeAppl_Out GetExchangeApplById(string id)
        {
            return BLL_Exchange.Instance.GetExchangeApplById(id);
        }

        /// <summary>
        /// 根据查询条件获取汇率申请信息列表
        /// </summary>
        /// <param name="searchExchangeApplListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchExchangeApplList_Out> SearchExchangeApplList(SearchExchangeApplList_In searchExchangeApplListIn)
        {
            return BLL_Exchange.Instance.SearchExchangeApplList(searchExchangeApplListIn);
        }

        /// <summary>
        /// 根据汇率申请信息汇率日期获取汇率申请信息
        /// </summary>
        [HttpPost]
        public ExchangeAppl_Out GetExchangeApplByExchangeDate(string exchangeDate)
        {
            return BLL_Exchange.Instance.GetExchangeApplByExchangeDate(exchangeDate);
        }

        /// <summary>
        /// 根据汇率申请信息汇率日期获取汇率申请信息列表
        /// </summary>
        [HttpPost]
        public List<ExchangeApplAndDate_Out> GetExchangeApplByDate(string date)
        {
            return BLL_Exchange.Instance.GetExchangeApplByDate(date);
        }

        /// <summary>
        /// 根据汇率日期获取汇率信息
        /// </summary>
        [HttpPost]
        public List<Exchange_Out> GetExchangeByExchangeDate(string exchangeDate)
        {
            return BLL_Exchange.Instance.GetExchangeByExchangeDate(exchangeDate);
        }
    }
}
