﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using static CRM2_API.Model.ControllersViewModel.VM_ContractCreatedTemplate;

namespace CRM2_API.BLL
{
    public class BLL_CreateTemplate : BaseBLL<BLL_CreateTemplate>
    {
        public AddCreaterTemplateResult AddCreateTemplate(AddOrUpdateCreateTemplate addCreateTemplate)
        {
            return DbOpe_crm_contract_createdtemplate.Instance.AddCreateTemplate(addCreateTemplate);
        }

        public AddCreaterTemplateResult UpdateCreateTemplate(AddOrUpdateCreateTemplate updateCreateTemplate)
        {
            return DbOpe_crm_contract_createdtemplate.Instance.UpdateCreateTemplate(updateCreateTemplate);
        }
    }
}
