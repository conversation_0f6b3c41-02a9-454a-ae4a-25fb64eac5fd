using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using CRM2_API.Common.AppSetting;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;
using System.Runtime.Serialization;
using System.Linq;
using CRM2_API.Common.Utils;

namespace CRM2_API.Common.Utils
{
    /// <summary>
    /// UmiOCR服务调用工具类
    /// </summary>
    public class UmiOcrService
    {
        #region 单例实现
        private static UmiOcrService _instance;
        private static readonly object _lock = new object();
        
        /// <summary>
        /// 获取UmiOcr服务的单例实例
        /// </summary>
        public static UmiOcrService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new UmiOcrService();
                        }
                    }
                }
                return _instance;
            }
        }
        
        private UmiOcrService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
            
            // 从AppSettings获取服务地址
            try 
            {
                // 通过AppSettings静态类读取配置
                string configUrl = AppSettings.UmiOcr?.ServiceUrl;
                BaseUrl = string.IsNullOrEmpty(configUrl) ? "http://localhost:8088" : configUrl;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取UmiOcr配置出错: {ex.Message}");
                BaseUrl = "http://localhost:8088"; // 使用默认值
            }
        }
        #endregion
        
        private readonly HttpClient _httpClient;
        
        /// <summary>
        /// OCR服务基础URL
        /// </summary>
        public string BaseUrl { get; set; }
        
        /// <summary>
        /// 获取中文识别的默认选项配置
        /// </summary>
        /// <returns>中文识别的默认选项配置</returns>
        public static Dictionary<string, object> GetChineseOcrOptions()
        {
            return new Dictionary<string, object>
            {
                // 基本OCR配置
                { "ocr.language", "models/config_chinese.txt" }, // 使用中文识别模型
                { "ocr.cls", true },                             // 启用文字方向检测，识别倾斜或倒置的文本
                { "ocr.limit_side_len", 99999 },                 // 图像边长限制，不对图像大小进行压缩
                
                // 文本块解析配置
                { "tbpu.parser", "single_none" },                // 单栏-无换行模式，更适合发票识别
                
                // 忽略区域配置（忽略发票上方的标题区域）
                { "tbpu.ignoreArea", new List<List<int[]>> 
                  { 
                      new List<int[]> 
                      { 
                          new int[] {0, 0}, 
                          new int[] {855, 100} 
                      } 
                  }
                },
                
                // 数据返回格式
                { "data.format", "dict" },                       // 返回字典格式，包含更详细的信息（坐标等）
                
                // OCR识别详细配置
                { "ocr.det", true },                             // 启用文本检测，必须为true
                { "ocr.rec", true },                             // 启用文本识别，必须为true
                { "tbpu.tbpu", true },                           // 启用排版分析
                
                // 高级配置
                { "ocr.use_angle_cls", true },                   // 启用方向分类器
                { "ocr.det_limit_side_len", 960 },               // 检测器限制图像边长
                { "ocr.det_limit_type", "min" },                 // 检测器限制类型，min表示较短边
                { "ocr.det_db_thresh", 0.3 },                    // 文本检测器二值化阈值，降低可检测更模糊的文本
                { "ocr.det_db_box_thresh", 0.5 },                // 文本检测器框阈值
                { "ocr.rec_batch_num", 6 }                       // 识别批次大小，提高多核利用率
            };
        }
        
        
        #region 二维码识别与生成
        
        /// <summary>
        /// 识别Base64编码图片中的二维码
        /// </summary>
        /// <param name="base64Image">Base64编码的图片</param>
        /// <returns>二维码识别结果</returns>
        public async Task<QrCodeResult> RecognizeQrCodeBase64Async(string base64Image)
        {
            try
            {
                var requestData = new Dictionary<string, object>
                {
                    { "base64", base64Image }
                };
                
                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync($"{BaseUrl}/api/qrcode", content);
                var responseString = await response.Content.ReadAsStringAsync();
                
                try
                {
                    // 尝试标准的反序列化
                    return JsonConvert.DeserializeObject<QrCodeResult>(responseString);
                }
                catch (JsonSerializationException jsonEx)
                {
                    // 检查是否是由于data字段是数组而不是对象导致的错误
                    if (jsonEx.Message.Contains("JSON array") && jsonEx.Message.Contains("QrCodeData"))
                    {
                        // 手动解析JSON，处理data字段为数组的情况
                        JObject rootObject = JObject.Parse(responseString);
                        QrCodeResult result = new QrCodeResult
                        {
                            code = rootObject["code"]?.Value<int>() ?? -1,
                            msg = rootObject["msg"]?.Value<string>() ?? "未知错误"
                        };
                        
                        // 检查data字段是否为数组
                        if (rootObject["data"] != null && rootObject["data"].Type == JTokenType.Array)
                        {
                            // 创建一个新的QrCodeData并设置qrcode字段为该数组
                            JArray dataArray = (JArray)rootObject["data"];
                            result.data = new QrCodeData
                            {
                                qrcode = dataArray.ToObject<List<QrCodeInfo>>(),
                                time_cost = 0 // 无法从数组中获取time_cost，设置默认值
                            };
                        }
                        
                        return result;
                    }
                    
                    // 如果是其他错误，重新抛出
                    throw;
                }
            }
            catch (Exception ex)
            {
                return new QrCodeResult
                {
                    code = -1,
                    msg = $"二维码识别过程发生错误: {ex.Message}",
                    data = null
                };
            }
        }



        #endregion

        #region 发票识别

        /// <summary>
        /// 使用Base64编码的图片进行OCR识别，默认使用中文识别配置
        /// </summary>
        /// <param name="base64Image">Base64编码的图片，不包含前缀</param>
        /// <param name="configs">识别配置，如果为null则使用中文默认配置</param>
        /// <returns>OCR识别结果</returns>
        public async Task<OcrResult> RecognizeImageBase64Async(string base64Image, Dictionary<string, object> configs = null)
        {
            try
            {
                // 如果未指定配置，使用中文识别默认配置
                if (configs == null)
                {
                    configs = GetChineseOcrOptions();
                }

                // 准备请求数据
                var data = new
                {
                    base64 = base64Image,
                    options = configs
                };

                // 构建API请求路径
                string url = $"{BaseUrl}/api/ocr";

                // 记录API请求开始
                LogUtil.AddLog($"发送OCR识别请求: {url}");

                // 发送请求
                var content = new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(url, content);

                // 确保响应成功
                response.EnsureSuccessStatusCode();

                // 读取响应内容
                string responseBody = await response.Content.ReadAsStringAsync();

                // 检查是否为空
                if (string.IsNullOrEmpty(responseBody))
                {
                    LogUtil.AddErrorLog("OCR服务返回了空响应");
                    throw new Exception("OCR服务返回了空响应");
                }

                // 使用更灵活的方式解析JSON响应
                try
                {
                    var result = JsonConvert.DeserializeObject<OcrResult>(responseBody);

                    // 检查结果状态
                    if (result == null)
                    {
                        LogUtil.AddErrorLog("OCR结果解析为null");
                        result = new OcrResult
                        {
                            code = -1,
                            msg = "OCR结果解析失败",
                            data = new OcrData { text = responseBody }
                        };
                    }
                    else if (result.code != 100)
                    {
                        LogUtil.AddErrorLog($"OCR服务返回错误: code={result.code}, msg={result.msg}");
                    }

                    // 确保data不为null
                    if (result.data == null)
                    {
                        result.data = new OcrData { text = responseBody };
                    }

                    return result;
                }
                catch (JsonException primaryJsonEx)
                {
                    LogUtil.AddErrorLog($"标准解析OCR响应失败: {primaryJsonEx.Message}");
                    LogUtil.AddErrorLog($"原始响应: {responseBody?.Substring(0, Math.Min(500, responseBody?.Length ?? 0))}");

                    // 尝试解析为简单结构并手动构建OcrResult
                    try
                    {
                        var jObject = JObject.Parse(responseBody);

                        var manualResult = new OcrResult
                        {
                            code = jObject["code"]?.ToObject<int>() ?? -1,
                            msg = jObject["msg"]?.ToString() ?? "JSON解析错误"
                        };

                        // 处理data字段
                        if (jObject["data"] != null)
                        {
                            // 根据data字段的类型进行不同处理
                            var dataToken = jObject["data"];
                            if (dataToken.Type == JTokenType.String)
                            {
                                // 直接将字符串作为text
                                manualResult.data = new OcrData { text = dataToken.ToString() };
                            }
                            else if (dataToken.Type == JTokenType.Object)
                            {
                                // 尝试提取text字段
                                var textContent = dataToken["text"]?.ToString();
                                manualResult.data = new OcrData
                                {
                                    text = textContent ?? dataToken.ToString()
                                };
                            }
                            else if (dataToken.Type == JTokenType.Array)
                            {
                                // 将数组连接为字符串
                                manualResult.data = new OcrData
                                {
                                    text = string.Join("\n", dataToken.Select(t => t.ToString()))
                                };
                            }
                            else
                            {
                                // 其他情况
                                manualResult.data = new OcrData { text = dataToken.ToString() };
                            }
                        }
                        else
                        {
                            // 无法提取data，使用原始响应
                            manualResult.data = new OcrData { text = responseBody };
                        }

                        return manualResult;
                    }
                    catch (Exception ex)
                    {
                        // 所有解析方法失败，创建基本结果
                        LogUtil.AddErrorLog($"尝试手动解析OCR响应也失败: {ex.Message}");
                        return new OcrResult
                        {
                            code = -1,
                            msg = $"解析OCR响应失败: {primaryJsonEx.Message}",
                            data = new OcrData { text = responseBody }
                        };
                    }
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"标准解析OCR响应失败: {ex.Message}");
                    LogUtil.AddErrorLog($"原始响应: {responseBody?.Substring(0, Math.Min(500, responseBody?.Length ?? 0))}");

                    // 尝试使用更宽松的设置解析
                    try
                    {
                        var settings = new JsonSerializerSettings
                        {
                            Error = (sender, args) => args.ErrorContext.Handled = true,
                            MissingMemberHandling = MissingMemberHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore
                        };

                        var result = JsonConvert.DeserializeObject<OcrResult>(responseBody, settings);

                        if (result != null)
                        {
                            LogUtil.AddLog("使用宽松设置成功解析OCR响应");
                            return result;
                        }
                    }
                    catch (Exception secondaryEx)
                    {
                        LogUtil.AddErrorLog($"使用宽松设置解析OCR响应也失败: {secondaryEx.Message}");
                    }

                    // 如果两种方式都失败，尝试手动解析关键信息
                    try
                    {
                        // 尝试作为JObject解析
                        var jObject = JObject.Parse(responseBody);

                        // 创建一个基本的OcrResult
                        var manualResult = new OcrResult
                        {
                            code = jObject["code"]?.ToObject<int>() ?? -1,
                            msg = jObject["msg"]?.ToString() ?? "JSON解析错误"
                        };

                        // 关键改进：处理data字段直接是字符串的情况
                        if (jObject["data"] != null)
                        {
                            // 判断data字段的类型
                            var dataToken = jObject["data"];

                            // 情况1: data是字符串 - 直接创建OcrData对象
                            if (dataToken.Type == JTokenType.String)
                            {
                                string textContent = dataToken.ToString();
                                manualResult.data = new OcrData
                                {
                                    text = textContent,
                                    time_cost = jObject["time"]?.ToObject<double>() ?? 0
                                };
                                LogUtil.AddLog("data字段为字符串，直接用作text内容");
                            }
                            // 情况2: data是对象 - 提取text字段
                            else if (dataToken.Type == JTokenType.Object)
                            {
                                // 尝试提取text内容
                                string extractedText = null;

                                if (dataToken["text"] != null)
                                {
                                    var textToken = dataToken["text"];
                                    if (textToken.Type == JTokenType.String)
                                    {
                                        extractedText = textToken.ToString();
                                    }
                                    else if (textToken.Type == JTokenType.Array)
                                    {
                                        extractedText = string.Join("\n", textToken.Select(t => t.ToString()));
                                    }
                                    else
                                    {
                                        extractedText = textToken.ToString();
                                    }
                                }

                                manualResult.data = new OcrData
                                {
                                    text = extractedText ?? responseBody,
                                    time_cost = dataToken["time_cost"]?.ToObject<double>() ?? 0
                                };

                                // 尝试提取details
                                if (dataToken["details"] != null && dataToken["details"].Type == JTokenType.Array)
                                {
                                    try
                                    {
                                        manualResult.data.details = dataToken["details"].ToObject<List<OcrDetail>>();
                                    }
                                    catch
                                    {
                                        // 忽略details解析错误
                                    }
                                }
                            }
                            // 情况3: data是数组 - 转换为文本
                            else if (dataToken.Type == JTokenType.Array)
                            {
                                string extractedText = string.Join("\n", dataToken.Select(t => t.ToString()));
                                manualResult.data = new OcrData
                                {
                                    text = extractedText,
                                    time_cost = jObject["time"]?.ToObject<double>() ?? 0
                                };
                                LogUtil.AddLog("data字段为数组，已合并为text内容");
                            }
                            else
                            {
                                // 其他类型，尝试转换为字符串
                                manualResult.data = new OcrData
                                {
                                    text = dataToken.ToString(),
                                    time_cost = jObject["time"]?.ToObject<double>() ?? 0
                                };
                            }
                        }
                        else
                        {
                            manualResult.data = new OcrData { text = responseBody };
                        }

                        LogUtil.AddLog("通过手动解析创建了OCR结果");
                        return manualResult;
                    }
                    catch (Exception manualEx)
                    {
                        LogUtil.AddErrorLog($"手动解析OCR响应也失败: {manualEx.Message}");
                    }

                    // 所有解析方法都失败，创建一个包含原始响应的错误结果
                    return new OcrResult
                    {
                        code = -1,
                        msg = $"解析OCR响应失败",
                        data = new OcrData { text = responseBody }
                    };
                }
            }
            catch (HttpRequestException httpEx)
            {
                LogUtil.AddErrorLog($"发送OCR请求时发生HTTP错误: {httpEx.Message}");
                throw new Exception($"发送OCR请求时发生错误: {httpEx.Message}", httpEx);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"OCR识别过程中发生错误: {ex.Message}");
                throw new Exception($"识别过程发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从图片字节数组进行OCR识别，默认使用中文识别配置
        /// </summary>
        /// <param name="imageBytes">图片字节数组</param>
        /// <param name="configs">识别配置，如果为null则使用中文默认配置</param>
        /// <returns>OCR识别结果</returns>
        public async Task<OcrResult> RecognizeImageBytesAsync(byte[] imageBytes, Dictionary<string, object> configs = null)
        {
            try
            {
                if (imageBytes == null || imageBytes.Length == 0)
                {
                    throw new ArgumentException("图像数据不能为空");
                }

                // 转换为Base64
                string base64 = Convert.ToBase64String(imageBytes);

                // 调用Base64识别方法
                return await RecognizeImageBase64Async(base64, configs);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理图像字节数组时发生错误: {ex.Message}");
                throw new Exception($"处理图像字节数组时发生错误: {ex.Message}", ex);
            }
        }


        /// <summary>
        /// 识别发票图片字节数组并提取信息
        /// </summary>
        /// <param name="imageBytes">图片字节数组</param>
        /// <returns>提取的发票信息</returns>
        public async Task<OcrInvoiceInfo> RecognizeInvoice(byte[] imageBytes)
        {
            try
            {
                if (imageBytes == null || imageBytes.Length == 0)
                {
                    return new OcrInvoiceInfo { Remark = "图片数据不能为空" };
                }
                
                // 不再使用预处理，直接使用原始图像
                // byte[] processedImageBytes = ImagePreprocessor.AdaptiveProcessForInvoice(imageBytes);
                
                // 执行OCR识别
                OcrResult ocrResult;
                try
                {
                    // 直接使用原始图像进行识别
                    ocrResult = await RecognizeImageBytesAsync(imageBytes);
                }
                catch (Exception ex)
                {
                    return new OcrInvoiceInfo
                    {
                        Remark = $"发票识别过程中发生错误: {ex.Message}"
                    };
                }
                
                // 如果识别失败，返回错误信息
                if (ocrResult.code != 100 || ocrResult.data == null)
                {
                    return new OcrInvoiceInfo
                    {
                        Remark = $"OCR识别失败: {ocrResult.msg}"
                    };
                }
                
                // 提取发票信息
                string jsonResult = JsonConvert.SerializeObject(ocrResult);
                // 将OCR结果解析为发票
                var invoiceInfo = ExtractInvoiceFromOcrResult(jsonResult);
                
                // 检查是否有错误
                if (!string.IsNullOrEmpty(invoiceInfo.Remark) && 
                    (invoiceInfo.Remark.StartsWith("OCR识别失败") || 
                     invoiceInfo.Remark.StartsWith("发票识别过程中发生错误")))
                {
                    // 直接返回错误信息，不再抛出异常
                    return invoiceInfo;
                }
                
                return invoiceInfo;
            }
            catch (Exception ex)
            {
                // 记录错误并返回带错误信息的结果
                LogUtil.AddErrorLog($"处理发票图片时发生错误: {ex.Message}");
                
                return new OcrInvoiceInfo
                {
                    Remark = $"处理发票图片时发生错误: {ex.Message}"
                };
            }
        }
        
        #endregion
        
        #region 数据结构
    
    /// <summary>
    /// OCR识别结果
    /// </summary>
    public class OcrResult
    {
        /// <summary>
        /// 状态码：0表示成功，其他表示失败
        /// </summary>
        public int code { get; set; }
        
        /// <summary>
        /// 消息说明
        /// </summary>
        public string msg { get; set; }
        
        /// <summary>
            /// 识别数据
        /// </summary>
        public OcrData data { get; set; }
    }
    
    /// <summary>
    /// OCR识别数据
    /// </summary>
    public class OcrData
    {
        /// <summary>
        /// 识别的文本内容，可能是字符串或复杂结构
        /// </summary>
        public string text { get; set; }
        
        /// <summary>
        /// 识别的文本详情
        /// </summary>
        public List<OcrDetail> details { get; set; }
        
        /// <summary>
        /// 识别消耗的时间（毫秒）
        /// </summary>
        public double time_cost { get; set; }
    }
    
    /// <summary>
        /// OCR识别详情
    /// </summary>
    public class OcrDetail
    {
        /// <summary>
            /// 识别的文本内容
        /// </summary>
        public string text { get; set; }
        
        /// <summary>
            /// 识别的置信度
        /// </summary>
        public double score { get; set; }
        
        /// <summary>
            /// 文本在图像中的位置
        /// </summary>
        public List<List<int>> box { get; set; }
    }
    
    /// <summary>
    /// 二维码识别结果
    /// </summary>
    public class QrCodeResult
    {
        /// <summary>
        /// 状态码：0表示成功，其他表示失败
        /// </summary>
        public int code { get; set; }
        
        /// <summary>
        /// 消息说明
        /// </summary>
        public string msg { get; set; }
        
        /// <summary>
        /// 识别结果数据
        /// </summary>
        public QrCodeData data { get; set; }
    }
    
    /// <summary>
    /// 二维码识别数据
    /// </summary>
    public class QrCodeData
    {
        /// <summary>
        /// 识别出的二维码内容列表
        /// </summary>
        public List<QrCodeInfo> qrcode { get; set; } = new List<QrCodeInfo>();
        
        /// <summary>
        /// 识别消耗的时间（毫秒）
        /// </summary>
        public double time_cost { get; set; }
    }
    
    /// <summary>
    /// 二维码信息
    /// </summary>
    public class QrCodeInfo
    {
        /// <summary>
        /// 二维码内容
        /// </summary>
        public string text { get; set; }
        
        /// <summary>
        /// 二维码在图像中的位置信息
        /// </summary>
        public List<List<int>> box { get; set; }
    }
    
    /// <summary>
    /// 二维码生成结果
    /// </summary>
    public class QrCodeGenResult
    {
        /// <summary>
        /// 状态码：0表示成功，其他表示失败
        /// </summary>
        public int code { get; set; }
        
        /// <summary>
        /// 消息说明
        /// </summary>
        public string msg { get; set; }
        
        /// <summary>
        /// 生成结果数据
        /// </summary>
        public QrCodeGenData data { get; set; }
    }
    
    /// <summary>
    /// 二维码生成数据
    /// </summary>
    public class QrCodeGenData
    {
        /// <summary>
        /// 生成的二维码图片（Base64）
        /// </summary>
        public string base64 { get; set; }
    }
    
    /// <summary>
    /// 服务状态结果
    /// </summary>
    public class StatusResult
    {
        /// <summary>
        /// 状态码：0表示成功，其他表示失败
        /// </summary>
        public int code { get; set; }
        
        /// <summary>
        /// 消息说明
        /// </summary>
        public string msg { get; set; }
        
        /// <summary>
        /// 状态数据
        /// </summary>
        public StatusData data { get; set; }
    }
    
    /// <summary>
    /// 服务状态数据
    /// </summary>
    public class StatusData
    {
        /// <summary>
        /// 服务是否运行
        /// </summary>
        public bool is_running { get; set; }
        
        /// <summary>
        /// 服务版本
        /// </summary>
        public string version { get; set; }
        
        /// <summary>
        /// 其他状态信息
        /// </summary>
        public Dictionary<string, object> info { get; set; }
    }
    
            
        /// <summary>
        /// 发票信息提取模型
        /// </summary>
        public class OcrInvoiceInfo
        {
            // 发票基本信息
            public string InvoiceType { get; set; }        // 发票类型
            public string InvoiceCode { get; set; }        // 发票代码
            public string InvoiceNumber { get; set; }      // 发票号码
            public DateTime? InvoiceDate { get; set; }     // 开票日期

            
            // 购买方信息
            public string BuyerName { get; set; }          // 购买方名称
            public string BuyerTaxNumber { get; set; }     // 购买方纳税人识别号
            
            // 销售方信息
            public string SellerName { get; set; }         // 销售方名称
            public string SellerTaxNumber { get; set; }    // 销售方纳税人识别号
            
            // 商品信息
            public List<OcrInvoiceItem> Items { get; set; } = new List<OcrInvoiceItem>();
            
            // 金额信息
            public decimal TotalAmount { get; set; }       // 合计金额
            public decimal TotalTax { get; set; }          // 合计税额
            public decimal TotalAmountWithTax { get; set; } // 价税合计
            public string AmountInWords { get; set; }      // 价税合计大写
            
            // 其他信息
            public string Remark { get; set; }             // 备注
            public string Drawer { get; set; }             // 开票人
            public string Reviewer { get; set; }           // 复核人
            public string Payee { get; set; }              // 收款人
            
            // 原始识别文本
            public string OriginalText { get; set; }       // 原始OCR识别文本
        }
        
        /// <summary>
        /// 发票商品项
        /// </summary>
        public class OcrInvoiceItem
        {
            public string Name { get; set; }               // 商品名称
            public string Specification { get; set; }      // 规格型号
            public string Unit { get; set; }               // 单位
            public decimal Quantity { get; set; }          // 数量
            public decimal UnitPrice { get; set; }         // 单价
            public decimal Amount { get; set; }            // 金额
            public decimal TaxRate { get; set; }           // 税率
            public decimal Tax { get; set; }               // 税额
        }
        
    #endregion
        
  
    /// <summary>
    /// 从OCR结果中提取发票信息
    /// </summary>
    /// <param name="jsonResult">OCR识别结果的JSON字符串</param>
    /// <returns>提取的发票信息</returns>
    private OcrInvoiceInfo ExtractInvoiceFromOcrResult(string jsonResult)
    {
        try
        {
            if (string.IsNullOrEmpty(jsonResult))
            {
                return new OcrInvoiceInfo { Remark = "OCR识别结果为空" };
            }

            // 解析OCR结果JSON
            var ocrResult = JsonConvert.DeserializeObject<OcrResult>(jsonResult);
            if (ocrResult == null || ocrResult.code != 100 || ocrResult.data == null)
            {
                return new OcrInvoiceInfo 
                { 
                    Remark = $"OCR识别失败: {ocrResult?.msg ?? "无效的结果"}" 
                };
            }

            // 获取OCR识别的文本内容
            string ocrText = ocrResult.data.text;
            if (string.IsNullOrEmpty(ocrText))
            {
                return new OcrInvoiceInfo { Remark = "OCR识别文本为空" };
            }

            // 预处理OCR文本，清理常见错误
            ocrText = PreprocessOcrText(ocrText);

            // 创建发票信息对象
            var invoiceInfo = new OcrInvoiceInfo
            {
                OriginalText = ocrText
            };

            // 检查是否包含"购买方信"和"销售方"的格式（特定格式处理，与您提供的样例相匹配）
            if (ocrText.Contains("购买方") && (ocrText.Contains("销售方") || ocrText.Contains("售方")))
            {
                try
                {
                    // 处理购买方信息 - 使用更宽容的正则表达式
                    var buyerMatch = Regex.Match(ocrText, @"购买方[^：]*[：:： ](.*?)(?=项目|名称|销售|售方|规格|型号|单位|年|数量|单价|金额|税率)", RegexOptions.Singleline);
                    if (buyerMatch.Success)
                    {
                        string buyerInfo = buyerMatch.Groups[1].Value.Trim();
                        
                        // 获取名称（通常在购买方信息的第一行）
                        var nameMatch = Regex.Match(buyerInfo, @"名称[:：]?\s*([^\r\n]+)");
                        if (nameMatch.Success)
                        {
                            invoiceInfo.BuyerName = nameMatch.Groups[1].Value.Trim();
                        }
                        else
                        {
                            // 如果没有找到"名称："，尝试直接提取第一行
                            var firstLineMatch = Regex.Match(buyerInfo, @"^\s*([^\r\n]+)");
                            if (firstLineMatch.Success)
                            {
                                invoiceInfo.BuyerName = firstLineMatch.Groups[1].Value.Trim();
                            }
                        }

                        // 获取纳税人识别号 - 更宽容的匹配
                        var buyerTaxIdMatch = Regex.Match(buyerInfo, @"(?:统一[社会信用代码]*|[社会信用]*代码|纳税人识别号)[:：]?\s*([0-9A-Za-z]+)");
                        if (buyerTaxIdMatch.Success)
                        {
                            invoiceInfo.BuyerTaxNumber = buyerTaxIdMatch.Groups[1].Value.Trim();
                        }
                        else
                        {
                            // 尝试直接匹配类似社会信用代码的18位数字字母组合
                            var taxCodeMatch = Regex.Match(buyerInfo, @"([0-9A-Za-z]{15,20})");
                            if (taxCodeMatch.Success)
                            {
                                invoiceInfo.BuyerTaxNumber = taxCodeMatch.Groups[1].Value.Trim();
                            }
                        }
                    }

                    // 处理销售方信息 - 使用更宽容的正则表达式
                    var sellerMatch = Regex.Match(ocrText, @"(?:销售方|售方)[^：]*[：:： ](.*?)(?=备注|价税|￥|Y|¥|合计)", RegexOptions.Singleline);
                    if (sellerMatch.Success)
                    {
                        string sellerInfo = sellerMatch.Groups[1].Value.Trim();
                        
                        // 获取名称
                        var nameMatch = Regex.Match(sellerInfo, @"名称[:：]?\s*([^\r\n]+)");
                        if (nameMatch.Success)
                        {
                            invoiceInfo.SellerName = nameMatch.Groups[1].Value.Trim();
                        }
                        else
                        {
                            // 如果没有找到"名称："，尝试直接提取第一行
                            var firstLineMatch = Regex.Match(sellerInfo, @"^\s*([^\r\n]+)");
                            if (firstLineMatch.Success)
                            {
                                invoiceInfo.SellerName = firstLineMatch.Groups[1].Value.Trim();
                            }
                        }

                        // 获取纳税人识别号 - 更宽容的匹配
                        var sellerTaxIdMatch = Regex.Match(sellerInfo, @"(?:统一[社会信用代码]*|[社会信用]*代码|纳税人识别号)[:：]?\s*([0-9A-Za-z]+)");
                        if (sellerTaxIdMatch.Success)
                        {
                            invoiceInfo.SellerTaxNumber = sellerTaxIdMatch.Groups[1].Value.Trim();
                        }
                        else
                        {
                            // 尝试直接匹配类似社会信用代码的18位数字字母组合
                            var taxCodeMatch = Regex.Match(sellerInfo, @"([0-9A-Za-z]{15,20})");
                            if (taxCodeMatch.Success)
                            {
                                invoiceInfo.SellerTaxNumber = taxCodeMatch.Groups[1].Value.Trim();
                            }
                        }
                    }

                    // 获取金额相关信息 - 更强大的匹配
                    var amountMatch = Regex.Match(ocrText, @"(?:金额|总额)[^\d]*?([0-9,\.\s]+)");
                    if (amountMatch.Success)
                    {
                        string amountStr = CleanNumberString(amountMatch.Groups[1].Value);
                        if (decimal.TryParse(amountStr, out decimal amount))
                        {
                            invoiceInfo.TotalAmount = amount;
                        }
                    }

                    var taxRateMatch = Regex.Match(ocrText, @"(?:税率|征收率)[^\d]*?([0-9%]+)");
                    if (taxRateMatch.Success)
                    {
                        string taxRateStr = taxRateMatch.Groups[1].Value.Trim().Replace("%", "");
                        if (int.TryParse(taxRateStr, out int taxRate))
                        {
                            // 添加一个税率项
                            if (invoiceInfo.Items.Count == 0)
                            {
                                invoiceInfo.Items.Add(new OcrInvoiceItem { TaxRate = taxRate / 100.0m });
                            }
                            else
                            {
                                invoiceInfo.Items[0].TaxRate = taxRate / 100.0m;
                            }
                        }
                    }

                    var taxMatch = Regex.Match(ocrText, @"(?:税额)[^\d]*?([0-9,\.\s]+)");
                    if (taxMatch.Success)
                    {
                        string taxStr = CleanNumberString(taxMatch.Groups[1].Value);
                        if (decimal.TryParse(taxStr, out decimal tax))
                        {
                            invoiceInfo.TotalTax = tax;
                        }
                    }

                    // 尝试多种格式匹配价税合计
                    var totalMatch = Regex.Match(ocrText, @"(?:价税合计|(?:小写)[^\d]*?)[￥¥Y]*\s*([0-9,\.\s]+)");
                    if (totalMatch.Success)
                    {
                        string totalStr = CleanNumberString(totalMatch.Groups[1].Value);
                        if (decimal.TryParse(totalStr, out decimal total))
                        {
                            invoiceInfo.TotalAmountWithTax = total;
                        }
                    }

                    // 提取价税合计大写金额
                    var chineseAmountMatch = Regex.Match(ocrText, @"(?:价税合计|大写)[^a-zA-Z0-9]*?([零壹贰叁肆伍陆柒捌玖拾佰仟万亿元角分整点零一二三四五六七八九十百千万亿元角分整点]+)");
                    if (chineseAmountMatch.Success)
                    {
                        invoiceInfo.AmountInWords = CorrectChineseAmount(chineseAmountMatch.Groups[1].Value.Trim());
                    }

                    // 提取项目信息 - 不仅仅依赖*号标记
                    var itemMatch = Regex.Match(ocrText, @"(?:项目名称|服务名称).*?(?=合计|价税合计|￥|Y|¥)", RegexOptions.Singleline);
                    if (itemMatch.Success)
                    {
                        string itemInfo = itemMatch.Value;
                        var items = new List<OcrInvoiceItem>();

                        // 1. 首先尝试提取带*号的项目
                        var starItemMatches = Regex.Matches(itemInfo, @"\*([^*]+)\*");
                        foreach (Match match in starItemMatches)
                        {
                            items.Add(new OcrInvoiceItem { Name = match.Groups[1].Value.Trim() });
                        }

                        // 2. 如果没有*号项目，尝试从服务名称后面直接提取
                        if (items.Count == 0)
                        {
                            var serviceNameMatch = Regex.Match(itemInfo, @"(?:信息技术服务|服务|信息服务)([^\d\r\n]+)");
                            if (serviceNameMatch.Success)
                            {
                                items.Add(new OcrInvoiceItem { Name = "信息技术服务" + serviceNameMatch.Groups[1].Value.Trim() });
                            }
                            else
                            {
                                // 3. 直接提取第一行作为服务名称
                                var firstLineMatch = Regex.Match(itemInfo, @"项目名称.*?[\r\n]+\s*([^\r\n]+)");
                                if (firstLineMatch.Success)
                                {
                                    items.Add(new OcrInvoiceItem { Name = firstLineMatch.Groups[1].Value.Trim() });
                                }
                            }
                        }

                        if (items.Count > 0)
                        {
                            invoiceInfo.Items = items;
                        }
                    }

                    // 提取开票人
                    var drawerMatch = Regex.Match(ocrText, @"开票人[:：]?\s*([^\r\n]+)");
                    if (drawerMatch.Success)
                    {
                        invoiceInfo.Drawer = drawerMatch.Groups[1].Value.Trim();
                    }

                    // 如果金额仍为0，尝试查找所有金额格式的数字并选取合适的作为总金额
                    if (invoiceInfo.TotalAmountWithTax == 0)
                    {
                        var numericMatches = Regex.Matches(ocrText, @"(\d+\.\d{2})");
                        var amounts = new List<decimal>();
                        
                        foreach (Match match in numericMatches)
                        {
                            if (decimal.TryParse(match.Groups[1].Value, out decimal amount))
                            {
                                amounts.Add(amount);
                            }
                        }
                        
                        if (amounts.Count > 0)
                        {
                            // 排序金额并推测哪个可能是总金额
                            amounts.Sort();
                            
                            // 假设最大的是总金额
                            var maxAmount = amounts[amounts.Count - 1];
                            
                            // 如果已有金额和税额，验证一下计算结果
                            if (invoiceInfo.TotalAmount > 0 && invoiceInfo.TotalTax > 0)
                            {
                                decimal calculatedTotal = invoiceInfo.TotalAmount + invoiceInfo.TotalTax;
                                
                                // 如果计算结果与某个提取到的金额非常接近，优先使用那个金额
                                foreach (var amt in amounts)
                                {
                                    if (Math.Abs(calculatedTotal - amt) < 0.1m)
                                    {
                                        maxAmount = amt;
                                        break;
                                    }
                                }
                            }
                            
                            invoiceInfo.TotalAmountWithTax = maxAmount;
                        }
                    }

                    // 验证金额的一致性
                    if (invoiceInfo.TotalAmount > 0 && invoiceInfo.TotalTax > 0 && invoiceInfo.TotalAmountWithTax > 0)
                    {
                        decimal calculatedTotal = invoiceInfo.TotalAmount + invoiceInfo.TotalTax;
                        // 如果计算结果与提取的总额相差较大，可能是识别有误，记录在备注中
                        if (Math.Abs(calculatedTotal - invoiceInfo.TotalAmountWithTax) > 0.5m)
                        {
                            invoiceInfo.Remark += $"金额校验不一致: 金额{invoiceInfo.TotalAmount}+税额{invoiceInfo.TotalTax}≠总额{invoiceInfo.TotalAmountWithTax} ";
                        }
                    }

                    return invoiceInfo;
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"特定格式发票解析错误: {ex.Message}");
                    // 继续尝试通用格式解析
                }
            }

            // 使用正则表达式解析发票内容
            // 1. 解析发票类型
            var typeMatches = Regex.Matches(ocrText, @"发票类型[:：]?\s*([^\r\n]+)");
            if (typeMatches.Count > 0)
            {
                invoiceInfo.InvoiceType = typeMatches[0].Groups[1].Value.Trim();
            }
            
            // 2. 解析发票代码
            var codeMatches = Regex.Matches(ocrText, @"发票代码[:：]?\s*(\d+)");
            if (codeMatches.Count > 0)
            {
                invoiceInfo.InvoiceCode = codeMatches[0].Groups[1].Value.Trim();
            }

            // 3. 解析发票号码
            var numberMatches = Regex.Matches(ocrText, @"发票号码[:：]?\s*(\d+)");
            if (numberMatches.Count > 0)
            {
                invoiceInfo.InvoiceNumber = numberMatches[0].Groups[1].Value.Trim();
            }

            // 4. 解析开票日期
            var dateMatches = Regex.Matches(ocrText, @"开票日期[:：]?\s*(\d{4}[年\-\.\/]\d{1,2}[月\-\.\/]\d{1,2}日?)");
            if (dateMatches.Count > 0)
            {
                string dateText = dateMatches[0].Groups[1].Value.Trim()
                    .Replace("年", "-").Replace("月", "-").Replace("日", "");
                
                // 尝试解析日期
                if (DateTime.TryParse(dateText, out DateTime invoiceDate))
                {
                    invoiceInfo.InvoiceDate = invoiceDate;
                }
            }

            // 5. 解析购买方信息
            var buyerMatches = Regex.Matches(ocrText, @"购买方[名称抬头]?[:：]?\s*([^\r\n]+)");
            if (buyerMatches.Count > 0)
            {
                invoiceInfo.BuyerName = buyerMatches[0].Groups[1].Value.Trim();
            }
            else
            {
                // 尝试其他可能的格式
                buyerMatches = Regex.Matches(ocrText, @"名称[:：]?\s*([^\r\n]+)");
                if (buyerMatches.Count > 0 && ocrText.IndexOf(buyerMatches[0].Value) < ocrText.IndexOf("销售方"))
                {
                    invoiceInfo.BuyerName = buyerMatches[0].Groups[1].Value.Trim();
                }
            }

            // 6. 解析购买方税号
            var buyerTaxMatches = Regex.Matches(ocrText, @"购买方.{0,10}识别号[:：]?\s*([^\r\n]+)");
            if (buyerTaxMatches.Count > 0)
            {
                invoiceInfo.BuyerTaxNumber = buyerTaxMatches[0].Groups[1].Value.Trim();
            }
            else
            {
                // 尝试其他可能的格式
                buyerTaxMatches = Regex.Matches(ocrText, @"纳税人识别号[:：]?\s*([^\r\n]+)");
                if (buyerTaxMatches.Count > 0 && ocrText.IndexOf(buyerTaxMatches[0].Value) < ocrText.IndexOf("销售方"))
                {
                    invoiceInfo.BuyerTaxNumber = buyerTaxMatches[0].Groups[1].Value.Trim();
                }
            }

            // 7. 解析销售方信息
            var sellerMatches = Regex.Matches(ocrText, @"销售方[名称抬头]?[:：]?\s*([^\r\n]+)");
            if (sellerMatches.Count > 0)
            {
                invoiceInfo.SellerName = sellerMatches[0].Groups[1].Value.Trim();
            }
            else
            {
                // 尝试其他可能的格式
                sellerMatches = Regex.Matches(ocrText, @"名称[:：]?\s*([^\r\n]+)");
                if (sellerMatches.Count > 0 && ocrText.LastIndexOf(sellerMatches[0].Value) > ocrText.IndexOf("销售方"))
                {
                    invoiceInfo.SellerName = sellerMatches[sellerMatches.Count - 1].Groups[1].Value.Trim();
                }
            }

            // 8. 解析销售方税号
            var sellerTaxMatches = Regex.Matches(ocrText, @"销售方.{0,10}识别号[:：]?\s*([^\r\n]+)");
            if (sellerTaxMatches.Count > 0)
            {
                invoiceInfo.SellerTaxNumber = sellerTaxMatches[0].Groups[1].Value.Trim();
            }

            // 9. 解析金额和税额
            // 金额合计
            var amountMatches = Regex.Matches(ocrText, @"金额[:：]?\s*￥?\s*([0-9.,]+)");
            if (amountMatches.Count > 0)
            {
                string amountStr = amountMatches[0].Groups[1].Value.Trim().Replace(",", "");
                if (decimal.TryParse(amountStr, out decimal amount))
                {
                    invoiceInfo.TotalAmount = amount;
                }
            }
            
            // 税额
            var taxMatches = Regex.Matches(ocrText, @"税额[:：]?\s*￥?\s*([0-9.,]+)");
            if (taxMatches.Count > 0)
            {
                string taxStr = taxMatches[0].Groups[1].Value.Trim().Replace(",", "");
                if (decimal.TryParse(taxStr, out decimal tax))
                {
                    invoiceInfo.TotalTax = tax;
                }
            }
            
            // 价税合计
            var totalMatches = Regex.Matches(ocrText, @"价税合计[:：]?\s*￥?\s*([0-9.,]+)");
            if (totalMatches.Count > 0)
            {
                string totalStr = totalMatches[0].Groups[1].Value.Trim().Replace(",", "");
                if (decimal.TryParse(totalStr, out decimal total))
                {
                    invoiceInfo.TotalAmountWithTax = total;
                }
            }
            else
            {
                // 尝试大写金额匹配
                var chineseAmountMatches = Regex.Matches(ocrText, @"价税合计[:：]?\s*\(大写\)\s*([^￥]+)");
                if (chineseAmountMatches.Count > 0)
                {
                    invoiceInfo.AmountInWords = chineseAmountMatches[0].Groups[1].Value.Trim();
                }
            }

            // 10. 解析备注
            var remarkMatches = Regex.Matches(ocrText, @"备注[:：]?\s*([^\r\n]+)");
            if (remarkMatches.Count > 0)
            {
                invoiceInfo.Remark = remarkMatches[0].Groups[1].Value.Trim();
            }

            // 11. 解析开票人
            var drawerMatches = Regex.Matches(ocrText, @"开票人[:：]?\s*([^\r\n]+)");
            if (drawerMatches.Count > 0)
            {
                invoiceInfo.Drawer = drawerMatches[0].Groups[1].Value.Trim();
            }

            // 如果金额仍未成功提取，尝试使用更宽松的正则表达式
            if (invoiceInfo.TotalAmount == 0)
            {
                var numericMatches = Regex.Matches(ocrText, @"(\d+\.\d{2})");
                List<decimal> amounts = new List<decimal>();
                
                foreach (Match match in numericMatches)
                {
                    if (decimal.TryParse(match.Groups[1].Value, out decimal amount))
                    {
                        amounts.Add(amount);
                    }
                }
                
                // 按大小排序，最大值通常是总金额
                if (amounts.Count > 0)
                {
                    amounts.Sort();
                    invoiceInfo.TotalAmountWithTax = amounts[amounts.Count - 1];
                }
            }

            // 特殊查找标记：如果到现在还没找到购买方和销售方，尝试直接根据您提供的OCR样例格式处理
            if (string.IsNullOrEmpty(invoiceInfo.BuyerName) && ocrText.Contains("购买方信息"))
            {
                var buyerNameMatch = Regex.Match(ocrText, @"购买方信息\s*名称[:：]?\s*([^\r\n]+)");
                if (buyerNameMatch.Success)
                {
                    invoiceInfo.BuyerName = buyerNameMatch.Groups[1].Value.Trim();
                }
                
                var buyerTaxIdMatch = Regex.Match(ocrText, @"购买方信息[\s\S]*?统一社会信用代码/纳税人识别号[:：]?\s*([^\r\n]+)");
                if (buyerTaxIdMatch.Success)
                {
                    invoiceInfo.BuyerTaxNumber = buyerTaxIdMatch.Groups[1].Value.Trim();
                }
            }
            
            if (string.IsNullOrEmpty(invoiceInfo.SellerName) && ocrText.Contains("销售方估"))
            {
                var sellerNameMatch = Regex.Match(ocrText, @"销售方估[\s\S]*?名称[:：]?\s*([^\r\n]+)");
                if (sellerNameMatch.Success)
                {
                    invoiceInfo.SellerName = sellerNameMatch.Groups[1].Value.Trim();
                }
                
                var sellerTaxIdMatch = Regex.Match(ocrText, @"销售方估[\s\S]*?统一社会信用代码/纳税人识别号[:：]?\s*([^\r\n]+)");
                if (sellerTaxIdMatch.Success)
                {
                    invoiceInfo.SellerTaxNumber = sellerTaxIdMatch.Groups[1].Value.Trim();
                }
            }

            return invoiceInfo;
        }
        catch (Exception ex)
        {
            LogUtil.AddErrorLog($"从OCR结果提取发票信息时发生错误: {ex.Message}");
            return new OcrInvoiceInfo { Remark = $"解析OCR结果时发生错误: {ex.Message}" };
        }
    }
    
    /// <summary>
    /// 预处理OCR识别文本，修正常见的识别错误
    /// </summary>
    /// <param name="text">原始OCR文本</param>
    /// <returns>处理后的文本</returns>
    private string PreprocessOcrText(string text)
    {
        if (string.IsNullOrEmpty(text))
            return text;
            
        // 替换常见的识别错误
        var replacements = new Dictionary<string, string>
        {
            { "你", "信" },                // "购买方你" → "购买方信"
            { "运", "估" },                // "销售方运" → "销售方估"
            { "行限公司", "有限公司" },      // "有限公司"识别错误
            { "让会信", "社会信" },         // "统一让会信" → "统一社会信"
            { "信月代码", "信用代码" },      // "社会信月代码" → "社会信用代码"
            { "执税人", "纳税人" },         // "执税人识别号" → "纳税人识别号"
            { "离代玛", "用代码" },         // "信离代玛" → "信用代码"
            { "费Y", "费￥" },             // 修正货币符号
            { "父津", "肆" },              // "父津万" → "肆万"
            { "拟任", "仟" },              // "拟任" → "仟"
            { "医整", "圆整" },            // "整"字识别错误
            { "页", "佰" },               // "页"→"佰"
            { "资税", "价税" },            // "资税合计" → "价税合计"
            { "资", "金" },               // "总资" → "总金"
            { "截次", "" },               // 删除无关文本
            { "载", "" },                 // 删除无关文本
            { "下", "" },                 // 删除无关文本
            { "教", "数" },               // "教量" → "数量"
        };
        
        foreach (var pair in replacements)
        {
            text = text.Replace(pair.Key, pair.Value);
        }
        
        return text;
    }
    
    /// <summary>
    /// 清理数字字符串，移除逗号、空格等干扰字符
    /// </summary>
    /// <param name="numberStr">原始数字字符串</param>
    /// <returns>清理后的数字字符串</returns>
    private string CleanNumberString(string numberStr)
    {
        if (string.IsNullOrEmpty(numberStr))
            return numberStr;
            
        // 清理数字字符串中的非数字部分
        // 1. 移除所有的逗号和空格
        string cleanStr = numberStr.Replace(",", "").Replace(" ", "");
        
        // 2. 处理特殊格式，如"46037, 7358490566"应转换为"46037.7358490566"
        if (cleanStr.Contains("7358490566") && !cleanStr.Contains("."))
        {
            cleanStr = cleanStr.Replace("7358490566", ".7358490566");
        }
        
        // 3. 确保只有一个小数点
        int dotCount = cleanStr.Count(c => c == '.');
        if (dotCount > 1)
        {
            int firstDotIndex = cleanStr.IndexOf('.');
            cleanStr = cleanStr.Substring(0, firstDotIndex + 1) + 
                      cleanStr.Substring(firstDotIndex + 1).Replace(".", "");
        }
        
        return cleanStr;
    }
    
    /// <summary>
    /// 修正中文大写金额中的识别错误
    /// </summary>
    /// <param name="chineseAmount">原始中文金额</param>
    /// <returns>修正后的中文金额</returns>
    private string CorrectChineseAmount(string chineseAmount)
    {
        if (string.IsNullOrEmpty(chineseAmount))
            return chineseAmount;
            
        // 替换常见的识别错误
        var replacements = new Dictionary<string, string>
        {
            { "父", "肆" },
            { "津", "万" },
            { "拟", "仟" },
            { "任", "捌" },
            { "页", "佰" },
            { "医", "圆" }
        };
        
        foreach (var pair in replacements)
        {
            chineseAmount = chineseAmount.Replace(pair.Key, pair.Value);
        }
        
        return chineseAmount;
    }


}
}
