﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System.Threading.Tasks;
using static CRM2_API.Model.ControllersViewModel.VM_Collectioninfo;
using Microsoft.AspNetCore.Http;
using System.IO;
using Newtonsoft.Json;
using System.Globalization;
using Microsoft.Extensions.Primitives;
using System.Linq;
using Newtonsoft.Json.Serialization;
using System.Text.Json.Nodes;
using Quartz;
using CRM2_API.Model.ControllersViewModel;

namespace CRM2_API.Common.Filter
{

    public class AddTransactionReceiptModelBinder<TAddTransactionReceipt> : IModelBinder
    where TAddTransactionReceipt : class, IAddTransactionReceipt,new()
    {
       // private static readonly IValueProvider EmptyValueProvider = new CompositeValueProvider();

        private static IEnumerable<string>? GetIndexNamesFromValueProviderResult(ValueProviderResult valueProviderResult)
        {
            var indexes = (string[]?)valueProviderResult;
            return (indexes == null || indexes.Length == 0) ? null : indexes;
        }

        public async Task<JsonObject> buildJson(FormValueProvider valueProvider, string key, JsonObject jsonObject,string jsonkey=null)
        {


            var childkeys = valueProvider.GetKeysFromPrefix(key);
            if (childkeys.Count > 0)
            {
                JsonObject newjson = new JsonObject();
                foreach (var item in childkeys.Keys)
                {
                 
                    string valuedata;
                    childkeys.TryGetValue(item, out valuedata);

                    await buildJson(valueProvider, valuedata, newjson, item);
                }
                if(string.IsNullOrEmpty(jsonkey))
                {
                    return newjson;
                }
                jsonObject.Add(jsonkey, newjson);
                return jsonObject;
            }
            else
            {
                //jsonObject.Add(jsonkey, JsonNode.Parse(valueProvider.GetValue(key).ToString()));
                jsonObject.Add(jsonkey, valueProvider.GetValue(key).ToString());
                return jsonObject;
            }

        }
        public async Task BindModelAsync(ModelBindingContext bindingContext)
        {
            if (bindingContext == null)
            {
                throw new ArgumentNullException(nameof(bindingContext));
            }

            if (bindingContext.ModelType == typeof(List<AddTransactionReceipt_In>))
            {
                await BindingContextResult<AddTransactionReceipt_In>(bindingContext);

            }
            else if (bindingContext.ModelType == typeof(List<UpdateTransactionReceipt_In>))
            {
                await BindingContextResult<UpdateTransactionReceipt_In>(bindingContext);
            }
            
        }

        public async Task BindingContextResult<TAddTransactionReceipt>(ModelBindingContext bindingContext) where TAddTransactionReceipt : class, IAddTransactionReceipt, new()
        {
            if (bindingContext.HttpContext.Request.Form.Keys.Count == 0)
            {
                var data = new List<TAddTransactionReceipt>();

                foreach (var item in bindingContext.HttpContext.Request.Form.Files)
                {
                    var fl = new FormFileCollection();
                    fl.Add(item);
                    var addTransactionReceipt_In = new TAddTransactionReceipt();
                    addTransactionReceipt_In.CollectioninfoAttachFile = fl;
                    data.Add(addTransactionReceipt_In);
                }
                bindingContext.Result = ModelBindingResult.Success(data);
                return;

            }
            else
            {

                var model = await bindingContext.HttpContext.Request.ReadFormAsync();


                var valueProvider = new FormValueProvider(
                       BindingSource.Form,
                       model,
                       CultureInfo.CurrentCulture);

                var datas = valueProvider.GetKeysFromPrefix(bindingContext.ModelName);
                List<TAddTransactionReceipt> datalist = new List<TAddTransactionReceipt>();
                int index = 0;
                foreach (var item in datas.Keys)
                {

                    string valuedata;
                    datas.TryGetValue(item, out valuedata);
                    var data = await buildJson(valueProvider, valuedata, new JsonObject());

                    var addTransactionReceipt_In = JsonConvert.DeserializeObject<TAddTransactionReceipt>(data.ToJsonString());
                    var flils = bindingContext.HttpContext.Request.Form.Files.Where(x => x.Name.Contains($"{bindingContext.ModelName}[{index}]")).ToList();
                    index++;
                    var flillist = new FormFileCollection();
                    flillist.AddRange(flils);
                    addTransactionReceipt_In.CollectioninfoAttachFile = flillist;
                    datalist.Add(addTransactionReceipt_In);
                }

                bindingContext.ValueProvider = valueProvider;
                // valueProvider.
                bindingContext.Result = ModelBindingResult.Success(datalist);

                return;
            }
        }




        private async Task GetFormFilesAsync(
            string modelName,
            ModelBindingContext bindingContext,
            ICollection<TAddTransactionReceipt> postedFiles)
        {
            var request = bindingContext.HttpContext.Request;
            if (request.HasFormContentType)
            {
                var form = await request.ReadFormAsync();

                foreach (var file in form.Files)
                {
                    // If there is an <input type="file" ... /> in the form and is left blank.
                    if (file.Length == 0 && string.IsNullOrEmpty(file.FileName))
                    {
                        continue;
                    }

                    if (file.Name.Equals(modelName, StringComparison.OrdinalIgnoreCase))
                    {
                       // postedFiles.Add(new RemoteStreamContent(file.OpenReadStream(), file.FileName, file.ContentType, file.Length).As<TRemoteStreamContent>());
                    }
                }
            }
            else if (bindingContext.IsTopLevelObject)
            {
               // postedFiles.Add(new RemoteStreamContent(request.Body, null, request.ContentType, request.ContentLength).As<TRemoteStreamContent>());
            }
        }

        private static ICollection<T> GetCompatibleCollection<T>(ModelBindingContext bindingContext)
        {
            var model = bindingContext.Model;
            var modelType = bindingContext.ModelType;

            // There's a limited set of collection types we can create here.
            //
            // For the simple cases: Choose List<T> if the destination type supports it (at least as an intermediary).
            //
            // For more complex cases: If the destination type is a class that implements ICollection<T>, then activate
            // an instance and return that.
            //
            // Otherwise just give up.
            if (typeof(T).IsAssignableFrom(modelType))
            {
                return new List<T>();
            }

            if (modelType == typeof(T[]))
            {
                return new List<T>();
            }

            // Does collection exist and can it be reused?
            if (model is ICollection<T> collection && !collection.IsReadOnly)
            {
                collection.Clear();

                return collection;
            }

            if (modelType.IsAssignableFrom(typeof(List<T>)))
            {
                return new List<T>();
            }

            return (ICollection<T>)Activator.CreateInstance(modelType)!;
        }


    }
}
