﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using System.Linq;

namespace CRM2_API.BLL
{
    public class BLL_Role : BaseBLL<BLL_Role>
    {
        //超级权限：系统管理员和高级管理人员
        private List<string> SUPERROLEIDS = new List<string>() { "ec6cf8a2-9ecd-4ede-a22e-63e9c8e454e8", "e4530a5d-540b-42db-9286-fe2217040cef" } ;
        //销售总监权限
        private List<string> SALESMANAGERIDS = new List<string>() { "0cafa3b1-1d14-4047-ae52-2feb0bf2c1cc" } ;
        /// <summary>
        /// 验证高级管理员权限
        /// </summary>
        /// <returns></returns>
        public bool CheckSuperUser()
        {
            var currentUserRoles = DbOpe_sys_userinrole.Instance.GetDataList(r => r.UserId == UserId);
            return currentUserRoles.Select(r => r.RoleId).ToList().Find(r=>SUPERROLEIDS.Contains(r))!=null;
        }

        public bool CheckAdminUser()
        {
            var currentUserRoles = DbOpe_sys_userinrole.Instance.GetDataList(r => r.UserId == UserId);
            return currentUserRoles.Select(r => r.RoleId).ToList().Find(r => r.Equals("ec6cf8a2-9ecd-4ede-a22e-63e9c8e454e8")) != null;
        }
        /// <summary>
        /// 验证销售总监权限
        /// </summary>
        /// <returns></returns>
        public bool CheckSalesManagerUser()
        {
            var currentUserRoles = DbOpe_sys_userinrole.Instance.GetDataList(r => r.UserId == UserId);
            return currentUserRoles.Select(r => r.RoleId).ToList().Find(r => SALESMANAGERIDS.Contains(r)) != null;
        }
        /// <summary>
        /// 角色状态默认为启用，角色编号：自动生成，生成规则：以5位数字堆积顺序显示，从00001开始生成
        /// </summary>
        /// <param name="addRole_In"></param>
        public void AddRole(AddRole_In addRole_In)
        {
            Db_sys_role sysRole = addRole_In.MappingTo<Db_sys_role>();
            //验证角色名称是否重复
            if (DbOpe_sys_role.Instance.CheckRoleName(sysRole.RoleName))
                throw new ApiException("角色名称已存在");
            //生成主键
            sysRole.Id = Guid.NewGuid().ToString();
            //生成角色编号
            sysRole.RoleNum = (DbOpe_sys_role.Instance.GetQueryCount(sysRole) + 1).ToString().PadLeft(5, '0');
            //配置其他参数
            sysRole.Deleted = false;
            sysRole.CreateUser = UserTokenInfo.id;
            sysRole.CreateDate = DateTime.Now;
            sysRole.IsProduct = true;
            //执行添加，把sql添加到执行队列中
            DbOpe_sys_role.Instance.InsertQueue(sysRole);
            //绑定FormId
            InsertSysPermission(addRole_In.FormIds.Split(',').ToList(), sysRole.Id);
            DbOpe_sys_role.Instance.SaveQueues();
        }

        /// <summary>
        /// 修改角色信息
        /// </summary>
        /// <param name="updRoleIn"></param>
        public void UpdateRole(UpdateRole_In updRoleIn)
        {
            /*//验证员工状态是否为停用
            if (DbOpe_sys_role.Instance.IsRoleEnable(updRoleIn.Id))
                throw new ApiException("当前角色为启用状态，无法修改");*/
            var curRole = DbOpe_sys_role.Instance.GetRoleBasicInfoById(updRoleIn.Id);
            Db_sys_role sysRole = updRoleIn.MappingTo(curRole);
            if (DbOpe_sys_role.Instance.CheckRoleName(sysRole.RoleName, sysRole.Id))
                throw new ApiException("角色名称已存在");
            //配置其他参数
            sysRole.UpdateUser = UserTokenInfo.id;  
            sysRole.UpdateDate = DateTime.Now;
            //执行添加，把sql添加到执行队列中
            DbOpe_sys_role.Instance.UpdateQueue(sysRole);
            //配置对应的List<sys_permission>
            ////查询当前关联的FormIds
            var hisFormList = DbOpe_sys_permission.Instance.GetFormIdsByRoleId(sysRole.Id);
            ////获取要修改的关联关系
            var curFormList = GetAllFormIds(updRoleIn.FormIds.Split(',').ToList());
            ////计算 hisFormList 和 curFormList 的交集
            var interFormList = hisFormList.Intersect(curFormList).ToList();
            ////计算 hisFormList 和 interFormList 的差集，记为待删除集合
            var toDelFormList = hisFormList.Except(interFormList).ToList();
            toDelFormList.Remove("9c8a7827-c945-44d4-9808-724452dd0017");
            ////计算 curFormList 和 interFormList 的差集，记为待增加集合
            var toAddFormList = curFormList.Except(interFormList).ToList();
            //删除现有的Form绑定关系
            DbOpe_sys_permission.Instance.DeletePermissionQueue(toDelFormList, updRoleIn.Id, UserTokenInfo.id);
            //绑定FormId
            InsertSysPermission(toAddFormList, sysRole.Id);
            DbOpe_sys_role.Instance.SaveQueues();
        }

        /*private void InsertSysPermission(List<string> formIds, string roleId)
        {

            formIds.ForEach(formId =>
            {
                Db_sys_permission sysPer = new Db_sys_permission();
                sysPer.Id = Guid.NewGuid().ToString();
                sysPer.RoleId = roleId;
                sysPer.FormId = formId;
                sysPer.Deleted = false;
                sysPer.CreateUser = UserTokenInfo.id;
                sysPer.CreateDate = DateTime.Now;
                //执行添加，把sql添加到执行队列中
                DbOpe_sys_permission.Instance.InsertQueue(sysPer);
            });
        }*/


        /// <summary>
        /// Role绑定FormId
        /// </summary>
        /// <param name="formIds"></param>
        /// <param name="roleId"></param>
        private void InsertSysPermission(List<string> formIds, string roleId)
        {
            var sysPerList = new List<Db_sys_permission>();
            formIds.ForEach(formId =>
            {
                Db_sys_permission sysPer = new Db_sys_permission();
                sysPer.RoleId = roleId;
                sysPer.FormId = formId;
                sysPerList.Add(sysPer);
                //查找当前formId的下级且type=5的form
                var subFormIds = DbOpe_sys_form.Instance.GetSubSysForm(formId);
                subFormIds.ForEach(subFormId =>
                {
                    Db_sys_permission sysSubPer = new Db_sys_permission();
                    sysSubPer.RoleId = roleId;
                    sysSubPer.FormId = subFormId;
                    sysPerList.Add(sysSubPer);
                });
            });

            var loginFunctionFormIds = DbOpe_sys_form.Instance.GetSubSysForm(Guid.Empty.ToString());
            loginFunctionFormIds.ForEach(formId =>
            {
                Db_sys_permission sysPer = new Db_sys_permission();
                sysPer.RoleId = roleId;
                sysPer.FormId = formId;
                sysPerList.Add(sysPer);
            });
            DbOpe_sys_permission.Instance.InsertListDataQueue(sysPerList);
        }

        /// <summary>
        /// 根据传入的formId列表，获取包含type=5的form，整理为要绑定的formIds
        /// </summary>
        /// <param name="formIds"></param>
        /// <returns></returns>
        private List<string> GetAllFormIds(List<string> formIds)
        {
            var formIdsList = new List<string>();
            formIds.ForEach(formId =>
            {
                formIdsList.Add(formId);
                //查找当前formId的下级且type=5的form
                var subFormIds = DbOpe_sys_form.Instance.GetSubSysForm(formId);
                formIdsList.AddRange(subFormIds);
            });
            var loginFunctionFormIds = DbOpe_sys_form.Instance.GetSubSysForm(Guid.Empty.ToString());
            formIdsList.AddRange(loginFunctionFormIds);
            return formIdsList;
        }


        /// <summary>
        /// 删除角色信息，多条记录全部执行成功则返回成功，否则返回失败。只做逻辑删除，修改Deleted字段为true
        /// </summary>
        /// <param name="Ids"></param>
        /// <exception cref="ApiException"></exception>
        public void DeleteRole(string Ids)
        {
            //验证传入的Ids是否为空
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("角色主键不可为空");
            var idList = Ids.Split(",").ToList();
            //验证是否存在启用的角色
            if (DbOpe_sys_role.Instance.HasEnableRole(idList))
                throw new ApiException("所选角色中存在启用的角色，无法删除");
            //执行删除，添加到sql队列中
            DbOpe_sys_role.Instance.DeleteRole(idList);
            //删除userinrole的关联关系
            DbOpe_sys_userinrole.Instance.DelUserInRoleByRoleIdsQueue(idList, UserTokenInfo.id);
            //删除permission的关联关系
            DbOpe_sys_permission.Instance.DeletePermissionByRoleIdsQueue(idList, UserTokenInfo.id);
            //执行队列中sql
            DbOpe_sys_role.Instance.SaveQueues();
        }

        /// <summary>
        /// 修改角色状态信息，变更角色状态为启用、停用，多条记录全部执行成功则返回成功，否则返回失败。
        /// </summary>
        /// <param name="updRoleStaIn"></param>
        /// <exception cref="ApiException"></exception>
        public void UpdateRoleStatus(UpdateRoleStatusIn updRoleStaIn)
        {
            //验证传入的Ids是否为空
            if (string.IsNullOrEmpty(updRoleStaIn.Ids))
                throw new ApiException("角色主键不可为空");
            //转换Ids为list
            var roleIds = updRoleStaIn.Ids.Split(",").ToList();

            if (updRoleStaIn.status)
            {//停用变启用 
                //验证传入的角色列表中是否有已启用的角色
                if (DbOpe_sys_role.Instance.HasEnableRole(roleIds))
                    throw new ApiException("所选角色中存在已启用的角色，无法启用");
            }
            else
            {//启用变停用
                //验证传入的角色列表中是否有已停用的角色
                if (DbOpe_sys_role.Instance.HasStoppedRole(roleIds))
                    throw new ApiException("所选角色中存在已停用的角色，无法停用");
                //获取传入的角色列表中 绑定员工信息列表
                var bindingUserList = DbOpe_sys_role.Instance.GetBindingUser(roleIds);
                //若绑定员工中存在启用的员工，不能停用；不存在启用的员工，将停用的员工删除关联关系
                if (bindingUserList.Any(e => e.UserStatus == true))
                    throw new ApiException("所选角色已绑定了启用状态的人员，无法停用");
                else
                {
                    roleIds.ForEach(roleId =>
                    {
                        DbOpe_sys_userinrole.Instance.DelUserInRoleByUserIdsQueue(roleId, bindingUserList.Select(e => e.Id).ToList(), UserId);
                    });

                }
            }
            //执行启用停用更新
            DbOpe_sys_role.Instance.UpdateRoleStatus(roleIds, updRoleStaIn.status, UserTokenInfo.id);
            DbOpe_sys_userinrole.Instance.SaveQueues();


        }

    }
}
