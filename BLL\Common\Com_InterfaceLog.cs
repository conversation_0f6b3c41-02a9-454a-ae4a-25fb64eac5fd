﻿using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Log;
using CRM2_API.DAL.DbModelOpe.Log;

namespace CRM2_API.BLL.Common
{
    public class Com_InterfaceLog
    {
        private static readonly AsyncLocal<Db_sys_api_log> _workLog = new();

        public static Db_sys_api_log Instance
        {
            get { return _workLog.Value ?? new(); }
        }

        public static void SetWorkLog(Db_sys_api_log log)
        {
            _workLog.Value = log;
            DbOpe_sys_api_log.Instance.Insert(log);
        }
    }
}
