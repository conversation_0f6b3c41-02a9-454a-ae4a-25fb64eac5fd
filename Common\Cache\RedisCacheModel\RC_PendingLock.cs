﻿using CRM2_API.Common.Filter;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BLLModel.Enum;

namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class PendingLock
        {
            const int AutoUnLockMinutes = 5;
            const int DelayRemainMinutes = 1440;
            const string PendingLockTitle = "PendingLock:";
            const string PendingDelayTitle = "PendingDelay:";
            public static bool CheckLock(EnumWorkFlow workFlow, string dataId)
            {
                var key = PendingLockTitle + workFlow + ":" + dataId;
                if (RedisHelper.Exists(key))
                    return true;
                else
                    return false;
            }
            public static void SetLock(EnumWorkFlow workFlow, string dataId,string userId)
            {
                var key = PendingLockTitle + workFlow + ":" + dataId;
                RedisHelper.Set(key, userId, TimeSpan.FromMinutes(AutoUnLockMinutes));
            }
            public static void ReleaseLock(EnumWorkFlow workFlow, string dataId, string userId)
            {
                var key = PendingLockTitle + workFlow + ":" + dataId;
                RedisHelper.Del(key);
            }
            public static List<LockData> GetLockDataIds(EnumWorkFlow workFlow,string userId)
            {
                var key = PendingLockTitle + workFlow + ":";
                var keys = RedisHelper.Keys(key+"*").ToList();
                List<LockData> r = new List<LockData>();
                keys.ForEach(k => {
                    var dataId = k.Replace(key, "");
                    var lockUser = RedisHelper.Get(k);
                    var self = userId == lockUser;
                    if (!self) {
                        r.Add(new LockData() { UserId = lockUser, DataId = dataId });
                    }
                });
                return r;
            }
            public static void SetDelay(EnumWorkFlow workFlow, string dataId, string userId)
            {
                var key = PendingDelayTitle + workFlow + ":" + userId + ":" + dataId;
                RedisHelper.Set(key, dataId, TimeSpan.FromMinutes(DelayRemainMinutes));
            }
            public static List<string> GetUserDelayDataIds(EnumWorkFlow workFlow, string userId)
            {
                var key = PendingDelayTitle + workFlow + ":" + userId + ":";
                var keys = RedisHelper.Keys(key + "*").ToList();
                List<string> r = new List<string>();
                keys.ForEach(k => {
                    var dataId = RedisHelper.Get(k);
                    r.Add(dataId);
                });
                return r;
            }
        }
        public class LockData {
            public string UserId { get; set; }
            public string DataId { get; set; }
        }
        
    }
}
