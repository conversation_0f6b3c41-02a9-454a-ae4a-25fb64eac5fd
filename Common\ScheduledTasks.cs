﻿using System.Diagnostics;
using System.Threading.Tasks;
using CRM2_API.BLL;
using CRM2_API.BLL.Common.Com_EmailHelper;
using CRM2_API.BLL.ComParam;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.BLL.PerformanceObjectives;
using CRM2_API.BLL.Promotion;
using CRM2_API.BLL.Schedule;
using CRM2_API.BLL.WeChat;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Promotion;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using Quartz;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static Lucene.Net.Util.Fst.Util;
using CRM2_API.Common.Cron;
using CRM2_API.Common.JWT;
using CRM2_API.Common.Cache;
using CRM2_API.BLL.WorkReportNew;

namespace CRM2_API.Common
{
    public class ScheduledTasks
    {
        public static readonly EnumUserPromotionType CLERK_ENUM = EnumUserPromotionType.ZY;    //职员对应的枚举
        public static readonly EnumUserPromotionType REGION_COMMISSIONER_ENUM = EnumUserPromotionType.QYZJ;  //区域总监职级描述枚举
        public static readonly string EVERY_DAY_CRON = "0 0 0 * * ?";  //每天0点
        public static readonly string EVERY_HOUR_CRON = "0 0 0/1 * * ?";  //每个整点


        public static void InitAllScheduledTasks()
        {

            //服务过期的定时任务
            CalculateServiceOverdue();
            //240806 合并客户签约状态刷新和客户过期释放和离职客户到期自动释放 因为这几个得有顺序，不能同时运行
            CustomerOverDue();
            //CustomerContractOverDue();
            ////客户过期释放
            //CustomerProtectCheck();
            ////离职客户到期自动释放
            //LeaveUserProtectCheck();


            //刷新客户私有池
            //CustomerSaveNumCheck();
            MaxCustomerRefresh();

            //计算员工职称的定时任务
            //CalculateUserPromotion();
            //邓白氏服务读取开通邮件的定时任务
            CalculateServiceDBReadEmail();
            //系统启动时跑一下系统设置一些表中的待生效任务，防止系统重启导致的任务失效
            TimingRefreshComParam();
            TimingRefreshPerformanceObjectives();
            TimingRefreshPromotion();
            //添加定时刷新Ip备案状态
            TimingRefreshIpkeeprecordState();
            //添加定时刷新日程计划的状态
            TimingRefreshScheduleState();
            //服务即将到期的定时任务
            CalculateServiceDue();
            //环球搜码状态更新定时任务
            CalculateGlobalSearchUserState();
            //自动作废合同信息
            AutoVoidContract();
            //合同条款到期刷新
            ContractTermOverDueCheck();

            //优惠券刷新
            CouponRefresh();

            //消息中心添加即将到期相关消息（客户 服务）
            AddMessageCenter();

            SendTodoMessage();

            


            //月初更新返还
            UpdateGiveBackDetail();

            ////绑定用户微信openid和unionid
            //BindWeChart();
            //绑定用户微信自定义菜单
            if (AppSettings.BindWeChatMenu)
            {
                BindWeChartMenu();
            }

            // 检查并修复未经过预处理的公司数据
            CheckAndFixUnprocessedCompanies();

            // 初始化刷新缓存的定时任务
            RefreshRedisCache();
            
            // 初始化Redis缓存监控
            InitializeCacheMonitor();

            // 节假日数据自动同步
            AutoSyncHolidayData();

            // SaleWits定时下发任务
            SaleWitsScheduledDistribution();

        }

        /// <summary>
        /// 定时刷新Redis缓存
        /// </summary>
        public static void RefreshRedisCache()
        {
            bool isDebugger = false;
#if DEBUG
            isDebugger = true;
#endif
            if (AppSettings.Env == Enum_SystemSettingEnv.Debug)
            {
                isDebugger = true;
            }

            string jobName = $"RefreshRedisCache";
            // 在开发环境下更频繁地刷新，生产环境每天凌晨2点刷新一次
            string cronExpression = isDebugger ? "0 0 */3 * * ?" : "0 0 2 * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                try
                {
                    RedisCache.CacheInit.InitAllCache();
                }
                catch (Exception ex)
                {
                    // 记录错误日志
                    LogUtil.AddLog("定时任务刷新Redis缓存失败", ex);
                }
            }, cronExpression);
        }

        /// <summary>
        /// 初始化Redis缓存监控
        /// </summary>
        public static void InitializeCacheMonitor()
        {
            try
            {
                RedisCache.CacheMonitor.InitializeCacheMonitor();
                LogUtil.AddLog("Redis缓存监控初始化完成");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"初始化Redis缓存监控失败: {ex.Message}");
            }
        }

        private static void UpdateGiveBackDetail()
        {
            bool isDebugger = false;
#if DEBUG
            isDebugger = true;
#endif
            if (AppSettings.Env == Enum_SystemSettingEnv.Debug)
            {
                isDebugger = true;
            }

            string jobName = $"UpdateGiveBackDetail";
            string cronExpression = isDebugger ? "0 0 */1 * * ?" : "0 58 23 L * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                BLL_PrivateService.Instance.UpdateGetBackDetail();
            }, cronExpression);
        }

        private static void AddMessageCenter()
        {
            bool isDebugger = false;
#if DEBUG
            isDebugger = true;
#endif
            if (AppSettings.Env == Enum_SystemSettingEnv.Debug)
            {
                isDebugger = true;
            }

            string jobName = $"AddMessageCenter";
            string cronExpression = isDebugger ? "0 0 0 * * ?" : "0 0 0 * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                BLL_MessageCenter.Instance.AddMessageCenter();
                DbOpe_sys_message_ignore.Instance.SetIgnoreAntiEffective();
                BLL_TradeLeads.Instance.SendIncomingExpiredMessgage();
            }, cronExpression);
        }

        private static void SendTodoMessage()
        {
            bool isDebugger = false;
#if DEBUG
            isDebugger = true;
#endif
            if (AppSettings.Env == Enum_SystemSettingEnv.Debug)
            {
                isDebugger = true;
            }

            string jobName = $"SendTodoMessage";
            string cronExpression = isDebugger ? "0 0 7 * * ?" : "0 0 7 * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                BLL_MessageCenter.Instance.ScheduleSendGZHMessage();
                DbOpe_sys_message_ignore.Instance.SetIgnoreAntiEffective();
            }, cronExpression);
        }

       

        private static void CouponRefresh()
        {
            bool isDebugger = false;
#if DEBUG
            isDebugger = true;
#endif
            if (AppSettings.Env == Enum_SystemSettingEnv.Debug)
            {
                isDebugger = true;
            }

            string jobName = $"CouponRefresh";
            string cronExpression = isDebugger ? "0 0 */1 * * ?" : "0 0 0 * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                BLL_Coupon.Instance.RefreshCoupanDeadline();
            }, cronExpression);
        }

        /// <summary>
        /// 自动作废合同信息
        /// </summary>
        public static void AutoVoidContract()
        {
            string jobName = $"AutoVoidContract";
            string cronExpression = "0 0 0 * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                BLL_Contract.Instance.AutoVoidContract();
            }, cronExpression);
        }

        /// <summary>
        /// 绑定用户微信openid和unionid
        /// </summary>
        public static void BindWeChart()
        {
            string jobName = $"BindWeChart";
            // DEBUG环境下每10秒执行一次，生产环境下每分钟执行一次
            string cronExpression = AppSettings.Env != Enum_SystemSettingEnv.Product ? "0/10 * * * * ?" : "0 0/1 * * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                DbOpe_sys_user.Instance.TransDeal(() =>
                {
                    List<Db_sys_weixin_user_add_menu> list = DbOpe_sys_weixin_user_add_menu.Instance.GetDataList(r => r.BindState == 1);
                    foreach (Db_sys_weixin_user_add_menu obj in list)
                    {
                        DbOpe_sys_user.Instance.BindWeChat(obj.UserId, obj.UnionId, obj.OpenId, obj.NickName);
                        DbOpe_sys_weixin_user_add_menu.Instance.UpdateData(r => new Db_sys_weixin_user_add_menu { BindState = 2 }, obj.Id);
                    }
                });
            }, cronExpression);
        }

        /// <summary>
        /// 绑定用户微信自定义菜单
        /// </summary>
        public static void BindWeChartMenu()
        {
            string jobName = $"BindWeChart";
            string cronExpression = "0 0/2 * * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                DbOpe_sys_user.Instance.TransDeal(() =>
                {
                    List<Db_sys_weixin_user_add_menu> list = DbOpe_sys_weixin_user_add_menu.Instance.GetDataList(r => r.State == 1).Take(40).ToList();
                    List<string> openid = list.Select(x => x.OpenId).ToList();
                    if (openid.Count > 0)
                    {
                        string errmsg = BLL_WeiXinGZHelper.Instance.Batchtagging(openid, 100);
                        LogUtil.AddLog("BindWeChartMenu 执行" + errmsg);
                    }
                    foreach (Db_sys_weixin_user_add_menu obj in list)
                    {
                        DbOpe_sys_weixin_user_add_menu.Instance.UpdateData(r => new Db_sys_weixin_user_add_menu { State = 2 }, obj.Id);
                    }
                });
            }, cronExpression);
        }

        /// <summary>
        /// 服务即将到期的定时任务
        /// </summary>
        public static void CalculateServiceDue()
        {
            string jobName = $"calculateServiceDue";
            string cronExpression = "0 10 0 * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                //获取即将到期的合同
                List<ContractDueReminder_Out> contractList = DbOpe_crm_contract.Instance.GetDueContractList();
                foreach (ContractDueReminder_Out contractDueReminder in contractList)
                {
                    DbOpe_sys_messages.Instance.AddContractServiceinfoDue(contractDueReminder.Issuer, contractDueReminder.FirstPartyName);
                }
            }, cronExpression);
        }

        /// <summary>
        /// 计算员工职称的定时任务
        /// </summary>
        public static void CalculateUserPromotion()
        {
            bool isDebugger = false;
#if DEBUG
            isDebugger = true;
#endif
            if (AppSettings.Env == Enum_SystemSettingEnv.Debug)
            {
                isDebugger = true;
            }

            string jobName = $"calculateUserPromotion";
            string cronExpression = isDebugger ? "0 0 */1 * * ?" : "0 58 23 L * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                //时间值
                DateTime now = DateTime.Now;

                TimeSpan dayStartTime = new(0, 0, 0);
                TimeSpan dayEndTime = new(23, 59, 59);

                List<UserSalesAchivementAmount> accumulateUserSalesAchivementAmountList = BLL_ContractAchievement.Instance.GetUserSalesAchivementAmountList(new DateSpanParams(), null); //员工的累计业绩
                List<UserSalesAchivementAmount> quarterUserSalesAchivementAmountList = new();    //员工的季度业绩
                List<UserSalesAchivementAmount> halfYearUserSalesAchivementAmountList = new();    //员工的半年业绩
                List<UserSalesAchivementAmount> yearUserSalesAchivementAmountList = new();   //员工的年度业绩

                //工号处理部分 优先于其他部分
                foreach (var item in accumulateUserSalesAchivementAmountList)
                {
                    string needJobNum = item.UserId;
                    DbOpe_sys_user.Instance.CheckAndInsertJobNum(needJobNum);
                }

                List<PromotionInfoPlus> allPromotions = DbOpe_sys_promotion.Instance.GetAllPromotionWithDesc(1); //系统生效的晋升信息
                List<PromotionInfoPlus> brigadePromotions = allPromotions.Where(w => w.Type == EnumPromotionType.BrigadeLeader).ToList();  //大队领导晋升条件
                List<PromotionInfoPlus> squadronPromotions = allPromotions.Where(w => w.Type == EnumPromotionType.SquadronLeader).ToList();  //中队领导晋升条件
                List<PromotionInfoPlus> workersPromotions = allPromotions.Where(w => w.Type == EnumPromotionType.Worker).ToList();  //职工晋升条件

                var userPromotionInfoList = DbOpe_crm_user_promotion.Instance.GetUserPromotionInfo();    //用户信息

                var allUserPromotionDescribeList = DbOpe_crm_user_promotion_describe.Instance.GetUserPromotionDescribeArrayList(); //所有的职级描述信息
                var allUserPromotionDescribeDict = allUserPromotionDescribeList.ToDictionary(k => k.RankCode, v => v);    //所有的职级描述字段
                var regionCommissioner = allUserPromotionDescribeDict[REGION_COMMISSIONER_ENUM.ToString()];//获取区域总监职级描述信息

                List<Db_crm_user_promotion> insertUserPromotionList = new();  //最终保存的荣誉
                Db_crm_user_promotion temp = null;  //临时实体

                // 如果是 3、6、9、12月份，则计算员工的季度业绩
                if (now is { Month: 3 or 6 or 9 or 12 } || isDebugger)
                {
                    DateTime startQuarter = now.AddMonths(0 - (now.Month - 1) % 3).AddDays(1 - now.Day).Date + dayStartTime; // 本季度初
                    DateTime endQuarter = startQuarter.AddMonths(3).AddDays(-1).Date + dayEndTime; // 本季度末
                    DateSpanParams dateSpanParams = new DateSpanParams()
                    {
                        DateStart = startQuarter,
                        DateEnd = endQuarter,
                    };
                    quarterUserSalesAchivementAmountList = BLL_ContractAchievement.Instance.GetUserSalesAchivementAmountList(dateSpanParams, null); //员工的季度业绩
                }
                // 如果是6月份，则计算员工的半年度业绩
                if (now.Month == 6 || isDebugger)
                {
                    DateSpanParams dateSpanParams = new DateSpanParams()
                    {
                        DateStart = new DateTime(now.Year, 1, 1, 0, 0, 0),
                        DateEnd = new DateTime(now.Year, 6, 30, 23, 59, 59),
                    };
                    halfYearUserSalesAchivementAmountList = BLL_ContractAchievement.Instance.GetUserSalesAchivementAmountList(dateSpanParams, null); //员工的半年度业绩
                }

                // 如果是12月份，则计算员工的年度业绩
                if (now.Month == 12 || isDebugger)
                {
                    DateSpanParams dateSpanParams = new DateSpanParams()
                    {
                        DateStart = new DateTime(now.Year, 1, 1, 0, 0, 0),
                        DateEnd = new DateTime(now.Year, 12, 31, 23, 59, 59),
                    };
                    yearUserSalesAchivementAmountList = BLL_ContractAchievement.Instance.GetUserSalesAchivementAmountList(dateSpanParams, null); //员工的年度业绩
                }

                Dictionary<string, UserSalesAchivementAmount> accumulateUserSalesAchivementAmountDict =
                    accumulateUserSalesAchivementAmountList.ToDictionary(k => k.UserId, v => v);    //累计业绩字典

                Dictionary<string, UserSalesAchivementAmount> quarterUserSalesAchivementAmountDict =
                    quarterUserSalesAchivementAmountList.ToDictionary(k => k.UserId, v => v); //员工的季度字典

                Dictionary<string, UserSalesAchivementAmount> halfYearUserSalesAchivementAmountDict =
                    halfYearUserSalesAchivementAmountList.ToDictionary(k => k.UserId, v => v);   //员工的半年度字典

                Dictionary<string, UserSalesAchivementAmount> yearUserSalesAchivementAmountDict =
                    yearUserSalesAchivementAmountList.ToDictionary(k => k.UserId, v => v);  //员工的年度字典

                // 遍历所有用户
                foreach (UserPromotionInfo userPromotionInfo in userPromotionInfoList)
                {
                    //如果员工没有累计业绩则跳过, 累计业绩没有则不会存在任何业绩，该用户不会有晋升的条件
                    if (!accumulateUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                    {
                        continue;
                    }
                    //获取用户当前职称
                    Db_crm_user_promotion_describe currentUserPromotion = null;
                    //如果客户没有职称则初始化默认值[注：领导一般是有初始值的，没有则为用户创建时未添加]
                    if (string.IsNullOrEmpty(userPromotionInfo.PromotionDescribeId) || !allUserPromotionDescribeDict.ContainsKey(userPromotionInfo.RankCode))
                    {
                        if (userPromotionInfo.UserType == 1)
                        {
                            currentUserPromotion = allUserPromotionDescribeDict[REGION_COMMISSIONER_ENUM.ToString()]; //管理者初始化为区域总监的ID
                            //if (userPromotionInfo.OrganizationId is null)
                            //    currentUserPromotion = allUserPromotionDescribeDict[REGION_BEIJING_ENUM.ToString()];
                        }
                        else if (userPromotionInfo.UserType == 2)
                        {
                            currentUserPromotion = allUserPromotionDescribeDict[CLERK_ENUM.ToString()]; //职工初始化为普通职工 -> 实习生
                        }
                    }
                    else
                    {
                        // 员工有职称则赋值为用户的当前职称
                        currentUserPromotion = allUserPromotionDescribeDict[userPromotionInfo.RankCode];
                    }
                    //获取员工可以晋级的职称列表
                    List<PromotionInfoPlus> canUpgradePromotions = null;
                    //若员工的职级大于等于区域总监
                    if (currentUserPromotion.RankSortCode >= regionCommissioner.RankSortCode)
                    {
                        int currentUserPromotionIndex = allUserPromotionDescribeList.IndexOf(currentUserPromotion); //用户当前职级所在索引
                                                                                                                    // 用户当前职称所在的索引 + 1 超出了所有的职称信息之和，则代表已经升级到了最高的职称，因此跳过
                        if ((currentUserPromotionIndex + 1) >= allUserPromotionDescribeList.Count)  //已经升级到头了
                        {
                            currentUserPromotionIndex = currentUserPromotionIndex - 1;
                            continue;
                        }

                        // 因为区域总监可以跳级，此处固定晋升到下一级作废
                        /*var nextUserPromotion = allUserPromotionDescribeList[(currentUserPromotionIndex + 1)];
                        if (userPromotionInfo.UserType == 1)
                        {
                            if (userPromotionInfo is { OrgType: EnumOrgType.BattleTeam or EnumOrgType.Battalion })     //适用于大队领导晋升规则
                            {
                                canUpgradePromotions = brigadePromotions.Where(w => w.PromotionEndKey.Equals(nextUserPromotion.Id)).ToList();
                            }
                            else if (userPromotionInfo is { OrgType: EnumOrgType.Squadron })      //适用于中队领导晋升规则
                            {
                                canUpgradePromotions = squadronPromotions.Where(w => w.PromotionEndKey.Equals(nextUserPromotion.Id)).ToList();
                            }
                        }
                        else if (userPromotionInfo.UserType == 2)
                        {
                            canUpgradePromotions = workersPromotions.Where(w => w.PromotionEndKey.Equals(nextUserPromotion.Id)).ToList();
                        }*/

                        //区域总监之后的可以跳级，拿到可以跳级到的职位
                        int nextUserPromotionIndex = currentUserPromotionIndex + 1; //当前职称的索引加1
                                                                                    // 以当前职称的索引加1为开始拿到之后的所有职称，这些都是可以跳级到的
                        var canUpgradePromotionDescribeList = allUserPromotionDescribeList.GetRange(nextUserPromotionIndex,
                            allUserPromotionDescribeList.Count - nextUserPromotionIndex).Select(s => s.Id).ToList();
                        //如果用户类型是管理员
                        if (userPromotionInfo.UserType == 1)
                        {
                            // 判断用户的组织机构类型,战队和大队 则适用于大队领导晋升规则
                            if (userPromotionInfo is { OrgType: EnumOrgType.BattleTeam or EnumOrgType.Battalion })     //适用于大队领导晋升规则
                            {
                                canUpgradePromotions = brigadePromotions.Where(w => canUpgradePromotionDescribeList.Contains(w.PromotionEndKey)).ToList();
                            }
                            // 中队 则适用于中队领导晋升规则
                            else if (userPromotionInfo is { OrgType: EnumOrgType.Squadron })      //适用于中队领导晋升规则
                            {
                                canUpgradePromotions = squadronPromotions.Where(w => canUpgradePromotionDescribeList.Contains(w.PromotionEndKey)).ToList();
                            }
                        }
                        // 如果用户类型类型是职员
                        else if (userPromotionInfo.UserType == 2)
                        {
                            // 则适用于职员晋升规则
                            canUpgradePromotions = workersPromotions.Where(w => canUpgradePromotionDescribeList.Contains(w.PromotionEndKey)).ToList();
                        }
                    }
                    // 如果用户当前职称在区域总监之下，目前不存在跳级的可能性
                    // 如果用户的职称在区域总监之下，则只适用于职员晋升规则，因为管理员一般初始化就是区域总监
                    else
                    {
                        // 以用户当前职称的Id作为开始,查询可以晋升到的职称列表
                        canUpgradePromotions = workersPromotions.Where(w => w.PromotionStartKey.Equals(currentUserPromotion.Id)).ToList();
                    }

                    // 如果可以晋升到的职称列表不为空
                    if (canUpgradePromotions is not null and { Count: > 0 })
                    {
                        canUpgradePromotions.Reverse(); //反过来一下,能升级到大的肯定要升级到大的
                        foreach (PromotionInfoPlus promotionInfo in canUpgradePromotions)
                        {
                            // 下面则根据条件去判断，注意最多的时候会有两个条件。
                            if (promotionInfo.PromotionConditionType1 == EnumPromotionConditionType.GrandTotal)
                            {
                                var accumulate = accumulateUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue1 * 10000)
                                {
                                    if (promotionInfo.PromotionConditionType2 != null)
                                    {
                                        if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.GrandTotal)
                                        {
                                            if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                            {
                                                //通过，需要晋升,下个月生效
                                                temp = new()
                                                {
                                                    Id = Guid.NewGuid().ToString(),
                                                    UserId = userPromotionInfo.Id,
                                                    PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                    PromotionTime = DateTime.Now,
                                                    TakingEffectTime = isDebugger ? DateTime.Now : new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                    Deleted = false,
                                                    CreateUser = null,
                                                    CreateDate = DateTime.Now,
                                                };
                                                insertUserPromotionList.Add(temp);
                                                break;
                                            }
                                        }
                                        else if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.Annual)
                                        {
                                            if (DateTime.Now.Month == 6 || isDebugger)
                                            {
                                                if (halfYearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                {
                                                    var halfYear = halfYearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                    if (halfYear.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = DateTime.Now,
                                                            TakingEffectTime = isDebugger ? DateTime.Now : new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = DateTime.Now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }

                                            }
                                            if (DateTime.Now.Month == 12 || isDebugger)
                                            {
                                                if (yearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                {
                                                    var year = yearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                    if (year.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = DateTime.Now,
                                                            TakingEffectTime = isDebugger ? DateTime.Now : new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = DateTime.Now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        else if (promotionInfo.PromotionConditionType2 is EnumPromotionConditionType.Quarter && (DateTime.Now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                                        {
                                            if (quarterUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                            {
                                                var quarter = quarterUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                {
                                                    //通过，需要晋升,下个月生效
                                                    temp = new()
                                                    {
                                                        Id = Guid.NewGuid().ToString(),
                                                        UserId = userPromotionInfo.Id,
                                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                        PromotionTime = DateTime.Now,
                                                        TakingEffectTime = isDebugger ? DateTime.Now : new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                        Deleted = false,
                                                        CreateUser = null,
                                                        CreateDate = DateTime.Now,
                                                    };
                                                    insertUserPromotionList.Add(temp);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        //通过，需要晋升,下个月生效
                                        temp = new()
                                        {
                                            Id = Guid.NewGuid().ToString(),
                                            UserId = userPromotionInfo.Id,
                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                            PromotionTime = DateTime.Now,
                                            TakingEffectTime = isDebugger ? DateTime.Now : new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                            Deleted = false,
                                            CreateUser = null,
                                            CreateDate = DateTime.Now,
                                        };
                                        insertUserPromotionList.Add(temp);
                                        break;
                                    }
                                }
                            }
                            else if (promotionInfo.PromotionConditionType1 == EnumPromotionConditionType.Annual)
                            {
                                //看看有没有提前晋升的机会
                                if (DateTime.Now.Month == 6 || isDebugger)
                                {
                                    if (halfYearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                    {
                                        var halfYear = halfYearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                        if (halfYear.AchivementAmount >= promotionInfo.PromotionConditionValue1 * 10000)
                                        {
                                            if (promotionInfo.PromotionConditionType2 != null)
                                            {
                                                if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.GrandTotal)
                                                {
                                                    var accumulate = accumulateUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                    if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = DateTime.Now,
                                                            TakingEffectTime = isDebugger ? DateTime.Now : new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = DateTime.Now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }
                                                else if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.Annual)
                                                {
                                                    if (halfYear.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = DateTime.Now,
                                                            TakingEffectTime = isDebugger ? DateTime.Now : new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = DateTime.Now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }
                                                else if (promotionInfo.PromotionConditionType2 is EnumPromotionConditionType.Quarter && (DateTime.Now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                                                {
                                                    if (quarterUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                    {
                                                        var quarter = quarterUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                        if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                        {
                                                            //通过，需要晋升,下个月生效
                                                            temp = new()
                                                            {
                                                                Id = Guid.NewGuid().ToString(),
                                                                UserId = userPromotionInfo.Id,
                                                                PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                                PromotionTime = DateTime.Now,
                                                                TakingEffectTime = isDebugger ? DateTime.Now : new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                                Deleted = false,
                                                                CreateUser = null,
                                                                CreateDate = DateTime.Now,
                                                            };
                                                            insertUserPromotionList.Add(temp);
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                //通过，需要晋升,下个月生效
                                                temp = new()
                                                {
                                                    Id = Guid.NewGuid().ToString(),
                                                    UserId = userPromotionInfo.Id,
                                                    PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                    PromotionTime = DateTime.Now,
                                                    TakingEffectTime = isDebugger ? DateTime.Now :
                                                         new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                    Deleted = false,
                                                    CreateUser = null,
                                                    CreateDate = DateTime.Now,
                                                };
                                                insertUserPromotionList.Add(temp);
                                                break;
                                            }
                                        }
                                    }
                                }
                                if (DateTime.Now.Month == 12 || isDebugger)
                                {
                                    if (yearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                    {
                                        var year = yearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                        if (year.AchivementAmount >= promotionInfo.PromotionConditionValue1 * 10000)
                                        {
                                            if (promotionInfo.PromotionConditionType2 != null)
                                            {
                                                if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.GrandTotal)
                                                {
                                                    var accumulate = accumulateUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                    if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = DateTime.Now,
                                                            TakingEffectTime = isDebugger ? DateTime.Now :
                                                                 new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = DateTime.Now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }
                                                else if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.Annual)
                                                {
                                                    if (year.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                    {
                                                        //通过，需要晋升,下个月生效
                                                        temp = new()
                                                        {
                                                            Id = Guid.NewGuid().ToString(),
                                                            UserId = userPromotionInfo.Id,
                                                            PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                            PromotionTime = DateTime.Now,
                                                            TakingEffectTime = isDebugger ? DateTime.Now :
                                                                 new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                            Deleted = false,
                                                            CreateUser = null,
                                                            CreateDate = DateTime.Now,
                                                        };
                                                        insertUserPromotionList.Add(temp);
                                                        break;
                                                    }
                                                }
                                                else if (promotionInfo.PromotionConditionType2 is EnumPromotionConditionType.Quarter && (DateTime.Now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                                                {
                                                    if (quarterUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                    {
                                                        var quarter = quarterUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                        if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                        {
                                                            //通过，需要晋升,下个月生效
                                                            temp = new()
                                                            {
                                                                Id = Guid.NewGuid().ToString(),
                                                                UserId = userPromotionInfo.Id,
                                                                PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                                PromotionTime = DateTime.Now,
                                                                TakingEffectTime = isDebugger ? DateTime.Now :
                                                                     new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                                Deleted = false,
                                                                CreateUser = null,
                                                                CreateDate = DateTime.Now,
                                                            };
                                                            insertUserPromotionList.Add(temp);
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                //通过，需要晋升,下个月生效
                                                temp = new()
                                                {
                                                    Id = Guid.NewGuid().ToString(),
                                                    UserId = userPromotionInfo.Id,
                                                    PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                    PromotionTime = DateTime.Now,
                                                    TakingEffectTime = isDebugger ? DateTime.Now :
                                                         new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                    Deleted = false,
                                                    CreateUser = null,
                                                    CreateDate = DateTime.Now,
                                                };
                                                insertUserPromotionList.Add(temp);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            else if (promotionInfo.PromotionConditionType1 is EnumPromotionConditionType.Quarter && (DateTime.Now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                            {
                                if (quarterUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                {
                                    var quarter = quarterUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                    if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue1 * 10000)
                                    {
                                        if (promotionInfo.PromotionConditionType2 != null)
                                        {
                                            if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.GrandTotal)
                                            {
                                                var accumulate = accumulateUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                if (accumulate.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                {
                                                    //通过，需要晋升,下个月生效
                                                    temp = new()
                                                    {
                                                        Id = Guid.NewGuid().ToString(),
                                                        UserId = userPromotionInfo.Id,
                                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                        PromotionTime = DateTime.Now,
                                                        TakingEffectTime = isDebugger ? DateTime.Now :
                                                             new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                        Deleted = false,
                                                        CreateUser = null,
                                                        CreateDate = DateTime.Now,
                                                    };
                                                    insertUserPromotionList.Add(temp);
                                                    break;
                                                }
                                            }
                                            else if (promotionInfo.PromotionConditionType2 == EnumPromotionConditionType.Annual)
                                            {
                                                if (DateTime.Now.Month == 6 || isDebugger)
                                                {
                                                    if (halfYearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                    {
                                                        var halfYear = halfYearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                        if (halfYear.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                        {
                                                            //通过，需要晋升,下个月生效
                                                            temp = new()
                                                            {
                                                                Id = Guid.NewGuid().ToString(),
                                                                UserId = userPromotionInfo.Id,
                                                                PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                                PromotionTime = DateTime.Now,
                                                                TakingEffectTime = isDebugger ? DateTime.Now :
                                                                     new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                                Deleted = false,
                                                                CreateUser = null,
                                                                CreateDate = DateTime.Now,
                                                            };
                                                            insertUserPromotionList.Add(temp);
                                                            break;
                                                        }
                                                    }

                                                }
                                                if (DateTime.Now.Month == 12 || isDebugger)
                                                {
                                                    if (yearUserSalesAchivementAmountDict.ContainsKey(userPromotionInfo.Id))
                                                    {
                                                        var year = yearUserSalesAchivementAmountDict[userPromotionInfo.Id];
                                                        if (year.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                        {
                                                            //通过，需要晋升,下个月生效
                                                            temp = new()
                                                            {
                                                                Id = Guid.NewGuid().ToString(),
                                                                UserId = userPromotionInfo.Id,
                                                                PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                                PromotionTime = DateTime.Now,
                                                                TakingEffectTime = isDebugger ? DateTime.Now :
                                                                     new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                                Deleted = false,
                                                                CreateUser = null,
                                                                CreateDate = DateTime.Now,
                                                            };
                                                            insertUserPromotionList.Add(temp);
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                            else if (promotionInfo.PromotionConditionType2 is EnumPromotionConditionType.Quarter && (DateTime.Now is { Month: 3 or 6 or 9 or 12 } || isDebugger))
                                            {
                                                if (quarter.AchivementAmount >= promotionInfo.PromotionConditionValue2 * 10000)
                                                {
                                                    //通过，需要晋升,下个月生效
                                                    temp = new()
                                                    {
                                                        Id = Guid.NewGuid().ToString(),
                                                        UserId = userPromotionInfo.Id,
                                                        PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                        PromotionTime = DateTime.Now,
                                                        TakingEffectTime = isDebugger ? DateTime.Now :
                                                             new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                        Deleted = false,
                                                        CreateUser = null,
                                                        CreateDate = DateTime.Now,
                                                    };
                                                    insertUserPromotionList.Add(temp);
                                                    break;
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //通过，需要晋升,下个月生效
                                            temp = new()
                                            {
                                                Id = Guid.NewGuid().ToString(),
                                                UserId = userPromotionInfo.Id,
                                                PromotionDescribeId = promotionInfo.PromotionEndKey,
                                                PromotionTime = DateTime.Now,
                                                TakingEffectTime = isDebugger ? DateTime.Now :
                                                     new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1),
                                                Deleted = false,
                                                CreateUser = null,
                                                CreateDate = DateTime.Now,
                                            };
                                            insertUserPromotionList.Add(temp);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (insertUserPromotionList is not null and { Count: > 0 })
                {
                    int result = DbOpe_crm_user_promotion.Instance.Insert(insertUserPromotionList);
                    Debug.WriteLine($"职工晋升{result}条执行完成!");
                }
                Debug.WriteLine($"{jobName}任务执行完成！ \t {DateTime.Now}");
            }, cronExpression);
        }


        /// <summary>
        /// 服务过期的定时任务
        /// </summary>
        public static void CalculateServiceOverdue()
        {
            string jobName = $"calculateServiceOverdue";
            string cronExpression = "0 0 0 * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                //处理过期的GTIS正式
                DbOpe_crm_contract_serviceinfo_gtis.Instance.CheckGtisServiceOutOfTime();
                //查找过期的GTIS临时
                //标记过期的D&B邓白氏
                List<string> DBOverDueServiceIds = DbOpe_crm_contract_serviceinfo_db.Instance.GetOverDueServiceIds();
                DbOpe_crm_contract_serviceinfo_db.Instance.OverDueContractService(DBOverDueServiceIds);
                //标记过期的环球搜
                List<string> GlobalSearchOverDueServiceIds = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetOverDueServiceIds();
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.OverDueContractService(GlobalSearchOverDueServiceIds);
                List<string> GlobalSearchOverDueServiceUserIds = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetOverDueServiceUserIds(GlobalSearchOverDueServiceIds);
                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.OverDueContractService(GlobalSearchOverDueServiceUserIds);
                //标记过期的慧思学院服务
                List<string> CollegeOverDueServiceIds = DbOpe_crm_contract_serviceinfo_college.Instance.GetOverDueServiceIds();
                DbOpe_crm_contract_serviceinfo_college.Instance.OverDueContractService(CollegeOverDueServiceIds);
                //停用过期的慧思学院服务对应的账号
                List<string> CollegeOverDueServiceContacts = DbOpe_crm_contract_serviceinfo_college_contacts.Instance.GetOverDueServiceContacts(CollegeOverDueServiceIds);
                DbOpe_crm_contract_serviceinfo_college_contacts.Instance.OverDueContractService(CollegeOverDueServiceContacts);
                //标记过期的其他数据
                List<string> OtherDataOverDueServiceIds = DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetOverDueServiceIds();
                DbOpe_crm_contract_serviceinfo_otherdata.Instance.OverDueContractService(OtherDataOverDueServiceIds);
            }, cronExpression);
        }

        /// <summary>
        /// 每日0点读取邮箱未读邮件，更新邓白氏开通信息
        /// </summary>
        public static void CalculateServiceDBReadEmail()
        {
            string jobName = $"calculateServiceDBReadEmail";
            string cronExpression = "0 0 0 * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                Com_EmailHelper.ReadDBEmail();
            }, cronExpression);
        }

        /// <summary>
        /// 系统公共参数的定时任务
        /// </summary>
        public static void TimingRefreshComParam()
        {
            BLL_ComParam.Instance.AddTimingRefreshRetainTask();
            //BLL_ComParam.Instance.AddTimingRefreshAchiTask();
            BLL_ComParam.Instance.AddTimingRefreshParamTask();
        }

        /// <summary>
        /// 系统设置-目标设置的定时任务
        /// </summary>
        public static void TimingRefreshPerformanceObjectives()
        {
            BLL_PerformanceObjectives.Instance.AddTimingRefreshPerformanceObjectivesTask();
        }

        /// <summary>
        /// 系统设置-晋升设置的定时任务
        /// </summary>
        public static void TimingRefreshPromotion()
        {
            BLL_Promotion.Instance.AddTimingRefreshPromotionTask();
        }

        /// <summary>
        /// 定时任务刷新Ip备案中超过过期时间的状态
        /// </summary>
        public static void TimingRefreshIpkeeprecordState()
        {
            var ipkeeprecordPassList = DbOpe_crm_ipkeeprecord.Instance.GetIpkeeprecordStatePassList();
            foreach (Db_crm_ipkeeprecord item in ipkeeprecordPassList)
            {
                BLL_ContractOverseasIPRecord.Instance.AddTimingRefreshIPKeepRecordTask(item);
            }
        }

        /// <summary>
        /// 刷新客户私有池
        /// </summary>
        public static void CustomerProtectCheck()
        {
            string jobName = $"customerProtectCheck";
            string cronExpression = AppSettings.Env != Enum_SystemSettingEnv.Product ? EVERY_HOUR_CRON : EVERY_DAY_CRON;

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                //查找过期的客户
                DbOpe_crm_customer_privatepool.Instance.ExcuteProtectCheck();
            }, cronExpression);
        }

        /// <summary>
        /// 刷新客户私有池--离职
        /// </summary>
        public static void LeaveUserProtectCheck()
        {
            LogUtil.AddLog($"定时任务开启：LeaveUserProtectCheck");
            string jobName = $"leaveUserProtectCheck";
            string cronExpression = AppSettings.Env != Enum_SystemSettingEnv.Product ? EVERY_HOUR_CRON : EVERY_DAY_CRON;

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                DbOpe_crm_customer_privatepool.Instance.ExcuteLeaveUserProtectCheck();
            }, cronExpression);
        }

        /// <summary>
        /// 刷新客户私有池
        /// </summary>
        public static void CustomerSaveNumCheck()
        {
            string jobName = $"customerSaveNumCheck";
            string cronExpression = AppSettings.Env != Enum_SystemSettingEnv.Product ? EVERY_HOUR_CRON : EVERY_DAY_CRON;

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                //定时任务执行时刷新用户表的可保留用户数据
                DbOpe_sys_user.Instance.RefreshUserMaxSaveCustomer();
            }, cronExpression);
        }

        /// <summary>
        /// 依据周期业绩刷新客户私有池
        /// </summary>
        public static void MaxCustomerRefresh()
        {
            string jobName = $"MaxCustomerRefresh";
            string cronExpression = "0 0 0 1 * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                //定时任务执行时刷新用户表的可保留用户数据
                DbOpe_sys_usermaxsaveustomer_calclog.Instance.RefreshUserMaxSaveCustomer();
            }, cronExpression);
        }
        /// <summary>
        /// 刷新合同过期客户的跟踪阶段
        /// </summary>
        public static void CustomerContractOverDue()
        {
            string jobName = $"customerStageCheck";
            string cronExpression = AppSettings.Env != Enum_SystemSettingEnv.Product ? EVERY_HOUR_CRON : EVERY_DAY_CRON;

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                //定时任务执行时刷新用户表的可保留用户数据
                DbOpe_crm_customer.Instance.ExcuteContractOverDueCheck();
            }, cronExpression);
        }

        /// <summary>
        /// 定时任务刷新日程计划超过访客时间的状态
        /// </summary>
        public static void TimingRefreshScheduleState()
        {
            var stateNormalList = DbOpe_crm_schedule.Instance.GetScheduleStateNormalOrPostponeList();
            foreach (Db_crm_schedule scheduleItem in stateNormalList)
            {
                BLL_Schedule.Instance.AddTimingRefreshScheduleTask(scheduleItem);
            }
        }

        /// <summary>
        /// 每月10号2点获取最新的环球搜账号状态
        /// </summary>
        public static void CalculateGlobalSearchUserState()
        {
            string jobName = $"calculateGlobalSearchUserState";
            string cronExpression = "0 0 2 10 * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                //获取全部在服务中的环球搜账号
                var codeList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetOnServiceGlobalSearchCode();
                int index = 0;
                int querySize = 20;
                while (index < codeList.Count())
                {
                    var queryNum = 20;
                    if (index + querySize > codeList.Count)
                        queryNum = codeList.Count() - index;
                    //获取本次循环要查询的codeList
                    var queryCodeList = codeList.GetRange(index, queryNum);
                    //查询
                    {
                        //累加起始index
                        index += querySize;
                        var queryStr = string.Join(';', queryCodeList);
                        var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, queryStr);
                        try
                        {
                            var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                            if ("200".Equals(ret["status"].ToString()))
                            {
                                var results = ret["results"].ToList();
                                results.ForEach(result =>
                                {
                                    var code = queryCodeList.Find(e => e == result["idCst"].ToString());
                                    EnumContractServiceGlobalSearchUserState openingStatus = new EnumContractServiceGlobalSearchUserState();
                                    if ("active".Equals(result["status"].ToString()))
                                        //返回正常，账号状态标记正常
                                        openingStatus = EnumContractServiceGlobalSearchUserState.Normal;
                                    else if ("disabled".Equals(result["status"].ToString()))
                                        //返回过期，账号状态标记停用
                                        openingStatus = EnumContractServiceGlobalSearchUserState.Stop;
                                    else if ("noexisted".Equals(result["status"].ToString()))
                                        //返回不存在，账号状态标记异常
                                        openingStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                                    var startDate = result["begindate"].ToString();
                                    var endDate = result["enddate"].ToString();
                                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateServiceUserOpeningStatusByNumber(code, openingStatus, startDate, endDate);
                                });
                            }
                        }
                        catch
                        {
                            continue;
                        }
                    }

                }
                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.SaveQueues();
            }, cronExpression);
        }
        /// <summary>
        /// 刷新客户的跟踪阶段并且执行自动释放
        /// </summary>
        public static void CustomerOverDue()
        {
            string jobName = $"customerOverDue";
            string cronExpression = AppSettings.Env != Enum_SystemSettingEnv.Product ? EVERY_HOUR_CRON : EVERY_DAY_CRON;

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                DbOpe_crm_customer.Instance.ExcuteContractOverDueCheck();
                DbOpe_crm_customer_privatepool.Instance.ExcuteProtectCheck();
                DbOpe_crm_customer_privatepool.Instance.ExcuteLeaveUserProtectCheck();
            }, cronExpression);
        }
        /// <summary>
        /// 刷新合同条款过期状态
        /// </summary>
        public static void ContractTermOverDueCheck()
        {
            LogUtil.AddLog($"定时任务开启：ContractTermOverDueCheck");
            string jobName = $"contractTermOverDueCheck";
            string cronExpression = AppSettings.Env != Enum_SystemSettingEnv.Product ? EVERY_HOUR_CRON : EVERY_DAY_CRON;

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                DbOpe_crm_contractterms_template.Instance.CheckContractTermTemplateDate();
            }, cronExpression);
        }

        /// <summary>
        /// 定时检查并修复未经过预处理的公司数据
        /// </summary>
        public static void CheckAndFixUnprocessedCompanies()
        {
            string jobName = $"checkAndFixUnprocessedCompanies";
            string cronExpression = AppSettings.Env != Enum_SystemSettingEnv.Product ? EVERY_HOUR_CRON : EVERY_DAY_CRON;

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                try
                {
                    // 检查系统参数是否允许执行预处理任务
                    string enablePreprocessTask = DbOpe_sys_systemparameter.Instance.GetValueByKey("EnableCompanyNamePreprocessTask");
                    if (enablePreprocessTask == "0")
                    {
                        LogUtil.AddLog($"公司名称预处理任务已被禁用，跳过执行");
                        return;
                    }

                    LogUtil.AddLog($"开始检查并修复未经过预处理的公司数据（包括主公司、子公司和关联公司）");
                    int fixedCount = CRM2_API.BLL.Customer.CustomerPreInspectionBLL.Instance.GetAndFixUnprocessedCompanies();
                    LogUtil.AddLog($"检查并修复未经过预处理的公司数据完成，共修复 {fixedCount} 家公司（包括主公司、子公司和关联公司）");
                }
                catch (Exception ex)
                {
                    LogUtil.AddLog($"检查并修复未经过预处理的公司数据失败: {ex.Message}");
                }
            }, cronExpression);
        }

        /// <summary>
        /// 节假日数据自动同步定时任务
        /// </summary>
        public static void AutoSyncHolidayData()
        {
            string jobName = $"AutoSyncHolidayData";
            // 每天凌晨2点执行，同步当前年份和下一年的节假日数据
            string cronExpression = "0 0 2 * * ?";

            SafeCronUtil.AddCronJob(jobName, () =>
            {
                try
                {
                    var currentYear = DateTime.Now.Year;
                    var nextYear = currentYear + 1;
                    
                    // 同步当前年份的节假日数据
                    BLL_WorkReportDatabase.Instance.AutoSyncHolidayData(currentYear);
                    
                    // 同步下一年的节假日数据
                    BLL_WorkReportDatabase.Instance.AutoSyncHolidayData(nextYear);
                    
                    LogUtil.AddLog($"节假日数据自动同步任务执行完成，同步年份：{currentYear}, {nextYear}");
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"节假日数据自动同步任务执行失败：{ex.Message}", ex);
                }
            }, cronExpression);
        }

        /// <summary>
        /// SaleWits定时下发任务
        /// 每天凌晨2点执行，检查需要进行年度资源下发的服务
        /// </summary>
        private static void SaleWitsScheduledDistribution()
        {
            var jobName = "SaleWits定时下发任务";
            var cronExpression = "0 2 * * *"; // 每天凌晨2点执行

            LogUtil.AddLog($"注册{jobName}，执行时间：{cronExpression}");

            SafeCronUtil.AddCronJob(jobName, async () =>
            {
                try
                {
                    LogUtil.AddLog($"{jobName}开始执行");

                    // 执行SaleWits定时下发
                    await BLL.ServiceOpening.BLL_ServiceOpening.ExecuteSaleWitsScheduledDistribution();

                    LogUtil.AddLog($"{jobName}执行完成");
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"{jobName}执行失败：{ex.Message}", ex);
                }
            }, cronExpression);
        }
    }


}