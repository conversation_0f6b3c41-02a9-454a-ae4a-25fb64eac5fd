using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 汇总报告表 - 用于存储周报月报汇总数据
    /// </summary>
    [SugarTable("crm_report_summary")]
    public class Db_crm_report_summary
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 汇总类型：2-周报，3-月报
        /// </summary>
        public int SummaryType { get; set; }

        /// <summary>
        /// 汇总标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 创建用户姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 团队ID
        /// </summary>
        public string TeamId { get; set; }

        /// <summary>
        /// 团队名称
        /// </summary>
        public string TeamName { get; set; }

        /// <summary>
        /// 汇总状态：1-草稿，2-已提交，3-已审核
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 汇总开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 汇总结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 汇总年份
        /// </summary>
        public int SummaryYear { get; set; }

        /// <summary>
        /// 汇总月份（月报使用）
        /// </summary>
        public int? SummaryMonth { get; set; }

        /// <summary>
        /// 汇总周数（周报使用）
        /// </summary>
        public int? SummaryWeek { get; set; }

        /// <summary>
        /// 包含的日报数量
        /// </summary>
        public int DailyReportCount { get; set; }

        /// <summary>
        /// 包含的周报数量（月报使用）
        /// </summary>
        public int? WeeklyReportCount { get; set; }

        /// <summary>
        /// 工作总结
        /// </summary>
        public string WorkSummary { get; set; }

        /// <summary>
        /// 主要成果
        /// </summary>
        public string MainAchievements { get; set; }

        /// <summary>
        /// 遇到的问题
        /// </summary>
        public string Issues { get; set; }

        /// <summary>
        /// 下期计划
        /// </summary>
        public string NextPlan { get; set; }

        /// <summary>
        /// 客户跟进总数
        /// </summary>
        public int TotalCustomers { get; set; }

        /// <summary>
        /// 新增客户数
        /// </summary>
        public int NewCustomers { get; set; }

        /// <summary>
        /// 有效跟进数
        /// </summary>
        public int EffectiveFollowUps { get; set; }

        /// <summary>
        /// 签约客户数
        /// </summary>
        public int SignedCustomers { get; set; }

        /// <summary>
        /// 签约金额
        /// </summary>
        public decimal SignedAmount { get; set; }

        /// <summary>
        /// 是否自动生成：0-否，1-是
        /// </summary>
        public bool IsAutoGenerated { get; set; }

        /// <summary>
        /// 生成方式：1-基于日报汇总，2-基于周报汇总，3-手动创建
        /// </summary>
        public int GenerateType { get; set; }

        /// <summary>
        /// 源报告ID列表（JSON格式存储）
        /// </summary>
        public string SourceReportIds { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SubmitTime { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? ReviewTime { get; set; }

        /// <summary>
        /// 审核人ID
        /// </summary>
        public string ReviewerId { get; set; }

        /// <summary>
        /// 审核人姓名
        /// </summary>
        public string ReviewerName { get; set; }

        /// <summary>
        /// 审核意见
        /// </summary>
        public string ReviewComment { get; set; }

        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateDate { get; set; }
    }
}
