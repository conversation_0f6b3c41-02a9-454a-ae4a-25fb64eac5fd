﻿using Microsoft.AspNetCore.Http;
using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using System.IO;
using System.Web;
using static CRM2_API.Common.Cache.LocalCache;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using Spire.Doc;
using CRM2_API.BLL.Schedule;
using CRM2_API.Model.ControllersViewModel.Schedule;
using CRM2_API.Model.ControllersViewModel.TrackingRecord;
using CRM2_API.BLL.TrackingRecord;
using NPOI.POIFS.Properties;
using System.Diagnostics.Contracts;
using DocumentFormat.OpenXml.VariantTypes;
using Lucene.Net.Support;
using System.Linq;
using LgyUtil;
using DocumentFormat.OpenXml.EMMA;
using static CRM2_API.Common.Cache.RedisCache;
using Microsoft.IdentityModel.Tokens;
using static Lucene.Net.Util.Fst.Util;
using System.Reflection.PortableExecutable;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;
using DocumentFormat.OpenXml.Presentation;
using System.Runtime.Intrinsics.X86;
using SqlSugar;
using SqlSugar.Extensions;
using CRM2_API.BLL.GtisOpe;
using SkiaSharp;
using LumiSoft.Net.Media;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.JWT;
using DocumentFormat.OpenXml.Bibliography;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using Microsoft.AspNetCore.Mvc;
using DocumentFormat.OpenXml.ExtendedProperties;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Utils;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;
using CRM2_API.DAL.DbCommon;
//using DocumentFormat.OpenXml.Spreadsheet;

namespace CRM2_API.BLL
{
    public partial class BLL_Contract : BaseBLL<BLL_Contract>
    {

        /// <summary>
        /// 原合同修改后，将增项合同状态改为草稿
        /// </summary>
        /// <param name="parentContractId">父合同ID</param>
        /// <param name="newContractId">新合同ID</param>
        public void UpdateAddItemContracts(string parentContractId,string newContractId)
        {
            try
            {
                // 1. 参数验证
                if (string.IsNullOrEmpty(parentContractId))
                {
                    LogUtil.AddErrorLog("UpdateAddItemContracts: 父合同ID不能为空");
                    return;
                }

                // 2. 查找所有需要更新的增项合同
                // 只更新非草稿、非作废状态的增项合同
                int draftStatus = EnumContractStatus.Draft.ToInt();
                int cancelStatus = EnumContractStatus.Cancel.ToInt();
                int addItemType = (int)EnumContractType.AddItem;

                var addItemContracts = DbContext.Crm2Db.Queryable<Db_crm_contract>()
                    .Where(c => c.ParentContractId == parentContractId)
                    .Where(c => c.ContractType == addItemType)
                    .Where(c => c.Deleted == false)
                    .Where(c => c.ContractStatus != draftStatus && c.ContractStatus != cancelStatus)
                    .ToList();

                if (!addItemContracts.Any())
                {
                    LogUtil.AddLog($"UpdateAddItemContracts: 父合同[{parentContractId}]下没有需要更新的增项合同");
                    return;
                }

                // 3. 检查所有增项合同是否都可以修改
                var cannotUpdateContracts = new List<string>();
                foreach (var contract in addItemContracts)
                {
                    if (!CheckContractCanUpdate(contract))
                    {
                        cannotUpdateContracts.Add($"合同[{contract.Id}]({contract.ContractName})");
                    }
                }

                if (cannotUpdateContracts.Any())
                {
                    string errorMsg = $"以下增项合同当前状态下不可修改，无法更新为草稿状态：{string.Join(", ", cannotUpdateContracts)}";
                    LogUtil.AddErrorLog($"UpdateAddItemContracts: {errorMsg}");
                    throw new ApiException(errorMsg);
                }

                // 4. 批量更新增项合同状态为草稿
                var contractIds = addItemContracts.Select(c => c.Id).ToList();

                int updatedCount = DbContext.Crm2Db.Updateable<Db_crm_contract>()
                    .SetColumns(c => c.ContractStatus == draftStatus)
                    .SetColumns(c => c.UpdateUser == UserId)
                    .SetColumns(c => c.UpdateDate == DateTime.Now)
                    .SetColumns(c => c.ParentContractId == newContractId)
                    .Where(c => contractIds.Contains(c.Id))
                    .ExecuteCommand();

                // 5. 记录日志
                LogUtil.AddLog($"UpdateAddItemContracts: 成功将父合同[{parentContractId}]下的{updatedCount}个增项合同状态更新为草稿");

                // 6. 记录详细的合同信息
                foreach (var contract in addItemContracts)
                {
                    string originalStatusDesc = contract.ContractStatus.HasValue ?
                        ((EnumContractStatus)contract.ContractStatus.Value).ToString() : "未知";
                    LogUtil.AddLog($"增项合同状态更新: 合同ID[{contract.Id}], 合同名称[{contract.ContractName}], " +
                                 $"原状态[{originalStatusDesc}] -> 草稿");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"UpdateAddItemContracts异常: 父合同ID[{parentContractId}], 错误信息: {ex.Message}");
                throw new ApiException($"更新增项合同状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查合同是否可以修改（参考UpdateContract方法的逻辑）
        /// </summary>
        /// <param name="contract">合同实体</param>
        /// <returns>是否可以修改</returns>
        private bool CheckContractCanUpdate(Db_crm_contract contract)
        {
            try
            {
                // 1. 检查合同是否已开通服务
                bool isHaveContractProductService = DbOpe_crm_contract_productinfo.Instance.IsHaveContractProductServiceInfoByContractId(contract.Id);
                if (isHaveContractProductService)
                {
                    return false; // 当前合同已开通服务,不可以修改合同信息
                }

                // 2. 检查是否存在审核中的到账登记
                List<Db_crm_contract_receiptregister> inViewReceiptRegister = DbOpe_crm_contract_receiptregister.Instance.GetInViewContractReceiptRegisterByContractId(contract.Id);
                if (inViewReceiptRegister.Count > 0)
                {
                    return false; // 当前合同存到账登记审核中,不可以修改合同信息
                }

                // 3. 检查是否存在已确认的到账信息
                List<Db_crm_contract_receiptregister> receiptRegister = DbOpe_crm_contract_receiptregister.Instance.GetConfirmedContractReceiptRegisterByContractId(contract.Id);
                if (receiptRegister.Count > 0)
                {
                    return false; // 当前合同存在到账信息,不可以修改合同信息
                }

                // 4. 检查是否已上传盖章合同
                List<Db_crm_contract_sealedcontract_attachfile> sealedContract = DbOpe_crm_contract_sealedcontract_attachfile.Instance.GetDataList(r => r.ContractId == contract.Id);
                if (sealedContract.Count > 0)
                {
                    return false; // 当前合同已上传盖章合同,不可以修改合同信息
                }

                // 5. 检查是否已上传电子合同
                List<Db_crm_contract_electroniccontract_attachfile> electronicContract = DbOpe_crm_contract_electroniccontract_attachfile.Instance.GetDataList(r => r.ContractId == contract.Id);
                if (electronicContract.Count > 0)
                {
                    return false; // 当前合同已上传电子合同,不可以修改合同信息
                }

                // 6. 检查合同状态是否允许修改
                // 参考UpdateContract方法中的完整状态检查逻辑
                int passStatus = EnumContractStatus.Pass.ToInt();

                // 特殊情况：如果是通过状态的合同，参考CopyContract的逻辑
                // 无论是自动审核还是手动审核通过的合同，都可以在满足条件时修改
                if (contract.ContractStatus == passStatus)
                {
                    // 通过状态的合同可以修改（已在前面检查了服务开通、到账登记、盖章合同等条件）
                    return true; // 通过状态的合同在满足其他条件下可以修改
                }

                // 其他状态的检查：参考CopyContract方法中的完整状态列表
                var allowedStatuses = new int[]
                {
                    EnumContractStatus.Draft.ToInt(),
                    EnumContractStatus.Submit.ToInt(),
                    EnumContractStatus.Refuse.ToInt(),
                    EnumContractStatus.SuperSubAccountSubmit.ToInt(),
                    EnumContractStatus.SuperSubAccountPass.ToInt(),
                    EnumContractStatus.SuperSubAccountRefuse.ToInt(),
                    EnumContractStatus.ToBeConfirmed.ToInt(),
                    EnumContractStatus.InitialAuditPass.ToInt(),
                    EnumContractStatus.InitialAuditRefuse.ToInt(),
                    EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit.ToInt(),
                    EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit.ToInt(),
                    EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit.ToInt(),
                    EnumContractStatus.OrgRegimentAuditPass.ToInt(),
                    EnumContractStatus.OrgRegimentAuditRefuse.ToInt(),
                    EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit.ToInt(),
                    EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit.ToInt(),
                    EnumContractStatus.OrgBrigadeAuditPass.ToInt(),
                    EnumContractStatus.OrgBrigadeAuditRefuse.ToInt(),
                    EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit.ToInt(),
                    EnumContractStatus.OrgDivisionAuditPass.ToInt(),
                    EnumContractStatus.OrgDivisionAuditRefuse.ToInt(),
                    EnumContractStatus.SubmitOrgRegimentAudit.ToInt(),
                    EnumContractStatus.SubmitOrgBrigadeAudit.ToInt(),
                    EnumContractStatus.SubmitOrgDivisionAudit.ToInt(),
                    EnumContractStatus.SubmitOrgRegimentAuditPass.ToInt(),
                    EnumContractStatus.SubmitOrgRegimentAuditRefuse.ToInt(),
                    EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit.ToInt(),
                    EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit.ToInt(),
                    EnumContractStatus.SubmitOrgBrigadeAuditPass.ToInt(),
                    EnumContractStatus.SubmitOrgBrigadeAuditRefuse.ToInt(),
                    EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit.ToInt(),
                    EnumContractStatus.SubmitOrgDivisionAuditPass.ToInt(),
                    EnumContractStatus.SubmitOrgDivisionAuditRefuse.ToInt()
                };

                if (!allowedStatuses.Contains(contract.ContractStatus.GetValueOrDefault()))
                {
                    return false; // 当前合同状态下，不可以修改合同信息
                }

                return true; // 所有检查通过，可以修改
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"CheckContractCanUpdate异常: 合同ID[{contract.Id}], 错误信息: {ex.Message}");
                return false; // 出现异常时，为安全起见返回不可修改
            }
        }

        /// <summary>
        /// 增项合同保存时验证
        /// </summary>
        /// <param name="currentContractId"></param>
        /// <param name="ContractType"></param>
        /// <param name="ContractStatus"></param>
        /// <param name="ParentContractId"></param>
        /// <exception cref="ApiException"></exception>
        public void CheckAddItemContractCanSave(string currentContractId,int? ContractType, int? ContractStatus,string ParentContractId)
        {
            //增项合同验证(草稿不验证)
            if(ContractType == EnumContractType.AddItem.ToInt() &&
            ContractStatus != EnumContractStatus.Draft.ToInt()){
                var msg = "";
                if(!CheckContractCanAddItem(ParentContractId,ref msg,currentContractId)){
                    throw new ApiException(msg);
                }
            }

        }

        /// <summary>
        /// 判断合同是否可以进行增项
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="currentContractId">当前增项合同ID，用于排除当前合同自己</param>
        /// <param name="msg">提示信息</param>
        /// <returns>是否可以增项</returns>
        public bool CheckContractCanAddItem(string contractId, ref string msg,string currentContractId = "")
        {
            try
            {
                // 1. 检查合同ID是否为空
                if (string.IsNullOrEmpty(contractId))
                {
                    throw new ApiException("合同ID不能为空");
                }

                // 2. 获取原合同信息，检查合同是否存在、未删除且有权限
                var contract = DbOpe_crm_contract.Instance.GetContractById(contractId, true);
                if (contract == null)
                {
                    throw new ApiException("未找到合同或该用户没有合同权限");
                }

                // 3. 检查合同状态是否为通过状态
                if (contract.ContractStatus != EnumContractStatus.Pass.ToInt() &&
                    contract.ContractStatus != EnumContractStatus.AutoPass.ToInt())
                {
                    throw new ApiException("当前合同状态下，不可以进行增项操作");
                }

                // 4. 检查合同类型是否为新增合同或续约合同
                if (contract.ContractType != (int)EnumContractType.New &&
                    contract.ContractType != (int)EnumContractType.ReNew)
                {
                    throw new ApiException("只有新增合同或续约合同才可以进行增项操作");
                }

                // 5. 检查是否已经存在审核中（除了通过 草稿 自动通过 作废）的增项合同
                int[] canAddItemStatus = new int[] {
                    EnumContractStatus.Pass.ToInt()
                        ,EnumContractStatus.Cancel.ToInt()
                       ,EnumContractStatus.Draft.ToInt()
                      ,EnumContractStatus.AutoPass.ToInt()
                };
                int addItemType = (int)EnumContractType.AddItem;
                // 查是否有审核中的增项合同(如果有当前合同，排除当前合同本身)
                var existingAddItemContracts = DbContext.Crm2Db.Queryable<Db_crm_contract>()
                    .Where(c => c.ParentContractId == contractId)
                    .Where(c => c.ContractType == addItemType)
                    .Where(c => c.Deleted == false)
                    .Where(c => !SqlFunc.ContainsArray(canAddItemStatus,c.ContractStatus ))
                    .WhereIF(!string.IsNullOrEmpty(currentContractId),c=>c.Id!=currentContractId)
                    .ToList();

                if (existingAddItemContracts.Any())
                {
                    throw new ApiException("该合同已存在其他未作废的增项合同，不能再创建新的增项合同，请修改原增项合同或将原增项合同作废后，再创建新的增项合同");
                }

                // 6. 所有检查通过
                msg = "可以进行增项操作";
                return true;
            }
            catch (Exception ex)
            {
                throw new ApiException($"检查合同增项权限时发生错误：{ex.Message}");
            }
        }
    }
}
