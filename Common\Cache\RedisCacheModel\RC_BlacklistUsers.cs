using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbCommon;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        /// <summary>
        /// 黑名单用户缓存
        /// </summary>
        public class BlacklistUsers
        {
            const string BLACKLIST_ALL = "blacklist_users_all";
            
            /// <summary>
            /// 缓存的简化模型
            /// </summary>
            public class BlacklistUserSimple
            {
                /// <summary>
                /// 主键ID
                /// </summary>
                public string Id { get; set; }
                
                /// <summary>
                /// 用户ID
                /// </summary>
                public string UserId { get; set; }
            }
            
            /// <summary>
            /// 初始化黑名单用户缓存
            /// </summary>
            public static void InitializeCache()
            {
                // 从数据库获取并更新缓存
                var dbBlacklistUsers = DbContext.Crm2Db.Queryable<Db_crm_customer_blacklistuser>().ToList();
                ClearCache();
                
                if (dbBlacklistUsers != null && dbBlacklistUsers.Count > 0)
                {
                    var blacklistUsers = dbBlacklistUsers.Select(r => new BlacklistUserSimple
                    {
                        Id = r.Id,
                        UserId = r.UserId
                    }).ToList();
                    
                    // 保存到缓存
                    SaveAllBlacklistUsers(blacklistUsers);
                }
            }
            
            /// <summary>
            /// 获取所有黑名单用户信息
            /// </summary>
            /// <returns>黑名单用户列表，如果缓存不存在则返回null</returns>
            public static List<BlacklistUserSimple> GetAllBlacklistUsers()
            {
                if (RedisHelper.Exists(BLACKLIST_ALL))
                    return RedisHelper.Get<List<BlacklistUserSimple>>(BLACKLIST_ALL);
                
                // 如果缓存不存在，初始化缓存
                InitializeCache();
                return RedisHelper.Get<List<BlacklistUserSimple>>(BLACKLIST_ALL);
            }
            
            /// <summary>
            /// 获取所有黑名单用户ID
            /// </summary>
            /// <returns>黑名单用户ID列表</returns>
            public static List<string> GetAllBlacklistUserIds()
            {
                var users = GetAllBlacklistUsers();
                if (users != null && users.Count > 0)
                {
                    return users.Select(u => u.UserId).Distinct().ToList();
                }
                return new List<string>();
            }
            
            /// <summary>
            /// 保存所有黑名单用户信息到缓存
            /// </summary>
            /// <param name="blacklistUsers">黑名单用户列表</param>
            public static void SaveAllBlacklistUsers(List<BlacklistUserSimple> blacklistUsers)
            {
                RedisHelper.Set(BLACKLIST_ALL, blacklistUsers, TimeSpan.FromHours(26));
            }
            
            /// <summary>
            /// 检查用户是否在黑名单中
            /// </summary>
            /// <param name="userId">用户ID</param>
            /// <returns>如果在黑名单中返回true，否则返回false</returns>
            public static bool IsUserInBlacklist(string userId)
            {
                if (string.IsNullOrEmpty(userId))
                    return false;
                
                var allUsers = GetAllBlacklistUsers();
                if (allUsers != null && allUsers.Count > 0)
                {
                    return allUsers.Any(u => u.UserId == userId);
                }
                return false;
            }
            
            /// <summary>
            /// 添加用户到黑名单
            /// </summary>
            /// <param name="userId">用户ID</param>
            /// <returns>添加结果</returns>
            public static bool AddUserToBlacklist(string userId)
            {
                if (string.IsNullOrEmpty(userId))
                    return false;
                
                try
                {
                    // 先检查是否已在黑名单中
                    if (IsUserInBlacklist(userId))
                        return true;
                    
                    // 创建实体并保存到数据库
                    var entity = new Db_crm_customer_blacklistuser
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserId = userId
                    };
                    
                    var result = DbContext.Crm2Db.Insertable(entity).ExecuteCommand() > 0;
                    if (result)
                    {
                        // 更新全局缓存
                        var allUsers = GetAllBlacklistUsers() ?? new List<BlacklistUserSimple>();
                        allUsers.Add(new BlacklistUserSimple
                        {
                            Id = entity.Id,
                            UserId = entity.UserId
                        });
                        SaveAllBlacklistUsers(allUsers);
                    }
                    return result;
                }
                catch
                {
                    return false;
                }
            }
            
            /// <summary>
            /// 从黑名单中移除用户
            /// </summary>
            /// <param name="userId">用户ID</param>
            /// <returns>移除结果</returns>
            public static bool RemoveUserFromBlacklist(string userId)
            {
                if (string.IsNullOrEmpty(userId))
                    return false;
                
                try
                {
                    // 从数据库中删除
                    var result = DbContext.Crm2Db.Deleteable<Db_crm_customer_blacklistuser>()
                        .Where(u => u.UserId == userId)
                        .ExecuteCommand() > 0;
                    
                    if (result)
                    {
                        // 更新全局缓存
                        var allUsers = GetAllBlacklistUsers();
                        if (allUsers != null && allUsers.Count > 0)
                        {
                            allUsers = allUsers.Where(u => u.UserId != userId).ToList();
                            SaveAllBlacklistUsers(allUsers);
                        }
                    }
                    return result;
                }
                catch
                {
                    return false;
                }
            }
            
            /// <summary>
            /// 清除所有黑名单用户缓存
            /// </summary>
            public static void ClearCache()
            {
                RedisHelper.Del(BLACKLIST_ALL);
            }
        }
    }
} 