﻿using System;
using System.Linq;
using System.Text;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_operation_log")]
    public class Db_sys_operation_log
    {
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:操作人系统Id主键(sys_user表主键)
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string OperatorSysId { get; set; }

        /// <summary>
        /// Desc:操作人用户编号Id
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string OperatorUserNum { get; set; }

        /// <summary>
        /// Desc:操作人账号名称
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string OperatorUserName { get; set; }

        /// <summary>
        /// Desc:操作人真实姓名
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string OperatorName { get; set; }

        /// <summary>
        /// Desc:所属组织id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string OrgId { get; set; }

        /// <summary>
        /// Desc:组织名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string OrgName { get; set; }

        /// <summary>
        /// Desc:表单表描述字段或名称字段
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string OperationType { get; set; }

        /// <summary>
        /// Desc:操作环境(1:移动端;2:Web端)
        /// Default:
        /// Nullable:False
        /// </summary>           
        public EnumOpeEnv OperationEnv { get; set; }

        /// <summary>
        /// Desc:被操作数据
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string OperationData { get; set; }

        /// <summary>
        /// Desc:操作内容
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string OperationContent { get; set; }

        /// <summary>
        /// Desc:操作时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? OperationDate { get; set; }

        /// <summary>
        /// Desc:表单表Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string FormId { get; set; }

        /// <summary>
        /// Desc:接口调用结果
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? MethodResult { get; set; }

        /// <summary>
        /// Desc:接口异常信息
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ExceptionContent { get; set; }

    }
}
