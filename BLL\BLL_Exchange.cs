﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.Schedule;
using CRM2_API.BLL.TrackingRecord;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Spreadsheet;
using LgyUtil;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System.Diagnostics.Contracts;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;
using static CRM2_API.Model.ControllersViewModel.VM_Exchange;

namespace CRM2_API.BLL
{
    public class BLL_Exchange : BaseBLL<BLL_Exchange>
    {
        /// <summary>
        /// 添加汇率申请信息
        /// </summary>
        public void AddExchangeAppl(AddExchangeAppl_In addExchangeAppl)
        {
            DbOpe_sys_exchange_appl.Instance.TransDeal(() => {
                Db_sys_exchange_appl item = new Db_sys_exchange_appl();
                item = addExchangeAppl.MappingTo<Db_sys_exchange_appl>();
                item.State = EnumExchangeApplState.Submit.ToInt();
                Guid exchangeApplId = DbOpe_sys_exchange_appl.Instance.InsertDataReturnId(item);

                foreach (ExchangeApplItem_In it in addExchangeAppl.ExchangeApplItem)
                {
                    Db_sys_exchange_appl_item ai = new Db_sys_exchange_appl_item();
                    ai = it.MappingTo<Db_sys_exchange_appl_item>();
                    ai.ExchangeApplId = exchangeApplId.ToString();
                    ai.ExchangeDate = addExchangeAppl.ExchangeDate;
                    DbOpe_sys_exchange_appl_item.Instance.InsertData(ai);
                }
                DbOpe_sys_exchange_audit.Instance.AddExchangeAudit(exchangeApplId.ToString(), UserId);
                string dataState = Dictionary.ExchangeApplState.First(e => e.Value == item.State.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_sys_exchange_appl>("汇率审批流程", exchangeApplId.ToString(), item, "", dataState, "新建");
            });
        }

        /// <summary>
        /// 修改汇率申请信息
        /// </summary>
        public void UpdateExchangeAppl(UpdateExchangeAppl_In updateExchangeAppl)
        {
            Db_sys_exchange_appl exchangeAppl = DbOpe_sys_exchange_appl.Instance.GetDataById(updateExchangeAppl.Id);
            if (exchangeAppl.State != EnumExchangeApplState.Refuse.ToInt())
            {
                throw new ApiException("该汇率信息不可以修改");
            }

            DbOpe_sys_exchange_appl.Instance.TransDeal(() => {
                Db_sys_exchange_appl item = new Db_sys_exchange_appl();
                item = updateExchangeAppl.MappingTo<Db_sys_exchange_appl>();
                item.State = EnumExchangeApplState.Submit.ToInt();
                DbOpe_sys_exchange_appl.Instance.UpdateData(item);

                foreach (ExchangeApplItem_In it in updateExchangeAppl.ExchangeApplItem)
                {
                    Db_sys_exchange_appl_item ai = new Db_sys_exchange_appl_item();
                    ai = it.MappingTo<Db_sys_exchange_appl_item>();
                    ai.ExchangeDate = updateExchangeAppl.ExchangeDate;
                    ai.ExchangeApplId = updateExchangeAppl.Id;
                    DbOpe_sys_exchange_appl_item.Instance.UpdateData(ai);
                }

                //更新审核历史状态
                DbOpe_sys_exchange_audit.Instance.UpdateIsHistoryByExchangeApplId(updateExchangeAppl.Id);

                //提交审核
                DbOpe_sys_exchange_audit.Instance.AddExchangeAudit(updateExchangeAppl.Id, UserId);

                string dataState = Dictionary.ExchangeApplState.First(e => e.Value == item.State.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_sys_exchange_appl>("汇率审批流程", updateExchangeAppl.Id.ToString(), item, "", dataState, "修改");
            });
        }

        /// <summary>
        /// 删除汇率申请信息
        /// </summary>
        public void DeleteExchangeAppl(List<string> ids)
        {
            DbOpe_crm_contract.Instance.TransDeal(() =>
            {
                int Submit = EnumExchangeApplState.Submit.ToInt();
                int Pass = EnumExchangeApplState.Pass.ToInt();
                List<Db_sys_exchange_appl> exchangeAppls = DbOpe_sys_exchange_appl.Instance.GetDataList(r => ids.Contains(r.Id) && (r.State == Submit || r.State == Pass));
                if (exchangeAppls.Count() > 0)
                {
                    throw new ApiException("该汇率申请信息不可以删除");
                }
                DbOpe_sys_exchange_appl.Instance.DeleteData(ids);
                DbOpe_sys_exchange_audit.Instance.DeleteData(r => ids.Contains(r.ExchangeApplId));
                DbOpe_sys_exchange_appl_item.Instance.DeleteData(r => ids.Contains(r.ExchangeApplId));
            });
        }

        /// <summary>
        /// 根据汇率申请信息Id获取汇率申请信息
        /// </summary>
        public ExchangeAppl_Out GetExchangeApplById(string id)
        {
            return DbOpe_sys_exchange_appl.Instance.GetExchangeApplById(id);
        }

        /// <summary>
        /// 根据查询条件获取汇率申请信息列表
        /// </summary>
        public ApiTableOut<SearchExchangeApplList_Out> SearchExchangeApplList(SearchExchangeApplList_In searchExchangeApplListIn)
        {
            int total = 0;
            return new ApiTableOut<SearchExchangeApplList_Out> { Data = DbOpe_sys_exchange_appl.Instance.SearchExchangeApplList(searchExchangeApplListIn, UserId, ref total), Total = total };
        }

        /// <summary>
        /// 审核汇率申请信息
        /// </summary>
        public void AuditExchangeAppl(AuditExchangeAppl_In auditExchangeApplIn)
        {
            List<string> ids = auditExchangeApplIn.Id.Select(r => r.ToString()).ToList();

            List<Db_sys_exchange_appl> exchangeAppls = DbOpe_sys_exchange_appl.Instance.GetDataList(r=> ids.Contains(r.Id));
            foreach (Db_sys_exchange_appl ea in exchangeAppls)
            {
                if (ea.State != EnumExchangeApplState.Submit.ToInt())
                {
                    throw new ApiException("该汇率信息不可以审核");
                }
            }
            
            DbOpe_sys_exchange_appl.Instance.TransDeal(() =>
            {
                foreach (Guid id in auditExchangeApplIn.Id)
                {
                    DbOpe_sys_exchange_appl.Instance.UpdateData(r => new Db_sys_exchange_appl()
                    {
                        State = auditExchangeApplIn.State
                    }, id.ToString());

                    Db_sys_exchange_audit audit = DbOpe_sys_exchange_audit.Instance.GetData(r => r.ExchangeApplId == id.ToString() && r.IsHistory == false && r.Deleted == false);
                    DbOpe_sys_exchange_audit.Instance.AuditExchangeAppl(audit.Id, auditExchangeApplIn.Feedback, auditExchangeApplIn.State, UserId);

                    if (auditExchangeApplIn.State == EnumExchangeApplState.Pass.ToInt())
                    {
                        List<Db_sys_exchange_appl_item> item = DbOpe_sys_exchange_appl_item.Instance.GetDataList(r => r.ExchangeApplId == id.ToString());
                        foreach (Db_sys_exchange_appl_item it in item)
                        {
                            int Deactivate = EnumExchangeState.Deactivate.ToInt();
                            int Enable = EnumExchangeState.Enable.ToInt();
                            DbOpe_sys_exchange.Instance.UpdateData(r => new Db_sys_exchange { State = Deactivate }, r => r.ExchangeDate == it.ExchangeDate && r.StandardCurrency == it.StandardCurrency && r.TargetCurrency == it.TargetCurrency && r.State == Enable);

                            Db_sys_exchange exchange = new Db_sys_exchange();
                            exchange.ExchangeDate = it.ExchangeDate;
                            exchange.StandardCurrency = it.StandardCurrency;
                            exchange.TargetCurrency = it.TargetCurrency;
                            exchange.ExchangeRate = it.ExchangeRate;
                            exchange.State = EnumExchangeState.Enable.ToInt();
                            DbOpe_sys_exchange.Instance.InsertData(exchange);

                            //更新银行到账的到账金额(未登记的)
                            int Registered = EnumMatchingStatus.Registered.ToInt();
                            //DbOpe_crm_collectioninfo.Instance.UpdateData(r => new Db_crm_collectioninfo { ArrivalAmount = r.FCArrivalAmount / it.ExchangeRate }, r => r.ArrivalDate == it.ExchangeDate && r.ArrivalAmount == 0 && r.Currency == it.TargetCurrency && r.Deleted == false && r.MatchingStatus != Registered);
                            DbOpe_crm_collectioninfo.Instance.UpdateData(r => new Db_crm_collectioninfo { ArrivalAmount = SqlFunc.Floor(r.FCArrivalAmount * it.ExchangeRate) }, r => SqlFunc.ToDateShort(r.ArrivalDate) == SqlFunc.ToDateShort(it.ExchangeDate) && (r.ExchangeRate != it.ExchangeRate || r.ExchangeRate == null) && r.Currency == it.TargetCurrency && r.Deleted == false && r.MatchingStatus != Registered);


                            //更新银行到账的到账金额(已登记的)
                            List<Db_crm_collectioninfo> collectionInfo = DbOpe_crm_collectioninfo.Instance.GetDataList(r => SqlFunc.ToDateShort(r.ArrivalDate) == SqlFunc.ToDateShort(it.ExchangeDate) && (r.ExchangeRate != it.ExchangeRate || r.ExchangeRate == null) && r.Currency == it.TargetCurrency && r.Deleted == false && r.MatchingStatus == Registered);
                            List<string> collectionInfoIds = collectionInfo.Select(r => r.Id).ToList();
                            DbOpe_crm_collectioninfo.Instance.UpdateData(r => new Db_crm_collectioninfo { ArrivalAmount = SqlFunc.Floor(r.FCArrivalAmount * it.ExchangeRate) }, r => collectionInfoIds.Contains(r.Id));
                            //更新已登记的银行到账金额(未确认的)
                            //获取到账登记相关记录
                            List<Db_crm_contract_receiptregister> receiptregisterList = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterByCollectionInfoIds(collectionInfoIds);
                            foreach (Db_crm_contract_receiptregister receiptregister in receiptregisterList)
                            {
                                BLL_ContractReceiptRegister.Instance.ReCalculatePerformance(receiptregister.Id);
                            }

                            //更新已登记的银行到账金额(已确认的)
                            int State = EnumRegisterState.Pass.ToInt();
                            int AchievementState = EnumAchievementState.Confirmed.ToInt();
                            List<Db_crm_contract_receiptregister> receiptregisterConfirmed = receiptregisterList.Where(r => r.State == State && r.AchievementState == AchievementState).ToList();
                            foreach (Db_crm_contract_receiptregister receiptregister in receiptregisterConfirmed)
                            {
                                //更新审核历史状态
                                DbOpe_crm_contract_receiptregister_achievement_audit.Instance.UpdateIsHistoryByContractReceiptRegisterId(receiptregister.Id);
                                //变更确认状态
                                int UnConfirmed = EnumAchievementState.UnConfirmed.ToInt();
                                DbOpe_crm_contract_receiptregister.Instance.UpdateData(r => new Db_crm_contract_receiptregister { AchievementState = UnConfirmed }, receiptregister.Id);
                                int Pass = EnumRegisterState.Pass.ToInt();
                                Db_crm_contract_receiptregister_audit receiptregister_audit = DbOpe_crm_contract_receiptregister_audit.Instance.GetData(r => r.ContractReceiptRegisterId == receiptregister.Id && r.State == Pass && r.IsHistory == false);
                                DbOpe_crm_contract_receiptregister_achievement_audit.Instance.AddReceiptRegisterAchievementAudit(receiptregister.Id, receiptregister_audit.Id, UserId);

                                ////判断该合同下是否还有其他已确认的登记信息，如果没有登记信息，则清空合同的客户编码
                                //List<Db_crm_contract_receiptregister> rrlist = DbOpe_crm_contract_receiptregister.Instance.GetConfirmedContractReceiptRegisterByContractId(receiptregister.ContractId);
                                //if (rrlist.Count() == 0)
                                //{
                                //    DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ContractNum = String.Empty }, receiptregister.ContractId);
                                //}

                                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(receiptregister.ContractId);
                                int state = DbOpe_crm_contract_receiptregister.Instance.GetReceiptRegisterStateById(receiptregister.Id.ToString()).State.Value;
                                string dataReceiptRegisterState = Dictionary.RegisterState.First(e => e.Value == state.ToInt().ToString()).Name;
                                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_receiptregister, Db_crm_contract>("到账业绩审核流程", receiptregister.Id, receiptregister, contract, contract.Issuer, "汇率变更", dataReceiptRegisterState, "汇率变更");
                            }


                            //更新合同的合同金额转换
                            int Currency = EnumCurrency.CNY.ToInt();
                            DbOpe_crm_contract.Instance.UpdateData(r => new Db_crm_contract { ContractAmountConvert = SqlFunc.Floor(r.FCContractAmount * it.ExchangeRate) }, r => SqlFunc.ToDateShort(r.SigningDate) == SqlFunc.ToDateShort(it.ExchangeDate) && r.Currency == it.TargetCurrency && r.Currency != Currency && r.Deleted == false);
                        }
                    }

                    Db_sys_exchange_appl exchangeAppl = exchangeAppls.Where(r => r.Id == id.ToString()).First();
                    exchangeAppl.State = auditExchangeApplIn.State;
                    string dataState = Dictionary.ExchangeApplState.First(e => e.Value == exchangeAppl.State.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_sys_exchange_appl>("汇率审批流程", id.ToString(), exchangeAppl, auditExchangeApplIn.Feedback, dataState, "审核");
                }
            });
        }

        /// <summary>
        /// 根据汇率申请信息汇率日期获取汇率申请信息
        /// </summary>
        public ExchangeAppl_Out GetExchangeApplByExchangeDate(string exchangeDate)
        {
            return DbOpe_sys_exchange_appl.Instance.GetExchangeApplByExchangeDate(exchangeDate);
        }

        /// <summary>
        /// 根据汇率申请信息汇率日期获取汇率申请信息列表
        /// </summary>
        public List<ExchangeApplAndDate_Out> GetExchangeApplByDate(string date)
        {
            return DbOpe_sys_exchange_appl.Instance.GetExchangeApplByDate(date);
        }

        /// <summary>
        /// 根据汇率日期获取汇率信息
        /// </summary>
        public List<Exchange_Out> GetExchangeByExchangeDate(string exchangeDate)
        {
            return DbOpe_sys_exchange.Instance.GetExchangeByExchangeDate(exchangeDate);
        }
    }
}
