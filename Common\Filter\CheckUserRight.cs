﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;

namespace CRM2_API.Common.Filter
{
    public class CheckUserRight : IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            //判断被访问接口是否跳过登录token验证,跳过token验证的接口(如登录相关接口)不需要进行权限验证
            if (!context.Filters.Any(f => f is SkipAuthCheckAttribute) && !context.Filters.Any(f => f is SkipRightCheckAttribute))
            {
                //获取接口的ControllerName
                var controllerName = context.ActionDescriptor.DisplayName.Split('.')[2].ToString().Replace("Controller", "");
                //获取接口的ActionName
                var methodName = context.ActionDescriptor.DisplayName.Split('.')[3].Replace(" (CRM2_API)", "");
                //从TokenModel中获取用户主键userId
                var userId = TokenModel.Instance.id;
                //根据userId，从缓存中获取当前用户的接口权限
                var interfaces = RedisCache.UserInterfaceRight.GetUserInterfaceRight(userId);
                //比较当前用户是否可以访问当前接口
                if (interfaces == null || !interfaces.Where(e => e.ControllerName == controllerName && e.MethodName == methodName).Any())
                    throw new ApiException("当前用户无接口使用权限");
                //填充Com_SysForm
                Com_SysForm.SetDbSysFrom(interfaces.First(e => e.ControllerName == controllerName && e.MethodName == methodName));
            }
        }
    }

    /// <summary>
    /// 跳过权限验证特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class SkipRightCheckAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {

        }
    }
}
