using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.Enum;
using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.System;
using Newtonsoft.Json;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告时效管理业务逻辑类（数据库版本）
    /// </summary>
    public partial class BLL_WorkReportDatabase : BaseBLL<BLL_WorkReportDatabase>
    {
        #region 节假日管理

        /// <summary>
        /// 获取节假日分页列表
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns></returns>
        public ApiTableOut<VM_Holiday_Out> GetHolidayPageList(VM_HolidayQuery_In input)
        {
            try
            {
                var holidayDbOpe = DbOpe_crm_holiday.Instance;
                
                // 转换枚举类型
                EnumHolidayType? holidayType = null;
                if (input.HolidayType.HasValue)
                {
                    holidayType = (EnumHolidayType)input.HolidayType.Value;
                }

                // 查询数据库
                var (data, total) = holidayDbOpe.GetHolidayPageList(
                    input.Year, 
                    input.Month, 
                    holidayType, 
                    input.PageNumber, 
                    input.PageSize
                );

                // 转换为输出模型
                var result = data.Select(h => new VM_Holiday_Out
                {
                    Id = h.Id,
                    HolidayDate = h.HolidayDate,
                    HolidayYear = h.HolidayYear,
                    HolidayMonth = h.HolidayMonth,
                    HolidayDay = h.HolidayDay,
                    HolidayName = h.HolidayName,
                    HolidayType = (EnumHolidayType)h.HolidayType,
                    IsWorkday = h.IsWorkday,
                    DayOfWeek = GetDayOfWeekName(h.HolidayDate.DayOfWeek),
                    Remark = h.Remark,
                    Source = h.Source,
                    CreateDate = h.CreateDate ?? DateTime.Now
                }).ToList();

                return new ApiTableOut<VM_Holiday_Out>
                {
                    Data = result,
                    Total = total
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取节假日分页列表失败：{ex.Message}", ex);
                throw new ApiException($"获取节假日分页列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否为工作日
        /// </summary>
        /// <param name="input">日期信息</param>
        /// <returns></returns>
        public VM_WorkdayCheck_Out CheckWorkday(VM_WorkdayCheck_In input)
        {
            try
            {
                // 参数验证
                if (input == null)
                {
                    throw new ApiException("输入参数不能为空");
                }

                if (input.Date == default(DateTime))
                {
                    throw new ApiException("日期不能为空或无效");
                }

                // 使用数据库操作层判断工作日
                var holidayDbOpe = DbOpe_crm_holiday.Instance;
                var isWorkday = holidayDbOpe.IsWorkday(input.Date);

                // 查询节假日信息
                var holidays = holidayDbOpe.GetHolidaysByDateRange(input.Date.Date, input.Date.Date);
                var holiday = holidays.FirstOrDefault();

                string holidayInfo;
                if (holiday != null)
                {
                    // 如果是数据库中的节假日记录
                    holidayInfo = holiday.HolidayName ?? GetDayOfWeekName(input.Date.DayOfWeek);
                }
                else
                {
                    // 如果不是特殊节假日，显示星期几
                    holidayInfo = isWorkday ? "工作日" : GetDayOfWeekName(input.Date.DayOfWeek);
                }

                return new VM_WorkdayCheck_Out
                {
                    Date = input.Date,
                    IsWorkday = isWorkday,
                    DayOfWeek = GetDayOfWeekName(input.Date.DayOfWeek),
                    HolidayInfo = holidayInfo
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"判断工作日失败：{ex.Message}", ex);
                throw new ApiException($"判断工作日失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取下一个工作日
        /// </summary>
        /// <param name="input">起始日期</param>
        /// <returns></returns>
        public VM_NextWorkday_Out GetNextWorkday(VM_NextWorkday_In input)
        {
            try
            {
                // 使用数据库操作层获取下一个工作日
                var holidayDbOpe = DbOpe_crm_holiday.Instance;
                var nextWorkday = holidayDbOpe.GetNextWorkday(input.StartDate, input.OffsetDays);

                return new VM_NextWorkday_Out
                {
                    StartDate = input.StartDate,
                    OffsetDays = input.OffsetDays,
                    NextWorkday = nextWorkday,
                    DayOfWeek = GetDayOfWeekName(nextWorkday.DayOfWeek),
                    Description = $"从{input.StartDate:yyyy-MM-dd}开始的第{input.OffsetDays}个工作日是{nextWorkday:yyyy-MM-dd}({GetDayOfWeekName(nextWorkday.DayOfWeek)})"
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取下一工作日失败：{ex.Message}", ex);
                return new VM_NextWorkday_Out
                {
                    StartDate = input.StartDate,
                    OffsetDays = input.OffsetDays,
                    NextWorkday = input.StartDate.AddDays(input.OffsetDays),
                    DayOfWeek = GetDayOfWeekName(input.StartDate.AddDays(input.OffsetDays).DayOfWeek),
                    Description = "计算失败，返回默认值"
                };
            }
        }

        #endregion

        #region 时间配置管理

        /// <summary>
        /// 获取指定报告类型的生效时间配置
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns></returns>
        public VM_TimeConfig_Out GetActiveTimeConfig(VM_ActiveTimeConfig_In input)
        {
            try
            {
                var timeConfigDbOpe = DbOpe_crm_report_time_config.Instance;
                var config = timeConfigDbOpe.GetActiveTimeConfig(input.ReportType);

                if (config == null)
                {
                    throw new ApiException($"未找到报告类型 {input.ReportType} 的生效时间配置");
                }

                return new VM_TimeConfig_Out
                {
                    Id = config.Id,
                    ReportType = input.ReportType,
                    ConfigName = config.ConfigName,
                    RegularTimeType = config.RegularTimeType,
                    RegularTimeValue = config.RegularTimeValue,
                    RegularTimeDescription = ParseTimeDescription(config.RegularTimeValue),
                    FinalTimeType = config.FinalTimeType,
                    FinalTimeValue = config.FinalTimeValue,
                    FinalTimeDescription = ParseTimeDescription(config.FinalTimeValue),
                    LateTimeType = config.LateTimeType,
                    LateTimeValue = config.LateTimeValue,
                    LateTimeDescription = ParseTimeDescription(config.LateTimeValue),
                    IgnoreHoliday = config.IgnoreHoliday ?? false,
                    IsActive = config.IsActive ?? false,
                    EffectiveDate = config.EffectiveDate,
                    ExpiryDate = config.ExpiryDate,
                    Description = config.Description,
                    CreateDate = config.CreateDate ?? DateTime.Now,
                    UpdateDate = config.UpdateDate ?? DateTime.Now
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取生效时间配置失败：{ex.Message}", ex);
                throw new ApiException($"获取生效时间配置失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 计算报告截止时间
        /// </summary>
        /// <param name="input">计算参数</param>
        /// <returns></returns>
        public VM_ReportDeadlineCalc_Out CalculateReportDeadline(VM_ReportDeadlineCalc_In input)
        {
            try
            {
                // 先获取时间配置
                var config = GetActiveTimeConfig(new VM_ActiveTimeConfig_In { ReportType = input.ReportType });
                
                // 使用真实的时间计算逻辑
                var regularTime = CalculateTimeFromConfig(input.ReportDate, config.RegularTimeValue, config.IgnoreHoliday, input.ReportType);
                var finalTime = CalculateTimeFromConfig(input.ReportDate, config.FinalTimeValue, config.IgnoreHoliday, input.ReportType);
                
                // 使用数据库配置的迟交时间
                var lateTime = CalculateTimeFromConfig(input.ReportDate, config.LateTimeValue, config.IgnoreHoliday, input.ReportType);

                var now = DateTime.Now;
                EnumReportTimeStatus currentStatus;
                string statusDescription;
                string remainingDescription;

                if (now <= regularTime)
                {
                    currentStatus = EnumReportTimeStatus.BeforeRegularTime;
                    statusDescription = EnumHelper.GetEnumDescription<EnumReportTimeStatus>(currentStatus);
                    var remaining = regularTime - now;
                    remainingDescription = FormatTimeRemaining(remaining);
                }
                else if (now <= lateTime)
                {
                    currentStatus = EnumReportTimeStatus.BetweenRegularAndLateTime;
                    statusDescription = EnumHelper.GetEnumDescription<EnumReportTimeStatus>(currentStatus);
                    var remaining = lateTime - now;
                    remainingDescription = "剩余" + FormatTimeRemaining(remaining);
                }
                else if (now <= finalTime)
                {
                    currentStatus = EnumReportTimeStatus.BetweenLateAndFinalTime;
                    statusDescription = EnumHelper.GetEnumDescription<EnumReportTimeStatus>(currentStatus);
                    var remaining = finalTime - now;
                    remainingDescription = "剩余" + FormatTimeRemaining(remaining);
                }
                else
                {
                    currentStatus = EnumReportTimeStatus.AfterFinalTime;
                    statusDescription = EnumHelper.GetEnumDescription<EnumReportTimeStatus>(currentStatus);
                    var overdue = now - finalTime;
                    remainingDescription = "已逾期" + FormatTimeRemaining(overdue);
                }

                return new VM_ReportDeadlineCalc_Out
                {
                    ReportType = input.ReportType,
                    ReportDate = input.ReportDate.ToString("yyyy-MM-dd"),
                    RegularTime = regularTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    LateTime = lateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    FinalTime = finalTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    CurrentStatus = currentStatus,
                    RemainingTimeDescription = remainingDescription
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"计算报告截止时间失败：{ex.Message}", ex);
                return new VM_ReportDeadlineCalc_Out
                {
                    ReportType = input.ReportType,
                    ReportDate = input.ReportDate.ToString("yyyy-MM-dd"),
                    RegularTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    LateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    FinalTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    CurrentStatus = EnumReportTimeStatus.AfterFinalTime,
                    RemainingTimeDescription = "无法计算"
                };
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取星期几的中文名称
        /// </summary>
        /// <param name="dayOfWeek">星期几</param>
        /// <returns></returns>
        private string GetDayOfWeekName(DayOfWeek dayOfWeek)
        {
            return dayOfWeek switch
            {
                DayOfWeek.Monday => "周一",
                DayOfWeek.Tuesday => "周二",
                DayOfWeek.Wednesday => "周三",
                DayOfWeek.Thursday => "周四",
                DayOfWeek.Friday => "周五",
                DayOfWeek.Saturday => "周六",
                DayOfWeek.Sunday => "周日",
                _ => "未知"
            };
        }

        /// <summary>
        /// 解析时间配置描述
        /// </summary>
        /// <param name="timeValue">时间配置JSON</param>
        /// <returns></returns>
        private string ParseTimeDescription(string timeValue)
        {
            try
            {
                if (string.IsNullOrEmpty(timeValue))
                    return "未配置";

                var config = JsonConvert.DeserializeObject<dynamic>(timeValue);
                var baseType = config?.baseType?.ToString();
                var hour = config?.hour?.ToString() ?? "0";
                var minute = config?.minute?.ToString() ?? "0";

                return baseType switch
                {
                    "current" => $"当日{hour}:{minute:D2}",
                    "next" => $"次工作日{hour}:{minute:D2}",
                    "week_end" => $"本周最后工作日{hour}:{minute:D2}",
                    "month_end" => $"本月最后工作日{hour}:{minute:D2}",
                    _ => "未知配置"
                };
            }
            catch
            {
                return "配置解析失败";
            }
        }

        /// <summary>
        /// 根据配置计算具体时间
        /// </summary>
        /// <param name="reportDate">报告日期</param>
        /// <param name="timeValue">时间配置JSON</param>
        /// <param name="ignoreHoliday">是否忽略节假日</param>
        /// <param name="reportType">报告类型</param>
        /// <returns></returns>
        private DateTime CalculateTimeFromConfig(DateTime reportDate, string timeValue, bool ignoreHoliday, EnumReportType reportType)
        {
            try
            {
                if (string.IsNullOrEmpty(timeValue))
                    return reportDate.Date.AddHours(17); // 默认17:00

                var config = JsonConvert.DeserializeObject<dynamic>(timeValue);
                var baseType = config?.baseType?.ToString();
                var dayOffset = int.Parse(config?.dayOffset?.ToString() ?? "0");
                var hour = int.Parse(config?.hour?.ToString() ?? "17");
                var minute = int.Parse(config?.minute?.ToString() ?? "0");
                var workdayOnly = bool.Parse(config?.workdayOnly?.ToString() ?? "false");

                DateTime baseDate;

                // 根据基准类型计算基准日期
                baseDate = baseType switch
                {
                    "current" => reportDate.Date,
                    "next" => reportDate.Date, // 次工作日从报告日期开始计算
                    "week_end" => GetWeekEndWorkday(reportDate),
                    "month_end" => GetMonthEndWorkday(reportDate),
                    _ => reportDate.Date
                };

                // 根据报告类型进行特殊处理
                if (baseType == "next")
                {
                    // 对于"next"类型，周报和月报的截止时间需要从周末/月末开始计算
                    if (reportType == EnumReportType.Weekly)
                    {
                        baseDate = GetWeekEndWorkday(reportDate);
                    }
                    else if (reportType == EnumReportType.Monthly)
                    {
                        baseDate = GetMonthEndWorkday(reportDate);
                    }
                    // 日报保持原有逻辑（从报告日期开始计算）
                }

                // 如果需要工作日，调整到工作日
                if (workdayOnly && !ignoreHoliday)
                {
                    var holidayDbOpe = DbOpe_crm_holiday.Instance;
                    // 如果baseType是"next"，需要从基准日期开始计算下一个工作日
                    if (baseType == "next")
                    {
                        baseDate = holidayDbOpe.GetNextWorkday(baseDate, dayOffset + 1);
                    }
                    else
                    {
                        // 先应用日期偏移，再调整到工作日
                        baseDate = baseDate.AddDays(dayOffset);
                        baseDate = holidayDbOpe.GetNextWorkday(baseDate.AddDays(-1), 1);
                    }
                }
                else
                {
                    // 不需要工作日调整时，直接应用日期偏移
                    baseDate = baseDate.AddDays(dayOffset);
                }

                // 设置时间
                return baseDate.AddHours(hour).AddMinutes(minute);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"计算时间配置失败：{ex.Message}", ex);
                return reportDate.Date.AddHours(17); // 默认17:00
            }
        }

        /// <summary>
        /// 获取本周最后一个工作日
        /// </summary>
        /// <param name="date">基准日期</param>
        /// <returns></returns>
        private DateTime GetWeekEndWorkday(DateTime date)
        {
            LogUtil.AddLog($"GetWeekEndWorkday - 输入日期: {date:yyyy-MM-dd}, 星期: {date.DayOfWeek}");
            
            // 找到本周日：如果今天是周日则是今天，否则计算到本周日还有几天
            var currentDayOfWeek = (int)date.DayOfWeek;
            var daysToSunday = currentDayOfWeek == 0 ? 0 : (7 - currentDayOfWeek);
            var sunday = date.Date.AddDays(daysToSunday);
            LogUtil.AddLog($"GetWeekEndWorkday - 计算出的周日日期: {sunday:yyyy-MM-dd}");

            // 从周日开始，向前查找本周的最后一个工作日
            var holidayDbOpe = DbOpe_crm_holiday.Instance;
            
            // 从周日往前找，找到第一个工作日就是本周的最后一个工作日
            for (int i = 0; i >= -6; i--)
            {
                var checkDate = sunday.AddDays(i);
                LogUtil.AddLog($"GetWeekEndWorkday - 检查日期: {checkDate:yyyy-MM-dd}, 星期: {checkDate.DayOfWeek}");
                
                if (holidayDbOpe.IsWorkday(checkDate))
                {
                    LogUtil.AddLog($"GetWeekEndWorkday - 找到本周最后一个工作日: {checkDate:yyyy-MM-dd}");
                    return checkDate;
                }
            }

            LogUtil.AddLog($"GetWeekEndWorkday - 未找到工作日，返回默认周日: {sunday:yyyy-MM-dd}");
            return sunday; // 默认返回周日
        }

        /// <summary>
        /// 获取本月最后一个工作日
        /// </summary>
        /// <param name="date">基准日期</param>
        /// <returns></returns>
        private DateTime GetMonthEndWorkday(DateTime date)
        {
            // 找到本月最后一天
            var lastDay = new DateTime(date.Year, date.Month, DateTime.DaysInMonth(date.Year, date.Month));

            // 从最后一天向前找工作日
            var holidayDbOpe = DbOpe_crm_holiday.Instance;
            for (int i = 0; i >= -10; i--)
            {
                var checkDate = lastDay.AddDays(i);
                if (holidayDbOpe.IsWorkday(checkDate))
                {
                    return checkDate;
                }
            }

            return lastDay; // 默认返回最后一天
        }





        /// <summary>
        /// 格式化剩余时间描述
        /// </summary>
        /// <param name="timeSpan">时间间隔</param>
        /// <returns></returns>
        private string FormatTimeRemaining(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
            {
                return $"{(int)timeSpan.TotalDays}天{timeSpan.Hours}小时";
            }
            else if (timeSpan.TotalHours >= 1)
            {
                return $"{timeSpan.Hours}小时{timeSpan.Minutes}分钟";
            }
            else
            {
                return $"{timeSpan.Minutes}分钟";
            }
        }

        #endregion
    }
}
