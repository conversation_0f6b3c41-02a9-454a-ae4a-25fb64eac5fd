﻿using CRM2_API.BLL;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 业绩管理控制器
    /// </summary>
    [Description("业绩管理控制器")]
    //[SkipAuthCheck]
    //[SkipRSAKey]
    public class ContractAchievementController : MyControllerBase
    {
        public ContractAchievementController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        /// <summary>
        /// 根据查询条件获取合同到账登记列表
        /// </summary>
        /// <param name="queryContractAchievement_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<QueryContractAchievement_OUT> SearchContractReceiptRegisterConfirmList(QueryContractAchievement_IN queryContractAchievement_IN)
        {
            return BLL_ContractAchievement.Instance.SearchContractReceiptRegisterConfirmList(queryContractAchievement_IN);
        }
        /// <summary>
        /// 确认合同到账登记业绩审核信息
        /// </summary>
        /// <param name="auditContractReceiptRegisterAchievement_IN"></param>
        [HttpPost]
        public void AuditContractReceiptRegisterAchievement(AuditContractReceiptRegisterAchievement_IN auditContractReceiptRegisterAchievement_IN)
        {
            BLL_ContractAchievement.Instance.AuditContractReceiptRegisterAchievement(auditContractReceiptRegisterAchievement_IN);
        }

        /// <summary>
        /// 批量确认合同到账登记业绩审核信息
        /// </summary>
        /// <param name="batchAuditContractReceiptRegisterAchievement_IN"></param>
        [HttpPost]
        public void BatchAuditContractReceiptRegisterAchievement(BatchAuditContractReceiptRegisterAchievement_IN batchAuditContractReceiptRegisterAchievement_IN)
        {
            BLL_ContractAchievement.Instance.BatchAuditContractReceiptRegisterAchievement(batchAuditContractReceiptRegisterAchievement_IN);
        }

        /// <summary>
        /// 撤销合同到账登记业绩审核信息(状态为确认、有疑问才可以)
        /// </summary>
        /// <param name="revokeAuditContractReceiptRegisterAchievement_IN"></param>
        [HttpPost]
        public void RevokeContractReceiptRegisterAchievement(RevokeAuditContractReceiptRegisterAchievement_IN revokeAuditContractReceiptRegisterAchievement_IN)
        {
            BLL_ContractAchievement.Instance.RevokeContractReceiptRegisterAchievement(revokeAuditContractReceiptRegisterAchievement_IN);
        }
        /// <summary>
        /// 根据合同Id获取到账登记的合同信息
        /// </summary>
        /// <param name="contractId"></param>
        [HttpPost]
        public ContractSimple_Out GetContractInfoByContractId(string contractId)
        {
            return BLL_ContractAchievement.Instance.GetContractInfoByContractId(contractId);
        }
        /// <summary>
        /// 根据到账登记表Id获取合同到账登记的合同信息和合同到账登记信息
        /// </summary>
        /// <param name="contractReceiptRegisterId"></param>
        /// <returns></returns>
        [HttpPost]
        public ContractWithReceiptRegister_Out GetContractInfoByContractReceiptRegisterId(string contractReceiptRegisterId)
        {
            return BLL_ContractAchievement.Instance.GetContractInfoByContractReceiptRegisterId(contractReceiptRegisterId);
        }
        /// <summary>
        /// 根据到账登记审核表id获取合同到账登记业绩审核确认信息
        /// </summary>
        /// <param name="contractReceiptRegisterAuditId"></param>
        [HttpPost]
        public ContractReceiptRegisterAchievementAudit_Out GetContractReceiptRegisterAchievementAuditById(string contractReceiptRegisterAuditId)
        {
            return BLL_ContractAchievement.Instance.GetContractReceiptRegisterAchievementAuditById(contractReceiptRegisterAuditId);
        }
        /// <summary>
        /// 根据到账登记表Id获取业绩确认详情信息（销售用）
        /// </summary>
        /// <param name="contractReceiptRegisterId"></param>
        /// <returns></returns>
        [HttpPost]
        public ContractReceiptRegisterInfo_Out GetInfoByContractReceiptRegisterId(string contractReceiptRegisterId)
        {
            return BLL_ContractAchievement.Instance.GetInfoByContractReceiptRegisterId(contractReceiptRegisterId,true);
        }
        /// <summary>
        /// 根据到账登记表Id获取业绩确认详情信息（销售用）(这个暂时未启用，替代上面那个)
        /// </summary>
        /// <param name="contractReceiptRegisterId"></param>
        /// <returns></returns>
        [HttpPost]
        public ContractReceiptRegisterInfoSales_Out GetInfoByContractReceiptRegisterId_Sales(string contractReceiptRegisterId)
        {
            return BLL_ContractAchievement.Instance.GetInfoByContractReceiptRegisterId_Sales(contractReceiptRegisterId);
        }
        /// <summary>
        /// （组织）客户数据-销售目标统计
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesTargetStatistics_OUT SalesTargetStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesTargetStatistics(salesDataStatistics_IN);
        }
        /// <summary>
        /// （个人-组织）客户数据-销售业绩 (包含同比环比 12.25 有效业绩)
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesDataStatistics_OUT SalesDataStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesDataStatistics(salesDataStatistics_IN);
        }
        /// <summary>
        /// （组织）客户数据-业绩排行
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesRankStatistics_OUT SalesOrgRankStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesOrgRankStatistics(salesDataStatistics_IN);
        }
        /// <summary>
        /// （个人）客户数据-业绩排行
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesRankStatistics_OUT SalesUserRankStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesUserRankStatistics(salesDataStatistics_IN);
        }
        /// <summary>
        /// 客户数据-产品业绩
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesProductStatistics_OUT SalesProductStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesProductStatistics(salesDataStatistics_IN);
        }
        /// <summary>
        /// 客户数据-合同统计
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesContractStatistics_OUT SalesContractStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesContractStatistics(salesDataStatistics_IN);
        }
        /// <summary>
        /// 客户数据-客户统计
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public CustomerStatistics_OUT CustomerStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.CustomerStatistics(salesDataStatistics_IN);
        }
        /// <summary>
        /// 客户数据-跟踪统计
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public TrackRecordStatistics_OUT TrackRecordStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.TrackRecordStatistics(salesDataStatistics_IN);
        }
        /// <summary>
        /// 客户数据-跟踪排行
        /// </summary>
        /// <param name="salesDataStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]

        public TrackingRecordRankStatistics_OUT TrackingRecordRankStatistics(SalesDataStatistics_IN salesDataStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.TrackingRecordRankStatistics(salesDataStatistics_IN);
        }

        /// <summary>
        /// 销售分析-客户（按合同计算：新增合同和续约合同都各自算一个新客户）地域分布
        /// </summary>
        /// <param name="salesAnalyseStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesCitysStatistics_OUT SalesCitysStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesCitysStatistics(salesAnalyseStatistics_IN);
        }
        /// <summary>
        /// 销售分析-客户（签过合同且未作废的算签约客户）地域分布-国家详情
        /// </summary>
        /// <param name="salesCountryAnalyseStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesCountryDetailAnalyseStatistics_OUT SalesCountryDetailAnalyseStatistics(SalesCountryAnalyseStatistics_IN salesCountryAnalyseStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesCountryDetailAnalyseStatistics(salesCountryAnalyseStatistics_IN);
        }
        /// <summary>
        /// 销售分析-客户（签过合同且未作废的算签约客户）地域分布-国家排行
        /// </summary>
        /// <param name="salesCountryAnalyseStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesCountryRankAnalyseStatistics_OUT SalesCountryRankAnalyseStatistics(SalesCountryAnalyseStatistics_IN salesCountryAnalyseStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesCountryRankAnalyseStatistics(salesCountryAnalyseStatistics_IN);
        }
        ///// <summary>
        ///// 销售分析-销售漏斗
        ///// </summary>
        ///// <param name="salesAnalyseStatistics_IN"></param>
        ///// <returns></returns>
        //[HttpPost]
        //public SalesFunnelStatistics_OUT SalesFunnelStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        //{
        //    return BLL_ContractAchievement.Instance.SalesFunnelStatistics(salesAnalyseStatistics_IN);
        //}
        /// <summary>
        /// 销售分析-到账客户（）来源
        /// </summary>
        /// <param name="salesAnalyseStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesReciveCustomerSourceStatistics_OUT SalesReciveCustomerSourceStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesReciveCustomerSourceStatistics(salesAnalyseStatistics_IN);
        }
        /// <summary>
        /// 销售分析-到账客户（）合同转化率
        /// </summary>
        /// <param name="salesAnalyseStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesReciveContractTypeTransStatistics_OUT SalesReciveContractTypeTransStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesReciveContractTypeTransStatistics(salesAnalyseStatistics_IN);
        }
        /// <summary>
        /// 销售分析-到账客户（）来源转化率
        /// </summary>
        /// <param name="salesAnalyseStatistics_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesReciveCustomerSourceTransStatistics_OUT SalesReciveCustomerSourceTransStatistics(SalesAnalyseStatistics_IN salesAnalyseStatistics_IN)
        {
            return BLL_ContractAchievement.Instance.SalesReciveCustomerSourceTransStatistics(salesAnalyseStatistics_IN);
        }

    }
}
