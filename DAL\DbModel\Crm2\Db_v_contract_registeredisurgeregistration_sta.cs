﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///VIEW
    ///</summary>
    [SugarTable("v_contract_registeredisurgeregistration_sta")]
    public class Db_v_contract_registeredisurgeregistration_sta
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] Id {get;set;}

           /// <summary>
           /// Desc:客户编号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractNum { get; set; }

           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Remark { get; set; }

           /// <summary>
           /// Desc:保护截止日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ProtectionDeadline { get; set; }

           /// <summary>
           /// Desc:合同号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractNo {get;set;}

           /// <summary>
           /// Desc:合同名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractName {get;set;}

           /// <summary>
           /// Desc:合同金额
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? ContractAmount {get;set;}

           /// <summary>
           /// Desc:外币合同金额
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? FCContractAmount {get;set;}

           /// <summary>
           /// Desc:合同金额转换
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? ContractAmountConvert { get; set; }

           /// <summary>
           /// Desc:币种
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? Currency {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int State {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string PaymentCompanyName {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int IsReceipt {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int PaymentType {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string CollectingCompanyName {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int PaymentMethod {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int ArrivalAmount {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int BankPaymentAmount {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int CashPaymentAmount {get;set;}

           /// <summary>
           /// Desc:承兑
           /// Default:
           /// Nullable:True
           /// </summary>      
           public decimal? Honour { get; set; }

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] ReturnPerformance {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] RefundItems { get; set; }

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] OtheritemsPerformance { get; set; }

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] ArrivalDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int IsSecret {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] DeductPerformance {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] SalesPerformance {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] EffectivePerformance {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string IssuerName {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string ReviewerName {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] ReviewerDate {get;set;}

           /// <summary>
           /// Desc:出单人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string Issuer {get;set;}

           /// <summary>
           /// Desc:战队
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgDivisionName {get;set;}

           /// <summary>
           /// Desc:大队
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgBrigadeName {get;set;}

           /// <summary>
           /// Desc:中队
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgRegimentName {get;set;}

           /// <summary>
           /// Desc:战队Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgDivisionId { get; set; }

           /// <summary>
           /// Desc:大队Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgBrigadeId { get; set; }

           /// <summary>
           /// Desc:中队Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OrgRegimentId { get; set; }

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string ContractId {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public byte[] CollectionInfoAutoMatchingId {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int Type {get;set;}

           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public long IsUrgeRegistration {get;set;}

           /// <summary>
           /// Desc:归属月份
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? BelongingMonth { get; set; }

           /// <summary>
           /// Desc:是否结汇
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? IsSettlementExchange { get; set; }

           /// <summary>
           /// Desc:签约日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? SigningDate { get; set; }

           /// <summary>
           /// Desc:是否境外客户
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? IsOverseasCustomer { get; set; }

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string IsIsRepeatedReceiptCustomerId { get; set; }

           /// <summary>
           /// Desc:出单人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string RepeatedReceiptIssuer { get; set; }

           /// <summary>
           /// Desc:出单人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string RepeatedReceiptIssuerName { get; set; }

           /// <summary>
           /// Desc:战队
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractOrgDivisionName { get; set; }

           /// <summary>
           /// Desc:大队
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractOrgBrigadeName { get; set; }

           /// <summary>
           /// Desc:中队
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractOrgRegimentName { get; set; }

           /// <summary>
           /// Desc:战队Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractOrgDivisionId { get; set; }

           /// <summary>
           /// Desc:大队Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractOrgBrigadeId { get; set; }

           /// <summary>
           /// Desc:中队Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ContractOrgRegimentId { get; set; }

           /// <summary>
           /// Desc:收款银行
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CollectingBankName { get; set; }

           /// <summary>
           /// Desc:修改备注
           /// Default:
           /// Nullable:True
           /// </summary>    
           public string UpdateRemark { get; set; }

           /// <summary>
           /// Desc:是否代付款
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? IsBehalfPayment { get; set; }

           /// <summary>
           /// Desc:国家
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? Country { get; set; }

           /// <summary>
           /// Desc:国家
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CountryName { get; set; }

           /// <summary>
           /// 环球搜结算单位
           /// </summary>
           public decimal? GlobalSearchUnit { get; set; }
    }
}
