﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Common.JWT;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.TrackingRecord;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using SqlSugar;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Linq.Dynamic.Core.Tokenizer;
using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    [Description("工作报告控制器")]
    public class WorkReportsController : MyControllerBase
    {
        public WorkReportsController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }


        #region 日报[HttpPost,SkipRightCheck]
        /// <summary>
        /// 根据查询条件获取工作日报列表。
        /// </summary>
        /// <param name="searchDailyWorkReportListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchDailyWorkReportList_Out> SearchDailyWorkReportList(SearchWorkReportList_In searchDailyWorkReportListIn)
        {
            int total = 0;
            List<string> roleUserList = BLL_Product.Instance.GetOrgUserByTokenUserId();
            //查询全部不做处理
            //查询自己发布的将查询条件修改为查询发布者为自己的
            //查询团队的将自己从roleuser中去掉（暂定）
            //默认全部
            switch (searchDailyWorkReportListIn.IsTeamOrMe)
            {
                case 0:
                    break;
                case 1:
                    searchDailyWorkReportListIn.UserId = UserTokenInfo.id;
                    break;
                case 2:
                    roleUserList.Remove(UserTokenInfo.id);
                    break;
                default: break;

            }
            var list = DbOpe_crm_daily_work_report.Instance.SearchDailyWorkReportList(searchDailyWorkReportListIn, roleUserList, UserTokenInfo.id, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 根据工作日报Id获取工作日报信息。
        /// </summary>
        /// <param name="Id_In"></param>
        /// <returns></returns>
        [HttpPost]
        public DailyWorkReport GetDailyWorkReportById(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            return DbOpe_crm_daily_work_report.Instance.GetDailyWorkReportById(Id);
        }

        /// <summary>
        /// 根据用户Id获取工作日报信息提交列表。获取该用户组织下用户已提交报告和未提交报告的信息。
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<WorkReportSubmitStateByUserId> GetDailyWorkReportSubmitStateByUserId()
        {
            return DbOpe_crm_daily_work_report.Instance.GetDailyWorkReportSubmitStateByUserId();
        }

        /// <summary>
        ///添加工作日报信息。
        /// </summary>
        /// <param name="addDailyWorkReportIn"></param>
        [HttpPost]
        public WorkReportActionResult AddDailyWorkReport([FromForm] AddDailyWorkReport_In addDailyWorkReportIn)
        {
            return BLL_WorkReports.Instance.AddDailyWorkReport(addDailyWorkReportIn);
        }
        /// <summary>
        /// 修改工作日报信息。
        /// </summary>
        /// <param name="updateDailyWorkReportIn"></param>
        [HttpPost]
        public WorkReportActionResult UpdateDailyWorkReport([FromForm] UpdateDailyWorkReport_In updateDailyWorkReportIn)
        {
            return BLL_WorkReports.Instance.UpdateDailyWorkReport(updateDailyWorkReportIn);
        }

        /// <summary>
        /// 删除工作日报信息，当发布状态为草稿、已发送、已查阅时，可操作删除。
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void DeleteDailyWorkReport(WorkReportParameter_In Id_In)
        {
            List<string> IdList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_daily_work_report.Instance.DeleteDailyWorkReport(IdList);
        }

        /// <summary>
        /// 撤销工作日报信息，当发布状态为已发送时，可撤销，支持多选操作。验证数据权限
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void RevokeDailyWorkReport(WorkReportParameter_In Id_In)
        {
            List<string> idList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_daily_work_report.Instance.RevokeDailyWorkReport(idList);
        }

        /// <summary>
        /// 设置已读工作日报信息。验证数据权限。
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void SetIsReadDailyWorkReport(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            DbOpe_crm_daily_work_report.Instance.SetIsReadDailyWorkReport(Id);
        }


        #endregion
        #region 周报[HttpPost,SkipRightCheck]
        /// <summary>
        /// 根据查询条件获取工作周报列表。
        /// </summary>
        /// <param name="searchWeekWorkReportListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchWeekWorkReportList_Out> SearchWeekWorkReportList(SearchWorkReportList_In searchWeekWorkReportListIn)
        {
            int total = 0;
            List<string> roleUserList = BLL_Product.Instance.GetOrgUserByTokenUserId();
            //查询全部不做处理
            //查询自己发布的将查询条件修改为查询发布者为自己的
            //查询团队的将自己从roleuser中去掉（暂定）
            //默认全部
            switch (searchWeekWorkReportListIn.IsTeamOrMe)
            {
                case 0:
                    break;
                case 1:
                    searchWeekWorkReportListIn.UserId = UserTokenInfo.id;
                    break;
                case 2:
                    roleUserList.Remove(UserTokenInfo.id);
                    break;
                default: break;

            }
            var list = DbOpe_crm_week_work_report.Instance.SearchWeekWorkReportList(searchWeekWorkReportListIn, roleUserList, UserTokenInfo.id, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据工作周报Id获取工作周报信息。
        /// </summary>
        /// <param name="Id_In"></param>
        /// <returns></returns>
        [HttpPost]
        public WeekWorkReport GetWeekWorkReportById(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            return DbOpe_crm_week_work_report.Instance.GetWeekWorkReportById(Id);
        }

        /// <summary>
        /// 根据用户Id获取工作周报信息提交列表。获取该用户组织下用户已提交报告和未提交报告的信息。
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<WorkReportSubmitStateByUserId> GetWeekWorkReportSubmitStateByUserId()
        {
            return DbOpe_crm_week_work_report.Instance.GetWeekWorkReportSubmitStateByUserId();
        }

        /// <summary>
        /// 添加工作周报信息。
        /// </summary>
        /// <param name="addWeekWorkReportIn"></param>
        [HttpPost]
        public WorkReportActionResult AddWeekWorkReport([FromForm] AddWeekWorkReport_In addWeekWorkReportIn)
        {
            return BLL_WorkReports.Instance.AddWeekWorkReport(addWeekWorkReportIn);
        }
        /// <summary>
        /// 修改工作周报信息。
        /// </summary>
        /// <param name="updateWeekWorkReportIn"></param>
        [HttpPost]
        public WorkReportActionResult UpdateWeekWorkReport([FromForm] UpdateWeekWorkReport_In updateWeekWorkReportIn)
        {
            return BLL_WorkReports.Instance.UpdateWeekWorkReport(updateWeekWorkReportIn);
        }


        /// <summary>
        /// 删除工作周报信息，当发布状态为草稿、已发送、已查阅时，可操作删除
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void DeleteWeekWorkReport(WorkReportParameter_In Id_In)
        {
            List<string> IdList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_week_work_report.Instance.DeleteWeekWorkReport(IdList);
        }

        /// <summary>
        /// 撤销工作周报信息，当发布状态为已发送时，可撤销，支持多选操作。验证数据权限
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void RevokeWeekWorkReport(WorkReportParameter_In Id_In)
        {
            List<string> idList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_week_work_report.Instance.RevokeWeekWorkReport(idList);
        }

        /// <summary>
        /// 设置已读工作周报信息。验证数据权限。
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void SetIsReadWeekWorkReport(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            DbOpe_crm_week_work_report.Instance.SetIsReadWeekWorkReport(Id);
        }
        #endregion
        #region 月报[HttpPost,SkipRightCheck]
        /// <summary>
        /// 根据查询条件获取工作月报列表。
        /// </summary>
        /// <param name="searchMonthWorkReportListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchMonthWorkReportList_Out> SearchMonthWorkReportList(SearchWorkReportList_In searchMonthWorkReportListIn)
        {
            int total = 0;
            List<string> roleUserList = BLL_Product.Instance.GetOrgUserByTokenUserId();
            //查询全部不做处理
            //查询自己发布的将查询条件修改为查询发布者为自己的
            //查询团队的将自己从roleuser中去掉（暂定）
            //默认全部
            switch (searchMonthWorkReportListIn.IsTeamOrMe)
            {
                case 0:
                    break;
                case 1:
                    searchMonthWorkReportListIn.UserId = UserTokenInfo.id;
                    break;
                case 2:
                    roleUserList.Remove(UserTokenInfo.id);
                    break;
                default: break;

            }
            var list = DbOpe_crm_month_work_report.Instance.SearchMonthWorkReportList(searchMonthWorkReportListIn, roleUserList, UserTokenInfo.id, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据工作月报Id获取工作月报信息。
        /// </summary>
        /// <param name="Id_In"></param>
        /// <returns></returns>
        [HttpPost]
        public MonthWorkReport GetMonthWorkReportById(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            return DbOpe_crm_month_work_report.Instance.GetMonthWorkReportById(Id);
        }

        /// <summary>
        /// 根据用户Id获取工作月报信息提交列表。获取该用户组织下用户已提交报告和未提交报告的信息。
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<WorkReportSubmitStateByUserId> GetMonthWorkReportSubmitStateByUserId()
        {
            return DbOpe_crm_month_work_report.Instance.GetMonthWorkReportSubmitStateByUserId();
        }

        /// <summary>
        /// 添加工作月报信息。
        /// </summary>
        /// <param name="addMonthWorkReportIn"></param>
        [HttpPost]
        public WorkReportActionResult AddMonthWorkReport([FromForm] AddMonthWorkReport_In addMonthWorkReportIn)
        {
            return BLL_WorkReports.Instance.AddMonthWorkReport(addMonthWorkReportIn);
        }
        /// <summary>
        /// 修改工作月报信息。保存草稿：添加工作月报信息，状态为草稿。新建：添加工作月报信息，状态为已发布。
        /// </summary>
        /// <param name="updateMonthWorkReportIn"></param>
        [HttpPost]
        public WorkReportActionResult UpdateMonthWorkReport([FromForm] UpdateMonthWorkReport_In updateMonthWorkReportIn)
        {
            return BLL_WorkReports.Instance.UpdateMonthWorkReport(updateMonthWorkReportIn);
        }

        /// <summary>
        /// 删除工作月报信息，当发布状态为草稿、已发送、已查阅时，可操作删除。
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void DeleteMonthWorkReport(WorkReportParameter_In Id_In)
        {
            List<string> IdList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_month_work_report.Instance.DeleteMonthWorkReport(IdList);
        }

        /// <summary>
        /// 撤销工作月报信息，当发布状态为已发送时，可撤销，支持多选操作。验证数据权限
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void RevokeMonthWorkReport(WorkReportParameter_In Id_In)
        {
            List<string> idList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_month_work_report.Instance.RevokeMonthWorkReport(idList);
        }

        /// <summary>
        /// 设置已读工作月报信息。验证数据权限。
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void SetIsReadMonthWorkReport(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            DbOpe_crm_month_work_report.Instance.SetIsReadMonthWorkReport(Id);
        }

        #endregion
        #region 管理日报[HttpPost,SkipRightCheck]
        /// <summary>
        /// 根据查询条件获取管理日报列表。
        /// </summary>
        /// <param name="searchManagerDailyWorkReportListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchManagerDailyWorkReportList_Out> SearchManagerDailyWorkReportList(SearchWorkReportList_In searchManagerDailyWorkReportListIn)
        {
            int total = 0;
            List<string> roleUserList = BLL_Product.Instance.GetOrgUserByTokenUserId();
            //查询全部不做处理
            //查询自己发布的将查询条件修改为查询发布者为自己的
            //查询团队的将自己从roleuser中去掉（暂定）
            //默认全部
            switch (searchManagerDailyWorkReportListIn.IsTeamOrMe)
            {
                case 0:
                    break;
                case 1:
                    searchManagerDailyWorkReportListIn.UserId = UserTokenInfo.id;
                    break;
                case 2:
                    roleUserList.Remove(UserTokenInfo.id);
                    break;
                default: break;

            }
            var list = DbOpe_crm_manager_daily_work_report.Instance.SearchManagerDailyWorkReportList(searchManagerDailyWorkReportListIn, roleUserList, UserTokenInfo.id, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据管理日报Id获取管理日报信息。
        /// </summary>
        /// <param name="Id_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ManagerDailyWorkReport GetManagerDailyWorkReportById(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            return DbOpe_crm_manager_daily_work_report.Instance.GetManagerDailyWorkReportById(Id);
        }

        /// <summary>
        /// 根据用户Id获取管理日报信息提交列表。获取该用户组织下用户已提交报告和未提交报告的信息。
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<WorkReportSubmitStateByUserId> GetManagerDailyWorkReportSubmitStateByUserId()
        {
            return DbOpe_crm_manager_daily_work_report.Instance.GetManagerDailyWorkReportSubmitStateByUserId();
        }

        /// <summary>
        /// 添加工作管理日报信息。
        /// </summary>
        /// <param name="addManagerDailyWorkReportIn"></param>
        [HttpPost]
        public WorkReportActionResult AddManagerDailyWorkReport([FromForm] AddManagerDailyWorkReport_In addManagerDailyWorkReportIn)
        {
            return BLL_WorkReports.Instance.AddManagerDailyWorkReport(addManagerDailyWorkReportIn);
        }
        /// <summary>
        /// 修改工作管理日报信息。
        /// </summary>
        /// <param name="updateManagerDailyWorkReportIn"></param>
        [HttpPost]
        public WorkReportActionResult UpdateManagerDailyWorkReport([FromForm] UpdateManagerDailyWorkReport_In updateManagerDailyWorkReportIn)
        {
            return BLL_WorkReports.Instance.UpdateManagerDailyWorkReport(updateManagerDailyWorkReportIn);
        }
        /// <summary>
        /// 删除管理工作日报信息，当发布状态为草稿、已发送、已查阅时，可操作删除。
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void DeleteManagerDailyWorkReport(WorkReportParameter_In Id_In)
        {
            List<string> IdList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_manager_daily_work_report.Instance.DeleteManagerDailyWorkReport(IdList);
        }

        /// <summary>
        /// 撤销管理工作日报信息，当发布状态为已发送时，可撤销，支持多选操作。验证数据权限
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void RevokeManagerManagerDailyWorkReport(WorkReportParameter_In Id_In)
        {
            List<string> idList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_manager_daily_work_report.Instance.RevokeManagerDailyWorkReport(idList);
        }

        /// <summary>
        /// 设置已读管理工作日报信息。验证数据权限。
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void SetIsReadManagerDailyWorkReport(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            DbOpe_crm_manager_daily_work_report.Instance.SetIsReadManagerDailyWorkReport(Id);
        }
        #endregion
        #region 工作简报[HttpPost,SkipRightCheck]
        /// <summary>
        /// 根据查询条件获取工作简报列表。
        /// </summary>
        /// <param name="searchBriefReportListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchBriefReportList_Out> SearchBriefReportList(SearchWorkReportList_In searchBriefReportListIn)
        {
            int total = 0;
            List<string> roleUserList = BLL_Product.Instance.GetOrgUserByTokenUserId();
            //查询全部不做处理
            //查询自己发布的将查询条件修改为查询发布者为自己的
            //查询团队的将自己从roleuser中去掉（暂定）
            //默认全部
            switch (searchBriefReportListIn.IsTeamOrMe)
            {
                case 0:
                    break;
                case 1:
                    searchBriefReportListIn.UserId = UserTokenInfo.id;
                    break;
                case 2:
                    roleUserList.Remove(UserTokenInfo.id);
                    break;
                default: break;

            }
            var list = DbOpe_crm_brief_report.Instance.SearchBriefReportList(searchBriefReportListIn, roleUserList, UserTokenInfo.id, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据工作简报Id获取工作简报信息。
        /// </summary>
        /// <param name="Id_In"></param>
        /// <returns></returns>
        [HttpPost]
        public BriefReport GetBriefReportById(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            return DbOpe_crm_brief_report.Instance.GetBriefReportById(Id);
        }

        /// <summary>
        /// 根据用户Id获取工作简报信息提交列表。获取该用户组织下用户已提交报告和未提交报告的信息。
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<WorkReportSubmitStateByUserId> GetBriefReportSubmitStateByUserId()
        {
            return DbOpe_crm_brief_report.Instance.GetBriefReportSubmitStateByUserId();
        }
        /// <summary>
        /// 添加工作简报信息。
        /// </summary>
        /// <param name="addBriefReportIn"></param>
        [HttpPost]
        public WorkReportActionResult AddBriefReport([FromForm] AddBriefReport_In addBriefReportIn)
        {
            return BLL_WorkReports.Instance.AddBriefReport(addBriefReportIn);
        }
        /// <summary>
        /// 修改工作简报信息。
        /// </summary>
        /// <param name="updateBriefReportIn"></param>
        [HttpPost]
        public WorkReportActionResult UpdateBriefReport([FromForm] UpdateBriefReport_In updateBriefReportIn)
        {
            return BLL_WorkReports.Instance.UpdateBriefReport(updateBriefReportIn);
        }

        /// <summary>
        /// 删除工作简报信息
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void DeleteBriefReport(WorkReportParameter_In Id_In)
        {
            List<string> IdList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_brief_report.Instance.DeleteBriefReport(IdList);
        }

        /// <summary>
        /// 撤销工作简报信息，当发布状态为已发送时，可撤销，支持多选操作。验证数据权限
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void RevokeBriefReport(WorkReportParameter_In Id_In)
        {
            List<string> idList = Id_In.Ids.Split(',').ToList();
            DbOpe_crm_brief_report.Instance.RevokeBriefReport(idList);
        }

        /// <summary>
        /// 设置已读工作简报信息。验证数据权限。
        /// </summary>
        /// <param name="Id_In"></param>
        [HttpPost]
        public void SetIsReadBriefReport(WorkReportParameter_In Id_In)
        {
            string Id = Id_In.Ids.ToString();
            DbOpe_crm_brief_report.Instance.SetIsReadBriefReport(Id);
        }
        #endregion
        #region 报告通用型接口 跟踪信息获取/管理者获取

        /// <summary>
        /// 获取工作报告最新追踪信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<CustomerTraceRecordStage2_Out> GetCustomerTraceRecord(WorkReportTypeParameter_In reportsType)
        {
            //return BLL_WorkReports.Instance.GetCustomerTraceRecord(UserTokenInfo.id, reportsType.ReportsType);
            return DbOpe_crm_daily_work_report.Instance.GetTrackingRecordByUserIdState2(UserTokenInfo.id, reportsType.ReportsType);
        }
        /// <summary>
        /// 获取工作报告最新追踪信息
        /// </summary>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public List<CustomerTraceRecordStage2_Out> GetCustomerTraceRecordStage2(WorkReportTypeParameter_In reportsType)
        {
            return DbOpe_crm_daily_work_report.Instance.GetTrackingRecordByUserIdState2(UserTokenInfo.id, reportsType.ReportsType);
            //return BLL_WorkReports.Instance.GetCustomerTraceRecord("5bb52b03-2476-4fc8-b7df-66844727e5ec", reportsType.ReportsType);
        }
        /// <summary>
        /// 获取周报统计数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public WeekWorkReport WeekWorkReportStatistics()
        {
            return BLL_WorkReports.Instance.WeekWorkReportStatistics();
        }

        /// <summary>
        /// 获取月报统计数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public MonthWorkReport MonthWorkReportStatistics()
        {
            return BLL_WorkReports.Instance.MonthWorkReportStatistics();
        }

        /// <summary>
        /// 获取管理日报统计数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ManagerDailyWorkReport ManagerReportStatistics()
        {
            return BLL_WorkReports.Instance.ManagerReportStatistics();
        }

        ///// <summary>
        ///// 获取日报最新追踪信息
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //public List<CustomerTraceRecord_Out> GetDailyCustomerTraceRecord()
        //{
        //    return BLL_WorkReports.Instance.GetCustomerTraceRecord(UserTokenInfo.id, EnumWorkReportsType.Daily);
        //}

        ///// <summary>
        ///// 获取周报最新追踪信息
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //public List<CustomerTraceRecord_Out> GetWeekCustomerTraceRecord()
        //{
        //    return BLL_WorkReports.Instance.GetCustomerTraceRecord(UserTokenInfo.id, EnumWorkReportsType.Week);
        //}


        ///// <summary>
        ///// 获取月报最新追踪信息
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //public List<CustomerTraceRecord_Out> GetMonthCustomerTraceRecord()
        //{
        //    return BLL_WorkReports.Instance.GetCustomerTraceRecord(UserTokenInfo.id, EnumWorkReportsType.Month);
        //}

        /// <summary>
        /// 根据当前登陆人员获取上级管理者ID及名字
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<GetParentManagerUser> GetParentManagerUsers()
        {
            //List<GetParentManagerUser> ParentManager = DbOpe_sys_user.Instance.GetParentManagerUsers(UserTokenInfo.id);
            //List<GetParentManagerUser> ChildManager = DbOpe_sys_user.Instance.GetParentManagerUsers(UserTokenInfo.id, false);

            ////List<GetParentManagerUser> allManger = ParentManager.Union(ChildManager).ToList();
            //List<GetParentManagerUser> allManger = ParentManager;

            //foreach (var child in ChildManager)
            //{
            //    bool isNotAdd = ParentManager.Select(e => e.Id).ToList().Contains(child.Id);
            //    if (!isNotAdd)
            //    {
            //        allManger.Add(child);
            //    }
            //}


            return DbOpe_sys_user.Instance.GetParentManagerUsers(UserTokenInfo.id);
            //return allManger;
        }
        /// <summary>
        /// 获取工作报告接收人(树形结构)
        /// </summary>
        /// <param name="workReportsType"></param>
        /// <returns></returns>
        [HttpGet]
        public List<GetOrganizationWithUserTree_Out> GetReportReciverUsers(EnumWorkReportsType workReportsType)
        {
            //return BLL_WorkReports.Instance.GetReportReciver(workReportsType);

            return BLL_Organization.Instance.GetSelfAndChildOrgWithUserTree();
        }

        #endregion
    }
}
