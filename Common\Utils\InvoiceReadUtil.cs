﻿using System.Drawing;
using System.IO;
using System.Text.RegularExpressions;
using UglyToad.PdfPig;
using UglyToad.PdfPig.Content;
using UglyToad.PdfPig.Writer;
using ZXing;
using System.Threading.Tasks;
using System.Text;
using System.Collections.Generic;
using System;
using System.Linq;
using Newtonsoft.Json;
using UglyToad.PdfPig.Core;
using System.Drawing.Imaging;
using System.Drawing.Drawing2D;
using ZXing.Common;
using ZXing.QrCode;
using ZXing.Windows.Compatibility;
// 添加SixLabors.ImageSharp相关命名空间
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Png;

namespace CRM2_API.Common.Utils
{
    public class InvoiceReadCol { 
        public InvoiceReadCol() { }
        public InvoiceReadCol(string _InvoiceNo,string _InvoiceDate,string _InvoiceDetails, string _InvoiceAmount) {
            InvoiceAmount = (_InvoiceAmount == null || _InvoiceAmount == "?" )? "" : Math.Abs(Convert.ToDecimal(_InvoiceAmount)).ToString();
            InvoiceNo = _InvoiceNo;
            InvoiceDate = _InvoiceDate;
            InvoiceDetails = _InvoiceDetails;
        }
        public string? InvoiceAmount { get; set; }
        public string? InvoiceDetails { get; set; }
        public string? InvoiceNo { get; set; }
        public string? InvoiceDate { get; set; }
    }
    public static class InvoiceReadUtil
    {
        
        /// <summary>
        /// 正则匹配大写金额
        /// [壹贰叁肆伍陆柒捌玖拾]\s?[零壹贰叁肆伍陆柒捌玖拾佰仟万亿整元圆角分\s]+[整元圆角分]
        /// </summary>
        private static readonly Regex amountRegex = new Regex(@"[壹贰叁肆伍陆柒捌玖拾]\s?[零壹贰叁肆伍陆柒捌玖拾佰仟万亿整元圆角分\s]+[整元圆角分]", RegexOptions.Compiled);

        /// <summary>
        /// 正则匹配年月日
        /// 开票日期[:：]\s*\d{4}年\d{2}月\d{2}日
        /// </summary>
        private static readonly Regex dateRegex = new Regex(@"\d{4}年\d{2}月\d{2}日", RegexOptions.Compiled);

        /// <summary>
        /// 匹配发票号码
        /// </summary>
        private static readonly Regex noRegex = new Regex(@"发票号码[:：]?\s*(\d+)", RegexOptions.Compiled);

        /// <summary>
        /// 直接匹配完整发票号
        /// </summary>
        private static readonly Regex fullInvoiceNumberRegex = new Regex(@"(\d{20})", RegexOptions.Compiled);

        /// <summary>
        /// 匹配类目
        /// 匹配到第一个，然后去除两边的*号
        /// </summary>
        private static readonly Regex typeRegex = new Regex(@"\*.*?\*", RegexOptions.Compiled);

        /// <summary>
        /// 匹配金额
        /// </summary>
        private static readonly Regex amountRegex2 = new Regex(@"[¥￥]\s*([0-9]+[.][0-9]{2})", RegexOptions.Compiled);

        /// <summary>
        /// 匹配购买方名称
        /// </summary>
        private static readonly Regex buyerNameRegex = new Regex(@"名称[:：]?\s*([^\r\n]+)", RegexOptions.Compiled);

        /// <summary>
        /// 匹配销售方名称
        /// </summary>
        private static readonly Regex sellerNameRegex = new Regex(@"名称[:：]?\s*([^\r\n]+)", RegexOptions.Compiled);
        
        /// <summary>
        /// 匹配纳税人识别号
        /// </summary>
        private static readonly Regex taxNoRegex = new Regex(@"纳税人识别号[:：]?\s*([^\r\n]+)", RegexOptions.Compiled);
        
        /// <summary>
        /// 匹配统一社会信用代码
        /// </summary>
        private static readonly Regex creditCodeRegex = new Regex(@"统一社会信用代码[/／纳税人识别号]*[:：]?\s*([0-9A-Z]+)", RegexOptions.Compiled);
                
        /// <summary>
        /// 匹配价税合计
        /// </summary>
        private static readonly Regex totalAmountRegex = new Regex(@"价税合计[:：]?\s*[（(]?小写[)）]?\s*[¥￥]?\s*([0-9,.]+)", RegexOptions.Compiled);

        /// <summary>
        /// 使用ZXing.Net识别图像中的二维码，使用SixLabors.ImageSharp实现
        /// </summary>
        /// <param name="imageBytes">图像字节数组</param>
        /// <returns>识别出的二维码文本，如果识别失败则返回null</returns>
        private static string RecognizeQrCodeWithZXing(byte[] imageBytes)
        {
            try
            {
                using (MemoryStream ms = new MemoryStream(imageBytes))
                {
                    // 使用SixLabors.ImageSharp加载图像
                    // 使用完全限定的类型名称避免命名冲突
                    using (SixLabors.ImageSharp.Image<Rgba32> image = SixLabors.ImageSharp.Image.Load<Rgba32>(ms))
                    {
                        // 创建与System.Drawing.Bitmap兼容的LuminanceSource
                        // 通过构建一个兼容ZXing的RGB数组
                        byte[] rgbData = new byte[image.Width * image.Height * 3];
                        int index = 0;
                        
                        // 将ImageSharp图像转换为RGB字节数组
                        image.ProcessPixelRows(accessor =>
                        {
                            for (int y = 0; y < accessor.Height; y++)
                            {
                                var row = accessor.GetRowSpan(y);
                                for (int x = 0; x < accessor.Width; x++)
                                {
                                    rgbData[index++] = row[x].R;
                                    rgbData[index++] = row[x].G;
                                    rgbData[index++] = row[x].B;
                                }
                            }
                        });

                        // 创建ZXing的RGBLuminanceSource
                        RGBLuminanceSource luminanceSource = new RGBLuminanceSource(rgbData, image.Width, image.Height);
                        
                        // 创建二进制位图
                        BinaryBitmap binaryBitmap = new BinaryBitmap(new HybridBinarizer(luminanceSource));

                        // 设置解码选项
                        var hints = new Dictionary<DecodeHintType, object>
                        {
                            { DecodeHintType.TRY_HARDER, true },
                            { DecodeHintType.POSSIBLE_FORMATS, new List<BarcodeFormat> { BarcodeFormat.QR_CODE } }
                        };

                        // 创建QR码识别器
                        QRCodeReader reader = new QRCodeReader();
                        
                        // 尝试解码
                        var result = reader.decode(binaryBitmap, hints);
                        if (result != null)
                        {
                            LogUtil.AddLog($"ZXing.Net成功识别二维码: {result.Text}");
                            return result.Text;
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"使用ZXing.Net识别二维码失败: {ex.Message}");
                return null;
            }
        }

        // 处理分析单个PDF文件
        public static async Task<InvoiceReadCol> AnalyzePdfQRCode(byte[] bytes)
        {
            InvoiceReadCol invoiceReadCol = new InvoiceReadCol();
            using (PdfDocument document = PdfDocument.Open(bytes))
            {
                Page firstPage = document.GetPages().FirstOrDefault();
                if (firstPage != null)
                {
                    // 处理二维码
                    var images = firstPage.GetImages().Where(i => i.HeightInSamples == i.WidthInSamples && i.WidthInSamples > 100 && i.HeightInSamples > 100);
                    var firstImage = images.FirstOrDefault();
                    if (firstImage != null)
                    {
                        // 使用重构后的方法获取图像 
                        var imageBytes = ConvertPdfImageToBytes(firstImage);
                        
                        // 获取图像字节数据
                        firstImage.TryGetPng(out byte[] pngBytes);
                        
                        if (pngBytes != null)
                        {
                            // 使用ZXing.Net本地识别
                            string qrCodeText = RecognizeQrCodeWithZXing(pngBytes);
                            
                            // // 如果本地识别失败，则回退到UmiOcrService
                            // if (string.IsNullOrEmpty(qrCodeText))
                            // {
                            //     LogUtil.AddLog("本地识别二维码失败，尝试使用外部服务识别");
                            //     var qrCodeResult = await UmiOcrService.Instance.RecognizeQrCodeBase64Async(Convert.ToBase64String(pngBytes));
                                
                            //     // 处理二维码结果
                            //     if (qrCodeResult != null && qrCodeResult.code == 100 && qrCodeResult.data != null && 
                            //         qrCodeResult.data.qrcode != null && qrCodeResult.data.qrcode.Count > 0)
                            //     {
                            //         qrCodeText = qrCodeResult.data.qrcode[0].text;
                            //     }
                            // }
                            
                            var result = qrCodeText ?? "0,99,?,?,?,?,?";
                            if (result != null)
                            {
                                string[] values = result.Split(',');
                                if (values.Length > 6)
                                {
                                    // 格式化日期统一
                                    var invoiceDate = values[5].Contains("-") || values[5] == "?" ? values[5] : values[5].Insert(4, "-").Insert(7, "-");

                                    invoiceReadCol = new InvoiceReadCol(values[3], invoiceDate, "", values[4]);
                                }
                                else
                                {
                                    invoiceReadCol = new InvoiceReadCol("?", "?", "?", "?");
                                }

                                // 处理文字内容，解决非31,32数电的发票
                                var text = firstPage.Text;
                                if (values[1] != "31" && values[1] != "32")
                                {
                                    var matches = amountRegex2.Matches(text);
                                    // 取所有符合条件的金额中最大的
                                    decimal amount = 0;
                                    foreach (Match match in matches)
                                    {
                                        var value = Convert.ToDecimal(match.Groups[1].Value);
                                        if (value > amount)
                                        {
                                            amount = value;
                                        }
                                    }
                                    invoiceReadCol.InvoiceAmount = Math.Abs(amount).ToString();
                                }

                                // 处理类目
                                var typeMatch = typeRegex.Match(text);
                                if (typeMatch.Success)
                                {
                                    var type = typeMatch.Value.Trim('*');
                                    invoiceReadCol.InvoiceDetails= type;
                                }
                                // 处理发票号码
                                if (invoiceReadCol.InvoiceNo.ToString() == "?")
                                {
                                    var noMatch = noRegex.Match(text);
                                    if (noMatch.Success)
                                    {
                                        var no = noMatch.Groups[1].Value;
                                        invoiceReadCol.InvoiceNo = no;
                                    }
                                }
                                // 开票日期
                                if (invoiceReadCol.InvoiceDate.ToString() == "?")
                                {
                                    var dateMatch = dateRegex.Match(text);
                                    if (dateMatch.Success)
                                    {
                                        var date = dateMatch.Value;
                                        // 修改日期格式
                                        date = date.Replace("年", "-").Replace("月", "-").Replace("日", "");
                                        invoiceReadCol.InvoiceDate = date;
                                    }
                                }

                            }
                        }
                    }

                }
            }
            return invoiceReadCol;
        }

        /// <summary>
        /// 将PDF图像转换为字节数组，使用SixLabors.ImageSharp代替System.Drawing.Bitmap
        /// </summary>
        /// <param name="pdfImage">PDF图像</param>
        /// <returns>图像字节数组</returns>
        static byte[] ConvertPdfImageToBytes(IPdfImage pdfImage)
        {
            try
            {
                // 获取图像数据和尺寸
                pdfImage.TryGetPng(out byte[] pngBytes);
                if (pngBytes != null && pngBytes.Length > 0)
                {
                    return pngBytes;
                }

                // 如果无法直接获取PNG，则创建一个新的图像
                int width = pdfImage.WidthInSamples;
                int height = pdfImage.HeightInSamples;
                
                // 创建一个新的ImageSharp图像
                using (var image = new SixLabors.ImageSharp.Image<Rgba32>(width, height))
                {
                    // 获取像素数据
                    var pixelData = pdfImage.RawBytes;
                    
                    // 填充图像数据
                    image.ProcessPixelRows(accessor =>
                    {
                        for (int y = 0; y < height; y++)
                        {
                            var row = accessor.GetRowSpan(y);
                            for (int x = 0; x < width; x++)
                            {
                                int index = (y * width + x) * pdfImage.BitsPerComponent / 8;
                                if (index + 2 < pixelData.Count) // 使用Count而不是Length
                                {
                                    // 假设图像是RGB格式
                                    row[x] = new Rgba32(
                                        pixelData[index],      // R
                                        pixelData[index + 1],  // G
                                        pixelData[index + 2],  // B
                                        255                    // A
                                    );
                                }
                            }
                        }
                    });

                    // 将图像保存为PNG字节数组
                    using (var memoryStream = new MemoryStream())
                    {
                        image.Save(memoryStream, new PngEncoder());
                        return memoryStream.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"转换PDF图像失败: {ex.Message}");
                return null;
            }
        }

        // 原方法保持不变，但不再使用
        static Bitmap ConvertPdfImageToBitmap(IPdfImage pdfImage)
        {
            try
            {
                int width = pdfImage.WidthInSamples;
                int height = pdfImage.HeightInSamples;
                Bitmap bitmap = new Bitmap(width, height);
                
                // 获取图像数据
                var pixelData = pdfImage.RawBytes;
                
                // 设置像素数据
                for (int y = 0; y < height; y++)
                {
                    for (int x = 0; x < width; x++)
                    {
                        int index = (y * width + x) * pdfImage.BitsPerComponent / 8;
                        
                        if (index + 2 < pixelData.Count) // 使用Count而不是Length
                        {
                            // 使用完全限定的类型名称
                            bitmap.SetPixel(x, y, System.Drawing.Color.FromArgb(
                                pixelData[index],
                                pixelData[index + 1],
                                pixelData[index + 2]
                            ));
                        }
                    }
                }
                
                return bitmap;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"转换PDF图像失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 直接从PDF提取发票信息，不使用OCR
        /// </summary>
        /// <param name="pdfBytes">PDF文件字节数组</param>
        /// <returns>发票信息对象</returns>
        public static Task<UmiOcrService.OcrInvoiceInfo> ExtractInvoiceInfoFromPdf(byte[] pdfBytes)
        {
            try
            {
                if (pdfBytes == null || pdfBytes.Length == 0)
                {
                    return Task.FromResult(new UmiOcrService.OcrInvoiceInfo { Remark = "PDF数据不能为空" });
                }
                
                // 1. 从PDF直接提取文本
                string extractedText = ExtractTextFromPdf(pdfBytes);
                LogUtil.AddLog($"提取的PDF文本内容长度: {extractedText?.Length ?? 0}");
                
                // 记录前200个字符用于调试
                if (!string.IsNullOrEmpty(extractedText) && extractedText.Length > 0)
                {
                    int logLength = Math.Min(extractedText.Length, 300);
                    LogUtil.AddLog($"前{logLength}个字符: {extractedText.Substring(0, logLength)}");
                }
                
                // 2. 检查文本是否包含足够的发票信息
                if (string.IsNullOrEmpty(extractedText) || 
                    (!extractedText.Contains("发票") && !extractedText.Contains("购买方") && !extractedText.Contains("销售方")))
                {
                    // 如果文本提取失败，返回错误信息
                    return Task.FromResult(new UmiOcrService.OcrInvoiceInfo 
                    { 
                        Remark = "无法从PDF直接提取有效发票信息，文本内容不包含发票关键信息"
                    });
                }
                
                // 3. 先尝试直接用专用增值税发票解析逻辑处理
                if (extractedText.Contains("增值税专用发票") || extractedText.Contains("增值税普通发票"))
                {
                    var specialInvoiceInfo = ParseSpecialInvoiceFormat(extractedText);
                    if (specialInvoiceInfo != null && !string.IsNullOrEmpty(specialInvoiceInfo.InvoiceNumber) && 
                        (!string.IsNullOrEmpty(specialInvoiceInfo.BuyerName) || !string.IsNullOrEmpty(specialInvoiceInfo.SellerName)))
                    {
                        LogUtil.AddLog("使用增值税专用发票格式成功解析");
                        specialInvoiceInfo.OriginalText = extractedText;
                        return Task.FromResult(specialInvoiceInfo);
                    }
                }
                
                // 4. 使用通用文本解析
                var invoiceInfo = new UmiOcrService.OcrInvoiceInfo { OriginalText = extractedText };
                
                // 解析发票号码
                var invoiceNumberMatch = noRegex.Match(extractedText);
                if (invoiceNumberMatch.Success)
                {
                    invoiceInfo.InvoiceNumber = invoiceNumberMatch.Groups[1].Value.Trim();
                    LogUtil.AddLog($"提取到发票号码: {invoiceInfo.InvoiceNumber}");
                }
                else
                {
                    // 尝试匹配完整的20位发票号
                    var fullNumberMatch = fullInvoiceNumberRegex.Match(extractedText);
                    if (fullNumberMatch.Success)
                    {
                        invoiceInfo.InvoiceNumber = fullNumberMatch.Groups[1].Value.Trim();
                        LogUtil.AddLog($"提取到完整发票号码: {invoiceInfo.InvoiceNumber}");
                    }
                }
                
                // 解析日期
                var dateMatch = dateRegex.Match(extractedText);
                if (dateMatch.Success)
                {
                    string dateStr = dateMatch.Value.Replace("年", "-").Replace("月", "-").Replace("日", "");
                    if (DateTime.TryParse(dateStr, out DateTime date))
                    {
                        invoiceInfo.InvoiceDate = date;
                        LogUtil.AddLog($"提取到开票日期: {date:yyyy-MM-dd}");
                    }
                }
                
                // 解析购买方和销售方信息
                // 尝试提取区域
                var buyerSection = ExtractSectionBetween(extractedText, "购买方", "销售方");
                var sellerSection = ExtractSectionBetween(extractedText, "销售方", "项目名称");
                
                // 提取购买方信息
                if (!string.IsNullOrEmpty(buyerSection))
                {
                    // 购买方名称
                    var buyerNameMatch = buyerNameRegex.Match(buyerSection);
                    if (buyerNameMatch.Success)
                    {
                        invoiceInfo.BuyerName = buyerNameMatch.Groups[1].Value.Trim();
                        LogUtil.AddLog($"提取到购买方名称: {invoiceInfo.BuyerName}");
                    }
                    
                    // 购买方税号
                    var buyerTaxMatch = creditCodeRegex.Match(buyerSection);
                    if (buyerTaxMatch.Success)
                    {
                        invoiceInfo.BuyerTaxNumber = buyerTaxMatch.Groups[1].Value.Trim();
                        LogUtil.AddLog($"提取到购买方税号: {invoiceInfo.BuyerTaxNumber}");
                    }
                }
                
                // 提取销售方信息
                if (!string.IsNullOrEmpty(sellerSection))
                {
                    // 销售方名称
                    var sellerNameMatch = sellerNameRegex.Match(sellerSection);
                    if (sellerNameMatch.Success)
                    {
                        invoiceInfo.SellerName = sellerNameMatch.Groups[1].Value.Trim();
                        LogUtil.AddLog($"提取到销售方名称: {invoiceInfo.SellerName}");
                    }
                    
                    // 销售方税号
                    var sellerTaxMatch = creditCodeRegex.Match(sellerSection);
                    if (sellerTaxMatch.Success)
                    {
                        invoiceInfo.SellerTaxNumber = sellerTaxMatch.Groups[1].Value.Trim();
                        LogUtil.AddLog($"提取到销售方税号: {invoiceInfo.SellerTaxNumber}");
                    }
                }
                
                // 提取金额信息
                // 价税合计
                var totalAmountMatch = totalAmountRegex.Match(extractedText);
                if (totalAmountMatch.Success)
                {
                    string amountStr = totalAmountMatch.Groups[1].Value.Replace(",", "");
                    if (decimal.TryParse(amountStr, out decimal amount))
                    {
                        invoiceInfo.TotalAmountWithTax = amount;
                        LogUtil.AddLog($"提取到价税合计: {amount}");
                    }
                }
                
                // 大写金额
                var chineseAmountMatch = amountRegex.Match(extractedText);
                if (chineseAmountMatch.Success)
                {
                    invoiceInfo.AmountInWords = chineseAmountMatch.Value.Trim();
                    LogUtil.AddLog($"提取到大写金额: {invoiceInfo.AmountInWords}");
                }
                
                // 提取商品信息
                var typeMatch = typeRegex.Match(extractedText);
                if (typeMatch.Success)
                {
                    string itemName = typeMatch.Value.Trim('*');
                    var item = new UmiOcrService.OcrInvoiceItem { Name = itemName };
                    invoiceInfo.Items.Add(item);
                    LogUtil.AddLog($"提取到商品项目: {itemName}");
                }
                
                // 5. 如果关键信息缺失，尝试更详细的解析或补充
                if (string.IsNullOrEmpty(invoiceInfo.BuyerName) || string.IsNullOrEmpty(invoiceInfo.InvoiceNumber))
                {
                    EnhanceInvoiceInfoFromPdf(pdfBytes, ref invoiceInfo);
                }
                
                // 6. 如果信息仍然不足，返回可能不完整的结果，但添加说明
                if ((string.IsNullOrEmpty(invoiceInfo.BuyerName) && string.IsNullOrEmpty(invoiceInfo.SellerName)) || 
                    (invoiceInfo.TotalAmountWithTax == 0 && invoiceInfo.TotalAmount == 0))
                {
                    LogUtil.AddLog("PDF文本提取的信息不足");
                    invoiceInfo.Remark = "通过PDF文本直接提取，但信息可能不完整";
                }
                else
                {
                    invoiceInfo.Remark = "通过PDF文本直接提取";
                }
                
                return Task.FromResult(invoiceInfo);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"从PDF提取发票信息时发生错误: {ex.Message}");
                
                // 出现异常时，返回错误信息
                return Task.FromResult(new UmiOcrService.OcrInvoiceInfo
                {
                    Remark = $"提取PDF文本时发生错误: {ex.Message}"
                });
            }
        }
        
        /// <summary>
        /// 专门解析增值税专用发票的结构化文本
        /// </summary>
        private static UmiOcrService.OcrInvoiceInfo ParseSpecialInvoiceFormat(string extractedText)
        {
            try
            {
                LogUtil.AddLog("开始使用专用解析逻辑处理增值税专用发票");
                
                var invoiceInfo = new UmiOcrService.OcrInvoiceInfo();
                
                // 1. 提取发票号码 - 两种方式
                // 1.1 直接匹配数字序列
                var invoiceNumberMatch = new Regex(@"(\d{20}|\d{10,12})").Match(extractedText);
                if (invoiceNumberMatch.Success)
                {
                    invoiceInfo.InvoiceNumber = invoiceNumberMatch.Groups[1].Value.Trim();
                    LogUtil.AddLog($"通过序列匹配提取到发票号码: {invoiceInfo.InvoiceNumber}");
                }
                
                // 1.2 匹配"发票号码："后面的内容
                if (string.IsNullOrEmpty(invoiceInfo.InvoiceNumber))
                {
                    var invoiceNumberMatch2 = new Regex(@"发票号码[:：]?\s*(\d+)").Match(extractedText);
                    if (invoiceNumberMatch2.Success)
                    {
                        invoiceInfo.InvoiceNumber = invoiceNumberMatch2.Groups[1].Value.Trim();
                        LogUtil.AddLog($"通过标签匹配提取到发票号码: {invoiceInfo.InvoiceNumber}");
                    }
                }
                
                // 2. 提取开票日期
                var dateMatch = new Regex(@"(\d{4}年\d{1,2}月\d{1,2}日)").Match(extractedText);
                if (dateMatch.Success)
                {
                    string dateStr = dateMatch.Groups[1].Value
                        .Replace("年", "-").Replace("月", "-").Replace("日", "");
                    if (DateTime.TryParse(dateStr, out DateTime date))
                    {
                        invoiceInfo.InvoiceDate = date;
                        LogUtil.AddLog($"提取到开票日期: {date:yyyy-MM-dd}");
                    }
                }
                
                // 3. 提取购买方名称和税号
                // 寻找"购买方"附近的公司名称
                var buyerSection = ExtractSectionBetween(extractedText, "购买方", "销售方");
                if (!string.IsNullOrEmpty(buyerSection))
                {
                    LogUtil.AddLog($"提取到购买方区域: {buyerSection}");
                    
                    // 3.1 提取购买方名称
                    var buyerNameMatch = new Regex(@"名称[:：]?\s*([^\r\n]+)").Match(buyerSection);
                    if (buyerNameMatch.Success)
                    {
                        invoiceInfo.BuyerName = buyerNameMatch.Groups[1].Value.Trim();
                        LogUtil.AddLog($"提取到购买方名称: {invoiceInfo.BuyerName}");
                    }
                    
                    // 3.2 提取购买方税号
                    var buyerTaxMatch = new Regex(@"统一社会信用代码[/／纳税人识别号]*[:：]?\s*([0-9A-Z]+)").Match(buyerSection);
                    if (buyerTaxMatch.Success)
                    {
                        invoiceInfo.BuyerTaxNumber = buyerTaxMatch.Groups[1].Value.Trim();
                        LogUtil.AddLog($"提取到购买方税号: {invoiceInfo.BuyerTaxNumber}");
                    }
                }
                else
                {
                    // 备选方案：如果没有找到明确的购买方区域，尝试直接匹配公司名称
                    // 由于样例中公司名称格式较为固定，可以采用更直接的方式
                    var companyMatches = new Regex(@"(?:名称[:：]?\s*)([^统一社会\r\n]{5,})").Matches(extractedText);
                    if (companyMatches.Count >= 1)
                    {
                        invoiceInfo.BuyerName = companyMatches[0].Groups[1].Value.Trim();
                        LogUtil.AddLog($"备选方式提取到购买方名称: {invoiceInfo.BuyerName}");
                        
                        if (companyMatches.Count >= 2)
                        {
                            invoiceInfo.SellerName = companyMatches[1].Groups[1].Value.Trim();
                            LogUtil.AddLog($"备选方式提取到销售方名称: {invoiceInfo.SellerName}");
                        }
                    }
                    
                    // 提取税号
                    var taxMatches = new Regex(@"(?:统一社会信用代码[/／纳税人识别号]*[:：]?\s*)([0-9A-Z]{15,})").Matches(extractedText);
                    if (taxMatches.Count >= 1)
                    {
                        invoiceInfo.BuyerTaxNumber = taxMatches[0].Groups[1].Value.Trim();
                        LogUtil.AddLog($"备选方式提取到购买方税号: {invoiceInfo.BuyerTaxNumber}");
                        
                        if (taxMatches.Count >= 2)
                        {
                            invoiceInfo.SellerTaxNumber = taxMatches[1].Groups[1].Value.Trim();
                            LogUtil.AddLog($"备选方式提取到销售方税号: {invoiceInfo.SellerTaxNumber}");
                        }
                    }
                }
                
                // 4. 提取销售方名称和税号
                var sellerSection = ExtractSectionBetween(extractedText, "销售方", "项目名称");
                if (!string.IsNullOrEmpty(sellerSection))
                {
                    LogUtil.AddLog($"提取到销售方区域: {sellerSection}");
                    
                    // 4.1 提取销售方名称
                    var sellerNameMatch = new Regex(@"名称[:：]?\s*([^\r\n]+)").Match(sellerSection);
                    if (sellerNameMatch.Success)
                    {
                        invoiceInfo.SellerName = sellerNameMatch.Groups[1].Value.Trim();
                        LogUtil.AddLog($"提取到销售方名称: {invoiceInfo.SellerName}");
                    }
                    
                    // 4.2 提取销售方税号
                    var sellerTaxMatch = new Regex(@"统一社会信用代码[/／纳税人识别号]*[:：]?\s*([0-9A-Z]+)").Match(sellerSection);
                    if (sellerTaxMatch.Success)
                    {
                        invoiceInfo.SellerTaxNumber = sellerTaxMatch.Groups[1].Value.Trim();
                        LogUtil.AddLog($"提取到销售方税号: {invoiceInfo.SellerTaxNumber}");
                    }
                }
                
                // 5. 提取商品信息
                ExtractItemsForSpecialInvoice(extractedText, invoiceInfo);
                
                // 6. 提取金额信息
                ExtractAmountsForSpecialInvoice(extractedText, invoiceInfo);
                
                // 7. 提取备注和开票人
                var drawerMatch = new Regex(@"开票人[:：]?\s*([^\r\n]+)").Match(extractedText);
                if (drawerMatch.Success)
                {
                    invoiceInfo.Drawer = drawerMatch.Groups[1].Value.Trim();
                    LogUtil.AddLog($"提取到开票人: {invoiceInfo.Drawer}");
                }
                
                // 8. 记录发票类型
                if (extractedText.Contains("增值税专用发票"))
                {
                    invoiceInfo.InvoiceType = "增值税专用发票";
                }
                else if (extractedText.Contains("增值税普通发票"))
                {
                    invoiceInfo.InvoiceType = "增值税普通发票";
                }
                else
                {
                    invoiceInfo.InvoiceType = "电子发票";
                }
                
                return invoiceInfo;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"解析增值税专用发票时出错: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 从两个关键词之间提取文本部分
        /// </summary>
        private static string ExtractSectionBetween(string fullText, string startKeyword, string endKeyword)
        {
            try
            {
                int startIndex = fullText.IndexOf(startKeyword);
                if (startIndex < 0)
                {
                    // 尝试带空格的版本
                    startIndex = fullText.IndexOf(startKeyword.Replace("", " "));
                    if (startIndex < 0) return string.Empty;
                }
                
                int endIndex = fullText.IndexOf(endKeyword, startIndex + startKeyword.Length);
                if (endIndex < 0)
                {
                    // 尝试带空格的版本
                    endIndex = fullText.IndexOf(endKeyword.Replace("", " "), startIndex + startKeyword.Length);
                    if (endIndex < 0)
                    {
                        // 如果找不到结束关键词，取一个合理的长度
                        endIndex = Math.Min(startIndex + 300, fullText.Length);
                    }
                }
                
                return fullText.Substring(startIndex, endIndex - startIndex);
            }
            catch
            {
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 为增值税专用发票提取商品项目信息
        /// </summary>
        private static void ExtractItemsForSpecialInvoice(string fullText, UmiOcrService.OcrInvoiceInfo invoiceInfo)
        {
            try
            {
                // 1. 先尝试提取带星号的服务名称
                var starItemMatch = new Regex(@"\*([^*]+)\*").Match(fullText);
                if (starItemMatch.Success)
                {
                    string itemName = starItemMatch.Groups[1].Value.Trim();
                    
                    var item = new UmiOcrService.OcrInvoiceItem { Name = itemName };
                    
                    // 尝试提取税率
                    var taxRateMatch = new Regex(@"(\d+)[%％]").Match(fullText);
                    if (taxRateMatch.Success)
                    {
                        if (decimal.TryParse(taxRateMatch.Groups[1].Value, out decimal taxRate))
                        {
                            item.TaxRate = taxRate / 100.0m; // 转换为小数形式
                            LogUtil.AddLog($"提取到税率: {taxRate}%");
                        }
                    }
                    
                    // 查找项目区域 - 从项目名称到合计之间
                    var itemsSection = ExtractSectionBetween(fullText, "项目名称", "合计");
                    if (!string.IsNullOrEmpty(itemsSection))
                    {
                        LogUtil.AddLog($"提取到项目区域: {itemsSection}");
                        
                        // 尝试提取金额和税额
                        // 在样例中，金额通常是以¥开头的数字
                        var amountMatches = new Regex(@"[¥￥]\s*([0-9,.]+)").Matches(itemsSection);
                        if (amountMatches.Count >= 1)
                        {
                            string amountStr = amountMatches[0].Groups[1].Value.Replace(",", "");
                            if (decimal.TryParse(amountStr, out decimal amount))
                            {
                                item.Amount = amount;
                                LogUtil.AddLog($"提取到商品金额: {amount}");
                            }
                            
                            if (amountMatches.Count >= 2)
                            {
                                string taxStr = amountMatches[1].Groups[1].Value.Replace(",", "");
                                if (decimal.TryParse(taxStr, out decimal tax))
                                {
                                    item.Tax = tax;
                                    LogUtil.AddLog($"提取到商品税额: {tax}");
                                }
                            }
                        }
                    }
                    
                    invoiceInfo.Items.Add(item);
                    LogUtil.AddLog($"提取到商品: {itemName}");
                }
                else
                {
                    // 2. 如果没有找到带星号的项目，尝试直接从"项目名称"之后提取内容
                    var itemSection = ExtractSectionBetween(fullText, "项目名称", "合计");
                    if (!string.IsNullOrEmpty(itemSection))
                    {
                        // 将整段文本按行分割
                        var lines = itemSection.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                        if (lines.Length > 1) // 跳过表头
                        {
                            string firstLine = lines[1].Trim();
                            // 从行中提取第一个单词作为商品名称
                            var words = firstLine.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                            if (words.Length > 0)
                            {
                                string itemName = words[0].Trim();
                                var item = new UmiOcrService.OcrInvoiceItem { Name = itemName };
                                
                                // 尝试从行中提取数字作为金额
                                var amountMatches = new Regex(@"([0-9,.]+)").Matches(firstLine);
                                if (amountMatches.Count >= 2) // 假设至少有单价和金额
                                {
                                    if (decimal.TryParse(amountMatches[amountMatches.Count - 2].Value.Replace(",", ""), 
                                                        out decimal amount))
                                    {
                                        item.Amount = amount;
                                    }
                                    
                                    if (decimal.TryParse(amountMatches[amountMatches.Count - 1].Value.Replace(",", ""), 
                                                        out decimal tax))
                                    {
                                        item.Tax = tax;
                                    }
                                }
                                
                                invoiceInfo.Items.Add(item);
                                LogUtil.AddLog($"从项目行提取到商品: {itemName}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提取商品项目信息时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 为增值税专用发票提取金额信息
        /// </summary>
        private static void ExtractAmountsForSpecialInvoice(string fullText, UmiOcrService.OcrInvoiceInfo invoiceInfo)
        {
            try
            {
                // 1. 提取价税合计
                var totalWithTaxMatch = new Regex(@"[¥￥]\s*([0-9,.]+\.[0-9]{2})(?!\d)").Matches(fullText);
                if (totalWithTaxMatch.Count > 0)
                {
                    // 最后一个金额通常是价税合计
                    string totalStr = totalWithTaxMatch[totalWithTaxMatch.Count - 1].Groups[1].Value.Replace(",", "");
                    if (decimal.TryParse(totalStr, out decimal total))
                    {
                        invoiceInfo.TotalAmountWithTax = total;
                        LogUtil.AddLog($"提取到价税合计: {total}");
                    }
                }
                
                // 2. 提取合计金额和税额
                var totalSection = ExtractSectionBetween(fullText, "合计", "价税合计");
                if (!string.IsNullOrEmpty(totalSection))
                {
                    var amountMatches = new Regex(@"[¥￥]?\s*([0-9,.]+\.[0-9]{2})").Matches(totalSection);
                    if (amountMatches.Count >= 1)
                    {
                        string amountStr = amountMatches[0].Groups[1].Value.Replace(",", "");
                        if (decimal.TryParse(amountStr, out decimal amount))
                        {
                            invoiceInfo.TotalAmount = amount;
                            LogUtil.AddLog($"提取到合计金额: {amount}");
                        }
                        
                        if (amountMatches.Count >= 2)
                        {
                            string taxStr = amountMatches[1].Groups[1].Value.Replace(",", "");
                            if (decimal.TryParse(taxStr, out decimal tax))
                            {
                                invoiceInfo.TotalTax = tax;
                                LogUtil.AddLog($"提取到合计税额: {tax}");
                            }
                        }
                    }
                }
                
                // 3. 提取价税合计大写金额
                var chineseAmountMatch = new Regex(@"[（(]?大写[)）]?\s*([零壹贰叁肆伍陆柒捌玖拾佰仟万亿元角分整点零一二三四五六七八九十百千万亿元角分整点]+)").Match(fullText);
                if (chineseAmountMatch.Success)
                {
                    invoiceInfo.AmountInWords = chineseAmountMatch.Groups[1].Value.Trim();
                    LogUtil.AddLog($"提取到大写金额: {invoiceInfo.AmountInWords}");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提取金额信息时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 直接从PDF提取文本内容
        /// </summary>
        /// <param name="pdfBytes">PDF文件字节数组</param>
        /// <returns>提取的文本内容</returns>
        private static string ExtractTextFromPdf(byte[] pdfBytes)
        {
            var textBuilder = new StringBuilder();
            
            using (var memoryStream = new MemoryStream(pdfBytes))
            using (var document = PdfDocument.Open(memoryStream))
            {
                foreach (var page in document.GetPages())
                {
                    // 添加页面文本
                    textBuilder.AppendLine(page.Text);
                    
                    // 尝试提取页面上的表格数据
                    var tableData = ExtractTableFromPage(page);
                    if (!string.IsNullOrEmpty(tableData))
                    {
                        textBuilder.AppendLine(tableData);
                    }
                }
            }
            
            return textBuilder.ToString();
        }
        
        /// <summary>
        /// 尝试从PDF页面中提取表格数据
        /// </summary>
        /// <param name="page">PDF页面</param>
        /// <returns>表格数据文本</returns>
        private static string ExtractTableFromPage(Page page)
        {
            // 简单表格提取方法：按Y坐标对齐的文本可能是表格的同一行
            var words = page.GetWords().ToList();
            
            // 按Y坐标排序并分组
            var rowGroups = words
                .GroupBy(w => Math.Round(w.BoundingBox.Bottom, 1)) // 四舍五入Y坐标以合并相近的行
                .OrderByDescending(g => g.Key); // 从上到下排序
                
            var tableBuilder = new StringBuilder();
            
            foreach (var row in rowGroups)
            {
                // 按X坐标排序，从左到右
                var sortedWords = row.OrderBy(w => w.BoundingBox.Left).ToList();
                
                // 对于每一行，将单词组合成一行
                if (sortedWords.Count > 0)
                {
                    tableBuilder.AppendLine(string.Join(" ", sortedWords.Select(w => w.Text)));
                }
            }
            
            return tableBuilder.ToString();
        }
        
        /// <summary>
        /// 从PDF更详细地提取发票信息
        /// </summary>
        /// <param name="pdfBytes">PDF文件字节数组</param>
        /// <param name="invoiceInfo">已有的发票信息，将被更新</param>
        private static void EnhanceInvoiceInfoFromPdf(byte[] pdfBytes, ref UmiOcrService.OcrInvoiceInfo invoiceInfo)
        {
            try
            {
                using (var memoryStream = new MemoryStream(pdfBytes))
                using (var document = PdfDocument.Open(memoryStream))
                {
                    var page = document.GetPage(1); // 发票通常在第一页
                    string fullText = page.Text;
                    
                    LogUtil.AddLog("开始增强发票信息提取...");
                    
                    // 优先尝试匹配20位发票号码
                    if (string.IsNullOrEmpty(invoiceInfo.InvoiceNumber))
                    {
                        var fullNoMatch = fullInvoiceNumberRegex.Match(fullText);
                        if (fullNoMatch.Success)
                        {
                            invoiceInfo.InvoiceNumber = fullNoMatch.Groups[1].Value.Trim();
                            LogUtil.AddLog($"已提取完整发票号码: {invoiceInfo.InvoiceNumber}");
                        }
                        else
                        {
                            // 尝试常规发票号码匹配
                            var noMatch = noRegex.Match(fullText);
                            if (noMatch.Success)
                            {
                                invoiceInfo.InvoiceNumber = noMatch.Groups[1].Value.Trim();
                                LogUtil.AddLog($"已提取发票号码: {invoiceInfo.InvoiceNumber}");
                            }
                        }
                    }
                    
                    // 提取开票日期
                    if (invoiceInfo.InvoiceDate == null)
                    {
                        // 直接匹配带年月日格式的日期
                        var dateMatch = dateRegex.Match(fullText);
                        if (dateMatch.Success)
                        {
                            string dateString = dateMatch.Value.Replace("年", "-").Replace("月", "-").Replace("日", "");
                            if (DateTime.TryParse(dateString, out DateTime date))
                            {
                                invoiceInfo.InvoiceDate = date;
                                LogUtil.AddLog($"已提取开票日期: {date.ToString("yyyy-MM-dd")}");
                            }
                        }
                        else
                        {
                            // 尝试匹配"开票日期："后面的内容
                            var datePattern = new Regex(@"开票日期[:：]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}[-/]\d{1,2}[-/]\d{1,2})", RegexOptions.Compiled);
                            var dateMatch2 = datePattern.Match(fullText);
                            if (dateMatch2.Success)
                            {
                                string dateString = dateMatch2.Groups[1].Value
                                    .Replace("年", "-").Replace("月", "-").Replace("日", "");
                                if (DateTime.TryParse(dateString, out DateTime date))
                                {
                                    invoiceInfo.InvoiceDate = date;
                                    LogUtil.AddLog($"已提取开票日期(备选方式): {date.ToString("yyyy-MM-dd")}");
                                }
                            }
                        }
                    }
                    
                    // 提取购买方和销售方信息
                    ExtractBuyerAndSellerInfo(fullText, ref invoiceInfo);
                    
                    // 提取商品项目信息
                    ExtractItemsInfo(fullText, ref invoiceInfo);
                    
                    // 补充金额信息
                    ExtractAmountInfo(fullText, ref invoiceInfo);
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"增强发票信息时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 提取购买方和销售方信息
        /// </summary>
        private static void ExtractBuyerAndSellerInfo(string fullText, ref UmiOcrService.OcrInvoiceInfo invoiceInfo)
        {
            try
            {
                // 尝试定位购买方和销售方信息区域
                int buyerIndex = -1;
                int sellerIndex = -1;
                
                // 查找购买方区域的多种可能标识
                string[] buyerIndicators = { "购买方", "购 买 方", "买方" };
                foreach (var indicator in buyerIndicators)
                {
                    buyerIndex = fullText.IndexOf(indicator);
                    if (buyerIndex >= 0) break;
                }
                
                // 查找销售方区域的多种可能标识
                string[] sellerIndicators = { "销售方", "销 售 方", "售方" };
                foreach (var indicator in sellerIndicators)
                {
                    sellerIndex = fullText.IndexOf(indicator);
                    if (sellerIndex >= 0) break;
                }
                
                // 如果找到了购买方和销售方区域
                if (buyerIndex >= 0 && sellerIndex >= 0)
                {
                    // 确定购买方和销售方的顺序
                    int firstIndex = Math.Min(buyerIndex, sellerIndex);
                    int secondIndex = Math.Max(buyerIndex, sellerIndex);
                    bool buyerFirst = (buyerIndex == firstIndex);
                    
                    // 提取第一部分信息（购买方或销售方）
                    string firstSection = fullText.Substring(firstIndex, secondIndex - firstIndex);
                    
                    // 提取第二部分信息（销售方或购买方）
                    int endOfSecondSection = fullText.IndexOf("项目名称", secondIndex);
                    if (endOfSecondSection < 0) endOfSecondSection = fullText.IndexOf("合 计", secondIndex);
                    if (endOfSecondSection < 0) endOfSecondSection = Math.Min(secondIndex + 300, fullText.Length);
                    
                    string secondSection = fullText.Substring(secondIndex, endOfSecondSection - secondIndex);
                    
                    // 根据顺序分配购买方和销售方区域
                    string buyerSection = buyerFirst ? firstSection : secondSection;
                    string sellerSection = buyerFirst ? secondSection : firstSection;
                    
                    // 提取购买方信息
                    if (string.IsNullOrEmpty(invoiceInfo.BuyerName))
                    {
                        // 寻找购买方名称
                        var buyerNameMatch = buyerNameRegex.Match(buyerSection);
                        if (buyerNameMatch.Success)
                        {
                            invoiceInfo.BuyerName = buyerNameMatch.Groups[1].Value.Trim();
                            LogUtil.AddLog($"已提取购买方名称: {invoiceInfo.BuyerName}");
                        }
                    }
                    
                    if (string.IsNullOrEmpty(invoiceInfo.BuyerTaxNumber))
                    {
                        // 寻找购买方税号或统一社会信用代码
                        var creditCodeMatch = creditCodeRegex.Match(buyerSection);
                        if (creditCodeMatch.Success)
                        {
                            invoiceInfo.BuyerTaxNumber = creditCodeMatch.Groups[1].Value.Trim();
                            LogUtil.AddLog($"已提取购买方税号: {invoiceInfo.BuyerTaxNumber}");
                        }
                        else
                        {
                            var taxNoMatch = taxNoRegex.Match(buyerSection);
                            if (taxNoMatch.Success)
                            {
                                invoiceInfo.BuyerTaxNumber = taxNoMatch.Groups[1].Value.Trim();
                                LogUtil.AddLog($"已提取购买方税号(备选): {invoiceInfo.BuyerTaxNumber}");
                            }
                        }
                    }
                    
                    // 提取销售方信息
                    if (string.IsNullOrEmpty(invoiceInfo.SellerName))
                    {
                        // 寻找销售方名称
                        var sellerNameMatch = sellerNameRegex.Match(sellerSection);
                        if (sellerNameMatch.Success)
                        {
                            invoiceInfo.SellerName = sellerNameMatch.Groups[1].Value.Trim();
                            LogUtil.AddLog($"已提取销售方名称: {invoiceInfo.SellerName}");
                        }
                    }
                    
                    if (string.IsNullOrEmpty(invoiceInfo.SellerTaxNumber))
                    {
                        // 寻找销售方税号或统一社会信用代码
                        var creditCodeMatch = creditCodeRegex.Match(sellerSection);
                        if (creditCodeMatch.Success)
                        {
                            invoiceInfo.SellerTaxNumber = creditCodeMatch.Groups[1].Value.Trim();
                            LogUtil.AddLog($"已提取销售方税号: {invoiceInfo.SellerTaxNumber}");
                        }
                        else
                        {
                            var taxNoMatch = taxNoRegex.Match(sellerSection);
                            if (taxNoMatch.Success)
                            {
                                invoiceInfo.SellerTaxNumber = taxNoMatch.Groups[1].Value.Trim();
                                LogUtil.AddLog($"已提取销售方税号(备选): {invoiceInfo.SellerTaxNumber}");
                            }
                        }
                    }
                }
                else
                {
                    // 如果找不到明确的区域划分，尝试通过上下文识别
                    // 假设有两个名称，第一个可能是购买方，第二个可能是销售方
                    var nameMatches = buyerNameRegex.Matches(fullText);
                    if (nameMatches.Count >= 2)
                    {
                        invoiceInfo.BuyerName = nameMatches[0].Groups[1].Value.Trim();
                        invoiceInfo.SellerName = nameMatches[1].Groups[1].Value.Trim();
                        LogUtil.AddLog("通过上下文识别提取了购买方和销售方名称");
                    }
                    
                    // 尝试提取税号
                    var taxMatches = creditCodeRegex.Matches(fullText);
                    if (taxMatches.Count >= 2)
                    {
                        invoiceInfo.BuyerTaxNumber = taxMatches[0].Groups[1].Value.Trim();
                        invoiceInfo.SellerTaxNumber = taxMatches[1].Groups[1].Value.Trim();
                        LogUtil.AddLog("通过上下文识别提取了购买方和销售方税号");
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提取购买方和销售方信息时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 提取商品项目信息
        /// </summary>
        private static void ExtractItemsInfo(string fullText, ref UmiOcrService.OcrInvoiceInfo invoiceInfo)
        {
            try
            {
                if (invoiceInfo.Items.Count == 0)
                {
                    // 先尝试通过*号标记提取商品名称
                    var typeMatch = typeRegex.Match(fullText);
                    if (typeMatch.Success)
                    {
                        string itemName = typeMatch.Value.Trim('*');
                        var item = new UmiOcrService.OcrInvoiceItem
                        {
                            Name = itemName
                        };
                        
                        // 尝试查找与该项目关联的金额和税率
                        int itemIndex = fullText.IndexOf(itemName);
                        if (itemIndex > 0)
                        {
                            // 假设项目后面的数据包含金额和税率
                            string itemContext = fullText.Substring(itemIndex, Math.Min(200, fullText.Length - itemIndex));
                            
                            // 提取税率
                            var taxRateMatch = new Regex(@"(\d+)[%％]").Match(itemContext);
                            if (taxRateMatch.Success)
                            {
                                if (decimal.TryParse(taxRateMatch.Groups[1].Value, out decimal taxRate))
                                {
                                    item.TaxRate = taxRate;
                                    LogUtil.AddLog($"为项目[{itemName}]提取到税率: {taxRate}%");
                                }
                            }
                            
                            // 提取不含税金额
                            var amountMatch = new Regex(@"([0-9]+[.][0-9]+)").Matches(itemContext);
                            if (amountMatch.Count >= 2) // 假设有至少两个金额，一个是不含税金额，一个是税额
                            {
                                if (decimal.TryParse(amountMatch[0].Groups[1].Value, out decimal amount))
                                {
                                    item.Amount = amount;
                                    LogUtil.AddLog($"为项目[{itemName}]提取到金额: {amount}");
                                }
                                
                                if (decimal.TryParse(amountMatch[1].Groups[1].Value, out decimal taxAmount))
                                {
                                    // 使用正确的Tax属性
                                    item.Tax = taxAmount;
                                    LogUtil.AddLog($"为项目[{itemName}]提取到税额: {taxAmount}");
                                }
                            }
                        }
                        
                        invoiceInfo.Items.Add(item);
                        LogUtil.AddLog($"已提取商品项目: {itemName}");
                    }
                    else
                    {
                        // 如果没有找到*号标记，尝试查找"项目名称"后面的内容
                        int projectIndex = fullText.IndexOf("项目名称");
                        if (projectIndex > 0)
                        {
                            int endIndex = fullText.IndexOf("合 计", projectIndex);
                            if (endIndex < 0) endIndex = fullText.IndexOf("价税合计", projectIndex);
                            if (endIndex < 0) endIndex = Math.Min(projectIndex + 300, fullText.Length);
                            
                            string itemsSection = fullText.Substring(projectIndex, endIndex - projectIndex);
                            
                            // 尝试提取第一行项目数据
                            var lines = itemsSection.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                            if (lines.Length > 1) // 跳过标题行
                            {
                                string itemLine = lines[1];
                                // 简单地将第一个可见的单词作为项目名称
                                string itemName = itemLine.Trim();
                                if (itemName.Length > 10) itemName = itemName.Substring(0, 10); // 限制长度
                                
                                invoiceInfo.Items.Add(new UmiOcrService.OcrInvoiceItem
                                {
                                    Name = itemName
                                });
                                LogUtil.AddLog($"已提取商品项目(备选方式): {itemName}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提取商品项目信息时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 提取金额信息
        /// </summary>
        private static void ExtractAmountInfo(string fullText, ref UmiOcrService.OcrInvoiceInfo invoiceInfo)
        {
            try
            {
                // 提取含税总金额
                if (invoiceInfo.TotalAmountWithTax == 0)
                {
                    // 尝试匹配价税合计
                    var totalMatch = totalAmountRegex.Match(fullText);
                    if (totalMatch.Success)
                    {
                        string amountStr = totalMatch.Groups[1].Value.Trim().Replace(",", "");
                        if (decimal.TryParse(amountStr, out decimal amount))
                        {
                            invoiceInfo.TotalAmountWithTax = amount;
                            LogUtil.AddLog($"已提取价税合计金额: {amount}");
                        }
                    }
                    else
                    {
                        // 直接寻找"小写"后面的金额
                        var smallAmountMatch = new Regex(@"小写[)）]?\s*[¥￥]?\s*([0-9,.]+)").Match(fullText);
                        if (smallAmountMatch.Success)
                        {
                            string amountStr = smallAmountMatch.Groups[1].Value.Trim().Replace(",", "");
                            if (decimal.TryParse(amountStr, out decimal amount))
                            {
                                invoiceInfo.TotalAmountWithTax = amount;
                                LogUtil.AddLog($"已提取小写金额: {amount}");
                            }
                        }
                        else
                        {
                            // 尝试寻找格式为¥XX.XX的所有金额，取最大值作为总额
                            var amountMatches = amountRegex2.Matches(fullText);
                            if (amountMatches.Count > 0)
                            {
                                decimal maxAmount = 0;
                                foreach (Match match in amountMatches)
                                {
                                    if (decimal.TryParse(match.Groups[1].Value, out decimal current) && current > maxAmount)
                                    {
                                        maxAmount = current;
                                    }
                                }
                                
                                if (maxAmount > 0)
                                {
                                    invoiceInfo.TotalAmountWithTax = maxAmount;
                                    LogUtil.AddLog($"已提取可能的最大金额作为总额: {maxAmount}");
                                }
                            }
                        }
                    }
                }
                
                // 如果有项目，但没有项目金额，尝试提取
                if (invoiceInfo.TotalAmount == 0 && invoiceInfo.Items.Count > 0)
                {
                    // 尝试在"合计"附近找不含税金额
                    int totalIndex = fullText.IndexOf("合 计");
                    if (totalIndex < 0) totalIndex = fullText.IndexOf("合计");
                    
                    if (totalIndex > 0)
                    {
                        string totalSection = fullText.Substring(totalIndex, Math.Min(100, fullText.Length - totalIndex));
                        var amountMatches = new Regex(@"[¥￥]?\s*([0-9,.]+[.][0-9]{2})").Matches(totalSection);
                        
                        if (amountMatches.Count > 0)
                        {
                            // 假设第一个金额是不含税金额
                            string amountStr = amountMatches[0].Groups[1].Value.Trim().Replace(",", "");
                            if (decimal.TryParse(amountStr, out decimal amount))
                            {
                                invoiceInfo.TotalAmount = amount;
                                LogUtil.AddLog($"已提取不含税总金额: {amount}");
                                
                                // 如果有第二个金额，可能是税额
                                if (amountMatches.Count > 1)
                                {
                                    string taxStr = amountMatches[1].Groups[1].Value.Trim().Replace(",", "");
                                    if (decimal.TryParse(taxStr, out decimal taxAmount))
                                    {
                                        // 使用正确的TotalTax属性
                                        invoiceInfo.TotalTax = taxAmount;
                                        LogUtil.AddLog($"已提取税额: {taxAmount}");
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提取金额信息时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 将PDF转换为图像用于OCR处理
        /// </summary>
        /// <param name="pdfBytes">PDF文件字节数组</param>
        /// <returns>PDF第一页的图像字节数组</returns>
        private static byte[] ConvertPdfToImage(byte[] pdfBytes)
        {
            try
            {
                // 由于我们使用的是UglyToad.PdfPig，它没有直接的渲染功能
                // 我们可以尝试提取第一页的图像，如果有的话
                using (var memoryStream = new MemoryStream(pdfBytes))
                using (var document = PdfDocument.Open(memoryStream))
                {
                    var page = document.GetPage(1);
                    var images = page.GetImages().ToList();
                    
                    // 尝试找到页面中最大的图像，可能是发票的背景图
                    var largestImage = images
                        .OrderByDescending(img => img.WidthInSamples * img.HeightInSamples)
                        .FirstOrDefault();
                        
                    if (largestImage != null)
                    {
                        largestImage.TryGetPng(out byte[] pngBytes);
                        if (pngBytes != null && pngBytes.Length > 0)
                        {
                            LogUtil.AddLog("从PDF中提取到图像");
                            return pngBytes;
                        }
                    }
                    
                    // 如果没有找到合适的图像，直接返回原PDF数据
                    // 在实际应用中，这里应该使用PDF渲染库（如PdfiumViewer、iTextSharp等）
                    // 将PDF页面渲染为图像
                    
                    // 注意：由于我们没有直接渲染PDF的能力，在实际环境中需要添加以下依赖：
                    // 1. Magick.NET - 强大的图像处理库
                    // 2. PdfiumViewer - 用于渲染PDF
                    // 3. PDFsharp - 另一个PDF处理库
                    
                    // 示例代码（需要添加相应的引用）:
                    /*
                    using (var tempFilePath = Path.GetTempFileName() + ".pdf")
                    {
                        File.WriteAllBytes(tempFilePath, pdfBytes);
                        
                        using (var pdfDocument = PdfiumViewer.PdfDocument.Load(tempFilePath))
                        {
                            var image = pdfDocument.Render(0, 300, 300, true);
                            using (var ms = new MemoryStream())
                            {
                                image.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
                                return ms.ToArray();
                            }
                        }
                    }
                    */
                    
                    LogUtil.AddLog("未能从PDF生成图像，将使用原PDF数据");
                }
                
                // 如果无法生成图像，返回原始PDF数据
                return pdfBytes;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"转换PDF到图像时出错: {ex.Message}");
                // 出错时返回原始PDF数据
                return pdfBytes;
            }
        }
        
        /// <summary>
        /// 回退到OCR处理，用于PDF文本提取失败的情况
        /// </summary>
        private static async Task<UmiOcrService.OcrInvoiceInfo> FallbackToOcrProcessing(byte[] pdfBytes)
        {
            try
            {
                // 将PDF转换为图像
                byte[] imageBytes = ConvertPdfToImage(pdfBytes);
                
                // 使用OCR服务识别图像
                return await UmiOcrService.Instance.RecognizeInvoice(imageBytes);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"回退到OCR处理时发生错误: {ex.Message}");
                return new UmiOcrService.OcrInvoiceInfo
                {
                    Remark = $"处理PDF发票时发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 处理PDF发票文件，仅通过PDF文本直接提取信息
        /// </summary>
        /// <param name="pdfBytes">PDF文件字节数组</param>
        /// <returns>处理结果</returns>
        public static async Task<UmiOcrService.OcrInvoiceInfo> ProcessInvoicePdfFile(byte[] pdfBytes)
        {
            try
            {
                LogUtil.AddLog("开始处理PDF发票文件（仅使用PDF文本提取，不使用OCR）");
                
                // 直接从PDF提取，不使用OCR回退
                var result = await ExtractInvoiceInfoFromPdf(pdfBytes);
                
                // 记录处理结果
                if (result.Remark.Contains("错误") || result.Remark.Contains("不完整") || result.Remark.Contains("无法"))
                {
                    LogUtil.AddLog($"PDF处理结果不理想: {result.Remark}");
                }
                else
                {
                    LogUtil.AddLog("PDF处理成功");
                }
                
                // 返回处理后的发票信息
                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理PDF发票文件时出错: {ex.Message}");
                return new UmiOcrService.OcrInvoiceInfo
                {
                    Remark = $"处理失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 基于位置的发票信息识别
        /// 针对固定格式的电子发票，根据关键文本位置提取相应信息
        /// </summary>
        /// <param name="pdfBytes">PDF文件字节数组</param>
        /// <returns>发票信息对象</returns>
        public static UmiOcrService.OcrInvoiceInfo ExtractInvoiceInfoByPosition(byte[] pdfBytes)
        {
            try
            {
                LogUtil.AddLog("开始使用基于位置的方法识别电子发票");
                var invoiceInfo = new UmiOcrService.OcrInvoiceInfo();
                
                using (var document = PdfDocument.Open(pdfBytes))
                {
                    Page firstPage = document.GetPages().FirstOrDefault();
                    if (firstPage == null)
                    {
                        return invoiceInfo;
                    }
                    
                    // 获取页面所有文本元素
                    var words = firstPage.GetWords().ToList();
                    
                    // 查找关键标识，确认是否是标准电子发票
                    bool isStandardInvoice = words.Any(w => w.Text.Contains("电子发票") || w.Text.Contains("增值税发票"));
                    if (!isStandardInvoice)
                    {
                        LogUtil.AddLog("文档不是标准电子发票格式，不使用位置识别");
                        return invoiceInfo;
                    }
                    
                    // 1. 识别发票类型
                    var invoiceTypeWord = words.FirstOrDefault(w => 
                        w.Text.Contains("电子发票") || 
                        w.Text.Contains("增值税专用发票") || 
                        w.Text.Contains("增值税普通发票"));
                    
                    if (invoiceTypeWord != null)
                    {
                        invoiceInfo.InvoiceType = invoiceTypeWord.Text;
                        LogUtil.AddLog($"识别到发票类型: {invoiceInfo.InvoiceType}");
                    }
                    
                    // 2. 识别发票号码 - 查找"发票号码"标签附近的数字
                    var invoiceNoLabel = words.FirstOrDefault(w => w.Text.Contains("发票号码"));
                    if (invoiceNoLabel != null)
                    {
                        // 查找标签右侧的数字
                        var invoiceNoWord = words.FirstOrDefault(w => 
                            w.BoundingBox.Left > invoiceNoLabel.BoundingBox.Right &&
                            Math.Abs(w.BoundingBox.Bottom - invoiceNoLabel.BoundingBox.Bottom) < 5 &&
                            new Regex(@"^\d+$").IsMatch(w.Text));
                        
                        if (invoiceNoWord != null)
                        {
                            invoiceInfo.InvoiceNumber = invoiceNoWord.Text;
                            LogUtil.AddLog($"通过位置识别到发票号码: {invoiceInfo.InvoiceNumber}");
                        }
                    }
                    
                    // 3. 识别开票日期 - 查找"开票日期"标签附近的日期
                    var dateLabel = words.FirstOrDefault(w => w.Text.Contains("开票日期"));
                    if (dateLabel != null)
                    {
                        // 查找标签右侧或下方的日期文本
                        var dateWords = words.Where(w => 
                            w.BoundingBox.Left > dateLabel.BoundingBox.Right &&
                            Math.Abs(w.BoundingBox.Bottom - dateLabel.BoundingBox.Bottom) < 10)
                            .OrderBy(w => w.BoundingBox.Left)
                            .Take(3) // 可能是年、月、日分开的
                            .ToList();
                        
                        if (dateWords.Count > 0)
                        {
                            string dateText = string.Join("", dateWords.Select(w => w.Text));
                            // 尝试转换各种可能的日期格式
                            string normalizedDate = dateText
                                .Replace("年", "-").Replace("月", "-").Replace("日", "")
                                .Replace("/", "-").Replace(".", "-");
                            
                            if (DateTime.TryParse(normalizedDate, out DateTime date))
                            {
                                invoiceInfo.InvoiceDate = date;
                                LogUtil.AddLog($"通过位置识别到开票日期: {date:yyyy-MM-dd}");
                            }
                        }
                    }
                    
                    // 4. 识别购买方和销售方信息
                    string buyerName = null;
                    string buyerTaxNumber = null;
                    string sellerName = null;
                    string sellerTaxNumber = null;
                    
                    // 使用临时变量传递给ref参数
                    ExtractPartyInfoByPosition(words, "购买方", ref buyerName, ref buyerTaxNumber);
                    ExtractPartyInfoByPosition(words, "销售方", ref sellerName, ref sellerTaxNumber);
                    
                    // 设置结果
                    if (!string.IsNullOrEmpty(buyerName)) invoiceInfo.BuyerName = buyerName;
                    if (!string.IsNullOrEmpty(buyerTaxNumber)) invoiceInfo.BuyerTaxNumber = buyerTaxNumber;
                    if (!string.IsNullOrEmpty(sellerName)) invoiceInfo.SellerName = sellerName;
                    if (!string.IsNullOrEmpty(sellerTaxNumber)) invoiceInfo.SellerTaxNumber = sellerTaxNumber;
                    
                    // 5. 识别金额信息
                    ExtractAmountInfoByPosition(words, invoiceInfo);
                    
                    // 6. 识别商品信息
                    ExtractItemsByPosition(words, invoiceInfo);
                    
                    // 设置处理方式备注
                    invoiceInfo.Remark = "通过基于位置的识别提取";
                    
                    return invoiceInfo;
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"基于位置识别发票信息时出错: {ex.Message}");
                return new UmiOcrService.OcrInvoiceInfo
                {
                    Remark = $"基于位置识别发票失败: {ex.Message}"
                };
            }
        }
        
        /// <summary>
        /// 通过位置识别购买方或销售方信息
        /// </summary>
        private static void ExtractPartyInfoByPosition(List<UglyToad.PdfPig.Content.Word> words, 
                                                      string partyLabel, 
                                                      ref string partyName, 
                                                      ref string partyTaxNumber)
        {
            try
            {
                // 查找购买方/销售方标签
                var label = words.FirstOrDefault(w => w.Text.Contains(partyLabel));
                if (label == null) return;
                
                // 确定该方区域的大致范围
                float minY = (float)(label.BoundingBox.Bottom - 50);
                float maxY = (float)(label.BoundingBox.Bottom + 10);
                
                // 在区域内查找"名称"标签
                var nameLabel = words.FirstOrDefault(w => 
                    w.Text.Contains("名称") && 
                    w.BoundingBox.Bottom >= minY && 
                    w.BoundingBox.Bottom <= maxY);
                
                if (nameLabel != null)
                {
                    // 查找名称标签右侧的内容
                    var nameWords = words.Where(w => 
                        w.BoundingBox.Left > nameLabel.BoundingBox.Right &&
                        Math.Abs(w.BoundingBox.Bottom - nameLabel.BoundingBox.Bottom) < 5)
                        .OrderBy(w => w.BoundingBox.Left)
                        .ToList();
                    
                    if (nameWords.Count > 0)
                    {
                        partyName = string.Join(" ", nameWords.Select(w => w.Text)).Trim();
                        LogUtil.AddLog($"通过位置识别到{partyLabel}名称: {partyName}");
                    }
                }
                
                // 查找税号
                var taxLabel = words.FirstOrDefault(w => 
                    (w.Text.Contains("纳税人识别号") || w.Text.Contains("统一社会信用代码")) && 
                    w.BoundingBox.Bottom >= minY && 
                    w.BoundingBox.Bottom <= maxY + 20); // 税号可能在下一行
                
                if (taxLabel != null)
                {
                    // 查找税号标签右侧的内容
                    var taxWords = words.Where(w => 
                        w.BoundingBox.Left > taxLabel.BoundingBox.Right &&
                        Math.Abs(w.BoundingBox.Bottom - taxLabel.BoundingBox.Bottom) < 5 &&
                        (new Regex(@"[0-9A-Z]").IsMatch(w.Text)))
                        .OrderBy(w => w.BoundingBox.Left)
                        .ToList();
                    
                    if (taxWords.Count > 0)
                    {
                        partyTaxNumber = string.Join("", taxWords.Select(w => w.Text)).Trim();
                        LogUtil.AddLog($"通过位置识别到{partyLabel}税号: {partyTaxNumber}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提取{partyLabel}信息时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 通过位置识别金额信息
        /// </summary>
        private static void ExtractAmountInfoByPosition(List<UglyToad.PdfPig.Content.Word> words, UmiOcrService.OcrInvoiceInfo invoiceInfo)
        {
            try
            {
                // 寻找价税合计标签
                var totalLabel = words.FirstOrDefault(w => w.Text.Contains("价税合计"));
                if (totalLabel != null)
                {
                    // 寻找标签附近的金额
                    // 首先查找小写金额
                    var smallAmountLabel = words.FirstOrDefault(w => 
                        w.Text.Contains("小写") && 
                        Math.Abs(w.BoundingBox.Bottom - totalLabel.BoundingBox.Bottom) < 10);
                    
                    if (smallAmountLabel != null)
                    {
                        // 查找小写标签右侧的数字
                        var amountWords = words.Where(w => 
                            w.BoundingBox.Left > smallAmountLabel.BoundingBox.Right &&
                            Math.Abs(w.BoundingBox.Bottom - smallAmountLabel.BoundingBox.Bottom) < 5 &&
                            (w.Text.Contains("¥") || new Regex(@"[0-9.,]").IsMatch(w.Text)))
                            .OrderBy(w => w.BoundingBox.Left)
                            .ToList();
                        
                        if (amountWords.Count > 0)
                        {
                            string amountText = string.Join("", amountWords.Select(w => w.Text))
                                .Replace("¥", "").Replace("￥", "").Replace(",", "").Trim();
                            
                            if (decimal.TryParse(amountText, out decimal amount))
                            {
                                invoiceInfo.TotalAmountWithTax = amount;
                                LogUtil.AddLog($"通过位置识别到价税合计: {amount}");
                            }
                        }
                    }
                    else
                    {
                        // 如果没有找到"小写"标签，直接寻找价税合计附近的金额
                        var amountWords = words.Where(w => 
                            w.BoundingBox.Left > totalLabel.BoundingBox.Right &&
                            Math.Abs(w.BoundingBox.Bottom - totalLabel.BoundingBox.Bottom) < 5 &&
                            (w.Text.Contains("¥") || new Regex(@"[0-9.,]").IsMatch(w.Text)))
                            .OrderBy(w => w.BoundingBox.Left)
                            .ToList();
                        
                        if (amountWords.Count > 0)
                        {
                            string amountText = string.Join("", amountWords.Select(w => w.Text))
                                .Replace("¥", "").Replace("￥", "").Replace(",", "").Trim();
                            
                            if (decimal.TryParse(amountText, out decimal amount))
                            {
                                invoiceInfo.TotalAmountWithTax = amount;
                                LogUtil.AddLog($"通过位置识别到价税合计: {amount}");
                            }
                        }
                    }
                    
                    // 查找大写金额
                    var chineseAmountLabel = words.FirstOrDefault(w => 
                        w.Text.Contains("大写") && 
                        Math.Abs(w.BoundingBox.Bottom - totalLabel.BoundingBox.Bottom) < 15);
                    
                    if (chineseAmountLabel != null)
                    {
                        // 查找大写标签右侧的文本
                        var chineseWords = words.Where(w => 
                            w.BoundingBox.Left > chineseAmountLabel.BoundingBox.Right &&
                            Math.Abs(w.BoundingBox.Bottom - chineseAmountLabel.BoundingBox.Bottom) < 5 &&
                            new Regex(@"[零壹贰叁肆伍陆柒捌玖拾佰仟万亿元角分整点零一二三四五六七八九十百千万亿元角分整点]").IsMatch(w.Text))
                            .OrderBy(w => w.BoundingBox.Left)
                            .ToList();
                        
                        if (chineseWords.Count > 0)
                        {
                            invoiceInfo.AmountInWords = string.Join("", chineseWords.Select(w => w.Text)).Trim();
                            LogUtil.AddLog($"通过位置识别到大写金额: {invoiceInfo.AmountInWords}");
                        }
                    }
                }
                
                // 寻找合计标签，获取不含税金额和税额
                var subTotalLabel = words.FirstOrDefault(w => w.Text.Contains("合计") && !w.Text.Contains("价税"));
                if (subTotalLabel != null)
                {
                    // 获取合计行上的所有数字，通常是金额、税额和税率
                    var amountWords = words.Where(w => 
                        Math.Abs(w.BoundingBox.Bottom - subTotalLabel.BoundingBox.Bottom) < 5 &&
                        (w.Text.Contains("¥") || new Regex(@"[0-9.,]").IsMatch(w.Text)))
                        .OrderBy(w => w.BoundingBox.Left)
                        .ToList();
                    
                    // 通常合计行上的金额按从左到右的顺序是：不含税金额、税额
                    List<decimal> amounts = new List<decimal>();
                    foreach (var word in amountWords)
                    {
                        string amountText = word.Text.Replace("¥", "").Replace("￥", "").Replace(",", "").Trim();
                        if (decimal.TryParse(amountText, out decimal amount))
                        {
                            amounts.Add(amount);
                        }
                    }
                    
                    // 设置找到的金额
                    if (amounts.Count >= 1)
                    {
                        invoiceInfo.TotalAmount = amounts[0];
                        LogUtil.AddLog($"通过位置识别到不含税金额: {amounts[0]}");
                        
                        if (amounts.Count >= 2)
                        {
                            invoiceInfo.TotalTax = amounts[1];
                            LogUtil.AddLog($"通过位置识别到税额: {amounts[1]}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提取金额信息时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 通过位置识别商品项目信息
        /// </summary>
        private static void ExtractItemsByPosition(List<UglyToad.PdfPig.Content.Word> words, UmiOcrService.OcrInvoiceInfo invoiceInfo)
        {
            try
            {
                // 查找项目区域的表头
                var headerRow = words.FirstOrDefault(w => w.Text.Contains("项目名称"));
                if (headerRow == null) return;
                
                // 确定表格区域
                float tableTop = (float)(headerRow.BoundingBox.Bottom + 10); // 表头下方
                
                // 查找"合计"或"价税合计"位置作为表格底部边界
                var tableBottom = words.FirstOrDefault(w => 
                    (w.Text.Contains("合计") || w.Text.Contains("价税合计")) && 
                    w.BoundingBox.Bottom < tableTop);
                
                float bottomPosition = tableBottom != null ? 
                    (float)(tableBottom.BoundingBox.Bottom - 5) : tableTop - 100; // 如果找不到底部标记，取一个合理范围
                
                // 确定表格行 - 按Y坐标分组
                var rows = words
                    .Where(w => w.BoundingBox.Bottom < tableTop && w.BoundingBox.Bottom > bottomPosition)
                    .GroupBy(w => Math.Round(w.BoundingBox.Bottom, 1)) // 将Y坐标相近的词组成一行
                    .OrderByDescending(g => g.Key) // 从上到下排序
                    .ToList();
                
                foreach (var row in rows)
                {
                    // 每一行按X坐标排序，从左到右
                    var sortedWords = row.OrderBy(w => w.BoundingBox.Left).ToList();
                    
                    // 检查行是否包含有意义的数据
                    if (sortedWords.Count < 3) continue; // 表格行应至少有品名、单价、金额等几列
                    if (sortedWords.All(w => !new Regex(@"[a-zA-Z\u4e00-\u9fa5]").IsMatch(w.Text))) continue; // 至少应有一些文字
                    
                    // 提取商品信息
                    string itemName = sortedWords[0].Text; // 假设第一列是商品名称
                    decimal itemAmount = 0;
                    decimal itemTax = 0;
                    decimal taxRate = 0;
                    
                    // 尝试从行中提取金额信息
                    List<decimal> numbers = new List<decimal>();
                    foreach (var word in sortedWords.Skip(1)) // 跳过第一列（商品名称）
                    {
                        if (decimal.TryParse(word.Text.Replace("¥", "").Replace(",", ""), out decimal number))
                        {
                            numbers.Add(number);
                        }
                    }
                    
                    // 根据位置推断各数值的含义
                    if (numbers.Count >= 3)
                    {
                        // 通常的列顺序：数量、单价、金额、税率、税额
                        itemAmount = numbers[numbers.Count - 3]; // 倒数第三个通常是金额
                        taxRate = numbers[numbers.Count - 2]; // 倒数第二个通常是税率
                        itemTax = numbers[numbers.Count - 1]; // 最后一个通常是税额
                    }
                    else if (numbers.Count == 2)
                    {
                        // 只有两个数字，可能是金额和税额
                        itemAmount = numbers[0];
                        itemTax = numbers[1];
                    }
                    
                    // 添加商品项目
                    if (!string.IsNullOrWhiteSpace(itemName) && (itemAmount > 0 || itemTax > 0))
                    {
                        var item = new UmiOcrService.OcrInvoiceItem
                        {
                            Name = itemName,
                            Amount = itemAmount,
                            Tax = itemTax,
                            TaxRate = taxRate > 1 ? taxRate / 100 : taxRate // 转换百分比为小数
                        };
                        
                        invoiceInfo.Items.Add(item);
                        LogUtil.AddLog($"通过位置识别到商品: {itemName}, 金额: {itemAmount}, 税额: {itemTax}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"提取商品项目信息时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理指定路径的PDF发票文件
        /// 仅通过PDF文本直接提取信息，不使用OCR
        /// </summary>
        /// <param name="pdfFilePath">PDF文件路径</param>
        /// <returns>处理结果</returns>
        public static async Task<UmiOcrService.OcrInvoiceInfo> ProcessInvoicePdfFromPath(string pdfFilePath)
        {
            try
            {
                LogUtil.AddLog($"开始处理PDF发票文件: {pdfFilePath}（仅使用PDF文本提取，不使用OCR）");
                
                // 检查文件是否存在
                if (!File.Exists(pdfFilePath))
                {
                    LogUtil.AddErrorLog($"PDF文件不存在: {pdfFilePath}");
                    return new UmiOcrService.OcrInvoiceInfo
                    {
                        Remark = "文件不存在"
                    };
                }
                
                // 读取PDF文件
                byte[] pdfBytes = File.ReadAllBytes(pdfFilePath);
                
                // 首先尝试使用基于位置的识别方法
                var invoiceInfo = ExtractInvoiceInfoByPosition(pdfBytes);
                
                // 检查基于位置的识别是否获取到足够的信息
                bool hasEnoughInfo = !string.IsNullOrEmpty(invoiceInfo.InvoiceNumber) && 
                                    (!string.IsNullOrEmpty(invoiceInfo.BuyerName) || !string.IsNullOrEmpty(invoiceInfo.SellerName)) &&
                                    invoiceInfo.TotalAmountWithTax > 0;
                
                // 如果基于位置的识别没有获取到足够信息，则使用通用文本提取方法
                if (!hasEnoughInfo)
                {
                    LogUtil.AddLog("基于位置的识别未获取到足够信息，尝试使用通用文本提取方法");
                    invoiceInfo = await ExtractInvoiceInfoFromPdf(pdfBytes);
                }
                
                // 记录处理结果
                if (invoiceInfo.Remark.Contains("错误") || invoiceInfo.Remark.Contains("不完整") || invoiceInfo.Remark.Contains("无法"))
                {
                    LogUtil.AddLog($"PDF处理结果不理想: {invoiceInfo.Remark}");
                }
                else
                {
                    LogUtil.AddLog("PDF处理成功");
                }
                
                return invoiceInfo;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"处理PDF发票文件时出错: {ex.Message}");
                return new UmiOcrService.OcrInvoiceInfo
                {
                    Remark = $"处理失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 基于固定坐标范围提取发票信息
        /// 适用于格式完全固定的电子发票，使用预定义坐标直接提取信息
        /// </summary>
        /// <param name="pdfBytes">PDF文件字节数组</param>
        /// <returns>发票信息对象</returns>
        public static UmiOcrService.OcrInvoiceInfo ExtractInvoiceInfoByFixedCoordinates(byte[] pdfBytes)
        {
            try
            {
                LogUtil.AddLog("开始使用固定坐标方法识别电子发票");
                var invoiceInfo = new UmiOcrService.OcrInvoiceInfo();
                
                using (var document = PdfDocument.Open(pdfBytes))
                {
                    Page firstPage = document.GetPages().FirstOrDefault();
                    if (firstPage == null)
                    {
                        return invoiceInfo;
                    }
                    
                    // 获取页面尺寸，用于坐标调整
                    double pageWidth = firstPage.Width;
                    double pageHeight = firstPage.Height;
                    LogUtil.AddLog($"发票页面尺寸: 宽={pageWidth}, 高={pageHeight}");
                    
                    // 获取页面所有文本元素
                    var words = firstPage.GetWords().ToList();
                    
                    // 输出所有文本元素位置以便调试
                    LogUtil.AddLog("--- 页面中所有文本元素的坐标和内容(用于调整坐标范围) ---");
                    //List<string> t = new List<string>();
                    foreach (var word in words)
                    {
                        //t.Add($"文本: '{word.Text}', x1: X={word.BoundingBox.Left - 2:F1}, x2={word.BoundingBox.Right + 2:F1}, y1={word.BoundingBox.Bottom  - 2:F1}, y2={word.BoundingBox.Top + 2:F1}");
                        LogUtil.AddLog($"文本: '{word.Text}', x1: X={word.BoundingBox.Left - 2:F1}, x2={word.BoundingBox.Right + 2:F1}, y1={word.BoundingBox.Bottom  - 2:F1}, y2={word.BoundingBox.Top + 2:F1}");
                        LogUtil.AddLog($"文本: '{word.Text}', 左下坐标: X={word.BoundingBox.BottomLeft.X:F1}, Y={word.BoundingBox.BottomLeft.Y:F1}, 右上坐标：X={word.BoundingBox.TopRight.X:F1}, Y={word.BoundingBox.TopRight.Y:F1}");
                    }
                    //string test = JsonConvert.SerializeObject(t);
                    LogUtil.AddLog("开始使用精确坐标提取发票信息");
                    
                    // 使用用户提供的精确坐标
                    // 注意：PDF坐标系原点已经在左下角，坐标单位为点，无需转换
                    
                    // 1. 提取发票类型 (假设在发票顶部区域)
                    ExtractTextInRect(words, 
                        159.7, 424.4, 354.9, 375.3, 
                        (text) => {
                            LogUtil.AddLog($"发票类型区域找到文本: {text}");
                            if (text.Contains("增值税专用发票") || text.Contains("增值税普通发票") || text.Contains("电子发票"))
                            {
                                invoiceInfo.InvoiceType = text.Trim();
                                LogUtil.AddLog($"提取到发票类型: {invoiceInfo.InvoiceType}");
                            }
                        });
                    
                    // 2. 提取发票号码 (通常在发票右上方)
                    ExtractTextInRect(words, 
                        479.9, 575, 355.3, 365.6, 
                        (text) => {
                            LogUtil.AddLog($"发票号码区域找到文本: {text}");
                            var match = Regex.Match(text, @"\d{8,}");
                            if (match.Success)
                            {
                                invoiceInfo.InvoiceNumber = match.Value;
                                LogUtil.AddLog($"提取到发票号码: {invoiceInfo.InvoiceNumber}");
                            }
                        });
                    // 3. 提取开票日期 ("[(x:481.882, y:347.45249679828123), 62.998985000000005, 7.417849238281235]"

                    ExtractTextInRect(words, 
                        479.9,575, 338, 349.5, 
                        (text) => {
                            LogUtil.AddLog($"开票日期区域找到文本: {text}");
                            var dateMatch = Regex.Match(text, @"\d{4}[年/\-\.]\d{1,2}[月/\-\.]\d{1,2}[日]?");
                            if (dateMatch.Success)
                            {
                                string dateStr = dateMatch.Value.Replace("年", "-").Replace("月", "-").Replace("日", "");
                                if (DateTime.TryParse(dateStr, out DateTime date))
                                {
                                    invoiceInfo.InvoiceDate = date;
                                    LogUtil.AddLog($"提取到开票日期: {date:yyyy-MM-dd}");
                                }
                            }
                        });
                    
                    // 4. 购买方名称 - 基于精确坐标：(50, 350) - (250, 370)
                    ExtractTextInRect(words, 
                        54.7, 273.5,273, 302.7, 
                        (text) => {
                            LogUtil.AddLog($"购买方名称区域找到文本: {text}");
                            if (text.Contains("名称") && (text.Contains(":") || text.Contains("：")))
                            {
                                string[] parts = text.Split(new char[] { ':', '：' }, 2);
                                if (parts.Length > 1 && !string.IsNullOrWhiteSpace(parts[1]))
                                {
                                    invoiceInfo.BuyerName = parts[1].Trim();
                                    LogUtil.AddLog($"提取到购买方名称: {invoiceInfo.BuyerName}");
                                }
                            }
                            else if (text.Length > 5 && !text.Contains("税号") && !text.Contains("识别号"))
                            {
                                invoiceInfo.BuyerName = text.Trim();
                                LogUtil.AddLog($"提取到购买方名称: {text}");
                            }
                        });
                    
                    // 5. 购买方纳税人识别号 - 基于精确坐标：(50, 330) - (250, 350)
                    ExtractTextInRect(words, 
                        151.1, 284.7,261.4, 272.9,
                        (text) => {
                            LogUtil.AddLog($"购买方税号区域找到文本: {text}");
                            if (text.Contains("识别号") && (text.Contains(":") || text.Contains("：")))
                            {
                                string[] parts = text.Split(new char[] { ':', '：' }, 2);
                                if (parts.Length > 1 && !string.IsNullOrWhiteSpace(parts[1]))
                                {
                                    string taxNum = parts[1].Trim();
                                    if (Regex.IsMatch(taxNum, @"[0-9A-Z]{15,20}"))
                                    {
                                        invoiceInfo.BuyerTaxNumber = taxNum;
                                        LogUtil.AddLog($"提取到购买方税号: {taxNum}");
                                    }
                                }
                            }
                            else
                            {
                                var match = Regex.Match(text, @"[0-9A-Z]{15,20}");
                                if (match.Success)
                                {
                                    invoiceInfo.BuyerTaxNumber = match.Value;
                                    LogUtil.AddLog($"提取到购买方税号: {match.Value}");
                                }
                            }
                        });
                    
                    // 6. 销售方名称 - 基于精确坐标：(300, 350) - (550, 370)
                    ExtractTextInRect(words, 
                        338.2, 575,273, 302.7,
                        (text) => {
                            LogUtil.AddLog($"销售方名称区域找到文本: {text}");
                            if (text.Contains("名称") && (text.Contains(":") || text.Contains("：")))
                            {
                                string[] parts = text.Split(new char[] { ':', '：' }, 2);
                                if (parts.Length > 1 && !string.IsNullOrWhiteSpace(parts[1]))
                                {
                                    invoiceInfo.SellerName = parts[1].Trim();
                                    LogUtil.AddLog($"提取到销售方名称: {invoiceInfo.SellerName}");
                                }
                            }
                            else if (text.Length > 5 && !text.Contains("税号") && !text.Contains("识别号"))
                            {
                                invoiceInfo.SellerName = text.Trim();
                                LogUtil.AddLog($"提取到销售方名称: {text}");
                            }
                        });
                    
                    // 7. 销售方纳税人识别号 - 基于精确坐标：(300, 330) - (550, 350)
                    ExtractTextInRect(words, 
                        435.9, 575,261.4,272.9,
                        (text) => {
                            LogUtil.AddLog($"销售方税号区域找到文本: {text}");
                            if (text.Contains("识别号") && (text.Contains(":") || text.Contains("：")))
                            {
                                string[] parts = text.Split(new char[] { ':', '：' }, 2);
                                if (parts.Length > 1 && !string.IsNullOrWhiteSpace(parts[1]))
                                {
                                    string taxNum = parts[1].Trim();
                                    if (Regex.IsMatch(taxNum, @"[0-9A-Z]{15,20}"))
                                    {
                                        invoiceInfo.SellerTaxNumber = taxNum;
                                        LogUtil.AddLog($"提取到销售方税号: {taxNum}");
                                    }
                                }
                            }
                            else
                            {
                                var match = Regex.Match(text, @"[0-9A-Z]{15,20}");
                                if (match.Success && match.Value != invoiceInfo.BuyerTaxNumber)
                                {
                                    invoiceInfo.SellerTaxNumber = match.Value;
                                    LogUtil.AddLog($"提取到销售方税号: {match.Value}");
                                }
                            }
                        });
                    
                    //// 8. 商品名称 - 基于精确坐标：(50, 280) - (150, 300)
                    //List<string> itemNames = new List<string>();
                    //ExtractTextInRect(words, 
                    //    50, 150, 
                    //    280, 300, 
                    //    (text) => {
                    //        LogUtil.AddLog($"项目名称区域找到文本: {text}");
                    //        if (text.Length > 2 && !text.Contains("项目") && !text.Contains("货物") && !text.Contains("名称"))
                    //        {
                    //            itemNames.Add(text.Trim());
                    //            LogUtil.AddLog($"找到商品: {text}");
                    //        }
                    //    });
                    
                    //// 9. 规格型号 - 基于精确坐标：(150, 280) - (250, 300)
                    //List<string> specifications = new List<string>();
                    //ExtractTextInRect(words, 
                    //    150, 250, 
                    //    280, 300, 
                    //    (text) => {
                    //        LogUtil.AddLog($"规格型号区域找到文本: {text}");
                    //        if (text.Length > 0 && !text.Contains("规格") && !text.Contains("型号"))
                    //        {
                    //            specifications.Add(text.Trim());
                    //            LogUtil.AddLog($"找到规格型号: {text}");
                    //        }
                    //    });
                    
                    //// 10. 单位 - 基于精确坐标：(250, 280) - (300, 300)
                    //List<string> units = new List<string>();
                    //ExtractTextInRect(words, 
                    //    250, 300, 
                    //    280, 300, 
                    //    (text) => {
                    //        LogUtil.AddLog($"单位区域找到文本: {text}");
                    //        if (text.Length <= 3 && !text.Contains("单位"))
                    //        {
                    //            units.Add(text.Trim());
                    //            LogUtil.AddLog($"找到单位: {text}");
                    //        }
                    //    });
                    
                    //// 11. 数量 - 基于精确坐标：(300, 280) - (350, 300)
                    //List<double> quantities = new List<double>();
                    //ExtractTextInRect(words, 
                    //    300, 350, 
                    //    280, 300, 
                    //    (text) => {
                    //        LogUtil.AddLog($"数量区域找到文本: {text}");
                    //        var match = Regex.Match(text, @"(\d+(?:\.\d+)?)");
                    //        if (match.Success)
                    //        {
                    //            if (double.TryParse(match.Groups[1].Value, out double quantity))
                    //            {
                    //                quantities.Add(quantity);
                    //                LogUtil.AddLog($"找到数量: {quantity}");
                    //            }
                    //        }
                    //    });
                    
                    //// 12. 单价 - 基于精确坐标：(350, 280) - (450, 300)
                    //List<decimal> unitPrices = new List<decimal>();
                    //ExtractTextInRect(words, 
                    //    350, 450, 
                    //    280, 300, 
                    //    (text) => {
                    //        LogUtil.AddLog($"单价区域找到文本: {text}");
                    //        var match = Regex.Match(text, @"[¥￥]?\s*([\d,]+\.?\d*)");
                    //        if (match.Success)
                    //        {
                    //            string priceStr = match.Groups[1].Value.Replace(",", "");
                    //            if (decimal.TryParse(priceStr, out decimal price))
                    //            {
                    //                unitPrices.Add(price);
                    //                LogUtil.AddLog($"找到单价: {price}");
                    //            }
                    //        }
                    //    });
                    
                    //// 13. 金额 - 基于精确坐标：(450, 280) - (500, 300)
                    //List<decimal> amounts = new List<decimal>();
                    //ExtractTextInRect(words, 
                    //    450, 500, 
                    //    280, 300, 
                    //    (text) => {
                    //        LogUtil.AddLog($"金额区域找到文本: {text}");
                    //        var match = Regex.Match(text, @"[¥￥]?\s*([\d,]+\.?\d*)");
                    //        if (match.Success)
                    //        {
                    //            string amountStr = match.Groups[1].Value.Replace(",", "");
                    //            if (decimal.TryParse(amountStr, out decimal amount))
                    //            {
                    //                amounts.Add(amount);
                    //                LogUtil.AddLog($"找到金额: {amount}");
                                    
                    //                // 同时设置总金额
                    //                if (invoiceInfo.TotalAmount == 0)
                    //                {
                    //                    invoiceInfo.TotalAmount = amount;
                    //                    LogUtil.AddLog($"设置总金额: {amount}");
                    //                }
                    //            }
                    //        }
                    //    });
                    
                    //// 14. 税率 - 基于精确坐标：(500, 280) - (550, 300)
                    //List<string> taxRates = new List<string>();
                    //ExtractTextInRect(words, 
                    //    500, 550, 
                    //    280, 300, 
                    //    (text) => {
                    //        LogUtil.AddLog($"税率区域找到文本: {text}");
                    //        var match = Regex.Match(text, @"(\d+)%");
                    //        if (match.Success)
                    //        {
                    //            taxRates.Add(match.Groups[1].Value + "%");
                    //            LogUtil.AddLog($"找到税率: {match.Groups[1].Value}%");
                    //        }
                    //    });
                    
                    //// 15. 税额 - 基于精确坐标：(550, 280) - (595, 300)
                    //List<decimal> taxes = new List<decimal>();
                    //ExtractTextInRect(words, 
                    //    550, 595, 
                    //    280, 300, 
                    //    (text) => {
                    //        LogUtil.AddLog($"税额区域找到文本: {text}");
                    //        var match = Regex.Match(text, @"[¥￥]?\s*([\d,]+\.?\d*)");
                    //        if (match.Success)
                    //        {
                    //            string taxStr = match.Groups[1].Value.Replace(",", "");
                    //            if (decimal.TryParse(taxStr, out decimal tax))
                    //            {
                    //                taxes.Add(tax);
                    //                LogUtil.AddLog($"找到税额: {tax}");
                                    
                    //                // 同时设置总税额
                    //                if (invoiceInfo.TotalTax == 0)
                    //                {
                    //                    invoiceInfo.TotalTax = tax;
                    //                    LogUtil.AddLog($"设置总税额: {tax}");
                    //                }
                    //            }
                    //        }
                    //    });
                    
                    //// 16. 商品/服务明细数据行 - 基于精确坐标：(50, 240) - (595, 260)
                    //// 通常第二行包含商品的完整信息
                    //ExtractTextInRect(words, 
                    //    50, 595, 
                    //    240, 260, 
                    //    (text) => {
                    //        LogUtil.AddLog($"商品明细行区域找到文本: {text}");
                    //        if (text.Length > 5 && !itemNames.Contains(text) && !text.Contains("小计") && !text.Contains("合计"))
                    //        {
                    //            // 如果没有在项目名称区域找到商品，可以从这里添加
                    //            if (itemNames.Count == 0)
                    //            {
                    //                itemNames.Add(text.Trim());
                    //                LogUtil.AddLog($"从明细行区域找到商品: {text}");
                    //            }
                    //        }
                    //    });
                    
                    //// 17. 大写金额 - 基于精确坐标：(50, 100) - (300, 140)
                    //ExtractTextInRect(words, 
                    //    50, 300, 
                    //    100, 140, 
                    //    (text) => {
                    //        LogUtil.AddLog($"大写金额区域找到文本: {text}");
                    //        if (Regex.IsMatch(text, @"[零壹贰叁肆伍陆柒捌玖拾佰仟万亿元角分整圆]"))
                    //        {
                    //            invoiceInfo.AmountInWords = text.Trim();
                    //            LogUtil.AddLog($"提取到大写金额: {text}");
                    //        }
                    //    });
                    
                    // 18. 小写金额/价税合计 - 基于精确坐标：(450, 100) - (550, 140)
                    ExtractTextInRect(words, 
                        438.8, 575, 
                        108.6, 120.2, 
                        (text) => {
                            LogUtil.AddLog($"小写金额区域找到文本: {text}");
                            var match = Regex.Match(text, @"[¥￥]\s*([\d,]+\.\d{2})");
                            if (match.Success)
                            {
                                string totalStr = match.Groups[1].Value.Replace(",", "");
                                if (decimal.TryParse(totalStr, out decimal total))
                                {
                                    invoiceInfo.TotalAmountWithTax = Math.Abs(total);
                                    LogUtil.AddLog($"提取到价税合计: {total}");
                                }
                            }
                        });
                    
                    //// 19. 备注 - 基于精确坐标：(50, 20) - (300, 80)
                    //ExtractTextInRect(words, 
                    //    50, 300, 
                    //    20, 80, 
                    //    (text) => {
                    //        LogUtil.AddLog($"备注区域找到文本: {text}");
                    //        if (text.Contains("备注"))
                    //        {
                    //            string remarkContent = text.Replace("备注", "").Trim();
                    //            remarkContent = remarkContent.TrimStart(':').TrimStart('：').Trim();
                    //            if (!string.IsNullOrEmpty(remarkContent))
                    //            {
                    //                invoiceInfo.Remark = remarkContent;
                    //                LogUtil.AddLog($"提取到备注: {remarkContent}");
                    //            }
                    //        }
                    //        else if (text.Length > 2 && !string.IsNullOrEmpty(text.Trim()))
                    //        {
                    //            // 可能是备注内容
                    //            invoiceInfo.Remark = text.Trim();
                    //            LogUtil.AddLog($"提取到可能的备注内容: {text}");
                    //        }
                    //    });
                    
                    //// 20. 开票人 - 基于精确坐标：(450, 20) - (550, 50)
                    //ExtractTextInRect(words, 
                    //    53.3,200, 
                    //    18.9, 30.3, 
                    //    (text) => {
                    //        LogUtil.AddLog($"开票人区域找到文本: {text}");
                    //        if (text.Contains("开票人"))
                    //        {
                    //            string drawerText = text.Replace("开票人", "").Trim();
                    //            drawerText = drawerText.TrimStart(':').TrimStart('：').Trim();
                    //            if (!string.IsNullOrEmpty(drawerText))
                    //            {
                    //                invoiceInfo.Drawer = drawerText;
                    //                LogUtil.AddLog($"提取到开票人: {drawerText}");
                    //            }
                    //        }
                    //        else if (text.Length <= 5 && text.All(c => char.IsLetterOrDigit(c) || c == ' '))
                    //        {
                    //            invoiceInfo.Drawer = text.Trim();
                    //            LogUtil.AddLog($"提取到可能的开票人: {text}");
                    //        }
                    //    });
                    
                    //// 创建商品条目
                    //for (int i = 0; i < itemNames.Count; i++)
                    //{
                    //    var item = new UmiOcrService.OcrInvoiceItem { Name = itemNames[i] };
                        
                    //    // 添加对应的规格型号、单位、数量、单价信息
                    //    if (i < specifications.Count) item.Specification = specifications[i];
                    //    if (i < units.Count) item.Unit = units[i];
                    //    if (i < quantities.Count) item.Quantity = (decimal)quantities[i];
                    //    if (i < unitPrices.Count) item.UnitPrice = unitPrices[i];
                    //    if (i < amounts.Count) item.Amount = amounts[i];
                    //    if (i < taxRates.Count) 
                    //    {
                    //        // 税率需要转换为decimal (例如: "6%" -> 0.06m)
                    //        string rateStr = taxRates[i].TrimEnd('%');
                    //        if (decimal.TryParse(rateStr, out decimal rate))
                    //        {
                    //            item.TaxRate = rate / 100m;
                    //        }
                    //    }
                    //    if (i < taxes.Count) item.Tax = taxes[i];
                        
                    //    invoiceInfo.Items.Add(item);
                    //}
                    
                    //// 如果没有找到明细项，尝试整体添加
                    //if (invoiceInfo.Items.Count == 0 && !string.IsNullOrEmpty(invoiceInfo.BuyerName))
                    //{
                    //    // 尝试在表格区域找一些有用的信息
                    //    ExtractTextInRect(words, 
                    //        50, 595, 
                    //        240, 300, 
                    //        (text) => {
                    //            LogUtil.AddLog($"商品表格区域找到文本: {text}");
                    //            if (text.Length > 5 && !text.StartsWith("项目") && !text.StartsWith("金额") && !text.StartsWith("税率"))
                    //            {
                    //                var item = new UmiOcrService.OcrInvoiceItem { Name = text.Trim() };
                    //                if (invoiceInfo.TotalAmount > 0) 
                    //                {
                    //                    item.Amount = invoiceInfo.TotalAmount;
                    //                }
                    //                if (invoiceInfo.TotalTax > 0)
                    //                {
                    //                    item.Tax = invoiceInfo.TotalTax;
                    //                }
                                    
                    //                invoiceInfo.Items.Add(item);
                    //                LogUtil.AddLog($"添加了一个商品条目: {text}");
                    //            }
                    //        });
                    //}
                    
                    // 设置处理方式备注
                    if (string.IsNullOrEmpty(invoiceInfo.Remark))
                    {
                        invoiceInfo.Remark = "通过固定坐标范围提取";
                    }
                    else
                    {
                        invoiceInfo.Remark += " (通过固定坐标范围提取)";
                    }
                    
                    // 检查提取是否成功
                    bool hasBasicInfo = !string.IsNullOrEmpty(invoiceInfo.InvoiceNumber) && invoiceInfo.InvoiceDate.HasValue;
                    bool hasPartyInfo = !string.IsNullOrEmpty(invoiceInfo.BuyerName) || !string.IsNullOrEmpty(invoiceInfo.SellerName);
                    bool hasAmountInfo = invoiceInfo.TotalAmountWithTax > 0;
                    
                    LogUtil.AddLog($"提取结果: 基本信息={hasBasicInfo}, 购销方信息={hasPartyInfo}, 金额信息={hasAmountInfo}");
                    
                    return invoiceInfo;
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"基于固定坐标识别发票信息时出错: {ex.Message}");
                return new UmiOcrService.OcrInvoiceInfo
                {
                    Remark = $"基于固定坐标识别发票失败: {ex.Message}"
                };
            }
        }
        
        /// <summary>
        /// 在指定矩形区域内提取文本
        /// </summary>
        /// <param name="words">所有文本元素</param>
        /// <param name="x1">左边界坐标</param>
        /// <param name="x2">右边界坐标</param>
        /// <param name="y1">下边界坐标</param>
        /// <param name="y2">上边界坐标</param>
        /// <param name="textProcessor">文本处理回调函数</param>
        private static void ExtractTextInRect(List<UglyToad.PdfPig.Content.Word> words, 
                                             double x1, double x2, double y1, double y2, 
                                             Action<string> textProcessor)
        {
            // 找出指定矩形区域内的所有单词
            var wordsInRect = words.Where(w => 
                w.BoundingBox.Left >= x1 && 
                w.BoundingBox.Right <= x2 && 
                w.BoundingBox.Bottom >= y1 && 
                w.BoundingBox.Top <= y2)
                .OrderBy(w => w.BoundingBox.Bottom)  // 先按Y坐标排序（从下到上）
                .ThenBy(w => w.BoundingBox.Left)     // 再按X坐标排序（从左到右）
                .ToList();
            
            if (wordsInRect.Count > 0)
            {
                // 将同一位置的单词组合在一起形成文本
                var lines = wordsInRect
                    .GroupBy(w => Math.Round(w.BoundingBox.Bottom, 1))  // 按Y坐标分组
                    .OrderByDescending(g => g.Key);  // 从上到下排序
                
                foreach (var line in lines)
                {
                    // 将同一行的单词按从左到右排序
                    var sortedWords = line.OrderBy(w => w.BoundingBox.Left).ToList();
                    string lineText = string.Join("", sortedWords.Select(w => w.Text));
                    
                    if (!string.IsNullOrWhiteSpace(lineText))
                    {
                        textProcessor(lineText.Trim());
                    }
                }
            }
        }
        
        /// <summary>
        /// 可视化发票中的坐标区域，用于调试和校准坐标
        /// </summary>
        public static void VisualizeCoordinates(string pdfFilePath, string outputImagePath)
        {
            try
            {
                LogUtil.AddLog($"开始可视化PDF文件坐标: {pdfFilePath}");
                byte[] pdfBytes = File.ReadAllBytes(pdfFilePath);
                
                using (var document = PdfDocument.Open(pdfBytes))
                {
                    Page firstPage = document.GetPages().FirstOrDefault();
                    if (firstPage == null)
                    {
                        LogUtil.AddErrorLog("PDF文件不包含页面");
                        return;
                    }
                    
                    double pageWidth = firstPage.Width;
                    double pageHeight = firstPage.Height;
                    LogUtil.AddLog($"PDF页面尺寸: 宽={pageWidth}, 高={pageHeight}");
                    
                    // 首先将PDF转换为图像
                    byte[] pageImageBytes = ConvertPdfPageToImage(pdfBytes, 0);
                    if (pageImageBytes == null || pageImageBytes.Length == 0)
                    {
                        LogUtil.AddErrorLog("无法将PDF转换为图像");
                        return;
                    }
                    
                    // 使用System.Drawing绘制坐标和文本位置
                    using (MemoryStream ms = new MemoryStream(pageImageBytes))
                    using (System.Drawing.Bitmap bitmap = new System.Drawing.Bitmap(ms))
                    using (System.Drawing.Graphics graphics = System.Drawing.Graphics.FromImage(bitmap))
                    using (System.Drawing.Pen redPen = new System.Drawing.Pen(System.Drawing.Color.Red, 1))
                    using (System.Drawing.Brush redBrush = new System.Drawing.SolidBrush(System.Drawing.Color.Red))
                    using (System.Drawing.Font font = new System.Drawing.Font("Arial", 8))
                    {
                        // 绘制网格线
                        for (int x = 0; x < pageWidth; x += 50)
                        {
                            graphics.DrawLine(redPen, x, 0, x, (int)pageHeight);
                            graphics.DrawString(x.ToString(), font, redBrush, x, 10);
                        }
                        
                        for (int y = 0; y < pageHeight; y += 50)
                        {
                            graphics.DrawLine(redPen, 0, y, (int)pageWidth, y);
                            graphics.DrawString(y.ToString(), font, redBrush, 10, y);
                        }
                        
                        // 绘制所有文本元素的边界框
                        foreach (var word in firstPage.GetWords())
                        {
                            // PDF坐标系原点在左下角，需要转换为图像坐标系（左上角为原点）
                            float left = (float)word.BoundingBox.Left;
                            float bottom = (float)(pageHeight - word.BoundingBox.Bottom);
                            float width = (float)word.BoundingBox.Width;
                            float height = (float)word.BoundingBox.Height;
                            
                            // 绘制文本边界框
                            graphics.DrawRectangle(redPen, left, bottom - height, width, height);
                            
                            // 绘制文本及坐标
                            string coordText = $"{word.Text} ({(int)left},{(int)(pageHeight - bottom)})";
                            graphics.DrawString(coordText, font, redBrush, left, bottom);
                        }
                        
                        // 保存图像
                        try
                        {
                            // 使用保存为PNG的编码器
                            System.Drawing.Imaging.EncoderParameters encoderParams = new System.Drawing.Imaging.EncoderParameters(1);
                            encoderParams.Param[0] = new System.Drawing.Imaging.EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 100L);
                            System.Drawing.Imaging.ImageCodecInfo pngEncoder = GetEncoderInfo("image/png");
                            bitmap.Save(outputImagePath, pngEncoder, encoderParams);
                            
                            LogUtil.AddLog($"坐标可视化图像已保存到: {outputImagePath}");
                        }
                        catch (Exception ex)
                        {
                            LogUtil.AddErrorLog($"保存坐标可视化图像时出错: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"可视化坐标时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 将PDF的指定页面转换为图像
        /// </summary>
        private static byte[] ConvertPdfPageToImage(byte[] pdfBytes, int pageIndex = 0)
        {
            try
            {
                using (var pdfDocument = UglyToad.PdfPig.PdfDocument.Open(pdfBytes))
                {
                    if (pageIndex >= pdfDocument.NumberOfPages)
                    {
                        return null;
                    }
                    
                    var page = pdfDocument.GetPage(pageIndex + 1); // PDF页码从1开始
                    
                    // 获取页面尺寸
                    double width = page.Width;
                    double height = page.Height;
                    
                    // 创建一个白色背景的图像
                    using (var image = new SixLabors.ImageSharp.Image<Rgba32>((int)width, (int)height))
                    {
                        // 填充白色背景（使用像素操作而不是Fill方法）
                        for (int y = 0; y < image.Height; y++)
                        {
                            for (int x = 0; x < image.Width; x++)
                            {
                                image[x, y] = new Rgba32(255, 255, 255, 255); // 白色
                            }
                        }
                        
                        // 转换为字节数组
                        using (var ms = new MemoryStream())
                        {
                            // 使用PNG编码器保存图像
                            image.Save(ms, new SixLabors.ImageSharp.Formats.Png.PngEncoder());
                            return ms.ToArray();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"将PDF转换为图像时出错: {ex.Message}");
                return null;
            }
        }
        

        /// <summary>
        /// 获取指定MIME类型的图像编码器
        /// </summary>
        private static System.Drawing.Imaging.ImageCodecInfo GetEncoderInfo(string mimeType)
        {
            System.Drawing.Imaging.ImageCodecInfo[] encoders = System.Drawing.Imaging.ImageCodecInfo.GetImageEncoders();
            for (int i = 0; i < encoders.Length; i++)
            {
                if (encoders[i].MimeType == mimeType)
                {
                    return encoders[i];
                }
            }
            return null;
        }
    }
}
