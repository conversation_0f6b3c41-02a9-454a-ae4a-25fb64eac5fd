# EnumGtisServiceChangeProject 枚举对照表

## 📋 系统枚举定义

根据 `Model/Enum/EnumUtil.cs` 中的定义：

| 枚举值 | 枚举名称 | 中文描述 | 说明 |
|--------|----------|----------|------|
| 1 | ApplyResidualService | 尾款申请剩余服务 | 原有枚举 |
| 2 | ChangeServiceContent | 变更服务内容 | 原有枚举 |
| 3 | DelayPersonalServiceDays | 个人服务天数延期 | 原有枚举 |
| 4 | DelayCoupons | 优惠券延期 | 原有枚举 |
| 5 | OtherReason | 其他 | 原有枚举 |
| 6 | OpenSaleWits | 开通SaleWits | 新增枚举 |
| 7 | SalesWitsRecharge | SalesWits充值 | 新增枚举 |
| 8 | SalesWitsAddAccount | SalesWits新增子账号 | 新增枚举 |

## 🔧 数据库配置对应关系

在 `crm_service_change_reason_field_config` 表中，`ChangeReasonEnum` 字段应该使用上述枚举值：

### SalesWits 相关配置

| 服务类型 | 变更原因枚举值 | 对应枚举名称 | 说明 |
|----------|----------------|--------------|------|
| 2 (GTIS) | 6 | OpenSaleWits | 开通SaleWits |
| 2 (GTIS) | 7 | SalesWitsRecharge | SalesWits充值 |
| 2 (GTIS) | 8 | SalesWitsAddAccount | SalesWits新增子账号 |
| 5 (慧思) | 6 | OpenSaleWits | 开通SaleWits |
| 5 (慧思) | 7 | SalesWitsRecharge | SalesWits充值 |
| 5 (慧思) | 8 | SalesWitsAddAccount | SalesWits新增子账号 |

## ⚠️ 常见错误

1. **错误的枚举值**：使用了不存在的枚举值（如 9、10、11 等）
2. **枚举值错位**：将 SalesWitsRecharge 配置为 10，应该是 7
3. **表名错误**：实际表名是 `crm_service_change_reason_field_config`，不是 `crm_service_change_field_permission`

## 🔍 验证查询

```sql
-- 查看当前配置
SELECT
    ServiceType,
    ChangeReasonEnum,
    FieldKey,
    FieldName,
    CASE ChangeReasonEnum
        WHEN 1 THEN '尾款申请剩余服务'
        WHEN 2 THEN '变更服务内容'
        WHEN 3 THEN '个人服务天数延期'
        WHEN 4 THEN '优惠券延期'
        WHEN 5 THEN '其他'
        WHEN 6 THEN '开通SaleWits'
        WHEN 7 THEN 'SalesWits充值'
        WHEN 8 THEN 'SalesWits新增子账号'
        ELSE CONCAT('❌ 错误枚举值: ', ChangeReasonEnum)
    END AS ChangeReasonDescription
FROM crm_service_change_reason_field_config
WHERE ChangeReasonEnum IN (6, 7, 8, 9, 10, 11)
ORDER BY ChangeReasonEnum, ServiceType;
```

## 📝 更新说明

- 原有的 1-5 枚举值保持不变
- 新增的 6-8 枚举值对应 SalesWits 相关功能
- 确保数据库配置与代码枚举定义完全一致
