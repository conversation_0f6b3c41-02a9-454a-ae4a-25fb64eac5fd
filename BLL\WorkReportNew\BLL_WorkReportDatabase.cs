using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.Enum;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Common.JWT;
using CRM2_API.Model.System;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Log;
using QCloud;
using Newtonsoft.Json;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbCommon;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告常量定义
    /// </summary>
    public static class WorkReportConstants
    {
        /// <summary>
        /// 附件模块常量
        /// </summary>
        public static class Attachment
        {
            public const string ModuleKey = "attachments";
            public const string ModuleTitle = "附件";
            public const int ModuleOrder = 99;
            public const string SectionKey = "files";
            public const string SectionTitle = "文件附件";
            public const int SectionOrder = 0;
        }

        /// <summary>
        /// 工作量数据验证常量
        /// </summary>
        public static class WorkDataValidation
        {
            /// <summary>
            /// 工作量数据最大值
            /// </summary>
            public const int MaxWorkDataValue = 999999;

            /// <summary>
            /// 工作量数据最小值
            /// </summary>
            public const int MinWorkDataValue = 0;
        }
    }

    /// <summary>
    /// 工作报告数据库操作业务逻辑类
    /// 实现真实的数据库操作，替换Mock实现
    /// </summary>
    public partial class BLL_WorkReportDatabase : BaseBLL<BLL_WorkReportDatabase>
    {
        #region 创建报告（固定字段结构）

        /// <summary>
        /// 创建报告（固定字段结构）
        /// 严格按照第一阶段定义的接口和模型
        /// </summary>
        /// <param name="input">创建输入（固定字段）</param>
        /// <returns>创建结果</returns>
        public SaveReport_Out CreateReportFixed(CreateReportFixed_In input)
        {
            // 1. 数据验证
            var validationResult = ValidateCreateReportInput(input);
            if (!validationResult.IsValid)
            {
                throw new ApiException(validationResult.ErrorMessage);
            }
            // 新增校验：提交时必须有接收人
            if (input.Status == EnumReportStatus.Submitted && (input.ReceiverIds == null || input.ReceiverIds.Count == 0))
            {
                throw new ApiException("提交报告时必须至少选择一个接收人");
            }

            // 2. 获取当前用户信息
            var currentUserId = UserId;
            var currentUserName = GetCurrentUserName();

            // 3. 检查是否已存在相同日期的报告
            var existingReport = DbOpe_crm_report_base.Instance.CheckReportExists(
                currentUserId, input.ReportType, input.ReportDate);

            if (existingReport)
            {
                throw new ApiException($"该日期已存在{GetReportTypeName(input.ReportType)}，请选择其他日期或修改现有报告");
            }
            var reportId = Guid.NewGuid().ToString();
            DbOpe_crm_report_base.Instance.TransDeal(() =>
            {
                // 4. 创建报告基础信息
                var now = DateTime.Now;
                var reportBase = new Db_crm_report_base
                {
                    Id = reportId,
                    ReportType = (int)input.ReportType,
                    Title = input.Title,
                    UserId = currentUserId,
                    UserName = currentUserName,
                    Status = (int)input.Status,
                    ReportDate = input.ReportDate,
                    ReportYear = input.ReportDate.Year,
                    ReportMonth = input.ReportDate.Month,
                    CreateUser = currentUserId,
                    CreateDate = now,
                    UpdateUser = currentUserId,
                    UpdateDate = now
                };

                // 如果是提交状态，计算并设置提交状态和时间
                if (input.Status == EnumReportStatus.Submitted)
                {
                    reportBase.SubmitTime = now;
                    
                    // 计算提交状态，如果计算失败则默认为准时
                    try
                    {
                        reportBase.SubmitStatus = (int)CalculateSubmitStatus((int)input.Status, now, input.ReportDate, input.ReportType);
                    }
                    catch (Exception ex)
                    {
                        throw new ApiException($"计算提交状态失败，报告类型：{input.ReportType}，报告日期：{input.ReportDate}，错误：{ex.Message}");
                    }
                }

                // 5. 保存报告基础信息
                var createdReportId = DbOpe_crm_report_base.Instance.CreateReportBase(reportBase);
                if (string.IsNullOrEmpty(createdReportId))
                {
                    throw new ApiException("保存报告基础信息失败");
                }

                // 使用创建成功的报告ID
                reportId = createdReportId;

                // 6. 保存报告内容
                SaveReportContent(reportId, input);

                // 7. 保存接收人和抄送人
                SaveReportReceivers(reportId, input);

                // 8. 处理附件
                if (input.Files != null && input.Files.Count > 0)
                {
                    try
                    {
                        var attachmentList = SaveAttachments(
                            reportId,
                            (int)input.ReportType,
                            input.ReportDate,
                            input.Title,
                            WorkReportConstants.Attachment.ModuleKey,
                            WorkReportConstants.Attachment.ModuleTitle,
                            WorkReportConstants.Attachment.ModuleOrder,
                            WorkReportConstants.Attachment.SectionKey,
                            WorkReportConstants.Attachment.SectionTitle,
                            WorkReportConstants.Attachment.SectionOrder,
                            input.Files);

                        if (attachmentList.Count > 0)
                        {
                            DbOpe_crm_report_attachment.Instance.CreateAttachments(attachmentList);
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new ApiException($"保存附件失败：{ex.Message}");
                    }
                }

                // 9. 更新报告数量字段（包括附件数量）
                UpdateReportCounts(reportId);
            });
            var message = $"{GetReportTypeName(input.ReportType)}创建成功";
            return new SaveReport_Out
            {
                Id = reportId,
                Success = true,
                Message = message
            };
        }

        /// <summary>
        /// 更新报告（固定字段结构）
        /// 严格按照第一阶段定义的接口和模型
        /// </summary>
        /// <param name="input">更新输入（固定字段）</param>
        /// <returns>更新结果</returns>
        public SaveReport_Out UpdateReportFixed(UpdateReportFixed_In input)
        {
            try
            {
                // 1. 数据验证
                var validationResult = ValidateUpdateReportInput(input);
                if (!validationResult.IsValid)
                {
                    throw new ApiException(validationResult.ErrorMessage);
                }
                // 新增校验：提交时必须有接收人
                if (input.Status == EnumReportStatus.Submitted && (input.ReceiverIds == null || input.ReceiverIds.Count == 0))
                {
                    throw new ApiException("提交报告时必须至少选择一个接收人");
                }

                // 2. 获取当前用户信息
                var currentUserId = UserId;
                var currentUserName = GetCurrentUserName();

                // 3. 检查报告是否存在
                var existingReport = DbOpe_crm_report_base.Instance.GetData(x => x.Id == input.Id && x.Deleted == false);
                if (existingReport == null)
                {
                    throw new ApiException("报告不存在或已被删除");
                }

                // 4. 权限检查
                if (!CanEditReport(existingReport, currentUserId))
                {
                    // CanEditReport方法内部会抛出具体的错误信息
                    return new SaveReport_Out
                    {
                        Success = false,
                        Message = "修改失败：权限不足或报告状态不允许修改"
                    };
                }

                // 5. 状态变更验证：已提交的报告不能改为草稿状态
                if (existingReport.Status == (int)EnumReportStatus.Submitted && input.Status == EnumReportStatus.Draft)
                {
                    throw new ApiException("已提交的报告不能保存为草稿状态");
                }

            var now = DateTime.Now;
            // 如果状态从草稿变为已提交，计算并设置提交状态和时间
            if (input.Status == EnumReportStatus.Submitted && existingReport.Status != (int)EnumReportStatus.Submitted)
            {
                existingReport.SubmitTime = now;
                
                // 计算提交状态，如果计算失败则默认为准时
                try
                {
                    existingReport.SubmitStatus = (int)CalculateSubmitStatus((int)input.Status, now, input.ReportDate, input.ReportType);
                }
                catch (Exception ex)
                {
                    throw new ApiException($"计算提交状态失败，报告ID：{input.Id}，错误：{ex.Message}");
                }
            }
                        // 6. 更新报告基础信息

            existingReport.Title = input.Title;
            existingReport.ReportDate = input.ReportDate;
            existingReport.Status = (int)input.Status;
            existingReport.UpdateUser = currentUserId;
            existingReport.UpdateDate = now;

            DbOpe_crm_report_base.Instance.TransDeal(() =>
            {
                // 6. 保存基础信息更新
                DbOpe_crm_report_base.Instance.Update(existingReport);

                // 7. 删除原有的相关数据（除了附件，附件单独处理）
                DeleteExistingReportDataExceptAttachments(input.Id);

                // 8. 保存新的报告内容
                SaveReportContent(input.Id, input);

                // 9. 保存接收人和抄送人
                SaveReportReceivers(input.Id, input);

                // 10. 增量更新附件
                var attachmentResult = UpdateReportAttachmentsIncremental(input.Id, existingReport.Title, input.KeepAttachmentIds, input.Files, currentUserId, currentUserName);
                if (!attachmentResult.Success)
                {
                    LogUtil.AddErrorLog($"更新附件失败：{attachmentResult.Message}");
                    throw new ApiException($"更新附件失败：{attachmentResult.Message}");
                }
                else
                {
                    Console.WriteLine($"报告附件更新成功：{attachmentResult.Message}");
                }
            });

            // 事务结束后，更新报告数量字段（包括附件数量）
            UpdateReportCounts(input.Id);

            var message = $"报告更新成功";
                Console.WriteLine($"报告更新成功，报告ID：{input.Id}，用户：{currentUserName}");

                return new SaveReport_Out
                {
                    Id = input.Id,
                    Success = true,
                    Message = message
                };
            }
            catch (ApiException)
            {
                // 重新抛出ApiException，保持原有的错误信息
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新报告失败，报告ID：{input.Id}，错误：{ex.Message}", ex);
                throw new ApiException($"更新报告失败：{ex.Message}");
            }
        }

        #endregion

        #region 搜索报告列表

        /// <summary>
        /// 搜索报告列表
        /// </summary>
        /// <param name="input">搜索条件</param>
        /// <returns>报告列表</returns>
        public ApiTableOut<SearchReportList_Out> SearchReportList(SearchReportList_In input)
        {
            try
            {
                // 1. 验证输入参数
                if (input == null)
                {
                    throw new ArgumentException("搜索条件不能为空");
                }

                // 2. 获取当前用户信息
                var currentUserId = UserId;
                var currentUserName = GetCurrentUserName();

                // 3. 构建查询条件
                var whereExp = SqlSugar.Expressionable.Create<Db_crm_report_base>();

                // 基础条件：未删除
                whereExp.And(r => r.Deleted == false);

                // 报告类型筛选
                if (input.ReportTypes != null && input.ReportTypes.Count > 0)
                {
                    var reportTypeIds = input.ReportTypes.Select(x => (int)x).ToList();
                    whereExp.And(r => reportTypeIds.Contains(r.ReportType));
                }

                // 日期范围筛选
                if (input.StartDate.HasValue)
                {
                    whereExp.And(r => r.ReportDate >= input.StartDate.Value);
                }

                if (input.EndDate.HasValue)
                {
                    whereExp.And(r => r.ReportDate <= input.EndDate.Value);
                }

                // 状态筛选
                if (input.StatusList != null && input.StatusList.Count > 0)
                {
                    var statusIds = input.StatusList.Select(x => (int)x).ToList();
                    whereExp.And(r => statusIds.Contains(r.Status));
                }

                // 创建者筛选
                if (input.UserIds != null && input.UserIds.Count > 0)
                {
                    whereExp.And(r => input.UserIds.Contains(r.CreateUser));
                }

                // 标题关键字搜索
                if (!string.IsNullOrEmpty(input.Keyword))
                {
                    whereExp.And(r => r.Title.Contains(input.Keyword));
                }

                // 部门筛选
                if (input.DepartmentIds != null && input.DepartmentIds.Count > 0)
                {
                    // 通过用户表关联部门筛选
                    var userQuery = DbContext.Crm2Db.Queryable<Db_v_userwithorg>()
                        .Where(u => input.DepartmentIds.Contains(u.OrganizationId))
                        .Select(u => u.Id)
                        .ToList();
                    if (userQuery.Any())
                    {
                        whereExp.And(r => userQuery.Contains(r.CreateUser));
                    }
                }

                // 创建时间范围筛选
                if (input.CreateStartDate.HasValue)
                {
                    whereExp.And(r => r.CreateDate >= input.CreateStartDate.Value);
                }

                if (input.CreateEndDate.HasValue)
                {
                    whereExp.And(r => r.CreateDate <= input.CreateEndDate.Value);
                }

                // 提交时间范围筛选
                if (input.SubmitStartDate.HasValue)
                {
                    whereExp.And(r => r.SubmitTime >= input.SubmitStartDate.Value);
                }

                if (input.SubmitEndDate.HasValue)
                {
                    whereExp.And(r => r.SubmitTime <= input.SubmitEndDate.Value);
                }

                // 提交状态筛选
                if (input.SubmitStatusList != null && input.SubmitStatusList.Count > 0)
                {
                    var submitStatusIds = input.SubmitStatusList.Select(x => (int)x).ToList();
                    
                    // 构建提交状态筛选条件
                    var submitStatusExp = SqlSugar.Expressionable.Create<Db_crm_report_base>();
                    
                    foreach (var status in input.SubmitStatusList)
                    {
                        if (status == EnumSubmitStatus.NotSubmitted)
                        {
                            // 未提交状态：SubmitStatus为null或等于NotSubmitted值
                            submitStatusExp.Or(r => r.SubmitStatus == null || r.SubmitStatus == (int)EnumSubmitStatus.NotSubmitted);
                        }
                        else
                        {
                            // 其他状态：SubmitStatus有具体值
                            submitStatusExp.Or(r => r.SubmitStatus.HasValue && r.SubmitStatus.Value == (int)status);
                        }
                    }
                    
                    whereExp.And(submitStatusExp.ToExpression());
                }

                // 附件筛选
                if (input.HasAttachments.HasValue)
                {
                    if (input.HasAttachments.Value)
                    {
                        whereExp.And(r => r.AttachmentCount > 0);
                    }
                    else
                    {
                        whereExp.And(r => r.AttachmentCount == 0);
                    }
                }

                // 客户数量范围筛选
                if (input.CustomerCountMin.HasValue)
                {
                    whereExp.And(r => r.CustomerCount >= input.CustomerCountMin.Value);
                }

                if (input.CustomerCountMax.HasValue)
                {
                    whereExp.And(r => r.CustomerCount <= input.CustomerCountMax.Value);
                }

                // 是否逾期筛选 - 在数据库层面处理
                if (input.IsOverdue.HasValue)
                {
                    if (input.IsOverdue.Value)
                    {
                        // 逾期：SubmitStatus为null或NotSubmitted
                        whereExp.And(r => r.SubmitStatus == null || r.SubmitStatus == (int)EnumSubmitStatus.NotSubmitted);
                    }
                    else
                    {
                        // 未逾期：SubmitStatus不为null且不是NotSubmitted
                        whereExp.And(r => r.SubmitStatus != null && r.SubmitStatus != (int)EnumSubmitStatus.NotSubmitted);
                    }
                }

                // 未读报告筛选 - 在数据库层面处理
                if (input.OnlyUnreadReports.HasValue && input.OnlyUnreadReports.Value)
                {
                    // 查询当前用户作为接收人或抄送人的已读报告ID
                    var readReportIds = new List<string>();
                    
                    // 作为接收人的已读报告
                    var receivedReadIds = DbOpe_crm_report_receiver.Instance.GetDataList(x =>
                        x.ReceiverId == currentUserId && 
                        x.Status == (int)EnumReceiverStatus.Read && 
                        x.Deleted == false)
                        .Select(x => x.ReportId)
                        .ToList();
                    readReportIds.AddRange(receivedReadIds);
                    
                    // 作为抄送人的已读报告
                    var ccReadIds = DbOpe_crm_report_cc.Instance.GetDataList(x =>
                        x.CcUserId == currentUserId && 
                        x.Status == (int)EnumReceiverStatus.Read && 
                        x.Deleted == false)
                        .Select(x => x.ReportId)
                        .ToList();
                    readReportIds.AddRange(ccReadIds);
                    
                    // 排除已读的报告
                    if (readReportIds.Any())
                    {
                        whereExp.And(r => !readReportIds.Contains(r.Id));
                    }
                    
                    // 排除自己创建的报告（创建者本身就是已知状态，不应该出现在未读列表中）
                    whereExp.And(r => r.CreateUser != currentUserId);
                }

                // 4. 权限控制：根据数据权限过滤
                var dataPermissionFilter = GetDataPermissionFilter(currentUserId);
                if (!string.IsNullOrEmpty(dataPermissionFilter) && dataPermissionFilter == "personal_access")
                {
                    // 普通用户权限过滤：只能查看自己创建的报告、接收的报告、抄送的报告
                    var permissionExp = SqlSugar.Expressionable.Create<Db_crm_report_base>();

                    // 如果不是只查看未读报告，则包含自己创建的报告
                    if (!(input.OnlyUnreadReports.HasValue && input.OnlyUnreadReports.Value))
                    {
                        // 1. 自己创建的报告（可以查看所有状态，包括草稿）
                        permissionExp.Or(r => r.CreateUser == currentUserId);
                    }

                    // 2. 自己是接收人的报告（只能查看已提交的报告，不能查看草稿）
                    var receivedReportIds = DbOpe_crm_report_receiver.Instance.GetDataList(x =>
                        x.ReceiverId == currentUserId && x.Deleted == false)
                        .Select(x => x.ReportId).ToList();
                    if (receivedReportIds.Any())
                    {
                        permissionExp.Or(r => receivedReportIds.Contains(r.Id) && r.Status == (int)EnumReportStatus.Submitted);
                    }

                    // 3. 自己是抄送人的报告（只能查看已提交的报告，不能查看草稿）
                    var ccReportIds = DbOpe_crm_report_cc.Instance.GetDataList(x =>
                        x.CcUserId == currentUserId && x.Deleted == false)
                        .Select(x => x.ReportId).ToList();
                    if (ccReportIds.Any())
                    {
                        permissionExp.Or(r => ccReportIds.Contains(r.Id) && r.Status == (int)EnumReportStatus.Submitted);
                    }

                    whereExp.And(permissionExp.ToExpression());
                }
                // 如果是后台管理人员，dataPermissionFilter为空，不添加任何权限限制

                // 接收人筛选
                if (!string.IsNullOrEmpty(input.ReceiverId))
                {
                    var receivedReportIds = DbOpe_crm_report_receiver.Instance.GetDataList(x =>
                        x.ReceiverId == input.ReceiverId && x.Deleted == false)
                        .Select(x => x.ReportId).ToList();
                    if (receivedReportIds.Any())
                    {
                        whereExp.And(r => receivedReportIds.Contains(r.Id));
                    }
                    else
                    {
                        // 如果没有找到任何报告，返回空结果
                        whereExp.And(r => false);
                    }
                }

                // 抄送人筛选
                if (!string.IsNullOrEmpty(input.CcId))
                {
                    var ccReportIds = DbOpe_crm_report_cc.Instance.GetDataList(x =>
                        x.CcUserId == input.CcId && x.Deleted == false)
                        .Select(x => x.ReportId).ToList();
                    if (ccReportIds.Any())
                    {
                        whereExp.And(r => ccReportIds.Contains(r.Id));
                    }
                    else
                    {
                        // 如果没有找到任何报告，返回空结果
                        whereExp.And(r => false);
                    }
                }

                // 权限筛选
                if (input.OnlyMyReports.HasValue)
                {
                    if (input.OnlyMyReports.Value)
                    {
                        // 只查询我创建的
                    whereExp.And(r => r.CreateUser == currentUserId);
                    }
                    else
                    {
                        // 排除我创建的（查询非我创建的）
                        whereExp.And(r => r.CreateUser != currentUserId);
                    }
                }
                else if (input.OnlyRelatedReports.HasValue && input.OnlyRelatedReports.Value)
                {
                    // 只查询我相关的（创建、接收、抄送）
                    var permissionExp = SqlSugar.Expressionable.Create<Db_crm_report_base>();

                    // 1. 自己创建的报告
                    permissionExp.Or(r => r.CreateUser == currentUserId);

                    // 2. 自己是接收人的报告
                    var receivedReportIds = DbOpe_crm_report_receiver.Instance.GetDataList(x =>
                        x.ReceiverId == currentUserId && x.Deleted == false)
                        .Select(x => x.ReportId).ToList();
                    if (receivedReportIds.Any())
                    {
                        permissionExp.Or(r => receivedReportIds.Contains(r.Id));
                    }

                    // 3. 自己是抄送人的报告
                    var ccReportIds = DbOpe_crm_report_cc.Instance.GetDataList(x =>
                        x.CcUserId == currentUserId && x.Deleted == false)
                        .Select(x => x.ReportId).ToList();
                    if (ccReportIds.Any())
                    {
                        permissionExp.Or(r => ccReportIds.Contains(r.Id));
                    }

                    whereExp.And(permissionExp.ToExpression());
                }

                // 5. 执行优化查询 - 使用JOIN减少N+1查询
                var total = 0;

                // 使用JOIN查询，一次性获取报告和用户信息
                var query = DbContext.Crm2Db.Queryable<Db_crm_report_base>()
                    .LeftJoin<Db_v_userwithorg>((r, u) => r.CreateUser == u.Id)
                    .Where(whereExp.ToExpression());

                var reportWithUserList = query
                    .OrderByDescending((r, u) => r.CreateDate)
                    .Select((r, u) => new
                    {
                        Report = r,
                        UserName = u.Name,
                        DepartmentName = u.OrgFullName
                    })
                    .ToPageList(input.PageNumber, input.PageSize, ref total);

                // 提取报告ID列表，用于批量查询关联数据
                var reportIds = reportWithUserList.Select(r => r.Report.Id).ToList();
                var reportList = reportWithUserList.Select(r => r.Report).ToList();

                // 6. 批量查询需要实时获取的数据（数量字段已存储在base表中）
                var realTimeData = GetRealTimeData(reportIds, currentUserId);

                // 7. 构建结果列表
                var allResults = reportWithUserList.Select(item => new SearchReportList_Out
                {
                    Id = item.Report.Id,
                    ReportType = (EnumReportType)item.Report.ReportType,
                    Title = item.Report.Title,
                    UserName = item.UserName ?? "未知用户",
                    UserId = item.Report.CreateUser,
                    DepartmentName = item.DepartmentName ?? "",
                    ReportDate = item.Report.ReportDate.ToString("yyyy-MM-dd"),
                    CreateDate = item.Report.CreateDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    UpdateDate = item.Report.UpdateDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    Status = (EnumReportStatus)item.Report.Status,
                    CanEdit = item.Report.CreateUser == currentUserId,
                    CanDelete = item.Report.CreateUser == currentUserId,
                    CanComment = HasCommentPermission(item.Report.Id, currentUserId),
                    
                    // 使用存储的数量字段，避免查询关联表
                    AttachmentCount = item.Report.AttachmentCount,
                    HasAttachments = item.Report.AttachmentCount > 0,
                    CustomerCount = item.Report.CustomerCount,
                    LikeCount = item.Report.LikeCount,
                    CommentCount = item.Report.CommentCount,
                    CcCount = item.Report.CcCount,
                    ReceiverNames = realTimeData.ReceiverNames.ContainsKey(item.Report.Id) ? realTimeData.ReceiverNames[item.Report.Id] : new List<string>(),
                    CcNames = realTimeData.CcNames.ContainsKey(item.Report.Id) ? realTimeData.CcNames[item.Report.Id] : new List<string>(),

                    // 状态计算 - 直接读取存储的字段
                    SubmitStatus = item.Report.SubmitStatus.HasValue ? (EnumSubmitStatus)item.Report.SubmitStatus.Value : EnumSubmitStatus.NotSubmitted,
                    SubmitDate = item.Report.SubmitTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? (item.Report.Status == (int)EnumReportStatus.Submitted ? item.Report.CreateDate.ToString("yyyy-MM-dd HH:mm:ss") : null),
                    SubmitUserId = item.Report.Status == (int)EnumReportStatus.Submitted ? item.Report.CreateUser : null,
                    SubmitUserName = item.Report.Status == (int)EnumReportStatus.Submitted ? item.UserName : null,
                    IsLate = item.Report.SubmitStatus.HasValue && item.Report.SubmitStatus.Value == (int)EnumSubmitStatus.Late,
                    // IsOverdue：SubmitStatus为null或NotSubmitted的报告算作逾期
                    IsOverdue = item.Report.SubmitStatus == null || (item.Report.SubmitStatus.HasValue && item.Report.SubmitStatus.Value == (int)EnumSubmitStatus.NotSubmitted),
                    OverdueDays = (item.Report.SubmitStatus == null || (item.Report.SubmitStatus.HasValue && item.Report.SubmitStatus.Value == (int)EnumSubmitStatus.NotSubmitted)) ? CalculateOverdueDays(item.Report.Status, item.Report.ReportDate, (EnumReportType)item.Report.ReportType) : 0,
                    
                    // 阅读状态逻辑：发送人默认已读不显示时间，接收人按自己查看状态显示
                    MyReadStatus = GetReadStatusForUser(item.Report.CreateUser, currentUserId, realTimeData.MyReadStatus.ContainsKey(item.Report.Id) ? realTimeData.MyReadStatus[item.Report.Id].status : null),
                    MyReadDate = GetReadDateForUser(item.Report.CreateUser, currentUserId, realTimeData.MyReadStatus.ContainsKey(item.Report.Id) ? realTimeData.MyReadStatus[item.Report.Id].readTime : null),
                    LastModifiedDate = item.Report.UpdateDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    LastModifiedUserName = item.UserName ?? "未知用户"
                })
                .ToList();

                // 8. 返回结果
                return new ApiTableOut<SearchReportList_Out>
                {
                    Data = allResults,
                    Total = total // 使用数据库查询的总数
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"搜索报告列表失败，错误：{ex.Message}");
                throw new ApiException($"搜索报告列表失败，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取数据权限过滤条件
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限过滤条件</returns>
        private string GetDataPermissionFilter(string userId)
        {
            try
            {
                // 1. 检查用户是否存在
                var userInfo = DbOpe_v_userwithorg.Instance.GetData(x => x.Id == userId);
                if (userInfo == null)
                {
                    throw new ApiException("用户不存在或已被删除");
                }

                // 2. 检查是否是销售总监
                // 销售总监也需要按个人权限进行筛选，不能查看所有报告
                if (IsSalesManager(userId))
                {
                    return "personal_access"; // 销售总监也需要进行个人权限过滤
                }

                // 3. 检查是否是后台管理人员（非销售组织的特殊角色用户）
                // 后台人员可以查看所有报告
                if (IsSystemAdmin(userId))
                {
                    return string.Empty; // 无限制，可以查看所有报告
                }

                // 4. 其他组织人员，根据创建人、抄送人、接收人进行筛选
                return "personal_access"; // 需要进行个人权限过滤
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取数据权限过滤条件失败，用户ID：{userId}，错误：{ex.Message}");
                throw new ApiException($"获取数据权限过滤条件失败，用户ID：{userId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否是系统管理员（后台人员）
        /// 根据用户的组织ID判断是否属于非销售组织
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否是系统管理员</returns>
        private bool IsSystemAdmin(string userId)
        {
            try
            {
                // 获取用户信息
                var user = DbOpe_sys_user.Instance.GetData(x => x.Id == userId && x.Deleted == false);
                if (user == null)
                    return false;

                // 使用项目中现有的非销售组织判断逻辑
                // OrganizationId为"00000000-0000-0000-0000-000000000000"表示非销售组织（后台人员）
                return user.OrganizationId == "00000000-0000-0000-0000-000000000000";
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"检查系统管理员权限失败，用户ID：{userId}，错误：{ex.Message}");
                throw new ApiException($"检查系统管理员权限失败，用户ID：{userId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否是部门管理员（暂不使用，保留接口）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否是部门管理员</returns>
        private bool IsDepartmentManager(string userId)
        {
            // 根据当前业务需求，暂不区分部门管理员
            // 只有后台人员和普通用户两种权限
            return false;
        }

        /// <summary>
        /// 检查是否是销售总监
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否是销售总监</returns>
        private bool IsSalesManager(string userId)
        {
            try
            {
                // 销售总监角色ID
                const string SALES_MANAGER_ROLE_ID = "0cafa3b1-1d14-4047-ae52-2feb0bf2c1cc";
                
                // 检查用户是否具有销售总监角色
                var userRole = DbOpe_sys_userinrole.Instance.GetData(x => 
                    x.UserId == userId && 
                    x.RoleId == SALES_MANAGER_ROLE_ID);
                
                return userRole != null;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"检查销售总监权限失败，用户ID：{userId}，错误：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 批量查询优化方法

        /// <summary>
        /// 批量查询报告相关的实时数据，数量字段已存储在base表中
        /// </summary>
        /// <param name="reportIds">报告ID列表</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>需要实时获取的数据集合</returns>
        private (Dictionary<string, List<string>> ReceiverNames,
                Dictionary<string, List<string>> CcNames,
                Dictionary<string, (EnumReceiverStatus? status, DateTime? readTime)> MyReadStatus) 
            GetRealTimeData(List<string> reportIds, string currentUserId)
        {
            if (reportIds == null || !reportIds.Any())
            {
                return (new Dictionary<string, List<string>>(),
                        new Dictionary<string, List<string>>(),
                        new Dictionary<string, (EnumReceiverStatus? status, DateTime? readTime)>());
            }

            try
            {
                // 并行执行需要实时获取的查询，数量字段已存储在base表中
                var receiverTask = Task.Run(() => GetReportReceiverNames(reportIds));
                var ccTask = Task.Run(() => GetReportCcNames(reportIds));
                var readStatusTask = Task.Run(() => GetMyReadStatus(reportIds, currentUserId));

                // 等待所有查询完成
                Task.WaitAll(receiverTask, ccTask, readStatusTask);

                return (receiverTask.Result, ccTask.Result, readStatusTask.Result);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"批量查询报告实时数据失败：{ex.Message}");
                // 返回空字典，避免整个查询失败
                return (new Dictionary<string, List<string>>(),
                        new Dictionary<string, List<string>>(),
                        new Dictionary<string, (EnumReceiverStatus? status, DateTime? readTime)>());
            }
        }

        #endregion

        #region 删除报告

        /// <summary>
        /// 删除报告
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <returns>删除结果</returns>
        public OperationResult_Out DeleteReport(string reportId)
        {
            try
            {
                // 1. 验证输入参数
                if (string.IsNullOrEmpty(reportId))
                {
                    throw new ApiException("报告ID不能为空");
                }

                // 2. 获取当前用户信息
                var currentUserId = UserId;
                var currentUserName = GetCurrentUserName();

                // 3. 获取报告基础信息
                var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == reportId && x.Deleted == false);
                if (reportBase == null)
                {
                    throw new ApiException("报告不存在或已被删除");
                }

                // 4. 检查删除权限
                if (!CanDeleteReport(reportBase, currentUserId))
                {
                    // CanDeleteReport方法内部会抛出具体的错误信息
                    return new OperationResult_Out
                    {
                        Success = false,
                        Message = "删除失败：权限不足或报告状态不允许删除",
                        RowsAffected = 0
                    };
                }

                // 5. 删除报告及其所有相关数据
                DeleteCompleteReport(reportId, currentUserId, currentUserName);

                Console.WriteLine($"成功删除报告，ID：{reportId}，标题：{reportBase.Title}，操作人：{currentUserName}");

                return new OperationResult_Out
                {
                    Success = true,
                    Message = "报告删除成功",
                    RowsAffected = 1
                };
            }
            catch (ApiException)
            {
                // 重新抛出ApiException，保持原有的错误信息
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"删除报告失败，报告ID：{reportId}，错误：{ex.Message}", ex);
                throw new ApiException($"删除报告失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除完整的报告（包括所有相关数据和附件）
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="currentUserName">当前用户姓名</param>
        private void DeleteCompleteReport(string reportId, string currentUserId, string currentUserName)
        {
            try
            {
                var currentTime = DateTime.Now;

                // 1. 删除报告基础信息
                var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == reportId);
                if (reportBase != null)
                {
                    reportBase.Deleted = true;
                    reportBase.UpdateUser = currentUserId;
                    reportBase.UpdateDate = currentTime;
                    DbOpe_crm_report_base.Instance.Update(reportBase);
                }

                // 2. 删除所有相关数据（包括附件）
                DeleteExistingReportData(reportId);

                Console.WriteLine($"完整删除报告成功，报告ID：{reportId}");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"完整删除报告失败，报告ID：{reportId}，错误：{ex.Message}");  
                throw new ApiException($"完整删除报告失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        #endregion

        #region 获取报告详情（固定字段结构）

        /// <summary>
        /// 获取报告详情（简化版，固定字段结构）
        /// 严格按照需求文档的日报/周报/月报模板字段返回
        /// </summary>
        /// <param name="id">报告ID</param>
        /// <returns>报告详情（简化版）</returns>
        public GetReportDetailSimple_Out GetReportDetailSimple(string id)
        {
            // 1. 验证输入参数
            if (string.IsNullOrEmpty(id))
            {
                throw new ApiException("报告ID不能为空");
            }

            // 2. 获取报告基础信息
            var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == id && x.Deleted == false);
            if (reportBase == null)
            {
                throw new ApiException($"报告不存在，ID：{id}");
            }

            // 3. 更新报告阅读状态
            UpdateReportReadStatus(id);

            // 4. 根据报告类型获取详情
            return reportBase.ReportType switch
            {
                (int)EnumReportType.Daily => GetDailyReportDetailSimple(reportBase),
                (int)EnumReportType.Weekly => GetWeeklyReportDetailSimple(reportBase),
                (int)EnumReportType.Monthly => GetMonthlyReportDetailSimple(reportBase),
                _ => throw new ApiException($"不支持的报告类型：{reportBase.ReportType}")
            };
        }

        #endregion

        #region 获取具体报告类型详情的私有方法

        /// <summary>
        /// 获取日报详情（简化版）
        /// </summary>
        /// <param name="reportBase">报告基础信息</param>
        /// <returns>日报详情</returns>
        private GetReportDetailSimple_Out GetDailyReportDetailSimple(Db_crm_report_base reportBase)
        {
            try
            {
                // 1. 构建基础信息
                var baseInfo = BuildReportBaseInfo(reportBase);

                // 2. 获取日报内容
                var dailyContent = GetDailyReportContent(reportBase.Id);

                // 3. 获取接收人和抄送人
                var receivers = GetReportReceivers(reportBase.Id);
                var ccList = GetReportCcList(reportBase.Id);

                // 4. 获取附件
                var attachments = GetReportAttachments(reportBase.Id);

                // 5. 判断权限
                var currentUserId = UserId;
                var canEdit = CanEditReport(reportBase, currentUserId,false);
                var canDelete = CanDeleteReport(reportBase, currentUserId,false);
                var canComment = CanCommentReport(reportBase, currentUserId);
                return new GetReportDetailSimple_Out
                {
                    BaseInfo = baseInfo,
                    DailyContent = dailyContent,
                    Receivers = receivers,
                    CcList = ccList,
                    Attachments = attachments,
                    CanEdit = canEdit,
                    CanDelete = canDelete,
                    CanComment = canComment
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取日报详情失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
                throw new ApiException($"获取日报详情失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取周报详情（简化版）
        /// </summary>
        /// <param name="reportBase">报告基础信息</param>
        /// <returns>周报详情</returns>
        private GetReportDetailSimple_Out GetWeeklyReportDetailSimple(Db_crm_report_base reportBase)
        {
            try
            {
                // 1. 构建基础信息
                var baseInfo = BuildReportBaseInfo(reportBase);

                // 2. 获取周报内容
                var weeklyContent = GetWeeklyReportContent(reportBase.Id);

                // 3. 获取接收人和抄送人
                var receivers = GetReportReceivers(reportBase.Id);
                var ccList = GetReportCcList(reportBase.Id);

                // 4. 获取附件
                var attachments = GetReportAttachments(reportBase.Id);

                // 5. 判断权限
                var currentUserId = UserId;
                var canEdit = CanEditReport(reportBase, currentUserId,false);
                var canDelete = CanDeleteReport(reportBase, currentUserId,false);

                return new GetReportDetailSimple_Out
                {
                    BaseInfo = baseInfo,
                    WeeklyContent = weeklyContent,
                    Receivers = receivers,
                    CcList = ccList,
                    Attachments = attachments,
                    CanEdit = canEdit,
                    CanDelete = canDelete
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取周报详情失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
                throw new ApiException($"获取周报详情失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取月报详情（简化版）
        /// </summary>
        /// <param name="reportBase">报告基础信息</param>
        /// <returns>月报详情</returns>
        private GetReportDetailSimple_Out GetMonthlyReportDetailSimple(Db_crm_report_base reportBase)
        {
            try
            {
                // 1. 构建基础信息
                var baseInfo = BuildReportBaseInfo(reportBase);

                // 2. 获取月报内容
                var monthlyContent = GetMonthlyReportContent(reportBase.Id);

                // 3. 获取接收人和抄送人
                var receivers = GetReportReceivers(reportBase.Id);
                var ccList = GetReportCcList(reportBase.Id);

                // 4. 获取附件
                var attachments = GetReportAttachments(reportBase.Id);

                // 5. 判断权限
                var currentUserId = UserId;
                var canEdit = CanEditReport(reportBase, currentUserId,false);
                var canDelete = CanDeleteReport(reportBase, currentUserId,false);

                return new GetReportDetailSimple_Out
                {
                    BaseInfo = baseInfo,
                    MonthlyContent = monthlyContent,
                    Receivers = receivers,
                    CcList = ccList,
                    Attachments = attachments,
                    CanEdit = canEdit,
                    CanDelete = canDelete
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取月报详情失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
                throw new ApiException($"获取月报详情失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 构建报告基础信息
        /// </summary>
        /// <param name="reportBase">数据库报告基础信息</param>
        /// <returns>视图模型基础信息</returns>
        private VM_ReportBase BuildReportBaseInfo(Db_crm_report_base reportBase)
        {
            try
            {
                // 获取用户名称
                var userName = GetUserNameById(reportBase.CreateUser);

                // 获取团队信息
                var (teamId, teamName) = GetUserTeamInfo(reportBase.CreateUser);

                // 使用存储的数量字段，避免实时统计
                var attachmentCount = reportBase.AttachmentCount;
                var receiverCount = reportBase.ReceiverCount;
                var ccCount = reportBase.CcCount; // 使用存储的抄送人数量
                var customerCount = reportBase.CustomerCount;

                return new VM_ReportBase
                {
                    Id = reportBase.Id,
                    ReportType = (EnumReportType)reportBase.ReportType,
                    Title = reportBase.Title,
                    UserId = reportBase.CreateUser,
                    UserName = userName,
                    DepartmentName = teamName, // 使用团队名称作为部门名称
                    Status = (EnumReportStatus)reportBase.Status,
                    ReportDate = reportBase.ReportDate.ToString("yyyy-MM-dd"),
                    CreateDate = reportBase.CreateDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    SubmitDate = reportBase.SubmitTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    AttachmentCount = attachmentCount,
                    FieldCount = GetFieldCount(reportBase.Id), // 需要实现字段统计
                    ReceiverCount = receiverCount,
                    CcCount = ccCount,
                    CustomerCount = customerCount
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"构建报告基础信息失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
                throw new ApiException($"构建报告基础信息失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取报告字段数量
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <returns>字段数量</returns>
        private int GetFieldCount(string reportId)
        {
            try
            {
                // 统计内容字段数量
                var contentCount = DbOpe_crm_report_content.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false).Count;

                // 统计工作量数据字段数量
                var workDataCount = DbOpe_crm_report_work_data.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false).Count;

                return contentCount + workDataCount;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取报告字段数量失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"获取报告字段数量失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取日报内容
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <returns>日报内容</returns>
        private VM_DailyReportContent GetDailyReportContent(string reportId)
        {
            try
            {
                var dailyContent = new VM_DailyReportContent();

                    // 1. 获取工作量数据
                    var workDataList = DbOpe_crm_report_work_data.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                    foreach (var workData in workDataList)
                    {
                        switch (workData.SectionKey)
                        {
                            case "phone_count":
                                dailyContent.PhoneCount = workData.DataValue;
                                break;
                            case "visit_count":
                                dailyContent.VisitCount = workData.DataValue;
                                break;
                            case "email_precise_count":
                                dailyContent.EmailPreciseCount = workData.DataValue;
                                break;
                            case "email_bulk_count":
                                dailyContent.EmailBulkCount = workData.DataValue;
                                break;
                            case "social_media_count":
                                dailyContent.SocialMediaCount = workData.DataValue;
                                break;
                            case "reply_count":
                                dailyContent.ReplyCount = workData.DataValue;
                                break;
                            case "demo_count":
                                dailyContent.DemoCount = workData.DataValue;
                                break;
                        }
                    }
                                    // 2. 获取文本内容
                var contentList = DbOpe_crm_report_content.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                foreach (var content in contentList)
                {
                    switch (content.SectionKey)
                    {
                        case "other":
                            dailyContent.Other = content.Content;
                            break;
                        case "product_planning":
                            dailyContent.ProductPlanning = content.Content;
                            break;
                        case "region_planning":
                            dailyContent.RegionPlanning = content.Content;
                            break;
                        case "sales_planning":
                            dailyContent.SalesPlanning = content.Content;
                            break;
                        case "difficulties":
                            dailyContent.Difficulties = content.Content;
                            break;
                        case "support_needed":
                            dailyContent.SupportNeeded = content.Content;
                            break;
                        case "suggestions":
                            dailyContent.Suggestions = content.Content;
                            break;
                        case "learning_thinking":
                            dailyContent.LearningAndThinking = content.Content;
                            break;
                    }
                }

                    // 3. 获取客户数据
                    var customerList = DbOpe_crm_report_customer.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);

                    // 按模块分组客户数据
                    var customerGroups = customerList.GroupBy(x => x.ModuleKey);
                    foreach (var group in customerGroups)
                    {
                        switch (group.Key)
                        {
                            case "customer_new":
                                dailyContent.CustomerNew = group.Select(ConvertToCustomerNew).ToList();
                                break;
                            case "customer_follow":
                                dailyContent.CustomerFollow = group.Select(ConvertToCustomerFollow).ToList();
                                break;
                            case "customer_contract":
                                dailyContent.CustomerContract = group.Select(ConvertToCustomerContract).ToList();
                                break;
                            case "customer_service":
                                dailyContent.CustomerService = group.Select(ConvertToCustomerService).ToList();
                                break;
                        }
                    }

                return dailyContent;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取日报内容失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"获取日报内容失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 转换为客户新增视图模型
        /// </summary>
        /// <param name="customer">数据库客户记录</param>
        /// <returns>客户新增视图模型</returns>
        private VM_CustomerNew ConvertToCustomerNew(Db_crm_report_customer customer)
        {
            try
            {
                return new VM_CustomerNew
                {
                    CustomerId = customer.CustomerId,
                    CustomerLevel = int.TryParse(customer.CustomerLevel, out var _CustomerLevel) ? _CustomerLevel : (int?)null,
                    Country = int.TryParse(customer.CustomerCountry, out var _CustomerCountry) ? _CustomerCountry : (int?)null,
                    CompanyName = customer.CustomerName,
                    MainProducts = customer.CustomerProduct,
                    CommunicationSummary = customer.Content
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"转换客户新增数据失败，客户ID：{customer.CustomerId}，错误：{ex.Message}");
                throw new ApiException($"转换客户新增数据失败，客户ID：{customer.CustomerId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 转换为客户跟进视图模型
        /// </summary>
        /// <param name="customer">数据库客户记录</param>
        /// <returns>客户跟进视图模型</returns>
        private VM_CustomerFollow ConvertToCustomerFollow(Db_crm_report_customer customer)
        {
            try
            {
                return new VM_CustomerFollow
                {
                    CustomerId = customer.CustomerId,
                    CompanyName = customer.CustomerName,
                    MainProducts = customer.CustomerProduct,
                    RecommendedSolution = customer.Solution,
                    FollowUpSummary = customer.Content
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"转换客户跟进数据失败，客户ID：{customer.CustomerId}，错误：{ex.Message}");
                throw new ApiException($"转换客户跟进数据失败，客户ID：{customer.CustomerId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 转换为客户签约视图模型
        /// </summary>
        /// <param name="customer">数据库客户记录</param>
        /// <returns>客户签约视图模型</returns>
        private VM_CustomerContract ConvertToCustomerContract(Db_crm_report_customer customer)
        {
            try
            {
                return new VM_CustomerContract
                {
                    CustomerId = customer.CustomerId,
                    CompanyName = customer.CustomerName,
                    MainProducts = customer.CustomerProduct,
                    ContractSolution = customer.Solution,
                    ContractSummary = customer.Content
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"转换客户签约数据失败，客户ID：{customer.CustomerId}，错误：{ex.Message}");
                throw new ApiException($"转换客户签约数据失败，客户ID：{customer.CustomerId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 转换为客户服务视图模型
        /// </summary>
        /// <param name="customer">数据库客户记录</param>
        /// <returns>客户服务视图模型</returns>
        private VM_CustomerService ConvertToCustomerService(Db_crm_report_customer customer)
        {
            try
            {
                return new VM_CustomerService
                {
                    CustomerId = customer.CustomerId,
                    CompanyName = customer.CustomerName,
                    ServiceContent = customer.Content
                };
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"转换客户服务数据失败，客户ID：{customer.CustomerId}，错误：{ex.Message}");
                throw new ApiException($"转换客户服务数据失败，客户ID：{customer.CustomerId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取周报内容
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <returns>周报内容</returns>
        private VM_WeeklyReportContent GetWeeklyReportContent(string reportId)
        {
            try
            {
                var weeklyContent = new VM_WeeklyReportContent();

                // 1. 获取工作量数据
                var workDataList = DbOpe_crm_report_work_data.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                foreach (var workData in workDataList)
                {
                    switch (workData.SectionKey)
                    {
                        case "phone_count":
                            weeklyContent.TotalPhoneCount = workData.DataValue;
                            break;
                        case "visit_count":
                            weeklyContent.TotalVisitCount = workData.DataValue;
                            break;
                        case "email_precise_count":
                            weeklyContent.TotalEmailPreciseCount = workData.DataValue;
                            break;
                        case "email_bulk_count":
                            weeklyContent.TotalEmailBulkCount = workData.DataValue;
                            break;
                        case "social_media_count":
                            weeklyContent.TotalSocialMediaCount = workData.DataValue;
                            break;
                        case "reply_count":
                            weeklyContent.TotalReplyCount = workData.DataValue;
                            break;
                        case "demo_count":
                            weeklyContent.TotalDemoCount = workData.DataValue;
                            break;
                        case "new_customer_count":
                            weeklyContent.TotalNewCustomerCount = workData.DataValue;
                            break;
                        case "follow_customer_count":
                            weeklyContent.TotalFollowCustomerCount = workData.DataValue;
                            break;
                        case "contract_customer_count":
                            weeklyContent.TotalContractCustomerCount = workData.DataValue;
                            break;
                    }
                }

                // 2. 获取文本内容
                var contentList = DbOpe_crm_report_content.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                foreach (var content in contentList)
                {
                    switch (content.SectionKey)
                    {
                        case "weekly_summary":
                            weeklyContent.WeeklySummary = content.Content;
                            break;
                        case "next_week_plan":
                            weeklyContent.NextWeekPlan = content.Content;
                            break;
                    }
                }

                return weeklyContent;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取周报内容失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"获取周报内容失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取月报内容
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <returns>月报内容</returns>
        private VM_MonthlyReportContent GetMonthlyReportContent(string reportId)
        {
            try
            {
                var monthlyContent = new VM_MonthlyReportContent();

                // 1. 获取工作量数据
                var workDataList = DbOpe_crm_report_work_data.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                foreach (var workData in workDataList)
                {
                    switch (workData.SectionKey)
                    {
                        case "phone_count":
                            monthlyContent.TotalPhoneCount = workData.DataValue;
                            break;
                        case "visit_count":
                            monthlyContent.TotalVisitCount = workData.DataValue;
                            break;
                        case "email_precise_count":
                            monthlyContent.TotalEmailPreciseCount = workData.DataValue;
                            break;
                        case "email_bulk_count":
                            monthlyContent.TotalEmailBulkCount = workData.DataValue;
                            break;
                        case "social_media_count":
                            monthlyContent.TotalSocialMediaCount = workData.DataValue;
                            break;
                        case "reply_count":
                            monthlyContent.TotalReplyCount = workData.DataValue;
                            break;
                        case "demo_count":
                            monthlyContent.TotalDemoCount = workData.DataValue;
                            break;
                        case "new_customer_count":
                            monthlyContent.TotalNewCustomerCount = workData.DataValue;
                            break;
                        case "follow_customer_count":
                            monthlyContent.TotalFollowCustomerCount = workData.DataValue;
                            break;
                        case "contract_customer_count":
                            monthlyContent.TotalContractCustomerCount = workData.DataValue;
                            break;
                    }
                }

                // 2. 获取文本内容
                var contentList = DbOpe_crm_report_content.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                foreach (var content in contentList)
                {
                    switch (content.SectionKey)
                    {
                        case "monthly_summary":
                            monthlyContent.MonthlySummary = content.Content;
                            break;
                        case "next_month_plan":
                            monthlyContent.NextMonthPlan = content.Content;
                            break;
                    }
                }

                return monthlyContent;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取月报内容失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"获取月报内容失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取报告接收人列表
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <returns>接收人列表</returns>
        private List<VM_ReportReceiver> GetReportReceivers(string reportId)
        {
            try
            {
                var receivers = DbOpe_crm_report_receiver.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                return receivers.Select(r => new VM_ReportReceiver
                {
                    UserId = r.ReceiverId,
                    UserName = r.ReceiverName,
                    IsDefault = r.IsDefault
                }).ToList();
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取报告接收人失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"获取报告接收人失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取报告抄送人列表
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <returns>抄送人列表</returns>
        private List<VM_ReportReceiver> GetReportCcList(string reportId)
        {
            try
            {
                var ccList = DbOpe_crm_report_cc.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                return ccList.Select(cc => new VM_ReportReceiver
                {
                    UserId = cc.CcUserId,
                    UserName = cc.CcUserName,
                    IsDefault = cc.IsDefault
                }).ToList();
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取报告抄送人失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"获取报告抄送人失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取报告附件列表
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <returns>附件列表</returns>
        private List<VM_ReportAttachment> GetReportAttachments(string reportId)
        {
            try
            {
                var attachments = DbOpe_crm_report_attachment.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                return attachments.Select(a => new VM_ReportAttachment
                {
                    Id = a.Id,
                    ReportId = a.ReportId,
                    ModuleKey = a.ModuleKey,
                    SectionKey = a.SectionKey,
                    FileName = a.FileName,
                    FilePath = a.FilePath,
                    FileSize = a.FileSize,
                    //这里filetype是与Attachfile表的filetype一致
                    FileType = "Report"
                }).ToList();
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取报告附件失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"获取报告附件失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否可以编辑报告
        /// </summary>
        /// <param name="reportBase">报告基础信息</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="throwException">是否抛出异常</param>
        /// <returns>是否可以编辑</returns>
        private bool CanEditReport(Db_crm_report_base reportBase, string currentUserId,bool throwException=true)
        {
            try
            {
                // 只有报告创建者可以编辑
                if (reportBase.CreateUser != currentUserId)
                {
                    if (throwException)
                        throw new ApiException("您没有权限编辑该报告，只有报告创建者才能编辑");
                    return false;
                }

                // 草稿状态可以编辑，不受截止时间限制
                if (reportBase.Status == (int)EnumReportStatus.Draft)
                {
                    return true;
                }

                // 已提交的报告，检查是否超过截止时间
                if (IsAfterDeadline(reportBase))
                {
                    if (throwException)
                        throw new ApiException("报告已超过截止时间，无法编辑。草稿状态的报告不受此限制");
                    return false;
                }

                // 已提交的报告在截止时间内可以编辑，但不能改为草稿状态
                return true;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"判断编辑权限失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
                if (throwException)
                    throw new ApiException($"判断编辑权限失败：{ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 判断是否可以评论报告(接收人、抄送人)
        /// </summary>
        /// <param name="reportBase">报告基础信息</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>是否可以编辑</returns>
        private bool CanCommentReport(Db_crm_report_base reportBase, string currentUserId)
        {
            try
            {
                var receivers = DbOpe_crm_report_receiver.Instance.GetDataList(x => x.ReportId == reportBase.Id && x.Deleted == false);
                var ccList = DbOpe_crm_report_cc.Instance.GetDataList(x => x.ReportId == reportBase.Id && x.Deleted == false);
                if (receivers.Any(x => x.ReceiverId == currentUserId) || ccList.Any(x => x.CcUserId == currentUserId))
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"判断编辑权限失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
                throw new ApiException($"判断编辑权限失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
            }
        }
        /// <summary>
        /// 判断是否可以删除报告
        /// </summary>
        /// <param name="reportBase">报告基础信息</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="throwException">是否抛出异常</param>
        /// <returns>是否可以删除</returns>
        private bool CanDeleteReport(Db_crm_report_base reportBase, string currentUserId,bool throwException=true)
        {
            try
            {
                // 只有报告创建者可以删除
                if (reportBase.CreateUser != currentUserId)
                {
                    if (throwException)
                        throw new ApiException("您没有权限删除该报告，只有报告创建者才能删除");
                    return false;
                }

                // 草稿状态可以删除，不受截止时间限制
                if (reportBase.Status == (int)EnumReportStatus.Draft)
                {
                    return true;
                }

                // 已提交的报告，检查是否超过截止时间
                if (IsAfterDeadline(reportBase))
                {
                    if (throwException)
                        throw new ApiException("报告已超过截止时间，无法删除。草稿状态的报告不受此限制");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"判断删除权限失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
                if (throwException)
                    throw new ApiException($"判断删除权限失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 判断是否超过截止时间
        /// </summary>
        private bool IsAfterDeadline(Db_crm_report_base reportBase)
        {
            try
            {
                // 使用数据库版本的时效管理功能计算截止时间
                var deadlineResult = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                {
                    ReportType = (EnumReportType)reportBase.ReportType,
                    ReportDate = reportBase.ReportDate
                });

                // 判断当前状态是否已过截止时间
                return deadlineResult.CurrentStatus == EnumReportTimeStatus.AfterFinalTime;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"判断截止时间失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
                throw new ApiException($"判断截止时间失败，报告ID：{reportBase.Id}，错误：{ex.Message}");
            }
        }



        /// <summary>
        /// 验证创建报告输入
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateCreateReportInput(CreateReportFixed_In input)
        {
            if (input == null)
                return (false, "输入参数不能为空");

            if (string.IsNullOrWhiteSpace(input.Title))
                return (false, "报告标题不能为空");

            if (input.ReportDate == default)
                return (false, "报告日期不能为空");

            // 根据报告类型验证内容
            switch (input.ReportType)
            {
                case EnumReportType.Daily:
                    if (input.DailyContent == null)
                        return (false, "日报内容不能为空");
                    
                    // 验证日报工作量数据
                    var dailyValidation = ValidateDailyWorkData(input.DailyContent);
                    if (!dailyValidation.IsValid)
                        return dailyValidation;
                    

                    break;
                    
                case EnumReportType.Weekly:
                    if (input.WeeklyContent == null)
                        return (false, "周报内容不能为空");
                    
                    // 验证周报工作量数据
                    var weeklyValidation = ValidateWeeklyWorkData(input.WeeklyContent);
                    if (!weeklyValidation.IsValid)
                        return weeklyValidation;
                    break;
                    
                case EnumReportType.Monthly:
                    if (input.MonthlyContent == null)
                        return (false, "月报内容不能为空");
                    
                    // 验证月报工作量数据
                    var monthlyValidation = ValidateMonthlyWorkData(input.MonthlyContent);
                    if (!monthlyValidation.IsValid)
                        return monthlyValidation;
                    break;
            }

            return (true, "");
        }

        /// <summary>
        /// 验证更新报告输入
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateUpdateReportInput(UpdateReportFixed_In input)
        {
            if (input == null)
                return (false, "输入参数不能为空");

            if (string.IsNullOrWhiteSpace(input.Id))
                return (false, "报告ID不能为空");

            if (string.IsNullOrWhiteSpace(input.Title))
                return (false, "报告标题不能为空");

            if (input.ReportDate == default)
                return (false, "报告日期不能为空");

            // 根据报告类型验证内容
            switch (input.ReportType)
            {
                case EnumReportType.Daily:
                    if (input.DailyContent == null)
                        return (false, "日报内容不能为空");
                    
                    // 验证日报工作量数据
                    var dailyValidation = ValidateDailyWorkData(input.DailyContent);
                    if (!dailyValidation.IsValid)
                        return dailyValidation;
                    break;
                    
                case EnumReportType.Weekly:
                    if (input.WeeklyContent == null)
                        return (false, "周报内容不能为空");
                    
                    // 验证周报工作量数据
                    var weeklyValidation = ValidateWeeklyWorkData(input.WeeklyContent);
                    if (!weeklyValidation.IsValid)
                        return weeklyValidation;
                    break;
                    
                case EnumReportType.Monthly:
                    if (input.MonthlyContent == null)
                        return (false, "月报内容不能为空");
                    
                    // 验证月报工作量数据
                    var monthlyValidation = ValidateMonthlyWorkData(input.MonthlyContent);
                    if (!monthlyValidation.IsValid)
                        return monthlyValidation;
                    break;
            }

            // 验证状态变更的合理性
            if (input.Status == EnumReportStatus.Draft)
            {
                // 检查原报告状态，如果已经是已提交状态，则不允许改为草稿
                var existingReport = DbOpe_crm_report_base.Instance.GetData(x => x.Id == input.Id && x.Deleted == false);
                if (existingReport != null && existingReport.Status == (int)EnumReportStatus.Submitted)
                {
                    return (false, "已提交的报告不能保存为草稿状态");
                }
            }

            return (true, "");
        }

        /// <summary>
        /// 删除现有的报告数据（除了附件）
        /// </summary>
        private void DeleteExistingReportDataExceptAttachments(string reportId)
        {
            try
            {
                // 删除工作量数据
                var workDataList = DbOpe_crm_report_work_data.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var workData in workDataList)
                {
                    workData.Deleted = true;
                    workData.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_work_data.Instance.Update(workData);
                }

                // 删除文本内容数据
                var contentList = DbOpe_crm_report_content.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var content in contentList)
                {
                    content.Deleted = true;
                    content.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_content.Instance.Update(content);
                }

                // 删除客户数据
                var customerList = DbOpe_crm_report_customer.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var customer in customerList)
                {
                    customer.Deleted = true;
                    customer.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_customer.Instance.Update(customer);
                }

                // 处理接收人数据：如果是默认配置，先复制到虚拟ID，然后删除
                var receiverList = DbOpe_crm_report_receiver.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var receiver in receiverList)
                {
                    if (receiver.IsDefault)
                    {
                        // 如果是默认配置，复制到虚拟ID保存
                        var virtualReceiver = new Db_crm_report_receiver
                        {
                            Id = Guid.NewGuid().ToString(),
                            ReportId = Guid.Empty.ToString(), // 虚拟ID
                            ReceiverId = receiver.ReceiverId,
                            ReceiverName = receiver.ReceiverName,
                            IsDefault = true,
                            Status = receiver.Status,
                            Deleted = false,
                            CreateUser = receiver.CreateUser,
                            CreateDate = DateTime.Now,
                            UpdateUser = receiver.UpdateUser,
                            UpdateDate = DateTime.Now
                        };
                        DbOpe_crm_report_receiver.Instance.Insert(virtualReceiver);
                    }
                    
                    // 删除原记录
                    receiver.Deleted = true;
                    receiver.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_receiver.Instance.Update(receiver);
                }

                // 处理抄送人数据：如果是默认配置，先复制到虚拟ID，然后删除
                var ccList = DbOpe_crm_report_cc.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var cc in ccList)
                {
                    if (cc.IsDefault)
                    {
                        // 如果是默认配置，复制到虚拟ID保存
                        var virtualCc = new Db_crm_report_cc
                        {
                            Id = Guid.NewGuid().ToString(),
                            ReportId = Guid.Empty.ToString(), // 虚拟ID
                            CcUserId = cc.CcUserId,
                            CcUserName = cc.CcUserName,
                            IsDefault = true,
                            Status = cc.Status,
                            Deleted = false,
                            CreateUser = cc.CreateUser,
                            CreateDate = DateTime.Now,
                            UpdateUser = cc.UpdateUser,
                            UpdateDate = DateTime.Now
                        };
                        DbOpe_crm_report_cc.Instance.Insert(virtualCc);
                    }
                    
                    // 删除原记录
                    cc.Deleted = true;
                    cc.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_cc.Instance.Update(cc);
                }

                Console.WriteLine($"删除报告现有数据成功（除附件），报告ID：{reportId}");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"删除报告现有数据失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"删除报告数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除现有的报告数据（包含附件）
        /// </summary>
        private void DeleteExistingReportData(string reportId)
        {
            try
            {
                // 删除工作量数据
                var workDataList = DbOpe_crm_report_work_data.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var workData in workDataList)
                {
                    workData.Deleted = true;
                    workData.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_work_data.Instance.Update(workData);
                }

                // 删除文本内容数据
                var contentList = DbOpe_crm_report_content.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var content in contentList)
                {
                    content.Deleted = true;
                    content.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_content.Instance.Update(content);
                }

                // 删除客户数据
                var customerList = DbOpe_crm_report_customer.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var customer in customerList)
                {
                    customer.Deleted = true;
                    customer.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_customer.Instance.Update(customer);
                }

                // 处理接收人数据：如果是默认配置，先复制到虚拟ID，然后删除
                var receiverList = DbOpe_crm_report_receiver.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var receiver in receiverList)
                {
                    if (receiver.IsDefault)
                    {
                        // 如果是默认配置，复制到虚拟ID保存
                        var virtualReceiver = new Db_crm_report_receiver
                        {
                            Id = Guid.NewGuid().ToString(),
                            ReportId = Guid.Empty.ToString(), // 虚拟ID
                            ReceiverId = receiver.ReceiverId,
                            ReceiverName = receiver.ReceiverName,
                            IsDefault = true,
                            Status = receiver.Status,
                            Deleted = false,
                            CreateUser = receiver.CreateUser,
                            CreateDate = DateTime.Now,
                            UpdateUser = receiver.UpdateUser,
                            UpdateDate = DateTime.Now
                        };
                        DbOpe_crm_report_receiver.Instance.Insert(virtualReceiver);
                    }
                    
                    // 删除原记录
                    receiver.Deleted = true;
                    receiver.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_receiver.Instance.Update(receiver);
                }

                // 处理抄送人数据：如果是默认配置，先复制到虚拟ID，然后删除
                var ccList = DbOpe_crm_report_cc.Instance.GetDataList(x => x.ReportId == reportId);
                foreach (var cc in ccList)
                {
                    if (cc.IsDefault)
                    {
                        // 如果是默认配置，复制到虚拟ID保存
                        var virtualCc = new Db_crm_report_cc
                        {
                            Id = Guid.NewGuid().ToString(),
                            ReportId = Guid.Empty.ToString(), // 虚拟ID
                            CcUserId = cc.CcUserId,
                            CcUserName = cc.CcUserName,
                            IsDefault = true,
                            Status = cc.Status,
                            Deleted = false,
                            CreateUser = cc.CreateUser,
                            CreateDate = DateTime.Now,
                            UpdateUser = cc.UpdateUser,
                            UpdateDate = DateTime.Now
                        };
                        DbOpe_crm_report_cc.Instance.Insert(virtualCc);
                    }
                    
                    // 删除原记录
                    cc.Deleted = true;
                    cc.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_cc.Instance.Update(cc);
                }

                // 删除附件数据（包括云存储文件）
                var attachmentList = DbOpe_crm_report_attachment.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);
                var deletedFileCount = 0;
                var failedFileCount = 0;

                foreach (var attachment in attachmentList)
                {
                    // 先删除云存储上的实际文件
                    if (!string.IsNullOrEmpty(attachment.FilePath))
                    {
                        if (DeleteFileFromStorage(attachment.FilePath))
                        {
                            deletedFileCount++;
                        }
                        else
                        {
                            failedFileCount++;
                            LogUtil.AddErrorLog($"删除云存储文件失败，文件路径：{attachment.FilePath}，附件ID：{attachment.Id}");
                            throw new ApiException($"删除云存储文件失败，文件路径：{attachment.FilePath}，附件ID：{attachment.Id}");        
                        }
                    }

                    // 然后软删除数据库记录
                    attachment.Deleted = true; // true表示已删除
                    attachment.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_attachment.Instance.Update(attachment);
                }

                Console.WriteLine($"删除报告现有数据成功，报告ID：{reportId}，附件总数：{attachmentList.Count}，云文件删除成功：{deletedFileCount}，失败：{failedFileCount}");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"删除报告现有数据失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"删除报告数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 保存报告内容
        /// </summary>
        private void SaveReportContent(string reportId, CreateReportFixed_In input)
        {
            var contentList = new List<Db_crm_report_content>();

            // 根据报告类型解析固定字段并创建内容记录
            switch (input.ReportType)
            {
                case EnumReportType.Daily:
                    contentList = ConvertDailyFieldsToRecords(reportId, input);
                    break;
                case EnumReportType.Weekly:
                    contentList = ConvertWeeklyFieldsToRecords(reportId, input);
                    break;
                case EnumReportType.Monthly:
                    contentList = ConvertMonthlyFieldsToRecords(reportId, input);
                    break;
            }


            // 批量保存内容到数据库
            DbOpe_crm_report_content.Instance.CreateReportContents(contentList);

        }

        /// <summary>
        /// 将日报固定字段转换为数据库记录
        /// </summary>
        private List<Db_crm_report_content> ConvertDailyFieldsToRecords(string reportId, CreateReportFixed_In input)
        {
            var dailyContent = input.DailyContent;
            if (dailyContent == null) return new List<Db_crm_report_content>();

            // 分类保存到不同的表
            SaveDailyWorkData(reportId, input, dailyContent);
            SaveDailyCustomerData(reportId, input, dailyContent);

            // 返回文本内容记录
            return SaveDailyTextContent(reportId, input, dailyContent);
        }

        /// <summary>
        /// 保存日报工作量数据到work_data表
        /// </summary>
        private void SaveDailyWorkData(string reportId, CreateReportFixed_In input, VM_DailyReportInput dailyContent)
        {
            var workDataList = new List<Db_crm_report_work_data>();
            var currentUserId = UserId;
            var currentUserName = GetCurrentUserName();
            var reportDate = input.ReportDate;
            var (teamId, teamName) = GetUserTeamInfo(currentUserId);

            // 工作量数据
            var workDataItems = new List<(string SectionKey, string SectionTitle, int? Value)>
            {
                ("phone_count", "电话量", dailyContent.PhoneCount),
                ("visit_count", "拜访量", dailyContent.VisitCount),
                ("email_precise_count", "邮件量（精发）", dailyContent.EmailPreciseCount),
                ("email_bulk_count", "邮件量（粗发）", dailyContent.EmailBulkCount),
                ("social_media_count", "社媒量", dailyContent.SocialMediaCount),
                ("reply_count", "回复量", dailyContent.ReplyCount),
                ("demo_count", "演示量", dailyContent.DemoCount)
            };

            foreach (var (sectionKey, sectionTitle, value) in workDataItems)
            {
                // 修复：当值为0时也要保存，确保读取时能正确显示0
                if (value.HasValue)
                {
                    workDataList.Add(new Db_crm_report_work_data
                    {
                        Id = Guid.NewGuid().ToString(),
                        ReportId = reportId,
                        ReportType = (int)input.ReportType,
                        UserId = currentUserId,
                        UserName = currentUserName,
                        TeamId = teamId,
                        TeamName = teamName,
                        ReportDate = reportDate,
                        ReportYear = reportDate.Year,
                        ReportMonth = reportDate.Month,
                        ReportWeek = GetWeekOfYear(reportDate),
                        ModuleKey = "work_review",
                        ModuleTitle = "工作回顾",
                        ModuleOrder = 1,
                        SectionKey = sectionKey,
                        SectionTitle = sectionTitle,
                        SectionOrder = 0,
                        DataType = "count",
                        DataValue = value.Value,
                        Deleted = false,
                        CreateUser = currentUserId,
                        CreateDate = DateTime.Now,
                        UpdateUser = currentUserId,
                        UpdateDate = DateTime.Now
                    });
                }
            }

            if (workDataList.Count > 0)
            {
                DbOpe_crm_report_work_data.Instance.CreateWorkDataRecords(workDataList);
            }
        }

        /// <summary>
        /// 保存日报客户数据到customer表
        /// </summary>
        private void SaveDailyCustomerData(string reportId, CreateReportFixed_In input, VM_DailyReportInput dailyContent)
        {
            var customerDataList = new List<Db_crm_report_customer>();
            var currentUserId = UserId;
            var currentUserName = GetCurrentUserName();
            var reportDate = input.ReportDate;
            var (teamId, teamName) = GetUserTeamInfo(currentUserId);

            // 1.1.2 客户新增
            if (dailyContent.CustomerNew != null && dailyContent.CustomerNew.Count > 0)
            {
                int validCustomerIndex = 1;
                for (int i = 0; i < dailyContent.CustomerNew.Count; i++)
                {
                    var customer = dailyContent.CustomerNew[i];
                    
                    // 检查是否所有关键字段都为空，如果是则跳过
                    var hasValidData = !string.IsNullOrWhiteSpace(customer.CompanyName) ||
                                      !string.IsNullOrWhiteSpace(customer.CustomerLevel) ||
                                      !string.IsNullOrWhiteSpace(customer.Country) ||
                                      !string.IsNullOrWhiteSpace(customer.MainProducts) ||
                                      !string.IsNullOrWhiteSpace(customer.CommunicationSummary);
                    
                    if (!hasValidData)
                    {
                        continue; // 跳过空记录
                    }
                    
                    customerDataList.Add(new Db_crm_report_customer
                    {
                        Id = Guid.NewGuid().ToString(),
                        ReportId = reportId,
                        ReportType = (int)input.ReportType,
                        UserId = currentUserId,
                        UserName = currentUserName,
                        TeamId = teamId,
                        TeamName = teamName,
                        ReportDate = reportDate,
                        ReportYear = reportDate.Year,
                        ReportMonth = reportDate.Month,
                        ReportWeek = GetWeekOfYear(reportDate),
                        ModuleKey = "customer_new",
                        ModuleTitle = "客户新增",
                        ModuleOrder = 2,
                        SectionKey = "customer_record",
                        SectionTitle = "客户记录",
                        SectionOrder = 0,
                        CustomerIndex = validCustomerIndex++, // 使用有效的索引
                        CustomerId = customer.CustomerId ?? "",
                        CustomerName = customer.CompanyName ?? "",
                        CustomerLevel = customer.CustomerLevel ?? "",
                        CustomerCountry = customer.Country ?? "",
                        CustomerProduct = customer.MainProducts ?? "",
                        Content = customer.CommunicationSummary ?? "",
                        IsFromTracking = false,
                        Deleted = false,
                        CreateUser = currentUserId,
                        CreateDate = DateTime.Now,
                        UpdateUser = currentUserId,
                        UpdateDate = DateTime.Now
                    });
                }
            }

            // 1.1.3 客户跟进
            if (dailyContent.CustomerFollow != null && dailyContent.CustomerFollow.Count > 0)
            {
                int validCustomerIndex = 1;
                for (int i = 0; i < dailyContent.CustomerFollow.Count; i++)
                {
                    var customer = dailyContent.CustomerFollow[i];
                    
                    // 检查是否所有关键字段都为空，如果是则跳过
                    var hasValidData = !string.IsNullOrWhiteSpace(customer.CompanyName) ||
                                      !string.IsNullOrWhiteSpace(customer.MainProducts) ||
                                      !string.IsNullOrWhiteSpace(customer.RecommendedSolution) ||
                                      !string.IsNullOrWhiteSpace(customer.FollowUpSummary);
                    
                    if (!hasValidData)
                    {
                        continue; // 跳过空记录
                    }
                    
                    customerDataList.Add(new Db_crm_report_customer
                    {
                        Id = Guid.NewGuid().ToString(),
                        ReportId = reportId,
                        ReportType = (int)input.ReportType,
                        UserId = currentUserId,
                        UserName = currentUserName,
                        TeamId = teamId,
                        TeamName = teamName,
                        ReportDate = reportDate,
                        ReportYear = reportDate.Year,
                        ReportMonth = reportDate.Month,
                        ReportWeek = GetWeekOfYear(reportDate),
                        ModuleKey = "customer_follow",
                        ModuleTitle = "客户跟进",
                        ModuleOrder = 3,
                        SectionKey = "customer_record",
                        SectionTitle = "客户记录",
                        SectionOrder = 0,
                        CustomerIndex = validCustomerIndex++, // 使用有效的索引
                        CustomerId = customer.CustomerId ?? "",
                        CustomerName = customer.CompanyName ?? "",
                        CustomerProduct = customer.MainProducts ?? "",
                        Solution = customer.RecommendedSolution ?? "",
                        Content = customer.FollowUpSummary ?? "",
                        IsFromTracking = false,
                        Deleted = false,
                        CreateUser = currentUserId,
                        CreateDate = DateTime.Now,
                        UpdateUser = currentUserId,
                        UpdateDate = DateTime.Now
                    });
                }
            }

            // 1.1.4 客户签约
            if (dailyContent.CustomerContract != null && dailyContent.CustomerContract.Count > 0)
            {
                int validCustomerIndex = 1;
                for (int i = 0; i < dailyContent.CustomerContract.Count; i++)
                {
                    var customer = dailyContent.CustomerContract[i];
                    
                    // 检查是否所有关键字段都为空，如果是则跳过
                    var hasValidData = !string.IsNullOrWhiteSpace(customer.CompanyName) ||
                                      !string.IsNullOrWhiteSpace(customer.MainProducts) ||
                                      !string.IsNullOrWhiteSpace(customer.ContractSolution) ||
                                      !string.IsNullOrWhiteSpace(customer.ContractSummary);
                    
                    if (!hasValidData)
                    {
                        continue; // 跳过空记录
                    }
                    
                    customerDataList.Add(new Db_crm_report_customer
                    {
                        Id = Guid.NewGuid().ToString(),
                        ReportId = reportId,
                        ReportType = (int)input.ReportType,
                        UserId = currentUserId,
                        UserName = currentUserName,
                        TeamId = teamId,
                        TeamName = teamName,
                        ReportDate = reportDate,
                        ReportYear = reportDate.Year,
                        ReportMonth = reportDate.Month,
                        ReportWeek = GetWeekOfYear(reportDate),
                        ModuleKey = "customer_contract",
                        ModuleTitle = "客户签约",
                        ModuleOrder = 4,
                        SectionKey = "customer_record",
                        SectionTitle = "客户记录",
                        SectionOrder = 0,
                        CustomerIndex = validCustomerIndex++, // 使用有效的索引
                        CustomerId = customer.CustomerId ?? "",
                        CustomerName = customer.CompanyName ?? "",
                        CustomerProduct = customer.MainProducts ?? "",
                        Solution = customer.ContractSolution ?? "",
                        Content = customer.ContractSummary ?? "",
                        IsFromTracking = false,
                        Deleted = false,
                        CreateUser = currentUserId,
                        CreateDate = DateTime.Now,
                        UpdateUser = currentUserId,
                        UpdateDate = DateTime.Now
                    });
                }
            }

            // 1.1.5 客户服务
            if (dailyContent.CustomerService != null && dailyContent.CustomerService.Count > 0)
            {
                int validCustomerIndex = 1;
                for (int i = 0; i < dailyContent.CustomerService.Count; i++)
                {
                    var customer = dailyContent.CustomerService[i];
                    
                    // 检查是否所有关键字段都为空，如果是则跳过
                    var hasValidData = !string.IsNullOrWhiteSpace(customer.CompanyName) ||
                                      !string.IsNullOrWhiteSpace(customer.ServiceContent);
                    
                    if (!hasValidData)
                    {
                        continue; // 跳过空记录
                    }
                    
                    customerDataList.Add(new Db_crm_report_customer
                    {
                        Id = Guid.NewGuid().ToString(),
                        ReportId = reportId,
                        ReportType = (int)input.ReportType,
                        UserId = currentUserId,
                        UserName = currentUserName,
                        TeamId = teamId,
                        TeamName = teamName,
                        ReportDate = reportDate,
                        ReportYear = reportDate.Year,
                        ReportMonth = reportDate.Month,
                        ReportWeek = GetWeekOfYear(reportDate),
                        ModuleKey = "customer_service",
                        ModuleTitle = "客户服务",
                        ModuleOrder = 5,
                        SectionKey = "customer_record",
                        SectionTitle = "客户记录",
                        SectionOrder = 0,
                        CustomerIndex = validCustomerIndex++, // 使用有效的索引
                        CustomerId = customer.CustomerId ?? "",
                        CustomerName = customer.CompanyName ?? "",
                        Content = customer.ServiceContent ?? "",
                        IsFromTracking = false,
                        Deleted = false,
                        CreateUser = currentUserId,
                        CreateDate = DateTime.Now,
                        UpdateUser = currentUserId,
                        UpdateDate = DateTime.Now
                    });
                }
            }

            if (customerDataList.Count > 0)
            {
                DbOpe_crm_report_customer.Instance.CreateCustomerRecords(customerDataList);
            }
        }

        /// <summary>
        /// 保存日报文本内容到content表
        /// </summary>
        private List<Db_crm_report_content> SaveDailyTextContent(string reportId, CreateReportFixed_In input, VM_DailyReportInput dailyContent)
        {
            var contentList = new List<Db_crm_report_content>();
            var currentUserId = UserId;
            var currentUserName = GetCurrentUserName();
            var reportDate = input.ReportDate;

            // 1.1.6 其他规划与建议模块
            var planningContents = new List<(string SectionKey, string SectionTitle, string Content)>
            {
                ("product_planning", "选品规划", dailyContent.ProductPlanning ?? ""),
                ("region_planning", "区域规划", dailyContent.RegionPlanning ?? ""),
                ("sales_planning", "销售综合规划", dailyContent.SalesPlanning ?? ""),
                ("difficulties", "遇到的难点", dailyContent.Difficulties ?? ""),
                ("support_needed", "需要的支持", dailyContent.SupportNeeded ?? ""),
                ("suggestions", "合理化建议", dailyContent.Suggestions ?? "")
            };

            foreach (var (sectionKey, sectionTitle, content) in planningContents)
            {
                if (!string.IsNullOrEmpty(content))
                {
                    contentList.Add(CreateContentRecord(reportId, input, "planning_suggestions", "其他规划与建议", 6,
                        sectionKey, sectionTitle, content, currentUserId, currentUserName, reportDate));
                }
            }

            // 1.1.7 学习与思考模块
            if (!string.IsNullOrEmpty(dailyContent.LearningAndThinking))
            {
                contentList.Add(CreateContentRecord(reportId, input, "learning_thinking", "学习与思考", 7,
                    "learning_thinking", "学习与思考", dailyContent.LearningAndThinking, currentUserId, currentUserName, reportDate));
            }

            // 其他文本字段（如果有的话）
            if (!string.IsNullOrEmpty(dailyContent.Other))
            {
                contentList.Add(CreateContentRecord(reportId, input, "work_review", "工作回顾", 1,
                    "other", "其他", dailyContent.Other, currentUserId, currentUserName, reportDate));
            }

            return contentList;
        }

        /// <summary>
        /// 将周报固定字段转换为数据库记录
        /// </summary>
        private List<Db_crm_report_content> ConvertWeeklyFieldsToRecords(string reportId, CreateReportFixed_In input)
        {
            var weeklyContent = input.WeeklyContent;
            if (weeklyContent == null) return new List<Db_crm_report_content>();

            // 分类保存到不同的表
            SaveWeeklyWorkData(reportId, input, weeklyContent);

            // 返回文本内容记录
            return SaveWeeklyTextContent(reportId, input, weeklyContent);
        }

        /// <summary>
        /// 将月报固定字段转换为数据库记录
        /// </summary>
        private List<Db_crm_report_content> ConvertMonthlyFieldsToRecords(string reportId, CreateReportFixed_In input)
        {
            var monthlyContent = input.MonthlyContent;
            if (monthlyContent == null) return new List<Db_crm_report_content>();

            // 分类保存到不同的表
            SaveMonthlyWorkData(reportId, input, monthlyContent);

            // 返回文本内容记录
            return SaveMonthlyTextContent(reportId, input, monthlyContent);
        }

        /// <summary>
        /// 创建内容记录
        /// </summary>
        private Db_crm_report_content CreateContentRecord(string reportId, CreateReportFixed_In input,
            string moduleKey, string moduleTitle, int moduleOrder, string sectionKey, string sectionTitle,
            string content, string currentUserId, string currentUserName, DateTime reportDate)
        {
            return new Db_crm_report_content
            {
                Id = Guid.NewGuid().ToString(),
                ReportId = reportId,
                ReportType = (int)input.ReportType,
                ReportTitle = input.Title,
                UserId = currentUserId,
                UserName = currentUserName,
                TeamId = "", // 文本内容记录暂不关联团队信息
                TeamName = "", // 文本内容记录暂不关联团队信息
                ReportDate = reportDate,
                ReportYear = reportDate.Year,
                ReportMonth = reportDate.Month,
                ReportWeek = GetWeekOfYear(reportDate),
                ModuleKey = moduleKey,
                ModuleTitle = moduleTitle,
                ModuleOrder = moduleOrder,
                SectionKey = sectionKey,
                SectionTitle = sectionTitle,
                SectionOrder = 0,
                Content = content,
                Deleted = false,
                CreateUser = currentUserId,
                CreateDate = DateTime.Now,
                UpdateUser = currentUserId,
                UpdateDate = DateTime.Now
            };
        }

        /// <summary>
        /// 获取一年中的第几周
        /// </summary>
        private int GetWeekOfYear(DateTime date)
        {
            var culture = System.Globalization.CultureInfo.CurrentCulture;
            var calendar = culture.Calendar;
            return calendar.GetWeekOfYear(date, culture.DateTimeFormat.CalendarWeekRule, culture.DateTimeFormat.FirstDayOfWeek);
        }

        /// <summary>
        /// 保存接收人和抄送人
        /// </summary>
        private void SaveReportReceivers(string reportId, CreateReportFixed_In input)
        {
            try
            {
                var currentUserId = UserId;
                var currentTime = DateTime.Now;

                // 保存接收人
                if (input.ReceiverIds != null && input.ReceiverIds.Count > 0)
                {
                    var receivers = new List<Db_crm_report_receiver>();
                    foreach (var receiverId in input.ReceiverIds)
                    {
                        var receiverName = GetUserNameById(receiverId);

                        receivers.Add(new Db_crm_report_receiver
                        {
                            Id = Guid.NewGuid().ToString(),
                            ReportId = reportId,
                            ReceiverId = receiverId,
                            ReceiverName = receiverName,
                            Status = 1, // 未读
                            CreateUser = currentUserId,
                            CreateDate = currentTime,
                            UpdateUser = currentUserId,
                            UpdateDate = currentTime
                        });
                    }

                    DbOpe_crm_report_receiver.Instance.CreateReceivers(receivers);

                    // 如果用户选择设置为默认接收人，则更新用户的默认接收人设置
                    if (input.SetAsDefaultReceivers)
                    {
                        UpdateDefaultReceivers(currentUserId, input.ReceiverIds, input.ReportType, reportId);
                    }
                }

                // 保存抄送人
                if (input.CcIds != null && input.CcIds.Count > 0)
                {
                    var ccList = new List<Db_crm_report_cc>();
                    foreach (var ccId in input.CcIds)
                    {
                        var ccUserName = GetUserNameById(ccId);

                        ccList.Add(new Db_crm_report_cc
                        {
                            Id = Guid.NewGuid().ToString(),
                            ReportId = reportId,
                            CcUserId = ccId,
                            CcUserName = ccUserName,
                            Status = 1, // 未读
                            Deleted = false,
                            CreateUser = currentUserId,
                            CreateDate = currentTime,
                            UpdateUser = currentUserId,
                            UpdateDate = currentTime,
                            IsDefault = input.SetAsDefaultCc // 根据用户选择设置是否为默认抄送人
                        });
                    }

                    DbOpe_crm_report_cc.Instance.CreateCcs(ccList);

                    // 如果用户选择设置为默认抄送人，则更新用户的默认抄送人设置
                    if (input.SetAsDefaultCc)
                    {
                        UpdateDefaultCcUsers(currentUserId, input.CcIds, input.ReportType, reportId);
                    }
                }

                // 更新报告的数量字段
                UpdateReportCounts(reportId);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"保存接收人和抄送人失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"保存接收人和抄送人失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新报告的数量字段
        /// </summary>
        /// <param name="reportId">报告ID</param>
        private void UpdateReportCounts(string reportId)
        {
            try
            {
                var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == reportId);
                if (reportBase != null)
                {
                    // 更新附件数量
                    reportBase.AttachmentCount = DbOpe_crm_report_attachment.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false).Count;
                    
                    // 更新客户数量（根据报告类型使用不同的计算方式）
                    if (reportBase.ReportType == (int)EnumReportType.Daily)
                    {
                        // 日报：从客户记录表统计
                        reportBase.CustomerCount = DbOpe_crm_report_customer.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false).Count;
                    }
                    else
                    {
                        // 周报月报：从工作量数据表统计客户相关数据
                        var workDataRecords = DbOpe_crm_report_work_data.Instance.GetDataList(x => 
                            x.ReportId == reportId && 
                            x.Deleted == false &&
                            (x.SectionKey == "new_customer_count" || 
                             x.SectionKey == "follow_customer_count" || 
                             x.SectionKey == "contract_customer_count"))
                            .ToList();
                        
                        // 计算客户总数（新增+跟进+签约）
                        reportBase.CustomerCount = workDataRecords.Sum(x => x.DataValue);
                    }
                    
                    // 更新点赞数量
                    reportBase.LikeCount = DbOpe_crm_report_like.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false).Count;
                    
                    // 更新评论数量
                    reportBase.CommentCount = DbOpe_crm_report_comment.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false).Count;
                    
                    // 更新接收人数量
                    reportBase.ReceiverCount = DbOpe_crm_report_receiver.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false).Count;
                    
                    // 更新抄送人数量
                    reportBase.CcCount = DbOpe_crm_report_cc.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false).Count;
                    
                    reportBase.UpdateUser = UserId;
                    reportBase.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_base.Instance.Update(reportBase);
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新报告数量字段失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取报告类型名称
        /// </summary>
        private string GetReportTypeName(EnumReportType reportType)
        {
            return reportType switch
            {
                EnumReportType.Daily => "日报",
                EnumReportType.Weekly => "周报",
                EnumReportType.Monthly => "月报",
                _ => "报告"
            };
        }

        /// <summary>
        /// 保存周报工作量数据到work_data表
        /// </summary>
        private void SaveWeeklyWorkData(string reportId, CreateReportFixed_In input, VM_WeeklyReportInput weeklyContent)
        {
            var workDataList = new List<Db_crm_report_work_data>();
            var currentUserId = UserId;
            var currentUserName = GetCurrentUserName();
            var reportDate = input.ReportDate;
            var (teamId, teamName) = GetUserTeamInfo(currentUserId);

            // 周报工作量数据
            var workDataItems = new List<(string SectionKey, string SectionTitle, int? Value)>
            {
                ("phone_count", "电话量汇总", weeklyContent.TotalPhoneCount),
                ("visit_count", "拜访量汇总", weeklyContent.TotalVisitCount),
                ("email_precise_count", "邮件量汇总（精发）", weeklyContent.TotalEmailPreciseCount),
                ("email_bulk_count", "邮件量汇总（粗发）", weeklyContent.TotalEmailBulkCount),
                ("social_media_count", "社媒量汇总", weeklyContent.TotalSocialMediaCount),
                ("reply_count", "回复量汇总", weeklyContent.TotalReplyCount),
                ("demo_count", "演示量汇总", weeklyContent.TotalDemoCount),
                ("new_customer_count", "新增客户数", weeklyContent.TotalNewCustomerCount),
                ("follow_customer_count", "跟进客户数", weeklyContent.TotalFollowCustomerCount),
                ("contract_customer_count", "签约客户数", weeklyContent.TotalContractCustomerCount)
            };

            foreach (var (sectionKey, sectionTitle, value) in workDataItems)
            {
                // 修复：当值为0时也要保存，确保读取时能正确显示0
                if (value.HasValue)
                {
                    workDataList.Add(new Db_crm_report_work_data
                    {
                        Id = Guid.NewGuid().ToString(),
                        ReportId = reportId,
                        ReportType = (int)input.ReportType,
                        UserId = currentUserId,
                        UserName = currentUserName,
                        TeamId = teamId,
                        TeamName = teamName,
                        ReportDate = reportDate,
                        ReportYear = reportDate.Year,
                        ReportMonth = reportDate.Month,
                        ReportWeek = GetWeekOfYear(reportDate),
                        ModuleKey = "work_summary",
                        ModuleTitle = "工作汇总",
                        ModuleOrder = 1,
                        SectionKey = sectionKey,
                        SectionTitle = sectionTitle,
                        SectionOrder = 0,
                        DataType = "count",
                        DataValue = value.Value,
                        Deleted = false,
                        CreateUser = currentUserId,
                        CreateDate = DateTime.Now,
                        UpdateUser = currentUserId,
                        UpdateDate = DateTime.Now
                    });
                }
            }

            if (workDataList.Count > 0)
            {
                DbOpe_crm_report_work_data.Instance.CreateWorkDataRecords(workDataList);
            }
        }

        /// <summary>
        /// 保存周报文本内容到content表
        /// </summary>
        private List<Db_crm_report_content> SaveWeeklyTextContent(string reportId, CreateReportFixed_In input, VM_WeeklyReportInput weeklyContent)
        {
            var contentList = new List<Db_crm_report_content>();
            var currentUserId = UserId;
            var currentUserName = GetCurrentUserName();
            var reportDate = input.ReportDate;

            // 周报文本内容
            var textContents = new List<(string ModuleKey, string ModuleTitle, int ModuleOrder, string SectionKey, string SectionTitle, string Content)>
            {
                ("weekly_summary", "本周总结", 2, "weekly_summary", "本周总结", weeklyContent.WeeklySummary ?? ""),
                ("next_week_plan", "下周规划", 3, "next_week_plan", "下周规划", weeklyContent.NextWeekPlan ?? "")
            };

            foreach (var (moduleKey, moduleTitle, moduleOrder, sectionKey, sectionTitle, content) in textContents)
            {
                if (!string.IsNullOrEmpty(content))
                {
                    contentList.Add(CreateContentRecord(reportId, input, moduleKey, moduleTitle, moduleOrder,
                        sectionKey, sectionTitle, content, currentUserId, currentUserName, reportDate));
                }
            }

            return contentList;
        }

        /// <summary>
        /// 保存月报工作量数据到work_data表
        /// </summary>
        private void SaveMonthlyWorkData(string reportId, CreateReportFixed_In input, VM_MonthlyReportInput monthlyContent)
        {
            var workDataList = new List<Db_crm_report_work_data>();
            var currentUserId = UserId;
            var currentUserName = GetCurrentUserName();
            var reportDate = input.ReportDate;
            var (teamId, teamName) = GetUserTeamInfo(currentUserId);

            // 月报工作量数据
            var workDataItems = new List<(string SectionKey, string SectionTitle, int? Value)>
            {
                ("phone_count", "电话量汇总", monthlyContent.TotalPhoneCount),
                ("visit_count", "拜访量汇总", monthlyContent.TotalVisitCount),
                ("email_precise_count", "邮件量汇总（精发）", monthlyContent.TotalEmailPreciseCount),
                ("email_bulk_count", "邮件量汇总（粗发）", monthlyContent.TotalEmailBulkCount),
                ("social_media_count", "社媒量汇总", monthlyContent.TotalSocialMediaCount),
                ("reply_count", "回复量汇总", monthlyContent.TotalReplyCount),
                ("demo_count", "演示量汇总", monthlyContent.TotalDemoCount),
                ("new_customer_count", "新增客户数", monthlyContent.TotalNewCustomerCount),
                ("follow_customer_count", "跟进客户数", monthlyContent.TotalFollowCustomerCount),
                ("contract_customer_count", "签约客户数", monthlyContent.TotalContractCustomerCount)
            };

            foreach (var (sectionKey, sectionTitle, value) in workDataItems)
            {
                // 修复：当值为0时也要保存，确保读取时能正确显示0
                if (value.HasValue)
                {
                    workDataList.Add(new Db_crm_report_work_data
                    {
                        Id = Guid.NewGuid().ToString(),
                        ReportId = reportId,
                        ReportType = (int)input.ReportType,
                        UserId = currentUserId,
                        UserName = currentUserName,
                        TeamId = teamId,
                        TeamName = teamName,
                        ReportDate = reportDate,
                        ReportYear = reportDate.Year,
                        ReportMonth = reportDate.Month,
                        ReportWeek = GetWeekOfYear(reportDate),
                        ModuleKey = "work_summary",
                        ModuleTitle = "工作汇总",
                        ModuleOrder = 1,
                        SectionKey = sectionKey,
                        SectionTitle = sectionTitle,
                        SectionOrder = 0,
                        DataType = "count",
                        DataValue = value.Value,
                        Deleted = false,
                        CreateUser = currentUserId,
                        CreateDate = DateTime.Now,
                        UpdateUser = currentUserId,
                        UpdateDate = DateTime.Now
                    });
                }
            }

            if (workDataList.Count > 0)
            {
                DbOpe_crm_report_work_data.Instance.CreateWorkDataRecords(workDataList);
            }
        }

        /// <summary>
        /// 保存月报文本内容到content表
        /// </summary>
        private List<Db_crm_report_content> SaveMonthlyTextContent(string reportId, CreateReportFixed_In input, VM_MonthlyReportInput monthlyContent)
        {
            var contentList = new List<Db_crm_report_content>();
            var currentUserId = UserId;
            var currentUserName = GetCurrentUserName();
            var reportDate = input.ReportDate;

            // 月报文本内容
            var textContents = new List<(string ModuleKey, string ModuleTitle, int ModuleOrder, string SectionKey, string SectionTitle, string Content)>
            {
                ("monthly_summary", "本月总结", 2, "monthly_summary", "本月总结", monthlyContent.MonthlySummary ?? ""),
                ("next_month_plan", "下月规划", 3, "next_month_plan", "下月规划", monthlyContent.NextMonthPlan ?? "")
            };

            foreach (var (moduleKey, moduleTitle, moduleOrder, sectionKey, sectionTitle, content) in textContents)
            {
                if (!string.IsNullOrEmpty(content))
                {
                    contentList.Add(CreateContentRecord(reportId, input, moduleKey, moduleTitle, moduleOrder,
                        sectionKey, sectionTitle, content, currentUserId, currentUserName, reportDate));
                }
            }

            return contentList;
        }



        /// <summary>
        /// 根据用户ID获取用户名称
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户名称</returns>
        private string GetUserNameById(string userId)
        {
            try
            {
                if (string.IsNullOrEmpty(userId))
                    return "";

                var user = DbOpe_sys_user.Instance.GetUserById(userId);
                return user?.Name ?? userId;
            }
            catch (Exception ex)
            {
                // 记录错误日志但不影响主流程
                LogUtil.AddErrorLog($"获取用户名称失败，用户ID：{userId}，错误：{ex.Message}");
                throw new ApiException($"获取用户名称失败，用户ID：{userId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户的团队信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>团队信息(团队ID, 团队名称)</returns>
        private (string TeamId, string TeamName) GetUserTeamInfo(string userId)
        {
            try
            {
                if (string.IsNullOrEmpty(userId))
                    return ("", "");

                var user = DbOpe_sys_user.Instance.GetUserById(userId);
                if (user == null || string.IsNullOrEmpty(user.OrganizationId))
                    return ("", "");

                var organization = DbOpe_sys_organization.Instance.GetOrganizationById(user.OrganizationId);
                return (organization?.Id ?? "", organization?.OrgName ?? "");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取用户团队信息失败，用户ID：{userId}，错误：{ex.Message}");
                throw new ApiException($"获取用户团队信息失败，用户ID：{userId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户的默认接收人设置
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="receiverIds">接收人ID列表</param>
        /// <param name="reportType">报告类型</param>
        /// <param name="reportId">报告ID，如果为null则创建虚拟reportId</param>
        private void UpdateDefaultReceivers(string userId, List<string> receiverIds, EnumReportType reportType, string reportId = null)
        {
            try
            {
                // 生成虚拟reportId
                string settingsReportId = Guid.Empty.ToString();
                
                // 1. 先将该用户所有的默认接收人设置为非默认
                var allUserReceivers = DbOpe_crm_report_receiver.Instance.GetDataList(x => 
                    x.IsDefault == true && 
                    x.CreateUser == userId &&
                    x.Deleted == false);
                
                foreach (var receiver in allUserReceivers)
                {
                    receiver.IsDefault = false;
                    receiver.UpdateUser = userId;
                    receiver.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_receiver.Instance.Update(receiver);
                }
                
                // 2. 设置新的默认接收人
                // 如果提供了实际reportId，则使用实际报告的接收人记录
                if (!string.IsNullOrEmpty(reportId))
                {
                    var reportReceivers = DbOpe_crm_report_receiver.Instance.GetDataList(x => 
                        x.ReportId == reportId && 
                        x.Deleted == false &&
                        receiverIds.Contains(x.ReceiverId));
                    
                    foreach (var receiver in reportReceivers)
                    {
                        receiver.IsDefault = true;
                        receiver.UpdateUser = userId;
                        receiver.UpdateDate = DateTime.Now;
                        DbOpe_crm_report_receiver.Instance.Update(receiver);
                    }
                }
                else
                {
                    // 如果没有提供实际reportId，则使用虚拟reportId创建记录
                    // 先删除旧的虚拟记录
                    var oldVirtualReceivers = DbOpe_crm_report_receiver.Instance.GetDataList(x => 
                        x.ReportId == settingsReportId && 
                        x.Deleted == false);
                    
                    foreach (var receiver in oldVirtualReceivers)
                    {
                        DbOpe_crm_report_receiver.Instance.Delete(receiver);
                    }
                    
                    // 创建新的虚拟记录
                    foreach (var receiverId in receiverIds)
                    {
                        var receiverUser = DbOpe_sys_user.Instance.GetData(x => x.Id == receiverId && x.Deleted == false);
                        if (receiverUser != null)
                        {
                            var newReceiver = new DAL.DbModel.Crm2.Db_crm_report_receiver
                            {
                                Id = Guid.NewGuid().ToString(),
                                ReportId = settingsReportId,
                                ReceiverId = receiverId,
                                ReceiverName = receiverUser.Name,
                                IsDefault = true,
                                Status = 1, // 未读状态
                                Deleted = false,
                                CreateUser = userId,
                                CreateDate = DateTime.Now,
                                UpdateUser = userId,
                                UpdateDate = DateTime.Now
                            };
                            
                            DbOpe_crm_report_receiver.Instance.Insert(newReceiver);
                        }
                    }
                }
                
                // 记录操作日志
                LogUtil.AddLog($"更新默认接收人：用户ID={userId}，报告类型={reportType}，接收人数量={receiverIds.Count}");
            }
            catch (Exception ex)
            {
                throw new ApiException($"更新默认接收人失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户的默认抄送人设置
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="ccIds">抄送人ID列表</param>
        /// <param name="reportType">报告类型</param>
        /// <param name="reportId">报告ID，如果为null则创建虚拟reportId</param>
        private void UpdateDefaultCcUsers(string userId, List<string> ccIds, EnumReportType reportType, string reportId = null)
        {
            try
            {
                // 生成虚拟reportId
                string settingsReportId = Guid.Empty.ToString();
                
                // 1. 先将该用户所有的默认抄送人设置为非默认
                var allUserCcs = DbOpe_crm_report_cc.Instance.GetDataList(x => 
                    x.IsDefault == true && 
                    x.CreateUser == userId &&
                    x.Deleted == false);
                
                foreach (var cc in allUserCcs)
                {
                    cc.IsDefault = false;
                    cc.UpdateUser = userId;
                    cc.UpdateDate = DateTime.Now;
                    DbOpe_crm_report_cc.Instance.Update(cc);
                }
                
                // 2. 设置新的默认抄送人
                // 如果提供了实际reportId，则使用实际报告的抄送人记录
                if (!string.IsNullOrEmpty(reportId))
                {
                    var reportCcs = DbOpe_crm_report_cc.Instance.GetDataList(x => 
                        x.ReportId == reportId && 
                        x.Deleted == false &&
                        ccIds.Contains(x.CcUserId));
                    
                    foreach (var cc in reportCcs)
                    {
                        cc.IsDefault = true;
                        cc.UpdateUser = userId;
                        cc.UpdateDate = DateTime.Now;
                        DbOpe_crm_report_cc.Instance.Update(cc);
                    }
                }
                else
                {
                    // 如果没有提供实际reportId，则使用虚拟reportId创建记录
                    // 先删除旧的虚拟记录
                    var oldVirtualCcs = DbOpe_crm_report_cc.Instance.GetDataList(x => 
                        x.ReportId == settingsReportId && 
                        x.Deleted == false);
                    
                    foreach (var cc in oldVirtualCcs)
                    {
                        DbOpe_crm_report_cc.Instance.Delete(cc);
                    }
                    
                    // 创建新的虚拟记录
                    foreach (var ccId in ccIds)
                    {
                        var ccUser = DbOpe_sys_user.Instance.GetData(x => x.Id == ccId && x.Deleted == false);
                        if (ccUser != null)
                        {
                            var newCc = new DAL.DbModel.Crm2.Db_crm_report_cc
                            {
                                Id = Guid.NewGuid().ToString(),
                                ReportId = settingsReportId,
                                CcUserId = ccId,
                                CcUserName = ccUser.Name,
                                IsDefault = true,
                                Status = 1, // 未读状态
                                Deleted = false,
                                CreateUser = userId,
                                CreateDate = DateTime.Now,
                                UpdateUser = userId,
                                UpdateDate = DateTime.Now
                            };
                            
                            DbOpe_crm_report_cc.Instance.Insert(newCc);
                        }
                    }
                }
                
                // 记录操作日志
                LogUtil.AddLog($"更新默认抄送人：用户ID={userId}，报告类型={reportType}，抄送人数量={ccIds.Count}");
            }
            catch (Exception ex)
            {
                throw new ApiException($"更新默认抄送人失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户现有的默认接收人ID列表
        /// </summary>
        private List<string> GetExistingDefaultReceiverIds(string userId, EnumReportType reportType)
        {
            try
            {
                // 生成虚拟reportId
                string virtualReportId = Guid.Empty.ToString();

                // 查询该虚拟reportId的默认接收人
                var defaultReceivers = DbOpe_crm_report_receiver.Instance.GetDataList(x => 
                    x.ReportId == virtualReportId && 
                    x.IsDefault == true && 
                    x.Deleted == false);

                return defaultReceivers.Select(x => x.ReceiverId).ToList();
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取现有默认接收人失败：{ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 增量更新报告附件
        /// </summary>
        /// <param name="reportId">报告ID</param>
        /// <param name="reportTitle">报告标题</param>
        /// <param name="keepAttachmentIds">要保留的附件ID列表</param>
        /// <param name="newFiles">新上传的文件</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="currentUserName">当前用户姓名</param>
        /// <returns>更新结果</returns>
        private (bool Success, string Message) UpdateReportAttachmentsIncremental(string reportId, string reportTitle, List<string> keepAttachmentIds, IFormFileCollection newFiles, string currentUserId, string currentUserName)
        {
            try
            {
                var currentTime = DateTime.Now;
                var deletedCount = 0;
                var keptCount = 0;
                var addedCount = 0;

                // 1. 获取现有的所有附件
                var existingAttachments = DbOpe_crm_report_attachment.Instance.GetDataList(x => x.ReportId == reportId && x.Deleted == false);

                // 2. 处理现有附件：保留指定的，删除其他的
                foreach (var attachment in existingAttachments)
                {
                    if (keepAttachmentIds != null && keepAttachmentIds.Contains(attachment.Id))
                    {
                        // 保留此附件
                        keptCount++;
                    }
                    else
                    {
                        // 删除此附件（包括云存储文件）
                        if (!string.IsNullOrEmpty(attachment.FilePath))
                        {
                            DeleteFileFromStorage(attachment.FilePath);
                        }

                        // 软删除数据库记录
                        attachment.Deleted = true;
                        attachment.UpdateDate = currentTime;
                        attachment.UpdateUser = currentUserId;
                        DbOpe_crm_report_attachment.Instance.Update(attachment);
                        deletedCount++;
                    }
                }

                // 3. 添加新上传的附件
                if (newFiles != null && newFiles.Count > 0)
                {
                    try
                    {
                        // 获取报告基础信息
                        var reportBase = DbOpe_crm_report_base.Instance.GetData(x => x.Id == reportId);
                        if (reportBase == null)
                        {
                            return (false, "报告不存在，无法添加附件");
                        }

                        var attachmentList = SaveAttachments(
                            reportId,
                            reportBase.ReportType,
                            reportBase.ReportDate,
                            reportTitle,
                            WorkReportConstants.Attachment.ModuleKey,
                            WorkReportConstants.Attachment.ModuleTitle,
                            WorkReportConstants.Attachment.ModuleOrder,
                            WorkReportConstants.Attachment.SectionKey,
                            WorkReportConstants.Attachment.SectionTitle,
                            WorkReportConstants.Attachment.SectionOrder,
                            newFiles);

                        if (attachmentList.Count > 0)
                        {
                            DbOpe_crm_report_attachment.Instance.CreateAttachments(attachmentList);
                            addedCount = attachmentList.Count;
                        }
                    }
                    catch (Exception ex)
                    {
                        return (false, $"保存新附件失败：{ex.Message}");
                    }
                }

                var message = $"保留 {keptCount} 个附件，删除 {deletedCount} 个附件，新增 {addedCount} 个附件";
                return (true, message);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"增量更新报告附件失败，报告ID：{reportId}，错误：{ex.Message}");
                throw new ApiException($"增量更新报告附件失败，报告ID：{reportId}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 从存储中删除文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        private bool DeleteFileFromStorage(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                    return true;

                // 检查是否启用腾讯云存储
                if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                {
                    // 从腾讯云删除
                    QCloudOperator qCloud = new QCloudOperator();
                    qCloud.DeleteFile(filePath);
                }
                else
                {
                    // 从本地文件系统删除
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"删除存储文件失败，路径：{filePath}，错误：{ex.Message}");
                throw new ApiException($"删除存储文件失败，路径：{filePath}，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量获取报告的附件数量
        /// </summary>
        /// <param name="reportIds">报告ID列表</param>
        /// <returns>报告ID和附件数量的字典</returns>
        private Dictionary<string, int> GetReportAttachmentCounts(List<string> reportIds)
        {
            try
            {
                if (reportIds == null || reportIds.Count == 0)
                    return new Dictionary<string, int>();

                var attachmentCounts = new Dictionary<string, int>();

                // 查询所有报告的附件数量
                var attachments = DbOpe_crm_report_attachment.Instance.GetDataList(x => reportIds.Contains(x.ReportId) && x.Deleted == false);

                // 按报告ID分组统计
                var groupedAttachments = attachments.GroupBy(x => x.ReportId);
                foreach (var group in groupedAttachments)
                {
                    attachmentCounts[group.Key] = group.Count();
                }

                return attachmentCounts;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取报告附件数量失败，错误：{ex.Message}");
                throw new ApiException($"获取报告附件数量失败，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量获取报告的客户数量
        /// </summary>
        /// <param name="reportIds">报告ID列表</param>
        /// <returns>报告ID和客户数量的字典</returns>
        private Dictionary<string, int> GetReportCustomerCounts(List<string> reportIds)
        {
            try
            {
                if (reportIds == null || reportIds.Count == 0)
                    return new Dictionary<string, int>();

                var customerCounts = new Dictionary<string, int>();

                // 查询所有报告的客户数量
                var customers = DbOpe_crm_report_customer.Instance.GetDataList(x => reportIds.Contains(x.ReportId) && x.Deleted == false);

                // 按报告ID分组统计
                var groupedCustomers = customers.GroupBy(x => x.ReportId);
                foreach (var group in groupedCustomers)
                {
                    customerCounts[group.Key] = group.Count();
                }

                return customerCounts;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取报告客户数量失败，错误：{ex.Message}");
                throw new ApiException($"获取报告客户数量失败，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量获取报告的点赞数量
        /// </summary>
        /// <param name="reportIds">报告ID列表</param>
        /// <returns>报告ID和点赞数量的字典</returns>
        private Dictionary<string, int> GetReportLikeCounts(List<string> reportIds)
        {
            try
            {
                if (reportIds == null || reportIds.Count == 0)
                    return new Dictionary<string, int>();

                // 使用DbOpe层的批量查询方法
                return DbOpe_crm_report_like.Instance.GetLikeCounts(reportIds);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取报告点赞数量失败，错误：{ex.Message}");
                throw new ApiException($"获取报告点赞数量失败，错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量获取报告的评论数量
        /// </summary>
        /// <param name="reportIds">报告ID列表</param>
        /// <returns>报告ID和评论数量的字典</returns>
        private Dictionary<string, int> GetReportCommentCounts(List<string> reportIds)
        {
            try
            {
                if (reportIds == null || reportIds.Count == 0)
                    return new Dictionary<string, int>();

                // 使用DbOpe层的批量查询方法
                return DbOpe_crm_report_comment.Instance.GetCommentCounts(reportIds);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取报告评论数量失败，错误：{ex.Message}");
                throw new ApiException($"获取报告评论数量失败，错误：{ex.Message}");
            }
        }

        #endregion

        #region SearchReportList 增强功能字段辅助方法

        /// <summary>
        /// TODO-H007: 批量获取报告的接收人姓名列表
        /// </summary>
        /// <param name="reportIds">报告ID列表</param>
        /// <returns>报告ID和接收人姓名列表的字典</returns>
        private Dictionary<string, List<string>> GetReportReceiverNames(List<string> reportIds)
        {
            try
            {
                if (reportIds == null || reportIds.Count == 0)
                    return new Dictionary<string, List<string>>();

                // 查询接收人记录
                var receivers = DbOpe_crm_report_receiver.Instance.GetDataList(x =>
                    reportIds.Contains(x.ReportId) && x.Deleted == false);

                // 获取用户ID列表
                var userIds = receivers.Select(x => x.ReceiverId).Distinct().ToList();

                // 查询用户信息
                var users = DbOpe_v_userwithorg.Instance.GetDataList(x => userIds.Contains(x.Id));
                var userDict = users.ToDictionary(x => x.Id, x => x.Name);

                // 构建结果字典
                var result = new Dictionary<string, List<string>>();
                foreach (var group in receivers.GroupBy(x => x.ReportId))
                {
                    var names = group.Select(r => userDict.ContainsKey(r.ReceiverId) ? userDict[r.ReceiverId] : r.ReceiverName ?? "未知用户")
                                    .Where(name => !string.IsNullOrEmpty(name))
                                    .Distinct()
                                    .ToList();
                    result[group.Key] = names;
                }

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取接收人姓名列表失败：{ex.Message}");
                throw new ApiException($"获取接收人姓名列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// TODO-H008: 批量获取报告的抄送人姓名列表
        /// </summary>
        /// <param name="reportIds">报告ID列表</param>
        /// <returns>报告ID和抄送人姓名列表的字典</returns>
        private Dictionary<string, List<string>> GetReportCcNames(List<string> reportIds)
        {
            try
            {
                if (reportIds == null || reportIds.Count == 0)
                    return new Dictionary<string, List<string>>();

                // 查询抄送人记录
                var ccRecords = DbOpe_crm_report_cc.Instance.GetDataList(x =>
                    reportIds.Contains(x.ReportId) && x.Deleted == false);

                // 获取用户ID列表
                var userIds = ccRecords.Select(x => x.CcUserId).Distinct().ToList();

                // 查询用户信息
                var users = DbOpe_v_userwithorg.Instance.GetDataList(x => userIds.Contains(x.Id));
                var userDict = users.ToDictionary(x => x.Id, x => x.Name);

                // 构建结果字典
                var result = new Dictionary<string, List<string>>();
                foreach (var group in ccRecords.GroupBy(x => x.ReportId))
                {
                    var names = group.Select(r => userDict.ContainsKey(r.CcUserId) ? userDict[r.CcUserId] : r.CcUserName ?? "未知用户")
                                    .Where(name => !string.IsNullOrEmpty(name))
                                    .Distinct()
                                    .ToList();
                    result[group.Key] = names;
                }

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取抄送人姓名列表失败：{ex.Message}");
                throw new ApiException($"获取抄送人姓名列表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 计算提交状态
        /// </summary>
        /// <param name="status">报告状态</param>
        /// <param name="submitTime">提交时间</param>
        /// <param name="reportDate">报告日期</param>
        /// <param name="reportType">报告类型</param>
        /// <returns>提交状态</returns>
        private EnumSubmitStatus CalculateSubmitStatus(int status, DateTime submitTime, DateTime reportDate, EnumReportType reportType)
        {
            try
            {
                // 如果是草稿状态，检查是否超期
                if (status == (int)EnumReportStatus.Draft)
                {
                    var deadlineResult = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                    {
                        ReportType = reportType,
                        ReportDate = reportDate
                    });
                    var deadline = DateTime.Parse(deadlineResult.FinalTime);
                    if (DateTime.Now > deadline)
                        return EnumSubmitStatus.NotSubmitted; // 未提交（超期）
                    else
                        return EnumSubmitStatus.NotSubmitted; // 未提交（未超期）
                }

                // 如果已提交，检查是否迟交
                if (status == (int)EnumReportStatus.Submitted)
                {
                    var deadlineResult = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                    {
                        ReportType = reportType,
                        ReportDate = reportDate
                    });
                    var regularTime = DateTime.Parse(deadlineResult.RegularTime);
                    var lateTime = DateTime.Parse(deadlineResult.LateTime);
                    var finalTime = DateTime.Parse(deadlineResult.FinalTime);
                    
                    // 如果超过最终截止时间提交，算作未提交
                    if (submitTime > finalTime)
                        return EnumSubmitStatus.NotSubmitted;
                    // 如果超过迟交时间但未超过最终截止时间，算作迟交
                    else if (submitTime > lateTime)
                        return EnumSubmitStatus.Late;
                    // 在迟交时间之前提交，算作准时
                    else
                        return EnumSubmitStatus.OnTime;
                }

                return EnumSubmitStatus.NotSubmitted;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"计算提交状态失败：{ex.Message}");
                throw new ApiException($"计算提交状态失败：{ex.Message}");
            }
        }

        /// <summary>
        /// TODO-M007: 计算是否迟交
        /// </summary>
        /// <param name="status">报告状态</param>
        /// <param name="createDate">创建时间</param>
        /// <param name="reportDate">报告日期</param>
        /// <param name="reportType">报告类型</param>
        /// <returns>是否迟交</returns>
        private bool CalculateIsLate(int status, DateTime createDate, DateTime reportDate, EnumReportType reportType)
        {
            try
            {
                if (status != (int)EnumReportStatus.Submitted)
                    return false;

                var deadlineResult = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                {
                    ReportType = reportType,
                    ReportDate = reportDate
                });
                var deadline = DateTime.Parse(deadlineResult.FinalTime);
                return createDate > deadline;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"计算是否迟交失败：{ex.Message}");
                throw new ApiException($"计算是否迟交失败：{ex.Message}");
            }
        }

        /// <summary>
        /// TODO-M007: 计算是否超期未提交
        /// </summary>
        /// <param name="status">报告状态</param>
        /// <param name="reportDate">报告日期</param>
        /// <param name="reportType">报告类型</param>
        /// <returns>是否超期未提交</returns>
        private bool CalculateIsOverdue(int status, DateTime reportDate, EnumReportType reportType)
        {
            try
            {
                if (status == (int)EnumReportStatus.Submitted)
                    return false;

                var deadlineResult = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                {
                    ReportType = reportType,
                    ReportDate = reportDate
                });
                var deadline = DateTime.Parse(deadlineResult.FinalTime);
                return DateTime.Now > deadline;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"计算是否超期失败：{ex.Message}");
                throw new ApiException($"计算是否超期失败：{ex.Message}");
            }
        }

        /// <summary>
        /// TODO-M007: 计算超期天数
        /// </summary>
        /// <param name="status">报告状态</param>
        /// <param name="reportDate">报告日期</param>
        /// <param name="reportType">报告类型</param>
        /// <returns>超期天数</returns>
        private int CalculateOverdueDays(int status, DateTime reportDate, EnumReportType reportType)
        {
            try
            {
                if (status == (int)EnumReportStatus.Submitted)
                    return 0;

                var deadlineResult = CalculateReportDeadline(new VM_ReportDeadlineCalc_In
                {
                    ReportType = reportType,
                    ReportDate = reportDate
                });
                var deadline = DateTime.Parse(deadlineResult.FinalTime);
                if (DateTime.Now <= deadline)
                    return 0;

                return (DateTime.Now.Date - deadline.Date).Days;
            }
            catch (Exception ex)
            {       
                LogUtil.AddErrorLog($"计算超期天数失败：{ex.Message}");
                throw new ApiException($"计算超期天数失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新报告阅读状态
        /// </summary>
        /// <param name="reportId">报告ID</param>
        private void UpdateReportReadStatus(string reportId)
        {
            try
            {
                var currentUserId = UserId;
                if (string.IsNullOrEmpty(currentUserId))
                    return;

                var currentTime = DateTime.Now;

                // 1. 更新作为接收人的阅读状态
                var receiverRecord = DbOpe_crm_report_receiver.Instance.GetData(x => 
                    x.ReportId == reportId && x.ReceiverId == currentUserId && x.Deleted == false);
                
                if (receiverRecord != null && receiverRecord.Status == (int)EnumReceiverStatus.Unread)
                {
                    receiverRecord.Status = (int)EnumReceiverStatus.Read;
                    receiverRecord.ReadTime = currentTime;
                    receiverRecord.UpdateUser = currentUserId;
                    receiverRecord.UpdateDate = currentTime;
                    DbOpe_crm_report_receiver.Instance.Update(receiverRecord);
                }

                // 2. 更新作为抄送人的阅读状态
                var ccRecord = DbOpe_crm_report_cc.Instance.GetData(x => 
                    x.ReportId == reportId && x.CcUserId == currentUserId && x.Deleted == false);
                
                if (ccRecord != null && ccRecord.Status == (int)EnumReceiverStatus.Unread)
                {
                    ccRecord.Status = (int)EnumReceiverStatus.Read;
                    ccRecord.UpdateUser = currentUserId;
                    ccRecord.UpdateDate = currentTime;
                    DbOpe_crm_report_cc.Instance.Update(ccRecord);
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新报告阅读状态失败，报告ID：{reportId}，错误：{ex.Message}");
                // 不抛出异常，避免影响报告详情获取
            }
        }

        /// <summary>
        /// TODO-M008: 获取当前用户对报告的阅读状态
        /// </summary>
        /// <param name="reportIds">报告ID列表</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>报告ID和阅读状态的字典</returns>
        private Dictionary<string, (EnumReceiverStatus? status, DateTime? readTime)> GetMyReadStatus(List<string> reportIds, string currentUserId)
        {
            try
            {
                if (reportIds == null || reportIds.Count == 0 || string.IsNullOrEmpty(currentUserId))
                    return new Dictionary<string, (EnumReceiverStatus?, DateTime?)>();

                var result = new Dictionary<string, (EnumReceiverStatus?, DateTime?)>();

                // 1. 查询作为接收人的记录
                var myReceiverRecords = DbOpe_crm_report_receiver.Instance.GetDataList(x =>
                    reportIds.Contains(x.ReportId) && x.ReceiverId == currentUserId && x.Deleted == false);

                foreach (var record in myReceiverRecords)
                {
                    var status = (EnumReceiverStatus)record.Status;
                    var readTime = record.ReadTime;
                    result[record.ReportId] = (status, readTime);
                }

                // 2. 查询作为抄送人的记录（如果还没有状态记录）
                var myCcRecords = DbOpe_crm_report_cc.Instance.GetDataList(x =>
                    reportIds.Contains(x.ReportId) && x.CcUserId == currentUserId && x.Deleted == false);

                foreach (var record in myCcRecords)
                {
                    if (!result.ContainsKey(record.ReportId))
                    {
                        var status = (EnumReceiverStatus)record.Status;
                        // CC表没有ReadTime字段，只有Status状态
                        DateTime? readTime = null;
                        result[record.ReportId] = (status, readTime);
                    }
                }

                // 3. 对于没有阅读状态记录的报告，检查是否是创建者
                // 如果是创建者，可以设置为已读状态（因为创建者默认已读）
                var reportsWithoutStatus = reportIds.Where(id => !result.ContainsKey(id)).ToList();
                if (reportsWithoutStatus.Any())
                {
                    var reportBaseList = DbOpe_crm_report_base.Instance.GetDataList(x => 
                        reportsWithoutStatus.Contains(x.Id) && x.Deleted == false);
                    
                    foreach (var report in reportBaseList)
                    {
                        if (report.CreateUser == currentUserId)
                        {
                            // 创建者默认已读
                            result[report.Id] = (EnumReceiverStatus.Read, report.CreateDate);
                        }
                        // 其他情况保持null，表示用户与此报告无关
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取阅读状态失败：{ex.Message}");
                throw new ApiException($"获取阅读状态失败：{ex.Message}");
            }
        }

        #endregion

        #region 工作量数据验证方法

        /// <summary>
        /// 验证日报工作量数据
        /// </summary>
        /// <param name="dailyContent">日报内容</param>
        /// <returns>验证结果</returns>
        private (bool IsValid, string ErrorMessage) ValidateDailyWorkData(VM_DailyReportInput dailyContent)
        {
            try
            {
                // 验证电话量
                if (dailyContent.PhoneCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(dailyContent.PhoneCount.Value, "电话量");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证拜访量
                if (dailyContent.VisitCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(dailyContent.VisitCount.Value, "拜访量");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证邮件量（精发）
                if (dailyContent.EmailPreciseCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(dailyContent.EmailPreciseCount.Value, "邮件量（精发）");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证邮件量（粗发）
                if (dailyContent.EmailBulkCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(dailyContent.EmailBulkCount.Value, "邮件量（粗发）");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证社媒量
                if (dailyContent.SocialMediaCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(dailyContent.SocialMediaCount.Value, "社媒量");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证回复量
                if (dailyContent.ReplyCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(dailyContent.ReplyCount.Value, "回复量");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证演示量
                if (dailyContent.DemoCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(dailyContent.DemoCount.Value, "演示量");
                    if (!validation.IsValid)
                        return validation;
                }

                return (true, "");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"验证日报工作量数据失败：{ex.Message}");
                return (false, $"验证日报工作量数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 验证周报工作量数据
        /// </summary>
        /// <param name="weeklyContent">周报内容</param>
        /// <returns>验证结果</returns>
        private (bool IsValid, string ErrorMessage) ValidateWeeklyWorkData(VM_WeeklyReportInput weeklyContent)
        {
            try
            {
                // 验证电话量汇总
                if (weeklyContent.TotalPhoneCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalPhoneCount.Value, "电话量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证拜访量汇总
                if (weeklyContent.TotalVisitCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalVisitCount.Value, "拜访量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证邮件量汇总（精发）
                if (weeklyContent.TotalEmailPreciseCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalEmailPreciseCount.Value, "邮件量汇总（精发）");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证邮件量汇总（粗发）
                if (weeklyContent.TotalEmailBulkCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalEmailBulkCount.Value, "邮件量汇总（粗发）");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证社媒量汇总
                if (weeklyContent.TotalSocialMediaCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalSocialMediaCount.Value, "社媒量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证回复量汇总
                if (weeklyContent.TotalReplyCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalReplyCount.Value, "回复量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证演示量汇总
                if (weeklyContent.TotalDemoCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalDemoCount.Value, "演示量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证新增客户数
                if (weeklyContent.TotalNewCustomerCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalNewCustomerCount.Value, "新增客户数");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证跟进客户数
                if (weeklyContent.TotalFollowCustomerCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalFollowCustomerCount.Value, "跟进客户数");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证签约客户数
                if (weeklyContent.TotalContractCustomerCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(weeklyContent.TotalContractCustomerCount.Value, "签约客户数");
                    if (!validation.IsValid)
                        return validation;
                }

                return (true, "");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"验证周报工作量数据失败：{ex.Message}");
                return (false, $"验证周报工作量数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 验证月报工作量数据
        /// </summary>
        /// <param name="monthlyContent">月报内容</param>
        /// <returns>验证结果</returns>
        private (bool IsValid, string ErrorMessage) ValidateMonthlyWorkData(VM_MonthlyReportInput monthlyContent)
        {
            try
            {
                // 验证电话量汇总
                if (monthlyContent.TotalPhoneCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalPhoneCount.Value, "电话量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证拜访量汇总
                if (monthlyContent.TotalVisitCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalVisitCount.Value, "拜访量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证邮件量汇总（精发）
                if (monthlyContent.TotalEmailPreciseCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalEmailPreciseCount.Value, "邮件量汇总（精发）");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证邮件量汇总（粗发）
                if (monthlyContent.TotalEmailBulkCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalEmailBulkCount.Value, "邮件量汇总（粗发）");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证社媒量汇总
                if (monthlyContent.TotalSocialMediaCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalSocialMediaCount.Value, "社媒量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证回复量汇总
                if (monthlyContent.TotalReplyCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalReplyCount.Value, "回复量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证演示量汇总
                if (monthlyContent.TotalDemoCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalDemoCount.Value, "演示量汇总");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证新增客户数
                if (monthlyContent.TotalNewCustomerCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalNewCustomerCount.Value, "新增客户数");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证跟进客户数
                if (monthlyContent.TotalFollowCustomerCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalFollowCustomerCount.Value, "跟进客户数");
                    if (!validation.IsValid)
                        return validation;
                }

                // 验证签约客户数
                if (monthlyContent.TotalContractCustomerCount.HasValue)
                {
                    var validation = ValidateWorkDataValue(monthlyContent.TotalContractCustomerCount.Value, "签约客户数");
                    if (!validation.IsValid)
                        return validation;
                }

                return (true, "");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"验证月报工作量数据失败：{ex.Message}");
                return (false, $"验证月报工作量数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 验证工作量数据值
        /// </summary>
        /// <param name="value">数据值</param>
        /// <param name="fieldName">字段名称</param>
        /// <returns>验证结果</returns>
        private (bool IsValid, string ErrorMessage) ValidateWorkDataValue(int value, string fieldName)
        {
            try
            {
                // 检查最小值
                if (value < WorkReportConstants.WorkDataValidation.MinWorkDataValue)
                {
                    return (false, $"{fieldName}不能小于{WorkReportConstants.WorkDataValidation.MinWorkDataValue}");
                }

                // 检查最大值
                if (value > WorkReportConstants.WorkDataValidation.MaxWorkDataValue)
                {
                    return (false, $"{fieldName}不能大于{WorkReportConstants.WorkDataValidation.MaxWorkDataValue}，当前值：{value}");
                }

                return (true, "");
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"验证工作量数据值失败，字段：{fieldName}，值：{value}，错误：{ex.Message}");
                return (false, $"验证{fieldName}失败：{ex.Message}");
            }
        }

        #endregion

        #region 阅读状态辅助方法

        /// <summary>
        /// 获取用户的阅读状态
        /// </summary>
        /// <param name="reportCreatorId">报告创建者ID</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="actualReadStatus">实际的阅读状态</param>
        /// <returns>阅读状态</returns>
        private EnumReceiverStatus? GetReadStatusForUser(string reportCreatorId, string currentUserId, EnumReceiverStatus? actualReadStatus)
        {
            // 如果是报告创建者，默认已读
            if (reportCreatorId == currentUserId)
            {
                return EnumReceiverStatus.Read;
            }
            
            // 如果是接收人，返回实际的阅读状态
            return actualReadStatus;
        }

        /// <summary>
        /// 获取用户的阅读时间
        /// </summary>
        /// <param name="reportCreatorId">报告创建者ID</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="actualReadTime">实际的阅读时间</param>
        /// <returns>阅读时间字符串</returns>
        private string GetReadDateForUser(string reportCreatorId, string currentUserId, DateTime? actualReadTime)
        {
            // 如果是报告创建者，不显示阅读时间
            if (reportCreatorId == currentUserId)
            {
                return null;
            }
            
            // 如果是接收人，返回实际的阅读时间
            return actualReadTime?.ToString("yyyy-MM-dd HH:mm:ss");
        }

        #endregion
    }
}
