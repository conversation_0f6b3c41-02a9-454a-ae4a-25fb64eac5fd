using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BLLModel.InvoiceSystem;
using CRM2_API.Model.ControllersViewModel.InvoiceSystem;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using CRM2_API.Common;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbCommon;
using SqlSugar;
using CRM2_API.DAL.DbModel.Crm2;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using CRM2_API.Model.System;
using Chinese;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 到账开票权限相关功能
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        #region 到账开票权限查询方法

        /// <summary>
        /// 根据到账ID获取权限详情信息
        /// </summary>
        /// <param name="receiptId">到账ID</param>
        /// <returns>权限详情信息</returns>
        public VM_InvoiceReceiptAuthority.AuthorityDetailResponse GetAuthorityByReceiptId(string receiptId)
        {
            if (string.IsNullOrEmpty(receiptId))
            {
                throw new ArgumentNullException(nameof(receiptId), "到账ID不能为空");
            }

            // 1. 获取权限记录
            var authority = DbOpe_crm_invoice_receipt_authority.Instance.GetByReceiptId(receiptId);
            if (authority == null)
            {
                throw new ApiException("未找到权限记录");
            }

            // 2. 获取到账信息
            var receipt = DbOpe_crm_contract_receiptregister.Instance.GetData(r => r.Id == receiptId);
            if (receipt == null)
            {
                throw new ApiException("未找到到账信息");
            }

            // 3. 获取到账匹配发票信息
            var matchedInvoice = GetMatchingDetailByReceiptId(receiptId);  

            // 5. 获取权限历史记录
            var historyRecords = GetAuthorityHistory(authority.Id);

            // 6. 获取创建人和修改人信息
            var createUser = DbOpe_sys_user.Instance.GetDataById(authority.CreateUser ?? "");
            var updateUser = DbOpe_sys_user.Instance.GetDataById(authority.UpdateUser ?? "");

            // 7. 构建权限信息
            var authorityInfo = new VM_InvoiceReceiptAuthority.AuthorityInfo
            {
                Id = authority.Id,
                ReceiptId = authority.ReceiptId,
                IsDomesticRmb = (EnumIsDomesticRmb)authority.IsDomesticRmb,
                CanInvoice = (EnumCanInvoice)authority.CanInvoice,
                AuthorityRemark = authority.AuthorityRemark,
                CreateUser = authority.CreateUser,
                CreateUserName = createUser?.Name,
                CreateDate = authority.CreateDate,
                UpdateUser = authority.UpdateUser,
                UpdateUserName = updateUser?.Name,
                UpdateDate = authority.UpdateDate
            };

            // 8. 构建完整响应
            return new VM_InvoiceReceiptAuthority.AuthorityDetailResponse
            {
                MatchingDetail = matchedInvoice,
                AuthorityInfo = authorityInfo,
                HistoryRecords = historyRecords
            };
        }

        /// <summary>
        /// 查询权限历史记录
        /// </summary>
        /// <param name="authorityId">权限记录ID</param>
        /// <returns>历史记录列表</returns>
        public List<VM_InvoiceReceiptAuthority.AuthorityHistoryResponse> GetAuthorityHistory(string authorityId)
        {
            if (string.IsNullOrEmpty(authorityId))
            {
                throw new ArgumentNullException(nameof(authorityId), "权限ID不能为空");
            }

            var histories = DbOpe_crm_invoice_receipt_authority_history.Instance.GetByAuthorityId(authorityId);
            if (histories == null || histories.Count == 0)
            {
                return new List<VM_InvoiceReceiptAuthority.AuthorityHistoryResponse>();
            }

            // 获取用户信息
            var userIds = histories.Select(h => h.CreateUser).Where(id => !string.IsNullOrEmpty(id)).Distinct().ToList();
            
            // 查询用户信息字典
            var userDict = new Dictionary<string, Db_sys_user>();
            if (userIds.Count > 0)
            {
                var users = DbOpe_sys_user.Instance.GetDataList(u => userIds.Contains(u.Id));
                userDict = users.ToDictionary(u => u.Id, u => u);
            }

            // 构建响应列表
            var result = new List<VM_InvoiceReceiptAuthority.AuthorityHistoryResponse>();
            foreach (var history in histories)
            {
                userDict.TryGetValue(history.CreateUser ?? "", out var createUser);

                result.Add(new VM_InvoiceReceiptAuthority.AuthorityHistoryResponse
                {
                    Id = history.Id,
                    AuthorityId = history.AuthorityId,
                    ReceiptId = history.ReceiptId,
                    OldIsDomesticRmb = (EnumIsDomesticRmb)history.OldIsDomesticRmb,
                    NewIsDomesticRmb = (EnumIsDomesticRmb)history.NewIsDomesticRmb,
                    OldCanInvoice = (EnumCanInvoice)history.OldCanInvoice,
                    NewCanInvoice = (EnumCanInvoice)history.NewCanInvoice,
                    ModifyRemark = history.ModifyRemark,
                    CreateUser = history.CreateUser,
                    CreateUserName = createUser?.Name,
                    CreateDate = history.CreateDate
                });
            }

            return result;
        }

        #endregion

        #region 到账开票权限操作方法

        /// <summary>
        /// 检测字符串是否包含繁体字符
        /// </summary>
        /// <param name="text">需要检测的文本</param>
        /// <returns>是否包含繁体字符</returns>
        private bool ContainsTraditionalChinese(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return false;

            try
            {
                // 使用Chinese库将文本转换为简体
                string simplified = ChineseConverter.ToSimplified(text);
                
                // 如果转换后的文本与原文不同，则包含繁体字
                return text != simplified;
            }
            catch (Exception ex)
            {
                // 记录异常但不影响业务流程
                Console.WriteLine($"检测繁体字符发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 保存权限记录（创建或更新）
        /// 如果关闭开票权限时存在绑定的发票/申请，则自动作废/解绑
        /// 刷新合同下其他匹配待确认/未到账的发票的匹配状态
        /// </summary>
        /// <param name="request">权限请求（创建或更新）</param>
        /// <param name="isAutoJudge">是否自动判断权限状态</param>
        /// <returns>权限ID</returns>
        public string SaveAuthority(VM_InvoiceReceiptAuthority.SaveAuthorityRequest request,bool isAutoJudge = false)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request), "请求参数不能为空");
            }

            string userId = TokenModel.Instance.id;
            DateTime now = DateTime.Now;
            
            // 查询是否存在权限记录
            var existingAuthority = DbOpe_crm_invoice_receipt_authority.Instance.GetByReceiptId(request.ReceiptId);

    
            // 如果存在记录，则更新(非自动判断)
            if (existingAuthority != null && !isAutoJudge)
            {
                if(string.IsNullOrEmpty(request.AuthorityRemark))
                {
                    throw new ApiException("权限设置备注不能为空");
                }
                var clearInvoice = existingAuthority.CanInvoice == (int)EnumCanInvoice.Yes && request.CanInvoice == (int)EnumCanInvoice.No;
                // 创建历史记录
                var history = new Db_crm_invoice_receipt_authority_history
                {
                    Id = Guid.NewGuid().ToString(),
                    AuthorityId = existingAuthority.Id,
                    ReceiptId = existingAuthority.ReceiptId,
                    OldIsDomesticRmb = (int)existingAuthority.IsDomesticRmb, 
                    NewIsDomesticRmb = (int)request.IsDomesticRmb,
                    OldCanInvoice = (int)existingAuthority.CanInvoice,
                    NewCanInvoice = (int)request.CanInvoice,
                    ModifyRemark = request.AuthorityRemark,
                    CreateUser = userId,
                    CreateDate = now,
                    UpdateUser = userId,
                    UpdateDate = now,
                    Deleted = false
                };

                // 更新权限记录
                existingAuthority.IsDomesticRmb = (int)request.IsDomesticRmb;
                existingAuthority.CanInvoice = (int)request.CanInvoice;
                existingAuthority.AuthorityRemark = request.AuthorityRemark;
                existingAuthority.UpdateUser = userId;
                existingAuthority.UpdateDate = now;
                // 开启事务进行更新
                try
                {
                    DbOpe_crm_invoice_receipt_authority.Instance.TransDeal(() =>
                    {

                        // 保存历史记录
                        DbOpe_crm_invoice_receipt_authority_history.Instance.Add(history);
                        
                        // 更新权限记录
                        DbOpe_crm_invoice_receipt_authority.Instance.Update(existingAuthority);
                        
                        // 如果开票权限从允许更改为不允许时，检查这笔到账存在的绑定的发票/申请，自动作废/解绑
                        if(clearInvoice)
                        {
                            UnbindInvoiceReceipt(request.ReceiptId,request.AuthorityRemark,false);
                        }
                        // 刷新合同下其他匹配待确认/未到账的发票的匹配状态
                        RefreshMatchingStatus(existingAuthority.ReceiptId);

                    });
                    return existingAuthority.Id;
                }
                catch (Exception ex)
                {
                    throw new ApiException($"更新权限记录失败: {ex.Message}");
                }
            }
            // 如果记录不存在，则创建(自动判断的话要先删除再创建)
            else
            {
                // 创建新权限记录
                var authority = new Db_crm_invoice_receipt_authority
                {
                    Id = Guid.NewGuid().ToString(),
                    ReceiptId = request.ReceiptId,
                    IsDomesticRmb = (int)request.IsDomesticRmb,
                    CanInvoice = (int)request.CanInvoice,
                    AuthorityRemark = request.AuthorityRemark,
                    CreateUser = userId,
                    CreateDate = now,
                    UpdateUser = userId,
                    UpdateDate = now,
                    Deleted = false
                };
                DbOpe_crm_invoice_receipt_authority.Instance.TransDeal(() =>
                {
                    var clearInvoice = false;
                    // 如果自动判断，则先删除
                    if(isAutoJudge && existingAuthority != null)
                    {
                        clearInvoice = existingAuthority.CanInvoice == (int)EnumCanInvoice.Yes && authority.CanInvoice == (int)EnumCanInvoice.No;
                        // 删除权限记录 
                        existingAuthority.Deleted = true;
                        existingAuthority.UpdateUser = userId;
                        existingAuthority.UpdateDate = now;
                        DbOpe_crm_invoice_receipt_authority.Instance.Update(existingAuthority);
                    }
                    // 保存记录
                    DbOpe_crm_invoice_receipt_authority.Instance.Insert(authority);
                    // 如果开票权限从允许更改为不允许时，检查这笔到账存在的绑定的发票/申请，自动作废/解绑
                    if(clearInvoice)
                    {
                        UnbindInvoiceReceipt(request.ReceiptId,request.AuthorityRemark,false);
                    }
                    // 刷新合同下其他匹配待确认/未到账的发票的匹配状态
                    RefreshMatchingStatus(request.ReceiptId);
                });
                return authority.Id;
            }
        }

        /// <summary>
        /// 清除权限记录
        /// </summary>
        /// <param name="receiptId">到账ID</param>
        public void ClearReceiptAuthority(string receiptId)
        {
            var authority = DbOpe_crm_invoice_receipt_authority.Instance.GetByReceiptId(receiptId);
            if(authority != null)
            {
                authority.Deleted = true;
                DbOpe_crm_invoice_receipt_authority.Instance.Update(authority);
            }
        }

        /// <summary>
        /// 单个判断到账开票权限
        /// 人工设置过权限的，不进行自动判断（历史记录中存在修改记录），自动返回当前权限状态
        /// 否则，会重新判断权限状态
        /// </summary>
        /// <param name="receiptId">到账ID</param>
        /// <returns>是否可以开票</returns>
        public bool AutoJudgeAuthority(string receiptId)
        {
            if (string.IsNullOrEmpty(receiptId))
            {
                throw new ArgumentNullException(nameof(receiptId), "到账ID不能为空");
            }

            // 查询是否已存在权限记录
            
            var existingAuthority = DbOpe_crm_invoice_receipt_authority.Instance.GetByReceiptId(receiptId);
            if (existingAuthority != null)
            {
                //已设置过权限的，需要判断是否人工修改过(存在修改历史记录)
                var history = DbOpe_crm_invoice_receipt_authority_history.Instance.GetByAuthorityId(existingAuthority.Id);
                if(history != null && history.Count > 0)
                {
                    // 如果人工修改过，则不进行自动判断,自动返回当前权限状态
                    LogUtil.AddDebugLog(receiptId + "人工修改过，则不进行自动判断,自动返回当前权限状态");
                    return existingAuthority.CanInvoice == (int)EnumCanInvoice.Yes;
                }
            }

            // 查询到账信息
            var receipt = BLL_ContractReceiptRegister.Instance.GetReceiptRegisterById(receiptId);
            if(receipt == null || receipt.ContractReceiptDetails == null || receipt.ContractReceiptDetails.Count == 0)
            {
                throw new ApiException("未找到到账信息");
            }
            var detail = receipt.ContractReceiptDetails[0];

            // 判断是否境内人民币和是否可开票
            EnumIsDomesticRmb isDomesticRmb = EnumIsDomesticRmb.No; // 默认为非境内人民币
            EnumCanInvoice canInvoice = EnumCanInvoice.No; // 默认为不可开票

            bool isRmb = detail.Currency == (int)EnumCurrency.CNY; 

            // 如果不是人民币，则不是境内人民币到账,也不能开票
            if (!isRmb)
            {
                isDomesticRmb = EnumIsDomesticRmb.No; // 非人民币不属于境内人民币
                canInvoice = EnumCanInvoice.No;
            }
            else
            { 
                var PaymentCompanyName = detail.PaymentCompanyName;
                if(string.IsNullOrEmpty(PaymentCompanyName))
                {
                    isDomesticRmb = EnumIsDomesticRmb.No; // 境内人民币到账的公司名称不能为空
                }
                else{
                    // 检查是否包含英文字符
                    bool hasEnglish = System.Text.RegularExpressions.Regex.IsMatch(
                        PaymentCompanyName, 
                        @"[a-zA-Z]");
                    
                    // 检查是否包含繁体字符
                    bool hasTraditionalChinese = ContainsTraditionalChinese(PaymentCompanyName);
                    
                    // 如果不包含英文或繁体，标记为境内人民币
                    if (!hasEnglish && !hasTraditionalChinese)
                    {
                        isDomesticRmb = EnumIsDomesticRmb.Yes; // Yes代表是境内人民币
                    }
                }
                if(isDomesticRmb == EnumIsDomesticRmb.Yes && detail.PaymentMethod == (int)EnumPaymentMethod.Bank)
                {
                    canInvoice = EnumCanInvoice.Yes;
                }
                else{
                    canInvoice = EnumCanInvoice.No;
                }
            }

            // 创建权限记录
            var saveRequest = new VM_InvoiceReceiptAuthority.SaveAuthorityRequest
            {
                ReceiptId = receiptId,
                IsDomesticRmb = isDomesticRmb,
                CanInvoice = canInvoice,
                // 自动判断的权限备注 
                AuthorityRemark = "系统自动判断"
            };
            var authorityId = SaveAuthority(saveRequest,true);
            return  canInvoice == EnumCanInvoice.Yes;
        }

        /// <summary>
        /// 批量自动判断到账开票权限
        /// </summary>
        /// <returns>成功判断的权限记录数量</returns>
        public int BatchAutoJudgeAuthority()
        {
            var receiptIds = DbOpe_crm_contract_receiptregister.Instance
            .GetDataList(r => r.AchievementState == (int)EnumAchievementState.Confirmed && r.Deleted != true)
            .Select(r => r.Id)
            .ToList();
            if (receiptIds == null || receiptIds.Count == 0)
            {
                return 0;
            }

            int successCount = 0;

            // 循环调用单个处理方法
            foreach (var receiptId in receiptIds)
            {
                try
                {
                    // 调用单个自动判断方法
                    AutoJudgeAuthority(receiptId);
                    successCount++;
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理下一条记录
                    Console.WriteLine($"自动判断到账权限失败，ID: {receiptId}, 错误: {ex.Message}");
                }
            }

            return successCount;
        }

        #endregion
    }
} 