﻿using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml;
using System.IO;
using DW = DocumentFormat.OpenXml.Drawing.Wordprocessing;
using PIC = DocumentFormat.OpenXml.Drawing.Pictures;
using A = DocumentFormat.OpenXml.Drawing;
using Text = DocumentFormat.OpenXml.Wordprocessing.Text;
using Spire.Pdf;
using Spire.Pdf.Graphics;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Png;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Runtime.InteropServices;
using System.Linq;
using Spire.Pdf.General.Find;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using System.Text.RegularExpressions;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using System.Collections.Generic;

namespace CRM2_API.BLL.Common
{
    public class DownLoadDocTemplate
    {
        public static Stream DownLoadTemplate(Stream stream, List<TemplateField> item)
        {
            using (Spire.Doc.Document document = new Spire.Doc.Document(stream))
            {

                //替换文本
                foreach (TemplateField i in item)
                {
                    string FieldName = i.FieldName;
                    string FieldValue = i.FieldValue;
                    document.Replace(FieldName, FieldValue, true, true);
                }

                Stream streamWord = new MemoryStream();
                document.SaveToStream(streamWord, Spire.Doc.FileFormat.Docx);
                return streamWord;
            }
        }

        public static Stream DownLoadDocHeaderTemplate(byte[] file, List<TemplateField> items)
        {
            using (MemoryStream stream = new MemoryStream())
            {
                stream.Write(file, 0, file.Length);
                stream.Position = 0;

                using (WordprocessingDocument wordDoc = WordprocessingDocument.Open(stream, true))
                {
                    //var headerPart = wordDoc.MainDocumentPart.HeaderParts.FirstOrDefault();
                    foreach (var headerPart in wordDoc.MainDocumentPart.HeaderParts)
                    {
                        if (headerPart != null)
                        {
                            var header = headerPart.RootElement;
                            foreach (TemplateField item in items)
                            {
                                string FieldName = item.FieldName;
                                string FieldValue = item.FieldValue;
                                foreach (var paragraph in header.Descendants<Paragraph>())
                                {
                                    foreach (var run in paragraph.Elements<Run>())
                                    {
                                        foreach (var text in run.Elements<Text>().ToList())
                                        {
                                            if (text.Text.Contains(FieldName))
                                            {
                                                text.Text = text.Text.Replace(FieldName, FieldValue);
                                            }
                                        }
                                    }
                                }
                            }
                            headerPart.RootElement.Save();
                        }
                    }
                }

                MemoryStream result = new MemoryStream();
                stream.WriteTo(result);
                result.Position = 0;
                return result;
            }
        }

        public static Stream DownLoadZhDocTemplate(byte[] file, List<TemplateField> items, List<TemplateZhTableField> rows, List<TemplateField> tableItems, List<string> contractDescription, string picturePath)
        {
            using (MemoryStream stream = new MemoryStream())
            {
                stream.Write(file, 0, file.Length);
                stream.Position = 0;

                using (WordprocessingDocument wordDoc = WordprocessingDocument.Open(stream, true))
                {
                    //替换模板标签
                    var body = wordDoc.MainDocumentPart!.Document.Body;
                    var paras = body!.Elements<Paragraph>();

                    foreach (TemplateField item in items)
                    {
                        string FieldName = item.FieldName;
                        string FieldValue = item.FieldValue;
                        foreach (var para in paras)
                        {
                            var runs = para.Elements<Run>();
                            string[] copy_text = new string[runs.Count()];
                            int pt = 0;
                            foreach (var run in runs)
                            {
                                var texts = run.Elements<Text>();
                                foreach (var text in texts)
                                {
                                    if (text.Text.Contains(FieldName))
                                    {
                                        if (FieldName == "${AddRowItem}")
                                        {
                                            text.Text = text.Text.Replace(FieldName, "");
                                            foreach (var line in FieldValue.Split('\n'))
                                            {
                                                run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text("") { Space = SpaceProcessingModeValues.Preserve });
                                                if (!string.IsNullOrEmpty(line))
                                                {
                                                    run.AppendChild(new Break());
                                                }
                                            }
                                        }
                                        else
                                        {
                                            if (FieldValue == null)
                                            {
                                                text.Text = text.Text.Replace(FieldName, FieldValue);
                                            }
                                            else if (FieldValue.Split('\n').Count() > 1)
                                            {
                                                int SplitNum = FieldValue.Split('\n').Count();
                                                int i = 1;
                                                foreach (var line in FieldValue.Split('\n'))
                                                {
                                                    if (i == 1)
                                                    {
                                                        text.Text = text.Text.Replace(FieldName, line);
                                                    }
                                                    else
                                                    {
                                                        run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text(line) { Space = SpaceProcessingModeValues.Preserve });
                                                    }
                                                    if (i < SplitNum)
                                                    {
                                                        run.AppendChild(new Break());
                                                    }
                                                    i = i + 1;
                                                }
                                            }
                                            else
                                            {
                                                if (FieldValue == "${DeleteLine}")
                                                {
                                                    if (para.Parent != null)
                                                    {
                                                        para.Remove();
                                                    }
                                                }
                                                else
                                                {
                                                    text.Text = text.Text.Replace(FieldName, FieldValue);
                                                }
                                            }
                                        }
                                    }
                                    copy_text[pt] += text.Text;
                                }
                                pt++;
                            }

                            string str = string.Join("", copy_text);
                            //如果存在目标字段，则将范围内的run合并在一起
                            if (str.Contains(FieldName))
                            {
                                //找到特定字段所在的第一个run的位置
                                int start = 0;
                                while (true)
                                {
                                    string sub_str = "";
                                    for (int i = start; i < runs.Count(); i++)
                                    {
                                        sub_str += copy_text[i];
                                    }
                                    if (!sub_str.Contains(FieldName))
                                    {
                                        start--;
                                        break;
                                    }
                                    else
                                    {
                                        start++;
                                    }
                                }

                                string inner_str = "";//范围内的字符串
                                int end = runs.Count();
                                while (true)
                                {
                                    string sub_str = "";
                                    for (int i = start; i < end; i++)
                                    {
                                        sub_str += copy_text[i];
                                    }
                                    if (!sub_str.Contains(FieldName))
                                    {
                                        end++;
                                        break;
                                    }
                                    else
                                    {
                                        inner_str = sub_str;
                                        end--;
                                    }
                                }

                                int sel_pt = 0;
                                foreach (var run in runs)
                                {
                                    if (sel_pt == start)
                                    {
                                        var texts = run.Elements<Text>();
                                        // 将run里面的文字改为inner_str的内容
                                        int num = 0;
                                        foreach (var mytext in texts)
                                        {
                                            if (num == 0)
                                            {
                                                mytext.Text = inner_str;
                                            }
                                            else
                                            {
                                                mytext.Text = "";
                                            }
                                            num++;
                                        }
                                    }
                                    else if (sel_pt > start && sel_pt < end)
                                    {
                                        var texts = run.Elements<Text>();
                                        foreach (var mytext in texts)
                                        {
                                            mytext.Text = "";
                                        }
                                    }
                                    sel_pt++;
                                }

                                foreach (var run in runs)
                                {
                                    var texts = run.Elements<Text>();
                                    //第一遍先遍历，把run内部的目标字段替换掉，并构建数组记录下所有的run
                                    //创建长度和texts一样的数组，用于记录每个text的长度
                                    foreach (var text in texts)
                                    {
                                        if (text.Text.Contains(FieldName))
                                        {
                                            ////text.Text = text.Text.Replace(FieldName, FieldValue);
                                            //if (FieldName.Contains("（人民币）小写："))
                                            //{
                                            //    //para.Remove();
                                            //    para.RemoveAllChildren();
                                            //    ////text.Text = text.Text.Replace(FieldName, "");
                                            //    int SplitNum = FieldValue.Split('\n').Count();
                                            //    int i = 1;
                                            //    foreach (var line in FieldValue.Split('\n'))
                                            //    {
                                            //        int g = line.IndexOf("（人民币）小写：");
                                            //        string ls = line.Substring(0, 8);
                                            //        string rs = line.Substring(8);
                                            //        //run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text(ls) { Space = SpaceProcessingModeValues.Preserve });
                                            //        //run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text(rs) { Space = SpaceProcessingModeValues.Preserve });

                                            //        Regex reg = new Regex("(?<key1>.*?)拾(?<key2>.*?)万(?<key3>.*?)仟(?<key4>.*?)佰(?<key5>.*?)拾(?<key6>.*?)元整");
                                            //        Match match = reg.Match(rs);
                                            //        string key1 = match.Groups["key1"].Value;
                                            //        string key2 = match.Groups["key2"].Value;
                                            //        string key3 = match.Groups["key3"].Value;
                                            //        string key4 = match.Groups["key4"].Value;
                                            //        string key5 = match.Groups["key5"].Value;
                                            //        string key6 = match.Groups["key6"].Value;
                                            //        if (match.Success)
                                            //        {
                                            //            var properties = new ParagraphProperties();
                                            //            properties.AppendChild(new Indentation { Start = "480" });

                                            //            RunProperties NoSingleRunProperties = new RunProperties();
                                            //            NoSingleRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                            //            NoSingleRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                                            //            Run lsRun = new Run();
                                            //            lsRun.Append(NoSingleRunProperties);
                                            //            lsRun.Append(new Text(ls) { Space = SpaceProcessingModeValues.Preserve });

                                            //            RunProperties SingleRunProperties = new RunProperties();
                                            //            SingleRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                            //            SingleRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体
                                            //            SingleRunProperties.Append(new Underline { Val = UnderlineValues.Single });

                                            //            //Run key1Run = new Run();
                                            //            //key1Run.Append(SingleRunProperties);
                                            //            //key1Run.Append(new Text(key1) { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run key1Run = GetSingleRunProperties(key1);

                                            //            //Run Run1 = new Run();
                                            //            //Run1.Append(NoSingleRunProperties);
                                            //            //Run1.Append(new Text("拾") { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run Run1 = GetNoSingleRunProperties("拾");

                                            //            //Run key2Run = new Run();
                                            //            //key2Run.Append(SingleRunProperties);
                                            //            //key2Run.Append(new Text(key2) { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run key2Run = GetSingleRunProperties(key2);

                                            //            //Run Run2 = new Run();
                                            //            //Run2.Append(NoSingleRunProperties);
                                            //            //Run2.Append(new Text("万") { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run Run2 = GetNoSingleRunProperties("万");

                                            //            //Run key3Run = new Run();
                                            //            //key3Run.Append(SingleRunProperties);
                                            //            //key3Run.Append(new Text(key3) { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run key3Run = GetSingleRunProperties(key3);

                                            //            //Run Run3 = new Run();
                                            //            //Run3.Append(NoSingleRunProperties);
                                            //            //Run3.Append(new Text("仟") { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run Run3 = GetNoSingleRunProperties("仟");

                                            //            //Run key4Run = new Run();
                                            //            //key4Run.Append(SingleRunProperties);
                                            //            //key4Run.Append(new Text(key4) { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run key4Run = GetSingleRunProperties(key4);

                                            //            //Run Run4 = new Run();
                                            //            //Run4.Append(NoSingleRunProperties);
                                            //            //Run4.Append(new Text("佰") { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run Run4 = GetNoSingleRunProperties("佰");

                                            //            //Run key5Run = new Run();
                                            //            //key5Run.Append(SingleRunProperties);
                                            //            //key5Run.Append(new Text(key5) { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run key5Run = GetSingleRunProperties(key5);

                                            //            //Run Run5 = new Run();
                                            //            //Run5.Append(NoSingleRunProperties);
                                            //            //Run5.Append(new Text("拾") { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run Run5 = GetNoSingleRunProperties("拾");

                                            //            //Run key6Run = new Run();
                                            //            //key6Run.Append(SingleRunProperties);
                                            //            //key6Run.Append(new Text(key6) { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run key6Run = GetSingleRunProperties(key6);

                                            //            //Run Run6 = new Run();
                                            //            //Run6.Append(NoSingleRunProperties);
                                            //            //Run6.Append(new Text("元整") { Space = SpaceProcessingModeValues.Preserve });
                                            //            Run Run6 = GetNoSingleRunProperties("元整");

                                            //            para.Append(properties);
                                            //            para.Append(lsRun);
                                            //            para.Append(key1Run);
                                            //            para.Append(Run1);
                                            //            para.Append(key2Run);
                                            //            para.Append(Run2);
                                            //            para.Append(key3Run);
                                            //            para.Append(Run3);
                                            //            para.Append(key4Run);
                                            //            para.Append(Run4);
                                            //            para.Append(key5Run);
                                            //            para.Append(Run5);
                                            //            para.Append(key6Run);
                                            //            para.Append(Run6);
                                            //        }
                                            //        else
                                            //        {
                                            //            var properties = new ParagraphProperties();
                                            //            properties.AppendChild(new Indentation { Start = "480" });

                                            //            RunProperties lsRunProperties = new RunProperties();
                                            //            lsRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                            //            lsRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                                            //            Run lsRun = new Run();
                                            //            lsRun.Append(lsRunProperties);
                                            //            lsRun.Append(new Text(ls) { Space = SpaceProcessingModeValues.Preserve });

                                            //            RunProperties rsRunProperties = new RunProperties();
                                            //            rsRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                            //            rsRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体
                                            //            rsRunProperties.Append(new Underline { Val = UnderlineValues.Single });

                                            //            Run rsRun = new Run();
                                            //            rsRun.Append(rsRunProperties);
                                            //            rsRun.Append(new Text(rs) { Space = SpaceProcessingModeValues.Preserve });

                                            //            para.Append(properties);
                                            //            para.Append(lsRun);
                                            //            para.Append(rsRun);
                                            //        }



                                            //        if (i < SplitNum)
                                            //        {
                                            //            Run Break = new Run();
                                            //            Break.Append(new Break());
                                            //            para.Append(Break);
                                            //        }
                                            //        i = i + 1;
                                            //        //var RunText = new Paragraph();
                                            //        //RunText.Append(properties);
                                            //        //RunText.Append(lsRun);
                                            //        //RunText.Append(rsRun);
                                            //        //para.AppendChild(RunText);
                                            //    }
                                            //}
                                            bool IsTemplate = false;
                                            ReplaceIsStageTemplate(para, FieldName, FieldValue, ref IsTemplate);
                                            if (!IsTemplate)
                                            {
                                                if (FieldValue == null)
                                                {
                                                    text.Text = text.Text.Replace(FieldName, FieldValue);
                                                }
                                                else if (FieldValue.Split('\n').Count() > 1)
                                                {
                                                    int SplitNum = FieldValue.Split('\n').Count();
                                                    int i = 1;
                                                    foreach (var line in FieldValue.Split('\n'))
                                                    {
                                                        if (i == 1)
                                                        {
                                                            text.Text = text.Text.Replace(FieldName, line);
                                                        }
                                                        else
                                                        {
                                                            run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text(line) { Space = SpaceProcessingModeValues.Preserve });
                                                        }
                                                        if (i < SplitNum)
                                                        {
                                                            run.AppendChild(new Break());
                                                        }
                                                        i = i + 1;
                                                    }
                                                }
                                                else
                                                {
                                                    if (FieldValue == "${DeleteLine}")
                                                    {
                                                        if (para.Parent != null)
                                                        {
                                                            para.Remove();
                                                        }
                                                    }
                                                    else
                                                    {
                                                        text.Text = text.Text.Replace(FieldName, FieldValue);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    //获取表格信息
                    List<Table> tables = wordDoc.MainDocumentPart.Document.Body.Elements<Table>().ToList();
                    //替换乙方信息
                    Table topTable = tables[0];

                    //获取行
                    List<TableRow> tableRows = topTable.Elements<TableRow>().ToList();
                    ReplaceTable(tableRows, tableItems);

                    //foreach (TableRow tableRow in tableRows)
                    //{
                    //    //获取列
                    //    List<TableCell> tableCells = tableRow.Elements<TableCell>().ToList();
                    //    foreach (TableCell tableCell in tableCells)
                    //    {
                    //        //获取格中table
                    //        List<Table> tablesSubs = tableCell.Elements<Table>().ToList();
                    //        foreach (Table tablesSub in tablesSubs)
                    //        {
                    //            List<TableRow> tablesSubRows = tablesSub.Elements<TableRow>().ToList();
                    //            foreach (TableRow tablesSubRow in tablesSubRows)
                    //            {
                    //                List<TableCell> tableSubCells = tablesSubRow.Elements<TableCell>().ToList();
                    //                foreach (TableCell tableSubCell in tableSubCells)
                    //                {
                    //                    List<Paragraph> tablesSubPs = tableSubCell.Elements<Paragraph>().ToList();

                    //                    foreach (Paragraph tableSubParagraph in tablesSubPs)
                    //                    {
                    //                        Run tableSubR = tableSubParagraph.Elements<Run>().First();
                    //                        Text tableSubT = tableSubR.Elements<Text>().First();
                    //                        foreach (TemplateField item in tableItems)
                    //                        {
                    //                            string FieldName = item.FieldName;
                    //                            string FieldValue = item.FieldValue;
                    //                            if (tableSubT.Text.Contains(FieldName))
                    //                            {
                    //                                tableSubT.Text = tableSubT.Text.Replace(FieldName, FieldValue);
                    //                            }
                    //                        }
                    //                    }
                    //                }
                    //            }

                    //        }

                    //        //获取格中段落
                    //        List<Paragraph> tablesPs = tableCell.Elements<Paragraph>().ToList();
                    //        foreach (Paragraph tableParagraph in tablesPs)
                    //        {
                    //            List<Run> tableRs = tableParagraph.Elements<Run>().ToList();
                    //            foreach (Run run in tableRs)
                    //            {
                    //                List<Text> tableTs = run.Elements<Text>().ToList();
                    //                foreach (Text tableT in tableTs)
                    //                {
                    //                    foreach (TemplateField item in tableItems)
                    //                    {
                    //                        string FieldName = item.FieldName;
                    //                        string FieldValue = item.FieldValue;
                    //                        if (tableT.Text.Contains(FieldName))
                    //                        {
                    //                            tableT.Text = tableT.Text.Replace(FieldName, FieldValue);
                    //                        }
                    //                    }
                    //                }

                    //            }

                    //        }
                    //    }

                    //}

                    //动态添加产品信息
                    Table table = tables[1];

                    TableRow row_d = table.Elements<TableRow>().ElementAt(1);
                    TableCell cell_d = row_d.Elements<TableCell>().ElementAt(1);
                    Paragraph p_d = cell_d.Elements<Paragraph>().First();
                    Run r = p_d.Elements<Run>().First();
                    //Text t = r.Elements<Text>().First();
                    //t.Text = contractDescription;
                    Text t = r.Elements<Text>().First();
                    t.Text = "";
                    bool first = true;
                    foreach (string line in contractDescription)
                    {
                        if (!first)
                        {
                            r.Append(new Break());
                        }
                        first = false;
                        Text txt = new Text();
                        txt.Text = line;
                        r.Append(txt);
                    }

                    // 添加数据
                    foreach (TemplateZhTableField tableField in rows)
                    {
                        TableRow row = new TableRow();
                        int CellContentLength = tableField.CellContent == null ? 0 : tableField.CellContent.Length;
                        int CellBriefIntroductionLength = tableField.CellBriefIntroduction == null ? 0 : tableField.CellBriefIntroduction.Length;
                        int CellLength = CellContentLength > CellBriefIntroductionLength ? CellContentLength : CellBriefIntroductionLength;
                        //int rownum = (tableField.CellContent.Length / 55) + ((tableField.CellContent.Length % 55) > 0 ? 1 : 0); 
                        int rownum = (CellLength / 10) + ((CellLength % 10) > 0 ? 1 : 0);
                        UInt32Value HeightVal = UInt32Value.ToUInt32((uint)((rownum) * 310)); //(tableField.CellContent.Length * 4);
                        if (HeightVal < 600)
                        {
                            HeightVal = 600;
                        }

                        TableRowProperties tabRowProps = row.AppendChild(new TableRowProperties(new TableRowHeight { Val = HeightVal, HeightType = HeightRuleValues.Exact }));

                        TableCell CellTitel = new TableCell();
                        //垂直居中显示
                        var CellTitelTcProperties = new TableCellProperties();
                        CellTitelTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellTitelPproperties = new ParagraphProperties();
                        CellTitelPproperties.AppendChild(new Justification { Val = JustificationValues.Center });
                        //设置字体样式
                        RunProperties CellTitelRunProperties = new RunProperties();//属性
                        CellTitelRunProperties.Append(new Bold());
                        CellTitelRunProperties.Append(new BoldComplexScript());
                        CellTitelRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellTitelRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellTitelRun = new Run();
                        CellTitelRun.Append(CellTitelRunProperties);
                        CellTitelRun.Append(new Text(tableField.CellTitel));
                        var CellTitelRunText = new Paragraph();
                        CellTitelRunText.ParagraphProperties = CellTitelPproperties;
                        CellTitelRunText.Append(CellTitelRun);
                        //CellTitelRunText.Append(CellTitelPproperties);
                        CellTitel.AppendChild(CellTitelTcProperties);
                        CellTitel.AppendChild(CellTitelRunText);
                        //CellTitel.Append(new Paragraph(new Run(new Text(tableField.CellTitel))));
                        row.Append(CellTitel);

                        TableCell CellBriefIntroduction = new TableCell();
                        //垂直居中显示
                        var CellBriefIntroductionTcProperties = new TableCellProperties();
                        CellBriefIntroductionTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellBriefIntroductionPproperties = new ParagraphProperties();
                        CellBriefIntroductionPproperties.AppendChild(new Justification { Val = JustificationValues.Center });
                        //设置字体样式
                        RunProperties CellBriefIntroductionRunProperties = new RunProperties();//属性
                        CellBriefIntroductionRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellBriefIntroductionRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellBriefIntroductionRun = new Run();
                        CellBriefIntroductionRun.Append(CellBriefIntroductionRunProperties);
                        CellBriefIntroductionRun.Append(new Text(tableField.CellBriefIntroduction));
                        var CellBriefIntroductionRunText = new Paragraph();
                        CellBriefIntroductionRunText.ParagraphProperties = CellBriefIntroductionPproperties;
                        CellBriefIntroductionRunText.Append(CellBriefIntroductionRun);
                        //CellBriefIntroductionRunText.Append(CellBriefIntroductionPproperties);
                        CellBriefIntroduction.AppendChild(CellBriefIntroductionTcProperties);
                        CellBriefIntroduction.AppendChild(CellBriefIntroductionRunText);

                        //CellBriefIntroduction.Append(new Paragraph(new Run(new Text(tableField.CellBriefIntroduction))));
                        row.Append(CellBriefIntroduction);

                        TableCell CellContent = new TableCell();
                        //垂直居中显示
                        var CellContentTcProperties = new TableCellProperties();
                        CellContentTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellContentPproperties = new ParagraphProperties();
                        CellContentPproperties.AppendChild(new Justification { Val = JustificationValues.Left });
                        //设置字体样式
                        RunProperties CellContentRunProperties = new RunProperties();//属性
                        CellContentRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellContentRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellContentRun = new Run();
                        CellContentRun.Append(CellContentRunProperties);
                        CellContentRun.Append(new Text(tableField.CellContent));
                        var CellContentRunText = new Paragraph();
                        CellContentRunText.ParagraphProperties = CellContentPproperties;
                        CellContentRunText.Append(CellContentRun);
                        //CellContentRunText.Append(CellContentPproperties);
                        CellContent.AppendChild(CellContentTcProperties);
                        CellContent.AppendChild(CellContentRunText);

                        //CellContent.Append(new Paragraph(new Run(new Text(tableField.CellContent))));
                        row.Append(CellContent);

                        TableCell CellType = new TableCell();
                        //垂直居中显示
                        var CellTypeTcProperties = new TableCellProperties();
                        CellTypeTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellTypePproperties = new ParagraphProperties();
                        CellTypePproperties.AppendChild(new Justification { Val = JustificationValues.Center });
                        //设置字体样式
                        RunProperties CellTypeRunProperties = new RunProperties();//属性
                        CellTypeRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellTypeRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellTypeRun = new Run();
                        CellTypeRun.Append(CellTypeRunProperties);
                        CellTypeRun.Append(new Text(tableField.CellType));
                        var CellTypeRunText = new Paragraph();
                        CellTypeRunText.Append(CellTypeRun);
                        CellTypeRunText.Append(CellTypePproperties);
                        CellType.AppendChild(CellTypeTcProperties);
                        CellType.AppendChild(CellTypeRunText);

                        //CellType.Append(new Paragraph(new Run(new Text(tableField.CellType))));
                        row.Append(CellType);

                        table.Elements<TableRow>().First().InsertAfterSelf(row);
                    }

                    ////string picturePath = "F:\\workGit\\crm2\\crm2_api\\CRM2_API\\Template\\图章.png";
                    //string picType = "png";
                    //ImagePartType imagePartType;
                    //ImagePart imagePart = null;
                    //// 通过后缀名判断图片类型, true 表示忽视大小写
                    //if (Enum.TryParse<ImagePartType>(picType, true, out imagePartType))
                    //{
                    //    imagePart = wordDoc.MainDocumentPart.AddImagePart(imagePartType);
                    //}
                    //imagePart.FeedData(File.Open(picturePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite)); // 读取图片二进制流

                    //MainDocumentPart mainPart = wordDoc.MainDocumentPart;
                    //var bookmarks = from bm in mainPart.Document.Body.Descendants<BookmarkStart>()
                    //                where bm.Name == "stamp"
                    //                select bm;
                    //var bookmark = bookmarks.SingleOrDefault();
                    //if (bookmark != null)
                    //{
                    //    OpenXmlElement elem = bookmark.NextSibling();
                    //    while (elem != null && !(elem is BookmarkEnd))
                    //    {
                    //        OpenXmlElement nextElem = elem.NextSibling();
                    //        elem.Remove();
                    //        elem = nextElem;
                    //    }
                    //    var parent = bookmark.Parent;
                    //    Run run = new Run(new RunProperties());
                    //    Run stamp = CreateImageParagraph2(wordDoc.MainDocumentPart.GetIdOfPart(imagePart), Path.GetFileNameWithoutExtension(picturePath), 1, 230, 230, 6, false);
                    //    parent.InsertAfter<Run>(new Run(new Run(stamp)), bookmark);
                    //}

                    wordDoc.Save();
                }

                MemoryStream result = new MemoryStream();
                stream.WriteTo(result);
                result.Position = 0;
                return result;
            }
        }

        private static Run GetRunProperties(string text, RunProperties singleRunProperties)
        {
            Run Run = new Run();
            Run.Append(singleRunProperties);
            Run.Append(new Text(text) { Space = SpaceProcessingModeValues.Preserve });
            return Run;
        }

        private static Run GetSingleRunProperties(string text)
        {
            RunProperties SingleRunProperties = new RunProperties();
            SingleRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
            SingleRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体
            SingleRunProperties.Append(new Underline { Val = UnderlineValues.Single });

            Run key1Run = new Run();
            key1Run.Append(SingleRunProperties);
            key1Run.Append(new Text(text) { Space = SpaceProcessingModeValues.Preserve });
            return key1Run;
        }

        private static Run GetNoSingleRunProperties(string text)
        {
            RunProperties NoSingleRunProperties = new RunProperties();
            NoSingleRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
            NoSingleRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

            Run lsRun = new Run();
            lsRun.Append(NoSingleRunProperties);
            lsRun.Append(new Text(text) { Space = SpaceProcessingModeValues.Preserve });
            return lsRun;
        }

        private static List<string> GetRegexServiceChargeEn()
        {
            List<string> result = new List<string>();
            result.Add("1. Total Service Fee: (?<key1>.*?)USD.");
            result.Add("1.服务费总额：(?<key1>.*?)美元。");
            result.Add("1. Total Service Fee: (?<key1>.*?)EUR.");
            result.Add("1.服务费总额：(?<key1>.*?)欧元。");
            result.Add("1. Total Service Fee: (?<key1>.*?)CNY.");
            result.Add("1.服务费总额：(?<key1>.*?)人民币。");
            return result;
        }

        private static List<string> GetBoldServiceChargeEn()
        {
            List<string> result = new List<string>();
            result.Add("2. Time Limit for Payment:");
            result.Add("2. 付款期限：");
            result.Add("5. Delivery Of Service");
            result.Add("5.服务交付");
            return result;
        }

        private static void ReplaceIsStageTemplate(Paragraph para, string FieldName, string FieldValue, ref bool IsTemplate)
        {
            List<Db_sys_isstage_template> templateList = DbOpe_sys_isstage_template.Instance.GetDataAllList().ToList();
            foreach (Db_sys_isstage_template template in templateList)
            {
                if (FieldName.Contains(template.Content))
                {
                    if (template.Type == 1)
                    {
                        List<Db_sys_isstage_template_item> templateItemList = DbOpe_sys_isstage_template_item.Instance.GetDataList(r => r.IsStageTemplateId == template.Id);
                        para.RemoveAllChildren();
                        int SplitNum = FieldValue.Split('\n').Count();
                        int i = 1;
                        foreach (var line in FieldValue.Split('\n'))
                        {
                            int lastindex = template.Content.Length;
                            string ls = line.Substring(0, lastindex);
                            string rs = line.Substring(lastindex);
                            //int lst = line.IndexOf(template.Content);
                            //string ls = line.Substring(lst, template.Content.Length);
                            //string rs = line.Substring(template.Content.Length);

                            bool sign = true;
                            foreach (Db_sys_isstage_template_item templateItem in templateItemList)
                            {
                                if (templateItem.Type == 1)
                                {
                                    //Regex reg = new Regex("(?<key1>.*?)拾(?<key2>.*?)万(?<key3>.*?)仟(?<key4>.*?)佰(?<key5>.*?)拾(?<key6>.*?)元整");
                                    Regex reg = new Regex(templateItem.Content);
                                    Match match = reg.Match(rs);
                                    if (match.Success)
                                    {
                                        sign = false;
                                        var properties = new ParagraphProperties();
                                        properties.AppendChild(new Indentation { Start = "480" });

                                        RunProperties NoSingleRunProperties = new RunProperties();
                                        NoSingleRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                        NoSingleRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                                        Run lsRun = new Run();
                                        lsRun.Append(NoSingleRunProperties);
                                        lsRun.Append(new Text(ls) { Space = SpaceProcessingModeValues.Preserve });

                                        para.Append(properties);
                                        para.Append(lsRun);

                                        //RunProperties SingleRunProperties = new RunProperties();
                                        //SingleRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                        //SingleRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体
                                        //SingleRunProperties.Append(new Underline { Val = UnderlineValues.Single });
                                        List<Db_sys_isstage_template_item_key> templateItemKeyList = DbOpe_sys_isstage_template_item_key.Instance.GetDataList(r => r.IsStageTemplateItemId == templateItem.Id).OrderBy(r => r.Key).ToList();

                                        foreach (Db_sys_isstage_template_item_key templateItemKey in templateItemKeyList)
                                        {
                                            string key = match.Groups[templateItemKey.Key].Value;
                                            Run keyRun = GetSingleRunProperties(key);
                                            Run Run = GetNoSingleRunProperties(templateItemKey.Item);
                                            para.Append(keyRun);
                                            para.Append(Run);
                                        }
                                        //string key1 = match.Groups["key1"].Value;
                                        //string key2 = match.Groups["key2"].Value;
                                        //string key3 = match.Groups["key3"].Value;
                                        //string key4 = match.Groups["key4"].Value;
                                        //string key5 = match.Groups["key5"].Value;
                                        //string key6 = match.Groups["key6"].Value;

                                        //Run key1Run = GetSingleRunProperties(key1);
                                        //Run Run1 = GetNoSingleRunProperties("拾");
                                        //Run key2Run = GetSingleRunProperties(key2);
                                        //Run Run2 = GetNoSingleRunProperties("万");
                                        //Run key3Run = GetSingleRunProperties(key3);
                                        //Run Run3 = GetNoSingleRunProperties("仟");
                                        //Run key4Run = GetSingleRunProperties(key4);
                                        //Run Run4 = GetNoSingleRunProperties("佰");
                                        //Run key5Run = GetSingleRunProperties(key5);
                                        //Run Run5 = GetNoSingleRunProperties("拾");
                                        //Run key6Run = GetSingleRunProperties(key6);
                                        //Run Run6 = GetNoSingleRunProperties("元整");

                                        //para.Append(key1Run);
                                        //para.Append(Run1);
                                        //para.Append(key2Run);
                                        //para.Append(Run2);
                                        //para.Append(key3Run);
                                        //para.Append(Run3);
                                        //para.Append(key4Run);
                                        //para.Append(Run4);
                                        //para.Append(key5Run);
                                        //para.Append(Run5);
                                        //para.Append(key6Run);
                                        //para.Append(Run6);
                                    }
                                }
                            }
                            if (sign)
                            {
                                var properties = new ParagraphProperties();
                                properties.AppendChild(new Indentation { Start = "480" });

                                RunProperties lsRunProperties = new RunProperties();
                                lsRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                lsRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                                Run lsRun = new Run();
                                lsRun.Append(lsRunProperties);
                                lsRun.Append(new Text(ls) { Space = SpaceProcessingModeValues.Preserve });

                                RunProperties rsRunProperties = new RunProperties();
                                rsRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                rsRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体
                                rsRunProperties.Append(new Underline { Val = UnderlineValues.Single });

                                Run rsRun = new Run();
                                rsRun.Append(rsRunProperties);
                                rsRun.Append(new Text(rs) { Space = SpaceProcessingModeValues.Preserve });

                                para.Append(properties);
                                para.Append(lsRun);
                                para.Append(rsRun);
                            }
                            if (i < SplitNum)
                            {
                                Run Break = new Run();
                                Break.Append(new Break());
                                para.Append(Break);
                            }
                            i = i + 1;
                        }
                    }
                    else if (template.Type == 2)
                    {
                        List<Db_sys_isstage_template_item> templateItemList = DbOpe_sys_isstage_template_item.Instance.GetDataList(r => r.IsStageTemplateId == template.Id);
                        para.RemoveAllChildren();
                        int SplitNum = FieldValue.Split('\n').Count();
                        int i = 1;
                        foreach (var line in FieldValue.Split('\n'))
                        {
                            foreach (Db_sys_isstage_template_item templateItem in templateItemList)
                            {
                                if (templateItem.Type == 1)
                                {
                                    Regex reg = new Regex(templateItem.Content);
                                    Match match = reg.Match(line);
                                    if (match.Success)
                                    {
                                        List<Db_sys_isstage_template_item_key> templateItemKeyList = DbOpe_sys_isstage_template_item_key.Instance.GetDataList(r => r.IsStageTemplateItemId == templateItem.Id).OrderBy(r => r.Key).ToList();
                                        foreach (Db_sys_isstage_template_item_key templateItemKey in templateItemKeyList)
                                        {
                                            string key = match.Groups[templateItemKey.Key].Value;
                                            string[] content = line.Split(key);
                                            string ls = content[0];
                                            string rs = content[1];

                                            var properties = new ParagraphProperties();
                                            properties.AppendChild(new Indentation { Start = "380" });

                                            RunProperties NoSingleRunProperties = new RunProperties();
                                            NoSingleRunProperties.Append(new FontSize() { Val = "20" });//设置字体大小
                                            NoSingleRunProperties.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体
                                            NoSingleRunProperties.Append(new Bold());
                                            NoSingleRunProperties.Append(new BoldComplexScript());

                                            Run lsRun = new Run();
                                            lsRun.Append(NoSingleRunProperties);
                                            lsRun.Append(new Text(ls) { Space = SpaceProcessingModeValues.Preserve });

                                            RunProperties key1RunProperties = new RunProperties();
                                            key1RunProperties.Append(new FontSize() { Val = "20" });//设置字体大小
                                            key1RunProperties.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体
                                            key1RunProperties.Append(new Underline { Val = UnderlineValues.Single });

                                            Run key1Run = GetRunProperties(key, key1RunProperties);

                                            RunProperties NoSingleRunProperties1 = new RunProperties();
                                            NoSingleRunProperties1.Append(new FontSize() { Val = "20" });//设置字体大小
                                            NoSingleRunProperties1.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体

                                            Run Run1 = GetRunProperties(rs, NoSingleRunProperties1);

                                            para.Append(properties);
                                            para.Append(lsRun);
                                            para.Append(key1Run);
                                            para.Append(Run1);
                                        }
                                    }
                                }
                            }
                            if (i < SplitNum)
                            {
                                Run Break = new Run();
                                Break.Append(new Break());
                                para.Append(Break);
                            }
                            i = i + 1;
                        }
                    }
                    else if (template.Type == 3)
                    {
                        List<Db_sys_isstage_template_item> templateItemList = DbOpe_sys_isstage_template_item.Instance.GetDataList(r => r.IsStageTemplateId == template.Id);
                        para.RemoveAllChildren();
                        int SplitNum = FieldValue.Split('\n').Count();
                        int i = 1;
                        foreach (var line in FieldValue.Split('\n'))
                        {
                            bool sign = true;
                            foreach (Db_sys_isstage_template_item templateItem in templateItemList)
                            {
                                if (templateItem.Type == 2)
                                {
                                    if (line.Contains(templateItem.Content))
                                    {
                                        int lastindex = templateItem.Content.Length;
                                        string ls = line.Substring(0, lastindex);
                                        string rs = line.Substring(lastindex);

                                        var properties = new ParagraphProperties();
                                        properties.AppendChild(new Indentation { Start = "380" });

                                        RunProperties NoSingleRunProperties = new RunProperties();
                                        NoSingleRunProperties.Append(new FontSize() { Val = "20" });//设置字体大小
                                        NoSingleRunProperties.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体
                                        NoSingleRunProperties.Append(new Bold());
                                        NoSingleRunProperties.Append(new BoldComplexScript());

                                        Run lsRun = new Run();
                                        lsRun.Append(NoSingleRunProperties);
                                        lsRun.Append(new Text(ls) { Space = SpaceProcessingModeValues.Preserve });

                                        RunProperties NoSingleRunProperties1 = new RunProperties();
                                        NoSingleRunProperties1.Append(new FontSize() { Val = "20" });//设置字体大小
                                        NoSingleRunProperties1.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体

                                        Run rsRun = GetRunProperties(rs, NoSingleRunProperties1);

                                        para.Append(properties);
                                        para.Append(lsRun);
                                        para.Append(rsRun);
                                        sign = false;
                                    }
                                }
                            }
                            if (sign)
                            {
                                var properties = new ParagraphProperties();
                                properties.AppendChild(new Indentation { Start = "380" });

                                RunProperties NoSingleRunProperties1 = new RunProperties();
                                NoSingleRunProperties1.Append(new FontSize() { Val = "20" });//设置字体大小
                                NoSingleRunProperties1.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体

                                Run lineRun = GetRunProperties(line, NoSingleRunProperties1);

                                para.Append(properties);
                                para.Append(lineRun);
                            }
                            if (i < SplitNum)
                            {
                                Run Break = new Run();
                                Break.Append(new Break());
                                para.Append(Break);
                            }
                            i = i + 1;
                        }
                    }
                    IsTemplate = true;
                }
            }
        }

        public static Stream DownLoadEnDocTemplate(byte[] file, List<TemplateField> items, List<TemplateEnTableField> rows, List<TemplateField> tableItems, List<string> contractDescription, string picturePath)
        {
            using (MemoryStream stream = new MemoryStream())
            {
                stream.Write(file, 0, file.Length);
                stream.Position = 0;

                using (WordprocessingDocument wordDoc = WordprocessingDocument.Open(stream, true))
                {
                    //替换模板标签
                    var body = wordDoc.MainDocumentPart!.Document.Body;
                    var paras = body!.Elements<Paragraph>();

                    foreach (TemplateField item in items)
                    {
                        string FieldName = item.FieldName;
                        string FieldValue = item.FieldValue;
                        foreach (var para in paras)
                        {
                            var runs = para.Elements<Run>();
                            string[] copy_text = new string[runs.Count()];
                            int pt = 0;
                            foreach (var run in runs)
                            {
                                var texts = run.Elements<Text>();
                                foreach (var text in texts)
                                {
                                    //if (text.Text.Contains(FieldName))
                                    //{
                                    //    text.Text = text.Text.Replace(FieldName, FieldValue);
                                    //}
                                    //copy_text[pt] += text.Text;
                                    if (text.Text.Contains(FieldName))
                                    {
                                        if (FieldName == "${AddRowItem}")
                                        {
                                            text.Text = text.Text.Replace(FieldName, "");
                                            foreach (var line in FieldValue.Split('\n'))
                                            {
                                                run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text("") { Space = SpaceProcessingModeValues.Preserve });
                                                if (!string.IsNullOrEmpty(line))
                                                {
                                                    run.AppendChild(new Break());
                                                }
                                            }
                                        }
                                        else
                                        {
                                            if (FieldValue == null)
                                            {
                                                text.Text = text.Text.Replace(FieldName, FieldValue);
                                            }
                                            else if (FieldValue.Split('\n').Count() > 1)
                                            {
                                                int SplitNum = FieldValue.Split('\n').Count();
                                                int i = 1;
                                                foreach (var line in FieldValue.Split('\n'))
                                                {
                                                    if (i == 1)
                                                    {
                                                        text.Text = text.Text.Replace(FieldName, line);
                                                    }
                                                    else
                                                    {
                                                        run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text(line) { Space = SpaceProcessingModeValues.Preserve });
                                                    }
                                                    if (i < SplitNum)
                                                    {
                                                        run.AppendChild(new Break());
                                                    }
                                                    i = i + 1;
                                                }
                                            }
                                            else
                                            {
                                                if (FieldValue == "${DeleteLine}")
                                                {
                                                    if (para.Parent != null)
                                                    {
                                                        para.Remove();
                                                    }
                                                }
                                                else
                                                {
                                                    text.Text = text.Text.Replace(FieldName, FieldValue);
                                                }
                                            }
                                        }
                                    }
                                    copy_text[pt] += text.Text;
                                }
                                pt++;
                            }

                            string str = string.Join("", copy_text);
                            //如果存在目标字段，则将范围内的run合并在一起
                            if (str.Contains(FieldName))
                            {
                                //找到特定字段所在的第一个run的位置
                                int start = 0;
                                while (true)
                                {
                                    string sub_str = "";
                                    for (int i = start; i < runs.Count(); i++)
                                    {
                                        sub_str += copy_text[i];
                                    }
                                    if (!sub_str.Contains(FieldName))
                                    {
                                        start--;
                                        break;
                                    }
                                    else
                                    {
                                        start++;
                                    }
                                }

                                string inner_str = "";//范围内的字符串
                                int end = runs.Count();
                                while (true)
                                {
                                    string sub_str = "";
                                    for (int i = start; i < end; i++)
                                    {
                                        sub_str += copy_text[i];
                                    }
                                    if (!sub_str.Contains(FieldName))
                                    {
                                        end++;
                                        break;
                                    }
                                    else
                                    {
                                        inner_str = sub_str;
                                        end--;
                                    }
                                }

                                int sel_pt = 0;
                                foreach (var run in runs)
                                {
                                    if (sel_pt == start)
                                    {
                                        var texts = run.Elements<Text>();
                                        // 将run里面的文字改为inner_str的内容
                                        int num = 0;
                                        foreach (var mytext in texts)
                                        {
                                            if (num == 0)
                                            {
                                                mytext.Text = inner_str;
                                            }
                                            else
                                            {
                                                mytext.Text = "";
                                            }
                                            num++;
                                        }
                                    }
                                    else if (sel_pt > start && sel_pt < end)
                                    {
                                        var texts = run.Elements<Text>();
                                        foreach (var mytext in texts)
                                        {
                                            mytext.Text = "";
                                        }
                                    }
                                    sel_pt++;
                                }

                                foreach (var run in runs)
                                {
                                    var texts = run.Elements<Text>();
                                    //第一遍先遍历，把run内部的目标字段替换掉，并构建数组记录下所有的run
                                    //创建长度和texts一样的数组，用于记录每个text的长度
                                    foreach (var text in texts)
                                    {
                                        if (text.Text.Contains(FieldName))
                                        {
                                            ////text.Text = text.Text.Replace(FieldName, FieldValue);
                                            //if (FieldName.Contains("Total Service Fee"))
                                            //{
                                            //    //para.Remove();
                                            //    para.RemoveAllChildren();
                                            //    ////text.Text = text.Text.Replace(FieldName, "");
                                            //    int SplitNum = FieldValue.Split('\n').Count();
                                            //    int i = 1;
                                            //    foreach (var line in FieldValue.Split('\n'))
                                            //    {
                                            //        List<string> RegexServiceChargeEns = GetRegexServiceChargeEn();
                                            //        foreach (string RegexItem in RegexServiceChargeEns)
                                            //        {
                                            //            Regex reg = new Regex(RegexItem);
                                            //            Match match = reg.Match(line);
                                            //            string key1 = match.Groups["key1"].Value;

                                            //            if (match.Success)
                                            //            {
                                            //                string[] content = line.Split(key1);
                                            //                string ls = content[0];
                                            //                string rs = content[1];

                                            //                var properties = new ParagraphProperties();
                                            //                properties.AppendChild(new Indentation { Start = "380" });

                                            //                RunProperties NoSingleRunProperties = new RunProperties();
                                            //                NoSingleRunProperties.Append(new FontSize() { Val = "20" });//设置字体大小
                                            //                NoSingleRunProperties.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体
                                            //                NoSingleRunProperties.Append(new Bold());
                                            //                NoSingleRunProperties.Append(new BoldComplexScript());

                                            //                Run lsRun = new Run();
                                            //                lsRun.Append(NoSingleRunProperties);
                                            //                lsRun.Append(new Text(ls) { Space = SpaceProcessingModeValues.Preserve });

                                            //                RunProperties key1RunProperties = new RunProperties();
                                            //                key1RunProperties.Append(new FontSize() { Val = "20" });//设置字体大小
                                            //                key1RunProperties.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体
                                            //                key1RunProperties.Append(new Underline { Val = UnderlineValues.Single });

                                            //                Run key1Run = GetRunProperties(key1, key1RunProperties);

                                            //                RunProperties NoSingleRunProperties1 = new RunProperties();
                                            //                NoSingleRunProperties1.Append(new FontSize() { Val = "20" });//设置字体大小
                                            //                NoSingleRunProperties1.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体

                                            //                Run Run1 = GetRunProperties(rs, NoSingleRunProperties1);

                                            //                para.Append(properties);
                                            //                para.Append(lsRun);
                                            //                para.Append(key1Run);
                                            //                para.Append(Run1);
                                            //            }
                                            //        }




                                            //        //else
                                            //        //{
                                            //        //    var properties = new ParagraphProperties();
                                            //        //    properties.AppendChild(new Indentation { Start = "480" });

                                            //        //    RunProperties lsRunProperties = new RunProperties();
                                            //        //    lsRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                            //        //    lsRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                                            //        //    Run lsRun = new Run();
                                            //        //    lsRun.Append(lsRunProperties);
                                            //        //    lsRun.Append(new Text(ls) { Space = SpaceProcessingModeValues.Preserve });

                                            //        //    RunProperties rsRunProperties = new RunProperties();
                                            //        //    rsRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                                            //        //    rsRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体
                                            //        //    rsRunProperties.Append(new Underline { Val = UnderlineValues.Single });

                                            //        //    Run rsRun = new Run();
                                            //        //    rsRun.Append(rsRunProperties);
                                            //        //    rsRun.Append(new Text(rs) { Space = SpaceProcessingModeValues.Preserve });

                                            //        //    para.Append(properties);
                                            //        //    para.Append(lsRun);
                                            //        //    para.Append(rsRun);
                                            //        //}



                                            //        if (i < SplitNum)
                                            //        {
                                            //            Run Break = new Run();
                                            //            Break.Append(new Break());
                                            //            para.Append(Break);
                                            //        }
                                            //        i = i + 1;
                                            //        //var RunText = new Paragraph();
                                            //        //RunText.Append(properties);
                                            //        //RunText.Append(lsRun);
                                            //        //RunText.Append(rsRun);
                                            //        //para.AppendChild(RunText);
                                            //    }
                                            //}


                                            //if (FieldName.Contains("Time Limit for Payment") || FieldName.Contains("Delivery Of Service"))
                                            //{
                                            //    //para.Remove();
                                            //    para.RemoveAllChildren();
                                            //    ////text.Text = text.Text.Replace(FieldName, "");
                                            //    int SplitNum = FieldValue.Split('\n').Count();
                                            //    int i = 1;
                                            //    foreach (var line in FieldValue.Split('\n'))
                                            //    {
                                            //        bool sign = true;
                                            //        List<string> BoldServiceChargeEns = GetBoldServiceChargeEn();
                                            //        foreach (string BoldItem in BoldServiceChargeEns)
                                            //        {
                                            //            if (line.Contains(BoldItem))
                                            //            {
                                            //                int lastindex = BoldItem.Length;
                                            //                string ls = line.Substring(0, lastindex);
                                            //                string rs = line.Substring(lastindex);

                                            //                var properties = new ParagraphProperties();
                                            //                properties.AppendChild(new Indentation { Start = "380" });

                                            //                RunProperties NoSingleRunProperties = new RunProperties();
                                            //                NoSingleRunProperties.Append(new FontSize() { Val = "20" });//设置字体大小
                                            //                NoSingleRunProperties.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体
                                            //                NoSingleRunProperties.Append(new Bold());
                                            //                NoSingleRunProperties.Append(new BoldComplexScript());

                                            //                Run lsRun = new Run();
                                            //                lsRun.Append(NoSingleRunProperties);
                                            //                lsRun.Append(new Text(ls) { Space = SpaceProcessingModeValues.Preserve });

                                            //                RunProperties NoSingleRunProperties1 = new RunProperties();
                                            //                NoSingleRunProperties1.Append(new FontSize() { Val = "20" });//设置字体大小
                                            //                NoSingleRunProperties1.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体

                                            //                Run rsRun = GetRunProperties(rs, NoSingleRunProperties1);

                                            //                para.Append(properties);
                                            //                para.Append(lsRun);
                                            //                para.Append(rsRun);
                                            //                sign = false;
                                            //            }
                                            //        }
                                            //        if (sign)
                                            //        {
                                            //            var properties = new ParagraphProperties();
                                            //            properties.AppendChild(new Indentation { Start = "380" });

                                            //            RunProperties NoSingleRunProperties1 = new RunProperties();
                                            //            NoSingleRunProperties1.Append(new FontSize() { Val = "20" });//设置字体大小
                                            //            NoSingleRunProperties1.Append(new RunFonts() { Ascii = "Arial", HighAnsi = "Arial", EastAsia = "宋体", });//设置字体

                                            //            Run lineRun = GetRunProperties(line, NoSingleRunProperties1);

                                            //            para.Append(properties);
                                            //            para.Append(lineRun);
                                            //        }

                                            //        if (i < SplitNum)
                                            //        {
                                            //            Run Break = new Run();
                                            //            Break.Append(new Break());
                                            //            para.Append(Break);
                                            //        }
                                            //        i = i + 1;

                                            //    }
                                            //}

                                            bool IsTemplate = false;
                                            ReplaceIsStageTemplate(para, FieldName, FieldValue, ref IsTemplate);
                                            if (!IsTemplate)
                                            {
                                                if (FieldValue == null)
                                                {
                                                    text.Text = text.Text.Replace(FieldName, FieldValue);
                                                }
                                                else if (FieldValue.Split('\n').Count() > 1)
                                                {
                                                    int SplitNum = FieldValue.Split('\n').Count();
                                                    int i = 1;
                                                    foreach (var line in FieldValue.Split('\n'))
                                                    {
                                                        if (i == 1)
                                                        {
                                                            text.Text = text.Text.Replace(FieldName, line);
                                                        }
                                                        else
                                                        {
                                                            run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text(line) { Space = SpaceProcessingModeValues.Preserve });
                                                        }
                                                        if (i < SplitNum)
                                                        {
                                                            run.AppendChild(new Break());
                                                        }
                                                        i = i + 1;
                                                    }
                                                }
                                                else
                                                {
                                                    if (FieldValue == "${DeleteLine}")
                                                    {
                                                        if (para.Parent != null)
                                                        {
                                                            para.Remove();
                                                        }
                                                    }
                                                    else
                                                    {
                                                        text.Text = text.Text.Replace(FieldName, FieldValue);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    //获取表格信息
                    List<Table> tables = wordDoc.MainDocumentPart.Document.Body.Elements<Table>().ToList();
                    //替换乙方信息
                    Table topTable = tables[0];

                    //获取行
                    List<TableRow> tableRows = topTable.Elements<TableRow>().ToList();
                    ReplaceTable(tableRows, tableItems);

                    //动态添加产品信息
                    Table table = tables[1];

                    TableRow row_d = table.Elements<TableRow>().ElementAt(1);
                    TableCell cell_d = row_d.Elements<TableCell>().ElementAt(1);
                    Paragraph p_d = cell_d.Elements<Paragraph>().First();
                    Run r = p_d.Elements<Run>().First();
                    //Text t = r.Elements<Text>().First();
                    //t.Text = contractDescription;
                    Text t = r.Elements<Text>().First();
                    t.Text = "";
                    bool first = true;
                    foreach (string line in contractDescription)
                    {
                        if (!first)
                        {
                            r.Append(new Break());
                        }
                        first = false;
                        Text txt = new Text();
                        txt.Text = line;
                        r.Append(txt);
                    }

                    // 添加数据
                    foreach (TemplateEnTableField tableField in rows)
                    {
                        TableRow row = new TableRow();
                        int DescriptionLength = tableField.Description == null ? 0 : tableField.Description.Length;
                        //int rownum = (tableField.CellContent.Length / 55) + ((tableField.CellContent.Length % 55) > 0 ? 1 : 0); 
                        int rownum = (DescriptionLength / 20) + ((DescriptionLength % 20) > 0 ? 1 : 0);
                        UInt32Value HeightVal = UInt32Value.ToUInt32((uint)((rownum) * 310)); //(tableField.CellContent.Length * 4);
                        if (HeightVal < 600)
                        {
                            HeightVal = 600;
                        }

                        TableRowProperties tabRowProps = row.AppendChild(new TableRowProperties(new TableRowHeight { Val = HeightVal, HeightType = HeightRuleValues.Exact }));
                        //TableRow row = new TableRow();
                        //TableRowProperties tabRowProps = row.AppendChild(new TableRowProperties(new TableRowHeight { Val = 600, HeightType = HeightRuleValues.Exact }));

                        TableCell CellName = new TableCell();

                        //垂直居中显示
                        var CellNameTcProperties = new TableCellProperties();
                        CellNameTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellNamePproperties = new ParagraphProperties();
                        CellNamePproperties.AppendChild(new Justification { Val = JustificationValues.Left });
                        //设置字体样式
                        RunProperties CellNameRunProperties = new RunProperties();//属性
                        //CellNameRunProperties.Append(new Bold());
                        //CellNameRunProperties.Append(new BoldComplexScript());
                        CellNameRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellNameRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellNameRun = new Run();
                        CellNameRun.Append(CellNameRunProperties);
                        CellNameRun.Append(new Text(tableField.Name));
                        var CellNameRunText = new Paragraph();
                        CellNameRunText.ParagraphProperties = CellNamePproperties;
                        CellNameRunText.Append(CellNameRun);
                        //CellNameRunText.Append(CellNamePproperties);
                        CellName.AppendChild(CellNameTcProperties);
                        CellName.AppendChild(CellNameRunText);
                        //CellName.Append(new Paragraph(new Run(new Text(tableField.CellName))));
                        row.Append(CellName);

                        TableCell CellDescription = new TableCell();
                        //垂直居中显示
                        var CellDescriptionTcProperties = new TableCellProperties();
                        CellDescriptionTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellDescriptionPproperties = new ParagraphProperties();
                        CellDescriptionPproperties.AppendChild(new Justification { Val = JustificationValues.Left });
                        //设置字体样式
                        RunProperties CellDescriptionRunProperties = new RunProperties();//属性
                        CellDescriptionRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellDescriptionRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellDescriptionRun = new Run();
                        CellDescriptionRun.Append(CellDescriptionRunProperties);
                        CellDescriptionRun.Append(new Text(tableField.Description));
                        var CellDescriptionRunText = new Paragraph();
                        CellDescriptionRunText.ParagraphProperties = CellDescriptionPproperties;
                        CellDescriptionRunText.Append(CellDescriptionRun);
                        //CellDescriptionRunText.Append(CellDescriptionPproperties);
                        CellDescription.AppendChild(CellDescriptionTcProperties);
                        CellDescription.AppendChild(CellDescriptionRunText);

                        //CellDescription.Append(new Paragraph(new Run(new Text(tableField.CellDescription))));
                        row.Append(CellDescription);

                        TableCell CellPrice = new TableCell();
                        //垂直居中显示
                        var CellPriceTcProperties = new TableCellProperties();
                        CellPriceTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellPricePproperties = new ParagraphProperties();
                        CellPricePproperties.AppendChild(new Justification { Val = JustificationValues.Center });
                        //设置字体样式
                        RunProperties CellPriceRunProperties = new RunProperties();//属性
                        CellPriceRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellPriceRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellPriceRun = new Run();
                        CellPriceRun.Append(CellPriceRunProperties);
                        CellPriceRun.Append(new Text(tableField.Price));
                        var CellPriceRunText = new Paragraph();
                        CellPriceRunText.ParagraphProperties = CellPricePproperties;
                        CellPriceRunText.Append(CellPriceRun);
                        //CellPriceRunText.Append(CellPricePproperties);
                        CellPrice.AppendChild(CellPriceTcProperties);
                        CellPrice.AppendChild(CellPriceRunText);

                        //CellPrice.Append(new Paragraph(new Run(new Text(tableField.CellPrice))));
                        row.Append(CellPrice);

                        TableCell CellOpeningMonths = new TableCell();
                        //垂直居中显示
                        var CellOpeningMonthsTcProperties = new TableCellProperties();
                        CellOpeningMonthsTcProperties.Append(new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center });
                        var CellOpeningMonthsPproperties = new ParagraphProperties();
                        CellOpeningMonthsPproperties.AppendChild(new Justification { Val = JustificationValues.Center });
                        //设置字体样式
                        RunProperties CellOpeningMonthsRunProperties = new RunProperties();//属性
                        CellOpeningMonthsRunProperties.Append(new FontSize() { Val = "18" });//设置字体大小
                        CellOpeningMonthsRunProperties.Append(new RunFonts() { Ascii = "宋体", HighAnsi = "宋体", EastAsia = "宋体" });//设置字体

                        Run CellOpeningMonthsRun = new Run();
                        CellOpeningMonthsRun.Append(CellOpeningMonthsRunProperties);
                        CellOpeningMonthsRun.Append(new Text(tableField.OpeningMonths));
                        var CellOpeningMonthsRunText = new Paragraph();
                        CellOpeningMonthsRunText.ParagraphProperties = CellOpeningMonthsPproperties;
                        CellOpeningMonthsRunText.Append(CellOpeningMonthsRun);
                        //CellOpeningMonthsRunText.Append(CellOpeningMonthsPproperties);
                        CellOpeningMonths.AppendChild(CellOpeningMonthsTcProperties);
                        CellOpeningMonths.AppendChild(CellOpeningMonthsRunText);

                        //CellOpeningMonths.Append(new Paragraph(new Run(new Text(tableField.CellOpeningMonths))));
                        row.Append(CellOpeningMonths);

                        table.Elements<TableRow>().First().InsertAfterSelf(row);
                    }

                    ////string picturePath = "F:\\workGit\\crm2\\crm2_api\\CRM2_API\\Template\\图章.png";
                    //string picType = "png";
                    //ImagePartType imagePartType;
                    //ImagePart imagePart = null;
                    //// 通过后缀名判断图片类型, true 表示忽视大小写
                    //if (Enum.TryParse<ImagePartType>(picType, true, out imagePartType))
                    //{
                    //    imagePart = wordDoc.MainDocumentPart.AddImagePart(imagePartType);
                    //}
                    //imagePart.FeedData(File.Open(picturePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite)); // 读取图片二进制流

                    //MainDocumentPart mainPart = wordDoc.MainDocumentPart;
                    //var bookmarks = from bm in mainPart.Document.Body.Descendants<BookmarkStart>()
                    //                where bm.Name == "stamp"
                    //                select bm;
                    //var bookmark = bookmarks.SingleOrDefault();
                    //if (bookmark != null)
                    //{
                    //    OpenXmlElement elem = bookmark.NextSibling();
                    //    while (elem != null && !(elem is BookmarkEnd))
                    //    {
                    //        OpenXmlElement nextElem = elem.NextSibling();
                    //        elem.Remove();
                    //        elem = nextElem;
                    //    }
                    //    var parent = bookmark.Parent;
                    //    Run run = new Run(new RunProperties());
                    //    Run stamp = CreateImageParagraph2(wordDoc.MainDocumentPart.GetIdOfPart(imagePart), Path.GetFileNameWithoutExtension(picturePath), 1, 230, 230, 6, false);
                    //    parent.InsertAfter<Run>(new Run(new Run(stamp)), bookmark);
                    //}

                    //动态添加产品信息
                    Table table3 = tables[3];

                    TableRow row_d3 = table3.Elements<TableRow>().ElementAt(0);
                    foreach (TableCell cell_d3 in row_d3.Elements<TableCell>())
                    {
                        //TableCell cell_d3 = row_d3.Elements<TableCell>().ElementAt(1);
                        var p_d3 = cell_d3.Elements<Paragraph>();
                        foreach (TemplateField item in items)
                        {
                            string FieldName = item.FieldName;
                            string FieldValue = item.FieldValue;
                            foreach (var para in p_d3)
                            {
                                var r3 = para.Elements<Run>();
                                foreach (var run in r3)
                                {
                                    var texts = run.Elements<Text>();
                                    //第一遍先遍历，把run内部的目标字段替换掉，并构建数组记录下所有的run
                                    //创建长度和texts一样的数组，用于记录每个text的长度
                                    foreach (var text in texts)
                                    {
                                        if (text.Text.Contains(FieldName))
                                        {
                                            text.Text = text.Text.Replace(FieldName, FieldValue);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    wordDoc.Save();
                }

                MemoryStream result = new MemoryStream();
                stream.WriteTo(result);
                result.Position = 0;
                return result;
            }
        }

        public static void ReplaceTable(List<TableRow> tableRows, List<TemplateField> tableItems)
        {
            foreach (TableRow tableRow in tableRows)
            {
                //获取列
                List<TableCell> tableCells = tableRow.Elements<TableCell>().ToList();
                foreach (TableCell tableCell in tableCells)
                {
                    //获取格中table
                    List<Table> tablesSubs = tableCell.Elements<Table>().ToList();
                    foreach (Table tablesSub in tablesSubs)
                    {
                        List<TableRow> tablesSubRows = tablesSub.Elements<TableRow>().ToList();
                        ReplaceTable(tablesSubRows, tableItems);
                    }

                    //获取格中段落
                    List<Paragraph> tablesPs = tableCell.Elements<Paragraph>().ToList();
                    foreach (Paragraph tableParagraph in tablesPs)
                    {
                        List<Run> tableRs = tableParagraph.Elements<Run>().ToList();
                        foreach (Run run in tableRs)
                        {
                            List<Text> tableTs = run.Elements<Text>().ToList();
                            foreach (Text tableT in tableTs)
                            {
                                foreach (TemplateField item in tableItems)
                                {
                                    string FieldName = item.FieldName;
                                    string FieldValue = item.FieldValue;
                                    if (tableT.Text.Contains(FieldName))
                                    {
                                        tableT.Text = tableT.Text.Replace(FieldName, FieldValue);
                                        if (tableT.Text.Contains("text-to-replace"))
                                        {
                                            tableT.Text = tableT.Text.Replace("text-to-replace", "");
                                            run.AppendChild(new Break());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        public static Stream PrintPdfFile(Stream file, string pngUrl, string stampText, int stampPosition, bool isLastPage)
        {
            //加载PDF测试文档
            PdfDocument doc = new PdfDocument();
            doc.LoadFromStream(file);
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                PdfDocument.SetCustomFontsFolders("/usr/share/fonts/windows");
            }
            string[] pngName = pngUrl.Split(";");
            int heightOld = -140;
            int weighOld = 120;
            for (int j = 0; j < pngName.Length; j++)
            {
                //获取分割后的印章图片
                Image[] images = GetImage(doc.Pages.Count, pngName[j]);
                float x = 0;
                float y = 0;

                PdfUnitConvertor convert = new PdfUnitConvertor();
                PdfPageBase pageBase = null;
                // 将图片绘制到PDF页面上的指定位置
                for (int i = 0; i < doc.Pages.Count; i++)
                {
                    pageBase = doc.Pages[i];
                    x = pageBase.Size.Width - convert.ConvertToPixels(images[i].Width, PdfGraphicsUnit.Point); //- 40;
                    y = pageBase.Size.Height / 2 + heightOld;
                    pageBase.Canvas.SetTransparency(0.8f, 0.8f, PdfBlendMode.Normal);//设置背景透明
                    pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(images[i])), x, y); //new System.Drawing.PointF(x, y));//,new SizeF()

                    //if (i == doc.Pages.Count - 1)
                    //{
                    //    pageBase.Canvas.SetTransparency(0.8f, 0.8f, PdfBlendMode.Normal); //设置背景透明
                    //    pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(GetImage(pngName[j]))), weighOld, pageBase.Size.Height / 2);// new System.Drawing.PointF(weighOld, pageBase.Size.Height / 2)); //,new SizeF()
                    //    weighOld = weighOld + 200;
                    //}

                    //添加尾章
                    PdfTextFindCollection stamp = pageBase.FindText(stampText, TextFindParameter.WholeWord);
                    bool sign = false;
                    if (isLastPage && (i == doc.Pages.Count - 1 || i == doc.Pages.Count - 2))
                    {
                        sign = true;
                    }
                    if (!isLastPage)
                    {
                        sign = true;
                    }
                    if (sign)
                    {
                        if (stamp.Finds.Length > 0)
                        {
                            for (int p = 0; p < stamp.Finds.Length; p++)
                            {
                                if (stamp.Finds[p].Position.X > 150)
                                {
                                    Image stampimage = GetImage(pngName[j]);
                                    float stampx = stamp.Finds[p].Position.X;
                                    float stampy = stamp.Finds[p].Position.Y - 60;
                                    if (pageBase.Size.Height - stampy < stampimage.Height)
                                    {
                                        stampy = pageBase.Size.Height - stampimage.Height;
                                    }
                                    if (stampy < 75)
                                    {
                                        stampy = 75;
                                    }
                                    pageBase.Canvas.SetTransparency(2f, 2f, PdfBlendMode.Normal);
                                    pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(stampimage)), stampx, stampy);
                                }
                            }
                        }
                    }
                }
                heightOld = heightOld + 140;
            }

            ////保存文档
            ////doc.SaveToFile("Result-pdf");
            ////System.Diagnostics.Process .Start("Result.pdf");
            //string output = @fileUrl;//添加图片后的新pdf文件的路径
            ////save pdf file
            //doc.SaveToFile(output);
            Stream streamPDF = new MemoryStream();
            doc.SaveToStream(streamPDF);
            return streamPDF;
        }

        private static Stream ImageToStream(Image image)
        {
            MemoryStream ms = new MemoryStream();
            image.Save(ms, new PngEncoder());
            return ms;
        }

        //定义GetImage万法，根据PDF页数分割印章图片
        private static Image[] GetImage(int num, string pngUrl)
        {
            List<Image> lists = new List<Image>();
            Image image = GetImage(pngUrl);//Image.FromFile(pngurl);
            int w = image.Width / num;
            for (int i = 0; i < num; i++)
            {
                Rectangle rect = new Rectangle(i * w, 0, w, image.Height);
                var clone = image.Clone(i => i.Resize(image.Width, image.Height).Crop(rect));
                MemoryStream ms = new MemoryStream();
                clone.Save(ms, new PngEncoder());
                Image imageNewSave = Image.Load(ms.ToArray());
                lists.Add(imageNewSave);
            }

            //Bitmap bitmap = null;
            //for (int i = 0; i < num; i++)
            //{
            //    bitmap = new Bitmap(w, image.Height);
            //    using (Graphics g = System.Drawing.Graphics.FromImage(bitmap))
            //    {
            //        g.Clear(Color.Transparent);

            //        Rectangle rect = new Rectangle(i * w, 0, w, image.Height);
            //        g.SmoothingMode = SmoothingMode.AntiAlias;
            //        g.DrawImage(image, new System.Drawing.Rectangle(0, 0, bitmap.Width, bitmap.Height), rect, GraphicsUnit.Pixel);
            //    }
            //    bitmap.MakeTransparent(System.Drawing.Color.Transparent);//设置背景透明
            //    //string pngName = Path.Combine(Directory.GetCurrentDirectory(), "Template\\" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".png"); //@"F:\Pictures\Camera Roll\" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".png";
            //    MemoryStream ms = new MemoryStream();
            //    bitmap.Save(ms, new PngEncoder());
            //    Image imageNewSave = Image.Load(ms);
            //    lists.Add(imageNewSave);
            //}
            return lists.ToArray();
        }

        //定义GetImage方法，根据PDF页数分割印章图片
        private static Image GetImage(string pngUrl)
        {
            Image image = Image.Load(pngUrl);
            image.Mutate(a => a.Resize(160, 160));
            ////Bitmap bitmapProxy = new Bitmap(image, new Size(230, 230));
            //image.Mutate(a => a.Resize(230, 230));
            //image.Dispose();
            //for (int i = 0; i < bitmapProxy.Width; i++)
            //{
            //    for (int j = 0; j < bitmapProxy.Height; j++)
            //    {
            //        Color c = bitmapProxy.GetPixel(i, j);
            //        if (!(c.R < 240 || c.G < 240 || c.B < 240))
            //        {
            //            bitmapProxy.SetPixel(i,j,Color.Transparent);//设置背景透明
            //        }
            //    }
            //}
            //bitmapProxy.MakeTransparent(System.Drawing.Color.Transparent);//设置背景透明
            //图片另算为
            //string pngName = Path.Combine(Directory.GetCurrentDirectory(), "Template\\" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".png"); //@"F:\Pictures\Camera Roll\" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".png";
            MemoryStream ms = new MemoryStream();
            image.Save(ms, new PngEncoder());
            Image imageNewSave = Image.Load(ms.ToArray());
            return imageNewSave;
        }

        /// <summary>
        /// 创建图片段落
        /// </summary>
        /// <param name="relationshipId">图片引用id</param>
        /// <param name="picname">图片名</param>
        /// <param name="picindex">图片在doc中的索引，即第几张图</param>
        /// <param name="width">宽度像素</param>
        /// <param name="height">高度像素</param>
        /// <param name="wrapType">图片环绕类型0=嵌入，1=四周环绕，2=紧密型环绕，3=穿越型环绕，4=上下型环绕，5=衬于文字下方，6=浮于文字上方</param>
        /// <param name="horizontalcenter">水平居中，仅对wrapType=0生效</param>
        /// <returns></returns>
        private static Run CreateImageParagraph2(string relationshipId, string picname, int picindex, int width, int height, int wrapType = 0, bool horizontalcenter = false)
        {
            Run run1 = new Run();

            RunProperties runProperties1 = new RunProperties();
            RunFonts runFonts2 = new RunFonts() { Hint = FontTypeHintValues.EastAsia, EastAsiaTheme = ThemeFontValues.MinorEastAsia };
            Languages languages2 = new Languages() { EastAsia = "zh-CN" };

            runProperties1.Append(runFonts2);
            runProperties1.Append(languages2);

            Drawing drawing1 = new Drawing();

            DW.Anchor anchor1 = new DW.Anchor() { DistanceFromTop = 0U, DistanceFromBottom = 0U, DistanceFromLeft = 0U, DistanceFromRight = 0U, SimplePos = false, RelativeHeight = 0U, BehindDoc = false, Locked = false, LayoutInCell = true, AllowOverlap = true };
            DW.Inline inline1 = new DW.Inline() { DistanceFromTop = 0U, DistanceFromBottom = 0U, DistanceFromLeft = 0U, DistanceFromRight = 0U };
            DocumentFormat.OpenXml.OpenXmlCompositeElement anchorline = anchor1;
            if (wrapType == 0) anchorline = inline1;

            DW.SimplePosition simplePosition1 = new DW.SimplePosition() { X = 0L, Y = 0L };

            DW.HorizontalPosition horizontalPosition1 = new DW.HorizontalPosition() { RelativeFrom = DW.HorizontalRelativePositionValues.Character };
            DW.PositionOffset positionOffset1 = new DW.PositionOffset();
            positionOffset1.Text = "0";

            horizontalPosition1.Append(positionOffset1);

            DW.VerticalPosition verticalPosition1 = new DW.VerticalPosition() { RelativeFrom = DW.VerticalRelativePositionValues.Paragraph };
            DW.PositionOffset positionOffset2 = new DW.PositionOffset();
            positionOffset2.Text = "-720000";

            verticalPosition1.Append(positionOffset2);
            DW.Extent extent1 = new DW.Extent() { Cx = (long)(width * 0.0264583 * 360000), Cy = (long)(height * 0.0264583 * 360000) };
            DW.EffectExtent effectExtent1 = new DW.EffectExtent() { LeftEdge = 0L, TopEdge = 0L, RightEdge = 0L, BottomEdge = 0L };
            DocumentFormat.OpenXml.OpenXmlElement wrap1 = null;
            var wrapPolygon1 = new DW.WrapPolygon();
            var startPoint1 = new DW.StartPoint() { X = 0L, Y = 0L };
            var lineTo1 = new DW.LineTo() { X = 0L, Y = 21405L };
            var lineTo2 = new DW.LineTo() { X = 21501L, Y = 21405L };
            var lineTo3 = new DW.LineTo() { X = 21501L, Y = 0L };
            var lineTo4 = new DW.LineTo() { X = 0L, Y = 0L };
            wrapPolygon1.Append(startPoint1);
            wrapPolygon1.Append(lineTo1);
            wrapPolygon1.Append(lineTo2);
            wrapPolygon1.Append(lineTo3);
            wrapPolygon1.Append(lineTo4);
            switch (wrapType)
            {
                case 1: wrap1 = new DW.WrapSquare() { WrapText = DW.WrapTextValues.BothSides }; break;
                case 2:
                    wrap1 = new DW.WrapTight() { WrapText = DW.WrapTextValues.BothSides };
                    wrap1.Append(wrapPolygon1);
                    break;
                case 3:
                    wrap1 = new DW.WrapThrough() { WrapText = DW.WrapTextValues.BothSides };
                    wrap1.Append(wrapPolygon1);
                    break;
                case 4:
                    wrap1 = new DW.WrapTopBottom();
                    break;
                case 5: wrap1 = new DW.WrapNone(); anchor1.BehindDoc = true; break;
                case 6: wrap1 = new DW.WrapNone(); anchor1.BehindDoc = false; break;
            }

            DW.DocProperties docProperties1 = new DW.DocProperties() { Id = (uint)picindex, Name = "Picture " + picindex, Description = picname };

            DW.NonVisualGraphicFrameDrawingProperties nonVisualGraphicFrameDrawingProperties1 = new DW.NonVisualGraphicFrameDrawingProperties();

            A.GraphicFrameLocks graphicFrameLocks1 = new A.GraphicFrameLocks() { NoChangeAspect = true };
            graphicFrameLocks1.AddNamespaceDeclaration("a", "http://schemas.openxmlformats.org/drawingml/2006/main");

            nonVisualGraphicFrameDrawingProperties1.Append(graphicFrameLocks1);

            A.Graphic graphic1 = new A.Graphic();
            graphic1.AddNamespaceDeclaration("a", "http://schemas.openxmlformats.org/drawingml/2006/main");

            A.GraphicData graphicData1 = new A.GraphicData() { Uri = "http://schemas.openxmlformats.org/drawingml/2006/picture" };
            PIC.Picture picture1 = new PIC.Picture();
            picture1.AddNamespaceDeclaration("pic", "http://schemas.openxmlformats.org/drawingml/2006/picture");

            PIC.NonVisualPictureProperties nonVisualPictureProperties1 = new PIC.NonVisualPictureProperties();
            PIC.NonVisualDrawingProperties nonVisualDrawingProperties1 = new PIC.NonVisualDrawingProperties() { Id = (uint)picindex, Name = "Picture " + picindex, Description = picname };

            PIC.NonVisualPictureDrawingProperties nonVisualPictureDrawingProperties1 = new PIC.NonVisualPictureDrawingProperties();
            A.PictureLocks pictureLocks1 = new A.PictureLocks() { NoChangeAspect = true };
            nonVisualPictureDrawingProperties1.Append(pictureLocks1);

            nonVisualPictureProperties1.Append(nonVisualDrawingProperties1);
            nonVisualPictureProperties1.Append(nonVisualPictureDrawingProperties1);

            PIC.BlipFill blipFill1 = new PIC.BlipFill();
            A.Blip blip1 = new A.Blip() { Embed = relationshipId };
            A.Stretch stretch1 = new A.Stretch();
            A.FillRectangle fillRectangle1 = new A.FillRectangle();

            stretch1.Append(fillRectangle1);

            blipFill1.Append(blip1);
            blipFill1.Append(stretch1);
            PIC.ShapeProperties shapeProperties1 = new PIC.ShapeProperties();

            A.Transform2D transform2D1 = new A.Transform2D();
            A.Offset offset1 = new A.Offset() { X = 0L, Y = 0L };
            A.Extents extents1 = new A.Extents() { Cx = (long)(width * 0.0264583 * 360000), Cy = (long)(height * 0.0264583 * 360000) };

            transform2D1.Append(offset1);
            transform2D1.Append(extents1);

            A.PresetGeometry presetGeometry1 = new A.PresetGeometry() { Preset = A.ShapeTypeValues.Rectangle };
            A.AdjustValueList adjustValueList1 = new A.AdjustValueList();

            presetGeometry1.Append(adjustValueList1);

            shapeProperties1.Append(transform2D1);
            shapeProperties1.Append(presetGeometry1);

            picture1.Append(nonVisualPictureProperties1);
            picture1.Append(blipFill1);
            picture1.Append(shapeProperties1);

            graphicData1.Append(picture1);
            graphic1.Append(graphicData1);

            anchorline.Append(simplePosition1);
            anchor1.Append(horizontalPosition1);
            anchor1.Append(verticalPosition1);
            anchorline.Append(extent1);
            anchorline.Append(effectExtent1);
            if (wrap1 != null)
                anchorline.Append(wrap1);
            anchorline.Append(docProperties1);
            anchorline.Append(nonVisualGraphicFrameDrawingProperties1);
            anchorline.Append(graphic1);
            drawing1.Append(anchorline);

            run1.Append(runProperties1);
            run1.Append(drawing1);

            return run1;
        }

        public static Stream PrintPdfFileCommonSeal(Stream file, string pngUrl, string stampText, int stampPosition, bool isLastPage)
        {
            //加载PDF测试文档
            PdfDocument doc = new PdfDocument();
            doc.LoadFromStream(file);
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                PdfDocument.SetCustomFontsFolders("/usr/share/fonts/windows");
            }
            string[] pngName = pngUrl.Split(";");
            int heightOld = -140;
            int weighOld = 120;
            for (int j = 0; j < pngName.Length; j++)
            {
                //获取分割后的印章图片
                Image[] images = GetImage(1, pngName[j]);
                float x = 0;
                float y = 0;

                PdfUnitConvertor convert = new PdfUnitConvertor();
                PdfPageBase pageBase = null;
                // 将图片绘制到PDF页面上的指定位置
                for (int i = 0; i < doc.Pages.Count; i++)
                {
                    pageBase = doc.Pages[i];
                    //x = pageBase.Size.Width - convert.ConvertToPixels(images[0].Width, PdfGraphicsUnit.Point); //- 40;
                    //y = pageBase.Size.Height / 2 + heightOld;
                    //pageBase.Canvas.SetTransparency(0.8f, 0.8f, PdfBlendMode.Normal);//设置背景透明
                    //pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(images[0])), x, y); //new System.Drawing.PointF(x, y));//,new SizeF()

                    ////if (i == doc.Pages.Count - 1)
                    ////{
                    ////    pageBase.Canvas.SetTransparency(0.8f, 0.8f, PdfBlendMode.Normal); //设置背景透明
                    ////    pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(GetImage(pngName[j]))), weighOld, pageBase.Size.Height / 2);// new System.Drawing.PointF(weighOld, pageBase.Size.Height / 2)); //,new SizeF()
                    ////    weighOld = weighOld + 200;
                    ////}

                    //添加尾章
                    PdfTextFindCollection stamp = pageBase.FindText(stampText, TextFindParameter.WholeWord);
                    bool sign = false;
                    if (isLastPage && (i == doc.Pages.Count - 1 || i == doc.Pages.Count - 2))
                    {
                        sign = true;
                    }
                    if (!isLastPage)
                    {
                        sign = true;
                    }
                    if (sign)
                    {
                        if (stamp.Finds.Length > 0)
                        {
                            for (int p = 0; p < stamp.Finds.Length; p++)
                            {
                                if (stamp.Finds[p].Position.X > 150)
                                {
                                    Image stampimage = GetImage(pngName[j]);
                                    float stampx = stamp.Finds[p].Position.X;
                                    float stampy = stamp.Finds[p].Position.Y - 60;
                                    if (pageBase.Size.Height - stampy < stampimage.Height)
                                    {
                                        stampy = pageBase.Size.Height - stampimage.Height;
                                    }
                                    if (stampy < 75)
                                    {
                                        stampy = 75;
                                    }
                                    pageBase.Canvas.SetTransparency(2f, 2f, PdfBlendMode.Normal);
                                    pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(stampimage)), stampx, stampy);
                                }
                            }
                        }
                        else
                        {
                            x = pageBase.Size.Width - convert.ConvertToPixels(images[0].Width, PdfGraphicsUnit.Point); //- 40;
                            y = pageBase.Size.Height / 2 + heightOld;
                            pageBase.Canvas.SetTransparency(0.8f, 0.8f, PdfBlendMode.Normal);//设置背景透明
                            pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(images[0])), x, y); //new System.Drawing.PointF(x, y));//,new SizeF()
                        }
                    }
                    else
                    {
                        x = pageBase.Size.Width - convert.ConvertToPixels(images[0].Width, PdfGraphicsUnit.Point); //- 40;
                        y = pageBase.Size.Height / 2 + heightOld;
                        pageBase.Canvas.SetTransparency(0.8f, 0.8f, PdfBlendMode.Normal);//设置背景透明
                        pageBase.Canvas.DrawImage(PdfImage.FromStream(ImageToStream(images[0])), x, y); //new System.Drawing.PointF(x, y));//,new SizeF()
                    }
                }
                heightOld = heightOld + 140;
            }

            ////保存文档
            ////doc.SaveToFile("Result-pdf");
            ////System.Diagnostics.Process .Start("Result.pdf");
            //string output = @fileUrl;//添加图片后的新pdf文件的路径
            ////save pdf file
            //doc.SaveToFile(output);
            Stream streamPDF = new MemoryStream();
            doc.SaveToStream(streamPDF);
            return streamPDF;
        }
    }
}
