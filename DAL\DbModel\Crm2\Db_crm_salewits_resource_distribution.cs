using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// SaleWits资源下发记录表
    /// </summary>
    [SugarTable("crm_salewits_resource_distribution")]
    public class Db_crm_salewits_resource_distribution
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 资源管理记录ID（关联crm_salewits_resource_management）
        /// </summary>
        public string ResourceManagementId { get; set; }

        /// <summary>
        /// 服务ID（关联crm_contract_serviceinfo_saleswits）
        /// </summary>
        public string ServiceId { get; set; }

        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }

        /// <summary>
        /// SaleWits租户ID
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// 下发类型：FirstDistribution-首次下发, AnnualDistribution-年度下发, AddAccountDistribution-增账号下发
        /// </summary>
        public string DistributionType { get; set; }

        /// <summary>
        /// 账号数量
        /// </summary>
        public int AccountCount { get; set; }

        /// <summary>
        /// 服务月数
        /// </summary>
        public int MonthsCount { get; set; }

        /// <summary>
        /// 邮件数量
        /// </summary>
        public int EmailCount { get; set; }

        /// <summary>
        /// Token数量（万个）
        /// </summary>
        public int TokenCount { get; set; }

        /// <summary>
        /// 充值金额（元）
        /// </summary>
        public decimal? RechargeAmount { get; set; }

        /// <summary>
        /// 下发是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 下发时间
        /// </summary>
        public DateTime DistributionTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }
    }
} 