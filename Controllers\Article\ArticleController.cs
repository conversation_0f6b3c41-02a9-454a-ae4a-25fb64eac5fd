﻿using System.ComponentModel;
using CRM2_API.BLL.Article;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Article;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 知识库控制器
    /// </summary>
    [Description("知识库控制器")]
    public class ArticleController : MyControllerBase
    {
        public ArticleController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        #region 知识库相关Action
        
        /// <summary>
        /// 根据查询条件获取文章列表。
        /// </summary>
        /// <param name="searchArticleListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchArticleList_Out> SearchArticleList(SearchArticleList_In searchArticleListIn)
        {
            int total = 0;
            var list =  DbOpe_crm_article.Instance.SearchArticleList(searchArticleListIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据文章Id获取文章信息。
        /// </summary>
        /// <param name="getArticleByIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public GetArticleById_Out GetArticleById(GetArticleById_In getArticleByIdIn)
        {
            if (getArticleByIdIn is null || string.IsNullOrEmpty(getArticleByIdIn.Id))
            {
                throw new ApiException("文章表主键id不可为空");
            }
            return DbOpe_crm_article.Instance.GetArticleById(getArticleByIdIn.Id);
        }

        /// <summary>
        /// 添加文章信息
        /// </summary>
        /// <param name="addArticleIn"></param>
        /// <returns></returns>
        [HttpPost]
        public AddArticle_Out AddArticle([FromForm] AddArticle_In addArticleIn)
        {
            return BLL_Article.Instance.AddArticle(addArticleIn);
        }

        /// <summary>
        /// 修改文章信息
        /// </summary>
        /// <param name="updateArticleIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdateArticle_Out UpdateArticle([FromForm]UpdateArticle_In updateArticleIn)
        {
            return BLL_Article.Instance.UpdateArticle(updateArticleIn);
        }

        /// <summary>
        /// 删除文章信息
        /// </summary>
        /// <param name="deleteArticleIn"></param>
        /// <returns></returns>
        [HttpPost]
        public DeleteArticle_Out DeleteArticle(DeleteArticle_In deleteArticleIn)
        {
            if (deleteArticleIn is null || string.IsNullOrEmpty(deleteArticleIn.Ids))
            {
                throw new ApiException("文章表主键列表不可为空");
            }

            return BLL_Article.Instance.DeleteArticle(deleteArticleIn.Ids);
        }

        /// <summary>
        /// 下架文章信息
        /// </summary>
        /// <param name="revokeArticleIn"></param>
        /// <returns></returns>
        [HttpPost]
        public RevokeArticle_Out RevokeArticle(RevokeArticle_In revokeArticleIn)
        {
            if (revokeArticleIn is null || string.IsNullOrEmpty(revokeArticleIn.Ids))
            {
                throw new ApiException("文章表主键列表不可为空");
            }

            return BLL_Article.Instance.RevokeArticle(revokeArticleIn.Ids);
        }

        /// <summary>
        /// 根据用户id查询该用户发布的知识库文章列表
        /// </summary>
        /// <param name="getArticleListByUserIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetArticleListByUserId_Out> GetArticleListByUserId(GetArticleListByUserId_In getArticleListByUserIdIn)
        {
            if (getArticleListByUserIdIn is null || string.IsNullOrEmpty(getArticleListByUserIdIn.UserId))
            {
                throw new ApiException("用户主键不可为空");
            }
            int total = 0;
            var list = DbOpe_crm_article.Instance.GetArticleListByUserId(getArticleListByUserIdIn, ref total);
            return GetApiTableOut(list, total);
        }
        
        /// <summary>
        /// 根据用户Id获取用户的晋升历史记录
        /// </summary>
        /// <param name="getUserPromotionListByUserIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetUserPromotionListByUserId_Out> GetUserPromotionListByUserId(GetUserPromotionListByUserId_In getUserPromotionListByUserIdIn)
        {
            if (getUserPromotionListByUserIdIn is null || string.IsNullOrEmpty(getUserPromotionListByUserIdIn.UserId))
            {
                throw new ApiException("用户主键不可为空");
            }

            return DbOpe_crm_user_promotion.Instance.GetUserPromotionListByUserId(getUserPromotionListByUserIdIn.UserId);
        }
        
        /// <summary>
        /// 获取文件[方案1]
        /// </summary>
        /// <param name="attId"></param>
        /// <returns></returns>
        [HttpGet,SkipAuthCheck]
        public IActionResult DownLoad(string attId)
        {
            if (string.IsNullOrEmpty(attId))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "附件id不可为空!"
                });
            }
            return BLL_Article.Instance.DownLoad(attId,Response);
        }
        
        /// <summary>
        /// 富文本上传图片[方案1]
        /// </summary>
        /// <param name="upload"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadImage([FromForm] ArticleTextImageUpload upload)
        {
            if (upload == null || string.IsNullOrEmpty(upload.ArticleId))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误,请检查参数!"
                });
            }
            return BLL_Article.Instance.UploadImage(upload);
        }
        
        /// <summary>
        /// 获取文件【方案2】
        /// </summary>
        /// <param name="attId"></param>
        /// <param name="articleId"></param>
        /// <returns></returns>
        [HttpGet,SkipAuthCheck]
        public IActionResult DownLoadNew(string attId, string articleId)
        {
            if (string.IsNullOrEmpty(attId) || string.IsNullOrEmpty(articleId))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误!"
                });
            }
            return BLL_Article.Instance.DownLoadNew(attId,articleId,Response);
        }
        
        /// <summary>
        /// 富文本上传图片[方案2]
        /// </summary>
        /// <param name="upload"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadImageNew([FromForm] ArticleTextImageUpload upload)
        {
            if (upload == null || upload.File == null)
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误,请检查参数!"
                });
            }
            return BLL_Article.Instance.UploadImageNew(upload);
        }

        /// <summary>
        /// 富文本心跳数据保持
        /// </summary>
        /// <param name="articleId"></param>
        /// <returns></returns>
        [HttpGet,SkipAuthCheck]
        public IActionResult ArticleTextHeartbeat(string articleId)
        {
            if (string.IsNullOrEmpty(articleId))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误,请检查参数!"
                });
            }
            return BLL_Article.Instance.ArticleTextHeartbeat(articleId);
        }

        /// <summary>
        /// 获取拥有知识库文章的用户列表
        /// </summary>
        /// <returns></returns>
        [HttpPost,SkipRightCheck]
        public List<ApiDictionary> GetHaveArticleUserList()
        {
            return DbOpe_crm_article.Instance.GetHaveArticleUserList();
        }

        #endregion

        #region 知识库评论相关的Action

        /// <summary>
        /// 添加文章评论信息
        /// </summary>
        /// <param name="addArticleCommentIn"></param>
        /// <returns></returns>
        [HttpPost]
        public AddArticleComment_Out AddArticleComment([FromForm] AddArticleComment_In addArticleCommentIn)
        {
            return BLL_ArticleComment.Instance.AddArticleComment(addArticleCommentIn);
        }

        /// <summary>
        /// 修改文章评论信息
        /// </summary>
        /// <param name="updateArticleCommentIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdateArticleComment_Out UpdateArticleComment([FromForm] UpdateArticleComment_In updateArticleCommentIn)
        {
            return BLL_ArticleComment.Instance.UpdateArticleComment(updateArticleCommentIn);
        }
        
        /// <summary>
        /// 删除文章评论信息
        /// </summary>
        /// <param name="deleteArticleCommentIn"></param>
        /// <returns></returns>
        [HttpPost]
        public DeleteArticleComment_Out DeleteArticleComment(DeleteArticleComment_In deleteArticleCommentIn)
        {
            if (deleteArticleCommentIn is null || string.IsNullOrEmpty(deleteArticleCommentIn.Id))
            {
                throw new ApiException("文章评论表主键不可为空");
            }

            return BLL_ArticleComment.Instance.DeleteArticleComment(deleteArticleCommentIn.Id);
        }

        /// <summary>
        /// 根据文章Id获取文章评论信息
        /// </summary>
        /// <param name="getArticleCommentListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetArticleCommentList_Out> GetArticleCommentList(GetArticleCommentList_In getArticleCommentListIn)
        {
            return BLL_ArticleComment.Instance.GetArticleCommentList(getArticleCommentListIn);
        }
        
        #endregion

        #region 知识库评论点赞相关的Action
        
        /// <summary>
        /// 添加文章评论点赞信息
        /// </summary>
        /// <param name="articleCommentPraiseIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public AddArticleCommentPraise_Out AddArticleCommentPraise(AddArticleCommentPraise_In articleCommentPraiseIn)
        {
            if (articleCommentPraiseIn is null || string.IsNullOrEmpty(articleCommentPraiseIn.ArticleCommentId))
            {
                throw new ApiException("文章评论表Id不可为空");
            }

            return DbOpe_crm_article_comment_praise.Instance.AddArticleCommentPraise(articleCommentPraiseIn
                .ArticleCommentId);
        }

        /// <summary>
        /// 撤销文章评论点赞信息。
        /// </summary>
        /// <param name="deleteArticleCommentPraiseIn"></param>
        /// <returns></returns>
        [HttpPost]
        public DeleteArticleCommentPraise_Out DeleteArticleCommentPraise(
            DeleteArticleCommentPraise_In deleteArticleCommentPraiseIn)
        {
            if (deleteArticleCommentPraiseIn is null || string.IsNullOrEmpty(deleteArticleCommentPraiseIn.ArticleCommentId))
            {
                throw new ApiException("文章评论表Id不可为空");
            }

            return DbOpe_crm_article_comment_praise.Instance.DeleteArticleCommentPraise(deleteArticleCommentPraiseIn
                .ArticleCommentId);
        }
        
        #endregion

        #region 知识库点赞相关的Action
        
        /// <summary>
        /// 添加文章点赞信息
        /// </summary>
        /// <param name="addArticlePraiseIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public AddArticlePraise_Out AddArticlePraise(AddArticlePraise_In addArticlePraiseIn)
        {
            if (addArticlePraiseIn is null || string.IsNullOrEmpty(addArticlePraiseIn.ArticleId))
            {
                throw new ApiException("文章表Id不可为空");
            }

            return DbOpe_crm_article_praise.Instance.AddArticlePraise(addArticlePraiseIn.ArticleId);
        }

        /// <summary>
        /// 撤销文章点赞信息
        /// </summary>
        /// <param name="deleteArticlePraiseIn"></param>
        /// <returns></returns>
        [HttpPost]
        public DeleteArticlePraise_Out DeleteArticlePraise(DeleteArticlePraise_In deleteArticlePraiseIn)
        {
            if (deleteArticlePraiseIn is null || string.IsNullOrEmpty(deleteArticlePraiseIn.ArticleId))
            {
                throw new ApiException("文章表Id不可为空");
            }
            return DbOpe_crm_article_praise.Instance.DeleteArticlePraise(deleteArticlePraiseIn.ArticleId);
        }
        
        #endregion

        #region 知识库浏览相关的Action
        
        /// <summary>
        /// 添加文章浏览信息。
        /// </summary>
        /// <param name="addArticleBrowseIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public AddArticleBrowse_Out AddArticleBrowse(AddArticleBrowse_In addArticleBrowseIn)
        {
            if (addArticleBrowseIn is null || string.IsNullOrEmpty(addArticleBrowseIn.ArticleId))
            {
                throw new ApiException("文章表id不可为空");
            }

            return DbOpe_crm_article_browse.Instance.AddArticleBrowse(addArticleBrowseIn.ArticleId);
        }

        /// <summary>
        /// 根据知识库id获取对应的浏览记录列表
        /// </summary>
        /// <param name="getBrowseRecordByArticleIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetBrowseRecordByArticleId_Out> GetBrowseRecordByArticleId(
            GetBrowseRecordByArticleId_In getBrowseRecordByArticleIdIn)
        {
            if (getBrowseRecordByArticleIdIn is null || string.IsNullOrEmpty(getBrowseRecordByArticleIdIn.ArticleId))
            {
                throw new ApiException("文章表id不可为空");
            }
            return DbOpe_crm_article_browse.Instance.GetBrowseRecordByArticleId(getBrowseRecordByArticleIdIn);
        }

        #endregion

        #region 板块评论部分

        /// <summary>
        /// 新增板块
        /// </summary>
        /// <param name="addArticleSectionIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ArticleBase_Out AddArticleSection(AddArticleSection_In addArticleSectionIn)
        {
            return BLL_Article.Instance.AddArticleSection(addArticleSectionIn);
        }

        /// <summary>
        /// 修改板块
        /// </summary>
        /// <param name="updateArticleSection"></param>
        /// <returns></returns>
        [HttpPost]
        public ArticleBase_Out UpdateArticleSection(UpdateArticleSection_In updateArticleSection)
        {
            return BLL_Article.Instance.UpdateArticleSection(updateArticleSection);
        }

        /// <summary>
        /// 删除板块
        /// </summary>
        /// <param name="deleteArticleSection"></param>
        /// <returns></returns>
        [HttpPost]
        public ArticleBase_Out DeleteArticleSection(DeleteArticleSection_In deleteArticleSection)
        {
            if (deleteArticleSection is null || string.IsNullOrEmpty(deleteArticleSection.Ids))
            {
                throw new ApiException("删除板块主键列表不可为空");
            }
            return DbOpe_crm_article_section.Instance.DeleteArticleSection(deleteArticleSection);
        }
        /// <summary>
        /// 根据条件查询板块列表
        /// </summary>
        /// <param name="searchArticleSectionIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchArticleSection_Out> SearchArticleSectionList(SearchArticleSection_In searchArticleSectionIn)
        {
            int total = 0;
            var list = DbOpe_crm_article_section.Instance.SearchArticleSectionList(searchArticleSectionIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据ID获取板块
        /// </summary>
        /// <param name="getArticleSectionById"></param>
        /// <returns></returns>
        [HttpPost]
        public GetArticleSectionById GetArticleSectionById(GetArticleSectionById_In getArticleSectionById)
        {
            return DbOpe_crm_article_section.Instance.GetArticleSectionById(getArticleSectionById);
        }

        /// <summary>
        /// 获取板块列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<GetArticleSection> GetArticleSectionList()
        {
            return DbOpe_crm_article_section.Instance.GetArticleSectionList();
        }
        /// <summary>
        /// 获取标签列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<GetArticleTips> GetArticleTipsList()
        {
            return DbOpe_crm_article_section.Instance.GetArticleTipsList();
        }
        /// <summary>
        /// 添加标签列表(可以批量)
        /// </summary>
        /// <param name="addArticleTipsList"></param>
        /// <returns></returns>
        [HttpPost]
        public AddTips_Out AddArticleTipsList(List<AddArticleTips_In> addArticleTipsList)
        {
            return BLL_Article.Instance.AddArticleTipsList(addArticleTipsList);
        }


        #endregion
    }
}