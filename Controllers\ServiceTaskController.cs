﻿using Microsoft.AspNetCore.Mvc;
using ServiceTaskScheduler;
using ServiceTaskScheduler.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using CRM2_API.Controllers.Base;
using Microsoft.AspNetCore.Http;
using CRM2_API.BLL;
using System.ComponentModel;
using CRM2_API.Common.Filter;

namespace ServiceTaskScheduler.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TasksController : MyControllerBase
    {
        public TasksController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取所有任务
        /// </summary>
        /// <returns>任务列表</returns>
        [HttpGet]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IEnumerable<TaskInfo> GetAllTasks()
        {
            var tasks = BLL_Service.Instance.GetAllTasksAsync().Result;
            return tasks;
        }

        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>任务详细信息</returns>
        [HttpGet("{id}")]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public ActionResult<TaskInfo> GetTask(string id)
        {
            var task = BLL_Service.Instance.GetTaskAsync(id).Result;
            return task;
        }

        /// <summary>
        /// 创建账号开通多步骤任务
        /// </summary>
        /// <param name="request">账号开通请求参数</param>
        /// <returns>创建的任务ID</returns>
        [HttpPost("account-opening")]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public string CreateAccountOpeningTask([FromBody] AccountOpeningTaskRequest request)
        {
            try
            {
                var taskId = BLL_Service.Instance.CreateAccountOpeningTaskAsync(
                    request.Username,
                    request.Password,
                    request.ScheduledTime,
                    request.Priority ?? 5,
                    request.OtherParams
                );

                return taskId.Result;
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        ///// <summary>
        ///// 创建错误重试测试任务
        ///// </summary>
        ///// <returns>创建的任务ID</returns>
        //[HttpPost("retry-test")]
        //[SwaggerOperation(Summary = "创建错误重试测试任务", Description = "创建一个将会失败并触发重试机制的任务")]
        //[SwaggerResponse(200, "任务创建成功", typeof(object))]
        //[SwaggerResponse(500, "服务器内部错误")]
        //public async Task<ActionResult<string>> CreateRetryTestTask()
        //{
        //    try
        //    {
        //        var taskId = await _accountService.CreateFastRetryTestTaskAsync();
        //        return Ok(new
        //        {
        //            TaskId = taskId,
        //            Message = "已创建错误重试测试任务，将使用快速重试延迟模式",
        //            RetryDelays = "2秒、5秒、10秒"
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, $"创建任务失败: {ex.Message}");
        //    }
        //}

        ///// <summary>
        ///// 创建自定义重试间隔测试任务
        ///// </summary>
        ///// <param name="request">重试间隔测试请求</param>
        ///// <returns>创建的任务ID</returns>
        //[HttpPost("custom-retry-test")]
        //[SwaggerOperation(Summary = "创建自定义重试间隔测试任务", Description = "创建一个将会失败并使用自定义重试间隔的任务")]
        //[SwaggerResponse(200, "任务创建成功", typeof(object))]
        //[SwaggerResponse(400, "请求参数无效")]
        //[SwaggerResponse(500, "服务器内部错误")]
        //public async Task<ActionResult<string>> CreateCustomRetryTestTask([FromBody] CustomRetryTestRequest request)
        //{
        //    if (request == null || request.RetryDelaySeconds == null || request.RetryDelaySeconds.Length == 0)
        //        return BadRequest("重试延迟秒数不能为空");

        //    try
        //    {
        //        var taskId = await _accountService.CreateCustomRetryTestTaskAsync(request.RetryDelaySeconds);
        //        return Ok(new
        //        {
        //            TaskId = taskId,
        //            Message = "已创建自定义重试间隔测试任务",
        //            RetryDelays = string.Join("、", request.RetryDelaySeconds.Select(s => $"{s}秒"))
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, $"创建任务失败: {ex.Message}");
        //    }
        //}
    }

    /// <summary>
    /// 账号开通任务请求
    /// </summary>
    public class AccountOpeningTaskRequest
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Description("用户的登录名")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Description("用户的登录密码")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 计划执行时间（为空表示立即执行）
        /// </summary>
        [Description("任务计划执行时间，为空表示立即执行")]
        public DateTime? ScheduledTime { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        [Description("任务优先级，范围1-10，值越高优先级越高，默认为5")]
        public int? Priority { get; set; }

        /// <summary>
        /// 其他参数
        /// </summary>
        [Description("其他业务参数，JSON格式字符串")]
        public string OtherParams { get; set; } = string.Empty;
    }

    /// <summary>
    /// 自定义重试测试请求
    /// </summary>
    public class CustomRetryTestRequest
    {
        /// <summary>
        /// 重试延迟秒数数组
        /// </summary>
        [Description("自定义的重试延迟秒数数组，例如 [3, 10, 30]")]
        public int[] RetryDelaySeconds { get; set; } = Array.Empty<int>();
    }
}