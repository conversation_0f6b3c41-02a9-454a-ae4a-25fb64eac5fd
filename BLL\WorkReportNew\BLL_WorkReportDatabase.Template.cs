using System;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.Enum;
using CRM2_API.Model.System;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告模板管理业务逻辑类
    /// </summary>
    public partial class BLL_WorkReportDatabase
    {
        #region 报告模板管理

        /// <summary>
        /// 获取报告模板
        /// </summary>
        /// <param name="reportType">报告类型</param>
        /// <returns>模板数据</returns>
        public GetReportTemplate_Out GetReportTemplate(EnumReportType reportType)
        {
            try
            {
                var templateBll = new BLL_WorkReport();
                return templateBll.GetReportTemplate(reportType);
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取报告模板失败：{ex.Message}");
            }
        }

        #endregion
    }
} 