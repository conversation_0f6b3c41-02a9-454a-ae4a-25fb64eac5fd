-- 服务变更字段记录表
-- 执行日期: 2025-01-29

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `crm_service_change_field_record` (
  `Id` varchar(50) NOT NULL COMMENT '主键ID',
  `ApplyId` varchar(50) DEFAULT NULL COMMENT '申请ID',
  `ServiceType` int(11) DEFAULT NULL COMMENT '服务类型',
  `ChangeReasonEnum` int(11) DEFAULT NULL COMMENT '变更原因',
  `FieldKey` varchar(100) DEFAULT NULL COMMENT '变更的字段键名',
  `FieldName` varchar(100) DEFAULT NULL COMMENT '变更的字段名称',
  `IsArray` tinyint(1) DEFAULT '0' COMMENT '是否是数组类型',
  `OriginValue` text COMMENT '原始值',
  `ChangedValue` text COMMENT '变更后的值',
  `Deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `CreateDate` datetime DEFAULT NULL COMMENT '创建时间',
  `CreateUser` varchar(50) DEFAULT NULL COMMENT '创建人',
  `UpdateDate` datetime DEFAULT NULL COMMENT '更新时间',
  `UpdateUser` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`Id`),
  KEY `idx_apply_id` (`ApplyId`),
  KEY `idx_field_key` (`FieldKey`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务变更字段记录表';

-- 添加表注释
ALTER TABLE `crm_service_change_field_record` COMMENT '服务变更字段记录表（所有服务类型通用）';

SELECT '创建服务变更字段记录表完成！' AS message;