﻿using System;
using System.Diagnostics;
using CRM2_API.BLL.Common;
using CRM2_API.Common.JWT;
using CRM2_API.Common.Cron;
using CRM2_API.Common.Utils;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Schedule;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Quartz;

namespace CRM2_API.BLL.Schedule
{
    public class BLL_Schedule : BaseBLL<BLL_Schedule>
    {
        /// <summary>
        /// 日程计划定时任务的前缀
        /// </summary>
        public static readonly string ScheduleTaskPrefix = "refreshScheduleStateTask_";

        /// <summary>
        /// 根据日程计划表主键Id设置日程计划为已读
        /// </summary>
        /// <param name="setScheduleReadByIdIn"></param>
        /// <returns></returns>
        public SetScheduleReadById_Out SetScheduleReadById(SetScheduleReadById_In setScheduleReadByIdIn)
        {
            if (setScheduleReadByIdIn != null && !string.IsNullOrEmpty(setScheduleReadByIdIn.Id))
            {
                bool existsResult = CheckScheduleExists(setScheduleReadByIdIn.Id);
                if (existsResult)
                {
                    return DbOpe_crm_schedule_read.Instance.SetScheduleReadById(setScheduleReadByIdIn);
                }
                throw new ApiException("日程计划Id所对应的数据不存在");
            }
            return new SetScheduleReadById_Out()
            {
                Data = 0,
            };
        }

        /// <summary>
        /// 添加日程计划信息
        /// </summary>
        /// <param name="addScheduleIn"></param>
        /// <param name="isCheckVisitorTime">是否检验传入的日程计划时间</param>
        /// <param name="isReadModelUser">是否读取Model中的User属性，前端接口则不读取防止注入，内部调用则读取</param>
        /// <param name="customerSubCompanyId">公司Id[用于内部调用使用]</param>
        /// <param name="canEdit">允许编辑[用于内部调用使用]</param>
        /// <param name="ReceiptregisterId">业绩相关ID[用于内部调用使用]</param>
        /// <returns></returns>
        public AddSchedule_Out AddSchedule(AddSchedule_In addScheduleIn, bool isCheckVisitorTime = true, bool isReadModelUser = false, string customerSubCompanyId = "", bool canEdit = false, string ReceiptregisterId = "")
        {
            var result = new AddSchedule_Out { Data = 0 };
            var checkParamResult = CheckScheduleParams(addScheduleIn);
            var checkResult = true;
            if (isCheckVisitorTime)
            {
                checkResult = CheckVisitorTime(string.Empty, Tuple.Create(addScheduleIn.VisitorTimeStart, addScheduleIn.VisitorTimeEnd));
            }
            if (checkParamResult && checkResult)
            {
                var insertId = Guid.NewGuid().ToString();
                var customerData = DbOpe_crm_schedule.Instance.GetCustomerInfo(addScheduleIn.CustomerId, addScheduleIn.CustomerDataSource, customerSubCompanyId);    //读取数据库中的客户信息并快照
                if (customerData is null)
                {
                    throw new ApiException("客户数据为空!");
                }

                var newEntity = new Db_crm_schedule
                {
                    Id = insertId,
                    //UserId = addScheduleIn.UserId,
                    UserId = isReadModelUser ? addScheduleIn.UserId : TokenModel.Instance.id,
                    CustomerDataSource = addScheduleIn.CustomerDataSource,
                    CustomerId = customerData.Id,
                    CustomerTemporaryId = addScheduleIn.CustomerDataSource == EnumCustomerDataSource.Temporary ? customerData.CustomerTemporaryId : null,
                    ContractId = addScheduleIn.ContractId,
                    TrackingType = addScheduleIn.TrackingType,
                    TrackingPurpose = addScheduleIn.TrackingPurpose,
                    TrackingStage = addScheduleIn.TrackingStage,
                    Remark = addScheduleIn.Remark,
                    VisitorTimeStart = addScheduleIn.VisitorTimeStart?.LocalDateTime,
                    VisitorTimeEnd = addScheduleIn.VisitorTimeEnd?.LocalDateTime,
                    IsVisible = addScheduleIn.CustomerDataSource.Equals(EnumCustomerDataSource.Private) ? 1 : 0,
                    Deleted = false,
                    CreateUser = TokenModel.Instance.id,
                    CreateDate = DateTime.Now,
                    TrackingEvents = addScheduleIn.TrackingEvents,
                    State = EnumSchedulePlanStatus.Normal,
                    CanEdit = canEdit,
                    //将客户快照信息存入到跟踪记录表中
                    CustomerSubCompanyId = customerData.CustomerSubCompanyId,
                    CustomerSubCompanyTemporaryId = addScheduleIn.CustomerDataSource == EnumCustomerDataSource.Temporary ? customerData.CustomerSubCompanyTemporaryId : null,
                    CustomerName = customerData.CustomerName,
                    CustomerLevel = customerData.CustomerLevel,
                    ServiceState = customerData.ServiceState,
                    CustomerOrderNum = customerData.CustomerOrderNum,
                    CustomerNum = customerData.CustomerNum,
                    CustomerNature = customerData.CustomerNature,
                    CustomerSize = customerData.CustomerSize,
                    CustomerBindUserId = customerData.CustomerBindUserId,
                    CustomerBindUserOrgId = customerData.CustomerBindUserOrgId,
                    CustomerBindUserOrgName = customerData.OrgName,
                    CustomerSource = customerData.CustomerSource,
                    CollectionTime = customerData.CollectionTime,
                    CustomerIndustrys = customerData.CustomerIndustrys,
                    ReceiptregisterId = ReceiptregisterId.IsNotNullOrEmpty() ? ReceiptregisterId : null,
                };

                //快照合同信息
                if (!string.IsNullOrEmpty(addScheduleIn.ContractId))
                {
                    var contractInfo = DbOpe_crm_schedule.Instance.GetContractInfo(addScheduleIn.ContractId);
                    newEntity.ContractName = contractInfo.ContractName;
                    newEntity.PlannedArrivalDate = contractInfo.PlannedArrivalDate;
                    newEntity.PlannedArrivalAmount = contractInfo.PlannedArrivalAmount;
                    newEntity.Currency = contractInfo.Currency;
                    newEntity.PaymentCompany = contractInfo.PaymentCompany;
                    newEntity.PaymentCompanyName = contractInfo.PaymentCompanyName;
                    newEntity.CollectingCompany = contractInfo.CollectingCompany;
                    newEntity.CollectingCompanyName = contractInfo.CollectingCompanyName;
                    newEntity.PaymentMethod = contractInfo.PaymentMethod;

                    newEntity.ExpectedInvoicingTime = contractInfo.ExpectedInvoicingTime;
                    newEntity.InvoicedAmount = contractInfo.InvoicedAmount;
                    newEntity.BillingType = contractInfo.BillingType;
                    newEntity.InvoiceType = contractInfo.InvoiceType;
                    newEntity.BillingCompany = contractInfo.BillingCompany;
                    newEntity.BillingCompanyName = contractInfo.CollectingCompanyName;
                    newEntity.BillingHeader = contractInfo.BillingHeader;
                    newEntity.BillingHeaderName = contractInfo.BillingHeaderName;
                }

                DbOpe_crm_schedule.Instance.InsertQueue(newEntity);
                if (addScheduleIn.AttachFiles is not null && addScheduleIn.AttachFiles.Count > 0)
                {
                    Util<DbOpe_crm_schedule_attachfile, BM_AttachFiles> util = new(DbOpe_crm_schedule_attachfile.Instance);
                    bool uploadResult = util.UploadFiles(insertId, addScheduleIn.AttachFiles, AttachEnumOption.Schedule);
                    if (!uploadResult)
                    {
                        throw new ApiException("文件上传异常!");
                    }
                }
                DbOpe_crm_schedule.Instance.SaveQueues();
                result.Data = 1;
                result.Content = new
                {
                    insertId
                };
                AddTimingRefreshScheduleTask(newEntity);    //添加定时任务
            }
            return result;
        }

        /// <summary>
        /// 修改日程计划信息
        /// </summary>
        /// <param name="updateScheduleIn"></param>
        /// <returns></returns>
        public UpdateSchedule_Out UpdateSchedule(UpdateSchedule_In updateScheduleIn)
        {
            var result = new UpdateSchedule_Out { Data = 0 };
            var schedule = DbOpe_crm_schedule.Instance.QueryByPrimaryKey(updateScheduleIn.Id) ?? throw new ApiException("提供主键所对应的日程计划信息不存在!");
            var checkCreatorResult = CheckScheduleCreator(schedule);
            if (!checkCreatorResult)
            {
                throw new ApiException("无法修改非本人创建的跟踪记录");
            }
            var checkResult = CheckScheduleParams(updateScheduleIn);
            var checkTimeResult = CheckVisitorTime(updateScheduleIn.Id, Tuple.Create(updateScheduleIn.VisitorTimeStart, updateScheduleIn.VisitorTimeEnd));
            if (checkCreatorResult && checkResult && checkTimeResult)
            {
                //拿一下客户数据
                var customerData = DbOpe_crm_schedule.Instance.GetCustomerInfo(updateScheduleIn.CustomerId,
                    updateScheduleIn.CustomerDataSource);    //读取数据库中的客户信息并快照
                if (customerData is null)
                {
                    throw new ApiException("客户数据为空!");
                }
                //将客户快照信息存入到跟踪记录表中
                schedule = DbOpe_crm_schedule.Instance.UpdateSchedule(schedule, updateScheduleIn, customerData);

                //附件操作
                var attachFileList = DbOpe_crm_schedule.Instance.GetScheduleAttachFilesById(updateScheduleIn.Id);
                var attachFileIdList = attachFileList.Select(w => w.Id).ToList();
                if (updateScheduleIn.AttachFiles is null or { Count: 0 })
                {
                    if (attachFileIdList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_schedule.Instance.DeleteAttachFileByIdList(attachFileIdList);
                    }
                }
                else
                {
                    var attachFileUpdateIdList = new List<string>();
                    FormFileCollection formFileCollection = new();
                    foreach (Update_ScheduleAttachFile updateScheduleAttachFile in updateScheduleIn.AttachFiles)
                    {
                        if (attachFileIdList.Contains(updateScheduleAttachFile.Id))
                        {
                            attachFileUpdateIdList.Add(updateScheduleAttachFile.Id);
                            continue;
                        }

                        if (updateScheduleAttachFile.File != null)
                        {
                            formFileCollection.Add(updateScheduleAttachFile.File);
                        }

                    }

                    if (formFileCollection.Count > 0)
                    {
                        Util<DbOpe_crm_schedule_attachfile, BM_AttachFiles> util = new(DbOpe_crm_schedule_attachfile.Instance);
                        bool uploadResult = util.UploadFiles(updateScheduleIn.Id, formFileCollection, AttachEnumOption.Schedule);
                        if (!uploadResult)
                        {
                            throw new ApiException("文件上传异常!");
                        }
                    }
                    var attachFileDeleteIdList = attachFileIdList.Except(attachFileUpdateIdList).ToList();
                    DbOpe_crm_schedule.Instance.DeleteAttachFileByIdList(attachFileDeleteIdList);
                }

                int execCount = DbOpe_crm_schedule.Instance.SaveQueues();
                if (execCount > 0)
                {
                    AddTimingRefreshScheduleTask(schedule);    //添加定时任务
                    result.Data = 1;
                }
            }
            return result;
        }


        /// <summary>
        /// 删除日程计划信息
        /// </summary>
        /// <param name="deleteScheduleIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public DeleteSchedule_Out DeleteSchedule(DeleteSchedule_In deleteScheduleIn)
        {
            var result = new DeleteSchedule_Out { Data = 0 };
            var schedule = DbOpe_crm_schedule.Instance.QueryByPrimaryKey(deleteScheduleIn.Id) ?? throw new ApiException("提供主键所对应的日程计划信息不存在!");
            var checkCreatorResult = CheckScheduleCreator(schedule);
            if (checkCreatorResult)
            {
                result = DbOpe_crm_schedule.Instance.DeleteSchedule(deleteScheduleIn.Id);
                if (result.Data > 0)
                {
                    string jobName = $"{ScheduleTaskPrefix}{deleteScheduleIn.Id}";
                    // CronUtil.StopJob(jobName); // 不再需要手动停止任务，SafeCronUtil会自动管理
                }
            }
            else
            {
                throw new ApiException("无法对非本人创建的日程计划执行操作!");
            }
            return result;
        }

        /// <summary>
        /// 取消日程计划信息
        /// </summary>
        /// <param name="cancelScheduleIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public CancelSchedule_Out CancelSchedule(CancelSchedule_In cancelScheduleIn)
        {
            var result = new CancelSchedule_Out { Data = 0 };
            var schedule = DbOpe_crm_schedule.Instance.QueryByPrimaryKey(cancelScheduleIn.Id) ?? throw new ApiException("提供主键所对应的日程计划信息不存在!");
            var checkCreatorResult = CheckScheduleCreator(schedule);
            if (checkCreatorResult)
            {
                result = DbOpe_crm_schedule.Instance.CancelSchedule(cancelScheduleIn.Id);
                if (result.Data > 0)
                {
                    string jobName = $"{ScheduleTaskPrefix}{cancelScheduleIn.Id}";
                    // CronUtil.StopJob(jobName); // 不再需要手动停止任务，SafeCronUtil会自动管理
                }
            }
            else
            {
                throw new ApiException("无法对非本人创建的日程计划执行操作!");
            }
            return result;
        }

        /// <summary>
        /// 延期日程计划信息
        /// </summary>
        /// <param name="delayScheduleIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public DelaySchedule_Out DelaySchedule(DelaySchedule_In delayScheduleIn)
        {
            var result = new DelaySchedule_Out { Data = 0 };
            var schedule = DbOpe_crm_schedule.Instance.QueryByPrimaryKey(delayScheduleIn.Id) ?? throw new ApiException("提供主键所对应的日程计划信息不存在!");
            var checkCreatorResult = CheckScheduleCreator(schedule);
            if (!checkCreatorResult)
            {
                throw new ApiException("无法对非本人创建的日程计划执行操作");
            }
            var checkTimeResult = CheckVisitorTime(delayScheduleIn.Id, Tuple.Create(delayScheduleIn.VisitorTimeStart, delayScheduleIn.VisitorTimeEnd));
            if (checkCreatorResult && checkTimeResult)
            {
                var execResult = DbOpe_crm_schedule.Instance.DelaySchedule(delayScheduleIn, schedule);
                result = execResult.result;
                if (result.Data > 0)
                {
                    string jobName = $"{ScheduleTaskPrefix}{execResult.data.Id}";
                    // CronUtil.StopJob(jobName); // 不再需要手动停止任务，SafeCronUtil会自动管理
                    AddTimingRefreshScheduleTask(execResult.data);    //重新添加定时任务
                }
            }
            return result;
        }

        /// <summary>
        /// 完成日程计划信息
        /// </summary>
        /// <param name="completeScheduleIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public CompleteSchedule_Out CompleteSchedule(CompleteSchedule_In completeScheduleIn)
        {
            var result = new CompleteSchedule_Out { Data = 0 };
            var schedule = DbOpe_crm_schedule.Instance.QueryByPrimaryKey(completeScheduleIn.Id) ?? throw new ApiException("提供主键所对应的日程计划信息不存在!");
            var checkCreatorResult = CheckScheduleCreator(schedule);
            if (checkCreatorResult)
            {
                DbOpe_crm_schedule.Instance.CompleteSchedule(completeScheduleIn);

                //附件操作
                var attachFileList = DbOpe_crm_schedule.Instance.GetScheduleAttachFilesById(completeScheduleIn.Id, 2);
                var attachFileIdList = attachFileList.Select(w => w.Id).ToList();
                if (completeScheduleIn.AttachFiles is null or { Count: 0 })
                {
                    if (attachFileIdList is not null and { Count: > 0 })
                    {
                        DbOpe_crm_schedule.Instance.DeleteAttachFileByIdList(attachFileIdList);
                    }
                }
                else
                {
                    var attachFileUpdateIdList = new List<string>();
                    FormFileCollection formFileCollection = new();
                    foreach (Update_ScheduleAttachFile updateScheduleAttachFile in completeScheduleIn.AttachFiles)
                    {
                        if (attachFileIdList.Contains(updateScheduleAttachFile.Id))
                        {
                            attachFileUpdateIdList.Add(updateScheduleAttachFile.Id);
                            continue;
                        }

                        if (updateScheduleAttachFile.File != null)
                        {
                            formFileCollection.Add(updateScheduleAttachFile.File);
                        }

                    }

                    if (formFileCollection.Count > 0)
                    {
                        Util<DbOpe_crm_schedule_attachfile, BM_AttachFiles> util = new(DbOpe_crm_schedule_attachfile.Instance);
                        bool uploadResult = util.UploadFiles(completeScheduleIn.Id, formFileCollection, AttachEnumOption.ScheduleAfter);
                        if (!uploadResult)
                        {
                            throw new ApiException("文件上传异常!");
                        }
                    }
                    var attachFileDeleteIdList = attachFileIdList.Except(attachFileUpdateIdList).ToList();
                    DbOpe_crm_schedule.Instance.DeleteAttachFileByIdList(attachFileDeleteIdList);
                }

                int execCount = DbOpe_crm_schedule.Instance.SaveQueues();
                if (execCount > 0)
                {
                    string jobName = $"{ScheduleTaskPrefix}{completeScheduleIn.Id}";  //停止定时任务
                    // CronUtil.StopJob(jobName); // 不再需要手动停止任务，SafeCronUtil会自动管理
                    result.Data = 1;
                }
            }
            else
            {
                throw new ApiException("无法对非本人创建的日程计划执行操作!");
            }
            return result;
        }

        /// <summary>
        /// 根据日程计划id判断是否存在
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public bool CheckScheduleExists(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                throw new ArgumentNullException("日程计划id不可为空");
            }
            var dbData = DbOpe_crm_schedule.Instance.QueryByPrimaryKey(id);
            return dbData is not null;
        }

        /// <summary>
        /// 校验日程计划的创建人是否为当前登陆人
        /// </summary>
        /// <param name="schedule"></param>
        /// <returns>true == 校验通过,false == 校验不通过</returns>
        public bool CheckScheduleCreator(Db_crm_schedule schedule)
        {
            if (TokenModel.Instance is null) return false;
            if (string.IsNullOrEmpty(schedule.UserId)) return false;
            if (schedule.UserId.Equals(TokenModel.Instance.id)) return true;
            return false;
        }

        /// <summary>
        /// 校验新增和修改时的公共参数
        /// </summary>
        /// <returns></returns>
        private bool CheckScheduleParams(ScheduleAddOrModifyBase param)
        {
            //对输入的参数进行校验
            //_ = DbOpe_sys_user.Instance.QueryByPrimaryKey(param.UserId) ?? throw new ApiException("用户主键所对应的数据不存在!");

            if (param.CustomerDataSource.Equals(EnumCustomerDataSource.Private))
            {
                //验证客户信息是否需要补录
                BLL_Customer.Instance.CheckCustomerInfoNeedSupplementary(new List<string>() { param.CustomerId });

                _ = DbOpe_crm_customer.Instance.QueryByPrimaryKey(param.CustomerId) ??
                    throw new ApiException("客户主键所对应的数据不存在!");
            }
            else if (param.CustomerDataSource.Equals(EnumCustomerDataSource.Temporary))
            {
                _ = DbOpe_crm_customer_temporary.Instance.QueryByPrimaryKey(param.CustomerId) ??
                    throw new ApiException("客户主键所对应的数据不存在!");
            }

            if (!string.IsNullOrEmpty(param.ContractId))
            {
                _ = DbOpe_crm_contract.Instance.QueryByPrimaryKey(param.ContractId) ??
                    throw new ApiException("合同主键所对应的数据不存在!");
            }
            return true;
        }

        /// <summary>
        /// 校验传入的实体中访客时间参数是否通过校验
        /// </summary>
        /// <param name="id">更新时需要传入此参数用于排除自身</param>
        /// <param name="param">访客时间区间</param>
        /// <returns>通过校验, false == 不通过校验</returns>
        /// <exception cref="ApiException"></exception>
        public bool CheckVisitorTime(string? id, Tuple<DateTimeOffset?, DateTimeOffset?> param)
        {
            if (param.Item1.HasValue)
            {
                //验证开始时间是否大于当前时间
                DateTime now = DateTime.Now;
                int nowCompareTo = now.CompareTo(param.Item1.Value.LocalDateTime);
                if (nowCompareTo > 0)
                {
                    throw new ApiException("访客开始时间必须大于当前时间");
                }
                //验证开始时间和结束时间相差的分钟数
                if (param.Item2.HasValue)
                {
                    TimeSpan tsStart = new(param.Item1.Value.LocalDateTime.Ticks);
                    TimeSpan tsEnd = new(param.Item2.Value.LocalDateTime.Ticks);
                    TimeSpan tsDiff = tsEnd.Subtract(tsStart).Duration();
                    if (tsDiff.Days > 0 || tsDiff.Hours > 0 || tsDiff.Minutes >= 5)
                    {
                        TimeSpan tsStart2 = new(param.Item1.Value.LocalDateTime.GetDaysStart().Ticks);
                        TimeSpan tsEnd2 = new(param.Item2.Value.LocalDateTime.GetDaysEnd().Ticks);
                        TimeSpan tsDiff2 = tsEnd2.Subtract(tsStart2).Duration();
                        if (tsDiff.Days > 0 || tsDiff2.Days > 0)
                        {
                            throw new ApiException("访客时间范围不能跨天");
                        }
                        Tuple<DateTime, DateTime>[] ranges = DbOpe_crm_schedule.Instance.SearchCurrentUserAllScheduleVisitorTimeRanges(id);
                        if (ranges is not null && ranges is { Length: > 0 })
                        {
                            Tuple<DateTime, DateTime> source = Tuple.Create(param.Item1.Value.LocalDateTime, param.Item2.Value.LocalDateTime);
                            var checkResult = CheckDateOverlapMultiple(source, ranges);
                            if (checkResult)
                            {
                                throw new ApiException("当前访客时间和其他日程的访客时间冲突!");
                            }
                        }
                        return true;
                    }
                    throw new ApiException("访客时间范围必须大于5分钟");
                }
                throw new ApiException("访客结束时间不可为空");
            }
            throw new ApiException("访客开始时间不可为空");
        }

        /// <summary>
        /// 比较时间区间参数在一些时间区间列表中是否重叠
        /// </summary>
        /// <param name="source">要比较的日期区间</param>
        /// <param name="ranges">比较的日期区间列表</param>
        /// <returns>true == 重叠, false == 没有重叠</returns>
        private bool CheckDateOverlapMultiple(Tuple<DateTime, DateTime> source, Tuple<DateTime, DateTime>[]? ranges)
        {
            _ = source ?? throw new ArgumentException("要比较的时间区间不可为空!");
            if (ranges is not null && ranges is { Length: > 0 })
            {
                foreach (var item in ranges)
                {
                    if (source.Item2 < item.Item1 || item.Item2 < source.Item1) continue;
                    if (source.Item1 < item.Item1 && item.Item1 <= source.Item2) return true;
                    if (source.Item1 <= item.Item2 && item.Item2 < source.Item2) return true;
                    if (source.Item1 < item.Item2 && source.Item2 == item.Item1) return true;
                    if (source.Item1 <= item.Item2 && item.Item2 <= source.Item2) return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 根据传入的访客时间生成访客时间
        /// 若传入的访客时间被占用则自动后移一小时到当日PM：17：00开始，自动切换到次日工作日
        /// 用于签约客户回访、预计到账计划自动生成访客时间
        /// </summary>
        /// <param name="source">传入的访客时间</param>
        /// <returns>生成的访客时间</returns>
        public Tuple<DateTime, DateTime> GenerateVisitorTime(Tuple<DateTime, DateTime> source)
        {
            //查找当前登录人所有的日程计划区间
            Tuple<DateTime, DateTime>[] ranges = DbOpe_crm_schedule.Instance.SearchCurrentUserAllScheduleVisitorTimeRanges();
            if (ranges is not null && ranges is { Length: > 0 })
            {
                for (; ; )
                {
                    (DateTime result, bool isConform) = GenerateWeekdayDateTime(source.Item1);
                    if (!isConform)
                    {
                        source = Tuple.Create(result, result.AddHours(1));
                    }
                    var checkResult = CheckDateOverlapMultiple(source, ranges);
                    if (!checkResult)
                    {
                        return source;
                    }
                    source = Tuple.Create(source.Item1.AddHours(1), source.Item2.AddHours(1));
                }
            }
            else
            {
                (DateTime result, bool isConform) = GenerateWeekdayDateTime(source.Item1);
                if (!isConform)
                {
                    source = Tuple.Create(result, result.AddHours(1));
                }
                return source;
            }
        }

        /// <summary>
        /// 生成工作日时间
        /// 若传入的时间是工作日时间并且没超过PM：17：00 则返回
        /// 若传入的时间非工作日或是工作日但超过了PM：17：00 则生成新的工作日时间
        /// 新的工作日时间为下一个工作日的AM: 9:00
        /// </summary>
        /// <param name="source"></param>
        /// <returns>
        /// source 返回的时间
        /// isConform 指示传入的时间是否符合规定
        /// </returns>
        private (DateTime source, bool isConform) GenerateWeekdayDateTime(DateTime source)
        {
            string dayOfWeek = source.DayOfWeek.ToString();
            if (!"Saturday".Equals(dayOfWeek) && !"Sunday".Equals(dayOfWeek) && source.Hour < 17)
            {
                return (source, true);
            }
            TimeSpan time = new TimeSpan(9, 0, 0);
            if ("Saturday".Equals(dayOfWeek)) //周六
            {
                source = source.AddDays(2).Date + time;
            }
            else if (("Sunday").Equals(dayOfWeek))   //周日
            {
                source = source.AddDays(1).Date + time;
            }
            else if (source.Hour >= 17)
            {
                if ("Friday".Equals(dayOfWeek)) //周五
                {
                    source = source.AddDays(3).Date + time;
                }
                else
                {
                    source = source.AddDays(1).Date + time;
                }
            }
            return (source, false);
        }

        /// <summary>
        /// 添加日程计划过期的定时任务
        /// </summary>
        /// <param name="schedule"></param>
        public void AddTimingRefreshScheduleTask(Db_crm_schedule schedule)
        {
            if (schedule.VisitorTimeEnd.Value < DateTime.Now)
            {
                DbOpe_crm_schedule.Instance.ExpiredSchedule(schedule);    //状态修改为过期
                return;
            }
            string cronExpression = ScheduledTaskUtil.GetCronByDateTime(schedule.VisitorTimeEnd.Value);
            string jobName = $"{ScheduleTaskPrefix}{schedule.Id}";
            
            if (ScheduledTaskUtil.CheckCronExpression(cronExpression))
            {
                SafeCronUtil.AddCronJob(jobName, () =>
                {
                    DbOpe_crm_schedule.Instance.ExpiredSchedule(schedule);    //状态修改为过期
                    Debug.WriteLine($"{jobName}任务执行完成！ \t {DateTime.Now}");
                }, cronExpression);
            }
        }

        /// <summary>
        /// 获取消息通知所需要的日程计划
        /// 明日日程计划提醒-工作日PM 4.30主动发起提醒
        /// 今日日程计划提醒{单日日程提前1小时提醒}）
        /// 日程计划即将过期提醒{快结束15分钟提醒}
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, List<Db_crm_schedule>> GetScheduleMessageNotify()
        {
            Dictionary<string, (DateTime start, DateTime end)> conditionDict = new();
            DateTime now = DateTime.Now;
            TimeSpan span = new TimeSpan(16, 30, 00);
            if (now > (now.Date + span))
            {
                //查询明日
                DateTime start = now.AddDays(1).Date + new TimeSpan(0, 0, 0);
                DateTime end = now.AddDays(1).Date + new TimeSpan(23, 59, 59);
                conditionDict["tomorrow"] = (start, end);
            }
            var after1Hour = now.AddHours(1);   //1小时之后的时间
            conditionDict["today"] = (now, after1Hour);
            var after15Minute = now.AddMinutes(15); //15分钟之后的时间
            conditionDict["expire"] = (now, after15Minute);
            return DbOpe_crm_schedule.Instance.GetScheduleMessageNotify(conditionDict);
        }

        /// <summary>
        /// 获取明天未读的日程计划提醒
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessageReminder> GetUNReadTomorrowScheduleMessage()
        {
            return DbOpe_crm_schedule.Instance.GetUNReadTomorrowScheduleMessage();
        }

        /// <summary>
        /// 获取今天未读的日程计划提醒
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessageReminder> GetUNReadTodayScheduleMessage()
        {
            return DbOpe_crm_schedule.Instance.GetUNReadTodayScheduleMessage();
        }

        /// <summary>
        /// 获取即将过期的日程计划提醒
        /// </summary>
        /// <returns></returns>
        public List<VM_Messages.MessageReminder> GetUNReadExpireScheduleMessage()
        {
            return DbOpe_crm_schedule.Instance.GetUNReadExpireScheduleMessage();
        }
    }
}