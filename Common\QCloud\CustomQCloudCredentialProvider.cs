﻿using COSXML;
using COSXML.Auth;
using CRM2_API.Common.AppSetting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace QCloud
{
    public class CustomQCloudCredentialProvider
    {
        private static CosXml cosXml;
        // 用于锁定
        private static readonly object _lock = new object();
        private CustomQCloudCredentialProvider()
        {
        }
        // 公共静态方法提供全局访问点来获取 cosXml 实例
        public static CosXml getInstance()
        {
            try
            {
                if (cosXml == null)
                {            // 使用 lock 语句确保线程安全
                    lock (_lock)
                    {
                        //初始化 CosXmlConfig 
                        string region = AppSettings.QCloud.Region; //存储桶地域
                        CosXmlConfig config = new CosXmlConfig.Builder()
                          .IsHttps(true)  //设置默认 HTTPS 请求
                          .SetRegion(region)  //设置一个默认的存储桶地域
                          .SetDebugLog(true)  //显示日志
                          .SetHost(AppSettings.QCloud.Host)
                          .Build();  //创建 CosXmlConfig 对象
                        string secretId = AppSettings.QCloud.SecretId; //用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
                        string secretKey = AppSettings.QCloud.SecretKey; //用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
                        long durationSecond = 600;  //每次请求签名有效时长，单位为秒
                        QCloudCredentialProvider cosCredentialProvider = new DefaultQCloudCredentialProvider(
                          secretId, secretKey, durationSecond);
                        cosXml = new CosXmlServer(config, cosCredentialProvider);

                    }
                }
            }
            catch (Exception e)
            { 
               throw new Exception("初始化文件服务链接失败", e);
            }
            return cosXml;
        }
    }
}
