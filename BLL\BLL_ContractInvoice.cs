﻿using Aspose.Cells;
using CRM2_API.BLL.Common;
using CRM2_API.BLL.Common.Com_EmailHelper;
using CRM2_API.BLL.Schedule;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using System.Diagnostics.Contracts;
using System.IO;
using System.Security.Cryptography.Xml;
using System.Web;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_OriginalInvoice;
using Spire.Doc;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoicingDetails;
using System.Text.RegularExpressions;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Utils;

namespace CRM2_API.BLL
{
    public class BLL_ContractInvoice : BaseBLL<BLL_ContractInvoice>
    {
        private void Validating(AuditContractInvoiceAppl_In auditContractInvoiceApplIn)
        {
            if (auditContractInvoiceApplIn.SubmitType == EnumAuditStatus.Refuse.ToInt())
            {
                if (auditContractInvoiceApplIn.Feedback == null)
                {
                    throw new ApiException("审核反馈意见不可为空");
                }
            }
            if (auditContractInvoiceApplIn.SubmitType == EnumAuditStatus.Pass.ToInt())
            {
                if (auditContractInvoiceApplIn.InvoicingForm == EnumInvoicingForm.PaperInvoice.ToInt())
                {
                    if (auditContractInvoiceApplIn.CourierCompany == null)
                    {
                        throw new ApiException("快递公司不可为空");
                    }
                    if (auditContractInvoiceApplIn.CourierNumber == null)
                    {
                        throw new ApiException("快递单号不可为空");
                    }
                }

                if (auditContractInvoiceApplIn.InvoicingForm == EnumInvoicingForm.ElectronicInvoice.ToInt())
                {
                    //if (auditContractInvoiceApplIn.InvoiceLink == null)
                    //{
                    //    throw new ApiException("发票链接不可为空");
                    //}
                }

                if (auditContractInvoiceApplIn.BillingCompany.IsNullOrEmpty())
                {
                    throw new ApiException("开票公司不可为空");
                }
                if (auditContractInvoiceApplIn.InvoicingForm == null || auditContractInvoiceApplIn.InvoicingForm == 0)
                {
                    throw new ApiException("开票形式不可为空");
                }
                if (auditContractInvoiceApplIn.InvoicingDetails.IsNullOrEmpty())
                {
                    throw new ApiException("开票明细不可为空");
                }
                if (auditContractInvoiceApplIn.InvoicingDate == null)
                {
                    throw new ApiException("开票日期不可为空");
                }
                if (auditContractInvoiceApplIn.InvoicedAmount == null || auditContractInvoiceApplIn.InvoicedAmount == 0)
                {
                    throw new ApiException("开票金额不可为空");
                }
                //if (auditContractInvoiceApplIn.OriginalInvoice == null)
                //{
                //    throw new ApiException("发票原件不可为空");
                //}
                //if (auditContractInvoiceApplIn.InvoiceInfo == null || auditContractInvoiceApplIn.InvoiceInfo.Count == 0)
                //{
                //    throw new ApiException("发票编号不可为空");
                //}

            }
        }

        private void Validating(ContractInvoiceAppl_In contractInvoiceApplIn)
        {
            if (contractInvoiceApplIn.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
            {
                if (contractInvoiceApplIn.Email.IsNullOrEmpty())
                {
                    //throw new ApiException("电子邮箱不可为空");
                }
            }
            else
            {
                //if (contractInvoiceApplIn.BankDeposit.IsNullOrEmpty())
                //{
                //    throw new ApiException("开户行不可为空");
                //}
                //if (contractInvoiceApplIn.BankAccount.IsNullOrEmpty())
                //{
                //    throw new ApiException("银行账号不可为空");
                //}
                //if (contractInvoiceApplIn.BankNum.IsNullOrEmpty())
                //{
                //    throw new ApiException("行号不可为空");
                //}
                //if (contractInvoiceApplIn.CreditCode.IsNullOrEmpty())
                //{
                //    throw new ApiException("信用代码不可为空");
                //}
                //if (contractInvoiceApplIn.TaxNumber.IsNullOrEmpty())
                //{
                //    throw new ApiException("税务登记号不可为空");
                //}
                if (contractInvoiceApplIn.ReceiveEmail.IsNullOrEmpty())
                {
                    throw new ApiException("收票邮箱不可为空");
                }
            }
        }

        /// <summary>
        /// 添加合同发票申请
        /// </summary>
        public void AddContractInvoiceAppl(AddContractInvoiceAppl_In addContractInvoiceApplIn)
        {
            DbOpe_crm_contract_invoiceappl.Instance.TransDeal(() =>
            {

                Validating(addContractInvoiceApplIn);


                //240912 合同、发票、到账权限更改
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(addContractInvoiceApplIn.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("未找到合同或该用户没有合同权限");
                }
                if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
                {
                    throw new ApiException("当前合同状态下，不可以申请发票");
                }

                //合同已开票金额
                decimal InvoicedAmount = GetInvoicedAmountByContractId(addContractInvoiceApplIn.ContractId);
                //合同已申请未开票金额
                decimal InvoicedAmountAppl = GetInvoicedAmountApplByContractId(addContractInvoiceApplIn.ContractId);
                //合同金额
                //合同金额
                decimal ContractAmount = 0;
                if (contract.Currency == EnumCurrency.CNY.ToInt())
                {
                    ContractAmount = contract.ContractAmount.ToDecimal();
                }
                else
                {
                    ContractAmount = contract.FCContractAmount.ToDecimal();
                }
                //decimal ContractAmount = contract.ContractAmount.ToDecimal();
                if (addContractInvoiceApplIn.InvoicedAmount > ContractAmount - InvoicedAmount - InvoicedAmountAppl)
                {
                    throw new ApiException("开票金额大于剩余开票金额，不可以申请发票");
                }
                if (ContractAmount - InvoicedAmount - InvoicedAmountAppl <= 0)
                {
                    throw new ApiException("开票金额大于剩余开票金额，不可以申请发票");
                }

                int total = 0;
                var CreditCode = string.Empty;
                var infos = new List<Db_crm_tianyancha_info>();
                if (addContractInvoiceApplIn.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
                {//如果是形式发票，取消全英文的验证
                    infos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(addContractInvoiceApplIn.BillingHeaderName, false, ref total);
                    if (infos.Count == 0)
                    {
                        CreditCode = DateTime.Now.ToString("yyyyMMddHHmmssff") + new Random().Next(10) + "X";
                    }
                }
                else
                {//普票或专票，要限制开票抬头不可以是全英文
                    infos = BLL_Tianyancha.Instance.QueryTianyanchaWithEnStrCheck(addContractInvoiceApplIn.BillingHeaderName, true, ref total);
                }
                if (string.IsNullOrEmpty(CreditCode))
                {
                    var exactCompany = infos.Find(i => i.CompanyName == addContractInvoiceApplIn.BillingHeaderName);
                    var historyCompany = infos.Find(i => i.HistoryNames != null && i.HistoryNames.Split("|").Contains(addContractInvoiceApplIn.BillingHeaderName));
                    if (exactCompany != null)
                    {
                        //名称输入与天眼查一致
                        CreditCode = exactCompany.CreditCode;
                    }
                    else if (historyCompany != null)
                    {
                        var index = historyCompany.HistoryNames.Split("|").ToList().IndexOf(addContractInvoiceApplIn.BillingHeaderName);
                        //输入的是曾用名 特殊处理编码
                        CreditCode = "$$" + index + historyCompany.CreditCode;
                    }
                    else
                    {
                        throw new ApiException("未找到发票抬头公司信息，请检查输入的公司名称是否正确且完整");
                    }
                }





                //获取用户信息
                var user = DbOpe_sys_user.Instance.GetDbSysUserById(UserId);
                //获取组织信息树，从当前组织追溯出所有上级组织
                var orgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                string OrgDivisionId = Guid.Empty.ToString();
                string OrgDivisionName = "";
                string OrgBrigadeId = Guid.Empty.ToString();
                string OrgBrigadeName = "";
                string OrgRegimentId = Guid.Empty.ToString();
                string OrgRegimentName = "";
                foreach (var org in orgList)
                {
                    switch (org.OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            OrgDivisionId = org.Id;
                            OrgDivisionName = org.OrgName;
                            break;
                        case EnumOrgType.Battalion:
                            OrgBrigadeId = org.Id;
                            OrgBrigadeName = org.OrgName;
                            break;
                        case EnumOrgType.Squadron:
                            OrgRegimentId = org.Id;
                            OrgRegimentName = org.OrgName;
                            break;
                    }
                }

                Guid contractInvoiceApplId = DbOpe_crm_contract_invoiceappl.Instance.AddContractInvoiceAppl(addContractInvoiceApplIn, OrgDivisionId, OrgDivisionName, OrgBrigadeId, OrgBrigadeName, OrgRegimentId, OrgRegimentName, CreditCode);
                if (addContractInvoiceApplIn.InvoiceAttachment != null && addContractInvoiceApplIn.InvoiceAttachment.Count > 0)
                {
                    //FormFileCollection InvoiceAttachment = Tools.GetUploadInfoFile(addContractInvoiceApplIn.InvoiceAttachment);// new FormFileCollection();
                    ////foreach (var item in addContractInvoiceApplIn.InvoiceAttachment)
                    ////{
                    ////    InvoiceAttachment.Add(item.File);
                    ////}
                    Util<DbOpe_crm_contract_invoiceappl_attachment, BM_AttachFile> invoiceapplAttachFile = new Util<DbOpe_crm_contract_invoiceappl_attachment, BM_AttachFile>(DbOpe_crm_contract_invoiceappl_attachment.Instance);
                    invoiceapplAttachFile.UploadFile(addContractInvoiceApplIn.InvoiceAttachment, EnumAttachFileType.ContractInvoiceAppl.ToString(), UserId, contractInvoiceApplId.ToString());
                }

                if (addContractInvoiceApplIn.AuditStatus == EnumAuditStatus.Draft.ToInt())
                {

                }
                if (addContractInvoiceApplIn.AuditStatus == EnumAuditStatus.Submit.ToInt())
                {
                    if (addContractInvoiceApplIn.ExpectedInvoicingTime != null)
                    {
                        if (addContractInvoiceApplIn.ExpectedInvoicingTime.Value.Date != DateTime.Now.Date)
                        {
                            Tuple<DateTime, DateTime> visitorTime = BLL_Schedule.Instance.GenerateVisitorTime(Tuple.Create(addContractInvoiceApplIn.ExpectedInvoicingTime.Value, addContractInvoiceApplIn.ExpectedInvoicingTime.Value.AddHours(1)));
                            //日程计划 start 
                            BLL_Schedule.Instance.AddSchedule(new()
                            {
                                TrackingType = EnumTrackingType.TelephoneCommunication,
                                TrackingPurpose = EnumTrackingPurpose.SignContract,
                                TrackingStage = EnumTrackingStage.Received,
                                TrackingEvents = EnumTrackingEvents.InvoiceReminder,
                                Remark = "开具发票自动生成的日程计划",
                                UserId = contract.Issuer,
                                CustomerDataSource = EnumCustomerDataSource.Private,
                                CustomerId = contract.CustomerId,
                                ContractId = addContractInvoiceApplIn.ContractId,
                                VisitorTimeStart = visitorTime.Item1,//addContractInvoiceApplIn.ExpectedInvoicingTime,
                                VisitorTimeEnd = visitorTime.Item2 //addContractInvoiceApplIn.ExpectedInvoicingTime.Value.AddHours(1)
                            }, false, true, contract.FirstParty, false, contractInvoiceApplId.ToString());
                            //日程计划 end
                        }
                    }

                    Guid InvoiceAudit = DbOpe_crm_contract_invoiceaudit.Instance.AddContractInvoiceAudit(addContractInvoiceApplIn.ContractId, contractInvoiceApplId.ToString(), UserId);
                    //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(addContractInvoiceApplIn.ContractId);
                    int state = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplIsinvoice(contractInvoiceApplId.ToString()).IsInvoice;
                    string dataState = Dictionary.IsInvoice.First(e => e.Value == state.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.AddWorkFlow<AddContractInvoiceAppl_In, Db_crm_contract>("发票申请审批流程", contractInvoiceApplId.ToString(), addContractInvoiceApplIn, contract, addContractInvoiceApplIn.InvoiceRemark, dataState, "新建");


                    if (addContractInvoiceApplIn.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
                    {
                        Db_sys_form form = DbOpe_sys_form.Instance.GetData(r => r.MethodName == "AuditContractInvoiceAppl" && r.ControllerName == "ContractInvoice");
                        Com_SysForm.Instance.FormId = form.Id;
                        //List<InvoicingDetails_Out> InvoicingDetails =DbOpe_crm_contract_invoice_invoicingdetails_item.Instance.GetInvoicingDetails();
                        //if (InvoicingDetails.Count == 0)
                        //{
                        //    throw new ApiException("开票明细为空,形式发票无法自动通过");
                        //}
                        string FormalInvoiceNumber = GetFormalInvoiceNumber();

                        AuditContractInvoiceAppl_In auditContractInvoiceApplIn = new AuditContractInvoiceAppl_In();
                        auditContractInvoiceApplIn.Id = InvoiceAudit.ToString();
                        auditContractInvoiceApplIn.SubmitType = EnumAuditStatus.Pass.ToInt();
                        auditContractInvoiceApplIn.Feedback = "形式发票自动通过";
                        auditContractInvoiceApplIn.BillingCompany = addContractInvoiceApplIn.BillingCompany;
                        auditContractInvoiceApplIn.InvoicingForm = 2;
                        auditContractInvoiceApplIn.InvoicingDetails = addContractInvoiceApplIn.ApplInvoicingDetails;//InvoicingDetails.First().Id;
                        auditContractInvoiceApplIn.InvoicingDate = DateTime.Now;
                        auditContractInvoiceApplIn.InvoicedAmount = addContractInvoiceApplIn.InvoicedAmount;
                        auditContractInvoiceApplIn.FormalInvoiceNumber = FormalInvoiceNumber;

                        AuditContractInvoiceAppl(auditContractInvoiceApplIn);
                    }

                }
            });
        }

        /// <summary>
        /// 修改合同发票申请
        /// </summary>
        public void UpdateContractInvoiceAppl(UpdateContractInvoiceAppl_In updateContractInvoiceApplIn)
        {
            DbOpe_crm_contract_invoiceappl.Instance.TransDeal(() =>
            {
                Validating(updateContractInvoiceApplIn);

                //240912 合同、发票、到账权限更改
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(updateContractInvoiceApplIn.ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("未找到合同或该用户没有合同权限");
                }
                if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
                {
                    throw new ApiException("当前合同状态下，不可以申请发票");
                }
                Db_crm_contract_invoiceappl invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(updateContractInvoiceApplIn.Id);
                if (invoiceappl.AuditStatus != EnumAuditStatus.Refuse.ToInt() && invoiceappl.AuditStatus != EnumAuditStatus.Draft.ToInt())
                {
                    //形式发票在合同未匹配到账之前可以修改（重开一张新的形式发票）
                    if (invoiceappl.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt() && invoiceappl.AuditStatus == EnumAuditStatus.Pass.ToInt())
                    {
                        //List<Db_crm_contract_receiptregister> receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetConfirmedContractReceiptRegisterByContractId(updateContractInvoiceApplIn.ContractId);
                        //if (receiptregister.Count > 0)
                        bool IsHaveContractCollectionInfoAutoMatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(updateContractInvoiceApplIn.ContractId);
                        if (IsHaveContractCollectionInfoAutoMatching)
                        {
                            throw new ApiException("当前合同发票状态下，不可以修改申请发票");
                        }
                    }
                    else
                    {
                        throw new ApiException("当前合同发票状态下，不可以修改申请发票");
                    }
                }

                if (invoiceappl.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
                {
                    //存在形式发票的先删除，重新开
                    DbOpe_crm_contract_invoice.Instance.DeleteContractInvoiceByInvoiceApplId(updateContractInvoiceApplIn.Id);
                    int InvoicingStatus = EnumInvoicingStatus.NotInvoiced.ToInt();
                    int IsSignReceiving = EnumIsSignReceiving.NotSentOut.ToInt();
                    int IsReminder = EnumIsReminder.UnUrgedTicket.ToInt();
                    int RefundStatus = EnumRefundStatus.NotRefunded.ToInt();
                    int AuditStatus = EnumAuditStatus.Draft.ToInt();
                    DbOpe_crm_contract_invoiceappl.Instance.UpdateData(r => new Db_crm_contract_invoiceappl() { InvoicingStatus = InvoicingStatus, AuditStatus = AuditStatus, IsReminder = IsReminder, IsSignReceiving = IsSignReceiving }, invoiceappl.Id);
                }

                //合同已开票金额
                decimal InvoicedAmount = GetInvoicedAmountByContractId(updateContractInvoiceApplIn.ContractId);
                //合同已申请未开票金额
                decimal InvoicedAmountAppl = GetInvoicedAmountApplByContractIdAndApplId(updateContractInvoiceApplIn.ContractId, updateContractInvoiceApplIn.Id);
                //合同金额
                //合同金额
                decimal ContractAmount = 0;
                if (contract.Currency == EnumCurrency.CNY.ToInt())
                {
                    ContractAmount = contract.ContractAmount.ToDecimal();
                }
                else
                {
                    ContractAmount = contract.FCContractAmount.ToDecimal();
                }
                //decimal ContractAmount = contract.ContractAmount.ToDecimal();
                if (updateContractInvoiceApplIn.InvoicedAmount > ContractAmount - InvoicedAmount - InvoicedAmountAppl)
                {
                    throw new ApiException("开票金额大于剩余开票金额，不可以申请发票");
                }
                if (ContractAmount - InvoicedAmount - InvoicedAmountAppl <= 0)
                {
                    throw new ApiException("开票金额大于剩余开票金额，不可以申请发票");
                }

                DbOpe_crm_contract_invoiceappl.Instance.UpdateData(updateContractInvoiceApplIn);


                //FormFileCollection InvoiceAttachment = Tools.GetUploadInfoFile(updateContractInvoiceApplIn.InvoiceAttachment);//new FormFileCollection();
                //List<BM_FileInfo> InvoiceAttachmentInfo = Tools.GetUploadInfoId(updateContractInvoiceApplIn.InvoiceAttachment);//new List<BM_FileInfo>();
                ////foreach (var item in updateContractInvoiceApplIn.InvoiceAttachment)
                ////{
                ////    if (item.Id != null)
                ////    {
                ////        if (item.Id != "")
                ////        {
                ////            InvoiceAttachmentInfo.Add(new BM_FileInfo { Id = item.Id });
                ////        }
                ////    }
                ////    if (item.File != null)
                ////    {
                ////        InvoiceAttachment.Add(item.File);
                ////    }
                ////}

                DbOpe_crm_contract_invoiceappl_attachment.Instance.DeleteInvoiceApplAttachFileList(updateContractInvoiceApplIn.InvoiceAttachmentInfo, updateContractInvoiceApplIn.Id);
                if (updateContractInvoiceApplIn.InvoiceAttachment != null && updateContractInvoiceApplIn.InvoiceAttachment.Count > 0)
                {
                    Util<DbOpe_crm_contract_invoiceappl_attachment, BM_AttachFile> invoiceapplAttachFile = new Util<DbOpe_crm_contract_invoiceappl_attachment, BM_AttachFile>(DbOpe_crm_contract_invoiceappl_attachment.Instance);
                    invoiceapplAttachFile.UploadFile(updateContractInvoiceApplIn.InvoiceAttachment, EnumAttachFileType.ContractInvoiceAppl.ToString(), UserId, updateContractInvoiceApplIn.Id);
                }

                if (updateContractInvoiceApplIn.AuditStatus == EnumAuditStatus.Draft.ToInt())
                {

                }
                if (updateContractInvoiceApplIn.AuditStatus == EnumAuditStatus.Submit.ToInt())
                {
                    //更新审核历史状态
                    DbOpe_crm_contract_invoiceaudit.Instance.UpdateIsHistoryByContractInvoiceApplId(updateContractInvoiceApplIn.Id);

                    if (updateContractInvoiceApplIn.ExpectedInvoicingTime != null)
                    {
                        if (updateContractInvoiceApplIn.ExpectedInvoicingTime.Value.Date != DateTime.Now.Date)
                        {
                            //修改 2024年1月19日 修改也需要更新日程计划
                            Tuple<DateTime, DateTime> visitorTime = BLL_Schedule.Instance.GenerateVisitorTime(Tuple.Create(updateContractInvoiceApplIn.ExpectedInvoicingTime.Value, updateContractInvoiceApplIn.ExpectedInvoicingTime.Value.AddHours(1)));
                            DbOpe_crm_schedule.Instance.UpdateSchedule4InvoiceAppl(visitorTime, updateContractInvoiceApplIn.Id);
                        }
                    }
                    //提交审核
                    Guid InvoiceAudit = DbOpe_crm_contract_invoiceaudit.Instance.AddContractInvoiceAudit(updateContractInvoiceApplIn.ContractId, updateContractInvoiceApplIn.Id, UserId);
                    //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(updateContractInvoiceApplIn.ContractId);
                    int state = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplIsinvoice(updateContractInvoiceApplIn.Id).IsInvoice;
                    string dataState = Dictionary.IsInvoice.First(e => e.Value == state.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.AddWorkFlow<UpdateContractInvoiceAppl_In, Db_crm_contract>("发票申请审批流程", updateContractInvoiceApplIn.Id, updateContractInvoiceApplIn, contract, updateContractInvoiceApplIn.InvoiceRemark, dataState, "修改");

                    if (updateContractInvoiceApplIn.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
                    {
                        ////存在形式发票的先删除，重新开
                        //DbOpe_crm_contract_invoice.Instance.DeleteContractInvoiceByInvoiceApplId(updateContractInvoiceApplIn.Id);

                        Db_sys_form form = DbOpe_sys_form.Instance.GetData(r => r.MethodName == "AuditContractInvoiceAppl" && r.ControllerName == "ContractInvoice");
                        Com_SysForm.Instance.FormId = form.Id;
                        //List<InvoicingDetails_Out> InvoicingDetails =DbOpe_crm_contract_invoice_invoicingdetails_item.Instance.GetInvoicingDetails();
                        //if (InvoicingDetails.Count == 0)
                        //{
                        //    throw new ApiException("开票明细为空,形式发票无法自动通过");
                        //}
                        string FormalInvoiceNumber = GetFormalInvoiceNumber();

                        AuditContractInvoiceAppl_In auditContractInvoiceApplIn = new AuditContractInvoiceAppl_In();
                        auditContractInvoiceApplIn.Id = InvoiceAudit.ToString();
                        auditContractInvoiceApplIn.SubmitType = EnumAuditStatus.Pass.ToInt();
                        auditContractInvoiceApplIn.Feedback = "形式发票自动通过";
                        auditContractInvoiceApplIn.BillingCompany = updateContractInvoiceApplIn.BillingCompany;
                        auditContractInvoiceApplIn.InvoicingForm = 2;
                        auditContractInvoiceApplIn.InvoicingDetails = updateContractInvoiceApplIn.ApplInvoicingDetails;//InvoicingDetails.First().Id;
                        auditContractInvoiceApplIn.InvoicingDate = DateTime.Now;
                        auditContractInvoiceApplIn.InvoicedAmount = updateContractInvoiceApplIn.InvoicedAmount;
                        auditContractInvoiceApplIn.FormalInvoiceNumber = FormalInvoiceNumber;

                        AuditContractInvoiceAppl(auditContractInvoiceApplIn);
                    }

                }
            });
        }

        /// <summary>
        /// 删除合同形式发票申请
        /// </summary>
        public void DeleteContractProformaTicketInvoiceApplByAppId(string id)
        {
            DbOpe_crm_contract_invoiceappl.Instance.TransDeal(() =>
            {
                GetContractInvoiceAppl_OUT invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplById(id, true);
                if (invoiceappl == null)
                {
                    throw new ApiException("该用户没有数据权限");
                }
                //240912 合同、发票、到账权限更改
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(invoiceappl._ContractId, true);
                if (contract == null)
                {
                    throw new ApiException("未找到合同或该用户没有合同权限");
                }
                if (invoiceappl.InvoiceType != EnumInvoiceType.ProformaTicket.ToInt())
                {
                    throw new ApiException("请选择形式发票");
                }
                if (invoiceappl.AuditStatus != EnumAuditStatus.Refuse.ToInt() && invoiceappl.AuditStatus != EnumAuditStatus.Draft.ToInt())
                {
                    //形式发票在合同未匹配到账之前可以修改（重开一张新的形式发票）
                    if (invoiceappl.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt() && invoiceappl.AuditStatus == EnumAuditStatus.Pass.ToInt())
                    {
                        //List<Db_crm_contract_receiptregister> receiptregister = DbOpe_crm_contract_receiptregister.Instance.GetConfirmedContractReceiptRegisterByContractId(updateContractInvoiceApplIn.ContractId);
                        //if (receiptregister.Count > 0)
                        bool IsHaveContractCollectionInfoAutoMatching = DbOpe_crm_contract_collectioninfo_automatching.Instance.IsHaveContractCollectionInfoAutoMatchingByContractId(invoiceappl.ContractId);
                        if (IsHaveContractCollectionInfoAutoMatching)
                        {
                            throw new ApiException("当前合同发票状态下，不可以删除形式发票申请");
                        }
                    }
                    else
                    {
                        throw new ApiException("当前合同发票状态下，不可以删除发票申请");
                    }
                }
                if (invoiceappl.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
                {
                    //存在形式发票的先删除，重新开
                    DbOpe_crm_contract_invoice.Instance.DeleteContractInvoiceByInvoiceApplId(id);
                    int InvoicingStatus = EnumInvoicingStatus.NotInvoiced.ToInt();
                    int IsSignReceiving = EnumIsSignReceiving.NotSentOut.ToInt();
                    int IsReminder = EnumIsReminder.UnUrgedTicket.ToInt();
                    int RefundStatus = EnumRefundStatus.NotRefunded.ToInt();
                    int AuditStatus = EnumAuditStatus.Draft.ToInt();
                    DbOpe_crm_contract_invoiceappl.Instance.UpdateData(r => new Db_crm_contract_invoiceappl() { Deleted = true }, id);
                }
            });
        }

        /// <summary>
        /// 审核合同发票申请
        /// </summary>
        public void AuditContractInvoiceAppl(AuditContractInvoiceAppl_In auditContractInvoiceApplIn)
        {
            //验证输入项
            Validating(auditContractInvoiceApplIn);
            DbOpe_crm_contract_invoiceaudit.Instance.TransDeal(() =>
            {
                Db_crm_contract_invoiceappl invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplByInvoiceAuditId(auditContractInvoiceApplIn.Id);
                if (invoiceappl.AuditStatus != EnumAuditStatus.Submit.ToInt())
                {
                    throw new ApiException("当前状态不可以开票");
                }
                if (auditContractInvoiceApplIn.SubmitType == EnumAuditStatus.Pass.ToInt())
                {
                    if (invoiceappl.InvoiceType != EnumInvoiceType.ProformaTicket.ToInt())
                    {
                        if (auditContractInvoiceApplIn.OriginalInvoice == null)
                        {
                            throw new ApiException("发票原件不可为空");
                        }
                        //if (auditContractInvoiceApplIn.InvoiceInfo == null || auditContractInvoiceApplIn.InvoiceInfo.Count == 0)
                        //{
                        //    throw new ApiException("发票编号不可为空");
                        //}
                        if (auditContractInvoiceApplIn.ElectronicInvoiceNumber == null)
                        {
                            throw new ApiException("电子发票编号不可为空");
                        }

                        List<Db_crm_contract_invoice> invoice = DbOpe_crm_contract_invoice.Instance.GetDataList(r => r.ElectronicInvoiceNumber == auditContractInvoiceApplIn.ElectronicInvoiceNumber).ToList();
                        if (invoice.Count > 0)
                        {
                            throw new ApiException("电子发票编号已存在");
                        }
                    }
                }

                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(invoiceappl.ContractId);
                //设置开票状态
                int InvoicingStatus = EnumInvoicingStatus.NotInvoiced.ToInt();
                if (auditContractInvoiceApplIn.SubmitType == EnumAuditStatus.Pass.ToInt())
                {
                    //合同已开票金额
                    decimal InvoicedAmount = GetInvoicedAmountByContractId(invoiceappl.ContractId);
                    //合同金额
                    decimal ContractAmount = 0;
                    if (contract.Currency == EnumCurrency.CNY.ToInt())
                    {
                        ContractAmount = contract.ContractAmount.ToDecimal();
                    }
                    else
                    {
                        ContractAmount = contract.FCContractAmount.ToDecimal();
                    }
                    //开票金额
                    decimal InvoicingAmount = auditContractInvoiceApplIn.InvoicedAmount.Value;
                    //验证开票金额
                    //if (InvoicingAmount > (ContractAmount - InvoicedAmount))
                    //{
                    //    throw new ApiException("开票金额大于剩余开票金额，不可以开发票");
                    //}
                    if (ContractAmount - InvoicedAmount <= 0)
                    {
                        throw new ApiException("开票金额大于剩余开票金额，不可以申请发票");
                    }

                    if (InvoicingAmount >= ContractAmount - InvoicedAmount)
                    {
                        InvoicingStatus = EnumInvoicingStatus.Invoiced.ToInt();
                    }
                    else
                    {
                        InvoicingStatus = EnumInvoicingStatus.PartialInvoiced.ToInt();
                    }
                }

                if (auditContractInvoiceApplIn.SubmitType == EnumAuditStatus.Pass.ToInt())
                {
                    if (invoiceappl.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
                    {
                        if (auditContractInvoiceApplIn.FormalInvoiceNumber != "")
                        {
                            List<Db_crm_contract_invoice> FormalInvoiceList = DbOpe_crm_contract_invoice.Instance.GetDataList(r => r.FormalInvoiceNumber == auditContractInvoiceApplIn.FormalInvoiceNumber);
                            if (FormalInvoiceList.Count > 0)
                            {
                                throw new ApiException("当前形式发票编号已存在,请重新生成形式发票编号");
                            }
                        }
                    }
                }

                //int UnUrgedTicket = EnumIsReminder.UnUrgedTicket.ToInt();
                ////保存审核状态
                //int SentOut = EnumIsSignReceiving.SentOut.ToInt();
                //DbOpe_crm_contract_invoiceappl.Instance.UpdateData(r => new Db_crm_contract_invoiceappl() { InvoicingStatus = InvoicingStatus, AuditStatus = auditContractInvoiceApplIn.SubmitType, IsReminder = UnUrgedTicket, IsSignReceiving = SentOut } , invoiceappl.Id);
                DbOpe_crm_contract_invoiceaudit.Instance.AuditContractInvoiceAudit(auditContractInvoiceApplIn.Id, auditContractInvoiceApplIn.Feedback, auditContractInvoiceApplIn.SubmitType, UserId);

                //开票则创建正式发票
                if (auditContractInvoiceApplIn.SubmitType == EnumAuditStatus.Pass.ToInt())
                {
                    Guid contractInvoiceId = DbOpe_crm_contract_invoice.Instance.AddContractInvoice(auditContractInvoiceApplIn, invoiceappl.ContractId, invoiceappl.Id);
                    if (auditContractInvoiceApplIn.OriginalInvoice != null && auditContractInvoiceApplIn.OriginalInvoice.Count > 0)
                    {
                        Util<DbOpe_crm_contract_invoice_attachment, BM_AttachFile> invoiceAttachFile = new Util<DbOpe_crm_contract_invoice_attachment, BM_AttachFile>(DbOpe_crm_contract_invoice_attachment.Instance);
                        invoiceAttachFile.UploadFile(auditContractInvoiceApplIn.OriginalInvoice, EnumAttachFileType.ContractInvoice.ToString(), UserId, contractInvoiceId.ToString());
                    }

                    //if (invoiceappl.InvoiceType != EnumInvoiceType.ProformaTicket.ToInt())
                    //{
                    //    DbOpe_crm_contract_invoice_item.Instance.AddContractInvoiceItem(auditContractInvoiceApplIn.InvoiceInfo, invoiceappl.ContractId, invoiceappl.Id, contractInvoiceId.ToString(), UserId);
                    //    List<string> InvoiceNos = auditContractInvoiceApplIn.InvoiceInfo.Select(r => r.InvoiceNo).ToList();
                    //    if (InvoiceNos.Count > 0)
                    //    {
                    //        DbOpe_crm_original_invoice_details_entity.Instance.UpdateData(r => new Db_crm_original_invoice_details_entity { State = EnumInvoiceEntityState.Used }, r => InvoiceNos.Contains(r.Id));
                    //    }
                    //}  

                    int UnUrgedTicket = EnumIsReminder.UnUrgedTicket.ToInt();
                    //保存审核状态
                    int SentOut = EnumIsSignReceiving.SentOut.ToInt();
                    DbOpe_crm_contract_invoiceappl.Instance.UpdateData(r => new Db_crm_contract_invoiceappl() { InvoicingStatus = InvoicingStatus, AuditStatus = auditContractInvoiceApplIn.SubmitType, IsReminder = UnUrgedTicket, IsSignReceiving = SentOut }, invoiceappl.Id);

                    if (invoiceappl.InvoiceType != EnumInvoiceType.ProformaTicket.ToInt())
                    {
                        List<BM_FileInfo> fileList = DbOpe_crm_contract_invoice_attachment.Instance.GetDataList(r => r.ContractInvoiceId == contractInvoiceId.ToString()).Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();
                        Com_EmailHelper.SendElectronicInvoiceServiceEmail(invoiceappl.ReceiveEmail, "环球慧思电子发票", fileList);
                    }


                    MessageMainInfo message = new MessageMainInfo();
                    message.Issuer = contract.Issuer;
                    message.MessageTypeToId = invoiceappl.Id;//contract.Id;
                    message.MessagemMainAboutDes = contract.ContractName;
                    MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.InvoiceAbout, EnumMessageStepInfo.Review, EnumMessageStateInfo.Pass);
                    BLL_MessageCenter.Instance.RealTimeSend(giveBack);
                }
                else
                {
                    int UnUrgedTicket = EnumIsReminder.UnUrgedTicket.ToInt();
                    //保存审核状态
                    DbOpe_crm_contract_invoiceappl.Instance.UpdateData(r => new Db_crm_contract_invoiceappl() { InvoicingStatus = InvoicingStatus, AuditStatus = auditContractInvoiceApplIn.SubmitType, IsReminder = UnUrgedTicket }, invoiceappl.Id);

                    MessageMainInfo message = new MessageMainInfo();
                    message.Issuer = contract.Issuer;
                    message.MessageTypeToId = invoiceappl.Id;//contract.Id;
                    message.MessagemMainAboutDes = contract.ContractName;
                    message.LocalFeedBack = auditContractInvoiceApplIn.Feedback;
                    MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.InvoiceAbout, EnumMessageStepInfo.Review, EnumMessageStateInfo.Refus);
                    BLL_MessageCenter.Instance.RealTimeSend(giveBack);
                }

                invoiceappl.AuditStatus = auditContractInvoiceApplIn.SubmitType;

                int state = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplIsinvoice(invoiceappl.Id).IsInvoice;
                string dataState = Dictionary.IsInvoice.First(e => e.Value == state.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_invoiceappl, Db_crm_contract>("发票申请审批流程", invoiceappl.Id, invoiceappl, contract, auditContractInvoiceApplIn.Feedback, dataState, "审核");
            });
        }

        /// <summary>
        /// 根据发票申请Id获取合同基本信息和发票申请信息
        /// </summary>
        public ContractInfoAndInvoiceAppl_Out GetContractInfoAndInvoiceApplByInvoiceApplId(string id)
        {
            ContractInfoAndInvoiceAppl_Out result = DbOpe_crm_contract_invoiceappl.Instance.GetContractInfoAndInvoiceApplByInvoiceApplId(id, true);
            if (result == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            result.CollectionInfo = BLL_ContractReceiptRegister.Instance.GetConfirmedReceiptDetailsByContractId(result.ContractId);
            return result;
        }

        /// <summary>
        /// 根据查询条件获取合同发票申请列表
        /// </summary>
        public ApiTableOut<SearchContractInvoiceAuditList_Out> SearchContractInvoiceAuditList(SearchContractInvoiceAuditList_In searchContractInvoiceAuditListIn)
        {
            int total = 0;
            return new ApiTableOut<SearchContractInvoiceAuditList_Out> { Data = DbOpe_crm_contract_invoiceappl.Instance.SearchContractInvoiceAuditList(searchContractInvoiceAuditListIn, UserId, ref total, true), Total = total };
        }

        /// <summary>
        /// 根据查询条件获取合同发票申请列表统计
        /// </summary>
        public SearchContractInvoiceAuditList_Sta_Out SearchContractInvoiceAuditListSta(SearchContractInvoiceAuditList_In searchContractInvoiceAuditListIn)
        {
            return DbOpe_crm_contract_invoiceappl.Instance.SearchContractInvoiceAuditListSta(searchContractInvoiceAuditListIn, UserId, true);
        }

        /// <summary>
        /// 根据合同Id获取合同信息和到账信息
        /// </summary>
        public ContractInfoAndReceipt_Out GetContractAndReceiptByContractId(string id)
        {
            ContractInfoAndReceipt_Out result = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(id, true);
            //if (result.Issuer != UserId)
            //{
            //    throw new ApiException("该用户没有数据权限");
            //}
            if (result == null)
            {
                throw new ApiException("该用户没有数据权限");
            }
            return result;
        }

        /// <summary>
        /// 根据合同发票申请Id获取合同发票信息和审核信息
        /// </summary>
        public ContractInvoiceAndAudit_Out GetContractInvoiceAndAuditByContractInvoiceApplId(string id)
        {
            ContractInvoiceAndAudit_Out result = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceAndAuditByContractInvoiceApplId(id, true);
            if (result == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return result;
        }

        /// <summary>
        /// 根据合同发票申请Id获取合同信息和发票信息、发票申请信息
        /// </summary>
        public ContractInfoAndInvoiceInfo_Out GetContractInfoAndInvoiceInfoByInvoiceApplId(string id)
        {
            ContractInfoAndInvoiceInfo_Out result = DbOpe_crm_contract_invoiceappl.Instance.GetContractInfoAndInvoiceInfoByInvoiceApplId(id, true);
            if (result == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return result;
        }

        /// <summary>
        /// 发起退票
        /// </summary>
        public void InitiateContractRefundInvoice(ContractRefundInvoice_In contractRefundInvoice)
        {
            DbOpe_crm_contract_refundinvoice.Instance.TransDeal(() =>
            {
                Db_crm_contract_invoice invoice = DbOpe_crm_contract_invoice.Instance.GetContractInvoiceByInvoiceApplId(contractRefundInvoice.ContractInvoiceApplId);
                if (invoice == null)
                {
                    throw new ApiException("不存在发票，不可以退票");
                }
                Db_crm_contract_invoiceappl invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(contractRefundInvoice.ContractInvoiceApplId);
                if (invoiceappl.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
                {
                    throw new ApiException("形式发票不支持退票");
                }
                else
                {
                    if (String.IsNullOrEmpty(contractRefundInvoice.RefundInvoiceElectronicInvoiceNumber))
                    {
                        throw new ApiException("退票编号不可以为空");
                    }
                }
                Db_crm_contract_refundinvoice refund = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceInReviewByContractInvoiceAppl(contractRefundInvoice.ContractInvoiceApplId);
                if (refund != null)
                {
                    throw new ApiException("退票申请审核中，不可以退票");
                }

                //获取可退票金额
                decimal InvoicedAmount = GetInvoicedAmountByContractInvoiceApplId(invoice.ContractId, invoice.ContractInvoiceApplId);
                //if (contractRefundInvoice.InvoicedAmount > InvoicedAmount)
                //{
                //    throw new ApiException("退票金额不可以大于可退发票金额");
                //}

                if (contractRefundInvoice.InvoicedAmount != InvoicedAmount)
                {
                    throw new ApiException("退票金额与可退发票金额不相等,不可以退票");
                }
                ////退票金额
                //decimal RefundAmount = contractRefundInvoice.InvoicedAmount;
                ////退票数和开票数相等，则需要退全部金额。如果只退了部分发票，则退票金额大于零小于全部开票金额。
                //List<Db_crm_contract_invoice_item> invoiceItem = DbOpe_crm_contract_invoice_item.Instance.GetDataList(r => r.ContractInvoiceApplId == contractRefundInvoice.ContractInvoiceApplId);
                //List<Db_crm_contract_refundinvoice_item> refundinvoiceItem = DbOpe_crm_contract_refundinvoice_item.Instance.GetDataList(r => r.ContractInvoiceApplId == contractRefundInvoice.ContractInvoiceApplId);
                ////if (invoiceItem.Count == (refundinvoiceItem.Count + contractRefundInvoice.InvoiceInfo.Count))
                //if (invoice.InvoicedAmount == InvoicedAmount + RefundAmount)
                //{
                //    int TicketToBeRefunded = EnumRefundStatus.TicketRefunded.ToInt();
                //    //获取本次申请全部已退票金额
                //    decimal? refundInvoicedAmount = DbOpe_crm_contract_refundinvoice.Instance.GetDataList(r => r.ContractInvoiceApplId == contractRefundInvoice.ContractInvoiceApplId && r.State == TicketToBeRefunded).Sum(r => r.InvoicedAmount);
                //    if (refundInvoicedAmount == null)
                //    {
                //        refundInvoicedAmount = 0;
                //    }
                //    if (refundInvoicedAmount + RefundAmount != invoice.InvoicedAmount)
                //    {
                //        throw new ApiException("如果全部退票,退票金额和开票金额需要相等");
                //    }
                //}
                //else
                //{
                //    if (!(RefundAmount > 0 && RefundAmount < invoice.InvoicedAmount))
                //    {
                //        throw new ApiException("如果部分退票,则退票金额大于零小于全部开票金额");
                //    }
                //}

                Guid contractRefundInvoiceId = DbOpe_crm_contract_refundinvoice.Instance.AddContractRefundInvoice(contractRefundInvoice, invoice.ContractId, invoice.ContractInvoiceApplId, invoice.Id, UserId);
                //DbOpe_crm_contract_refundinvoice_item.Instance.AddContractRefundInvoiceItem(contractRefundInvoice.InvoiceInfo, invoice.ContractId, invoice.ContractInvoiceApplId, invoice.Id, contractRefundInvoiceId.ToString(), UserId);
                DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplRefundStatus(contractRefundInvoice.ContractInvoiceApplId, EnumRefundStatus.TicketToBeRefunded.ToInt());

                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(invoice.ContractId);
                Db_crm_contract_refundinvoice refundinvoice = DbOpe_crm_contract_refundinvoice.Instance.GetDataById(contractRefundInvoiceId.ToString());
                //Db_crm_contract_invoiceappl invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(contractRefundInvoice.ContractInvoiceApplId);

                int state = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplIsinvoice(invoiceappl.Id).IsInvoice;
                string dataState = Dictionary.IsInvoice.First(e => e.Value == state.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_refundinvoice, Db_crm_contract>("发票退票审批流程", contractRefundInvoiceId.ToString(), refundinvoice, contract, invoiceappl.CreateUser, contractRefundInvoice.RefundReason, dataState, "退票");
            });
        }

        /// <summary>
        /// 操作退票 (红冲)上传退票信息
        /// </summary>
        public void OperateContractRefundInvoice(OperateContractRefundInvoice_IN operateContractRefundInvoice_IN)
        {
            DbOpe_crm_contract_refundinvoice.Instance.TransDeal(() =>
            {
                Db_crm_contract_refundinvoice refundinvoice = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceInReviewByContractInvoiceAppl(operateContractRefundInvoice_IN.ContractInvoiceApplId);
                if (refundinvoice == null || (refundinvoice.Deleted != null && refundinvoice.Deleted.Value))
                {
                    throw new ApiException("未找到退票信息");
                }
                else
                {
                    if (refundinvoice.State != EnumRefundStatus.TicketToBeRefunded.ToInt() && refundinvoice.State != EnumRefundStatus.Questionable.ToInt())
                    {
                        throw new ApiException("当前状态不可以退票");
                    }
                }
                //240912 合同、发票、到账权限更改
                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(refundinvoice.ContractId, true);
                if (contract == null || (contract.Deleted != null && contract.Deleted.Value))
                {
                    throw new ApiException("未找到合同或该用户没有合同权限");
                }
                //if (contract.Issuer != UserId)
                //{
                //    throw new ApiException("该用户没有数据权限");
                //}

                //维护红冲发票附件信息
                DbOpe_crm_contract_refundinvoice_attachment.Instance.DeleteContractRefundInvoiceAttachmentList(operateContractRefundInvoice_IN.RefundInvoiceAttachmentInfo, refundinvoice.Id.ToString());

                operateContractRefundInvoice_IN.MappingTo(refundinvoice);
                if (operateContractRefundInvoice_IN.RefundInvoiceAttachment != null)
                {
                    Util<DbOpe_crm_contract_refundinvoice_attachment, BM_AttachFile> refundInvoiceAttachFile = new Util<DbOpe_crm_contract_refundinvoice_attachment, BM_AttachFile>(DbOpe_crm_contract_refundinvoice_attachment.Instance);
                    refundInvoiceAttachFile.UploadFile(operateContractRefundInvoice_IN.RefundInvoiceAttachment, EnumAttachFileType.RefundContractInvoice.ToString(), UserId, refundinvoice.Id.ToString());
                }
                refundinvoice.OperatorId = UserId;
                refundinvoice.OperationTime = DateTime.Now;
                refundinvoice.State = EnumRefundStatus.PendingRefundConfirmation.ToInt();
                DbOpe_crm_contract_refundinvoice.Instance.Update(refundinvoice);
                DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplRefundStatus(refundinvoice.ContractInvoiceApplId, EnumRefundStatus.PendingRefundConfirmation.ToInt());

                int state = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplIsinvoice(operateContractRefundInvoice_IN.ContractInvoiceApplId).IsInvoice;
                string dataState = Dictionary.IsInvoice.First(e => e.Value == state.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_refundinvoice, Db_crm_contract>("发票退票审批流程", refundinvoice.Id.ToString(), refundinvoice, contract, operateContractRefundInvoice_IN.Remark, dataState, "红冲");

            });
        }

        /// <summary>
        /// 审核退票信息
        /// </summary>
        public void AuditContractRefundInvoice(AuditContractRefundInvoice_In auditContractRefundInvoice)
        {
            DbOpe_crm_contract_refundinvoice.Instance.TransDeal(() =>
            {
                Db_crm_contract_refundinvoice refundinvoice = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceInReviewByContractInvoiceAppl(auditContractRefundInvoice.ContractInvoiceApplId);
                if (refundinvoice == null)
                {
                    throw new ApiException("当前状态不可以退票");
                }
                else
                {
                    if (refundinvoice.State != EnumRefundStatus.PendingRefundConfirmation.ToInt())
                    {
                        throw new ApiException("当前状态不可以退票");
                    }
                }
                if (auditContractRefundInvoice.State != EnumRefundStateConfirm.Confirmed.ToInt() && auditContractRefundInvoice.State != EnumRefundStateConfirm.Questionable.ToInt())
                {
                    throw new ApiException("是否确认状态不正确");
                }


                int state = EnumRefundStatus.TicketRefunded.ToInt();
                if (auditContractRefundInvoice.State == EnumRefundStateConfirm.Confirmed.ToInt())
                {
                    state = EnumRefundStatus.TicketRefunded.ToInt();
                    refundinvoice.State = EnumRefundStatus.TicketRefunded.ToInt();
                }
                if (auditContractRefundInvoice.State == EnumRefundStateConfirm.Questionable.ToInt())
                {
                    state = EnumRefundStatus.Questionable.ToInt();
                    refundinvoice.State = EnumRefundStatus.Questionable.ToInt();
                }

                Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(refundinvoice.ContractId);

                if (auditContractRefundInvoice.State == EnumRefundStateConfirm.Confirmed.ToInt())
                {
                    //设置退票状态
                    int InvoicingStatus = EnumInvoicingStatus.AllRefunds.ToInt();
                    //合同金额
                    decimal ContractAmount = contract.ContractAmount.ToDecimal();
                    //合同已开票金额
                    decimal InvoicedAmount = GetInvoicedAmountByContractId(refundinvoice.ContractId);
                    //退票金额
                    decimal RefundAmount = refundinvoice.InvoicedAmount.Value;

                    Db_crm_contract_invoice invoice = DbOpe_crm_contract_invoice.Instance.GetContractInvoiceByInvoiceApplId(refundinvoice.ContractInvoiceApplId);
                    //if (RefundAmount > invoice.InvoicedAmount)
                    //{
                    //    throw new ApiException("退票金额不可以大于本次开发票金额");
                    //}

                    if (RefundAmount != invoice.InvoicedAmount)
                    {
                        throw new ApiException("退票金额与可退发票金额不相等,不可以退票");
                    }
                    ////退票数和开票数相等，则需要退全部金额。如果只退了部分发票，则退票金额大于零小于全部开票金额。
                    //List<Db_crm_contract_invoice_item> invoiceItem = DbOpe_crm_contract_invoice_item.Instance.GetDataList(r => r.ContractInvoiceApplId == refundinvoice.ContractInvoiceApplId);
                    //List<Db_crm_contract_refundinvoice_item> refundinvoiceItem = DbOpe_crm_contract_refundinvoice_item.Instance.GetDataList(r => r.ContractInvoiceApplId == refundinvoice.ContractInvoiceApplId);
                    //if (invoiceItem.Count == refundinvoiceItem.Count)
                    //{
                    //    int TicketToBeRefunded = EnumRefundStatus.TicketRefunded.ToInt();
                    //    //获取本次申请全部已退票金额
                    //    decimal? refundInvoicedAmount = DbOpe_crm_contract_refundinvoice.Instance.GetDataList(r => r.ContractInvoiceApplId == refundinvoice.ContractInvoiceApplId && r.State == TicketToBeRefunded).Sum(r => r.InvoicedAmount);
                    //    if (refundInvoicedAmount == null)
                    //    {
                    //        refundInvoicedAmount = 0;
                    //    }
                    //    if (refundInvoicedAmount + RefundAmount != invoice.InvoicedAmount)
                    //    {
                    //        throw new ApiException("如果全部退票,退票金额和开票金额需要相等");
                    //    }
                    //}
                    //else
                    //{
                    //    if (!(RefundAmount > 0 && RefundAmount < invoice.InvoicedAmount))
                    //    {
                    //        throw new ApiException("如果全部退票,退票金额和开票金额需要相等");
                    //    }
                    //}

                    InvoicingStatus = EnumInvoicingStatus.AllRefunds.ToInt();
                    //if (RefundAmount == InvoicedAmount)
                    //{
                    //    InvoicingStatus = EnumInvoicingStatus.AllRefunds.ToInt();
                    //}
                    //else
                    //{
                    //    //需求调整（原需求是合同金额5000 一次开票5000 本次退票 4500 是 部分退票，否则是部分开票）
                    //    ////全部开票后只退一部分才可能是部分退票（本次退票所退发票为全部开票的发票，即单次开出全部发票的，才可以是部分退票）
                    //    //if (InvoicedAmount == invoice.InvoicedAmount && ContractAmount == InvoicedAmount)// && RefundAmount < InvoicedAmount)
                    //    //{
                    //    //    InvoicingStatus = EnumInvoicingStatus.PartialRefund.ToInt();
                    //    //}
                    //    //else
                    //    //{
                    //    //    InvoicingStatus = EnumInvoicingStatus.PartialInvoiced.ToInt();
                    //    //}
                    //    //新需求是只要发生了部分退票不管合同金额开票金额退票金额是多少，只要不是 全部退票就是部分退票
                    //    InvoicingStatus = EnumInvoicingStatus.PartialRefund.ToInt();
                    //}

                    //保存开票状态
                    DbOpe_crm_contract_invoiceappl.Instance.UpdateData(r => new Db_crm_contract_invoiceappl() { InvoicingStatus = InvoicingStatus }, refundinvoice.ContractInvoiceApplId);
                }

                DbOpe_crm_contract_refundinvoice.Instance.AuditContractRefundInvoice(auditContractRefundInvoice, refundinvoice.Id, state, UserId);
                DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplRefundStatus(refundinvoice.ContractInvoiceApplId, state);

                //Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(refundinvoice.ContractId);

                int invoicingStatus = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplIsinvoice(auditContractRefundInvoice.ContractInvoiceApplId).IsInvoice;
                string dataState = Dictionary.IsInvoice.First(e => e.Value == invoicingStatus.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_refundinvoice, Db_crm_contract>("发票退票审批流程", refundinvoice.Id.ToString(), refundinvoice, contract, auditContractRefundInvoice.Feedback, dataState, "审核");
            });
        }

        /// <summary>
        /// 根据退票id获取退票信息
        /// </summary>
        public ContractRefundInvoice_Out GetContractRefundInvoiceById(string id)
        {
            ContractRefundInvoice_Out result = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceById(id, true);
            if (result == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return result;
        }

        /// <summary>
        /// 根据合同发票申请Id获取合同信息和发票信息、发票申请信息、退票信息
        /// </summary>
        public ContractInfoAndInvoiceInfoAndRefundInvoiceInfo_Out GetContractInfoAndInvoiceInfoAndRefundInvoiceInfoByInvoiceApplId(string id)
        {
            ContractInfoAndInvoiceInfoAndRefundInvoiceInfo_Out info = DbOpe_crm_contract_invoiceappl.Instance.GetContractInfoAndInvoiceInfoAndRefundInvoiceInfoByInvoiceApplId(id, true);
            if (info == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            info.HistoryRefundInvoice = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceTicketRefundedListByInvoiceApplId(id);
            return info;
        }

        /// <summary>
        /// 根据合同发票申请Id获取发票信息、发票申请信息
        /// </summary>
        public ContractInvoiceApplAndInvoiceInfo_Out GetContractInvoiceApplAndInvoiceInfoByInvoiceApplId(string id)
        {
            ContractInvoiceApplAndInvoiceInfo_Out result = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplAndInvoiceInfoByInvoiceApplId(id);
            if (result == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return result;
        }

        /// <summary>
        /// 根据查询条件获取合同发票申请列表
        /// </summary>
        public ApiTableOut<SearchContractInvoiceApplList_Out> SearchContractInvoiceApplList(SearchContractInvoiceApplList_In searchContractInvoiceApplList_In)
        {
            int total = 0;
            return new ApiTableOut<SearchContractInvoiceApplList_Out> { Data = DbOpe_crm_contract_invoiceappl.Instance.SearchContractInvoiceApplList(searchContractInvoiceApplList_In, UserId, ref total), Total = total };
        }

        /// <summary>
        /// 根据合同发票申请Id获取合同发票申请信息
        /// </summary>
        public GetContractInvoiceAppl_OUT GetContractInvoiceApplById(string id)
        {
            GetContractInvoiceAppl_OUT result = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplById(id, true);
            if (result == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return result;
        }

        /// <summary>
        /// 根据发票申请id获取退票信息列表
        /// </summary>
        public List<ContractRefundInvoice_Out> GetContractRefundInvoiceListByInvoiceApplId(string id)
        {
            List<ContractRefundInvoice_Out> result = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceListByInvoiceApplId(id, true);
            return result;
        }

        /// <summary>
        /// 作废合同发票申请信息，当开票状态为全部开票、部分开票时、全部退票，可操作。
        /// </summary>
        public void VoidContractInvoiceAppl(string id)
        {
            //发票审核通过才可作废
            Db_crm_contract_invoiceappl invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(id);
            if (invoiceappl.AuditStatus != EnumAuditStatus.Pass.ToInt() && invoiceappl.AuditStatus != EnumAuditStatus.Refuse.ToInt())
            {
                throw new ApiException("当前发票申请状态下，不可以作废发票申请");
            }
            if (invoiceappl.AuditStatus == EnumAuditStatus.Pass.ToInt())
            {
                //当开票状态为全部开票、部分开票时、全部退票，可操作
                if (invoiceappl.InvoicingStatus != EnumInvoicingStatus.Invoiced.ToInt() && invoiceappl.InvoicingStatus != EnumInvoicingStatus.PartialInvoiced.ToInt() && invoiceappl.InvoicingStatus != EnumInvoicingStatus.AllRefunds.ToInt())
                {
                    throw new ApiException("当前发票申请状态下，不可以作废发票申请");
                }
            }
            //存在审核中的退票流程则不可作废
            Db_crm_contract_refundinvoice refund = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceInReviewByContractInvoiceAppl(id);
            if (refund != null)
            {
                throw new ApiException("退票申请审核中，不可以作废");
            }
            Db_crm_contract_invoiceaudit invoiceaudit = DbOpe_crm_contract_invoiceaudit.Instance.GetContractInvoiceAuditByContractInvoiceApplId(invoiceappl.Id, true);
            //验证数据权限（审核人员可以作废）
            if (invoiceaudit == null)
            {
                throw new ApiException("该用户没有数据权限");
            }

            DbOpe_crm_contract_invoiceappl.Instance.TransDeal(() =>
            {
                //修改发票状态
                DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplAuditStatus(id, EnumAuditStatus.Cancel.ToInt());
                //作废发票原件
                List<string> InvoiceNos = DbOpe_crm_contract_invoice_item.Instance.GetDataList(r => r.ContractInvoiceApplId == id && r.Deleted == false).Select(r => r.InvoiceNo).ToList();
                if (InvoiceNos.Count > 0)
                {
                    DbOpe_crm_original_invoice_details_entity.Instance.UpdateData(r => new Db_crm_original_invoice_details_entity { State = EnumInvoiceEntityState.Void }, r => InvoiceNos.Contains(r.InvoiceNo));
                }
                //撤销已发送的消息

                BLL_WorkFlow.Instance.VoidWorkflowPending("发票申请审批流程", id, "作废");
            });
        }

        /// <summary>
        /// 删除合同发票申请信息，当开票状态为草稿、作废、拒绝时，可删除。
        /// </summary>
        public void DeleteContractInvoiceAppl(string id)
        {
            //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限
            GetContractInvoiceAppl_OUT invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplById(id, true);
            if (invoiceappl == null)
            {
                throw new ApiException("该用户没有数据权限");
            }
            //开票状态为草稿、作废、拒绝时，可删除
            if (invoiceappl.AuditStatus != EnumAuditStatus.Draft.ToInt() && invoiceappl.AuditStatus != EnumAuditStatus.Refuse.ToInt() && invoiceappl.AuditStatus != EnumAuditStatus.Cancel.ToInt())
            {
                throw new ApiException("当前发票申请状态下，不可以删除发票申请");
            }
            DbOpe_crm_contract_invoiceappl.Instance.TransDeal(() =>
            {
                //修改发票状态
                var o = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(id);
                o.Deleted = true;
                DbOpe_crm_contract_invoiceappl.Instance.Update(o);
                //删除发票
                DbOpe_crm_contract_invoice.Instance.DeleteContractInvoiceByInvoiceApplId(id);
            });
        }

        /// <summary>
        /// 催票，当开票状态为申请时，可操作。
        /// </summary>
        public void ReminderContractInvoiceAppl(ReminderContractInvoiceAppl_IN reminderContractInvoiceAppl_IN)
        {
            DbOpe_crm_contract_invoiceappl.Instance.TransDeal(() =>
            {
                foreach (var id in reminderContractInvoiceAppl_IN.Ids)
                {

                    //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限
                    GetContractInvoiceAppl_OUT invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplById(id, true);
                    if (invoiceappl == null)
                    {
                        throw new ApiException("该用户没有数据权限");
                    }
                    if (invoiceappl.AuditStatus != EnumAuditStatus.Submit.ToInt())
                    {
                        throw new ApiException("当前发票申请状态下，不可以进行催票");
                    }
                    //修改发票状态
                    var o = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(id);
                    o.IsReminder = EnumIsReminder.UrgedTicket.ToInt();
                    o.ExpectedInvoicingTime = reminderContractInvoiceAppl_IN.ExpectedInvoicingTime;
                    DbOpe_crm_contract_invoiceappl.Instance.Update(o);
                }
            });
        }

        /// <summary>
        /// 确认签收，当开票状态为全部开票、部分开票时，可操作
        /// </summary>
        public void SignReceivingContractInvoiceAppl(string id)
        {
            //20240911 更改权限判断，拥有客户权限的，同时拥有合同/发票/到账权限
            GetContractInvoiceAppl_OUT invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceApplById(id, true);
            if (invoiceappl == null)
            {
                throw new ApiException("该用户没有数据权限");
            }
            //当开票状态为全部开票、部分开票时，可操作
            if (invoiceappl.InvoicingStatus == EnumInvoicingStatus.NotInvoiced.ToInt())
            {
                throw new ApiException("当前发票申请状态下，不可以进行签收");
            }
            DbOpe_crm_contract_invoiceappl.Instance.TransDeal(() =>
            {
                //修改发票状态
                var o = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(id);
                o.IsSignReceiving = EnumIsSignReceiving.ConfirmReceipt.ToInt();
                DbOpe_crm_contract_invoiceappl.Instance.Update(o);
            });
        }

        /// <summary>
        /// 撤销合同发票申请信息type 1 申请 2 审核
        /// </summary>
        public void RevokeContractInvoiceAppl(string id, int type)
        {
            //先撤销退票流程
            //存在审核中的退票流程则直接撤销到发起退票前，已审核的退票可以撤销
            //如果存在退票流程不过是否通过，按顺序撤销
            Db_crm_contract_refundinvoice refund = DbOpe_crm_contract_refundinvoice.Instance.GetLastContractRefundInvoiceByContractInvoiceApplId(id);
            if (refund != null)
            {
                //验证数据权限（合同发票申请创建者处理，审核人员也可以撤销）
                //if (refund.ApplicantId != UserId && refund.OperatorId != UserId && refund.ReviewerId != UserId)
                //{
                //    throw new ApiException("该用户没有数据权限");
                //}
                ContractRefundInvoice_Out result = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceById(refund.Id, true);
                if (result == null)
                {
                    throw new ApiException("没有数据权限或者数据不存在");
                }
                DbOpe_crm_contract_invoiceappl.Instance.TransDeal(() =>
                {
                    if (type == EnumOperate.Appl.ToInt())
                    {
                        if (refund.State != EnumRefundStatus.PendingRefundConfirmation.ToInt())
                        {
                            throw new ApiException("当前发票申请状态下，不可以撤销发票申请");
                        }
                    }
                    else if (type == EnumOperate.Examine.ToInt())
                    {
                        //if (refund.State == EnumRefundStatus.PendingRefundConfirmation.ToInt())
                        //{
                        //    throw new ApiException("当前发票申请状态下，不可以撤销发票申请");
                        //}
                    }


                    //退票状态：退票审核人发起后状态：待退票（审核人撤销后回到部分开票或全部开票，销售无法进行撤销）
                    //》销售人员红冲后状态：待确认（销售人员撤销后回到待退票，或审核人撤销后回到部分开票或全部开票）
                    //》审核人确认红冲后状态：部分退票或全部退票（审核人撤销后回到部分开票或全部开票，销售无法进行撤销），
                    //若审核人驳回后状态：有疑问（审核人撤销后回到部分开票或全部开票，销售无法进行撤销）
                    //退票若已存在退票，连续发起第二次退票，并在这之间未存在开票行为，审核人撤销均回到状态部分退票
                    //退票金额需小于或等于开票金额

                    //销售人员撤销
                    //if (refund.State == EnumRefundStatus.PendingRefundConfirmation.ToInt())
                    if (type == EnumOperate.Appl.ToInt())
                    {
                        if (refund.OperatorId != UserId)
                        {
                            throw new ApiException("该用户没有数据权限");
                        }
                        DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplRefundStatus(refund.ContractInvoiceApplId, EnumRefundStatus.TicketToBeRefunded.ToInt());
                        //清除退票操作信息
                        DbOpe_crm_contract_refundinvoice.Instance.CancleTicketToBeRefundedById(refund.Id);
                        //删除退票申请附件
                        DbOpe_crm_contract_refundinvoice_attachment.Instance.DeleteContractRefundInvoiceAttachmentByRefundInvoiceId(refund.Id);
                        ////删除退票明细
                        //DbOpe_crm_contract_refundinvoice_item.Instance.DeleteContractRefundInvoiceItemByRefundInvoiceId(refund.Id);
                    }
                    //审核人员撤销 就全部撤销
                    else if (type == EnumOperate.Examine.ToInt())
                    {
                        //删除退票申请
                        DbOpe_crm_contract_refundinvoice.Instance.DeleteContractRefundInvoiceById(refund.Id);
                        //删除退票申请附件
                        DbOpe_crm_contract_refundinvoice_attachment.Instance.DeleteContractRefundInvoiceAttachmentByRefundInvoiceId(refund.Id);
                        //删除退票明细
                        DbOpe_crm_contract_refundinvoice_item.Instance.DeleteContractRefundInvoiceItemByRefundInvoiceId(refund.Id);

                        //判断发票申请下是否还存在已通过的退票,如果没有则改为未退票，有则改为已通过
                        Db_crm_contract_refundinvoice ticketRefunded = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceByContractInvoiceApplIdAndState(id, EnumRefundStatus.TicketRefunded.ToInt());
                        //修改发票申请退票状态
                        int state = EnumRefundStatus.NotRefunded.ToInt();
                        if (ticketRefunded != null)
                        {
                            state = EnumRefundStatus.TicketRefunded.ToInt();
                        }
                        else
                        {
                            state = EnumRefundStatus.NotRefunded.ToInt();
                        }
                        DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplRefundStatus(id, state);


                        Db_crm_contract_refundinvoice refundNext = DbOpe_crm_contract_refundinvoice.Instance.GetLastContractRefundInvoiceByContractInvoiceApplId(id);
                        if (refundNext != null)
                        {
                            int PartialRefund = EnumInvoicingStatus.PartialRefund.ToInt();
                            DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplInvoicingStatus(id, PartialRefund);
                        }
                        else
                        {
                            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(refund.ContractId);
                            decimal InvoicedAmount = GetInvoicedAmountByContractId(refund.ContractId);
                            //合同金额
                            decimal ContractAmount = contract.ContractAmount.ToDecimal();

                            ////退票金额小于合同金额,已开票金额大于合同金额不可以退票
                            //if (result.InvoicedAmount < ContractAmount)
                            //{
                            //    if (ContractAmount <= InvoicedAmount)
                            //    {
                            //        throw new ApiException("已开票金额大于合同金额不可以撤销退票");
                            //    }
                            //}
                            //else
                            //{
                            //    if (InvoicedAmount != 0)
                            //    {
                            //        throw new ApiException("已开票金额大于合同金额不可以撤销退票");
                            //    }
                            //}

                            int InvoicingStatus = 0;
                            if (ContractAmount <= InvoicedAmount)
                            {
                                InvoicingStatus = EnumInvoicingStatus.Invoiced.ToInt();
                            }
                            else
                            {
                                InvoicingStatus = EnumInvoicingStatus.PartialInvoiced.ToInt();
                            }
                            DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplInvoicingStatus(id, InvoicingStatus);
                        }
                    }
                    //撤销日程计划
                    DbOpe_crm_schedule.Instance.RevokeSchedule4Invoiceappl(id);
                    //撤销已发送的消息
                    string dataState = ((EnumOperate)type).GetEnumDescription();//Dictionary.IsInvoice.First(e => e.Value == state.ToInt().ToString()).Name;
                    if (type == EnumOperate.Appl.ToInt())
                    {
                        Db_crm_contract_refundinvoice dataObject = DbOpe_crm_contract_refundinvoice.Instance.GetDataById(refund.Id);
                        BLL_WorkFlow.Instance.CancelWorkflowPending("发票退票审批流程", refund.Id, dataState, dataObject);
                    }
                    else if (type == EnumOperate.Examine.ToInt())
                    {
                        BLL_WorkFlow.Instance.CancelWorkflowPending("发票退票审批流程", refund.Id, dataState);
                    }
                });
            }
            else
            {
                //发票状态为草稿和作废不可以撤销
                Db_crm_contract_invoiceappl invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(id);
                if (invoiceappl.AuditStatus == EnumAuditStatus.Cancel.ToInt() || invoiceappl.AuditStatus == EnumAuditStatus.Draft.ToInt())
                {
                    throw new ApiException("当前发票申请状态下，不可以撤销发票申请");
                }
                DbOpe_crm_contract_invoiceappl.Instance.TransDeal(() =>
                {
                    Db_crm_contract_invoiceaudit invoiceaudit = DbOpe_crm_contract_invoiceaudit.Instance.GetContractInvoiceAuditByContractInvoiceApplId(invoiceappl.Id, true);
                    //验证数据权限（合同发票申请创建者处理，审核人员也可以撤销）
                    //if (invoiceaudit.ApplicantId != UserId && invoiceaudit.ReviewerId != UserId)
                    if (invoiceaudit == null)
                    {
                        throw new ApiException("该用户没有数据权限");
                    }

                    ////如果不是审核人员待审核状态以外的不可以撤销
                    //if (invoiceaudit.ReviewerId != UserId)
                    //{
                    //    if (invoiceappl.AuditStatus != EnumAuditStatus.Submit.ToInt())
                    //    {
                    //        throw new ApiException("当前发票申请状态下，不可以撤销发票申请");
                    //    }
                    //}
                    //type 1 申请 2 审核
                    if (type == EnumOperate.Appl.ToInt())
                    {
                        if (invoiceappl.AuditStatus == EnumAuditStatus.Pass.ToInt() || invoiceappl.AuditStatus == EnumAuditStatus.Refuse.ToInt())
                        {
                            throw new ApiException("当前发票申请状态下，不可以撤销发票申请");
                        }
                    }
                    else if (type == EnumOperate.Examine.ToInt())
                    {
                        if (invoiceappl.AuditStatus == EnumAuditStatus.Submit.ToInt())
                        {
                            throw new ApiException("当前发票申请状态下，不可以撤销发票申请");
                        }
                    }

                    int auditStatus = EnumAuditStatus.Submit.ToInt();
                    if (invoiceappl.AuditStatus == EnumAuditStatus.Submit.ToInt())
                    {
                        auditStatus = EnumAuditStatus.Draft.ToInt();
                        ////还原开票状态
                        //int NotInvoiced = EnumInvoicingStatus.NotInvoiced.ToInt();
                        //DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplInvoicingStatus(invoiceappl.Id, NotInvoiced);
                        //删除发票审核                          
                        DbOpe_crm_contract_invoiceaudit.Instance.DeleteContractInvoiceAuditByContractInvoiceApplId(invoiceappl.Id);
                    }
                    else if (invoiceappl.AuditStatus == EnumAuditStatus.Pass.ToInt() || invoiceappl.AuditStatus == EnumAuditStatus.Refuse.ToInt())
                    {
                        auditStatus = EnumAuditStatus.Submit.ToInt();
                        //删除发票审核                          
                        DbOpe_crm_contract_invoiceaudit.Instance.RevokeContractInvoiceAuditByInvoiceApplId(invoiceappl.Id, auditStatus);
                        if (invoiceappl.AuditStatus == EnumAuditStatus.Pass.ToInt())
                        {
                            Db_crm_contract_invoice invoice = DbOpe_crm_contract_invoice.Instance.GetContractInvoiceByInvoiceApplId(invoiceappl.Id);
                            //删除发票
                            DbOpe_crm_contract_invoice.Instance.DeleteContractInvoiceByInvoiceApplId(invoiceappl.Id);
                            //删除发票附件
                            DbOpe_crm_contract_invoice_attachment.Instance.DeleteContractInvoiceAttachmentByInvoiceId(invoice.Id);
                            //恢复发票原件
                            List<string> InvoiceNos = DbOpe_crm_contract_invoice_item.Instance.GetDataList(r => r.ContractInvoiceApplId == invoiceappl.Id && r.Deleted == false).Select(r => r.InvoiceNo).ToList();
                            if (InvoiceNos.Count > 0)
                            {
                                DbOpe_crm_original_invoice_details_entity.Instance.UpdateData(r => new Db_crm_original_invoice_details_entity { State = EnumInvoiceEntityState.UnUsed }, r => InvoiceNos.Contains(r.Id));
                            }
                            //删除发票明细
                            DbOpe_crm_contract_invoice_item.Instance.DeleteContractInvoiceItemByInvoiceId(invoice.Id);
                        }
                        //还原寄出状态
                        int NotSentOut = EnumIsSignReceiving.NotSentOut.ToInt();
                        DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplIsSignReceiving(invoiceappl.Id, NotSentOut);
                    }
                    //还原开票状态
                    int NotInvoiced = EnumInvoicingStatus.NotInvoiced.ToInt();
                    DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplInvoicingStatus(invoiceappl.Id, NotInvoiced);
                    //修改发票申请状态
                    DbOpe_crm_contract_invoiceappl.Instance.UpdateContractInvoiceApplAuditStatus(id, auditStatus);
                    //撤销日程计划
                    DbOpe_crm_schedule.Instance.RevokeSchedule4Invoiceappl(id);
                    //撤销已发送的消息
                    string dataState = ((EnumOperate)type).GetEnumDescription();//Dictionary.IsInvoice.First(e => e.Value == state.ToInt().ToString()).Name;
                    Db_crm_contract_invoiceappl dataObject = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(id);
                    BLL_WorkFlow.Instance.CancelWorkflowPending("发票申请审批流程", invoiceappl.Id, dataState, dataObject);
                });
            }
        }

        /// <summary>
        /// 根据合同id获取已开票金额
        /// </summary>
        public decimal GetInvoicedAmountByContractId(string id)
        {
            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(id, true);
            if (contract == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            decimal invoicedAmount = DbOpe_crm_contract_invoice.Instance.GetInvoicedAmountByContractId(id);
            decimal refundInvoicedAmount = DbOpe_crm_contract_refundinvoice.Instance.GetRefundInvoicedAmountByContractId(id);
            return invoicedAmount - refundInvoicedAmount;
        }

        /// <summary>
        /// 根据合同id获取已申请未开票金额
        /// </summary>
        public decimal GetInvoicedAmountApplByContractId(string id)
        {
            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(id, true);
            if (contract == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return DbOpe_crm_contract_invoiceappl.Instance.GetInvoicedAmountApplByContractId(id);
        }

        /// <summary>
        /// 根据合同id获取已申请未开票金额
        /// </summary>
        public decimal GetInvoicedAmountApplByContractIdAndApplId(string ContractId, string ApplId)
        {
            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(ContractId, true);
            if (contract == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            return DbOpe_crm_contract_invoiceappl.Instance.GetInvoicedAmountApplByContractIdAndApplId(ContractId, ApplId);
        }

        public decimal GetMayApplInvoicedAmountByContractIdAndApplId(string ContractId, string ApplId)
        {
            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(ContractId, true);
            if (contract == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            decimal invoicedAmount = DbOpe_crm_contract_invoice.Instance.GetInvoicedAmountByContractId(ContractId);
            decimal refundInvoicedAmount = DbOpe_crm_contract_refundinvoice.Instance.GetRefundInvoicedAmountByContractId(ContractId);

            decimal InvoicedAmount = invoicedAmount - refundInvoicedAmount;
            //合同金额
            decimal ContractAmount = 0;
            if (contract.Currency == EnumCurrency.CNY.ToInt())
            {
                ContractAmount = contract.ContractAmount.ToDecimal();
            }
            else
            {
                ContractAmount = contract.FCContractAmount.ToDecimal();
            }
            //decimal ContractAmount = contract.ContractAmount.ToDecimal();

            decimal InvoicedAmountAppl = 0;
            if (ApplId.IsNullOrEmpty())
            {
                InvoicedAmountAppl = DbOpe_crm_contract_invoiceappl.Instance.GetInvoicedAmountApplByContractId(ContractId);
            }
            else
            {
                InvoicedAmountAppl = DbOpe_crm_contract_invoiceappl.Instance.GetInvoicedAmountApplByContractIdAndApplId(ContractId, ApplId);
            }
            decimal result = ContractAmount - InvoicedAmount - InvoicedAmountAppl;
            if (result < 0)
            {
                return 0;
            }
            else
            {
                return result;
            }
        }


        /// <summary>
        /// 根据发票申请id获取已开票金额
        /// </summary>
        public decimal GetInvoicedAmountByContractInvoiceApplId(string contractId, string contractInvoiceApplId)
        {
            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(contractId, true);
            if (contract == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            decimal invoicedAmount = DbOpe_crm_contract_invoice.Instance.GetInvoicedAmountByContractInvoiceApplId(contractInvoiceApplId);
            decimal refundInvoicedAmount = DbOpe_crm_contract_refundinvoice.Instance.GetRefundInvoicedAmountByContractInvoiceApplId(contractInvoiceApplId);
            return invoicedAmount - refundInvoicedAmount;
        }

        /// <summary>
        /// 根据合同发票申请Id获取发票信息、发票申请信息、退票信息
        /// </summary>
        public ContractInvoiceInfoAndRefundInvoiceInfo_Out GetContractInvoiceInfoAndRefundInvoiceInfoByInvoiceApplId(string id)
        {
            InvoiceApplAndInvoiceInfo_Out InvoiceApplAndInvoiceInfo = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceInfoByInvoiceApplId(id, true);
            if (InvoiceApplAndInvoiceInfo == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            ContractInvoiceInfoAndRefundInvoiceInfo_Out InvoiceAppl = new ContractInvoiceInfoAndRefundInvoiceInfo_Out();
            InvoiceAppl.InvoiceApplAndInvoiceInfo = InvoiceApplAndInvoiceInfo;
            InvoiceAppl.RefundInvoice = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceTicketRefundedListByInvoiceApplId(id);
            return InvoiceAppl;
        }

        /// <summary>
        /// 根据合同发票申请Id获取发票信息、发票申请信息、退票信息
        /// </summary>
        public ContractInvoiceInfoAndRefundInvoiceInfo_Out GetContractInvoiceInfoAndRefundInvoiceInfoRefundedAndPendingRefundConfirmationByInvoiceApplId(string id)
        {
            InvoiceApplAndInvoiceInfo_Out InvoiceApplAndInvoiceInfo = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceInfoByInvoiceApplId(id, true);
            if (InvoiceApplAndInvoiceInfo == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            ContractInvoiceInfoAndRefundInvoiceInfo_Out InvoiceAppl = new ContractInvoiceInfoAndRefundInvoiceInfo_Out();
            InvoiceAppl.InvoiceApplAndInvoiceInfo = InvoiceApplAndInvoiceInfo;
            InvoiceAppl.RefundInvoice = DbOpe_crm_contract_refundinvoice.Instance.GetContractRefundInvoiceTicketRefundedAndPendingRefundConfirmationListByInvoiceApplId(id);
            return InvoiceAppl;
        }

        /// <summary>
        /// 根据合同发票申请Id获取发票的发票编号信息
        /// </summary>
        public List<GetOriginalInvoiceList_Out> GetInvoiceInfoByInvoiceApplId(string invoiceApplId)
        {
            return DbOpe_crm_contract_invoice_item.Instance.GetInvoiceInfoByInvoiceApplId(invoiceApplId, true);
        }

        /// <summary>
        /// 根据合同Id获取发票信息
        /// </summary>
        public List<InvoiceInfo_Out> GetInvoiceInfoByContractId(string contractId)
        {
            //240912 合同、发票、到账权限更改
            Db_crm_contract result = DbOpe_crm_contract.Instance.GetContractById(contractId, true);
            if (result == null)
            {
                throw new ApiException("未找到合同或该用户没有合同权限");
            }
            return DbOpe_crm_contract_invoiceappl.Instance.GetInvoiceInfoByContractId(contractId);
        }

        /// <summary>
        /// 根据合同Id和开票时间获取发票信息列表
        /// </summary>
        public ApiTableOut<ContractInvoiceList_Out> GetContractInvoiceListByContractId(ContractInvoiceList_In contractInvoiceListIn)
        {
            //Db_crm_contract result = DbOpe_crm_contract.Instance.GetContractById(contractInvoiceListIn.ContractId);
            //if (result.Issuer != UserId)
            //{
            //    throw new ApiException("该用户没有数据权限");
            //}
            //240912 合同、发票、到账权限更改
            Db_crm_contract result = DbOpe_crm_contract.Instance.GetContractById(contractInvoiceListIn.ContractId, true);
            if (result == null)
            {
                throw new ApiException("未找到合同或该用户没有合同权限");
            }
            int total = 0;
            return new ApiTableOut<ContractInvoiceList_Out> { Data = DbOpe_crm_invoice_application.Instance.GetContractInvoiceListByContractId(contractInvoiceListIn, ref total), Total = total };
            //return new ApiTableOut<ContractInvoiceList_Out> { Data = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceListByContractId(contractInvoiceListIn, ref total), Total = total };
        }

        /// <summary>
        /// 根据客户Id和开票时间获取发票信息列表
        /// </summary>
        public ApiTableOut<ContractInvoiceList_Out> GetContractInvoiceListByCustomerId(ContractInvoiceList_CustomerId_In contractInvoiceListCustomerIdIn)
        {
            PrivateCustomerSimpleInfo privatepool = null;
            bool IsPrivate = DbOpe_crm_customer_privatepool.Instance.CheckInPool(contractInvoiceListCustomerIdIn.CustomerId, ref privatepool, UserId, false, false);
            bool IsPublic = DbOpe_crm_customer_publicpool.Instance.CheckInPool(contractInvoiceListCustomerIdIn.CustomerId, true, UserId, false, false);
            if (!IsPrivate && !IsPublic)
            {
                throw new ApiException("该用户没有数据权限");
            }
            int total = 0;
            return new ApiTableOut<ContractInvoiceList_Out> { Data = DbOpe_crm_invoice_application.Instance.GetContractInvoiceListByCustomerId(contractInvoiceListCustomerIdIn, ref total), Total = total };
            //return new ApiTableOut<ContractInvoiceList_Out> { Data = DbOpe_crm_contract_invoiceappl.Instance.GetContractInvoiceListByCustomerId(contractInvoiceListCustomerIdIn, ref total), Total = total };
        }

        /// <summary>
        /// 根据合同id获取开票状态
        /// </summary>
        public int GetIsInvoicedByContractId(string contractId)
        {
            return DbOpe_crm_contract_invoice.Instance.GetIsInvoicedByContractId(contractId);
        }

        /// <summary>
        /// 获取形式发票票号
        /// </summary>
        public string GetFormalInvoiceNumber()
        {
            int Num = DbOpe_crm_contract_invoice.Instance.GetFormalInvoiceNumber();
            string FormalInvoiceNumber = (Num + 1).ToString().PadLeft(3, '0');
            return DateTime.Now.ToString("yyyyMMdd") + FormalInvoiceNumber;
        }

        /// <summary>
        /// 下载发票
        /// </summary>
        public Stream DownloadInvoice(string invoiceId, HttpResponse response)
        {
            Db_crm_contract_invoice invoice = DbOpe_crm_contract_invoice.Instance.GetContractInvoiceById(invoiceId);
            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(invoice.ContractId, true);
            if (contract == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            Db_crm_contract_invoiceappl invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetData(r => r.Id == invoice.ContractInvoiceApplId);
            //Db_crm_customer_subcompany subcompany = DbOpe_crm_customer_subcompany.Instance.GetData(r => r.Id == invoiceappl.BillingHeader);

            //加载数据
            decimal ContractAmount = 0;
            if (contract.Currency == EnumCurrency.CNY.ToInt())
            {
                ContractAmount = contract.ContractAmount.Value;
            }
            else
            {
                ContractAmount = contract.FCContractAmount.Value;
            }

            //decimal InvoicedAmountTotal = GetInvoicedAmountByContractId(invoice.ContractId);
            decimal InvoicedAmountTotal = ContractAmount;
            decimal AmountApplied = DbOpe_crm_contract_invoice.Instance.GetInvoicedAmountByContractIdAndInvoicingDate(invoice.ContractId, invoice.InvoicingDate.Value, invoice.Id);
            decimal BalanceDue = invoice.BalanceDue == null ? invoice.InvoicedAmount.Value : invoice.BalanceDue.Value;
            //decimal InvoicedAmountTotal = invoice.InvoicedAmount.Value;
            //decimal AmountApplied = invoice.InvoicedAmount.Value;
            //decimal BalanceDue = invoice.InvoicedAmount.Value;

            //if (invoiceappl.BillingType == EnumBillingType.InvoicingBeforePayment.ToInt())
            //{
            //    AmountApplied = 0;
            //}

            //string ExpectedInvoicingTime = "";
            //if (invoiceappl.ExpectedInvoicingTime != null)
            //{
            //    ExpectedInvoicingTime = invoiceappl.ExpectedInvoicingTime.Value.Month.ToString("00") + "/" + invoiceappl.ExpectedInvoicingTime.Value.Day.ToString("00") + "/" + invoiceappl.ExpectedInvoicingTime.Value.Year.ToString();
            //}
            //else
            //{
            //    ExpectedInvoicingTime = invoice.InvoicingDate.Value.Month.ToString("00") + "/" + invoice.InvoicingDate.Value.Day.ToString("00") + "/" + invoice.InvoicingDate.Value.Year.ToString();
            //}
            string ExpectedInvoicingTime = "";
            if (invoiceappl.ExpectedInvoicingTime != null)
            {
                ExpectedInvoicingTime = invoiceappl.ExpectedInvoicingTime.Value.Year.ToString() + "-" + invoiceappl.ExpectedInvoicingTime.Value.Month.ToString("00") + "-" + invoiceappl.ExpectedInvoicingTime.Value.Day.ToString("00");
            }
            else
            {
                ExpectedInvoicingTime = invoice.InvoicingDate.Value.Year.ToString() + "-" + invoice.InvoicingDate.Value.Month.ToString("00") + "-" + invoice.InvoicingDate.Value.Day.ToString("00");
            }

            List<TemplateField> templateFields = new List<TemplateField>();
            templateFields.Add(new TemplateField { FieldName = "${BillingHeader}", FieldValue = invoiceappl.BillingHeaderName });//subcompany.CompanyName });
            //templateFields.Add(new TemplateField { FieldName = "${InvoicingDate}", FieldValue = invoice.InvoicingDate.Value.ToString("MM/dd/yyyy") });
            string InvoicingDate = invoice.InvoicingDate.Value.Month.ToString("00") + "/" + invoice.InvoicingDate.Value.Day.ToString("00") + "/" + invoice.InvoicingDate.Value.Year.ToString();
            templateFields.Add(new TemplateField { FieldName = "${InvoicingDate}", FieldValue = ExpectedInvoicingTime });//InvoicingDate });
            templateFields.Add(new TemplateField { FieldName = "${InvoicedAmount}", FieldValue = invoice.InvoicedAmount.ToString() });
            templateFields.Add(new TemplateField { FieldName = "${FormalInvoiceNumber}", FieldValue = invoice.FormalInvoiceNumber });
            //templateFields.Add(new TemplateField { FieldName = "${SigningDate}", FieldValue = contract.SigningDate.Value.ToString("MM/dd/yyyy") });
            string SigningDate = contract.SigningDate.Value.Month.ToString("00") + "/" + contract.SigningDate.Value.Day.ToString("00") + "/" + contract.SigningDate.Value.Year.ToString();
            templateFields.Add(new TemplateField { FieldName = "${SigningDate}", FieldValue = ExpectedInvoicingTime });//SigningDate });
            templateFields.Add(new TemplateField { FieldName = "${ContractAmount}", FieldValue = ContractAmount.ToString() });
            templateFields.Add(new TemplateField { FieldName = "${InvoicedAmountTotal}", FieldValue = InvoicedAmountTotal.ToString() });
            templateFields.Add(new TemplateField { FieldName = "${AmountApplied}", FieldValue = AmountApplied.ToString() });
            templateFields.Add(new TemplateField { FieldName = "${BalanceDue}", FieldValue = BalanceDue.ToString() });

            Db_crm_contract_paymentinfo PaymentInfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByContractId(contract.Id);
            string AddressAll = "";
            if (PaymentInfo.IsBehalfPayment == true)
            {
                string CountryName = "";
                Db_sys_country country = DbOpe_sys_country.Instance.GetDataById(PaymentInfo.PaymentCountry.ToString());
                //240905 外国国家后面加空格
                CountryName = country.NameEN + " ";

                string Address = PaymentInfo.PaymentAddress;
                if (PaymentInfo.PaymentCountry == 337)
                {
                    CountryName = "";
                    string ProvinceName = "";
                    Db_sys_province province = DbOpe_sys_province.Instance.GetDataById(PaymentInfo.PaymentProvince.ToString());
                    if (province != null)
                    {
                        ProvinceName = province.Name;
                    }
                    string CityName = "";
                    Db_sys_city city = DbOpe_sys_city.Instance.GetDataById(PaymentInfo.PaymentCity.ToString());
                    if (city != null)
                    {
                        CityName = city.Name;
                    }
                    if (ProvinceName == CityName)
                    {
                        CityName = "";
                    }
                    CountryName = CountryName + ProvinceName + CityName;
                }

                AddressAll = CountryName + Address;
            }
            else
            {
                string CountryName = "";
                Db_sys_country country = DbOpe_sys_country.Instance.GetDataById(contract.Country.ToString());
                //240905 外国国家后面加空格
                CountryName = country.NameEN + " ";

                string Address = contract.Address;
                if (contract.Country == 337)
                {
                    CountryName = "";
                    string ProvinceName = "";
                    Db_sys_province province = DbOpe_sys_province.Instance.GetDataById(contract.Province.ToString());
                    if (province != null)
                    {
                        ProvinceName = province.Name;
                    }
                    string CityName = "";
                    Db_sys_city city = DbOpe_sys_city.Instance.GetDataById(contract.City.ToString());
                    if (city != null)
                    {
                        CityName = city.Name;
                    }
                    if (ProvinceName == CityName)
                    {
                        CityName = "";
                    }
                    CountryName = CountryName + ProvinceName + CityName;
                }

                AddressAll = CountryName + Address;
            }

            string Contacts = contract.Contacts == null ? "" : contract.Contacts;
            string Email = contract.Email == null ? "" : contract.Email;
            string Fax = contract.Fax == null ? "" : contract.Fax;
            string PostalCode = contract.PostalCode == null ? "" : contract.PostalCode;
            string ContactWay = contract.ContactWay == null ? "" : contract.ContactWay;
            string Telephone = contract.Telephone.IsNullOrEmpty() ? ContactWay : contract.Telephone;

            if (PaymentInfo.IsBehalfPayment == true)
            {
                Contacts = PaymentInfo.PaymentContacts == null ? "" : PaymentInfo.PaymentContacts;
                Email = PaymentInfo.PaymentEmail == null ? "" : PaymentInfo.PaymentEmail;
                Fax = PaymentInfo.PaymentFax == null ? "" : PaymentInfo.PaymentFax;
                PostalCode = PaymentInfo.PaymentPostalCode == null ? "" : PaymentInfo.PaymentPostalCode;
                ContactWay = PaymentInfo.PaymentContactWay == null ? "" : PaymentInfo.PaymentContactWay;
                Telephone = PaymentInfo.PaymentTelephone.IsNullOrEmpty() ? ContactWay : PaymentInfo.PaymentTelephone;
            }

            templateFields.Add(new TemplateField { FieldName = "${Contacts}", FieldValue = Contacts });
            templateFields.Add(new TemplateField { FieldName = "${Address}", FieldValue = AddressAll });
            templateFields.Add(new TemplateField { FieldName = "${Telephone}", FieldValue = ContactWay });
            templateFields.Add(new TemplateField { FieldName = "${Fax}", FieldValue = Fax });
            templateFields.Add(new TemplateField { FieldName = "${PostalCode}", FieldValue = PostalCode });
            //templateTopTableFields.Add(new TemplateField { FieldName = "${Telephone}", FieldValue = contract.Telephone });
            //templateTopTableFields.Add(new TemplateField { FieldName = "${Fax}", FieldValue = contract.Fax });
            templateFields.Add(new TemplateField { FieldName = "${Email}", FieldValue = Email });
            templateFields.Add(new TemplateField { FieldName = "${ContactWay}", FieldValue = ContactWay });


            //临时根据币种判断
            string Currency = "";
            if (EnumCurrency.USD.ToInt() == contract.Currency)
            {
                Currency = "Usd";
            }
            if (EnumCurrency.EUR.ToInt() == contract.Currency)
            {
                Currency = "Eur";
            }
            if (EnumCurrency.CNY.ToInt() == contract.Currency)
            {
                Currency = "Cny";
            }

            //根据公司名和中英文类型选择模板
            string TemplatePath = DbOpe_crm_collectingcompany.Instance.GetDataById(invoice.BillingCompany).TemplatePath;
            string StampTemplatePath = TemplatePath;

            TemplatePath = TemplatePath + "/NoAccount2023" + Currency + ".docx";

            //加载模板
            string filePath = Path.Combine(Directory.GetCurrentDirectory(), "InvoiceTemplate/" + TemplatePath);
            string picturePath = Path.Combine(Directory.GetCurrentDirectory(), "InvoiceTemplate/" + StampTemplatePath + "/stamp.png");

            if (File.Exists(filePath))
            {
                using (Stream stream = File.OpenRead(filePath))
                {
                    Stream result = SpireDocCreateDoc.SpireDocCreateDocStream(stream, templateFields);
                    result.Seek(0, SeekOrigin.Begin);

                    Document document = new Document();
                    document.LoadFromStream(result, Spire.Doc.FileFormat.Docx);
                    
                    // 处理Unicode字符字体问题（支持土耳其语、阿拉伯语、中文等）
                    UnicodeTextProcessor.ProcessDocumentForUnicodeText(document);
                    
                    Stream streamPDF = new MemoryStream();
                    document.SaveToStream(streamPDF, Spire.Doc.FileFormat.PDF);

                    streamPDF.Seek(0, SeekOrigin.Begin);

                    string Filename = Regex.Replace(contract.ContractName, "\\.", "");
                    string encodeFilename = HttpUtility.UrlEncode(Filename + "形式发票", Encoding.GetEncoding("UTF-8"));
                    response.Headers.Add("download-name", HttpUtility.UrlEncode(encodeFilename + ".pdf"));
                    response.Headers.Add("Access-Control-Expose-Headers", "download-name");
                    response.Headers.Add("Content-Disposition", "attachment; filename=" + encodeFilename + ".pdf");
                    return streamPDF;
                }
            }
            else
            {
                throw new ApiException("当前乙方公司不存在合同币种对应的形式发票");
            }

        }

        /// <summary>
        /// 修改应付金额
        /// </summary>
        public void UpdateBalanceDue(UpdateBalanceDueIn updateBalanceDueIn)
        {
            Db_crm_contract_invoice invoice = DbOpe_crm_contract_invoice.Instance.GetContractInvoiceById(updateBalanceDueIn.id);
            Db_crm_contract contract = DbOpe_crm_contract.Instance.GetContractById(invoice.ContractId, true);
            if (contract == null)
            {
                throw new ApiException("没有数据权限或者数据不存在");
            }
            DbOpe_crm_contract_invoice.Instance.UpdateBalanceDue(updateBalanceDueIn);
        }

        /// <summary>
        /// 根据合同发票Id获取开票金额、应付金额
        /// </summary>
        public GetBalanceDue_Out GetBalanceByInvoiceId(string id)
        {
            return DbOpe_crm_contract_invoice.Instance.GetBalanceByInvoiceId(id);
        }

        /// <summary>
        /// 获取下载发票列表
        /// </summary>
        public List<BM_FileInfo> GetDownloadInvoiceList(string invoiceId)
        {
            List<Db_crm_contract_invoice_attachment> attachment = DbOpe_crm_contract_invoice_attachment.Instance.GetContractInvoiceAttachmentByInvoiceId(invoiceId, true);
            List<BM_FileInfo> result = new List<BM_FileInfo>();
            foreach (Db_crm_contract_invoice_attachment item in attachment)
            {
                result.Add(new BM_FileInfo { Id = item.Id, FileName = item.FileName, FilePath = item.FilePath });
            }
            Db_crm_contract_invoice invoice = DbOpe_crm_contract_invoice.Instance.GetContractInvoiceById(invoiceId, true);
            if (invoice != null)
            {
                Db_crm_contract_invoiceappl invoiceappl = DbOpe_crm_contract_invoiceappl.Instance.GetDataById(invoice.ContractInvoiceApplId);
                if (invoiceappl.InvoiceType == EnumInvoiceType.ProformaTicket.ToInt())
                {
                    Db_crm_contract contract = DbOpe_crm_contract.Instance.GetDataById(invoiceappl.ContractId);

                    result.Add(new BM_FileInfo { Id = invoiceId, FileName = contract.ContractName, FilePath = AppSettings.DownLoadFilePath + "/ProformaTicket/" });
                }
            }
            return result;
        }
    }
}
