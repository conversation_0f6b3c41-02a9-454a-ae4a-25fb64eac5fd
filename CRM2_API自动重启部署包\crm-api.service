[Unit]
Description=CRM2 API Service
After=network.target

[Service]
# 确保服务以root用户运行，拥有足够权限
User=root
WorkingDirectory=/hqhs_data/00.API/01.CRM/01.API
# 在启动服务前先尝试杀死占用6001端口的进程 (仅使用ss)
ExecStartPre=-/bin/sh -c 'pid=$(ss -tulpn | grep ":6001 " | awk \'{print $7}\' | cut -d"," -f2 | cut -d"=" -f2 | grep -o "[0-9]*"); [ -n "$pid" ] && kill -9 $pid || true'
# 设置文件权限
ExecStartPre=/bin/chmod -R 777 /hqhs_data/00.API/01.CRM/01.API
ExecStart=/hqhs_data/00.API/01.CRM/01.API/CRM2_API
Restart=always
# 重启延迟3秒
RestartSec=3
# 崩溃或异常退出时重启
KillSignal=SIGINT
# 设置环境变量
Environment=ASPNETCORE_ENVIRONMENT=Production
# 标准输出和错误输出重定向到日志文件
StandardOutput=append:/hqhs_data/00.API/01.CRM/log/log
StandardError=append:/hqhs_data/00.API/01.CRM/log/log


[Install]
WantedBy=multi-user.target 