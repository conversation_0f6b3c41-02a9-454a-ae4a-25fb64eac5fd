using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CRM2_API.Common.JWT;
using SqlSugar;
using CRM2_API.BLL.Common.Com_EmailHelper;
using CRM2_API.DAL.DbCommon;
using CRM2_API.Common.Cache;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 发票审核相关功能
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        #region 发票审核相关方法

        /// <summary>
        /// 审核发票申请
        /// </summary>
        /// <param name="request">审核请求</param>
        /// <returns>是否审核成功</returns>
        public bool AuditInvoiceApplication(AuditInvoiceApplicationRequest request)
        {
            if (request == null)
            {
                throw new ApiException("请求参数不能为空");
            }

            // 1. 验证发票申请是否存在
            var invoiceApplication = DbOpe_crm_invoice_application.Instance.GetData(d => d.Id == request.Id);
            if (invoiceApplication == null)
            {
                throw new ApiException("未找到发票申请记录");
            }

            // 2. 验证发票申请状态是否为"申请中"
            if (invoiceApplication.AuditStatus != (int)EnumInvoiceApplicationStatus.Applied)
            {
                throw new ApiException("只有在申请中状态的发票申请才能审核");
            }

            // 3. 如果审核结果为"通过"，则需要验证ActualInvoiceInfo信息
            if (request.AuditResult == 1)
            {
                if (request.ActualInvoiceInfo == null)
                {
                    throw new ApiException("审核通过时必须提供开票信息");
                }

                // 验证发票类型
                if (!Enum.IsDefined(typeof(EnumInvoiceType), request.ActualInvoiceInfo.InvoiceType))
                {
                    throw new ApiException("发票类型错误");
                }

                // 验证发票开具形式
                if (!Enum.IsDefined(typeof(EnumInvoicingForm), request.ActualInvoiceInfo.InvoiceForm))
                {
                    throw new ApiException("发票开具形式错误");
                }

                // 验证必填字段
                if (string.IsNullOrWhiteSpace(request.ActualInvoiceInfo.InvoiceNumber))
                {
                    throw new ApiException("发票号码不能为空");
                }

                if (request.ActualInvoiceInfo.InvoiceDate == null)
                {
                    throw new ApiException("开票日期不能为空");
                }

                if (request.ActualInvoiceInfo.InvoiceAmount == null || request.ActualInvoiceInfo.InvoiceAmount <= 0)
                {
                    throw new ApiException("开票金额必须大于0");
                }

                // 验证其他必要字段
                if (string.IsNullOrWhiteSpace(request.ActualInvoiceInfo.InvoicingDetails))
                {
                    throw new ApiException("开票明细不能为空");
                }
            }

            try
            {
                // 从TokenModel获取当前用户ID
                string userId = TokenModel.Instance.id;

                // 4. 使用事务审核发票申请记录
                DbOpe_crm_invoice_application.Instance.TransDeal(() =>
                {
                    // 根据审核结果更新状态
                    if (request.AuditResult == 1) // 通过
                    {
                        // 更新发票申请状态为"已审核"
                        invoiceApplication.AuditStatus = (int)EnumInvoiceApplicationStatus.Processed;
                    }
                    else // 拒绝
                    {
                        // 更新发票申请状态为"已拒绝"
                        invoiceApplication.AuditStatus = (int)EnumInvoiceApplicationStatus.Rejected;
                        // 如果是到账开票类型，删除初始匹配记录
                        if (invoiceApplication.BillingType == (int)EnumBillingType.InvoicingUponReceipt &&
                            !string.IsNullOrEmpty(invoiceApplication.ReceiptId))
                        {
                            try
                            {
                                // 查找并删除初始匹配记录
                                var matchingRecord = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m =>
                                    m.ReceiptId == invoiceApplication.ReceiptId &&
                                    m.InvoiceApplicationId == invoiceApplication.Id &&
                                    m.Deleted != true);

                                if (matchingRecord != null)
                                {
                                    // 逻辑删除匹配记录
                                    matchingRecord.Deleted = true;
                                    matchingRecord.UpdateUser = userId;
                                    matchingRecord.UpdateDate = DateTime.Now;
                                    DbOpe_crm_invoice_receipt_matching.Instance.Update(matchingRecord);

                                    LogUtil.AddLog($"审核拒绝：删除到账开票初始匹配记录，ID: {matchingRecord.Id}，到账ID: {invoiceApplication.ReceiptId}");
                                }
                            }
                            catch (Exception ex)
                            {
                                LogUtil.AddErrorLog($"删除到账开票初始匹配记录异常: {ex.Message}");
                                // 不抛出异常，让审核流程正常完成
                            }
                        }
                    }

                    // 更新发票申请记录
                    invoiceApplication.UpdateUser = userId;
                    invoiceApplication.UpdateDate = DateTime.Now;
                    // 更新发票后台备注
                    invoiceApplication.InvoiceBackgroundRemark = request.InvoiceBackgroundRemark;

                    // 更新发票申请
                    DbOpe_crm_invoice_application.Instance.Update(invoiceApplication);

                    // 查找是否已有该申请的审核记录
                    var existingReviews = DbOpe_crm_invoice_review.Instance.GetDataList(
                        r => r.InvoiceApplicationId == request.Id &&
                             r.Deleted != true)
                        .OrderByDescending(r => r.CreateDate)
                        .ToList();

                    if (existingReviews != null && existingReviews.Count > 0)
                    {
                        // 有历史记录，且当前申请状态是"已拒绝"被驳回后重新提交
                        // 将以前的审核记录标记为删除（软删除），保留历史并避免数据混乱
                        foreach (var review in existingReviews)
                        {
                            review.Deleted = true;
                            review.UpdateUser = userId;
                            review.UpdateDate = DateTime.Now;
                            DbOpe_crm_invoice_review.Instance.Update(review);
                            LogUtil.AddLog($"标记旧审核记录为删除状态，ID: {review.Id}, 申请ID: {request.Id}");
                        }
                    }

                    // 创建新的审核记录
                    var reviewRecord = new Db_crm_invoice_review
                    {
                        Id = Guid.NewGuid().ToString(),
                        InvoiceApplicationId = request.Id,
                        ContractId = invoiceApplication.ContractId,
                        ProcessorId = userId,
                        ProcessTime = DateTime.Now,
                        AuditStatus = (request.AuditResult == 1) ?
                            (int)EnumInvoiceReviewStatus.WaitingAudit :
                            (int)EnumInvoiceReviewStatus.Rejected,
                        ProcessorRemark = request.AuditComments,
                        Deleted = false,
                        CreateUser = userId,
                        CreateDate = DateTime.Now,
                        UpdateUser = userId,
                        UpdateDate = DateTime.Now
                    };

                    // 如果审核通过，还需要记录发票相关信息
                    if (request.AuditResult == 1 && request.ActualInvoiceInfo != null)
                    {
                        // 填充发票信息
                        reviewRecord.InvoiceNumber = request.ActualInvoiceInfo.InvoiceNumber;
                        reviewRecord.InvoicingDate = request.ActualInvoiceInfo.InvoiceDate;
                        reviewRecord.InvoicedAmount = request.ActualInvoiceInfo.InvoiceAmount ?? 0;
                        reviewRecord.BillingCompany = request.ActualInvoiceInfo.BillingCompanyId;
                        reviewRecord.BillingHeader = request.ActualInvoiceInfo.BillingHeader;
                        reviewRecord.InvoicingDetails = request.ActualInvoiceInfo.InvoicingDetails;
                        reviewRecord.InvoiceType = (int)request.ActualInvoiceInfo.InvoiceType;
                        reviewRecord.IsPersonalInvoice = request.ActualInvoiceInfo.IsPersonalInvoice ?? false;
                        var ocrInvoiceInfo = DbOpe_crm_invoice_recognition.Instance.GetData(r => r.InvoiceApplicationId == request.Id && r.Deleted != true);
                        if (ocrInvoiceInfo != null)
                        {
                            reviewRecord.CreditCode = ocrInvoiceInfo.BuyerTaxCode;
                        }
                        else
                        {
                            throw new ApiException("未找到发票上传识别结果");
                        }
                    }

                    // 插入审核记录
                    DbOpe_crm_invoice_review.Instance.Insert(reviewRecord);

                    LogUtil.AddLog($"创建新审核记录: {request.Id}, 结果: {(request.AuditResult == 1 ? "通过" : "拒绝")}");

                    // 更新发票显示状态
                    UpdateInvoiceDisplayStatus(request.Id);
                });

                return true;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"审核发票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"审核发票申请系统异常: {ex.Message}");
                throw new ApiException("审核发票申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 复核发票申请
        /// </summary>
        /// <param name="request">复核请求</param>
        /// <returns>是否复核成功</returns>
        public bool ReviewInvoiceApplication(ReviewInvoiceApplicationRequest request)
        {
            if (request == null)
            {
                throw new ApiException("请求参数不能为空");
            }

            // 1. 验证发票申请是否存在
            var invoiceApplication = DbOpe_crm_invoice_application.Instance.QueryByPrimaryKey(request.Id);
            if (invoiceApplication == null)
            {
                throw new ApiException("未找到发票申请记录");
            }

            // 2. 验证发票申请状态是否为"待复核"
            if (invoiceApplication.AuditStatus != (int)EnumInvoiceApplicationStatus.Processed)
            {
                throw new ApiException("只有待复核状态的发票申请才能审核");
            }

            try
            {
                // 从TokenModel获取当前用户ID
                string userId = TokenModel.Instance.id;

                // 3. 使用事务进行复核
                DbOpe_crm_invoice_application.Instance.TransDeal(() =>
                {
                    // 根据复核结果更新状态
                    if (request.ReviewResult == 1) // 通过
                    {
                        // 更新发票申请状态为"已完成"
                        invoiceApplication.AuditStatus = (int)EnumInvoiceApplicationStatus.Completed;
                        invoiceApplication.IsReminded = (int)EnumIsReminder.UnUrgedTicket;
                    }
                    else // 驳回
                    {
                        // 更新发票申请状态 驳回后还是申请中 需要经办人重新提交
                        invoiceApplication.AuditStatus = (int)EnumInvoiceApplicationStatus.Applied;

                        // 如果是到账开票类型，删除初始匹配记录
                        if (invoiceApplication.BillingType == (int)EnumBillingType.InvoicingUponReceipt &&
                            !string.IsNullOrEmpty(invoiceApplication.ReceiptId))
                        {
                            try
                            {
                                // 查找并删除初始匹配记录
                                var matchingRecord = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m =>
                                    m.ReceiptId == invoiceApplication.ReceiptId &&
                                    m.InvoiceApplicationId == invoiceApplication.Id &&
                                    m.Deleted != true);

                                if (matchingRecord != null)
                                {
                                    // 逻辑删除匹配记录
                                    matchingRecord.Deleted = true;
                                    matchingRecord.UpdateUser = userId;
                                    matchingRecord.UpdateDate = DateTime.Now;
                                    DbOpe_crm_invoice_receipt_matching.Instance.Update(matchingRecord);

                                    LogUtil.AddLog($"复核驳回：删除到账开票初始匹配记录，ID: {matchingRecord.Id}，到账ID: {invoiceApplication.ReceiptId}");
                                }
                            }
                            catch (Exception ex)
                            {
                                LogUtil.AddErrorLog($"删除到账开票初始匹配记录异常: {ex.Message}");
                                // 不抛出异常，让审核流程正常完成
                            }
                        }
                    }

                    // 更新发票申请记录
                    invoiceApplication.UpdateUser = userId;
                    invoiceApplication.UpdateDate = DateTime.Now;
                    // 更新发票后台备注
                    invoiceApplication.InvoiceBackgroundRemark = request.InvoiceBackgroundRemark;

                    // 更新发票申请
                    DbOpe_crm_invoice_application.Instance.Update(invoiceApplication);

                    // 查找最新的一条审核记录
                    var currentReview = DbOpe_crm_invoice_review.Instance.GetDataList(
                        r => r.InvoiceApplicationId == request.Id &&
                             r.Deleted != true)
                        .OrderByDescending(r => r.CreateDate)
                        .FirstOrDefault();

                    if (currentReview == null)
                    {
                        // 未找到审核记录，记录错误日志
                        LogUtil.AddErrorLog($"复核发票申请失败: 未找到该申请的审核记录，ID: {request.Id}");
                        throw new ApiException("未找到该申请的审核记录，无法完成复核");
                    }

                    // 更新审核记录的审核状态
                    currentReview.AuditStatus = (request.ReviewResult == 1) ?
                        (int)EnumInvoiceReviewStatus.Approved :
                        (int)EnumInvoiceReviewStatus.ReviewRejected;

                    // 更新审核人信息
                    currentReview.AuditorId = userId;
                    currentReview.AuditTime = DateTime.Now;
                    currentReview.AuditFeedback = request.ReviewComments;
                    currentReview.UpdateUser = userId;
                    currentReview.UpdateDate = DateTime.Now;

                    // 更新审核记录
                    DbOpe_crm_invoice_review.Instance.Update(currentReview);

                    LogUtil.AddLog($"复核发票申请记录: {request.Id}, 结果: {(request.ReviewResult == 1 ? "通过" : "驳回")}");

                    // 如果审核通过，归档到正式发票表
                    if (request.ReviewResult == 1)
                    {
                        // 使用当前审核记录填充发票信息
                        var reviewInfo = currentReview;

                        // 创建正式发票记录
                        var formalInvoice = new Db_crm_invoice
                        {
                            Id = Guid.NewGuid().ToString(),
                            ContractId = reviewInfo.ContractId,
                            InvoiceApplicationId = request.Id,
                            InvoiceReviewId = reviewInfo.Id,
                            BillingCompany = reviewInfo.BillingCompany,
                            InvoiceType = reviewInfo.InvoiceType,
                            InvoicingDetails = reviewInfo.InvoicingDetails,
                            InvoicingDate = reviewInfo.InvoicingDate,
                            InvoicedAmount = reviewInfo.InvoicedAmount,
                            TotalAmount = reviewInfo.InvoicedAmount, // 如果没有税额信息，总金额等于开票金额
                            TaxAmount = 0, // 默认税额为0，实际应从发票或OCR识别结果中获取
                            InvoiceNumber = reviewInfo.InvoiceNumber,
                            InvoiceCode = "", // 发票代码需要从OCR识别结果中获取
                            BillingHeader = reviewInfo.BillingHeader,
                            BillingType = invoiceApplication.BillingType,
                            CreditCode = reviewInfo.CreditCode,
                            IsPersonalInvoice = reviewInfo.IsPersonalInvoice,
                            MatchingStatus = 1, // 1-未到账
                            // 判断是否为补充发票类型，如果是则设置IsSupplementInvoice为true
                            IsSupplementInvoice = invoiceApplication.BillingType == (int)EnumBillingType.SupplementInvoice,
                            RefundStatus = (int)EnumInvoiceRefundStatus.Normal,
                            Recipient = invoiceApplication.Recipient,
                            Email = invoiceApplication.Email,
                            Remark = invoiceApplication.InvoiceBackgroundRemark,
                            Deleted = false,
                            CreateUser = userId,
                            CreateDate = DateTime.Now,
                            UpdateUser = userId,
                            UpdateDate = DateTime.Now
                        };

                        // 如果是到账开票类型，设置到账记录ID并将匹配状态设为已匹配
                        if (invoiceApplication.BillingType == (int)EnumBillingType.InvoicingUponReceipt &&
                            !string.IsNullOrEmpty(invoiceApplication.ReceiptId))
                        {
                            formalInvoice.ReceiptId = invoiceApplication.ReceiptId;
                            formalInvoice.MatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccess; // 5-匹配成功
                        }



                        // 保存正式发票信息
                        DbOpe_crm_invoice.Instance.Insert(formalInvoice);

                        LogUtil.AddLog($"发票已归档至正式发票表，ID: {formalInvoice.Id}");

                        // 如果是到账开票类型，创建发票-到账匹配关系记录
                        if (invoiceApplication.BillingType == (int)EnumBillingType.InvoicingUponReceipt &&
                            !string.IsNullOrEmpty(invoiceApplication.ReceiptId))
                        {
                            // 先检查是否存在初始匹配记录
                            var existingMatching = DbOpe_crm_invoice_receipt_matching.Instance.GetData(m =>
                                m.ReceiptId == invoiceApplication.ReceiptId &&
                                m.InvoiceApplicationId == request.Id &&
                                m.Deleted != true);

                            if (existingMatching != null)
                            {
                                // 更新初始匹配记录
                                existingMatching.InvoiceId = formalInvoice.Id;
                                existingMatching.MatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccess; // 匹配成功
                                existingMatching.AuditorId = userId;
                                existingMatching.AuditTime = DateTime.Now;
                                existingMatching.AuditRemark = "到账开票复核通过，更新为匹配成功";
                                existingMatching.UpdateUser = userId;
                                existingMatching.UpdateDate = DateTime.Now;

                                // 更新匹配记录
                                DbOpe_crm_invoice_receipt_matching.Instance.Update(existingMatching);

                                LogUtil.AddLog($"更新初始匹配记录为匹配成功，ID: {existingMatching.Id}，" +
                                               $"发票ID: {formalInvoice.Id}，到账ID: {invoiceApplication.ReceiptId}");
                            }
                            else
                            {
                                // 创建新的匹配关系记录
                                var matchingRecord = new Db_crm_invoice_receipt_matching
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    InvoiceId = formalInvoice.Id,
                                    ReceiptId = invoiceApplication.ReceiptId,
                                    InvoiceApplicationId = request.Id,
                                    MatchingStatus = (int)EnumInvoiceMatchingStatus.MatchSuccess, // 匹配成功
                                    MatchingTime = DateTime.Now,
                                    MatchingUser = userId,
                                    AuditorId = userId,
                                    AuditTime = DateTime.Now,
                                    AuditRemark = "到账开票自动匹配",
                                    IsRecommended = false, // 非系统推荐，而是人工指定
                                    Remark = "到账开票自动创建的匹配关系",
                                    Deleted = false,
                                    CreateUser = userId,
                                    CreateDate = DateTime.Now,
                                    UpdateUser = userId,
                                    UpdateDate = DateTime.Now
                                };

                                // 保存匹配关系记录
                                DbOpe_crm_invoice_receipt_matching.Instance.Insert(matchingRecord);

                                LogUtil.AddLog($"创建发票-到账匹配关系记录成功，ID: {matchingRecord.Id}，" +
                                               $"发票ID: {formalInvoice.Id}，到账ID: {invoiceApplication.ReceiptId}");
                            }
                        }

                        // 只有在成功归档后才发送通知邮件
                        SendInvoiceNotificationEmail(request.Id);
                    }
                });

                // 更新发票显示状态
                UpdateInvoiceDisplayStatus(request.Id);

                return true;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"复核发票申请业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"复核发票申请系统异常: {ex.Message}");
                throw new ApiException("复核发票申请失败，请联系系统管理员");
            }
        }

        /// <summary>
        /// 发送发票通知邮件
        /// </summary>
        /// <param name="applicationId">发票申请ID</param>
        private void SendInvoiceNotificationEmail(string applicationId)
        {
            try
            {
                // 获取申请详情信息
                var application = DbOpe_crm_invoice_application.Instance.GetData(a => a.Id == applicationId && a.Deleted != true);
                if (application == null || string.IsNullOrEmpty(application.Email))
                {
                    LogUtil.AddLog($"未发送邮件通知：申请ID {applicationId} 不存在或邮箱地址为空");
                    return;
                }

                // 获取合同信息
                var contract = DbOpe_crm_contract.Instance.GetData(c => c.Id == application.ContractId && c.Deleted != true);
                string contractNumber = contract?.ContractNo ?? "未知";
                string contractName = contract?.ContractName ?? "未知";

                // 从正式发票表获取发票信息
                var formalInvoice = DbOpe_crm_invoice.Instance.GetData(
                    i => i.InvoiceApplicationId == applicationId && i.Deleted != true);

                if (formalInvoice == null)
                {
                    LogUtil.AddLog($"未找到正式发票记录，申请ID: {applicationId}，无法发送邮件");
                    return;
                }

                string invoiceNumber = formalInvoice.InvoiceNumber ?? "未知";
                decimal invoiceAmount = formalInvoice.InvoicedAmount;

                // 邮件主题
                string subject = $"环球慧思电子发票开具通知 - 合同：{contractNumber}";

                // 邮件内容
                string body = $@"<html>
<body>
<p>尊敬的客户：</p>
<p>您好！</p>
<p>您的发票已开具完成，具体信息如下：</p>
<ul>
<li>合同编号：{contractNumber}</li>
<li>合同名称：{contractName}</li>
<li>发票号码：{invoiceNumber}</li>
<li>发票金额：{invoiceAmount:F2}元</li>
</ul>
<p>请注意查收实物发票或电子发票。如有任何疑问，请与我们联系。</p>
<p>此致，</p>
<p>敬礼！</p>
</body>
</html>";
                List<BM_FileInfo> fileList = DbOpe_crm_contract_invoice_attachment.Instance.GetDataList(r => r.ContractInvoiceId == applicationId.ToString()).Select(r => new BM_FileInfo { Id = r.Id, FileName = r.FileName, FilePath = r.FilePath, CreateDate = r.CreateDate.ToString() }).ToList();
                // 使用SendElectronicInvoiceServiceEmail方法发送电子发票邮件
                Com_EmailHelper.SendElectronicInvoiceServiceEmail(application.Email, subject, fileList);
                LogUtil.AddLog($"已发送发票通知邮件至：{application.Email}");
            }
            catch (Exception ex)
            {
                // 邮件发送失败不影响整体流程
                LogUtil.AddErrorLog($"发送发票通知邮件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取发票OCR比对结果详情
        /// </summary>
        /// <param name="applicationId">发票申请ID</param>
        /// <returns>OCR比对结果详情</returns>
        public InvoiceOcrCompareDetail GetInvoiceOcrCompareDetail(string applicationId)
        {
            if (string.IsNullOrEmpty(applicationId))
            {
                throw new ApiException("发票申请ID不能为空");
            }

            try
            {
                // 1. 获取发票申请信息
                var application = DbOpe_crm_invoice_application.Instance.GetData(a => a.Id == applicationId && a.Deleted != true);
                if (application == null)
                {
                    throw new ApiException("未找到发票申请记录");
                }

                // 创建返回结果
                var result = new InvoiceOcrCompareDetail
                {
                    ApplicationId = applicationId,
                    ApplicationInfo = new InvoiceApplicationInfo
                    {
                        Id = application.Id,
                        ContractId = application.ContractId,
                        BillingType = (EnumBillingType)application.BillingType,
                        AppliedAmount = application.AppliedAmount,
                        BillingCompany = application.BillingCompany,
                        BillingHeaderName = application.BillingHeader,
                        BillingCompanyName = RedisCache.CollectingCompany.GetCollectingCompanyById(application.BillingCompany)?.SellerName ?? string.Empty,
                        ApplicantId = application.ApplicantId,
                        ApplicantName = RedisCache.UserWithOrg.GetUserById(application.ApplicantId)?.UserWithOrgFullName ?? string.Empty,
                        ApplyTime = application.ApplyTime,
                        AuditStatus = (EnumInvoiceApplicationStatus)application.AuditStatus,
                        DisplayStatus = (EnumInvoiceDisplayStatus)(application.DisplayStatus ?? 0),
                        InvoiceType = (EnumInvoiceType)application.InvoiceType,
                        BillingHeader = application.BillingHeader,
                        CreditCode = application.CreditCode,
                        Recipient = application.Recipient,
                        Email = application.Email,
                        Remark = application.Remark,
                        ExpectedInvoicingDate = application.ExpectedInvoicingDate,
                        InvoiceDetails = application.InvoicingDetails
                    },
                    ComparisonResults = new List<FieldComparisonResult>(),
                    OcrInfo = new OcrInvoiceResult()
                };

                // 2. 获取OCR识别结果
                var ocrRecord = DbOpe_crm_invoice_recognition.Instance.GetData(r => r.InvoiceApplicationId == applicationId && r.Deleted != true);
                if (ocrRecord != null)
                {
                    result.OcrInfo = new OcrInvoiceResult
                    {
                        RecognitionId = ocrRecord.Id,
                        InvoiceCode = ocrRecord.InvoiceCode,
                        InvoiceNumber = ocrRecord.InvoiceNumber,
                        InvoiceDate = ocrRecord.InvoiceDate.Value.ToString("yyyy-MM-dd"),
                        BuyerName = ocrRecord.BuyerName,
                        BuyerTaxNumber = ocrRecord.BuyerTaxCode,
                        SellerName = ocrRecord.SellerName,
                        SellerTaxNumber = ocrRecord.SellerTaxCode,
                        TotalAmount = ocrRecord.InvoiceAmount ?? 0,
                        TotalTax = ocrRecord.TaxAmount ?? 0,
                        TotalAmountWithTax = (ocrRecord.TotalAmount ?? 0).ToString("F2"),
                        OriginalText = ocrRecord.InvoiceDetails
                    };
                    result.OcrInfo.CollectingCompanyId = RedisCache.CollectingCompany.GetCollectingCompanyBySellerName(ocrRecord.SellerName)?.Id ?? string.Empty;

                    result.OriginalText = ocrRecord.InvoiceDetails;
                }

                // 3. 获取比对结果
                var comparisonResults = DbOpe_crm_invoice_comparison.Instance.GetDataList(c => c.InvoiceApplicationId == applicationId && c.Deleted != true);
                if (comparisonResults != null && comparisonResults.Count > 0)
                {
                    foreach (var comparison in comparisonResults)
                    {
                        // 解析比对详情，格式："字段:发票金额,OCR值:100,申请值:100,是否匹配:是"
                        var details = comparison.ComparisonDetails.Split(',');
                        if (details.Length >= 3)
                        {
                            var fieldNamePart = details[0].Split(':');
                            var ocrValuePart = details[1].Split(':');
                            var applValuePart = details[2].Split(':');
                            var isMatchPart = details.Length > 3 ? details[3].Split(':') : new string[] { "是否匹配", "否" };

                            var fieldName = fieldNamePart.Length > 1 ? fieldNamePart[1] : "";
                            var ocrValue = ocrValuePart.Length > 1 ? ocrValuePart[1] : "";
                            var applValue = applValuePart.Length > 1 ? applValuePart[1] : "";
                            var isMatch = isMatchPart.Length > 1 && isMatchPart[1] == "是";

                            result.ComparisonResults.Add(new FieldComparisonResult
                            {
                                FieldName = fieldName,
                                ActualValue = ocrValue,
                                ExpectedValue = applValue,
                                IsMatch = isMatch
                            });
                        }
                    }

                    // 计算匹配率
                    if (result.ComparisonResults.Count > 0)
                    {
                        result.IsMatch = result.ComparisonResults.Count(r => r.IsMatch) > 0;
                    }
                }

                // 4. 获取审核记录和修改字段信息
                var reviewRecord = DbContext.Crm2Db.Queryable<Db_crm_invoice_review>()
                    .Where(r => r.InvoiceApplicationId == applicationId && r.Deleted != true)
                    .Select(r => new InvoiceReviewInfo
                    {
                    }, true)
                    .Mapper(t =>
                    {
                        t.ProcessorName = GetAuditorNameFromCache(t.ProcessorId);
                        t.AuditorName = GetAuditorNameFromCache(t.AuditorId);
                        t.BillingCompanyName = RedisCache.CollectingCompany.GetCollectingCompanyById(t.BillingCompany)?.CollectingCompanyName ?? "";
                    })
                    .OrderByDescending(r => r.AuditTime)
                    .First();

                if (reviewRecord != null)
                {
                    result.ReviewInfo = reviewRecord;

                    // 获取修改字段信息
                    result.ModifiedFields = reviewRecord.ModifiedFields;
                }

                return result;
            }
            catch (ApiException ex)
            {
                LogUtil.AddErrorLog($"获取发票OCR比对结果详情业务异常: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取发票OCR比对结果详情系统异常: {ex.Message}");
                throw new ApiException("获取发票OCR比对结果详情失败，请联系系统管理员");
            }
        }

        #endregion

        #region 发票显示状态更新方法

        /// <summary>
        /// 更新发票申请的显示状态（DisplayStatus）
        /// </summary>
        /// <param name="invoiceApplicationId">发票申请ID</param>
        /// <returns>更新后的显示状态</returns>
        private int UpdateInvoiceDisplayStatus(string invoiceApplicationId)
        {
            try
            {
                // 获取发票申请记录
                var application = DbOpe_crm_invoice_application.Instance.GetData(a => a.Id == invoiceApplicationId && a.Deleted != true);
                if (application == null)
                {
                    LogUtil.AddErrorLog($"更新显示状态失败：未找到发票申请记录，ID: {invoiceApplicationId}");
                    return 0;
                }

                // 获取相关发票记录
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => i.InvoiceApplicationId == invoiceApplicationId && i.Deleted != true);

                // 获取相关退票申请记录
                var refundApplication = invoice != null
                    ? DbOpe_crm_invoice_refund_application.Instance.GetData(r => r.InvoiceId == invoice.Id && r.Deleted != true)
                    : null;

                // 计算显示状态
                int displayStatus = 0;
                string statusName = string.Empty;

                // 根据发票申请状态确定显示状态
                switch (application.AuditStatus)
                {
                    case (int)EnumInvoiceApplicationStatus.Applied:
                        // 默认状态为发票申请中
                        displayStatus = (int)EnumInvoiceDisplayStatus.InvoiceApplied;
                        statusName = "发票申请中";

                        // 查询最新的审核记录，检查是否是复核驳回导致的申请中状态
                        var latestReviewForApplied = DbOpe_crm_invoice_review.Instance.GetDataList(
                            r => r.InvoiceApplicationId == invoiceApplicationId && r.Deleted != true)
                            .OrderByDescending(r => r.AuditTime)
                            .FirstOrDefault();

                        // 如果最新的审核记录状态是复核驳回，则显示状态应为发票复核驳回
                        if (latestReviewForApplied != null && latestReviewForApplied.AuditStatus == (int)EnumInvoiceReviewStatus.ReviewRejected)
                        {
                            displayStatus = (int)EnumInvoiceDisplayStatus.InvoiceReviewRejected;
                            statusName = "发票复核驳回";
                            LogUtil.AddLog($"发票复核驳回状态已更新(申请中)，申请ID: {invoiceApplicationId}");
                        }
                        break;

                    case (int)EnumInvoiceApplicationStatus.Processed:
                        // 默认状态为待复核
                        displayStatus = (int)EnumInvoiceDisplayStatus.InvoiceProcessed;
                        statusName = "发票待复核";

                        // 查询最新的审核记录
                        var latestReview = DbOpe_crm_invoice_review.Instance.GetDataList(
                            r => r.InvoiceApplicationId == invoiceApplicationId && r.Deleted != true)
                            .OrderByDescending(r => r.AuditTime)
                            .FirstOrDefault();

                        // 检查是否有复核驳回状态的审核记录
                        if (latestReview != null && latestReview.AuditStatus == (int)EnumInvoiceReviewStatus.ReviewRejected)
                        {
                            displayStatus = (int)EnumInvoiceDisplayStatus.InvoiceReviewRejected;
                            statusName = "发票复核驳回";
                            LogUtil.AddLog($"发票复核驳回状态已更新，申请ID: {invoiceApplicationId}");
                        }
                        break;

                    case (int)EnumInvoiceApplicationStatus.Rejected:
                        displayStatus = (int)EnumInvoiceDisplayStatus.InvoiceRejected;
                        statusName = "发票申请被拒绝";
                        break;

                    case (int)EnumInvoiceApplicationStatus.Completed:
                        // 发票申请已完成，检查退票状态
                        if (invoice != null)
                        {
                            // 检查是否有退票申请
                            if (refundApplication != null)
                            {
                                // 根据退票申请状态设置显示状态
                                switch (refundApplication.AuditStatus)
                                {
                                    case (int)EnumRefundApplicationStatus.Applied:
                                        displayStatus = (int)EnumInvoiceDisplayStatus.RefundApplied;
                                        statusName = "退票申请中";

                                        // 检查是否是复核驳回导致的状态变更为申请中
                                        var refundReview = DbOpe_crm_invoice_refund_review.Instance.GetDataList(r =>
                                            r.RefundApplicationId == refundApplication.Id &&
                                            r.AuditStatus == (int)EnumRefundReviewStatus.RefundReviewRejected &&
                                            r.Deleted != true)
                                            .OrderByDescending(r => r.UpdateDate)
                                            .FirstOrDefault();

                                        if (refundReview != null)
                                        {
                                            // 存在退票复核驳回的记录，显示为退票复核驳回
                                            displayStatus = (int)EnumInvoiceDisplayStatus.RefundReviewRejected;
                                            statusName = "退票复核驳回";
                                        }
                                        break;

                                    case (int)EnumRefundApplicationStatus.Processed:
                                        displayStatus = (int)EnumInvoiceDisplayStatus.RefundProcessed;
                                        statusName = "退票待复核";
                                        break;

                                    //case (int)EnumRefundApplicationStatus.Rejected:
                                    //    displayStatus = (int)EnumInvoiceDisplayStatus.RefundRejected;
                                    //    statusName = "退票申请被拒绝";
                                    //    break;

                                    case (int)EnumRefundApplicationStatus.Completed:
                                        // 判断是部分退票还是全部退票
                                        if (invoice.RefundStatus == (int)EnumInvoiceRefundStatus.PartiallyRefunded)
                                        {
                                            displayStatus = (int)EnumInvoiceDisplayStatus.PartiallyRefunded;
                                            statusName = "部分退票";
                                        }
                                        else // 全部退票
                                        {
                                            displayStatus = (int)EnumInvoiceDisplayStatus.FullyRefunded;
                                            statusName = "全部退票";
                                        }
                                        break;

                                    default:
                                        displayStatus = (int)EnumInvoiceDisplayStatus.InvoiceCompleted;
                                        statusName = "发票已完成";
                                        break;
                                }
                            }
                            else
                            {
                                // 没有退票申请，根据匹配状态设置显示状态
                                displayStatus = (int)EnumInvoiceDisplayStatus.InvoiceCompleted;


                            }
                        }
                        else
                        {
                            // 异常情况：发票申请已完成但没有找到对应的发票记录
                            displayStatus = (int)EnumInvoiceDisplayStatus.InvoiceCompleted;
                            statusName = "发票已完成(无发票记录)";
                            LogUtil.AddErrorLog($"发票申请已完成但未找到对应的发票记录，申请ID: {invoiceApplicationId}");
                        }
                        break;

                    case (int)EnumInvoiceApplicationStatus.Invalidated:
                        displayStatus = (int)EnumInvoiceDisplayStatus.InvoiceCancelled;
                        statusName = "发票已作废";
                        break;

                    default:
                        displayStatus = 0;
                        statusName = "未知状态";
                        break;
                }

                // 更新发票申请的显示状态
                application.DisplayStatus = displayStatus;
                application.DisplayStatusName = statusName;
                application.UpdateDate = DateTime.Now;
                DbOpe_crm_invoice_application.Instance.Update(application);

                LogUtil.AddLog($"更新发票申请显示状态成功，ID: {invoiceApplicationId}, 状态: {statusName}({displayStatus})");

                return displayStatus;
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新发票申请显示状态失败: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region 发票备注修改
        /// <summary>
        /// 更新发票备注(开具发票，仅对后台管理人员开放，仅支持普票、专票)
        /// </summary>
        /// <param name="invoiceApplicationId">发票申请ID</param>
        /// <param name="remark">备注</param>
        /// <exception cref="ApiException">更新失败</exception>
        public void UpdateInvoiceRemarkByApplicationId(string invoiceApplicationId, string remark)
        {
            try
            {
                // 获取发票申请记录
                var application = DbOpe_crm_invoice_application.Instance.GetData(a => a.Id == invoiceApplicationId && a.Deleted != true);
                if (application == null)
                {
                    LogUtil.AddErrorLog($"更新发票备注失败：未找到发票申请记录，ID: {invoiceApplicationId}");
                    throw new ApiException("更新发票备注失败：未找到发票申请记录");
                }
                if (application.InvoiceType != (int)EnumInvoiceType.UniversalTicket && application.InvoiceType != (int)EnumInvoiceType.SpecialTicket)
                {
                    throw new ApiException("更新发票备注失败：仅支持普票、专票");
                }
                // 判断用户是否为后台管理人员
                bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                // 判断权限，如果是后台人员 获取发票后台备注 (还没开票的取申请表的发票备注，已经开票的，如果已经开始退票，取退票备注，否则取发票备注；)
                if (isBackendManager)
                {
                    var invoiceInfo = DbOpe_crm_invoice.Instance.GetData(i => i.InvoiceApplicationId == invoiceApplicationId && i.Deleted != true);

                    if (invoiceInfo == null)
                    {
                        //如果还没有开票，更新申请表的发票备注
                        application.InvoiceBackgroundRemark = remark;
                        application.UpdateDate = DateTime.Now;
                        application.UpdateUser = UserId;
                        DbOpe_crm_invoice_application.Instance.Update(application);
                    }
                    else
                    {
                        var refundApplicationInfo = DbOpe_crm_invoice_refund_application.Instance.GetData(r => r.InvoiceId == invoiceInfo.Id && r.Deleted != true);
                        if (refundApplicationInfo != null)
                        {
                            var refundInfo = DbOpe_crm_invoice.Instance.GetData(r => r.Id == refundApplicationInfo.Id && r.Deleted != true);
                            if (refundInfo != null)
                            {
                                //如果退票成功，更新退票发票备注 （invoice的remark）
                                refundInfo.Remark = remark;
                                refundInfo.UpdateDate = DateTime.Now;
                                refundInfo.UpdateUser = UserId;
                                DbOpe_crm_invoice.Instance.Update(refundInfo);
                            }
                            else
                            {
                                //如果退票未成功，取退票申请表的发票备注 （refund_application的refund_background_remark）
                                refundApplicationInfo.RefundBackgroundRemark = remark;
                                refundApplicationInfo.UpdateDate = DateTime.Now;
                                refundApplicationInfo.UpdateUser = UserId;
                                DbOpe_crm_invoice_refund_application.Instance.Update(refundApplicationInfo);
                            }
                        }
                        else
                        {
                            //如果还没有退票，取发票备注 （invoice的remark）
                            invoiceInfo.Remark = remark;
                            invoiceInfo.UpdateDate = DateTime.Now;
                            invoiceInfo.UpdateUser = UserId;
                            DbOpe_crm_invoice.Instance.Update(invoiceInfo);
                        }
                    }
                }
                else
                {
                    //如果不是后台人员，不允许更新
                    throw new ApiException("没有权限更新发票备注");
                }
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新发票备注失败: {ex.Message}");
                throw new ApiException("更新发票备注失败");
            }
        }

        /// <summary>
        /// 更新发票备注(开票查询/到账开票查询，仅对后台管理人员开放,仅支持普票、专票)
        /// </summary>
        /// <param name="invoiceId">发票ID</param>
        /// <param name="remark">备注</param>
        /// <exception cref="ApiException">更新失败</exception>
        public void UpdateInvoiceRemark(string invoiceId, string remark)
        {
            try
            {
                // 获取发票记录
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => i.Id == invoiceId && i.Deleted != true);
                if (invoice == null)
                {
                    throw new ApiException("更新发票备注失败：未找到发票记录");
                }
                if (invoice.InvoiceType != (int)EnumInvoiceType.UniversalTicket && invoice.InvoiceType != (int)EnumInvoiceType.SpecialTicket)
                {
                    throw new ApiException("更新发票备注失败：仅支持普票、专票");
                }
                // 判断用户是否为后台管理人员
                bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                if (!isBackendManager)
                {
                    throw new ApiException("没有权限更新发票备注");
                }
                // 更新发票备注 
                invoice.Remark = remark;
                invoice.UpdateDate = DateTime.Now;
                invoice.UpdateUser = UserId;
                DbOpe_crm_invoice.Instance.Update(invoice);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"更新发票备注失败: {ex.Message}");
                throw new ApiException("更新发票备注失败");
            }
        }
        #endregion

        #region 发票强制忽略
        /// <summary>
        /// 发票强制忽略
        /// </summary>
        /// <param name="invoiceApplicationId">发票申请ID</param>
        /// <param name="isIgnore">是否忽略</param>
        public void ForceIgnoreInvoiceApplication(string invoiceApplicationId,bool isIgnore)
        {
            try
            {
                // 获取发票申请记录
                var application = DbOpe_crm_invoice_application.Instance.GetData(a => a.Id == invoiceApplicationId && a.Deleted != true);
                if (application == null)
                {
                    throw new ApiException("发票申请记录不存在");
                }
                application.ForcedProcessed = isIgnore;
                application.UpdateDate = DateTime.Now;
                application.UpdateUser = UserId;
                DbOpe_crm_invoice_application.Instance.Update(application);
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"发票强制忽略失败: {ex.Message}");
                throw new ApiException("发票强制忽略失败");
            }
        }
        #endregion
    }
}