﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///海关编码表
    ///</summary>
    [SugarTable("sys_hscode")]
    public class Db_sys_hscode
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:国家ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? SID { get; set; }

           /// <summary>
           /// Desc:国家
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CountryName { get; set; }

           /// <summary>
           /// Desc:进出口类型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? IAEType {get;set;}

           /// <summary>
           /// Desc:海关编码类型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? HsCodeType {get;set;}

           /// <summary>
           /// Desc:海关编码
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string HsCode {get;set;}

           /// <summary>
           /// Desc:实际时段
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string TimePeriod {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
