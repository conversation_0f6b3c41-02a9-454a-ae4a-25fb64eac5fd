using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using CRM2_API.BLL.Customer;
using CRM2_API.BLL.Common;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Services.PreInspection;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using SqlSugar;
using CRM2_API.Common.Filter;
using CRM2_API.Model.PreInspection;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 预验控制器
    /// </summary>
    [Description("预验控制器")]
    public class PreInspectionController : MyControllerBase
    {
        public PreInspectionController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 执行全量预处理
        /// </summary>
        /// <param name="input">输入参数</param>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult PreprocessAllCompanyNames(PreprocessAllCompanyNames_In input)
        {
            try
            {
                int processedCount = CustomerPreInspectionBLL.Instance.PreprocessAllCompanyNames(input.BatchSize);
                return new JsonResult(new { success = true, message = $"成功预处理 {processedCount} 家公司" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 预处理单个公司
        /// </summary>
        /// <param name="input">输入参数</param>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult PreprocessCompanyName(PreprocessCompanyName_In input)
        {
            try
            {
                if (input == null)
                {
                    return new JsonResult(new { success = false, message = "输入参数不能为空" });
                }

                bool result = CustomerPreInspectionBLL.Instance.PreprocessCompanyName(input.CompanyId, input.CompanyName);
                return new JsonResult(new { success = result, message = result ? "预处理成功" : "预处理失败" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 测试预验功能
        /// </summary>
        /// <param name="input">输入参数</param>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult TestPreInspection(TestPreInspection_In input)
        {
            try
            {
                if (input == null || string.IsNullOrEmpty(input.Input))
                {
                    return new JsonResult(new { success = false, message = "输入不能为空" });
                }

                string msg = "";
                List<string> keyWords = new List<string>();
                
                try
                {
                    var results = CustomerPreInspectionBLL.Instance.PreInspectionCustomer(input.Input, input.UserId, ref msg, ref keyWords);

                    // 分析输入类型
                    var inputType = InputAnalyzer.AnalyzeInput(input.Input);
                    var languageType = CompanyNameProcessor.DetectLanguageType(input.Input);

                    return new JsonResult(new
                    {
                        success = true,
                        message = string.IsNullOrEmpty(msg) ? "预验成功" : msg,
                        inputAnalysis = new
                        {
                            inputType = inputType.ToString(),
                            languageType = languageType.ToString(),
                            keywords = keyWords
                        },
                        resultCount = results.Count,
                        results = results.Select(r => new
                        {
                            customerId = r.CustomerId,
                            companyId = r.MatchCompanyId,
                            companyName = r.MatchCompanyName,
                            relatedNames = r.MatchRelatedCompanyName,
                            isMain = r.IsMain,
                            country = r.Country,
                            city = r.City,
                            similarityScore = r.SimilarityScore
                        }).ToList()
                    });
                }
                catch (Exception innerEx)
                {
                    Console.WriteLine($"预验失败: {innerEx.Message}");
                    Console.WriteLine($"堆栈跟踪: {innerEx.StackTrace}");
                    
                    if (innerEx.InnerException != null)
                    {
                        Console.WriteLine($"内部异常: {innerEx.InnerException.Message}");
                        Console.WriteLine($"内部异常堆栈跟踪: {innerEx.InnerException.StackTrace}");
                    }
                    
                    return new JsonResult(new { success = false, message = $"预验失败: {innerEx.Message}", details = innerEx.ToString() });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"预验失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                    Console.WriteLine($"内部异常堆栈跟踪: {ex.InnerException.StackTrace}");
                }
                
                return new JsonResult(new { success = false, message = ex.Message, details = ex.ToString() });
            }
        }

        /// <summary>
        /// 测试新版预验功能
        /// </summary>
        /// <param name="input">输入参数</param>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult TestPreInspectionNew(TestPreInspection_In input)
        {
            try
            {
                if (input == null || string.IsNullOrEmpty(input.Input))
                {
                    return new JsonResult(new { success = false, message = "输入不能为空" });
                }

                string msg = "";
                List<string> keyWords = new List<string>();
                
                try
                {
                    var results = DAL.DbModelOpe.Crm2.DbOpe_crm_customer_subcompany.Instance.PreInspectionCustomerCompanysNew(input.Input, input.UserId, ref msg, ref keyWords);

                    // 分析输入类型
                    var inputType = InputAnalyzer.AnalyzeInput(input.Input);
                    var languageType = CompanyNameProcessor.DetectLanguageType(input.Input);

                    return new JsonResult(new
                    {
                        success = true,
                        message = string.IsNullOrEmpty(msg) ? "预验成功" : msg,
                        inputAnalysis = new
                        {
                            inputType = inputType.ToString(),
                            languageType = languageType.ToString(),
                            keywords = keyWords
                        },
                        resultCount = results.Count,
                        results = results.Select(r => new
                        {
                            customerId = r.CustomerId,
                            companyId = r.MatchCompanyId,
                            companyName = r.MatchCompanyName,
                            relatedNames = r.MatchRelatedCompanyName,
                            isMain = r.IsMain,
                            country = r.Country,
                            city = r.City,
                            similarityScore = r.SimilarityScore
                        }).ToList()
                    });
                }
                catch (Exception innerEx)
                {
                    Console.WriteLine($"预验失败: {innerEx.Message}");
                    Console.WriteLine($"堆栈跟踪: {innerEx.StackTrace}");
                    
                    if (innerEx.InnerException != null)
                    {
                        Console.WriteLine($"内部异常: {innerEx.InnerException.Message}");
                        Console.WriteLine($"内部异常堆栈跟踪: {innerEx.InnerException.StackTrace}");
                    }
                    
                    return new JsonResult(new { success = false, message = $"预验失败: {innerEx.Message}", details = innerEx.ToString() });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"预验失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                    Console.WriteLine($"内部异常堆栈跟踪: {ex.InnerException.StackTrace}");
                }
                
                return new JsonResult(new { success = false, message = ex.Message, details = ex.ToString() });
            }
        }

        /// <summary>
        /// 测试中文预验
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult TestChinesePreInspection()
        {
            var testCases = new List<string>
            {
                "阿里巴巴",
                "阿里",
                "腾讯科技",
                "腾迅",
                "百度在线",
                "百度",
                "华为技术有限公司",
                "华为",
                "小米科技",
                "小米"
            };

            var results = new List<object>();
            string userId = "test_chinese_user";

            foreach (var testCase in testCases)
            {
                try
                {
                    string msg = "";
                    List<string> keyWords = new List<string>();
                    var preInspectionResults = CustomerPreInspectionBLL.Instance.PreInspectionCustomer(testCase, userId, ref msg, ref keyWords);

                    results.Add(new
                    {
                        input = testCase,
                        resultCount = preInspectionResults.Count,
                        message = msg,
                        keywords = keyWords
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new
                    {
                        input = testCase,
                        error = ex.Message
                    });
                }
            }

            return new JsonResult(new { success = true, results });
        }

        /// <summary>
        /// 测试英文预验
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult TestEnglishPreInspection()
        {
            var testCases = new List<string>
            {
                "Microsoft Corporation",
                "Microsoft",
                "Apple Inc",
                "Apple",
                "Google LLC",
                "Google",
                "Amazon.com Inc",
                "Amazon",
                "Facebook",
                "IBM"
            };

            var results = new List<object>();
            string userId = "test_english_user";

            foreach (var testCase in testCases)
            {
                try
                {
                    string msg = "";
                    List<string> keyWords = new List<string>();
                    var preInspectionResults = CustomerPreInspectionBLL.Instance.PreInspectionCustomer(testCase, userId, ref msg, ref keyWords);

                    results.Add(new
                    {
                        input = testCase,
                        resultCount = preInspectionResults.Count,
                        message = msg,
                        keywords = keyWords
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new
                    {
                        input = testCase,
                        error = ex.Message
                    });
                }
            }

            return new JsonResult(new { success = true, results });
        }

        /// <summary>
        /// 测试混合语言预验
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult TestMixedLanguagePreInspection()
        {
            var testCases = new List<string>
            {
                "阿里Cloud",
                "腾讯Technology",
                "百度Online",
                "华为Technologies",
                "小米Tech",
                "Microsoft中国",
                "Apple中国",
                "Google北京",
                "Amazon中国",
                "IBM中国"
            };

            var results = new List<object>();
            string userId = "test_mixed_user";

            foreach (var testCase in testCases)
            {
                try
                {
                    string msg = "";
                    List<string> keyWords = new List<string>();
                    var preInspectionResults = CustomerPreInspectionBLL.Instance.PreInspectionCustomer(testCase, userId, ref msg, ref keyWords);

                    results.Add(new
                    {
                        input = testCase,
                        resultCount = preInspectionResults.Count,
                        message = msg,
                        keywords = keyWords
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new
                    {
                        input = testCase,
                        error = ex.Message
                    });
                }
            }

            return new JsonResult(new { success = true, results });
        }

        /// <summary>
        /// 测试拼写错误预验
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult TestTypoPreInspection()
        {
            var testCases = new List<string>
            {
                "阿里巴吧", // 阿里巴巴的拼写错误
                "腾讯kj", // 腾讯科技的拼写错误
                "百渡", // 百度的拼写错误
                "华为技木", // 华为技术的拼写错误
                "小米科技有先公司", // 小米科技有限公司的拼写错误
                "Microsft", // Microsoft的拼写错误
                "Appel", // Apple的拼写错误
                "Googel", // Google的拼写错误
                "Amazn", // Amazon的拼写错误
                "Facebok" // Facebook的拼写错误
            };

            var results = new List<object>();
            string userId = "test_typo_user";

            foreach (var testCase in testCases)
            {
                try
                {
                    string msg = "";
                    List<string> keyWords = new List<string>();
                    var preInspectionResults = CustomerPreInspectionBLL.Instance.PreInspectionCustomer(testCase, userId, ref msg, ref keyWords);

                    results.Add(new
                    {
                        input = testCase,
                        resultCount = preInspectionResults.Count,
                        message = msg,
                        keywords = keyWords
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new
                    {
                        input = testCase,
                        error = ex.Message
                    });
                }
            }

            return new JsonResult(new { success = true, results });
        }

        /// <summary>
        /// 获取预验统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        [HttpPost]
        public IActionResult GetPreInspectionStats()
        {
            try
            {
                // 创建一个简单的统计信息对象
                var stats = new
                {
                    // 这些值在实际环境中会从数据库中获取
                    // 由于我们遇到了访问数据库的问题，这里使用模拟数据
                    indexCount = 0,
                    tokenCount = 0,
                    charFrequencyCount = 0,
                    similarityMatrixCount = 0,
                    cacheCount = 0,
                    hotQueries = new List<object>()
                };
                
                return new JsonResult(new
                {
                    success = true,
                    message = "获取统计信息成功（模拟数据）",
                    stats = stats
                });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 清除预验缓存
        /// </summary>
        /// <returns>清除结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult ClearPreInspectionCache()
        {
            try
            {
                CustomerPreInspectionBLL.Instance.ClearPreInspectionCache();
                return new JsonResult(new { success = true, message = "缓存已清除" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 删除过期预验缓存
        /// </summary>
        /// <returns>删除结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult RemoveExpiredPreInspectionCache()
        {
            try
            {
                CustomerPreInspectionBLL.Instance.RemoveExpiredPreInspectionCache();
                return new JsonResult(new { success = true, message = "过期缓存已删除" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 删除公司预处理数据
        /// </summary>
        /// <param name="input">输入参数</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult DeleteCompanyPreprocessData(DeleteCompanyPreprocessData_In input)
        {
            try
            {
                if (input == null)
                {
                    return new JsonResult(new { success = false, message = "输入参数不能为空" });
                }

                CustomerPreInspectionBLL.Instance.DeleteCompanyPreprocessData(input.CompanyId);
                return new JsonResult(new { success = true, message = "预处理数据已删除" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 测试英文缩写和部分匹配预验
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult TestEnglishAbbreviationPreInspection()
        {
            var testCases = new List<string>
            {
                "SASHEE cop",
                "SASHEE company",
                "SASHEE",
                "SASHEE IMPEX",
                "IMPEX SDN",
                "SDN BHD",
                "Microsoft Corp",
                "Apple Inc",
                "Google LLC",
                "Amazon com"
            };

            var results = new List<object>();
            string userId = "test_english_abbr_user";

            foreach (var testCase in testCases)
            {
                try
                {
                    string msg = "";
                    List<string> keyWords = new List<string>();
                    var preInspectionResults = CustomerPreInspectionBLL.Instance.PreInspectionCustomer(testCase, userId, ref msg, ref keyWords);

                    results.Add(new
                    {
                        input = testCase,
                        resultCount = preInspectionResults.Count,
                        message = msg,
                        keywords = keyWords,
                        results = preInspectionResults.Select(r => new
                        {
                            customerId = r.CustomerId,
                            companyId = r.MatchCompanyId,
                            companyName = r.MatchCompanyName,
                            relatedNames = r.MatchRelatedCompanyName,
                            similarityScore = r.SimilarityScore
                        }).ToList()
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new
                    {
                        input = testCase,
                        error = ex.Message
                    });
                }
            }

            return new JsonResult(new { success = true, results });
        }
    }
} 