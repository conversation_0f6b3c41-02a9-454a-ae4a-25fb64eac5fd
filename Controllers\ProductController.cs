﻿using CRM2_API.BLL;
using CRM2_API.BLL.BLL_Example;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    [Description("产品控制器")]
    public class ProductController : MyControllerBase
    {
        public ProductController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        /// <summary>
        /// 根据查询条件获取产品列表。
        /// </summary>
        /// <param name="searchProductListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchProductList_Out> SearchProductList(SearchProductList_In searchProductListIn)
        {
            int total = 0;
            var list = DbOpe_crm_product.Instance.SearchProductList(searchProductListIn, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 根据产品Id获取产品信息。
        /// </summary>
        /// <param name="productParameter_In"></param>
        /// <returns></returns>
        [HttpPost]
        public GetProductById_Out GetProductById(ProductParameter_In productParameter_In)
        {
            string Ids = productParameter_In.Ids.ToString();
            return DbOpe_crm_product.Instance.GetProductById(Ids);
        }
        /// <summary>
        /// 根据查询条件获取产品信息，状态为启用的产品信息。
        /// </summary>
        /// <param name="getProductListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetProductList_Out> GetProductList(GetProductList_In getProductListIn)
        {
            int total = 0;
            var list = DbOpe_crm_product.Instance.GetProductList(getProductListIn, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 修改产品状态信息，变更产品状态为启用、停用，多条记录全部执行成功则返回成功，否则返回失败
        /// </summary>
        /// <param name="productParameter_In"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost, PreLog]
        public void UpdateProductState(ProductParameter_In productParameter_In)
        {
            //验证Ids是否为空
            if (string.IsNullOrEmpty(productParameter_In.Ids))
                throw new ApiException("未选择产品");
            BLL_Product.Instance.UpdateProductState(productParameter_In.Ids, productParameter_In.State, productParameter_In.Remark);
        }

        /// <summary>
        /// 删除产品信息，当产品状态为停用、草稿时，可删除。验证数据权限。只做逻辑删除，修改Deleted字段为1。
        /// </summary>
        /// <param name="productParameter_In"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost, PreLog]
        public void DeleteProduct(ProductParameter_In productParameter_In)
        {
            if (string.IsNullOrEmpty(productParameter_In.Ids))
                throw new ApiException("未选择产品");
            BLL_Product.Instance.DeleteProduct(productParameter_In.Ids);
        }

        /// <summary>
        /// 添加产品信息。保存草稿：添加产品信息，状态为草稿。新建：添加产品信息，状态为启用。
        /// </summary>
        /// <param name="addProduct_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public void AddProduct(AddProduct_In addProduct_In)
        {
            BLL_Product.Instance.AddProduct(addProduct_In);
        }

        /// <summary>
        /// 修改产品信息。保存草稿：修改产品信息，状态为草稿。新建：修改产品信息，状态为启用。
        /// 表名:Crm_Product 产品表。表名:Crm_Product_Price 产品价格表。表名:Crm_Product_Combination 产品组合表。
        /// （修改应不改变产品状态，所以应该只有一个提交按钮。）只可在产品状态为草稿、停用时修改产品信息。
        /// </summary>
        /// <param name="updateProduct_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public void UpdateProduct(UpdateProduct_In updateProduct_In)
        {
            BLL_Product.Instance.UpdateProduct(updateProduct_In);
        }

        /// <summary>
        /// 根据ProductType获取计费参数详情
        /// </summary>
        /// <param name="ProductType"></param>
        /// <returns></returns>
        [HttpPost]
        public GetProductChargingParameters_Out GetProductChargingParametersByProductType(int ProductType)
        {
            return DbOpe_crm_product.Instance.GetChargingParametersByProductType(ProductType);
        }

        /// <summary>
        /// 获取产品列表用于下拉菜单，无筛选条件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<GetProductNameListNoCondition> GetProductNameListNoCondition(bool haveCombination = false)
        {
            return DbOpe_crm_product.Instance.GetProductNameListNoCondition(haveCombination);
        }

        /// <summary>
        /// 返回推荐产品剩余数量，最大为3
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public int GetIsRecommendCount()
        {
            return DbOpe_crm_product.Instance.GetIsRecommendCount();
        }
        /// <summary>
        /// 根据产品ID获取操作记录
        /// </summary>
        /// <param name="ProductId"></param>
        /// <returns></returns>
        [HttpGet, SkipRightCheck]
        public List<ProductOperateLog> GetProductOperateLog(string ProductId)
        {
            return BLL_Product.Instance.GetProductOperateLog(ProductId);
        }

        /// <summary>
        /// 获取是否可以添加超级子账号
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public bool GetIsAllowSuperSubAccount()
        {
            return BLL_Product.Instance.GetIsAllowSuperSubAccount();
        }
        /// <summary>
        /// 获取人员产品特殊权限
        /// </summary>
        /// <param name="searchSpecialProductRules_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchSpecialProductRules_Out> SearchSpecialProductRules(SearchSpecialProductRules_In searchSpecialProductRules_In)
        {
            int total = 0;
            var result = BLL_Product.Instance.SearchSpecialProductRules(searchSpecialProductRules_In, ref total);
            return GetApiTableOut(result, total);
        }
        /// <summary>
        /// 修改人员产品特殊权限
        /// </summary>
        /// <param name="addUserSpecialProductRules"></param>
        /// <returns></returns>
        [HttpPost]
        public ProductActionResult AddUserSpecialProductRules(AddUserSpecialProductRules addUserSpecialProductRules)
        {
            return DbOpe_crm_product.Instance.AddUserSpecialProductRules(addUserSpecialProductRules);
        }
        /// <summary>
         /// 人员产品特殊权限查询
         /// </summary>
         /// <param name="getSpecialProductRecorderList_In"></param>
         /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SpecialProductRecorder> SearchUserSpecialProductRules(GetSpecialProductRecorderList_In getSpecialProductRecorderList_In)
        {
            int total = 0;
            var result = DbOpe_crm_product.Instance.SearchUserSpecialProductRules(getSpecialProductRecorderList_In, ref total);
            return GetApiTableOut(result, total);
        }

        /// <summary>
        /// 添加人员特别使用乙方公司
        /// </summary>
        /// <param name="addUserSpecialCollectingcompany"></param>
        /// <returns></returns>
        [HttpPost]
        public ProductActionResult AddUserSpecialCollectingcompany(AddUserSpecialCollectingcompany addUserSpecialCollectingcompany)
        {
            return DbOpe_crm_product.Instance.AddUserSpecialCollectingcompany(addUserSpecialCollectingcompany);
        }

        /// <summary>
        /// 删除人员特别使用乙方公司
        /// </summary>
        /// <param name="productParameter_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ProductActionResult DeleteUserSpecialCollectingcompany(ProductParameter_In productParameter_In)
        {
            if (string.IsNullOrEmpty(productParameter_In.Ids))
                throw new ApiException("未选择产品");
            //转换Ids为list
            var idList = productParameter_In.Ids.Split(",").ToList();
            return DbOpe_crm_product.Instance.DeleteUserSpecialCollectingcompany(idList);
        }

        /// <summary>
        /// 人员产品特殊乙方公司查询
        /// </summary>
        /// <param name="getUserSpecialCollectingcompanyList_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchUserSpecialCollectingcompany_Out> SearchUserSpecialCollectingcompany(GetUserSpecialCollectingcompanyList_In getUserSpecialCollectingcompanyList_In)
        {
            int total = 0;
            var result = DbOpe_crm_product.Instance.SearchUserSpecialCollectingcompany(getUserSpecialCollectingcompanyList_In, ref total);
            return GetApiTableOut(result, total);
        }


        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck, SkipIPCheck,SkipRecordLog]
        public void  SendMessage()
        {
            BLL_MessageCenter.Instance.ScheduleSendGZHMessage();
        }

        [HttpPost, SkipAuthCheck, SkipRSAKey, SkipRightCheck, SkipIPCheck, SkipRecordLog]
        public void RefreshUserMaxSaveCustomer()
        {
            DbOpe_sys_usermaxsaveustomer_calclog.Instance.RefreshUserMaxSaveCustomerHandOff();
        }
    }
}
