﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using LumiSoft.Net.Media;
using SqlSugar;
using System.Diagnostics;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractTerm;
using CRM2_API.Common.JWT;

namespace CRM2_API.BLL
{
    public class BLL_ContractTerm : BaseBLL<BLL_ContractTerm>
    {
        #region 合同条款

        /// <summary>
        /// 获取可用条款列表
        /// </summary>
        /// <param name="getContractCanUseTemplateList_IN"></param>
        /// <returns></returns>
        public ApiTableOut<IContractTermTemplateQueryResShow> GetContractCanUseTemplateList(GetContractCanUseTemplateList_IN getContractCanUseTemplateList_IN)
        {
            return DbOpe_crm_contractterms_template.Instance.GetContractCanUseTemplateList(getContractCanUseTemplateList_IN, UserId);
        }

        /// <summary>
        /// 合同条款是否可用
        /// </summary>
        /// <param name="addContractIn"></param>
        /// <returns></returns>
        public CheckContractTermListCanUse_OUT CheckContractTermListCanUse_TransDeal(AddContract_In addContractIn)
        {
            CheckContractTermListCanUse_OUT result = new CheckContractTermListCanUse_OUT();
            List<string> ErrorTermContents = new List<string>();
            var checkContractTermListCanUse =
                new CheckContractTermListCanUse()
                {
                    IsOverseasCustomer = addContractIn.IsOverseasCustomer,
                    Collecting_Company = addContractIn.SecondParty.ToString(),
                    CustomerId = addContractIn.CustomerId.ToString(),
                    Term_ContractProductInfo = addContractIn.ProductInfo.MappingTo<List<Term_ContractSelectProductInfo>>()
                };
            result.CanUse = DbOpe_crm_contractterms_template.Instance.CheckContractTermListCanUse_TransDeal(checkContractTermListCanUse, UserId, addContractIn.ContractTermTemplate, ref ErrorTermContents);
            result.ErrorTermContents = ErrorTermContents;
            return result;
        }

        public List<Db_crm_contractterms_template> GetContractTermTemplateByContractId(string id)
        {
            return DbOpe_crm_contractterms_template.Instance.GetContractTermTemplateByContractId(id);
        }

        public List<Db_crm_contractterms_template> GetContractTermTemplateByIds(List<string> ids)
        {
            return DbOpe_crm_contractterms_template.Instance.GetDataList(r => ids.Contains(r.Id));
        }

        public List<Db_crm_contractterms_template> GetContractTermTemplateNoAutoAuditByIds(List<string> ids)
        {
            return DbOpe_crm_contractterms_template.Instance.GetDataList(r => ids.Contains(r.Id) && (r.Audit_Flow_Type != 1 || (r.Audit_Flow_Type == 1 && r.Term_Template_Status != 3)));
        }

        public bool IsHaveTermIsNewByContractId(string contractId)
        {
            return DbOpe_crm_contractterms.Instance.IsHaveTermIsNewByContractId(contractId);
        }

        /// <summary>
        /// 创建合同附带的合同条款
        /// </summary>
        /// <param name="ContractTermTemplateIn"></param>
        /// <param name="ContractId"></param>
        public void AddContractTerm(List<AddContractTermTemplate_In> ContractTermTemplateIn, string ContractId)
        {
            if (string.IsNullOrEmpty(ContractId))
            {
                throw new ApiException("合同ID不能为空");
            }
            if (ContractTermTemplateIn == null || ContractTermTemplateIn?.Count == 0)
            {
                return;
            }
            var index = 0;
            foreach (var term in ContractTermTemplateIn)
            {
                if (term == null)
                {
                    continue;
                }
                else
                {
                    if (string.IsNullOrEmpty(term.Term_Template_Id))
                    {
                        //新增的合同条款模板
                        //这里把合同模板的来源合同再赋值一下
                        term.Source_Contract_Id = ContractId;
                        var termTemplateAddRes = AddSingleContractTermTemplate(term);
                        AddSingleContractTermFromTermTemplate(termTemplateAddRes.Term_Template_Id, ContractId, index++, true);
                    }
                    else
                    {
                        //选择的之前的合同条款模板
                        var termTemplate = DbOpe_crm_contractterms_template.Instance.GetData(t => t.Id == term.Term_Template_Id);
                        if (termTemplate.Term_Template_Status != (int)EnumTermTemplateStatus.Approved)
                        {
                            //包含需要审核的合同条款模板
                            AddSingleContractTermFromTermTemplate(term.Term_Template_Id, ContractId, index++, true);
                        }
                        else
                        {
                            AddSingleContractTermFromTermTemplate(term.Term_Template_Id, ContractId, index++, false);
                        }
                    }
                }

            }
        }

        /// <summary>
        /// 修改合同附带的合同条款
        /// </summary>
        /// <param name="ContractTermTemplateIn"></param>
        /// <param name="ContractId"></param>
        public void UpdateContractTerm(List<UpdateContractTermTemplate_In> ContractTermTemplateIn, string ContractId)
        {
            if (string.IsNullOrEmpty(ContractId))
            {
                throw new ApiException("合同ID不能为空");
            }
            //获取之前的合同条款
            var currentContractTerms = DbOpe_crm_contractterms.Instance.GetDataList(t => t.Term_Contract_Id == ContractId);
            var termTemplateIds = currentContractTerms.Select(t => t.Term_Template_Id).ToList();
            //处理合同条款模板，看是否需要删除
            foreach (var termTemplateId in termTemplateIds)
            {
                TryDeleteContractTermTemplate(termTemplateId);
            }
            //之前的合同条款要删除
            DbOpe_crm_contractterms.Instance.DeleteData(t => t.Term_Contract_Id == ContractId);
            DbOpe_crm_contractterms_audits.Instance.DeleteData(t => t.Term_Contract_Id == ContractId);
            //添加新的合同条款
            AddContractTerm(ContractTermTemplateIn.MappingTo<List<AddContractTermTemplate_In>>(), ContractId);
        }

        /// <summary>
        /// 初审审核合同条款
        /// </summary>
        /// <param name="auditContractTerm_IN"></param>
        public void AuditContractTerm(AuditContractTerm_IN auditContractTerm_IN)
        {
            if (string.IsNullOrEmpty(auditContractTerm_IN.ContractId))
            {
                throw new ApiException("合同ID不能为空");
            }
            //获取合同相关合同条款 
            var currentContractTerms = GetContractTermList(auditContractTerm_IN.ContractId);
            var relatedTermTemplateIds = currentContractTerms.Select(t => t.ContractTermTemplate.Term_Template_Id).ToList();
            var newTermTemplates = DbOpe_crm_contractterms_template.Instance.GetDataList(t =>
                SqlFunc.ContainsArray(relatedTermTemplateIds, t.Id)
                && t.Term_Template_Status != (int)EnumTermTemplateStatus.Approved
            );
            var hasNewTermTemplate = newTermTemplates.Count > 0;
            switch (auditContractTerm_IN.State)
            {
                case (int)EnumContractStatus.InitialAuditPass:
                case (int)EnumContractStatus.InitialAuditPassSubmitOrgDivisionAudit:
                case (int)EnumContractStatus.InitialAuditPassSubmitOrgBrigadeAudit:
                case (int)EnumContractStatus.InitialAuditPassSubmitOrgRegimentAudit:
                    //合同审核通过，所有条款都需要通过，并且所有新加的模板必须设置对应的审核流程
                    foreach (var term in currentContractTerms)
                    {
                        //判断是否需要审核
                        if (!term.TermNeedAudit && !term.TermTemplateNeedConfirm)
                        {
                            continue;
                        }
                        var termAuditResult = auditContractTerm_IN.ContractTermAuditList?.Find(a => a.Term_Id == term.Term_Id);
                        if (termAuditResult == null)
                        {
                            throw new ApiException("合同审核通过时不能存在未审核的条款");
                        }
                        else
                        {
                            //判断是否需要审核合同条款模板
                            if (term.TermTemplateNeedConfirm)
                            {
                                //是新加的尚未确认审核流程的模板
                                var setting = termAuditResult.TermTemplateSetting;
                                if (setting == null)
                                {
                                    throw new ApiException("存在合同条款模板未设置对应的审核流程");
                                }
                                else if (!setting.AuditResult)
                                {
                                    throw new ApiException("合同审核通过时不能存在不通过的条款");
                                }
                                else
                                {
                                    SetNewSingleContractTermTemplate(setting);
                                }
                            }
                            //重新获取一下合同条款对应的模板
                            var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(term.ContractTermTemplate.Term_Template_Id);
                            if (hasNewTermTemplate)
                            {
                                //有新加条款，走新加条款的审核流程
                                switch (termTemplate.Audit_Flow_Type)
                                {
                                    case (int)EnumAuditFlowType.MarketingAndHeadquartersAudit:
                                        switch (termTemplate.Term_Audit_OrgType)
                                        {
                                            case (int)EnumOrgType.BattleTeam:
                                                term.Term_Audit_Process = (int)EnumTermAuditProcess.BattleTeam;
                                                break;
                                            case (int)EnumOrgType.Battalion:
                                                term.Term_Audit_Process = (int)EnumTermAuditProcess.Battalion;
                                                break;
                                            case (int)EnumOrgType.Squadron:
                                                term.Term_Audit_Process = (int)EnumTermAuditProcess.Squadron;
                                                break;
                                            default:
                                                throw new ApiException("组织类型不正确");
                                        }
                                        term.Term_Status = (int)EnumTermStatus.UnderReview;
                                        break;
                                    case (int)EnumAuditFlowType.HeadquartersCustomerServiceAudit:
                                        term.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
                                        term.Term_Status = (int)EnumTermStatus.UnderReview;
                                        break;
                                    case (int)EnumAuditFlowType.AutoAudit:
                                        term.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
                                        if (termTemplate.Term_Template_Status != (int)EnumTermTemplateStatus.Approved)
                                        {
                                            term.Term_Status = (int)EnumTermStatus.UnderReview;
                                        }
                                        else
                                        {
                                            term.Term_Status = (int)EnumTermStatus.Approved;
                                        }
                                        break;
                                    default:
                                        throw new ApiException("合同条款审核流程类型错误");
                                }
                            }
                            else
                            {
                                //没有新加条款，走原流程,不是自动审核就进复核
                                if (termTemplate.Audit_Flow_Type == (int)EnumAuditFlowType.AutoAudit && termTemplate.Term_Template_Status == (int)EnumTermTemplateStatus.Approved)
                                {
                                    term.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
                                    term.Term_Status = (int)EnumTermStatus.Approved;
                                }
                                else
                                {
                                    term.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
                                    term.Term_Status = (int)EnumTermStatus.UnderReview;
                                }
                            }
                            DbOpe_crm_contractterms.Instance.UpdateData(term);
                            //记录合同条款审核表数据
                            #region 添加合同条款审核表Db_crm_contractterms_audits
                            Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                            {
                                Id = Guid.NewGuid().ToString(),
                                Term_Template_Id = termTemplate.Id,
                                Term_Template_Audit_Id = termTemplate.Last_Audit_Id,
                                Term_Id = term.Term_Id,
                                Term_Contract_Id = auditContractTerm_IN.ContractId,
                                Term_Audit_Result = (int)EnumProcessStatus.Pass,
                                Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Initial,
                                Term_Auditor = UserId,
                                Term_Audit_Time = DateTime.Now
                            };
                            DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                            #endregion

                        }
                    }
                    break;
                case (int)EnumContractStatus.InitialAuditRefuse:
                    //合同审核不通过  条款可以有通过的，也可以有不通过的
                    //还要看条款模板可不可以通过，模板是可以单独通过的 (模板单独通过的，下一个使用这个模板的合同，模板就不用审核了，按本次审核的流程继续向下走)
                    //避免多个合同共用一个条款模板时，多次审核 导致没法处理
                    foreach (var term in currentContractTerms)
                    {
                        //判断是否需要审核
                        if (!term.TermNeedAudit && !term.TermTemplateNeedConfirm)
                        {
                            continue;
                        }
                        var termAuditResult = auditContractTerm_IN.ContractTermAuditList?.Find(a => a.Term_Id == term.Term_Id);
                        if (termAuditResult == null)
                        {
                            throw new ApiException("合同审核通过时不能存在未审核的条款");
                        }
                        else
                        {
                            if (termAuditResult.AuditResult)
                            {
                                //通过的条款
                                //判断是否是新加的合同条款模板
                                if (term.TermTemplateNeedConfirm)
                                {
                                    //是新加的尚未确认审核流程的模板
                                    var setting = termAuditResult.TermTemplateSetting;
                                    if (setting == null)
                                    {
                                        throw new ApiException("存在合同条款模板未设置对应的审核流程");
                                    }
                                    else if (!setting.AuditResult)
                                    {
                                        throw new ApiException("合同条款通过时合同模板也必须通过");
                                    }
                                    else
                                    {
                                        SetNewSingleContractTermTemplate(setting);
                                    }
                                }
                                var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(term.ContractTermTemplate.Term_Template_Id);
                                //更新合同条款表数据
                                //因为合同整体是拒绝的，所以通过的合同条款，审核流程和状态都先保持不变，停留在待审核状态即可;这样销售可以看到哪些条款是拒绝的，哪些不是拒绝的
                                //合同重新提交回来时，所有条款会重新审核
                                //自动审核的条款除外
                                if (!(termTemplate.Audit_Flow_Type == (int)EnumAuditFlowType.AutoAudit && termTemplate.Term_Template_Status == (int)EnumTermTemplateStatus.Approved))
                                {
                                    term.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                                    term.Term_Status = (int)EnumTermStatus.UnderReview;
                                    DbOpe_crm_contractterms.Instance.UpdateData(term);
                                }
                                //记录合同条款审核表数据
                                #region 添加合同条款审核表Db_crm_contractterms_audits
                                Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    Term_Template_Id = termTemplate.Id,
                                    Term_Template_Audit_Id = termTemplate.Last_Audit_Id,
                                    Term_Id = term.Term_Id,
                                    Term_Contract_Id = auditContractTerm_IN.ContractId,
                                    Term_Audit_Result = termAuditResult.AuditResult ? (int)EnumProcessStatus.Pass : (int)EnumProcessStatus.Refuse,
                                    Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Initial,
                                    Term_Auditor = UserId,
                                    Term_Audit_Time = DateTime.Now
                                };
                                DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                                #endregion
                            }
                            else
                            {
                                //拒绝的条款
                                //判断是否是新加的合同条款模板
                                if (term.TermTemplateNeedConfirm)
                                {
                                    //是新加的尚未确认审核流程的模板
                                    var setting = termAuditResult.TermTemplateSetting;
                                    if (setting == null)
                                    {
                                        throw new ApiException("存在合同条款模板未设置对应的审核流程");
                                    }
                                    else if (!setting.AuditResult)
                                    {
                                        //模板也没通过,看有没有其他关联待审核合同（不是草稿、作废、拒绝合同），如果没有其他关联待审核合同了，模板状态变为拒绝
                                        var relatedContracts = DbOpe_crm_contractterms.Instance.GetContractTermTemplateRelatedContracts(term.ContractTermTemplate.Term_Template_Id);
                                        if (relatedContracts.Where(c => c.Id != auditContractTerm_IN.ContractId).Count() == 0)
                                        {
                                            SetNewSingleContractTermTemplate(setting);
                                        }
                                    }
                                    else
                                    {
                                        //模板通过了，需要设置审核流程
                                        SetNewSingleContractTermTemplate(setting);
                                    }
                                }
                                var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(term.ContractTermTemplate.Term_Template_Id);
                                //更新合同条款表数据
                                term.Term_Status = (int)EnumTermStatus.Rejected;
                                term.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                                DbOpe_crm_contractterms.Instance.UpdateData(term);
                                #region 添加合同条款审核表Db_crm_contractterms_audits
                                Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    Term_Template_Id = termTemplate.Id,
                                    Term_Template_Audit_Id = termTemplate.Last_Audit_Id,
                                    Term_Id = term.Term_Id,
                                    Term_Contract_Id = auditContractTerm_IN.ContractId,
                                    Term_Audit_Result = (int)EnumProcessStatus.Refuse,
                                    Term_Audit_Flow = term.Term_Audit_Process,
                                    Term_Auditor = UserId,
                                    Term_Audit_Time = DateTime.Now
                                };
                                DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                                #endregion
                            }
                        }
                    }
                    break;
                default:
                    throw new ApiException("初审合同条款-合同状态错误");
            }
        }

        /// <summary>
        /// 团队审核合同条款
        /// </summary>
        /// <param name="auditContractTerm_Team_IN"></param>
        /// <exception cref="ApiException"></exception>
        public void AuditContractTermTeam(AuditContractTerm_Team_IN auditContractTerm_Team_IN)
        {
            if (string.IsNullOrEmpty(auditContractTerm_Team_IN.ContractId))
            {
                throw new ApiException("合同ID不能为空");
            }
            //获取当前用户的管理层级
            var user = DbOpe_sys_user.Instance.GetUserById(UserId);
            if (user == null)
            {
                throw new ApiException("未获取到用户信息");
            }
            if (user.UserType != EnumUserType.Manager)
            {
                throw new ApiException("当前用户并非管理者，没有审核权限");
            }
            var org = DbOpe_sys_organization.Instance.GetDataById(user.OrganizationId);
            if (org == null)
            {
                throw new ApiException("未获取到用户所属组织信息");
            }
            //获取合同相关合同条款 
            var currentContractTerms = GetContractTermList(auditContractTerm_Team_IN.ContractId);
            var relatedTermTemplateIds = currentContractTerms.Select(t => t.ContractTermTemplate.Term_Template_Id).ToList();
            var newTermTemplates = DbOpe_crm_contractterms_template.Instance.GetDataList(t =>
                SqlFunc.ContainsArray(relatedTermTemplateIds, t.Id)
                && t.Term_Template_Status != (int)EnumTermTemplateStatus.Approved
            );
            var hasNewTermTemplate = newTermTemplates.Count > 0;

            var process = (int)EnumTermAuditProcess.BattleTeam;
            var contractPass = false;
            switch (auditContractTerm_Team_IN.State)
            {
                case (int)EnumContractStatus.OrgDivisionAuditPass:
                case (int)EnumContractStatus.SubmitOrgDivisionAuditPass:
                    if (org.OrgType != EnumOrgType.BattleTeam)
                    {
                        throw new ApiException("当前用户没有审核权限");
                    }
                    process = (int)EnumTermAuditProcess.BattleTeam;
                    contractPass = true;
                    break;
                case (int)EnumContractStatus.OrgDivisionAuditRefuse:
                case (int)EnumContractStatus.SubmitOrgDivisionAuditRefuse:
                    if (org.OrgType != EnumOrgType.BattleTeam)
                    {
                        throw new ApiException("当前用户没有审核权限");
                    }
                    process = (int)EnumTermAuditProcess.BattleTeam;
                    break;
                case (int)EnumContractStatus.OrgBrigadeAuditRefuse:
                case (int)EnumContractStatus.SubmitOrgBrigadeAuditRefuse:
                    if (org.OrgType != EnumOrgType.Battalion)
                    {
                        throw new ApiException("当前用户没有审核权限");
                    }
                    process = (int)EnumTermAuditProcess.Battalion;
                    break;
                case (int)EnumContractStatus.OrgBrigadeAuditPass:
                case (int)EnumContractStatus.OrgBrigadeAuditSubmitOrgDivisionAudit:
                case (int)EnumContractStatus.SubmitOrgBrigadeAuditPass:
                case (int)EnumContractStatus.SubmitOrgBrigadeAuditSubmitOrgDivisionAudit:
                    if (org.OrgType != EnumOrgType.Battalion)
                    {
                        throw new ApiException("当前用户没有审核权限");
                    }
                    process = (int)EnumTermAuditProcess.Battalion;
                    contractPass = true;
                    break;
                case (int)EnumContractStatus.OrgRegimentAuditPass:
                case (int)EnumContractStatus.OrgRegimentAuditSubmitOrgBrigadeAudit:
                case (int)EnumContractStatus.OrgRegimentAuditSubmitOrgDivisionAudit:
                case (int)EnumContractStatus.SubmitOrgRegimentAuditPass:
                case (int)EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgBrigadeAudit:
                case (int)EnumContractStatus.SubmitOrgRegimentAuditSubmitOrgDivisionAudit:
                    if (org.OrgType != EnumOrgType.Squadron)
                    {
                        throw new ApiException("当前用户没有审核权限");
                    }
                    process = (int)EnumTermAuditProcess.Squadron;
                    contractPass = true;
                    break;

                case (int)EnumContractStatus.OrgRegimentAuditRefuse:
                case (int)EnumContractStatus.SubmitOrgRegimentAuditRefuse:
                    if (org.OrgType != EnumOrgType.Squadron)
                    {
                        throw new ApiException("当前用户没有审核权限");
                    }
                    process = (int)EnumTermAuditProcess.Squadron;
                    break;
                default:
                    throw new ApiException("团队审核合同条款-合同状态错误");

            }

            //逐条合同条款进行处理
            foreach (var term in currentContractTerms)
            {
                //判断是否需要当前用户审核
                if (!term.TermNeedAudit)
                {
                    continue;
                }
                var termAuditResult = auditContractTerm_Team_IN.ContractTermAuditList?.Find(a => a.Term_Id == term.Term_Id);
                if (termAuditResult == null)
                {
                    throw new ApiException("存在未审核的条款");
                }
                else
                {
                    //判断是否是新加的合同条款模板
                    var newTermFlag = newTermTemplates.Find(t => t.Id == term.ContractTermTemplate.Term_Template_Id) != null;
                    if (newTermFlag)
                    {
                        //如果是新加的，条款通过的话，模板也通过，但条款拒绝的话，模板要看还有没有关联合同，没关联合同的也拒绝
                        //模板也没通过,看有没有其他关联待审核合同（不是草稿、作废、拒绝合同），如果没有其他关联待审核合同了，模板状态变为拒绝
                        if (contractPass)
                        {
                            TeamAuditSingleContractTermTemplate(new TeamAuditSingleContractTermTemplate
                            {
                                Term_Template_Id = term.ContractTermTemplate.Term_Template_Id,
                                AuditResult = true
                            });
                        }
                        else
                        {
                            var relatedContracts = DbOpe_crm_contractterms.Instance.GetContractTermTemplateRelatedContracts(term.ContractTermTemplate.Term_Template_Id);
                            if (relatedContracts.Where(c => c.Id != auditContractTerm_Team_IN.ContractId).Count() == 0)
                            {
                                TeamAuditSingleContractTermTemplate(new TeamAuditSingleContractTermTemplate
                                {
                                    Term_Template_Id = term.ContractTermTemplate.Term_Template_Id,
                                    AuditResult = false
                                });
                            }
                        }
                    }
                    var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(term.ContractTermTemplate.Term_Template_Id);
                    //更新合同条款表数据
                    if (contractPass)
                    {
                        term.Term_Audit_Process = hasNewTermTemplate ? (int)EnumTermAuditProcess.BJ_Review : (int)EnumTermAuditProcess.BJ_Initial;
                        term.Term_Status = (int)EnumTermStatus.UnderReview;
                    }
                    else
                    {
                        //合同拒绝了
                        if (termAuditResult.AuditResult)
                        {
                            //因为合同整体是拒绝的，所以通过的合同条款，审核流程和状态都先保持不变，停留在待审核状态即可;这样销售可以看到哪些条款是拒绝的，哪些不是拒绝的
                            //合同重新提交回来时，所有条款会重新审核
                            term.Term_Status = (int)EnumTermStatus.UnderReview;
                            term.Term_Audit_Process = process;
                        }
                        else
                        {
                            //条款被拒绝
                            term.Term_Audit_Process = process;
                            term.Term_Status = (int)EnumTermStatus.Rejected;
                        }
                    }
                    DbOpe_crm_contractterms.Instance.UpdateData(term);
                    //记录合同条款审核表数据
                    #region 添加合同条款审核表Db_crm_contractterms_audits
                    Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                    {
                        Id = Guid.NewGuid().ToString(),
                        Term_Template_Id = termTemplate.Id,
                        Term_Template_Audit_Id = termTemplate.Last_Audit_Id,
                        Term_Id = term.Term_Id,
                        Term_Contract_Id = auditContractTerm_Team_IN.ContractId,
                        Term_Audit_Result = termAuditResult.AuditResult ? (int)EnumProcessStatus.Pass : (int)EnumProcessStatus.Refuse,
                        Term_Audit_Flow = process,
                        Term_Auditor = UserId,
                        Term_Audit_Time = DateTime.Now
                    };
                    DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                    #endregion
                }

            }
        }

        /// <summary>
        /// 复核审核合同条款
        /// </summary>
        /// <param name="reviewContractTerm_IN"></param>
        public void ReviewContractTerm(ReviewContractTerm_IN reviewContractTerm_IN)
        {
            if (string.IsNullOrEmpty(reviewContractTerm_IN.ContractId))
            {
                throw new ApiException("合同ID不能为空");
            }
            //获取合同相关合同条款 
            var currentContractTerms = GetContractTermList(reviewContractTerm_IN.ContractId);
            var relatedTermTemplateIds = currentContractTerms.Select(t => t.ContractTermTemplate.Term_Template_Id).ToList();
            var newTermTemplates = DbOpe_crm_contractterms_template.Instance.GetDataList(t =>
                SqlFunc.ContainsArray(relatedTermTemplateIds, t.Id)
                && t.Term_Template_Status != (int)EnumTermTemplateStatus.Approved
            );
            switch (reviewContractTerm_IN.State)
            {
                case (int)EnumContractStatus.Pass:
                    //判断是否需要审核

                    //合同审核通过，所有条款都需要通过，并且所有新加的模板必须设置对应的审核流程
                    foreach (var term in currentContractTerms)
                    {
                        //判断是否需要当前用户审核
                        if (!term.TermNeedAudit && !term.TermTemplateNeedConfirm)
                        {
                            continue;
                        }
                        var termAuditResult = reviewContractTerm_IN.ContractTermAuditList?.Find(a => a.Term_Id == term.Term_Id);
                        if (termAuditResult == null)
                        {
                            throw new ApiException("合同审核通过时不能存在未审核的条款");
                        }
                        else
                        {
                            //判断合同条款模板是否也需要审核
                            var newTermFlag = newTermTemplates.Find(t => t.Id == term.ContractTermTemplate.Term_Template_Id) != null;
                            if (newTermFlag)
                            {
                                //合同条款模板审核通过
                                ReviewSingleContractTermTemplate(new ReviewSingleContractTermTemplate()
                                {
                                    Term_Template_Id = term.ContractTermTemplate.Term_Template_Id,
                                    AuditResult = true
                                });
                            }
                            //更新合同条款表数据
                            var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(term.ContractTermTemplate.Term_Template_Id);
                            term.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
                            term.Term_Status = (int)EnumTermStatus.Approved;
                            DbOpe_crm_contractterms.Instance.UpdateData(term);
                            //记录合同条款审核表数据
                            #region 添加合同条款审核表Db_crm_contractterms_audits
                            Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                            {
                                Id = Guid.NewGuid().ToString(),
                                Term_Template_Id = termTemplate.Id,
                                Term_Template_Audit_Id = termTemplate.Last_Audit_Id,
                                Term_Id = term.Term_Id,
                                Term_Contract_Id = reviewContractTerm_IN.ContractId,
                                Term_Audit_Result = (int)EnumProcessStatus.Pass,
                                Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Review,
                                Term_Auditor = UserId,
                                Term_Audit_Time = DateTime.Now
                            };
                            DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                            #endregion

                        }
                    }
                    break;
                case (int)EnumContractStatus.Refuse:
                    //合同审核不通过  条款可以有通过的，也可以有不通过的
                    //还要看条款模板可不可以通过，模板是可以单独通过的
                    foreach (var term in currentContractTerms)
                    {
                        //判断是否需要审核
                        if (!term.TermNeedAudit && !term.TermTemplateNeedConfirm)
                        {
                            continue;
                        }
                        var termAuditResult = reviewContractTerm_IN.ContractTermAuditList?.Find(a => a.Term_Id == term.Term_Id);
                        if (termAuditResult == null)
                        {
                            throw new ApiException("合同审核通过时不能存在未审核的条款");
                        }
                        else
                        {
                            if (termAuditResult.AuditResult)
                            {
                                //通过的条款
                                //判断是否是新加的合同条款模板
                                var newTermFlag = newTermTemplates.Find(t => t.Id == term.ContractTermTemplate.Term_Template_Id) != null;
                                if (newTermFlag)
                                {
                                    //合同条款模板审核通过
                                    ReviewSingleContractTermTemplate(new ReviewSingleContractTermTemplate()
                                    {
                                        Term_Template_Id = term.ContractTermTemplate.Term_Template_Id,
                                        AuditResult = true
                                    });
                                }
                                var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(term.ContractTermTemplate.Term_Template_Id);
                                //更新合同条款表数据
                                //因为合同整体是拒绝的，所以通过的合同条款，审核流程和状态都先保持不变，停留在待审核状态即可
                                //记录合同条款审核表数据
                                #region 添加合同条款审核表Db_crm_contractterms_audits
                                Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    Term_Template_Id = termTemplate.Id,
                                    Term_Template_Audit_Id = termTemplate.Last_Audit_Id,
                                    Term_Id = term.Term_Id,
                                    Term_Contract_Id = reviewContractTerm_IN.ContractId,
                                    Term_Audit_Result = termAuditResult.AuditResult ? (int)EnumProcessStatus.Pass : (int)EnumProcessStatus.Refuse,
                                    Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Review,
                                    Term_Auditor = UserId,
                                    Term_Audit_Time = DateTime.Now
                                };
                                DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                                #endregion
                            }
                            else
                            {
                                //拒绝的条款
                                //判断是否是新加的合同条款模板
                                var newTermFlag = newTermTemplates.Find(t => t.Id == term.ContractTermTemplate.Term_Template_Id) != null;
                                if (newTermFlag)
                                {
                                    //如果是新加的条款拒绝的话，模板要看还有没有关联合同，没关联合同的也拒绝
                                    //模板也没通过,看有没有其他关联待审核合同（不是草稿、作废、拒绝合同），如果没有其他关联待审核合同了，模板状态变为拒绝
                                    var relatedContracts = DbOpe_crm_contractterms.Instance.GetContractTermTemplateRelatedContracts(term.ContractTermTemplate.Term_Template_Id);
                                    if (relatedContracts.Where(c => c.Id != reviewContractTerm_IN.ContractId).Count() == 0)
                                    {
                                        //合同条款模板审核拒绝
                                        ReviewSingleContractTermTemplate(new ReviewSingleContractTermTemplate()
                                        {
                                            Term_Template_Id = term.ContractTermTemplate.Term_Template_Id,
                                            AuditResult = false
                                        });
                                    }
                                }
                                var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(term.ContractTermTemplate.Term_Template_Id);
                                //更新合同条款表数据
                                if (termAuditResult.AuditResult)
                                {
                                    //因为合同整体是拒绝的，所以通过的合同条款，审核流程和状态都先保持不变，停留在待审核状态即可;这样初审的可以看到哪些条款是拒绝的，哪些不是拒绝的
                                    //合同重新提交回来时，所有条款会重新审核
                                    term.Term_Status = (int)EnumTermStatus.UnderReview;
                                    term.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                                }
                                else
                                {
                                    //条款被拒绝
                                    term.Term_Status = (int)EnumTermStatus.Rejected;
                                    term.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                                }
                                DbOpe_crm_contractterms.Instance.UpdateData(term);
                                #region 添加合同条款审核表Db_crm_contractterms_audits
                                Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    Term_Template_Id = termTemplate.Id,
                                    Term_Template_Audit_Id = termTemplate.Last_Audit_Id,
                                    Term_Id = term.Term_Id,
                                    Term_Contract_Id = reviewContractTerm_IN.ContractId,
                                    Term_Audit_Result = (int)EnumProcessStatus.Refuse,
                                    Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Review,
                                    Term_Auditor = UserId,
                                    Term_Audit_Time = DateTime.Now
                                };
                                DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                                #endregion
                            }
                        }
                    }
                    break;
                default:
                    throw new ApiException("复核合同条款-合同状态错误");
            }
        }

        /// <summary>
        /// 从合同条款模板添加到某个合同的条款中
        /// </summary>
        /// <param name="Term_Template_Id"></param>
        /// <param name="ContractId"></param>
        /// <param name="index"></param>
        /// <param name="Term_Is_New"></param>
        public void AddSingleContractTermFromTermTemplate(string Term_Template_Id, string ContractId, int index, bool Term_Is_New = false)
        {
            var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(Term_Template_Id);
            if (termTemplate == null)
            {
                throw new ApiException("合同条款模板不存在");
            }
            var contract = DbOpe_crm_contract.Instance.GetDataById(ContractId);
            if (contract == null)
            {
                throw new ApiException("合同不存在");
            }
            //添加合同条款表数据
            var termId = Guid.NewGuid().ToString();
            var auditId = Guid.NewGuid().ToString();
            var contractTerm = new Db_crm_contractterms
            {
                Id = termId,
                Term_Contract_Id = ContractId,
                Term_Template_Id = Term_Template_Id,
                Term_Is_New = Term_Is_New,
                Sort_Index = index
            };
            //添加合同条款审核表数据

            Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
            {
                Id = auditId,
                Term_Template_Id = termTemplate.Id,
                Term_Template_Audit_Id = termTemplate.Last_Audit_Id,
                Term_Id = termId,
                Term_Contract_Id = ContractId
            };


            //根据模板的状态，设置条款的状态
            if (termTemplate.Term_Template_Status == (int)EnumTermTemplateStatus.Approved)
            {
                //条款模板已经审核通过，按模板的流程走就可以了
                if (termTemplate.Audit_Flow_Type == (int)EnumAuditFlowType.AutoAudit)
                {
                    //自动审核，直接通过审核
                    contractTerm.Term_Status = (int)EnumTermStatus.Approved;
                    contractTerm.Term_Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                    contractTerm.Term_Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                    contractTerm.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
                    contractterms_audit.Term_Audit_Result = (int)EnumProcessStatus.Pass;
                }
                else if (termTemplate.Audit_Flow_Type == (int)EnumAuditFlowType.HeadquartersCustomerServiceAudit)
                {
                    //初审-复核 
                    contractTerm.Term_Status = (int)EnumTermStatus.UnderReview;
                    contractTerm.Term_Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                    contractTerm.Term_Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                    contractTerm.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                    contractterms_audit.Term_Audit_Result = (int)EnumProcessStatus.Submit;
                }
                else if (termTemplate.Audit_Flow_Type == (int)EnumAuditFlowType.MarketingAndHeadquartersAudit)
                {
                    //团队-初审-复核
                    contractTerm.Term_Status = (int)EnumTermStatus.UnderReview;
                    contractTerm.Term_Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                    contractTerm.Term_Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                    //下一阶段转到指定的团队
                    switch (termTemplate.Term_Audit_OrgType)
                    {
                        case (int)EnumOrgType.BattleTeam:
                            contractTerm.Term_Audit_Process = (int)EnumTermAuditProcess.BattleTeam;
                            break;
                        case (int)EnumOrgType.Battalion:
                            contractTerm.Term_Audit_Process = (int)EnumTermAuditProcess.Battalion;
                            break;
                        case (int)EnumOrgType.Squadron:
                            contractTerm.Term_Audit_Process = (int)EnumTermAuditProcess.Squadron;
                            break;
                        default:
                            throw new ApiException("组织类型不正确");
                    }
                    contractterms_audit.Term_Audit_Result = (int)EnumProcessStatus.Submit;
                }
                else
                {
                    throw new ApiException("合同条款模板审核流程类型不存在");
                }
            }
            else if (termTemplate.Term_Template_Status == (int)EnumTermTemplateStatus.UnderReview && termTemplate.Audit_Flow_Type != (int)EnumAuditFlowType.PendingConfirmation)
            {
                //条款模板在审核中，且模板的审核流程已经填写过了，只是待确认，这里就不用再确认一遍流程了；
                //注意：虽然不用再确认审核流程，但是需要把条款的状态设置成待审核，初审还是要审一遍条款的
                contractTerm.Term_Status = (int)EnumTermStatus.UnderReview;
                contractTerm.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                contractterms_audit.Term_Audit_Result = (int)EnumProcessStatus.Submit;
                if (termTemplate.Audit_Flow_Type == (int)EnumAuditFlowType.AutoAudit || termTemplate.Audit_Flow_Type == (int)EnumAuditFlowType.HeadquartersCustomerServiceAudit)
                {
                    contractTerm.Term_Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                    contractTerm.Term_Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                }
                else if (termTemplate.Audit_Flow_Type == (int)EnumAuditFlowType.MarketingAndHeadquartersAudit)
                {
                    contractTerm.Term_Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                    contractTerm.Term_Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                }
                else
                {
                    throw new ApiException("合同条款模板审核流程类型不存在");
                }
            }
            else
            {
                //条款模板还没有确定审核流程（包括拒绝、作废、过期等情况），需要重新确认审核流程
                contractTerm.Term_Status = (int)EnumTermStatus.UnderReview;
                contractTerm.Term_Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingConfirmation;
                contractTerm.Term_Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingConfirmation;
                contractterms_audit.Term_Audit_Result = (int)EnumProcessStatus.Submit;
                contractTerm.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                //这种情况下，需要把合同条款模板状态重新更新为待审核，由北京初审开始，重新走一遍审核流程
                termTemplate.Term_Template_Status = (int)EnumTermTemplateStatus.UnderReview;
                termTemplate.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                DbOpe_crm_contractterms_template.Instance.UpdateData(termTemplate);
            }
            contractterms_audit.Term_Audit_Flow = contractTerm.Term_Audit_Process;
            DbOpe_crm_contractterms.Instance.Insert(contractTerm);
            DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
        }

        /// <summary>
        /// 获取合同关联的合同条款列表
        /// 根据身份不同，要做区分显示 判断条款模板/条款是否需要审核
        /// </summary>
        public List<ContractTerm> GetContractTermList(string contractId)
        {
            var query = DbOpe_crm_contractterms.Instance.GetContractTermList(contractId);
            //根据身份不同，要做区分显示 判断条款模板/条款是否需要审核
            //获取当前用户的管理层级
            var user = DbOpe_sys_user.Instance.GetUserById(UserId);
            if (user == null)
            {
                throw new ApiException("未获取到用户信息");
            }
            if (user.UserType != EnumUserType.Manager)
            {
                //普通销售权限，看当前合同权限（以客户权限判断）
                var contract = DbOpe_crm_contract.Instance.GetContractBasicInfoById(contractId);
                if (contract == null)
                {
                    throw new ApiException("未找到合同或没有合同权限");
                }
                query.ForEach(q => { q.TermNeedAudit = false; q.TermTemplateNeedConfirm = false; });
            }
            else
            {
                var org = DbOpe_sys_organization.Instance.GetDataById(user.OrganizationId);
                if (org == null)
                {
                    throw new ApiException("未获取到用户所属组织信息");
                }
                else if (org.Id == Guid.Empty.ToString())
                {
                    //后台管理员
                    query.ForEach(t =>
                    {
                        if (t.Term_Status != (int)EnumTermStatus.Approved &&
                        (t.Term_Audit_Process == (int)EnumTermAuditProcess.BJ_Initial || t.Term_Audit_Process == (int)EnumTermAuditProcess.BJ_Review))
                        {
                            t.TermNeedAudit = true;
                        }
                        if (t.ContractTermTemplate.Term_Template_Status != (int)EnumTermTemplateStatus.Approved &&
                        (t.ContractTermTemplate.Term_Audit_Process == (int)EnumTermAuditProcess.BJ_Initial))
                        {
                            t.TermTemplateNeedConfirm = true;
                        }
                    });
                }
                else
                {
                    //队长
                    //根据当前管理的层级，查询对应的审核阶段
                    var teamProcess = (int)EnumTermAuditProcess.BattleTeam;
                    switch (org.OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            teamProcess = (int)EnumTermAuditProcess.BattleTeam;
                            break;
                        case EnumOrgType.Battalion:
                            teamProcess = (int)EnumTermAuditProcess.Battalion;
                            break;
                        case EnumOrgType.Squadron:
                            teamProcess = (int)EnumTermAuditProcess.Squadron;
                            break;
                        default:
                            throw new ApiException("当前用户所属组织类型不正确");
                    }
                    query.ForEach(t =>
                    {
                        if (t.Term_Status == (int)EnumTermStatus.UnderReview && t.Term_Audit_Process == teamProcess)
                        {
                            t.TermNeedAudit = true;
                        }
                        //if (t.ContractTermTemplate.Term_Template_Status == (int)EnumTermTemplateStatus.UnderReview 
                        //&& t.ContractTermTemplate.Term_Audit_Process == teamProcess)
                        //{
                        //    t.TermTemplateNeedConfirm = true;
                        //}
                    });
                }
            }
            return query;
        }

        #endregion

        #region 合同条款模板

        /// <summary>
        /// 客户经理-添加单条合同条款模板
        /// 仅添加模板表，关联合同条款表不操作
        /// </summary>
        /// <param name="addContractTermTemplate"></param>
        public AddContractTermTemplateRes AddSingleContractTermTemplate(AddContractTermTemplate addContractTermTemplate)
        {
            #region 参数验证
            if (addContractTermTemplate == null)
            {
                throw new ApiException("参数不能为空");
            }
            #endregion

            #region 基础参数准备
            string contractTermTemplateId = Guid.NewGuid().ToString();
            string contractTermAuditId = Guid.NewGuid().ToString();
            string contractTermId = "";
            //if (StringUtil.IsNotNullOrEmpty(addContractTermTemplate.Source_Contract_Id))
            //{
            //    contractTermId = Guid.NewGuid().ToString();
            //}
            string userId = UserId;
            AddContractTermTemplateRes res = new AddContractTermTemplateRes()
            {
                Term_Audit_Id = contractTermAuditId,
                Term_Template_Id = contractTermTemplateId,
                Term_Id = contractTermId
            };
            #endregion

            #region 添加合同条款模板表Db_crm_contractterms_template
            Db_crm_contractterms_template term_template = addContractTermTemplate.MappingTo<Db_crm_contractterms_template>();
            term_template.Id = contractTermTemplateId;
            term_template.Term_Template_Level = (int)EnumTermTemplateLevel.PersonalLevel;
            term_template.Audit_Flow_Type = (int)EnumAuditFlowType.PendingConfirmation;
            term_template.Term_Template_Status = (int)EnumTermTemplateStatus.UnderReview;
            term_template.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
            term_template.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingConfirmation;
            term_template.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingConfirmation;
            term_template.Last_Audit_Id = contractTermAuditId;
            DbOpe_crm_contractterms_template.Instance.Insert(term_template);
            #endregion

            #region 添加其他关联的表
            AddSingleContractTermTemplateRelatedTables(res, addContractTermTemplate);
            #endregion

            #region 生成产品规则描述
            DbOpe_crm_contractterms_template_productrules_item.Instance.FormatProductDecs(contractTermTemplateId);
            #endregion

            #region 工作流（待补充） 根据是否关联合同，进入不同的工作流处理流程
            #endregion

            return res;

        }

        /// <summary>
        /// 添加合同条款模板其他关联的表
        /// 注意：如果是关联合同建立的新模板  这个方法里 不包含合同条款使用表 和  audits表的操作
        /// </summary>
        /// <param name="res"></param>
        /// <param name="addContractTermTemplate"></param>
        private void AddSingleContractTermTemplateRelatedTables(AddContractTermTemplateRes res, AddContractTermTemplate addContractTermTemplate)
        {
            #region 添加合同条款适用公司表Db_crm_contractterms_template_applicablecollectingcompanies
            if (addContractTermTemplate.Collecting_Companines?.Count > 0)
            {
                var applicableCollectingCompanies = addContractTermTemplate.Collecting_Companines.Select(item =>
                    new Db_crm_contractterms_template_applicablecollectingcompanies
                    {
                        Id = Guid.NewGuid().ToString(),
                        Term_Template_Id = res.Term_Template_Id,
                        Collecting_Company_Id = item
                    }).ToList();
                DbOpe_crm_contractterms_template_applicablecollectingcompanies.Instance.Insert(applicableCollectingCompanies);
            }
            #endregion

            #region 添加合同条款适用产品规则表Db_crm_contractterms_template_applicableproductrulegroups
            if (addContractTermTemplate.Product_Rule_Group?.Count > 0)
            {
                //是否存在实际的产品规则
                var hasProductRule = false;
                //关联产品规则表
                var applicableproductrulegroups = new List<Db_crm_contractterms_template_applicableproductrulegroups>();
                //产品规则组
                var productrules_itemgroups = new List<Db_crm_contractterms_template_productrules_itemgroup>();
                var groupIndex = 0;
                foreach (var group in addContractTermTemplate.Product_Rule_Group)
                {
                    if (group.RuleItems?.Count > 0)
                    {
                        hasProductRule = true;
                        var groupId = Guid.NewGuid().ToString();
                        var applicableproductrulegroup = new Db_crm_contractterms_template_applicableproductrulegroups();
                        applicableproductrulegroup.Term_Template_Id = res.Term_Template_Id;
                        applicableproductrulegroup.Term_Template_Product_Rule_Item_Group_Id = groupId;
                        applicableproductrulegroup.Combination_Type = (int)EnumCombinationType.And;
                        applicableproductrulegroup.Sort_Index = groupIndex;
                        applicableproductrulegroup.Id = Guid.NewGuid().ToString();
                        var productrules_itemgroup = new Db_crm_contractterms_template_productrules_itemgroup()
                        {
                            Id = groupId,
                            Term_Template_Id = res.Term_Template_Id,
                            Combination_Type = (int)EnumCombinationType.Or
                        };
                        //保存规则组中具体的产品规则项目
                        foreach (var item in group.RuleItems)
                        {
                            var productrule_item = new Db_crm_contractterms_template_productrules_item()
                            {
                                Id = Guid.NewGuid().ToString(),
                                Term_Template_Product_Rule_Item_Group_Id = groupId,
                                Term_Template_Id = res.Term_Template_Id,
                                Product_Id = item.Product_Id,
                                Product_Type = item.Product_Type,
                                //Year_Type = (int)item.Year_Type
                                Year_Type = (int)group.Year_Type
                            };
                            DbOpe_crm_contractterms_template_productrules_item.Instance.Insert(productrule_item);
                        }
                        applicableproductrulegroups.Add(applicableproductrulegroup);
                        productrules_itemgroups.Add(productrules_itemgroup);
                        groupIndex++;
                    }
                }
                if (hasProductRule)
                {
                    DbOpe_crm_contractterms_template_productrules_itemgroup.Instance.Insert(productrules_itemgroups);
                    DbOpe_crm_contractterms_template_applicableproductrulegroups.Instance.Insert(applicableproductrulegroups);
                }
            }
            #endregion

            //#region 根据是否关联合同，对合同相关的合同条款使用表进行操作Db_crm_contractterms
            //if (StringUtil.IsNotNullOrEmpty(addContractTermTemplate.Source_Contract_Id))
            //{
            //    Db_crm_contractterms contractterm = new Db_crm_contractterms()
            //    {
            //        Id = res.Term_Id,
            //        Term_Template_Id = res.Term_Template_Id,
            //        Term_Contract_Id = addContractTermTemplate.Source_Contract_Id,
            //        Term_Is_New = true,
            //        Term_Status = (int)EnumTermStatus.UnderReview,
            //        Term_Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingConfirmation,
            //        Term_Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingConfirmation
            //    };
            //    DbOpe_crm_contractterms.Instance.Insert(contractterm);
            //}
            //#endregion

            #region 添加合同条款审核表Db_crm_contractterms_audits   Term_Audit_Id为空的表示不需要审核（后台升级条款）
            if (!string.IsNullOrEmpty(res.Term_Audit_Id) && string.IsNullOrEmpty(addContractTermTemplate.Source_Contract_Id))
            {
                Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                {
                    Id = res.Term_Audit_Id,
                    Term_Template_Id = res.Term_Template_Id,
                    Term_Template_Audit_Id = res.Term_Audit_Id,
                    //Term_Id = null,
                    //Term_Contract_Id = addContractTermTemplate.Source_Contract_Id,
                    Term_Audit_Result = (int)EnumProcessStatus.Submit,
                    Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Initial
                };
                DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
            }
            #endregion
        }

        /// <summary>
        /// 修改合同条款模板（仅限于尚未有合同使用过的模板）
        /// 条款模板 修改后默认重新提交审核
        /// 相当于之前的合同条款模板删除，然后重新提交一个新的
        /// </summary>
        public AddContractTermTemplateRes UpdateContractTermTemplate(UpdateContractTermTemplate_IN updateContractTermTemplate_IN)
        {
            AddContractTermTemplateRes res = new AddContractTermTemplateRes();
            #region 参数验证
            if (updateContractTermTemplate_IN == null)
            {
                throw new ApiException("参数不能为空");
            }
            #endregion
            //有合同的不能改了
            if (CheckContractTermTemplateHasRelatedContract(updateContractTermTemplate_IN.Term_Template_Id))
            {
                throw new ApiException("合同条款模板已经关联了合同，不能修改");
            }
            DbOpe_crm_contractterms_template.Instance.TransDeal(() =>
            {
                //先删除原有的合同条款模板
                ExcuteDeleteContractTermTemplate(updateContractTermTemplate_IN.Term_Template_Id);
                //添加新的合同条款模板
                res = AddSingleContractTermTemplate(updateContractTermTemplate_IN);
                #region 工作流（待补充） 根据是否关联合同，进入不同的工作流处理流程
                #endregion
            });

            return res;
        }

        /// <summary>
        /// 升级合同条款模板（本质就是新建一个公司层级的条款）
        /// </summary>
        public AddContractTermTemplateRes UpgradeContractTermTemplate(UpgradeContractTermTemplate_IN upgradeContractTermTemplate_IN)
        {
            #region 参数验证
            if (upgradeContractTermTemplate_IN == null || upgradeContractTermTemplate_IN.NewContractTermTemplate == null)
            {
                throw new ApiException("参数不能为空");
            }
            #endregion

            #region 基础参数准备
            string contractTermTemplateId = Guid.NewGuid().ToString();
            string contractTermAuditId = "";
            string contractTermId = "";
            string userId = UserId;
            AddContractTermTemplateRes res = new AddContractTermTemplateRes()
            {
                Term_Audit_Id = contractTermAuditId,
                Term_Template_Id = contractTermTemplateId,
                Term_Id = contractTermId
            };
            #endregion

            #region 添加合同条款模板表Db_crm_contractterms_template
            Db_crm_contractterms_template term_template = upgradeContractTermTemplate_IN.MappingTo<Db_crm_contractterms_template>();
            term_template.Id = contractTermTemplateId;
            term_template.Term_Template_Level = (int)EnumTermTemplateLevel.CompanyLevel;
            term_template.Term_Template_Status = (int)EnumTermTemplateStatus.Approved;
            term_template.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
            SetNewSingleContractTermTemplate contractTermTemplate = upgradeContractTermTemplate_IN.NewContractTermTemplate;
            term_template.Specified_Customers = contractTermTemplate.Specified_Customers;
            term_template.Audit_Flow_Type = (int)contractTermTemplate.Audit_Flow_Type;
            term_template.Term_Audit_OrgType = contractTermTemplate.Term_Audit_OrgType == null ? null : (int)contractTermTemplate.Term_Audit_OrgType;
            term_template.One_Time = contractTermTemplate.One_Time;
            term_template.StartDate = contractTermTemplate.StartDate;
            term_template.EndDate = contractTermTemplate.EndDate;
            term_template.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
            term_template.AuditUser = TokenModel.Instance.id;
            term_template.AuditDate = DateTime.Now;

            switch (contractTermTemplate.Audit_Flow_Type)
            {
                case EnumAuditFlowType.AutoAudit:
                    term_template.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                    term_template.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                    break;
                case EnumAuditFlowType.HeadquartersCustomerServiceAudit:
                    term_template.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                    term_template.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.Need;
                    break;
                case EnumAuditFlowType.MarketingAndHeadquartersAudit:
                    term_template.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.Need;
                    term_template.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.Need;
                    break;
                default:
                    throw new ApiException("合同条款模板审核流程(" + term_template.Audit_Flow_Type + ")不正确");
            }
            DbOpe_crm_contractterms_template.Instance.Insert(term_template);
            #endregion

            #region 添加其他关联的表
            AddSingleContractTermTemplateRelatedTables(res, upgradeContractTermTemplate_IN);
            #endregion
            #region 生成产品规则描述
            DbOpe_crm_contractterms_template_productrules_item.Instance.FormatProductDecs(contractTermTemplateId);
            #endregion
            return res;
        }

        /// <summary>
        /// 判断是不是要删除合同条款模板
        /// 1、看是不是关联合同建立的，单独建立的合同条款模板不用删除
        /// 2、（看还有没有使用该模板的,如果没有了，就可以把这个模板也删除了）
        /// </summary>
        /// <param name="Term_Template_Id"></param>
        public void TryDeleteContractTermTemplate(string Term_Template_Id)
        {
            //1、看是不是关联合同建立的，单独建立的合同条款模板不用删除
            var termTemplate = DbOpe_crm_contractterms_template.Instance.GetData(t => t.Id == Term_Template_Id);
            if (!StringUtil.IsNullOrEmpty(termTemplate.Source_Contract_Id))
            {
                // 2、看还有没有使用该模板的,如果没有了，就可以把这个模板也删除了
                var anyRelatedContract = CheckContractTermTemplateHasRelatedContract(Term_Template_Id);
                if (!anyRelatedContract)
                {
                    DbOpe_crm_contractterms_template.Instance.DeleteData(t => t.Id == Term_Template_Id);
                    DbOpe_crm_contractterms_audits.Instance.DeleteData(t => t.Term_Template_Audit_Id == termTemplate.Last_Audit_Id && t.Term_Template_Id == Term_Template_Id);
                    DbOpe_crm_contractterms_template_applicablecollectingcompanies.Instance.DeleteData(t => t.Term_Template_Id == Term_Template_Id);
                    DbOpe_crm_contractterms_template_applicableproductrulegroups.Instance.DeleteData(t => t.Term_Template_Id == Term_Template_Id);
                    DbOpe_crm_contractterms_template_productrules_itemgroup.Instance.DeleteData(t => t.Term_Template_Id == Term_Template_Id);
                    DbOpe_crm_contractterms_template_productrules_item.Instance.DeleteData(t => t.Term_Template_Id == Term_Template_Id);
                }
            }
        }

        /// <summary>
        /// 删除合同条款模板
        /// </summary>
        /// <param name="Term_Template_Id"></param>
        public void ExcuteDeleteContractTermTemplate(string Term_Template_Id)
        {
            var termTemplate = DbOpe_crm_contractterms_template.Instance.GetData(t => t.Id == Term_Template_Id);
            DbOpe_crm_contractterms_template.Instance.DeleteData(t => t.Id == Term_Template_Id);
            DbOpe_crm_contractterms_audits.Instance.DeleteData(t => t.Term_Template_Audit_Id == termTemplate.Last_Audit_Id && t.Term_Template_Id == Term_Template_Id);
            DbOpe_crm_contractterms_template_applicablecollectingcompanies.Instance.DeleteData(t => t.Term_Template_Id == Term_Template_Id);
            DbOpe_crm_contractterms_template_applicableproductrulegroups.Instance.DeleteData(t => t.Term_Template_Id == Term_Template_Id);
            DbOpe_crm_contractterms_template_productrules_itemgroup.Instance.DeleteData(t => t.Term_Template_Id == Term_Template_Id);
            DbOpe_crm_contractterms_template_productrules_item.Instance.DeleteData(t => t.Term_Template_Id == Term_Template_Id);

        }


        /// <summary>
        /// 删除合同条款模板（真正删除）
        /// </summary>
        /// <param name="Term_Template_Id"></param>
        public void ExcuteDeleteContractTermTemplate_RealDelete(string Term_Template_Id)
        {
            var termTemplate = DbOpe_crm_contractterms_template.Instance.GetData(t => t.Id == Term_Template_Id);
            if (termTemplate != null)
            {
                DbOpe_crm_contractterms_template.Instance.DeleteData_RealDelete(t => t.Id == Term_Template_Id);
                DbOpe_crm_contractterms_audits.Instance.DeleteData_RealDelete(t => t.Term_Template_Audit_Id == termTemplate.Last_Audit_Id && t.Term_Template_Id == Term_Template_Id);
                DbOpe_crm_contractterms_template_applicablecollectingcompanies.Instance.DeleteData_RealDelete(t => t.Term_Template_Id == Term_Template_Id);
                DbOpe_crm_contractterms_template_applicableproductrulegroups.Instance.DeleteData_RealDelete(t => t.Term_Template_Id == Term_Template_Id);
                DbOpe_crm_contractterms_template_productrules_itemgroup.Instance.DeleteData_RealDelete(t => t.Term_Template_Id == Term_Template_Id);
                DbOpe_crm_contractterms_template_productrules_item.Instance.DeleteData_RealDelete(t => t.Term_Template_Id == Term_Template_Id);
            }

        }

        /// <summary>
        /// 客户经理获取合同条款模板列表
        /// </summary>
        /// <param name="searchContractTermTemplateList_IN"></param>
        /// <returns></returns>
        public ApiTableOut<IContractTermTemplateQueryResShow> SearchContractTermTemplateList(SearchContractTermTemplateList_IN searchContractTermTemplateList_IN)
        {
            return DbOpe_crm_contractterms_template.Instance.SearchContractTermTemplateList(searchContractTermTemplateList_IN, UserId);
        }

        /// <summary>
        /// 后台管理获取合同条款列表
        /// （对于审核中的条款模板，仅显示单独审核的模板，关联合同的条款模板不在这里进行审核，所以不会显示）
        /// （对于已经审核通过（通过/过期/作废）的条款模板，无论单独审核还是关联合同审核，都需要显示出来）
        /// </summary>
        public ApiTableOut<IContractTermTemplateQueryResManageShow> SearchContractTermTemplateBJList(SearchContractTermTemplateManageList_IN searchContractTermTemplateManageList_IN)
        {
            ContractTermTemplateQueryParams queryParams = searchContractTermTemplateManageList_IN.MappingTo<ContractTermTemplateQueryParams>();
            var query = DbOpe_crm_contractterms_template.Instance.SearchContractTermTemplateQuery(queryParams);
            int total = 0;
            var result = query
                .LeftJoin<Db_v_userwithorg>((basicQuery, auditUser) => basicQuery.Term_Auditor == auditUser.Id)
                .LeftJoin<Db_v_userwithorg>((basicQuery, auditUser, user) => basicQuery.CreateUser == user.Id)
                //（对于审核中的条款模板，仅显示单独审核的模板，关联合同的条款模板不在这里进行审核，所以不会显示）
                // (对于已经审核通过（通过/过期/作废）的条款模板，无论单独审核还是关联合同审核，都需要显示出来）
                .Where((basicQuery, auditUser, user) => (
                basicQuery.Term_Template_Status == (int)EnumTermTemplateStatus.UnderReview && SqlFunc.IsNullOrEmpty(basicQuery.Source_Contract_Id))
                || (SqlFunc.ContainsArray(new int[] { (int)EnumTermTemplateStatus.Approved, (int)EnumTermTemplateStatus.Expired, (int)EnumTermTemplateStatus.Cancelled }, basicQuery.Term_Template_Status))
                )
                .WhereIF(searchContractTermTemplateManageList_IN.AuditUsers != null && searchContractTermTemplateManageList_IN.AuditUsers.Count > 0, (basicQuery, auditUser, user) => searchContractTermTemplateManageList_IN.AuditUsers.Contains(basicQuery.Term_Auditor))
                .OrderByDescending((basicQuery, auditUser, user) => basicQuery.CreateDate)
                .Select((basicQuery, auditUser, user) => new ContractTermTemplateQueryResManageShow()
                {
                    Term_Template_Id = basicQuery.Term_Template_Id,
                    Content = basicQuery.Content,
                    Remark = basicQuery.Remark,
                    Specified_Customers = basicQuery.Specified_Customers,
                    Term_Auditor_Name = auditUser.UserWithOrgFullName,
                    Term_Audit_Time_Name = basicQuery.Term_Audit_Time != null ? basicQuery.Term_Audit_Time.Value.ToString("yyyy-MM-dd HH:mm") : "",
                    ProductsRuleDescription = basicQuery.ProductsRuleDescription,
                    UserName = user.UserWithOrgFullName,
                    CreateDateShow = basicQuery.CreateDate != null ? basicQuery.CreateDate.Value.ToString("yyyy-MM-dd HH:mm") : "",
                }, true)
                .MergeTable()
                .Mapper(it =>
                {
                    it.Period_Name = DbOpe_crm_contractterms_template.Instance.GetPeriodName(it.One_Time, it.StartDate, it.EndDate);
                    it.Term_Level_Name = ((EnumTermTemplateLevel)it.Term_Template_Level).GetEnumDescription();
                    it.Applicable_Years_Name = ((EnumApplicableYears)it.Applicable_Years).GetEnumDescription();
                    it.Applicable_Areas_Name = ((EnumTermTemplateApplicableAreas)it.Applicable_Areas).GetEnumDescription();
                    it.Audit_Flow_Type_Name = ((EnumAuditFlowType)it.Audit_Flow_Type).GetEnumDescription();
                    it.Term_Template_Status_Name = ((EnumTermTemplateStatus)it.Term_Template_Status).GetEnumDescription();
                    it.Marketing_Audit_Status_Name = ((EnumTermTemplateMarketingBJAuditStatus)it.Marketing_Audit_Status).GetEnumDescription();
                    it.Beijing_Audit_Status_Name = ((EnumTermTemplateMarketingBJAuditStatus)it.Beijing_Audit_Status).GetEnumDescription();
                    it.Term_Audit_OrgType_Name = it.Term_Audit_OrgType == null ? "" : ((EnumOrgType)it.Term_Audit_OrgType).GetEnumDescription();
                    it.Term_Audit_Process_Name = ((EnumTermAuditProcess)it.Term_Audit_Process).GetEnumDescription();
                })
                .ToPageList(searchContractTermTemplateManageList_IN.PageNumber, searchContractTermTemplateManageList_IN.PageSize, ref total);
            return new ApiTableOut<IContractTermTemplateQueryResManageShow>() { Data = result, Total = total };
        }

        /// <summary>
        /// 团队获取合同条款模板审核列表
        /// </summary>
        public ApiTableOut<IContractTermTemplateQueryResManageShow> SearchContractTermTemplateTeamList(SearchContractTermTemplateManageList_IN searchContractTermTemplateManageList_IN)
        {
            ContractTermTemplateQueryParams queryParams = searchContractTermTemplateManageList_IN.MappingTo<ContractTermTemplateQueryParams>();
            //获取当前用户的管理层级
            var user = DbOpe_sys_user.Instance.GetUserById(UserId);
            if (user == null)
            {
                throw new ApiException("未获取到用户信息");
            }
            if (user.UserType != EnumUserType.Manager)
            {
                throw new ApiException("当前用户并非管理者，没有查询权限");
            }
            var org = DbOpe_sys_organization.Instance.GetDataById(user.OrganizationId);
            if (org == null)
            {
                throw new ApiException("未获取到用户所属组织信息");
            }
            //根据当前管理的层级，查询对应的审核阶段
            switch (org.OrgType)
            {
                case EnumOrgType.BattleTeam:
                    queryParams.Term_Audit_Process = new List<int>() { (int)EnumTermAuditProcess.BattleTeam };
                    break;
                case EnumOrgType.Battalion:
                    queryParams.Term_Audit_Process = new List<int>() { (int)EnumTermAuditProcess.Battalion };
                    break;
                case EnumOrgType.Squadron:
                    queryParams.Term_Audit_Process = new List<int>() { (int)EnumTermAuditProcess.Squadron };
                    break;
                default:
                    throw new ApiException("当前用户所属组织类型不正确");
            }
            //获取下属所有队员
            var manageUsers = new List<string>();
            DbOpe_crm_contract_receiptregister.Instance.GetAuthShowOrgIdsAndUserIds(user.OrganizationId, UserId, ref manageUsers);
            if (queryParams.ManageSearchUserIds?.Count > 0)
            {
                foreach (var userId in queryParams.ManageSearchUserIds)
                {
                    if (!manageUsers.Contains(userId))
                    {
                        throw new ApiException("要搜索的人员的中存在权限问题");
                    }
                }
            }
            else
            {
                queryParams.ManageSearchUserIds = manageUsers;
            }
            //必须是待审核状态
            queryParams.Term_Template_Status = new List<int>() { (int)EnumTermTemplateStatus.UnderReview };
            //必须是团队+总部审核
            queryParams.Audit_Flow_Type = new List<int>() { (int)EnumAuditFlowType.MarketingAndHeadquartersAudit };

            var query = DbOpe_crm_contractterms_template.Instance.SearchContractTermTemplateQuery(queryParams);
            int total = 0;
            var result = query
                .LeftJoin<Db_v_userwithorg>((basicQuery, auditUser) => basicQuery.Term_Auditor == auditUser.Id)
                .LeftJoin<Db_v_userwithorg>((basicQuery, auditUser, user) => basicQuery.CreateUser == user.Id)
                .OrderByDescending((basicQuery, auditUser, user) => basicQuery.CreateDate)
                .Select((basicQuery, auditUser, user) => new ContractTermTemplateQueryResManageShow()
                {
                    Term_Template_Id = basicQuery.Term_Template_Id,
                    Content = basicQuery.Content,
                    Remark = basicQuery.Remark,
                    Specified_Customers = basicQuery.Specified_Customers,
                    Term_Auditor_Name = auditUser.UserWithOrgFullName,
                    Term_Audit_Time_Name = basicQuery.Term_Audit_Time != null ? basicQuery.Term_Audit_Time.Value.ToString("yyyy-MM-dd HH:mm") : "",
                    ProductsRuleDescription = basicQuery.ProductsRuleDescription,
                    UserName = user.UserWithOrgFullName,
                    CreateDateShow = basicQuery.CreateDate != null ? basicQuery.CreateDate.Value.ToString("yyyy-MM-dd HH:mm") : "",
                }, true)
                .MergeTable()
                .Mapper(it =>
                {
                    it.Period_Name = DbOpe_crm_contractterms_template.Instance.GetPeriodName(it.One_Time, it.StartDate, it.EndDate);
                    it.Term_Level_Name = ((EnumTermTemplateLevel)it.Term_Template_Level).GetEnumDescription();
                    it.Applicable_Years_Name = ((EnumApplicableYears)it.Applicable_Years).GetEnumDescription();
                    it.Applicable_Areas_Name = ((EnumTermTemplateApplicableAreas)it.Applicable_Areas).GetEnumDescription();
                    it.Audit_Flow_Type_Name = ((EnumAuditFlowType)it.Audit_Flow_Type).GetEnumDescription();
                    it.Term_Template_Status_Name = ((EnumTermTemplateStatus)it.Term_Template_Status).GetEnumDescription();
                    it.Marketing_Audit_Status_Name = ((EnumTermTemplateMarketingBJAuditStatus)it.Marketing_Audit_Status).GetEnumDescription();
                    it.Beijing_Audit_Status_Name = ((EnumTermTemplateMarketingBJAuditStatus)it.Beijing_Audit_Status).GetEnumDescription();
                    it.Term_Audit_OrgType_Name = it.Term_Audit_OrgType == null ? "" : ((EnumOrgType)it.Term_Audit_OrgType).GetEnumDescription();
                    it.Term_Audit_Process_Name = ((EnumTermAuditProcess)it.Term_Audit_Process).GetEnumDescription();
                })
                .ToPageList(searchContractTermTemplateManageList_IN.PageNumber, searchContractTermTemplateManageList_IN.PageSize, ref total);
            return new ApiTableOut<IContractTermTemplateQueryResManageShow>() { Data = result, Total = total };
        }

        /// <summary>
        /// 后台、团队管理获取合同条款列表
        /// </summary>
        /// <param name="searchContractTermTemplateManageList_IN"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public ApiTableOut<IContractTermTemplateQueryResManageShow> SearchContractTermTemplateManageList(SearchContractTermTemplateManageList_IN searchContractTermTemplateManageList_IN)
        {
            //获取当前用户的管理层级
            var user = DbOpe_sys_user.Instance.GetUserById(UserId);
            if (user == null)
            {
                throw new ApiException("未获取到用户信息");
            }
            if (user.UserType != EnumUserType.Manager)
            {
                throw new ApiException("当前用户并非管理者，没有查询权限");
            }
            if (user.OrganizationId == Guid.Empty.ToString())
            {
                return SearchContractTermTemplateBJList(searchContractTermTemplateManageList_IN);
            }
            else
            {
                return SearchContractTermTemplateTeamList(searchContractTermTemplateManageList_IN);
            }

        }

        /// <summary>
        /// 后台(初审)单条合同条款，设置合同条款审核流程等
        /// (仅审核模板，条款和合同状态并不会进行更新)
        /// </summary>
        /// <param name="contractTermTemplate"></param>
        public void SetNewSingleContractTermTemplate(SetNewSingleContractTermTemplate contractTermTemplate)
        {
            if (contractTermTemplate == null || StringUtil.IsNullOrEmptyTrim(contractTermTemplate.Term_Template_Id))
            {
                throw new ApiException("合同条款模板ID不能为空");
            }
            //获取条款模板信息
            var termTemplateInfo = DbOpe_crm_contractterms_template.Instance.GetDataById(contractTermTemplate.Term_Template_Id);
            if (termTemplateInfo == null)
            {
                throw new ApiException("未获取到合同条款模板信息");
            }
            //判断审核状态 
            if (termTemplateInfo.Term_Template_Status != (int)EnumTermTemplateStatus.UnderReview)
            {
                throw new ApiException("合同条款模板审核状态(" + termTemplateInfo.Term_Template_Status + ")不正确");
            }
            //判断审核阶段
            if (termTemplateInfo.Term_Audit_Process != (int)EnumTermAuditProcess.BJ_Initial)
            {
                throw new ApiException("合同条款模板审核阶段(" + termTemplateInfo.Term_Audit_Process + ")不正确");
            }
            var auditId = Guid.NewGuid().ToString();
            if (contractTermTemplate.AuditResult)
            {
                //初审通过，记录信息，审核状态还是审核中，复核通过才更新为通过
                termTemplateInfo.Specified_Customers = contractTermTemplate.Specified_Customers;
                termTemplateInfo.Audit_Flow_Type = (int)contractTermTemplate.Audit_Flow_Type;
                termTemplateInfo.Term_Audit_OrgType = contractTermTemplate.Term_Audit_OrgType == null ? null : (int)contractTermTemplate.Term_Audit_OrgType;
                termTemplateInfo.One_Time = contractTermTemplate.One_Time;
                termTemplateInfo.StartDate = contractTermTemplate.StartDate;
                termTemplateInfo.EndDate = contractTermTemplate.EndDate;

                //审核阶段转到下一阶段
                if (termTemplateInfo.Audit_Flow_Type == (int)EnumAuditFlowType.MarketingAndHeadquartersAudit)
                {
                    //下一阶段转到指定的团队
                    switch (contractTermTemplate.Term_Audit_OrgType)
                    {
                        case EnumOrgType.BattleTeam:
                            termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.BattleTeam;
                            break;
                        case EnumOrgType.Battalion:
                            termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.Battalion;
                            break;
                        case EnumOrgType.Squadron:
                            termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.Squadron;
                            break;
                        default:
                            throw new ApiException("组织类型不正确");
                    }
                    termTemplateInfo.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                    termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                }
                else if (termTemplateInfo.Audit_Flow_Type == (int)EnumAuditFlowType.HeadquartersCustomerServiceAudit
                    || termTemplateInfo.Audit_Flow_Type == (int)EnumAuditFlowType.AutoAudit)
                {
                    //下一阶段转到北京复核
                    termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
                    termTemplateInfo.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                    termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                }
                else
                {
                    throw new ApiException("审核流程选择不正确");
                }
                //记录合同条款模板关联的审核记录id
                termTemplateInfo.Last_Audit_Id = auditId;
                termTemplateInfo.AuditUser = UserId;
                termTemplateInfo.AuditDate = DateTime.Now;
                DbOpe_crm_contractterms_template.Instance.Update(termTemplateInfo);
                //如果是单独建的合同条款模板 则添加合同条款审核表Db_crm_contractterms_audits
                if (string.IsNullOrEmpty(termTemplateInfo.Source_Contract_Id))
                {
                    Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                    {
                        Id = auditId,
                        Term_Template_Id = termTemplateInfo.Id,
                        Term_Template_Audit_Id = auditId,
                        Term_Id = "",
                        Term_Contract_Id = "",
                        Term_Audit_Result = (int)EnumProcessStatus.Pass,
                        Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Initial,
                        Term_Auditor = UserId,
                        Term_Audit_Time = DateTime.Now
                    };
                    DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                }

            }
            else
            {
                //审核不通过
                termTemplateInfo.Term_Template_Status = (int)EnumTermTemplateStatus.Rejected;
                //审核阶段终止，记录最后阶段为初审
                termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                //记录合同条款模板关联的审核记录id
                termTemplateInfo.Last_Audit_Id = auditId;
                termTemplateInfo.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingConfirmation;
                termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.Rejected;
                termTemplateInfo.AuditUser = UserId;
                termTemplateInfo.AuditDate = DateTime.Now;
                DbOpe_crm_contractterms_template.Instance.Update(termTemplateInfo);
                //添加合同条款审核表Db_crm_contractterms_audits
                //如果是单独建的合同条款模板 则添加合同条款审核表Db_crm_contractterms_audits
                if (string.IsNullOrEmpty(termTemplateInfo.Source_Contract_Id))
                {
                    Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                    {
                        Id = auditId,
                        Term_Template_Id = termTemplateInfo.Id,
                        Term_Template_Audit_Id = auditId,
                        Term_Id = "",
                        Term_Contract_Id = "",
                        Term_Audit_Result = (int)EnumProcessStatus.Refuse,
                        Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Initial,
                        Term_Auditor = UserId,
                        Term_Audit_Time = DateTime.Now
                    };
                    DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                }
            }
        }



        /// <summary>
        /// 初审撤回单条合同条款模板
        /// </summary>
        /// <param name="revokeSingleTermTemplate"></param>
        /// <exception cref="ApiException"></exception>
        public void RevokeNewSingleContractTermTemplate(RevokeSingleTermTemplate revokeSingleTermTemplate)
        {
            #region 判断参数
            if (revokeSingleTermTemplate == null || StringUtil.IsNullOrEmptyTrim(revokeSingleTermTemplate.Term_Template_Id))
            {
                throw new ApiException("合同条款模板ID不能为空");
            }
            #endregion

            #region 获取合同条款模板信息
            //获取条款模板信息
            var termTemplateInfo = DbOpe_crm_contractterms_template.Instance.GetDataById(revokeSingleTermTemplate.Term_Template_Id);
            if (termTemplateInfo == null)
            {
                throw new ApiException("未获取到合同条款模板信息");
            }
            //判断审核状态
            if (termTemplateInfo.Term_Template_Status != (int)EnumTermTemplateStatus.UnderReview
                && termTemplateInfo.Term_Template_Status != (int)EnumTermTemplateStatus.Rejected)
            {
                throw new ApiException("合同条款模板审核状态(" + termTemplateInfo.Term_Template_Status + ")不正确");
            }
            var currentOrgProcess = (int)EnumTermAuditProcess.BJ_Initial;
            //判断审核阶段
            var lastAuditInfo = DbOpe_crm_contractterms_audits.Instance.GetDataById(termTemplateInfo.Last_Audit_Id);
            if (lastAuditInfo == null)
            {
                throw new ApiException("合同条款模板关联的审核记录不存在");
            }
            if (lastAuditInfo.Term_Audit_Flow != currentOrgProcess)
            {
                throw new ApiException("合同条款模板关联的最后审核记录所属流程(" + lastAuditInfo.Term_Audit_Flow + ")不正确");
            }
            //撤回审核数据
            //删除最后一条审核记录
            DbOpe_crm_contractterms_audits.Instance.DeleteData(lastAuditInfo.Id);
            //重新获取最后一条审核记录
            var newLastAuditInfo = DbOpe_crm_contractterms_audits.Instance.GetDataList(a => a.Term_Template_Id == termTemplateInfo.Id)
                .OrderByDescending(a => a.Term_Audit_Time).FirstOrDefault();
            if (newLastAuditInfo != null)
            {
                termTemplateInfo.Last_Audit_Id = newLastAuditInfo.Id;
                termTemplateInfo.AuditUser = newLastAuditInfo.CreateUser;
                termTemplateInfo.AuditDate = newLastAuditInfo.CreateDate;
            }
            else
            {
                termTemplateInfo.Last_Audit_Id = null;
                termTemplateInfo.AuditUser = null;
                termTemplateInfo.AuditDate = null;
            }

            termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
            termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingConfirmation;
            termTemplateInfo.Term_Template_Status = (int)EnumTermTemplateStatus.UnderReview;

            DbOpe_crm_contractterms_template.Instance.Update(termTemplateInfo);
            #endregion


        }


        /// <summary>
        /// 团队审核单条合同条款模板(如果审核流程不是当前人的流程，则直接返回)
        /// </summary>
        /// <param name="teamAudit"></param>
        /// <exception cref="ApiException"></exception>
        public void TeamAuditSingleContractTermTemplate(TeamAuditSingleContractTermTemplate teamAudit)
        {
            if (teamAudit == null || StringUtil.IsNullOrEmptyTrim(teamAudit.Term_Template_Id))
            {
                throw new ApiException("合同条款模板ID不能为空");
            }
            //获取条款模板信息
            var termTemplateInfo = DbOpe_crm_contractterms_template.Instance.GetDataById(teamAudit.Term_Template_Id);
            if (termTemplateInfo == null)
            {
                throw new ApiException("未获取到合同条款模板信息");
            }
            //判断审核状态
            if (termTemplateInfo.Term_Template_Status != (int)EnumTermTemplateStatus.UnderReview)
            {
                throw new ApiException("合同条款模板审核状态(" + termTemplateInfo.Term_Template_Status + ")不正确");
            }
            //获取当前用户的管理层级
            var user = DbOpe_sys_user.Instance.GetUserById(UserId);
            if (user == null)
            {
                throw new ApiException("未获取到用户信息");
            }
            if (user.UserType != EnumUserType.Manager)
            {
                throw new ApiException("当前用户并非管理者，没有审核权限");
            }
            var org = DbOpe_sys_organization.Instance.GetDataById(user.OrganizationId);
            if (org == null)
            {
                throw new ApiException("未获取到用户所属组织信息");
            }
            //判断审核阶段
            var currentProcess = (int)EnumTermAuditProcess.BattleTeam;
            switch (org.OrgType)
            {
                case EnumOrgType.BattleTeam:
                    break;
                case EnumOrgType.Battalion:
                    currentProcess = (int)EnumTermAuditProcess.Battalion;

                    break;
                case EnumOrgType.Squadron:
                    currentProcess = (int)EnumTermAuditProcess.Squadron;

                    break;
                default:
                    throw new ApiException("当前用户所属组织类型不正确");
            }
            if (termTemplateInfo.Term_Audit_Process != currentProcess)
            {
                return;
            }
            var auditId = Guid.NewGuid().ToString();
            if (teamAudit.AuditResult)
            {
                //团队审核通过
                //审核阶段转到下一阶段
                termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
                termTemplateInfo.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.Approved;
                termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                //记录合同条款模板关联的审核记录id
                termTemplateInfo.Last_Audit_Id = auditId;
                termTemplateInfo.AuditUser = UserId;
                termTemplateInfo.AuditDate = DateTime.Now;
                DbOpe_crm_contractterms_template.Instance.Update(termTemplateInfo);
                //如果是单独建的合同条款模板 则添加合同条款审核表Db_crm_contractterms_audits
                if (string.IsNullOrEmpty(termTemplateInfo.Source_Contract_Id))
                {
                    Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                    {
                        Id = auditId,
                        Term_Template_Id = termTemplateInfo.Id,
                        Term_Template_Audit_Id = auditId,
                        Term_Id = "",
                        Term_Contract_Id = "",
                        Term_Audit_Result = (int)EnumProcessStatus.Pass,
                        Term_Audit_Flow = currentProcess,
                        Term_Auditor = UserId,
                        Term_Audit_Time = DateTime.Now
                    };
                    DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                }

            }
            else
            {
                //审核不通过
                termTemplateInfo.Term_Template_Status = (int)EnumTermTemplateStatus.Rejected;
                termTemplateInfo.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.Rejected;
                //这得回初审重新定流程
                termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                //记录合同条款模板关联的审核记录id
                termTemplateInfo.Last_Audit_Id = auditId;
                termTemplateInfo.AuditUser = UserId;
                termTemplateInfo.AuditDate = DateTime.Now;
                DbOpe_crm_contractterms_template.Instance.Update(termTemplateInfo);
                //如果是单独建的合同条款模板 则添加合同条款审核表Db_crm_contractterms_audits
                if (string.IsNullOrEmpty(termTemplateInfo.Source_Contract_Id))
                {
                    Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                    {
                        Id = auditId,
                        Term_Template_Id = termTemplateInfo.Id,
                        Term_Template_Audit_Id = auditId,
                        Term_Id = "",
                        Term_Contract_Id = "",
                        Term_Audit_Result = (int)EnumProcessStatus.Refuse,
                        Term_Audit_Flow = currentProcess,
                        Term_Auditor = UserId,
                        Term_Audit_Time = DateTime.Now
                    };
                    DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                }
            }
        }

        /// <summary>
        /// 撤销团队审核单条合同条款
        /// </summary>
        /// <param name="revokeTeamSingleTermTemplate"></param>
        public void RevokeTeamSingleTermTemplate(RevokeSingleTermTemplate revokeTeamSingleTermTemplate)
        {
            #region 判断参数
            if (revokeTeamSingleTermTemplate == null || StringUtil.IsNullOrEmptyTrim(revokeTeamSingleTermTemplate.Term_Template_Id))
            {
                throw new ApiException("合同条款模板ID不能为空");
            }
            #endregion

            #region 获取合同条款模板信息
            //获取条款模板信息
            var termTemplateInfo = DbOpe_crm_contractterms_template.Instance.GetDataById(revokeTeamSingleTermTemplate.Term_Template_Id);
            if (termTemplateInfo == null)
            {
                throw new ApiException("未获取到合同条款模板信息");
            }
            //判断审核状态
            if (termTemplateInfo.Term_Template_Status != (int)EnumTermTemplateStatus.UnderReview
                && termTemplateInfo.Term_Template_Status != (int)EnumTermTemplateStatus.Rejected)
            {
                throw new ApiException("合同条款模板审核状态(" + termTemplateInfo.Term_Template_Status + ")不正确");
            }
            //获取当前用户的管理层级
            var user = DbOpe_sys_user.Instance.GetUserById(UserId);
            if (user == null)
            {
                throw new ApiException("未获取到用户信息");
            }
            if (user.UserType != EnumUserType.Manager)
            {
                throw new ApiException("当前用户并非管理者，没有审核权限");
            }
            var org = DbOpe_sys_organization.Instance.GetDataById(user.OrganizationId);
            if (org == null)
            {
                throw new ApiException("未获取到用户所属组织信息");
            }
            var currentOrgProcess = (int)EnumTermAuditProcess.BattleTeam;
            switch (org.OrgType)
            {
                case EnumOrgType.BattleTeam:
                    currentOrgProcess = (int)EnumTermAuditProcess.BattleTeam;
                    break;
                case EnumOrgType.Battalion:
                    currentOrgProcess = (int)EnumTermAuditProcess.Battalion;
                    break;
                case EnumOrgType.Squadron:
                    currentOrgProcess = (int)EnumTermAuditProcess.Squadron;
                    break;
                default:
                    throw new ApiException("当前用户所属组织类型不正确");
            }
            //判断审核阶段
            var lastAuditInfo = DbOpe_crm_contractterms_audits.Instance.GetDataById(termTemplateInfo.Last_Audit_Id);
            if (lastAuditInfo == null)
            {
                throw new ApiException("合同条款模板关联的审核记录不存在");
            }
            if (termTemplateInfo.Audit_Flow_Type != (int)EnumAuditFlowType.MarketingAndHeadquartersAudit)
            {
                throw new ApiException("合同条款模板审核流程(" + termTemplateInfo.Term_Audit_Process + ")不正确，该条款无需团队审核");
            }
            if (lastAuditInfo.Term_Audit_Flow != currentOrgProcess)
            {
                throw new ApiException("合同条款模板关联的最后审核记录所属流程(" + lastAuditInfo.Term_Audit_Flow + ")不正确");
            }
            //撤回审核数据
            //删除最后一条审核记录
            DbOpe_crm_contractterms_audits.Instance.DeleteData(lastAuditInfo.Id);
            //重新获取最后一条审核记录
            var newLastAuditInfo = DbOpe_crm_contractterms_audits.Instance.GetDataList(a => a.Term_Template_Id == termTemplateInfo.Id)
                .OrderByDescending(a => a.Term_Audit_Time).FirstOrDefault();
            termTemplateInfo.Last_Audit_Id = newLastAuditInfo.Id;
            termTemplateInfo.AuditUser = newLastAuditInfo.CreateUser;
            termTemplateInfo.AuditDate = newLastAuditInfo.CreateDate;
            termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
            termTemplateInfo.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
            DbOpe_crm_contractterms_template.Instance.Update(termTemplateInfo);
            #endregion

        }

        /// <summary>
        /// 后台复核单条合同条款
        /// </summary>
        /// <param name="reviewSingleContractTermTemplate"></param>
        public void ReviewSingleContractTermTemplate(ReviewSingleContractTermTemplate reviewSingleContractTermTemplate)
        {
            if (reviewSingleContractTermTemplate == null || StringUtil.IsNullOrEmptyTrim(reviewSingleContractTermTemplate.Term_Template_Id))
            {
                throw new ApiException("合同条款模板ID不能为空");
            }
            //获取条款模板信息
            var termTemplateInfo = DbOpe_crm_contractterms_template.Instance.GetDataById(reviewSingleContractTermTemplate.Term_Template_Id);
            if (termTemplateInfo == null)
            {
                throw new ApiException("未获取到合同条款模板信息");
            }
            //判断审核状态
            if (termTemplateInfo.Term_Template_Status != (int)EnumTermTemplateStatus.UnderReview)
            {
                throw new ApiException("合同条款模板审核状态(" + termTemplateInfo.Term_Template_Status + ")不正确");
            }
            //判断审核阶段
            if (termTemplateInfo.Term_Audit_Process != (int)EnumTermAuditProcess.BJ_Review)
            {
                throw new ApiException("合同条款模板审核阶段(" + termTemplateInfo.Term_Audit_Process + ")不正确");
            }
            var auditId = Guid.NewGuid().ToString();
            if (reviewSingleContractTermTemplate.AuditResult)
            {
                //复核通过
                termTemplateInfo.Term_Template_Status = (int)EnumTermTemplateStatus.Approved;
                //记录合同条款模板关联的审核记录id
                termTemplateInfo.Last_Audit_Id = auditId;
                termTemplateInfo.AuditUser = UserId;
                termTemplateInfo.AuditDate = DateTime.Now;
                switch (termTemplateInfo.Audit_Flow_Type)
                {
                    case (int)EnumAuditFlowType.AutoAudit:
                        termTemplateInfo.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                        termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                        break;
                    case (int)EnumAuditFlowType.HeadquartersCustomerServiceAudit:
                        termTemplateInfo.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.NoAudit;
                        termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.Need;
                        break;
                    case (int)EnumAuditFlowType.MarketingAndHeadquartersAudit:
                        termTemplateInfo.Marketing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.Need;
                        termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.Need;
                        break;
                    default:
                        throw new ApiException("合同条款模板审核流程(" + termTemplateInfo.Term_Audit_Process + ")不正确");
                }
                DbOpe_crm_contractterms_template.Instance.Update(termTemplateInfo);
                //如果是单独建的合同条款模板 则添加合同条款审核表Db_crm_contractterms_audits
                if (string.IsNullOrEmpty(termTemplateInfo.Source_Contract_Id))
                {
                    Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                    {
                        Id = auditId,
                        Term_Template_Id = termTemplateInfo.Id,
                        Term_Template_Audit_Id = auditId,
                        Term_Id = "",
                        Term_Contract_Id = "",
                        Term_Audit_Result = (int)EnumProcessStatus.Pass,
                        Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Review,
                        Term_Auditor = UserId,
                        Term_Audit_Time = DateTime.Now
                    };
                    DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                }
            }
            else
            {
                //审核不通过,审核状态不变，仍然是审核中
                //审核阶段退回初审
                termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Initial;
                termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
                termTemplateInfo.Term_Template_Status = (int)EnumTermTemplateStatus.UnderReview;
                //记录合同条款模板关联的审核记录id
                termTemplateInfo.Last_Audit_Id = auditId;
                termTemplateInfo.AuditUser = UserId;
                termTemplateInfo.AuditDate = DateTime.Now;
                DbOpe_crm_contractterms_template.Instance.Update(termTemplateInfo);
                //如果是单独建的合同条款模板 则添加合同条款审核表Db_crm_contractterms_audits
                if (string.IsNullOrEmpty(termTemplateInfo.Source_Contract_Id))
                {
                    Db_crm_contractterms_audits contractterms_audit = new Db_crm_contractterms_audits()
                    {
                        Id = auditId,
                        Term_Template_Id = termTemplateInfo.Id,
                        Term_Template_Audit_Id = auditId,
                        Term_Id = "",
                        Term_Contract_Id = "",
                        Term_Audit_Result = (int)EnumProcessStatus.Refuse,
                        Term_Audit_Flow = (int)EnumTermAuditProcess.BJ_Review,
                        Term_Auditor = UserId,
                        Term_Audit_Time = DateTime.Now
                    };
                    DbOpe_crm_contractterms_audits.Instance.Insert(contractterms_audit);
                }
            }
        }

        /// <summary>
        /// 复核撤回单条合同条款模板
        /// </summary>
        /// <param name="revokeSingleTermTemplate"></param>
        /// <exception cref="ApiException"></exception>
        public void RevokeReviewSingleContractTermTemplate(RevokeSingleTermTemplate revokeSingleTermTemplate)
        {
            #region 判断参数
            if (revokeSingleTermTemplate == null || StringUtil.IsNullOrEmptyTrim(revokeSingleTermTemplate.Term_Template_Id))
            {
                throw new ApiException("合同条款模板ID不能为空");
            }
            #endregion

            #region 获取合同条款模板信息
            //获取条款模板信息
            var termTemplateInfo = DbOpe_crm_contractterms_template.Instance.GetDataById(revokeSingleTermTemplate.Term_Template_Id);
            if (termTemplateInfo == null)
            {
                throw new ApiException("未获取到合同条款模板信息");
            }
            //判断审核状态
            if (termTemplateInfo.Term_Template_Status != (int)EnumTermTemplateStatus.UnderReview
                && termTemplateInfo.Term_Template_Status != (int)EnumTermTemplateStatus.Approved)
            {
                throw new ApiException("合同条款模板审核状态(" + termTemplateInfo.Term_Template_Status + ")不正确");
            }
            var currentOrgProcess = (int)EnumTermAuditProcess.BJ_Review;
            //判断审核阶段
            var lastAuditInfo = DbOpe_crm_contractterms_audits.Instance.GetDataById(termTemplateInfo.Last_Audit_Id);
            if (lastAuditInfo == null)
            {
                throw new ApiException("合同条款模板关联的审核记录不存在");
            }
            if (lastAuditInfo.Term_Audit_Flow != currentOrgProcess)
            {
                throw new ApiException("合同条款模板关联的最后审核记录所属流程(" + lastAuditInfo.Term_Audit_Flow + ")不正确");
            }
            //撤回审核数据
            //删除最后一条审核记录
            DbOpe_crm_contractterms_audits.Instance.DeleteData(lastAuditInfo.Id);
            //重新获取最后一条审核记录
            var newLastAuditInfo = DbOpe_crm_contractterms_audits.Instance.GetDataList(a => a.Term_Template_Id == termTemplateInfo.Id)
                .OrderByDescending(a => a.Term_Audit_Time).FirstOrDefault();
            termTemplateInfo.Last_Audit_Id = newLastAuditInfo.Id;
            termTemplateInfo.AuditUser = newLastAuditInfo.CreateUser;
            termTemplateInfo.AuditDate = newLastAuditInfo.CreateDate;
            termTemplateInfo.Term_Audit_Process = (int)EnumTermAuditProcess.BJ_Review;
            termTemplateInfo.Beijing_Audit_Status = (int)EnumTermTemplateMarketingBJAuditStatus.PendingAudit;
            termTemplateInfo.Term_Template_Status = (int)EnumTermTemplateStatus.UnderReview;

            DbOpe_crm_contractterms_template.Instance.Update(termTemplateInfo);
            #endregion


        }

        /// <summary>
        /// 获取单条合同条款模板
        /// </summary>
        /// <param name="Term_Template_Id"></param>
        public ContractTermTemplate GetSingleContractTermTemplate(string Term_Template_Id)
        {
            return DbOpe_crm_contractterms_template.Instance.GetSingleContractTermTemplate(Term_Template_Id, UserId);
        }

        #endregion

        #region 其他尚未归类
        /// <summary>
        /// 获取用户可以选择的组织类型
        /// </summary>
        public List<EnumOrgType> GetUserOrgTypes(string userId)
        {
            List<EnumOrgType> enumOrgTypes = new List<EnumOrgType>();
            var user = DbOpe_sys_user.Instance.GetUserById(userId);
            if (user != null && user.OrganizationId != Guid.Empty.ToString())
            {
                var userOrg = DbOpe_sys_organization.Instance.GetOrganizationById(user.OrganizationId);
                enumOrgTypes.Add(userOrg.OrgType);
                var pOrgs = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                if (pOrgs?.Count > 0)
                {
                    foreach (var pOrg in pOrgs)
                    {

                        if (!enumOrgTypes.Contains(pOrg.OrgType) && pOrg.Id != Guid.Empty.ToString())
                        {
                            enumOrgTypes.Add(pOrg.OrgType);
                        }
                    }
                }
            }
            return enumOrgTypes;
        }
        /// <summary>
        /// 获取用户可以选择的组织类型
        /// </summary>
        public List<string> GetUserOrgManageOrgs(string userId)
        {
            List<string> manageOrgs = new List<string>();
            var user = DbOpe_sys_user.Instance.GetUserById(userId);
            if (user != null && user.OrganizationId != Guid.Empty.ToString())
            {
                var userOrg = DbOpe_sys_organization.Instance.GetOrganizationById(user.OrganizationId);
                manageOrgs.Add(userOrg.Id);
                var pOrgs = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                if (pOrgs?.Count > 0)
                {
                    foreach (var pOrg in pOrgs)
                    {

                        if (!manageOrgs.Contains(pOrg.Id) && pOrg.Id != Guid.Empty.ToString())
                        {
                            manageOrgs.Add(pOrg.Id);
                        }
                    }
                }
            }
            return manageOrgs;
        }

        /// <summary>
        /// 检查合同条款模板是否已经关联了合同
        /// </summary>
        /// <param name="termTemplateId"></param>
        /// <returns></returns>
        private bool CheckContractTermTemplateHasRelatedContract(string termTemplateId)
        {
            return DbOpe_crm_contractterms.Instance.GetDataList(t => t.Term_Template_Id == termTemplateId).Any();
        }

        /// <summary>
        /// 删除合同条款模板(仅仅是不显示了，物理上数据保留)
        /// 关联合同的不能删除；没有关联合同的可以删除
        /// Term_Template_Status = 1
        /// </summary>
        public void DeleteContractTermTemplate(string termTemplateId)
        {
            var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(termTemplateId);
            if (termTemplate == null)
            {
                throw new ApiException("合同条款模板不存在");
            }
            else
            {
                if (CheckContractTermTemplateHasRelatedContract(termTemplateId))
                {
                    throw new ApiException("合同条款模板存在关联合同，不能删除");
                }
                else
                {
                    termTemplate.Term_Template_Status = (int)EnumTermTemplateStatus.Deleted;
                    DbOpe_crm_contractterms_template.Instance.Update(termTemplate);
                }
            }
        }


        /// <summary>
        /// 后台作废合同条款模板
        /// 作废后客户经理可以查单，不能再使用，再使用需要重新提交审核
        /// 通过的才可以作废，其他状态不能作废
        /// Term_Template_Status = 5
        /// </summary>
        public void VoidContractTermTemplate(string termTemplateId)
        {
            var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(termTemplateId);
            if (termTemplate == null)
            {
                throw new ApiException("合同条款模板不存在");
            }
            else
            {
                if (termTemplate.Term_Template_Status != (int)EnumTermTemplateStatus.Approved)
                {
                    throw new ApiException("审核通过状态的合同条款模板才可以进行作废");
                }
                else
                {
                    termTemplate.Term_Template_Status = (int)EnumTermTemplateStatus.Cancelled;
                    DbOpe_crm_contractterms_template.Instance.Update(termTemplate);
                }
            }
        }
        /// <summary>
        /// 延期合同条款模板
        /// 只对过期状态的条款模板有效
        /// </summary>
        public void DelayContractTermTemplate(DelayContractTermTemplate_IN delayContractTermTemplate_IN)
        {
            var termTemplate = DbOpe_crm_contractterms_template.Instance.GetDataById(delayContractTermTemplate_IN.Term_Template_Id);
            if (termTemplate == null)
            {
                throw new ApiException("合同条款模板不存在");
            }
            else
            {
                if (termTemplate.Term_Template_Status != (int)EnumTermTemplateStatus.Expired)
                {
                    throw new ApiException("只有过期的合同条款模板才能进行延期操作");
                }
                else
                {
                    if (delayContractTermTemplate_IN.EndDate < DateTime.Now)
                    {
                        throw new ApiException("延期时间不能小于当前时间");
                    }
                    termTemplate.Term_Template_Status = (int)EnumTermTemplateStatus.Approved;
                    termTemplate.EndDate = delayContractTermTemplate_IN.EndDate;
                    DbOpe_crm_contractterms_template.Instance.Update(termTemplate);
                }
            }
        }



        #endregion




    }

}
