﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    [Description("合同服务管理-其他数据")]
    public class ContractServiceInfoOtherDataController : MyControllerBase
    {
        public ContractServiceInfoOtherDataController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        /// <summary>
        /// 根据查询条件获取合同服务信息其他数据申请信息列表
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchContractProductServiceInfoOtherDataApplList_Out> SearchContractProductServiceInfoOtherDataApplList(SearchContractProductServiceInfoOtherDataApplList_In search_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.SearchContractProductServiceInfoOtherDataApplList(search_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据申请id获取合同服务申请信息_其他数据信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog] 
        public GetContractServiceInfoApplyInfoOtherDataByApplId_Out GetContractServiceInfoApplyInfoOtherDataByApplId(string Id)
        {
            return BLL_ContractServiceOtherData.Instance.GetContractServiceInfoApplyInfoOtherDataByApplId(Id);
        }

        /// <summary>
        /// 撤销合同服务信息其他数据申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void RevokeContractProductServiceInfoOtherDataAudit(OperateContractProductServiceInfoOtherDataAudit operate_In)
        {
            BLL_ContractServiceOtherData.Instance.RevokeContractProductServiceInfoOtherDataAudit(operate_In);
        }

        /// <summary>
        /// 作废合同服务信息其他数据申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void VoidContractProductServiceInfoOtherDataAudit(OperateContractProductServiceInfoOtherDataAudit operate_In)
        {
            BLL_ContractServiceOtherData.Instance.VoidContractProductServiceInfoOtherDataAudit(operate_In);
        }

        /// <summary>
        /// 删除合同服务信息其他数据申请信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void DeleteContractProductServiceInfoOtherDataAudit(OperateContractProductServiceInfoOtherDataAudit operate_In)
        {
            BLL_ContractServiceOtherData.Instance.DeleteContractProductServiceInfoOtherDataAudit(operate_In);
        }


        /// <summary>
        /// 根据申请id获取合同服务信息_其他数据信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoOtherDataByApplId_Out GetContractServiceInfoOtherDataByApplId(string Id)
        {
            //获取申请详细信息
            return DbOpe_crm_contract_productserviceinfo_otherdata_appl.Instance.GetContractServiceInfoOtherDataByApplId(Id);
        }


        /// <summary>
        /// 根据申请id获取合同服务信息_其他数据信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoOtherDataByApplId4ChangeApply_Out GetContractServiceInfoOtherDataByApplId4ChangeApply(string Id)
        {
            //获取服务详细信息
            return DbOpe_crm_contract_serviceinfo_otherdata.Instance.GetContractServiceInfoOtherDataByApplId4ChangeApply(Id);
        }

        /// <summary>
        /// 获取海关编码匹配信息
        /// </summary>
        /// <param name="match_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetOtherDataMatchingData_Out GetMatchingData(GetOtherDataMatchingData_In match_In)
        {
            return BLL_ContractServiceOtherData.Instance.GetMatchingData(match_In);
        }

    }
}
