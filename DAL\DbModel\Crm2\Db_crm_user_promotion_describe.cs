﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///员工职级描述表
    ///</summary>
    [SugarTable("crm_user_promotion_describe")]
    public class Db_crm_user_promotion_describe : IComparable<Db_crm_user_promotion_describe>
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:职级编码
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string RankCode {get;set;}

           /// <summary>
           /// Desc:职级描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string RankDescription {get;set;}

           /// <summary>
           /// Desc:职级排序码
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? RankSortCode {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

           public int CompareTo(Db_crm_user_promotion_describe? other)
           {
               if (ReferenceEquals(this, other))
               {
                   return 0;
               }

               if (ReferenceEquals(null, other))
               {
                   return 1;
               }
               return Nullable.Compare(RankSortCode, other.RankSortCode);
           }

           protected bool Equals(Db_crm_user_promotion_describe other)
           {
               return Id == other.Id;
           }
    }
}
