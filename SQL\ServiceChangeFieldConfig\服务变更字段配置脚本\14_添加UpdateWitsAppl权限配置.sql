-- 为接口添加权限配置
-- 执行日期: 2025-01-29

-- ================================
-- 变量定义
-- ================================
SET @now = NOW();
SET @userId = 'sys'; -- 创建用户

-- ================================
-- 1. 为合同列表模块下的服务变更添加UpdateWitsAppl接口权限
-- ================================
-- 动态获取服务变更模块ID（合同列表下的服务变更）
SET @serviceChangeId = (SELECT `Id` FROM sys_form WHERE `Describe` = '合同管理-合同列表-服务变更' AND `Deleted` = 0 LIMIT 1);

-- 检查服务变更模块是否存在并插入权限配置
INSERT INTO sys_form (`Id`, `Name`, `Describe`, `Type`, `ParentId`, `Title`, `OrderNum`, `ControllerName`, `MethodName`, `LogTemplate`, `Deleted`, `CreateUser`, `CreateDate`)
SELECT 
    UUID(), 
    'UpdateWitsAppl', 
    '合同管理-合同列表-服务变更-UpdateWitsAppl', 
    5, 
    @serviceChangeId, 
    'UpdateWitsAppl', 
    9, 
    'Contract', 
    'UpdateWitsAppl', 
    NULL, 
    0, 
    @userId, 
    @now
WHERE @serviceChangeId IS NOT NULL;

-- 显示服务变更模块配置结果
SELECT 
    CASE 
        WHEN @serviceChangeId IS NOT NULL THEN CONCAT('服务变更模块权限配置成功，模块ID: ', @serviceChangeId)
        ELSE '警告：未找到服务变更模块，请检查模块描述是否正确'
    END AS message;

-- ================================
-- 验证配置结果
-- ================================
SELECT 'UpdateWitsAppl接口权限配置完成！' AS message;

-- 显示添加的权限配置
SELECT 
    f.Id,
    f.Name,
    f.Describe,
    f.Title,
    f.ControllerName,
    f.MethodName,
    f.ParentId,
    p.Title as ParentTitle,
    f.OrderNum,
    f.CreateUser,
    f.CreateDate
FROM sys_form f
LEFT JOIN sys_form p ON f.ParentId = p.Id
WHERE f.MethodName = 'UpdateWitsAppl' 
  AND f.ControllerName = 'Contract'
  AND f.Deleted = 0
ORDER BY f.CreateDate DESC;

-- 显示服务变更模块下的所有权限（包括新增的UpdateWitsAppl）
SELECT 
    f.Id,
    f.Name,
    f.Describe,
    f.Title,
    f.ControllerName,
    f.MethodName,
    f.OrderNum,
    f.CreateUser,
    f.CreateDate
FROM sys_form f
WHERE f.ParentId = @serviceChangeId
  AND f.Deleted = 0
ORDER BY f.OrderNum ASC; 