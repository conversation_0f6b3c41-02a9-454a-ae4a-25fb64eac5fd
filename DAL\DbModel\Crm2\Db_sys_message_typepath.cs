﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_message_typepath")]
    public class Db_sys_message_typepath
    {
        /// <summary>
        /// Desc:主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:消息名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Name { get; set; }

        /// <summary>
        /// Desc:消息类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? Type { get; set; }

        /// <summary>
        /// Desc:信息窗体名称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageFormName { get; set; }

        /// <summary>
        /// Desc:信息路由
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageToPath { get; set; }

        /// <summary>
        /// Desc:微信消息路由
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageVXPath { get; set; }

        /// <summary>
        /// Desc:消息模板
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string MessageDetailTemp { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

    }
}
