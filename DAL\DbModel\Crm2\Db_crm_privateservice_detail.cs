﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_privateservice_detail")]
    public class Db_crm_privateservice_detail
    {
        /// <summary>
        /// Desc:个人服务天数详细主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:个人服务天数主键
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string PrivateServiceId { get; set; }

        /// <summary>
        /// Desc:服务ID主键
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ContractServiceInfoGtisId { get; set; }

        /// <summary>
        /// Desc:人员Id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UserId { get; set; }

        /// <summary>
        /// Desc:合同表id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ContractId { get; set; }

        /// <summary>
        /// Desc:使用天数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? UsedDays { get; set; }

        /// <summary>
        /// Desc:付费天数(可以为负数)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? ChargeDays { get; set; }

        /// <summary>
        /// Desc:状态 1 使用中 2 已使用 
        /// Default:0
        /// Nullable:False
        /// </summary>           
        public int PrivateServiceType { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Remark { get; set; }

    }
}
