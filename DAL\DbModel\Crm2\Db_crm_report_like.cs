using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 工作报告点赞表
    /// </summary>
    [SugarTable("crm_report_like")]
    public partial class Db_crm_report_like
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 报告ID
        /// </summary>
        public string ReportId { get; set; }

        /// <summary>
        /// 报告类型：1-日报，2-周报，3-月报
        /// </summary>
        public int ReportType { get; set; }

        /// <summary>
        /// 报告标题（冗余字段，便于展示）
        /// </summary>
        public string ReportTitle { get; set; }

        /// <summary>
        /// 报告作者ID（冗余字段）
        /// </summary>
        public string ReportUserId { get; set; }

        /// <summary>
        /// 报告作者姓名（冗余字段）
        /// </summary>
        public string ReportUserName { get; set; }

        /// <summary>
        /// 报告日期
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// 报告年份
        /// </summary>
        public int ReportYear { get; set; }

        /// <summary>
        /// 报告月份
        /// </summary>
        public int? ReportMonth { get; set; }

        /// <summary>
        /// 报告周数
        /// </summary>
        public int? ReportWeek { get; set; }







        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 点赞用户ID
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateDate { get; set; }
    }
} 