using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// SaleWits资源管理表
    /// </summary>
    [SugarTable("crm_salewits_resource_management")]
    public class Db_crm_salewits_resource_management
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 服务ID（关联crm_contract_serviceinfo_saleswits）
        /// </summary>
        public string ServiceId { get; set; }

        /// <summary>
        /// 合同ID
        /// </summary>
        public string ContractId { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public string CompanyId { get; set; }

        /// <summary>
        /// SaleWits租户ID
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// 首次下发日期
        /// </summary>
        public DateTime? FirstDistributionDate { get; set; }

        /// <summary>
        /// 下次定时下发日期
        /// </summary>
        public DateTime? NextDistributionDate { get; set; }

        /// <summary>
        /// 最后下发日期
        /// </summary>
        public DateTime? LastDistributionDate { get; set; }

        /// <summary>
        /// 当前总账号数
        /// </summary>
        public int TotalAccountCount { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }
    }
} 