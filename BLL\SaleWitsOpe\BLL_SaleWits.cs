using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using CRM2_API.Model.System;
using CRM2_API.Common.AppSetting;
using Newtonsoft.Json;

namespace CRM2_API.BLL
{
    /// <summary>
    /// SaleWits资源下发业务逻辑类
    /// </summary>
    public class BLL_SaleWits
    {
        private readonly string _apiUrl;
        private readonly HttpClient _httpClient;
        private readonly bool _isOnlineEnv;

        /// <summary>
        /// 构造函数，使用配置中的环境设置
        /// </summary>
        public BLL_SaleWits()
        {
            // 从配置中读取环境设置
            _isOnlineEnv = string.Equals(AppSettings.SaleWits.Environment, "Online", StringComparison.OrdinalIgnoreCase);
            _apiUrl = _isOnlineEnv ? AppSettings.SaleWits.ApiUrl_Online : AppSettings.SaleWits.ApiUrl_Test;
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// 构造函数，允许手动指定环境（用于向后兼容）
        /// </summary>
        /// <param name="useOnlineEnv">是否使用线上环境</param>
        public BLL_SaleWits(bool useOnlineEnv)
        {
            _isOnlineEnv = useOnlineEnv;
            _apiUrl = _isOnlineEnv ? AppSettings.SaleWits.ApiUrl_Online : AppSettings.SaleWits.ApiUrl_Test;
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// 调用SaleWits充值接口
        /// </summary>
        /// <param name="uuid">用户UUID</param>
        /// <param name="amount">充值金额</param>
        /// <param name="emailCount">赠送邮件数</param>
        /// <param name="tokenCount">赠送token数量</param>
        /// <param name="opUserName">操作人名称</param>
        /// <returns>接口返回结构</returns>
        public async Task<SaleWitsCreditResult> CreditAsync(string uuid, int amount, int? emailCount, int? tokenCount,string opUserName)
        {
            try{
                var jwtToken = AppSettings.SaleWits.OnlineToken;
                if (string.IsNullOrEmpty(jwtToken))
                {
                    throw new ApiException("SaleWits OnlineToken配置为空");
                }

                // UUID处理逻辑
                var settingUUID = AppSettings.SaleWits.TestUuid;
                if (_isOnlineEnv)
                {
                    //正式环境，uuid不能为空
                    if(uuid.IsNullOrEmpty())
                    {
                        throw new ApiException("SaleWits 正式环境 uuid不能为空");
                    }
                }
                else
                {
                    //测试环境，优先使用配置的测试uuid
                    if(settingUUID.IsNotNullOrEmpty())
                    {
                        uuid = settingUUID;
                    }
                    else if(uuid.IsNullOrEmpty())
                    {
                        throw new ApiException("SaleWits 测试环境 uuid不能为空，且配置中的TestUuid也为空");
                    }
                }
                var payload = new
                {
                    uuid = uuid,
                    amount = amount,
                    emailCount = emailCount > 0 ? emailCount : (int?)null,
                    tokenCount = tokenCount > 0 ? tokenCount : (int?)null,
                    createUser = opUserName
                };
                var json = JsonConvert.SerializeObject(payload);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", jwtToken);
                var response = await _httpClient.PostAsync(_apiUrl, content);
                var respStr = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<SaleWitsCreditResult>(respStr) ?? new SaleWitsCreditResult { code = -1, msg = "接口返回解析异常" };
                return result;
            }
            catch(Exception ex)
            {
                throw new ApiException("调用SaleWits充值接口异常: " + ex.Message);
            }
            
        }


    }

    /// <summary>
    /// SaleWits充值接口返回结构
    /// </summary>
    public class SaleWitsCreditResult
    {
        public string msg { get; set; }
        public int code { get; set; }
        public bool success { get { return code == 200; } }
        /// <summary>
        /// 错误码中文提示
        /// </summary>
        public string errCodeMsg { get { return GetErrorMsg(code); } }
        /// <summary>
        /// 根据code获取中文错误提示
        /// </summary>
        private string GetErrorMsg(int code)
        {
            switch (code)
            {
                case 200: return "正常";
                case 1404: return "uuid对应的租户不存在";
                case 1400: return "参数错误";
                case 1500: return "其它错误";
                case -1: return "接口返回解析异常";
                default: return "未知错误";
            }
        }
    }
} 