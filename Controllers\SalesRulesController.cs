﻿using System.ComponentModel;
using CRM2_API.BLL;
using CRM2_API.Common.Cache;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.ContractOverseasIPRecord;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using static CRM2_API.Model.ControllersViewModel.VM_SalesRules;

namespace CRM2_API.Controllers
{
    [Description("营销规则控制器")]
    public class SalesRulesController : MyControllerBase
    {
        public SalesRulesController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 查询销售规则列表
        /// </summary>
        /// <param name="searchSalesRules_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchSalesRules_Out> SearchSalesRules(SearchSalesRules_In searchSalesRules_In)
        {
            int total = 0;
            var list = DbOpe_sys_salesrules.Instance.SearchSalesRules(searchSalesRules_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 添加销售规则
        /// </summary>
        /// <param name="addsalesrules"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesRulesResult AddSalesRules(AddOrUpdateSalesRules addsalesrules)
        {
            return BLL_SalesRules.Instance.AddSalesRules(addsalesrules);

        }

        /// <summary>
        /// 更新销售规则
        /// </summary>
        /// <param name="updatesalesrules"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesRulesResult UpdateSalesRules(AddOrUpdateSalesRules updatesalesrules)
        {
            return BLL_SalesRules.Instance.UpdateSalesRules(updatesalesrules);

        }

        /// <summary>
        /// 删除销售规则
        /// </summary>
        /// <param name="salesRulesParameter_In"></param>
        /// <returns></returns>
        [HttpPost]
        public SalesRulesResult DeleteSalesRules(SalesRulesParameter_In salesRulesParameter_In)
        {
            return DbOpe_sys_salesrules.Instance.DeleteSalesRules(salesRulesParameter_In.Ids);
        }

        /// <summary>
        /// 根据Id获取详情
        /// </summary>
        /// <param name="salesRulesParameter_In"></param>
        /// <returns></returns>
        [HttpPost]
        public SearchSalesRules_Out GetSearchSalesRulesById(SalesRulesParameter_In salesRulesParameter_In)
        {
            return DbOpe_sys_salesrules.Instance.GetSearchSalesInfoById(salesRulesParameter_In.Ids);
        }

    }
}
