﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using System.IO;

namespace CRM2_API.Controllers
{
    [Description("合同服务管理")]
    public class ContractServiceInfoController : MyControllerBase
    {
        public ContractServiceInfoController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取GTIS临时账号模版
        /// </summary>
        [HttpPost]
        public List<Tscountry> GetTemporaryAccountTemplateByTemplateId()
        {
            return BLL_ContractService.Instance.GetTemporaryAccountTemplateByTemplateId();
        }

        /// <summary>
        /// 获取GTIS临时账号模版国家
        /// </summary>
        [HttpPost]
        public List<AccountCountry_Out> GetAccountCountryByTemplateId()
        {
            return BLL_ContractService.Instance.GetAccountCountryByTemplateId();
        }

        /// <summary>
        /// 根据模版Id创建GTIS临时账号
        /// </summary>
        [HttpPost]
        public void AddContractServiceInfoTemporaryAccountByTemplateId(string TemplateId)
        {
            BLL_ContractService.Instance.AddContractServiceInfoTemporaryAccountByTemplateId(TemplateId);
        }

        /// <summary>
        /// GTIS临时账号创建
        /// </summary>
        [HttpPost]
        public void AddContractServiceInfoTemporaryAccount(ContractServiceInfoTemporaryAccount_IN contractServiceInfoTemporaryAccountIN)
        {
            BLL_ContractService.Instance.AddContractServiceInfoTemporaryAccount(contractServiceInfoTemporaryAccountIN);
        }

        /// <summary>
        /// 删除临时账号信息
        /// </summary>
        [HttpPost]
        public void DeleteContractServiceInfoTemporaryAccount(string id)
        {
            BLL_ContractService.Instance.DeleteContractServiceInfoTemporaryAccount(id);
        }

        /// <summary>
        /// 变更合同服务信息临时账号状态信息
        /// </summary>
        [HttpPost]
        public void UpdateContractServiceInfoTemporaryAccountState(string id, int state)
        {
            BLL_ContractService.Instance.UpdateContractServiceInfoTemporaryAccountState(id, state);
        }

        /// <summary>
        /// 延期合同服务信息临时账号信息
        /// </summary>
        [HttpPost]
        public void DelayContractServiceInfoTemporaryAccount(string id, int duration)
        {
            BLL_ContractService.Instance.DelayContractServiceInfoTemporaryAccount(id, duration);
        }

        /// <summary>
        /// 获取延期合同服务信息临时账号信息
        /// </summary>
        [HttpPost]
        public List<DelayContractServiceInfoTemporaryAccountList_Out> GetDelayContractServiceInfoTemporaryAccountListById(string id)
        {
            return BLL_ContractService.Instance.GetDelayContractServiceInfoTemporaryAccountListById(id);
        }

        /// <summary>
        /// 根据查询条件获取合同服务信息临时账号信息列表
        /// </summary>
        /// <param name="searchContractServiceInfoTemporaryAccountListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchContractServiceInfoTemporaryAccountList_Out> SearchContractServiceInfoTemporaryAccountList(SearchContractServiceInfoTemporaryAccountList_In searchContractServiceInfoTemporaryAccountListIn)
        {
            return BLL_ContractService.Instance.SearchContractServiceInfoTemporaryAccountList(searchContractServiceInfoTemporaryAccountListIn);
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public ContractServiceInfoTemporaryAccount_Out GetContractServiceInfoTemporaryAccountById(string id)
        {
            return BLL_ContractService.Instance.GetContractServiceInfoTemporaryAccountById(id);
        }

        /// <summary>
        /// 根据客户名称获取用户私有池客户信息
        /// </summary>
        /// <param name="customerName"></param>
        /// <returns></returns>
        [HttpPost]
        public List<PrivateCustomerInfo> GetPrivateCustomerInfo(string customerName)
        {
            return BLL_Customer.Instance.GetPrivateCustomerInfo(customerName);
        }

        /// <summary>
        /// 根据临时账号Id获取合同服务信息临时账号用户信息
        /// </summary>
        /// <param name="serviceInfoTemporaryAccountID"></param>
        /// <returns></returns>
        [HttpPost]
        public List<TemporaryAccountUser> GetTemporaryAccountUserByTemporaryAccountID(string serviceInfoTemporaryAccountID)
        {
            return BLL_ContractService.Instance.GetTemporaryAccountUserByTemporaryAccountID(serviceInfoTemporaryAccountID);
        }

        /// <summary>
        /// 获取当天开通演示账号剩余个数
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public int GetContractServiceInfoTemporaryAccountLeftOpenTimes()
        {
            return BLL_ContractService.Instance.GetContractServiceInfoTemporaryAccountLeftOpenTimes();
        }

        /// <summary>
        /// 根据TaskId获取合同服务信息临时账号用户日志信息
        /// </summary>
        /// <param name="serviceInfoTemporaryAccountID"></param>
        /// <returns></returns>
        [HttpPost]
        public List<BM_AllUserOperateLog> GetAllDemoUserOperateLogSta(string serviceInfoTemporaryAccountID)
        {
            return BLL_ContractService.Instance.GetAllDemoUserOperateLogSta(serviceInfoTemporaryAccountID);
        }

        /// <summary>
        /// 变更合同服务信息临时账号用户状态信息
        /// </summary>
        /// <param name="sysUserID"></param>
        /// <param name="deleted"></param>
        /// <returns></returns>
        [HttpPost]
        public void UpdateContractServiceInfoTemporaryAccountUserState(string sysUserID,int deleted)
        {
            BLL_ContractService.Instance.UpdateContractServiceInfoTemporaryAccountUserState(sysUserID, deleted);
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号用户按月统计
        /// </summary>
        /// <param name="sysUserID"></param>
        /// <returns></returns>
        [HttpPost]
        public List<BM_OneUserOperateLogByMonth> GetOneUserOpeLogStaByMonth(string sysUserID)
        {
            return BLL_ContractService.Instance.GetOneUserOpeLogStaByMonth(sysUserID);
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号用户详情
        /// </summary>
        /// <param name="temporaryAccountUserLogDetail"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<BM_UserOpeLogDetail> GetOpeLogDetail(TemporaryAccountUserLogDetail_IN temporaryAccountUserLogDetail)
        {
            return BLL_ContractService.Instance.GetOpeLogDetail(temporaryAccountUserLogDetail);
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号用户导出
        /// </summary>
        /// <param name="sysUserID"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetOpeLogDetailDownload(string sysUserID)
        {
            Stream result = BLL_ContractService.Instance.GetOpeLogDetailDownload(sysUserID);
            return new FileStreamResult(result, "application/octet-stream");
        }

        /// <summary>
        /// 根据Id获取合同服务信息临时账号用户导出次数详情
        /// </summary>
        /// <param name="temporaryAccountUserExportLogDetail"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<BM_UserExportLogDetail> GetOpeExportLogDetail(TemporaryAccountUserExportLogDetail_IN temporaryAccountUserExportLogDetail)
        {
            return BLL_ContractService.Instance.GetOpeExportLogDetail(temporaryAccountUserExportLogDetail);
        }

        /// <summary>
        /// 添加用户临时账户国家模版
        /// </summary>
        /// <param name="addUserTSCountryIn"></param>
        [HttpPost]
        public void AddUserTSCountry(AddUserTSCountry_In addUserTSCountryIn)
        {
            BLL_ContractService.Instance.AddUserTSCountry(addUserTSCountryIn);
        }

        /// <summary>
        /// 修改用户临时账户国家模版
        /// </summary>
        /// <param name="updateUserTSCountryIn"></param>
        [HttpPost]
        public void UpdateUserTSCountry(UpdateUserTSCountry_In updateUserTSCountryIn)
        {
            BLL_ContractService.Instance.UpdateUserTSCountry(updateUserTSCountryIn);
        }

        /// <summary>
        /// 删除用户临时账户国家模版
        /// </summary>
        /// <param name="id"></param>
        [HttpPost]
        public void DeleteUserTSCountryById(string id)
        {
            BLL_ContractService.Instance.DeleteUserTSCountryById(id);
        }

        /// <summary>
        /// 查询用户临时账户国家模版
        /// </summary>
        /// <param name="searchUserTSCountryListIn"></param>
        [HttpPost]
        public ApiTableOut<SearchUserTSCountryList_Out> SearchUserTSCountryList(SearchUserTSCountryList_In searchUserTSCountryListIn)
        {
            return BLL_ContractService.Instance.SearchUserTSCountryList(searchUserTSCountryListIn);
        }

        /// <summary>
        /// 根据id获取用户临时账户国家模版
        /// </summary>
        /// <param name="id"></param>
        [HttpPost]
        public GetUserTSCountryList_Out GetUserTSCountryById(string id)
        {
            return BLL_ContractService.Instance.GetUserTSCountryById(id);
        }

        /// <summary>
        /// 删除对美洲国家客户禁售的国家数据
        /// </summary>
        /// <param name="deleteAmericaCountryData_In"></param>
        [HttpPost]
        public void DeleteAmericaCountryData(DeleteAmericaCountryData_In deleteAmericaCountryData_In)
        {
            BLL_ContractService.Instance.DeleteAmericaCountryData(deleteAmericaCountryData_In.AccountId, deleteAmericaCountryData_In.SysUserIds);
        }
    }
}
