﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using CRM2_API.BLL.WorkLog;
using CRM2_API.Common.JWT;
using CRM2_API.Model.BusinessModel;
using DocumentFormat.OpenXml.Office.CustomUI;
using DocumentFormat.OpenXml.Drawing;
using Newtonsoft.Json;

namespace CRM2_API.BLL
{
    public class BLL_Organization : BaseBLL<BLL_Organization>
    {
        #region 组织架构调整
        public void ExcuteOrgChange()
        {
            var changes = DbOpe_team_operatelog.Instance.GetList();
            var token = new TokenModel { id = Guid.Empty.ToString(), suppled = 1 };
            TokenModel.SetTokenModel(token);
            foreach (var change in changes)
            {
                try
                {
                    DbOpe_team_operatelog.Instance.TransDeal(() =>
                    {
                        switch (change.OperateType)
                        {
                            case "晋升":
                                //组织升级，原组织停用
                                UpdateLevel(change.OldTeam, change.NewTeam, change.BelongTeam, change.ChargeUser, change.EffectiveDate.Value);
                                break;
                            case "新增":
                                AddOrg(change.NewTeam, change.BelongTeam, change.ChargeUser);
                                break;
                            case "人员迁移":
                                UserMove(change.MoveUser, change.OldTeam, change.NewTeam, change.EffectiveDate.Value);
                                break;
                            case "删除队伍":
                                RemoveOrg(change.OldTeam);
                                break;
                            case "修改":
                                UpdateOrgInfo(change.OldTeam, change.NewTeam, change.BelongTeam, change.ChargeUser, change.EffectiveDate.Value);
                                break;
                            case "人员停用":
                                RemoveUser(change.MoveUser);
                                break;
                            default:
                                return;

                        }
                        change.OperateResult = "1";
                        change.OperateDate = DateTime.Now;
                        DbOpe_team_operatelog.Instance.Update(change);
                    });
                }
                catch (Exception ex)
                {
                    change.OperateResult = "0";
                    change.OperateDate = DateTime.Now;
                    DbOpe_team_operatelog.Instance.Update(change);
                    LogUtil.AddErrorLog(JsonConvert.SerializeObject(change));
                    LogUtil.AddErrorLog(ex.Message);
                }
            }
        }

        /// <summary>
        /// 组织升级，原组织停用
        /// </summary>
        /// <param name="originOrgName"></param>
        /// <param name="newOrgName"></param>
        /// <param name="newOrgParentName"></param>
        /// <param name="newOrgManagerName"></param>
        /// <param name="effectDate"></param>
        /// <exception cref="ApiException"></exception>
        public void UpdateLevel(string originOrgName, string newOrgName, string newOrgParentName, string newOrgManagerName, DateTime effectDate)
        {
            if (string.IsNullOrEmpty(originOrgName))
            {
                throw new ApiException("原组织名称不能为空");
            }
            if (string.IsNullOrEmpty(newOrgName))
            {
                throw new ApiException("新组织名称不能为空");
            }
            if (string.IsNullOrEmpty(newOrgParentName))
            {
                throw new ApiException("新上级组织名称不能为空");
            }
            if (string.IsNullOrEmpty(newOrgManagerName))
            {
                throw new ApiException("新负责人名称不能为空");
            }
            //获取原组织信息
            var originOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == originOrgName && o.Deleted == false && o.OrgStatus == true);
            if (originOrg == null)
                throw new ApiException("原组织不存在");
            //获取新组织信息
            var newOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == newOrgName && o.Deleted == false && o.OrgStatus == true);
            if (newOrg != null)
                throw new ApiException("新组织已存在");
            //获取上级组织信息
            var parentOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == newOrgParentName && o.Deleted == false && o.OrgStatus == true);
            if (parentOrg == null)
                throw new ApiException("新上级组织不存在");
            //获取负责人信息
            var managerUser = DbOpe_sys_user.Instance.GetData(o => o.Name == newOrgManagerName && o.Deleted == false && o.UserStatus == true);
            if (managerUser == null)
                throw new ApiException("新负责人不存在");


            var orgType = newOrgName.Contains("大队") ? EnumOrgType.Battalion : (newOrgName.Contains("中队") ? EnumOrgType.Squadron : EnumOrgType.BattleTeam);
            int count = DbOpe_sys_organization.Instance.GetDataAllList().Count();
            var newOrgId = Guid.NewGuid().ToString();
            #region 创建新组织
            newOrg = new Db_sys_organization()
            {
                Id = newOrgId,
                OrgName = newOrgName,
                OrgType = orgType,
                ParentId = parentOrg.Id,
                OrgStatus = true,
                Remark = "组织架构调整",
                OrgNum = (count + 1).ToString().PadLeft(5, '0'),
                Deleted = false
            };
            DbOpe_sys_organization.Instance.Insert(newOrg);
            #endregion
            #region 原组织人员转移到新组织
            var userList = DbOpe_sys_user.Instance.GetDataList(o => o.OrganizationId == originOrg.Id && o.Deleted == false && o.UserStatus == true);
            foreach (var user in userList)
            {
                user.OrganizationId = newOrgId;
                DbOpe_sys_user.Instance.Update(user);
                //客户归属变更
                var parentOrgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                var moveParams = new UserMoveCustomerLogParams()
                {
                    UserId = user.Id,
                    TargetOrgId = string.IsNullOrEmpty(user.OrganizationId) ? Guid.Empty.ToString() : user.OrganizationId,
                    New_OrgDivisionId = parentOrgList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id,
                    New_OrgDivisionName = parentOrgList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName,
                    New_OrgBrigadeId = parentOrgList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id,
                    New_OrgBrigadeName = parentOrgList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName,
                    New_OrgRegimentId = parentOrgList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id,
                    New_OrgRegimentName = parentOrgList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName,
                };
                DbOpe_crm_customer_org_log.Instance.UserMoveCustomerLog(moveParams);
            }
            #endregion
            #region 合同/业绩归属变更
            var originOrgParentList = DbOpe_sys_organization.Instance.GetParentOrgList(originOrg.Id);
            var newOrgParentList = DbOpe_sys_organization.Instance.GetParentOrgList(newOrg.Id);
            var originOrgDivisionId = originOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id;
            var originOrgBrigadeId = originOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id;
            var originOrgRegimentId = originOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id;
            var contractList = DbOpe_crm_contract.Instance.GetDataList(o =>
            (o.OrgDivisionId == originOrgDivisionId || originOrgDivisionId == null)
            && (o.OrgBrigadeId == originOrgBrigadeId || originOrgBrigadeId == null)
            && (o.OrgRegimentId == originOrgRegimentId || originOrgRegimentId == null)
            && o.SigningDate >= effectDate
            && o.Deleted == false);
            foreach (var contract in contractList)
            {
                contract.OrgDivisionId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id;
                contract.OrgDivisionName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName;
                contract.OrgBrigadeId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id;
                contract.OrgBrigadeName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName;
                contract.OrgRegimentId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id;
                contract.OrgRegimentName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName;
                DbOpe_crm_contract.Instance.Update(contract);
            }
            var performanceList = DbOpe_crm_contract_receiptregister.Instance.GetDataList(o =>
            (o.OrgDivisionId == originOrgDivisionId || originOrgDivisionId == null)
            && (o.OrgBrigadeId == originOrgBrigadeId || originOrgBrigadeId == null)
            && (o.OrgRegimentId == originOrgRegimentId || originOrgRegimentId == null)
            && o.BelongingMonth >= effectDate
            && o.BelongingMonth != null
            && o.Deleted == false);
            foreach (var performance in performanceList)
            {
                performance.OrgDivisionId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id;
                performance.OrgDivisionName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName;
                performance.OrgBrigadeId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id;
                performance.OrgBrigadeName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName;
                performance.OrgRegimentId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id;
                performance.OrgRegimentName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName;
                DbOpe_crm_contract_receiptregister.Instance.Update(performance);
            }
            #endregion
            #region 原组织停用
            var idList = new List<string>() { originOrg.Id };
            if (DbOpe_sys_organization.Instance.HasStoppedOrg(idList))
                throw new ApiException("所选组织存在已停用的组织，无法停用");
            //传入orgs中存在启用状态的下级组织，无法停用
            if (DbOpe_sys_organization.Instance.HasEnableSubOrg(idList))
                throw new ApiException("所选组织存在启用的下级组织，无法停用");
            //传入orgs中存在已经启用的员工，无法停用
            if (DbOpe_sys_user.Instance.HasEnableUserInOrg(idList))
                throw new ApiException("组织内存在启用的员工,无法停用");
            //停用组织
            originOrg.OrgStatus = false;
            DbOpe_sys_organization.Instance.Update(originOrg);
            #endregion
            #region 新组织管理人设置
            DbOpe_sys_organization_params.Instance.ExcuteChangeOrgManager(new ChangeOrgManagerParams { OrgId = newOrg.Id, OrgType = orgType.ToInt(), NewManagerUserId = managerUser.Id, NewManagerUserName = managerUser.Name });
            #endregion
        }
        public void AddOrg(string newOrgName, string newOrgParentName, string newOrgManagerName)
        {
            if (string.IsNullOrEmpty(newOrgName))
            {
                throw new ApiException("新组织名称不能为空");
            }
            if (string.IsNullOrEmpty(newOrgParentName))
            {
                throw new ApiException("新上级组织名称不能为空");
            }
            if (string.IsNullOrEmpty(newOrgManagerName))
            {
                throw new ApiException("新负责人名称不能为空");
            }
            //获取新组织信息
            var newOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == newOrgName && o.Deleted == false && o.OrgStatus == true);
            if (newOrg != null)
                throw new ApiException("新组织已存在");
            //获取上级组织信息
            var parentOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == newOrgParentName && o.Deleted == false && o.OrgStatus == true);
            if (parentOrg == null)
                throw new ApiException("新上级组织不存在");
            //获取负责人信息
            var managerUser = DbOpe_sys_user.Instance.GetData(o => o.Name == newOrgManagerName && o.Deleted == false && o.UserStatus == true);
            if (managerUser == null)
                throw new ApiException("新负责人不存在");

            var orgType = newOrgName.Contains("大队") ? EnumOrgType.Battalion : (newOrgName.Contains("中队") ? EnumOrgType.Squadron : EnumOrgType.BattleTeam);
            int count = DbOpe_sys_organization.Instance.GetDataAllList().Count();
            var newOrgId = Guid.NewGuid().ToString();
            #region 创建新组织
            newOrg = new Db_sys_organization()
            {
                Id = newOrgId,
                OrgName = newOrgName,
                OrgType = orgType,
                ParentId = parentOrg.Id,
                OrgStatus = true,
                Remark = "组织架构调整",
                OrgNum = (count + 1).ToString().PadLeft(5, '0'),
                Deleted = false
            };
            DbOpe_sys_organization.Instance.Insert(newOrg);
            #endregion
            #region 新组织管理人设置
            DbOpe_sys_organization_params.Instance.ExcuteChangeOrgManager(new ChangeOrgManagerParams { OrgId = newOrg.Id, OrgType = orgType.ToInt(), NewManagerUserId = managerUser.Id, NewManagerUserName = managerUser.Name });
            #endregion
        }
        public void UserMove(string userName, string originOrgName, string newOrgName, DateTime effectDate)
        {

            if (string.IsNullOrEmpty(originOrgName))
            {
                throw new ApiException("原组织名称不能为空");
            }
            if (string.IsNullOrEmpty(newOrgName))
            {
                throw new ApiException("新组织名称不能为空");
            }
            if (string.IsNullOrEmpty(userName))
            {
                throw new ApiException("名称不能为空");
            }
            //获取原组织信息
            var originOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == originOrgName && o.Deleted == false && o.OrgStatus == true);
            if (originOrg == null)
                throw new ApiException("原组织不存在");
            //获取新组织信息
            var newOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == newOrgName && o.Deleted == false && o.OrgStatus == true);
            if (newOrg == null)
                throw new ApiException("新组织不存在");
            //获取人信息
            var moveUser = DbOpe_sys_user.Instance.GetData(o => o.Name == userName && o.Deleted == false && o.UserStatus == true);
            if (moveUser == null)
                throw new ApiException("人不存在");


            #region 人员转移到新组织

            moveUser.OrganizationId = newOrg.Id;
            DbOpe_sys_user.Instance.Update(moveUser);
            //客户归属变更
            var parentOrgList = DbOpe_sys_organization.Instance.GetParentOrgList(moveUser.OrganizationId);
            var moveParams = new UserMoveCustomerLogParams()
            {
                UserId = moveUser.Id,
                TargetOrgId = string.IsNullOrEmpty(moveUser.OrganizationId) ? Guid.Empty.ToString() : moveUser.OrganizationId,
                New_OrgDivisionId = parentOrgList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id,
                New_OrgDivisionName = parentOrgList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName,
                New_OrgBrigadeId = parentOrgList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id,
                New_OrgBrigadeName = parentOrgList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName,
                New_OrgRegimentId = parentOrgList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id,
                New_OrgRegimentName = parentOrgList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName,
            };
            DbOpe_crm_customer_org_log.Instance.UserMoveCustomerLog(moveParams);

            #endregion
            #region 合同/业绩归属变更
            var originOrgParentList = DbOpe_sys_organization.Instance.GetParentOrgList(originOrg.Id);
            var newOrgParentList = DbOpe_sys_organization.Instance.GetParentOrgList(newOrg.Id);
            var originOrgDivisionId = originOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id;
            var originOrgBrigadeId = originOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id;
            var originOrgRegimentId = originOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id;
            var contractList = DbOpe_crm_contract.Instance.GetDataList(o =>
            (o.OrgDivisionId == originOrgDivisionId || originOrgDivisionId == null)
            && (o.OrgBrigadeId == originOrgBrigadeId || originOrgBrigadeId == null)
            && (o.OrgRegimentId == originOrgRegimentId || originOrgRegimentId == null)
            && o.Issuer == moveUser.Id
            && o.SigningDate >= effectDate
            && o.Deleted == false);
            foreach (var contract in contractList)
            {
                contract.OrgDivisionId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id;
                contract.OrgDivisionName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName;
                contract.OrgBrigadeId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id;
                contract.OrgBrigadeName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName;
                contract.OrgRegimentId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id;
                contract.OrgRegimentName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName;
                DbOpe_crm_contract.Instance.Update(contract);
            }
            var performanceList = DbOpe_crm_contract_receiptregister.Instance.GetDataList(o =>
            (o.OrgDivisionId == originOrgDivisionId || originOrgDivisionId == null)
            && (o.OrgBrigadeId == originOrgBrigadeId || originOrgBrigadeId == null)
            && (o.OrgRegimentId == originOrgRegimentId || originOrgRegimentId == null)
            && o.BelongingMonth >= effectDate
            && o.BelongingMonth != null
            && o.Issuer == moveUser.Id
            && o.Deleted == false);
            foreach (var performance in performanceList)
            {
                performance.OrgDivisionId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id;
                performance.OrgDivisionName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName;
                performance.OrgBrigadeId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id;
                performance.OrgBrigadeName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName;
                performance.OrgRegimentId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id;
                performance.OrgRegimentName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName;
                DbOpe_crm_contract_receiptregister.Instance.Update(performance);
            }
            #endregion
        }
        public void RemoveOrg(string originOrgName)
        {
            if (string.IsNullOrEmpty(originOrgName))
            {
                throw new ApiException("原组织名称不能为空");
            }

            //获取原组织信息
            var originOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == originOrgName && o.Deleted == false && o.OrgStatus == true);
            if (originOrg == null)
                throw new ApiException("原组织不存在");



            #region 原组织停用
            var idList = new List<string>() { originOrg.Id };
            if (DbOpe_sys_organization.Instance.HasStoppedOrg(idList))
                throw new ApiException("所选组织存在已停用的组织，无法停用");
            //传入orgs中存在启用状态的下级组织，无法停用
            if (DbOpe_sys_organization.Instance.HasEnableSubOrg(idList))
                throw new ApiException("所选组织存在启用的下级组织，无法停用");
            //传入orgs中存在已经启用的员工，无法停用
            if (DbOpe_sys_user.Instance.HasEnableUserInOrg(idList))
                throw new ApiException("组织内存在启用的员工,无法停用");
            //停用组织
            originOrg.OrgStatus = false;
            DbOpe_sys_organization.Instance.Update(originOrg);
            #endregion

        }
        /// <summary>
        /// 组织修改（负责人、名称、上级， 本身组织级别不变）
        /// </summary>
        /// <param name="originOrgName"></param>
        /// <param name="newOrgName"></param>
        /// <param name="newOrgParentName"></param>
        /// <param name="newOrgManagerName"></param>
        /// <param name="effectDate"></param>
        /// <exception cref="ApiException"></exception>
        public void UpdateOrgInfo(string originOrgName, string newOrgName, string newOrgParentName, string newOrgManagerName, DateTime effectDate)
        {
            if (string.IsNullOrEmpty(originOrgName))
            {
                throw new ApiException("原组织名称不能为空");
            }
            if (string.IsNullOrEmpty(newOrgName))
            {
                throw new ApiException("新组织名称不能为空");
            }
            if (string.IsNullOrEmpty(newOrgManagerName))
            {
                throw new ApiException("新负责人名称不能为空");
            }
            if (string.IsNullOrEmpty(newOrgParentName))
            {
                throw new ApiException("新上级组织名称不能为空");
            }
            //获取原组织信息
            var originOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == originOrgName && o.Deleted == false && o.OrgStatus == true);
            if (originOrg == null)
                throw new ApiException("原组织不存在");
            //获取新组织信息
            var newOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == newOrgName && o.Deleted == false && o.OrgStatus == true);
            if (newOrg != null)
                throw new ApiException("新组织已存在");
            //获取上级组织信息
            var parentOrg = DbOpe_sys_organization.Instance.GetData(o => o.OrgName == newOrgParentName && o.Deleted == false && o.OrgStatus == true);
            if (parentOrg == null)
                throw new ApiException("新上级组织不存在");
            //获取负责人信息
            var managerUser = DbOpe_sys_user.Instance.GetData(o => o.Name == newOrgManagerName && o.Deleted == false && o.UserStatus == true);
            if (managerUser == null)
                throw new ApiException("新负责人不存在");


            var orgType = newOrgName.Contains("大队") ? EnumOrgType.Battalion : (newOrgName.Contains("中队") ? EnumOrgType.Squadron : EnumOrgType.BattleTeam);
            #region 在变更前进行原组织信息获取
            var originOrgParentList = DbOpe_sys_organization.Instance.GetParentOrgList(originOrg.Id);
            var originOrgDivisionId = originOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id;
            var originOrgBrigadeId = originOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id;
            var originOrgRegimentId = originOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id;
            #endregion
            #region 组织信息修改
            originOrg.OrgName = newOrgName;
            originOrg.ParentId = parentOrg.Id;
            DbOpe_sys_organization.Instance.Update(originOrg);
            #endregion
            #region 客户所属组织结构更新
            var userList = DbOpe_sys_user.Instance.GetDataList(o => o.OrganizationId == originOrg.Id && o.Deleted == false && o.UserStatus == true);
            foreach (var user in userList)
            {
                //客户归属变更
                var parentOrgList = DbOpe_sys_organization.Instance.GetParentOrgList(user.OrganizationId);
                var moveParams = new UserMoveCustomerLogParams()
                {
                    UserId = user.Id,
                    TargetOrgId = string.IsNullOrEmpty(user.OrganizationId) ? Guid.Empty.ToString() : user.OrganizationId,
                    New_OrgDivisionId = parentOrgList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id,
                    New_OrgDivisionName = parentOrgList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName,
                    New_OrgBrigadeId = parentOrgList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id,
                    New_OrgBrigadeName = parentOrgList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName,
                    New_OrgRegimentId = parentOrgList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id,
                    New_OrgRegimentName = parentOrgList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName,
                };
                DbOpe_crm_customer_org_log.Instance.UserMoveCustomerLog(moveParams);
            }
            #endregion
            #region 合同/业绩归属信息更新
            var newOrgParentList = DbOpe_sys_organization.Instance.GetParentOrgList(originOrg.Id);
            var contractList = DbOpe_crm_contract.Instance.GetDataList(o =>
            (o.OrgDivisionId == originOrgDivisionId || originOrgDivisionId == null)
            && (o.OrgBrigadeId == originOrgBrigadeId || originOrgBrigadeId == null)
            && (o.OrgRegimentId == originOrgRegimentId || originOrgRegimentId == null)
            && o.SigningDate >= effectDate
            && o.Deleted == false);
            foreach (var contract in contractList)
            {
                contract.OrgDivisionId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id;
                contract.OrgDivisionName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName;
                contract.OrgBrigadeId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id;
                contract.OrgBrigadeName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName;
                contract.OrgRegimentId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id;
                contract.OrgRegimentName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName;
                DbOpe_crm_contract.Instance.Update(contract);
            }
            var performanceList = DbOpe_crm_contract_receiptregister.Instance.GetDataList(o =>
            (o.OrgDivisionId == originOrgDivisionId || originOrgDivisionId == null)
            && (o.OrgBrigadeId == originOrgBrigadeId || originOrgBrigadeId == null)
            && (o.OrgRegimentId == originOrgRegimentId || originOrgRegimentId == null)
            && o.BelongingMonth >= effectDate
            && o.BelongingMonth != null
            && o.Deleted == false);
            foreach (var performance in performanceList)
            {
                performance.OrgDivisionId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.Id;
                performance.OrgDivisionName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.BattleTeam).FirstOrDefault()?.OrgName;
                performance.OrgBrigadeId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.Id;
                performance.OrgBrigadeName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Battalion).FirstOrDefault()?.OrgName;
                performance.OrgRegimentId = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.Id;
                performance.OrgRegimentName = newOrgParentList.Where(e => e.OrgType == EnumOrgType.Squadron).FirstOrDefault()?.OrgName;
                DbOpe_crm_contract_receiptregister.Instance.Update(performance);
            }
            #endregion
            #region 组织管理人更新
            DbOpe_sys_organization_params.Instance.ExcuteChangeOrgManager(new ChangeOrgManagerParams { OrgId = originOrg.Id, OrgType = orgType.ToInt(), NewManagerUserId = managerUser.Id, NewManagerUserName = managerUser.Name });
            #endregion

        }
        public void RemoveUser(string userName)
        {
            if (string.IsNullOrEmpty(userName))
            {
                throw new ApiException("名称不能为空");
            }
            //获取人信息
            var removeUser = DbOpe_sys_user.Instance.GetData(o => o.Name == userName && o.Deleted == false && o.UserStatus == true);
            if (removeUser == null)
                throw new ApiException("人不存在");
            var removeUserOrg = DbOpe_sys_organization.Instance.GetData(o => o.Id == removeUser.OrganizationId);
            if (removeUser == null)
                throw new ApiException("人的组织不存在");
            #region 人员停用
            removeUser.UserStatus = false;
            removeUser.ResignationTime = DateTime.Now;
            DbOpe_sys_user.Instance.Update(removeUser);
            if (removeUser.UserType == (int)EnumUserType.Manager)
            {
                DbOpe_sys_organization_params.Instance.ChangeOrgManager(new ChangeOrgManagerParams { OrgId = removeUser.OrganizationId, OrgType = removeUserOrg.OrgType.ToInt(), NewManagerUserId = "", NewManagerUserName = "" });
            }
            #endregion
            
        }
        #endregion


        /// <summary>
        /// 添加组织信息
        /// 组织状态默认为启用(OrgStatus=true);自动生成组织编号(生成规则:以5位数字堆积顺序显示,从00001开始生成)
        /// </summary>
        /// <param name="addOrg_In"></param>
        public void AddOrganization(AddOrganization_In addOrg_In)
        {
            //将addOrg_In转化为sys_organization类型
            Db_sys_organization sysOrg = addOrg_In.MappingTo<Db_sys_organization>();
            /* 大队、中队可以没有上级组织 2023.12.22修改
            if ((sysOrg.OrgType == EnumOrgType.Battalion || sysOrg.OrgType == EnumOrgType.Squadron) && string.IsNullOrEmpty(sysOrg.ParentId))
                throw new ApiException("未选择上级组织");*/
            //验证组织名称是否重复
            if (DbOpe_sys_organization.Instance.CheckOrgName(sysOrg.OrgName))
                throw new ApiException("组织名称已存在");
            //获取当前存在的全部组织数，包含已删除的组织
            int count = DbOpe_sys_organization.Instance.GetQueryCount(sysOrg);
            //补充sysOrg属性
            sysOrg.Id = Guid.NewGuid().ToString();
            sysOrg.OrgNum = (count + 1).ToString().PadLeft(5, '0');
            sysOrg.Deleted = false;
            sysOrg.CreateUser = UserTokenInfo.id;
            sysOrg.CreateDate = DateTime.Now;
            //若创建最高级别组织，没有parentId,将parentId的值填为0
            if (string.IsNullOrEmpty(sysOrg.ParentId))
                sysOrg.ParentId = Guid.Empty.ToString();
            //执行添加操作,提交sql到队列中
            DbOpe_sys_organization.Instance.InsertQueue(sysOrg);
            List<string> subIdList = new List<string>();
            //关联下级部门的parentId,提交sql到队列中,并记录被关联的下级组织的日志
            if (!string.IsNullOrEmpty(addOrg_In.SubOrgIds))
            {
                subIdList = addOrg_In.SubOrgIds.Split(",").ToList();
                //关联下级组织
                DbOpe_sys_organization.Instance.BatchMergeOrg(sysOrg.Id, subIdList);
            }
            //记录组织添加相关日志
            WorkLog_Organization.Instance.AddOrgLog(sysOrg, subIdList);
            //执行队列中的sql
            DbOpe_sys_organization.Instance.SaveQueues();
        }

        /// <summary>
        /// 修改组织信息
        /// </summary>
        /// <param name="updOrg_In"></param>
        public void UpdateOrganization(UpdateOrganization_In updOrg_In)
        {
            //判断当前组织是否为停用状态，若启用状态则不允许修改
            if (DbOpe_sys_organization.Instance.IsOrgStarted(updOrg_In.Id))
                throw new ApiException("当前组织为启用状态，无法修改");
            //获取当前的sysOrg信息
            var sysOrg = DbOpe_sys_organization.Instance.GetOrgById(updOrg_In.Id);
            //验证组织名称是否重复
            if (DbOpe_sys_organization.Instance.CheckOrgName(updOrg_In.OrgName, sysOrg.Id))
                throw new ApiException("组织名称已存在");
            //克隆当前sysOrg对象，记录日志使用
            var hisOrg = sysOrg.CloneNewtonJson();
            //将传入参数转为sys_organization对象
            updOrg_In.MappingTo(sysOrg);
            /* 映射部分属性的写法，留存参考
               sys_organization sysUpdOrg = updOrg_In.MappingTo<sys_organization>();
               sysUpdOrg.MappingTo(sysOrg, setter => { setter.Settings.IgnoreNullValues = true; setter.Ignore(dest => dest.CreateDate); });
            */
            //补充sysOrg其他属性
            if (string.IsNullOrEmpty(sysOrg.ParentId))
                sysOrg.ParentId = Guid.Empty.ToString();//若没有ParentId,赋值为0
            sysOrg.UpdateUser = UserTokenInfo.id;
            sysOrg.UpdateDate = DateTime.Now;
            //执行更新操作，提交sql到队列中
            DbOpe_sys_organization.Instance.UpdateQueue(sysOrg);
            //处理关联关系，删除现有的关联关系，关联新的关联关系
            ////获取当前关联的下级组织的Id集合，更新日志用
            var hisSubOrgIds = DbOpe_sys_organization.Instance.GetSubOrgById(sysOrg.Id).Select(e => e.Id).ToList();
            ////删除现有的下级组织关联（清空下级组织的parentId），提交sql到队列中
            //DbOpe_sys_organization.Instance.DeleteChildrenOrg(sysOrg.Id, sysOrg.UpdateUser);
            ////判断更新的组织信息是否含有下级组织
            /*if (!string.IsNullOrEmpty(updOrg_In.SubOrgIds))
                //关联下级组织的parentId,提交sql到队列中
                DbOpe_sys_organization.Instance.BatchMergeOrg(sysOrg.Id, updOrg_In.SubOrgIds.Split(",").ToList());*/
            //记录组织更新相关日志
            WorkLog_Organization.Instance.UpdateOrgLog(updOrg_In, hisOrg, hisSubOrgIds);
            //执行队列中的sql
            DbOpe_sys_organization.Instance.SaveQueues();
        }

        /// <summary>
        /// 删除组织信息
        /// 删除前检测当前组织下是否存在启用的员工，若存在则无法删除，多条记录全部执行成功则返回成功，否则返回失败。只做逻辑删除，修改Deleted字段为1
        /// </summary>
        /// <param name="Ids"></param>
        public void DeleteOrganization(string Ids)
        {
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("未选择待删除的组织");
            //将ids转化为List
            List<string> idList = Ids.Split(",").ToList();
            //验证Ids对应的组织是否全部停用
            if (DbOpe_sys_organization.Instance.HasEnableOrg(idList))
                throw new ApiException("所选择组织存在启用中的组织，无法删除");
            #region 修改停用规则后，停用组织后清空所有下级的组织和员工，删除时不再需要这两步验证验证
            /* 
            //验证当前orgnization中是否存在已经启用的员工
            if (DbOpe_sys_user.Instance.HasEnableUser(idList))
                throw new ApiException("所选择组织内存在启用的员工,无法删除");
            //验证有没有下级组织
            if (DbOpe_sys_organization.Instance.HasSubOrgs(idList))
                throw new ApiException("所选择组织存在未删除的下级组织，无法删除");
            */
            #endregion
            //执行删除，同时删除parentId
            DbOpe_sys_organization.Instance.DeleteOrgs(idList, UserTokenInfo.id);
            //批量记录操作日志
            WorkLog_Organization.Instance.DeleteOrgsLog(idList);
            //执行队列中的sql
            DbOpe_sys_organization.Instance.SaveQueues();
        }

        /// <summary>
        /// 修改组织状态信息
        /// 变更组织状态为启用、停用，停用前检测当前组织下是否存在启用的员工，若存在则无法停用，多条记录全部执行成功则返回成功，否则返回失败。
        /// </summary>
        /// <param name="updOrgStatusIn"></param>
        public void UpdateOrgStatus(UpdateOrganizationStatus_In updOrgStatusIn)
        {
            //将ids转化为List
            List<string> idList = updOrgStatusIn.Ids.Split(",").ToList();
            if (updOrgStatusIn.status)
            {//停用变启用
                //传入ids中存在启用状态组织，无法启用
                if (DbOpe_sys_organization.Instance.HasEnableOrg(idList))
                    throw new ApiException("所选组织存在已启用的组织，无法启用");
                //执行更新
                DbOpe_sys_organization.Instance.UpdateOrgStatus(idList, updOrgStatusIn.status, UserTokenInfo.id);
                //记录日志
                WorkLog_Organization.Instance.StartOrgsLog(idList);
            }
            else
            {//启用变停用
                //传入orgs中存在停用状态组织，无法停用
                if (DbOpe_sys_organization.Instance.HasStoppedOrg(idList))
                    throw new ApiException("所选组织存在已停用的组织，无法停用");
                //传入orgs中存在启用状态的下级组织，无法停用
                if (DbOpe_sys_organization.Instance.HasEnableSubOrg(idList))
                    throw new ApiException("所选组织存在启用的下级组织，无法停用");
                //传入orgs中存在已经启用的员工，无法停用
                if (DbOpe_sys_user.Instance.HasEnableUserInOrg(idList))
                    throw new ApiException("组织内存在启用的员工,无法停用");
                //停用组织
                DbOpe_sys_organization.Instance.UpdateOrgStatus(idList, updOrgStatusIn.status, UserTokenInfo.id);
                //解除下级组织的关联关系
                DbOpe_sys_organization.Instance.BatchUnlinkSubOrg(idList, UserTokenInfo.id);
                //解除与组织内员工的关联关系
                DbOpe_sys_user.Instance.UnLinkOrgAndUser(idList, UserTokenInfo.id);
                //记录日志
                WorkLog_Organization.Instance.StopOrgsLog(idList);
            }
            //执行队列中的sql
            DbOpe_sys_organization.Instance.SaveQueues();
        }

        /// <summary>
        /// 检查组织下是否存在启用状态的员工信息，如果存在则返回该部门及员工信息，否则返回空数组。
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        public List<CheckOrgEnableUser_Out> CheckOrganizationEnableUser(string Ids)
        {
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("未选择待删除的组织");
            //将ids转化为List
            List<string> idList = Ids.Split(",").ToList();
            //执行查询，没有数据返回空数组
            return DbOpe_sys_user.Instance.CheckEnableUser(idList);
        }

        /// <summary>
        /// 合并组织
        /// </summary>
        /// <param name="id">当前组织Id</param>
        /// <param name="mergeId">被合并组织Id</param>
        /// <returns></returns>
        public void MergeOrganization(string id, string mergeId)
        {
            /*
             1.判断当前组织(id)和合并组织(mergId)的组织类型
             2.if(id级别>mergeId级别)：直接修改mergeId数据的parentId
             3.else if(id级别=mergeId级别)：1)将mergeID的所有下级组织的parentId改为id;2)将mergeID的所有直属人员的orgId改为id;
             */
            //日志详情
            string content = string.Empty;
            //获取当前组织的组织信息
            var curOrg = DbOpe_sys_organization.Instance.GetOrgById(id);
            //获取合并组织的组织信息
            var mergeOrg = DbOpe_sys_organization.Instance.GetOrgById(mergeId);
            if (curOrg.OrgType > mergeOrg.OrgType)
            {//小级别合并大级别，违反合并规则
                throw new ApiException("当前组织级别低于目标组织级别,无法合并");
            }
            else if (curOrg.OrgType < mergeOrg.OrgType)
            {//大级别合并小级别
                //将小级别的ParentId修改为id
                DbOpe_sys_organization.Instance.MergeOrg(id, mergeId);
                //记录操作日志
                WorkLog_Organization.Instance.MergeOrgLog(curOrg, mergeOrg);
            }
            else if (curOrg.OrgType == mergeOrg.OrgType)
            {//相同级别组织合并
                //查询mergeId的所有下级组织,id + name
                var mergeSubOrgList = DbOpe_sys_organization.Instance.GetSubOrgById(mergeId);
                //若存在下级组织,执行合并
                if (mergeSubOrgList != null)
                {
                    foreach (var mergeSubOrg in mergeSubOrgList)
                    {
                        //将mergeOrg的下级组织并入到当前组织中
                        DbOpe_sys_organization.Instance.MergeOrg(id, mergeSubOrg.Id);
                    }
                }
                //获取被合并组织的直属人员
                var mergeUserList = DbOpe_sys_user.Instance.GetOrganizationUserById(mergeId);
                //将被合并组织下的直属人员关系转移到接收组织名下
                DbOpe_sys_user.Instance.MergeChildrenOrgUser(id, mergeUserList.Select(e => e.Id).ToList());
                //删除被合并组织(软删除)
                DbOpe_sys_organization.Instance.DeleteOrg(mergeId, UserTokenInfo.id);
                //记录操作日志
                WorkLog_Organization.Instance.MergeSameLevelOrgLog(curOrg, mergeOrg, mergeSubOrgList, mergeUserList);
            }
            //执行队列中的sql
            DbOpe_sys_organization.Instance.SaveQueues();
        }

        /// <summary>
        /// 组织插入
        /// </summary>
        /// <param name="id"></param>
        /// <param name="parentId"></param>
        /// <exception cref="ApiException"></exception>
        public void InsertOrganization(string id, string parentId)
        {
            //获取当前组织的组织类型
            var curOrgType = DbOpe_sys_organization.Instance.GetOrgById(id).OrgType;
            //获取插入组织的组织类型
            var parentOrgType = DbOpe_sys_organization.Instance.GetOrgById(parentId).OrgType;
            if (curOrgType <= parentOrgType)
            {//大级别插入小级别，违反合并规则
                throw new ApiException("当前组织级别高于目标组织级别,无法插入");
            }
            //执行插入操作，使用合并方法，调换两个id的位置
            DbOpe_sys_organization.Instance.MergeOrg(parentId, id);
            DbOpe_sys_organization.Instance.SaveQueues();
        }

        /// <summary>
        /// 根据团队类型获取组织信息列表,只获取没有关联父级的组织
        /// </summary>
        /// <param name="getOrgByType_In"></param>
        /// <returns></returns>
        public List<GetOrganizationByOrgType_Out> GetOrganizationByOrgType(GetOrganizationByOrgType_In getOrgByType_In)
        {
            /* 新建组织时，去掉下级组织的勾选项，故注释获取下级组织列表的代码 2023.11.10
             * var subOrgTypeList = Enum.GetValues(typeof(EnumOrgType)).Cast<EnumOrgType>().Where(e => e.ToInt() > getOrgByType_In.orgType).ToList();
             var subOrgList = DbOpe_sys_organization.Instance.GetOrganizationByOrgType(subOrgTypeList, true);*/
            var upOrgTypeList = Enum.GetValues(typeof(EnumOrgType)).Cast<EnumOrgType>().Where(e => e.ToInt() < getOrgByType_In.orgType).ToList();
            var upOrgList = DbOpe_sys_organization.Instance.GetOrganizationByOrgType(upOrgTypeList, false);
            //subOrgList.AddRange(upOrgList);
            return upOrgList;
        }

        /// <summary>
        /// 获取系统中的组织机构[携带用户]树
        /// 员工信息中包含剩余可保存最大客户数信息
        /// </summary>
        /// <returns></returns>
        public List<GetOrganizationWithUserTree_Out> GetOrganizationWithUserTree(bool useUserStatus = false, bool allowAllUser = false)
        {
            //获取获取系统中的组织机构[携带用户]基础树
            var baseData = DbOpe_sys_organization.Instance.GetBaseOrgWithUserTree(useUserStatus, allowAllUser);
            //创建返回列表对象
            List<GetOrganizationWithUserTree_Out> outData = new();
            if (baseData is not null and { Count: > 0 })
            {
                //获取用户最大可保存客户数的字典
                Dictionary<string, GetMaxSavePrivateCustomerNumLeft_OUT> userNumLeftDict =
                    DbOpe_crm_customer_privatepool.Instance.GetMaxSavePrivateCustomerNumLeft();
                foreach (GetBaseOrgWithUserTree item in baseData)
                {
                    var temp = new GetOrganizationWithUserTree_Out()
                    {
                        Id = item.Id,
                        Name = item.OrgName,
                        Leaf = false,
                        Child = ConvertTreeOutModel(item.OrgChild, item.UserChild, userNumLeftDict),
                        //ConvertTreeOutModelWithLeftMaxSavePrivateCustomerNum
                    };
                    outData.Add(temp);
                }
            }
            return outData;
        }



        /// <summary>
        /// 获取组织机构的全称
        /// </summary>
        /// <param name="organizationTree"></param>
        /// <param name="separator"></param>
        /// <returns></returns>
        [Obsolete]
        public string GetOrganizationAllName(SysOrganizationTree organizationTree, string separator = "/")
        {
            if (organizationTree == null)
            {
                return string.Empty;
            }

            if (organizationTree.Parent == null)
            {
                return organizationTree.OrgName;
            }

            return $"{GetOrganizationAllName(organizationTree.Parent)}{separator}{organizationTree.OrgName}";
        }

        /// <summary>
        /// 获取传入组织Id的下级组织树结构
        /// </summary>
        /// <returns></returns>
        public List<SysOrganizationTree> GetSelfAndChildOrgList()
        {
            return DbOpe_sys_organization.Instance.GetSelfAndChildOrgList(UserId);
        }

        /// <summary>
        /// 获取录人的当前组织及下级组织[携带用户]树
        /// </summary>
        /// <returns></returns>
        public List<GetOrganizationWithUserTree_Out> GetSelfAndChildOrgWithUserTree()
        {
            var baseData = DbOpe_sys_organization.Instance.GetSelfAndChildOrgWithUserTree(UserId);
            List<GetOrganizationWithUserTree_Out> outData = new();
            if (baseData is not null and { Count: > 0 })
            {
                foreach (GetBaseOrgWithUserTree item in baseData)
                {
                    var temp = new GetOrganizationWithUserTree_Out()
                    {
                        Id = item.Id,
                        Name = item.OrgName,
                        Leaf = false,
                        Child = ConvertTreeOutModel(item.OrgChild, item.UserChild),//不获取剩余可保留客户数，userNumLeftDict传null
                        //Child = ConvertTreeOutModel4SelfAndSub(null, item.OrgChild, item.UserChild),
                    };
                    outData.Add(temp);
                }
            }
            return outData;
        }

        /// <summary>
        /// 获取录人的当前组织[携带用户]及下级组织[仅携带队长]树
        /// </summary>
        /// <returns></returns>
        public List<GetOrganizationWithUserTree_Out> GetSelfOrgWithUserAndChildOrgWithManagerTree()
        {
            var baseData = DbOpe_sys_organization.Instance.GetSelfOrgWithUserAndChildOrgWithManagerTree(UserId);
            List<GetOrganizationWithUserTree_Out> outData = new();
            if (baseData is not null and { Count: > 0 })
            {
                foreach (GetBaseOrgWithUserTree item in baseData)
                {
                    var temp = new GetOrganizationWithUserTree_Out()
                    {
                        Id = item.Id,
                        Name = item.OrgName,
                        Leaf = false,
                        Child = ConvertTreeOutModel(item.OrgChild, item.UserChild),//不获取剩余可保留客户数，userNumLeftDict传null
                        //Child = ConvertTreeOutModel4SelfAndSub(null, item.OrgChild, item.UserChild),
                    };
                    outData.Add(temp);
                }
            }
            return outData;
        }

        /// <summary>
        /// 员工管理页面用 获取系统中的组织机构[携带用户(含组织名称及可保留客户数)]树
        /// </summary>
        /// <returns></returns>
        public List<GetOrganizationWithUserTree_Out> GetOrgTreeWithUserMaxSaveCustomer()
        {
            //获取携带用户的基础组织结构树
            var baseData = DbOpe_sys_organization.Instance.GetBaseOrgWithUserTree();
            List<GetOrganizationWithUserTree_Out> outData = new();
            if (baseData is not null and { Count: > 0 })
            {
                var userOrgFullNameDict = DbOpe_sys_user.Instance.GetAllUserWithOrgFullNameDic();
                foreach (var item in baseData)
                {
                    var temp = new GetOrganizationWithUserTree_Out()
                    {
                        Id = item.Id,
                        Name = item.OrgName,
                        Leaf = false,
                        Child = ConvertTreeOutModel(item.OrgChild, item.UserChild, null, userOrgFullNameDict),
                        //Child = ConvertTreeOutModel4UserMaxSaveCustomer(item.OrgChild, item.UserChild),
                    };
                    outData.Add(temp);
                }
            }
            return outData;
        }


        /// <summary>
        /// 递归封装返回数据
        /// 合并组织机构子级中用户及组织机构
        /// </summary>
        /// <param name="orgChild">机构的org子级</param>
        /// <param name="userChild">机构的user子级</param>
        /// <param name="userNumLeftDict">剩余可保留最大客户数字典</param>
        /// <param name="userOrgFullNameDict">组织全树名称字典</param>
        /// <returns></returns>
        public List<GetOrganizationWithUserTree_Out> ConvertTreeOutModel(List<GetBaseOrgWithUserTree> orgChild = default, List<Db_v_userwithorg> userChild = default, Dictionary<string, GetMaxSavePrivateCustomerNumLeft_OUT> userNumLeftDict = null, Dictionary<string, string> userOrgFullNameDict = null)
        {
            List<GetOrganizationWithUserTree_Out> outData = new();
            GetOrganizationWithUserTree_Out temp = default;
            if (orgChild is not null and { Count: > 0 })
            {
                foreach (GetBaseOrgWithUserTree orgItem in orgChild)
                {
                    temp = new GetOrganizationWithUserTree_Out()
                    {
                        Id = orgItem.Id,
                        Name = orgItem.OrgName,
                        Leaf = false,
                        Child = ConvertTreeOutModel(orgItem.OrgChild, orgItem.UserChild, userNumLeftDict, userOrgFullNameDict),
                    };
                    outData.Add(temp);
                }
            }
            if (userChild is not null and { Count: > 0 })
            {
                foreach (Db_v_userwithorg userItem in userChild)
                {
                    temp = new GetOrganizationWithUserTree_Out()
                    {
                        Id = userItem.Id,
                        Name = userItem.Name,
                        UserName = userItem.UserName,
                        UserNum = userItem.UserNum,
                        AvatarImage = userItem.AvatarImage,
                        AvatarImageUrl = string.IsNullOrEmpty(userItem.AvatarImage) ? null : $"/api/Attachfile/Preview?id={userItem.Id}&fileType=AvatarImage&Authorization={TokenModel.Token}",
                        Leaf = true,
                        MaxSaveCustomer = userItem.MaxSaveCustomer > userItem.MaxSaveCustomerMannualInput ? userItem.MaxSaveCustomer : userItem.MaxSaveCustomerMannualInput,
                    };
                    if (userNumLeftDict != null && userNumLeftDict.ContainsKey(userItem.Id))
                        temp.LeftCustomerNumber = userNumLeftDict[userItem.Id].LeftCustomerNumber;
                    if (userOrgFullNameDict != null && userOrgFullNameDict.ContainsKey(userItem.Id))
                        temp.UserOrgFullName = userOrgFullNameDict[userItem.Id].ToString();
                    outData.Add(temp);
                }
            }
            return outData;
        }

    }

}
