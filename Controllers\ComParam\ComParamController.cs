﻿using System.ComponentModel;
using CRM2_API.BLL.ComParam;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.ControllersViewModel.ComParam;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 公共参数控制器
    /// </summary>
    [Description("公共参数控制器")]
    public class ComParamController : MyControllerBase
    {
        public ComParamController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        
        /// <summary>
        /// 修改公共参数信息
        /// </summary>
        /// <param name="updateComParamDictionaryIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public UpdateComParamDictionary_Out UpdateComParamDictionary(UpdateComParamDictionary_In updateComParamDictionaryIn)
        {
            if (updateComParamDictionaryIn is null)
            {
                throw new ApiException("数据为空");
            }

            return BLL_ComParam.Instance.UpdateComParamDictionary(updateComParamDictionaryIn,true);
        }
        
        /*/// <summary>
        /// 修改公共参数信息[备份]
        /// </summary>
        /// <param name="updateComParamDictionaryIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public UpdateComParamDictionary_Out UpdateComParamDictionaryOld(
            List<UpdateComParamDictionaryOld_In> updateComParamDictionaryIn)
        {
            if (updateComParamDictionaryIn is null or {Count:0})
            {
                throw new ApiException("数据为空");
            }

            return BLL_ComParam.Instance.UpdateComParamDictionaryOld(updateComParamDictionaryIn,true);
        }*/
        
        /// <summary>
        /// 修改公共参数业绩规则信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updateComParamAchiIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdateComParamAchi_Out UpdateComParamAchi(List<UpdateComParamAchi_In> updateComParamAchiIn)
        {
            if (updateComParamAchiIn is null or {Count:0})
            {
                throw new ApiException("数据为空");
            }
            return BLL_ComParam.Instance.UpdateComParamAchi(updateComParamAchiIn,true);
        }

        /// <summary>
        /// 修改公共参数保留客户数规则信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updateComParamRetainIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdateComParamRetain_Out UpdateComParamRetain(List<UpdateComParamRetain_In> updateComParamRetainIn)
        {
            return BLL_ComParam.Instance.UpdateComParamRetain(updateComParamRetainIn,true);
        }

        /// <summary>
        /// 修改公共参数信息，参数分为三个数组，分别为参数数组字典、业绩规则和保留客户数，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updateComParamIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdateComParam_Out UpdateComParam(UpdateComParam_In updateComParamIn)
        {
            if (updateComParamIn == null)
            {
                throw new ApiException("参数不可为空!");
            }
            bool isTimingRefresh = true;    //开始定时刷新的总开关
            return BLL_ComParam.Instance.UpdateComParam(updateComParamIn,isTimingRefresh);
        }

        /// <summary>
        /// 获取公共参数信息。
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public GetComParamList_Out GetComParamList(GetComParamList_In? getComParamListIn)
        {
            getComParamListIn = getComParamListIn ?? new GetComParamList_In();
            return BLL_ComParam.Instance.GetComParamList(getComParamListIn.type.Value);
        }
    }
}