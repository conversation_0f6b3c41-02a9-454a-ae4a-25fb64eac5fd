using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Log;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using SqlSugar;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbCommon;
using CRM2_API.Common.Log;
using CRM2_API.Common.Utils;

namespace CRM2_API.Common.Filter
{
    /// <summary>
    /// 接口进入前调用filter
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class InterfaceLogAttribute : Attribute, IAsyncActionFilter
    {
        /// <summary>
        /// 敏感数据处理 - 对敏感字段进行脱敏
        /// </summary>
        /// <param name="input">要处理的JSON字符串</param>
        /// <returns>已脱敏的JSON字符串</returns>
        private string SanitizeParameters(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            try
            {
                // 定义需要脱敏的字段
                var sensitiveFields = new Dictionary<string, string> 
                {
                    { "password", "******" },
                    { "pwd", "******" }
                };

                // 正则表达式替换敏感内容
                var result = input;
                foreach (var field in sensitiveFields)
                {
                    // 查找字段名，并替换其值
                    var pattern = $"\"{field.Key}\"\\s*:\\s*\"([^\"]*)\"";
                    result = Regex.Replace(result, pattern, $"\"{field.Key}\":\"{field.Value}\"", RegexOptions.IgnoreCase);
                }

                return result;
            }
            catch
            {
                // 处理异常情况，返回安全提示信息
                return "{\"notice\":\"内容已脱敏\"}";
            }
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            //判断接口是否跳过身份验证
            var isSkipAuthCheck = context.Filters.Any(f => f is SkipAuthCheckAttribute);
            var isSkipRightCheck = context.Filters.Any(f => f is SkipRightCheckAttribute);
            var isSkipRecordLog = context.Filters.Any(f => f is SkipRecordLogAttribute);
            //创建log数据
            Db_sys_api_log log = new Db_sys_api_log();
            
            // 生成唯一请求ID并添加到日志
            log.Id = Guid.NewGuid().ToString();
            // 将请求ID添加到响应头，便于客户端关联
            context.HttpContext.Response.Headers.Add("X-Request-ID", log.Id);
            
            string tokenBearer;
            if (isSkipAuthCheck)
            {
                //获取接口的ControllerName
                var controllerName = context.ActionDescriptor.DisplayName.Split('.')[2].ToString().Replace("Controller", "");
                //获取接口的ActionName
                var methodName = context.ActionDescriptor.DisplayName.Split('.')[3].Replace(" (CRM2_API)", "");
                if (!isSkipRecordLog && controllerName != "Example")
                {
                    log.FormId = DbOpe_sys_form.Instance.GetSysForm(controllerName, methodName).Id;
                    log.ControllerName = controllerName;
                    log.MethodName = methodName;
                }
                
            }
            else if (isSkipRightCheck)
            {
                //获取接口的ControllerName
                var controllerName = context.ActionDescriptor.DisplayName.Split('.')[2].ToString().Replace("Controller", "");
                //获取接口的ActionName
                var methodName = context.ActionDescriptor.DisplayName.Split('.')[3].Replace(" (CRM2_API)", "");
                //log.FormId = DbOpe_sys_form.Instance.GetSysForm(controllerName, methodName).Id;
                log.ControllerName = controllerName;
                log.MethodName = methodName;
                if (context.HttpContext.Request.Headers.ContainsKey("Authorization"))
                {
                    tokenBearer = context.HttpContext.Request.Headers["Authorization"];

                }
                else if (context.HttpContext.Request.Method.Equals("GET") && context.HttpContext.Request.Query.ContainsKey("Authorization"))
                {
                    tokenBearer = context.HttpContext.Request.Query["Authorization"];
                    //IsMobile = context.HttpContext.Request.Query["IsMobile"].ToString().ToBool();
                }
                else
                {
                    throw new ApiException("您尚未登录系统", Enum_ReturnErrorCode.TokenError);
                }
                log.UserToken = tokenBearer;
            }
            else
            {
                //对应formId
                log.FormId = Com_SysForm.Instance.FormId;
                var form = DbOpe_sys_form.Instance.GetDataById(log.FormId);
                log.ControllerName = form.ControllerName;
                log.MethodName = form.MethodName;
                if (context.HttpContext.Request.Headers.ContainsKey("Authorization"))
                {
                    tokenBearer = context.HttpContext.Request.Headers["Authorization"];

                }
                else if (context.HttpContext.Request.Method.Equals("GET") && context.HttpContext.Request.Query.ContainsKey("Authorization"))
                {
                    tokenBearer = context.HttpContext.Request.Query["Authorization"];
                    //IsMobile = context.HttpContext.Request.Query["IsMobile"].ToString().ToBool();
                }
                else
                {
                    throw new ApiException("您尚未登录系统", Enum_ReturnErrorCode.TokenError);
                }
                log.UserToken = tokenBearer;
            }
            var opUser = RedisCache.UserInfo.GetUserInfo(TokenModel.Instance.id);
            if (opUser != null)
            {
                log.UserId = opUser.Id;
                log.UserName = opUser.UserName;
            }
            
            // 获取HTTP方法
            string httpMethod = context.HttpContext.Request.Method.ToUpper();
            // 获取请求URL
            string requestUrl = context.HttpContext.Request.Path.ToString() + context.HttpContext.Request.QueryString.Value;
            
            // 获取参数信息
            string args;
            if (httpMethod == "GET")
            {
                args = context.HttpContext.Request.QueryString.Value;
            }
            else
            {
                args = JsonConvert.SerializeObject(context.ActionArguments);
            }
            log.ParamsContent = args;
            
            //创建时间
            log.CreateDate = DateTime.Now;
            
            //操作人使用设备环境和用户代理
            string userAgent = context.HttpContext.Request.Headers.UserAgent.ToString();
            
            // 从请求头中获取客户端类型信息
            bool isMobile = false;
            bool IsDingTalk = false;
            
            // 读取IsMobile请求头
            if (context.HttpContext.Request.Headers.ContainsKey("IsMobile"))
            {
                bool.TryParse(context.HttpContext.Request.Headers["IsMobile"], out isMobile);
            }
            // 或从URL参数中获取(用于GET请求)
            else if (context.HttpContext.Request.Method.Equals("GET") && context.HttpContext.Request.Query.ContainsKey("IsMobile"))
            {
                bool.TryParse(context.HttpContext.Request.Query["IsMobile"], out isMobile);
            }
            
            // 读取IsDingTalk请求头
            if (context.HttpContext.Request.Headers.ContainsKey("IsDingTalk"))
            {
                bool.TryParse(context.HttpContext.Request.Headers["IsDingTalk"], out IsDingTalk);
            }
            // 或从URL参数中获取(用于GET请求)
            else if (context.HttpContext.Request.Method.Equals("GET") && context.HttpContext.Request.Query.ContainsKey("IsDingTalk"))
            {
                bool.TryParse(context.HttpContext.Request.Query["IsDingTalk"], out IsDingTalk);
            }
            
            // 确定客户端类型（使用扩展的枚举值）
            if (StringUtil.IsNullOrEmpty(userAgent))
            {
                log.Env = -1; // 未知设备
            }
            else {
                // 根据请求头和User-Agent确定设备环境
                bool isComputer = NetUtil.GetUserAgentDetail(context.HttpContext.Request.Headers.UserAgent).IsComputer;
                
                if (IsDingTalk && isMobile)
                {
                    log.Env = (int)EnumOpeEnv.DingTalkMobile; // 钉钉移动端
                }
                else if (IsDingTalk && !isMobile)
                {
                    log.Env = (int)EnumOpeEnv.DingTalkWeb; // 钉钉PC端
                }
                else if (!IsDingTalk && isMobile)
                {
                    log.Env = (int)EnumOpeEnv.Mobile; // 普通移动端
                }
                else
                {
                    log.Env = (int)EnumOpeEnv.Web; // 普通Web端
                }
            }

            //操作人使用ip地址
            log.IPAddress = NetIPUtil.GetRealIpAddress(context.HttpContext);

            //开始计时
            var stopwatch = Stopwatch.StartNew();
            ActionExecutedContext result = null;
            log.Result = false;
            int statusCode = 200;
            string errorDetail = null;
            
            try
            {
                result = await next();
                if (result.Exception != null && !result.ExceptionHandled)
                {
                    log.ExceptionContent = result.Exception.Message;
                    // 记录错误详情
                    if (result.Exception.InnerException != null)
                    {
                        errorDetail = result.Exception.InnerException.Message;
                    }
                }
                else
                {
                    log.Result = true;
                }
                
                // 获取HTTP状态码
                statusCode = context.HttpContext.Response.StatusCode;
            }
            catch (Exception ex)
            {
                log.ExceptionContent = ex.Message;
                // 记录更详细的异常信息
                errorDetail = ex.StackTrace;
                
                // 如果是ApiException，在异常内容前添加特征标识前缀，便于后续识别
                if (ex is ApiException)
                {
                    log.ExceptionContent = "ApiException: " + ex.Message;
                }
                
                if (ex.InnerException != null)
                {
                    errorDetail += $" | Inner: {ex.InnerException.Message}";
                }
                throw;
            }
            finally
            {
                stopwatch.Stop();
                log.Duration = Convert.ToInt32(stopwatch.Elapsed.TotalMilliseconds);
                
                // 使用统一的异步处理方式记录日志，避免创建过多的线程
                // 使用ConfigureAwait(false)并忽略任务以消除CS4014警告
                _ = Task.Run(() => 
                {
                    try
                    {
                        // 记录数据库日志
                        Com_InterfaceLog.SetWorkLog(log);
                        
                        try 
                        {
                            // 处理敏感数据
                            var safeParams = SanitizeParameters(log.ParamsContent);
                            
                            // 检查是否启用日志并且ClickHouse数据库连接已初始化
                            if (AppSettings.LogConfig != null && AppSettings.LogConfig.EnableLogging && DbContext.ClickHouseDb != null)
                            {
                                // 确定错误类型和对应的Success值
                                byte successValue = 1; // 默认为成功
                                
                                if (log.Result != true) // 明确与true比较
                                {
                                    // 检查是否为业务错误还是系统错误
                                    bool isBusinessError = false;
                                    
                                    // 判断是否为业务错误的条件
                                    if (!string.IsNullOrEmpty(log.ExceptionContent))
                                    {
                                        // 检查是否为ApiException类型的异常（通过前缀标识）
                                        bool isApiException = log.ExceptionContent.StartsWith("ApiException: ");
                                        
                                        // 通过常见的业务错误消息模式判断
                                        bool containsBusinessErrorPattern = 
                                            log.ExceptionContent.Contains("不可为空") ||
                                            log.ExceptionContent.Contains("不能为空") ||
                                            log.ExceptionContent.Contains("已存在") ||
                                            log.ExceptionContent.Contains("不存在") ||
                                            log.ExceptionContent.Contains("无效") ||
                                            log.ExceptionContent.Contains("未找到") ||
                                            log.ExceptionContent.Contains("您没有权限") ||
                                            log.ExceptionContent.Contains("参数错误") ||
                                            log.ExceptionContent.Contains("您尚未登录系统") ||
                                            log.ExceptionContent.Contains("数据不存在") ||
                                            errorDetail == null || // 没有详细错误堆栈通常是业务错误
                                            statusCode == 403 || // 权限不足
                                            statusCode == 401;   // 未授权
                                        
                                        // 只要满足其中一个条件就认为是业务错误
                                        isBusinessError = isApiException || containsBusinessErrorPattern;
                                    }
                                    
                                    successValue = isBusinessError ? (byte)2 : (byte)0;
                                }
                                
                                // 使用LogService记录日志，该服务内部处理批量日志和错误重试
                                LogService.Instance.LogApiRequest(
                                    log.Id,
                                    log.ControllerName,
                                    log.MethodName,
                                    log.Duration ?? 0,
                                    statusCode,
                                    log.UserName ?? "Unknown",
                                    log.UserId ?? "Unknown",
                                    log.IPAddress,
                                    httpMethod,
                                    requestUrl,
                                    successValue, 
                                    log.ExceptionContent,
                                    (int)(log.Env ?? 0),
                                    safeParams
                                );
                            }
                        }
                        catch (Exception ex)
                        {
                            // 记录日志服务失败不应影响正常业务流程
                            Console.WriteLine($"记录ClickHouse日志失败: {ex.Message}");
                            if (ex.InnerException != null)
                            {
                                Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"异步写入数据库日志失败: {ex.Message}");
                        Console.WriteLine($"数据库日志记录失败 RequestId:{log.Id}, 异常:{ex.Message}");
                    }
                }).ConfigureAwait(false);
            }
            return;
        }
    }

    /// <summary>
    /// 跳过权限验证特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class SkipRecordLogAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {

        }
    }
}





