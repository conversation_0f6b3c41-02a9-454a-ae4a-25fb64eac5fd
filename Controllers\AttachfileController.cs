﻿using System.ComponentModel;
using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.ControllersViewModel.Attachfile;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.Controllers
{
    [Description("附件控制器")]
    public class AttachfileController : MyControllerBase
    {
        public AttachfileController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 附件下载
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileType"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult Download(string id, string fileType)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(fileType))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误!"
                });
            }
            return BLL_Attachfile.Instance.Preview(id, fileType, Response);
        }

        /// <summary>
        /// 附件预览
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileType"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult Preview(string id, string fileType)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(fileType))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误!"
                });
            }
            return BLL_Attachfile.Instance.Preview(id, fileType, Response);
        }
        
        /// <summary>
        /// 将上传的Word文件转换为Html返回
        /// </summary>
        /// <param name="wordToHtmlIn"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public IActionResult WordToHtml([FromForm] WordToHtml_In wordToHtmlIn)
        {
            return BLL_Attachfile.Instance.WordToHtml(wordToHtmlIn.File);
        }

        /// <summary>
        /// 删除盖章合同
        /// </summary>
        [HttpPost]
        public void DeleteAttachFileAll(string id, string fileType)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(fileType))
            {
                throw new ApiException("参数错误");
            }
            BLL_Attachfile.Instance.DeleteAttachFileAll(id, fileType);
        }

        /// <summary>
        /// 删除盖章合同
        /// </summary>
        [HttpPost]
        public void DeleteAttachFile(string id, string fileType)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(fileType))
            {
                throw new ApiException("参数错误");
            }
            BLL_Attachfile.Instance.DeleteAttachFile(id, fileType);
        }

        /// <summary>
        /// 删除盖章合同
        /// </summary>
        [HttpPost]
        public void DeleteAttachFileBySelf(string id, string fileType)
        {
            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(fileType))
            {
                throw new ApiException("参数错误");
            }
            BLL_Attachfile.Instance.DeleteAttachFileBySelf(id, fileType);
        }
    }
}