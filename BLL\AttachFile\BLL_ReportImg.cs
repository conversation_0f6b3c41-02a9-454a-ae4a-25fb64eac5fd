﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using Microsoft.AspNetCore.Http;
using QCloud;
using System.IO;

namespace CRM2_API.BLL.AttachFile
{
    public class BLL_ReportImg : BaseBLL<BLL_ReportImg>
    {
        /// <summary>
        /// 保存附件并返回相应的实体列表[未对数据库进行操作,实体列表需要插入到数据库中]
        /// </summary>
        /// <param name="ReportType">附件分类的枚举</param>
        /// <param name="WorkReportId">工作报告表</param>
        /// <param name="attachFiles">附件</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public List<Db_crm_work_report_img> saveAttachfiles(EnumWorkReportsType ReportType, string WorkReportId, IFormFileCollection? attachFiles)
        {
            if (string.IsNullOrEmpty(WorkReportId))
            {
                throw new ArgumentNullException("工作报告表主键不可为空!");
            }

            List<Db_crm_work_report_img> ImgList = null;
            Db_crm_work_report_img imgfile;
            if (attachFiles is not null && attachFiles.Count > 0)
            {
                ImgList = new List<Db_crm_work_report_img>(attachFiles.Count);
                foreach (IFormFile file in attachFiles)
                {
                    //string fileSavePath = AppSettings.fileSavePath ?? "/FileUpload";
                    string AttachFileId = Guid.NewGuid().ToString();

                    string fileSavePath = "/FileUpload";
                    string fileHead = $"{fileSavePath}/WorkReports/{ReportType.ToString()}/{DateTime.Now:yyyyMMdd}";
                    //设置文件上传路径
                    string fileName = file.FileName;
                    String fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
                    //string fullFileName = $"{fileHead}/{Path.GetFileName(file.FileName)}";
                    string fullFileName = $"{fileHead}/" + AttachFileId;

                    if (AppSettings.QCloud != null && AppSettings.QCloud.Enable)
                    {
                        using (Stream stream = file.OpenReadStream())
                        {
                            QCloudOperator qCloud = new QCloudOperator();
                            qCloud.UploadStream(fullFileName, stream, file.ContentType);
                            //将信息写入到附件表中
                            imgfile = new Db_crm_work_report_img
                            {
                                //Id = Guid.NewGuid().ToString(),
                                Id = AttachFileId,
                                Type = ReportType.ToIntNullable(),
                                WorkReportId = WorkReportId,
                                ImgName = fileName,
                                ImgPath = fullFileName,
                                Deleted = false,
                                CreateUser = TokenModel.Instance.id,
                                CreateDate = DateTime.Now
                            };
                            ImgList.Add(imgfile);
                        }
                    }
                    else
                    {
                        ////创建文件夹，保存文件
                        string? path = Path.GetDirectoryName(fullFileName);
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }

                        //保存文件  文件存在则先删除原来的文件
                        if (File.Exists(fullFileName))
                        {
                            File.Delete(fullFileName);
                        }

                        //将流写入文件
                        using Stream stream = file.OpenReadStream();
                        // 把 Stream 转换成 byte[]
                        byte[] bytes = new byte[stream.Length];
                        stream.Read(bytes, 0, bytes.Length);
                        // 设置当前流的位置为流的开始
                        stream.Seek(0, SeekOrigin.Begin);
                        // 把 byte[] 写入文件
                        using FileStream fs = new(fullFileName, FileMode.Create);
                        using BinaryWriter bw = new(fs);
                        bw.Write(bytes);
                        //将信息写入到附件表中
                        imgfile = new Db_crm_work_report_img
                        {
                            Id = AttachFileId,
                            Type = ReportType.ToIntNullable(),
                            WorkReportId = WorkReportId,
                            ImgName = fileName,
                            ImgPath = fullFileName,
                            Deleted = false,
                            CreateUser = TokenModel.Instance.id,
                            CreateDate = DateTime.Now
                        };
                        ImgList.Add(imgfile);
                    }
                }
            }
            return ImgList;
        }
    }
}
