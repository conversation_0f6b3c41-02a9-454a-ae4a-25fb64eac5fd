﻿using System.Collections.ObjectModel;
using System.IO;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Xml;

namespace CRM2_API.Common.Utils.IdentityCard
{
    /// <summary>
    /// 区域配置
    /// </summary>
    public class AreaCodeMapping
    {
        /// <summary>
        /// 区域字典
        /// </summary>
        private static readonly Dictionary<string, Area> areas;

        /// <summary>
        /// Initializes static members of the <see cref="AreaCodeMapping"/> class.
        /// </summary>
        static AreaCodeMapping()
        {
            areas = LoadAreaInfo();
        }

        /// <summary>
        /// 加载信息
        /// </summary>
        /// <returns>区域信息</returns>
        private static Dictionary<string, Area> LoadAreaInfo()
        {
            XmlDocument doc = LoadXmlDocument("AreaCodes.xml");
            XmlNode areasNode = doc.SelectSingleNode("AreaCode");
            if (areasNode != null)
            {
                XmlNodeList provinceNodeList = areasNode.ChildNodes;
                return LoadProvinces(provinceNodeList);
            }

            return null;
        }

        /// <summary>
        /// 加载XML
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>XmlDocument</returns>
        private static XmlDocument LoadXmlDocument(string fileName)
        {
            var declaringType = MethodBase.GetCurrentMethod().DeclaringType;
            if (declaringType != null)
            {
                string resourceName = declaringType.Namespace + "." + fileName;
                Assembly assembly = Assembly.GetExecutingAssembly();
                Stream stream = assembly.GetManifestResourceStream(resourceName);
                XmlDocument result = new XmlDocument();
                if (stream != null)
                {
                    result.Load(stream);
                }
                return result;
            }
            return null;
        }

        /// <summary>
        /// 解析XML节点
        /// </summary>
        /// <param name="provinceNodeList">provinceNodeList</param>
        /// <returns>结果</returns>
        private static Dictionary<string, Area> LoadProvinces(XmlNodeList provinceNodeList)
        {
            Dictionary<string, Area> result = new Dictionary<string, Area>();
            foreach (XmlNode provinceNode in provinceNodeList)
            {
                string code = GetAttribute(provinceNode, "code");
                string name = GetAttribute(provinceNode, "name");
                Area province = new Area(code, name, null);
                var cities = LoadCities(province, provinceNode.ChildNodes);
                foreach (var city in cities)
                {
                    province.AppendChild(city);
                }
                result.Add(code, province);
            }
            return result;
        }

        /// <summary>
        /// 加载城市
        /// </summary>
        /// <param name="province">省</param>
        /// <param name="cityNodeList">节点</param>
        /// <returns>结果</returns>
        private static IEnumerable<Area> LoadCities(Area province, XmlNodeList cityNodeList)
        {
            List<Area> result = new List<Area>();
            if (cityNodeList != null)
            {
                foreach (XmlNode cityNode in cityNodeList)
                {
                    string code = GetAttribute(cityNode, "code");
                    string name = GetAttribute(cityNode, "name");
                    Area city = new Area(code, name, province);
                    var counties = loadCounties(city, cityNode.ChildNodes);
                    foreach (var county in counties)
                    {
                        city.AppendChild(county);
                    }
                    result.Add(city);
                }
            }
            return result;
        }

        /// <summary>
        /// 加载区域
        /// </summary>
        /// <param name="city">市</param>
        /// <param name="countyNodeList">节点</param>
        /// <returns>结果</returns>
        private static IEnumerable<Area> loadCounties(Area city, XmlNodeList countyNodeList)
        {
            List<Area> result = new List<Area>();
            if (countyNodeList != null)
            {
                foreach (XmlNode countyNode in countyNodeList)
                {
                    string code = GetAttribute(countyNode, "code");
                    string name = GetAttribute(countyNode, "name");
                    Area county = new Area(code, name, city);
                    result.Add(county);
                }
            }
            return result;
        }

        /// <summary>
        /// 获取节点属性
        /// </summary>
        /// <param name="node">node</param>
        /// <param name="attributeName">attributeName</param>
        /// <returns>结果</returns>
        private static string GetAttribute(XmlNode node, string attributeName)
        {
            if (node.Attributes != null)
            {
                XmlAttribute attribute = node.Attributes[attributeName];
                return attribute == null ? string.Empty : attribute.Value;
            }
            return string.Empty;
        }

        /// <summary>
        /// 获取区域信息
        /// </summary>
        /// <param name="areaCode">区域代码</param>
        /// <returns>结果</returns>
        public static AreaInformation GetArea(string areaCode)
        {
            Area targetArea = null;
            if (!string.IsNullOrWhiteSpace(areaCode) && areaCode.Length == 6)
            {
                string provinceCode = areaCode.Substring(0, 2);
                if (areas.ContainsKey(provinceCode))
                {
                    var province = areas[provinceCode];
                    string cityCode = areaCode.Substring(2, 2);
                    if (province.ContainsChild(cityCode))
                    {
                        var city = province.GetChild(cityCode);
                        string countyCode = areaCode.Substring(4);
                        if (city.ContainsChild(countyCode))
                        {
                            targetArea = city.GetChild(countyCode);
                        }
                        else
                        {
                            targetArea = city;
                        }
                    }
                    else if (province.ContainsChild(areaCode.Substring(2)))
                    {
                        targetArea = province.GetChild(areaCode.Substring(2));
                    }
                    else
                    {
                        targetArea = province;
                    }
                }
            }
            return targetArea == null ? null : targetArea.ToAreaInformation();
        }
    }

    /// <summary>
    /// 区域
    /// </summary>
    public class Area
    {
        /// <summary>
        /// 子区域
        /// </summary>
        private readonly Dictionary<string, Area> childrenDic;

        /// <summary>
        /// 区域集
        /// </summary>
        private readonly List<Area> childrenList;

        /// <summary>
        /// Initializes a new instance of the <see cref="Area"/> class.
        /// </summary>
        /// <param name="code">代码</param>
        /// <param name="name">名称</param>
        /// <param name="parent">父区域</param>
        internal Area(string code, string name, Area parent)
        {
            this.Info = new CodeNameMapping(code, name);
            this.Parent = parent;
            this.childrenDic = new Dictionary<string, Area>();
            this.childrenList = new List<Area>();
        }

        /// <summary>
        /// 代码名称映射信息
        /// </summary>
        public CodeNameMapping Info
        {
            get;
            private set;
        }

        /// <summary>
        /// 父区域
        /// </summary>
        public Area Parent
        {
            get;
            private set;
        }

        /// <summary>
        /// 子区域
        /// </summary>
        public ReadOnlyCollection<Area> Children
        {
            get
            {
                return this.childrenList.AsReadOnly();
            }
        }

        /// <summary>
        /// 区域集是否包含
        /// </summary>
        /// <param name="code">代码</param>
        /// <returns>结果</returns>
        internal bool ContainsChild(string code)
        {
            return this.childrenDic.ContainsKey(code);
        }

        /// <summary>
        /// 获取区域
        /// </summary>
        /// <param name="code">代码</param>
        /// <returns>区域</returns>
        internal Area GetChild(string code)
        {
            return this.childrenDic[code];
        }

        /// <summary>
        /// 父亲区域
        /// </summary>
        internal Area TopParent
        {
            get
            {
                return this.Parent == null ? this : this.Parent.TopParent;
            }
        }

        /// <summary>
        /// 添加子区域
        /// </summary>
        /// <param name="child">子节点</param>
        internal void AppendChild(Area child)
        {
            if (!this.childrenDic.ContainsKey(child.Info.Code))
            {
                this.childrenDic.Add(child.Info.Code, child);
                this.childrenList.Add(child);
            }
        }

        /// <summary>
        /// 区域信息转化
        /// </summary>
        /// <returns>区域信息</returns>
        internal AreaInformation ToAreaInformation()
        {
            CodeNameMapping province = this.TopParent.Info;
            CodeNameMapping city = default(CodeNameMapping);
            CodeNameMapping county = default(CodeNameMapping);
            if (this.Parent != null)
            {
                if (this.Parent.Info == province)
                {
                    city = this.Info;
                }
                else
                {
                    city = this.Parent.Info;
                    county = this.Info;
                }
            }
            return new AreaInformation(province, city, county);
        }
    }

    /// <summary>
    /// 区域信息
    /// </summary>
    public class AreaInformation
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="AreaInformation"/> class.
        /// </summary>
        /// <param name="province">省</param>
        /// <param name="city">市</param>
        /// <param name="county">区</param>
        public AreaInformation(CodeNameMapping province, CodeNameMapping city, CodeNameMapping county)
        {
            this.Province = province;
            this.City = city;
            this.County = county;
        }

        /// <summary>
        /// 代码
        /// </summary>
        public string Code
        {
            get
            {
                return this.Province.Code + this.City.Code + this.County.Code;
            }
        }

        /// <summary>
        /// 省
        /// </summary>
        public CodeNameMapping Province
        {
            get;
            private set;
        }

        /// <summary>
        /// 市
        /// </summary>
        public CodeNameMapping City
        {
            get;
            private set;
        }

        /// <summary>
        /// 区
        /// </summary>
        public CodeNameMapping County
        {
            get;
            private set;
        }

        /// <summary>
        /// 名称
        /// </summary>
        public string FullName
        {
            get
            {
                return this.Province.Name + this.City.Name + this.County.Name;
            }
        }

        /// <summary>
        /// 重写ToString
        /// </summary>
        /// <returns>结果</returns>
        public override string ToString()
        {
            return this.FullName;
        }
    }

    /// <summary>
    /// 代码名称映射
    /// </summary>
    public struct CodeNameMapping
    {
        /// <summary>
        /// 代码
        /// </summary>
        private readonly string code;

        /// <summary>
        /// 名称
        /// </summary>
        private readonly string name;

        /// <summary>
        /// Initializes a new instance of the <see cref="CodeNameMapping"/> struct.
        /// </summary>
        /// <param name="code">代码</param>
        /// <param name="name">名称</param>
        internal CodeNameMapping(string code, string name)
        {
            this.code = code;
            this.name = name;
        }

        /// <summary>
        /// 代码
        /// </summary>
        public string Code
        {
            get { return this.code; }
        }

        /// <summary>
        /// 名称
        /// </summary>
        public string Name
        {
            get { return this.name; }
        }

        /// <summary>
        /// 重写比较
        /// </summary>
        /// <param name="obj">对象</param>
        /// <returns>结果</returns>
        public override bool Equals(object obj)
        {
            if (obj != null && obj is CodeNameMapping)
            {
                return ((CodeNameMapping)obj).Code == this.Code;
            }
            return false;
        }

        /// <summary>
        /// GetHashCode
        /// </summary>
        /// <returns>HashCode</returns>
        public override int GetHashCode()
        {
            return this.Code.GetHashCode();
        }

        /// <summary>
        /// 相等比较器
        /// </summary>
        /// <param name="left">left</param>
        /// <param name="right">right</param>
        /// <returns>结果</returns>
        public static bool operator ==(CodeNameMapping left, CodeNameMapping right)
        {
            return left.Code != right.Code;
        }

        /// <summary>
        /// 不相等比较器
        /// </summary>
        /// <param name="left">left</param>
        /// <param name="right">right</param>
        /// <returns>结果</returns>
        public static bool operator !=(CodeNameMapping left, CodeNameMapping right)
        {
            return left.Code != right.Code;
        }
    }
    /// <summary>
    /// 验证类
    /// </summary>
    public class IdValidator
    {
        /// <summary>
        /// 验证码
        /// </summary>
        private readonly char[] verifyCodeMapping = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' };

        /// <summary>
        /// 地区代码
        /// </summary>
        private string areaCode;

        /// <summary>
        /// 生日
        /// </summary>
        private string birthCode;

        /// <summary>
        /// 随机
        /// </summary>
        private string randomCode;

        /// <summary>
        /// 性别
        /// </summary>
        private char sexCode;

        /// <summary>
        /// Initializes a new instance of the <see cref="IdValidator"/> class.
        /// </summary>
        /// <param name="id">Id</param>
        public IdValidator(string id)
        {
            this.Id = id;
            this.Success = false;
            this.ErrorMessage = string.Empty;
            this.Area = null;
            this.Birth = BirthDate.Empty;
            this.Sex = Sex.Male;
        }

        /// <summary>
        /// 身份证ID
        /// </summary>
        public string Id { get; private set; }

        /// <summary>
        /// 结果
        /// </summary>
        public bool Success { get; private set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; private set; }

        /// <summary>
        ///  区域信息
        /// </summary>
        public AreaInformation Area { get; private set; }

        /// <summary>
        ///  生日
        /// </summary>
        public BirthDate Birth { get; private set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex Sex { get; private set; }

        /// <summary>
        /// 执行比较结果
        /// </summary>
        /// <returns>结果</returns>
        public bool Execute()
        {
            string msg = this.CheckAndParse();
            if (string.IsNullOrWhiteSpace(msg))
            {
                this.ErrorMessage = string.Empty;
                this.Success = true;
            }
            else
            {
                this.ErrorMessage = msg;
                this.Success = false;
            }
            return this.Success;
        }

        /// <summary>
        /// IdentityCard18
        /// </summary>
        public string IdentityCard18
        {
            get
            {
                if (string.IsNullOrWhiteSpace(this.Id))
                {
                    return "身份证号码不能为空";
                }
                if (this.Success && this.Id.Length == 15)
                {
                    return this.ToCardInfo18();
                }
                return this.Id;
            }
        }

        /// <summary>
        /// ToCardInfo18
        /// </summary>
        /// <returns>结果</returns>
        private string ToCardInfo18()
        {
            string bodyCode = GetBodyCode(this.areaCode, "19" + this.birthCode, this.randomCode, this.sexCode);
            char verifyCode = this.GetVerifyCode(bodyCode);
            return bodyCode + verifyCode;
        }

        /// <summary>
        /// 获取bodyCode
        /// </summary>
        /// <param name="areaCode">areaCode</param>
        /// <param name="birthCode">birthCode</param>
        /// <param name="randomCode">randomCode</param>
        /// <param name="sexCode">sexCode</param>
        /// <returns></returns>
        private static string GetBodyCode(string areaCode, string birthCode, string randomCode, char sexCode)
        {
            return areaCode + birthCode + randomCode + sexCode.ToString();
        }

        /// <summary>
        /// 检查身份证基本信息
        /// </summary>
        /// <returns>结果</returns>
        private string CheckAndParse()
        {
            if (string.IsNullOrWhiteSpace(this.Id))
            {
                return "身份证号码不能为空";
            }
            if (this.Id.Length == 15)
            {
                return this.ParseCardInfo15();
            }
            if (this.Id.Length == 18)
            {
                return this.ParseCardInfo18();
            }
            return "身份证号码必须为15位或18位";
        }

        /// <summary>
        /// 18位身份证
        /// </summary>
        /// <returns>结果</returns>
        private string ParseCardInfo18()
        {
            const string CardIdParttern = @"(\d{6})(\d{4})(\d{2})(\d{2})(\d{2})(\d{1})([\d,x,X]{1})";
            Match match = Regex.Match(this.Id, CardIdParttern);
            if (match.Success)
            {
                this.areaCode = match.Groups[1].Value;
                string year = match.Groups[2].Value;
                string month = match.Groups[3].Value;
                string day = match.Groups[4].Value;
                this.birthCode = year + month + day;
                this.randomCode = match.Groups[5].Value;
                this.sexCode = char.Parse(match.Groups[6].Value);
                string verifyCode = match.Groups[7].Value.ToUpper();

                if (this.ValidateVerifyCode(this.Id.Substring(0, 17), char.Parse(verifyCode)))
                {
                    try
                    {
                        this.Birth = BirthDate.GetBirthDate(year, month, day);
                        //this.Area = AreaCodeMapping.GetArea(this.areaCode);
                        Sex = GetSex(this.sexCode);
                    }
                    catch (System.Exception ex)
                    {
                        return ex.Message;
                    }
                    return string.Empty;
                }
            }
            return "身份证号码格式错误";
        }

        /// <summary>
        /// 验证验证码
        /// </summary>
        /// <param name="bodyCode">bodyCode</param>
        /// <param name="verifyCode">verifyCode</param>
        /// <returns>结果</returns>
        private bool ValidateVerifyCode(string bodyCode, char verifyCode)
        {
            char calculatedVerifyCode = this.GetVerifyCode(bodyCode);
            return calculatedVerifyCode == verifyCode;
        }

        /// <summary>
        /// 15位身份证
        /// </summary>
        /// <returns>结果</returns>
        private string ParseCardInfo15()
        {
            const string CardIdParttern = @"(\d{6})(\d{2})(\d{2})(\d{2})(\d{2})(\d{1})";
            Match match = Regex.Match(this.Id, CardIdParttern);
            if (match.Success)
            {
                this.areaCode = match.Groups[1].Value;
                string year = match.Groups[2].Value;
                string month = match.Groups[3].Value;
                string day = match.Groups[4].Value;
                this.birthCode = year + month + day;
                this.randomCode = match.Groups[5].Value;
                this.sexCode = char.Parse(match.Groups[6].Value);

                try
                {
                    //this.Area = AreaCodeMapping.GetArea(this.areaCode);
                    this.Birth = BirthDate.GetBirthDate(year, month, day);
                    Sex = GetSex(this.sexCode);
                }
                catch (System.Exception ex)
                {
                    return ex.Message;
                }
                return string.Empty;
            }
            return "身份证号码格式错误";
        }

        /// <summary>
        /// 获取验证码
        /// </summary>
        /// <param name="bodyCode">bodyCode</param>
        /// <returns>结果</returns>
        private char GetVerifyCode(string bodyCode)
        {
            char[] bodyCodeArray = bodyCode.ToCharArray();
            ////int sum = 0;
            ////for (int index = 0; index < bodyCodeArray.Length; index++)
            ////{
            ////    sum += int.Parse(bodyCodeArray[index].ToString()) * GetWeight(index);
            ////}
            ////return this.verifyCodeMapping[sum % 11];
            int sum = bodyCodeArray.Select((t, index) => int.Parse(t.ToString()) * GetWeight(index)).Sum();
            return this.verifyCodeMapping[sum % 11];
        }

        /// <summary>
        /// GetWeight
        /// </summary>
        /// <param name="index">index</param>
        /// <returns>index</returns>
        private static int GetWeight(int index)
        {
            return (1 << (17 - index)) % 11;
        }

        /// <summary>
        /// 获取性别
        /// </summary>
        /// <param name="sexCode">性别代码</param>
        /// <returns>性别</returns>
        private static Sex GetSex(char sexCode)
        {
            return ((int)sexCode) % 2 == 0 ? Sex.Female : Sex.Male;
        }
    }

    /// <summary>
    /// 生日
    /// </summary>
    public struct BirthDate
    {
        /// <summary>
        /// 年
        /// </summary>
        private readonly string year;

        /// <summary>
        /// 月
        /// </summary>
        private readonly string month;

        /// <summary>
        /// 日
        /// </summary>
        private readonly string day;

        /// <summary>
        /// 默认
        /// </summary>
        public static BirthDate Empty
        {
            get { return new BirthDate("00", "00", "00"); }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="BirthDate"/> struct.
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="day">日</param>
        public BirthDate(string year, string month, string day)
        {
            this.year = year;
            this.month = month;
            this.day = day;
        }

        /// <summary>
        /// 获取生日
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="month">月</param>
        /// <param name="day">日</param>
        /// <returns>结果</returns>
        public static BirthDate GetBirthDate(string year, string month, string day)
        {
            DateTime date;
            if (DateTime.TryParse(string.Format("{0}-{1}-{2}", year, month, day), out date))
            {
                return new BirthDate(year, month, day);
            }
            throw new System.Exception("日期不存在");
        }

        /// <summary>
        /// 年
        /// </summary>
        public string Year
        {
            get { return this.year; }
        }

        /// <summary>
        /// 年
        /// </summary>
        public string Month
        {
            get { return this.month; }
        }

        /// <summary>
        /// 日
        /// </summary>
        public string Day
        {
            get { return this.day; }
        }

        /// <summary>
        /// 重写ToString
        /// </summary>
        /// <returns>结果</returns>
        public override string ToString()
        {
            return string.Format("{0}年{1}月{2}日", this.year, this.month, this.day);
        }

        /// <summary>
        /// 重写ToString
        /// </summary>
        /// <param name="separator">separator</param>
        /// <returns>结果</returns>
        public string ToString(string separator)
        {
            return string.Format("{1}{0}{2}{0}{3}", separator, this.year, this.month, this.day);
        }
    }

    /// <summary>
    /// 性别
    /// </summary>
    public enum Sex
    {
        /// <summary>
        /// 男
        /// </summary>
        Male,

        /// <summary>
        /// 女
        /// </summary>
        Female
    }

}
