﻿using CRM2_API.BLL;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;

namespace CRM2_API.Controllers
{
    [Description("角色控制器")]
    public class RoleController : MyControllerBase
    {
        public RoleController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 添加角色信息
        /// </summary>
        /// <param name="addRole_In"></param>
        [HttpPost]
        public void AddRole(AddRole_In addRole_In)
        {
            BLL_Role.Instance.AddRole(addRole_In);
        }

        /// <summary>
        /// 修改角色信息
        /// </summary>
        /// <param name="updRole_In"></param>
        [HttpPost]
        public void UpdateRole(UpdateRole_In updRole_In)
        {
            BLL_Role.Instance.UpdateRole(updRole_In);
        }

        

        /// <summary>
        /// 删除角色信息
        /// </summary>
        /// <param name="Ids"></param>
        [HttpPost]
        public void DeleteRole(string Ids)
        {
            BLL_Role.Instance.DeleteRole(Ids);
        }

        /// <summary>
        /// 修改角色状态信息
        /// </summary>
        /// <param name="updRoleStaIn"></param>
        [HttpPost]
        public void UpdateRoleStatus(UpdateRoleStatusIn updRoleStaIn)
        {
            BLL_Role.Instance.UpdateRoleStatus(updRoleStaIn);
        }

        /// <summary>
        /// 根据角色Id获取角色信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public GetRoleById_Out GetRoleById(string Id)
        {
            if (string.IsNullOrEmpty(Id))
                throw new ApiException("角色主键不可为空");
            return DbOpe_sys_role.Instance.GetRoleById(Id);
        }

        /// <summary>
        /// 根据角色获取绑定人员列表--分页
        /// </summary>
        /// <param name="roleBindingUsers_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetRoleBindingUsers_Out> GetRoleBindingUsers(GetRoleBindingUsers_In roleBindingUsers_In)
        {
            int total = 0;
            var list = DbOpe_sys_role.Instance.GetRoleBindingUsers(roleBindingUsers_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据查询条件获取角色信息列表
        /// </summary>
        /// <param name="searchRoleList_In"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchRoleList_Out> SearchRoleList(SearchRoleList_In searchRoleList_In)
        {
            int total = 0;
            var list = DbOpe_sys_role.Instance.SearchRoleList(searchRoleList_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 获取当前所有可用角色
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<Db_sys_role> GetRoleList()
        {
            return DbOpe_sys_role.Instance.GetRoleList(AppSettings.Env);
        }

        /// <summary>
        /// 获取当前所有可用角色 员工创建或修改使用
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<Db_sys_role> GetRoleList4AddAndUpd()
        {
            return DbOpe_sys_role.Instance.GetRoleList4AddAndUpd();
        }

        /// <summary>
        /// 返回当前系统功能信息，只返回类型为菜单(模块、表单)、列表、列、按钮的数据。
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<GetFormList_Out> GetFormList()
        {
            return DbOpe_sys_form.Instance.GetFormList();
        }

        /// <summary>
        /// 根据用户Id获取用户拥有的功能信息，只返回类型为菜单(模块、表单)、列表、列、按钮的数据,全部获取然后前台判断。
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetFormByUserId_Out> GetFormByUserId(string UserId)
        {
            if (string.IsNullOrEmpty(UserId))
                throw new ApiException("用户主键不可为空");
            return DbOpe_sys_form.Instance.GetFormByUserId(UserId);
        }

        /// <summary>
        /// 根据用户Id和父级节点和类型获取用户拥有的功能信息，只返回类型为菜单(模块、表单)、列表、列、按钮的数据，前台一页一判断和GetFormByUserId类似。
        /// </summary>
        [HttpPost]
        public List<GetFormByUserIdAndParentId_Out> GetFormByUserIdAndParentId( GetFormByUserIdAndParentId_In getFormByUserIdAndParentId_In)
        {
            return DbOpe_sys_form.Instance.GetFormByUserIdAndParentId(getFormByUserIdAndParentId_In);
        }
    }
}
