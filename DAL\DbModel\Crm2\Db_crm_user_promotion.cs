﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///员工职级表
    ///</summary>
    [SugarTable("crm_user_promotion")]
    public class Db_crm_user_promotion
    {
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:员工Id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UserId {get;set;}

           /// <summary>
           /// Desc:员工职级描述表ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string PromotionDescribeId {get;set;}

           /// <summary>
           /// Desc:晋级时间（获取时间）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? PromotionTime {get;set;}

           /// <summary>
           /// Desc:生效时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? TakingEffectTime {get;set;}

           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
