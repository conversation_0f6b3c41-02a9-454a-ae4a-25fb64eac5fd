using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Filter;
using CRM2_API.Model.System;
using Newtonsoft.Json.Linq;

namespace CRM2_API.BLL.GlobalSearchOpe
{
    /// <summary>
    /// 环球搜接口操作业务逻辑类
    /// </summary>
    public class BLL_GlobalSearch
    {
        /// <summary>
        /// 生成环球搜主账号
        /// </summary>
        /// <param name="contractNum">合同号</param>
        /// <param name="gtisAccount">GTIS账号</param>
        /// <param name="startDate">服务开始日期</param>
        /// <param name="endDate">服务结束日期</param>
        /// <param name="settlementLevel">结算级别</param>
        /// <param name="settlementMonth">结算月份</param>
        /// <param name="st_memo">备注</param>
        /// <returns>环球搜码</returns>
        public string AddGlobalSearchPrimaryUser(string contractNum, string gtisAccount, DateTime? startDate, DateTime? endDate, string settlementLevel, decimal? settlementMonth, string st_memo)
        {
            //正确 "{\"result\":\"success\",\"msg\":\"\",\"globalsouID\":\"16116\"}"; 
            //错误 {"result":"error","msg":"Error converting data type varchar to bigint."}
            string accountNumber = "";
            var url = string.Format(AppSettings.GlobalSearchAPI.AddPrimaryUserUrl, contractNum, gtisAccount, startDate.Value.ToyyyyMMdd(), endDate.Value.ToyyyyMMdd(), string.IsNullOrEmpty(settlementLevel) ? "" : settlementLevel.Replace("+", "%2B"), settlementMonth == null ? "" : settlementMonth.ToString(), st_memo);

            int createdCount = 0, targetCount = 5;
            while (createdCount < targetCount)
            {
                var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                if ("success".Equals(ret["result"].ToString()))
                {
                    accountNumber = ret["globalsouID"].ToString();
                    break;
                }
                createdCount++;
                //LogUtil.AddLog(url + "||" + ret["result"].ToString(), "主账号Post");
            }
            if (string.IsNullOrEmpty(accountNumber))
                throw new ApiException("环球搜主账号生成失败");
            return accountNumber;
        }

        /// <summary>
        /// 生成环球搜子账号--单独生成
        /// </summary>
        /// <param name="priHqsCode">父账号环球搜码</param>
        /// <param name="contractNum">合同号</param>
        /// <param name="gtisAccount">GTIS账号</param>
        /// <param name="st_memo">备注</param>
        /// <returns>环球搜码</returns>
        public string AddGlobalSearchSubUser(string priHqsCode, string contractNum, string gtisAccount, string st_memo)
        {
            //正确 "{\"result\":\"success\",\"msg\":\"\",\"globalsouID\":\"16116\"}"; 
            //错误 {"result":"error","msg":"Error converting data type varchar to bigint."}
            string hqsCode = "";
            var url = string.Format(AppSettings.GlobalSearchAPI.AddSubUserUrl, priHqsCode, contractNum, gtisAccount, st_memo);
            int createdCount = 0, targetCount = 5;
            while (createdCount < targetCount)
            {
                var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                if ("success".Equals(ret["result"].ToString()))
                {
                    hqsCode = ret["globalsouID"].ToString();
                    break;
                }
                createdCount++;
                //LogUtil.AddLog(url + "||" + ret["result"].ToString(), "主账号Post");
            }
            if (string.IsNullOrEmpty(hqsCode))
                throw new ApiException("环球搜子账号生成失败");
            return hqsCode;
        }

        /// <summary>
        /// 更新环球搜账号时间
        /// </summary>
        /// <param name="globalSearchCode">环球搜码</param>
        /// <param name="contractNum">合同号</param>
        /// <param name="gtisAccount">GTIS账号</param>
        /// <param name="startDate">服务开始日期</param>
        /// <param name="endDate">服务结束日期</param>
        /// <param name="settlementLevel">结算级别</param>
        /// <param name="settlementMonth">结算月份</param>
        /// <param name="st_memo">备注</param>
        /// <returns>更新是否成功</returns>
        public bool UpdateGlobalSearchUser(string globalSearchCode, string contractNum, string gtisAccount, DateTime? startDate, DateTime? endDate, string settlementLevel, decimal? settlementMonth, string st_memo)
        {
            //"https://mgr.globalsou.com/api/?souID={0}&globalwitsID={1}&globalwitsName={2}&member=ent&acctType=online&dt_service_from={3}&dt_service_to{4}&st_memo={5}"
            //*{"result":"success".Equals(ret["result"].ToString()))
            var url = string.Format(AppSettings.GlobalSearchAPI.UpdateUserUrl, globalSearchCode, contractNum, gtisAccount, startDate.Value.ToyyyyMMdd(), endDate.Value.ToyyyyMMdd(), string.IsNullOrEmpty(settlementLevel) ? "" : settlementLevel.Replace("+", "%2B"), settlementMonth == null ? "" : settlementMonth.ToString(), st_memo);
            var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
            if (!"success".Equals(ret["result"].ToString()))
                throw new ApiException("环球搜续约失败");

            return true;
        }

        /// <summary>
        /// 查询环球搜账号状态
        /// </summary>
        /// <param name="globalSearchCodes">环球搜码列表，多个用分号分隔</param>
        /// <returns>查询结果</returns>
        public List<GlobalSearchUserStatus> CheckUserStatus(string globalSearchCodes)
        {
            var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, globalSearchCodes);
            var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
            var userStatuses = new List<GlobalSearchUserStatus>();
            
            if ("200".Equals(ret["status"].ToString()))
            {
                var results = ret["results"].ToList();
                foreach (var result in results)
                {
                    var userStatus = new GlobalSearchUserStatus
                    {
                        GlobalSearchCode = result["idCst"]?.ToString() ?? "",
                        Status = result["status"]?.ToString() ?? "",
                        AccountType = result["acctType"]?.ToString() ?? "",
                        StartDate = DateTime.TryParse(result["begindate"]?.ToString(), out var start) ? start : (DateTime?)null,
                        EndDate = DateTime.TryParse(result["enddate"]?.ToString(), out var end) ? end : (DateTime?)null
                    };
                    userStatuses.Add(userStatus);
                }
            }
            else
            {
                throw new ApiException(ret["message"]?.ToString() ?? "查询环球搜账号状态失败");
            }
            
            return userStatuses;
        }

        /// <summary>
        /// 查询单个环球搜账号状态
        /// </summary>
        /// <param name="globalSearchCode">环球搜码</param>
        /// <returns>单个账号状态</returns>
        public GlobalSearchUserStatus CheckSingleUserStatus(string globalSearchCode)
        {
            var results = CheckUserStatus(globalSearchCode);
            return results?.FirstOrDefault();
        }

        /// <summary>
        /// 整合开通环球搜账号：先开主账号，再开子账号
        /// </summary>
        /// <param name="contractNum">合同号</param>
        /// <param name="gtisAccount">GTIS账号</param>
        /// <param name="startDate">服务开始日期</param>
        /// <param name="endDate">服务结束日期</param>
        /// <param name="settlementLevel">结算级别</param>
        /// <param name="settlementMonth">结算月份</param>
        /// <param name="subAccountCount">子账号数量</param>
        /// <param name="st_memo">备注</param>
        /// <returns>开通结果，包含主账号和所有子账号的环球搜码</returns>
        public GlobalSearchBatchResult CreateGlobalSearchAccountBatch(string contractNum, string gtisAccount, DateTime startDate, DateTime endDate, string settlementLevel, decimal? settlementMonth, int subAccountCount, string st_memo = "xxx_testwu")
        {
            var result = new GlobalSearchBatchResult
            {
                Success = true,
                SubAccounts = new List<string>()
            };

            try
            {
                // 1. 先创建主账号
                result.PrimaryAccount = AddGlobalSearchPrimaryUser(contractNum, gtisAccount, startDate, endDate, settlementLevel, settlementMonth, st_memo);
            }
            catch (Exception ex)
            {
                throw new ApiException($"环球搜主账号创建失败: {ex.Message}");
            }
                // 2. 批量创建子账号
                for (int i = 1; i <= subAccountCount; i++)
                {
                    try
                    {   
                        var subAccount = AddGlobalSearchSubUser(result.PrimaryAccount, contractNum, gtisAccount, st_memo);
                        result.SubAccounts.Add(subAccount);
                    }
                    catch (Exception ex)
                    {
                        throw new ApiException($"环球搜子账号{i}创建失败: {ex.Message}");
                    }
                }


            return result;
        }
    }

    /// <summary>
    /// 环球搜批量开通结果
    /// </summary>
    public class GlobalSearchBatchResult
    {
        /// <summary>
        /// 是否全部成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 主账号环球搜码
        /// </summary>
        public string PrimaryAccount { get; set; }

        /// <summary>
        /// 子账号环球搜码列表
        /// </summary>
        public List<string> SubAccounts { get; set; }

        /// <summary>
        /// 成功创建的总账号数（主账号+子账号）
        /// </summary>
        public int SuccessCount
        {
            get
            {
                int count = 0;
                if (!string.IsNullOrEmpty(PrimaryAccount)) count++;
                if (SubAccounts != null) count += SubAccounts.Count;
                return count;
            }
        }

        /// <summary>
        /// 所有账号环球搜码（主账号+子账号）
        /// </summary>
        public List<string> AllAccounts
        {
            get
            {
                var all = new List<string>();
                if (!string.IsNullOrEmpty(PrimaryAccount))
                    all.Add(PrimaryAccount);
                if (SubAccounts != null)
                    all.AddRange(SubAccounts);
                return all;
            }
        }
    }

    /// <summary>
    /// 环球搜用户状态
    /// </summary>
    public class GlobalSearchUserStatus
    {
        /// <summary>
        /// 环球搜码
        /// </summary>
        public string GlobalSearchCode { get; set; }

        /// <summary>
        /// 状态：active(正常)、disabled(停用)、noexisted(不存在)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 账号类型
        /// </summary>
        public string AccountType { get; set; }

        /// <summary>
        /// 服务开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 服务结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
    }
} 