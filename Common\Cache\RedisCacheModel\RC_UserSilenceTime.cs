﻿using CRM2_API.Common.AppSetting;

namespace CRM2_API.Common.Cache

{
    public partial class RedisCache
    {
        public class SilenceTime
        {
            const string SILENCETIMEKEY = "SilenceTime_";
            /// <summary>
            /// 获取用户当前Redis缓存中的的SilenceTime
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            /// <returns></returns>
            public static string GetSilenceTime(string userId, bool IsMobile)
            {
                var key = SILENCETIMEKEY + userId + (IsMobile ? "_Mobile" : "");
                return RedisHelper.Get(key);
            }
            /// <summary>
            /// 验证当前用户的静默时间是否过期，true:过期，返回登录页面;false:未过期，可以正常使用
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            /// <returns></returns>
            public static bool CheckSilenceTime(string userId, bool IsMobile)
            {
                var key = SILENCETIMEKEY + userId + (IsMobile ? "_Mobile" : "");
                var silenceTime = RedisHelper.Get(key);
                if (silenceTime == null)
                    return false;
                else
                    return (DateTime.Now.CompareTo(silenceTime.ToDateTime()) > 0);
                    
            }
            /// <summary>
            /// 保存用户的静默时间
            /// </summary>
            /// <param name="userId"></param>
            /// <param name="IsMobile"></param>
            public static void SetSilenceTime(string userId, bool IsMobile)
            {
                var key = SILENCETIMEKEY + userId + (IsMobile ? "_Mobile" : "");
                RedisHelper.Set(key, DateTime.Now.AddMinutes(AppSettings.SilenceMinutes));
            }
        }

    }
}
