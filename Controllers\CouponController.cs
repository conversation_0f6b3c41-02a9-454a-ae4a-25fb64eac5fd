﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using CRM2_API.Common.Filter;
using static CRM2_API.Common.Filter.WorkLog;
using CRM2_API.Model.BusinessModel;
using static CRM2_API.Model.ControllersViewModel.VM_Coupon;
using CRM2_API.Model.ControllersViewModel.TrackingRecord;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;

namespace CRM2_API.Controllers
{
    /// <summary>
    /// 优惠券控制器
    /// </summary>
    [Description("优惠券控制器")]
    public class CouponController : MyControllerBase
    {
        public CouponController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取优惠券列表
        /// </summary>
        /// <param name="searchCouponList_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public ApiTableOut<CouponList> SearchCouponList(SearchCouponList_In searchCouponList_In)
        {
            int total = 0;
            var list = DbOpe_crm_customer_coupon.Instance.SearchCouponList(searchCouponList_In, ref total);
            return GetApiTableOut(list, total);
        }
        /// <summary>
        /// 获取建议优惠券数量
        /// </summary>
        /// <param name="recommendCoupon_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public RecommendCoupon_Out RecommendCoupon(RecommendCoupon_In recommendCoupon_In)
        {
            return DbOpe_crm_customer_coupon.Instance.RecommendCoupon(recommendCoupon_In);
        }

        /// <summary>
        /// 修改优惠券状态(测试用)
        /// </summary>
        /// <param name="changeCoupon_In"></param>
        /// <returns></returns>
        [HttpPost, SkipRightCheck]
        public CouponResult ChangeCouponType(ChangeCoupon changeCoupon_In)
        {
            return BLL_Coupon.Instance.ChangeCouponType(changeCoupon_In.CouponDetailIds, changeCoupon_In.CouponType);
        }

        /// <summary>
        /// 添加优惠券(测试用)
        /// </summary>
        /// <param name="addCouponByContractReceipt_In"></param>
        [HttpPost, SkipRightCheck]
        public void AddCouponByContractReceipt(AddCouponByContractReceipt_In addCouponByContractReceipt_In)
        {
            addCouponByContractReceipt_In.CouponTypeIn = EnumCouponType.Grant;
            addCouponByContractReceipt_In.State = 1;
            BLL_Coupon.Instance.AddCouponByContractReceipt(addCouponByContractReceipt_In);
        }

        [HttpPost, SkipRightCheck]
        public void UpdateCoupanDeadline(UpdateCoupanDeadline updateCoupanDeadline)
        {
            //DbOpe_crm_customer_coupon.Instance.GetCouponDetailByContractId(updateCoupanDeadline.ContractId);
            DbOpe_crm_customer_coupon.Instance.UpdateCoupanDeadline(updateCoupanDeadline.ContractId, updateCoupanDeadline.newDeadline);
        }

        [HttpPost, SkipRightCheck]
        public void RefreshCoupanDeadline()
        {
            DbOpe_crm_customer_coupon.Instance.RefreshCoupanDeadline();
        }

        //[HttpPost, SkipRightCheck]
        //public void testcoupon(string id)
        //{
        //    BLL_Coupon.Instance.GetCanUseCouponByContractId(id);
        //}
    }
}
