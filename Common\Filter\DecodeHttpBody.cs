﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Asn1.Ocsp;
using System.IO;

namespace CRM2_API.Common.Filter
{

    public class DecodeHttpBody : IAuthorizationFilter
    {
        public bool IsDefaultRSAKey { get; set; }
        public void OnAuthorization(AuthorizationFilterContext context)
        {

            if (AppSettings.Env != Enum_SystemSettingEnv.Debug)
            {
                try
                {
                    if (string.IsNullOrEmpty(context.HttpContext.Request.ContentType))
                        return;
                    if ("GET".Equals(context.HttpContext.Request.Method))
                        return;
                    if (context.HttpContext.Request.ContentType.Contains("multipart/form-data"))
                        return;
                    if (context.Filters.Any(f => f is SkipRSAKeyAttribute))
                        return;

                    var syncIOFeature = context.HttpContext.Features.Get<IHttpBodyControlFeature>();
                    if (syncIOFeature != null)
                    {
                        syncIOFeature.AllowSynchronousIO = true;
                    }
                    //从Header中读取 特殊秘钥
                    var specialKey = context.HttpContext.Request.Headers["SpecialKey"];
                    //从Header中读取当前使用环境是否为移动端
                    var IsMobile = context.HttpContext.Request.Headers["IsMobile"].ToString().ToBool();
                    //从token中读取用户Id,根据用户Id的缓存获取用户私钥
                    var userId = TokenModel.Instance.id;
                    string privateKey = "";
                    if (context.Filters.Any(f => f is UseDefaultRSAKeyAttribute || f is SkipAuthCheckAttribute))
                        privateKey = AppSettings.RSAKey.PrivateKey.ToString();
                    else if (!RedisCache.RSAKey.CheckRSAKey(userId, IsMobile))
                        throw new ApiException("用户私钥不存在或已过期");
                    else
                        privateKey = RedisCache.RSAKey.GetPrivateKey(userId, IsMobile);


                    //使用用户私钥解密特殊秘钥，得到对称加密的AES秘钥
                    var aesKey = EncryptUtil.RSAPrivateDecrypt_Pem(specialKey, privateKey);

                    //使用AES秘钥解密httpbody内容，将解密后的body内容写入到context.Request.Body中，覆盖原有body
                    ////读取现有body内容
                    var body = new StreamReader(context.HttpContext.Request.Body).ReadToEnd();
                    ////定义16位偏移量
                    string vector = "hqhscrm220230101";
                    ////解密现有body后存入新body
                    var newBody = EncryptUtil.AESDecrypt(body, aesKey, vector);
                    ////将newbody转换为字节
                    byte[] buffer = Encoding.UTF8.GetBytes(newBody);
                    ////创建新流
                    var requestBodyStream = new MemoryStream();
                    ////设置为从位置0开始读取
                    requestBodyStream.Seek(0, SeekOrigin.Begin);
                    ////写入流
                    requestBodyStream.Write(buffer, 0, buffer.Length);
                    ////将新的流赋值给body
                    context.HttpContext.Request.Body = requestBodyStream;
                    ////设置body的读取位置从0开始
                    context.HttpContext.Request.Body.Seek(0, SeekOrigin.Begin);
                    //存入actDescriptor
                    Com_RequestBody.SetActionDescriptor(context.ActionDescriptor);
                    //切换contentType为application/json
                    context.HttpContext.Request.ContentType = "application/json";
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog($"解密请求体失败：{ex.Message}，堆栈：{ex.StackTrace}");
                    LogUtil.AddErrorLog($"接口：{context.ActionDescriptor.DisplayName}");
                    if (ex.InnerException != null)
                    {
                        LogUtil.AddErrorLog($"内部异常：{ex.InnerException.Message}，堆栈：{ex.InnerException.StackTrace}");
                    }
                    throw;
                }


            }


        }

    }

    /// <summary>
    /// 使用默认公钥验证标签
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class UseDefaultRSAKeyAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {

        }
    }

    /// <summary>
    /// 跳过RSA秘钥验证标签
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class SkipRSAKeyAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {

        }
    }


}
