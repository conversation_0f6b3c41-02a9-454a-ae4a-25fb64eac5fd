﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.ContractTemplate;
using CRM2_API.Model.ControllersViewModel.ContractTemplateApply;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Spreadsheet;
using LgyUtil;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractTerm;

namespace CRM2_API.Controllers
{
    [Description("新-合同条款控制器")]
    public class ContractTermController : MyControllerBase
    {
        public ContractTermController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }


        #region 合同条款模板
        /// <summary>
        /// 添加合同条款模板
        /// </summary>
        /// <param name="addContractTermTemplate_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public AddContractTermTemplateRes_OUT AddSingleContractTermTemplate(AddContractTermTemplate_IN addContractTermTemplate_IN)
        {
            return BLL_ContractTerm.Instance.AddSingleContractTermTemplate(addContractTermTemplate_IN).MappingTo<AddContractTermTemplateRes_OUT>();
        }
        /// <summary>
        /// 修改合同条款模板（仅限于尚未有合同使用过的模板）
        /// </summary>
        /// <param name="updateContractTermTemplate_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public AddContractTermTemplateRes_OUT UpdateContractTermTemplate(UpdateContractTermTemplate_IN updateContractTermTemplate_IN)
        {
            return BLL_ContractTerm.Instance.UpdateContractTermTemplate(updateContractTermTemplate_IN).MappingTo<AddContractTermTemplateRes_OUT>();
        }
        /// <summary>
        /// 升级合同条款模板（本质就是新建一个公司层级的条款）
        /// </summary>
        /// <param name="upgradeContractTermTemplate_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public AddContractTermTemplateRes_OUT UpgradeContractTermTemplate(UpgradeContractTermTemplate_IN upgradeContractTermTemplate_IN)
        {
            return BLL_ContractTerm.Instance.UpgradeContractTermTemplate(upgradeContractTermTemplate_IN).MappingTo<AddContractTermTemplateRes_OUT>();
        }
        /// <summary>
        /// 客户经理获取合同条款模板列表
        /// </summary>
        /// <param name="searchContractTermTemplateList_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public ApiTableOut<IContractTermTemplateQueryResShow> SearchContractTermTemplateList(SearchContractTermTemplateList_IN searchContractTermTemplateList_IN)
        {
            return BLL_ContractTerm.Instance.SearchContractTermTemplateList(searchContractTermTemplateList_IN);
        }
        /// <summary>
        /// 后台、团队管理获取合同条款列表
        /// </summary>
        /// <param name="searchContractTermTemplateManageList_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public ApiTableOut<IContractTermTemplateQueryResManageShow> SearchContractTermTemplateManageList(SearchContractTermTemplateManageList_IN searchContractTermTemplateManageList_IN)
        {
            return BLL_ContractTerm.Instance.SearchContractTermTemplateManageList(searchContractTermTemplateManageList_IN);
        }
        /// <summary>
        /// 后台(初审)单条合同条款，设置合同条款审核流程等
        /// </summary>
        /// <param name="contractTermTemplate"></param>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void SetNewSingleContractTermTemplate(SetNewSingleContractTermTemplate_IN contractTermTemplate)
        {
            BLL_ContractTerm.Instance.SetNewSingleContractTermTemplate(contractTermTemplate);
        }
        /// <summary>
        /// 团队审核单条合同条款模板
        /// </summary>
        /// <param name="teamAudit"></param>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void TeamAuditSingleContractTermTemplate(TeamAuditSingleContractTermTemplate_IN teamAudit)
        {
            BLL_ContractTerm.Instance.TeamAuditSingleContractTermTemplate(teamAudit);
        }
        /// <summary>
        /// 后台复核单条合同条款
        /// </summary>
        /// <param name="reviewSingleContractTermTemplate"></param>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void ReviewSingleContractTermTemplate(ReviewSingleContractTermTemplate_IN reviewSingleContractTermTemplate)
        {
            BLL_ContractTerm.Instance.ReviewSingleContractTermTemplate(reviewSingleContractTermTemplate);
        }
        /// <summary>
        /// 获取单条合同条款模板
        /// </summary>
        /// <param name="getTermTemplate_IN"></param>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public ContractTermTemplate_IN GetSingleContractTermTemplate(GetTermTemplate_IN getTermTemplate_IN)
        {
            return BLL_ContractTerm.Instance.GetSingleContractTermTemplate(getTermTemplate_IN.Term_Template_Id).MappingTo<ContractTermTemplate_IN>();
        }


        /// <summary>
        /// 删除合同条款模板(仅仅是不显示了，物理上数据保留)
        /// 审核中不能删除
        /// Term_Template_Status = 1
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void DeleteContractTermTemplate(SetTermTemplate_IN setTermTemplate_IN)
        {
            BLL_ContractTerm.Instance.DeleteContractTermTemplate(setTermTemplate_IN.Term_Template_Id);
        }


        /// <summary>
        /// 后台作废合同条款模板
        /// 作废后客户经理可以查单，不能再使用，再使用需要重新提交审核
        /// 通过的才可以作废，其他状态不能作废
        /// Term_Template_Status = 5
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void VoidContractTermTemplate(SetTermTemplate_IN setTermTemplate_IN)
        {
            BLL_ContractTerm.Instance.VoidContractTermTemplate(setTermTemplate_IN.Term_Template_Id);
        }

        /// <summary>
        /// 延期合同条款模板
        /// 只对过期状态的条款模板有效
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void DelayContractTermTemplate(DelayContractTermTemplate_IN delayContractTermTemplate_IN)
        {
            BLL_ContractTerm.Instance.DelayContractTermTemplate(delayContractTermTemplate_IN);
        }
        #endregion

        #region 合同条款

        /// <summary>
        /// 获取可用条款列表
        /// </summary>
        /// <param name="getContractCanUseTemplateList_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public ApiTableOut<IContractTermTemplateQueryResShow> GetContractCanUseTemplateList(GetContractCanUseTemplateList_IN getContractCanUseTemplateList_IN)
        {
            return BLL_ContractTerm.Instance.GetContractCanUseTemplateList(getContractCanUseTemplateList_IN);
        }

        /// <summary>
        /// 合同条款是否可用
        /// </summary>
        /// <param name="addContractIn"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public CheckContractTermListCanUse_OUT CheckContractTermListCanUse_TransDeal([FromForm] AddContract_In addContractIn)
        {
            return BLL_ContractTerm.Instance.CheckContractTermListCanUse_TransDeal(addContractIn);
        }

        /// <summary>
        /// 获取合同关联的合同条款列表
        /// 根据身份不同，要做区分显示 判断条款模板/条款是否需要审核
        /// </summary>
        /// <param name="getContractTerms_IN"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public List<ContractTerm_OUT> GetContractTermList(GetContractTerms_IN getContractTerms_IN)
        {
            return BLL_ContractTerm.Instance.GetContractTermList(getContractTerms_IN.ContractId).MappingTo<List<ContractTerm_OUT>>();
        }
        #endregion


        #region 撤销相关
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void RevokeNewSingleContractTermTemplate(RevokeSingleTermTemplate_IN revokeSingleTermTemplate)
        {
            BLL_ContractTerm.Instance.RevokeNewSingleContractTermTemplate(revokeSingleTermTemplate);
        }
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void RevokeTeamSingleTermTemplate(RevokeSingleTermTemplate_IN revokeTeamSingleTermTemplate)
        {
            BLL_ContractTerm.Instance.RevokeTeamSingleTermTemplate(revokeTeamSingleTermTemplate);
        }
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void RevokeReviewSingleContractTermTemplate(RevokeSingleTermTemplate_IN revokeSingleTermTemplate)
        {
            BLL_ContractTerm.Instance.RevokeReviewSingleContractTermTemplate(revokeSingleTermTemplate);
        }

        #endregion


        #region 其他
        /// <summary>
        /// 获取用户可以选择的组织类型
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public List<EnumOrgType> GetUserOrgTypes(string userId)
        {
            return BLL_ContractTerm.Instance.GetUserOrgTypes(userId);
        }
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        public void testovt()
        {
            DbOpe_crm_contractterms_template.Instance.CheckContractTermTemplateDate();
        }
        #endregion


    }
}


