﻿using System.Diagnostics;
using System.IO;
using System.Web;
using Aspose.Cells;
using CRM2_API.BLL.Common;
using CRM2_API.Common.JWT;
using CRM2_API.Common.Cron;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.PerformanceObjectives;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Quartz;

namespace CRM2_API.BLL.PerformanceObjectives
{
    /// <summary>
    /// 业绩目标业务处理
    /// </summary>
    public class BLL_PerformanceObjectives : BaseBLL<BLL_PerformanceObjectives>
    {
        /// <summary>
        /// 修改业绩目标信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updatePerformanceObjectivesIn"></param>
        /// <param name="isTimingRefresh"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public UpdatePerformanceObjectives_Out UpdatePerformanceObjectives(
            UpdatePerformanceObjectives_In updatePerformanceObjectivesIn, bool isTimingRefresh = false)
        {
            var result = new UpdatePerformanceObjectives_Out { Data = 0 };
            if (updatePerformanceObjectivesIn is not null)
            {
                //年份列表
                List<int> yearStrList = new() { updatePerformanceObjectivesIn.ObjectivesYear };
                var checkYearResult = CheckYear(yearStrList);
                if (!checkYearResult)
                {
                    throw new ApiException("年份参数不正确,不是正确的年份或输入的年份不在当前年份的20年区间内");
                }

                List<string> orgIdList = updatePerformanceObjectivesIn.ObjectivesDatas.Select(s => s.OrgId).Distinct().ToList();
                var checkOrgResult = CheckOrg(orgIdList);
                if (!checkOrgResult)
                {
                    throw new ApiException("组织机构id不存在!");
                }

                var checkParamsRepeat = CheckRepeat(updatePerformanceObjectivesIn.ObjectivesDatas.Select(i => new Tuple<string, int>(i.OrgId, updatePerformanceObjectivesIn.ObjectivesYear)).ToArray(), 1);
                if (!checkParamsRepeat)
                {
                    throw new ApiException("输入的参数中有重复的参数");
                }

                if (checkYearResult && checkParamsRepeat && checkOrgResult)
                {
                    //开始操作
                    //获取需要更新的id集合
                    List<string> updateIdList = updatePerformanceObjectivesIn.ObjectivesDatas.Where(w => !string.IsNullOrEmpty(w.Id)).Select(s => s.Id).Distinct().ToList();
                    //根据待更新id列表查询数据库中数据
                    List<Db_sys_performance_objectives> performanceObjectivesListByIdList = DbOpe_sys_performance_objectives.Instance.GetPerformanceObjectivesListByIdList(updateIdList);
                    //将待更新的制作为字典
                    Dictionary<string, Db_sys_performance_objectives> performanceObjectivesByIdDict = performanceObjectivesListByIdList.ToDictionary(k => k.Id, v => v);

                    //获取所有在年份列表中的参数
                    List<Db_sys_performance_objectives> performanceObjectivesList = DbOpe_sys_performance_objectives.Instance.GetPerformanceObjectivesListByYearList(yearStrList);

                    //根据年份进行分离
                    Dictionary<int?, List<Db_sys_performance_objectives>> performanceObjectivesByYearDict =
                        performanceObjectivesList
                            .GroupBy(g => g.ObjectivesYear)
                            .ToDictionary(s => s.Key, v => v.ToList());


                    List<Db_sys_performance_objectives> insertList = new();  //插入列表
                    List<Db_sys_performance_objectives> updateList = new(); //更新列表
                    Db_sys_performance_objectives temp = null;  //临时实体
                    bool isOpenJob = false; //是否开启定时标记
                    List<string> notRemoveIdList = new();   //不被删除的id列表
                    List<string> removeIdList = new();  //移除的id列表
                    Dictionary<int, List<string>> removeIdDict = new();  //年份移除的Id列表字典

                    ///
                    /// 添加的本地函数
                    /// temp 临时实体
                    /// isTimingRefresh 是否开启定时
                    /// type add or rootAdd 区分是继承式新增还是根级新增
                    /// poItem 前端传来的参数
                    /// 
                    Action<Db_sys_performance_objectives, bool, string, UpdatePerformanceObjectives>
                        addPerformanceObjectives =
                            (temp, isTimingRefresh, type, poItem) =>
                            {
                                temp = new()
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    ObjectivesYear = updatePerformanceObjectivesIn.ObjectivesYear,
                                    OrgId = poItem.OrgId,
                                    Jan = poItem.Jan,
                                    Feb = poItem.Feb,
                                    Mar = poItem.Mar,
                                    Apr = poItem.Apr,
                                    May = poItem.May,
                                    Jun = poItem.Jun,
                                    Jul = poItem.Jul,
                                    Aug = poItem.Aug,
                                    Sept = poItem.Sept,
                                    Oct = poItem.Oct,
                                    Nov = poItem.Nov,
                                    Dec = poItem.Dec,
                                    AnnualSummary = CalculateAnnualSummary(poItem),
                                    Deleted = false,
                                    CreateUser = TokenModel.Instance.id,
                                    CreateDate = DateTime.Now
                                };
                                if (type.Equals("add")) //如果是继承式新增,则新实体的Inherit属性赋值上1条的Id
                                {
                                    temp.Inherit = poItem.Id;
                                }

                                if (isTimingRefresh)    //开启定时任务
                                {
                                    temp.WaitEffec = 1; //实体的定时标识
                                    isOpenJob = true;   //定时任务标识为true
                                }
                                else
                                {
                                    temp.TakingEffectTime = DateTime.Now;   //立即生效
                                }
                                insertList.Add(temp);
                            };

                    ///
                    /// 更新的本地函数
                    /// temp 临时实体
                    /// isTimingRefresh 是否开启定时任务
                    /// poItem 前端参数
                    /// 
                    Action<Db_sys_performance_objectives, bool, UpdatePerformanceObjectives> modifyPerformanceObjectives =
                        (temp, isTimingRefresh, poItem) =>
                        {
                            temp.ObjectivesYear = updatePerformanceObjectivesIn.ObjectivesYear;
                            temp.OrgId = poItem.OrgId;
                            temp.Jan = poItem.Jan;
                            temp.Feb = poItem.Feb;
                            temp.Mar = poItem.Mar;
                            temp.Apr = poItem.Apr;
                            temp.May = poItem.May;
                            temp.Jun = poItem.Jun;
                            temp.Jul = poItem.Jul;
                            temp.Aug = poItem.Aug;
                            temp.Sept = poItem.Sept;
                            temp.Oct = poItem.Oct;
                            temp.Nov = poItem.Nov;
                            temp.Dec = poItem.Dec;
                            temp.AnnualSummary = CalculateAnnualSummary(poItem);
                            temp.UpdateUser = TokenModel.Instance.id;
                            temp.UpdateDate = DateTime.Now;
                            if (isTimingRefresh)    //开启定时任务
                            {
                                isOpenJob = true;   //定时标识为true
                            }
                            else
                            {
                                temp.WaitEffec = null;  //实体定时标识为null   
                                temp.TakingEffectTime = DateTime.Now;   //立即生效
                            }
                            notRemoveIdList.Add(temp.Id);   //插入到不被删除的Id列表中
                            updateList.Add(temp);
                        };

                    //当前年份下的所有数据[包括已生效和待生效]
                    List<Db_sys_performance_objectives> performanceObjectivesByItemYear = new();
                    if (performanceObjectivesByYearDict.ContainsKey(updatePerformanceObjectivesIn.ObjectivesYear))
                    {
                        performanceObjectivesByItemYear = performanceObjectivesByYearDict[updatePerformanceObjectivesIn.ObjectivesYear];
                    }

                    foreach (UpdatePerformanceObjectives poItem in updatePerformanceObjectivesIn.ObjectivesDatas)
                    {
                        // 如果参数的月度数据全部为空，则跳过,不存库
                        if (DbOpe_sys_performance_objectives.Instance.CalculatePerformanceObjectivesMonthlyIsNull(poItem)) continue;

                        // 参数id为空
                        if (string.IsNullOrEmpty(poItem.Id))
                        {
                            // 根据参数的组织机构Id和待生效标识判断数据库中是否存在数据，有就在基础上修改，没有则新建
                            temp = performanceObjectivesByItemYear.FirstOrDefault(w =>
                                w.OrgId == poItem.OrgId && w.WaitEffec == 1);
                            // 存在则在基础上修改
                            if (temp != null)
                            {
                                modifyPerformanceObjectives(temp, isTimingRefresh, poItem);
                            }
                            //不存在则新建
                            else
                            {
                                addPerformanceObjectives(temp, isTimingRefresh, "rootAdd", poItem);
                            }
                        }
                        // 参数id不为空
                        else
                        {
                            // 是否存在继承于当前Id的待生效数据，这条数据会被移除掉
                            temp = performanceObjectivesListByIdList.FirstOrDefault(s =>
                                poItem.Id.Equals(s.Inherit) && s.WaitEffec == 1);

                            // 存在，则修改当前数据
                            if (temp != null)
                            {
                                modifyPerformanceObjectives(temp, isTimingRefresh, poItem);
                            }
                            // 不存在，则根据参数的Id获取数据库中的数据
                            else
                            {
                                temp = performanceObjectivesByIdDict[poItem.Id];
                                // 如果是待生效
                                if (temp.WaitEffec == 1)
                                {
                                    //则修改
                                    modifyPerformanceObjectives(temp, isTimingRefresh, poItem);
                                }
                                else
                                {
                                    //如果已经生效，则新建，新建的那条继承当前数据，所以为add。
                                    addPerformanceObjectives(temp, isTimingRefresh, "add", poItem);
                                }
                            }
                        }
                        // 如果开启的定时任务
                        if (isTimingRefresh)
                        {
                            //将当前待生效的数据移除[排除掉不被移除的生效数据] 存放到字典中
                            removeIdDict[updatePerformanceObjectivesIn.ObjectivesYear] = performanceObjectivesByItemYear
                                .Where(w => w.WaitEffec == 1)
                                .Select(s => s.Id)
                                .ToList()
                                .Except(notRemoveIdList)
                                .ToList();
                        }
                        // 如果没有开启定时任务
                        else
                        {
                            //将当前的数据移除[排除掉不被移除的数据]  存放到字典中
                            removeIdDict[updatePerformanceObjectivesIn.ObjectivesYear] = performanceObjectivesByItemYear
                                .Select(s => s.Id)
                                .ToList()
                                .Except(notRemoveIdList)
                                .ToList();
                        }
                    }

                    if (insertList is not null and { Count: > 0 })
                    {
                        DbOpe_sys_performance_objectives.Instance.Insert(insertList);
                    }

                    if (updateList is not null and { Count: > 0 })
                    {
                        DbOpe_sys_performance_objectives.Instance.Update(updateList);
                    }

                    if (removeIdDict.Count > 0)
                    {
                        removeIdList = removeIdDict.Values.SelectMany(x => x).ToList();
                        if (removeIdList is not null and { Count: > 0 })
                        {
                            DbOpe_sys_performance_objectives.Instance.RemoveByIdList(removeIdList);
                        }
                    }

                    DbOpe_sys_performance_objectives.Instance.SaveQueues();
                    result.Data = 1;
                    RefreshPerformanceObjectivesParam();
                    if (isTimingRefresh && isOpenJob)
                    {
                        AddTimingRefreshPerformanceObjectivesTask();
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 修改业绩目标信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// 和上面方法不同的是：该方法会返回每行的信息及最后执行的状态值
        /// 而上面的方法仅仅返回成功与否
        /// </summary>
        /// <param name="updatePerformanceObjectivesPresentDetailsIn"></param>
        /// <param name="isTimingRefresh"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public UpdatePerformanceObjectivesPresentDetails_Out UpdatePerformanceObjectivesPresentDetails(
            UpdatePerformanceObjectivesPresentDetails_In updatePerformanceObjectivesPresentDetailsIn, bool isTimingRefresh = false)
        {
            var result = new UpdatePerformanceObjectivesPresentDetails_Out { Data = 0 };
            if (updatePerformanceObjectivesPresentDetailsIn is not null)
            {
                //年份列表
                List<int> yearStrList = new() { updatePerformanceObjectivesPresentDetailsIn.ObjectivesYear };
                var checkYearResult = CheckYear(yearStrList);
                if (!checkYearResult)
                {
                    throw new ApiException("年份参数不正确,不是正确的年份或输入的年份不在当前年份的20年区间内");
                }
                List<string> orgIdList = updatePerformanceObjectivesPresentDetailsIn.ObjectivesDatas.Where(w => w.Status == EnumPerformanceObjectivesImportStatus.Success && !string.IsNullOrEmpty(w.OrgId)).Select(s => s.OrgId).Distinct().ToList();
                var checkOrgResult = CheckOrg(orgIdList);
                if (!checkOrgResult)
                {
                    throw new ApiException("组织机构id不存在!");
                }

                var checkParamsRepeat = CheckRepeat(updatePerformanceObjectivesPresentDetailsIn.ObjectivesDatas.Where(w => w.Status == EnumPerformanceObjectivesImportStatus.Success && !string.IsNullOrEmpty(w.OrgId)).Select(i => new Tuple<string, int>(i.OrgId, updatePerformanceObjectivesPresentDetailsIn.ObjectivesYear)).ToArray(), 1);
                if (!checkParamsRepeat)
                {
                    throw new ApiException("输入的参数中有重复的参数");
                }

                if (checkYearResult && checkParamsRepeat && checkOrgResult)
                {
                    //开始操作
                    //获取需要更新的id集合
                    List<string> updateIdList = updatePerformanceObjectivesPresentDetailsIn.ObjectivesDatas.Where(w => !string.IsNullOrEmpty(w.Id))
                        .Select(s => s.Id).ToList();

                    List<Db_sys_performance_objectives> performanceObjectivesListByIdList =
                        DbOpe_sys_performance_objectives
                            .Instance.GetPerformanceObjectivesListByIdList(updateIdList);
                    //将待更新的制作为字典
                    Dictionary<string, Db_sys_performance_objectives> performanceObjectivesByIdDict =
                        performanceObjectivesListByIdList.ToDictionary(k => k.Id, v => v);

                    //获取所有在年份列表中的参数
                    List<Db_sys_performance_objectives> performanceObjectivesList =
                        DbOpe_sys_performance_objectives.Instance.GetPerformanceObjectivesListByYearList(yearStrList);

                    //根据年份进行分离
                    Dictionary<int?, List<Db_sys_performance_objectives>> performanceObjectivesByYearDict =
                        performanceObjectivesList
                            .GroupBy(g => g.ObjectivesYear)
                            .ToDictionary(s => s.Key, v => v.ToList());


                    Db_sys_performance_objectives temp = null;
                    bool isOpenJob = false;
                    List<string> notRemoveIdList = new();
                    List<string> removeIdList = new();
                    Dictionary<int, List<string>> removeIdDict = new();
                    List<PerformanceObjectivesImportResult> importResultDatas = new();
                    PerformanceObjectivesImportResult importResultTemp = null;
                    int importSuccessNum = 0;
                    int importFailNum = 0;
                    Action<Db_sys_performance_objectives, bool, string, UpdatePerformanceObjectivesPlus>
                        addPerformanceObjectives =
                            (temp, isTimingRefresh, type, poItem) =>
                            {
                                temp = new()
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    ObjectivesYear = updatePerformanceObjectivesPresentDetailsIn.ObjectivesYear,
                                    OrgId = poItem.OrgId,
                                    Jan = poItem.Jan,
                                    Feb = poItem.Feb,
                                    Mar = poItem.Mar,
                                    Apr = poItem.Apr,
                                    May = poItem.May,
                                    Jun = poItem.Jun,
                                    Jul = poItem.Jul,
                                    Aug = poItem.Aug,
                                    Sept = poItem.Sept,
                                    Oct = poItem.Oct,
                                    Nov = poItem.Nov,
                                    Dec = poItem.Dec,
                                    AnnualSummary = CalculateAnnualSummary(poItem),
                                    Deleted = false,
                                    CreateUser = TokenModel.Instance.id,
                                    CreateDate = DateTime.Now
                                };
                                if (type.Equals("add"))
                                {
                                    temp.Inherit = poItem.Id;
                                }

                                if (isTimingRefresh)
                                {
                                    temp.WaitEffec = 1;
                                    isOpenJob = true;
                                }
                                else
                                {
                                    temp.TakingEffectTime = DateTime.Now;
                                }

                                var insertExecNum = DbOpe_sys_performance_objectives.Instance.Insert(temp);
                                importResultTemp = new()
                                {
                                    No = poItem.No,
                                    Id = temp.Id,
                                    OrgId = poItem.OrgId,
                                    OrgNum = poItem.OrgNum,
                                    OrgName = poItem.OrgName,
                                    OrgType = poItem.OrgType,
                                    OrgTypeName = poItem.OrgTypeName,
                                    ParentId = poItem.ParentId,
                                    Status = insertExecNum >= 1 ? EnumPerformanceObjectivesImportStatus.Success : EnumPerformanceObjectivesImportStatus.Fail,
                                    StatusName = insertExecNum >= 1 ? EnumPerformanceObjectivesImportStatus.Success.GetEnumDescription() : EnumPerformanceObjectivesImportStatus.Fail.GetEnumDescription(),
                                };
                                importResultDatas.Add(importResultTemp);
                                if (importResultTemp.Status == EnumPerformanceObjectivesImportStatus.Success)
                                {
                                    importSuccessNum++;
                                }
                                else
                                {
                                    importFailNum++;
                                }

                            };
                    Action<Db_sys_performance_objectives, bool, UpdatePerformanceObjectivesPlus> modifyPerformanceObjectives =
                        (temp, isTimingRefresh, poItem) =>
                        {
                            temp.ObjectivesYear = updatePerformanceObjectivesPresentDetailsIn.ObjectivesYear;
                            temp.OrgId = poItem.OrgId;
                            temp.Jan = poItem.Jan == null ? temp.Jan : poItem.Jan;
                            temp.Feb = poItem.Feb == null ? temp.Feb : poItem.Feb;
                            temp.Mar = poItem.Mar == null ? temp.Mar : poItem.Mar;
                            temp.Apr = poItem.Apr == null ? temp.Apr : poItem.Apr;
                            temp.May = poItem.May == null ? temp.May : poItem.May;
                            temp.Jun = poItem.Jun == null ? temp.Jun : poItem.Jun;
                            temp.Jul = poItem.Jul == null ? temp.Jul : poItem.Jul;
                            temp.Aug = poItem.Aug == null ? temp.Aug : poItem.Aug;
                            temp.Sept = poItem.Sept == null ? temp.Sept : poItem.Sept;
                            temp.Oct = poItem.Oct == null ? temp.Oct : poItem.Oct;
                            temp.Nov = poItem.Nov == null ? temp.Nov : poItem.Nov;
                            temp.Dec = poItem.Dec == null ? temp.Dec : poItem.Dec;
                            temp.AnnualSummary = CalculateAnnualSummary(poItem);
                            temp.UpdateUser = TokenModel.Instance.id;
                            temp.UpdateDate = DateTime.Now;
                            if (isTimingRefresh)
                            {
                                isOpenJob = true;
                            }
                            else
                            {
                                temp.WaitEffec = null;
                                temp.TakingEffectTime = DateTime.Now;
                            }
                            notRemoveIdList.Add(temp.Id);

                            var insertExecNum = DbOpe_sys_performance_objectives.Instance.Update(temp);
                            importResultTemp = new()
                            {
                                No = poItem.No,
                                Id = temp.Id,
                                OrgId = poItem.OrgId,
                                OrgNum = poItem.OrgNum,
                                OrgName = poItem.OrgName,
                                OrgType = poItem.OrgType,
                                OrgTypeName = poItem.OrgTypeName,
                                ParentId = poItem.ParentId,
                                Status = insertExecNum >= 1 ? EnumPerformanceObjectivesImportStatus.Success : EnumPerformanceObjectivesImportStatus.Fail,
                                StatusName = insertExecNum >= 1 ? EnumPerformanceObjectivesImportStatus.Success.GetEnumDescription() : EnumPerformanceObjectivesImportStatus.Fail.GetEnumDescription(),
                            };
                            importResultDatas.Add(importResultTemp);
                            if (importResultTemp.Status == EnumPerformanceObjectivesImportStatus.Success)
                            {
                                importSuccessNum++;
                            }
                            else
                            {
                                importFailNum++;
                            }

                        };

                    foreach (UpdatePerformanceObjectivesPlus poItem in updatePerformanceObjectivesPresentDetailsIn.ObjectivesDatas)
                    {
                        if (poItem.Status == EnumPerformanceObjectivesImportStatus.Fail || DbOpe_sys_performance_objectives.Instance.CalculatePerformanceObjectivesMonthlyIsNull(poItem))
                        {
                            importResultTemp = new()
                            {
                                No = poItem.No,
                                Id = poItem.Id,
                                OrgId = poItem.OrgId,
                                OrgNum = poItem.OrgNum,
                                OrgName = poItem.OrgName,
                                OrgType = poItem.OrgType,
                                OrgTypeName = poItem.OrgTypeName,
                                ParentId = poItem.ParentId,
                                Status = EnumPerformanceObjectivesImportStatus.Fail,
                                StatusName = EnumPerformanceObjectivesImportStatus.Fail.GetEnumDescription(),
                            };
                            importResultDatas.Add(importResultTemp);
                            importFailNum++;    //失败值+1
                            continue;   // 如果参数的月度数据全部为空，则跳过
                        }

                        //当前年份下的所有数据[包括已生效和待生效]
                        List<Db_sys_performance_objectives> performanceObjectivesByItemYear = new();
                        if (performanceObjectivesByYearDict.ContainsKey(updatePerformanceObjectivesPresentDetailsIn.ObjectivesYear))
                        {
                            performanceObjectivesByItemYear = performanceObjectivesByYearDict[updatePerformanceObjectivesPresentDetailsIn.ObjectivesYear];
                        }

                        if (string.IsNullOrEmpty(poItem.Id))
                        {
                            //1.判断参数是否有新建
                            //有就在基础上修改
                            //没有则新建
                            temp = performanceObjectivesByItemYear.FirstOrDefault(w =>
                                w.OrgId == poItem.OrgId && w.WaitEffec == 1);
                            if (temp != null)
                            {
                                modifyPerformanceObjectives(temp, isTimingRefresh, poItem);
                            }
                            else
                            {
                                addPerformanceObjectives(temp, isTimingRefresh, "rootAdd", poItem);
                            }
                        }
                        else
                        {
                            temp = performanceObjectivesListByIdList.FirstOrDefault(s =>
                                poItem.Id.Equals(s.Inherit) && s.WaitEffec == 1);
                            if (temp != null)
                            {
                                modifyPerformanceObjectives(temp, isTimingRefresh, poItem);
                            }
                            else
                            {
                                temp = performanceObjectivesByIdDict[poItem.Id];
                                //if (temp.WaitEffec == 1)
                                //{
                                modifyPerformanceObjectives(temp, isTimingRefresh, poItem);
                                //}
                                //else
                                //{
                                //    addPerformanceObjectives(temp, isTimingRefresh, "add", poItem);
                                //}
                            }
                        }

                        if (isTimingRefresh)
                        {
                            removeIdDict[updatePerformanceObjectivesPresentDetailsIn.ObjectivesYear] = performanceObjectivesByItemYear
                                .Where(w => w.WaitEffec == 1)
                                .Select(s => s.Id)
                                .ToList()
                                .Except(notRemoveIdList)
                                .ToList();
                        }
                        else
                        {
                            removeIdDict[updatePerformanceObjectivesPresentDetailsIn.ObjectivesYear] = performanceObjectivesByItemYear
                                .Select(s => s.Id)
                                .ToList()
                                .Except(notRemoveIdList)
                                .ToList();
                        }
                    }

                    if (removeIdDict.Count > 0)
                    {
                        removeIdList = removeIdDict.Values.SelectMany(x => x).ToList();
                        if (removeIdList is not null and { Count: > 0 })
                        {
                            DbOpe_sys_performance_objectives.Instance.RemoveByIdList(removeIdList);
                        }
                    }

                    DbOpe_sys_performance_objectives.Instance.SaveQueues();
                    RefreshPerformanceObjectivesParam();
                    result.Data = 1;
                    result.ImportSuccessNum = importSuccessNum;
                    result.ImportResultDatas = importResultDatas;
                    result.ImportFailNum = importFailNum;
                    result.IsAllSuccess = result.ImportFailNum <= 0;
                    if (isTimingRefresh && isOpenJob)
                    {
                        AddTimingRefreshPerformanceObjectivesTask();
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// PerformanceObjectives定时任务
        /// </summary>
        public void AddTimingRefreshPerformanceObjectivesTask()
        {
            string jobName = $"refreshPerformanceObjectivesTask";
            string cronExpression = "*/25 * * * * ?";
            
            SafeCronUtil.AddCronJob(jobName, () =>
            {
                RefreshPerformanceObjectivesParam();
                Debug.WriteLine($"{jobName}任务执行完成！ \t {DateTime.Now}");
            }, cronExpression);
        }

        /// <summary>
        /// 刷新PerformanceObjectives操作Action
        /// </summary>
        private void RefreshPerformanceObjectivesParam()
        {
            List<Db_sys_performance_objectives> allData =
                DbOpe_sys_performance_objectives.Instance.GetAllPerformanceObjectives();
            List<Db_sys_performance_objectives> newData = allData.Where(w => w.WaitEffec == 1).ToList();
            if (newData is null or { Count: 0 }) return;
            //拿出有更新内容的年份
            List<int?> newYears = newData.Select(s => s.ObjectivesYear).Distinct().ToList();
            List<string> updataOrg = newData.Select(s => s.OrgId).Distinct().ToList();
            List<Db_sys_performance_objectives> oldData = allData.Where(w => newYears.Contains(w.ObjectivesYear)).Where(w => updataOrg.Contains(w.OrgId)).Except(newData).ToList();

            //移除旧的参数并修改新的参数
            DbOpe_sys_performance_objectives.Instance.RemoveByIdList(oldData.Select(s => s.Id).ToList());
            foreach (Db_sys_performance_objectives newItem in newData)
            {
                newItem.WaitEffec = null;
                newItem.TakingEffectTime = DateTime.Now;
                newItem.UpdateUser = UserTokenInfo.id;
                newItem.UpdateDate = DateTime.Now;
            }

            DbOpe_sys_performance_objectives.Instance.Update(newData);
            DbOpe_sys_performance_objectives.Instance.SaveQueues();
        }

        /// <summary>
        /// 根据年份获取业绩目标信息
        /// </summary>
        /// <param name="getPerformanceObjectivesListIn"></param>
        /// <returns></returns>
        public List<GetPerformanceObjectivesList_Out> GetPerformanceObjectivesList(
            GetPerformanceObjectivesList_In getPerformanceObjectivesListIn)
        {
            bool checkYearResult = CheckYear(new List<int> { getPerformanceObjectivesListIn.ObjectivesYear });
            if (checkYearResult)
            {
                return DbOpe_sys_performance_objectives.Instance.GetPerformanceObjectivesList(getPerformanceObjectivesListIn) ?? new();
            }

            return new List<GetPerformanceObjectivesList_Out>(0);
        }

        /// <summary>
        /// 统一配置业绩目标信息
        /// </summary>
        /// <param name="unifiedConfigPerformance"></param>
        /// <param name="isTimingRefresh"></param>
        /// <returns></returns>
        public UnifiedConfigPerformanceObjectives_Out UnifiedConfigPerformanceObjectives(
            List<UnifiedConfigPerformanceObjectives_In> unifiedConfigPerformance, bool isTimingRefresh = false)
        {
            UnifiedConfigPerformanceObjectives_Out result = new() { Data = 0 };
            if (unifiedConfigPerformance is not null and { Count: > 0 })
            {
                //年份列表
                List<int> yearList = unifiedConfigPerformance.Select(s => s.ObjectivesYear).Distinct().ToList();
                var checkYearResult = CheckYear(yearList);
                if (!checkYearResult)
                {
                    throw new ApiException("年份参数不正确,不是正确的年份或输入的年份不在当前年份的20年区间内");
                }

                var checkParamsRepeat = CheckRepeat(unifiedConfigPerformance.Select(i =>
                    new Tuple<string, int>(i.OrgType.ToString(), i.ObjectivesYear)).ToArray(), 2);
                if (!checkParamsRepeat)
                {
                    throw new ApiException("输入的参数中有重复的参数");
                }

                //获取所有在年份列表中的参数
                List<Db_sys_performance_objectives> performanceObjectivesList =
                    DbOpe_sys_performance_objectives.Instance.GetPerformanceObjectivesListByYearList(yearList);

                //根据年份进行分离
                Dictionary<int?, List<Db_sys_performance_objectives>> performanceObjectivesByYearDict =
                    performanceObjectivesList
                        .GroupBy(g => g.ObjectivesYear)
                        .ToDictionary(s => s.Key, v => v.ToList());

                List<Db_sys_performance_objectives> insertList = new();
                List<Db_sys_performance_objectives> updateList = new();
                bool isOpenJob = false;
                List<string> notRemoveIdList = new();
                List<string> removeIdList = new();

                foreach (UnifiedConfigPerformanceObjectives_In inItem in unifiedConfigPerformance)
                {
                    int orgType = inItem.OrgType;
                    List<SysOrganizationTree> orgTree =
                        DbOpe_sys_performance_objectives.Instance.GetOrganizationTreeByOrgType(orgType);
                    //当前年份下的所有数据[包括已生效和待生效]
                    List<Db_sys_performance_objectives> performanceObjectivesByItemYear = new();
                    if (performanceObjectivesByYearDict.ContainsKey(inItem.ObjectivesYear))
                    {
                        performanceObjectivesByItemYear = performanceObjectivesByYearDict[inItem.ObjectivesYear];
                    }

                    if (orgTree is not null and { Count: > 0 })
                    {
                        BuildData(inItem.ObjectivesYear, inItem.ObjectivesValue, orgTree, performanceObjectivesByItemYear, isTimingRefresh, ref isOpenJob, ref insertList, ref updateList, ref notRemoveIdList, ref removeIdList);
                    }
                    if (isTimingRefresh)
                    {
                        removeIdList.AddRange(
                            performanceObjectivesByItemYear
                                .Where(w => w.WaitEffec == 1)
                                .Select(s => s.Id)
                                .ToList()
                                .Except(notRemoveIdList)
                                .ToList());
                    }
                    else
                    {
                        removeIdList.AddRange(
                            performanceObjectivesByItemYear
                                .Select(s => s.Id)
                                .ToList()
                                .Except(notRemoveIdList)
                                .ToList());
                    }
                }
                if (insertList is not null and { Count: > 0 })
                {
                    DbOpe_sys_performance_objectives.Instance.Insert(insertList);
                }

                if (updateList is not null and { Count: > 0 })
                {
                    DbOpe_sys_performance_objectives.Instance.Update(updateList);
                }

                if (removeIdList is not null and { Count: > 0 })
                {
                    DbOpe_sys_performance_objectives.Instance.RemoveByIdList(removeIdList);
                }

                DbOpe_sys_performance_objectives.Instance.SaveQueues();
                result.Data = 1;
                if (isTimingRefresh && isOpenJob)
                {
                    AddTimingRefreshPerformanceObjectivesTask();
                }
            }
            return result;
        }

        /// <summary>
        /// 构建数据
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="value">目标金额</param>
        /// <param name="orgTree">组织机构列表</param>
        /// <param name="performanceObjectivesByItemYear">当前年份在db中已存在的数据</param>
        /// <param name="isTimingRefresh">是否定时刷新</param>
        /// <param name="isOpenJob">是否开启定时任务</param>
        /// <param name="insertList">插入列表</param>
        /// <param name="updateList">更新列表</param>
        /// <param name="notRemoveIdList">不会移除的id列表</param>
        /// <param name="removeIdList">必须移除的id列表</param>
        private void BuildData(int year, decimal value, List<SysOrganizationTree> orgTree, List<Db_sys_performance_objectives> performanceObjectivesByItemYear, bool isTimingRefresh
            , ref bool isOpenJob, ref List<Db_sys_performance_objectives> insertList, ref List<Db_sys_performance_objectives> updateList, ref List<string> notRemoveIdList, ref List<string> removeIdList)
        {
            Db_sys_performance_objectives temp = null;
            if (orgTree is not null and { Count: > 0 })
            {
                //计算业绩分配
                decimal avgValue = value / orgTree.Count;
                foreach (SysOrganizationTree orgItem in orgTree)
                {
                    decimal monthlyAvgValue = avgValue / 12;
                    //业务处理
                    temp = performanceObjectivesByItemYear.Where(w =>
                            w.ObjectivesYear.Equals(year) && w.OrgId.Equals(orgItem.Id) && w.WaitEffec == 1)
                        .FirstOrDefault();
                    if (temp is not null)
                    {
                        temp.ObjectivesYear = year;
                        temp.OrgId = orgItem.Id;
                        temp.Jan = monthlyAvgValue;
                        temp.Feb = monthlyAvgValue;
                        temp.Mar = monthlyAvgValue;
                        temp.Apr = monthlyAvgValue;
                        temp.May = monthlyAvgValue;
                        temp.Jun = monthlyAvgValue;
                        temp.Jul = monthlyAvgValue;
                        temp.Aug = monthlyAvgValue;
                        temp.Sept = monthlyAvgValue;
                        temp.Oct = monthlyAvgValue;
                        temp.Nov = monthlyAvgValue;
                        temp.Dec = monthlyAvgValue;
                        temp.AnnualSummary = avgValue;
                        temp.UpdateUser = TokenModel.Instance.id;
                        temp.UpdateDate = DateTime.Now;
                        if (isTimingRefresh)
                        {
                        }
                        else
                        {
                            temp.WaitEffec = null;
                            temp.TakingEffectTime = DateTime.Now;
                        }
                        notRemoveIdList.Add(temp.Id);
                        updateList.Add(temp);
                    }
                    else
                    {
                        temp = new()
                        {
                            Id = Guid.NewGuid().ToString(),
                            ObjectivesYear = year,
                            OrgId = orgItem.Id,
                            Jan = monthlyAvgValue,
                            Feb = monthlyAvgValue,
                            Mar = monthlyAvgValue,
                            Apr = monthlyAvgValue,
                            May = monthlyAvgValue,
                            Jun = monthlyAvgValue,
                            Jul = monthlyAvgValue,
                            Aug = monthlyAvgValue,
                            Sept = monthlyAvgValue,
                            Oct = monthlyAvgValue,
                            Nov = monthlyAvgValue,
                            Dec = monthlyAvgValue,
                            AnnualSummary = avgValue,
                            Deleted = false,
                            CreateUser = TokenModel.Instance.id,
                            CreateDate = DateTime.Now
                        };
                        if (isTimingRefresh)
                        {
                            temp.WaitEffec = 1;
                            isOpenJob = true;
                        }
                        else
                        {
                            temp.TakingEffectTime = DateTime.Now;
                        }
                        insertList.Add(temp);
                    }

                    if (orgItem.Child is not null and { Count: > 0 })
                    {
                        BuildData(year, avgValue, orgItem.Child, performanceObjectivesByItemYear, isTimingRefresh, ref isOpenJob, ref insertList, ref updateList, ref notRemoveIdList, ref removeIdList);
                    }
                }
            }
        }

        /// <summary>
        /// 校验年份
        /// </summary>
        /// <param name="yearList"></param>
        /// <returns></returns>
        private bool CheckYear(List<int> yearList)
        {
            if (yearList is null or { Count: 0 }) return false;
            foreach (int year in yearList)
            {
                int differ = DateTime.Now.Year - year;
                if (Math.Abs(differ) > 20) throw new ApiException($"{year}不在当前年份的20年区间内");
            }
            return true;
        }

        /// <summary>
        /// 校验组织机构参数
        /// </summary>
        /// <param name="orgIdList"></param>
        /// <returns></returns>
        private bool CheckOrg(List<string> orgIdList)
        {
            if (orgIdList is null or { Count: 0 }) return false;
            var orgDict = DbOpe_sys_performance_objectives.Instance.GetOrganizationDictByOrgIdList(orgIdList);
            foreach (string orgId in orgIdList)
            {
                if (string.IsNullOrEmpty(orgId)) return false;
                if (!orgDict.ContainsKey(orgId))
                {
                    throw new ApiException($"请求参数中对应的组织机构不存在!");
                }
            }
            return true;
        }

        /// <summary>
        /// 检查核心参数中是否存在重复项
        /// </summary>
        /// <param name="param">核心参数</param>
        /// <param name="checkType">检查类型：1 == 机构id：年份，2 == 机构类型：年份</param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        private bool CheckRepeat(Tuple<string, int>[]? param, int checkType)
        {
            if (param is not null and { Length: > 0 })
            {
                var groupParam = param.GroupBy(i => i.Item1 + "|" + i.Item2)
                    .Select(g => (new Tuple<string, int>(g.Key, g.Count()))).ToList();
                foreach ((string? item, int count) in groupParam)
                {
                    if (count > 1)
                    {
                        var itemList = item.Split("|").ToList();
                        string type = checkType == 1 ? "id" : "类型";
                        throw new ApiException($"机构{type}：{itemList[0]}在年份：{itemList[1]}中重复,请移除重复项后重新添加");
                    }
                }
            }
            return true;
        }


        /// <summary>
        /// 根据每个月的值计算汇总值
        /// </summary>
        /// <param name="performanceObjectivesBase"></param>
        /// <returns></returns>
        private decimal CalculateAnnualSummary(PerformanceObjectives_Base performanceObjectivesBase)
        {
            Decimal count = decimal.Zero;
            if (performanceObjectivesBase.Jan != null && performanceObjectivesBase.Jan != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Jan.Value);
            }
            if (performanceObjectivesBase.Feb != null && performanceObjectivesBase.Feb != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Feb.Value);
            }
            if (performanceObjectivesBase.Mar != null && performanceObjectivesBase.Mar != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Mar.Value);
            }
            if (performanceObjectivesBase.Apr != null && performanceObjectivesBase.Apr != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Apr.Value);
            }
            if (performanceObjectivesBase.May != null && performanceObjectivesBase.May != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.May.Value);
            }
            if (performanceObjectivesBase.Jun != null && performanceObjectivesBase.Jun != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Jun.Value);
            }
            if (performanceObjectivesBase.Jul != null && performanceObjectivesBase.Jul != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Jul.Value);
            }
            if (performanceObjectivesBase.Aug != null && performanceObjectivesBase.Aug != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Aug.Value);
            }
            if (performanceObjectivesBase.Sept != null && performanceObjectivesBase.Sept != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Sept.Value);
            }
            if (performanceObjectivesBase.Oct != null && performanceObjectivesBase.Oct != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Oct.Value);
            }
            if (performanceObjectivesBase.Nov != null && performanceObjectivesBase.Nov != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Nov.Value);
            }
            if (performanceObjectivesBase.Dec != null && performanceObjectivesBase.Dec != decimal.Zero)
            {
                count = Decimal.Add(count, performanceObjectivesBase.Dec.Value);
            }
            return count;
        }

        /// <summary>
        /// 下载业绩目标的导入模版
        /// </summary>
        /// <param name="downloadPerformanceObjectivesImportTemplateIn"></param>
        /// <param name="response"></param>
        /// <returns></returns>
        public IActionResult DownloadPerformanceObjectivesImportTemplate(
            DownloadPerformanceObjectivesImportTemplate_In downloadPerformanceObjectivesImportTemplateIn, HttpResponse response)
        {
            var fileTemplatePath = Path.Combine(Directory.GetCurrentDirectory(), "PerformanceObjectivesImport.xlsx");
            if (!File.Exists(fileTemplatePath))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "业绩目标导入模版不存在!"
                });
            }
            MemoryStream outputStream = new();
            using FileStream fileStream = new(fileTemplatePath, FileMode.Open);
            using MemoryStream memoryStream = new();
            fileStream.CopyTo(memoryStream);
            Workbook workbook = new(memoryStream);
            Worksheet worksheet = workbook.Worksheets[0];
            Cell cell = worksheet.Cells["C2"];  //获取年度目标的单元格
            //Style style = cell.GetStyle();
            cell.PutValue($"{downloadPerformanceObjectivesImportTemplateIn.ObjectivesYear}年度目标");
            workbook.Save(outputStream, SaveFormat.Xlsx);
            outputStream.Position = 0;
            string fileName = "业绩目标导入.xlsx";
            string encodeFilename = HttpUtility.UrlEncode(fileName, Encoding.GetEncoding("UTF-8"));
            response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
            return new FileStreamResult(outputStream, "application/octet-stream");
        }

        /// <summary>
        /// 导入业绩目标的模版数据
        /// </summary>
        /// <param name="importPerformanceObjectivesTemplateDataIn"></param>
        /// <param name="operateType">导入后操作类型, 1 == 将导入数据作为JSON返回, 非1 == 直接入库</param>
        /// <returns></returns>
        public IActionResult ImportPerformanceObjectivesTemplateData(ImportPerformanceObjectivesTemplateData_In importPerformanceObjectivesTemplateDataIn, int operateType = 1)
        {
            IFormFile file = importPerformanceObjectivesTemplateDataIn.File;
            string fileName = file.FileName;
            string fileExtension = fileName.Substring(fileName.LastIndexOf(".") + 1);
            Workbook workbook = null;
            if (fileExtension.Equals("xlsx") || fileExtension.Equals("xls"))
            {
                workbook = new Workbook(file.OpenReadStream());
            }
            else
            {
                return new JsonResult(new
                {
                    result = "Error",
                    Msg = "请检查文件是否选择正确!"
                });
            }
            Worksheet worksheet = workbook.Worksheets[0];
            Cell yearFlagCell = worksheet.Cells["C2"];  //获取年度目标的单元格
            string yearFlagStr = yearFlagCell.StringValue;
            if (string.IsNullOrEmpty(yearFlagStr))
            {
                return new JsonResult(new
                {
                    result = "Error",
                    Msg = "无法确定年度目标的年份!"
                });
            }
            if (yearFlagStr.Length < 4)
            {
                return new JsonResult(new
                {
                    result = "Error",
                    Msg = "请检查文件是否选择正确!"
                });
            }
            string yearStr = yearFlagStr.Substring(0, 4);   //年份
            if (!int.TryParse(yearStr, out int year))
            {
                return new JsonResult(new
                {
                    result = "Error",
                    Msg = "请检查文件是否选择正确!"
                });
            }
            Cells cells = worksheet.Cells;
            var orgNumDict = DbOpe_sys_performance_objectives.Instance.GetOrganizationOrgNumDictByOrgIdList();
            ImportPerformanceObjectivesTemplateData_Out result = new();
            result.ObjectivesYear = year;
            List<UpdatePerformanceObjectivesPlus> objectivesDatas = new();
            Dictionary<string, int> importOrgNumWithRowNumDict = new();
            UpdatePerformanceObjectivesPlus temp;
            int no = 0;
            // 遍历第三行及以后的所有行
            for (int row = 3; row < cells.MaxDataRow; row++)
            {
                temp = null;
                //遍历到合计行后直接结束循环
                if (cells[row, 1].StringValue.Trim().Equals("合计") && string.IsNullOrEmpty(cells[row, 0].StringValue.Trim()))
                {
                    break;
                }
                //空行跳过,不加入数据集
                if (string.IsNullOrEmpty(cells[row, 1].StringValue.Trim()) && string.IsNullOrEmpty(cells[row, 0].StringValue.Trim()))
                {
                    continue;
                }

                temp = new();
                temp.No = (++no);
                temp.Status = EnumPerformanceObjectivesImportStatus.Success;
                // 遍历当前行的所有单元格
                for (int col = 0; col <= 13; col++)
                {
                    Cell cell = cells[row, col];
                    string cellVal = cell.StringValue;
                    if (col == 0)   //序号
                    {
                        if (string.IsNullOrEmpty(cellVal))
                        {
                            temp.OrgNum = cellVal;
                            temp.Status = EnumPerformanceObjectivesImportStatus.Fail;
                        }
                        else if (importOrgNumWithRowNumDict.ContainsKey(cellVal))
                        {
                            // 有重复行直接报错, ---- 要求返回的数据标记该行为失败
                            /*return new JsonResult(new
                            {
                                result = "Error",
                                Msg = $@"第{row + 1}行的组织机构与第{importOrgNumWithRowNumDict[cellVal]}行的组织机构重复!"
                            });*/
                            temp.OrgNum = cellVal;
                            temp.Status = EnumPerformanceObjectivesImportStatus.Fail;
                        }
                        else if (orgNumDict.TryGetValue(cellVal, out var orgInfo))
                        {
                            temp.OrgId = orgInfo.Id;
                            temp.OrgNum = orgInfo.OrgNum;
                            temp.OrgName = orgInfo.OrgName;
                            temp.OrgType = orgInfo.OrgType;
                            temp.OrgTypeName = orgInfo.OrgType.GetEnumDescription();
                            temp.ParentId = orgInfo.ParentId;
                        }
                        else
                        {
                            //组织机构序号不存在直接报错， --- 要求返回的数据标记该行为失败
                            /*return new JsonResult(new
                            {
                                result = "Error",
                                Msg = $@"未找到第{row + 1}行序号为'{cellVal}'的组织机构!"
                            });*/
                            //忽视该行 --- 要求返回的数据标记该行为失败
                            /*temp = null;
                            --no;
                            break;*/
                            temp.OrgNum = cellVal;
                            temp.Status = EnumPerformanceObjectivesImportStatus.Fail;
                        }
                    }
                    else if (col == 1)  //组织名称
                    {
                        if (string.IsNullOrEmpty(cellVal) || string.IsNullOrEmpty(temp.OrgName) || !temp.OrgName.Trim().Equals(cellVal.Trim()))
                        {
                            //组织机构序号与名称不对应直接报错， --- 要求返回的数据标记该行为失败
                            /*return new JsonResult(new
                            {
                                result = "Error",
                                Msg = $@"第{row + 1}行的组织机构名称'{cellVal}'与系统中登记的不一致!"
                            });*/
                            //忽视该行 --- 要求返回的数据标记该行为失败
                            /*temp = null;
                            --no;
                            break;*/
                            temp.OrgName = cellVal;
                            temp.Status = EnumPerformanceObjectivesImportStatus.Fail;
                        }
                    }
                    else if (col == 2)  //1月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var jan))
                        {
                            temp.Jan = jan;
                        }
                    }
                    else if (col == 3)  //2月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var feb))
                        {
                            temp.Feb = feb;
                        }
                    }
                    else if (col == 4)  //3月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var mar))
                        {
                            temp.Mar = mar;
                        }
                    }
                    else if (col == 5)  //4月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var apr))
                        {
                            temp.Apr = apr;
                        }
                    }
                    else if (col == 6)  //5月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var may))
                        {
                            temp.May = may;
                        }
                    }
                    else if (col == 7)  //6月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var jun))
                        {
                            temp.Jun = jun;
                        }
                    }
                    else if (col == 8)  //7月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var jul))
                        {
                            temp.Jul = jul;
                        }
                    }
                    else if (col == 9)  //8月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var aug))
                        {
                            temp.Aug = aug;
                        }
                    }
                    else if (col == 10) //9月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var sept))
                        {
                            temp.Sept = sept;
                        }
                    }
                    else if (col == 11) //10月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var oct))
                        {
                            temp.Oct = oct;
                        }
                    }
                    else if (col == 12) //11月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var nov))
                        {
                            temp.Nov = nov;
                        }
                    }
                    else if (col == 13) //12月份
                    {
                        if (!string.IsNullOrEmpty(cellVal) && decimal.TryParse(cellVal, out var dec))
                        {
                            temp.Dec = dec;
                        }
                    }
                }

                if (temp != null)
                {
                    var needupdate = DbOpe_sys_performance_objectives.Instance.GetPerformanceObjectivesListByYearOrgId(year, temp.OrgId);
                    if (needupdate.Count > 0)
                    {
                        temp.Id = needupdate.FirstOrDefault().Id;
                    }
                    temp.AnnualSummary = CalculateAnnualSummary(temp);
                    temp.StatusName = temp.Status.GetEnumDescription();
                    objectivesDatas.Add(temp);
                    importOrgNumWithRowNumDict[temp.OrgNum] = row + 1;    //用于去重,记录组织编号 + 行数的字典
                }
            }
            result.ObjectivesDatas = objectivesDatas;
            if (operateType != 1)
            {
                //执行直接入库流程
                UpdatePerformanceObjectives_In updatePerformanceObjectivesIn = new();
                List<UpdatePerformanceObjectives> updateObjectivesDatas = new();
                UpdatePerformanceObjectives updateTemp = null;
                updatePerformanceObjectivesIn.ObjectivesYear = result.ObjectivesYear;
                foreach (UpdatePerformanceObjectivesPlus item in result.ObjectivesDatas)
                {
                    updateTemp = new()
                    {
                        OrgId = item.OrgId,
                        Jan = item.Jan,
                        Feb = item.Feb,
                        Mar = item.Mar,
                        Apr = item.Apr,
                        May = item.May,
                        Jun = item.Jun,
                        Jul = item.Jul,
                        Aug = item.Aug,
                        Sept = item.Sept,
                        Oct = item.Oct,
                        Nov = item.Nov,
                        Dec = item.Dec,
                        AnnualSummary = item.AnnualSummary,
                    };
                    updateObjectivesDatas.Add(updateTemp);
                }
                updatePerformanceObjectivesIn.ObjectivesDatas = updateObjectivesDatas;
                UpdatePerformanceObjectives(updatePerformanceObjectivesIn, true);
                return new JsonResult(new
                {
                    result = "Success",
                    Msg = "插入数据成功!",
                });
            }
            else
            {
                return new JsonResult(new
                {
                    result = "Success",
                    Msg = result,
                });
            }
        }
    }
}