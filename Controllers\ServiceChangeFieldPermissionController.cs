using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.BLLModel.ServiceChange;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using CRM2_API.Model.ControllersViewModel;

namespace CRM2_API.Controllers
{
    [Description("服务变更字段权限控制器")]
    public class ServiceChangeFieldPermissionController : MyControllerBase
    {
        private readonly BLL_ServiceChangeFieldPermission _bll;

        public ServiceChangeFieldPermissionController(IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _bll = new BLL_ServiceChangeFieldPermission();
        }


        /// <summary>
        /// 根据合同ID动态获取可用的服务变更原因
        /// 基于主合同下所有增项合同的产品和申请状态，动态返回可用的变更原因选项
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>可用的服务变更原因列表</returns>
        [HttpPost]
        public List<ApplGtisInfo_OUT_ChangeReason> GetAvailableServiceChangeReasons(string contractId)
        {
            return BLL_ContractServiceChangeReason.Instance.GetAvailableServiceChangeReasons(contractId);
        }
        
        /// <summary>
        /// 获取申请场景的字段权限配置
        /// </summary>
        [HttpPost]
        public ServiceChangeFieldPermissionsResponse GetApplyFieldPermissions(ServiceChangeFieldPermissionsRequest request)
        {
            return _bll.GetApplyFieldPermissions(request);
        }

        /// <summary>
        /// 获取审核场景的字段权限配置
        /// </summary>
        [HttpPost]
        public ServiceChangeFieldPermissionsResponse GetAuditFieldPermissions(ServiceChangeFieldPermissionsRequest request)
        {
            return _bll.GetAuditFieldPermissions(request);
        }

        /// <summary>
        /// 测试接口：获取合同列表（无需登录验证）
        /// </summary>
        [HttpPost]
        public TestContractListResponse GetTestContractList(TestContractListRequest request)
        {
            try
            {
                // 调用BLL层获取真实合同数据
                var result = _bll.GetTestContractList(request);
                return result;
            }
            catch (Exception ex)
            {
                return new TestContractListResponse
                {
                    Success = false,
                    Message = $"获取测试合同列表失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 测试接口：获取合同产品信息（无需登录验证）
        /// </summary>
        [HttpPost]
        public TestContractProductListResponse GetTestContractProductList(TestContractProductListRequest request)
        {
            try
            {
                // 调用BLL层获取真实合同产品数据
                var result = _bll.GetTestContractProductList(request);
                return result;
            }
            catch (Exception ex)
            {
                return new TestContractProductListResponse
                {
                    Success = false,
                    Message = $"获取测试合同产品信息失败: {ex.Message}"
                };
            }
        }
    }
} 