-- 服务变更字段配置 - 特殊字段标准化脚本
-- 执行日期: 2025-02-01
-- 将特殊映射的字段key更新为符合前缀规则的标准形式

-- ================================
-- 更新GTIS特殊字段为标准前缀形式
-- ================================

-- 1. GtisAccountNum → GtisSubAccountsNum (统一命名)
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisSubAccountsNum', FieldName = 'GTIS子账号数量'
WHERE FieldKey = 'GtisAccountNum';

-- 2. GtisForbidSearchExport → GtisForbidSearchExport (保持不变，已符合规则)
-- 无需更新

-- 3. GtisWordRptPermissions → GtisWordRptPermissions (保持不变，已符合规则)
-- 无需更新

-- 4. GtisWordRptMaxTimes → GtisWordRptMaxTimes (保持不变，已符合规则)
-- 无需更新

-- 5. GtisAuthorizationNum → GtisAuthorizationNum (保持不变，已符合规则)
-- 无需更新

-- 6. GtisShareUsageNum → GtisShareUsageNum (保持不变，已符合规则)
-- 无需更新

-- 7. GtisSharePeopleNum → GtisSharePeopleNum (保持不变，已符合规则)
-- 无需更新

-- ================================
-- 更新SalesWits特殊字段为标准前缀形式
-- ================================

-- 1. SalesWitsAccountNum → SalesWitsAccountCount (统一使用AccountCount)
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'SalesWitsAccountCount', FieldName = 'SalesWits账号数量'
WHERE FieldKey = 'SalesWitsAccountNum';

-- 2. SalesWitsTokenCount → SalesWitsTokenCount (保持不变，已符合规则)
-- 无需更新

-- 3. SalesWitsEmailCount → SalesWitsEmailCount (保持不变，已符合规则)
-- 无需更新

-- 4. SalesWitsUsers → SalesWitsPhoneId (统一使用SalesWitsPhoneId)
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'SalesWitsPhoneId', FieldName = 'SalesWits使用者ID'
WHERE FieldKey = 'SalesWitsUsers';

-- 5. SalesWitsAddCredit → SalesWitsAddCredit (保持不变，已符合规则)
-- 无需更新

-- ================================
-- 更新GlobalSearch特殊字段为标准前缀形式
-- ================================

-- 1. GlobalSearchAccountNum → GlobalSearchAccountCount (统一使用AccountCount)
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GlobalSearchAccountCount', FieldName = '环球搜账号数量'
WHERE FieldKey = 'GlobalSearchAccountNum';

-- 2. GlobalSearchPaymentPeriod → GlobalSearchPaymentPeriod (保持不变，已符合规则)
-- 无需更新

-- 3. GlobalSearchAccountLevel → GlobalSearchAccountLevel (保持不变，已符合规则)
-- 无需更新

-- ================================
-- 更新College特殊字段为标准前缀形式
-- ================================

-- 1. CollegeAccountNum → CollegeAccountCount (统一使用AccountCount)
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'CollegeAccountCount', FieldName = '慧思学院账号数量'
WHERE FieldKey = 'CollegeAccountNum';

-- ================================
-- 更新通用特殊字段为标准形式
-- ================================

-- 1. CounponDetailIdList → CouponList (修正拼写错误)
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'CouponList', FieldName = '优惠券列表'
WHERE FieldKey = 'CounponDetailIdList';

-- ================================
-- 更新TriggerFields中的字段引用
-- ================================

-- 更新TriggerFields中的GtisAccountNum为GtisSubAccountsNum
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisAccountNum', 'GtisSubAccountsNum')
WHERE TriggerFields LIKE '%GtisAccountNum%';

-- 更新TriggerFields中的SalesWitsAccountNum为SalesWitsAccountCount
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'SalesWitsAccountNum', 'SalesWitsAccountCount')
WHERE TriggerFields LIKE '%SalesWitsAccountNum%';

-- 更新TriggerFields中的SalesWitsUsers为SalesWitsPhoneId
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'SalesWitsUsers', 'SalesWitsPhoneId')
WHERE TriggerFields LIKE '%SalesWitsUsers%';

-- 更新TriggerFields中的GlobalSearchAccountNum为GlobalSearchAccountCount
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GlobalSearchAccountNum', 'GlobalSearchAccountCount')
WHERE TriggerFields LIKE '%GlobalSearchAccountNum%';

-- 更新TriggerFields中的CollegeAccountNum为CollegeAccountCount
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'CollegeAccountNum', 'CollegeAccountCount')
WHERE TriggerFields LIKE '%CollegeAccountNum%';

-- 更新TriggerFields中的CounponDetailIdList为CouponList
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'CounponDetailIdList', 'CouponList')
WHERE TriggerFields LIKE '%CounponDetailIdList%';

-- ================================
-- 验证更新结果
-- ================================

SELECT '特殊字段标准化更新完成！' AS message;

-- 显示更新后的字段统计
SELECT 
    FieldKey,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT ChangeReasonEnum ORDER BY ChangeReasonEnum) as change_reasons
FROM crm_service_change_reason_field_config 
WHERE FieldKey IN (
    'GtisSubAccountsNum', 'SalesWitsAccountCount', 'SalesWitsPhoneId', 'GlobalSearchAccountCount', 
    'CollegeAccountCount', 'CouponList'
)
GROUP BY FieldKey
ORDER BY FieldKey;