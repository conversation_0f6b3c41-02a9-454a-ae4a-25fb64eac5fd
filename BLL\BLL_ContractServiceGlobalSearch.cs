﻿
using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.Controllers;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.EMMA;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.OpenXmlFormats.Dml;
using SqlSugar;
using System.IO;
using System.Linq;
using System.Security.Policy;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;

namespace CRM2_API.BLL
{
    public class BLL_ContractServiceGlobalSearch : BaseBLL<BLL_ContractServiceGlobalSearch>
    {


        /// <summary>
        /// 合同服务信息环球搜申请信息--初审登记
        /// </summary>
        /// <param name="audit_In"></param>
        public void AuditContractProductServiceInfoGlobalSearchAppl(AuditContractProductServiceInfoGlobalSearchAppl_In audit_In)
        {
            //获取申请的数据
            var apply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.QueryByPrimaryKey(audit_In.ProductServiceInfoGlobalSearchApplId);
            var contractInfo = DbOpe_crm_contract.Instance.GetContractInfoByContractNum(audit_In.ContractNum);
            #region 判断是否可以进行初审登记操作
            /*获取当前申请绑定的服务数据，如果没有绑定服务数据&&申请处在提交状态--可以登记; 如果绑定了服务数据且服务数据是被驳回状态--可以登记; 其余情况不可进行登记*/
            bool couldRegistered = false;
            var curService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetData(g => g.ProductServiceInfoGlobalSearchApplId == apply.Id && g.IsHistory == false);
            if (curService == null && apply.State == EnumProcessStatus.Submit.ToInt())
                couldRegistered = true;
            else if (curService != null && (curService.State == EnumContractServiceState.RETURNED || curService.State == EnumContractServiceState.TO_BE_OPENED))
                couldRegistered = true;
            if (!couldRegistered)
                throw new ApiException("当前申请无法进行初审操作");
            #endregion
            //如果登记人员执行拒绝操作，修改申请数据apply的状态为拒绝，如果是服务变更数据，对已经锁定的旧数据放开锁定
            if (!audit_In.State)
            {
                //放开被变更的服务数据可操作
                var curOpenService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetData(g => (g.State == EnumContractServiceState.VALID || g.State == EnumContractServiceState.OUT) && g.ContractProductInfoId == apply.ContractProductInfoId);
                if (curOpenService != null)
                {
                    var curOpenAppl = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetDataById(curOpenService.ProductServiceInfoGlobalSearchApplId);
                    curOpenAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                    DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateQueue(curOpenAppl);
                }
                apply.State = EnumProcessStatus.Refuse.ToInt();
                apply.ReviewerDate = DateTime.Now;
                apply.ReviewerId = UserId;
                apply.Feedback = audit_In.ReviewerRemark;
                if (apply.ProcessingType == (int)EnumProcessingType.Change)
                    //拒绝后该数据状态为无效
                    apply.IsInvalid = (int)EnumIsInvalid.Invalid;
                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = apply.CreateUser;
                message.MessageTypeToId = apply.Id;
                message.MessagemMainAboutDes = contractInfo.ContractName;
                message.LocalFeedBack = apply.Feedback;
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.SearchService, EnumMessageStateInfo.Refus, apply.ContractId);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);
            }
            else
            {//通过处理1--验证状态
                if (string.IsNullOrEmpty(audit_In.ContractNum))
                    throw new ApiException("未到账合同不能进行初审操作");
                //判断当前合同是否已经开通GTIS服务
                //var gtisCase = DbOpe_crm_contract_serviceinfo_gtis.Instance.CheckHasUnValidGTISService(apply.ContractId);
                var gtisCase = DbOpe_crm_contract_productinfo.Instance.CheckHasUnValidGTISService(apply.ContractId);
                if (gtisCase == EnumContractGtisProductServiceState.ExistAndNotopen)
                    throw new ApiException("请先开通GTIS正式服务");
                if (gtisCase == EnumContractGtisProductServiceState.Opened)//如果gtis已经开通,获取开通的子账号数量
                {
                    var gtisSubAccountNum = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetSubAccountNum(apply.ContractId);
                    if (audit_In.SubAccountsNum > gtisSubAccountNum)
                        throw new ApiException("环球搜账号数量不能超过Gtis账号数量");
                }
            }
            apply.Remark4List = audit_In.ReviewerRemark;
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateQueue(apply);
            var now = DateTime.Now;
            //创建服务数据
            var servGlobalSearchInfo = audit_In.MappingTo<Db_crm_contract_serviceinfo_globalsearch>();
            servGlobalSearchInfo.Id = Guid.NewGuid().ToString();
            servGlobalSearchInfo.ContractId = apply.ContractId;
            servGlobalSearchInfo.ProductId = apply.ProductId;
            servGlobalSearchInfo.ContractProductInfoId = apply.ContractProductInfoId;
            servGlobalSearchInfo.ServiceCycleStart = audit_In.ServiceCycleStart;
            servGlobalSearchInfo.ServiceCycleEnd = audit_In.ServiceCycleEnd;
            servGlobalSearchInfo.ServiceMonth = audit_In.ServiceMonth;
            servGlobalSearchInfo.State = audit_In.State ? EnumContractServiceState.TO_BE_REVIEW : EnumContractServiceState.REFUSE;
            servGlobalSearchInfo.IsChanged = false;
            servGlobalSearchInfo.Deleted = false;
            servGlobalSearchInfo.CreateDate = now;
            servGlobalSearchInfo.CreateUser = UserId;
            servGlobalSearchInfo.IsHistory = false;//如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
            servGlobalSearchInfo.RegisteredId = UserId;
            servGlobalSearchInfo.RegisteredTime = now;
            servGlobalSearchInfo.RegisteredRemark = audit_In.ReviewerRemark;
            servGlobalSearchInfo.ProcessingType = apply.ProcessingType;
            servGlobalSearchInfo.ExecuteServiceCycleStart = audit_In.ExecuteServiceCycleStart;
            servGlobalSearchInfo.ExecuteServiceCycleEnd = audit_In.ExecuteServiceCycleEnd;
            servGlobalSearchInfo.ExecuteServiceMonth = audit_In.ExecuteServiceMonth;
            if (audit_In.State)
            {//通过处理2--保存相关内容

                //保存gtis和环球搜码对应关系
                audit_In.GlobalSearchUserList.ForEach(item => item.GlobalSearchServiceId = servGlobalSearchInfo.Id);
                DbOpe_crm_contract_serviceinfo_globalsearch_user_gtisuser.Instance.InsertListDataQueue(audit_In.GlobalSearchUserList);
                //判断是否是续约合同
                if (contractInfo.ContractType == (int)EnumContractType.ReNew && servGlobalSearchInfo.ProcessingType == EnumProcessingType.Add.ToInt())
                {//续约合同需要保存OldContractNum
                    #region 直接保存申请数据中的RenewContractNum,不再根据OldFirstParty查找
                    //找最近的一份开通、即将过期、过期状态服务数据的客户编码
                    /*servGlobalSearchInfo.OldContractNum = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetOldServiceInfo(contractInfo.Id, contractInfo.FirstParty)?.ContractNum;*/
                    /*if (string.IsNullOrEmpty(apply.OldFirstParty))
                        throw new Exception("未找到指定的续约公司，请拒绝后重新申请补全信息");
                    servGlobalSearchInfo.OldContractNum = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetLastSameFirstPartyServiceContractInfo(apply.OldFirstParty, contractInfo.Id)?.ContractNum;*/
                    #endregion
                    servGlobalSearchInfo.OldContractNum = apply.RenewContractNum;
                }
                if (apply.ProcessingType == EnumProcessingType.Change.ToInt())
                    servGlobalSearchInfo.HistoryId = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.CheckContractHasGlobalSearchServiceByContractId(apply.ContractId)?.Id;
                //绑定到账信息
                if (audit_In.ReceiptRegisterIds != null && audit_In.ReceiptRegisterIds.Count > 0)
                {
                    DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptDataQueue(audit_In.ReceiptRegisterIds, servGlobalSearchInfo.Id, EnumProductType.Global);
                    var rrList = DbOpe_crm_contract_receiptregister.Instance.GetByIds(audit_In.ReceiptRegisterIds);
                    if (rrList != null)
                        servGlobalSearchInfo.SettlementCount = rrList.Sum(e => e.GlobalSearchUnit == null ? 0 : e.GlobalSearchUnit.Value);
                    else
                        servGlobalSearchInfo.SettlementCount = 0;
                }
            }
            //判断是否是被驳回数据
            if (curService != null)
            {
                //如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
                curService.IsHistory = true;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateQueue(curService);
                //被驳回服务重新初审通过时，删除被驳回服务的到账绑定关系
                DbOpe_crm_contract_receiptregister_service.Instance.DeleteReturnServiceLinkDataQueue(curService.Id);
            }
            //插入服务数据队列
            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.InsertQueue(servGlobalSearchInfo);
            //开通的workflow记录
            var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(apply.ContractId);
            string dataState = String.Empty;
            if (audit_In.State)
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_globalsearch, Db_crm_contract>("环球搜服务审批流程", apply.Id, servGlobalSearchInfo, contract, servGlobalSearchInfo.ReviewerRemark, EnumContractServiceOpenState.ToBeReview.GetEnumDescription(), "初审");
            else
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_globalsearch, Db_crm_contract>("环球搜服务审批流程", apply.Id, servGlobalSearchInfo, contract, apply.ApplicantId, apply.Feedback, EnumContractServiceOpenState.Refuse.GetEnumDescription(), "初审");
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.SaveQueues();
        }

        /// <summary>
        /// 环球搜复核开通
        /// </summary>
        /// <param name="review_In"></param>
        public void ReviewContractProductServiceInfoGlobalSearchAppl(ReviewContractProductServiceInfoGlobalSearchAppl_In review_In)
        {
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.TransDeal(() =>
            {
                //根据 ProductServiceInfoGlobalSearchApplId 获取申请表信息
                var apply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.QueryByPrimaryKey(review_In.ProductServiceInfoGlobalSearchApplId);
                //根据 ProductServiceInfoGlobalSearchApplId 获取服务表信息
                var curService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetData(g => g.ProductServiceInfoGlobalSearchApplId == apply.Id && g.IsHistory == false);
                curService.ServiceCycleStart = review_In.ServiceCycleStart;
                curService.ServiceCycleEnd = review_In.ServiceCycleEnd;
                #region 判断是否可以进行复核操作
                /*如果存在绑定服务数据&&服务数据为待复核状态--可以复核;其余情况不可进行复核*/
                if (!(curService != null && curService.State == EnumContractServiceState.TO_BE_REVIEW))
                    throw new ApiException("当前申请无法进行复核操作");
                #endregion
                var now = DateTime.Now;
                //服务变更或续约记录变化类型
                var globalSearchChangeItem = new List<EnumGlobalSearchModifyType>();
                //驳回
                if (!review_In.State)
                {
                    curService.State = EnumContractServiceState.RETURNED;
                    apply.Remark4List = review_In.FeedBack;
                }

                //开通
                else
                {
                    //合同信息验证
                    var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);

                    if (contractInfo == null)
                        throw new ApiException("未找到合同信息");
                    if (string.IsNullOrEmpty(contractInfo.ContractNum))
                        throw new ApiException("未到账合同不能开通服务产品");
                    if (contractInfo.ContractAmount == null && contractInfo.FCContractAmount == null)
                        throw new ApiException("合同金额不可为空");
                    //验证合同是否有GTIS产品且服务未开通，这种情况需要先开通GTIS后才能开通环球搜
                    //var gtisCase = DbOpe_crm_contract_serviceinfo_gtis.Instance.CheckHasUnValidGTISService(apply.ContractId);
                    var gtisCase = DbOpe_crm_contract_productinfo.Instance.CheckHasUnValidGTISService(apply.ContractId);
                    if (gtisCase == EnumContractGtisProductServiceState.ExistAndNotopen)
                        throw new ApiException("请先开通GTIS正式服务");
                    if (contractInfo.ContractType == (int)EnumContractType.ReNew && apply.ProcessingType == EnumProcessingType.Add.ToInt() && string.IsNullOrEmpty(apply.OldFirstParty))//续约合同且服务申请没有指定甲方公司，需要退回重新申请
                        throw new Exception("未找到指定的续约公司，请驳回拒绝后重新申请补全信息");
                    //同步gtis数据
                    List<Db_crm_contract_serviceinfo_gtis_user> userDatas = new List<Db_crm_contract_serviceinfo_gtis_user>();
                    var gtisContractNum = string.Empty;
                    BLL_ContractService.Instance.SynchroGtisUserData(contractInfo.ContractNum, ref userDatas, ref gtisContractNum);
                    //处理申请信息
                    apply.State = EnumProcessStatus.Pass.ToInt();
                    apply.ReviewerDate = now;
                    apply.ReviewerId = UserId;
                    apply.UpdateDate = now;
                    apply.UpdateUser = UserId;
                    apply.Feedback = review_In.FeedBack;
                    apply.Remark4List = review_In.ReviewerRemark;
                    //服务状态置位生效
                    curService.State = EnumContractServiceState.VALID;
                    #region 环球搜没有变更
                    //如果是服务变更，对原开通数据进行处理
                    /*if (servDBInfo.ProcessingType == EnumProcessingType.Change.ToInt())
                     {//服务申请修改 需要找到当前的服务记录数据(根据ContractProductInfoId)，修改isChanged=true，补充ChangedId,当前数据补充HistoryId
                         //获取原服务数据进行修改
                         var originalServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetServiceInfoByConProInfoId(apply.ContractProductInfoId);
                         originalServe.IsChanged = true;
                         originalServe.ChangedId = servDBInfo.Id;
                         originalServe.UpdateDate = DateTime.Now;
                         originalServe.UpdateUser = UserId;
                         originalServe.State = EnumContractServiceState.INVALID;
                         DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateQueue(originalServe);
                         //变更服务补充HistoryId
                         servDBInfo.HistoryId = originalServe.Id;
                     }*/
                    #endregion
                    //环球搜主账号code
                    //string priAccountNumber = string.Empty;
                    //List<string> subAccList = new List<string>();
                    var gtisUserList = new List<Db_crm_contract_serviceinfo_gtis_user>();
                    /*合同不包含gtis产品，需要新建gtis账号*/
                    if (gtisCase == EnumContractGtisProductServiceState.Inexistence)
                    {
                        #region 对接参数CreateUserInfo或AddUserInfo,调用Gtis系统创建账户接口需要的参数,按照续约的类(BM_RenewalHqsCode_NoUserInfo)创建,重新生成账号时映射为新生成的类(BM_SaveHqsCode_UserInfo)
                        var createUserInfo = new BM_RenewalHqsCode_NoUserInfo();
                        createUserInfo.SvCode = contractInfo.ContractNum;
                        createUserInfo.Company = contractInfo.FirstPartyName;
                        createUserInfo.Addr = string.Join("/", new string[] { contractInfo.CountryName, contractInfo.ProvinceName, contractInfo.CityName, contractInfo.Address });
                        createUserInfo.UserName = contractInfo.Contacts;
                        createUserInfo.MaxChildrens = curService.SubAccountsNum.Value;
                        createUserInfo.StartServerDate = curService.ServiceCycleStart.Value;// audit_In.ServiceCycleStart;
                        createUserInfo.EndServerDate = curService.ServiceCycleEnd.Value;// audit_In.ServiceCycleEnd,;
                        createUserInfo.ContractNum = (contractInfo.ContractAmount != null) ? contractInfo.ContractAmount.ToString() : contractInfo.FCContractAmount.ToString();
                        //createUserInfo.PayDate = contractInfo.ArrivalDate;
                        createUserInfo.PayDate = string.IsNullOrEmpty(contractInfo.ArrivalDate) ? "" : DateTime.Parse(contractInfo.ArrivalDate).ToString("yyyyMM");
                        createUserInfo.Tel = string.IsNullOrEmpty(contractInfo.ContactWay) ? contractInfo.Telephone : contractInfo.ContactWay;
                        createUserInfo.crm_country = contractInfo.CountryName;
                        createUserInfo.crm_city = contractInfo.CityName;
                        createUserInfo.Email = contractInfo.Email;
                        #endregion
                        //声明公共参数
                        var retList = new List<BM_AddGtisUserRetModel>();
                        var crmAddGtisUserList = new List<Db_crm_contract_serviceinfo_gtis_user>();
                        /*没有账号或重新生成账号*/
                        if (curService.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake)
                        {
                            //对接参数：账号CrmId与环球搜码对应，第一个是主账号
                            var wholeUserInfo = new List<BM_SaveHqsCode_UserInfo>();
                            for (int i = 0; i <= curService.SubAccountsNum; i++)
                            {
                                //声明gtis账户user数据,第一个生成的是主账号，其余是子账号
                                var crmGtisUser = new Db_crm_contract_serviceinfo_gtis_user()
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    AccountType = i == 0 ? EnumGtisAccountType.Main.ToInt() : EnumGtisAccountType.Sub.ToInt(),
                                    OpeningStatus = (int)EnumGtisUserOpeningStatus.INPROCESS,
                                    StartDate = curService.ServiceCycleStart,
                                    EndDate = curService.ServiceCycleEnd,
                                    IsProcessed = (int)EnumGtisServiceIsProcess.Not,
                                    ContractServiceInfoGlobalSearchId = curService.Id,
                                };
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertQueue(crmGtisUser);
                                crmAddGtisUserList.Add(crmGtisUser);
                                //填充对接参数 newAllUserInfo
                                wholeUserInfo.Add(new BM_SaveHqsCode_UserInfo { CrmId = crmGtisUser.Id });
                            }
                            //调用Gtis接口创建Gtis账号前，执行sql队列
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.SaveQueues();
                            var newParam = new BM_SaveHqsCode
                            {
                                isNoUser = true,
                                CreateUserInfo = createUserInfo.MappingTo<BM_SaveHqsCode_NoUserInfo>(),
                                AllUserInfo = wholeUserInfo,
                                OtherInfo = review_In.ReviewerRemark,
                            };
                            //调用Gtis接口，创建Gtis账号
                            retList = BLL_GtisOpe.Instance.SaveHqsCode(newParam).Result;
                            #region 与使用原有账号合并处理，2024.3.15注释
                            /*//根据Gtis返回的账号信息，回填crm中gtis_user表中的UserId,AccountNumber和Password
                            retList.ForEach(ret =>
                            {

                                var crmAddGtisUser = crmAddGtisUserList.Find(e => e.Id == ret.CrmId);
                                crmAddGtisUser.AccountNumber = ret.Uid;
                                crmAddGtisUser.UserId = ret.SysUserID;
                                crmAddGtisUser.PassWord = ret.Pwd;
                                gtisUserList.Add(crmAddGtisUser);

                                *//*if (ret.CrmId == newPriUser.Id)
                                {
                                    newPriUser.AccountNumber = ret.Uid;
                                    gtisUserList.Add(newPriUser);
                                }
                                else
                                {
                                    var subUser = subUserList.Find(e => e.Id == ret.CrmId);
                                    subUser.AccountNumber = ret.Uid;
                                    gtisUserList.Add(subUser);
                                }*//*
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchGtisUser(ret, UserId);
                            });
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.SaveQueues();*/
                            #endregion
                        }
                        /*使用原有账号*/
                        else if (curService.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate)
                        {
                            if (apply.ProcessingType == EnumProcessingType.Change.ToInt())
                            {//服务变更
                                createUserInfo.SvCodeOld = curService.ContractNum;
                                createUserInfo.ChangeId = curService.ContractId;
                                createUserInfo.IsChange = true;
                            }
                            else
                            {//续约
                             //补充旧合同的客户编码
                                createUserInfo.SvCodeOld = curService.OldContractNum;
                                createUserInfo.ChangeId = DbOpe_crm_contract.Instance.GetContractInfoByContractNum(createUserInfo.SvCodeOld).Id;
                                createUserInfo.IsChange = false;
                            }
                            createUserInfo.OtherInfo = BLL_GtisOpe.Instance.GetCompanyOtherInfo(createUserInfo.SvCodeOld.ToString()).Result + ";" + review_In.ReviewerRemark;
                            #region 获取续约合同与老合同的账号差异,得到待删除的账号及需要新增的账号数量
                            //1.查询原有服务的账号及环球搜码 OldList
                            var oldList = DbOpe_crm_contract_serviceinfo_globalsearch_user_gtisuser.Instance.GetOldDataByContractNum(createUserInfo.SvCodeOld);
                            //2.查询现有服务的初审保存的账号及环球搜码 CurrentList
                            var curList = DbOpe_crm_contract_serviceinfo_globalsearch_user_gtisuser.Instance.GetDataByServiceId(curService.Id);
                            //3.取Old和Cur的差集，得到的内容是删除的账号
                            var toDisableUserList = oldList.ExceptBy(curList.Select(e => e.GtisAccountNumber), e => e.GtisAccountNumber).ToList();
                            //4.curList中GtisSysUserId是空的数量 是新增账号的数量
                            var toAddAccountNum = curList.Where(e => string.IsNullOrEmpty(e.GtisSysUserId)).ToList().Count();//curService.PrimaryAccountsNum + curService.SubAccountsNum - curList.Count;
                            #endregion

                            //现有服务保存的账号需要在gtis_user表中重新添加数据
                            curList.Where(e => !string.IsNullOrEmpty(e.GtisSysUserId)).ToList().ForEach(curUser =>
                            {
                                var crmCurGtisUser = new Db_crm_contract_serviceinfo_gtis_user();
                                crmCurGtisUser.Id = Guid.NewGuid().ToString();
                                crmCurGtisUser.AccountType = curUser.GtisAccountType;
                                crmCurGtisUser.AccountNumber = curUser.GtisAccountNumber;
                                crmCurGtisUser.PassWord = curUser.GtisUserPassword;
                                crmCurGtisUser.UserId = curUser.GtisSysUserId;
                                crmCurGtisUser.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                                crmCurGtisUser.StartDate = curService.ServiceCycleStart;
                                crmCurGtisUser.EndDate = curService.ServiceCycleEnd;
                                crmCurGtisUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                                crmCurGtisUser.ContractServiceInfoGlobalSearchId = curService.Id;
                                crmCurGtisUser.GlobalSearchCode = curUser.GlobalSearchNumber;
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertQueue(crmCurGtisUser);
                                gtisUserList.Add(crmCurGtisUser);
                            });

                            //声明删除gtis账号的List对象，如果没有删除则为空
                            var disableUserList = new List<BM_GtisOpe_RenewalContact.BM_GtisOpe_DisabledUser>();
                            if (toDisableUserList.Count > 0)
                                disableUserList = toDisableUserList.Select(e => new BM_GtisOpe_RenewalContact.BM_GtisOpe_DisabledUser { SysUserID = e.GtisSysUserId }).ToList();
                            //声明新增gtis账号的List对象，如果没有新增则为空
                            var addUserList = new List<BM_AddGtisUserRetModel>();
                            /*如果存在新增账号,在crm中创建gtisuser,并调用Gtis系统接口创建对应子账号*/
                            if (toAddAccountNum > 0)
                            {
                                for (int i = 0; i < toAddAccountNum; i++)
                                {
                                    //声明gtis账户user数据
                                    var user = new Db_crm_contract_serviceinfo_gtis_user()
                                    {
                                        Id = Guid.NewGuid().ToString(),
                                        AccountType = EnumGtisAccountType.Sub.ToInt(),
                                        OpeningStatus = (int)EnumGtisUserOpeningStatus.INPROCESS,
                                        StartDate = curService.ServiceCycleStart,
                                        EndDate = curService.ServiceCycleEnd,
                                        IsProcessed = (int)EnumGtisServiceIsProcess.Not,
                                        ContractServiceInfoGlobalSearchId = curService.Id,
                                    };
                                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertQueue(user);
                                    //gtisUserList.Add(user);
                                    crmAddGtisUserList.Add(user);
                                    //填充新增账号的对接参数
                                    addUserList.Add(new BM_AddGtisUserRetModel { CrmId = user.Id });
                                }
                            }
                            //在调用gtis系统接口续约或新建账号之前，执行sql队列
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.SaveQueues();
                            //整理gtis接口调用参数
                            var newParam = new BM_RenewalHqsCode
                            {
                                CreateUserInfo = createUserInfo,
                                AddUserInfo = addUserList,
                                DisableUserInfo = disableUserList,
                            };
                            //调用gtis接口续约或新建账号，得到新建账号的其他信息
                            retList = BLL_GtisOpe.Instance.RenewalHqsCode(newParam).Result;
                        }
                        //根据Gtis返回的账号信息，回填crm中gtis_user表中的UserId,AccountNumber和Password
                        retList.ForEach(ret =>
                        {
                            var crmAddGtisUser = crmAddGtisUserList.Find(e => e.Id == ret.CrmId);
                            crmAddGtisUser.AccountNumber = ret.Uid;
                            crmAddGtisUser.UserId = ret.SysUserID;
                            crmAddGtisUser.PassWord = ret.Pwd;
                            gtisUserList.Add(crmAddGtisUser);

                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchGtisUser(ret, UserId);
                        //DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertQueue(crmCurGtisUser);
                        });
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.SaveQueues();
                    }
                    //声明环球搜主账号编码
                    string priHqsCode = string.Empty;
                    var allUserInfo = new List<BM_SaveHqsCode_UserInfo>();
                    //如果合同存在Gtis服务，获取Gtis的账号，并更新这部分账号的GlobalSearchServiceId
                    if (gtisUserList.Count == 0)
                    {
                        //当前合同开通的Gtis服务账号
                        gtisUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetUserListByContractId(apply.ContractId);
                        if (curService.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate)//使用原有账号
                        {
                            //获取初审时绑定的gtisuser和globalCode的关系
                            var curGtisGlobalSearchUserList = DbOpe_crm_contract_serviceinfo_globalsearch_user_gtisuser.Instance.GetDataByServiceId(curService.Id);
                            //根据初审时的绑定关系，更新gtis_user中的GlobalSearchServiceId和GlobalSearchCode
                            gtisUserList.ForEach(gtisUser =>
                            {
                                gtisUser.GlobalSearchCode = curGtisGlobalSearchUserList.Where(e => e.CrmGtisUserId == gtisUser.Id).First()?.GlobalSearchNumber;
                                gtisUser.ContractServiceInfoGlobalSearchId = curService.Id;
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchInfo(gtisUser, UserId);
                            });
                        }
                    }
                    //验证环球搜开通账号数量是否超过了gtis账号数量
                    if (curService.PrimaryAccountsNum + curService.SubAccountsNum > gtisUserList.Count())
                        throw new ApiException("环球搜账号数量不能超过Gtis账号数量");
                    //获取gtis主账号
                    if (gtisUserList.Where(e => e.AccountType == EnumGtisAccountType.Main.ToInt()).Count() == 0)
                        throw new ApiException("Gtis主账号缺失或停用，无法开通环球搜");
                    var priGtisUser = gtisUserList.Where(e => e.AccountType == EnumGtisAccountType.Main.ToInt()).ToList().First();
                    //环球搜账号列表（和gtis交互接口用）
                    var hqsCodeList = new List<string>();
                    /*没有账号或重新生成账号*/
                    if (curService.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake || curService.AccountGenerationMethod == null)
                    {
                        //根据gtis主账号生成环球搜主账号，返回主账号code
                        AddGlobalSearchPrimaryUser(curService.Id, apply.Id, contractInfo.ContractNum, priGtisUser.AccountNumber, curService.ServiceCycleStart, curService.ServiceCycleEnd, curService.SettlementLevel, curService.SettlementMonth, review_In.GlobalSearchRemark, out priHqsCode);
                        if (string.IsNullOrEmpty(priHqsCode))
                        {
                            //补账号标记
                            //异常
                            throw new ApiException("");
                        }

                        //创建主账号与gtis交互对象
                        var priUser = new BM_SaveHqsCode_UserInfo();
                        priUser.CrmId = priGtisUser.Id;
                        priUser.Uid = priGtisUser.AccountNumber;
                        priUser.HqsCode = priHqsCode;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchCode(priGtisUser.Id, priHqsCode, UserId, curService.Id);
                        allUserInfo.Add(priUser);

                        hqsCodeList.Add(priHqsCode);
                        //获取gtis子账号列表
                        var subGtisUserList = gtisUserList.Where(e => e.AccountType == EnumGtisAccountType.Sub.ToInt()).OrderBy(e => e.CreateDate).ToList();
                        var subHqsCodeList = new List<string>();
                        for (int i = 1; i <= subGtisUserList.Count; i++)
                        {
                            var subGtisUser = subGtisUserList[i - 1];
                            //如果环球搜子账号数量为0 ，都绑定主账号
                            if (curService.SubAccountsNum == 0)
                            {
                                var subUser = new BM_SaveHqsCode_UserInfo();
                                subUser.CrmId = subGtisUser.Id;
                                subUser.Uid = subGtisUser.AccountNumber;
                                subUser.HqsCode = priHqsCode;
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchCode(subUser.CrmId, subUser.HqsCode, UserId, curService.Id);
                                allUserInfo.Add(subUser);
                            }

                            else if (i <= curService.SubAccountsNum)
                            {
                                string subHqsCode = string.Empty;
                                AddGlobalSearchSubUser(curService.Id, apply.Id, priHqsCode, contractInfo.ContractNum, subGtisUser.AccountNumber, curService.ServiceCycleStart, curService.ServiceCycleEnd, review_In.GlobalSearchRemark, out subHqsCode);
                                var subUser = new BM_SaveHqsCode_UserInfo();
                                subUser.CrmId = subGtisUser.Id;
                                subUser.Uid = subGtisUser.AccountNumber;
                                subUser.HqsCode = subHqsCode;
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchCode(subUser.CrmId, subUser.HqsCode, UserId, curService.Id);
                                subHqsCodeList.Add(subHqsCode);
                                allUserInfo.Add(subUser);
                            }
                            else if (i > curService.SubAccountsNum)
                            {
                                string subHqsCode = string.Empty;
                                int index = i;
                                while (index > curService.SubAccountsNum)
                                {
                                    index -= curService.SubAccountsNum.Value;
                                }
                                var subUser = new BM_SaveHqsCode_UserInfo();
                                subUser.CrmId = subGtisUser.Id;
                                subUser.Uid = subGtisUser.AccountNumber;
                                subUser.HqsCode = subHqsCodeList[index - 1];
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchCode(subUser.CrmId, subUser.HqsCode, UserId, curService.Id);
                                allUserInfo.Add(subUser);
                            }
                        }
                        hqsCodeList.AddRange(subHqsCodeList);
                    }
                    /*使用原有账号*/
                    else if (curService.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate)
                    {
                        #region 续约/变更账号：主账号续约，子账号自动续约
                        //记录已经创建Db_crm_contract_serviceinfo_globalsearch_user数据的环球搜码
                        var CopiedGlobalSearchCode = new List<string>();
                        //获取主账号的环球搜code
                        priHqsCode = priGtisUser.GlobalSearchCode;
                        //创建主账号与gtis交互对象
                        allUserInfo.Add(new BM_SaveHqsCode_UserInfo()
                        {
                            CrmId = priGtisUser.Id,
                            Uid = priGtisUser.AccountNumber,
                            HqsCode = priHqsCode,
                        });
                        //环球搜主账号操作续约
                        UpdateGlobalSearchUser(curService.Id, review_In.ProductServiceInfoGlobalSearchApplId, priGtisUser.GlobalSearchCode, curService.ContractNum, priGtisUser.AccountNumber, curService.ServiceCycleStart, curService.ServiceCycleEnd, curService.SettlementLevel, curService.SettlementMonth, review_In.GlobalSearchRemark);
                        CopiedGlobalSearchCode.Add(priHqsCode);

                        //获取有环球搜码的子账号
                        var subGtisUserListWithCode = gtisUserList.Where(e => e.AccountType == EnumGtisAccountType.Sub.ToInt() && !string.IsNullOrEmpty(e.GlobalSearchCode)).ToList();
                        //对环球搜子账号码进行续约操作
                        subGtisUserListWithCode.ForEach(user =>
                        {
                            allUserInfo.Add(new BM_SaveHqsCode_UserInfo()
                            {
                                CrmId = user.Id,
                                Uid = user.AccountNumber,
                                HqsCode = user.GlobalSearchCode,
                            });
                        //如果记录的已创建 globalsearch_user 表数据不包含当前环球搜码，向插入globalsearch_user插入数据，并记录当前环球搜码
                            if (!CopiedGlobalSearchCode.Contains(user.GlobalSearchCode))
                            {
                            //更新crm系统中环球搜账号
                                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertData(new Db_crm_contract_serviceinfo_globalsearch_user()
                                {
                                    ContractServiceInfoGlobalSearchId = curService.Id,
                                    ProductServiceInfoGlobalSearchApplId = review_In.ProductServiceInfoGlobalSearchApplId,
                                    AccountNumber = user.GlobalSearchCode,
                                    AccountType = EnumContractServiceGlobalSearchAccountType.SubAccount,
                                    OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal,
                                    StartDate = curService.ServiceCycleStart,
                                    EndDate = curService.ServiceCycleEnd,
                                });
                                CopiedGlobalSearchCode.Add(user.GlobalSearchCode);
                            }
                        });
                        #endregion
                        #region 新增子账号
                        //获取没有环球搜码的账号，生成新环球搜码
                        var subGtisUserListWithoutCode = gtisUserList.Where(e => e.AccountType == EnumGtisAccountType.Sub.ToInt() && string.IsNullOrEmpty(e.GlobalSearchCode)).ToList();
                        //声明新生成的环球搜码集合
                        var subHqsCodeList = new List<string>();
                        //计算需要新生成的环球搜码的数量
                        var haveBindCodeList = subGtisUserListWithCode.Select(e => e.GlobalSearchCode).Distinct().ToList();
                        haveBindCodeList.Remove(priHqsCode);
                        var toAddSubAccountsNum = curService.SubAccountsNum - haveBindCodeList.Count;
                        //循环没有绑定环球搜码的gtis账号，对应生成环球搜码，并绑定到gtis账号下
                        for (int i = 1; i <= subGtisUserListWithoutCode.Count; i++)
                        {
                            //当前待绑定gtis账号数据
                            var subGtisUser = subGtisUserListWithoutCode[i - 1];
                            //如果当前循环索引小于等于待生成数量，调用环球搜接口生成新的环球搜码，并绑定gtis账号
                            if (i <= toAddSubAccountsNum)
                            {
                                string subHqsCode = string.Empty;
                                AddGlobalSearchSubUser(curService.Id, apply.Id, priHqsCode, contractInfo.ContractNum, subGtisUser.AccountNumber, curService.ServiceCycleStart, curService.ServiceCycleEnd, review_In.GlobalSearchRemark, out subHqsCode);
                                var subUser = new BM_SaveHqsCode_UserInfo();
                                subUser.CrmId = subGtisUser.Id;
                                subUser.Uid = subGtisUser.AccountNumber;
                                subUser.HqsCode = subHqsCode;
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchCode(subGtisUser.Id, subHqsCode, UserId, curService.Id);
                                subHqsCodeList.Add(subHqsCode);
                                allUserInfo.Add(subUser);
                            }
                            //如果当前循环索引大于待生成数量，不再新生成环球搜码，从已生成的环球搜码集合subHqsCodeList中根据索引值提取环球搜码，绑定到gtis账户上
                            else if (i > toAddSubAccountsNum)
                            {
                                string subHqsCode = string.Empty;
                                int index = i;
                                while (index > toAddSubAccountsNum)
                                {
                                    index -= toAddSubAccountsNum.Value;
                                }
                                var subUser = new BM_SaveHqsCode_UserInfo();
                                subUser.CrmId = subGtisUser.Id;
                                subUser.Uid = subGtisUser.AccountNumber;
                                subUser.HqsCode = subHqsCodeList[index - 1];
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchCode(subUser.CrmId, subUser.HqsCode, UserId, curService.Id);
                                allUserInfo.Add(subUser);
                            }
                        }
                        #endregion
                        hqsCodeList.AddRange(CopiedGlobalSearchCode);
                        hqsCodeList.AddRange(subHqsCodeList);
                    }
                    #region 应对生成过程中连接中断的情况，暂时注销不用
                    /*//判断当前申请是否操作过环球搜开通，已经存在环球搜的账号
                    var globalsearch_Users = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.CheckHasCodeByApplyId(apply.Id);
                    //没有生成过主账号，就表示没有环球搜账号，需要重新生成全部账号
                    if (!globalsearch_Users.Any(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount))
                    {
                        //获取Gtis的账号
                        var gtisUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetUserListByContractId(apply.ContractId);
                        //验证环球搜开通账号数量是否超过了gtis账号数量
                        if (curService.PrimaryAccountsNum + curService.SubAccountsNum > gtisUserList.Count())
                            throw new ApiException("环球搜账号数量不能超过Gtis账号数量");
                        //获取gtis主账号
                        var priGtisUser = gtisUserList.Where(e => e.AccountType == EnumGtisAccountType.Main.ToInt()).ToList().First();
                        //根据gtis主账号生成环球搜主账号，返回主账号code
                        AddGlobalSearchPrimaryUser(curService.Id, apply.Id, contractInfo.ContractNum, priGtisUser.AccountNumber, curService.ServiceCycleStart, curService.ServiceCycleEnd, out priHqsCode);
                        //创建主账号与gtis交互对象
                        var priUser = new BM_SaveHqsCode_UserInfo();
                        priUser.CrmId = priGtisUser.Id;
                        priUser.Uid = priGtisUser.AccountNumber;
                        priUser.HqsCode = priHqsCode;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchCode(priUser.CrmId, priUser.HqsCode, UserId);
                        allUserInfo.Add(priUser);
                        //获取gtis子账号列表
                        var subGtisUserList = gtisUserList.Where(e => e.AccountType == EnumGtisAccountType.Sub.ToInt()).OrderBy(e => e.CreateDate).Take(curService.SubAccountsNum.Value).ToList();
                        //循环子账号列表，创建环球搜子账号
                        subGtisUserList.ForEach(subGtisUser =>
                        {
                            string subHqsCode = string.Empty;
                            //根据gtis子账号生成环球搜子账号
                            AddGlobalSearchSubUser(curService.Id, apply.Id, priHqsCode, contractInfo.ContractNum, subGtisUser.AccountNumber, curService.ServiceCycleStart, curService.ServiceCycleEnd, out subHqsCode);
                            var subUser = new BM_SaveHqsCode_UserInfo();
                            subUser.CrmId = subGtisUser.Id;
                            subUser.Uid = subGtisUser.AccountNumber;
                            subUser.HqsCode = subHqsCode;
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchCode(subUser.CrmId, subUser.HqsCode, UserId);
                            allUserInfo.Add(subUser);
                        });
                    }

                    else
                    {
                        //获取当前的主账号
                        var priAccount = globalsearch_Users.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).First();
                        priAccount.ContractServiceInfoGlobalSearchId = curService.Id;
                        DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateDataQueue(priAccount);
                        priHqsCode = priAccount.AccountNumber;
                        //获取当前的子账号列表
                        var subUserList = globalsearch_Users.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).ToList();
                        if (subUserList.Count > 0)
                        {//如果存在子账号
                            if (subUserList.Count > curService.SubAccountsNum)
                            {//如果现存子账号数量大于开通的子账号数量，移除多余的子账号
                                var count = subUserList.Count - curService.SubAccountsNum;
                                subUserList.RemoveRange(curService.SubAccountsNum.Value, count.Value);
                            }
                            //更新保留的子账号的serviceId
                            subUserList.ForEach(user =>
                            {
                                user.ContractServiceInfoGlobalSearchId = curService.Id;
                                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateDataQueue(user);
                            });
                        }
                        subAccList = subUserList.Select(e => e.AccountNumber).ToList();
                        //如果子账号数量少于开通要求的账号数量，补充生成
                        if (subUserList.Count < curService.SubAccountsNum)
                        {
                            var supCount = curService.SubAccountsNum - subUserList.Count;
                            //补充的子账号列表
                            List<string> supSubAccList = new List<string>();
                            AddGlobalSearchSubUser(curService.Id, apply.Id, priAccountNumber, contractInfo.ContractNum, curService.ServiceCycleStart, curService.ServiceCycleEnd, supCount, out supSubAccList);
                            subAccList.AddRange(supSubAccList);
                        }
                    }*/
                    #endregion
                    var param = new BM_SaveHqsCode
                    {
                        isNoUser = false,
                        AllUserInfo = allUserInfo,
                        HqsCodeList = hqsCodeList,
                    };
                    if (gtisCase == EnumContractGtisProductServiceState.Opened)
                        //当前合同包含gtis服务，用当前的ContractNum取gtis备注 + 本次备注 提交给gtis; G5备注处理 续约合同无gtis且重新生成账号，备注在生成gtis账号时上传过了
                        param.OtherInfo = BLL_GtisOpe.Instance.GetCompanyOtherInfo(curService.ContractNum.ToString()).Result + ";" + review_In.ReviewerRemark;
                    LogUtil.AddLog("svCode:" + contractInfo.ContractNum + ";   AllUserInfo:" + JsonConvert.SerializeObject(allUserInfo));
                    BLL_GtisOpe.Instance.SaveHqsCode(param).Wait();
                    //如果【续约合同】【使用原有环球搜码】【当前合同存在Gtis服务】，需要判断环球搜续约和gtis续约是否指向同一个合同
                    if (contractInfo.ContractType == (int)EnumContractType.ReNew && curService.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate && gtisCase == EnumContractGtisProductServiceState.Opened)
                    {
                        //获取当前合同的gtis续约的旧合同的客户编码
                        var gtisOldContractNum = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetSameContractGtisOldContractNum(contractInfo.ContractNum);
                        //gtis和环球搜分别续约了不同的合同，需要将环球搜被续约的账号禁用
                        if (gtisOldContractNum != curService.OldContractNum)
                        {
                            var toDisableUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetGtisUsersByContractNum(curService.OldContractNum);
                            List<BM_ModifyUserState> listUserState = new List<BM_ModifyUserState>();
                            if (toDisableUserList.Count > 0)
                            {
                                listUserState = toDisableUserList.Select(e => new BM_ModifyUserState { SysUserID = e.UserId, Deleted = 1 }).ToList();
                                BLL_GtisOpe.Instance.ModifyUserState(listUserState).Wait();
                            }
                        }
                    }
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.SaveQueues();
                    //如果是续约服务，需要将老的服务置为作废
                    if (!string.IsNullOrEmpty(curService.OldContractNum))
                    {
                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateOldGlobalSearchServiceVoidQueue(curService.OldContractNum, UserId);
                        DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateOldGtisServiceVoid(curService.OldContractNum, UserId);
                        //续约服务申请时需要记录服务变化类型
                        if (apply.ProcessingType == EnumProcessingType.Add.ToInt())
                        {
                            //旧服务
                            var oldService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(curService.OldContractNum);
                            //判断账号生成方式
                            if (curService.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake)
                            {
                                globalSearchChangeItem.Add(EnumGlobalSearchModifyType.NewService);
                            }
                            //判断账号数是否一致
                            else if (curService.SubAccountsNum != curService.SubAccountsNum)
                            {
                                globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeCode);
                            }
                            //判断服务时间是否一致
                            var oldServiceStart = oldService.ExecuteServiceCycleStart == null ? oldService.ServiceCycleStart : oldService.ExecuteServiceCycleStart;
                            var oldServiceEnd = oldService.ExecuteServiceCycleEnd == null ? oldService.ServiceCycleEnd : oldService.ExecuteServiceCycleEnd;
                            var curServiceStart = curService.ExecuteServiceCycleStart == null ? curService.ServiceCycleStart : curService.ExecuteServiceCycleStart;
                            var curServiceEnd = curService.ExecuteServiceCycleEnd == null ? curService.ServiceCycleEnd : curService.ExecuteServiceCycleEnd;
                            if (oldServiceStart != curServiceStart || oldServiceEnd != curServiceEnd)
                            {
                                globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeTime);
                            }
                            //服务更改列表为空，标记为未变更
                            if (globalSearchChangeItem.Count == 0)
                            {
                                globalSearchChangeItem.Add(EnumGlobalSearchModifyType.Unchanged);
                            }
                        }


                    }

                    //MessageMainInfo message = new MessageMainInfo();
                    //message.Issuer = apply.CreateUser;
                    //message.MessageTypeToId = apply.Id;
                    //message.MessagemMainAboutDes = contractInfo.ContractName;
                    //BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.SearchService, EnumMessageStateInfo.Pass, apply.ContractId);
                    #region 原环球搜码生成对应逻辑，已注销不用
                    /*
                    //判断当前申请是否操作过环球搜开通，已经存在环球搜的账号
                    var globalsearch_Users = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.CheckHasCodeByApplyId(apply.Id);
                    if (!globalsearch_Users.Any(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount))
                    {//没有生成过主账号，就表示没有环球搜账号，需要重新生成全部账号
                        //生成环球搜主账号，返回主账号code
                        AddGlobalSearchPrimaryUser(curService.Id, apply.Id, contractInfo.ContractNum, curService.ServiceCycleStart, curService.ServiceCycleEnd, out priAccountNumber);
                        //循环生成环球搜子账号
                        AddGlobalSearchSubUser(curService.Id, apply.Id, priAccountNumber, contractInfo.ContractNum, curService.ServiceCycleStart, curService.ServiceCycleEnd, curService.SubAccountsNum, out subAccList);
                    }
                    else
                    {
                        //获取当前的主账号
                        var priAccount = globalsearch_Users.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).First();
                        priAccount.ContractServiceInfoGlobalSearchId = curService.Id;
                        DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateDataQueue(priAccount);
                        priAccountNumber = priAccount.AccountNumber;
                        //获取当前的子账号列表
                        var subUserList = globalsearch_Users.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).ToList();
                        if (subUserList.Count > 0)
                        {//如果存在子账号
                            if (subUserList.Count > curService.SubAccountsNum)
                            {//如果现存子账号数量大于开通的子账号数量，移除多余的子账号
                                var count = subUserList.Count - curService.SubAccountsNum;
                                subUserList.RemoveRange(curService.SubAccountsNum.Value, count.Value);
                            }
                            //更新保留的子账号的serviceId
                            subUserList.ForEach(user =>
                            {
                                user.ContractServiceInfoGlobalSearchId = curService.Id;
                                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateDataQueue(user);
                            });
                        }
                        subAccList = subUserList.Select(e => e.AccountNumber).ToList();
                        //如果子账号数量少于开通要求的账号数量，补充生成
                        if (subUserList.Count < curService.SubAccountsNum)
                        {
                            var supCount = curService.SubAccountsNum - subUserList.Count;
                            //补充的子账号列表
                            List<string> supSubAccList = new List<string>();
                            AddGlobalSearchSubUser(curService.Id, apply.Id, priAccountNumber, contractInfo.ContractNum, curService.ServiceCycleStart, curService.ServiceCycleEnd, supCount, out supSubAccList);
                            subAccList.AddRange(supSubAccList);
                        }
                    }

                    //对接GTIS系统，写入环球搜账号
                    if (gtisCase == EnumContractGtisProductServiceState.Inexistence)
                    {//不存在Gtis服务
                        BindGtisSystemUserWithOutGtisUser(apply.ContractId, priAccountNumber, subAccList, curService.ServiceCycleStart, curService.ServiceCycleEnd, curService.SubAccountsNum.Value);
                    }
                    //存在Gtis服务
                    else if (gtisCase == EnumContractGtisProductServiceState.Opened)
                    {
                        BindGtisSystemUserWithCurrentGtisUser(apply.ContractId, priAccountNumber, subAccList);
                    }
                    */
                    #endregion
                }
                curService.ReviewerId = UserId;
                curService.ReviewerTime = now;
                curService.ReviewerRemark = review_In.ReviewerRemark;
                curService.UpdateDate = now;
                curService.UpdateUser = UserId;
                curService.GlobalSearchRemark = review_In.GlobalSearchRemark;
                curService.Remark = review_In.FeedBack;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateQueue(curService);
                DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateQueue(apply);
                if (review_In.State && apply.ProcessingType == EnumProcessingType.Change.ToInt())
                {
                    var oldService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.CheckContractHasGlobalSearchServiceByContractId(apply.ContractId);
                    oldService.State = EnumContractServiceState.INVALID;
                    oldService.ChangedId = curService.Id;
                    oldService.IsChanged = true;
                    DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateQueue(oldService);

                    //判断账号数是否一致
                    if (curService.SubAccountsNum != curService.SubAccountsNum)
                    {
                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeCode);
                    }
                    //判断服务时间是否一致
                    var oldServiceStart = oldService.ExecuteServiceCycleStart == null ? oldService.ServiceCycleStart : oldService.ExecuteServiceCycleStart;
                    var oldServiceEnd = oldService.ExecuteServiceCycleEnd == null ? oldService.ServiceCycleEnd : oldService.ExecuteServiceCycleEnd;
                    var curServiceStart = curService.ExecuteServiceCycleStart == null ? curService.ServiceCycleStart : curService.ExecuteServiceCycleStart;
                    var curServiceEnd = curService.ExecuteServiceCycleEnd == null ? curService.ServiceCycleEnd : curService.ExecuteServiceCycleEnd;
                    if (oldServiceStart != curServiceStart || oldServiceEnd != curServiceEnd)
                    {
                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeTime);
                    }
                    //服务更改列表为空，标记为未变更
                    if (globalSearchChangeItem.Count == 0)
                    {
                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.Unchanged);
                    }
                }
                //记录服务变化类型
                if (review_In.State && globalSearchChangeItem.Count == 0)
                {
                    var modifyTypeData = new Db_crm_contract_serviceinfo_globalsearch_modifytype();
                    modifyTypeData.ContractServiceInfoGlobalSearchId = curService.Id;
                    modifyTypeData.ProductServiceInfoGlobalSearchApplId = curService.ProductServiceInfoGlobalSearchApplId;
                    modifyTypeData.ContractNum = curService.ContractNum;
                    modifyTypeData.ModifyType = EnumGlobalSearchModifyType.NewService;
                    DbOpe_crm_contract_serviceinfo_globalsearch_modifytype.Instance.InsertDataQueue(modifyTypeData);
                }
                else
                {
                    foreach (var item in globalSearchChangeItem)
                    {
                        var modifyTypeData = new Db_crm_contract_serviceinfo_globalsearch_modifytype();
                        modifyTypeData.ContractServiceInfoGlobalSearchId = curService.Id;
                        modifyTypeData.ProductServiceInfoGlobalSearchApplId = curService.ProductServiceInfoGlobalSearchApplId;
                        modifyTypeData.ContractNum = curService.ContractNum;
                        modifyTypeData.ModifyType = item;
                        DbOpe_crm_contract_serviceinfo_globalsearch_modifytype.Instance.InsertDataQueue(modifyTypeData);
                    }
                }



                //开通的workflow记录
                var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(apply.ContractId);
                if (review_In.State)
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_globalsearch, Db_crm_contract>("环球搜服务审批流程", apply.Id, curService, contract, apply.ApplicantId, curService.ReviewerRemark, EnumContractServiceOpenState.Open.GetEnumDescription(), "复核");
                else
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_globalsearch, Db_crm_contract>("环球搜服务审批流程", apply.Id, curService, contract, apply.ApplicantId, curService.Remark, EnumContractServiceOpenState.Returned.GetEnumDescription(), "复核");

                #region 2024.3.19改为所有流转记录都保存审核反馈，这段内容注释
                /*string dataState = String.Empty;
                string remark = String.Empty;
                if (review_In.State)
                {
                    dataState = EnumContractServiceOpenState.Open.GetEnumDescription();
                    remark = curService.Remark;
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_globalsearch, Db_crm_contract>("环球搜服务审批流程", apply.Id, curService, contract, apply.ApplicantId, remark, dataState, "复核");
                }
                else
                {
                    dataState = EnumContractServiceOpenState.Returned.GetEnumDescription();
                    remark = curService.ReviewerRemark;
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_globalsearch, Db_crm_contract>("环球搜服务审批流程", apply.Id, curService, contract, remark, dataState, "复核");
                }*/
                #endregion
                //提交sql队列
                DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.SaveQueues();
                //11.20 开通服务要刷新合同的保护截止日
                if (review_In.State)
                    DbOpe_crm_contract.Instance.RefreshContractProtectDate(apply.ContractId);
            });


        }

        /// <summary>
        /// 不存在Gtis服务的情况下，与GTIS5系统交互，绑定环球搜码
        /// </summary>
        /// <param name="contractId">合同号</param>
        /// <param name="priAccountNumber">环球搜主账号</param>
        /// <param name="subAccList">环球搜子账号列表</param>
        /// <param name="startDate">服务开始时间</param>
        /// <param name="endTime">服务结束时间</param>
        /// <param name="subAccountsNum">开通的子账号数量</param>
        private void BindGtisSystemUserWithOutGtisUser(string contractId, string priAccountNumber, List<string> subAccList, DateTime? startDate, DateTime? endTime, int subAccountsNum)
        {
            //对接参数：账号CrmId与环球搜码对应，第一个是主账号
            var allUserInfo = new List<BM_SaveHqsCode_UserInfo>();
            //根据主账号判断是否已经创建过账户
            var priUser = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataByPrimaryCode(priAccountNumber);
            if (priUser == null)
            {
                priUser = new Db_crm_contract_serviceinfo_gtis_user();
                priUser.Id = Guid.NewGuid().ToString();
                priUser.AccountType = EnumGtisAccountType.Main.ToInt();
                priUser.OpeningStatus = (int)EnumGtisUserOpeningStatus.INPROCESS;
                priUser.StartDate = startDate;
                priUser.EndDate = endTime;
                priUser.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
                priUser.GlobalSearchCode = priAccountNumber;
                priUser.CreateDate = DateTime.Now;
                priUser.CreateUser = UserId;
                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertQueue(priUser);
            }
            allUserInfo.Add(new BM_SaveHqsCode_UserInfo
            {
                CrmId = priUser.Id,
                HqsCode = priUser.GlobalSearchCode
            });
            //根据子账号列表判断子账号对应账户是否完全创建
            var subUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataBySubCodeList(subAccList);
            //取差额
            var diff = subAccountsNum - subUserList.Count;
            //取当前在crm中已经绑定好的环球搜码
            var curAccList = subUserList.Select(e => e.GlobalSearchCode).ToList();
            //取当前在crm中还未绑定的环球搜码
            var tobeAddAccList = subAccList.Except(curAccList).ToList();
            //根据环球搜账号数创建gtis账号，多循环1次给主账号创建
            for (int i = 0; i < diff; i++)
            {
                //声明gtis账户user数据
                var user = new Db_crm_contract_serviceinfo_gtis_user()
                {
                    Id = Guid.NewGuid().ToString(),
                    AccountType = EnumGtisAccountType.Sub.ToInt(),
                    OpeningStatus = (int)EnumGtisUserOpeningStatus.INPROCESS,
                    StartDate = startDate,// audit_In.ServiceCycleStart,
                    EndDate = endTime,// audit_In.ServiceCycleEnd,
                    IsProcessed = (int)EnumGtisServiceIsProcess.Not,
                    GlobalSearchCode = tobeAddAccList.First().ToString(),
                    CreateDate = DateTime.Now,
                    CreateUser = UserId
                };
                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertQueue(user);
                subUserList.Add(user);
                tobeAddAccList.Remove(user.GlobalSearchCode);
            }
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.SaveQueues();


            subUserList.ForEach(user =>
            {
                //填充对接参数
                allUserInfo.Add(new BM_SaveHqsCode_UserInfo
                {
                    CrmId = user.Id,
                    HqsCode = user.GlobalSearchCode
                });
            });
            var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(contractId);
            var createUserInfo = new BM_SaveHqsCode_NoUserInfo();
            createUserInfo.SvCode = contractInfo.ContractNum;
            createUserInfo.Company = contractInfo.FirstPartyName;
            createUserInfo.Addr = string.Join("/", new string[] { contractInfo.CountryName, contractInfo.ProvinceName, contractInfo.CityName, contractInfo.Address });
            createUserInfo.UserName = contractInfo.Contacts;
            createUserInfo.MaxChildrens = subAccountsNum;
            createUserInfo.StartServerDate = startDate.Value;// audit_In.ServiceCycleStart;
            createUserInfo.EndServerDate = endTime.Value;// audit_In.ServiceCycleEnd,;
            createUserInfo.ContractNum = contractInfo.ContractAmount.Value.ToString();
            //createUserInfo.PayDate = contractInfo.ArrivalDate;
            createUserInfo.PayDate = DateTime.Parse(contractInfo.ArrivalDate).ToString("yyyyMM");
            createUserInfo.Tel = string.IsNullOrEmpty(contractInfo.ContactWay) ? contractInfo.Telephone : contractInfo.ContactWay;
            createUserInfo.crm_country = contractInfo.CountryName;
            createUserInfo.crm_city = contractInfo.CityName;

            var param = new BM_SaveHqsCode
            {
                isNoUser = true,
                CreateUserInfo = createUserInfo,
                AllUserInfo = allUserInfo,
            };
            var retList = BLL_GtisOpe.Instance.SaveHqsCode(param).Result;
            retList.ForEach(ret =>
            {
                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchGtisUser(ret, UserId);
            });

            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.SaveQueues();
        }


        /// <summary>
        /// 存在Gtis服务的情况下，与GTIS5系统交互，绑定环球搜码
        /// </summary>
        /// <param name="contractId">合同号</param>
        /// <param name="priAccountNumber">环球搜主账号</param>
        /// <param name="subAccList">环球搜子账号列表</param>
        private void BindGtisSystemUserWithCurrentGtisUser(string contractId, string priAccountNumber, List<string> subAccList)
        {
            //存在Gtis服务，查找gits账号
            var allUserInfo = new List<BM_SaveHqsCode_UserInfo>();
            var gtisUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetUserListByContractId(contractId);
            foreach (var gtisUser in gtisUserList)
            {
                var user = new BM_SaveHqsCode_UserInfo();
                user.CrmId = gtisUser.Id;
                user.Uid = gtisUser.AccountNumber;
                user.HqsCode = gtisUser.AccountType == EnumGtisAccountType.Main.ToInt() ? priAccountNumber : subAccList.First();
                allUserInfo.Add(user);
                subAccList.Remove(user.HqsCode);
                //账号数不一致,环球搜子账号列表为空跳出循环
                if (subAccList.Count == 0)
                    break;
            }
            var param = new BM_SaveHqsCode
            {
                isNoUser = false,
                AllUserInfo = allUserInfo,
            };
            BLL_GtisOpe.Instance.SaveHqsCode(param).Wait();
            allUserInfo.ForEach(user =>
            {
                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateGlobalSearchCode(user.CrmId, user.HqsCode, UserId, "");
            });
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.SaveQueues();
        }



        /// <summary>
        /// 生成环球搜主账号
        /// </summary>
        /// <param name="servId"></param>
        /// <param name="applyId"></param>
        /// <param name="contractNum"></param>
        /// <param name="gtisAccount"></param>
        /// <param name="startDate"></param>
        /// <param name="endTime"></param>
        /// <param name="settlementLevel"></param>
        /// <param name="settlementMonth"></param>
        /// <param name="st_memo"></param>
        /// <param name="accountNumber"></param>
        /// <exception cref="ApiException"></exception>
        private void AddGlobalSearchPrimaryUser(string servId, string applyId, string contractNum, string gtisAccount, DateTime? startDate, DateTime? endTime, string settlementLevel, decimal? settlementMonth, string st_memo, out string accountNumber)
        {
            //正确 "{\"result\":\"success\",\"msg\":\"\",\"globalsouID\":\"16116\"}"; 
            //错误 {"result":"error","msg":"Error converting data type varchar to bigint."}
            accountNumber = "";
            //"https://mgr.globalsou.com/api/?globalwitsID={0}&globalwitsName=xxxx_testwu&member=ent&acctType=online&dt_service_from={2}&dt_service_to={3}&actionType=acct_add&st_memo={4}"
            var url = string.Format(AppSettings.GlobalSearchAPI.AddPrimaryUserUrl, contractNum, gtisAccount, startDate.Value.ToyyyyMMdd(), endTime.Value.ToyyyyMMdd(), string.IsNullOrEmpty(settlementLevel) ? "" : settlementLevel.Replace("+", "%2B"), settlementMonth == null ? "" : settlementMonth.ToString(), st_memo);

            int createdCount = 0, targetCount = 5;
            while (createdCount < targetCount)
            {
                var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                if ("success".Equals(ret["result"].ToString()))
                {
                    accountNumber = ret["globalsouID"].ToString();
                    var user = new Db_crm_contract_serviceinfo_globalsearch_user();
                    user.ContractServiceInfoGlobalSearchId = servId;
                    user.ProductServiceInfoGlobalSearchApplId = applyId;
                    user.AccountNumber = accountNumber;
                    user.AccountType = EnumContractServiceGlobalSearchAccountType.PrimaryAccount;
                    user.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                    user.StartDate = startDate;
                    user.EndDate = endTime;
                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertData(user);//2023.10.26改为直接提交
                    break;
                }
                createdCount++;
                LogUtil.AddLog(url + "||" + ret["result"].ToString(), "主账号Post");
            }
        }

        /// <summary>
        /// 生成环球搜子账号--单独生成
        /// </summary>
        /// <param name="servId"></param>
        /// <param name="applyId"></param>
        /// <param name="priHqsCode"></param>
        /// <param name="contractNum"></param>
        /// <param name="gtisAccount"></param>
        /// <param name="startDate"></param>
        /// <param name="endTime"></param>
        /// <param name="st_memo"></param>
        /// <param name="hqsCode"></param>
        private void AddGlobalSearchSubUser(string servId, string applyId, string priHqsCode, string contractNum, string gtisAccount, DateTime? startDate, DateTime? endTime, string st_memo, out string hqsCode)
        {
            //正确 "{\"result\":\"success\",\"msg\":\"\",\"globalsouID\":\"16116\"}"; 
            //错误 {"result":"error","msg":"Error converting data type varchar to bigint."}
            hqsCode = "";
            var url = string.Format(AppSettings.GlobalSearchAPI.AddSubUserUrl, priHqsCode, contractNum, gtisAccount, st_memo);
            int createdCount = 0, targetCount = 5;
            while (createdCount < targetCount)
            {
                var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                if ("success".Equals(ret["result"].ToString()))
                {
                    hqsCode = ret["globalsouID"].ToString();
                    var user = new Db_crm_contract_serviceinfo_globalsearch_user();
                    user.ContractServiceInfoGlobalSearchId = servId;
                    user.ProductServiceInfoGlobalSearchApplId = applyId;
                    user.AccountNumber = hqsCode;
                    user.AccountType = EnumContractServiceGlobalSearchAccountType.SubAccount;
                    user.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                    user.StartDate = startDate;
                    user.EndDate = endTime;
                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertData(user);//2023.10.26改为直接提交
                    break;
                }
                createdCount++;
                //LogUtil.AddLog(url + "||" + ret["result"].ToString(), "主账号Post");
            }
        }

        /// <summary>
        /// 更新环球搜账号时间
        /// </summary>
        /// <param name="servId"></param>
        /// <param name="applyId"></param>
        /// <param name="globalSearchCode"></param>
        /// <param name="contractNum"></param>
        /// <param name="gtisAccount"></param>
        /// <param name="startDate"></param>
        /// <param name="endTime"></param>
        /// <param name="settlementLevel"></param>
        /// <param name="settlementMonth"></param>
        /// <param name="st_memo"></param>
        private void UpdateGlobalSearchUser(string servId, string applyId, string globalSearchCode, string contractNum, string gtisAccount, DateTime? startDate, DateTime? endTime, string settlementLevel, decimal? settlementMonth, string st_memo)
        {
            //"https://mgr.globalsou.com/api/?souID={0}&globalwitsID={1}&globalwitsName={2}&member=ent&acctType=online&dt_service_from={3}&dt_service_to{4}&st_memo={5}"
            //*{"result":"success","msg":"","globalsouID":"20494","term":{"begin":"2024-04-01","to":"2024-04-30"}*/
            var url = string.Format(AppSettings.GlobalSearchAPI.UpdateUserUrl, globalSearchCode, contractNum, gtisAccount, startDate.Value.ToyyyyMMdd(), endTime.Value.ToyyyyMMdd(), string.IsNullOrEmpty(settlementLevel) ? "" : settlementLevel.Replace("+", "%2B"), settlementMonth == null ? "" : settlementMonth.ToString(), st_memo);
            var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
            if ("success".Equals(ret["result"].ToString()))
            {
                var user = new Db_crm_contract_serviceinfo_globalsearch_user();
                user.ContractServiceInfoGlobalSearchId = servId;
                user.ProductServiceInfoGlobalSearchApplId = applyId;
                user.AccountNumber = globalSearchCode;
                user.AccountType = EnumContractServiceGlobalSearchAccountType.PrimaryAccount;
                user.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                user.StartDate = startDate;
                user.EndDate = endTime;
                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertData(user);
            }
            else
                throw new ApiException("环球搜续约失败");
        }

        /// <summary>
        /// 更新环球搜账号时间--gtis变更使用优惠券
        /// </summary>
        /// <param name="servId"></param>
        /// <param name="globalSearchCode"></param>
        /// <param name="contractNum"></param>
        /// <param name="gtisAccount"></param>
        /// <param name="startDate"></param>
        /// <param name="endTime"></param>
        /// <param name="settlementLevel"></param>
        /// <param name="settlementMonth"></param>
        /// <param name="st_memo"></param>
        public void UpdateGlobalSearchUserBecauseCoupon(string servId, string globalSearchCode, string contractNum, string gtisAccount, DateTime? startDate, DateTime? endTime, string settlementLevel, int? settlementMonth, string st_memo)
        {
            //"https://mgr.globalsou.com/api/?souID={0}&globalwitsID={1}&globalwitsName={2}&member=ent&acctType=online&dt_service_from={3}&dt_service_to{4}&st_memo={5}"
            //*{"result":"success","msg":"","globalsouID":"20494","term":{"begin":"2024-04-01","to":"2024-04-30"}*/
            var url = string.Format(AppSettings.GlobalSearchAPI.UpdateUserUrl, globalSearchCode, contractNum, gtisAccount, startDate.Value.ToyyyyMMdd(), endTime.Value.ToyyyyMMdd(), settlementLevel, (settlementMonth == null ? "" : settlementMonth.ToString()), st_memo);
            var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
            if ("success".Equals(ret["result"].ToString()))
            {
                var userList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(servId);
                userList.ForEach(user =>
                {
                    user.StartDate = startDate;
                    user.EndDate = endTime;
                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateDataQueue(user);
                });
                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.SaveQueues();
            }
            else
                throw new ApiException("环球搜服务周期修改失败");
        }


        /// <summary>
        /// 生成环球搜子账号--批量生成
        /// </summary>
        /// <param name="servId"></param>
        /// <param name="applyId"></param>
        /// <param name="priAccountNumber"></param>
        /// <param name="contractNum"></param>
        /// <param name="gtisAccount"></param>
        /// <param name="startDate"></param>
        /// <param name="endTime"></param>
        /// <param name="targetCount"></param>
        /// <param name="accList"></param>
        public void AddGlobalSearchSubUser(string servId, string applyId, string priAccountNumber, string contractNum, string gtisAccount, DateTime? startDate, DateTime? endTime, int? targetCount, out List<string> accList)
        {
            accList = new List<string>();
            if (targetCount == null)
                return;
            var url = string.Format(AppSettings.GlobalSearchAPI.AddSubUserUrl, priAccountNumber, contractNum);
            while (accList.Count < targetCount)
            {
                var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                if ("success".Equals(ret["result"].ToString()))
                {
                    var accountNumber = ret["globalsouID"].ToString();
                    var user = new Db_crm_contract_serviceinfo_globalsearch_user();
                    user.ContractServiceInfoGlobalSearchId = servId;
                    user.ProductServiceInfoGlobalSearchApplId = applyId;
                    user.AccountNumber = accountNumber;
                    user.AccountType = EnumContractServiceGlobalSearchAccountType.SubAccount;
                    user.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                    user.StartDate = startDate;
                    user.EndDate = endTime;
                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertDataQueue(user);
                    accList.Add(accountNumber);
                }
                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.SaveQueues();//2023.10.26 增加提交sql队列
                //LogUtil.AddLog(url + "||" + ret["result"].ToString(), "子账号Post");
            }
        }

        /// <summary>
        /// 根据申请id获取合同服务申请信息_环球搜信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public GetContractServiceInfoApplyInfoGlobalSearchByApplId_Out GetContractServiceInfoApplyInfoGlobalSearchByApplId(string Id)
        {
            //获取申请基本信息
            var apply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetContractServiceInfoApplyInfoByApplId(Id);
            //获取合同详细信息
            apply.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
            //到账备注列表
            apply.ReceiptRemarks = DbOpe_crm_contract_receiptregister.Instance.GetRemarksByContractId(apply.ContractId);

            //申请信息
            #region 申请信息
            apply.ApplyInfo = new GetContractServiceInfoApplyInfoGlobalSearchByApplId_Out_ApplyInfo()
            {
                ApplyId = apply.Id,
                ContractProductInfoId = apply.ContractProductInfoId,
                ContractNum = apply.ContractInfo.ContractNum,
                PrimaryAccountsNum = apply.PrimaryAccountsNum,
                SubAccountsNum = apply.SubAccountsNum,
                ServiceCycle = ((apply.ServiceCycleStart == null) ? "--" : apply.ServiceCycleStart.Value.ToString("yyyy-MM-dd")) + "至" + ((apply.ServiceCycleEnd == null) ? "--" : apply.ServiceCycleEnd.Value.ToString("yyyy-MM-dd")),
                ServiceMonth = apply.ServiceMonth.Value,
                Remark = apply.Remark,
                /*ExecuteServiceCycleStart = apply.ServiceCycleStart,
                ExecuteServiceCycleEnd = apply.ServiceCycleEnd,
                ExecuteServiceMonth = apply.ServiceMonth,*/
            };
            #endregion

            /*if (apply.ProcessingType == (int)EnumProcessingType.Change)
            {
                //获取被变更的环球搜服务信息
                var old_global_serve = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(apply.ContractInfo.ContractNum);
                if (old_global_serve != null)
                {
                    //存在被变更的环球搜服务，本次的开始时间默认值为被变更环球搜服务的结束时间
                    apply.ApplyInfo.ExecuteServiceCycleStart = old_global_serve.ServiceCycleEnd;
                    //本次的服务月份默认值为本次变更申请后整体的服务月份减去被变更服务的服务月份
                    apply.ApplyInfo.ExecuteServiceMonth -= old_global_serve.ServiceMonth;
                    //本次的结束时间默认值为本次开始时间+本次服务月份
                    apply.ApplyInfo.ExecuteServiceCycleEnd = apply.ApplyInfo.ExecuteServiceCycleStart.Value.AddMonths(apply.ApplyInfo.ExecuteServiceMonth.GetValueOrDefault(0));
                }
            }*/
            #region 在服信息
            //apply.OnServiceInfo = new Db_crm_contract_serviceinfo_globalsearch();
            if (apply.ProcessingType == (int)EnumProcessingType.Change)
                //获取被变更的环球搜服务信息
                apply.OnServiceInfo = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(apply.ContractInfo.ContractNum);
            else if (apply.ContractInfo.ContractType == (int)EnumContractType.ReNew)
                apply.OnServiceInfo = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(apply.RenewContractNum);
            if (apply.OnServiceInfo != null)
            {
                string globalMainCode = string.Empty;
                /*var globalMainUser = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(apply.OnServiceInfo.Id).Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount)?.First();*/
                var globalMainUserList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(apply.OnServiceInfo.Id).Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).ToList();
                if (globalMainUserList != null && globalMainUserList.Count > 0)
                    globalMainCode = globalMainUserList.First().AccountNumber;
                if (!string.IsNullOrEmpty(globalMainCode))
                {
                    var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, globalMainCode);
                    try
                    {
                        var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                        if ("200".Equals(ret["status"].ToString()))
                        {
                            var results = ret["results"].ToList();
                            results.ForEach(result =>
                            {
                                apply.OnServiceInfo.ExecuteServiceCycleEnd = DateTime.Parse(result["enddate"].ToString());
                            });
                        }
                        else
                        {
                            throw new ApiException(ret["message"].ToString());
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new ApiException(ex.Message);
                    }
                }
            }
            #endregion
            #region apply.ProductInfo
            //获取产品详细信息,如果是普通服务，从合同产品表中获取信息，如果是赠送服务，从产品表中获取信息
            if (!string.IsNullOrEmpty(apply.ContractProductInfoId))
                apply.ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(apply.ContractProductInfoId).MappingTo<GetContractServiceApplInfoGlobalSearchByApplId_Out_ProductInfo>();
            else
            {
                apply.ProductInfo = DbOpe_crm_product.Instance.QueryByPrimaryKey(apply.ProductId).MappingTo<GetContractServiceApplInfoGlobalSearchByApplId_Out_ProductInfo>();
                apply.ProductInfo.OpeningMonths = apply.ServiceMonth.Value;
                apply.ProductInfo.FirstOpeningMonths = apply.ServiceMonth.Value;
            }
            #endregion
            //声明被续约合同对象
            var oldContract = new Db_crm_contract();
            //当前合同的Gtis服务状态
            //var gtisCase = DbOpe_crm_contract_serviceinfo_gtis.Instance.CheckHasUnValidGTISService(apply.ContractId);
            var gtisCase = DbOpe_crm_contract_productinfo.Instance.CheckHasUnValidGTISService(apply.ContractId);
            #region 根据gtis的服务时间，修改环球搜的服务时间
            if (gtisCase == EnumContractGtisProductServiceState.Opened)
            {
                var gtisService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractId(apply.ContractId);
                apply.ServiceCycle = gtisService.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + gtisService.ServiceCycleEnd.Value.ToString("yyyy-MM-dd");
                apply.ServiceCycleStart = gtisService.ServiceCycleStart;
                apply.ServiceCycleEnd = gtisService.ServiceCycleEnd;
                apply.ServiceMonth = gtisService.ServiceMonth;
            }
            #endregion
            #region apply.IsGtisUseOrgAccount 判断当前服务能否使用原有账号,同时返回被续约的服务信息
            Db_crm_contract_serviceinfo_globalsearch oldService = null;
            var oldFlag = apply.ProcessingType == EnumProcessingType.Change.ToInt() || apply.ContractInfo.ContractType == (int)EnumContractType.ReNew;
            if (apply.ProcessingType == EnumProcessingType.Change.ToInt())
            {
                //判断历史合同存在环球搜服务，存在环球搜服务可以使用原有，因为有赠送服务的情况，需要根据合同ID去服务表中查询
                oldService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.CheckContractHasGlobalSearchServiceByContractId(apply.ContractInfo.Id);
                oldContract = apply.ContractInfo.MappingTo<Db_crm_contract>();
            }
            else if (apply.ContractInfo.ContractType == (int)EnumContractType.ReNew)//续约合同
            {
                if (!string.IsNullOrEmpty(apply.ContractInfo.RenewalContractNum))
                    oldContract = DbOpe_crm_contract.Instance.GetContractByContractNum(apply.RenewContractNum);
                else
                    //查找【相同甲方公司的】【包含Gtis服务或者环球搜服务的】【最近的】一份历史合同
                    oldContract = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetLastSameFirstPartyServiceContractInfo(apply.OldFirstParty, apply.ContractId);
                if (oldContract != null) //存在这样的历史合同
                    //判断历史合同存在环球搜服务，存在环球搜服务可以使用原有，因为有赠送服务的情况，需要根据合同ID去服务表中查询
                    oldService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.CheckContractHasGlobalSearchServiceByContractId(oldContract.Id);
            }
            if (oldFlag && oldService != null)//存在被续约的服务信息，可以使用原有账号，并返回被续约的服务信息
            {
                //查找oldService是否有环球搜码
                var globalSearchUserList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(oldService.Id);
                //存在环球搜码列表
                if (globalSearchUserList.Count != 0 && globalSearchUserList != null)
                {
                    //允许使用原有
                    apply.IsGtisUseOrgAccount = true;
                    //整理被续约的服务信息
                    apply.OldServiceInfo = new GetContractServiceInfoApplyInfoGlobalSearchByApplId_Out_OldServiceInfo()
                    {
                        Id = oldService.Id,
                        ProductId = oldService.ProductId,
                        PrimaryAccountsNum = oldService.PrimaryAccountsNum ?? default(int),
                        SubAccountsNum = oldService.SubAccountsNum ?? default(int),
                        ServiceCycleStart = oldService.ServiceCycleStart,
                        ServiceCycleEnd = oldService.ServiceCycleEnd,
                        ServiceMonth = oldService.ServiceMonth ?? default(int),
                    };
                    apply.OldServiceInfo.ProductName = DbOpe_crm_product.Instance.QueryByPrimaryKey(oldService.ProductId).ProductName;
                }
                apply.SettlementLevel = oldService.SettlementLevel;
                apply.SettlementMonth = oldService.SettlementMonth;
            }
            #endregion
            #region apply.GtisRemark Gtis备注
            if (gtisCase == EnumContractGtisProductServiceState.Opened)//存在Gtis服务
                apply.GtisRemark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(apply.ContractInfo.ContractNum.ToString()).Result;
            else if (apply.IsGtisUseOrgAccount)//允许使用原有
                apply.GtisRemark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(oldContract.ContractNum.ToString()).Result;
            #endregion
            #region apply.IsGlobalSearchFree
            //判断环球搜产品是否是赠送的，根据 ContractProductInfoId 查找价格，如果为0则是赠送，若没有 ContractProductInfoId 也是赠送
            if (string.IsNullOrEmpty(apply.ContractProductInfoId))
                apply.IsGlobalSearchFree = true;
            else
                apply.IsGlobalSearchFree = DbOpe_crm_contract_productinfo.Instance.QueryByPrimaryKey(apply.ContractProductInfoId).ContractProductinfoPriceTotal.ToInt() == 0;
            #endregion
            if (gtisCase == EnumContractGtisProductServiceState.Opened)
                apply.IsHaveGtisService = true;
            #region 可以使用原有时,需要填充的数据
            if (apply.IsGtisUseOrgAccount)//可以使用原有账号
            {
                //合同对应gtis账号及环球搜编码,如果没有gtis服务，显示原合同的gtis账号信息
                if (gtisCase == EnumContractGtisProductServiceState.Inexistence)
                {
                    //当前环球搜服务可以增减Gtis账号
                    apply.CouldChangeGtisAccountNum = true;
                    //旧合同的Gtis服务情况
                    //var oldGtisCase = DbOpe_crm_contract_serviceinfo_gtis.Instance.CheckHasUnValidGTISService(oldContract.Id);
                    var oldGtisCase = DbOpe_crm_contract_productinfo.Instance.CheckHasUnValidGTISService(oldContract.Id);
                    if (oldGtisCase == EnumContractGtisProductServiceState.Inexistence)
                        //从环球搜服务数据切入查询gtis_user表数据
                        apply.CurContractGTISUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetGtisUserListFromGlobalSearch(oldContract.Id);
                    else
                        //从gtis服务数据切入查询gtis_user表数据
                        apply.CurContractGTISUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetGtisUserListFromGtis(oldContract.Id);
                }
                else
                {
                    //从gtis服务数据切入查询gtis_user表数据
                    apply.CurContractGTISUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetGtisUserListFromGtis(apply.ContractInfo.Id);
                }

                //原合同的环球搜服务开通的编码
                apply.OldContractGlobalSearchUserList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserListByContractId(oldContract.Id);
                if (apply.OldContractGlobalSearchUserList.Count == 0 || apply.OldContractGlobalSearchUserList == null)
                {
                    apply.OldContractGlobalSearchUserList = apply.CurContractGTISUserList.DistinctBy(e => e.GlobalSearchCode).Select(e => new GetContractServiceInfoApplyInfoGlobalSearchByApplId_Out_OldContractGlobalSearchUserInfo()
                    {
                        AccountNumber = e.GlobalSearchCode,
                        AccountType = e.AccountType == 1 ? 1 : 2,
                    }).ToList();
                }
                //当前环球搜申请编码个数
                apply.CurContractGlobalSearchAccountTotalNum = apply.PrimaryAccountsNum + apply.SubAccountsNum;
                //当前续约合同的环球搜账号是否小于原合同
                apply.IsGlobalSearchAccountNumReduce = (apply.OldContractGlobalSearchUserList.Count > apply.CurContractGlobalSearchAccountTotalNum);
            }
            #endregion
            #region 复核时查看的信息
            apply.ReviewInfo = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetReviewBasicInfoById(Id);
            if (apply.ReviewInfo != null)
            {
                //如果是赠送服务，跳过第一次初审流程，所以在复核页面回写当前开通的gtis服务时间
                if (string.IsNullOrEmpty(apply.ContractProductInfoId) && apply.ReviewFeedbacks.Count == 1)
                {
                    //var gtisService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractId(apply.ContractId);
                    apply.ReviewInfo.ServiceCycleStart = apply.ServiceCycleStart.Value;
                    apply.ReviewInfo.ServiceCycleEnd = apply.ServiceCycleEnd.Value;
                    apply.ReviewInfo.ServiceMonth = apply.ServiceMonth;
                }
                apply.ReviewInfo.GtisRemark = apply.GtisRemark;
                apply.ReviewInfo.GlobalSearchUserList = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetReviewGtisUserInfo(apply.ReviewInfo.ServiceId, apply.ReviewInfo.ContractNum, gtisCase == EnumContractGtisProductServiceState.Opened);
                apply.ReviewInfo.IsGlobalSearchFree = apply.IsGlobalSearchFree;
            }
            #endregion
            #region 历史服务信息
            if (oldContract != null && apply.ContractInfo != null && apply.ContractInfo.ContractType != null && apply.ProcessingType != null)
            {
                apply.HistoryGlobalSearchServiceList = GetHistoryGlobalSearchServiceList(apply.Id, apply.ContractInfo.ContractType.Value, apply.ProcessingType.Value, oldContract.ContractNum, apply.ContractInfo.ContractNum);
            }
            else
            {
                apply.HistoryGlobalSearchServiceList = new List<HistoryGlobalsearchServiceInfo>();
            }

            #endregion
            //合同的所有到账信息
            apply.ReceiptRegisterCollectionList = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(apply.ContractId, String.Empty, EnumProductType.Global, apply.ReviewInfo == null ? null : apply.ReviewInfo.ServiceId);
            //当前服务已绑定的到账信息
            if (apply.ReviewInfo != null)
                apply.LinkedReceiptRegisterIds = DbOpe_crm_contract_receiptregister_service.Instance.GetReceiptRegisterIdsByServiceId(apply.ReviewInfo.ServiceId);
            var retApply = apply.MappingTo<GetContractServiceInfoApplyInfoGlobalSearchByApplId_Out>();
            return retApply;
        }

        /// <summary>
        /// 撤销合同服务信息环球搜申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        /// <exception cref="ApiException"></exception>
        public void RevokeContractProductServiceInfoGlobalSearchAudit(OperateContractProductServiceInfoGlobalSearchAudit_In operate_In)
        {
            //获取申请数据
            var apply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
            //待开通(申请为Submit)状态的数据不可撤销
            if (apply.State == EnumProcessStatus.Submit.ToInt())
                throw new ApiException("所选的申请不可撤销");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetServiceInfoByApplyId(operate_In);
            //若存在服务，过期(服务为过期)、作废(服务为作废或失效)状态的数据不可撤销
            if (service != null && (service.State == EnumContractServiceState.INVALID || service.State == EnumContractServiceState.VOID || service.State == EnumContractServiceState.OUT))
                throw new ApiException("所选的申请不可撤销");
            //申请的状态改为待开通
            apply.State = EnumProcessStatus.Submit.ToInt();
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateQueue(apply);
            if (service != null)
            {
                //服务的状态改为作废
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateQueue(service);
                #region 环球搜目前没有服务变更
                /*//如果是服务变更，原服务恢复正常
                if (!string.IsNullOrEmpty(service.HistoryId))
                {
                    var originalServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateQueue(originalServe);
                }*/
                #endregion
                //workflow撤销
                string state = ((EnumProcessStatus)apply.State).GetEnumDescription();
                BLL_WorkFlow.Instance.CancelWorkflowPending("环球搜服务审批流程", service.Id, state, service);
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.SaveQueues();
        }

        /// <summary>
        /// 作废合同服务信息环球搜申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        /// <exception cref="ApiException"></exception>
        public void VoidContractProductServiceInfoGlobalSearchAudit(OperateContractProductServiceInfoGlobalSearchAudit_In operate_In)
        {
            //获取申请数据 
            var apply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
            /* 2023.10.7 拒绝状态改为可以作废
            //申请为拒绝状态的数据不可以作废 
            if (apply.State == EnumProcessStatus.Refuse.ToInt())
                throw new ApiException("所选的申请不可作废");
            */
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetServiceInfoByApplyId(operate_In);
            //若存在服务，过期(服务为过期)、作废(服务为作废或失效)状态的数据不可以作废
            if (service != null && (service.State == EnumContractServiceState.INVALID || service.State == EnumContractServiceState.VOID || service.State == EnumContractServiceState.OUT))
                throw new ApiException("所选的申请不可作废");
            //申请的状态改为作废
            apply.State = EnumProcessStatus.Void.ToInt();
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateQueue(apply);
            //如果存在服务，处理服务后续内容
            if (service != null)
            {
                //服务的状态改为作废
                service.State = EnumContractServiceState.VOID;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateQueue(service);
                #region 环球搜目前没有服务变更
                //如果是服务变更，原服务恢复正常
                /*if (!string.IsNullOrEmpty(service.HistoryId))
                {
                    var originalServe = DbOpe_crm_contract_serviceinfo_db.Instance.QueryByPrimaryKey(service.HistoryId);
                    originalServe.ChangedId = "";
                    originalServe.UpdateDate = DateTime.Now;
                    originalServe.UpdateUser = UserId;
                    if (originalServe.ServiceCycleEnd < DateTime.Now)
                        originalServe.State = EnumContractServiceState.OUT;
                    else
                        originalServe.State = EnumContractServiceState.VALID;
                    DbOpe_crm_contract_serviceinfo_db.Instance.UpdateQueue(originalServe);
                }*/
                #endregion
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.SaveQueues();

        }


        /// <summary>
        /// 删除合同服务信息环球搜申请信息
        /// </summary>
        /// <param name="operate_In"></param>
        public void DeleteContractProductServiceInfoGlobalSearchAudit(OperateContractProductServiceInfoGlobalSearchAudit_In operate_In)
        {
            //声明可删除状态的集合, 拒绝、作废状态的数据可以进行删除操作
            var couldDeleteOpenStateList = new List<EnumContractServiceOpenState> { EnumContractServiceOpenState.Refuse, EnumContractServiceOpenState.Void };
            //获取申请数据
            //var apply = DbOpe_crm_contract_productserviceinfo_db_appl.Instance.GetServiceDBApplyInfoWithStateByApplyId(operate_In.ApplyId);
            var apply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetServiceGlobalSearchApplyInfoWithStateByApplyId(operate_In.ApplyId);
            //判断当前数据是否可以删除
            if (!couldDeleteOpenStateList.Contains(apply.OpenState))
                throw new ApiException("所选的申请不可删除");
            //删除申请
            apply.Deleted = true;
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateQueue(apply.MappingTo<Db_crm_contract_productserviceinfo_globalsearch_appl>());
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetServiceInfoByApplyId(operate_In);
            if (service != null)
            {
                //删除服务
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateQueue(service);
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.SaveQueues();

            #region 原删除逻辑，2023.12.13注释
            /*
            //获取申请数据 
            var apply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.QueryByPrimaryKey(operate_In.ApplyId);
            //申请待开通状态的数据不可以删除
            if (apply.State == EnumProcessStatus.Submit.ToInt())
                throw new ApiException("所选的申请不可删除");
            //获取服务数据
            var service = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetServiceInfoByApplyId(operate_In);
            //若存在服务，正常状态的数据不可以删除
            if (service != null && service.State == EnumContractServiceState.VALID)
                throw new ApiException("所选的申请不可删除");
            //删除申请
            apply.Deleted = true;
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateQueue(apply);
            if (service != null)
            {
                //删除服务
                service.Deleted = true;
                service.UpdateUser = UserId;
                service.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateQueue(service);
            }
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.SaveQueues();
            */
            #endregion
        }

        /// <summary>
        /// 设置环球搜用户状态信息
        /// </summary>
        /// <param name="userList_In"></param>
        public void SetContractServiceInfoGlobalSearchUser(List<SetContractServiceInfoGlobalSearchUser_In> userList_In)
        {
            foreach (var user in userList_In)
            {
                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.SetContractServiceInfoGlobalSearchUser(user, UserId);
            }
            //提交sql队列
            DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.SaveQueues();
        }

        /// <summary>
        /// 根据申请Id批量修改服务用户状态
        /// </summary>
        /// <param name="applyList_In"></param>
        public void SetContractServiceInfoGlobalSearchUserByApplyIds(SetContractServiceInfoGlobalSearchUserByApplyIds_In applyList_In)
        {
            var applyIdList = applyList_In.Ids.Split(',').ToList();
            var serviceIds = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetServiceInfoIdByApplyIds(applyIdList);
            DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.SetContractServiceInfoGlobalSearchUserByApplyId(serviceIds, applyList_In.OpeningStatus, UserId);
        }

        /*
                /// <summary>
                /// 
                /// </summary>
                /// <param name="search_In"></param>
                /// <param name="total"></param>
                /// <returns></returns>
                public List<SearchContractProductServiceInfoGlobalSearchApplList_Out> SearchContractProductServiceInfoGlobalSearchApplList(SearchContractProductServiceInfoGlobalSearchApplList_In search_In, ref int total)
                {
                    var list = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.SearchContractProductServiceInfoGlobalSearchApplList(search_In, ref total);
                    //获取所有环球搜账号信息list
                    var userList = new List<Db_crm_contract_serviceinfo_globalsearch_user>();
                    //全部环球搜账号编码
                    string cstids = string.Empty;
                    var userList_In = list.Where(e => e.OpenState != EnumContractServiceOpenState.Void).Select(e => e.UserList).ToList();
                    foreach (var temp in userList_In)
                    {
                        if (!string.IsNullOrEmpty(cstids))
                            cstids += ";";
                        cstids += string.Join(';', temp.Select(e => e.AccountNumber).ToList());
                        userList.AddRange(temp);
                    }
                    if (string.IsNullOrEmpty(cstids))
                        return list.MappingTo<List<SearchContractProductServiceInfoGlobalSearchApplList_Out>>();
                    var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, cstids);
                    try
                    {
                        var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                        if ("200".Equals(ret["status"].ToString()))
                        {//查询接口调用成功，根据查询的结果回写数据库中各个账号的状态
                            var results = ret["results"].ToList();
                            var accountList = new List<Db_crm_contract_serviceinfo_globalsearch_user>();
                            results.ForEach(result =>
                            {
                                var account = userList.Where(e => e.AccountNumber.Equals(result["idCst"].ToString())).First();
                                if ("active".Equals(result["status"].ToString()))
                                    //返回正常，账号状态标记正常
                                    account.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                                else if ("disabled".Equals(result["status"].ToString()))
                                    //返回过期，账号状态标记停用
                                    account.OpeningStatus = EnumContractServiceGlobalSearchUserState.Stop;
                                else if ("noexisted".Equals(result["status"].ToString()))
                                    //返回不存在，账号状态标记异常
                                    account.OpeningStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                                accountList.Add(account);
                                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateDataQueue(account);
                            });

                            list.ForEach(item =>
                            {
                                item.UserList = accountList.Where(e => e.ContractServiceInfoGlobalSearchId.Equals(item.ServiceId)).ToList();
                            });
                            DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.SaveQueues();
                        }
                        else
                        {
                            throw new ApiException(ret["message"].ToString());
                        }
                        list.ForEach(item =>
                        {
                            if (item.UserList.Count == 0)
                                item.AccountStatus = null;
                            else if (item.OpenState == EnumContractServiceOpenState.Void)
                                item.AccountStatus = null;
                            //全部正常为正常;2023.10.25改为：存在正常且没有异常为正常
                            else if (item.UserList.Any(e => e.OpeningStatus == EnumContractServiceGlobalSearchUserState.Normal) && item.UserList.All(e => e.OpeningStatus != EnumContractServiceGlobalSearchUserState.AbNormal))
                                item.AccountStatus = EnumContractServiceGlobalSearchUserState.Normal;
                            //存在异常且没用停用为异常;2023.10.25改为存在异常为异常
                            else if (item.UserList.Any(e => e.OpeningStatus == EnumContractServiceGlobalSearchUserState.AbNormal))
                                item.AccountStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                            //存在停用为停用;2023.10.25改为全部停用为停用
                            else if (item.UserList.All(e => e.OpeningStatus == EnumContractServiceGlobalSearchUserState.Stop))
                                item.AccountStatus = EnumContractServiceGlobalSearchUserState.Stop;
                            item.AccountStatusName = (item.AccountStatus == null) ? "--" : item.AccountStatus.GetEnumDescription();
                            //环球搜账号信息
                            item.PrimaryUserList = item.UserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).ToList();
                            item.SubUserList = item.UserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).ToList();
                        });
                        var retList = list.MappingTo<List<SearchContractProductServiceInfoGlobalSearchApplList_Out>>();
                        return retList;
                    }
                    catch (Exception ex)
                    {
                        throw new ApiException(ex.Message);
                    }
                }
        */
        public List<GetGlobalSearchCodeStateAsync_Out> GetGlobalSearchCodeStateAsync(List<GetGlobalSearchCodeStateAsync_In> search_In)
        {
            //获取所有环球搜账号信息list
            var userList = new List<Db_crm_contract_serviceinfo_globalsearch_user>();
            //全部环球搜账号编码
            string cstids = string.Empty;
            var userList_In = search_In.Where(e => e.OpenState != EnumContractServiceOpenState.Void).Select(e => e.UserList).ToList();

            foreach (var temp in userList_In)
            {
                if (!string.IsNullOrEmpty(cstids))
                    cstids += ";";
                cstids += string.Join(';', temp.Select(e => e.AccountNumber).ToList());
                userList.AddRange(temp);
            }
            if (string.IsNullOrEmpty(cstids))
                return null;
            var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, cstids);
            try
            {
                var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                if ("200".Equals(ret["status"].ToString()))
                {//查询接口调用成功，根据查询的结果回写数据库中各个账号的状态
                    var results = ret["results"].ToList();
                    var accountList = new List<Db_crm_contract_serviceinfo_globalsearch_user>();
                    results.ForEach(result =>
                    {
                        var account = userList.Where(e => e.AccountNumber.Equals(result["idCst"].ToString())).First();
                        if ("active".Equals(result["status"].ToString()))
                            //返回正常，账号状态标记正常
                            account.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                        else if ("disabled".Equals(result["status"].ToString()))
                            //返回过期，账号状态标记停用
                            account.OpeningStatus = EnumContractServiceGlobalSearchUserState.Stop;
                        else if ("noexisted".Equals(result["status"].ToString()))
                            //返回不存在，账号状态标记异常
                            account.OpeningStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                        accountList.Add(account);
                        DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateDataQueue(account);
                    });

                    search_In.ForEach(item =>
                    {
                        item.UserList = accountList.Where(e => e.ContractServiceInfoGlobalSearchId.Equals(item.ServiceId)).ToList();
                    });
                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.SaveQueues();
                }
                else
                {
                    throw new ApiException(ret["message"].ToString());
                }

                var retList = new List<GetGlobalSearchCodeStateAsync_Out>();
                search_In.ForEach(item =>
                {
                    var retObj = new GetGlobalSearchCodeStateAsync_Out();

                    retObj.ApplyId = item.ApplyId;
                    retObj.ServiceId = item.ServiceId;
                    if (item.UserList.Count == 0)
                        retObj.AccountStatus = null;
                    else if (item.OpenState == EnumContractServiceOpenState.Void)
                        retObj.AccountStatus = null;
                    //全部正常为正常;2023.10.25改为：存在正常且没有异常为正常
                    else if (item.UserList.Any(e => e.OpeningStatus == EnumContractServiceGlobalSearchUserState.Normal) && item.UserList.All(e => e.OpeningStatus != EnumContractServiceGlobalSearchUserState.AbNormal))
                        retObj.AccountStatus = EnumContractServiceGlobalSearchUserState.Normal;
                    //存在异常且没用停用为异常;2023.10.25改为存在异常为异常
                    else if (item.UserList.Any(e => e.OpeningStatus == EnumContractServiceGlobalSearchUserState.AbNormal))
                        retObj.AccountStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                    //存在停用为停用;2023.10.25改为全部停用为停用
                    else if (item.UserList.All(e => e.OpeningStatus == EnumContractServiceGlobalSearchUserState.Stop))
                        retObj.AccountStatus = EnumContractServiceGlobalSearchUserState.Stop;
                    retObj.AccountStatusName = (retObj.AccountStatus == null) ? "--" : retObj.AccountStatus.GetEnumDescription();
                    //环球搜账号信息
                    retObj.PrimaryUserList = item.UserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).ToList();
                    retObj.SubUserList = item.UserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).ToList();
                    retList.Add(retObj);
                });

                return retList;
            }
            catch (Exception ex)
            {
                throw new ApiException(ex.Message);
            }

        }

        /// <summary>
        /// 赠送环球搜
        /// </summary>
        /// <param name="present_In"></param>
        /// <exception cref="ApiException"></exception>
        public void PresentedGlobalSearchService(PresentedGlobalSearchService_In present_In)
        {
            /*申请流程*/
            DateTime startDate = present_In.ServiceCycleStart.Value;
            DateTime endDate = present_In.ServiceCycleEnd.Value;
            int monthsDifference = ((endDate.Year - startDate.Year) * 12) + endDate.Month - startDate.Month;
            int ServiceMonth = present_In.ServiceMonth.Value;
            if (monthsDifference != ServiceMonth)
                throw new ApiException("服务月份设置不正确");
            Db_crm_contract contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(present_In.ContractId);
            //合同审核通过后，才可申请服务，并只可申请合同内签约产品的服务项目。
            if (contract.ContractStatus != EnumContractStatus.Pass.ToInt())
                throw new ApiException("当前合同状态下，不可以添加合同服务信息-环球搜申请信息");
            //判断当前合同是否存在环球搜产品，如果存在则无法赠送 环球搜产品类型-5
            //if (DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckContractHasGlobalSearchProduct(present_In.ContractId))
            if (DbOpe_crm_contract_productinfo.Instance.CheckContractHasGlobalSearchProduct(present_In.ContractId))
                throw new ApiException("当前合同已经存在环球搜产品，无法赠送");
            if (DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckContractHasPresentedGlobalSearchProduct(present_In.ContractId))
                throw new ApiException("当前合同已经赠送过环球搜产品，无法再次赠送");
            Db_crm_contract_productserviceinfo_globalsearch_appl globalsearch_appl = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.PresentedGlobalSearchService(present_In);
            //如果是续约合同用到，被续约的服务的信息
            var OldServiceInfo = new GetPresentContractGtisAppl_Out();
            if (contract.ContractType == EnumContractType.ReNew.ToInt())
            {
                OldServiceInfo = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetPresentContractGtisAppl(present_In.ContractId);
                globalsearch_appl.OldFirstParty = OldServiceInfo.OldFirstParty;
                DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.Update(globalsearch_appl);
            }
            string dataState = Dictionary.ProcessStatus.First(e => e.Value == globalsearch_appl.State.ToInt().ToString()).Name;
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_globalsearch_appl, Db_crm_contract>("环球搜服务审批流程", globalsearch_appl.Id, globalsearch_appl, contract, present_In.Remark, "待开通", "申请");//dataState, "申请");
            /*初审流程*/
            var now = DateTime.Now;
            //创建服务数据
            var servGlobalSearchInfo = globalsearch_appl.MappingTo<Db_crm_contract_serviceinfo_globalsearch>();
            servGlobalSearchInfo.Id = Guid.NewGuid().ToString();
            servGlobalSearchInfo.ProductServiceInfoGlobalSearchApplId = globalsearch_appl.Id;
            servGlobalSearchInfo.ContractNum = contract.ContractNum;
            servGlobalSearchInfo.State = EnumContractServiceState.TO_BE_REVIEW;
            servGlobalSearchInfo.IsChanged = false;
            /*            servGlobalSearchInfo.Deleted = false;
                        servGlobalSearchInfo.CreateDate = now;
                        servGlobalSearchInfo.CreateUser = UserId;*/
            servGlobalSearchInfo.IsHistory = false;//如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
            servGlobalSearchInfo.RegisteredId = UserId;
            servGlobalSearchInfo.RegisteredTime = now;
            //servGlobalSearchInfo.GlobalSearchRemark = "赠送"; //2025.1.16 去掉备注中的赠送
            servGlobalSearchInfo.SettlementLevel = present_In.SettlementLevel;
            servGlobalSearchInfo.SettlementMonth = present_In.SettlementMonth;
            if (contract.ContractType == EnumContractType.ReNew.ToInt())
            {
                servGlobalSearchInfo.OldContractNum = OldServiceInfo.OldContractNum;
            }
            //插入服务数据队列
            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Insert(servGlobalSearchInfo);
            //开通的workflow记录
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_globalsearch, Db_crm_contract>("环球搜服务审批流程", globalsearch_appl.Id, servGlobalSearchInfo, contract, globalsearch_appl.Feedback, EnumContractServiceOpenState.ToBeReview.GetEnumDescription(), "初审");
        }


        /// <summary>
        /// 自动开通环球搜
        /// </summary>
        /// <param name="gtisServData"></param>
        /// <param name="gtisApplData"></param>
        /// <param name="productId"></param>
        /// <param name="conProductId"></param>
        /// <param name="globalSearchChangeItem"></param>
        public void AutoOpenGlobalSearch(Db_crm_contract_serviceinfo_gtis gtisServData, Db_crm_contract_productserviceinfo_gtis_appl gtisApplData, string productId, string conProductId, List<EnumGlobalSearchModifyType> globalSearchChangeItem)
        {
            /*
             1.补充申请数据
             2.补充审核数据
             3.与环球搜交互，开通新环球搜码或对旧环球搜码进行续约
             4.与gtis交互，调用saveHqsCode接口
             */

            //如果当前服务是变更或续约合同，需要获取旧环球搜服务信息
            var old_globalsearch_serv = new Db_crm_contract_serviceinfo_globalsearch();
            if (gtisServData.ProcessingType == EnumProcessingType.Change.ToInt())//服务变更
                old_globalsearch_serv = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.CheckContractHasGlobalSearchServiceByContractId(gtisServData.ContractId);
            else if (!string.IsNullOrEmpty(gtisServData.OldContractNum))
                old_globalsearch_serv = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(gtisServData.OldContractNum);
            //旧环球搜的账号列表
            var old_globalsearch_serv_users = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(old_globalsearch_serv?.Id);
            #region 1.补充申请数据
            //获取当前合同存在的环球搜服务申请数据
            var oldApplList = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetOldApplList(gtisServData.ContractId);

            oldApplList.ForEach(oldAppl =>
            {
                //如果当前申请为服务变更，需要将旧的申请要置为无效，只保留最新的这个申请
                if (gtisServData.ProcessingType == (int)EnumProcessingType.Change)
                    oldAppl.IsInvalid = (int)EnumIsInvalid.Invalid;
                //如果当前服务状态是待开通，可判断该数据是2025.3.1(需求791)更新前提交申请的环球搜数据，直接删除
                if (oldAppl.State == EnumProcessStatus.Submit.ToInt())
                {
                    oldAppl.Deleted = true;
                    DbOpe_crm_contract_serviceinfo_globalsearch.Instance.DeleteGlobalSearchByApplyId(oldAppl.Id);
                }
                DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.Update(oldAppl);
            });
            //创建申请数据对象 数据状态直接标记为开通
            Db_crm_contract_productserviceinfo_globalsearch_appl globalsearch_appl = gtisApplData.MappingTo<Db_crm_contract_productserviceinfo_globalsearch_appl>();
            globalsearch_appl.Id = Guid.NewGuid().ToString();
            globalsearch_appl.ProductId = productId;
            globalsearch_appl.ContractProductInfoId = conProductId;
            globalsearch_appl.PrimaryAccountsNum = gtisApplData.GlobalSearchAccountCount == 0 ? 0 : 1; ;
            globalsearch_appl.SubAccountsNum = gtisApplData.GlobalSearchAccountCount == 0 ? 0 : (gtisApplData.GlobalSearchAccountCount - 1);
            globalsearch_appl.ServiceCycleStart = gtisServData.ServiceCycleStart;
            globalsearch_appl.ServiceCycleEnd = gtisServData.ServiceCycleEnd;
            globalsearch_appl.State = (int)EnumProcessStatus.Pass;
            globalsearch_appl.IsInvalid = (int)EnumIsInvalid.Effective;
            globalsearch_appl.ServiceMonth = gtisServData.ServiceMonth;
            globalsearch_appl.IsGtisAutoGeneration = true;
            globalsearch_appl.ExecuteServiceCycleStart = gtisServData.GlobalSearchExecuteServiceCycleStart;
            globalsearch_appl.ExecuteServiceCycleEnd = gtisServData.GlobalSearchExecuteServiceCycleEnd;
            globalsearch_appl.ExecuteServiceMonth = gtisServData.GlobalSearchExecuteServiceMonth;
            //插入申请数据
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.Insert(globalsearch_appl);
            #endregion
            #region 2.补充审核数据
            //获取到账绑定关系
            var rrList = DbOpe_crm_contract_receiptregister_service.Instance.GetReceiptRegisterDataByServiceId(gtisServData.Id);
            //创建服务数据
            var globalsearch_serv = gtisServData.MappingTo<Db_crm_contract_serviceinfo_globalsearch>();
            globalsearch_serv.Id = Guid.NewGuid().ToString();
            globalsearch_serv.ProductId = productId;
            globalsearch_serv.ContractProductInfoId = conProductId;
            globalsearch_serv.ProductServiceInfoGlobalSearchApplId = globalsearch_appl.Id;
            globalsearch_serv.PrimaryAccountsNum = gtisServData.GlobalSearchAccountCount == 0 ? 0 : 1;
            globalsearch_serv.SubAccountsNum = gtisServData.GlobalSearchAccountCount == 0 ? 0 : (gtisServData.GlobalSearchAccountCount - 1);
            globalsearch_serv.IsHistory = gtisServData.IsApplHistory;
            globalsearch_serv.SettlementCount = rrList.Sum(e => e.SettlementCount);
            globalsearch_serv.SettlementMonth = gtisServData.GlobalSearchSettlementMonth;
            globalsearch_serv.SettlementLevel = gtisServData.GlobalSearchSettlementLevel;
            globalsearch_serv.RegisteredTime = gtisServData.RegisteredDate;
            globalsearch_serv.ReviewerTime = gtisServData.ReviewerDate;
            globalsearch_serv.HistoryId = globalsearch_serv.ProcessingType == EnumProcessingType.Change.ToInt() ? old_globalsearch_serv?.Id : "";//服务变更才会有historyId
            globalsearch_serv.ExecuteServiceCycleStart = gtisServData.GlobalSearchExecuteServiceCycleStart;
            globalsearch_serv.ExecuteServiceCycleEnd = gtisServData.GlobalSearchExecuteServiceCycleEnd;
            globalsearch_serv.ExecuteServiceMonth = gtisServData.GlobalSearchExecuteServiceMonth;
            //账号生成方式默认取Gtis服务的数据。但若不存在旧的环球搜服务或旧环球搜服务不存在主环球搜码，账号生成方式需要修改为重新生成
            if (old_globalsearch_serv == null || old_globalsearch_serv_users == null || old_globalsearch_serv_users.Count <= 0 || !old_globalsearch_serv_users.Any(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount))
                globalsearch_serv.AccountGenerationMethod = EnumGtisAccountGenerationMethod.Remake.ToInt();
            //插入服务数据队列
            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Insert(globalsearch_serv);
            //插入到账信息绑定
            DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptData(rrList.Select(e => e.ReceiptRegisterId).ToList(), globalsearch_serv.Id, EnumProductType.Global);
            //回写gtis服务表主键关联
            gtisServData.GlobalSearchSerivceId = globalsearch_serv.Id;
            DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateGlobalSeachId(gtisServData.Id, gtisServData.GlobalSearchSerivceId);
            #endregion
            #region 3.与环球搜交互，开通新环球搜码或对旧环球搜码进行续约.此部分逻辑建立在账号数量大于0的基础上
            //定义实际开通的时间 统一取execute前缀的时间作为实际开通的服务周期，但可能存在旧数据此处值为空的情况，则取无execute前缀的时间
            var executeStart = globalsearch_serv.ExecuteServiceCycleStart == null ? globalsearch_serv.ServiceCycleStart : globalsearch_serv.ExecuteServiceCycleStart;
            var executeEnd = globalsearch_serv.ExecuteServiceCycleEnd == null ? globalsearch_serv.ServiceCycleEnd : globalsearch_serv.ExecuteServiceCycleEnd;
            if (gtisServData.GlobalSearchAccountCount > 0)
            {
                //获取gtis账号列表
                var gtisUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(gtisServData.Id);
                var priGtisUser = gtisUserList.Where(e => e.AccountType == (int)EnumGtisAccountType.Main).First();
                var subGtisUserList = gtisUserList.Where(e => e.AccountType == (int)EnumGtisAccountType.Sub).ToList();
                //环球搜码集合
                var gtis_HqsCodeList = new List<string>();
                //Gtis系统交互参数
                var gtis_AllUserInfo = new List<BM_SaveHqsCode_UserInfo>();
                //重新生成账号(新增合同的服务申请 或 选择了重新生成账号)
                if (globalsearch_serv.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake)
                {
                    /*环球搜主账号码处理*/
                    string priHqsCode = string.Empty;
                    //生成环球搜主账号码并保存Db_crm_contract_serviceinfo_globalsearch_user数据
                    AddGlobalSearchPrimaryUser(globalsearch_serv.Id, globalsearch_appl.Id, globalsearch_serv.ContractNum, priGtisUser.AccountNumber, executeStart, executeEnd, globalsearch_serv.SettlementLevel, globalsearch_serv.SettlementMonth, globalsearch_serv.GlobalSearchRemark, out priHqsCode);
                    //gtis_user主账号补充环球搜绑定信息
                    priGtisUser.ContractServiceInfoGlobalSearchId = globalsearch_serv.Id;
                    priGtisUser.GlobalSearchCode = priHqsCode;
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(priGtisUser);
                    //创建主账号与gtis交互对象
                    gtis_HqsCodeList.Add(priHqsCode);
                    gtis_AllUserInfo.Add(new BM_SaveHqsCode_UserInfo() { CrmId = priGtisUser.Id, Uid = priGtisUser.AccountNumber, HqsCode = priHqsCode });
                    //循环生成环球搜子账号码并保存Db_crm_contract_serviceinfo_globalsearch_user数据.方便续约时处理,环球搜要的globalwits_name参数都统一使用gtis主账号的账号名AccountNumber
                    var subHqsCodeList = new List<string>();
                    for (var i = 0; i < globalsearch_serv.SubAccountsNum; i++)
                    {
                        string subHqsCode = string.Empty;
                        AddGlobalSearchSubUser(globalsearch_serv.Id, globalsearch_appl.Id, priHqsCode, globalsearch_serv.ContractNum, priGtisUser.AccountNumber, executeStart, executeEnd, globalsearch_serv.GlobalSearchRemark, out subHqsCode);
                        subHqsCodeList.Add(subHqsCode);
                        gtis_HqsCodeList.Add(subHqsCode);
                    }

                    //gtis_user子账号补充环球搜绑定信息
                    for (var i = 0; i < subGtisUserList.Count; i++)
                    {
                        string hqsCode = string.Empty;
                        // 如果环球搜子账号数量为0 ，都绑定主账号
                        if (subHqsCodeList.Count == 0)
                            hqsCode = priHqsCode;
                        else if (i < subHqsCodeList.Count)
                            hqsCode = subHqsCodeList[i].ToString();
                        else
                        {
                            int index = i;
                            while (index >= subHqsCodeList.Count)
                            {
                                index -= subHqsCodeList.Count;
                            }
                            hqsCode = subHqsCodeList[index].ToString();
                        }
                        subGtisUserList[i].ContractServiceInfoGlobalSearchId = globalsearch_serv.Id;
                        subGtisUserList[i].GlobalSearchCode = hqsCode;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(subGtisUserList[i]);
                        //创建子账号与gtis交互对象
                        gtis_AllUserInfo.Add(new BM_SaveHqsCode_UserInfo() { CrmId = subGtisUserList[i].Id, Uid = subGtisUserList[i].AccountNumber, HqsCode = subGtisUserList[i].GlobalSearchCode });
                    }
                    #region 2025.2.26注释 环球搜码可以被重复绑定,故改为上面两段内容:先批量生成子账号再循环绑定的逻辑,故注销
                    /*环球搜子账号码处理*//*
                    //循环生成环球搜子账号码并保存Db_crm_contract_serviceinfo_globalsearch_user数据
                    subGtisUserList.ForEach(subGtisUser =>
                    {
                        string subHqsCode = string.Empty;
                        AddGlobalSearchSubUser(globalsearch_serv.Id, globalsearch_appl.Id, priHqsCode, globalsearch_serv.ContractNum,
                            *//*subGtisUser.AccountNumber, 2025.2.26注释.方便续约时处理,环球搜要的globalwits_name参数都统一使用gtis主账号的账号名AccountNumber*//*priGtisUser.AccountNumber,
                            globalsearch_serv.ServiceCycleStart, globalsearch_serv.ServiceCycleEnd, globalsearch_serv.GlobalSearchRemark, out subHqsCode);
                        gtis_HqsCodeList.Add(subHqsCode);
                        //gtis_user补充环球搜信息
                        subGtisUser.ContractServiceInfoGlobalSearchId = globalsearch_serv.Id;
                        subGtisUser.GlobalSearchCode = subHqsCode;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(subGtisUser);
                        //创建子账号与gtis交互对象
                        gtis_AllUserInfo.Add(new BM_SaveHqsCode_UserInfo() { CrmId = subGtisUser.Id, Uid = subGtisUser.AccountNumber, HqsCode = subHqsCode });
                    });*/
                    #endregion

                }
                //续约或服务变更，使用原有账号
                else if (globalsearch_serv.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate)
                {
                    //确定环球搜的主账号.此时gtis主账号绑定的可能不是主环球搜码,所以需要从旧服务的环球搜码列表中查找主环球搜码
                    var priHqsCode = old_globalsearch_serv_users.First(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).AccountNumber;
                    //环球搜主账号操作续约.主账号续约,子账号自动续约.此方法会自动插入Db_crm_contract_serviceinfo_globalsearch_user数据
                    UpdateGlobalSearchUser(globalsearch_serv.Id, globalsearch_appl.Id, priHqsCode, globalsearch_serv.ContractNum, priGtisUser.AccountNumber, executeStart, executeEnd, globalsearch_serv.SettlementLevel, globalsearch_serv.SettlementMonth, globalsearch_serv.GlobalSearchRemark);
                    //补充gtis_HqsCodeList交互参数
                    gtis_HqsCodeList.Add(priHqsCode);
                    #region 此段内容注释.2025.2.26环球搜所有账号开通/续约操作,globalwits_name的值均传主账号值,所以不再考虑当前的绑定关系
                    /* //判断gtis主账号是否绑定了环球搜主码，若没有，确定绑定主环球搜码的gtis账号
                     var priHqsCode_gtisuser = (priHqsCode == priGtisUser.GlobalSearchCode) ? priGtisUser : gtisUserList.First(e => e.GlobalSearchCode == priHqsCode);
                     //若此时priHqsCode_gtisuser依然为null，说明没有gtis账号绑定了环球搜主码，需要分配一个账号进行绑定
                     if (priHqsCode_gtisuser == null)
                     {
                         if (!gtisUserList.Any(e => string.IsNullOrEmpty(e.GlobalSearchCode)))
                         {//如果不存在未绑定环球搜码的gtis账号，则无法分配账号绑定，令priHqsCode_gtisuser赋值为主账号，满足环球搜续约接口的参数要求
                             priHqsCode_gtisuser = priGtisUser;
                         }
                         else
                         {//存在未绑定环球搜码的gtis账号，优先绑定gtis主账号
                             //查找未绑定环球搜码的gtis账号列表
                             var gtisUser_unBindHqs_list = gtisUserList.Where(e => string.IsNullOrEmpty(e.GlobalSearchCode)).OrderBy(e => e.AccountNumber).ToList();
                             if (gtisUser_unBindHqs_list.Any(e => e.AccountType == (int)EnumGtisAccountType.Main))//存在gtis主账号，优先绑定
                                 priHqsCode_gtisuser = gtisUser_unBindHqs_list.First(e => e.AccountType == (int)EnumGtisAccountType.Main);
                             else//不存在，绑定排在第一位的gtis账号
                                 priHqsCode_gtisuser = gtisUser_unBindHqs_list.First();
                         }

                     }
                     //环球搜主账号操作续约。主账号续约，子账号自动续约
                     UpdateGlobalSearchUser(globalsearch_serv.Id, globalsearch_appl.Id, priHqsCode, globalsearch_serv.ContractNum, priHqsCode_gtisuser.AccountNumber, globalsearch_serv.ServiceCycleStart, globalsearch_serv.ServiceCycleEnd, globalsearch_serv.SettlementLevel, globalsearch_serv.SettlementMonth, globalsearch_serv.GlobalSearchRemark);
                     //补充gtis_HqsCodeList交互参数
                     gtis_HqsCodeList.Add(priHqsCode);*/
                    #endregion
                    //复制旧服务的所有子环球搜码，插入到globalsearch_user表
                    var old_globalsearch_serv_subusers = old_globalsearch_serv_users.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).ToList();
                    if (old_globalsearch_serv_subusers != null && old_globalsearch_serv_subusers.Count > 0)
                    {
                        old_globalsearch_serv_subusers.ForEach(subUser =>
                        {
                            var user = new Db_crm_contract_serviceinfo_globalsearch_user();
                            user.ContractServiceInfoGlobalSearchId = globalsearch_serv.Id;
                            user.ProductServiceInfoGlobalSearchApplId = globalsearch_serv.ProductServiceInfoGlobalSearchApplId;
                            user.AccountNumber = subUser.AccountNumber;
                            user.AccountType = EnumContractServiceGlobalSearchAccountType.SubAccount;
                            user.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                            user.StartDate = globalsearch_serv.ServiceCycleStart;
                            user.EndDate = globalsearch_serv.ServiceCycleEnd;
                            DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertData(user);
                            //补充gtis_HqsCodeList交互参数
                            gtis_HqsCodeList.Add(user.AccountNumber);
                        });
                    }
                    //获取当前环球搜子账号数量与旧环球搜服务子账号数量的差值,即：当前环球搜子账号 - 旧环球搜子账号(因为主账号都是1，计算差值时忽略)
                    var differValue = globalsearch_serv.SubAccountsNum.GetValueOrDefault(0) - old_globalsearch_serv_users.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).Count();
                    if (differValue > 0)
                    {
                        for (int i = 0; i < differValue; i++)
                        {
                            string subHqsCode = string.Empty;
                            AddGlobalSearchSubUser(globalsearch_serv.Id, globalsearch_appl.Id, priHqsCode, globalsearch_serv.ContractNum, priGtisUser.AccountNumber, globalsearch_serv.ServiceCycleStart, globalsearch_serv.ServiceCycleEnd, globalsearch_serv.GlobalSearchRemark, out subHqsCode);
                            gtis_HqsCodeList.Add(subHqsCode);
                        }
                    }
                    //已绑定环球搜码的gtis账号list
                    var gtisUser_hadBoundHqs_list = gtisUserList.Where(e => !string.IsNullOrEmpty(e.GlobalSearchCode)).OrderBy(e => e.AccountNumber).ToList();
                    if (gtisUser_hadBoundHqs_list != null && gtisUser_hadBoundHqs_list.Count > 0)
                    {
                        gtisUser_hadBoundHqs_list.ForEach(gtisUser =>
                        {
                            //修改或补充环球搜服务Id，并更新crm中的gtis_user表信息
                            gtisUser.ContractServiceInfoGlobalSearchId = globalsearch_serv.Id;
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(gtisUser);
                            //填充gtis_AllUserInfo的交互数据
                            gtis_AllUserInfo.Add(new BM_SaveHqsCode_UserInfo() { CrmId = gtisUser.Id, Uid = gtisUser.AccountNumber, HqsCode = gtisUser.GlobalSearchCode });
                        });
                    }
                    #region 针对新增gtis账号处理绑定关系，这段暂缓开发
                    /*//没绑定环球搜码的gtis账号list
                    var gtisUser_unBindHqs_list = gtisUserList.Where(e => string.IsNullOrEmpty(e.GlobalSearchCode)).OrderBy(e => e.AccountNumber).ToList();
                    //如果存在未绑定环球搜码的gtis账号
                    if (gtisUser_unBindHqs_list != null && gtisUser_unBindHqs_list.Count > 0)
                    { 
                    }*/
                    #endregion
                    #region  2025.2.26注释. gtis和环球搜的账号数量关系不再保持一致,这段内容中涉及新增环球搜码以及gtis账号绑定环球搜码的逻辑已被重构,故注释不再使用
                    /*//当前环球搜服务的账号列表
                    var globalsearch_serv_users = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(globalsearch_serv?.Id);
                    //获取当前gtis服务账号数量与当前环球搜服务账号数量的差值(gtis - 环球搜)
                    var differValue = gtisUserList.Count() - globalsearch_serv_users.Count();
                    //判断gtis账号是否都已绑定了环球搜码
                    if (!gtisUserList.Any(e => string.IsNullOrEmpty(e.GlobalSearchCode)) && differValue > 0)
                    {//若gtis账号均已绑定环球搜码 且 gtis账号数多于环球搜账号数，需要补充环球搜码保持和gtis账号数一致,但不需要绑定gtis账号
                        for (int i = 0; i < differValue; i++)
                        {
                            string subHqsCode = string.Empty;
                            AddGlobalSearchSubUser(globalsearch_serv.Id, globalsearch_appl.Id, priHqsCode, globalsearch_serv.ContractNum, priHqsCode_gtisuser.AccountNumber, globalsearch_serv.ServiceCycleStart, globalsearch_serv.ServiceCycleEnd, globalsearch_serv.GlobalSearchRemark, out subHqsCode);
                            gtis_HqsCodeList.Add(subHqsCode);
                        }
                    }
                    else if (gtisUserList.Any(e => string.IsNullOrEmpty(e.GlobalSearchCode)))
                    {
                        //没绑定环球搜码的gtis账号list
                        var gtisUser_unBindHqs_list = gtisUserList.Where(e => string.IsNullOrEmpty(e.GlobalSearchCode)).OrderBy(e => e.AccountNumber).ToList();
                        //没绑定gtis账号的环球搜码list
                        var hqsCode_unbindGtis_list = gtis_HqsCodeList.Except(gtisUserList.Where(e => !string.IsNullOrEmpty(e.GlobalSearchCode)).Select(e => e.GlobalSearchCode).ToList()).ToList();
                        foreach (var gtisUser in gtisUser_unBindHqs_list)
                        {
                            var globalSearchCode = hqsCode_unbindGtis_list.FirstOrDefault();
                            gtisUser.ContractServiceInfoGlobalSearchId = globalsearch_serv.Id;
                            gtisUser.GlobalSearchCode = globalSearchCode;
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(gtisUser);
                            //填充gtis_AllUserInfo的交互数据
                            gtis_AllUserInfo.Add(new BM_SaveHqsCode_UserInfo() { CrmId = gtisUser.Id, Uid = gtisUser.AccountNumber, HqsCode = gtisUser.GlobalSearchCode });
                            //未被使用的环球搜码list删除刚刚绑定的数据，这个list为空时跳出循环
                            hqsCode_unbindGtis_list.Remove(globalSearchCode);
                            if (hqsCode_unbindGtis_list.Count == 0)
                                break;
                        }
                        //gtis账号数多于环球搜账号数，那此时还存在没绑定环球搜的gtis账号
                        if (differValue > 0)
                        {
                            //此时再查一遍 没绑定环球搜码的gtis账号list
                            gtisUser_unBindHqs_list = gtisUserList.Where(e => string.IsNullOrEmpty(e.GlobalSearchCode)).OrderBy(e => e.AccountNumber).ToList();
                            for (int i = 0; i < differValue; i++)
                            {
                                //确定调用生成环球搜码接口时需要传的gtis账号，如果i超出gtisUser_unbindHqs_list元素个数，参数使用gtis主账号
                                var gtisUser = new Db_crm_contract_serviceinfo_gtis_user(); ;
                                if (i <= gtisUser_unBindHqs_list.Count() - 1)
                                {
                                    gtisUser = gtisUser_unBindHqs_list[i];
                                }
                                else
                                {
                                    gtisUser = priGtisUser;
                                }
                                //生成新的环球搜子账号
                                string subHqsCode = string.Empty;
                                AddGlobalSearchSubUser(globalsearch_serv.Id, globalsearch_appl.Id, priHqsCode, globalsearch_serv.ContractNum, gtisUser.AccountNumber, globalsearch_serv.ServiceCycleStart, globalsearch_serv.ServiceCycleEnd, globalsearch_serv.GlobalSearchRemark, out subHqsCode);
                                //如果当前索引值小于gtisUser_unbindHqs_list的元素个数，需要填充gtis_AllUserInfo的交互数据
                                if (i < gtisUser_unBindHqs_list.Count())
                                {
                                    gtis_AllUserInfo.Add(new BM_SaveHqsCode_UserInfo() { CrmId = gtisUser_unBindHqs_list[i].Id, Uid = gtisUser_unBindHqs_list[i].AccountNumber, HqsCode = subHqsCode });
                                }
                                gtis_HqsCodeList.Add(subHqsCode);
                            }
                        }
                    }*/
                    #endregion
                }
                //整合最终Gtis交互参数，记录日志参数，调用GTIS系统保存环球搜码接口
                var param = new BM_SaveHqsCode { isNoUser = false, AllUserInfo = gtis_AllUserInfo, HqsCodeList = gtis_HqsCodeList };
                LogUtil.AddLog("svCode:" + globalsearch_serv.ContractNum + ";   AllUserInfo:" + JsonConvert.SerializeObject(gtis_AllUserInfo) + ";   HqsCodeList:" + String.Join(',', gtis_HqsCodeList));
                BLL_GtisOpe.Instance.SaveHqsCode(param).Wait();
            }
            #endregion
            #region 4.处理被续约或被变更的旧服务数据
            //续约服务，需要将老的服务置为作废
            if (!string.IsNullOrEmpty(globalsearch_serv.OldContractNum))
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateOldGlobalSearchServiceVoid(globalsearch_serv.OldContractNum, UserId);
            //变更服务，补充Change
            if (globalsearch_serv.ProcessingType == EnumProcessingType.Change.ToInt() && old_globalsearch_serv != null)
            {
                old_globalsearch_serv.State = EnumContractServiceState.INVALID;
                old_globalsearch_serv.ChangedId = globalsearch_serv.Id;
                old_globalsearch_serv.IsChanged = true;
                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.Update(old_globalsearch_serv);
            }
            #endregion
            #region 5.记录ModifyType
            if (globalSearchChangeItem.Count == 0)
            {
                var modifyTypeData = new Db_crm_contract_serviceinfo_globalsearch_modifytype();
                modifyTypeData.ContractServiceInfoGlobalSearchId = globalsearch_serv.Id;
                modifyTypeData.ProductServiceInfoGlobalSearchApplId = globalsearch_serv.ProductServiceInfoGlobalSearchApplId;
                modifyTypeData.ContractNum = globalsearch_serv.ContractNum;
                modifyTypeData.ModifyType = EnumGlobalSearchModifyType.NewService;
                DbOpe_crm_contract_serviceinfo_globalsearch_modifytype.Instance.InsertData(modifyTypeData);
            }
            else
            {
                foreach (var item in globalSearchChangeItem)
                {
                    var modifyTypeData = new Db_crm_contract_serviceinfo_globalsearch_modifytype();
                    modifyTypeData.ContractServiceInfoGlobalSearchId = globalsearch_serv.Id;
                    modifyTypeData.ProductServiceInfoGlobalSearchApplId = globalsearch_serv.ProductServiceInfoGlobalSearchApplId;
                    modifyTypeData.ContractNum = globalsearch_serv.ContractNum;
                    modifyTypeData.ModifyType = item;
                    DbOpe_crm_contract_serviceinfo_globalsearch_modifytype.Instance.InsertData(modifyTypeData);
                }
            }
            
            #endregion
        }

        /// <summary>
        /// 获取被续约合同的环球搜服务信息
        /// </summary>
        /// <param name="getOldGlobalSearchServiceInfo_In"></param>
        /// <returns></returns>
        public GetOldGlobalSearchServiceInfo_Out? GetOldGlobalSearchServiceInfo(GetOldGlobalSearchServiceInfo_In getOldGlobalSearchServiceInfo_In)
        {
            GetOldGlobalSearchServiceInfo_Out? retObj = new GetOldGlobalSearchServiceInfo_Out();
            var oldContract = new Db_crm_contract();
            string renewFirstParty = string.Empty;
            if (!string.IsNullOrEmpty(getOldGlobalSearchServiceInfo_In.ContractNum))
            {
                oldContract = DbOpe_crm_contract.Instance.GetContractByContractNum(getOldGlobalSearchServiceInfo_In.ContractNum);
            }

            else
            {
                if (string.IsNullOrEmpty(getOldGlobalSearchServiceInfo_In.FirstParty))
                    getOldGlobalSearchServiceInfo_In.FirstParty = DbOpe_crm_contract.Instance.QueryByPrimaryKey(getOldGlobalSearchServiceInfo_In.ContractId).FirstParty;
                oldContract = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetLastSameFirstPartyServiceContractInfo(getOldGlobalSearchServiceInfo_In.FirstParty, getOldGlobalSearchServiceInfo_In.ContractId);
            }

            if (oldContract != null) //存在这样的历史合同
            {
                //判断历史合同存在环球搜服务，存在环球搜服务可以使用原有，因为有赠送服务的情况，需要根据合同ID去服务表中查询
                var oldService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.CheckContractHasGlobalSearchServiceByContractId(oldContract.Id);
                //被续约的服务信息
                if (oldService != null)
                {
                    retObj = oldService.MappingTo<GetOldGlobalSearchServiceInfo_Out>();
                    retObj.ProductName = DbOpe_crm_product.Instance.QueryByPrimaryKey(oldService.ProductId).ProductName;
                    retObj.ServiceCycle = oldService.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "--" + oldService.ServiceCycleEnd.Value.ToString("yyyy-MM-dd");
                }
                else
                    retObj.ContractNum = oldContract.ContractNum;
                retObj.RenewFirstParty = oldContract.FirstParty;

            }
            return retObj;
        }

        /// <summary>
        /// 根据申请ID获取环球搜开通账号信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public List<GetContractServiceInfoGlobalSearchUserListByApplId_Out> GetContractServiceInfoGlobalSearchUserListByApplId(string Id)
        {
            var retList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetContractServiceInfoGlobalSearchUserListByApplId(Id);
            var codeList = retList.Where(e => !string.IsNullOrEmpty(e.GlobalSearchCode)).Select(e => e.GlobalSearchCode).ToList();
            if (codeList.Count == 0)
                return new List<GetContractServiceInfoGlobalSearchUserListByApplId_Out>();
            var queryStr = string.Join(';', codeList);
            var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, queryStr);
            try
            {
                var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                if ("200".Equals(ret["status"].ToString()))
                {
                    var results = ret["results"].ToList();
                    results.ForEach(result =>
                    {
                        var code = codeList.Find(e => e == result["idCst"].ToString());
                        EnumContractServiceGlobalSearchUserState openingStatus = new EnumContractServiceGlobalSearchUserState();
                        if ("active".Equals(result["status"].ToString()))
                            //返回正常，账号状态标记正常
                            openingStatus = EnumContractServiceGlobalSearchUserState.Normal;
                        else if ("disabled".Equals(result["status"].ToString()))
                            //返回过期，账号状态标记停用
                            openingStatus = EnumContractServiceGlobalSearchUserState.Stop;
                        else if ("noexisted".Equals(result["status"].ToString()))
                            //返回不存在，账号状态标记异常
                            openingStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                        retList.Find(e => e.GlobalSearchCode == code).OpeningStatus = openingStatus;

                        var startDate = result["begindate"].ToString();
                        var endDate = result["enddate"].ToString();
                        retList.Find(e => e.GlobalSearchCode == code).StartDate = startDate;
                        retList.Find(e => e.GlobalSearchCode == code).EndDate = endDate;

                        DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.UpdateServiceUserOpeningStatusByNumber(code, openingStatus, startDate, endDate);
                    });
                }
                else
                {
                    throw new ApiException(ret["message"].ToString());
                }
                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.SaveQueues();
                return retList;
            }
            catch (Exception ex)
            {
                throw new ApiException(ex.Message);
            }
        }

        /// <summary>
        /// 根据查询条件获取合同服务信息环球搜已开通的编码信息
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        public Stream DownloadGlobalSearchCodeInfo(DownloadGlobalSearchCodeInfo_In search_In)
        {
            var list = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetDownloadGlobalSearchCodeInfo(search_In);
            //var exporter = new ExcelExporter();
            //var result = await exporter.ExportAsByteArray<DownloadGlobalSearchCodeInfoExcelDto>(list);            
            var exporter = new ExcelExporterNPOI();
            var result = exporter.ExportAsByteArray<DownloadGlobalSearchCodeInfoExcelDto>(list);
            var fs = new MemoryStream(result);
            return fs;
        }


        ///// <summary>
        ///// 下载环球搜开通记录
        ///// </summary>
        ///// <returns></returns>
        //public async Task DownloadGlobalSearchCodeInfo()
        //{
        //    IExporter exporter = new ExcelExporter();

        //    var result = await exporter.Export("GlobalSearchCodeInfo.xlsx", new List<DownloadGlobalSearchCodeInfoExcelDto>()
        //        {
        //            new DownloadGlobalSearchCodeInfoExcelDto
        //            {
        //                ContractNum = "MR.A",
        //                PrimaryAccNum = 1,
        //                SubAccNum =3,
        //                ReviewerTime="202405",
        //                ServiceMonth = 3
        //            },
        //            new DownloadGlobalSearchCodeInfoExcelDto
        //            {
        //                ContractNum = "MR.B",
        //                PrimaryAccNum = 1,
        //                SubAccNum =3,
        //                ReviewerTime="202405",
        //                ServiceMonth = 3
        //            },
        //            new DownloadGlobalSearchCodeInfoExcelDto
        //            {
        //                ContractNum = "MR.C",
        //                PrimaryAccNum = 1,
        //                SubAccNum =3,
        //                ReviewerTime="202405",
        //                ServiceMonth = 3
        //            }
        //        });
        //}

        public GetContractServiceInfoGlobalSearchByApplId_Out GetContractServiceInfoGlobalSearchByApplId(string Id)
        {
            //获取申请详细信息
            var apply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetContractServiceInfoGlobalSearchByApplId(Id);
            //获取合同详细信息
            apply.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);
            //旧合同信息
            var oldContract = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetLastSameFirstPartyServiceContractInfo(apply.OldFirstParty, apply.ContractId);
            #region 历史服务信息
            if (oldContract != null && apply.ContractInfo != null && apply.ContractInfo.ContractType != null)
            {
                apply.HistoryGlobalSearchServiceList = GetHistoryGlobalSearchServiceList(apply.Id, apply.ContractInfo.ContractType.Value, apply.ProcessingType, oldContract.ContractNum, apply.ContractInfo.ContractNum);
            }
            else
            {
                apply.HistoryGlobalSearchServiceList = new List<HistoryGlobalsearchServiceInfo>();
            }
            #endregion
            return apply;
        }

        /// <summary>
        /// 获取历史服务信息
        /// </summary>
        /// <param name="hisParamApplyId"></param>
        /// <param name="contractType"></param>
        /// <param name="processingType"></param>
        /// <param name="hisParamContractNum"></param>
        /// <param name="contractNum"></param>
        /// <returns></returns>
        private List<HistoryGlobalsearchServiceInfo> GetHistoryGlobalSearchServiceList(string hisParamApplyId, int contractType, int processingType, string hisParamContractNum, string contractNum)
        {
            var retList = new List<HistoryGlobalsearchServiceInfo>();
            //判断是否是续约合同的服务
            var isRenew = contractType == (int)EnumContractType.ReNew;
            //判断是否是变更的服务
            var isChange = processingType == (int)EnumProcessingType.Change;
            /* //查找历史数据要用的ContractId
             var hisParamApplyId = apply.Id;
             //查找历史数据要用的ContractNum
             var hisParamContractNum = oldContract.ContractNum;*/
            //如果是续约合同的服务或是变更的服务，需要查找历史数据
            while (isRenew || isChange)
            {
                //声明历史数据
                var hisServe = new HistoryGlobalsearchServiceInfo_Mid();
                //如果当前服务是变更数据，取被变更的服务信息
                if (isChange)
                    //获取被变更的服务数据信息
                    hisServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetChangedGlobalSearchServiceInfoByContractId(hisParamApplyId, contractNum);
                else
                    //根据客户编码获取被续约服务信息
                    hisServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceInfoByContractNum(hisParamContractNum);
                if (hisServe != null)
                {
                    //修改isRenew 判断是否是续约合同的服务
                    isRenew = !string.IsNullOrEmpty(hisServe.OldContractNum);
                    //修改isChange 判断是否是变更的服务
                    isChange = hisServe.ProcessingType == (int)EnumProcessingType.Change;
                    //修改查找历史数据要用的ContractId
                    hisParamApplyId = hisServe.ApplyId;
                    //修改查找历史数据要用的ContractNum
                    hisParamContractNum = hisServe.OldContractNum;
                    //填充返回列表
                    retList.Add(hisServe.MappingTo<HistoryGlobalsearchServiceInfo>());
                }
                else
                {
                    isRenew = false;
                    isChange = false;
                }
            }
            return retList;
        }

        public void SynchroGlobalUserData()
        {
            DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.TransDeal(() =>
            {
                //获取2024-09-26 14:53之前的初审状态的环球搜服务申请
                var dataList = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetInAuditApplDataListBefore202409261453();
                //获取2024-08-26 11:02之前的复核状态的环球搜服务申请
                dataList.AddRange(DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetInReviewApplDataListBefore202408261102());
                //获取客户编码26580的数据
                var singleData_26580 = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetApplDataByContractNum26580();
                if (singleData_26580.ServiceOpenState == EnumContractServiceOpenState.ToBeOpened || singleData_26580.ServiceOpenState == EnumContractServiceOpenState.Returned)
                    singleData_26580.IsAudit = true;
                else
                    singleData_26580.IsAudit = false;
                dataList.Add(singleData_26580);
                var now = DateTime.Now;
                //处理初审状态的数据
                foreach (var it in dataList)
                {
                    //判断当前合同是否包含gtis服务
                    var gtisCase = DbOpe_crm_contract_productinfo.Instance.CheckHasUnValidGTISService(it.ContractId);
                    //根据客户编码，获取G5账号信息
                    var gtisOpeUserList = BLL_GtisOpe.Instance.GetUserInfo(it.ContractNum).Result;
                    //没查询到g5信息情况待处理
                    if (gtisOpeUserList == null || gtisOpeUserList.Count == 0)
                    {
                        if (gtisCase == EnumContractGtisProductServiceState.Opened)
                        {//如果 gtisOpeUserList 没有结果，作废gtis服务
                            var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(it.ContractNum);
                            DbOpe_crm_contract_serviceinfo_gtis.Instance.VoidGtisServiceByServiceId(gtis.Id);
                        }
                        continue;
                    }
                    //获取环球搜编码列表，去重
                    var globalCodeList = gtisOpeUserList.DistinctBy(e => e.GlobalSearchCode).ToList();
                    #region 修改申请数据状态
                    var applyData = it.MappingTo<Db_crm_contract_productserviceinfo_globalsearch_appl>();
                    applyData.State = EnumProcessStatus.Pass.ToInt();
                    applyData.ReviewerDate = now;
                    applyData.ReviewerId = UserId;
                    applyData.Feedback = "同步数据自动通过";
                    applyData.Remark4List = "同步数据自动通过";
                    DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateData(applyData);
                    #endregion
                    #region 处理驳回数据
                    if (it.ServiceOpenState == EnumContractServiceOpenState.Returned)
                    {
                        var returnData = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetData(g => g.ProductServiceInfoGlobalSearchApplId == applyData.Id && g.IsHistory == false);
                        returnData.IsHistory = true;
                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(returnData);
                    }
                    #endregion
                    #region 创建或修改服务数据
                    Db_crm_contract_serviceinfo_globalsearch serviceData = new Db_crm_contract_serviceinfo_globalsearch();
                    if (it.IsAudit)
                    {
                        serviceData.ContractNum = it.ContractNum;
                        serviceData.ContractId = it.ContractId;
                        serviceData.ProductId = it.ProductId;
                        serviceData.ContractProductInfoId = it.ContractProductInfoId;
                        serviceData.ServiceCycleStart = it.ServiceCycleStart;
                        serviceData.ServiceCycleEnd = it.ServiceCycleEnd;
                        serviceData.ServiceMonth = it.ServiceMonth;
                        serviceData.State = EnumContractServiceState.VALID;
                        serviceData.IsChanged = false;
                        serviceData.IsHistory = false;//如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
                        serviceData.RegisteredId = UserId;
                        serviceData.RegisteredTime = now;
                        serviceData.RegisteredRemark = "同步数据自动通过";
                        serviceData.ProcessingType = it.ProcessingType;
                        serviceData.ReviewerId = UserId;
                        serviceData.ReviewerTime = now;
                        serviceData.ReviewerRemark = "同步数据自动通过";
                        serviceData.UpdateDate = now;
                        serviceData.UpdateUser = UserId;
                        serviceData.GlobalSearchRemark = String.Empty;
                        serviceData.Remark = String.Empty;
                        serviceData.Id = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.InsertDataReturnId(serviceData).ToString();
                    }
                    else
                    {
                        serviceData = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(it.ContractNum);
                        serviceData.State = EnumContractServiceState.VALID;
                        serviceData.ReviewerId = UserId;
                        serviceData.ReviewerTime = now;
                        serviceData.ReviewerRemark = "同步数据自动通过";
                        serviceData.GlobalSearchRemark = String.Empty;
                        serviceData.Remark = String.Empty;
                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(serviceData);
                    }

                    #endregion
                    #region 创建环球搜Code数据
                    globalCodeList.ForEach(code =>
                    {
                        var user = new Db_crm_contract_serviceinfo_globalsearch_user()
                        {
                            ContractServiceInfoGlobalSearchId = serviceData.Id,
                            ProductServiceInfoGlobalSearchApplId = applyData.Id,
                            AccountNumber = code.GlobalSearchCode,
                            PassWord = code.PassWord,
                            AccountType = code.AccountType == 0 ? EnumContractServiceGlobalSearchAccountType.PrimaryAccount : EnumContractServiceGlobalSearchAccountType.SubAccount,
                            OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal,
                            StartDate = code.StartServerDate,
                            EndDate = code.EndServerDate,
                            LoginIP = code.LastLoginPlace,
                            LastLoginTime = code.LastLoginDate
                        };
                        DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertData(user);
                    });
                    #endregion
                    //判断是否存在gtis服务
                    if (gtisCase == EnumContractGtisProductServiceState.Inexistence)
                    {//不存在
                        //创建gtis_user
                        gtisOpeUserList.ForEach(gtisUser =>
                        {
                            var user = new Db_crm_contract_serviceinfo_gtis_user()
                            {
                                UserId = gtisUser.SysUserID,
                                AccountNumber = gtisUser.AccountNumber,
                                PassWord = gtisUser.PassWord,
                                AccountType = (int)(gtisUser.AccountType == 0 ? EnumGtisAccountType.Main : EnumGtisAccountType.Sub),
                                OpeningStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.TransGtisUserStates(gtisUser.State),
                                StartDate = gtisUser.StartServerDate,
                                EndDate = gtisUser.EndServerDate,
                                LoginIP = gtisUser.LastLoginPlace,
                                LastLoginTime = gtisUser.LastLoginDate,
                                SharePeopleNum = gtisUser.SharingTimesUse,
                                PhoneUser = (gtisUser.AllPhoneUserInfo.Count > 0) ? gtisUser.AllPhoneUserInfo.First().LinkMan : "",
                                PhoneUserNum = gtisUser.AllPhoneUserInfo.Count,
                                AuthorizationNum = gtisUser.AuthorizationNum,
                                IsProcessed = (int)EnumGtisServiceIsProcess.Processed,
                                ProcessedTime = now,
                                UpdateUser = UserId,
                                UpdateDate = now,
                                ContractServiceInfoGlobalSearchId = serviceData.Id,
                                GlobalSearchCode = gtisUser.GlobalSearchCode,
                            };
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertData(user);
                        });
                    }
                    else if (gtisCase == EnumContractGtisProductServiceState.Opened)
                    {//存在且已开通
                        //获取gtis服务信息
                        var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(it.ContractNum);
                        //开通、即将到期、过期状态需要同步校准
                        if (gtis.State == (int)EnumContractServiceState.VALID || gtis.State == (int)EnumContractServiceState.OUT)
                        {
                            #region GTIS同步
                            //待返回的Gtis用户列表
                            var retGtisUserList = new List<Db_crm_contract_serviceinfo_gtis_user>();
                            //获取当前服务中的gtis用户信息
                            var crmGtisUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetGtisUsersByServeId(gtis.Id);
                            var crmGtisUserIds = crmGtisUserList.Select(e => e.UserId).ToList();
                            var sysUserIDs = gtisOpeUserList.Select(e => e.SysUserID).ToList();
                            //交集--需要刷新的用户信息
                            var intersectUserIds = crmGtisUserIds.Intersect(sysUserIDs).ToList();
                            intersectUserIds.ForEach(sysUserId =>
                            {
                                //g5返回的用户信息
                                var gtisOpeUser = gtisOpeUserList.Find(e => e.SysUserID == sysUserId);
                                //当前crm保存的用户信息
                                var crmGtisUser = crmGtisUserList.Find(e => e.UserId == sysUserId);
                                //修改crmGtisUser的属性
                                crmGtisUser.OpeningStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.TransGtisUserStates(gtisOpeUser.State);
                                crmGtisUser.StartDate = gtisOpeUser.StartServerDate;
                                crmGtisUser.EndDate = gtisOpeUser.EndServerDate;
                                crmGtisUser.LoginIP = gtisOpeUser.LastLoginPlace;
                                crmGtisUser.LastLoginTime = gtisOpeUser.LastLoginDate;
                                crmGtisUser.SharePeopleNum = gtisOpeUser.SharingTimesUse;
                                crmGtisUser.PhoneUser = (gtisOpeUser.AllPhoneUserInfo.Count > 0) ? gtisOpeUser.AllPhoneUserInfo.First().LinkMan : "";
                                crmGtisUser.PhoneUserNum = gtisOpeUser.AllPhoneUserInfo.Count;
                                crmGtisUser.AuthorizationNum = gtisOpeUser.AuthorizationNum;
                                crmGtisUser.ContractServiceInfoGlobalSearchId = serviceData?.Id;
                                crmGtisUser.GlobalSearchCode = gtisOpeUser.GlobalSearchCode;
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateDataQueue(crmGtisUser);
                                retGtisUserList.Add(crmGtisUser);
                            });
                            //sysUserIDs 对 crmGtisUserIds 的差集--需要添加的用户信息
                            var tobeAddUserIds = sysUserIDs.Except(crmGtisUserIds).ToList();
                            tobeAddUserIds.ForEach(sysUserId =>
                            {
                                //g5返回的用户信息
                                var gtisOpeUser = gtisOpeUserList.Find(e => e.SysUserID == sysUserId);
                                //当前crm保存的用户信息
                                var crmGtisUser = new Db_crm_contract_serviceinfo_gtis_user();
                                //填充crmGtisUser的属性
                                crmGtisUser.ContractServiceInfoGtisId = gtis.Id;
                                crmGtisUser.UserId = gtisOpeUser.SysUserID;
                                crmGtisUser.AccountNumber = gtisOpeUser.AccountNumber;
                                crmGtisUser.PassWord = gtisOpeUser.PassWord;
                                crmGtisUser.AccountType = (int)(gtisOpeUser.AccountType == 0 ? EnumGtisAccountType.Main : EnumGtisAccountType.Sub);
                                crmGtisUser.IsProcessed = 0;
                                crmGtisUser.OpeningStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.TransGtisUserStates(gtisOpeUser.State);
                                crmGtisUser.StartDate = gtisOpeUser.StartServerDate;
                                crmGtisUser.EndDate = gtisOpeUser.EndServerDate;
                                crmGtisUser.LoginIP = gtisOpeUser.LastLoginPlace;
                                crmGtisUser.LastLoginTime = gtisOpeUser.LastLoginDate;
                                crmGtisUser.SharePeopleNum = gtisOpeUser.SharingTimesUse;
                                crmGtisUser.PhoneUser = (gtisOpeUser.AllPhoneUserInfo.Count > 0) ? gtisOpeUser.AllPhoneUserInfo.First().LinkMan : "";
                                crmGtisUser.PhoneUserNum = gtisOpeUser.AllPhoneUserInfo.Count;
                                crmGtisUser.AuthorizationNum = gtisOpeUser.AuthorizationNum;
                                crmGtisUser.ContractServiceInfoGlobalSearchId = serviceData?.Id;
                                crmGtisUser.GlobalSearchCode = gtisOpeUser.GlobalSearchCode;
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertDataQueue(crmGtisUser);
                                retGtisUserList.Add(crmGtisUser);
                                if (gtisOpeUser.AccountType == 0)
                                {
                                    gtis.ServiceCycleStart = gtisOpeUser.StartServerDate;
                                    gtis.ServiceCycleEnd = gtisOpeUser.EndServerDate;
                                }
                            });
                            //crmGtisUserIds 对 sysUserIDs 的差集--需要删除的用户信息
                            var tobeDelUserIds = crmGtisUserIds.Except(sysUserIDs).ToList();
                            var tobeDelUserList = crmGtisUserList.Where(e => tobeDelUserIds.Contains(e.UserId)).ToList();
                            tobeDelUserList.ForEach(user =>
                            {
                                user.OpeningStatus = (int)EnumGtisUserOpeningStatus.NotFound;
                                DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateDataQueue(user);
                                retGtisUserList.Add(user);
                            });
                            #endregion
                            #region gtis服务数据更新
                            gtis.SharePeopleNum = retGtisUserList.Where(user => user.OpeningStatus != (int)EnumGtisUserOpeningStatus.NotFound).Max(u => u.SharePeopleNum);
                            gtis.SubAccountsNum = retGtisUserList.Where(user => user.OpeningStatus != (int)EnumGtisUserOpeningStatus.NotFound).Where(u => u.AccountType == (int)EnumGtisAccountType.Sub).Count();
                            gtis.AuthorizationNum = retGtisUserList.Where(user => user.OpeningStatus != (int)EnumGtisUserOpeningStatus.NotFound).ToList().Find(u => u.AccountType == (int)EnumGtisAccountType.Main).AuthorizationNum;
                            //DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(gtis);
                            #endregion
                        }
                    }
                };
            });

        }


        public void SupplementGlobalSearchAccount20250407()
        {
            //查找2025年4月开通的环球搜服务
            var serviceList = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.SupplementGlobalSearchAccount20250407();
            //循环查看每个服务，检查环球搜账号数量是否小于合同中的环球搜账号数量
            serviceList.ForEach(serve =>
            {
                //获取开通的账号数量
                var accountNum = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(serve.Id).Count();
                //获取合同产品数据
                var conpro_info = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(serve.ContractProductInfoId);
                //若账号数量不一致，需要补服务变更申请
                if (conpro_info != null && accountNum != (conpro_info.PrimaryAccountsNum + conpro_info.SubAccountsNum.GetValueOrDefault(0)))
                {
                    //判断是否存在gits
                    var gtisCase = DbOpe_crm_contract_productinfo.Instance.CheckHasUnValidGTISService(serve.ContractId);
                    if (gtisCase == EnumContractGtisProductServiceState.Opened)
                    {//存在gtis

                        #region 提交gtis服务变更申请
                        //获取gtis开通信息
                        var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractId(serve.ContractId);
                        var gtisUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(gtis.Id);
                        var gtisCountry = new List<GtisApplCountry>();
                        var hasVip = DbOpe_crm_contract_productinfo.Instance.CheckContractHasVIP(serve.ContractId);
                        if (hasVip)
                        {
                            gtisCountry = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetGtisCountryListByServeId(serve.Id).MappingTo<List<GtisApplCountry>>();
                            var professionalId = new List<string>() { "761c3f51-afd0-4087-8cd3-9f9db8225cad", "716c3f51-afd0-4087-8cd3-9f9db8225cad" };
                            if (professionalId.Contains(serve.ProductId))
                            {
                                gtisCountry.AddRange(BLL_G4DbNames.Instance.GetG4DbNames(serve.ProductId).Select(e => e.SID.GetValueOrDefault(0)).ToList().MappingTo<List<GtisApplCountry>>());
                            }
                        }
                        var updateInfo = gtis.MappingTo<UpdateContractProductServiceInfoGtisAppl_In>();
                        updateInfo.ServiceMonthAfterDiscount = gtis.ServiceMonth;
                        updateInfo.ServiceCycleStartAfterDiscount = gtis.ServiceCycleStart;
                        updateInfo.ServiceCycleEndAfterDiscount = gtis.ServiceCycleEnd;
                        updateInfo.PerlongServiceDays = 0;
                        updateInfo.PrivateServiceDays = 0;
                        updateInfo.OverServiceDays = 0;
                        updateInfo.GlobalSearchAccountCount = conpro_info.PrimaryAccountsNum + conpro_info.SubAccountsNum.GetValueOrDefault(0);
                        updateInfo.UpdateContractProductServiceInfoGtisApplUser_In = gtisUserList.MappingTo<List<UpdateContractProductServiceInfoGtisApplUser_In>>();
                        updateInfo.GtisApplCountry = gtisCountry;
                        updateInfo.ChangeReasonList = new List<EnumGtisServiceChangeProject>();
                        updateInfo.ChangeReasonList.Add(EnumGtisServiceChangeProject.ChangeServiceContent);
                        updateInfo.Remark = "系统自动生成，处理2025年4月份已开通环球搜服务的账号数量有误的问题。";


                        Db_crm_contract_productserviceinfo_gtis_appl gtis_appl = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.UpdateContractProductServiceInfoGtisAppl(updateInfo);
                        var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(gtis_appl.ContractId);
                        Com_SysForm.Instance.FormId = "********-132e-4cbf-79b7-67d31b486554";
                        var userId = contract.Issuer;


                        string dataState = Dictionary.ProcessStatus.First(e => e.Value == gtis_appl.State.ToInt().ToString()).Name;
                        BLL_WorkFlow.Instance.AddWorkFlowNew<Db_crm_contract_productserviceinfo_gtis_appl, Db_crm_contract>("GTIS服务变更审批流程", gtis_appl.Id, gtis_appl, contract, "", gtis_appl.Remark, "待变更", "申请变更", "********-132e-4cbf-79b7-67d31b486554", userId);//dataState, "申请变更");
                        DbOpe_crm_contract_product_serviceinfo_status.Instance.UpdateContractProductServiceInfoStatus(contract.Id);
                        //DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.UpdateContractProductServiceInfoGtisAppl(updateInfo);
                        #endregion

                        #region 提交初审
                        #endregion
                    }
                    else if (gtisCase == EnumContractGtisProductServiceState.Inexistence)
                    {//不存在gtis
                        #region 提交环球搜服务变更申请
                        //查找合同当前的环球搜服务内容
                        var global = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(serve.ContractNum);
                        global.PrimaryAccountsNum = conpro_info.PrimaryAccountsNum;
                        global.SubAccountsNum = conpro_info.SubAccountsNum;
                        var updateInfo = global.MappingTo<UpdateContractProductServiceInfoGlobalSearchAppl_In>();
                        updateInfo.PrimaryAccountsNum = conpro_info.PrimaryAccountsNum.GetValueOrDefault(0);
                        updateInfo.SubAccountsNum = conpro_info.SubAccountsNum.GetValueOrDefault(0);
                        updateInfo.GlobalSearchUser = new List<UpdateContractProductServiceInfoGlobalSearchApplUser_In>();
                        //updateInfo.ChangeReasonList.Add(EnumGtisServiceChangeProject.ChangeServiceContent);
                        updateInfo.Remark = "系统自动生成，处理2025年4月份已开通环球搜服务的账号数量有误的问题。";

                        DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(serve.Id).MappingTo<List<UpdateContractProductServiceInfoGlobalSearchApplUser_In>>();
                        //List<UpdateContractProductServiceInfoGlobalSearchApplUser_In> GlobalSearchUser
                        Db_crm_contract_productserviceinfo_globalsearch_appl globalsearch_appl = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateContractProductServiceInfoGlobalSearchAppl(updateInfo);

                        var contract = DbOpe_crm_contract.Instance.QueryByPrimaryKey(globalsearch_appl.ContractId);
                        string dataState = Dictionary.ProcessStatus.First(e => e.Value == globalsearch_appl.State.ToInt().ToString()).Name;
                        Com_SysForm.Instance.FormId = "c460d593-86c6-e2fb-34e6-a4c9a3193f92";
                        TokenModel.Instance.id = contract.Issuer;


                        BLL_WorkFlow.Instance.AddWorkFlowNew<Db_crm_contract_productserviceinfo_globalsearch_appl, Db_crm_contract>("环球搜服务审批流程", globalsearch_appl.Id, globalsearch_appl, contract, "", globalsearch_appl.Remark, "待变更", "申请变更", "c460d593-86c6-e2fb-34e6-a4c9a3193f92", contract.Issuer);//dataState, "申请变更");
                        DbOpe_crm_contract_product_serviceinfo_status.Instance.UpdateContractProductServiceInfoStatus(contract.Id);
                        #endregion
                    }
                }
            });
        }

        public void HistoryGlobalSearchModifyType()
        {
            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.TransDeal(() =>
            {
                //查找所有需要追溯历史信息的在服服务数据
                var retroDataList = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetRetroDataList();
                foreach (var retro in retroDataList)
                {
                    //合同信息
                    var retroContractInfo = DbOpe_crm_contract.Instance.QueryByPrimaryKey(retro.ContractId);
                    //判断当前服务是否为 新增合同的服务申请
                    if ((retroContractInfo.ContractType == (int)EnumContractType.New || retroContractInfo.ContractType == (int)EnumContractType.AddItem) && retro.ProcessingType == (int)EnumProcessingType.Add)
                    {//若当前服务是新增合同的服务申请，则不需要寻找历史服务信息
                        var modifyTypeData = new Db_crm_contract_serviceinfo_globalsearch_modifytype();
                        modifyTypeData.ContractServiceInfoGlobalSearchId = retro.Id;
                        modifyTypeData.ProductServiceInfoGlobalSearchApplId = retro.ProductServiceInfoGlobalSearchApplId;
                        modifyTypeData.ContractNum = retro.ContractNum;
                        modifyTypeData.ModifyType = EnumGlobalSearchModifyType.NewService;
                        DbOpe_crm_contract_serviceinfo_globalsearch_modifytype.Instance.InsertData(modifyTypeData);
                        continue;
                    }
                    else
                    {
                        HistoryGlobalSearchModifyType_Sub(retro, retroContractInfo.ContractType.GetValueOrDefault(0));
                    }
                }
            });

        }

        private void HistoryGlobalSearchModifyType_Sub(Db_crm_contract_serviceinfo_globalsearch curService, int contractType)
        {
            //判断是否是续约合同的服务
            var isRenew = contractType == (int)EnumContractType.ReNew;
            //判断是否是变更的服务
            var isChange = curService.ProcessingType == (int)EnumProcessingType.Change;
            //如果是续约合同的服务或是变更的服务，需要查找历史数据
            if (isRenew || isChange)
            {
                //声明历史数据
                var hisServe = new HistoryGlobalsearchServiceInfo_Mid();
                //如果当前服务是变更数据，取被变更的服务信息
                if (isChange)
                    //获取被变更的服务数据信息
                    hisServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetChangedGlobalSearchServiceInfoByContractId(curService.ProductServiceInfoGlobalSearchApplId, curService.ContractNum);
                else
                    //根据客户编码获取被续约服务信息
                    hisServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceInfoByContractNum(curService.OldContractNum);
                if (hisServe != null)
                {
                    var globalSearchChangeItem = new List<EnumGlobalSearchModifyType>();
                    //重新生成账号或当前服务与历史服务的子账号个数不同(主账号个数均为1)，归为修改了账号
                    if (curService.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake)
                    {
                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.NewService);
                    }
                    else if(curService.SubAccountsNum != hisServe.SubAccountsNum)
                    {
                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeCode);
                    }
                    //执行时间不同，归为修改了服务时间
                    if (curService.ExecuteServiceCycleStart != hisServe.ExecuteServiceCycleStart || curService.ExecuteServiceCycleEnd != hisServe.ExecuteServiceCycleEnd)
                    {
                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeTime);
                    }
                    if (globalSearchChangeItem.Count == 0)
                    {
                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.Unchanged);
                    }
                    foreach (var item in globalSearchChangeItem)
                    {
                        var modifyTypeData = new Db_crm_contract_serviceinfo_globalsearch_modifytype();
                        modifyTypeData.ContractServiceInfoGlobalSearchId = curService.Id;
                        modifyTypeData.ProductServiceInfoGlobalSearchApplId = curService.ProductServiceInfoGlobalSearchApplId;
                        modifyTypeData.ContractNum = curService.ContractNum;
                        modifyTypeData.ModifyType = item;
                        DbOpe_crm_contract_serviceinfo_globalsearch_modifytype.Instance.InsertData(modifyTypeData);
                    }

                    //合同信息
                    var hisContractInfo = DbOpe_crm_contract.Instance.QueryByPrimaryKey(hisServe.ContractId);
                    //递归继续处理历史数据
                    curService = hisServe.MappingTo<Db_crm_contract_serviceinfo_globalsearch>();
                    curService.ProductServiceInfoGlobalSearchApplId = hisServe.ApplyId;
                    HistoryGlobalSearchModifyType_Sub(curService, hisContractInfo.ContractType.GetValueOrDefault(0));
                }
            }
        }

    }
}
