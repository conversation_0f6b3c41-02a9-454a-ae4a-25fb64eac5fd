using System;
using System.Collections.Generic;
using System.Linq;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.Enum;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbCommon;
using CRM2_API.Model.System;
using SqlSugar;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告业务逻辑类 - 跟踪记录查询功能（数据库版本）
    /// </summary>
    public partial class BLL_WorkReportDatabase
    {
        #region 跟踪记录查询

        /// <summary>
        /// 获取跟踪记录列表 - 工作报告模块专用查询方法
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>跟踪记录列表</returns>
        public ApiTableOut<VM_TrackingRecord> GetTrackingRecords(GetTrackingRecords_In input)
        {
            try
            {
                // 使用数据库分页查询
                var result = QueryTrackingRecordsForWorkReportWithPaging(input);

                return new ApiTableOut<VM_TrackingRecord>
                {
                    Data = result.Data,
                    Total = result.Total,
                    Success = true,
                    Message = "获取跟踪记录成功"
                };
            }
            catch (Exception ex)
            {
                throw new ApiException($"获取跟踪记录失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 工作报告模块专用跟踪记录查询方法 - 数据库分页版本
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>分页结果</returns>
        private (List<VM_TrackingRecord> Data, int Total) QueryTrackingRecordsForWorkReportWithPaging(GetTrackingRecords_In input)
        {
            try
            {
                string userId = UserId;
                int total = 0;

                // 使用SqlSugar直接查询，不依赖复杂的现有查询方法
                var query = DbContext.Crm2Db.Queryable<Db_crm_trackingrecord>()
                    .LeftJoin<Db_sys_user>((t, u) => t.UserId == u.Id)
                    .LeftJoin<Db_crm_customer>((t, u, c) => t.CustomerId == c.Id)
                    .LeftJoin<Db_crm_customer_subcompany>((t, u, c, cs) => c.Id == cs.CustomerId && cs.IsMain == 1 && cs.Deleted == 0)
                    .Where((t, u, c, cs) => t.Deleted == false);

                // 时间范围过滤
                if (input.StartDate.HasValue)
                {
                    query = query.Where((t, u, c, cs) => t.CreateDate >= input.StartDate.Value);
                }
                if (input.EndDate.HasValue)
                {
                    query = query.Where((t, u, c, cs) => t.CreateDate <= input.EndDate.Value);
                }

                // 关键词搜索
                if (!string.IsNullOrEmpty(input.SearchKeyword))
                {
                    query = query.Where((t, u, c, cs) => t.CustomerName.Contains(input.SearchKeyword) ||
                                                        cs.CompanyName.Contains(input.SearchKeyword));
                }

                // 根据查询类型过滤
                switch (input.QueryType)
                {
                    case 1: // 新增客户：Hidden = true
                        query = query.Where((t, u, c, cs) => t.Hidden == true);
                        break;
                    case 2: // 签约客户：签约成功/到账成功/签约过期
                        query = query.Where((t, u, c, cs) =>
                            t.TrackingStage == EnumTrackingStage.SignedContract ||
                            t.TrackingStage == EnumTrackingStage.Received ||
                            t.TrackingStage == EnumTrackingStage.SignedOverTime);
                        break;
                    case 3: // 跟进客户：除新增和签约外的其他客户
                        query = query.Where((t, u, c, cs) => t.Hidden == false &&
                            t.TrackingStage != EnumTrackingStage.SignedContract &&
                            t.TrackingStage != EnumTrackingStage.Received &&
                            t.TrackingStage != EnumTrackingStage.SignedOverTime);
                        break;
                }

                // 客户等级过滤 - 在数据库层面进行过滤
                if (input.CustomerLevel.HasValue)
                {
                    query = query.Where((t, u, c, cs) => c.CustomerLevel == (int)input.CustomerLevel.Value);
                }

                // 国家过滤 - 在数据库层面进行过滤
                if (!string.IsNullOrEmpty(input.Country))
                {
                    query = query.Where((t, u, c, cs) => cs.Country.ToString() == input.Country);
                }

                // 权限控制 - 工作报告模块只查询当前用户自己的跟踪记录
                query = query.Where((t, u, c, cs) => t.UserId.Equals(userId));

                // 执行查询并转换结果，使用MergeTable解决多表查询别名问题
                var results = query.Select((t, u, c, cs) => new VM_TrackingRecord
                {
                    Id = t.Id,
                    UserId = t.UserId,
                    UserName = u.Name,
                    CustomerDataSource = t.CustomerDataSource ?? EnumCustomerDataSource.Private,
                    CompanyId = t.CustomerId,
                    CompanyName = cs.CompanyName,
                    CustomerId = t.CustomerId,
                    CustomerName = t.CustomerName,
                    CustomerLevel = (EnumCustomerLevel)c.CustomerLevel,
                    CountryId = cs.Country,
                    TrackingType = t.TrackingType ?? EnumTrackingType.TelephoneCommunication,
                    TrackingPurpose = t.TrackingPurpose,
                    TrackingStage = t.TrackingStage ?? EnumTrackingStage.PreliminaryNegotiation,
                    TrackingContent = t.Remark ?? "",
                    TrackingTime = t.CreateDate.ToString(),
                    ContractId = t.ContractId ?? "",
                    CreateUser = u.Name,
                    Hidden = t.Hidden,
                    CustomerNum = t.CustomerNum ?? ""
                }).MergeTable().OrderByDescending(r => r.TrackingTime).ToPageList(input.PageNumber, input.PageSize, ref total);

                // 补充额外信息
                foreach (var record in results)
                {
                    // 补充国家信息
                    record.Country = GetCountryName(record.CountryId);

                    // 补充主营产品信息
                    record.MainProduct = GetMainProductsByCustomerId(record.CustomerId);

                    // 补充合同信息
                    if (!string.IsNullOrEmpty(record.ContractId))
                    {
                        record.ContractName = GetContractName(record.ContractId);
                    }
                }

                return (results, total);
            }
            catch (Exception ex)
            {
                throw new Exception($"查询跟踪记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 工作报告模块专用跟踪记录查询方法
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>跟踪记录列表</returns>
        private List<VM_TrackingRecord> QueryTrackingRecordsForWorkReport(GetTrackingRecords_In input)
        {
            try
            {
                string userId = UserId;

                // 使用SqlSugar直接查询，不依赖复杂的现有查询方法
                var query = DbContext.Crm2Db.Queryable<Db_crm_trackingrecord>()
                    .LeftJoin<Db_sys_user>((t, u) => t.UserId == u.Id)
                    .LeftJoin<Db_crm_customer>((t, u, c) => t.CustomerId == c.Id)
                    .LeftJoin<Db_crm_customer_subcompany>((t, u, c, cs) => c.Id == cs.CustomerId && cs.IsMain == 1 && cs.Deleted == 0)
                    .Where((t, u, c, cs) => t.Deleted == false);

                // 时间范围过滤
                if (input.StartDate.HasValue)
                {
                    query = query.Where((t, u, c, cs) => t.CreateDate >= input.StartDate.Value);
                }
                if (input.EndDate.HasValue)
                {
                    query = query.Where((t, u, c, cs) => t.CreateDate <= input.EndDate.Value);
                }

                // 关键词搜索
                if (!string.IsNullOrEmpty(input.SearchKeyword))
                {
                    query = query.Where((t, u, c, cs) => t.CustomerName.Contains(input.SearchKeyword) ||
                                                        cs.CompanyName.Contains(input.SearchKeyword));
                }

                // 根据查询类型过滤
                switch (input.QueryType)
                {
                    case 1: // 新增客户：Hidden = true
                        query = query.Where((t, u, c, cs) => t.Hidden == true);
                        break;
                    case 2: // 签约客户：签约成功/到账成功/签约过期
                        query = query.Where((t, u, c, cs) =>
                            t.TrackingStage == EnumTrackingStage.SignedContract ||
                            t.TrackingStage == EnumTrackingStage.Received ||
                            t.TrackingStage == EnumTrackingStage.SignedOverTime);
                        break;
                    case 3: // 跟进客户：除新增和签约外的其他客户
                        query = query.Where((t, u, c, cs) => t.Hidden == false &&
                            t.TrackingStage != EnumTrackingStage.SignedContract &&
                            t.TrackingStage != EnumTrackingStage.Received &&
                            t.TrackingStage != EnumTrackingStage.SignedOverTime);
                        break;
                }

                // 客户等级过滤 - 在数据库层面进行过滤
                if (input.CustomerLevel.HasValue)
                {
                    query = query.Where((t, u, c, cs) => c.CustomerLevel == (int)input.CustomerLevel.Value);
                }

                // 国家过滤 - 在数据库层面进行过滤
                if (!string.IsNullOrEmpty(input.Country))
                {
                    query = query.Where((t, u, c, cs) => cs.Country.ToString() == input.Country);
                }

                // 权限控制 - 工作报告模块只查询当前用户自己的跟踪记录
                query = query.Where((t, u, c, cs) => t.UserId.Equals(userId));

                // 执行查询并转换结果，使用MergeTable解决多表查询别名问题
                var results = query.Select((t, u, c, cs) => new VM_TrackingRecord
                {
                    Id = t.Id,
                    UserId = t.UserId,
                    UserName = u.Name,
                    CustomerDataSource = t.CustomerDataSource ?? EnumCustomerDataSource.Private,
                    CompanyId = t.CustomerId,
                    CompanyName = cs.CompanyName,
                    CustomerId = t.CustomerId,
                    CustomerName = t.CustomerName,
                    CustomerLevel = (EnumCustomerLevel)c.CustomerLevel,
                    CountryId = cs.Country,
                    TrackingType = t.TrackingType ?? EnumTrackingType.TelephoneCommunication,
                    TrackingPurpose = t.TrackingPurpose,
                    TrackingStage = t.TrackingStage ?? EnumTrackingStage.PreliminaryNegotiation,
                    TrackingContent = t.Remark ?? "",
                    TrackingTime = t.CreateDate.ToString(),
                    ContractId = t.ContractId ?? "",
                    CreateUser = u.Name,
                    Hidden = t.Hidden,
                    CustomerNum = t.CustomerNum ?? ""
                }).MergeTable().OrderByDescending(r => r.TrackingTime).ToList();

                // 补充额外信息
                foreach (var record in results)
                {
                    // 补充国家信息
                    record.Country = GetCountryName(record.CountryId);

                    // 补充主营产品信息
                    record.MainProduct = GetMainProductsByCustomerId(record.CustomerId);

                    // 补充合同信息
                    if (!string.IsNullOrEmpty(record.ContractId))
                    {
                        record.ContractName = GetContractName(record.ContractId);
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                throw new Exception($"查询跟踪记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取合同名称
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <returns>合同名称</returns>
        private string GetContractName(string contractId)
        {
            if (string.IsNullOrEmpty(contractId))
                return "";

            try
            {
                var contract = DbOpe_crm_contract.Instance.GetDataById(contractId);
                return contract?.ContractName ?? "";
            }
            catch
            {
                return "";
            }
        }



        /// <summary>
        /// 获取国家名称
        /// </summary>
        /// <param name="countryId">国家ID</param>
        /// <returns>国家名称</returns>
        private string GetCountryName(int? countryId)
        {
            if (!countryId.HasValue || countryId.Value == 0)
                return "";

            try
            {
                var country = DbContext.Crm2Db.Queryable<Db_sys_country>()
                    .Where(c => c.Id == countryId.Value)
                    .First();
                return country?.Name ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 获取客户主营产品信息
        /// </summary>
        /// <param name="customerId">客户ID</param>
        /// <returns>主营产品字符串</returns>
        private string GetMainProductsByCustomerId(string customerId)
        {
            if (string.IsNullOrEmpty(customerId))
                return "";

            try
            {
                // 先获取客户的主公司ID
                var mainCompany = DbOpe_crm_customer_subcompany.Instance.GetCustomerMainCompany(customerId);
                if (mainCompany == null)
                    return "";

                // 通过主公司ID查询主营产品信息
                var businessInfo = DbOpe_crm_customer_subcompany_mainbusiness.Instance.GetCompanyBusiness(mainCompany.Id);
                
                if (businessInfo != null && businessInfo.Any())
                {
                    var products = businessInfo
                        .Where(b => !string.IsNullOrEmpty(b.MainProductName))
                        .Select(b => b.MainProductName)
                        .Distinct()
                        .ToList();

                    return string.Join(", ", products);
                }

                return "";
            }
            catch
            {
                return "";
            }
        }

        #endregion
    }
}
