﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.ContractTemplate;
using CRM2_API.Model.ControllersViewModel.ContractTemplateApply;
using CRM2_API.Model.System;
using LgyUtil;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.BLL
{
    public class BLL_ContractTemplate : BaseBLL<BLL_ContractTemplate>
    {
        /// <summary>
        /// 添加合同说明模板申请信息
        /// </summary>
        /// <param name="templateApply_In"></param>
        public void AddContractTemplateApply(AddContractTemplateApply_In templateApply_In)
        {
            lock (this)
            {
                //获取当前合同说明模板表中数据数量，作为当前申请的模板编号的起始编号
                int tempNoStart = DbOpe_crm_contract_template.Instance.GetQueryCount(new Db_crm_contract_template());
                var mainApplyId = Guid.NewGuid().ToString();
                int tempNum = 0;
                templateApply_In.ContractTemplate.ForEach(it =>
                {
                    tempNum++;
                    //声明模板对象
                    var template = it.MappingTo<Db_crm_contract_template>();
                    //模板主键guid
                    template.Id = Guid.NewGuid().ToString();
                    //模板编号
                    template.ContractTemplateNo = (tempNoStart + tempNum).ToString().PadLeft(5, '0');
                    //模板状态
                    template.State = templateApply_In.State;
                    //适用客户类型
                    //template.CreditType = String.Join(",", it.CreditType);
                    //模板其他属性
                    template.Deleted = false;
                    template.CreateUser = UserTokenInfo.id;
                    template.CreateDate = DateTime.Now;
                    template.IsStatic = false;
                    //执行新增，添加到队列
                    DbOpe_crm_contract_template.Instance.InsertQueue(template);
                    //若是提交审核状态，创建申请和模板的对应关系数据
                    if (templateApply_In.State == EnumContractTemplateStatus.Commit)
                    {
                        //创建模板申请数据
                        var tempApply = new Db_crm_contract_templateapply();
                        tempApply.Id = (tempNum == 1) ? mainApplyId : Guid.NewGuid().ToString();
                        tempApply.IsMainApply = (tempNum == 1);
                        tempApply.MainApplyId = (tempNum == 1) ? "" : mainApplyId;
                        tempApply.ApplicantId = UserTokenInfo.id;
                        tempApply.ApplicantDate = DateTime.Now;
                        tempApply.State = EnumContractTemplateApplyStatus.Commit;
                        tempApply.Deleted = false;
                        tempApply.CreateUser = UserTokenInfo.id;
                        tempApply.CreateDate = DateTime.Now;
                        DbOpe_crm_contract_templateapply.Instance.InsertQueue(tempApply);
                        string dataState = Dictionary.ContractTemplateStatus.First(e => e.Value == templateApply_In.State.ToInt().ToString()).Name;
                        BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_templateapply>("合同说明模板审核流程", tempApply.Id, tempApply, "", dataState, "新建");

                        var tempApplyDetail = new Db_crm_contract_templateapply_detail();
                        tempApplyDetail.Id = Guid.NewGuid().ToString();
                        tempApplyDetail.ContractTemplateAppId = mainApplyId;
                        tempApplyDetail.ContractTemplateId = template.Id;
                        tempApplyDetail.Deleted = false;
                        tempApplyDetail.CreateUser = UserTokenInfo.id;
                        tempApplyDetail.CreateDate = DateTime.Now;
                        DbOpe_crm_contract_templateapply_detail.Instance.InsertQueue(tempApplyDetail);
                        dataState = Dictionary.ContractTemplateStatus.First(e => e.Value == templateApply_In.State.ToInt().ToString()).Name;
                        BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_template>("合同说明模板审核流程", template.Id, template, "", dataState, "新建");
                    }
                });
                /*//若是提交审核状态，创建模板申请数据
                if (templateApply_In.State == EnumContractTemplateStatus.Commit)
                {
                    var tempApply = new Db_crm_contract_templateapply();
                    tempApply.Id = mainApplyId;
                    tempApply.ApplicantId = UserTokenInfo.id;
                    tempApply.ApplicantDate = DateTime.Now;
                    tempApply.State = EnumContractTemplateApplyStatus.Commit;
                    tempApply.Deleted = false;
                    tempApply.CreateUser = UserTokenInfo.id;
                    tempApply.CreateDate = DateTime.Now;
                    DbOpe_crm_contract_templateapply.Instance.InsertQueue(tempApply);
                    string dataState = Dictionary.ContractTemplateStatus.First(e => e.Value == templateApply_In.State.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_templateapply>("合同说明模板审核流程", mainApplyId, tempApply, "", dataState, "新建");
                }*/

                //提交sql队列执行
                DbOpe_crm_contract_templateapply.Instance.SaveQueues();
            }

        }

        /// <summary> 
        /// 修改合同说明模板申请信息
        /// </summary>
        /// <param name="templateApply_In"></param>
        public void UpdateContractTemplateApply(UpdateContractTemplateApply_In templateApply_In)
        {
            lock (this)
            {
                //创建申请的主键
                var mainApplyId = Guid.NewGuid().ToString();
                //分离新增的和修改的
                var newTempList = templateApply_In.ContractTemplate.Where(e => string.IsNullOrEmpty(e.Id)).ToList();
                //获取当前合同说明模板表中数据数量，作为当前申请的模板编号的起始编号
                int tempNoStart = DbOpe_crm_contract_template.Instance.GetQueryCount(new Db_crm_contract_template());
                int tempNum = 0;
                int tempOrderNum = 1;
                var tempList = DbOpe_crm_contract_template.Instance.GetContractTemplateListByIds(templateApply_In.ContractTemplate.Select(e => e.Id).ToList());
                templateApply_In.ContractTemplate.ForEach(it =>
                {
                    //声明模板对象
                    var template = it.MappingTo<Db_crm_contract_template>();
                    //判断是修改还是新增
                    if (string.IsNullOrEmpty(it.Id))
                    {//新增
                        tempNum++;
                        //生成模板主键guid
                        template.Id = Guid.NewGuid().ToString();
                        //生成模板编号
                        template.ContractTemplateNo = (tempNoStart + tempNum).ToString().PadLeft(5, '0');
                        template.State = templateApply_In.State;
                        template.Deleted = false;
                        template.CreateUser = UserTokenInfo.id;
                        template.CreateDate = DateTime.Now;
                        template.IsStatic = false;
                        DbOpe_crm_contract_template.Instance.InsertQueue(template);
                    }
                    else
                    {//修改
                        //验证模板状态是否可以修改
                        if (!DbOpe_crm_contract_template.Instance.CheckTemplateUpd(template.Id))
                            throw new ApiException("模板不可被修改");
                        var oldTemp = tempList.Where(e => e.Id == it.Id).First();
                        template = it.MappingTo(oldTemp);
                        template.State = templateApply_In.State;
                        template.Deleted = false;
                        template.UpdateUser = UserTokenInfo.id;
                        template.UpdateDate = DateTime.Now;
                        DbOpe_crm_contract_template.Instance.UpdateQueue(template);
                    }
                    //若是提交审核状态，创建申请和模板的对应关系数据
                    if (templateApply_In.State == EnumContractTemplateStatus.Commit)
                    {
                        var tempApply = new Db_crm_contract_templateapply();
                        tempApply.Id = (tempOrderNum == 1) ? mainApplyId : Guid.NewGuid().ToString();
                        tempApply.ApplicantId = UserTokenInfo.id;
                        tempApply.ApplicantDate = DateTime.Now;
                        tempApply.IsMainApply = (tempOrderNum == 1);
                        tempApply.MainApplyId = (tempOrderNum == 1) ? "" : mainApplyId;
                        tempApply.State = EnumContractTemplateApplyStatus.Commit;
                        tempApply.Deleted = false;
                        tempApply.CreateUser = UserTokenInfo.id;
                        tempApply.CreateDate = DateTime.Now;
                        DbOpe_crm_contract_templateapply.Instance.InsertQueue(tempApply);

                        string dataState = Dictionary.ContractTemplateStatus.First(e => e.Value == templateApply_In.State.ToInt().ToString()).Name;
                        BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_templateapply>("合同说明模板审核流程", tempApply.Id, tempApply, "", dataState, "新建");


                        //将当前模板与其他申请的关联关系全部删除
                        DbOpe_crm_contract_templateapply_detail.Instance.DelRepetTemplateDetail(template.Id);
                        var tempApplyDetail = new Db_crm_contract_templateapply_detail();
                        tempApplyDetail.Id = Guid.NewGuid().ToString();
                        tempApplyDetail.ContractTemplateAppId = mainApplyId;
                        tempApplyDetail.ContractTemplateId = template.Id;
                        tempApplyDetail.Deleted = false;
                        tempApplyDetail.CreateUser = UserTokenInfo.id;
                        tempApplyDetail.CreateDate = DateTime.Now;
                        DbOpe_crm_contract_templateapply_detail.Instance.InsertQueue(tempApplyDetail);
                        dataState = Dictionary.ContractTemplateStatus.First(e => e.Value == templateApply_In.State.ToInt().ToString()).Name;
                        BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_template>("合同说明模板审核流程", template.Id, template, "", dataState, "修改");


                    }

                    tempOrderNum++;
                });
                /* //若是提交审核状态，创建模板申请数据
                 if (templateApply_In.State == EnumContractTemplateStatus.Commit)
                 {
                     var tempApply = new Db_crm_contract_templateapply();
                     tempApply.Id = mainApplyId;
                     tempApply.ApplicantId = UserTokenInfo.id;
                     tempApply.ApplicantDate = DateTime.Now;
                     tempApply.State = EnumContractTemplateApplyStatus.Commit;
                     tempApply.Deleted = false;
                     tempApply.CreateUser = UserTokenInfo.id;
                     tempApply.CreateDate = DateTime.Now;
                     DbOpe_crm_contract_templateapply.Instance.InsertQueue(tempApply);

                     string dataState = Dictionary.ContractTemplateStatus.First(e => e.Value == templateApply_In.State.ToInt().ToString()).Name;
                     BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_templateapply>("合同说明模板审核流程", mainApplyId, tempApply, "", dataState, "新建");

                 }*/
                //提交sql队列执行
                DbOpe_crm_contract_templateapply.Instance.SaveQueues();

            }

        }

        /// <summary>
        /// 删除合同说明模板信息
        /// </summary>
        /// <param name="Ids"></param>
        /// <exception cref="ApiException"></exception>
        public void DeleteContractTemplate(string Ids)
        {
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("未选择要删除的模板");
            List<string> idList = Ids.Split(',').ToList();
            //验证传入的ids是否全部为拒绝和草稿
            if (DbOpe_crm_contract_template.Instance.CheckTemplateListDelete(idList))
                throw new ApiException("所选列表存在不可删除模板，无法删除");
            DbOpe_crm_contract_template.Instance.DeleteTemplateList(idList, UserTokenInfo.id);
        }

        /// <summary>
        /// 撤销合同说明模板申请信息
        /// </summary>
        /// <param name="Ids"></param>
        /// <exception cref="ApiException"></exception>
        public void RevokeContractTemplateApply(string Ids)
        {
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("未选择要撤销的模板");
            List<string> idList = Ids.Split(',').ToList();
            //验证传入的ids是否全部为提交状态
            if (DbOpe_crm_contract_template.Instance.CheckTemplateListRevoke(idList))
                throw new ApiException("所选列表存在不可撤销模板，无法撤销");
            //获取关联的申请
            var applies = DbOpe_crm_contract_templateapply_detail.Instance.GetApplyIdsByTemplateIds(idList);
            //将模板状态改为草稿
            DbOpe_crm_contract_template.Instance.RevokeTemplateList(idList, UserTokenInfo.id);
            //将关联表数据删除
            DbOpe_crm_contract_templateapply_detail.Instance.DeleteApplyDetail(idList);
            //执行sql队列
            DbOpe_crm_contract_template.Instance.SaveQueues();
            //查找需要撤销的申请(没有关联的申请)
            var revokeApplies = applies.Except(DbOpe_crm_contract_templateapply_detail.Instance.GetHasTemplateApplies(applies)).ToList();
            //执行撤销申请操作
            DbOpe_crm_contract_templateapply.Instance.RevokeApplyForTemplate(revokeApplies, UserTokenInfo.id);
        }

        /// <summary>
        /// 审核合同说明模板申请信息
        /// </summary>
        /// <param name="apply"></param>
        public void AuditContractTemplateApply(AuditContractTemplateApply_In apply)
        {
            if (!DbOpe_crm_contract_templateapply.Instance.CheckApplyCouldAudit(apply.Id))
                throw new ApiException("所选数据无法进行审核");
            //根据ContractTemplateList的Ids,获取所有Template
            var tempIds = apply.ContractTemplate.Select(e => e.ContractTemplateId).ToList();
            var tempList = DbOpe_crm_contract_template.Instance.GetContractTemplateListByIds(tempIds);
            //找到mainApplyId == apply.id的数据
            var subApplyList = DbOpe_crm_contract_templateapply.Instance.GetApplyListByMainId(apply.Id);
            for (int i = 0; i < tempList.Count; i++)
            {
                var temp = tempList[i];
                temp.State = apply.ContractTemplate.First(e => e.ContractTemplateId.Equals(temp.Id)).State;
                if (temp.State == EnumContractTemplateStatus.Agree)
                {
                    temp.LifeCycle = apply.LifeCycle;
                    temp.LifeCycleStart = apply.LifeCycleStart;
                    temp.LifeCycleEnd = apply.LifeCycleEnd;
                }
                temp.UpdateUser = UserTokenInfo.id;
                temp.UpdateDate = DateTime.Now;
                DbOpe_crm_contract_template.Instance.UpdateQueue(temp);
                //审核流程记录
                string dataState_temp = Dictionary.ContractTemplateStatus.First(e => e.Value == temp.State.ToInt().ToString()).Name;
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_template>("合同说明模板审核流程", temp.Id, temp, apply.Feedback, dataState_temp, "审核");
                //第一条模板数据
                if (i == 0)
                {
                    //修改反馈和周期
                    var applyInfo = DbOpe_crm_contract_templateapply.Instance.QueryByPrimaryKey(apply.Id);
                    if (temp.State == EnumContractTemplateStatus.Agree)
                        applyInfo.State = EnumContractTemplateApplyStatus.Agree;
                    else
                        applyInfo.State = EnumContractTemplateApplyStatus.Refuse;
                    applyInfo.Feedback = apply.Feedback;
                    applyInfo.ReviewerId = UserTokenInfo.id;
                    applyInfo.ReviewerDate = DateTime.Now;
                    applyInfo.UpdateUser = UserTokenInfo.id;
                    applyInfo.UpdateDate = DateTime.Now;
                    DbOpe_crm_contract_templateapply.Instance.UpdateQueue(applyInfo);

                    //审核流程记录
                    string dataState = Dictionary.ContractTemplateStatus.First(e => e.Value == applyInfo.State.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_templateapply>("合同说明模板审核流程", applyInfo.Id, applyInfo, apply.Feedback, dataState, "审核");
                }

                //从第二个模板开始，创建新的合同模板申请，解绑和原申请的关联，补充创建的workflow
                if (i > 0)
                {
                    //创建新的模板申请Apply数据
                    var applyInfo = subApplyList[i - 1];
                    applyInfo.IsMainApply = true;
                    applyInfo.MainApplyId = "";
                    if (temp.State == EnumContractTemplateStatus.Agree)
                        applyInfo.State = EnumContractTemplateApplyStatus.Agree;
                    else
                        applyInfo.State = EnumContractTemplateApplyStatus.Refuse;
                    applyInfo.Feedback = apply.Feedback;
                    applyInfo.ReviewerId = UserTokenInfo.id;
                    applyInfo.ReviewerDate = DateTime.Now;
                    applyInfo.Deleted = false;
                    applyInfo.UpdateUser = UserTokenInfo.id;
                    applyInfo.UpdateDate = DateTime.Now;
                    DbOpe_crm_contract_templateapply.Instance.UpdateQueue(applyInfo);
                    //补充审批workflow
                    string dataState = Dictionary.ContractTemplateStatus.First(e => e.Value == applyInfo.State.ToInt().ToString()).Name;
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_templateapply>("合同说明模板审核流程", applyInfo.Id, applyInfo, apply.Feedback, dataState, "审核");
                    //将当前模板与其他申请的关联关系全部删除
                    DbOpe_crm_contract_templateapply_detail.Instance.DelRepetTemplateDetail(temp.Id);
                    var tempApplyDetail = new Db_crm_contract_templateapply_detail();
                    tempApplyDetail.Id = Guid.NewGuid().ToString();
                    tempApplyDetail.ContractTemplateAppId = applyInfo.Id;
                    tempApplyDetail.ContractTemplateId = temp.Id;
                    tempApplyDetail.Deleted = false;
                    tempApplyDetail.CreateUser = UserTokenInfo.id;
                    tempApplyDetail.CreateDate = DateTime.Now;
                    DbOpe_crm_contract_templateapply_detail.Instance.InsertQueue(tempApplyDetail);
                }

            }
            //DbOpe_crm_contract_template.Instance.UpdateQueue(tempList);
            //修改反馈和周期
            /*var applyInfo = DbOpe_crm_contract_templateapply.Instance.QueryByPrimaryKey(apply.Id);
            if (apply.ContractTemplate.Any(e => e.State == EnumContractTemplateStatus.Agree))
                applyInfo.State = EnumContractTemplateApplyStatus.Agree;
            else
                applyInfo.State = EnumContractTemplateApplyStatus.Refuse;
            applyInfo.Feedback = apply.Feedback;
            applyInfo.ReviewerId = UserTokenInfo.id;
            applyInfo.ReviewerDate = DateTime.Now;
            DbOpe_crm_contract_templateapply.Instance.UpdateQueue(applyInfo);

            //审核流程记录
            string dataState = Dictionary.ContractTemplateStatus.First(e => e.Value == applyInfo.State.ToInt().ToString()).Name;
            BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_templateapply>("合同说明模板审核流程", applyInfo.Id, applyInfo, apply.Feedback, dataState, "审核");*/

            //提交sql队列
            DbOpe_crm_contract_template.Instance.SaveQueues();

        }

        /// <summary>
        /// 撤销合同说明模板申请审核信息
        /// </summary>
        /// <param name="Ids"></param>
        public void RevokeContractTemplateAudit(string Ids)
        {
            if (string.IsNullOrEmpty(Ids))
                throw new ApiException("未选择要撤销的审核");
            List<string> idList = Ids.Split(',').ToList();
            //先验证当前申请是否是通过或拒绝状态
            if (!DbOpe_crm_contract_templateapply.Instance.CheckApplyAgreeOrRefuse(idList))
                throw new ApiException("当前申请不是通过或拒绝状态，无法撤销");
            if (DbOpe_crm_contract_template.Instance.CheckApplyHasStaticTemplate(idList))
                throw new ApiException("所选申请中存在固定模板，无法撤销");
            //获取所有申请的 主键+状态 列表
            var applyList = DbOpe_crm_contract_templateapply.Instance.GetToRevokeApplyInfo(idList);
            //获取所有模板的 主键+状态 列表
            var tempList = DbOpe_crm_contract_templateapply_detail.Instance.GetToRevokeTemplateInfo(idList);
            //模板状态改为提交申请
            DbOpe_crm_contract_template.Instance.UpdTempCommit(tempList.Select(e => e.Id).ToList(), UserId);
            //申请状态改为提交申请
            DbOpe_crm_contract_templateapply.Instance.RevokeApply(idList, UserId);
            tempList.ForEach(temp =>
            {
                //获取模板撤销前的状态名称
                string state = temp.State.GetEnumDescription();
                //撤销已发送的消息
                BLL_WorkFlow.Instance.CancelWorkflowPending("合同说明模板审核流程", temp.Id, state, temp);
                //BLL_WorkFlow.Instance.CancelWorkflowPending("合同审批流程", id, "作废");
            });
            applyList.ForEach(apply =>
            {
                //var tempIds = DbOpe_crm_contract_templateapply_detail.Instance.GetTemplateIdsByApplyId(id);
                //获取申请撤销前的状态名称
                string state = apply.State.GetEnumDescription();
                //撤销已发送的消息
                BLL_WorkFlow.Instance.CancelWorkflowPending("合同说明模板审核流程", apply.Id, state, apply);
            });

            //提交sql队列
            DbOpe_crm_contract_templateapply.Instance.SaveQueues();
        }

        /// <summary>
        /// 作废合同说明模板申请信息
        /// </summary>
        /// <param name="idList"></param>
        /// <exception cref="ApiException"></exception>
        public void VoidContractTemplateAudit(List<string> idList)
        {
            if (idList == null || idList.Count == 0)
                throw new ApiException("未选择要撤销的审核");
            //先验证当前申请是否是通过或拒绝状态
            if (!DbOpe_crm_contract_templateapply.Instance.CheckApplyAgreeOrRefuse(idList))
                throw new ApiException("当前申请不是通过或拒绝状态，无法作废");
            if (DbOpe_crm_contract_template.Instance.CheckApplyHasStaticTemplate(idList))
                throw new ApiException("所选申请中存在固定模板，无法作废");
            //获取所有模板的主键
            var tempIds = DbOpe_crm_contract_templateapply_detail.Instance.GetTemplateIdsByApplyIds(idList);
            //删除申请和模板的关联关系
            //DbOpe_crm_contract_templateapply_detail.Instance.DeleteApplyDetail(tempIds, Ids, UserId);//
            //获取所有申请的 主键+状态 列表
            var applyList = DbOpe_crm_contract_templateapply.Instance.GetToRevokeApplyInfo(idList);
            //获取所有模板的 主键+状态 列表
            var tempList = DbOpe_crm_contract_templateapply_detail.Instance.GetToRevokeTemplateInfo(idList);
            //模板状态改为作废（2023-09-19修改，原逻辑为模板改为草稿）
            DbOpe_crm_contract_template.Instance.VoidTemplate(tempList.Select(e => e.Id).ToList(), UserId);
            //申请状态改为作废
            DbOpe_crm_contract_templateapply.Instance.VoidApply(idList, UserId);
            tempList.ForEach(temp =>
            {
                //添加作废记录
                BLL_WorkFlow.Instance.VoidWorkflowPending("合同说明模板审核流程", temp.Id, "作废");
            });
            applyList.ForEach(apply =>
            {
                //添加作废记录
                BLL_WorkFlow.Instance.VoidWorkflowPending("合同说明模板审核流程", apply.Id, "作废");
            });
            //提交sql队列
            DbOpe_crm_contract_templateapply.Instance.SaveQueues();
        }

        /// <summary>
        /// 获取用户当前可申请的最大模板数
        /// </summary>
        /// <returns></returns>
        public int GetAllowApplyLimit()
        {
            //根据Id获取当前用户创建的合同说明模板数量
            var curTempCount = DbOpe_crm_contract_template.Instance.GetApplyCountUpLimit(UserId);
            //获取公共参数中合同说明模板可申请上限
            var upLimitCount = DbOpe_sys_comparam.Instance.GetComparams(EnumComParamKey.SetContractTemplatesNum.ToString());
            //计算当前允许申请上限
            var retValue = upLimitCount - curTempCount;
            //如果返回值为负数，返回0
            if (retValue < 0)
                retValue = 0;
            return retValue;
        }

        /// <summary>
        /// 根据GTIS产品或环球搜产品赠送月份，自动选定特殊的固定模板
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        public GetSpecialContractTemplateList_Out GetSpecialContractTemplateList(GetSpecialContractTemplateList_In search_In)
        {
            if (search_In.ProductList.IsNullOrEmpty<GetSpecialContractTemplateList_In_Product>())
                return null;
            //获取已选择的所有产品类型列表
            var searchProductTypeList = search_In.ProductList.Select(e => e.ProductType.ToEnum<EnumProductType>()).ToList();
            //固定模板允许的产品类型
            var staticTemplateProductTypeList = new List<EnumProductType>() { EnumProductType.Gtis, EnumProductType.Vip, EnumProductType.Combination, EnumProductType.Global };
            //如果所选产品不包含特殊固定模板允许的产品类型，返回空
            if (searchProductTypeList.Intersect(staticTemplateProductTypeList).ToList().Count <= 0)
                return null;
            if ((search_In.ServiceCycle == 1 && search_In.OpenMonth == 12) || (search_In.ServiceCycle == 2 && search_In.OpenMonth == 24) || (search_In.ServiceCycle == 3 && search_In.OpenMonth == 36))
            {
                return null;
            }
            if ((search_In.ServiceCycle == 1 && (search_In.OpenMonth < 12 || search_In.OpenMonth > 14)) || (search_In.ServiceCycle == 2 && (search_In.OpenMonth < 24 || search_In.OpenMonth > 30)) || (search_In.ServiceCycle == 3 && (search_In.OpenMonth < 36 || search_In.OpenMonth > 48)))
            {
                return new GetSpecialContractTemplateList_Out() { CouldAutoAudit = false };
            }
            //3个特殊固定模板：dff17438-ed7d-11ee-9b8d-000c29930887  （1年）\dff1aa07-ed7d-11ee-9b8d-000c29930887 （2年）\dff1dda4-ed7d-11ee-9b8d-000c29930887 （3年）,暂时只用1年的
            List<string> spTempId = new List<string> { "dff17438-ed7d-11ee-9b8d-000c29930887"/*, "dff1aa07-ed7d-11ee-9b8d-000c29930887", "dff1dda4-ed7d-11ee-9b8d-000c29930887"*/ };
            var spTempList = DbOpe_crm_contract_template.Instance.GetContractTemplateListByIds(spTempId);
            var serviceMonth = search_In.ServiceCycle * 12;//常规服务月份：年份*12
            var freeMonth = search_In.OpenMonth - serviceMonth;//赠送月份
            var retTemp = new GetSpecialContractTemplateList_Out()
            {
                CouldAutoAudit = true,
                Id = "dff17438-ed7d-11ee-9b8d-000c29930887",
                ContractDescription = String.Format(spTempList.First().ContractDescription, search_In.ServiceCycle.ToString(), freeMonth.ToString(), search_In.OpenMonth.ToString()),
            };
            string addDes = string.Empty;
            searchProductTypeList.ForEach(spT =>
            {
                if (staticTemplateProductTypeList.Contains(spT))
                {
                    if (spT == EnumProductType.Combination)
                    {
                        addDes += "套餐中GTIS产品、";
                    }
                    else
                    {
                        addDes += spT.GetEnumDescription() + "、";
                    }
                }
            });
            retTemp.ContractDescription = addDes.Substring(0, addDes.Length - 1) + retTemp.ContractDescription;
            return retTemp;
        }

        /// <summary>
        /// 获取合同说明模板主键+描述，无分页
        /// </summary>
        /// <param name="search_In"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        public List<GetUnlimitedContractTemplateList_Out> GetUnlimitedContractTemplateList(GetUnlimitedContractTemplateList_In search_In, ref int total)
        {
            //根据入参获取产品的Id、周期 对象的列表，如果入参的产品Id为空，则取全部产品
            //var serviceInfos = DbOpe_crm_product.Instance.GetProductServiceCycleById(search_In.ProductIdList);
            total = 0;
            if (search_In.ProductList.IsNullOrEmpty<GetUnlimitedContractTemplateList_In_Product>())
                return null;
            //获取所有审核通过的模板
            var tempList = DbOpe_crm_contract_template.Instance.GetAllAgreeTemplateList();
            //获取已选择的所有产品类型列表
            var searchProductTypeList = search_In.ProductList.Select(e => e.ProductType.ToString()).ToList();
            //固定模板允许的产品类型
            var staticTemplateProductTypeList = new List<EnumProductType>() { EnumProductType.Gtis, EnumProductType.Vip, EnumProductType.Combination, EnumProductType.Global };
            List<string> spTempId = new List<string> { "dff17438-ed7d-11ee-9b8d-000c29930887"/*, "dff1aa07-ed7d-11ee-9b8d-000c29930887", "dff1dda4-ed7d-11ee-9b8d-000c29930887"*/ };
            //获取固定模板
            var retList = tempList.Where(e => e.IsStatic == true)
                .Where(e => e.CreditType.Contains(search_In.CreditType.ToInt().ToString()))
                //.Where(e => staticTemplateProductTypeList.Intersect(searchProductTypeList).ToList().Count > 0)
                .Where(e => searchProductTypeList.Any(value => e.ApplicableProductTypes.Contains(value)))
                .Where(e => !spTempId.Contains(e.Id))
                .ToList()
                .MappingTo<List<GetUnlimitedContractTemplateList_Out>>();

            //修改 2024年3月29日 胡文靓 特殊处理两个模板 00009/00010 为gtis与环球搜和慧思学院 且关系，即同时存在，且价格为0
            //当年产品需要包含
            if (searchProductTypeList.Contains(EnumProductType.Global.ToInt().ToString()) || searchProductTypeList.Contains(EnumProductType.GlobalWitsSchool.ToInt().ToString()))
            {

                if (!searchProductTypeList.Contains(EnumProductType.Global.ToInt().ToString()) || search_In.ProductList.Where(e => e.ProductType == EnumProductType.Global.ToInt()).FirstOrDefault().Price > 0)
                {
                    retList = retList.Where(e => e.Id != "dff0a686-ed7d-11ee-9b8d-000c29930887").ToList();
                }

                if (!searchProductTypeList.Contains(EnumProductType.GlobalWitsSchool.ToInt().ToString()) || search_In.ProductList.Where(e => e.ProductType == EnumProductType.GlobalWitsSchool.ToInt()).FirstOrDefault().Price > 0)
                {
                    retList = retList.Where(e => e.Id != "dff0d834-ed7d-11ee-9b8d-000c29930887").ToList();
                }
            }
            else
            {
                retList = retList.Where(e => e.Id != "dff0a686-ed7d-11ee-9b8d-000c29930887")
                                 .Where(e => e.Id != "dff0d834-ed7d-11ee-9b8d-000c29930887")
                                 .ToList();
            }
            #region 2024.5.8 特殊固定模板不通过这里查询
            /* 
            //修改 2024年3月30日 特殊处理固定年份模板
            //dff17438-ed7d-11ee-9b8d-000c29930887  （1年）\dff1aa07-ed7d-11ee-9b8d-000c29930887 （2年）dff1dda4-ed7d-11ee-9b8d-000c29930887 （3年）
            search_In.ProductList.ForEach(info =>
            {
                switch (info.ServiceCycle)
                {
                    case 1:
                        //retList = retList.Where(e => e.Id != "dff1aa07-ed7d-11ee-9b8d-000c29930887")
                        //                 .Where(e => e.Id != "dff1dda4-ed7d-11ee-9b8d-000c29930887")
                        //                 .ToList();
                        retList = retList.Where(e => e.ApplicableYears == 0 || e.ApplicableYears == 1).ToList();
                        break;
                    case 2:
                        //retList = retList.Where(e => e.Id != "dff17438-ed7d-11ee-9b8d-000c29930887")
                        //                 .Where(e => e.Id != "dff1dda4-ed7d-11ee-9b8d-000c29930887")
                        //                 .ToList();
                        retList = retList.Where(e => e.ApplicableYears == 0 || e.ApplicableYears == 2).ToList();
                        break;
                    case 3:
                        //retList = retList.Where(e => e.Id != "dff1aa07-ed7d-11ee-9b8d-000c29930887")
                        //                 .Where(e => e.Id != "dff17438-ed7d-11ee-9b8d-000c29930887")
                        //                 .ToList(); break;
                        retList = retList.Where(e => e.ApplicableYears == 0 || e.ApplicableYears == 3).ToList();
                        break;
                    default:
                        break;
                }
            });
            */
            #endregion
            //var retList = new List<GetUnlimitedContractTemplateList_Out>();
            //获取普通模板
            tempList = tempList.Where(e => e.IsStatic == false).ToList();
            search_In.ProductList.ForEach(info =>
            {
                var list = tempList
                            .WhereIF(!string.IsNullOrEmpty(search_In.CompanyId), e => e.ContractCompany.Contains(search_In.CompanyId))
                            .WhereIF(!string.IsNullOrEmpty(search_In.Content), e => e.ContractDescription.Contains(search_In.Content))
                            .Where(e => e.CreditType.Contains(search_In.CreditType.ToInt().ToString()))
                            .Where(e => e.ApplicableProducts.Contains(info.ProductId))
                            .Where(e => e.ApplicableYears == info.ServiceCycle)
                            .Where(e => !retList.Select(s => s.Id).ToList().Contains(e.Id))
                            .ToList().MappingTo<List<GetUnlimitedContractTemplateList_Out>>();
                /*整理返回结果集retList, 若retList为空，直接填充list,若不为空,则与list取交集*/
                if (retList.Count == 0)
                    retList = list;
                else
                    retList = retList.Union(list).ToList();
                /*if (retList.Count == 0)
                    retList = list;
                else if (list.Count == 1)
                    retList.AddRange(list);//retList = retList.Union(list).ToList();*/
            });
            retList = retList.OrderBy(e => e.ContractTemplateNo).ToList();
            total = retList.Count;
            retList = retList.Skip((search_In.PageNumber - 1) * search_In.PageSize).Take(search_In.PageSize).ToList();


            retList.ForEach(temp =>
            {
                #region 2024.5.8 特殊固定模板不通过这里查询
                /*//修改 2024年3月30日 增加特殊处理 三条特殊模板需要在内容内明确当前选择的产品内容
                List<string> spTempId = new List<string> { "dff17438-ed7d-11ee-9b8d-000c29930887", "dff1aa07-ed7d-11ee-9b8d-000c29930887", "dff1dda4-ed7d-11ee-9b8d-000c29930887" };
                if (spTempId.Contains(temp.Id))
                {
                    string addDes = string.Empty;
                    searchProductTypeList.ForEach(spT =>
                    {
                        if (staticTemplateProductTypeList.Contains(spT.ToInt().ToEnum<EnumProductType>()))
                        {
                            if (spT.ToInt().ToEnum<EnumProductType>() == EnumProductType.Combination)
                            {
                                addDes += "套餐中GTIS产品、";
                            }
                            else
                            {
                                addDes += spT.ToInt().ToEnum<EnumProductType>().GetEnumDescription() + "、";
                            }
                        }
                    });
                    temp.ContractDescription = addDes.Substring(0, addDes.Length - 1) + temp.ContractDescription;
                }*/
                #endregion
                var creditTypeList = temp.CreditType.Split(",").ToList();
                temp.CreditTypeName = new List<string>();
                creditTypeList.ForEach(creditType =>
                {
                    temp.CreditTypeName.Add(creditType.ToEnum<EnumContractTemplateCreditType>().GetEnumDescription().ToString());
                });
                if (temp.IsStatic)
                {
                    temp.ContractCompanyNames = new List<string> { "全部公司" };
                    //temp.ApplicableProductsName = new List<string> { "GTIS正式、VIP零售、组合产品(含GTIS正式产品)" };
                    if (temp.ApplicableProductTypes.IsNotNullOrEmpty())
                    {
                        var productTypeList = temp.ApplicableProductTypes.Split(",").ToList();
                        temp.ApplicableProductsName = new List<string>();
                        productTypeList.ForEach(productType =>
                        {
                            //productType.ToInt() == 3? temp.ApplicableProductsName.Add("组合产品(含GTIS正式产品)"):
                            if (productType.ToInt() == 3)
                            {
                                temp.ApplicableProductsName.Add("组合产品中的GTIS正式产品");
                            }
                            else
                            {
                                temp.ApplicableProductsName.Add(productType.ToInt().ToEnum<EnumProductType>().GetEnumDescription());
                            }
                        });
                    }
                    else
                    {
                        temp.ApplicableProductsName = new List<string> { "GTIS正式、VIP零售、组合产品中的GTIS正式产品" };
                    }

                }
                else
                {
                    temp.ContractCompanyNames = DbOpe_crm_contract_template.Instance.GetContractCompanyNameList(temp.ContractCompany);
                    temp.ApplicableProductsName = DbOpe_crm_contract_template.Instance.GetApplicableProductsNameList(temp.ApplicableProducts);
                }
            });
            return retList;
        }

        /// <summary>
        /// 合同说明模板使用产品列表
        /// </summary>
        /// <param name="IsApply"></param>
        /// <returns></returns>
        public List<GetTemplateProductList_Out> GetTemplateProductList(bool IsApply)
        {
            var productIds = DbOpe_crm_contract_template.Instance.GetTemplateProductList(IsApply);
            return DbOpe_crm_product.Instance.GetTemplateProductList(productIds);
        }


        /// <summary>
        /// 后台人员修改合同说明模板
        /// </summary>
        /// <param name="updateContractTemplate_In"></param>
        public void UpdateContractTemplate4Manager(UpdateContractTemplate4Manager_In updateContractTemplate_In)
        {
            //验证只能修改通过的合同说明模板
            if (!DbOpe_crm_contract_templateapply.Instance.CheckTemplateApplyIsAgree(updateContractTemplate_In.ApplyId))
                throw new ApiException("所选合同说明模板不是通过状态，无法修改!");
            //验证模板是否是通过状态
            if (!DbOpe_crm_contract_template.Instance.CheckTemplateIsAgree(updateContractTemplate_In.ApplyId))
                throw new ApiException("所选合同说明模板不是通过状态，无法修改!");
            //执行更新
            DbOpe_crm_contract_template.Instance.UpdateContractTemplate(updateContractTemplate_In);
        }
    }

}
