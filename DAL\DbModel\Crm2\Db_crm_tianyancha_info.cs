﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("crm_tianyancha_info")]
    public class Db_crm_tianyancha_info
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:企业名
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CompanyName { get; set; }

        /// <summary>
        /// Desc:搜索用名称，去除相关符号。
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ShortName { get; set; }

        /// <summary>
        /// Desc:企业状态
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RegStatus { get; set; }

        /// <summary>
        /// Desc:所在市
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string City { get; set; }

        /// <summary>
        /// Desc:所在区
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string District { get; set; }

        /// <summary>
        /// Desc:人员规模
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string StaffNumRange { get; set; }

        /// <summary>
        /// Desc:经营开始时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string FromTime { get; set; }

        /// <summary>
        /// Desc:经营结束时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ToTime { get; set; }

        /// <summary>
        /// Desc:法人类型，1 人 2 公司
        /// Default:
        /// Nullable:True
        /// </summary>           
        public short? LegalPersonType { get; set; }

        /// <summary>
        /// Desc:股票名
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string BondName { get; set; }

        /// <summary>
        /// Desc:股票号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string BondNum { get; set; }

        /// <summary>
        /// Desc:股票曾用名
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UsedBondName { get; set; }

        /// <summary>
        /// Desc:股票类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string BondType { get; set; }

        /// <summary>
        /// Desc:企业id
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Cid { get; set; }

        /// <summary>
        /// Desc:是否是小微企业 0不是 1是
        /// Default:
        /// Nullable:True
        /// </summary>           
        public short? IsMicroEnt { get; set; }

        /// <summary>
        /// Desc:注册号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RegNumber { get; set; }

        /// <summary>
        /// Desc:企业评分(万分制)
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? PercentileScore { get; set; }

        /// <summary>
        /// Desc:注册资本
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RegCapital { get; set; }

        /// <summary>
        /// Desc:注册资本币种 人民币 美元 欧元 等
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RegCapitalCurrency { get; set; }

        /// <summary>
        /// Desc:登记机关
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RegInstitute { get; set; }

        /// <summary>
        /// Desc:注册地址
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RegLocation { get; set; }

        /// <summary>
        /// Desc:行业
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Industry { get; set; }

        /// <summary>
        /// Desc:核准时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ApprovedTime { get; set; }

        /// <summary>
        /// Desc:参保人数
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? SocialStaffNum { get; set; }

        /// <summary>
        /// Desc:企业标签
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Tags { get; set; }

        /// <summary>
        /// Desc:纳税人识别号
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string TaxNumber { get; set; }

        /// <summary>
        /// Desc:经营范围
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string BusinessScope { get; set; }

        /// <summary>
        /// Desc:英文名
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Property3 { get; set; }

        /// <summary>
        /// Desc:简称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Alias { get; set; }

        /// <summary>
        /// Desc:组织机构代码
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string OrgNumber { get; set; }

        /// <summary>
        /// Desc:成立日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string EstiblishTime { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateTimes { get; set; }

        /// <summary>
        /// Desc:法人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string LegalPersonName { get; set; }

        /// <summary>
        /// Desc:企业类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CompanyOrgType { get; set; }

        /// <summary>
        /// Desc:省份简称
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Base { get; set; }

        /// <summary>
        /// Desc:统一社会信用代码
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreditCode { get; set; }

        /// <summary>
        /// Desc:曾用名,多个用竖线分隔。
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string HistoryNames { get; set; }

        /// <summary>
        /// Desc:实收注册资金
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ActualCapital { get; set; }

        /// <summary>
        /// Desc:实收注册资本币种 人民币 美元 欧元 等
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string ActualCapitalCurrency { get; set; }

        /// <summary>
        /// Desc:吊销日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RevokeDate { get; set; }

        /// <summary>
        /// Desc:吊销原因
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string RevokeReason { get; set; }

        /// <summary>
        /// Desc:注销日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CancelDate { get; set; }

        /// <summary>
        /// Desc:注销原因
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CancelReason { get; set; }

        /// <summary>
        /// Desc:国民经济行业分类门类 ，竖线分隔(门类|大类|中类|小类)。
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Category { get; set; }

        /// <summary>
        /// Desc:记录日期
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? Indate { get; set; }

        /// <summary>
        /// Desc:匹配类型
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MatchType { get; set; }


    }
}
