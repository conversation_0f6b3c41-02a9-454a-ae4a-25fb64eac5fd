﻿using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.Common.Cache;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Math;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Office2013.Word;
using DocumentFormat.OpenXml.Presentation;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.VariantTypes;
using DocumentFormat.OpenXml.Wordprocessing;
using JiebaNet.Segmenter.Common;
using LgyUtil;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.OpenApi.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SixLabors.ImageSharp;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Dynamic;
using System.IO;
using System.Runtime.Intrinsics.X86;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;

namespace CRM2_API.BLL
{
    public class BLL_Statistics : BaseBLL<BLL_Statistics>
    {
        private string currentUser;


        public BLL_Statistics()
        {
            currentUser = UserId;
#if DEBUG
            //currentUser = "02880719-b912-4df5-a5e0-acf0c010a505";
            //currentUser = "5bb52b03-2476-4fc8-b7df-66844727e5ec";
            //currentUser = "5bb52b03-2476-4fc8-b7df-66844727e5ec";
            //currentUser = "78fe2d7b-6ce4-4040-a30b-3a2f10b9a972";
            //currentUser = "df18b32a-c6c1-4f9c-951f-aa36fe770628";
#endif
        }


        #region  最新的业绩统计方法

        /// <summary>
        /// 获取业绩统计数据
        /// </summary>
        /// <param name="achiveRankStatistics_IN"></param>
        /// <param name="userId">查询人（数据权限限制有关，不填默认当前登录用户）</param>
        /// <returns></returns>
        public List<AchiveRankStatistics_OUT> AchivementRankStatistics(AchiveRankStatistics_IN achiveRankStatistics_IN, string userId = "")
        {
            ////测试用
            //orgRankStatistics_IN.IgnoreSortSet = true;
            List<AchiveRankStatistics_OUT> result = new List<AchiveRankStatistics_OUT>();
            #region 拼默认参数
            var useCurrentOrg = false;
            if (achiveRankStatistics_IN.DateStart != null && achiveRankStatistics_IN.DateEnd != null)
            {
                var starYear = achiveRankStatistics_IN.DateStart.Value.Year;
                var endYear = achiveRankStatistics_IN.DateEnd.Value.Year;
                useCurrentOrg = starYear == endYear && starYear == DateTime.Now.Year;
                achiveRankStatistics_IN.DateStart = achiveRankStatistics_IN.DateStart.Value.GetMonthStart();
                achiveRankStatistics_IN.DateEnd = achiveRankStatistics_IN.DateEnd.Value.GetMonthEnd();
            }
            else
            {
                useCurrentOrg = true;
                achiveRankStatistics_IN.DateStart = DateTime.Now.GetMonthStart();
                achiveRankStatistics_IN.DateEnd = DateTime.Now.GetMonthEnd();
            }
            if (string.IsNullOrEmpty(userId))
            {
                userId = currentUser;
            }
            #endregion
            if (achiveRankStatistics_IN.EnumRankOrgType == EnumStatisticOrgType.User)
            {
                //先获取结果集
                var staResult = DbOpe_crm_contract_receiptregister.Instance.UserAchivementRank(achiveRankStatistics_IN.MappingTo<UserRankStatisticsParams>(), useCurrentOrg);
                //下面对结果集进行排序，格式化，筛序等操作
                int index = 0;
                int lastSortNum = 0;
                int tempSortIndex = 1;
                #region 获取当前用户有权限查看/并且在要查询的组织下 的所有组织Id(如果是普通销售，这里只查自己)  
                //这里要同时查组织和用户，因为有可能查的是当前组织历史业绩，而历史业绩所属人现在已经不在这个团队了
                //20250519 这里要修复一个问题 查下级团队业绩时，不要把当前管理者自己业绩也算进去
                List<string> showUserIds = new List<string>();
                List<string> showOrgIds = DbOpe_sys_user.Instance.GetAuthShowOrgIdsAndUserIds(achiveRankStatistics_IN.SelectOrgId, userId,ref showUserIds);
                #endregion

                #region 筛选职级
                if (achiveRankStatistics_IN.EnumStaPromotionType == EnumStaPromotionType.QYZJUP)
                {
                    staResult = staResult.Where(r => r.RankSortCode >= 90 || r.UserType == 1).ToList(); //管理者默认是区域总监
                }
                else if (achiveRankStatistics_IN.EnumStaPromotionType == EnumStaPromotionType.QYZJDOWN)
                {
                    staResult = staResult.Where(r => (r.RankSortCode < 90 || r.RankSortCode == null) && r.UserType != 1).ToList(); //管理者默认是区域总监
                }
                #endregion

                #region 对结果进行排序
                if (achiveRankStatistics_IN.IgnoreSortSet)
                {
                    staResult = staResult
                    .OrderByDescending(it => it.UserValue)
                    .ToList();
                }
                else
                {
                    staResult = staResult
                    .OrderBy(it => it.SortOrder)
                    .ThenByDescending(it => it.UserValue)
                    .ToList();
                }
                #endregion

                #region 循环处理数据的格式等
                foreach (var obj in staResult)
                {
                    AchiveRankStatistics_OUT r = obj.MappingTo<AchiveRankStatistics_OUT>();
                    r.OrgValue = obj.OrgValue == null ? "0" : obj.OrgValue.Value.ToString("F0");
                    r.UserValue = obj.UserValue == null ? "0" : obj.UserValue.Value.ToString("F0");
                    r.Value_In = obj.Value_In == null ? "0" : obj.Value_In.Value.ToString("F0");
                    r.Value_Out = obj.Value_Out == null ? "0" : obj.Value_Out.Value.ToString("F0");
                    #region 三层机构名称
                    if (!string.IsNullOrEmpty(r.OrgRegimentId) && string.IsNullOrEmpty(r.OrgDivisionId) && string.IsNullOrEmpty(r.OrgBrigadeId))
                    {
                        //空-空-中队
                        r.OrgDivisionName = "其他战队";
                        r.OrgBrigadeName = "其他大队";
                    }
                    else if (!string.IsNullOrEmpty(r.OrgRegimentId) && string.IsNullOrEmpty(r.OrgDivisionId))
                    {
                        //空-大队-中队
                        r.OrgDivisionName = "其他战队";
                    }
                    else if (!string.IsNullOrEmpty(r.OrgRegimentId) && string.IsNullOrEmpty(r.OrgBrigadeId))
                    {
                        //战队-空-中队
                        r.OrgBrigadeName = "其他大队";
                    }
                    else if (string.IsNullOrEmpty(r.OrgRegimentId) && string.IsNullOrEmpty(r.OrgBrigadeId))
                    {
                        //战队-空-空
                        r.OrgBrigadeName = "";
                        r.OrgRegimentName = r.OrgDivisionName + "直属";
                    }
                    else if (string.IsNullOrEmpty(r.OrgRegimentId) && string.IsNullOrEmpty(r.OrgDivisionId))
                    {
                        //空-大队-空
                        r.OrgDivisionName = "其他战队";
                        r.OrgRegimentName = r.OrgBrigadeName + "直属";
                        r.OrgName = r.OrgBrigadeName + "直属";
                    }
                    else if (string.IsNullOrEmpty(r.OrgRegimentId) && !string.IsNullOrEmpty(r.OrgDivisionId) && !string.IsNullOrEmpty(r.OrgBrigadeId))
                    {
                        //战队-大队-空
                        r.OrgRegimentName = r.OrgBrigadeName + "直属";
                        r.OrgName = r.OrgBrigadeName + "直属";
                    }
                    #endregion
                    #region 生成排序号
                    if (index == 0)
                    {
                        lastSortNum = r.SortOrder;
                    }
                    else if (lastSortNum != r.SortOrder && !achiveRankStatistics_IN.IgnoreSortSet)
                    {
                        //序号从1重新开始
                        tempSortIndex = 1;
                        lastSortNum = r.SortOrder;
                    }
                    r.OrderIndex = tempSortIndex++;
                    index++;
                    #endregion
                    #region 判断显示权限
                    if (r.OrgId == null || r.UserId == null || (!showOrgIds.Contains(r.OrgId) && !showUserIds.Contains(r.UserId)))
                    {
                        //如果当前用户没有权限查看此组织或此人员，则不显示
                        continue;
                    }
                    #endregion
                    result.Add(r);
                }
                #endregion
            }
            else
            {
                //先获取结果集
                var staResult = DbOpe_crm_contract_receiptregister.Instance.OrgAchivementRank(achiveRankStatistics_IN.MappingTo<OrgRankStatisticsParams>(), useCurrentOrg);
                //下面对结果集进行排序，格式化，筛序等操作
                int index = 0;
                int lastSortNum = 0;
                int tempSortIndex = 1;
                #region 获取当前用户有权限查看/并且在要查询的组织下 的所有组织Id
                List<string> showOrgIds = DbOpe_sys_organization.Instance.GetAuthShowOrgIds(achiveRankStatistics_IN.SelectOrgId, userId);
                #endregion

                #region 筛选业绩AB组
                if (achiveRankStatistics_IN.EnumRankOrgType == EnumStatisticOrgType.Squadron && achiveRankStatistics_IN.EnumSquadronType != EnumSquadronType.SquadronTypeAll)
                {
                    staResult = staResult
                        .WhereIF(achiveRankStatistics_IN.EnumSquadronType == EnumSquadronType.SquadronTypeA, x => x.OrgFactorValue == "A")
                        .WhereIF(achiveRankStatistics_IN.EnumSquadronType == EnumSquadronType.SquadronTypeB, x => x.OrgFactorValue == "B")
                        .ToList();
                }
                #endregion

                #region 处理人均业绩
                bool averageValid = (achiveRankStatistics_IN.EnumRankOrgType == EnumStatisticOrgType.BattleTeam || achiveRankStatistics_IN.EnumRankOrgType == EnumStatisticOrgType.Battalion);
                if (averageValid)
                {
                    int temp;
                    staResult.ForEach(s =>
                    {
                        //这里注意 暂时写-1 表示没有参数 后面会给为 -- 
                        s.OrgAverageValue = (int.TryParse(s.OrgFactorValue, out temp) && temp != 0) ? (s.OrgValue/(s.OrgFactorValue.ToDecimal())) : -1;
                        s.OrgAverageValue_In = (int.TryParse(s.OrgFactorValue, out temp) && temp != 0) ? (s.Value_In / (s.OrgFactorValue.ToDecimal())) : -1;
                        s.OrgAverageValue_Out = (int.TryParse(s.OrgFactorValue, out temp) && temp != 0) ? (s.Value_Out / (s.OrgFactorValue.ToDecimal())) : -1;
                    });
                }
                #endregion

                #region 对结果进行排序
                if (averageValid && achiveRankStatistics_IN.IsAverageSort)
                {
                    if (achiveRankStatistics_IN.IgnoreSortSet)
                    {
                        staResult = staResult
                        .OrderByDescending(it => it.OrgAverageValue)
                        .ToList();
                    }
                    else
                    {
                        staResult = staResult
                        .OrderBy(it => it.SortOrder)
                        .ThenByDescending(it => it.OrgAverageValue)
                        .ToList();
                    }
                }
                else {
                    if (achiveRankStatistics_IN.IgnoreSortSet)
                    {
                        staResult = staResult
                        .OrderByDescending(it => it.OrgValue)
                        .ToList();
                    }
                    else
                    {
                        staResult = staResult
                        .OrderBy(it => it.SortOrder)
                        .ThenByDescending(it => it.OrgValue)
                        .ToList();
                    }
                }
                #endregion
                staResult.ForEach(r =>
                {
                    AchiveRankStatistics_OUT orgRankStatistics_OUT = new AchiveRankStatistics_OUT();
                    orgRankStatistics_OUT = r.MappingTo<AchiveRankStatistics_OUT>();
                    orgRankStatistics_OUT.ManagerValue = r.ManagerValue == null ? "0" : r.ManagerValue.Value.ToString("F0");
                    if (averageValid)
                    {
                        if (r.OrgAverageValue == -1)
                        {
                            orgRankStatistics_OUT.OrgAverageValue = "--";
                            orgRankStatistics_OUT.OrgAverageValue_In = "--";
                            orgRankStatistics_OUT.OrgAverageValue_Out = "--";
                        }
                        else {
                            orgRankStatistics_OUT.OrgAverageValue = r.OrgAverageValue == null ? "0" : r.OrgAverageValue.Value.ToString("F0");
                            orgRankStatistics_OUT.OrgAverageValue_In = r.OrgAverageValue_In == null ? "0" : r.OrgAverageValue_In.Value.ToString("F0");
                            orgRankStatistics_OUT.OrgAverageValue_Out = r.OrgAverageValue_Out == null ? "0" : r.OrgAverageValue_Out.Value.ToString("F0");
                        }
                    }
                    else {
                        orgRankStatistics_OUT.OrgAverageValue = "--";
                        orgRankStatistics_OUT.OrgAverageValue_In = "--";
                        orgRankStatistics_OUT.OrgAverageValue_Out = "--";
                    }
                    orgRankStatistics_OUT.OtherValue = r.OtherValue == null ? "0" : r.OtherValue.Value.ToString("F0");
                    orgRankStatistics_OUT.OrgValue = r.OrgValue == null ? "0" : r.OrgValue.Value.ToString("F0");
                    orgRankStatistics_OUT.Value_In = r.Value_In == null ? "0" : r.Value_In.Value.ToString("F0");
                    orgRankStatistics_OUT.Value_Out = r.Value_Out == null ? "0" : r.Value_Out.Value.ToString("F0");
                    #region 排序号
                    if (index == 0)
                    {
                        lastSortNum = orgRankStatistics_OUT.SortOrder;
                    }
                    else if (lastSortNum != orgRankStatistics_OUT.SortOrder && !achiveRankStatistics_IN.IgnoreSortSet)
                    {
                        //序号从1重新开始
                        tempSortIndex = 1;
                        lastSortNum = orgRankStatistics_OUT.SortOrder;
                    }
                    orgRankStatistics_OUT.OrderIndex = tempSortIndex++;
                    index++;
                    #endregion
                    #region 判断显示权限
                    if (r.OrgId != null && !showOrgIds.Contains(r.OrgId))
                    {
                        //如果当前用户没有权限查看此组织，则不显示
                        return;
                    }
                    #endregion
                    result.Add(orgRankStatistics_OUT);
                });
                return result;
               
            }
            return result;
        }


        #endregion


        #region 统计分析
        private List<StaYearOtherDic> GenerateYearMonths(DateTime startDate, DateTime endDate)
        {
            List<StaYearOtherDic> result = new List<StaYearOtherDic>();

            // 循环从起始日期到结束日期的年月
            // 注意：这里使用了endDate.AddMonths(1)来确保endDate所在的月份被包括在内
            for (DateTime current = startDate.GetMonthEnd(); current < endDate.AddMonths(1).GetMonthStart(); current = current.AddMonths(1).GetMonthEnd())
            {
                result.Add(new StaYearOtherDic(current.Year, current.Month, EnumStaDateSpan.Month));
            }

            return result;
        }
        private List<StaYearOtherDic> GenerateYearQuarters(DateTime startDate, DateTime endDate)
        {
            List<StaYearOtherDic> result = new List<StaYearOtherDic>();

            // 计算起始和结束年份及季度
            int startYear = startDate.Year;
            int startQuarter = (int)Math.Ceiling((double)startDate.Month / 3.0);
            int endYear = endDate.Year;
            int endQuarter = (int)Math.Ceiling((double)endDate.Month / 3.0);

            // 循环遍历年份和季度
            for (int year = startYear; year <= endYear; year++)
            {
                for (int quarter = (year == startYear ? startQuarter : 1); quarter <= (year == endYear ? endQuarter : 4); quarter++)
                {
                    result.Add(new StaYearOtherDic(year, quarter, EnumStaDateSpan.Quarter));
                }
            }

            return result;
        }
        private List<StaYearOtherDic> GenerateYears(DateTime startDate, DateTime endDate)
        {
            List<StaYearOtherDic> result = new List<StaYearOtherDic>();

            // 循环从起始日期到结束日期的年月
            for (DateTime current = startDate; current.Year <= endDate.Year; current = current.AddYears(1))
            {
                result.Add(new StaYearOtherDic(current.Year, 0, EnumStaDateSpan.Year));
            }

            return result;
        }
        private void FindLeafNodesRecursive(List<Dictionary_Sta_Out> nodes, string parentId, ref List<Dictionary_Sta_Out> results)
        {
            foreach (var node in nodes.Where(n => n.ParentId.ToString() == parentId))
            {
                if (!nodes.Any(n => n.ParentId == node.Id)) // 检查是否是最下级节点
                {
                    results.Add(node);
                }
                else
                {
                    FindLeafNodesRecursive(nodes, node.Id.ToString(), ref results); // 递归检查子节点
                }
            }
        }
        public ISugarQueryable<GetAchivementQuery> GetAchivemntQuery(GetAchivementQuery_IN query_IN)
        {
            return DbOpe_crm_contract_receiptregister.Instance.GetAchivemntQuery(query_IN);
        }
        public ISugarQueryable<GetCustomerNowQueryShow> GetCustomerNowQuery(GetCustomerNowQuery getCustomerNowQuery)
        {
            return DbOpe_crm_customer_org_log.Instance.GetCustomerNowQuery(getCustomerNowQuery);
        }
        /// <summary>
        /// 趋势统计
        /// </summary>
        /// <param name="query_IN"></param>
        /// <returns></returns>
        public StaticticsTableResult GetAchivemntTrendQuery(GetAchivementTrendQuery_IN query_IN)
        {
            StaticticsTableResult result = new StaticticsTableResult();
            GetAchivementQuery_IN getAchivemntQuery = query_IN.MappingTo<GetAchivementQuery_IN>();
            getAchivemntQuery.DateType = EnumGetAchivemntQueryDateType.AchiveDate;
            #region 权限 (非销售组织的话就是所有组织下所有人)
            var user = DbOpe_sys_user.Instance.GetUserById(currentUser);
            List<string> showOrgIds = new List<string>();
            List<string> showUserIds = new List<string>();
            if (user.UserType != EnumUserType.Sales)
            {
                showOrgIds = DbOpe_crm_contract_receiptregister.Instance.GetAuthShowOrgIdsAndUserIds(query_IN.SelectOrgId, currentUser, ref showUserIds);
            }
            else
            {
                showUserIds = new List<string>() { currentUser };
                showOrgIds = new List<string>() { user.OrganizationId };
            }
            getAchivemntQuery.UserIds = showUserIds;
            getAchivemntQuery.UserOrgIds = showOrgIds;
            #endregion
            #region 获取统计数据配置
            var dic = DbOpe_sys_dictionary_statictics.Instance.GetStaDictionaryByPageAndParentName(new Dictionary_Sta_In()
            {
                PageType = 3
            }, currentUser, true);
            getAchivemntQuery.StaValues = new List<EnumGetAchivemntQueryValue>();
            if (query_IN.SelectStaValues != null)
            {
                //统计值列
                foreach (var selectValueCol in query_IN.SelectStaValues)
                {
                    var valueColParent = dic.Find(d => d.Id.ToString() == selectValueCol);
                    List<Dictionary_Sta_Out> valueColDics = new List<Dictionary_Sta_Out>();
                    FindLeafNodesRecursive(dic, valueColParent.Id.ToString(), ref valueColDics);

                    if (valueColDics.Count > 0)
                    {
                        foreach (var valueCol in valueColDics)
                        {
                            getAchivemntQuery.StaValues.Add((EnumGetAchivemntQueryValue)valueCol.Value.ToInt());
                        }
                    }
                    else
                    {
                        getAchivemntQuery.StaValues.Add((EnumGetAchivemntQueryValue)valueColParent.Value.ToInt());
                    }

                }
            }
            getAchivemntQuery.StaCountCols = new List<StatisticsCountItem>();
            if (query_IN.SelectStaCountCols != null)
            {
                //统计数量列
                foreach (var selectCountCol in query_IN.SelectStaCountCols)
                {
                    var countColParent = dic.Find(d => d.Id.ToString() == selectCountCol);
                    List<Dictionary_Sta_Out> countColDics = new List<Dictionary_Sta_Out>();
                    FindLeafNodesRecursive(dic, countColParent.Id.ToString(), ref countColDics);

                    if (countColDics.Count > 0)
                    {
                        foreach (var countCol in countColDics)
                        {
                            getAchivemntQuery.StaCountCols.Add(new StatisticsCountItem() { Name = countCol.Name, Value = countCol.Value, NameColKey = countCol.TableName, ConditionColKey = countCol.ConditionKey, AllowSort = countCol.AllowSort, AllowTable = countCol.AllowTable });
                        }
                    }
                    else
                    {
                        getAchivemntQuery.StaCountCols.Add(new StatisticsCountItem() { Name = countColParent.Name, Value = countColParent.Value, NameColKey = countColParent.TableName, ConditionColKey = countColParent.ConditionKey, AllowSort = countColParent.AllowSort, AllowTable = countColParent.AllowTable });
                    }

                }
            }
            getAchivemntQuery.StaFigures = new List<EnumFigureValue>();
            if (query_IN.SelectStaFigures != null)
            {

                //统计值列
                foreach (var selectFiguresCol in query_IN.SelectStaFigures)
                {
                    var figuresColParent = dic.Find(d => d.Id.ToString() == selectFiguresCol);
                    List<Dictionary_Sta_Out> figuresColDics = new List<Dictionary_Sta_Out>();
                    FindLeafNodesRecursive(dic, figuresColParent.Id.ToString(), ref figuresColDics);

                    if (figuresColDics.Count > 0)
                    {
                        foreach (var figuresCol in figuresColDics)
                        {
                            getAchivemntQuery.StaFigures.Add((EnumFigureValue)figuresCol.Value.ToInt());
                        }
                    }
                    else
                    {
                        getAchivemntQuery.StaFigures.Add((EnumFigureValue)figuresColParent.Value.ToInt());
                    }

                }
            }
            getAchivemntQuery.StaFigures.Sort();
            //统计左列
            getAchivemntQuery.LeftCols = new List<StatisticsLeftColItem>();
            var leftColParent = dic.Find(d => d.Id.ToString() == query_IN.SelectLeftCol);
            List<Dictionary_Sta_Out> leftColDics = new List<Dictionary_Sta_Out>();
            FindLeafNodesRecursive(dic, leftColParent.Id.ToString(), ref leftColDics);
            foreach (var leftCol in leftColDics)
            {
                getAchivemntQuery.LeftCols.Add(new StatisticsLeftColItem() { Name = leftCol.Name, Value = leftCol.Value, NameColKey = leftCol.TableName, AllowSort = leftCol.AllowSort });
            }
            //统计右列
            getAchivemntQuery.RightCols = new List<StatisticsLeftColItem>();
            var rightColParent = dic.Find(d => d.Id.ToString() == query_IN.SelectRightCol);
            List<Dictionary_Sta_Out> rightColDics = new List<Dictionary_Sta_Out>();
            FindLeafNodesRecursive(dic, rightColParent.Id.ToString(), ref rightColDics);
            foreach (var rightCol in rightColDics)
            {
                getAchivemntQuery.RightCols.Add(new StatisticsLeftColItem() { Name = rightCol.Name, Value = rightCol.Value, NameColKey = rightCol.TableName, AllowSort = rightCol.AllowSort });
            }
            #endregion
            #region 排序
            string sortField = getAchivemntQuery.SortField;
            if (string.IsNullOrEmpty(sortField) ||
                (
                    (getAchivemntQuery.StaCountCols.Find(c => c.NameColKey == sortField) == null)
                    && (getAchivemntQuery.LeftCols.Find(c => c.NameColKey == sortField) == null)
                    && (getAchivemntQuery.StaValues.Find(c => c.GetDisplayName() == sortField) == null)
                )
            )
            {
                sortField = getAchivemntQuery.LeftCols[0].NameColKey;
            }
            List<OrderByModel> orderList = OrderByModel.Create(
                new OrderByModel() { FieldName = sortField, OrderByType = getAchivemntQuery.IsDESC ? OrderByType.Desc : OrderByType.Asc }
            );
            #endregion
            #region 构建SelectModel
            string leftColKeys = getAchivemntQuery.LeftCols.Select(c => c.Value).ToList().JoinToString();
            string RightColKeys = getAchivemntQuery.RightCols.Select(c => c.Value).ToList().JoinToString();
            string groupKeys = getAchivemntQuery.LeftCols.Select(c => c.Value).Concat(getAchivemntQuery.RightCols.Select(c => c.Value)).ToList().JoinToString();
            var leftGroupSingleObj = getAchivemntQuery.LeftCols.OrderByDescending(c => c.AllowSort).FirstOrDefault();
            var selector = new List<SelectModel>();
            var nameSelector = new List<SelectModel>();
            var leftSelector = new List<SelectModel>();
            var leftNameSelector = new List<SelectModel>();
            foreach (var valueItem in getAchivemntQuery.StaValues)
            {
                selector.Add(new SelectModel() { AsName = valueItem.GetDisplayName(), FiledName = ObjectFuncModel.Create("AggregateSumNoNull", valueItem.GetDisplayName()) });
                nameSelector.Add(new SelectModel() { FiledName = valueItem.GetDisplayName() });
                leftSelector.Add(new SelectModel() { AsName = valueItem.GetDisplayName(), FiledName = ObjectFuncModel.Create("AggregateSumNoNull", valueItem.GetDisplayName()) });
                leftNameSelector.Add(new SelectModel() { FiledName = valueItem.GetDisplayName() });
                //增加同环比 注：未处理按年统计不存在环比问题
                foreach (var Figure in getAchivemntQuery.StaFigures)
                {
                    selector.Add(new SelectModel() { AsName = valueItem.GetDisplayName() + Figure.GetDisplayName(), FiledName = ObjectFuncModel.Create("AggregateSumNoNull", valueItem.GetDisplayName()) });
                    nameSelector.Add(new SelectModel()
                    {
                        FiledName = valueItem.GetDisplayName() + Figure.GetDisplayName(),
                    });
                }
            }
            foreach (var countItem in getAchivemntQuery.StaCountCols)
            {
                selector.Add(new SelectModel() { AsName = countItem.NameColKey, FiledName = ObjectFuncModel.Create("AggregateDistinctCount", countItem.Value) });
                nameSelector.Add(new SelectModel() { FiledName = countItem.NameColKey });
                leftSelector.Add(new SelectModel() { AsName = countItem.NameColKey, FiledName = ObjectFuncModel.Create("AggregateDistinctCount", countItem.Value) });
                leftNameSelector.Add(new SelectModel() { FiledName = countItem.NameColKey });
                //增加同环比 注：未处理按年统计不存在环比问题
                foreach (var Figure in getAchivemntQuery.StaFigures)
                {

                    if (Figure == EnumFigureValue.FigurePercent || Figure == EnumFigureValue.CyclePercent)
                    {
                        selector.Add(new SelectModel() { AsName = countItem.NameColKey + Figure.GetDisplayName(), FiledName = ObjectFuncModel.Create("ToDecimal", ObjectFuncModel.Create("AggregateSumNoNull", countItem.Value)) });
                    }
                    else
                    {
                        selector.Add(new SelectModel() { AsName = countItem.NameColKey + Figure.GetDisplayName(), FiledName = ObjectFuncModel.Create("AggregateSumNoNull", countItem.Value) });
                    }
                    nameSelector.Add(new SelectModel()
                    {
                        FiledName = countItem.NameColKey + Figure.GetDisplayName(),
                    });
                }
            }
            foreach (var leftItem in getAchivemntQuery.LeftCols)
            {

                selector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = leftItem.Value });
                leftSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = leftItem.Value });
                leftSelector.Add(new SelectModel() { AsName = leftItem.Value, FiledName = leftItem.Value });



                leftNameSelector.Add(new SelectModel() { AsName = leftItem.Value, FiledName = leftItem.Value });
                if (leftItem.Value == "UserId")
                {
                    selector.Add(new SelectModel() { AsName = leftItem.Value, FiledName = leftItem.Value });
                    leftNameSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = "user." + leftItem.NameColKey });
                    nameSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = "user." + leftItem.NameColKey });
                }
                else
                {
                    leftNameSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = leftItem.NameColKey });
                    nameSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = leftItem.NameColKey });
                }

            }
            foreach (var rightItem in getAchivemntQuery.RightCols)
            {
                selector.Add(new SelectModel() { AsName = rightItem.Value, FiledName = rightItem.Value });
                nameSelector.Add(new SelectModel() { AsName = rightItem.NameColKey, FiledName = rightItem.NameColKey });
            }
            #endregion
            #region 先获取当前排序下需要查询的所有左侧项目值
            int total = 0;
            var leftList = GetAchivemntQuery(getAchivemntQuery)
                .GroupBy(leftGroupSingleObj.Value)
                .Select(leftSelector)
                .MergeTable()
                .LeftJoinIF<Db_sys_user>(leftColKeys.Contains("UserId"), (it, user) => it.UserId == user.Id)
                .Select<dynamic>(leftNameSelector)
                .MergeTable()
                .OrderBy(orderList)
                .ToPageList(getAchivemntQuery.PageNumber, getAchivemntQuery.PageSize, ref total);
            result.total = total;
            #endregion

            #region 左侧列变为条件
            var conModels = new List<IConditionalModel>();
            foreach (var leftItem in getAchivemntQuery.LeftCols)
            {
                var conditionalList = new List<KeyValuePair<WhereType, SqlSugar.ConditionalModel>>();
                var fieldName = leftItem.Value.ToString();
                var fieldValues = leftList.Select(item =>
                {
                    try
                    {
                        IDictionary<string, object> vItem = item as IDictionary<string, object>;
                        if (vItem.TryGetDicValue<string>(fieldName, out string v))
                        {
                            return v;
                        }
                        else
                        {
                            return "";
                        }
                    }
                    catch (Exception)
                    {
                        // 如果在尝试获取属性时发生异常（比如属性不存在），则返回空字符串
                        return "";
                    }
                }).ToList();
                for (int i = 0; i < fieldValues.Count; i++)
                {
                    var leftResultObj = fieldValues[i];
                    WhereType whereType = WhereType.Or;
                    if (i == 0)
                    {
                        whereType = WhereType.And;
                    }
                    conditionalList.Add(new KeyValuePair<WhereType, ConditionalModel>(
                   whereType,
                   new ConditionalModel() { FieldName = fieldName, ConditionalType = ConditionalType.Equal, FieldValue = leftResultObj }));
                    if (string.IsNullOrEmpty(leftResultObj))
                    {
                        conditionalList.Add(new KeyValuePair<WhereType, ConditionalModel>(
                           WhereType.Or,
                           new ConditionalModel() { FieldName = fieldName, ConditionalType = ConditionalType.IsNullOrEmpty }));
                    }
                }
                conModels.Add(new ConditionalCollections() { ConditionalList = conditionalList });
            }
            #endregion

            #region 以左侧分页后数据位条件进行统计
            var list = GetAchivemntQuery(getAchivemntQuery)
                .Where(conModels)
                .GroupBy(groupKeys)
                .Select(selector)
                .MergeTable()
                .LeftJoinIF<Db_sys_user>(leftColKeys.Contains("UserId"), (it, user) => it.UserId == user.Id)
                .Select<ExpandoObject>(nameSelector)
                .MergeTable()
                .OrderBy(orderList)
                .ToList();
            #endregion

            #region 获取所有右侧时间段
            List<StaYearOtherDic> yearOtherDicList = new List<StaYearOtherDic>();
            if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupMonth") != null)
            {
                //月度统计
                yearOtherDicList = GenerateYearMonths(getAchivemntQuery.StartDate, getAchivemntQuery.EndDate);
            }
            else if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupQuarter") != null)
            {
                //季度统计
                yearOtherDicList = GenerateYearQuarters(getAchivemntQuery.StartDate, getAchivemntQuery.EndDate);
            }
            else
            {
                //年度统计
                yearOtherDicList = GenerateYears(getAchivemntQuery.StartDate, getAchivemntQuery.EndDate);
            }
            #endregion

            #region 处理同环比条件
            var hasFigure = getAchivemntQuery.StaFigures.Find(s => s == EnumFigureValue.Figure || s == EnumFigureValue.FigurePercent) != null;
            var hasCycle = getAchivemntQuery.StaFigures.Find(s => s == EnumFigureValue.Cycle || s == EnumFigureValue.CyclePercent) != null;
            List<ExpandoObject> figurelist = new List<ExpandoObject>();
            List<ExpandoObject> cyclelist = new List<ExpandoObject>();
            if (hasFigure || hasCycle)
            {
                //开始处理同环比数据
                //同比 -1年
                GetAchivementQuery_IN getFigureAchivemntQuery_IN = getAchivemntQuery.MappingTo<GetAchivementQuery_IN>();
                getFigureAchivemntQuery_IN.StartDate = getAchivemntQuery.StartDate.AddYears(-1);
                getFigureAchivemntQuery_IN.EndDate = getAchivemntQuery.EndDate.AddYears(-1);
                figurelist = GetAchivemntQuery(getFigureAchivemntQuery_IN)
                    .Where(conModels)
                    .GroupBy(groupKeys)
                    .Select(selector)
                    .MergeTable()
                    .LeftJoinIF<Db_sys_user>(leftColKeys.Contains("UserId"), (it, user) => it.UserId == user.Id)
                    .Select<ExpandoObject>(nameSelector)
                    .MergeTable()
                    .OrderBy(orderList)
                    .ToList();

                //环比 -开始日期结束间隔
                int days = getAchivemntQuery.EndDate.Subtract(getAchivemntQuery.StartDate).Days;  //值为1
                GetAchivementQuery_IN getCycleAchivemntQuery_IN = getAchivemntQuery.MappingTo<GetAchivementQuery_IN>();

                if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupMonth") != null)
                {
                    getCycleAchivemntQuery_IN.StartDate = getAchivemntQuery.StartDate.AddYears(-1).GetMonthStart();
                    //getCycleAchivemntQuery_IN.EndDate = getAchivemntQuery.EndDate.AddYears(-1).GetMonthEnd();
                }
                else if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupQuarter") != null)
                {
                    getCycleAchivemntQuery_IN.StartDate = getAchivemntQuery.StartDate.AddYears(-1).GetQuarterStart();
                    //getCycleAchivemntQuery_IN.EndDate = getAchivemntQuery.EndDate.AddYears(-1).GetQuarterEnd();
                }
                else
                {
                    getCycleAchivemntQuery_IN.StartDate = getAchivemntQuery.StartDate.AddYears(-1).GetYearsStart();
                    //getCycleAchivemntQuery_IN.EndDate = getAchivemntQuery.EndDate.AddYears(-1).GetYearsEnd();
                }
                cyclelist = GetAchivemntQuery(getCycleAchivemntQuery_IN)
                    .Where(conModels)
                    .GroupBy(groupKeys)
                    .Select(selector)
                    .MergeTable()
                    .LeftJoinIF<Db_sys_user>(leftColKeys.Contains("UserId"), (it, user) => it.UserId == user.Id)
                    .Select<ExpandoObject>(nameSelector)
                    .MergeTable()
                    .OrderBy(orderList)
                    .ToList();

            }

            #endregion





            #region 构建table
            List<StaticticsTableColData> Cols = new List<StaticticsTableColData>();
            foreach (var leftItem in getAchivemntQuery.LeftCols)
            {
                Cols.Add(new StaticticsTableColData()
                {
                    Prop = leftItem.NameColKey,
                    Label = leftItem.Name,
                    AllowSort = leftItem.AllowSort ? 1 : 0
                }); ;
            }
            if (getAchivemntQuery.RightCols != null && getAchivemntQuery.RightCols.Count > 0)
            {
                if (getAchivemntQuery.StaCountCols.Count + getAchivemntQuery.StaValues.Count + getAchivemntQuery.StaFigures.Count == 1)
                {
                    //单值统计  只有一层不存在树形结构
                    var singleValueProp = "";
                    if (getAchivemntQuery.StaCountCols.Count == 1)
                    {
                        singleValueProp = getAchivemntQuery.StaCountCols[0].NameColKey;
                    }
                    else
                    {
                        singleValueProp = getAchivemntQuery.StaValues[0].GetDisplayName();
                    }
                    foreach (var yearOtherItem in yearOtherDicList)
                    {
                        Cols.Add(new StaticticsTableColData()
                        {
                            Prop = string.Join("_", new string[] { yearOtherItem.ToString(), singleValueProp }),
                            Label = yearOtherItem.ToString()
                        });
                    }
                }
                else
                {
                    //多值统计  存在树形结构
                    foreach (var yearOtherItem in yearOtherDicList)
                    {
                        var dateCol = new StaticticsTableColData()
                        {
                            Prop = yearOtherItem.ToString(),
                            Label = yearOtherItem.ToString(),
                            Children = new List<StaticticsTableColData>()
                        };
                        Cols.Add(dateCol);
                        foreach (var countItem in getAchivemntQuery.StaCountCols)
                        {
                            if (getAchivemntQuery.StaFigures.Count == 0)
                            {
                                //没有同比环比的话，不需要三层结构了
                                dateCol.Children.Add(new StaticticsTableColData()
                                {
                                    Prop = string.Join("_", new string[] { yearOtherItem.ToString(), countItem.NameColKey }),
                                    Label = countItem.Name,
                                    ParentProp = dateCol.Prop
                                });
                            }
                            else
                            {
                                //有同环比，做成三层结构
                                var countCol = new StaticticsTableColData()
                                {
                                    Prop = countItem.NameColKey,
                                    Label = countItem.Name,
                                    Children = new List<StaticticsTableColData>(),
                                    ParentProp = dateCol.Prop
                                };
                                dateCol.Children.Add(countCol);
                                countCol.Children.Add(new StaticticsTableColData()
                                {
                                    Prop = string.Join("_", new string[] { yearOtherItem.ToString(), countItem.NameColKey }),
                                    Label = "数值",
                                    ParentProp = countCol.Prop
                                });
                                //增加同环比 注：未处理按年统计不存在环比问题
                                foreach (var Figure in getAchivemntQuery.StaFigures)
                                {
                                    countCol.Children.Add(new StaticticsTableColData()
                                    {
                                        Prop = string.Join("_", new string[] { yearOtherItem.ToString(), countItem.NameColKey, Figure.GetDisplayName() }),
                                        //Label = string.Join("_", new string[] { countItem.Name, Figure.GetEnumDescription() }),
                                        Label = Figure.GetEnumDescription(),
                                        ParentProp = countCol.Prop
                                    });
                                }
                            }

                        }
                        foreach (var valueItem in getAchivemntQuery.StaValues)
                        {
                            if (getAchivemntQuery.StaFigures.Count == 0)
                            {
                                dateCol.Children.Add(new StaticticsTableColData()
                                {
                                    Prop = string.Join("_", new string[] { yearOtherItem.ToString(), valueItem.GetDisplayName() }),
                                    Label = valueItem.GetEnumDescription(),
                                    ParentProp = dateCol.Prop
                                });
                            }
                            else
                            {
                                //有同环比，做成三层结构
                                var vCol = new StaticticsTableColData()
                                {
                                    Prop = valueItem.GetDisplayName(),
                                    Label = valueItem.GetEnumDescription(),
                                    Children = new List<StaticticsTableColData>(),
                                    ParentProp = dateCol.Prop
                                };
                                dateCol.Children.Add(vCol);
                                vCol.Children.Add(new StaticticsTableColData()
                                {
                                    Prop = string.Join("_", new string[] { yearOtherItem.ToString(), valueItem.GetDisplayName() }),
                                    Label = valueItem.GetEnumDescription(),
                                    ParentProp = vCol.Prop
                                });
                                //增加同环比 注：未处理按年统计不存在环比问题
                                foreach (var Figure in getAchivemntQuery.StaFigures)
                                {
                                    vCol.Children.Add(new StaticticsTableColData()
                                    {
                                        Prop = string.Join("_", new string[] { yearOtherItem.ToString(), valueItem.GetDisplayName(), Figure.GetDisplayName() }),
                                        //Label = string.Join("_", new string[] { valueItem.GetEnumDescription(), Figure.GetEnumDescription() }),
                                        Label = Figure.GetEnumDescription(),
                                        ParentProp = vCol.Prop
                                    });
                                }
                            }

                        }
                    }
                }
            }
            result.Cols = Cols;
            #endregion

            #region 构建tableData
            var otherDateGroupKey = "";
            EnumStaDateSpan enumStaDateSpan = EnumStaDateSpan.Year;
            //需要构建与col里相对应的prop
            if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupMonth") != null)
            {
                enumStaDateSpan = EnumStaDateSpan.Month;
                otherDateGroupKey = "GroupMonth";
            }
            else if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupQuarter") != null)
            {
                enumStaDateSpan = EnumStaDateSpan.Quarter;
                otherDateGroupKey = "GroupQuarter";
            }

            var pageList = list.Cast<IDictionary<string, object>>()
                .GroupBy(p => p.GetDefaultValue(leftGroupSingleObj.NameColKey))
                .Select(g => new
                {
                    Key = g.Key,
                    SpanObj = g.ToList()
                })
                .ToList();
            foreach (var item in pageList)
            {
                dynamic resultItem = new ExpandoObject();
                IDictionary<string, object> resultItemDic = resultItem as IDictionary<string, object>;
                resultItemDic.Add(leftGroupSingleObj.NameColKey, item.Key);
                Dictionary<string, Dictionary<string, object>> chartDic = new Dictionary<string, Dictionary<string, object>>();
                foreach (var countItem in getAchivemntQuery.StaCountCols)
                {
                    chartDic.Add(countItem.NameColKey, new Dictionary<string, object>());
                }
                foreach (var valueItem in getAchivemntQuery.StaValues)
                {
                    chartDic.Add(valueItem.GetDisplayName(), new Dictionary<string, object>());
                }
                foreach (var span in yearOtherDicList)
                {
                    //准备环比同比的值
                    ExpandoObject figureObj = new ExpandoObject();
                    if (hasFigure)
                    {
                        var figureYear = span.Year - 1;
                        var figureOtherValue = span.OtherValue;
                        StaYearOtherDic figureDic = new StaYearOtherDic(figureYear, figureOtherValue, enumStaDateSpan);
                        figureObj = figurelist.Find(d =>
                          (d.TryGetDicValue<string>(leftGroupSingleObj.NameColKey, out string key) && ((item.Key == null && string.IsNullOrEmpty(key)) || (item.Key != null && key == item.Key.ToString())))
                            && (string.IsNullOrEmpty(otherDateGroupKey) || (d.TryGetDicValue<int>(otherDateGroupKey, out int other) && other == figureOtherValue))
                            && (d.TryGetDicValue<int>("GroupYear", out int year) && year == figureYear)
                        ) ?? new ExpandoObject();
                    }
                    ExpandoObject cycleObj = new ExpandoObject();
                    if (hasCycle)
                    {
                        StaYearOtherDic cycleDic = span.MappingTo<StaYearOtherDic>();
                        cycleDic.SubtractOnePeriod();
                        cycleObj = cyclelist.Find(d =>
                            (d.TryGetDicValue<string>(leftGroupSingleObj.NameColKey, out string key) && ((item.Key == null && string.IsNullOrEmpty(key)) || (item.Key != null && key == item.Key.ToString())))
                            && (string.IsNullOrEmpty(otherDateGroupKey) || (d.TryGetDicValue<int>(otherDateGroupKey, out int other) && other == cycleDic.OtherValue))
                            && (d.TryGetDicValue<int>("GroupYear", out int year) && year == cycleDic.Year)
                        ) ?? new ExpandoObject();
                    }
                    //找对应时间的值
                    var dicItem = item.SpanObj.Find(d =>
                    (string.IsNullOrEmpty(otherDateGroupKey) || (d.TryGetDicValue<int>(otherDateGroupKey, out int other) && other == span.OtherValue)) && (d.TryGetDicValue<int>("GroupYear", out int year) && year == span.Year)
                    );
                    if (dicItem == null)
                    {
                        foreach (var countItem in getAchivemntQuery.StaCountCols)
                        {
                            chartDic[countItem.NameColKey].Add(span.ToString(), 0);
                            resultItemDic.Add(string.Join("_", new string[] { span.ToString(), countItem.NameColKey }), 0);
                            InsertFigureValue(0, getAchivemntQuery, figureObj, cycleObj, countItem.NameColKey, span, ref resultItemDic);
                        }
                        foreach (var valueItem in getAchivemntQuery.StaValues)
                        {
                            chartDic[valueItem.GetDisplayName()].Add(span.ToString(), 0);
                            resultItemDic.Add(string.Join("_", new string[] { span.ToString(), valueItem.GetDisplayName() }), 0);
                            InsertFigureValue(0, getAchivemntQuery, figureObj, cycleObj, valueItem.GetDisplayName(), span, ref resultItemDic);
                        }
                    }
                    else
                    {
                        foreach (var countItem in getAchivemntQuery.StaCountCols)
                        {
                            chartDic[countItem.NameColKey].Add(span.ToString(), dicItem[countItem.NameColKey]);
                            resultItemDic.Add(string.Join("_", new string[] { span.ToString(), countItem.NameColKey }), dicItem[countItem.NameColKey]);
                            InsertFigureValue(Convert.ToDecimal(dicItem[countItem.NameColKey]), getAchivemntQuery, figureObj, cycleObj, countItem.NameColKey, span, ref resultItemDic);
                        }
                        foreach (var valueItem in getAchivemntQuery.StaValues)
                        {
                            chartDic[valueItem.GetDisplayName()].Add(span.ToString(), dicItem[valueItem.GetDisplayName()]);
                            resultItemDic.Add(string.Join("_", new string[] { span.ToString(), valueItem.GetDisplayName() }), dicItem[valueItem.GetDisplayName()]);
                            InsertFigureValue(Convert.ToDecimal(dicItem[valueItem.GetDisplayName()]), getAchivemntQuery, figureObj, cycleObj, valueItem.GetDisplayName(), span, ref resultItemDic);
                        }
                    }
                }
                foreach (var cd in chartDic)
                {
                    resultItemDic.Add(cd.Key, cd.Value);
                }
                dynamic dynamicWrapper = resultItemDic.ToDynamic();
                result.Rows.Add(dynamicWrapper);

            }
            #endregion
            return result;
        }
        /// <summary>
        /// 综合统计
        /// </summary>
        /// <param name="getAchivemntAboutStaticticsQuery_In"></param>
        /// <returns></returns>
        public StaticticsTableResult GetAchivemntAboutStaticticsQuery(GetAchivemntAboutStaticticsQuery getAchivemntAboutStaticticsQuery_In)
        {
            return DbOpe_crm_contract_receiptregister.Instance.GetAchivemntAboutStaticticsQuery(getAchivemntAboutStaticticsQuery_In);
        }
        /// <summary>
        /// 跟踪记录统计
        /// </summary>
        /// <param name="getTrackingRecordStaInQuery"></param>
        /// <returns></returns>
        public StaticticsTableResult GetTrackingRecordStaQuery(GetTrackingRecordStaInQuery getTrackingRecordStaInQuery)
        {
            return DbOpe_crm_trackingrecord.Instance.GetTrackingRecordStaQuery(getTrackingRecordStaInQuery);
        }
        /// <summary>
        /// 客户统计
        /// </summary>
        /// <param name="getCustomerNowQuery_IN"></param>
        /// <returns></returns>
        public StaticticsTableResult GetCustomerNowStatictics(GetCustomerNowQuery_IN getCustomerNowQuery_IN)
        {
            StaticticsTableResult result = new StaticticsTableResult();
            GetCustomerNowQuery getCustomerNowQuery = getCustomerNowQuery_IN.MappingTo<GetCustomerNowQuery>();

            #region 权限 (非销售组织的话就是所有组织下所有人)
            var user = DbOpe_sys_user.Instance.GetUserById(currentUser);
            List<string> showOrgIds = new List<string>();
            List<string> showUserIds = new List<string>();
            if (user.UserType != EnumUserType.Sales)
            {
                showOrgIds = DbOpe_crm_contract_receiptregister.Instance.GetAuthShowOrgIdsAndUserIds(getCustomerNowQuery_IN.SelectOrgId, currentUser, ref showUserIds);
            }
            else
            {
                showUserIds = new List<string>() { currentUser };
                showOrgIds = new List<string>() { user.OrganizationId };
            }
            getCustomerNowQuery.UserIds = showUserIds;
            getCustomerNowQuery.UserOrgIds = showOrgIds;
            #endregion
            #region 获取统计数据配置
            var dic = DbOpe_sys_dictionary_statictics.Instance.GetStaDictionaryByPageAndParentName(new Dictionary_Sta_In()
            {
                PageType = 2
            }, currentUser, true);
            var countColParent = dic.Find(d => d.Id.ToString() == getCustomerNowQuery_IN.StaCountCol);
            List<Dictionary_Sta_Out> countColDics = new List<Dictionary_Sta_Out>();
            FindLeafNodesRecursive(dic, countColParent.Id.ToString(), ref countColDics);
            getCustomerNowQuery.StaCountCols = new List<StatisticsCountItem>();
            foreach (var countCol in countColDics)
            {
                getCustomerNowQuery.StaCountCols.Add(new StatisticsCountItem()
                {
                    Name = countCol.Name,
                    Value = countCol.Value,
                    NameColKey = countCol.TableName,
                    ConditionColKey = countCol.ConditionKey,
                    AllowSort = countCol.AllowSort,
                    AllowTable = countCol.AllowTable,
                    Type = countCol.Type.ToInt(),
                    ConditionColTypeString = "int"
                });
            }

            getCustomerNowQuery.LeftCols = new List<StatisticsLeftColItem>();
            var leftColParent = dic.Find(d => d.Id.ToString() == getCustomerNowQuery_IN.LeftCol);
            List<Dictionary_Sta_Out> leftColDics = new List<Dictionary_Sta_Out>();
            FindLeafNodesRecursive(dic, leftColParent.Id.ToString(), ref leftColDics);
            foreach (var leftCol in leftColDics)
            {
                getCustomerNowQuery.LeftCols.Add(new StatisticsLeftColItem() { Name = leftCol.Name, Value = leftCol.Value, NameColKey = leftCol.TableName, AllowSort = leftCol.AllowSort });
            }
            //展开type=4的统计列
            getCustomerNowQuery.StaCountCols = ExpandStaCols(getCustomerNowQuery.StaCountCols);
            #endregion
            #region 排序
            string sortField = getCustomerNowQuery.SortField;
            if (string.IsNullOrEmpty(sortField) ||
                (
                    (getCustomerNowQuery.StaCountCols.Find(c => c.NameColKey == sortField) == null)
                    && (getCustomerNowQuery.LeftCols.Find(c => c.NameColKey == sortField) == null)
                )
            )
            {
                sortField = getCustomerNowQuery.StaCountCols[0].NameColKey;
            }
            List<OrderByModel> orderList = OrderByModel.Create(
                new OrderByModel() { FieldName = sortField, OrderByType = getCustomerNowQuery.IsDESC ? OrderByType.Desc : OrderByType.Asc }
            );
            #endregion
            string leftColKeys = getCustomerNowQuery.LeftCols.Select(c => c.Value).ToList().JoinToString();
            var selector = new List<SelectModel>();
            var nameSelector = new List<SelectModel>();
            foreach (var countItem in getCustomerNowQuery.StaCountCols)
            {
                if (!string.IsNullOrEmpty(countItem.ConditionColKey))
                {
                    var ifSignCustomerNearlyRelease = new ObjectFuncModel()
                    {
                        FuncName = "IIF",
                        Parameters = new List<object>{
                                new ObjectFuncModel(){
                                        FuncName = "Equals",
                                        Parameters = new List<object>{ countItem.ConditionColKey, "{" + countItem.ConditionColTypeString + "}:" + countItem.Value }
                                },
                                "{int}:1","{int}:0"
                            }
                    };
                    selector.Add(new SelectModel() { AsName = countItem.NameColKey, FiledName = ObjectFuncModel.Create("AggregateSum", ifSignCustomerNearlyRelease) });
                }
                else
                {
                    selector.Add(new SelectModel() { AsName = countItem.NameColKey, FiledName = ObjectFuncModel.Create("AggregateDistinctCount", countItem.Value) });
                }
                nameSelector.Add(new SelectModel() { FiledName = countItem.NameColKey });

            }
            foreach (var leftItem in getCustomerNowQuery.LeftCols)
            {
                selector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = leftItem.Value });
                nameSelector.Add(new SelectModel() { FiledName = leftItem.NameColKey });
            }
            int total = 0;
            var list = GetCustomerNowQuery(getCustomerNowQuery)
                .GroupBy(leftColKeys)
                .Select(selector)
                .MergeTable()
                .Select<dynamic>(nameSelector)
                .MergeTable()
                .OrderBy(orderList)
                .ToPageList(getCustomerNowQuery.PageNumber, getCustomerNowQuery.PageSize, ref total);


            #region 构建table
            List<StaticticsTableColData> Cols = new List<StaticticsTableColData>();
            foreach (var leftItem in getCustomerNowQuery.LeftCols)
            {
                Cols.Add(new StaticticsTableColData()
                {
                    Prop = leftItem.NameColKey,
                    Label = leftItem.Name,
                    AllowSort = leftItem.AllowSort ? 1 : 0,
                    AllowTable = 0
                });
            }
            var totalCol = getCustomerNowQuery.StaCountCols.Find(c => c.NameColKey == "CustomerId");
            if (totalCol != null)
            {
                Cols.Add(new StaticticsTableColData()
                {
                    Prop = totalCol.NameColKey,
                    Label = totalCol.Name,
                    AllowSort = totalCol.AllowSort ? 1 : 0,
                    AllowTable = totalCol.AllowTable ? 1 : 0
                });
            }
            foreach (var countItem in getCustomerNowQuery.StaCountCols)
            {
                if (countItem.NameColKey != totalCol.NameColKey)
                {
                    Cols.Add(new StaticticsTableColData()
                    {
                        Prop = countItem.NameColKey,
                        Label = countItem.Name,
                        AllowSort = countItem.AllowSort ? 1 : 0,
                        AllowTable = countItem.AllowTable ? 1 : 0
                    });
                }
            }
            result.Cols = Cols;
            #endregion
            #region 构建tableData
            result.Rows = list;
            result.total = total;
            #endregion
            return result;
        }

        /// <summary>
        /// 首页用户 客户到期情况
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public CustomerNowStatistics_OUT CustomerNowStatictics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            GetCustomerNowQuery_IN getCustomerNowQuery = new GetCustomerNowQuery_IN()
            {
                SelectOrgId = userIndexStatistics_IN.OrgId,
                StaCountCol = "e012ffef-6078-11ef-8865-30d042e24322",
                LeftCol = "d0fe551a-606c-11ef-8865-30d042e24322",
                PageNumber = 1,
                PageSize = 1000,
                IsDESC = true,
                SortField = "SignCustomerNearlyRelease"
            };
            StaticticsTableResult r = GetCustomerNowStatictics(getCustomerNowQuery);
            CustomerNowStatistics_OUT customerNowStatistics_OUT = new CustomerNowStatistics_OUT();
            customerNowStatistics_OUT.List = new List<CustomerNowStatisticsItem_OUT>();
            r.Rows.ToList().ForEach(r =>
            {
                customerNowStatistics_OUT.List.Add(new CustomerNowStatisticsItem_OUT()
                {
                    Name = SafeConvert<string>(r?.Name),
                    SignCustomerNearlyReleaseNum = SafeConvert<int>(r?.SignCustomerNearlyRelease),
                    SaveCustomerNearlyReleaseNum = SafeConvert<int>(r?.SaveCustomerNearlyRelease),
                    ServiceNearlyEndNum = SafeConvert<int>(r?.ServiceNearlyEnd)
                });
            });
            return customerNowStatistics_OUT;
        }

        private void InsertFigureValue(decimal oValue, GetAchivementQuery_IN getAchivemntQuery, ExpandoObject figureObj, ExpandoObject cycleObj, string itemKey, StaYearOtherDic span, ref IDictionary<string, object> resultItemDic)
        {
            foreach (var figureItem in getAchivemntQuery.StaFigures)
            {
                decimal v = 0;
                var fo = figureObj as IDictionary<string, object>;
                var co = cycleObj as IDictionary<string, object>;
                decimal cv = 0;
                decimal fv = 0;
                decimal fp = 0;
                decimal cp = 0;
                switch (figureItem)
                {
                    case EnumFigureValue.Figure:
                        if (!fo.TryGetDicValue<decimal>(itemKey, out v))
                        {
                            v = 0;
                        }
                        fv = oValue - v;
                        resultItemDic.Add(string.Join("_", new string[] { span.ToString(), itemKey, figureItem.GetDisplayName() }), fv);
                        break;
                    case EnumFigureValue.FigurePercent:
                        if (!fo.TryGetDicValue<decimal>(itemKey, out v))
                        {
                            v = 0;
                        }
                        fv = oValue - v;
                        fp = v == 0 ? 0 : (Math.Round((decimal)fv * 100 / v, 2));
                        resultItemDic.Add(string.Join("_", new string[] { span.ToString(), itemKey, figureItem.GetDisplayName() }), fp);
                        break;
                    case EnumFigureValue.Cycle:
                        if (!co.TryGetDicValue<decimal>(itemKey, out v))
                        {
                            v = 0;
                        }

                        cv = oValue - v;
                        resultItemDic.Add(string.Join("_", new string[] { span.ToString(), itemKey, figureItem.GetDisplayName() }), cv);
                        break;
                    case EnumFigureValue.CyclePercent:
                        if (!co.TryGetDicValue<decimal>(itemKey, out v))
                        {
                            v = 0;
                        }
                        cv = oValue - v;
                        cp = v == 0 ? 0 : (Math.Round((decimal)cv * 100 / v, 2));
                        resultItemDic.Add(string.Join("_", new string[] { span.ToString(), itemKey, figureItem.GetDisplayName() }), cp);
                        break;
                }
            }
        }

        /// <summary>
        /// 安全转换方法，考虑null和类型转换
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        private T SafeConvert<T>(object value)
        {
            if (value == null)
            {
                // 对于可空类型，返回null；对于非可空类型，你可能需要抛出一个异常或返回一个默认值
                return default(T); // 如果T是可空的，这将返回null；如果T不是可空的，则返回类型的默认值
            }

            // 尝试转换
            try
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (InvalidCastException)
            {
                // 如果转换失败（虽然这里不太可能，因为Convert.ChangeType通常更宽容），你可能想抛出一个更具体的异常或返回默认值
                throw new InvalidOperationException($"Cannot convert {value} to type {typeof(T).Name}");
            }
        }
        /// <summary>
        /// 获取统计数据字典信息
        /// </summary>
        /// <param name="dictionary_Sta_In"></param>
        /// <returns></returns>
        public List<Dictionary_Sta_Out> GetStaDictionaryByPageAndParentName(Dictionary_Sta_In dictionary_Sta_In)
        {
            if (dictionary_Sta_In == null)
            {
                throw new ApiException("参数不可为空");
            }
            return DbOpe_sys_dictionary_statictics.Instance.GetStaDictionaryByPageAndParentName(dictionary_Sta_In, currentUser);
        }
        private List<StatisticsCountItem> ExpandStaCols(List<StatisticsCountItem> countItems)
        {
            List<StatisticsCountItem> result = new List<StatisticsCountItem>();
            foreach (var countItem in countItems)
            {
                if (countItem == null || countItem.Type != 4)
                {
                    result.Add(countItem.MappingTo<StatisticsCountItem>());
                }
                else
                {
                    //int index = 0;
                    //需要根据数据展开统计的列
                    switch (countItem.Value)
                    {
                        case "CustomerIndustryId":
                            var industries = LocalCache.LC_MainProduct.CustomerIndustryCache;
                            foreach (var ind in industries)
                            {
                                result.Add(new StatisticsCountItem()
                                {
                                    Name = ind.Name,
                                    Value = ind.Id,
                                    NameColKey = countItem.Value + "_" + ind.Id,
                                    ConditionColKey = countItem.Value,
                                    AllowSort = true,
                                    AllowTable = true,
                                    Type = 3,
                                    ConditionColTypeString = "string"
                                });
                            }
                            break;
                        case "Province":
                            var chinaProvinces = LocalCache.LC_Address.ProvinceCache.Where(p => p.CountryID == 337 && p.Extend != true).ToList();
                            foreach (var cp in chinaProvinces)
                            {
                                result.Add(new StatisticsCountItem()
                                {
                                    Name = cp.Name,
                                    Value = cp.Id.ToString(),
                                    NameColKey = countItem.Value + "_" + cp.Id,
                                    ConditionColKey = countItem.Value,
                                    AllowSort = true,
                                    AllowTable = true,
                                    Type = 3,
                                    ConditionColTypeString = "int"
                                });
                            }
                            break;
                    }
                }

            }
            return result;

        }
        #endregion


        #region 改版后首页相关方法

        /// <summary>
        /// 用户主页相关统计值
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public UserIndexStatisticsEx_OUT GetUserIndexStatisticsEx(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            UserIndexStatisticsEx_OUT userIndexStatistics_OUT = new UserIndexStatisticsEx_OUT();
            SalesDataStatisticsParams quarterParams = FormatSalesDataStatisticsParams(userIndexStatistics_IN, currentUser);
            var totalParams_IN = userIndexStatistics_IN.MappingTo<UserIndexStatistics_IN>();
            totalParams_IN.EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.Total;
            SalesDataStatisticsParams totalParams = FormatSalesDataStatisticsParams(totalParams_IN, currentUser);
            #region 跟进客户
            //阶段跟进
            var GetTrackingCustomer_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetTrackingCustomerQuery(quarterParams).Count();
            userIndexStatistics_OUT.Span_TrackingCustomer = new UserIndexStatisticsExItem()
            {
                ShowNum = GetTrackingCustomer_Quarter.ToString(),
                RemindToPath = "/trackRecord/index?EnumIndexRedirectType=" + EnumIndexRedirectType.Span_TrackingCustomer
                + "&EnumStatisticsDateSpanSelect=" + userIndexStatistics_IN.EnumStatisticsDateSpanSelect
                + "&SelectYear=" + userIndexStatistics_IN.SelectYear,
                RemindFormName = ""
            };
            #endregion
            #region 签约客户
            //阶段新签约客户
            var GetSignData_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetSignDataQuery(quarterParams).Select(d => d.CustomerId).Count();
            //阶段签约客户数
            userIndexStatistics_OUT.Span_SignCustomer = new UserIndexStatisticsExItem()
            {
                ShowNum = GetSignData_Quarter.ToString(),
                RemindToPath = "/customer/manage?EnumIndexRedirectType=" + EnumIndexRedirectType.Span_SignCustomer
                + "&EnumStatisticsDateSpanSelect=" + userIndexStatistics_IN.EnumStatisticsDateSpanSelect
                + "&SelectYear=" + userIndexStatistics_IN.SelectYear
                ,
                RemindFormName = "私有池"
            };
            #endregion
            #region 业绩
            //阶段业绩
            var GetAchivementData_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(quarterParams).Sum(d => d.EffectivePerformance);
            //累计业绩
            var GetAchivementData_Total = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(totalParams).Sum(d => d.EffectivePerformance);
            userIndexStatistics_OUT.Span_SalesAchivement = new UserIndexStatisticsExItem()
            {
                ShowNum = (decimal.Round(Convert.ToDecimal(GetAchivementData_Quarter), 0)).ToString(),
                RemindToPath = "/contract/performance?EnumIndexRedirectType=" + EnumIndexRedirectType.Span_SalesAchivement
                + "&EnumStatisticsDateSpanSelect=" + userIndexStatistics_IN.EnumStatisticsDateSpanSelect
                + "&SelectYear=" + userIndexStatistics_IN.SelectYear
                ,
                RemindFormName = ""
            };
            userIndexStatistics_OUT.Total_SalesAchivement = new UserIndexStatisticsExItem()
            {
                ShowNum = (decimal.Round(Convert.ToDecimal(GetAchivementData_Total), 0)).ToString(),
                RemindToPath = "/contract/performance?EnumIndexRedirectType=" + EnumIndexRedirectType.Total_SalesAchivement,
                RemindFormName = ""
            };
            #endregion
            #region 签约合同
            //阶段签约合同
            var GetSignContractData_Quarter = DbOpe_crm_contract.Instance.GetSignContractQuery(quarterParams).Count();
            //累计签约合同
            var GetSignContractData_Total = DbOpe_crm_contract.Instance.GetSignContractQuery(totalParams).Count();
            userIndexStatistics_OUT.Span_SignContract = new UserIndexStatisticsExItem()
            {
                ShowNum = (GetSignContractData_Quarter).ToString(),
                RemindToPath = "/contract/sales?EnumIndexRedirectType=" + EnumIndexRedirectType.Span_SignContract
                + "&EnumStatisticsDateSpanSelect=" + userIndexStatistics_IN.EnumStatisticsDateSpanSelect
                + "&SelectYear=" + userIndexStatistics_IN.SelectYear
                ,
                RemindFormName = "合同列表"
            };
            userIndexStatistics_OUT.Total_SignContract = new UserIndexStatisticsExItem()
            {
                ShowNum = (GetSignContractData_Total).ToString(),
                RemindToPath = "/contract/sales?EnumIndexRedirectType=" + EnumIndexRedirectType.Total_SignContract,
                RemindFormName = "合同列表"
            };
            #endregion
            #region 当前客户
            var getSaveCustomerData_Now = DbOpe_crm_customer_privatepool.Instance.GetPrivateSaveCustomerQuery(currentUser).MergeTable().Select(d => d.CustomerId).Count();
            var getRecieveCustomerData_Now = DbOpe_crm_customer_privatepool.Instance.GetRecievePrivateCustomerNum(currentUser);
            userIndexStatistics_OUT.Now_SaveCustomer = new UserIndexStatisticsExItem()
            {
                ShowNum = (getSaveCustomerData_Now).ToString(),
                RemindToPath = "/customer/manage?EnumIndexRedirectType=" + EnumIndexRedirectType.Now_SaveCustomer,
                RemindFormName = "私有池"
            };
            userIndexStatistics_OUT.Now_RecieveCustomer = new UserIndexStatisticsExItem()
            {
                ShowNum = (getRecieveCustomerData_Now).ToString(),
                RemindToPath = "/customer/manage?EnumIndexRedirectType=" + EnumIndexRedirectType.Now_RecieveCustomer,
                RemindFormName = "私有池"
            };
            #endregion
            return userIndexStatistics_OUT;
        }

        #endregion

        #region 首页
        /// <summary>
        /// 跟踪排行
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public TrackingRecordRankStatistics_OUT TrackingRecordRankStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            SalesRankStatisticsParmas salesRankStatisticsParmas = FormatSalesRankStatisticsParmas(userIndexStatistics_IN);
            //进行统计
            var rankData = DbOpe_crm_trackingrecord.Instance.TrackingRecordRankStatistics(salesRankStatisticsParmas);
            return rankData;
        }
        /// <summary>
        /// 销售漏斗  11.16 这个统计组织的 不是个人的
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public SalesFunnelStatistics_OUT SalesFunnelStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            SalesDataStatisticsParams salesDataStatisticsParams = FormatSalesDataStatisticsParams(userIndexStatistics_IN, "");
            //进行统计
            var rankData = DbOpe_crm_trackingrecord.Instance.SalesFunnelStatistics(salesDataStatisticsParams);
            return rankData;
        }
        /// <summary>
        /// 首页-销售目标统计(个人)（目标是所选组织的 12.22 管理员的话 业绩要看组织的而不是个人的）
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public SalesTargetStatistics_OUT SalesTargetStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            bool superRole = BLL_Role.Instance.CheckSuperUser();
            bool managerRole = BLL_Role.Instance.CheckSalesManagerUser();
            SalesDataStatisticsParams salesDataStatistics;
            if (!superRole && !managerRole)
            {
                salesDataStatistics = FormatSalesDataStatisticsParams(userIndexStatistics_IN, currentUser);
            }
            else
            {
                salesDataStatistics = FormatSalesDataStatisticsParams(userIndexStatistics_IN, "");
            }
            //进行统计
            SalesTargetStatistics_OUT salesTargetStatistics_OUT = new SalesTargetStatistics_OUT();
            //var contractData = DbOpe_crm_contract.Instance.SalesTargetStatistics(salesDataStatistics);
            var achieveData = DbOpe_crm_contract_receiptregister.Instance.SalesTargetStatistics(salesDataStatistics);
            //12.21 获取组织目标（目标也要根据时间段进行累加）
            //12.22 处理一下老板统计时的目标计算
            string targetOrg = "";
            if (string.IsNullOrEmpty(userIndexStatistics_IN.OrgId))
            {
                if (!superRole && !managerRole)
                {
                    //管理者 未选择查询的目标机构时，默认要取全公司
                    targetOrg = Guid.Empty.ToString();
                }
                else
                {
                    //非管理者 查自己的组织
                    targetOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
                }
            }
            else
            {
                targetOrg = userIndexStatistics_IN.OrgId;
            }
            var years = achieveData.Select(d => d.Key.Year).Distinct().ToList();
            List<Db_sys_performance_objectives> performanceList = new List<Db_sys_performance_objectives>();
            if (targetOrg == Guid.Empty.ToString())
            {
                performanceList = DbOpe_sys_performance_objectives.Instance.GetDataList(d =>
                            SqlFunc.ContainsArray(years, d.ObjectivesYear)
                            && (d.WaitEffec == null || d.WaitEffec == 0)
                );
            }
            else
            {
                performanceList = DbOpe_sys_performance_objectives.Instance.GetDataList(d =>
                            SqlFunc.ContainsArray(years, d.ObjectivesYear)
                            && (d.WaitEffec == null || d.WaitEffec == 0)
                            && d.OrgId == targetOrg
                );
            }
            salesTargetStatistics_OUT.TargetStatisticsItems = new List<SalesTargetStatisticsItem_OUT>();
            foreach (StatisticsDoubleItem<DateTime> ssi in achieveData)
            {
                salesTargetStatistics_OUT.TargetStatisticsItems.Add(new SalesTargetStatisticsItem_OUT()
                {
                    Name = ssi.Name,
                    //ContractAmountTotal = contractData.Find(d => d.Name == ssi.Name).Value == null ? 0 : contractData.Find(d => d.Name == ssi.Name).Value.Value,
                    PerformanceObjectiveTotal = decimal.Round(DbOpe_sys_performance_objectives.Instance.GetMonthObjFromRowData(ssi.Key, performanceList, true, achieveData[0].Key) * 10000, 2),
                    AchievementTotal = decimal.Round(ssi.Value == null ? 0 : ssi.Value.Value, 2),
                    EffectiveAchievementTotal = decimal.Round(ssi.SecondValue == null ? 0 : ssi.SecondValue.Value, 2),
                });
            }
            return salesTargetStatistics_OUT;
        }
        /// <summary>
        /// 首页-客户统计 11.16 这个统计组织的 不是个人的
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public CustomerStatistics_OUT CustomerStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            CustomerStatistics_OUT customerStatistics_OUT = new CustomerStatistics_OUT();
            SalesDataStatisticsParams salesDataStatisticsParams = FormatSalesDataStatisticsParams(userIndexStatistics_IN, "");
            customerStatistics_OUT.NewCustomer = DbOpe_crm_customer_privatepool.Instance.CustomerStatistics(salesDataStatisticsParams);
            customerStatistics_OUT.SignCustomer = DbOpe_crm_contract.Instance.CustomerStatistics(salesDataStatisticsParams);
            return customerStatistics_OUT;
        }
        /// <summary>
        /// （组织）首页-业绩排行
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public SalesRankStatistics_OUT SalesOrgRankStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            SalesRankStatisticsParmas salesRankStatisticsParmas = FormatSalesRankStatisticsParmas(userIndexStatistics_IN);
            //进行统计
            var rankData = DbOpe_crm_contract_receiptregister.Instance.SalesOrgRankStatistics(salesRankStatisticsParmas);
            return rankData;
        }
        /// <summary>
        /// 首页-全公司业绩排行（新）
        /// </summary>
        /// <param name="userIndexCompanyRankStatistics_IN"></param>
        /// <returns></returns>
        public CompanyRankStatistics_OUT CompanyRankStatistics(UserIndexCompanyRankStatistics_IN userIndexCompanyRankStatistics_IN)
        {
            CompanyRankStatisticsParmas companyRankStatisticsParmas = FormatCompanyRankStatisticsParmas(userIndexCompanyRankStatistics_IN);
            //进行统计
            var rankData = DbOpe_crm_contract_receiptregister.Instance.CompanyRankStatisticsEx(companyRankStatisticsParmas);
            return rankData;
        }
        /// <summary>
        /// （个人）首页-业绩排行
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public SalesRankStatistics_OUT SalesUserRankStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            SalesRankStatisticsParmas salesRankStatisticsParmas = FormatSalesRankStatisticsParmas(userIndexStatistics_IN);
            //进行统计
            var rankData = DbOpe_crm_contract_receiptregister.Instance.SalesUserRankStatistics(salesRankStatisticsParmas);
            return rankData;
        }
        /// <summary>
        /// 首页-销售业绩
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public SalesDataStatistics_OUT SalesDateStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            SalesDataStatisticsParams salesDataStatistics = FormatSalesDataStatisticsParams(userIndexStatistics_IN);
            //进行统计 
            SalesDataStatistics_OUT salesDataStatistics_OUT = new SalesDataStatistics_OUT();
            //验证超级权限
            bool superRole = BLL_Role.Instance.CheckSuperUser();
            bool managerRole = BLL_Role.Instance.CheckSalesManagerUser();
            if (!superRole && !managerRole)
            {
                salesDataStatistics_OUT.ShowUser = true;
            }
            else
            {
                //管理员权限不要显示个人业绩统计
                salesDataStatistics_OUT.ShowUser = false;
            }
            //个人当前统计 （环比要向前多统计一个月）
            SalesDataStatisticsParams userTempCon = salesDataStatistics.MappingTo<SalesDataStatisticsParams>();
            userTempCon.UserId = currentUser;
            userTempCon.OrgRegimentId = new List<string>();
            userTempCon.OrgBrigadeId = new List<string>();
            userTempCon.OrgDivisionId = new List<string>();
            var userAchieveData = DbOpe_crm_contract_receiptregister.Instance.SalesDateStatistics(userTempCon, EnumDateStatisticsType.HuanBi);
            //团队当前统计 （环比要向前多统计一个月）
            var orgAchieveData = DbOpe_crm_contract_receiptregister.Instance.SalesDateStatistics(salesDataStatistics, EnumDateStatisticsType.HuanBi);
            //个人同比
            var usertongbiAchieveData = DbOpe_crm_contract_receiptregister.Instance.SalesDateStatistics(userTempCon, EnumDateStatisticsType.TongBi);
            //团队同比统计
            var orgtongbiAchieveData = DbOpe_crm_contract_receiptregister.Instance.SalesDateStatistics(salesDataStatistics, EnumDateStatisticsType.TongBi);
            //统计结果
            salesDataStatistics_OUT.StatisticsItems = new List<SalesDataStatisticsItem_OUT>();
            //var first = userAchieveData.First();
            var compare = userAchieveData.First();
            var orgCompare = orgAchieveData.First();
            foreach (StatisticsSimpleItem<DateTime> ssi in userAchieveData)
            {
                if (ssi.Index >= 0)
                {
                    var userTongbi = usertongbiAchieveData.Find(d => d.Index == ssi.Index)?.Value;
                    var userHuanbi = compare?.Value;
                    var orgData = orgAchieveData.Find(d => d.Index == ssi.Index)?.Value;
                    var orgTongbi = orgtongbiAchieveData.Find(d => d.Index == ssi.Index)?.Value;
                    var orgHuanbi = orgCompare?.Value;
                    salesDataStatistics_OUT.StatisticsItems.Add(new SalesDataStatisticsItem_OUT()
                    {
                        Index = ssi.Index,
                        Name = ssi.Name,
                        UserAchievementTotal = ssi.Value == null ? 0 : ssi.Value.Value,
                        UserAchievementTotal_TongBi = userTongbi == null ? 0 : userTongbi.Value,
                        UserAchievementTotal_HuanBi = userHuanbi == null ? 0 : userHuanbi.Value,
                        OrgAchievementTotal = orgData == null ? 0 : orgData.Value,
                        OrgAchievementTotal_TongBi = orgTongbi == null ? 0 : orgTongbi.Value,
                        OrgAchievementTotal_HuanBi = orgHuanbi == null ? 0 : orgHuanbi.Value,
                    });
                    compare = ssi;
                    orgCompare = orgAchieveData.Find(d => d.Index == ssi.Index);
                }
            }
            return salesDataStatistics_OUT;
        }

        /// <summary>
        /// 用户主页相关统计值
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <returns></returns>
        public UserIndexStatistics_OUT GetUserIndexStatistics(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            UserIndexStatistics_OUT userIndexStatistics_OUT = new UserIndexStatistics_OUT();
            SalesDataStatisticsParams quarterParams = FormatSalesDataStatisticsParams(userIndexStatistics_IN, currentUser);
            var totalParams_IN = userIndexStatistics_IN.MappingTo<UserIndexStatistics_IN>();
            totalParams_IN.EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.Total;
            SalesDataStatisticsParams totalParams = FormatSalesDataStatisticsParams(totalParams_IN, currentUser);
            #region 新增客户
            //季度新增过的
            var GetGetCustomerCount_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetGetCustomerQuery(quarterParams).Count();
            //季度释放的
            var GetReleaseCustomerCount_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetReleaseCustomerQuery(quarterParams).Count();
            //累计新增过的
            var GetGetCustomerCount_Total = DbOpe_crm_contract_receiptregister.Instance.GetGetCustomerQuery(totalParams).Count();
            //累计释放的
            var GetReleaseCustomerCount_Total = DbOpe_crm_contract_receiptregister.Instance.GetReleaseCustomerQuery(totalParams).Count();
            // 新增 = 新增过的 - 释放的
            userIndexStatistics_OUT.Span_SaveCustomerNum = GetGetCustomerCount_Quarter - GetReleaseCustomerCount_Quarter;
            userIndexStatistics_OUT.Total_SaveCustomerNum = GetGetCustomerCount_Total - GetReleaseCustomerCount_Total;
            #endregion
            #region 跟进客户
            //季度跟进
            var GetTrackingCustomer_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetTrackingCustomerQuery(quarterParams).Count();
            //累计跟进
            var GetTrackingCustomer_Total = DbOpe_crm_contract_receiptregister.Instance.GetTrackingCustomerQuery(totalParams).Count();
            userIndexStatistics_OUT.Span_TrackingCustomerNum = GetTrackingCustomer_Quarter;
            userIndexStatistics_OUT.Total_TrackingCustomerNum = GetTrackingCustomer_Total;
            #endregion
            #region 签约客户
            //季度签约
            var GetSignData_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetSignDataQuery(quarterParams).Select(d => d.CustomerId).Count();
            //累计签约
            var GetSignData_Total = DbOpe_crm_contract_receiptregister.Instance.GetSignDataQuery(totalParams).Select(d => d.CustomerId).Count();
            userIndexStatistics_OUT.Span_SignCustomerNum = GetSignData_Quarter;
            userIndexStatistics_OUT.Total_SignCustomerNum = GetSignData_Total;
            #endregion
            #region 销售业绩
            //季度签约
            var GetAchivementData_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(quarterParams).Sum(d => d.EffectivePerformance);
            //累计签约
            var GetAchivementData_Total = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(totalParams).Sum(d => d.EffectivePerformance);
            userIndexStatistics_OUT.Span_SalesAchivement = decimal.Round(Convert.ToDecimal(GetAchivementData_Quarter), 2);
            userIndexStatistics_OUT.Total_SalesAchivement = decimal.Round(Convert.ToDecimal(GetAchivementData_Total), 2);
            #endregion
            #region 待到账
            //SalesDataStatisticsParams monthParams = TransUserIndexStatisticsDate(new UserIndexStatistics_IN()
            //{
            //    EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.CurrentMonth
            //}).MappingTo<SalesDataStatisticsParams>();
            //monthParams.UserId = currentUser;
            //SalesDataStatisticsParams lastMonthParams = new SalesDataStatisticsParams()
            //{
            //    DateStart = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01")).AddMonths(-1),
            //    DateEnd = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01")).AddDays(-1)
            //};
            //lastMonthParams.UserId = currentUser;
            //合同金额
            var GetContractAmountData_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetContractAmountDataQuery(quarterParams).Sum(d => d.ContractAmount);
            var GetContractAmountData_Total = DbOpe_crm_contract_receiptregister.Instance.GetContractAmountDataQuery(totalParams).Sum(d => d.ContractAmount);
            //var contractTest = DbOpe_crm_contract_receiptregister.Instance.GetContractAmountDataQuery(quarterParams).ToList();
            //到账
            var GetArrivalAmountDataQuery_Quarter = DbOpe_crm_contract_receiptregister.Instance.GetArrivalAmountDataQuery(quarterParams).Sum(d => d.ArrivalAmount);
            var GetArrivalAmountDataQuery_Total = DbOpe_crm_contract_receiptregister.Instance.GetArrivalAmountDataQuery(totalParams).Sum(d => d.ArrivalAmount);
            //var arrivalTest = DbOpe_crm_contract_receiptregister.Instance.GetArrivalAmountDataQuery(quarterParams).ToList();
            userIndexStatistics_OUT.Span_ToBeReceivedAmount = decimal.Round(Convert.ToDecimal(GetContractAmountData_Quarter) - Convert.ToDecimal(GetArrivalAmountDataQuery_Quarter), 2);
            userIndexStatistics_OUT.Total_ToBeReceivedAmount = decimal.Round(Convert.ToDecimal(GetContractAmountData_Total) - Convert.ToDecimal(GetArrivalAmountDataQuery_Total), 2);
            #endregion
            return userIndexStatistics_OUT;
        }
        /// <summary>
        /// 注意：这个userID只要填写了，就不会加组织筛选条件了，就默认按用户去统计，跟组织无关
        /// userIndexStatistics_IN里的orgid如果没写，默认是取currentUser的org的；跟userid无关
        /// </summary>
        /// <param name="userIndexStatistics_IN"></param>
        /// <param name="singleUserStatisticId"></param>
        /// <param name="containSubOrg"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public SalesDataStatisticsParams FormatSalesDataStatisticsParams(UserIndexStatistics_IN userIndexStatistics_IN, string singleUserStatisticId = "", bool containSubOrg = true)
        {
            SalesDataStatisticsParams p = TransUserIndexStatisticsDate(userIndexStatistics_IN).MappingTo<SalesDataStatisticsParams>(); ;
            //客户来源
            p.CustomerDataSources = userIndexStatistics_IN.CustomerDataSources;
            if (!string.IsNullOrEmpty(singleUserStatisticId))
            {
                //用户
                p.UserId = singleUserStatisticId;
            }
            else
            {
                p.OrgRegimentId = new List<string>() { };
                p.OrgBrigadeId = new List<string>() { };
                p.OrgDivisionId = new List<string>() { };
                //验证超级权限
                bool superRole = BLL_Role.Instance.CheckSuperUser();
                bool managerRole = BLL_Role.Instance.CheckSalesManagerUser();
                List<SysOrganizationTree> orgObjList = new List<SysOrganizationTree>();
                Db_sys_organization orgInfo = null;
                if (!superRole && !managerRole)
                {
                    //组织机构
                    var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
                    orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg);
                    orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
                    //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
                    var userOrgList = orgObjList.Select(o => o.Id).ToList();
                    userOrgList.Add(userOrg);
                    //判断参数里面的组织有没有权限
                    if (!StringUtil.IsNullOrEmpty(userIndexStatistics_IN.OrgId))
                    {
                        if (!userOrgList.Contains(userIndexStatistics_IN.OrgId))
                        {
                            throw new ApiException("没有组织权限");
                        }
                        orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userIndexStatistics_IN.OrgId);
                        orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(orgInfo.Id);
                    }
                }
                else
                {
                    if (!StringUtil.IsNullOrEmpty(userIndexStatistics_IN.OrgId))
                    {
                        orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userIndexStatistics_IN.OrgId);
                        orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(orgInfo.Id);
                    }
                }

                if (!containSubOrg && orgObjList.Count > 0)
                {
                    foreach (var subOrg in orgObjList)
                    {
                        switch (subOrg.OrgType)
                        {
                            case EnumOrgType.Squadron:
                                p.OrgRegimentId.Add(subOrg.Id);
                                break;
                            case EnumOrgType.Battalion:
                                p.OrgBrigadeId.Add(subOrg.Id);
                                break;
                            case EnumOrgType.BattleTeam:
                                p.OrgDivisionId.Add(subOrg.Id);
                                break;
                        }
                    }
                }
                if (orgInfo != null)
                {
                    switch (orgInfo.OrgType)
                    {
                        case EnumOrgType.Squadron:
                            p.OrgRegimentId.Add(orgInfo.Id);
                            break;
                        case EnumOrgType.Battalion:
                            p.OrgBrigadeId.Add(orgInfo.Id);
                            break;
                        case EnumOrgType.BattleTeam:
                            p.OrgDivisionId.Add(orgInfo.Id);
                            break;
                    }
                }

            }

            return p;
        }
        public DateSpanParams TransUserIndexStatisticsDate(UserIndexStatisticsDate userIndexStatistics_IN)
        {
            DateSpanParams p = new DateSpanParams();
            DateTime dt = DateTime.Now.GetDaysStart();
            if (userIndexStatistics_IN.EnumStatisticsDateSpanSelect == EnumStatisticsDateSpanSelect.Total)
            {
                p.DateStart = null;
                p.DateEnd = null;
            }
            else if (userIndexStatistics_IN.EnumStatisticsDateSpanSelect == EnumStatisticsDateSpanSelect.CurrentMonth)
            {
                DateTime startMonth = dt.AddDays(1 - dt.Day).GetDaysStart();  //本月月初
                DateTime endMonth = startMonth.AddMonths(1).AddDays(-1).GetDaysEnd();  //本月月末
                p.DateStart = startMonth;
                p.DateEnd = endMonth;
            }
            else if (userIndexStatistics_IN.EnumStatisticsDateSpanSelect == EnumStatisticsDateSpanSelect.FirstHalfYear)
            {
                DateTime startYear = DateTime.Parse(dt.ToString("yyyy-01-01")).GetDaysStart();//本年度第一天
                DateTime midYear = startYear.AddMonths(6).AddDays(-1).GetDaysEnd();  //上半年末
                p.DateStart = startYear;
                p.DateEnd = midYear;
            }
            else if (userIndexStatistics_IN.EnumStatisticsDateSpanSelect == EnumStatisticsDateSpanSelect.SecondHalfYear)
            {
                DateTime midYear = DateTime.Parse(dt.ToString("yyyy-07-01")).GetDaysStart();//下半年初
                DateTime endYear = DateTime.Parse(dt.ToString("yyyy-01-01")).AddYears(1).AddDays(-1).GetDaysEnd(); //年末
                p.DateStart = midYear;
                p.DateEnd = endYear;
            }
            else if (userIndexStatistics_IN.EnumStatisticsDateSpanSelect == EnumStatisticsDateSpanSelect.CurrentYear)
            {
                DateTime startYear = DateTime.Parse(dt.ToString("yyyy-01-01")).GetDaysStart();//本年度第一天
                DateTime endYear = DateTime.Parse(dt.ToString("yyyy-01-01")).AddYears(1).AddDays(-1).GetDaysEnd(); //年末
                p.DateStart = startYear;
                p.DateEnd = endYear;
            }
            else if (userIndexStatistics_IN.EnumStatisticsDateSpanSelect == EnumStatisticsDateSpanSelect.SelectYear)
            {
                DateTime startYear = new DateTime(userIndexStatistics_IN.SelectYear, 1, 1).GetDaysStart();  //年初
                DateTime endYear = new DateTime(userIndexStatistics_IN.SelectYear, 12, 31).GetDaysEnd();  //年末
                p.DateStart = startYear;
                p.DateEnd = endYear;
            }
            else if (userIndexStatistics_IN.EnumStatisticsDateSpanSelect == EnumStatisticsDateSpanSelect.Today)
            {
                DateTime dateStart = dt.GetDaysStart();
                DateTime dateEnd = dt.GetDaysEnd();
                p.DateStart = dateStart;
                p.DateEnd = dateEnd;
            }
            else if (userIndexStatistics_IN.EnumStatisticsDateSpanSelect == EnumStatisticsDateSpanSelect.CurrentWeek)
            {
                DateTime startWeek = dt.AddDays(1 - Convert.ToInt32(dt.DayOfWeek.ToString("d"))).GetDaysStart();  //本周周一
                DateTime endWeek = startWeek.AddDays(6).GetDaysEnd();  //本周周日
                p.DateStart = startWeek;
                p.DateEnd = endWeek;
            }
            else
            {
                //CurrentQuarter 默认本季度
                DateTime startQuarter = dt.AddMonths(0 - (dt.Month - 1) % 3).AddDays(1 - dt.Day);  //本季度初
                DateTime endQuarter = startQuarter.AddMonths(3).AddDays(-1).GetDaysEnd();  //本季度末
                p.DateStart = startQuarter;
                p.DateEnd = endQuarter;
            }
            return p;
        }
        public SalesRankStatisticsParmas FormatSalesRankStatisticsParmas(UserIndexStatistics_IN userIndexStatistics_IN)
        {
            SalesRankStatisticsParmas p = TransUserIndexStatisticsDate(userIndexStatistics_IN).MappingTo<SalesRankStatisticsParmas>();
            //客户来源
            p.CustomerDataSources = userIndexStatistics_IN.CustomerDataSources;


            //验证超级权限
            bool superRole = BLL_Role.Instance.CheckSuperUser();
            bool managerRole = BLL_Role.Instance.CheckSalesManagerUser();
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            Db_sys_organization orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userOrg); ;
            if (!superRole && !managerRole)
            {
                var orgObjList = DbOpe_sys_organization.Instance.GetSelfAndSubOrgListByOrgId(userOrg);
                //用户当前所有有权限的组织（用于判断参数里要统计的组织有没有权限）
                var userOrgList = orgObjList.Select(o => o.Id).ToList();
                //userOrgList.Add(userOrg);
                //判断参数里面的组织有没有权限
                if (!StringUtil.IsNullOrEmpty(userIndexStatistics_IN.OrgId))
                {
                    if (!userOrgList.Contains(userIndexStatistics_IN.OrgId))
                    {
                        throw new ApiException("没有组织权限");
                    }
                    orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userIndexStatistics_IN.OrgId);
                    p.QueryOrgId = userIndexStatistics_IN.OrgId;
                }
                else
                {
                    p.QueryOrgId = userOrg;
                }
            }
            else
            {
                if (!StringUtil.IsNullOrEmpty(userIndexStatistics_IN.OrgId))
                {
                    orgInfo = DbOpe_sys_organization.Instance.GetData(d => d.Id == userIndexStatistics_IN.OrgId);
                    p.QueryOrgId = userIndexStatistics_IN.OrgId;
                }
            }
            p.EnumRankOrgType = orgInfo.OrgType;
            p.FindOrgId = userOrg;
            p.FindUserId = currentUser;
            return p;
        }
        public CompanyRankStatisticsParmas FormatCompanyRankStatisticsParmas(UserIndexCompanyRankStatistics_IN userIndexCompanyRankStatistics_IN)
        {
            CompanyRankStatisticsParmas p = TransUserIndexStatisticsDate(userIndexCompanyRankStatistics_IN).MappingTo<CompanyRankStatisticsParmas>();
            //客户来源
            p.CustomerDataSources = userIndexCompanyRankStatistics_IN.CustomerDataSources;
            //排名类型
            p.EnumCompanyRankType = userIndexCompanyRankStatistics_IN.EnumCompanyRankType;
            var userOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            p.FindOrgId = userOrg;
            p.FindUserId = currentUser;
            return p;
        }

        #endregion

        #region 原系统的统计
        public List<UserRankStatistics_OUT> OrgAchivementRankStatistics(OrgRankStatistics_IN orgRankStatistics_IN, string userId = "")
        {
            ////测试用
            //orgRankStatistics_IN.IgnoreSortSet = true;
            var useCurrentOrg = false;
            if (orgRankStatistics_IN.DateStart != null && orgRankStatistics_IN.DateEnd != null)
            {
                var starYear = orgRankStatistics_IN.DateStart.Value.Year;
                var endYear = orgRankStatistics_IN.DateEnd.Value.Year;
                useCurrentOrg = starYear == endYear && starYear == DateTime.Now.Year;
                orgRankStatistics_IN.DateStart = orgRankStatistics_IN.DateStart.Value.GetMonthStart();
                orgRankStatistics_IN.DateEnd = orgRankStatistics_IN.DateEnd.Value.GetMonthEnd();
            }
            else
            {
                useCurrentOrg = true;
                orgRankStatistics_IN.DateStart = DateTime.Now.GetMonthStart();
                orgRankStatistics_IN.DateEnd = DateTime.Now.GetMonthEnd();
            }
            if (string.IsNullOrEmpty(userId))
            {
                userId = currentUser;
            }
            if (orgRankStatistics_IN.EnumRankOrgType == EnumStatisticOrgType.User)
            {
                return DbOpe_crm_contract_receiptregister.Instance.UserAchivementRank(orgRankStatistics_IN, userId, useCurrentOrg);
            }
            else
            {
                return DbOpe_crm_contract_receiptregister.Instance.OrgAchivementRank(orgRankStatistics_IN, userId, useCurrentOrg).MappingTo<List<UserRankStatistics_OUT>>();
            }

        }
        /// <summary>
        /// 导出业绩
        /// </summary>
        /// <param name="orgRankStatistics_IN">业绩参数</param>
        /// <returns></returns>
        public Stream GetOrgAchivementRankDownload(AchiveRankStatistics_IN orgRankStatistics_IN)
        {
            if (string.IsNullOrEmpty(currentUser))
            {
                throw new ApiException("未获取到当前登录人员信息");
            }
            //验证要查询的组织 当前用户是否有权限查询
            var userOrg = DbOpe_sys_user.Instance.GetOrgIdByUserId(currentUser);
            if (userOrg == Guid.Empty.ToString())
            {
                if (orgRankStatistics_IN.EnumRankOrgType == EnumStatisticOrgType.User)
                {
                    Stopwatch stopwatch = new Stopwatch();
                    // 开始计时
                    stopwatch.Start();
                    //非销售组织、人员业绩
                    var manageList = AchivementRankStatistics(orgRankStatistics_IN).MappingTo<List<UserRankStatisticsExcel_OUT>>();
                    stopwatch.Stop();
                    TimeSpan time1 = stopwatch.Elapsed;
                    LogUtil.AddErrorLog($"OrgAchivementRankStatistics and MappingTo took: {time1}");
                    // 重置Stopwatch以便下一次使用
                    stopwatch.Reset();
                    // 开始计时
                    stopwatch.Start();
                    ExcelExporterNPOI exp = new ExcelExporterNPOI();
                    var expBytes = exp.ExportAsByteArray(manageList);
                    // 停止计时并获取耗时
                    stopwatch.Stop();
                    TimeSpan time2 = stopwatch.Elapsed;
                    LogUtil.AddErrorLog($"ExportAsByteArray took: {time2}");

                    // 重置Stopwatch
                    stopwatch.Reset();
                    return new MemoryStream(expBytes);
                }
                else
                {
                    //非销售组织、组织业绩
                    var manageList = AchivementRankStatistics(orgRankStatistics_IN).MappingTo<List<OrgRankStatisticsExcel_OUT>>();
                    ExcelExporterNPOI exp = new ExcelExporterNPOI();
                    var expBytes = exp.ExportAsByteArray(manageList);
                    return new MemoryStream(expBytes);
                }

            }
            else
            {
                if (orgRankStatistics_IN.EnumRankOrgType == EnumStatisticOrgType.User)
                {
                    //销售组织、个人业绩
                    var simpleList = AchivementRankStatistics(orgRankStatistics_IN).MappingTo<List<UserRankStatisticsExcel_Basic_OUT>>();
                    ExcelExporterNPOI exp = new ExcelExporterNPOI();
                    var expBytes = exp.ExportAsByteArray(simpleList);
                    return new MemoryStream(expBytes);
                }
                else
                {
                    //销售组织、组织业绩
                    var manageList = AchivementRankStatistics(orgRankStatistics_IN).MappingTo<List<OrgRankStatisticsExcel_OUT>>();
                    ExcelExporterNPOI exp = new ExcelExporterNPOI();
                    var expBytes = exp.ExportAsByteArray(manageList);
                    return new MemoryStream(expBytes);

                }
            }
        }
        #endregion

        #region 工作报告
        //public ManagerReportStatistics_OUT ManagerReportStatistics()
        //{
        //    ManagerReportStatistics_OUT workReportStatistics_OUT = new ManagerReportStatistics_OUT();
        //    SalesDataStatisticsParams todayUserParams = FormatSalesDataStatisticsParams(new UserIndexStatistics_IN()
        //    {
        //        EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.Today
        //    }, currentUser);
        //    SalesDataStatisticsParams todayOrgParams = FormatSalesDataStatisticsParams(new UserIndexStatistics_IN()
        //    {
        //        EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.Today
        //    }, "", true);
        //    //今日业绩
        //    var GetAchivementData_Today_User = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(todayUserParams).Sum(d => d.SalesPerformance);
        //    var GetAchivementData_Today_Org = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(todayOrgParams).Sum(d => d.SalesPerformance);
        //    workReportStatistics_OUT.User_SalesAchivement_Today = Convert.ToDecimal(GetAchivementData_Today_User).ToString("0.00");
        //    workReportStatistics_OUT.Org_SalesAchivement_Today = Convert.ToDecimal(GetAchivementData_Today_Org).ToString("0.00");
        //    //月业绩
        //    SalesDataStatisticsParams monthOrgParams = FormatSalesDataStatisticsParams(new UserIndexStatistics_IN()
        //    {
        //        EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.CurrentMonth
        //    }, "", true);
        //    var GetAchivementData_Month_Org = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(monthOrgParams).Sum(d => d.SalesPerformance);
        //    workReportStatistics_OUT.Org_SalesAchivement_Month = Convert.ToDecimal(GetAchivementData_Month_Org).ToString("0.00");
        //    //业绩目标
        //    //目标
        //    List<Db_sys_performance_objectives> performanceList = DbOpe_sys_performance_objectives.Instance.GetPerformanceObjectivesListByYearList(new List<string>() { DateTime.Now.Year.ToString() }, 1);
        //    string targetOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
        //    var performance = performanceList.Find(l => l.OrgId == targetOrg);
        //    workReportStatistics_OUT.Org_SalesAchivementTarget_Month = Convert.ToDecimal(performance == null ? 0 : performance.ObjectivesValue).ToString("0.00");
        //    //合同
        //    var GetContractAmountData_Month_Org_Query = DbOpe_crm_contract_receiptregister.Instance.GetContractAmountDataQuery(monthOrgParams);
        //    //到账
        //    var GetArrivalAmountData_Month_Org_List = DbOpe_crm_contract_receiptregister.Instance.GetArrivalAmountDataQuery(monthOrgParams).ToList();
        //    //合同金额
        //    var GetContractAmountData_Month_Org_Currencys = GetContractAmountData_Month_Org_Query.GroupBy(a=>a.Currency).Select(a=>new ContractAmount { Currency = a.Currency,Value = SqlFunc.AggregateSum(a.ContractAmount) }).ToList();
        //    string GetContractAmountData_Month_Org = "";
        //    List<string> contractAmountGroup = new List<string>();
        //    foreach (var currencyGroup in GetContractAmountData_Month_Org_Currencys)
        //    {
        //        if (currencyGroup.Currency != null && Enum.IsDefined(typeof(EnumCurrencyLite), currencyGroup.Currency.Value))
        //        {
        //            contractAmountGroup.Add(Convert.ToDecimal(currencyGroup.Value).ToString("0.00") + ((EnumCurrencyLite)(currencyGroup.Currency.Value)).GetEnumDescription());
        //        }
        //    }
        //    GetContractAmountData_Month_Org = contractAmountGroup.JoinToString("+");
        //    workReportStatistics_OUT.Org_ContractAmount_Month = GetContractAmountData_Month_Org;
        //    //到账金额
        //    var GetArrivalAmountData_Month_Org = GetArrivalAmountData_Month_Org_List.Sum(o => o.ArrivalAmount);
        //    //未到账金额
        //    workReportStatistics_OUT.Org_ToBeRecivedAmount_Month = (Convert.ToDecimal(GetContractAmountData_Month_Org) - Convert.ToDecimal(GetArrivalAmountData_Month_Org)).ToString("0.00");
        //    //合同客户
        //    var GetContractAmountData_Month_Org_Customer = Convert.ToInt32(GetContractAmountData_Month_Org_Query.ToList().Select(o => o.CustomerId).Distinct().Count());
        //    //到账客户
        //    var GetArrivalAmountData_Month_Org_Customer_List = DbOpe_crm_contract_receiptregister.Instance.GetArrivalCustomerDataQuery(monthOrgParams).ToList();
        //    var GetArrivalAmountData_Month_Org_Customer = GetArrivalAmountData_Month_Org_Customer_List.Count();
        //    //未到账客户
        //    workReportStatistics_OUT.Org_ToBeRecivedCustomer_Month = GetContractAmountData_Month_Org_Customer - GetArrivalAmountData_Month_Org_Customer;
        //    return workReportStatistics_OUT;
        //}
        public ManagerReportStatistics_OUT ManagerReportStatistics()
        {
            ManagerReportStatistics_OUT workReportStatistics_OUT = new ManagerReportStatistics_OUT();
            SalesDataStatisticsParams todayUserParams = FormatSalesDataStatisticsParams(new UserIndexStatistics_IN()
            {
                EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.Today
            }, currentUser);
            SalesDataStatisticsParams todayOrgParams = FormatSalesDataStatisticsParams(new UserIndexStatistics_IN()
            {
                EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.Today
            }, "", true);
            //今日业绩
            var GetAchivementData_Today_User = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(todayUserParams).Sum(d => d.EffectivePerformance);
            var GetAchivementData_Today_Org = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(todayOrgParams).Sum(d => d.EffectivePerformance);
            workReportStatistics_OUT.User_SalesAchivement_Today = Convert.ToDecimal(GetAchivementData_Today_User).ToString("0.00");
            workReportStatistics_OUT.Org_SalesAchivement_Today = Convert.ToDecimal(GetAchivementData_Today_Org).ToString("0.00");
            //月业绩
            SalesDataStatisticsParams monthOrgParams = FormatSalesDataStatisticsParams(new UserIndexStatistics_IN()
            {
                EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.CurrentMonth
            }, "", true);
            var GetAchivementData_Month_Org = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(monthOrgParams).Sum(d => d.EffectivePerformance);
            workReportStatistics_OUT.Org_SalesAchivement_Month = Convert.ToDecimal(GetAchivementData_Month_Org).ToString("0.00");
            //业绩目标
            //目标
            List<Db_sys_performance_objectives> performanceList = DbOpe_sys_performance_objectives.Instance.GetPerformanceObjectivesListByYearList(new List<int>() { DateTime.Now.Year }, 1);
            string targetOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var performance = performanceList.FindAll(l => l.OrgId == targetOrg);
            workReportStatistics_OUT.Org_SalesAchivementTarget_Month = (DbOpe_sys_performance_objectives.Instance.GetMonthObjFromRowData(DateTime.Now, performance) * 10000).ToString("0.00");
            //合同
            var GetContractAmountData_Month_Org_List = DbOpe_crm_contract_receiptregister.Instance.GetContractAmountDataQuery(monthOrgParams).ToList();
            //到账
            var GetArrivalAmountData_Month_Org_List = DbOpe_crm_contract_receiptregister.Instance.GetArrivalAmountDataQuery(monthOrgParams).ToList();
            //合同金额
            var GetContractAmountData_Month_Org = GetContractAmountData_Month_Org_List.Sum(o => o.ContractAmount);
            workReportStatistics_OUT.Org_ContractAmount_Month = Convert.ToDecimal(GetContractAmountData_Month_Org).ToString("0.00");
            //到账金额
            var GetArrivalAmountData_Month_Org = GetArrivalAmountData_Month_Org_List.Sum(o => o.ArrivalAmount);
            //未到账金额
            workReportStatistics_OUT.Org_ToBeRecivedAmount_Month = (Convert.ToDecimal(GetContractAmountData_Month_Org) - Convert.ToDecimal(GetArrivalAmountData_Month_Org)).ToString("0.00");
            //合同客户
            var GetContractAmountData_Month_Org_Customer = Convert.ToInt32(GetContractAmountData_Month_Org_List.Select(o => o.CustomerId).Distinct().Count());
            //到账客户
            var GetArrivalAmountData_Month_Org_Customer_List = DbOpe_crm_contract_receiptregister.Instance.GetArrivalCustomerDataQuery(monthOrgParams).ToList();
            var GetArrivalAmountData_Month_Org_Customer = GetArrivalAmountData_Month_Org_Customer_List.Count();
            //未到账客户
            workReportStatistics_OUT.Org_ToBeRecivedCustomer_Month = GetContractAmountData_Month_Org_Customer - GetArrivalAmountData_Month_Org_Customer;
            return workReportStatistics_OUT;
        }
        public WeekWorkReportStatistics_OUT WeekWorkReportStatistics()
        {
            WeekWorkReportStatistics_OUT weekWorkReportStatistics_OUT = new WeekWorkReportStatistics_OUT();
            SalesDataStatisticsParams userParams = FormatSalesDataStatisticsParams(new UserIndexStatistics_IN()
            {
                EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.CurrentWeek
            }, currentUser);
            //业绩
            var GetAchivementData_Week_User_List = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(userParams).ToList();
            weekWorkReportStatistics_OUT.SalesAchivement_Week = Convert.ToDecimal(GetAchivementData_Week_User_List.Sum(l => l.SalesPerformance)).ToString("0.00");
            weekWorkReportStatistics_OUT.EffectiveAchivement_Week = Convert.ToDecimal(GetAchivementData_Week_User_List.Sum(l => l.EffectivePerformance)).ToString("0.00");
            //合同
            var GetContractAmountData_Week = DbOpe_crm_contract_receiptregister.Instance.GetContractAmountDataQuery(userParams).Sum(l => l.ContractAmount);
            weekWorkReportStatistics_OUT.ContractAmount_Week = Convert.ToDecimal(GetContractAmountData_Week).ToString("0.00");
            //客户
            var GetGetCustomer_Week_List = DbOpe_crm_contract_receiptregister.Instance.GetGetCustomerQuery(userParams).ToList();
            weekWorkReportStatistics_OUT.NewCustomerTypeStatistics = new List<CustomerStatisticsTypeItem_OUT>();
            foreach (EnumCustomerLevel i in Enum.GetValues(typeof(EnumCustomerLevel)))
            {
                var typeCount = GetGetCustomer_Week_List.FindAll(l => l.CustomerLevel == (int)i).Count();
                weekWorkReportStatistics_OUT.NewCustomerTypeStatistics.Add(new CustomerStatisticsTypeItem_OUT()
                {
                    Count = typeCount,
                    Key = (int)i + "",
                    Name = i.GetEnumDescription()
                }); ;
            }
            return weekWorkReportStatistics_OUT;
        }
        public MonthWorkReportStatistics_OUT MonthWorkReportStatistics()
        {
            MonthWorkReportStatistics_OUT monthWorkReportStatistics_OUT = new MonthWorkReportStatistics_OUT();
            SalesDataStatisticsParams userParams = FormatSalesDataStatisticsParams(new UserIndexStatistics_IN()
            {
                EnumStatisticsDateSpanSelect = EnumStatisticsDateSpanSelect.CurrentMonth
            }, currentUser);
            //业绩
            var GetAchivementData_Month = DbOpe_crm_contract_receiptregister.Instance.GetAchivementDataQuery(userParams).Sum(l => l.EffectivePerformance);
            monthWorkReportStatistics_OUT.SalesAchivement_Month = Convert.ToDecimal(GetAchivementData_Month).ToString("0.00");
            //合同
            var GetContractAmountData_Month = DbOpe_crm_contract_receiptregister.Instance.GetContractAmountDataQuery(userParams).Sum(l => l.ContractAmount);
            //var test = DbOpe_crm_contract_receiptregister.Instance.GetContractAmountDataQuery(userParams).ToList();
            monthWorkReportStatistics_OUT.ContractAmount_Month = Convert.ToDecimal(GetContractAmountData_Month).ToString("0.00");
            //目标
            List<Db_sys_performance_objectives> performanceList = DbOpe_sys_performance_objectives.Instance.GetPerformanceObjectivesListByYearList(new List<int>() { DateTime.Now.Year }, 1);
            string targetOrg = DbOpe_sys_user.Instance.GetDataById(currentUser).OrganizationId;
            var performance = performanceList.FindAll(l => l.OrgId == targetOrg);
            monthWorkReportStatistics_OUT.SalesAchivementTarget_Month = (DbOpe_sys_performance_objectives.Instance.GetMonthObjFromRowData(DateTime.Now, performance) * 10000).ToString("0.00");
            //新增客户
            var GetGetCustomer_Month_List = DbOpe_crm_contract_receiptregister.Instance.GetGetCustomerQuery(userParams).ToList();
            monthWorkReportStatistics_OUT.NewCustomerTypeStatistics = new List<CustomerStatisticsTypeItem_OUT>();
            foreach (EnumCustomerLevel i in Enum.GetValues(typeof(EnumCustomerLevel)))
            {
                var typeCount = GetGetCustomer_Month_List.FindAll(l => l.CustomerLevel == (int)i).Count();
                monthWorkReportStatistics_OUT.NewCustomerTypeStatistics.Add(new CustomerStatisticsTypeItem_OUT()
                {
                    Count = typeCount,
                    Key = (int)i + "",
                    Name = i.GetEnumDescription()
                }); ;
            }
            //签约客户
            var GetSignData_Month = DbOpe_crm_contract_receiptregister.Instance.GetSignDataQuery(userParams).Select(d => d.CustomerId).Count();
            monthWorkReportStatistics_OUT.SignCustomerNum_Month = GetSignData_Month;
            return monthWorkReportStatistics_OUT;
        }
        #endregion


        #region 移动端首页
        /// <summary>
        /// 趋势统计
        /// </summary>
        /// <param name="query_IN"></param>
        /// <returns></returns>
        public StaticticsTableResult GetAchivemntTrendQueryForH5(GetAchivementTrendQuery_IN query_IN)
        {
            StaticticsTableResult result = new StaticticsTableResult();
            GetAchivementQuery_IN getAchivemntQuery = query_IN.MappingTo<GetAchivementQuery_IN>();
            getAchivemntQuery.DateType = EnumGetAchivemntQueryDateType.AchiveDate;
            #region 权限 (非销售组织的话就是所有组织下所有人)
            var user = DbOpe_sys_user.Instance.GetUserById(currentUser);

            getAchivemntQuery.UserIds = new List<string>() { currentUser };
            getAchivemntQuery.UserOrgIds = new List<string>() { user.OrganizationId };
            #endregion
            #region 获取统计数据配置
            var dic = DbOpe_sys_dictionary_statictics.Instance.GetStaDictionaryByPageAndParentName(new Dictionary_Sta_In()
            {
                PageType = 3
            }, currentUser, true);
            getAchivemntQuery.StaValues = new List<EnumGetAchivemntQueryValue>();
            if (query_IN.SelectStaValues != null)
            {
                //统计值列
                foreach (var selectValueCol in query_IN.SelectStaValues)
                {
                    var valueColParent = dic.Find(d => d.Id.ToString() == selectValueCol);
                    List<Dictionary_Sta_Out> valueColDics = new List<Dictionary_Sta_Out>();
                    FindLeafNodesRecursive(dic, valueColParent.Id.ToString(), ref valueColDics);

                    if (valueColDics.Count > 0)
                    {
                        foreach (var valueCol in valueColDics)
                        {
                            getAchivemntQuery.StaValues.Add((EnumGetAchivemntQueryValue)valueCol.Value.ToInt());
                        }
                    }
                    else
                    {
                        getAchivemntQuery.StaValues.Add((EnumGetAchivemntQueryValue)valueColParent.Value.ToInt());
                    }

                }
            }
            getAchivemntQuery.StaCountCols = new List<StatisticsCountItem>();
            if (query_IN.SelectStaCountCols != null)
            {
                //统计数量列
                foreach (var selectCountCol in query_IN.SelectStaCountCols)
                {
                    var countColParent = dic.Find(d => d.Id.ToString() == selectCountCol);
                    List<Dictionary_Sta_Out> countColDics = new List<Dictionary_Sta_Out>();
                    FindLeafNodesRecursive(dic, countColParent.Id.ToString(), ref countColDics);

                    if (countColDics.Count > 0)
                    {
                        foreach (var countCol in countColDics)
                        {
                            getAchivemntQuery.StaCountCols.Add(new StatisticsCountItem() { Name = countCol.Name, Value = countCol.Value, NameColKey = countCol.TableName, ConditionColKey = countCol.ConditionKey, AllowSort = countCol.AllowSort, AllowTable = countCol.AllowTable });
                        }
                    }
                    else
                    {
                        getAchivemntQuery.StaCountCols.Add(new StatisticsCountItem() { Name = countColParent.Name, Value = countColParent.Value, NameColKey = countColParent.TableName, ConditionColKey = countColParent.ConditionKey, AllowSort = countColParent.AllowSort, AllowTable = countColParent.AllowTable });
                    }

                }
            }
            getAchivemntQuery.StaFigures = new List<EnumFigureValue>();
            if (query_IN.SelectStaFigures != null)
            {

                //统计值列
                foreach (var selectFiguresCol in query_IN.SelectStaFigures)
                {
                    var figuresColParent = dic.Find(d => d.Id.ToString() == selectFiguresCol);
                    List<Dictionary_Sta_Out> figuresColDics = new List<Dictionary_Sta_Out>();
                    FindLeafNodesRecursive(dic, figuresColParent.Id.ToString(), ref figuresColDics);

                    if (figuresColDics.Count > 0)
                    {
                        foreach (var figuresCol in figuresColDics)
                        {
                            getAchivemntQuery.StaFigures.Add((EnumFigureValue)figuresCol.Value.ToInt());
                        }
                    }
                    else
                    {
                        getAchivemntQuery.StaFigures.Add((EnumFigureValue)figuresColParent.Value.ToInt());
                    }

                }
            }
            getAchivemntQuery.StaFigures.Sort();
            //统计左列
            getAchivemntQuery.LeftCols = new List<StatisticsLeftColItem>();
            var leftColParent = dic.Find(d => d.Id.ToString() == query_IN.SelectLeftCol);
            List<Dictionary_Sta_Out> leftColDics = new List<Dictionary_Sta_Out>();
            FindLeafNodesRecursive(dic, leftColParent.Id.ToString(), ref leftColDics);
            foreach (var leftCol in leftColDics)
            {
                getAchivemntQuery.LeftCols.Add(new StatisticsLeftColItem() { Name = leftCol.Name, Value = leftCol.Value, NameColKey = leftCol.TableName, AllowSort = leftCol.AllowSort });
            }
            //统计右列
            getAchivemntQuery.RightCols = new List<StatisticsLeftColItem>();
            var rightColParent = dic.Find(d => d.Id.ToString() == query_IN.SelectRightCol);
            List<Dictionary_Sta_Out> rightColDics = new List<Dictionary_Sta_Out>();
            FindLeafNodesRecursive(dic, rightColParent.Id.ToString(), ref rightColDics);
            foreach (var rightCol in rightColDics)
            {
                getAchivemntQuery.RightCols.Add(new StatisticsLeftColItem() { Name = rightCol.Name, Value = rightCol.Value, NameColKey = rightCol.TableName, AllowSort = rightCol.AllowSort });
            }
            #endregion
            #region 排序
            string sortField = getAchivemntQuery.SortField;
            if (string.IsNullOrEmpty(sortField) ||
                (
                    (getAchivemntQuery.StaCountCols.Find(c => c.NameColKey == sortField) == null)
                    && (getAchivemntQuery.LeftCols.Find(c => c.NameColKey == sortField) == null)
                    && (getAchivemntQuery.StaValues.Find(c => c.GetDisplayName() == sortField) == null)
                )
            )
            {
                sortField = getAchivemntQuery.LeftCols[0].NameColKey;
            }
            List<OrderByModel> orderList = OrderByModel.Create(
                new OrderByModel() { FieldName = sortField, OrderByType = getAchivemntQuery.IsDESC ? OrderByType.Desc : OrderByType.Asc }
            );
            #endregion
            #region 构建SelectModel
            string leftColKeys = getAchivemntQuery.LeftCols.Select(c => c.Value).ToList().JoinToString();
            string RightColKeys = getAchivemntQuery.RightCols.Select(c => c.Value).ToList().JoinToString();
            string groupKeys = getAchivemntQuery.LeftCols.Select(c => c.Value).Concat(getAchivemntQuery.RightCols.Select(c => c.Value)).ToList().JoinToString();
            var leftGroupSingleObj = getAchivemntQuery.LeftCols.OrderByDescending(c => c.AllowSort).FirstOrDefault();
            var selector = new List<SelectModel>();
            var nameSelector = new List<SelectModel>();
            var leftSelector = new List<SelectModel>();
            var leftNameSelector = new List<SelectModel>();
            foreach (var valueItem in getAchivemntQuery.StaValues)
            {
                selector.Add(new SelectModel() { AsName = valueItem.GetDisplayName(), FiledName = ObjectFuncModel.Create("AggregateSumNoNull", valueItem.GetDisplayName()) });
                nameSelector.Add(new SelectModel() { FiledName = valueItem.GetDisplayName() });
                leftSelector.Add(new SelectModel() { AsName = valueItem.GetDisplayName(), FiledName = ObjectFuncModel.Create("AggregateSumNoNull", valueItem.GetDisplayName()) });
                leftNameSelector.Add(new SelectModel() { FiledName = valueItem.GetDisplayName() });
                //增加同环比 注：未处理按年统计不存在环比问题
                foreach (var Figure in getAchivemntQuery.StaFigures)
                {
                    selector.Add(new SelectModel() { AsName = valueItem.GetDisplayName() + Figure.GetDisplayName(), FiledName = ObjectFuncModel.Create("AggregateSumNoNull", valueItem.GetDisplayName()) });
                    nameSelector.Add(new SelectModel()
                    {
                        FiledName = valueItem.GetDisplayName() + Figure.GetDisplayName(),
                    });
                }
            }
            foreach (var countItem in getAchivemntQuery.StaCountCols)
            {
                selector.Add(new SelectModel() { AsName = countItem.NameColKey, FiledName = ObjectFuncModel.Create("AggregateDistinctCount", countItem.Value) });
                nameSelector.Add(new SelectModel() { FiledName = countItem.NameColKey });
                leftSelector.Add(new SelectModel() { AsName = countItem.NameColKey, FiledName = ObjectFuncModel.Create("AggregateDistinctCount", countItem.Value) });
                leftNameSelector.Add(new SelectModel() { FiledName = countItem.NameColKey });
                //增加同环比 注：未处理按年统计不存在环比问题
                foreach (var Figure in getAchivemntQuery.StaFigures)
                {

                    if (Figure == EnumFigureValue.FigurePercent || Figure == EnumFigureValue.CyclePercent)
                    {
                        selector.Add(new SelectModel() { AsName = countItem.NameColKey + Figure.GetDisplayName(), FiledName = ObjectFuncModel.Create("ToDecimal", ObjectFuncModel.Create("AggregateSumNoNull", countItem.Value)) });
                    }
                    else
                    {
                        selector.Add(new SelectModel() { AsName = countItem.NameColKey + Figure.GetDisplayName(), FiledName = ObjectFuncModel.Create("AggregateSumNoNull", countItem.Value) });
                    }
                    nameSelector.Add(new SelectModel()
                    {
                        FiledName = countItem.NameColKey + Figure.GetDisplayName(),
                    });
                }
            }
            foreach (var leftItem in getAchivemntQuery.LeftCols)
            {

                selector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = leftItem.Value });
                leftSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = leftItem.Value });
                leftSelector.Add(new SelectModel() { AsName = leftItem.Value, FiledName = leftItem.Value });



                leftNameSelector.Add(new SelectModel() { AsName = leftItem.Value, FiledName = leftItem.Value });
                if (leftItem.Value == "UserId")
                {
                    selector.Add(new SelectModel() { AsName = leftItem.Value, FiledName = leftItem.Value });
                    leftNameSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = "user." + leftItem.NameColKey });
                    nameSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = "user." + leftItem.NameColKey });
                }
                else
                {
                    leftNameSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = leftItem.NameColKey });
                    nameSelector.Add(new SelectModel() { AsName = leftItem.NameColKey, FiledName = leftItem.NameColKey });
                }

            }
            foreach (var rightItem in getAchivemntQuery.RightCols)
            {
                selector.Add(new SelectModel() { AsName = rightItem.Value, FiledName = rightItem.Value });
                nameSelector.Add(new SelectModel() { AsName = rightItem.NameColKey, FiledName = rightItem.NameColKey });
            }
            #endregion
            #region 先获取当前排序下需要查询的所有左侧项目值
            int total = 0;
            var leftList = GetAchivemntQuery(getAchivemntQuery)
                .GroupBy(leftGroupSingleObj.Value)
                .Select(leftSelector)
                .MergeTable()
                .LeftJoinIF<Db_sys_user>(leftColKeys.Contains("UserId"), (it, user) => it.UserId == user.Id)
                .Select<dynamic>(leftNameSelector)
                .MergeTable()
                .OrderBy(orderList)
                .ToPageList(getAchivemntQuery.PageNumber, getAchivemntQuery.PageSize, ref total);
            result.total = total;
            #endregion

            #region 左侧列变为条件
            var conModels = new List<IConditionalModel>();
            foreach (var leftItem in getAchivemntQuery.LeftCols)
            {
                var conditionalList = new List<KeyValuePair<WhereType, SqlSugar.ConditionalModel>>();
                var fieldName = leftItem.Value.ToString();
                var fieldValues = leftList.Select(item =>
                {
                    try
                    {
                        IDictionary<string, object> vItem = item as IDictionary<string, object>;
                        if (vItem.TryGetDicValue<string>(fieldName, out string v))
                        {
                            return v;
                        }
                        else
                        {
                            return "";
                        }
                    }
                    catch (Exception)
                    {
                        // 如果在尝试获取属性时发生异常（比如属性不存在），则返回空字符串
                        return "";
                    }
                }).ToList();
                for (int i = 0; i < fieldValues.Count; i++)
                {
                    var leftResultObj = fieldValues[i];
                    WhereType whereType = WhereType.Or;
                    if (i == 0)
                    {
                        whereType = WhereType.And;
                    }
                    conditionalList.Add(new KeyValuePair<WhereType, ConditionalModel>(
                   whereType,
                   new ConditionalModel() { FieldName = fieldName, ConditionalType = ConditionalType.Equal, FieldValue = leftResultObj }));
                    if (string.IsNullOrEmpty(leftResultObj))
                    {
                        conditionalList.Add(new KeyValuePair<WhereType, ConditionalModel>(
                           WhereType.Or,
                           new ConditionalModel() { FieldName = fieldName, ConditionalType = ConditionalType.IsNullOrEmpty }));
                    }
                }
                conModels.Add(new ConditionalCollections() { ConditionalList = conditionalList });
            }
            #endregion

            #region 以左侧分页后数据位条件进行统计
            var list = GetAchivemntQuery(getAchivemntQuery)
                .Where(conModels)
                .GroupBy(groupKeys)
                .Select(selector)
                .MergeTable()
                .LeftJoinIF<Db_sys_user>(leftColKeys.Contains("UserId"), (it, user) => it.UserId == user.Id)
                .Select<ExpandoObject>(nameSelector)
                .MergeTable()
                .OrderBy(orderList)
                .ToList();
            #endregion

            #region 获取所有右侧时间段
            List<StaYearOtherDic> yearOtherDicList = new List<StaYearOtherDic>();
            if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupMonth") != null)
            {
                //月度统计
                yearOtherDicList = GenerateYearMonths(getAchivemntQuery.StartDate, getAchivemntQuery.EndDate);
            }
            else if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupQuarter") != null)
            {
                //季度统计
                yearOtherDicList = GenerateYearQuarters(getAchivemntQuery.StartDate, getAchivemntQuery.EndDate);
            }
            else
            {
                //年度统计
                yearOtherDicList = GenerateYears(getAchivemntQuery.StartDate, getAchivemntQuery.EndDate);
            }
            #endregion

            #region 处理同环比条件
            var hasFigure = getAchivemntQuery.StaFigures.Find(s => s == EnumFigureValue.Figure || s == EnumFigureValue.FigurePercent) != null;
            var hasCycle = getAchivemntQuery.StaFigures.Find(s => s == EnumFigureValue.Cycle || s == EnumFigureValue.CyclePercent) != null;
            List<ExpandoObject> figurelist = new List<ExpandoObject>();
            List<ExpandoObject> cyclelist = new List<ExpandoObject>();
            if (hasFigure || hasCycle)
            {
                //开始处理同环比数据
                //同比 -1年
                GetAchivementQuery_IN getFigureAchivemntQuery_IN = getAchivemntQuery.MappingTo<GetAchivementQuery_IN>();
                getFigureAchivemntQuery_IN.StartDate = getAchivemntQuery.StartDate.AddYears(-1);
                getFigureAchivemntQuery_IN.EndDate = getAchivemntQuery.EndDate.AddYears(-1);
                figurelist = GetAchivemntQuery(getFigureAchivemntQuery_IN)
                    .Where(conModels)
                    .GroupBy(groupKeys)
                    .Select(selector)
                    .MergeTable()
                    .LeftJoinIF<Db_sys_user>(leftColKeys.Contains("UserId"), (it, user) => it.UserId == user.Id)
                    .Select<ExpandoObject>(nameSelector)
                    .MergeTable()
                    .OrderBy(orderList)
                    .ToList();

                //环比 -开始日期结束间隔
                int days = getAchivemntQuery.EndDate.Subtract(getAchivemntQuery.StartDate).Days;  //值为1
                GetAchivementQuery_IN getCycleAchivemntQuery_IN = getAchivemntQuery.MappingTo<GetAchivementQuery_IN>();

                if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupMonth") != null)
                {
                    getCycleAchivemntQuery_IN.StartDate = getAchivemntQuery.StartDate.AddYears(-1).GetMonthStart();
                    //getCycleAchivemntQuery_IN.EndDate = getAchivemntQuery.EndDate.AddYears(-1).GetMonthEnd();
                }
                else if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupQuarter") != null)
                {
                    getCycleAchivemntQuery_IN.StartDate = getAchivemntQuery.StartDate.AddYears(-1).GetQuarterStart();
                    //getCycleAchivemntQuery_IN.EndDate = getAchivemntQuery.EndDate.AddYears(-1).GetQuarterEnd();
                }
                else
                {
                    getCycleAchivemntQuery_IN.StartDate = getAchivemntQuery.StartDate.AddYears(-1).GetYearsStart();
                    //getCycleAchivemntQuery_IN.EndDate = getAchivemntQuery.EndDate.AddYears(-1).GetYearsEnd();
                }
                cyclelist = GetAchivemntQuery(getCycleAchivemntQuery_IN)
                    .Where(conModels)
                    .GroupBy(groupKeys)
                    .Select(selector)
                    .MergeTable()
                    .LeftJoinIF<Db_sys_user>(leftColKeys.Contains("UserId"), (it, user) => it.UserId == user.Id)
                    .Select<ExpandoObject>(nameSelector)
                    .MergeTable()
                    .OrderBy(orderList)
                    .ToList();

            }

            #endregion





            #region 构建table
            List<StaticticsTableColData> Cols = new List<StaticticsTableColData>();
            foreach (var leftItem in getAchivemntQuery.LeftCols)
            {
                Cols.Add(new StaticticsTableColData()
                {
                    Prop = leftItem.NameColKey,
                    Label = leftItem.Name,
                    AllowSort = leftItem.AllowSort ? 1 : 0
                }); ;
            }
            if (getAchivemntQuery.RightCols != null && getAchivemntQuery.RightCols.Count > 0)
            {
                if (getAchivemntQuery.StaCountCols.Count + getAchivemntQuery.StaValues.Count + getAchivemntQuery.StaFigures.Count == 1)
                {
                    //单值统计  只有一层不存在树形结构
                    var singleValueProp = "";
                    if (getAchivemntQuery.StaCountCols.Count == 1)
                    {
                        singleValueProp = getAchivemntQuery.StaCountCols[0].NameColKey;
                    }
                    else
                    {
                        singleValueProp = getAchivemntQuery.StaValues[0].GetDisplayName();
                    }
                    foreach (var yearOtherItem in yearOtherDicList)
                    {
                        Cols.Add(new StaticticsTableColData()
                        {
                            Prop = string.Join("_", new string[] { yearOtherItem.ToString(), singleValueProp }),
                            Label = yearOtherItem.ToString()
                        });
                    }
                }
                else
                {
                    //多值统计  存在树形结构
                    foreach (var yearOtherItem in yearOtherDicList)
                    {
                        var dateCol = new StaticticsTableColData()
                        {
                            Prop = yearOtherItem.ToString(),
                            Label = yearOtherItem.ToString(),
                            Children = new List<StaticticsTableColData>()
                        };
                        Cols.Add(dateCol);
                        foreach (var countItem in getAchivemntQuery.StaCountCols)
                        {
                            if (getAchivemntQuery.StaFigures.Count == 0)
                            {
                                //没有同比环比的话，不需要三层结构了
                                dateCol.Children.Add(new StaticticsTableColData()
                                {
                                    Prop = string.Join("_", new string[] { yearOtherItem.ToString(), countItem.NameColKey }),
                                    Label = countItem.Name,
                                    ParentProp = dateCol.Prop
                                });
                            }
                            else
                            {
                                //有同环比，做成三层结构
                                var countCol = new StaticticsTableColData()
                                {
                                    Prop = countItem.NameColKey,
                                    Label = countItem.Name,
                                    Children = new List<StaticticsTableColData>(),
                                    ParentProp = dateCol.Prop
                                };
                                dateCol.Children.Add(countCol);
                                countCol.Children.Add(new StaticticsTableColData()
                                {
                                    Prop = string.Join("_", new string[] { yearOtherItem.ToString(), countItem.NameColKey }),
                                    Label = "数值",
                                    ParentProp = countCol.Prop
                                });
                                //增加同环比 注：未处理按年统计不存在环比问题
                                foreach (var Figure in getAchivemntQuery.StaFigures)
                                {
                                    countCol.Children.Add(new StaticticsTableColData()
                                    {
                                        Prop = string.Join("_", new string[] { yearOtherItem.ToString(), countItem.NameColKey, Figure.GetDisplayName() }),
                                        //Label = string.Join("_", new string[] { countItem.Name, Figure.GetEnumDescription() }),
                                        Label = Figure.GetEnumDescription(),
                                        ParentProp = countCol.Prop
                                    });
                                }
                            }

                        }
                        foreach (var valueItem in getAchivemntQuery.StaValues)
                        {
                            if (getAchivemntQuery.StaFigures.Count == 0)
                            {
                                dateCol.Children.Add(new StaticticsTableColData()
                                {
                                    Prop = string.Join("_", new string[] { yearOtherItem.ToString(), valueItem.GetDisplayName() }),
                                    Label = valueItem.GetEnumDescription(),
                                    ParentProp = dateCol.Prop
                                });
                            }
                            else
                            {
                                //有同环比，做成三层结构
                                var vCol = new StaticticsTableColData()
                                {
                                    Prop = valueItem.GetDisplayName(),
                                    Label = valueItem.GetEnumDescription(),
                                    Children = new List<StaticticsTableColData>(),
                                    ParentProp = dateCol.Prop
                                };
                                dateCol.Children.Add(vCol);
                                vCol.Children.Add(new StaticticsTableColData()
                                {
                                    Prop = string.Join("_", new string[] { yearOtherItem.ToString(), valueItem.GetDisplayName() }),
                                    Label = valueItem.GetEnumDescription(),
                                    ParentProp = vCol.Prop
                                });
                                //增加同环比 注：未处理按年统计不存在环比问题
                                foreach (var Figure in getAchivemntQuery.StaFigures)
                                {
                                    vCol.Children.Add(new StaticticsTableColData()
                                    {
                                        Prop = string.Join("_", new string[] { yearOtherItem.ToString(), valueItem.GetDisplayName(), Figure.GetDisplayName() }),
                                        //Label = string.Join("_", new string[] { valueItem.GetEnumDescription(), Figure.GetEnumDescription() }),
                                        Label = Figure.GetEnumDescription(),
                                        ParentProp = vCol.Prop
                                    });
                                }
                            }

                        }
                    }
                }
            }
            result.Cols = Cols;
            #endregion

            #region 构建tableData
            var otherDateGroupKey = "";
            EnumStaDateSpan enumStaDateSpan = EnumStaDateSpan.Year;
            //需要构建与col里相对应的prop
            if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupMonth") != null)
            {
                enumStaDateSpan = EnumStaDateSpan.Month;
                otherDateGroupKey = "GroupMonth";
            }
            else if (getAchivemntQuery.RightCols.Find(r => r.Value == "GroupQuarter") != null)
            {
                enumStaDateSpan = EnumStaDateSpan.Quarter;
                otherDateGroupKey = "GroupQuarter";
            }

            var pageList = list.Cast<IDictionary<string, object>>()
                .GroupBy(p => p.GetDefaultValue(leftGroupSingleObj.NameColKey))
                .Select(g => new
                {
                    Key = g.Key,
                    SpanObj = g.ToList()
                })
                .ToList();
            foreach (var item in pageList)
            {
                dynamic resultItem = new ExpandoObject();
                IDictionary<string, object> resultItemDic = resultItem as IDictionary<string, object>;
                resultItemDic.Add(leftGroupSingleObj.NameColKey, item.Key);
                Dictionary<string, Dictionary<string, object>> chartDic = new Dictionary<string, Dictionary<string, object>>();
                foreach (var countItem in getAchivemntQuery.StaCountCols)
                {
                    chartDic.Add(countItem.NameColKey, new Dictionary<string, object>());
                }
                foreach (var valueItem in getAchivemntQuery.StaValues)
                {
                    chartDic.Add(valueItem.GetDisplayName(), new Dictionary<string, object>());
                }
                foreach (var span in yearOtherDicList)
                {
                    //准备环比同比的值
                    ExpandoObject figureObj = new ExpandoObject();
                    if (hasFigure)
                    {
                        var figureYear = span.Year - 1;
                        var figureOtherValue = span.OtherValue;
                        StaYearOtherDic figureDic = new StaYearOtherDic(figureYear, figureOtherValue, enumStaDateSpan);
                        figureObj = figurelist.Find(d =>
                          (d.TryGetDicValue<string>(leftGroupSingleObj.NameColKey, out string key) && ((item.Key == null && string.IsNullOrEmpty(key)) || (item.Key != null && key == item.Key.ToString())))
                            && (string.IsNullOrEmpty(otherDateGroupKey) || (d.TryGetDicValue<int>(otherDateGroupKey, out int other) && other == figureOtherValue))
                            && (d.TryGetDicValue<int>("GroupYear", out int year) && year == figureYear)
                        ) ?? new ExpandoObject();
                    }
                    ExpandoObject cycleObj = new ExpandoObject();
                    if (hasCycle)
                    {
                        StaYearOtherDic cycleDic = span.MappingTo<StaYearOtherDic>();
                        cycleDic.SubtractOnePeriod();
                        cycleObj = cyclelist.Find(d =>
                            (d.TryGetDicValue<string>(leftGroupSingleObj.NameColKey, out string key) && ((item.Key == null && string.IsNullOrEmpty(key)) || (item.Key != null && key == item.Key.ToString())))
                            && (string.IsNullOrEmpty(otherDateGroupKey) || (d.TryGetDicValue<int>(otherDateGroupKey, out int other) && other == cycleDic.OtherValue))
                            && (d.TryGetDicValue<int>("GroupYear", out int year) && year == cycleDic.Year)
                        ) ?? new ExpandoObject();
                    }
                    //找对应时间的值
                    var dicItem = item.SpanObj.Find(d =>
                    (string.IsNullOrEmpty(otherDateGroupKey) || (d.TryGetDicValue<int>(otherDateGroupKey, out int other) && other == span.OtherValue)) && (d.TryGetDicValue<int>("GroupYear", out int year) && year == span.Year)
                    );
                    if (dicItem == null)
                    {
                        foreach (var countItem in getAchivemntQuery.StaCountCols)
                        {
                            chartDic[countItem.NameColKey].Add(span.ToString(), 0);
                            resultItemDic.Add(string.Join("_", new string[] { span.ToString(), countItem.NameColKey }), 0);
                            InsertFigureValue(0, getAchivemntQuery, figureObj, cycleObj, countItem.NameColKey, span, ref resultItemDic);
                        }
                        foreach (var valueItem in getAchivemntQuery.StaValues)
                        {
                            chartDic[valueItem.GetDisplayName()].Add(span.ToString(), 0);
                            resultItemDic.Add(string.Join("_", new string[] { span.ToString(), valueItem.GetDisplayName() }), 0);
                            InsertFigureValue(0, getAchivemntQuery, figureObj, cycleObj, valueItem.GetDisplayName(), span, ref resultItemDic);
                        }
                    }
                    else
                    {
                        foreach (var countItem in getAchivemntQuery.StaCountCols)
                        {
                            chartDic[countItem.NameColKey].Add(span.ToString(), dicItem[countItem.NameColKey]);
                            resultItemDic.Add(string.Join("_", new string[] { span.ToString(), countItem.NameColKey }), dicItem[countItem.NameColKey]);
                            InsertFigureValue(Convert.ToDecimal(dicItem[countItem.NameColKey]), getAchivemntQuery, figureObj, cycleObj, countItem.NameColKey, span, ref resultItemDic);
                        }
                        foreach (var valueItem in getAchivemntQuery.StaValues)
                        {
                            chartDic[valueItem.GetDisplayName()].Add(span.ToString(), dicItem[valueItem.GetDisplayName()]);
                            resultItemDic.Add(string.Join("_", new string[] { span.ToString(), valueItem.GetDisplayName() }), dicItem[valueItem.GetDisplayName()]);
                            InsertFigureValue(Convert.ToDecimal(dicItem[valueItem.GetDisplayName()]), getAchivemntQuery, figureObj, cycleObj, valueItem.GetDisplayName(), span, ref resultItemDic);
                        }
                    }
                }
                foreach (var cd in chartDic)
                {
                    resultItemDic.Add(cd.Key, cd.Value);
                }
                dynamic dynamicWrapper = resultItemDic.ToDynamic();
                result.Rows.Add(dynamicWrapper);

            }
            #endregion
            return result;
        }
        #endregion
    }
}
