-- 服务变更字段配置 - 字段key更新脚本（补充）
-- 执行日期: 2025-02-01
-- 根据key变动 copy 2.md更新字段名称

-- ================================
-- 更新字段key映射
-- ================================

-- 更新GtisDownloadPermission为GtisForbidSearchExport
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisForbidSearchExport', FieldName = '禁止搜索导出'
WHERE FieldKey = 'GtisDownloadPermission';

-- 更新GtisApplyCountry为GtisApplCountry
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GtisApplCountry', FieldName = 'GTIS申请国家'
WHERE FieldKey = 'GtisApplyCountry';

-- 更新GlobalSearchServiceStart为GlobalSearchServiceCycleStart
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GlobalSearchServiceCycleStart', FieldName = '环球搜服务周期开始时间'
WHERE FieldKey = 'GlobalSearchServiceStart';

-- 更新GlobalSearchServiceEnd为GlobalSearchServiceCycleEnd
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'GlobalSearchServiceCycleEnd', FieldName = '环球搜服务周期结束时间'
WHERE FieldKey = 'GlobalSearchServiceEnd';

-- 更新CollegeServiceStart为CollegeServiceCycleStart
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'CollegeServiceCycleStart', FieldName = '慧思学院服务周期开始时间'
WHERE FieldKey = 'CollegeServiceStart';

-- 更新CollegeServiceEnd为CollegeServiceCycleEnd
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'CollegeServiceCycleEnd', FieldName = '慧思学院服务周期结束时间'
WHERE FieldKey = 'CollegeServiceEnd';

-- 更新SalesWitsServiceStart为SalesWitsServiceCycleStart
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'SalesWitsServiceCycleStart', FieldName = 'SalesWits服务周期开始时间'
WHERE FieldKey = 'SalesWitsServiceStart';

-- 更新SalesWitsServiceEnd为SalesWitsServiceCycleEnd
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'SalesWitsServiceCycleEnd', FieldName = 'SalesWits服务周期结束时间'
WHERE FieldKey = 'SalesWitsServiceEnd';

-- 更新Users为SalesWitsPhoneId
-- 注意：这里不更新FieldKey，只更新在服服务字段名称映射
-- 在服务字段对应关系文档中已更新

-- ================================
-- 更新TriggerFields中的字段引用
-- ================================

-- 更新TriggerFields中的GtisDownloadPermission为GtisForbidSearchExport
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisDownloadPermission', 'GtisForbidSearchExport')
WHERE TriggerFields LIKE '%GtisDownloadPermission%';

-- 更新TriggerFields中的GtisApplyCountry为GtisApplCountry
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GtisApplyCountry', 'GtisApplCountry')
WHERE TriggerFields LIKE '%GtisApplyCountry%';

-- 更新TriggerFields中的GlobalSearchServiceStart为GlobalSearchServiceCycleStart
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GlobalSearchServiceStart', 'GlobalSearchServiceCycleStart')
WHERE TriggerFields LIKE '%GlobalSearchServiceStart%';

-- 更新TriggerFields中的GlobalSearchServiceEnd为GlobalSearchServiceCycleEnd
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'GlobalSearchServiceEnd', 'GlobalSearchServiceCycleEnd')
WHERE TriggerFields LIKE '%GlobalSearchServiceEnd%';

-- 更新TriggerFields中的CollegeServiceStart为CollegeServiceCycleStart
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'CollegeServiceStart', 'CollegeServiceCycleStart')
WHERE TriggerFields LIKE '%CollegeServiceStart%';

-- 更新TriggerFields中的CollegeServiceEnd为CollegeServiceCycleEnd
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'CollegeServiceEnd', 'CollegeServiceCycleEnd')
WHERE TriggerFields LIKE '%CollegeServiceEnd%';

-- 更新TriggerFields中的SalesWitsServiceStart为SalesWitsServiceCycleStart
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'SalesWitsServiceStart', 'SalesWitsServiceCycleStart')
WHERE TriggerFields LIKE '%SalesWitsServiceStart%';

-- 更新TriggerFields中的SalesWitsServiceEnd为SalesWitsServiceCycleEnd
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'SalesWitsServiceEnd', 'SalesWitsServiceCycleEnd')
WHERE TriggerFields LIKE '%SalesWitsServiceEnd%';

-- ================================
-- 验证更新结果
-- ================================

SELECT '字段key补充更新完成！' AS message;

-- 显示更新后的字段统计
SELECT 
    FieldKey,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT ChangeReasonEnum ORDER BY ChangeReasonEnum) as change_reasons
FROM crm_service_change_reason_field_config 
WHERE FieldKey IN ('GtisForbidSearchExport', 'GtisApplCountry', 'GlobalSearchServiceCycleStart', 'GlobalSearchServiceCycleEnd', 'CollegeServiceCycleStart', 'CollegeServiceCycleEnd', 'SalesWitsServiceCycleStart', 'SalesWitsServiceCycleEnd')
GROUP BY FieldKey
ORDER BY FieldKey;