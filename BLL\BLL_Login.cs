﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.BLL.WeChat;
using CRM2_API.Common.AppSetting;
using CRM2_API.Common.Cache;
using DingTalkService = CRM2_API.Common.DingTalk.DingTalk;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.InkML;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Http;
using System.Linq.Dynamic.Core.Tokenizer;
using System.Net.Http;
using System.Security.Policy;

namespace CRM2_API.BLL
{
    public class BLL_Login : BaseBLL<BLL_Login>
    {
        /// <summary>
        /// 通过手机号码/邮箱地址和验证码进行登录，验证手机号码/邮箱地址和验证码的有效性，然后根据手机号码/邮箱地址获取用户信息，验证用户信息的有效性，根据登录规则判定用户是否可以登录。
        /// </summary>
        /// <param name="loginByVerificationCodeIn"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public string LoginByVerificationCode(LoginByVerificationCode_In loginByVerificationCodeIn, HttpContext httpContext)
        {
            //获取用户信息
            var sysUser = DbOpe_sys_user.Instance.GetUserByVerifyCode(loginByVerificationCodeIn.LoginName);
            //判断是否符合登录设置
            if (loginByVerificationCodeIn.Type == EnumLoginCode.Phone && !sysUser.AllowMessageAuthCodeLogin)
                throw new ApiException("当前用户无法使用短信验证码方式登录");
            if (loginByVerificationCodeIn.Type == EnumLoginCode.Email && !sysUser.AllowEmailLogin)
                throw new ApiException("当前用户无法使用邮箱方式登录");
            //从缓存中获取验证码
            if (!RedisCache.LoginCode.CheckLoginCodeExist(loginByVerificationCodeIn.LoginName + loginByVerificationCodeIn.Type.ToString()))
                throw new ApiException("验证码校验失败");
            var code = RedisCache.LoginCode.CheckLoginCode(loginByVerificationCodeIn.LoginName + loginByVerificationCodeIn.Type.ToString());
            //判断验证码是否正确
            if (string.IsNullOrEmpty(code) || !loginByVerificationCodeIn.Code.Equals(code))
                throw new ApiException("验证码校验失败");
            //清除验证码缓存
            RedisCache.LoginCode.DeleteVerifyCode(loginByVerificationCodeIn.LoginName, loginByVerificationCodeIn.Type);
            //生成token并返回
            return OperateAfterLogin(sysUser, httpContext);
        }

        /// <summary>
        /// 获取手机号/邮箱号验证码，生成当前手机号/邮箱号的验证码并发送到当前手机号/邮箱号，并将手机号/邮箱号和验证码存入缓存，验证码有效时间为10分钟，10分钟内不可以重复发送，4位验证码。根据Type参数保存为不同类型的缓存。
        /// </summary>
        /// <param name="getVerifyCodeIn"></param>
        /// <exception cref="ApiException"></exception>
        public void GetVerificationCode(GetVerificationCode_In getVerifyCodeIn)
        {
            //根据手机号获取用户信息
            var sysUser = DbOpe_sys_user.Instance.GetUserByVerifyCode(getVerifyCodeIn.LoginName);
            //判断手机号是否存在,可能未注册或删除
            if (sysUser == null)
                throw new ApiException("手机号或邮箱地址不存在");
            //判断账户是否停用
            if (!sysUser.UserStatus)
                throw new ApiException("用户已停用");
            if (getVerifyCodeIn.Type == EnumLoginCode.Phone && !sysUser.AllowMessageAuthCodeLogin)
                throw new ApiException("当前用户无法使用短信验证码方式登录");
            if (getVerifyCodeIn.Type == EnumLoginCode.Email && !sysUser.AllowEmailLogin)
                throw new ApiException("当前用户无法使用邮箱方式登录");
            //验证是否存在缓存
            if (RedisCache.LoginCode.CheckLoginCodeExist(getVerifyCodeIn.LoginName + getVerifyCodeIn.Type.ToString()))
                throw new ApiException("已存在缓存验证码");
            //生成验证码
            var code = new RandomUtil(4, Enum_RandomFormat.OnlyNumber).GetRandom().ToString();
            //发送短信或邮件
            if (getVerifyCodeIn.Type == EnumLoginCode.Phone)
            {
                //发送短信验证码
                Com_PhoneHelper.SendLoginCheckCode(getVerifyCodeIn.LoginName, code);
            }
            if (getVerifyCodeIn.Type == EnumLoginCode.Email)
            {
                //发送邮件验证码
            }
            //存入缓存
            RedisCache.LoginCode.SaveVerifyCode(getVerifyCodeIn.LoginName, getVerifyCodeIn.Type, code);
        }
        /// <summary>
        /// 通过账号/手机号/Email和密码进行登录，验证账号/手机号/Email和密码的有效性，然后根据账号/手机号/Email和密码获取用户信息，验证用户信息的有效性，根据登录规则判定用户是否可以登录。
        /// </summary>
        /// <param name="loginByPwdIn"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public string LoginByPwd(LoginByPwd_In loginByPwdIn, HttpContext httpContext)
        {
            //获取用户信息
            var sysUser = DbOpe_sys_user.Instance.GetUserByPwd(loginByPwdIn.LoginName);
            //先验证输入的LoginName是否可以登录
            if (sysUser == null)
                throw new ApiException("用户名或密码错误");
            var checkLoginType = false;
            if (loginByPwdIn.LoginName.Equals(sysUser.UserName) && sysUser.AllowUserNameLogin)
                checkLoginType = true;
            if (loginByPwdIn.LoginName.Equals(sysUser.Telephone) && sysUser.AllowTelphoneLogin)
                checkLoginType = true;
            if (loginByPwdIn.LoginName.Equals(sysUser.Email) && sysUser.AllowEmailLogin)
                checkLoginType = true;
            if (!checkLoginType)
                throw new ApiException("登录方式错误");
            //密码转Md5
            string pwdMd5 = EncryptUtil.GetMd5(sysUser.UserName + loginByPwdIn.PassWord);
            //验证密码
            if (!sysUser.PassWord.Equals(pwdMd5))
                throw new ApiException("用户名或密码错误");
            //判断用户是否停用
            if (sysUser.UserStatus == false)
                throw new ApiException("用户已停用");
            //生成token并返回
            return OperateAfterLogin(sysUser, httpContext);
        }
        /// <summary>
        /// 通过微信扫码进行登录，调用微信接口通过Code获取用户Openid，然后根据Openid获取用户信息，验证用户信息的有效性，根据登录规则判定用户是否可以登录。
        /// </summary>
        /// <param name="code"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public string LoginByWeChartCode(string code, HttpContext httpContext)
        {
            WechatInfo Info = BLL_WeiXinHelper.Instance.getWeixinUserInfo(code);
            var openId = Info.unionid;
            var sysUser = DbOpe_sys_user.Instance.GetUserByWxOpenId(openId);
            if (sysUser == null)
                throw new ApiException("请绑定微信");
            //判断是否可以使用微信登录
            if (!sysUser.AllowWechatLogin)
                throw new ApiException("当前用户无法使用微信登录");
            //判断用户是否停用
            if (sysUser.UserStatus == false)
                throw new ApiException("用户已停用");
            //更新NickName
            DbOpe_sys_user.Instance.UpdateNickNameById(sysUser.Id, Info.nickname);
            //生成token并返回
            return OperateAfterLogin(sysUser, httpContext);
        }


        ///// <summary>
        ///// 微信扫码code登陆+重定向信息
        ///// </summary>
        ///// <param name="code"></param>
        ///// <param name="httpContext"></param>
        ///// <returns></returns>
        ///// <exception cref="ApiException"></exception>
        //public LoginByWeChartCodeWithRedirect_Out LoginByWeChartCodeWithRedirect(string code, HttpContext httpContext)
        //{
        //    LoginByWeChartCodeWithRedirect_Out loginByWeChartCodeWithRedirect_Out = new LoginByWeChartCodeWithRedirect_Out();
        //    WechatInfo Info = BLL_WeiXinHelper.Instance.getWeixinUserInfo(code);
        //    var openId = Info.unionid;
        //    var sysUser = DbOpe_sys_user.Instance.GetUserByWxOpenId(openId);
        //    if (sysUser == null)
        //        throw new ApiException("请绑定微信");
        //    //判断是否可以使用微信登录
        //    if (!sysUser.AllowWechatLogin)
        //        throw new ApiException("当前用户无法使用微信登录");
        //    //判断用户是否停用
        //    if (sysUser.UserStatus == false)
        //        throw new ApiException("用户已停用");
        //    //更新NickName
        //    DbOpe_sys_user.Instance.UpdateNickNameById(sysUser.Id, Info.nickname);
        //    //生成token并返回
        //    loginByWeChartCodeWithRedirect_Out.Token = OperateAfterLogin(sysUser, httpContext);
        //    return loginByWeChartCodeWithRedirect_Out;
        //}

        /// <summary>
        /// 通过微信扫码进行登录，调用微信接口通过Code获取用户Openid，然后根据Openid获取用户信息，验证用户信息的有效性，根据登录规则判定用户是否可以登录。
        /// </summary>
        /// <param name="code"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public string LoginByWeChartGZCode(string code, HttpContext httpContext)
        {
            LogUtil.AddLog("LoginByWeChartGZCode 执行1" + code);
            WechatInfo Info = BLL_WeiXinGZHelper.Instance.getWeixinUserInfoJSON(code);
            LogUtil.AddLog("LoginByWeChartGZCode 执行2" + Info);
            var openId = Info.unionid;
            var sysUser = DbOpe_sys_user.Instance.GetUserByWxOpenId(openId);
            LogUtil.AddLog("LoginByWeChartGZCode 执行3" + sysUser);
            if (sysUser == null)
                throw new ApiException("请绑定微信");
            //判断是否可以使用微信登录
            if (!sysUser.AllowWechatLogin)
                throw new ApiException("当前用户无法使用微信登录");
            //判断用户是否停用
            if (sysUser.UserStatus == false)
                throw new ApiException("用户已停用");
            //更新NickName
            DbOpe_sys_user.Instance.UpdateNickNameById(sysUser.Id, Info.nickname);
            //生成token并返回
            return OperateAfterLogin(sysUser, httpContext);
        }

        /// <summary>
        /// 通过微信扫码绑定微信。
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public void BindWeChat(string code)
        {
            WechatInfo Info = BLL_WeiXinHelper.Instance.getWeixinUserInfo(code);
            var openId = Info.unionid;
            if (DbOpe_sys_user.Instance.IsWeChatIsBind(UserId))
            {
                throw new ApiException("当前用户已绑定微信,无法进行绑定");
            }
            //执行绑定
            DbOpe_sys_user.Instance.BindWeChat(UserId, openId, Info.nickname);

            DbOpe_sys_user.Instance.SaveQueues();
        }

        /// <summary>
        /// 检测微信扫码绑定微信。
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public bool CheckBindWeChat()
        {
            //执行绑定
            return DbOpe_sys_user.Instance.IsWeChatIsBind(UserId);
        }

        /// <summary>
        /// 获取微信公众号二维码。
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public string GetBindWxImg()
        {
            return BLL_GtisOpe.Instance.GetDemoBindWxImg(UserId).Result;
        }

        /// <summary>
        /// 解除绑定微信
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public void UnBindWeChat()
        {
            if (!DbOpe_sys_user.Instance.IsWeChatIsBind(UserId))
            {
                throw new ApiException("当前用户已未绑定微信,无法解除绑定");
            }
            //执行绑定
            DbOpe_sys_user.Instance.UnBindWeChat(UserId);

            DbOpe_sys_user.Instance.SaveQueues();
        }

        ///// <summary>
        ///// 根据Openid获取用户信息，验证用户信息的有效性，根据登录规则判定用户是否可以登录。
        ///// </summary>
        ///// <param name="openid"></param>
        ///// <param name="httpContext"></param>
        ///// <returns></returns>
        ///// <exception cref="ApiException"></exception>
        //public string LoginByOpenid(string openid, HttpContext httpContext)
        //{
        //    WechatInfo Info = BLL_WeiXinHelper.Instance.GetUnionid(openid);
        //    string openId = Info.unionid;
        //    var sysUser = DbOpe_sys_user.Instance.GetUserByWxOpenId(openId);
        //    if (sysUser == null)
        //        throw new ApiException("请绑定微信");
        //    //判断是否可以使用微信登录
        //    if (!sysUser.AllowWechatLogin)
        //        throw new ApiException("当前用户无法使用微信登录");
        //    //判断用户是否停用
        //    if (sysUser.UserStatus == false)
        //        throw new ApiException("用户已停用");
        //    //生成token并返回
        //    return OperateAfterLogin(sysUser, httpContext);
        //}

        /// <summary>
        /// 登录验证后的操作，记录公私钥缓存，记录sysUser常量，记录可访问接口缓存，生成token
        /// 240903 增加角色权限ip验证
        /// </summary>
        /// <param name="sysUser"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        private string OperateAfterLogin(UserBasicInfo sysUser, HttpContext httpContext)
        {
            //操作人使用ip地址
            string ip = NetUtil.GetIp_Nginx(httpContext);
            if (!DbOpe_sys_form_id.Instance.CheckRoleIpAuth(ip, sysUser.Id, ""))
            {
                throw new ApiException("当前IP禁止访问:(" + ip + ")");
            }
            var IsMobile = httpContext.Request.Headers["IsMobile"].ToString().ToBool();
            //非客户经理的后台管理角色无法登录手机端（开发环境除外）
            //if (IsMobile && AppSettings.Env != Enum_SystemSettingEnv.Debug)
            //{
            //    throw new ApiException("当前用户无移动端系统登录权限");
            //}
            //生成用户私钥存入Redis
            (string publicKey, string privateKey) = EncryptUtil.GetRSAKey_Pem();
            RedisCache.RSAKey.SavePrivateKey(sysUser.Id, IsMobile, publicKey, privateKey);
            //存储用户基本信息
            RedisCache.UserInfo.SaveUser(sysUser);
            //存储用户可操作的方法的缓存
            var interfaces = DbOpe_sys_form.Instance.GetFormIdsByUserId(sysUser.Id);
            var buttons = DbOpe_sys_form.Instance.GetFormButtonsByUserId(sysUser.Id);
            var menus = DbOpe_sys_form.Instance.GetFormMenusByUserId(sysUser.Id);
            var datas = DbOpe_sys_form_datacondition.Instance.GetFormDataConditionsByUserId(sysUser.Id);
            RedisCache.UserInterfaceRight.SaveUserInterfaceRight(sysUser.Id, interfaces);
            RedisCache.UserMenuRight.SaveUserMenuRight(sysUser.Id, menus);
            RedisCache.UserButtonRight.SaveUserButtonRight(sysUser.Id, buttons);
            RedisCache.UserDataRight.SaveUserDataRight(sysUser.Id, datas);
            DbOpe_sys_user.Instance.UpdateLastLoginIPAndDate(sysUser.Id, ip);
            //生成并返回token
            int hasSupplementInfo = 0;
            if (sysUser.OrganizationId == Guid.Empty.ToString())
                hasSupplementInfo = 1;
            else
                hasSupplementInfo = (sysUser.HasSupplementInfo && !string.IsNullOrEmpty(sysUser.Telephone) && !string.IsNullOrEmpty(sysUser.IDNo) && !string.IsNullOrEmpty(sysUser.Email)) ? 1 : 0;
            var token = new TokenModel { id = sysUser.Id, suppled = hasSupplementInfo };
            TokenModel.SetTokenModel(token);
            var retToken = JwtHelper.CreateToken(token);
            //存储用户token
            RedisCache.UserToken.SaveUserToken(sysUser.Id, IsMobile, retToken);
            //存储用户静默截止时间
            RedisCache.SilenceTime.SetSilenceTime(sysUser.Id, IsMobile);
            return retToken;
        }

        /// <summary>
        /// 修改手机号--发送验证码
        /// </summary>
        /// <param name="getUpdatePhoneVerificationCode_In"></param>
        /// <exception cref="ApiException"></exception>
        public void GetUpdatePhoneVerificationCode(GetUpdatePhoneVerificationCode_In getUpdatePhoneVerificationCode_In)
        {
            //根据手机号获取用户信息
            var sysUser = DbOpe_sys_user.Instance.GetDataById(UserId);
            //判断手机号是否存在,可能未注册或删除
            if (sysUser == null)
                throw new ApiException("用户不存在");
            /* throw new ApiException("手机号或邮箱地址不存在");*/
            if (sysUser.Telephone.IsNullOrEmpty())
                throw new ApiException("手机号不存在");
            //判断账户是否停用
            if (!sysUser.UserStatus)
                throw new ApiException("用户已停用");
            //验证是否存在缓存
            if (RedisCache.LoginCode.CheckLoginCodeExist(sysUser.Telephone + EnumLoginCode.UpdateOldPhone.ToString()))
                throw new ApiException("已存在缓存验证码");
            //生成验证码
            var code = new RandomUtil(4, Enum_RandomFormat.OnlyNumber).GetRandom().ToString();
            //发送短信验证码
            Com_PhoneHelper.SendLoginCheckCode(sysUser.Telephone, code);
            //存入缓存
            RedisCache.LoginCode.SaveVerifyCode(sysUser.Telephone, EnumLoginCode.UpdateOldPhone, code);
        }

        /// <summary>
        /// 修改手机号--校验验证码
        /// </summary>
        /// <param name="checkUpdatePhoneVerifyCode_In"></param>
        /// <exception cref="ApiException"></exception>
        public void CheckUpdatePhoneVerifyCode(CheckUpdatePhoneVerifyCode_In checkUpdatePhoneVerifyCode_In)
        {
            //根据手机号获取用户信息
            var sysUser = DbOpe_sys_user.Instance.GetDataById(UserId);
            //判断手机号是否存在,可能未注册或删除
            if (sysUser == null)
                throw new ApiException("用户不存在");
            /* throw new ApiException("手机号或邮箱地址不存在");*/
            if (sysUser.Telephone.IsNullOrEmpty())
                throw new ApiException("手机号不存在");
            //判断账户是否停用
            if (!sysUser.UserStatus)
                throw new ApiException("用户已停用");

            var redisKey = sysUser.Telephone + EnumLoginCode.UpdateOldPhone.ToString();
            string code = string.Empty;
            if (RedisCache.LoginCode.CheckLoginCodeExist(redisKey))
                code = RedisCache.LoginCode.CheckLoginCode(redisKey);//从缓存中获取验证码
            if (string.IsNullOrEmpty(code) || !checkUpdatePhoneVerifyCode_In.Code.Equals(code))
                throw new ApiException("验证码不正确");
        }

        /// <summary>
        /// 修改手机号--发送验证码
        /// </summary>
        /// <param name="getUpdateNewPhoneVerificationCode_In"></param>
        /// <exception cref="ApiException"></exception>
        public void GetUpdateNewPhoneVerificationCode(GetUpdateNewPhoneVerificationCode_In getUpdateNewPhoneVerificationCode_In)
        {
            //验证手机号是否重复
            if (DbOpe_sys_user.Instance.CheckUserPhone(getUpdateNewPhoneVerificationCode_In.Telephone, ""))
                throw new ApiException("该手机号已被注册");
            //根据手机号获取用户信息
            var sysUser = DbOpe_sys_user.Instance.GetDataById(UserId);
            //判断手机号是否存在,可能未注册或删除
            if (sysUser == null)
                throw new ApiException("用户不存在");
            /* throw new ApiException("手机号或邮箱地址不存在");*/
            if (sysUser.Telephone.IsNullOrEmpty())
                throw new ApiException("手机号不存在");
            //判断账户是否停用
            if (!sysUser.UserStatus)
                throw new ApiException("用户已停用");
            //验证是否存在缓存
            if (RedisCache.LoginCode.CheckLoginCodeExist(getUpdateNewPhoneVerificationCode_In.Telephone + EnumLoginCode.UpdateNewPhone.ToString()))
                throw new ApiException("已存在缓存验证码");
            //生成验证码
            var code = new RandomUtil(4, Enum_RandomFormat.OnlyNumber).GetRandom().ToString();
            //发送短信验证码
            Com_PhoneHelper.SendLoginCheckCode(getUpdateNewPhoneVerificationCode_In.Telephone, code);
            //存入缓存
            RedisCache.LoginCode.SaveVerifyCode(getUpdateNewPhoneVerificationCode_In.Telephone, EnumLoginCode.UpdateNewPhone, code);
        }

        /// <summary>
        /// 通过手机号码修改手机号码,验证手机号码/邮箱地址的验证码的有效性，然后根据手机号码，验证用户信息的有效性，如果用户信息有效则修改手机号。
        /// </summary>
        /// <param name="updateNewPhoneByVerifyCode_In"></param>
        /// <exception cref="ApiException"></exception>
        public void UpdateNewPhoneByVerifyCode(UpdateNewPhoneByVerifyCode_In updateNewPhoneByVerifyCode_In)
        {
            //验证手机号是否重复
            if (DbOpe_sys_user.Instance.CheckUserPhone(updateNewPhoneByVerifyCode_In.Telephone, ""))
                throw new ApiException("该手机号已被注册");
            //根据手机号获取用户信息
            var sysUser = DbOpe_sys_user.Instance.GetDataById(UserId);
            //判断手机号是否存在,可能未注册或删除
            if (sysUser == null)
                throw new ApiException("用户不存在");
            /* throw new ApiException("手机号或邮箱地址不存在");*/
            if (sysUser.Telephone.IsNullOrEmpty())
                throw new ApiException("手机号不存在");
            //判断账户是否停用
            if (!sysUser.UserStatus)
                throw new ApiException("用户已停用");

            //var redisKey = sysUser.Telephone + EnumLoginCode.UpdateOldPhone.ToString();
            //string code = string.Empty;
            //if (RedisCache.LoginCode.CheckLoginCodeExist(redisKey))
            //    code = RedisCache.LoginCode.CheckLoginCode(redisKey);//从缓存中获取验证码
            //if (string.IsNullOrEmpty(code) || !checkUpdatePhoneVerifyCode_In.Code.Equals(code))
            //    throw new ApiException("验证码不正确");

            //获取缓存验证码
            var code = RedisCache.LoginCode.CheckLoginCode(updateNewPhoneByVerifyCode_In.Telephone + EnumLoginCode.UpdateNewPhone.ToString());
            //比对验证码
            if (string.IsNullOrEmpty(code) || !updateNewPhoneByVerifyCode_In.Code.Equals(code))
                throw new ApiException("无接口访问权限");
            //修改密码
            DbOpe_sys_user.Instance.UpdateTelephone(updateNewPhoneByVerifyCode_In.Telephone, UserId);
            //清除缓存验证码
            RedisCache.LoginCode.DeleteVerifyCode(updateNewPhoneByVerifyCode_In.Telephone, EnumLoginCode.UpdateNewPhone);
            //记录系统消息
            DbOpe_sys_messages.Instance.AddModifyTelephone(sysUser.Id);
        }

        /// <summary>
        /// 密码找回--发送验证码
        /// </summary>
        /// <param name="getRetPwdVerifyCode_In"></param>
        /// <exception cref="ApiException"></exception>
        public void GetRetrievePwdVerificationCode(GetRetrievePwdVerificationCode_In getRetPwdVerifyCode_In)
        {
            //根据手机号获取用户信息
            var sysUser = DbOpe_sys_user.Instance.GetUserByVerifyCode(getRetPwdVerifyCode_In.LoginName);
            //判断手机号是否存在,可能未注册或删除
            if (sysUser == null)
                throw new ApiException("手机号或邮箱地址不存在");
            //判断账户是否停用
            if (!sysUser.UserStatus)
                throw new ApiException("用户已停用");
            //验证是否存在缓存
            if (RedisCache.LoginCode.CheckLoginCodeExist(getRetPwdVerifyCode_In.LoginName + getRetPwdVerifyCode_In.Type.ToString()))
                throw new ApiException("已存在缓存验证码");
            //生成验证码
            var code = new RandomUtil(4, Enum_RandomFormat.OnlyNumber).GetRandom().ToString();
            //发送短信或邮件
            if (getRetPwdVerifyCode_In.Type == EnumLoginCode.RetrievePwdByPhone)
            {
                //发送短信验证码
                Com_PhoneHelper.SendLoginCheckCode(getRetPwdVerifyCode_In.LoginName, code);
            }
            if (getRetPwdVerifyCode_In.Type == EnumLoginCode.RetrievePwdByEmail)
            {
                //发送邮件验证码123
            }
            //存入缓存
            RedisCache.LoginCode.SaveVerifyCode(getRetPwdVerifyCode_In.LoginName, getRetPwdVerifyCode_In.Type, code);
        }

        /// <summary>
        /// 找回密码--校验验证码
        /// </summary>
        /// <param name="checkRetrievePwdVerifyCode_In"></param>
        /// <exception cref="ApiException"></exception>
        public void CheckRetrievePwdVerifyCode(CheckRetrievePwdVerifyCode_In checkRetrievePwdVerifyCode_In)
        {
            var redisKey = checkRetrievePwdVerifyCode_In.LoginName + checkRetrievePwdVerifyCode_In.Type.ToString();
            string code = string.Empty;
            if (RedisCache.LoginCode.CheckLoginCodeExist(redisKey))
                code = RedisCache.LoginCode.CheckLoginCode(redisKey);//从缓存中获取验证码
            if (string.IsNullOrEmpty(code) || !checkRetrievePwdVerifyCode_In.Code.Equals(code))
                throw new ApiException("验证码不正确");
        }
        /// <summary>
        /// 通过手机号码重置用户密码,验证手机号码/邮箱地址的验证码的有效性，然后根据手机号码/邮箱地址获取用户信息，验证用户信息的有效性，如果用户信息有效则修改密码，并重置用户登录状态。
        /// </summary>
        /// <param name="retPwdByVerifyCode_In"></param>
        /// <exception cref="ApiException"></exception>
        public void RetrievePwdByVerifyCode(RetrievePwdByVerifyCode_In retPwdByVerifyCode_In)
        {
            //获取缓存验证码
            var code = RedisCache.LoginCode.CheckLoginCode(retPwdByVerifyCode_In.LoginName + retPwdByVerifyCode_In.Type.ToString());
            //比对验证码
            if (string.IsNullOrEmpty(code) || !retPwdByVerifyCode_In.Code.Equals(code))
                throw new ApiException("无接口访问权限");
            //获取用户的登录名username
            UserBasicInfo user = DbOpe_sys_user.Instance.GetUserByPwd(retPwdByVerifyCode_In.LoginName);
            var userName = user.UserName.ToString();
            //对新密码进行md5加密
            var pwd = EncryptUtil.GetMd5(userName + retPwdByVerifyCode_In.PassWord);
            //修改密码
            DbOpe_sys_user.Instance.UpdatePwdByPhoneOrEmail(retPwdByVerifyCode_In.LoginName, pwd);
            //清除缓存验证码
            RedisCache.LoginCode.DeleteVerifyCode(retPwdByVerifyCode_In.LoginName, retPwdByVerifyCode_In.Type);
            //记录系统消息
            DbOpe_sys_messages.Instance.AddModifyPassWord(user.Id);
        }

        /// <summary>
        /// 通过token获取用户基本信息(用户公钥)
        /// </summary>
        /// <returns></returns>
        public GetLoginBasicInfo_Out GetLoginBasicInfo(HttpContext context)
        {
            var isMobile = context.Request.Headers["IsMobile"].ToString().ToBool();
            var user = DbOpe_sys_user.Instance.GetDataById(UserTokenInfo.id);
            GetLoginBasicInfo_Out ret = new GetLoginBasicInfo_Out();
            ret.publicKey = RedisCache.RSAKey.GetPublicKey(UserTokenInfo.id, isMobile);
            /*if (user.OrganizationId != Guid.Empty.ToString())
                ret.HasSupplementInfo = (UserTokenInfo.suppled == 1) && !string.IsNullOrEmpty(user.Email) && !string.IsNullOrEmpty(user.IDNo);
            else
                ret.HasSupplementInfo = true;*/
            ret.HasSupplementInfo = UserTokenInfo.suppled == 1;
            ret.Email = user.Email;
            ret.IDNo = user.IDNo;
            ret.UserName = user.UserName;
            ret.Telephone = user.Telephone;
            ret.Version = "20250217";
            return ret;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpContext"></param>
        /// <param name="response"></param>
        public async void CheckUserSilenceTimeSSE(HttpContext httpContext, HttpResponse response)
        {
            //判断是否是手机端
            var IsMobile = httpContext.Request.Headers["IsMobile"].ToString().ToBool();
            //判断是否超过静默时间
            var flag = RedisCache.SilenceTime.CheckSilenceTime(UserId, IsMobile) ? "true" : "false";
            //定义下次发送消息的间隔时间
            var retry = new TimeSpan();
            //当前未超过静默时间, 计算当前时间和静默截止时间的差值，用为下一次发送SSE消息的时间
            if (flag == "false")
            {
                //获取当前用户的静默截止时间
                var silencetime = RedisCache.SilenceTime.GetSilenceTime(UserId, IsMobile).ToDateTime();
                retry = silencetime - DateTime.Now;
            }

            await NetUtil.SendSseMessageAsync(response, UserId, flag, "message", retry);

            /*response.ContentType = "text/event-stream";
            string writeString = $"retry:{timeSpan}\nevent:{"message"}\nid:{UserId}\ndata:{flag}\n\n";
            var writeBytes = writeString.ToByteArr(Encoding.UTF8);//必须用utf8格式的内容
            await response.Body.WriteAsync(writeBytes, 0, writeBytes.Length);
            await response.Body.FlushAsync();*/


        }

        /// <summary>
        /// 登出操作，删除Redis中的token
        /// </summary>
        /// <param name="httpContext"></param>
        public void LogOut(HttpContext httpContext)
        {
            //判断是否是手机端
            var IsMobile = httpContext.Request.Headers["IsMobile"].ToString().ToBool();
            //删除Redis中的token缓存
            RedisCache.UserToken.DeleteUserToken(UserId, IsMobile);
        }

        public bool OperateAfterLoginTemp()
        {
            var IsMobile = false;
            var sysUserList = DbOpe_sys_user.Instance.GetDataAllList().Where(r => r.UserName != "sysadmin").Select<UserBasicInfo>().ToList();
            foreach (var sysUser in sysUserList)
            {
                //生成用户私钥存入Redis
                (string publicKey, string privateKey) = EncryptUtil.GetRSAKey_Pem();
                RedisCache.RSAKey.SavePrivateKey(sysUser.Id, IsMobile, publicKey, privateKey);
                //存储用户基本信息
                RedisCache.UserInfo.SaveUser(sysUser);
                //存储用户可操作的方法的缓存
                var interfaces = DbOpe_sys_form.Instance.GetFormIdsByUserId(sysUser.Id);
                var buttons = DbOpe_sys_form.Instance.GetFormButtonsByUserId(sysUser.Id);
                var menus = DbOpe_sys_form.Instance.GetFormMenusByUserId(sysUser.Id);
                var datas = DbOpe_sys_form_datacondition.Instance.GetFormDataConditionsByUserId(sysUser.Id);
                RedisCache.UserInterfaceRight.SaveUserInterfaceRight(sysUser.Id, interfaces);
                RedisCache.UserMenuRight.SaveUserMenuRight(sysUser.Id, menus);
                RedisCache.UserButtonRight.SaveUserButtonRight(sysUser.Id, buttons);
                RedisCache.UserDataRight.SaveUserDataRight(sysUser.Id, datas);

                //生成并返回token
                var token = new TokenModel { id = sysUser.Id };
                TokenModel.SetTokenModel(token);
                var retToken = JwtHelper.CreateToken(token);
                //存储用户token
                RedisCache.UserToken.SaveUserToken(sysUser.Id, IsMobile, retToken);
                //存储用户静默截止时间
                RedisCache.SilenceTime.SetSilenceTime(sysUser.Id, IsMobile);
            }

            return true;
        }

        /// <summary>
        /// 验证token是否有效
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="token"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        public bool VerifyToken(string userId, string token, HttpContext httpContext)
        {
            //判断是否是手机端
            var IsMobile = httpContext.Request.Headers["IsMobile"].ToString().ToBool();
            //判断是否未超过静默时间 && token值正确
            if (!RedisCache.SilenceTime.CheckSilenceTime(userId, IsMobile) && RedisCache.UserToken.CheckUserToken(userId, IsMobile, token))
                return true;
            else
                return false;
        }

        /// <summary>
        /// 通过钉钉免登录code进行登录
        /// </summary>
        /// <param name="code">钉钉免登录code</param>
        /// <param name="httpContext">HTTP上下文</param>
        /// <returns>登录token</returns>
        /// <exception cref="ApiException">API异常</exception>
        public string LoginByDingTalkCode(string code, HttpContext httpContext)
        {
            if (string.IsNullOrEmpty(code))
                throw new ApiException("钉钉免登录code不能为空");

            // 同时获取钉钉用户ID和unionId
            var (userId, unionId) = DingTalkService.GetUserIdAndUnionId(code);
            if (string.IsNullOrEmpty(userId))
                throw new ApiException("获取钉钉用户ID失败");



            // 先查询是否已经绑定了系统用户
            var sysUser = DbOpe_sys_user.Instance.GetUserByDingDingOpenID(userId);
            if (sysUser != null)
            {
                // 已绑定，直接登录
                return OperateAfterLogin(sysUser, httpContext);
            }

            // 未绑定，通过身份证号匹配
            string idNo = DingTalkService.GetIDNo(userId);
            if (string.IsNullOrEmpty(idNo))
                throw new ApiException("未能获取到您的身份证信息，请联系管理员或HR确认您的钉钉智能人事花名册中是否已填写身份证号");

            // 根据身份证号查找用户
            sysUser = DbOpe_sys_user.Instance.GetUserByIDNo(idNo);
            if (sysUser == null)
                throw new ApiException("未找到与您身份证号匹配的系统用户，请联系管理员");

            // 绑定钉钉用户ID和unionid
            DbOpe_sys_user.Instance.BindDingDingOpenID(sysUser.Id, userId, unionId);

            // 登录
            return OperateAfterLogin(sysUser, httpContext);
        }

        /// <summary>
        /// 绑定钉钉账号
        /// </summary>
        /// <param name="code">钉钉免登录code</param>
        /// <exception cref="ApiException">API异常</exception>
        public void BindDingTalk(string code)
        {
            if (string.IsNullOrEmpty(code))
                throw new ApiException("钉钉免登录code不能为空");

            // 同时获取钉钉用户ID和unionId
            var (userId, unionId) = DingTalkService.GetUserIdAndUnionId(code);
            if (string.IsNullOrEmpty(userId))
                throw new ApiException("获取钉钉用户ID失败");

            // 检查是否已被其他用户绑定
            var existUser = DbOpe_sys_user.Instance.GetUserByDingDingOpenID(userId);
            if (existUser != null && existUser.Id != UserTokenInfo.id)
                throw new ApiException("该钉钉账号已被其他用户绑定");

            // 绑定钉钉用户ID和unionId
            DbOpe_sys_user.Instance.BindDingDingOpenID(UserTokenInfo.id, userId, unionId);
        }

        /// <summary>
        /// 解除绑定钉钉
        /// </summary>
        /// <exception cref="ApiException">API异常</exception>
        public void UnBindDingTalk()
        {
            // 解除绑定
            DbOpe_sys_user.Instance.UnBindDingDingOpenID(UserTokenInfo.id);
        }
    }
}
