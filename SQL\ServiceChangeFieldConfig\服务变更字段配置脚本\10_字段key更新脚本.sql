-- 服务变更字段配置 - 字段key更新脚本
-- 执行日期: 2025-01-29
-- 根据key变动.md更新字段名称

-- ================================
-- 更新字段key映射
-- ================================

-- 1. 更新AccountList为UserList
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'UserList', FieldName = '用户列表'
WHERE FieldKey = 'AccountList';

-- 2. 更新PersonalServiceDays为PerlongServiceDays
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'PerlongServiceDays', FieldName = '延长总天数'
WHERE FieldKey = 'PersonalServiceDays';

-- 3. 更新SelfPaidDays为PrivateServiceDays
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'PrivateServiceDays', FieldName = '个人服务天数'
WHERE FieldKey = 'SelfPaidDays';

-- 4. 更新CounponDetailIdList为CouponList
UPDATE crm_service_change_reason_field_config 
SET FieldKey = 'CouponList', FieldName = '优惠券列表'
WHERE FieldKey = 'CounponDetailIdList';

-- ================================
-- 更新TriggerFields中的字段引用
-- ================================

-- 1. 更新TriggerFields中的AccountList为UserList
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'AccountList', 'UserList')
WHERE TriggerFields LIKE '%AccountList%';

-- 2. 更新TriggerFields中的PersonalServiceDays为PerlongServiceDays
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'PersonalServiceDays', 'PerlongServiceDays')
WHERE TriggerFields LIKE '%PersonalServiceDays%';

-- 3. 更新TriggerFields中的SelfPaidDays为PrivateServiceDays
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'SelfPaidDays', 'PrivateServiceDays')
WHERE TriggerFields LIKE '%SelfPaidDays%';

-- 4. 更新TriggerFields中的CounponDetailIdList为CouponList
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'CounponDetailIdList', 'CouponList')
WHERE TriggerFields LIKE '%CounponDetailIdList%';

-- ================================
-- 添加新的通用字段
-- ================================

-- 添加IsUrgent字段 - 申请时可修改，审核时只读
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 1, 'IsUrgent', '是否加急', 200, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 1, 'IsUrgent', '是否加急', 200, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 2, 'IsUrgent', '是否加急', 200, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 2, 'IsUrgent', '是否加急', 200, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 3, 'IsUrgent', '是否加急', 200, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 3, 'IsUrgent', '是否加急', 200, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 4, 'IsUrgent', '是否加急', 200, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 4, 'IsUrgent', '是否加急', 200, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 5, 'IsUrgent', '是否加急', 200, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 5, 'IsUrgent', '是否加急', 200, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 6, 'IsUrgent', '是否加急', 200, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 6, 'IsUrgent', '是否加急', 200, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 7, 'IsUrgent', '是否加急', 200, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 7, 'IsUrgent', '是否加急', 200, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 8, 'IsUrgent', '是否加急', 200, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 8, 'IsUrgent', '是否加急', 200, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 9, 'IsUrgent', '是否加急', 200, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 9, 'IsUrgent', '是否加急', 200, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0);

-- 添加RenewContractNum字段 - 申请时可修改，审核时只读
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 1, 'RenewContractNum', '续约合同号', 201, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 1, 'RenewContractNum', '续约合同号', 201, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 2, 'RenewContractNum', '续约合同号', 201, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 2, 'RenewContractNum', '续约合同号', 201, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 3, 'RenewContractNum', '续约合同号', 201, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 3, 'RenewContractNum', '续约合同号', 201, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 4, 'RenewContractNum', '续约合同号', 201, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 4, 'RenewContractNum', '续约合同号', 201, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 5, 'RenewContractNum', '续约合同号', 201, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 5, 'RenewContractNum', '续约合同号', 201, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 6, 'RenewContractNum', '续约合同号', 201, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 6, 'RenewContractNum', '续约合同号', 201, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 7, 'RenewContractNum', '续约合同号', 201, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 7, 'RenewContractNum', '续约合同号', 201, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 8, 'RenewContractNum', '续约合同号', 201, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 8, 'RenewContractNum', '续约合同号', 201, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 9, 'RenewContractNum', '续约合同号', 201, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 9, 'RenewContractNum', '续约合同号', 201, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0);

-- 添加Remark字段 - 申请时可修改，审核时只读
INSERT INTO crm_service_change_reason_field_config
(Id, ServiceType, ChangeReasonEnum, FieldKey, FieldName, DisplayOrder, IsActive, FieldPermission, ApplyScenario, TriggerFields, Remark, CreateDate, CreateUser, UpdateDate, UpdateUser, Deleted)
VALUES
(UUID(), 5, 1, 'Remark', '申请备注', 202, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 1, 'Remark', '申请备注', 202, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 2, 'Remark', '申请备注', 202, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 2, 'Remark', '申请备注', 202, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 3, 'Remark', '申请备注', 202, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 3, 'Remark', '申请备注', 202, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 4, 'Remark', '申请备注', 202, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 4, 'Remark', '申请备注', 202, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 5, 'Remark', '申请备注', 202, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 5, 'Remark', '申请备注', 202, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 6, 'Remark', '申请备注', 202, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 6, 'Remark', '申请备注', 202, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 7, 'Remark', '申请备注', 202, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 7, 'Remark', '申请备注', 202, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 8, 'Remark', '申请备注', 202, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 8, 'Remark', '申请备注', 202, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 9, 'Remark', '申请备注', 202, 1, 1, 'apply', NULL, '申请时可修改', NOW(), 'system', NULL, 'system', 0),
(UUID(), 5, 9, 'Remark', '申请备注', 202, 1, 2, 'audit', NULL, '审核时只读', NOW(), 'system', NULL, 'system', 0);

-- ================================
-- 更新TriggerFields中的新字段引用
-- ================================

-- 更新所有TriggerFields中的PersonalServiceDays为PerlongServiceDays
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'PersonalServiceDays', 'PerlongServiceDays')
WHERE TriggerFields LIKE '%PersonalServiceDays%';

-- 更新所有TriggerFields中的SelfPaidDays为PrivateServiceDays
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'SelfPaidDays', 'PrivateServiceDays')
WHERE TriggerFields LIKE '%SelfPaidDays%';

-- 更新所有TriggerFields中的CounponDetailIdList为CouponList
UPDATE crm_service_change_reason_field_config 
SET TriggerFields = REPLACE(TriggerFields, 'CounponDetailIdList', 'CouponList')
WHERE TriggerFields LIKE '%CounponDetailIdList%';

-- ================================
-- 验证更新结果
-- ================================

SELECT '字段key更新完成！' AS message;

-- 显示更新后的字段统计
SELECT 
    FieldKey,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT ChangeReasonEnum ORDER BY ChangeReasonEnum) as change_reasons
FROM crm_service_change_reason_field_config 
WHERE FieldKey IN ('UserList', 'PerlongServiceDays', 'PrivateServiceDays', 'CouponList', 'IsUrgent', 'RenewContractNum', 'Remark')
GROUP BY FieldKey
ORDER BY FieldKey; 