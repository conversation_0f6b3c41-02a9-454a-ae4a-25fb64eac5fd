﻿using System.ComponentModel;
using CRM2_API.BLL.Notice;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.Notice;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;


namespace CRM2_API.Controllers
{
    /// <summary>
    /// 公告通知控制器
    /// </summary>
    [Description("公告通知控制器")]
    public class NoticeController : MyControllerBase
    {
        public NoticeController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 根据查询条件获取公告通知列表。
        /// </summary>
        /// <param name="searchNoticeListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchNoticeList_Out> SearchNoticeList(SearchNoticeList_In searchNoticeListIn)
        {
            int total = 0;
            var list =  DbOpe_crm_notice.Instance.SearchNoticeList(searchNoticeListIn, ref total);
            return GetApiTableOut(list, total);
        }
        
        /// <summary>
        /// 根据公告通知Id获取公告通知信息
        /// </summary>
        /// <param name="getNoticeByIdIn"></param>
        /// <returns></returns>
        [HttpPost]
        public GetNoticeById_Out GetNoticeById(GetNoticeById_In getNoticeByIdIn)
        {
            if (getNoticeByIdIn is null || string.IsNullOrEmpty(getNoticeByIdIn.Id))
            {
                throw new ApiException("");
            }
            return DbOpe_crm_notice.Instance.GetNoticeById(getNoticeByIdIn.Id);
        }

        /// <summary>
        /// 添加公告通知信息
        /// </summary>
        /// <param name="addNoticeIn"></param>
        /// <returns></returns>
        [HttpPost]
        public AddNotice_Out AddNotice([FromForm] AddNotice_In addNoticeIn)
        {
            return BLL_Notice.Instance.AddNotice(addNoticeIn);
        }

        /// <summary>
        /// 修改公告通知信息
        /// </summary>
        /// <param name="updateNoticeIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdateNotice_Out UpdateNotice([FromForm] UpdateNotice_In updateNoticeIn)
        {
            if (updateNoticeIn is null || string.IsNullOrEmpty(updateNoticeIn.Id))
            {
                throw new ApiException("公告通知表Id不可为空");
            }
            return BLL_Notice.Instance.UpdateNotice(updateNoticeIn);
        }
        
        /// <summary>
        /// 删除公告通知信息
        /// </summary>
        /// <param name="deleteNoticeIn"></param>
        /// <returns></returns>
        [HttpPost]
        public DeleteNotice_Out DeleteNotice(DeleteNotice_In deleteNoticeIn)
        {
            if (deleteNoticeIn == null || string.IsNullOrEmpty(deleteNoticeIn.Id))
            {
                throw new ApiException("公告通知表Id不可为空");
            }
            return BLL_Notice.Instance.DeleteNotice(deleteNoticeIn.Id);
        }

        /// <summary>
        /// 撤销公告通知信息，当发布状态为发布状态，可操作，撤销后状态变更为撤销
        /// </summary>
        /// <param name="revokeNoticeIn"></param>
        /// <returns></returns>
        [HttpPost]
        public RevokeNotice_Out RevokeNotice(RevokeNotice_In revokeNoticeIn)
        {
            if (revokeNoticeIn == null || string.IsNullOrEmpty(revokeNoticeIn.Id))
            {
                throw new ApiException("公告通知表Id不可为空");
            }
            return BLL_Notice.Instance.RevokeNotice(revokeNoticeIn.Id);
        }
        
        /// <summary>
        /// 设置已读公告通知信息。
        /// </summary>
        /// <param name="setIsReadNoticeIn"></param>
        /// <returns></returns>
        [HttpPost]
        public SetIsReadNotice_Out SetIsReadNotice(SetIsReadNotice_In setIsReadNoticeIn)
        {
            if (setIsReadNoticeIn == null || string.IsNullOrEmpty(setIsReadNoticeIn.Id))
            {
                throw new ApiException("公告通知表Id不可为空");
            }
            return DbOpe_crm_notice_browse.Instance.SetIsReadNotice(setIsReadNoticeIn.Id);
        }

        /// <summary>
        /// 根据通告id获取已读的人员列表
        /// </summary>
        /// <param name="getNoticeBrowseListByIdIn"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public List<GetNoticeBrowseListById_Out> GetNoticeBrowseListById(GetNoticeBrowseListById_In getNoticeBrowseListByIdIn)
        {
            if (getNoticeBrowseListByIdIn == null || string.IsNullOrEmpty(getNoticeBrowseListByIdIn.Id))
            {
                throw new ApiException("公告通知表Id不可为空");
            }

            return DbOpe_crm_notice_browse.Instance.GetNoticeBrowseListById(getNoticeBrowseListByIdIn.Id);
        }

        /// <summary>
        /// 根据用户id查询该用户发布的通告列表
        /// </summary>
        /// <param name="getNoticeListByUserId"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<GetNoticeListByUserId_Out> GetNoticeListByUserId(GetNoticeListByUserId_In getNoticeListByUserId)
        {
            if (getNoticeListByUserId is null || string.IsNullOrEmpty(getNoticeListByUserId.UserId))
            {
                throw new ApiException("用户主键不可为空");
            }
            int total = 0;
            var list = DbOpe_crm_notice.Instance.GetNoticeListByUserId(getNoticeListByUserId, ref total);
            return GetApiTableOut(list, total);
        }
        
        
        /// <summary>
        /// 获取文件[方案1]
        /// </summary>
        /// <param name="attId"></param>
        /// <returns></returns>
        [HttpGet,SkipAuthCheck, SkipIPCheck]
        public IActionResult DownLoad(string attId)
        {
            if (string.IsNullOrEmpty(attId))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "附件id不可为空!"
                });
            }
            return BLL_Notice.Instance.DownLoad(attId,Response);
        }
        
        /// <summary>
        /// 富文本上传图片[方案1]
        /// </summary>
        /// <param name="upload"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadImage([FromForm] NoticeTextImageUpload upload)
        {
            if (upload == null || string.IsNullOrEmpty(upload.NoticeId))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误,请检查参数!"
                });
            }
            return BLL_Notice.Instance.UploadImage(upload);
        }
        
        /// <summary>
        /// 获取文件[方案2]
        /// </summary>
        /// <param name="attId"></param>
        /// <param name="noticeId"></param>
        /// <returns></returns>
        [HttpGet,SkipAuthCheck, SkipIPCheck]
        public IActionResult DownLoadNew(string attId, string noticeId)
        {
            if (string.IsNullOrEmpty(attId) || string.IsNullOrEmpty(noticeId))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误!"
                });
            }
            return BLL_Notice.Instance.DownLoadNew(attId,noticeId,Response);
        }
        
        /// <summary>
        /// 富文本上传图片[方案2]
        /// </summary>
        /// <param name="upload"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult UploadImageNew([FromForm] NoticeTextImageUpload upload)
        {
            if (upload == null || upload.File == null)
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误,请检查参数!"
                });
            }
            return BLL_Notice.Instance.UploadImageNew(upload);
        }

        /// <summary>
        /// 富文本心跳数据保持
        /// </summary>
        /// <param name="noticeId"></param>
        /// <returns></returns>
        [HttpGet,SkipAuthCheck, SkipIPCheck]
        public IActionResult NoticeTextHeartbeat(string noticeId)
        {
            if (string.IsNullOrEmpty(noticeId))
            {
                return new JsonResult(new
                {
                    code = StatusCodes.Status404NotFound,
                    Msg = "参数错误,请检查参数!"
                });
            }
            return BLL_Notice.Instance.NoticeTextHeartbeat(noticeId);
        }
        
        /// <summary>
        /// 获取拥有公告通知的用户列表
        /// </summary>
        /// <returns></returns>
        [HttpPost,SkipRightCheck]
        public List<ApiDictionary> GetHaveNoticeUserList()
        {
            return DbOpe_crm_notice.Instance.GetHaveNoticeUserList();
        }
    }
}