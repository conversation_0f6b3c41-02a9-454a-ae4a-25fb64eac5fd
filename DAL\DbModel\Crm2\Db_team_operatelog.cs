﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("team_operatelog")]
    public class Db_team_operatelog
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:操作类型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OperateType {get;set;}

           /// <summary>
           /// Desc:迁移人员
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string MoveUser {get;set;}

           /// <summary>
           /// Desc:旧队伍
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OldTeam {get;set;}

           /// <summary>
           /// Desc:新队伍
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string NewTeam {get;set;}

           /// <summary>
           /// Desc:所属队伍
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string BelongTeam {get;set;}

           /// <summary>
           /// Desc:队伍负责人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ChargeUser {get;set;}

           /// <summary>
           /// Desc:顺序
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? Sort {get;set;}

           /// <summary>
           /// Desc:生效日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? EffectiveDate {get;set;}

           /// <summary>
           /// Desc:执行日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? OperateDate {get;set;}

           /// <summary>
           /// Desc:执行结果
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string OperateResult {get;set;}

    }
}
