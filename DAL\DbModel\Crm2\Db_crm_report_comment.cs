using System;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 工作报告评论表
    /// </summary>
    [SugarTable("crm_report_comment")]
    public partial class Db_crm_report_comment
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 报告ID
        /// </summary>
        public string ReportId { get; set; }

        /// <summary>
        /// 报告类型：1-日报，2-周报，3-月报
        /// </summary>
        public int ReportType { get; set; }

        /// <summary>
        /// 报告标题
        /// </summary>
        public string ReportTitle { get; set; }

        /// <summary>
        /// 报告创建用户ID
        /// </summary>
        public string ReportUserId { get; set; }

        /// <summary>
        /// 报告创建用户姓名
        /// </summary>
        public string ReportUserName { get; set; }

        /// <summary>
        /// 报告日期
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// 报告年份
        /// </summary>
        public int ReportYear { get; set; }

        /// <summary>
        /// 报告月份
        /// </summary>
        public int? ReportMonth { get; set; }

        /// <summary>
        /// 报告周数
        /// </summary>
        public int? ReportWeek { get; set; }

        /// <summary>
        /// 模块标题
        /// </summary>
        public string ModuleTitle { get; set; }

        /// <summary>
        /// 模块排序
        /// </summary>
        public int ModuleOrder { get; set; }

        /// <summary>
        /// 章节标题
        /// </summary>
        public string SectionTitle { get; set; }

        /// <summary>
        /// 子项排序
        /// </summary>
        public int SectionOrder { get; set; }

        /// <summary>
        /// 评论内容
        /// </summary>
        public string CommentText { get; set; }

        /// <summary>
        /// 评论类型：1-文本评论，2-模块标题评论，3-章节标题评论，4-数据项评论，5-整体报告评论
        /// </summary>
        public int CommentType { get; set; }

        /// <summary>
        /// 选中的文本内容（用于文本评论）
        /// </summary>
        public string SelectionText { get; set; }

        /// <summary>
        /// 选中文本的开始位置
        /// </summary>
        public int? SelectionStart { get; set; }

        /// <summary>
        /// 选中文本的结束位置
        /// </summary>
        public int? SelectionEnd { get; set; }

        /// <summary>
        /// 目标内容ID（关联到具体的内容项）
        /// </summary>
        public string ContentId { get; set; }

        /// <summary>
        /// 模块标识（如：workReview、clientFollow等）
        /// </summary>
        public string ModuleKey { get; set; }

        /// <summary>
        /// 章节标识（如：phoneCount、visitCount等）
        /// </summary>
        public string SectionKey { get; set; }

        /// <summary>
        /// 前端元素ID，用于精确定位评论位置
        /// </summary>
        public string TargetElementId { get; set; }



        /// <summary>
        /// 是否删除：0-否，1-是
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateDate { get; set; }
    }
} 