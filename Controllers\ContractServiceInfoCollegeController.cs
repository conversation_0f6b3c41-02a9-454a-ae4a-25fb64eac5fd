﻿using CRM2_API.BLL;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using static CRM2_API.Common.Filter.WorkLog;

namespace CRM2_API.Controllers
{
    [Description("合同服务管理-慧思学院")]
    public class ContractServiceInfoCollegeController : MyControllerBase
    {
        public ContractServiceInfoCollegeController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        /// <summary>
        /// 根据查询条件获取合同服务信息慧思学院申请信息列表
        /// </summary>
        /// <param name="search_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public ApiTableOut<SearchContractProductServiceInfoCollegeApplList_Out> SearchContractProductServiceInfoCollegeApplList(SearchContractProductServiceInfoCollegeApplList_In search_In)
        {
            int total = 0;
            var list = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.SearchContractProductServiceInfoCollegeApplList(search_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 根据申请id获取合同服务申请信息_慧思学院信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoApplyInfoCollegeByApplId_Out GetContractServiceInfoApplyInfoCollegeByApplId(string Id)
        {
            return BLL_ContractServiceCollege.Instance.GetContractServiceInfoApplyInfoCollegeByApplId(Id);
        }

        /// <summary>
        /// 撤销合同服务信息慧思学院申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void RevokeContractProductServiceInfoCollegeAudit(OperateContractProductServiceInfoCollegeAudit_In operate_In)
        {
            BLL_ContractServiceCollege.Instance.RevokeContractProductServiceInfoCollegeAudit(operate_In);
        }

        /// <summary>
        /// 作废合同服务信息慧思学院申请审核信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void VoidContractProductServiceInfoCollegeAudit(OperateContractProductServiceInfoCollegeAudit_In operate_In)
        {
            BLL_ContractServiceCollege.Instance.VoidContractProductServiceInfoCollegeAudit(operate_In);
        }

        /// <summary>
        /// 删除合同服务信息慧思学院申请信息
        /// </summary>
        /// <param name="operate_In"></param>
        [HttpPost, PreLog]
        public void DeleteContractProductServiceInfoCollegeAudit(OperateContractProductServiceInfoCollegeAudit_In operate_In)
        {
            BLL_ContractServiceCollege.Instance.DeleteContractProductServiceInfoCollegeAudit(operate_In);
        }

        /// <summary>
        /// 设置慧思学院用户状信息
        /// </summary>
        /// <param name="contacts_In"></param>
        [HttpPost, PreLog]
        public void SetCollegeContacts(SetCollegeContacts_In contacts_In)
        {
            BLL_ContractServiceCollege.Instance.SetCollegeContacts(contacts_In);
        }

        /// <summary>
        /// 根据申请Id批量修改服务用户状态-启用/停用
        /// </summary>
        /// <param name="applyList_In"></param>
        [HttpPost, PreLog]
        public void SetContractServiceInfoCollegeContactsByApplyIds(SetContractServiceInfoCollegeContactsByApplyIds_In applyList_In)
        {
            BLL_ContractServiceCollege.Instance.SetContractServiceInfoCollegeContactsByApplyIds(applyList_In);
        }

        /// <summary>
        /// 根据申请id获取合同服务信息_慧思学院信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoCollegeByApplId_Out GetContractServiceInfoCollegeByApplId(string Id)
        {
            //获取申请详细信息
            var apply = DbOpe_crm_contract_productserviceinfo_college_appl.Instance.GetContractServiceInfoCollegeByApplId(Id);
            //获取合同详细信息
            apply.ContractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(apply.ContractId);

            return apply;
        }
        
        /// <summary>
        /// 根据合同产品信息表id获取合同服务信息_慧思学院信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public GetContractServiceInfoCollegeByContractProductInfoId_Out GetContractServiceInfoCollegeByContractProductInfoId(string Id)
        {
            return DbOpe_crm_contract_serviceinfo_college.Instance.GetContractServiceInfoCollegeByContractProductInfoId(Id);
        }
        /// <summary>
        /// 验证开通账号是否存在重复
        /// </summary>
        /// <param name="check_In"></param>
        /// <returns></returns>
        [HttpPost, PreLog]
        public CheckHasSameValidContacts_Out CheckHasSameValidContacts(CheckHasSameValidContacts_In check_In)
        {
            return BLL_ContractServiceCollege.Instance.CheckHasSameValidContacts(check_In);
        }

        [HttpPost, PreLog]
        public void PresentedCollegeService(PresentedCollegeService_In present_In)
        {
            BLL_ContractServiceCollege.Instance.PresentedCollegeService(present_In);
        }
    }
}

