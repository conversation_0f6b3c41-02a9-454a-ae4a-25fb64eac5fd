-- =====================================================
-- 更新 EnumGtisServiceChangeProject 枚举配置脚本
-- 修正 ChangeReasonEnum 字段值与系统枚举的对应关系
-- 创建时间: 2025-01-03
-- =====================================================

USE [CRM2_DB];
GO

-- 1. 首先删除现有的错误配置
DELETE FROM [dbo].[crm_service_change_field_permission] 
WHERE [ProductType] = 5 -- EnumProductType.SalesWits
  AND [FieldName] IN ('SalesWitsRecharge', 'SalesWitsAddAccount', 'OpenSaleWits');

-- 2. 重新插入正确的配置
-- 注意：ChangeReasonEnum 字段值必须与 EnumGtisServiceChangeProject 枚举值一致

-- SalesWits充值 (对应枚举值 7)
INSERT INTO [dbo].[crm_service_change_field_permission] 
([Id], [ProductType], [FieldName], [FieldDisplayName], [FieldType], [IsRequired], [ChangeReasonEnum], [SortOrder], [IsActive], [CreateTime], [CreateUser], [Remark])
VALUES 
(NEWID(), 5, 'SalesWitsRecharge', 'SalesWits充值', 'decimal', 0, 7, 1, 1, GETDATE(), 'System', 'SalesWits充值金额配置');

-- SalesWits新增子账号 (对应枚举值 8)
INSERT INTO [dbo].[crm_service_change_field_permission] 
([Id], [ProductType], [FieldName], [FieldDisplayName], [FieldType], [IsRequired], [ChangeReasonEnum], [SortOrder], [IsActive], [CreateTime], [CreateUser], [Remark])
VALUES 
(NEWID(), 5, 'SalesWitsAddAccount', 'SalesWits新增子账号', 'int', 0, 8, 2, 1, GETDATE(), 'System', 'SalesWits新增子账号数量配置');

-- 开通SaleWits (对应枚举值 6)
INSERT INTO [dbo].[crm_service_change_field_permission] 
([Id], [ProductType], [FieldName], [FieldDisplayName], [FieldType], [IsRequired], [ChangeReasonEnum], [SortOrder], [IsActive], [CreateTime], [CreateUser], [Remark])
VALUES 
(NEWID(), 5, 'OpenSaleWits', '开通SaleWits', 'boolean', 0, 6, 3, 1, GETDATE(), 'System', '开通SaleWits服务配置');

-- 3. 验证更新结果
SELECT 
    [FieldName],
    [FieldDisplayName],
    [ChangeReasonEnum],
    CASE [ChangeReasonEnum]
        WHEN 1 THEN '尾款申请剩余服务'
        WHEN 2 THEN '变更服务内容'
        WHEN 3 THEN '个人服务天数延期'
        WHEN 4 THEN '优惠券延期'
        WHEN 5 THEN '其他'
        WHEN 6 THEN '开通SaleWits'
        WHEN 7 THEN 'SalesWits充值'
        WHEN 8 THEN 'SalesWits新增子账号'
        ELSE '未知枚举值'
    END AS [ChangeReasonDescription],
    [IsActive],
    [CreateTime]
FROM [dbo].[crm_service_change_field_permission] 
WHERE [ProductType] = 5 -- EnumProductType.SalesWits
ORDER BY [ChangeReasonEnum], [SortOrder];

-- 4. 检查是否还有其他产品类型的配置需要更新
SELECT DISTINCT 
    [ProductType],
    [ChangeReasonEnum],
    COUNT(*) AS [ConfigCount]
FROM [dbo].[crm_service_change_field_permission] 
GROUP BY [ProductType], [ChangeReasonEnum]
ORDER BY [ProductType], [ChangeReasonEnum];

PRINT '✅ EnumGtisServiceChangeProject 枚举配置更新完成！';
PRINT '📋 已更新的枚举值对应关系：';
PRINT '   6 = 开通SaleWits';
PRINT '   7 = SalesWits充值';
PRINT '   8 = SalesWits新增子账号';
