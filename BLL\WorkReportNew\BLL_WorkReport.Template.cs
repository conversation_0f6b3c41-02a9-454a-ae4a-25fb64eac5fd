using System.Collections.Generic;
using CRM2_API.Model.ControllersViewModel.Report;
using CRM2_API.Model.Enum;

namespace CRM2_API.BLL.WorkReportNew
{
    /// <summary>
    /// 工作报告模板管理业务逻辑类
    /// </summary>
    public partial class BLL_WorkReport
    {
        #region 报告模板管理

        /// <summary>
        /// 获取报告模板
        /// </summary>
        /// <param name="reportType">报告类型</param>
        /// <returns>模板数据</returns>
        public GetReportTemplate_Out GetReportTemplate(EnumReportType reportType)
        {
            // TODO: 从数据库获取模板配置
            // 当前返回Mock数据支持前端开发

            return reportType switch
            {
                EnumReportType.Daily => GetDailyReportTemplate(),
                EnumReportType.Weekly => GetWeeklyReportTemplate(),
                EnumReportType.Monthly => GetMonthlyReportTemplate(),
                _ => throw new System.Exception("不支持的报告类型")
            };
        }

        /// <summary>
        /// 获取日报模板
        /// </summary>
        private GetReportTemplate_Out GetDailyReportTemplate()
        {
            var modules = new List<VM_TemplateModule>();
            
            // 工作回顾模块 - 严格按照需求文档1.1.1
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "work_review",
                ModuleTitle = "工作回顾",
                ModuleOrder = 1,
                ModuleType = "workdata",
                Description = "填写当日工作量统计",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "PhoneCount", SectionTitle = "电话量", SectionOrder = 1, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "VisitCount", SectionTitle = "拜访量", SectionOrder = 2, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "EmailBulkCount", SectionTitle = "邮件量（粗发）", SectionOrder = 3, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "EmailPreciseCount", SectionTitle = "邮件量（精发）", SectionOrder = 4, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "SocialMediaCount", SectionTitle = "社媒量", SectionOrder = 5, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "ReplyCount", SectionTitle = "回复量", SectionOrder = 6, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "DemoCount", SectionTitle = "演示量", SectionOrder = 7, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "Other", SectionTitle = "其他", SectionOrder = 8, DataType = "text", IsRequired = false }
                }
            });

            // 客户新增模块 - 严格按照需求文档1.1.2
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "customer_new",
                ModuleTitle = "客户新增",
                ModuleOrder = 2,
                ModuleType = "customer",
                Description = "可以直接从跟踪记录中选择",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "CustomerLevel", SectionTitle = "客户级别", SectionOrder = 1, DataType = "select", Options = new List<string> { "A级", "B级", "C级" }, IsRequired = false },
                    new VM_TemplateSection { SectionKey = "Country", SectionTitle = "国别", SectionOrder = 2, DataType = "select", OptionsSource = "sys_country", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "CompanyName", SectionTitle = "公司名（中文/英文）", SectionOrder = 3, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "MainProducts", SectionTitle = "主营产品（HS编码）", SectionOrder = 4, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "CommunicationSummary", SectionTitle = "沟通情况概述", SectionOrder = 5, DataType = "text", IsRequired = false }
                }
            });

            // 客户跟进模块 - 严格按照需求文档1.1.3
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "customer_follow",
                ModuleTitle = "客户跟进",
                ModuleOrder = 3,
                ModuleType = "customer",
                Description = "回复/演示/拜访等（可以直接从跟踪记录中选择）",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "CompanyName", SectionTitle = "公司名（中文/英文）", SectionOrder = 1, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "MainProducts", SectionTitle = "主营产品（HS编码）", SectionOrder = 2, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "RecommendedSolution", SectionTitle = "推荐方案", SectionOrder = 3, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "FollowUpSummary", SectionTitle = "跟进情况概述", SectionOrder = 4, DataType = "text", DefaultValue = "包括客户意向、客户异议、演示情况等", IsRequired = false }
                }
            });

            // 客户签约模块 - 严格按照需求文档1.1.4
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "customer_contract",
                ModuleTitle = "客户签约",
                ModuleOrder = 4,
                ModuleType = "customer",
                Description = "可以直接从跟踪记录中选择",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "CompanyName", SectionTitle = "公司名（中文/英文）", SectionOrder = 1, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "MainProducts", SectionTitle = "主营产品（HS编码）", SectionOrder = 2, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "ContractSolution", SectionTitle = "签约方案", SectionOrder = 3, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "ContractSummary", SectionTitle = "签约情况概述", SectionOrder = 4, DataType = "text", DefaultValue = "包括客户合作过程的简要复盘、客户特殊情况备忘等", IsRequired = false }
                }
            });

            // 客户服务模块 - 严格按照需求文档1.1.5
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "customer_service",
                ModuleTitle = "客户服务",
                ModuleOrder = 5,
                ModuleType = "customer",
                Description = "客户服务信息记录",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "CompanyName", SectionTitle = "公司名（中文/英文）", SectionOrder = 1, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "ServiceContent", SectionTitle = "服务内容", SectionOrder = 2, DataType = "text", IsRequired = false }
                }
            });

            // 其他规划与建议模块 - 严格按照需求文档1.1.6
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "other_planning",
                ModuleTitle = "其他规划与建议",
                ModuleOrder = 6,
                ModuleType = "content",
                Description = "规划和建议内容",
                Sections = new List<VM_TemplateSection>
                {
                    // 规划部分
                    new VM_TemplateSection { SectionKey = "ProductPlanning", SectionTitle = "选品规划", SectionOrder = 1, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "RegionPlanning", SectionTitle = "区域规划", SectionOrder = 2, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "SalesPlanning", SectionTitle = "销售综合规划", SectionOrder = 3, DataType = "text", DefaultValue = "含业绩、思路、策略等", IsRequired = false },
                    // 建议部分
                    new VM_TemplateSection { SectionKey = "Difficulties", SectionTitle = "遇到的难点", SectionOrder = 4, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "SupportNeeded", SectionTitle = "需要的支持", SectionOrder = 5, DataType = "text", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "Suggestions", SectionTitle = "合理化建议", SectionOrder = 6, DataType = "text", IsRequired = false }
                }
            });

            // 学习与思考模块 - 严格按照需求文档1.1.7
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "learning_thinking",
                ModuleTitle = "学习与思考",
                ModuleOrder = 7,
                ModuleType = "content",
                Description = "包括晨会/夕会/公司课程/慧思学院/团队链接等",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "LearningAndThinking", SectionTitle = "学习与思考", SectionOrder = 1, DataType = "text", IsRequired = false }
                }
            });

            // 附件模块 - 严格按照需求文档1.1.8
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "attachments",
                ModuleTitle = "附件",
                ModuleOrder = 8,
                ModuleType = "attachment",
                Description = "工作截图/视频等参考资料",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "WorkAttachments", SectionTitle = "工作附件", SectionOrder = 1, DataType = "file", IsRequired = false }
                }
            });

            return new GetReportTemplate_Out
            {
                ReportType = EnumReportType.Daily,
                Modules = modules
            };
        }

        /// <summary>
        /// 获取周报模板
        /// </summary>
        private GetReportTemplate_Out GetWeeklyReportTemplate()
        {
            var modules = new List<VM_TemplateModule>();

            // 汇总数据模块 - 严格按照需求文档周报要求
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "work_review",
                ModuleTitle = "汇总相应周期可量化的数据",
                ModuleOrder = 1,
                ModuleType = "workdata",
                Description = "本周工作量统计汇总",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "TotalPhoneCount", SectionTitle = "本周电话量汇总", SectionOrder = 1, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalVisitCount", SectionTitle = "本周拜访量汇总", SectionOrder = 2, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalEmailBulkCount", SectionTitle = "本周邮件量汇总（粗发）", SectionOrder = 3, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalEmailPreciseCount", SectionTitle = "本周邮件量汇总（精发）", SectionOrder = 4, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalSocialMediaCount", SectionTitle = "本周社媒量汇总", SectionOrder = 5, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalReplyCount", SectionTitle = "本周回复量汇总", SectionOrder = 6, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalDemoCount", SectionTitle = "本周演示量汇总", SectionOrder = 7, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalNewCustomerCount", SectionTitle = "本周新增客户数", SectionOrder = 8, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalFollowCustomerCount", SectionTitle = "本周跟进客户数", SectionOrder = 9, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalContractCustomerCount", SectionTitle = "本周签约客户数", SectionOrder = 10, DataType = "number", IsRequired = false }
                }
            });

            // 本周总结模块
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "summary",
                ModuleTitle = "本周总结",
                ModuleOrder = 2,
                ModuleType = "content",
                Description = "本周工作总结",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "WeeklySummary", SectionTitle = "本周总结", SectionOrder = 1, DataType = "text", IsRequired = false }
                }
            });

            // 下周规划模块
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "plan",
                ModuleTitle = "下周规划",
                ModuleOrder = 3,
                ModuleType = "content",
                Description = "下周工作规划",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "NextWeekPlan", SectionTitle = "下周规划", SectionOrder = 1, DataType = "text", IsRequired = false }
                }
            });

            return new GetReportTemplate_Out
            {
                ReportType = EnumReportType.Weekly,
                Modules = modules
            };
        }

        /// <summary>
        /// 获取月报模板
        /// </summary>
        private GetReportTemplate_Out GetMonthlyReportTemplate()
        {
            var modules = new List<VM_TemplateModule>();

            // 汇总数据模块 - 严格按照需求文档月报要求
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "work_review",
                ModuleTitle = "汇总相应周期可量化的数据",
                ModuleOrder = 1,
                ModuleType = "workdata",
                Description = "本月工作量统计汇总",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "TotalPhoneCount", SectionTitle = "本月电话量汇总", SectionOrder = 1, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalVisitCount", SectionTitle = "本月拜访量汇总", SectionOrder = 2, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalEmailBulkCount", SectionTitle = "本月邮件量汇总（粗发）", SectionOrder = 3, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalEmailPreciseCount", SectionTitle = "本月邮件量汇总（精发）", SectionOrder = 4, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalSocialMediaCount", SectionTitle = "本月社媒量汇总", SectionOrder = 5, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalReplyCount", SectionTitle = "本月回复量汇总", SectionOrder = 6, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalDemoCount", SectionTitle = "本月演示量汇总", SectionOrder = 7, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalNewCustomerCount", SectionTitle = "本月新增客户数", SectionOrder = 8, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalFollowCustomerCount", SectionTitle = "本月跟进客户数", SectionOrder = 9, DataType = "number", IsRequired = false },
                    new VM_TemplateSection { SectionKey = "TotalContractCustomerCount", SectionTitle = "本月签约客户数", SectionOrder = 10, DataType = "number", IsRequired = false }
                }
            });

            // 本月总结模块
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "summary",
                ModuleTitle = "本月总结",
                ModuleOrder = 2,
                ModuleType = "content",
                Description = "本月工作总结",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "MonthlySummary", SectionTitle = "本月总结", SectionOrder = 1, DataType = "text", IsRequired = false }
                }
            });

            // 下月规划模块
            modules.Add(new VM_TemplateModule
            {
                ModuleKey = "plan",
                ModuleTitle = "下月规划",
                ModuleOrder = 3,
                ModuleType = "content",
                Description = "下月工作规划",
                Sections = new List<VM_TemplateSection>
                {
                    new VM_TemplateSection { SectionKey = "NextMonthPlan", SectionTitle = "下月规划", SectionOrder = 1, DataType = "text", IsRequired = false }
                }
            });

            return new GetReportTemplate_Out
            {
                ReportType = EnumReportType.Monthly,
                Modules = modules
            };
        }

        #endregion
    }
}