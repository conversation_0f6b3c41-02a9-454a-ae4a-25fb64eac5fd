﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Article;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;

namespace CRM2_API.BLL.Article
{
    public class BLL_ArticleComment : BaseBLL<BLL_ArticleComment>
    {
        /// <summary>
        /// 添加文章评论信息
        /// </summary>
        /// <param name="addArticleCommentIn"></param>
        /// <returns></returns>
        public AddArticleComment_Out AddArticleComment(AddArticleComment_In addArticleCommentIn)
        {
            AddArticleComment_Out result = new() { Data = 0 };
            var insertId = Guid.NewGuid().ToString();
            var newEntity = new Db_crm_article_comment
            {
                Id = insertId,
                ArticleId = addArticleCommentIn.ArticleId,
                CommentContent = addArticleCommentIn.CommentContent,
                CommentTime = DateTime.Now,
                UserId = UserTokenInfo.id,
                State = EnumArticleCommentState.Pass.ToIntNullable(),
                ParentId = addArticleCommentIn.ParentId,
                IsRecommend = 0,
                Hidden = false,
                IsRead = false,
                Deleted = false,
                CreateUser = UserTokenInfo.id,
                CreateDate = DateTime.Now,
            };
            if (!string.IsNullOrEmpty(addArticleCommentIn.ParentId))
            {
                var parentData = DbOpe_crm_article_comment.Instance.QueryByPrimaryKey(addArticleCommentIn.ParentId);
                if (parentData is not null)
                {
                    newEntity.RootId = parentData.RootId ?? parentData.Id;
                }
            }
            DbOpe_crm_article_comment.Instance.InsertQueue(newEntity);
            if (addArticleCommentIn.ArticleCommentAttachFile is not null and { Count: > 0 })
            {
                Util<DbOpe_crm_article_comment_attachfile, BM_AttachFiles> util = new(DbOpe_crm_article_comment_attachfile.Instance);
                bool uploadResult = util.UploadFiles(insertId, addArticleCommentIn.ArticleCommentAttachFile, AttachEnumOption.ArticleComment);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
            }
            DbOpe_crm_article_comment.Instance.SaveQueues();
            result.Data = 1;
            return result;
        }

        /// <summary>
        /// 修改文章评论信息
        /// </summary>
        /// <param name="updateArticleCommentIn"></param>
        /// <returns></returns>
        public UpdateArticleComment_Out UpdateArticleComment(UpdateArticleComment_In updateArticleCommentIn)
        {
            UpdateArticleComment_Out result = new() { Data = 0 };
            var dbData = DbOpe_crm_article_comment.Instance.QueryByPrimaryKey(updateArticleCommentIn.Id);
            if (dbData is null)
            {
                throw new ApiException("要修改的文章评论不存在!");
            }

            if (!dbData.CreateUser.Equals(UserTokenInfo.id))
            {
                throw new ApiException("无法修改他人的文章评论!");
            }
            dbData.IsRead = false;
            dbData.CommentContent = updateArticleCommentIn.CommentContent;
            dbData.UpdateUser = UserTokenInfo.id;
            dbData.UpdateDate = DateTime.Now;
            DbOpe_crm_article_comment.Instance.UpdateQueue(dbData);

            /*
            //先删除原有的附件[逻辑删除]
            List<Db_crm_article_comment_attachfile> attachfiles = DbOpe_crm_article_comment_attachfile.Instance.GetArticleCommentAttachFilesByArticleCommentId(updateArticleCommentIn.Id);
            attachfiles.ForEach(i =>
            {
                i.Deleted = true;
                i.UpdateUser = UserTokenInfo.id;
                i.UpdateDate = DateTime.Now;
            });
            DbOpe_crm_article_comment_attachfile.Instance.UpdateQueue(attachfiles);
            //保存附件
            if (updateArticleCommentIn.ArticleCommentAttachFile is not null and { Count: > 0 })
            {
                Util<DbOpe_crm_article_comment_attachfile, BM_AttachFiles> util = new (DbOpe_crm_article_comment_attachfile.Instance);
                bool uploadResult = util.UploadFiles(dbData.Id, updateArticleCommentIn.ArticleCommentAttachFile, AttachEnumOption.Article);
                if (!uploadResult)
                {
                    throw new ApiException("文件上传异常!");
                }
            }
            */

            //附件操作
            var attachFileList = DbOpe_crm_article_comment_attachfile.Instance.GetArticleCommentAttachFilesByArticleCommentId(updateArticleCommentIn.Id);
            var attachFileIdList = attachFileList.Select(w => w.Id).ToList();
            if (updateArticleCommentIn.ArticleCommentAttachFile is null or { Count: 0 })
            {
                if (attachFileIdList is not null and { Count: > 0 })
                {
                    DbOpe_crm_article_comment_attachfile.Instance.DeleteAttachFileByIdList(attachFileIdList);
                }
            }
            else
            {
                var attachFileUpdateIdList = new List<string>();
                FormFileCollection formFileCollection = new();
                foreach (Update_ArticleCommentAttachFile updateArticleCommentAttachFile in updateArticleCommentIn.ArticleCommentAttachFile)
                {
                    if (attachFileIdList.Contains(updateArticleCommentAttachFile.Id))
                    {
                        attachFileUpdateIdList.Add(updateArticleCommentAttachFile.Id);
                        continue;
                    }

                    if (updateArticleCommentAttachFile.File != null)
                    {
                        formFileCollection.Add(updateArticleCommentAttachFile.File);
                    }

                }

                if (formFileCollection.Count > 0)
                {
                    Util<DbOpe_crm_article_comment_attachfile, BM_AttachFiles> util = new(DbOpe_crm_article_comment_attachfile.Instance);
                    bool uploadResult = util.UploadFiles(dbData.Id, formFileCollection, AttachEnumOption.ArticleComment);
                    if (!uploadResult)
                    {
                        throw new ApiException("文件上传异常!");
                    }
                }
                var attachFileDeleteIdList = attachFileIdList.Except(attachFileUpdateIdList).ToList();
                DbOpe_crm_article_comment_attachfile.Instance.DeleteAttachFileByIdList(attachFileDeleteIdList);
            }

            int execCount = DbOpe_crm_article_comment.Instance.SaveQueues();
            if (execCount > 0)
            {
                result.Data = 1;
            }
            return result;
        }

        /// <summary>
        /// 删除文章评论信息
        /// </summary>
        /// <param name="articleCommentId"></param>
        /// <returns></returns>
        public DeleteArticleComment_Out DeleteArticleComment(string articleCommentId)
        {
            var dbData = DbOpe_crm_article_comment.Instance.QueryByPrimaryKey(articleCommentId);
            if (dbData is null)
            {
                throw new ApiException("要删除的知识库文章不存在!");
            }
            if (!dbData.CreateUser.Equals(UserTokenInfo.id))
            {
                throw new ApiException("无法删除他人的文章评论!");
            }
            return DbOpe_crm_article_comment.Instance.DeleteArticleComment(articleCommentId);
        }

        /// <summary>
        /// 根据文章Id获取文章评论信息
        /// </summary>
        /// <param name="getArticleCommentListIn"></param>
        /// <returns></returns>
        public List<GetArticleCommentList_Out> GetArticleCommentList(GetArticleCommentList_In getArticleCommentListIn)
        {
            //1.首先获取评论的根级
            List<GetArticleCommentList_Out> rootData =
                DbOpe_crm_article_comment.Instance.GetArticleCommentRootList(getArticleCommentListIn);
            if (rootData is not null and { Count: > 0 })
            {
                List<string> setReadIdAllList = new();
                foreach (GetArticleCommentList_Out rootItem in rootData)
                {
                    if (rootItem.CommentNum is null or 0) continue;
                    List<ArticleCommentTree> articleCommentTrees = DbOpe_crm_article_comment.Instance.getArticleCommentChildListByRootId(rootItem.Id, getArticleCommentListIn);
                    if (articleCommentTrees is not null and { Count: > 0 })
                    {
                        var result = ConvertArticleCommentModel(articleCommentTrees, default, default, rootItem.UserId);
                        rootItem.SubComment = result.outputList;
                        setReadIdAllList.AddRange(result.setReadIdList);
                    }
                }

                if (setReadIdAllList is not null and { Count: > 0 })
                {
                    //将这些回复设置为已读
                    DbOpe_crm_article_comment.Instance.SetArticleReplyIsRead(setReadIdAllList);
                }
            }
            return rootData;
        }

        /// <summary>
        /// 将树形结构的回复列表压缩成列表结构
        /// </summary>
        /// <param name="sourceList">树结构的数据</param>
        /// <param name="outputList">最终返回的列表-将树形结构压缩成列表结构</param>
        /// <param name="setReadIdList">保存将回复设置为已读的Id列表</param>
        /// <param name="upLevelUserId">上级的创建者</param>
        /// <returns></returns>
        public (List<ArticleCommentChild> outputList, List<string> setReadIdList) ConvertArticleCommentModel(List<ArticleCommentTree> sourceList, List<ArticleCommentChild> outputList = default, List<string> setReadIdList = default, string upLevelUserId = default)
        {
            ArticleCommentChild temp = default;
            outputList ??= new();
            setReadIdList ??= new();
            if (sourceList is not null and { Count: > 0 })
            {
                foreach (ArticleCommentTree treeItem in sourceList)
                {
                    temp = new()
                    {
                        Id = treeItem.Id,
                        ArticleId = treeItem.ArticleId,
                        ParentId = treeItem.ParentId,
                        CommentContent = treeItem.CommentContent,
                        CommentTime = treeItem.UpdateDate ?? treeItem.CreateDate,
                        UserId = treeItem.UserId,
                        UserName = treeItem.UserName,
                        AvatarImage = treeItem.AvatarImage,
                        AvatarImageUrl = treeItem.AvatarImageUrl,
                        IsMe = treeItem.IsMe,
                        IsAuthor = treeItem.IsAuthor,
                        ReplyToUserId = treeItem.ReplyToUserId,
                        ReplyToUserName = treeItem.ReplyToUserName,
                        ReplyToUserIsAuthor = treeItem.ReplyToUserIsAuthor,
                        ReplyToUserAvatarImage = treeItem.ReplyToUserAvatarImage,
                        ReplyToUserAvatarImageUrl = treeItem.ReplyToUserAvatarImageUrl,
                        hasPraise = treeItem.hasPraise,
                        PraiseNum = treeItem.PraiseNum,
                        ArticleCommentImageAttachFile = treeItem.ArticleCommentImageAttachFile,
                        ArticleCommentDocumentAttachFile = treeItem.ArticleCommentDocumentAttachFile,
                        //ArticleCommentAttachFile = treeItem.ArticleCommentAttachFile,
                    };
                    if (treeItem.Child is not null and { Count: > 0 })
                    {
                        temp.CommentNum = treeItem.Child.Count;
                    }
                    else
                    {
                        temp.CommentNum = 0;
                    }

                    //如果这上级创建者不为空，并且上级创建人为当前登录人，那么当前级别的回复将会设置为已读
                    if (!string.IsNullOrEmpty(upLevelUserId) && upLevelUserId.Equals(UserId))
                    {
                        setReadIdList.Add(treeItem.Id);
                    }

                    outputList.Add(temp);
                    if (treeItem.Child is not null and { Count: > 0 })
                    {
                        treeItem.Child = treeItem.Child.OrderBy(o => o.CreateDate).ToList();
                        ConvertArticleCommentModel(treeItem.Child, outputList, setReadIdList, treeItem.UserId);
                    }
                }
            }
            return (outputList, setReadIdList);
        }
    }
}