﻿using CRM2_API.BLL;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections;
using System.ComponentModel;
using static CRM2_API.Model.ControllersViewModel.VM_Collectioninfo;

namespace CRM2_API.Controllers
{
    [Description("回款信息管理")]
    public class CollectioninfoController : MyControllerBase
    {
        public CollectioninfoController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 添加回款信息
        /// </summary>
        [HttpPost]
        public List<Db_crm_collectioninfo> AddCollectionInfo([FromForm] List<AddCollectionInfo_In> addCollectionInfo)
        {
            return BLL_Collectioninfo.Instance.AddCollectionInfo(addCollectionInfo);
        }

        /// <summary>
        /// 修改回款信息
        /// </summary>
        [HttpPost]
        public Db_crm_collectioninfo UpdateCollectionInfo([FromForm] UpdateCollectionInfo_In updateCollectionInfo)
        {
            return BLL_Collectioninfo.Instance.UpdateCollectionInfo(updateCollectionInfo);
        }

        /// <summary>
        /// 删除回款信息
        /// </summary>
        [HttpPost]
        public void DeleteCollectionInfo(List<string> ids)
        {
            BLL_Collectioninfo.Instance.DeleteCollectionInfo(ids);
        }

        /// <summary>
        /// 根据回款信息Id获取回款信息
        /// </summary>
        [HttpPost]
        public CollectionInfo_Out GetCollectionInfoById(string id)
        {
            return BLL_Collectioninfo.Instance.GetCollectionInfoById(id);
        }

        /// <summary>
        /// 根据查询条件获取回款信息列表
        /// </summary>
        /// <param name="searchCollectionInfoListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchCollectionInfoList_Out> SearchCollectionInfoList(SearchCollectionInfoList_In searchCollectionInfoListIn)
        {
            return BLL_Collectioninfo.Instance.SearchCollectionInfoList(searchCollectionInfoListIn);
        }

        /// <summary>
        /// 根据查询条件获取回款信息核对确认统计列表
        /// </summary>
        /// <param name="searchCollectionInfoListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public SearchCollectionInfoListSta_Out SearchCollectionInfoVerifyConfirmListSta(SearchCollectionInfoList_In searchCollectionInfoListIn)
        {
            return BLL_Collectioninfo.Instance.SearchCollectionInfoVerifyConfirmListSta(searchCollectionInfoListIn);
        }

        /// <summary>
        /// 根据查询条件获取回款信息核对确认列表
        /// </summary>
        /// <param name="searchCollectionInfoListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchCollectionInfoVerifyConfirmList_Out> SearchCollectionInfoVerifyConfirmList(SearchCollectionInfoList_In searchCollectionInfoListIn)
        {
            return BLL_Collectioninfo.Instance.SearchCollectionInfoVerifyConfirmList(searchCollectionInfoListIn);
        }

        /// <summary>
        /// 识别回款信息
        /// </summary>
        [HttpPost]
        public IdentifyCollectionInfo_Out IdentifyCollectionInfo(string transactionReceipt)
        {
            //return BLL_Collectioninfo.Instance.IdentifyCollectionInfo(transactionReceipt);
            return BLL_Collectioninfo.Instance.IdentifyCollectionInfoList(transactionReceipt);
        }

        /// <summary>
        /// 识别是否定金
        /// </summary>
        [HttpPost]
        public bool GetIsDeposit(CollectionInfoIsDeposit_In isDepositIn)
        {
            return BLL_Collectioninfo.Instance.GetIsDeposit(isDepositIn);
        }

        /// <summary>
        /// 根据查询条件获取匹配状态为待登记的回款信息列表
        /// </summary>
        /// <param name="searchUnRegisteredCollectionInfoListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<SearchUnRegisteredCollectionInfoList_Out> SearchUnRegisteredCollectionInfoList(SearchUnRegisteredCollectionInfoList_In searchUnRegisteredCollectionInfoListIn)
        {
            return BLL_Collectioninfo.Instance.SearchUnRegisteredCollectionInfoList(searchUnRegisteredCollectionInfoListIn);
        }

        /// <summary>
        /// 添加回款交易凭证信息
        /// </summary>
        [HttpPost]
        //public List<Db_crm_collectioninfo> AddTransactionReceipt([ModelBinder(BinderType = typeof(AddTransactionReceiptModelBinder<AddTransactionReceipt_In>))] List<AddTransactionReceipt_In> addTransactionReceiptIn)
        public List<Db_crm_collectioninfo> AddTransactionReceipt([FromForm] List<AddTransactionReceipt_In> addTransactionReceiptIn)
        {
            return BLL_Collectioninfo.Instance.AddTransactionReceipt(addTransactionReceiptIn);
        }

        /// <summary>
        /// 修改回款交易凭证信息
        /// </summary>
        [HttpPost]
        //public Db_crm_collectioninfo UpdateTransactionReceipt([ModelBinder(BinderType = typeof(AddTransactionReceiptModelBinder<UpdateTransactionReceipt_In>))] UpdateTransactionReceipt_In updateTransactionReceiptIn)
        public Db_crm_collectioninfo UpdateTransactionReceipt([FromForm] UpdateTransactionReceipt_In updateTransactionReceiptIn)
        {
            return BLL_Collectioninfo.Instance.UpdateTransactionReceipt(updateTransactionReceiptIn);
        }

        /// <summary>
        /// 验证回款交易凭证信息重复
        /// </summary>
        [HttpPost]
        public void CheckTransactionReceiptRepeat(List<CheckTransactionReceiptRepeat_In> checkTransactionReceiptRepeatIn)
        {
            BLL_Collectioninfo.Instance.CheckTransactionReceiptRepeat(checkTransactionReceiptRepeatIn);
        }

        /// <summary>
        /// 手动匹配回款交易凭证信息
        /// </summary>
        [HttpPost]
        public void AddManualMatching(AddManualMatching_In addManualMatchingIn)
        {
            BLL_Collectioninfo.Instance.AddManualMatching(addManualMatchingIn);
        }

        /// <summary>
        /// 获取回款信息状态字典
        /// </summary>
        [HttpPost]
        public List<Dictionary_Out> GetCollectionInfoStateList()
        {
            return BLL_Collectioninfo.Instance.GetCollectionInfoStateList();
        }

        /// <summary>
        /// 验证回款信息是否重复
        /// </summary>
        [HttpPost]
        public CheckCollectionInfoRepeat_Out CheckCollectionInfoRepeat([FromForm] List<CheckCollectionInfoRepeat_In> checkCollectionInfoRepeatIn)
        {
            return BLL_Collectioninfo.Instance.CheckCollectionInfoRepeat(checkCollectionInfoRepeatIn);
        }

        /// <summary>
        /// 验证回款信息是否重复
        /// </summary>
        [HttpPost]
        public CheckCollectionInfoRepeat_Out CheckUpdateCollectionInfoRepeat([FromForm] CheckUpdateCollectionInfoRepeat_In checkUpdateCollectionInfoRepeatIn)
        {
            return BLL_Collectioninfo.Instance.CheckUpdateCollectionInfoRepeat(checkUpdateCollectionInfoRepeatIn);
        }

        /// <summary>
        /// 验证回款信息是否重复
        /// </summary>
        [HttpPost]
        public CheckCollectionInfoRepeat_Out CheckTransactionReceiptAndInfoRepeat([FromForm] List<CheckTransactionReceiptAndInfoRepeat_In> checkTransactionReceiptAndInfoRepeatIn)
        {
            return BLL_Collectioninfo.Instance.CheckTransactionReceiptAndInfoRepeat(checkTransactionReceiptAndInfoRepeatIn);
        }

        /// <summary>
        /// 验证回款信息是否重复
        /// </summary>
        [HttpPost]
        public CheckCollectionInfoRepeat_Out CheckUpdateTransactionReceiptAndInfoRepeat([FromForm] CheckUpdateTransactionReceiptAndInfoRepeat_In checkUpdateTransactionReceiptAndInfoRepeatIn)
        {
            return BLL_Collectioninfo.Instance.CheckUpdateTransactionReceiptAndInfoRepeat(checkUpdateTransactionReceiptAndInfoRepeatIn);
        }

        /// <summary>
        /// 核对确认
        /// </summary>
        [HttpPost]
        public void VerifyConfirmCollectionInfo(string id)
        {
            BLL_Collectioninfo.Instance.VerifyConfirmCollectionInfo(id);
        }

        /// <summary>
        /// 撤销核对确认
        /// </summary>
        [HttpPost]
        public void RevokeVerifyConfirmCollectionInfo(string id)
        {
            BLL_Collectioninfo.Instance.RevokeVerifyConfirmCollectionInfo(id);
        }

        /// <summary>
        /// 修改银行到账到账时间
        /// </summary>
        [HttpPost]
        public void UpdateCollectionArrivalDateInfo(UpdateCollectionArrivalDateInfo_In updateCollectionArrivalDateInfo)
        {
            BLL_Collectioninfo.Instance.UpdateCollectionArrivalDateInfo(updateCollectionArrivalDateInfo);
        }

        /// <summary>
        /// 锁定银行到账
        /// </summary>
        [HttpPost]
        public void LockCollectionLockStates(LockCollectionStates_In LockcollectionStates_In)
        {
            BLL_Collectioninfo.Instance.LockCollectionLockStates(LockcollectionStates_In);
        }

        /// <summary>
        /// 解锁银行到账
        /// </summary>
        [HttpPost]
        public void UnLockCollectionLockStates(LockCollectionStates_In LockcollectionStates_In)
        {
            BLL_Collectioninfo.Instance.UnLockCollectionLockStates(LockcollectionStates_In);
        }
    }
}
