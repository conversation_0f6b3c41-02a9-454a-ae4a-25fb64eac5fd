﻿using CRM2_API.BLL.Common;
using CRM2_API.BLL.GtisOpe;
using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using iText.StyledXmlParser.Css.Resolve.Shorthand.Impl;
using LumiSoft.Net.Media;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.IO;
using System.Text.Json.Nodes;
using static CRM2_API.Model.BLLModel.Enum.CouponEnumOption;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.BLLModel.Enum.PrivateServiceEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using CRM2_API.DAL.DbModel.Gtis;

namespace CRM2_API.BLL
{
    public partial class BLL_ContractService : BaseBLL<BLL_ContractService>
    {

        public List<int> GetUserStates()
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetUserStates(currentUser);
        }
        /// <summary>
        /// 根据查询条件获取合同服务信息环慧思申请信息列表
        /// </summary>
        /// <param name="searchContractProductServiceInfoGtisAppl_IN"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public ApiTableOut<SearchContractProductServiceInfoGtisAppl_OUT> SearchContractProductServiceInfoGtisApplList(SearchContractProductServiceInfoGtisAppl_IN searchContractProductServiceInfoGtisAppl_IN)
        {
            //DbOpe_crm_customer.Instance.ExcuteContractOverDueCheck();
            int total = 0;
            return new ApiTableOut<SearchContractProductServiceInfoGtisAppl_OUT> { Data = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.SearchContractProductServiceInfoGtisApplList(searchContractProductServiceInfoGtisAppl_IN, currentUser, ref total), Total = total };
        }

        public CheckGtisFreeAble_OUT CheckGtisFreeAble(string id)
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.CheckGtisFreeAble(id);
        }
        /// <summary>
        /// 根据申请id获取合同服务信息_环球慧思信息（申请信息+ 登记复核信息 + 在服信息）
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public ContractServiceGtisInfo_OUT GetContractServiceInfoGtisByApplId(string applId)
        {
            //return DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceInfoGtisByApplId(applId);

            ContractServiceGtisInfo_OUT retObj = new ContractServiceGtisInfo_OUT();
            #region 申请信息
            var agi = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetApplGtisInfo(applId);
            retObj.ApplGtisInfo = agi;
            #endregion
            #region 当前产品是否是赠送
            retObj.IsFree = agi.IsFree;
            #endregion
            #region 合同业绩信息
            retObj.ContractInfoAndReceipt = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(agi.ContractId);
            #endregion
            #region 历史服务信息
            var historyList = new List<HistoryGtisServiceInfo>();
            //判断是否是续约合同的服务
            var isRenew = !string.IsNullOrEmpty(agi.RenewContractNum);
            //判断是否是变更的服务
            var isChange = agi.ProcessingType == (int)EnumProcessingType.Change;
            //查找历史数据要用的ContractId
            var hisParamApplyId = agi.Id;
            //查找历史数据要用的ContractNum
            var hisParamContractNum = agi.RenewContractNum;
            //如果是续约合同的服务或是变更的服务，需要查找历史数据
            while (isRenew || isChange)
            {
                //声明历史数据
                var hisServe = new HistoryGtisServiceInfo_Mid();
                //如果当前服务是变更数据，取被变更的服务信息
                if (isChange)
                    //获取被变更的服务数据信息
                    hisServe = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetChangedGtisServiceInfoByApplyInfo(hisParamApplyId, retObj.ContractInfoAndReceipt.ContractNum);
                else
                    //根据客户编码获取被续约服务信息
                    hisServe = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetHisGtisServiceInfoByContractNum(hisParamContractNum);
                if (hisServe != null)
                {
                    //修改isRenew 判断是否是续约合同的服务
                    isRenew = !string.IsNullOrEmpty(hisServe.OldContractNum);
                    //修改isChange 判断是否是变更的服务
                    isChange = hisServe.ProcessingType == (int)EnumProcessingType.Change;
                    //修改查找历史数据要用的ContractId
                    hisParamApplyId = hisServe.ApplyId;
                    //修改查找历史数据要用的ContractNum
                    hisParamContractNum = hisServe.OldContractNum;
                    //填充返回列表
                    historyList.Add(hisServe.MappingTo<HistoryGtisServiceInfo>());
                }
                else
                {
                    isRenew = false;
                    isChange = false;
                }
            }
            retObj.HistoryGtisServiceList = historyList;
            #endregion
            //同步g5账号信息
            List<Db_crm_contract_serviceinfo_gtis_user> userDatas = new List<Db_crm_contract_serviceinfo_gtis_user>();
            var gtisContractNum = string.Empty;
            SynchroGtisUserData(retObj.ContractInfoAndReceipt.ContractNum, ref userDatas, ref gtisContractNum);
            #region 登记复核信息
            int gtisServiceState = (int)EnumContractServiceState.TO_BE_OPENED;
            DateTime? serviceCycleEnd = null;
            retObj.RegisterInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetRegisterInfoByApplId(applId);
            if (retObj.RegisterInfo != null)
            {
                gtisServiceState = retObj.RegisterInfo.State.Value;
                serviceCycleEnd = retObj.RegisterInfo.ServiceCycleEnd;
            }
            #endregion
            #region 状态
            //服务类型
            retObj.ServiceType = agi.ProcessingType;
            retObj.ServiceTypeName = ((EnumProcessingType)retObj.ServiceType).GetEnumDescription();

            retObj.State = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceOpenState(agi.State.Value, agi.ProcessingType.Value, gtisServiceState, serviceCycleEnd);
            retObj.StateName = ((EnumContractServiceOpenState)retObj.State).GetEnumDescription();
            retObj.SalesServiceState = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractSalesServiceState(agi.State.Value, agi.SendAccountTime, gtisServiceState);
            retObj.SalesServiceStateName = retObj.SalesServiceState.GetEnumDescription().ToString();
            //账号状态  正常的显示账号状态，其他不显示
            if (gtisServiceState != (int)EnumContractServiceState.VALID)
            {
                retObj.AccountStatus = (int)EnumGtisAccountStatus.INPROCESS;
                retObj.AccountStatusName = ((EnumGtisAccountStatus)retObj.AccountStatus).GetEnumDescription();
            }
            else
            {
                var svCode = retObj.ContractInfoAndReceipt.ContractNum;
                var accStates = BLL_GtisOpe.Instance.GetUserState(new string[] { svCode }).Result;
                retObj.AccountStatusName = accStates.ContainsKey(svCode) ? accStates[svCode] : "";
                retObj.AccountStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.TransGtisServiceAccountStates(retObj.AccountStatusName);
                //方法被其他外部接口重复调用,这里注释
                //retObj.Accounts = DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserByApplId(applId);
            }
            #endregion
            if (agi.ProcessingType == (int)EnumProcessingType.Change && !string.IsNullOrEmpty(agi.ChangeReasonEnums))
            {
                var reasonList = new List<EnumGtisServiceChangeProject>();
                try
                {
                    // 尝试解析JSON格式
                    var changeReasonData = System.Text.Json.JsonSerializer.Deserialize<dynamic>(agi.ChangeReasonEnums);
                    if (changeReasonData != null && changeReasonData.GetProperty("ChangeReasons").GetArrayLength() > 0)
                    {
                        var changeReasons = changeReasonData.GetProperty("ChangeReasons");
                        foreach (var reason in changeReasons.EnumerateArray())
                        {
                            var changeReason = reason.GetProperty("ChangeReason").GetInt32();
                            reasonList.Add(changeReason.ToEnum<EnumGtisServiceChangeProject>());
                        }
                    }
                }
                catch (System.Text.Json.JsonException)
                {
                    // JSON解析失败，尝试解析旧格式（兼容性处理）
                    reasonList = agi.ChangeReasonEnums.Split(",").ToList().Select(e => e.ToEnum<EnumGtisServiceChangeProject>()).ToList();
                }
                retObj.AllowChangeProperty = new JObject();
                retObj.AllowChangeProperty.Add("GlobalSearchSettlementMonth", true);
                retObj.AllowChangeProperty.Add("IsOpenCrm", true);
                if (reasonList.Contains(EnumGtisServiceChangeProject.ApplyResidualService))
                {
                    retObj.AllowChangeProperty.Add("GlobalSearchSettlementLevel", true);
                    retObj.AllowChangeProperty.Add("GlobalSearchAccountCount", true);
                    retObj.AllowChangeProperty.Add("ServiceCycleStart", true);
                    retObj.AllowChangeProperty.Add("ServiceCycleEnd", true);
                    retObj.AllowChangeProperty.Add("ServiceMonth", true);
                    retObj.AllowChangeProperty.Add("ShareUsageNum", true);
                    retObj.AllowChangeProperty.Add("ForbidSearchExport", true);
                    retObj.AllowChangeProperty.Add("WordRptPermissions", true);
                    retObj.AllowChangeProperty.Add("WordRptMaxTimes", true);
                    retObj.AllowChangeProperty.Add("GtisUserList", true);
                    retObj.AllowChangeProperty.Add("GtisApplCountry", true);
                }
                else
                {
                    if (reasonList.Contains(EnumGtisServiceChangeProject.DelayCoupons) || reasonList.Contains(EnumGtisServiceChangeProject.DelayPersonalServiceDays))
                    {
                        /*放开服务周期和开始结束时间*/
                        retObj.AllowChangeProperty.Add("ServiceMonth", true);
                        retObj.AllowChangeProperty.Add("ServiceCycleStart", true);
                        retObj.AllowChangeProperty.Add("ServiceCycleEnd", true);
                    }
                    var changeProjectList = DbOpe_crm_contract_productserviceinfo_gtis_appl_change_project.Instance.GetChangeProjectByApplyId(agi.Id);
                    if (changeProjectList != null && changeProjectList.Count > 0)
                    {
                        List<string> ServiceCycleKey = new List<string>() { "ServiceMonth", "ServiceCycleStart", "ServiceCycleEnd" };
                        foreach (var changeProject in changeProjectList)
                        {
                            if (ServiceCycleKey.Contains(changeProject.ChangePropertyKey) && (reasonList.Contains(EnumGtisServiceChangeProject.DelayCoupons) || reasonList.Contains(EnumGtisServiceChangeProject.DelayPersonalServiceDays)))
                                continue;
                            if (ServiceCycleKey.Contains(changeProject.ChangePropertyKey))
                            {
                                if (!retObj.AllowChangeProperty.ContainsKey("ServiceMonth"))
                                    retObj.AllowChangeProperty.Add("ServiceMonth", true);
                                if (!retObj.AllowChangeProperty.ContainsKey("ServiceCycleStart"))
                                    retObj.AllowChangeProperty.Add("ServiceCycleStart", true);
                                if (!retObj.AllowChangeProperty.ContainsKey("ServiceCycleEnd"))
                                    retObj.AllowChangeProperty.Add("ServiceCycleEnd", true);
                            }
                            if (!retObj.AllowChangeProperty.ContainsKey(changeProject.ChangePropertyKey))
                                retObj.AllowChangeProperty.Add(changeProject.ChangePropertyKey, true);
                            //变化的国家,由于要高亮显示删除的和新增的国家，这里返回的是JObject
                            if (changeProject.ChangePropertyKey == "GtisApplCountry")
                                retObj.AllowChangeProperty.Add("ChangedCountry", changeProject.ChangedValue);
                            //环球搜结算级别，由于这里初审不填写，所以需要带出原数据，所以这里返回的是JObject
                            if (changeProject.ChangePropertyKey == "GlobalSearchAccountCount")
                                retObj.AllowChangeProperty.Add("GlobalSearchSettlementLevel", changeProject.ChangedValue);
                        }
                    }
                }
            }
            #region 产品信息
            //这里的超级子账号数是套餐自带的
            retObj.ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(agi.ContractProductInfoId);
            //定制报告下载次数
            retObj.Appl_ReportTotalNum = retObj.ProductInfo.CustomizedReportDownloadsNum == null ? 0 : retObj.ProductInfo.CustomizedReportDownloadsNum.Value;
            //这里的超级子账号数是另外买的
            List<ProductInfo_Out> SuperSubAccountList = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(agi.ContractId);
            if (SuperSubAccountList.Count > 0)
            {
                retObj.ProductInfo_SuperSubAccount = SuperSubAccountList.First();
            }
            var productList = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoByContractId(agi.ContractId);
            //是否含VIP零售
            retObj.IsContainsVIP = false;
            var vipProduct = productList.Find(p => p.ProductType == (int)EnumProductType.Vip);
            if (vipProduct != null)
            {
                if (retObj.ProductInfo.ProductType != (int)EnumProductType.Vip)
                {
                    retObj.IsContainsVIP = true;
                    retObj.ProductInfo_VIP = vipProduct;
                    //retObj.ProductInfo.ProductName = retObj.ProductInfo.ProductName + (agi.RetailCountry == (int)EnumGtisRetailCountry.Add ? "（定制零售国家）" : "");//2025.2.24注释 bug5315 列表中已经显示，不再重复显示
                }
            }
            //是否包含环球搜
            retObj.IsContainsGlobalSearch = false;
            var globalProduct = productList.Find(p => p.ProductType == (int)EnumProductType.Global);
            if (globalProduct != null)
            {
                retObj.IsContainsGlobalSearch = true;
                retObj.ProductInfo_GlobalSearch = globalProduct;
            }
            agi.ProductName = retObj.ProductInfo.ProductName;
            if (retObj.IsContainsVIP && retObj.IsContainsGlobalSearch)
                agi.ProductName += "(零售定制国家+环球搜)";
            else if (retObj.IsContainsVIP)
                agi.ProductName += "(零售定制国家)";
            else if (retObj.IsContainsGlobalSearch)
                agi.ProductName += "(环球搜)";
            #endregion
            #region 判断是否是境内单国家/境外 零售  
            retObj.IsVIPSingle = false;
            if (retObj.ProductInfo.ProductType == (int)EnumProductType.Vip)
            {
                if (retObj.ContractInfoAndReceipt.IsOverseasCustomer == true)
                {
                    retObj.IsVIPSingle = true;
                }
                else
                {
                    var countryCount = agi.GtisRetailCountry.Select(c => c.BelongToSid).Distinct().Count();
                    if (countryCount == 1)
                    {
                        retObj.IsVIPSingle = true;
                    }
                }
            }
            #endregion
            #region 超级子账号数汇总
            retObj.SuperSubAccountTotalNum = (retObj.ProductInfo.SuperSubAccountNum == null ? 0 : retObj.ProductInfo.SuperSubAccountNum.Value) + ((retObj.ProductInfo_SuperSubAccount == null || retObj.ProductInfo_SuperSubAccount.SuperSubAccountNum == null) ? 0 : retObj.ProductInfo_SuperSubAccount.SuperSubAccountNum.Value);
            #endregion
            #region 在服信息   在通过之前：变更的话取上一份通过的gtis信息，不是变更的话，如果是续约合同的话，这里是公司上一份合同的服务产品信息
            //var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(agi.ContractProductInfoId);
            var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(gtisContractNum);
            if (gtis != null && (agi.State == (int)EnumProcessStatus.Pass || agi.ProcessingType == (int)EnumProcessingType.Change))
            {
                retObj.GtisInfo = gtis;
                //续约通过或者服务变更通过,优惠券数量使用gtis的数量+历史服务信息historyList的优惠券数量之和
                if (agi.State == (int)EnumProcessStatus.Pass && (agi.ProcessingType == (int)EnumProcessingType.Change || retObj.ContractInfoAndReceipt.ContractType == (int)EnumContractType.ReNew) && retObj.GtisInfo != null)
                {
                    retObj.GtisInfo.CouponCount += historyList.Sum(x => x.CouponCount);
                    if (!string.IsNullOrEmpty(retObj.GtisInfo.CouponIds))
                        retObj.GtisInfo.CouponIds += ",";
                    retObj.GtisInfo.CouponIds += string.Join(',', historyList.Select(x => x.CouponIds).ToList());
                }

                //服务变更且当前提交或拒绝状态，只使用历史服务信息historyList的优惠券数量之和
                else if (agi.ProcessingType == (int)EnumProcessingType.Change && (agi.State == (int)EnumProcessStatus.Submit || agi.State == (int)EnumProcessStatus.Refuse))
                {
                    retObj.GtisInfo.CouponCount = historyList.Sum(x => x.CouponCount);
                    retObj.GtisInfo.CouponIds = string.Join(',', historyList.Select(x => x.CouponIds).ToList());
                }
            }
            else if (retObj.ContractInfoAndReceipt.ContractType == (int)EnumContractType.ReNew)
            {
                var gtisContractNumNoUse = string.Empty;
                SynchroGtisUserData(agi.RenewContractNum, ref userDatas, ref gtisContractNumNoUse);
                retObj.GtisInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(agi.RenewContractNum);
                if (retObj.GtisInfo != null)
                {
                    retObj.GtisInfo.CouponCount = historyList.Sum(x => x.CouponCount);
                    if (!string.IsNullOrEmpty(retObj.GtisInfo.CouponIds))
                        retObj.GtisInfo.CouponIds += ",";
                    retObj.GtisInfo.CouponIds = string.Join(',', historyList.Select(x => x.CouponIds).ToList());
                }

                /*//r.GtisInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetOldContractGtisInfoByCompanyId(r.ContractInfoAndReceipt.FirstParty, r.ContractInfoAndReceipt.Id);
                var cpId = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetContractProductIdByContractNum(agi.RenewContractNum);
                if (cpId != null)
                {
                    r.GtisInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(cpId, true);
                }*/
            }
            else
            {
                retObj.GtisInfo = null;
            }
            if (retObj.GtisInfo != null)
            {
                //计算优惠券累计使用数量
                retObj.GtisInfo.CouponCountTotal = DbOpe_crm_customer_coupon.Instance.GetValidCouponDetailCountByCompanyId(retObj.ContractInfoAndReceipt.FirstParty);
                //获取在服信息中的主账号使用者
                var mainUser = retObj.GtisInfo.GtisUserInfo.Where(e => e.AccountType == (int)EnumGtisAccountType.Main).First();
                List<Db_sysuser_phone> sysPhoneUserList = new List<Db_sysuser_phone>();
                retObj.GtisInfo.MainSysUserPhones = BLL_GtisOpe.Instance.GetAccountUsersInfo(mainUser.UserId).Result.MappingTo<List<MainSysUserPhone>>();
            }
            #endregion
            #region 环球搜服务周期的默认值填充 目前逻辑不全面，暂时注释
            /*if (retObj.ApplGtisInfo.ProcessingType == (int)EnumProcessingType.Change)
            {
                //获取被变更的环球搜服务信息
                var old_global_serve = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(retObj.ContractInfoAndReceipt.ContractNum);
                if (old_global_serve != null)
                    //存在被变更的环球搜服务，本次的开始时间默认值为被变更环球搜服务的结束时间
                    retObj.ApplGtisInfo.GlobalSearchExecuteServiceCycleStart = old_global_serve.ServiceCycleEnd;
                else
                    //不存在被变更的环球搜服务，本次的开始时间默认值为被变更Gtis服务的结束时间
                    retObj.ApplGtisInfo.GlobalSearchExecuteServiceCycleStart = retObj.GtisInfo.ServiceCycleEnd;
                //本次的服务月份默认值为本次变更申请后整体的服务月份减去被变更的gtis服务的服务月份
                retObj.ApplGtisInfo.GlobalSearchExecuteServiceMonth -= retObj.GtisInfo.ServiceMonth;
                //本次的结束时间默认值为本次开始时间+本次服务月份
                retObj.ApplGtisInfo.GlobalSearchExecuteServiceCycleEnd = retObj.ApplGtisInfo.GlobalSearchExecuteServiceCycleStart.Value.AddMonths(retObj.ApplGtisInfo.GlobalSearchExecuteServiceMonth.GetValueOrDefault(0));
            }
            else if (retObj.ContractInfoAndReceipt.ContractType == (int)EnumContractType.ReNew)
            {*//*续约的服务，本次的服务月份默认值为申请的服务月份*//*
                //获取被续约的环球搜服务信息
                var old_global_serve = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(retObj.ApplGtisInfo.RenewContractNum);
                if (old_global_serve != null)
                    //存在被续约的环球搜服务，本次的开始时间默认值为被变更环球搜服务的结束时间
                    retObj.ApplGtisInfo.GlobalSearchExecuteServiceCycleStart = old_global_serve.ServiceCycleEnd;
                else
                    //不存在被续约的环球搜服务，本次的开始时间默认值为被变更Gtis服务的结束时间
                    retObj.ApplGtisInfo.GlobalSearchExecuteServiceCycleStart = retObj.GtisInfo.ServiceCycleEnd;
                //本次的结束时间默认值为本次开始时间+本次服务月份
                retObj.ApplGtisInfo.GlobalSearchExecuteServiceCycleEnd = retObj.ApplGtisInfo.GlobalSearchExecuteServiceCycleStart.Value.AddMonths(retObj.ApplGtisInfo.GlobalSearchExecuteServiceMonth.GetValueOrDefault(0));
            }*/
            #endregion
            #region 在服环球搜信息
            if (retObj.ApplGtisInfo.ProcessingType == (int)EnumProcessingType.Change)
                //获取被变更的环球搜服务信息
                retObj.GlobalSearchInfo = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(retObj.ContractInfoAndReceipt.ContractNum);
            else if (retObj.ContractInfoAndReceipt.ContractType == (int)EnumContractType.ReNew)
                //获取被续约的环球搜服务信息
                retObj.GlobalSearchInfo = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(retObj.ApplGtisInfo.RenewContractNum);
            if (retObj.GlobalSearchInfo != null)
            {
                /*var globalMainUser = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(retObj.GlobalSearchInfo.Id).Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount)?.First();*/
                var globalMainUserList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(retObj.GlobalSearchInfo.Id).Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).ToList();
                string globalMainCode = string.Empty;
                if (globalMainUserList != null && globalMainUserList.Count > 0)
                    globalMainCode = globalMainUserList.First().AccountNumber;
                if (!string.IsNullOrEmpty(globalMainCode))
                {
                    var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, globalMainCode);
                    try
                    {
                        var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                        if ("200".Equals(ret["status"].ToString()))
                        {
                            var results = ret["results"].ToList();
                            results.ForEach(result =>
                            {
                                var enddate = result["enddate"].ToString();
                                if (!string.IsNullOrEmpty(enddate) && enddate != "-")
                                    retObj.GlobalSearchInfo.ExecuteServiceCycleEnd = DateTime.Parse(result["enddate"].ToString());
                            });
                        }
                        else
                        {
                            throw new ApiException(ret["message"].ToString());
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new ApiException(ex.Message);
                    }
                }

            }
            #endregion
            #region 加急状态
            retObj.IsUrgent = retObj.ApplGtisInfo.IsUrgent == true;
            retObj.CouldUrgent = (retObj.ApplGtisInfo.State == (int)EnumProcessStatus.Submit || (retObj.ApplGtisInfo.State == (int)EnumProcessStatus.Pass && retObj.GtisInfo != null && !retObj.GtisInfo.SendAccount)) && !retObj.IsUrgent;
            #endregion
            #region 开通项目 OpenItems   新申请/续约的显示空，变更申请的显示上次开通的，申请通过的显示这次开通的
            retObj.OpenItems = new List<GtisRetailCountry_OUT>();
            if (agi.State == (int)EnumProcessStatus.Pass && retObj.RegisterInfo != null)
            {
                //通过的显示这次开通的
                var countrys = DbOpe_sys_g4_dbnames.Instance.GetDataAllList().Select<G4DbNames>().ToList();
                var gtisCountrys = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetDataList(u => u.ContractServiceInfoGtisId == retObj.RegisterInfo.Id);
                foreach (var country in gtisCountrys)
                {
                    GtisRetailCountry_OUT gtisRetailCountry = country.MappingTo<GtisRetailCountry_OUT>();
                    var c = countrys.Find(c => c.SID == gtisRetailCountry.Sid);
                    if (c != null)
                    {
                        gtisRetailCountry.SidName = c.CountryName;
                        retObj.OpenItems.Add(gtisRetailCountry);
                    }
                }
            }
            else if (agi.ProcessingType == (int)EnumProcessingType.Change)
            {
                //未通过 变更的 显示上次开通的
                if (retObj.GtisInfo != null)
                {
                    retObj.OpenItems = retObj.GtisInfo.GtisRetailCountry;
                }
            }
            #endregion
            #region 变更信息
            retObj.GtisChangeItems = new List<GtisChangeItems>();
            if (gtis != null)
            {
                var changeAbleCols = new List<GtisChangeItems>()
                {
                     new GtisChangeItems(){ PropKey = "PrimaryAccountsNum", PropName = "主账号数量",IsArray = false },
                     new GtisChangeItems(){ PropKey = "SubAccountsNum", PropName = "子账号数量",IsArray = false },
                     new GtisChangeItems(){ PropKey = "SharePeopleNum", PropName = "每个账户共享人数",IsArray = false },
                     new GtisChangeItems(){ PropKey = "ShareUsageNum", PropName = "共享使用总数",IsArray = false },
                     new GtisChangeItems(){ PropKey = "AuthorizationNum", PropName = "子账号授权国家次数",IsArray = false },
                     new GtisChangeItems(){ PropKey = "ServiceCycle", PropName = "服务周期",IsArray = false },
                     new GtisChangeItems(){ PropKey = "RetailCountry", PropName = "零售国家",IsArray = false },
                     new GtisChangeItems(){ PropKey = "GtisRetailCountry", PropName = "选择国家",IsArray = true },
                     new GtisChangeItems(){ PropKey = "ResidentCountries", PropName = "常驻国家",IsArray = true },
                     new GtisChangeItems(){ PropKey = "ResidentCitys", PropName = "常驻城市",IsArray = true }
                };
                foreach (var col in changeAbleCols)
                {
                    var applV = agi.GetType().GetProperty(col.PropKey).GetValue(agi, null);
                    var gtisV = gtis.GetType().GetProperty(col.PropKey).GetValue(gtis, null);
                    if (!col.IsArray)
                    {
                        if (applV.ToString() != gtisV.ToString())
                        {
                            var newItem = col.MappingTo<GtisChangeItems>();
                            newItem.ChangeValue = gtisV.ToString();
                            retObj.GtisChangeItems.Add(newItem);
                        }
                    }
                    else
                    {
                        List<int> appVs = new List<int>();
                        List<int> gtisVs = new List<int>();
                        List<string> gtisVsNames = new List<string>();
                        if (col.PropKey == "GtisRetailCountry")
                        {
                            appVs = agi.GtisRetailCountry.Select(c => c.Sid).ToList();
                            gtisVs = gtis.GtisRetailCountry.Select(c => c.Sid).ToList();
                            gtisVsNames = gtis.GtisRetailCountry.Select(c => c.SidName).ToList();
                        }
                        else if (col.PropKey == "ResidentCountries")
                        {
                            appVs = agi.ResidentCountries.Select(c => c.ResidentCountry).ToList();
                            gtisVs = gtis.ResidentCountries.Select(c => c.ResidentCountry).ToList();
                            gtisVsNames = gtis.ResidentCountries.Select(c => c.ResidentCountryName).ToList();
                        }
                        else if (col.PropKey == "ResidentCitys")
                        {
                            appVs = agi.ResidentCitys.Select(c => c.ResidentCity).ToList();
                            gtisVs = gtis.ResidentCitys.Select(c => c.ResidentCity).ToList();
                            gtisVsNames = gtis.ResidentCitys.Select(c => c.ResidentCityName).ToList();
                        }
                        if (!(appVs.Count == 0 && gtisVs.Count == 0) && appVs.Except(gtisVs).ToList().Count != 0)
                        {
                            var newItem = col.MappingTo<GtisChangeItems>();
                            newItem.ChangeValue = string.Join(',', gtisVsNames);
                            retObj.GtisChangeItems.Add(newItem);
                        }
                    }
                }
            }
            #endregion
            #region 相关赠送信息
            retObj.GetFreeGtisServiceInfo = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetFreeGtisServiceInfo(agi.ContractId);
            retObj.GetPresentedGlobalSearchService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetPresentedGlobalSearchServiceInfo(agi.ContractId);
            retObj.PresentedCollegeService = DbOpe_crm_contract_serviceinfo_college.Instance.GetPresentedCollegeServiceInfo(agi.ContractId);
            #endregion
            #region g5备注
            //复核备注 取g5的备注字段
            if (agi.ProcessingType == (int)EnumProcessingType.Change)
            {
                retObj.G5Remark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(retObj.ContractInfoAndReceipt.ContractNum).Result;
            }
            else if (retObj.ContractInfoAndReceipt.ContractType == (int)EnumContractType.ReNew && retObj.GtisInfo != null)
            {
                retObj.G5Remark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(retObj.GtisInfo.ContractNum).Result;
            }
            else if (agi.ProcessingType == (int)EnumProcessingType.Add)
            {
                if (agi.State == (int)EnumProcessStatus.Pass && retObj.GtisInfo != null)
                {
                    retObj.G5Remark = BLL_GtisOpe.Instance.GetCompanyOtherInfo(retObj.GtisInfo.ContractNum).Result;
                }
                else
                {
                    retObj.G5Remark = "";
                }
            }
            #endregion
            #region 本次备注
            if (retObj.RegisterInfo != null)
            {
                if (retObj.State == (int)EnumContractServiceOpenState.ToBeOpened
                    || retObj.State == (int)EnumContractServiceOpenState.ToBeReview
                    || retObj.State == (int)EnumContractServiceOpenState.Refuse
                    || retObj.State == (int)EnumContractServiceOpenState.TobeChanged)
                {
                    //显示初审备注;
                    retObj.CurrentRemark = retObj.RegisterInfo.RegisteredRemark;
                }
                else if (retObj.State == (int)EnumContractServiceOpenState.Open
                    || retObj.State == (int)EnumContractServiceOpenState.Returned
                    || retObj.State == (int)EnumContractServiceOpenState.NearlyEnd
                    || retObj.State == (int)EnumContractServiceOpenState.Void
                    || retObj.State == (int)EnumContractServiceOpenState.OverDue)
                {
                    //显示复核备注;
                    retObj.CurrentRemark = retObj.RegisterInfo.ReviewerRemark;
                }
            }
            else
            {
                retObj.CurrentRemark = "";
            }
            #endregion
            #region 优惠券
            retObj.CouponList = DbOpe_crm_customer_coupon.Instance.GetCouponDetailByContractId(agi.ContractId);
            /*retObj.DiscountType = (agi.DiscountType != null) ? agi.DiscountType.Value : EnumGtisDiscountType.Unused;
            if(retObj.DiscountType != EnumGtisDiscountType.MergeContract)
            {
                retObj.CouponList = DbOpe_crm_customer_coupon.Instance.GetCouponDetailByContractId(agi.ContractId);
            }*/
            #endregion
            #region 客户信息
            retObj.CustomerId = DbOpe_crm_customer_subcompany.Instance.GetCustomerIdByContractId(agi.ContractId);
            #endregion
            #region  服务变更时服务月份可选的上限增量 实际中用这个值叠加OpeningMonth得到下拉框的月份最大值
            //从历史服务信息中筛选出同一个合同的服务列表
            var sameContractNumServiceList = historyList.Where(e => e.ContractId == agi.ContractId).ToList();
            //计算历史服务信息中的服务月份增量，因为是同一个合同，实际之后服务变更或赠送时长产生的增量
            retObj.ServiceMonthAddUpperLimit4Update = sameContractNumServiceList.Sum(x => x.CouponCount) + sameContractNumServiceList.Sum(x => x.AddFreeGitsServiceMonth);
            //再叠加当前在服信息中出现的服务月份增量，优惠券数量或赠送服务月份
            retObj.ServiceMonthAddUpperLimit4Update += agi.CouponCount.Value + (agi.ServiceAddMonth == null ? 0 : agi.ServiceAddMonth.Value);
            #endregion
            #region  移动端使用 辅助判断由客户服务产品列表或公众号消息进入后,是否需要显示服务申请或服务变更按钮以及默认申请或变更的产品类型
            //判断能否申请服务  当前合同服务产品是否存在有效的服务申请？不可申请：可以申请
            retObj.CouldApplyService = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.CheckCouldApplyServiceByContractId(agi.ContractId);
            //判断能否服务变更
            if (retObj.CouldApplyService)//如果当前数据可以申请服务，则不能进行服务变更
                retObj.CouldUpdateService = false;
            else if (retObj.SalesServiceState != EnumSalesServiceState.VALID && retObj.SalesServiceState != EnumSalesServiceState.OVERDUE && retObj.SalesServiceState != EnumSalesServiceState.REFUSE)
                retObj.CouldUpdateService = false;
            else
                retObj.CouldUpdateService = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.CheckCouldUpdateServiceByContractId(agi.ContractId, agi.Id);
            //产品类型读取productinfo中的productType
            retObj.ProductType = retObj.ProductInfo.ProductType;
            #endregion
            #region 到账备注信息
            //到账备注列表
            retObj.ReceiptRemarks = DbOpe_crm_contract_receiptregister.Instance.GetRemarksByContractId(agi.ContractId);
            //合同的所有到账信息
            retObj.ReceiptRegisterCollectionList = DbOpe_crm_contract_receipt_details.Instance.GetHistoryCollectionInfoItemsByContractReceiptRegisterId(agi.ContractId, String.Empty, (EnumProductType)retObj.ProductInfo.ProductType, retObj.RegisterInfo == null ? null : retObj.RegisterInfo.Id);
            /*//续约的客户5.0到账月份--以最后一次到账年月为准
            retObj.LastReceiptMonth = retObj.ReceiptRegisterCollectionList.Select(e => e.ArrivalDate.ToDateTime()).ToList().OrderByDescending(e => e).First().ToString("yyyy-MM");*/
            #endregion
            //当前服务已绑定的到账信息
            if (retObj.RegisterInfo != null)
                retObj.LinkedReceiptRegisterIds = DbOpe_crm_contract_receiptregister_service.Instance.GetReceiptRegisterIdsByServiceId(retObj.RegisterInfo.Id);
            #region 判断能否使用个人服务天数
            //根据在服信息的服务截止时间进行判断
            if (gtis != null && gtis.ServiceCycleEnd.Value.AddMonths(6) > DateTime.Now.Date)
                retObj.CouldUsePriServDays = true;
            else
                retObj.CouldUsePriServDays = false;
            #endregion
            //在服状态判断
            if (retObj.GtisInfo != null)
            {
                if (retObj.GtisInfo.State == (int)EnumContractServiceState.VALID)
                    retObj.InSerivceState = EnumInServiceState.InService;
                else
                {
                    if (retObj.ApplGtisInfo.CreateDate <= retObj.GtisInfo.ServiceCycleEnd)
                        retObj.InSerivceState = EnumInServiceState.OutAndApplyInService;
                    else
                        retObj.InSerivceState = EnumInServiceState.OutService;
                }
                retObj.InSerivceStateName = retObj.InSerivceState.GetEnumDescription().ToString();
            }
            //合同保护状态判断
            if (retObj.ContractInfoAndReceipt != null && !string.IsNullOrEmpty(retObj.ContractInfoAndReceipt.ProtectionDeadline))
            {
                DateTime protection = DateTime.Parse(retObj.ContractInfoAndReceipt.ProtectionDeadline);
                var now = DateTime.Now;
                if (now <= protection)
                    retObj.ProtectionDeadlineState = EnumProtectionDeadlineState.InProtection;
                else
                {
                    if (retObj.ApplGtisInfo.CreateDate <= protection)
                        retObj.ProtectionDeadlineState = EnumProtectionDeadlineState.OutAndApplyInProtection;
                    else
                        retObj.ProtectionDeadlineState = EnumProtectionDeadlineState.OutProtection;
                }
                retObj.ProtectionDeadlineStateName = retObj.ProtectionDeadlineState.GetEnumDescription().ToString();
            }


            //非管理员不可看等级复核信息
            if (!DbOpe_sys_user.Instance.CheckUserIsManager(UserId))
                retObj.RegisterInfo = null;
            //如果历史服务信息包含在服信息,删除历史服务信息中重复的数据
            if (historyList != null && historyList.Count > 0 && retObj.GtisInfo != null)
            {

                var toRemoveList = historyList.Where(e => e.Id == retObj.GtisInfo.Id).ToList();
                if (toRemoveList != null && toRemoveList.Count > 0)
                {
                    toRemoveList.ForEach(obj => { historyList.Remove(obj); });
                }

            }


            return retObj;
        }
        /// <summary>
        /// 根据当前合同ID获取被续约合同中的GTIS服务信息
        /// </summary>
        /// <param name="contractId"></param>
        /// <param name="FristParty"></param>
        /// <returns></returns>
        public GtisInfo_OUT GetOldContractGtisInfoByContractId(string contractId, string FristParty)
        {
            GtisInfo_OUT gtisInfo_OUT = new GtisInfo_OUT();
            var r = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(contractId);
            var p = FristParty;
            if (string.IsNullOrEmpty(FristParty))
            {
                p = r.FirstParty;
            }
            if (r.ContractType == (int)EnumContractType.ReNew)
            {
                gtisInfo_OUT = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetOldContractGtisInfoByCompanyId(p, r.Id);
            }
            return gtisInfo_OUT;
        }
        /// <summary>
        /// 根据当前合同ID获取被续约合同中的GTIS服务信息
        /// </summary>
        /// <param name="oldContractGtisInfo_In"></param>
        /// <returns></returns>
        public GtisOldInfo_OUT GetOldContractGtisInfo(GetOldContractGtisInfo_In oldContractGtisInfo_In)
        {
            GtisOldInfo_OUT retObj = new GtisOldInfo_OUT();
            string oldContractNum = string.Empty;
            string renewFirstParty = string.Empty;
            if (!string.IsNullOrEmpty(oldContractGtisInfo_In.ContractNum))//存在续约客户编码
            {
                oldContractNum = oldContractGtisInfo_In.ContractNum;
                renewFirstParty = DbOpe_crm_contract.Instance.GetContractByContractNum(oldContractNum).FirstParty;
            }
            else
            {
                if (string.IsNullOrEmpty(oldContractGtisInfo_In.FirstParty))
                    oldContractGtisInfo_In.FirstParty = DbOpe_crm_contract.Instance.QueryByPrimaryKey(oldContractGtisInfo_In.ContractId).FirstParty;
                oldContractNum = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetOldContractContractNumByCompanyId(oldContractGtisInfo_In.FirstParty, oldContractGtisInfo_In.ContractId);
                renewFirstParty = oldContractGtisInfo_In.FirstParty;
            }

            var oldService = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(oldContractNum);
            if (oldService != null)
            {
                retObj = oldService.MappingTo<GtisOldInfo_OUT>();
                retObj.ProductName = DbOpe_crm_product.Instance.QueryByPrimaryKey(oldService.ProductId).ProductName;
                retObj.ServiceCycle = oldService.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "--" + oldService.ServiceCycleEnd.Value.ToString("yyyy-MM-dd");
                //服务连续或者中断期在6个月内的，可以使用个人服务天数
                if (oldService.ServiceCycleEnd.Value.AddMonths(6) >= DateTime.Now.Date)
                    retObj.CouldUsePriServDays = true;
                else
                    retObj.CouldUsePriServDays = false;
            }
            else
                retObj.ContractNum = oldContractNum;
            retObj.RenewFirstParty = renewFirstParty;
            return retObj;
        }
        /// <summary>
        /// 根据申请id获取环球慧思用户信息列表(这个获取的是实际开通的用户列表 10.18 调用gtis来刷新用户状态)
        /// </summary>
        /// <param name="applId"></param>
        /// <returns></returns>
        public List<ServiceInfoGtisUser_OUT> GetContractServiceInfoGtisUserByApplId(string applId)
        {
            return DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.GetContractServiceInfoGtisUserByApplId(applId);
        }
        /// <summary>
        /// 确认发送账号
        /// </summary>
        /// <param name="applId"></param>
        public void SendAccount(string applId)
        {
            DateTime dt = DateTime.Now;
            //获取服务申请信息
            var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(applId);
            if (applData == null || applData.Deleted == true)
            {
                throw new ApiException("未找到对应服务产品申请信息");
            }
            if (applData.State != (int)EnumProcessStatus.Pass)
            {
                throw new ApiException("服务产品审核未通过，无法发送账号信息");
            }
            if (applData.IsInvalid != (int)EnumIsInvalid.Effective)
            {
                throw new ApiException("已失效的审核无法进行操作");
            }
            if (applData.SendAccount)
            {
                throw new ApiException("不能重复确认账号已发送");
            }
            //获取合同信息
            var info = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(applData.ContractId);
            if (info == null)
            {
                throw new ApiException("未找到合同信息");
            }
            //获取gtis产品信息
            var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(applData.ContractProductInfoId);
            if (gtis == null || gtis.State != (int)EnumContractServiceState.VALID)
            {
                throw new ApiException("GTIS产品尚未开通或已失效、过期，不能操作");
            }
            applData.SendAccount = true;
            applData.SendAccountTime = dt;
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);

            MessageMainInfo message = new MessageMainInfo();
            message.Issuer = applData.CreateUser;
            message.MessageTypeToId = applData.Id;
            message.MessagemMainAboutDes = info.ContractName;
            MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.GtisService, EnumMessageStateInfo.Pass, applData.ContractId);
            BLL_MessageCenter.Instance.RealTimeSend(giveBack);
        }
        /// <summary>
        /// 设置环球慧思用户状态信息(启用/停用)  9.28 有还在审核中的申请时，不能对账号进行操作  (目前只有管理员禁用状态可以从这里手动再开通)
        /// </summary>
        /// <param name="setContractServiceInfoGtisUser_IN"></param>
        /// <exception cref="Exception"></exception>
        public void SetContractServiceInfoGtisUser(SetContractServiceInfoGtisUser_IN setContractServiceInfoGtisUser_IN)
        {
            var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == setContractServiceInfoGtisUser_IN.ContractServiceInfoGtisId);
            if (gtis == null)
            {
                throw new ApiException("未找到对应的服务产品信息");
            }
            if (gtis.State != (int)EnumContractServiceState.VALID)
            {
                throw new ApiException("对应的服务产品信息已过期/失效/作废/未开通");
            }
            var appls = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataList(d => d.ContractProductInfoId == gtis.ContractProductInfoId && d.State == (int)EnumProcessStatus.Submit);
            if (appls.Count > 0)
            {
                throw new ApiException("对应的服务产品存在审核中的申请时，不能对账号进行操作");
            }
            var gtisUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == gtis.Id);
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.TransDeal(() =>
            {
                List<BM_ModifyUserState> li = new List<BM_ModifyUserState>();
                foreach (var userStatus in setContractServiceInfoGtisUser_IN.setGtisUserStatuses)
                {
                    var findUser = gtisUsers.Find(u => u.Id == userStatus.Id);
                    if (findUser != null)
                    {
                        if ((findUser.OpeningStatus == (int)EnumGtisUserOpeningStatus.UserManagerStop
                        || findUser.OpeningStatus == (int)EnumGtisUserOpeningStatus.BackManageStop
                        || findUser.OpeningStatus == (int)EnumGtisUserOpeningStatus.Ok) && userStatus.Open)
                        {
                            //启用账号（后补）
                            li.Add(new BM_ModifyUserState()
                            {
                                SysUserID = findUser.UserId,
                                Deleted = 0
                            });
                            findUser.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                        }
                        else if ((findUser.OpeningStatus == (int)EnumGtisUserOpeningStatus.Ok
                        || findUser.OpeningStatus == (int)EnumGtisUserOpeningStatus.Lock
                        || findUser.OpeningStatus == (int)EnumGtisUserOpeningStatus.NoUser
                        || findUser.OpeningStatus == (int)EnumGtisUserOpeningStatus.UserManagerStop
                        || findUser.OpeningStatus == (int)EnumGtisUserOpeningStatus.BackManageStop
                        ) && !userStatus.Open)
                        {
                            //停用账号（后补）
                            li.Add(new BM_ModifyUserState()
                            {
                                SysUserID = findUser.UserId,
                                Deleted = 1
                            });
                            findUser.OpeningStatus = (int)EnumGtisUserOpeningStatus.BackManageStop;
                        }
                        else
                        {
                            throw new ApiException(((EnumGtisUserOpeningStatus)findUser.OpeningStatus).GetEnumDescription() + "的账号无法" + (userStatus.Open ? "启用" : "停用"));
                        }
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(findUser);
                    }
                    else
                    {
                        throw new ApiException("未找到账号");
                    }
                }
                if (!ModifyUserState(li))
                {
                    throw new ApiException("更改gtis账号状态失败");
                }
            });
        }
        /// <summary>
        /// 开通/停用gtis用户
        /// </summary>
        /// <param name="bM_ModifyUserStates"></param>
        /// <returns></returns>
        public bool ModifyUserState(List<BM_ModifyUserState> bM_ModifyUserStates)
        {
            bool r = false;
            try
            {
                if (bM_ModifyUserStates != null && bM_ModifyUserStates.Count > 0)
                {
                    BLL_GtisOpe.Instance.ModifyUserState(bM_ModifyUserStates).Wait();
                }
                r = true;
            }
            catch (Exception e)
            {

            }
            return r;
        }

        /// <summary>
        /// 启用/停用环球慧思产品中所有账户   9.28 有还在审核中的申请时，不能对账号进行操作  11.3有一个成功就提示成功，否则提示失败
        /// </summary>
        /// <param name="setContractServiceInfoGtisUserAll_IN"></param>
        /// <exception cref="Exception"></exception>
        public void SetContractServiceInfoGtisUserAll(SetContractServiceInfoGtisUserAll_IN setContractServiceInfoGtisUserAll_IN)
        {
            var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == setContractServiceInfoGtisUserAll_IN.ContractServiceInfoGtisId);
            if (gtis == null)
            {
                throw new ApiException("未找到对应的服务产品信息");
            }
            if (gtis.State != (int)EnumContractServiceState.VALID)
            {
                throw new ApiException("对应的服务产品信息已过期/失效/作废/未开通");
            }
            var appls = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataList(d => d.ContractProductInfoId == gtis.ContractProductInfoId && d.State == (int)EnumProcessStatus.Submit);
            if (appls.Count > 0)
            {
                throw new ApiException("对应的服务产品存在审核中的申请时，不能对账号进行操作");
            }
            var gtisUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == gtis.Id);
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.TransDeal(() =>
            {
                List<BM_ModifyUserState> li = new List<BM_ModifyUserState>();
                foreach (var user in gtisUsers)
                {
                    if ((user.OpeningStatus == (int)EnumGtisUserOpeningStatus.UserManagerStop || user.OpeningStatus == (int)EnumGtisUserOpeningStatus.BackManageStop) && setContractServiceInfoGtisUserAll_IN.Open)
                    {
                        //启用账号（后补）
                        li.Add(new BM_ModifyUserState()
                        {
                            SysUserID = user.UserId,
                            Deleted = 0
                        });
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                    }
                    else if ((user.OpeningStatus == (int)EnumGtisUserOpeningStatus.Ok || user.OpeningStatus == (int)EnumGtisUserOpeningStatus.Lock || user.OpeningStatus == (int)EnumGtisUserOpeningStatus.NoUser) && !setContractServiceInfoGtisUserAll_IN.Open)
                    {
                        //停用账号（后补）
                        li.Add(new BM_ModifyUserState()
                        {
                            SysUserID = user.UserId,
                            Deleted = 1
                        });
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.BackManageStop;
                    }
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(user);
                }
                if (li.Count == 0)
                {
                    throw new ApiException("没有可以" + (setContractServiceInfoGtisUserAll_IN.Open ? "启用" : "停用") + "的账号");
                }
                if (!ModifyUserState(li))
                {
                    throw new ApiException("更改gtis账号状态失败");
                }
            });
        }

        /// <summary>
        /// 赠送产品（延长GTIS服务，环球搜，慧思学院）
        /// </summary>
        public void AddFreeProductServices(AddFreeProductServices_In addFreeProductServices_In)
        {
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.TransDeal(() =>
            {
                //获取服务产品信息，只有当前开通状态的服务才能进行赠送操作(赠送的不能再赠送)
                var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(addFreeProductServices_In.ProductServiceInfoGtisApplId);
                if (applData == null || applData.Deleted == true)
                {
                    throw new ApiException("未找到对应服务产品申请信息");
                }
                if (applData.IsFree == true)
                {
                    throw new ApiException("已经赠送过的服务，不能再次进行赠送操作");
                }
                if (applData.State != (int)EnumProcessStatus.Pass)
                {
                    throw new ApiException("GTIS产品尚未开通或已失效，不能进行赠送操作");
                }
                if (applData.IsInvalid != (int)EnumIsInvalid.Effective)
                {
                    throw new ApiException("已失效的审核无法进行操作");
                }
                //获取合同信息
                var info = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(applData.ContractId);
                if (info == null)
                {
                    throw new ApiException("未找到合同信息");
                }
                //获取gtis产品信息
                var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(applData.ContractProductInfoId);
                if (gtis.State != (int)EnumContractServiceState.VALID)
                {
                    throw new ApiException("GTIS产品尚未开通或已失效，不能进行赠送操作");
                }
                //处理具体赠送的内容
                if (addFreeProductServices_In.AddFreeProductServices_GTIS != null)
                {
                    //延长GTIS服务时间（相当于做一个变更 且初审自动完成）

                    //11.8 申请需要加验证
                    DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.CheckAppl(applData.ContractProductInfoId, true);
                    #region 添加申请表数据
                    var newAppl = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.AddFreeContractProductServiceInfoGtisAppl(gtis, addFreeProductServices_In.AddFreeProductServices_GTIS);
                    #endregion
                    #region 自动初审
                    var newApplData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetApplGtisInfo(newAppl.Id);
                    AuditContractProductServiceInfoGtisAppl_IN auditContractProductServiceInfoGtisAppl_IN = new AuditContractProductServiceInfoGtisAppl_IN();
                    auditContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId = newAppl.Id;
                    //生成方式这个只有续约合同才有用。 赠送的这里其实随便填
                    auditContractProductServiceInfoGtisAppl_IN.AccountGenerationMethod = (int)EnumGtisAccountGenerationMethod.Generate;
                    auditContractProductServiceInfoGtisAppl_IN.ContractNum = gtis.ContractNum;
                    auditContractProductServiceInfoGtisAppl_IN.PrimaryAccountsNum = gtis.PrimaryAccountsNum.Value;
                    auditContractProductServiceInfoGtisAppl_IN.SubAccountsNum = gtis.SubAccountsNum.Value;
                    auditContractProductServiceInfoGtisAppl_IN.SharePeopleNum = gtis.SharePeopleNum.Value;
                    auditContractProductServiceInfoGtisAppl_IN.ShareUsageNum = gtis.ShareUsageNum;
                    auditContractProductServiceInfoGtisAppl_IN.AuthorizationNum = gtis.AuthorizationNum;
                    auditContractProductServiceInfoGtisAppl_IN.ServiceCycleStart = newApplData.ServiceCycleStart.Value;
                    auditContractProductServiceInfoGtisAppl_IN.ServiceCycleEnd = newApplData.ServiceCycleEnd.Value;
                    auditContractProductServiceInfoGtisAppl_IN.ServiceMonth = newApplData.ServiceMonth;
                    auditContractProductServiceInfoGtisAppl_IN.RetailCountry = newApplData.RetailCountry.Value;



                    auditContractProductServiceInfoGtisAppl_IN.ForbidSearchExport = gtis.ForbidSearchExport.Value;
                    auditContractProductServiceInfoGtisAppl_IN.WordRptPermissions = gtis.WordRptPermissions.Value;
                    auditContractProductServiceInfoGtisAppl_IN.WordRptMaxTimes = gtis.WordRptMaxTimes;
                    //auditContractProductServiceInfoGtisAppl_IN.IsGtisOldCustomer = gtis.IsGtisOldCustomer;
                    auditContractProductServiceInfoGtisAppl_IN.AllCountrySubUser = gtis.AllCountrySubUser.Value;
                    auditContractProductServiceInfoGtisAppl_IN.Pass = true;
                    auditContractProductServiceInfoGtisAppl_IN.GtisRetailCountry = new List<GtisRetailCountry_IN>();
                    foreach (var c in newApplData.GtisRetailCountry)
                    {
                        auditContractProductServiceInfoGtisAppl_IN.GtisRetailCountry.Add(new GtisRetailCountry_IN() { Sid = c.Sid });
                    }
                    auditContractProductServiceInfoGtisAppl_IN.GtisUser = new List<GtisUser_IN>();
                    foreach (var u in newApplData.GtisUserInfo)
                    {
                        auditContractProductServiceInfoGtisAppl_IN.GtisUser.Add(new GtisUser_IN()
                        {
                            AccountNumber = u.AccountNumber,
                            AccountType = u.AccountType.Value,
                            SharePeopleNum = u.SharePeopleNum,
                            AuthorizationNum = u.AuthorizationNum,
                            OpeningStatus = u.OpeningStatus.Value,
                        });
                    }
                    auditContractProductServiceInfoGtisAppl_IN.ResidentCountries = new List<int>();
                    foreach (var rc in newApplData.ResidentCountries)
                    {
                        auditContractProductServiceInfoGtisAppl_IN.ResidentCountries.Add(rc.ResidentCountry);
                    }
                    auditContractProductServiceInfoGtisAppl_IN.ResidentCitys = new List<int>();
                    foreach (var rci in newApplData.ResidentCitys)
                    {
                        auditContractProductServiceInfoGtisAppl_IN.ResidentCitys.Add(rci.ResidentCity);
                    }
                    _AuditContractProductServiceInfoGtisAppl(auditContractProductServiceInfoGtisAppl_IN);
                    //throw new Exception();
                    #endregion
                }
                if (addFreeProductServices_In.PresentedGlobalSearchService_In != null)
                {
                    BLL_ContractServiceGlobalSearch.Instance.PresentedGlobalSearchService(addFreeProductServices_In.PresentedGlobalSearchService_In);
                }
                if (addFreeProductServices_In.PresentedCollegeService_In != null)
                {
                    BLL_ContractServiceCollege.Instance.PresentedCollegeService(addFreeProductServices_In.PresentedCollegeService_In);
                }
            });
        }


        /// <summary>
        /// 登记合同服务信息环球慧思申请信息  //10.10 未到账不允许开通 （未到账没有客户编码）  //11.20 开通后要刷新合同的保护截止日  
        /// 12.5 改成了登记合同服务信息环球慧思申请信息  后面还需要复核进行实际服务的开通
        /// </summary>
        /// <param name="auditContractProductServiceInfoGtisAppl_IN"></param>
        public void AuditContractProductServiceInfoGtisAppl(AuditContractProductServiceInfoGtisAppl_IN auditContractProductServiceInfoGtisAppl_IN)
        {
            //RedisHelper.Set("log_" + currentUser, auditContractProductServiceInfoGtisAppl_IN, TimeSpan.FromMinutes(60));
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.TransDeal(() =>
            {
                _AuditContractProductServiceInfoGtisAppl(auditContractProductServiceInfoGtisAppl_IN);
            });
        }
        private void _AuditContractProductServiceInfoGtisAppl(AuditContractProductServiceInfoGtisAppl_IN auditContractProductServiceInfoGtisAppl_IN)
        {
            DateTime dt = DateTime.Now;
            /*
             根据是否存在已通过，且开通服务的记录，判断是变更申请还是新申请，如果是变更申请需要设置IsChanged为已被变更，ChangedId和HistoryId字段为当前合同的相应服务的正式开通表的主键，便于找历史
             */
            //获取服务申请信息
            var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(auditContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
            if (applData == null || applData.Deleted == true)
            {
                throw new ApiException("未找到对应服务产品申请信息");
            }
            if (applData.State != (int)EnumProcessStatus.Submit)
            {
                throw new ApiException("不能对服务产品申请信息重复审核");
            }
            if (applData.IsInvalid != (int)EnumIsInvalid.Effective)
            {
                throw new ApiException("已失效的审核无法进行操作");
            }
            //获取合同信息
            var info = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(applData.ContractId);
            if (info == null)
            {
                throw new ApiException("未找到合同信息");
            }
            //获取登记信息  (如果是驳回的这里应该有历史信息，初审的没有)
            var registerInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetRegisterInfoByApplId(auditContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (registerInfo != null)
            {
                if (registerInfo.State != (int)EnumContractServiceState.RETURNED && registerInfo.State != (int)EnumContractServiceState.TO_BE_OPENED)
                {
                    throw new ApiException("未找到待开通/被驳回的服务产品");
                }
                var oldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(d => d.Id == registerInfo.Id);
                oldGtis.IsApplHistory = true;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(oldGtis);
                //被驳回服务重新初审通过时，删除被驳回服务的到账绑定关系
                DbOpe_crm_contract_receiptregister_service.Instance.DeleteReturnServiceLinkData(oldGtis.Id);
            }
            Db_crm_contract_serviceinfo_gtis gtis = applData.MappingTo<Db_crm_contract_serviceinfo_gtis>();
            /*if (!string.IsNullOrEmpty(gtis.CouponIds))
            {
                var couponList = gtis.CouponIds.Split(',').ToList();
                gtis.ServiceMonth += couponList.Count();
                gtis.ServiceCycleEnd.Value.AddMonths(couponList.Count());
            }*/
            if (!auditContractProductServiceInfoGtisAppl_IN.Pass)
            {
                //拒绝的更改审核状态为拒绝  同时要加一条内容为空的登记信息
                applData.State = (int)EnumProcessStatus.Refuse;
                applData.ReviewerDate = dt;
                applData.ReviewerId = currentUser;
                applData.Feedback = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                applData.Remark4List = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                //服务变更数据拒绝后该数据状态为无效
                if (applData.ProcessingType == (int)EnumProcessingType.Change)
                    applData.IsInvalid = (int)EnumIsInvalid.Invalid;
                //applData.Feedback = auditContractProductServiceInfoGtisAppl_IN.Remark;
                //9.28 要把现在生效产品对应的申请的状态重新置为有效  (如果剩下也全是拒绝的那就全是无效也无所谓了)
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.ReActiveCurrentGtisAppl(applData.ContractProductInfoId);
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);
                //加一条内容为空的登记拒绝信息
                gtis.Id = Guid.NewGuid().ToString();
                gtis.ProductServiceInfoGtisApplId = applData.Id;
                gtis.ContractNum = info.ContractNum;
                gtis.State = (int)EnumContractServiceState.REFUSE;
                gtis.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
                gtis.Deleted = false;
                gtis.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
                gtis.RegisteredId = currentUser;
                gtis.RegisteredDate = dt;
                gtis.RegisteredRemark = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                gtis.Remark = null;
                gtis.CouponIds = String.Empty;
                gtis.IsOpenCrm = auditContractProductServiceInfoGtisAppl_IN.IsOpenCrm;
                //gtis.RegisteredRemark = auditContractProductServiceInfoGtisAppl_IN.Remark;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(gtis);
                //如果存在优惠券，将状态改为未使用
                if (!string.IsNullOrEmpty(applData.CouponIds))
                {
                    if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(applData.CouponIds.Split(',').ToList(), EnumCouponType.Grant).state != 1)
                        throw new ApiException("优惠券退回失败");
                }
                //如果使用了个人服务天数，修改状态为拒绝
                if (applData.PerlongServiceDays.GetValueOrDefault(0) > 0)
                {
                    BLL_PrivateService.Instance.UsingPrivateServiceForContractService(applData.ContractId, EnumPrivateServiceType.Refuse, applData.Id, applData.PrivateServiceDays.GetValueOrDefault(0), applData.OverServiceDays.GetValueOrDefault(0));
                }
                /*MessageMainInfo message = new MessageMainInfo();
                message.Issuer = applData.CreateUser;
                message.MessageTypeToId = applData.Id;
                message.MessagemMainAboutDes = info.ContractName;

                message.LocalFeedBack = applData.Feedback;
                MessageGiveBack giveBack = BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.GtisService, EnumMessageStateInfo.Refus, applData.ContractId);
                BLL_MessageCenter.Instance.RealTimeSend(giveBack);*/
            }
            else
            {
                applData.Remark4List = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);
                if (StringUtil.IsNullOrEmpty(info.ContractNum))
                {
                    throw new ApiException("未到账合同不能开通服务产品");
                }
                if (auditContractProductServiceInfoGtisAppl_IN.ServiceCycleStart == null || auditContractProductServiceInfoGtisAppl_IN.ServiceCycleEnd == null)
                {
                    throw new ApiException("服务周期不能为空");
                }

                //读前段输入的产品基础信息
                auditContractProductServiceInfoGtisAppl_IN.MappingTo(gtis);

                gtis.Id = Guid.NewGuid().ToString();
                gtis.ProductServiceInfoGtisApplId = applData.Id;
                gtis.ContractNum = info.ContractNum;
                gtis.State = (int)EnumContractServiceState.TO_BE_REVIEW;
                gtis.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
                if (gtis.WordRptPermissions != null && gtis.WordRptPermissions.Value && gtis.WordRptMaxTimes != null && gtis.WordRptMaxTimes.Value > 0)
                {
                    gtis.WordRptMaxTimes = gtis.WordRptMaxTimes;
                }
                else
                {
                    gtis.WordRptMaxTimes = 0;
                    gtis.WordRptPermissions = false;
                }
                gtis.Deleted = false;
                gtis.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
                gtis.Remark = null;
                gtis.RegisteredRemark = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                if (auditContractProductServiceInfoGtisAppl_IN.GtisUser != null)
                {
                    gtis.SubAccountsNum = auditContractProductServiceInfoGtisAppl_IN.GtisUser.Where(e => e.AccountType == (int)EnumGtisAccountType.Sub).Count();
                    //yzt 2025.3.13注释 共享使用总数直接保存为传入的值，不再根据每个账号的共享人数计算求和。
                    //gtis.ShareUsageNum = auditContractProductServiceInfoGtisAppl_IN.GtisUser.Sum(e => e.SharePeopleNum == null ? 0 : e.SharePeopleNum.Value);
                }

                var gtisGlobalsearchAccDiff = auditContractProductServiceInfoGtisAppl_IN.GlobalSearchAccountCount.GetValueOrDefault(0) - gtis.PrimaryAccountsNum - gtis.SubAccountsNum;
                if (gtisGlobalsearchAccDiff > 0)
                {
                    throw new ApiException("gtis总账号数(" + (gtis.PrimaryAccountsNum + gtis.SubAccountsNum) + "个)不能小于环球搜账号数(" + auditContractProductServiceInfoGtisAppl_IN.GlobalSearchAccountCount.GetValueOrDefault(0) + "个)，请重新填写");
                }


                //处理零售国家信息
                if (auditContractProductServiceInfoGtisAppl_IN.RetailCountry == (int)EnumGtisRetailCountry.Add)
                {
                    auditContractProductServiceInfoGtisAppl_IN.GtisRetailCountry.ForEach(c =>
                    {
                        DbOpe_crm_contract_serviceinfo_gtis_country.Instance.Insert(new Db_crm_contract_serviceinfo_gtis_country
                        {
                            Id = Guid.NewGuid().ToString(),
                            ContractServiceInfoGtisId = gtis.Id,
                            Sid = c.Sid
                        });
                    });
                }
                var paramsUsers = new List<GtisUser_IN>();
                if (auditContractProductServiceInfoGtisAppl_IN.GtisUser != null && auditContractProductServiceInfoGtisAppl_IN.GtisUser.Count > 0)
                {
                    paramsUsers = auditContractProductServiceInfoGtisAppl_IN.GtisUser.ToList();
                }

                //判断共享使用次数 每个账号共享使用人数之和 不能大于总的共享使用次数
                if (paramsUsers.Count > 0 && paramsUsers.Where(e => e.OpeningStatus == 1).Sum(p => p.SharePeopleNum) > gtis.ShareUsageNum)
                {
                    throw new ApiException("账号绑定人数之和不能大于绑定使用总数");
                }
                if (paramsUsers.Count > 0 && paramsUsers.Any(e => e.SharePeopleNum == null))
                {
                    throw new ApiException("账号绑定人数不可为空");
                }
                if (paramsUsers.Count > 0)
                {
                    gtis.SharePeopleNum = paramsUsers.Max(u => u.SharePeopleNum);
                    gtis.AuthorizationNum = paramsUsers.Find(u => u.AccountType == (int)EnumGtisAccountType.Main).AuthorizationNum;
                }
                if (paramsUsers.Count > 0 && (auditContractProductServiceInfoGtisAppl_IN.IsOpenCrm == 1 && paramsUsers.Find(u => u.AccountType == (int)EnumGtisAccountType.Main).SharePeopleNum > auditContractProductServiceInfoGtisAppl_IN.MaxCrmAccount))
                {
                    throw new ApiException("主账号绑定人数不能大于SalesWits账户最大数量");
                }


                #region 超级子账号数汇总
                //这里的超级子账号数是套餐自带的
                var ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(applData.ContractProductInfoId);
                //定制报告下载次数
                var Appl_ReportTotalNum = ProductInfo.CustomizedReportDownloadsNum == null ? 0 : ProductInfo.CustomizedReportDownloadsNum.Value;
                //这里的超级子账号数是另外买的
                List<ProductInfo_Out> SuperSubAccountList = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(applData.ContractId);
                ProductInfo_Out ProductInfo_SuperSubAccount = null;
                if (SuperSubAccountList.Count > 0)
                {
                    ProductInfo_SuperSubAccount = SuperSubAccountList.First();
                }
                var SuperSubAccountTotalNum = (ProductInfo.SuperSubAccountNum == null ? 0 : ProductInfo.SuperSubAccountNum.Value) + ((ProductInfo_SuperSubAccount == null || ProductInfo_SuperSubAccount.SuperSubAccountNum == null) ? 0 : ProductInfo_SuperSubAccount.SuperSubAccountNum.Value);
                //分配超级子账号，超级子账号数量不能大于子账号授权国家次数
                int LeftSuperSubAccountTotalNum = Math.Min(SuperSubAccountTotalNum, gtis.AuthorizationNum.Value);
                #endregion
                if (applData.ProcessingType == (int)EnumProcessingType.Change)
                {
                    gtis.SubAccountsNum = paramsUsers.FindAll(u => u.AccountType != (int)EnumGtisAccountType.Main).Count;
                    //服务变更，默认前端是继承账号，读前端的账号信息(老的账号信息不动，复制一份老的账号信息，在老账号基础上读取前端账号信息并进行更新)
                    gtis.AccountGenerationMethod = (int)EnumGtisAccountGenerationMethod.Generate;
                    //获取变更前用户信息
                    //GtisInfo_OUT OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(applData.ContractProductInfoId, true);
                    GtisInfo_OUT OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(info.ContractNum);
                    var OldUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == OldGtis.Id);
                    gtis.HistoryId = OldGtis.Id;
                    //取上一次的新老客户标记
                    gtis.IsGtisOldCustomer = OldGtis.IsGtisOldCustomer;

                    //10.9 变更的用户在数据库也要进行添加而不是更新 这样撤回更改时，账号的回退会更方便一点
                    foreach (var oldUser in OldUsers)
                    {
                        var findIndex = paramsUsers.FindIndex(u => u.AccountNumber == oldUser.AccountNumber);
                        if (findIndex >= 0)
                        {
                            //10.9 更新的用户 (变更的用户在数据库也要进行添加而不是更新 这样撤回更改时，账号的回退会更方便一点)
                            UpdateGtisUser(paramsUsers[findIndex], oldUser, gtis, ref LeftSuperSubAccountTotalNum);
                            paramsUsers.RemoveAt(findIndex);
                        }
                    }
                    foreach (var newUser in paramsUsers)
                    {
                        AddGtisUser(newUser, gtis, ref LeftSuperSubAccountTotalNum);
                    }
                    if (auditContractProductServiceInfoGtisAppl_IN.MainSysUserPhones != null && auditContractProductServiceInfoGtisAppl_IN.MainSysUserPhones.Count > 0)
                    {
                        if (auditContractProductServiceInfoGtisAppl_IN.MainSysUserPhones.Count > auditContractProductServiceInfoGtisAppl_IN.MaxCrmAccount)
                        {
                            throw new ApiException("开通SalesWits权限的使用者数量不能超过SalesWits账户最大数量");
                        }
                        foreach (var phoneUser in auditContractProductServiceInfoGtisAppl_IN.MainSysUserPhones)
                        {
                            AddGtisPhoneUser(applData.Id, gtis.Id, phoneUser);
                        }
                    }
                    /*//取gtis账号数和环球搜账号数的差
                    var gtisGlobalsearchAccDiff = gtis.GlobalSearchAccountCount.GetValueOrDefault(0) - gtis.PrimaryAccountsNum.GetValueOrDefault(0) - gtis.SubAccountsNum.GetValueOrDefault(0);
                    //如果差值 > 0,需要插入单环球搜的子账号
                    if (gtisGlobalsearchAccDiff > 0)
                    {
                        for (int i = 0; i < gtisGlobalsearchAccDiff; i++)
                        {
                            AddGtisUser(null, gtis, ref LeftSuperSubAccountTotalNum, EnumGtisAccountType.Sub);
                        }
                    }*/

                    //判断当前合同是否存在审核状态中的环球搜
                    if (DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckContractHasSubmitApply(OldGtis.ContractId))
                    {
                        RefineOldSubmitGlobalSearchData(OldGtis);
                        #region 集成到RefineOldSubmitGlobalSearchData中，注释
                        /*
                        //同步Gtis系统数据
                        var gtisOpeUserList = BLL_GtisOpe.Instance.GetUserInfo(gtis.ContractNum).Result;
                        //当前合同对应的g5账号绑定的环球搜码集合
                        var g5GlobalSearchCodes = gtisOpeUserList.Where(e => !string.IsNullOrEmpty(e.GlobalSearchCode)).Select(e => e.GlobalSearchCode).Distinct().ToList();
                        //如果gtis账号列表中存在环球搜码，调用环球搜接口查看环球搜码信息
                        if (g5GlobalSearchCodes.Count > 0)
                        {
                            try
                            {
                                //调用环球搜查询接口，查询每个环球搜码的账号类别及服务时间
                                var queryStr = string.Join(';', g5GlobalSearchCodes);
                                var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, queryStr);
                                var globalSearchUserList = new List<Db_crm_contract_serviceinfo_globalsearch_user>();
                                var servCycleStart = new DateTime();
                                var ServCycleEnd = new DateTime();
                                var servMonth = new int();
                                var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                                if ("200".Equals(ret["status"].ToString()))
                                {
                                    var results = ret["results"].ToList();
                                    results.ForEach(result =>
                                    {
                                        var globalSearchUser = new Db_crm_contract_serviceinfo_globalsearch_user();
                                        globalSearchUser.Id = Guid.NewGuid().ToString();
                                        globalSearchUser.AccountNumber = result["idCst"].ToString();
                                        EnumContractServiceGlobalSearchUserState openingStatus = new EnumContractServiceGlobalSearchUserState();
                                        if ("active".Equals(result["status"].ToString()))
                                            //返回正常，账号状态标记正常
                                            globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                                        else if ("disabled".Equals(result["status"].ToString()))
                                            //返回过期，账号状态标记停用
                                            globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.Stop;
                                        else if ("noexisted".Equals(result["status"].ToString()))
                                            //返回不存在，账号状态标记异常
                                            globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                                        globalSearchUser.AccountType = result["acctType"].ToString() == "主账户" ? EnumContractServiceGlobalSearchAccountType.PrimaryAccount : EnumContractServiceGlobalSearchAccountType.SubAccount;
                                        globalSearchUser.StartDate = DateTime.Parse(result["begindate"].ToString());
                                        globalSearchUser.EndDate = DateTime.Parse(result["enddate"].ToString()); ;
                                        globalSearchUserList.Add(globalSearchUser);
                                        //获取服务开始/结束时间，计算服务月份
                                        if (globalSearchUser.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount)
                                        {
                                            servCycleStart = globalSearchUser.StartDate.Value;
                                            ServCycleEnd = globalSearchUser.EndDate.Value;
                                            //计算服务月份
                                            DateTime startDate = globalSearchUser.StartDate.Value; ;
                                            DateTime endDate = globalSearchUser.EndDate.Value;
                                            int monthsDifference = 0;
                                            while (startDate <= endDate)
                                            {
                                                startDate = startDate.AddMonths(1);
                                                monthsDifference++;
                                            }
                                            monthsDifference--;
                                            servMonth = monthsDifference;
                                        }
                                    });
                                }

                                //查询当前合同的审核中的环球搜申请数据
                                var globalSearchApply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetSubmitApplyByContractId(gtis.ContractId);
                                //查询当前合同的审核中的环球搜服务数据
                                var globalSearchServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetSerivceDataByApplyId(globalSearchApply.Id);
                                //如果不存在服务数据或当前服务是被驳回数据，需要补充服务数据
                                if (globalSearchServe == null || globalSearchServe.State == EnumContractServiceState.RETURNED)
                                {
                                    //被驳回数据需要置位历史后再补充数据
                                    if (globalSearchServe != null && globalSearchServe.State == EnumContractServiceState.RETURNED)
                                    {
                                        globalSearchServe.IsHistory = true;
                                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(globalSearchServe);
                                    }
                                    globalSearchServe = new Db_crm_contract_serviceinfo_globalsearch();
                                    globalSearchServe.ContractNum = gtis.ContractNum;
                                    globalSearchServe.ContractId = gtis.ContractId;
                                    globalSearchServe.ProductId = globalSearchApply.ProductId;
                                    globalSearchServe.ContractProductInfoId = globalSearchApply.ContractProductInfoId;
                                    globalSearchServe.ProductServiceInfoGlobalSearchApplId = globalSearchApply.Id;
                                    globalSearchServe.AccountGenerationMethod = OldGtis.AccountGenerationMethod;
                                    globalSearchServe.ServiceCycleStart = servCycleStart;
                                    globalSearchServe.ServiceCycleEnd = ServCycleEnd;
                                    globalSearchServe.ServiceMonth = servMonth;
                                    globalSearchServe.State = EnumContractServiceState.VALID;
                                    globalSearchServe.PrimaryAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).Count();
                                    globalSearchServe.SubAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).Count();
                                    globalSearchServe.IsChanged = false;
                                    globalSearchServe.IsHistory = false;//如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
                                    globalSearchServe.RegisteredId = UserId;
                                    globalSearchServe.RegisteredTime = DateTime.Now;
                                    globalSearchServe.RegisteredRemark = "同步数据自动通过";
                                    globalSearchServe.ProcessingType = globalSearchApply.ProcessingType;
                                    globalSearchServe.ReviewerId = UserId;
                                    globalSearchServe.ReviewerTime = DateTime.Now;
                                    globalSearchServe.ReviewerRemark = "同步数据自动通过";
                                    globalSearchServe.UpdateDate = DateTime.Now;
                                    globalSearchServe.UpdateUser = UserId;
                                    globalSearchServe.GlobalSearchRemark = String.Empty;
                                    globalSearchServe.Remark = String.Empty;
                                    globalSearchServe.Id = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.InsertDataReturnId(globalSearchServe).ToString();
                                }
                                else
                                {
                                    globalSearchServe.State = EnumContractServiceState.VALID;
                                    globalSearchServe.ServiceCycleStart = servCycleStart;
                                    globalSearchServe.ServiceCycleEnd = ServCycleEnd;
                                    globalSearchServe.ServiceMonth = servMonth;
                                    globalSearchServe.PrimaryAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).Count();
                                    globalSearchServe.SubAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).Count();
                                    globalSearchServe.ReviewerId = UserId;
                                    globalSearchServe.ReviewerTime = DateTime.Now;
                                    globalSearchServe.ReviewerRemark = "同步数据自动通过";
                                    globalSearchServe.GlobalSearchRemark = String.Empty;
                                    globalSearchServe.Remark = String.Empty;
                                    DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(globalSearchServe);
                                }

                                globalSearchApply.State = EnumProcessStatus.Pass.ToInt();
                                DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.Update(globalSearchApply);

                                globalSearchUserList.ForEach(globalSearchCode =>
                                {
                                    globalSearchCode.ContractServiceInfoGlobalSearchId = globalSearchServe.Id;
                                    globalSearchCode.ProductServiceInfoGlobalSearchApplId = globalSearchApply.Id;
                                });
                                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertListData(globalSearchUserList);
                            }
                            catch
                            {
                                throw new ApiException("环球搜账号状态查询失败");
                            }
                        }*/
                        #endregion
                    }
                }
                else
                {
                    //默认是新客户
                    gtis.IsGtisOldCustomer = false;
                    //续约
                    if (info.ContractType == (int)EnumContractType.ReNew)
                    {
                        GtisInfo_OUT OldGtis = new GtisInfo_OUT();
                        if (string.IsNullOrEmpty(applData.RenewFirstParty))
                        {
                            throw new ApiException("申请时需要指定续约公司，请拒绝后让客户经理重新提交服务续约申请");
                        }
                        //续约的，读前台的账号生成方式
                        if (auditContractProductServiceInfoGtisAppl_IN.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake)
                        {
                            //重新生成
                            RemakeGtisUsers(gtis, ref LeftSuperSubAccountTotalNum);
                        }
                        else
                        {
                            gtis.SubAccountsNum = paramsUsers.FindAll(u => u.AccountType != (int)EnumGtisAccountType.Main).Count;
                            //继承账号，读前端的账号信息
                            //这里得找一下上一份合同的信息，用续约公司找
                            /*var companyOldContractProductInfoId = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetOldContractProductIdByCompanyId(applData.RenewFirstParty, info.Id);
                            if (string.IsNullOrEmpty(companyOldContractProductInfoId))
                            {
                                throw new ApiException("未找到上一份服务信息，无法使用原账户");
                            }*/

                            /*if (OldGtis.ContractNum != applData.RenewContractNum)
                            {
                                throw new ApiException("被续约合同信息已经变更，请拒绝后重新申请续约");
                            }*/
                            OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(applData.RenewContractNum);//DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(companyOldContractProductInfoId, true);
                            var OldUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == OldGtis.Id);
                            if (info.IsOverseasCustomer != null && !info.IsOverseasCustomer.Value && new TimeSpan(gtis.ServiceCycleStart.Value.Ticks - OldGtis.ServiceCycleEnd.Value.Ticks).TotalDays > 90)
                            {
                                //超过90天续约的境内客户，算新客户
                                gtis.IsGtisOldCustomer = false;
                            }
                            else
                            {
                                //其他情况沿用续约之前的新老客户标记;
                                gtis.IsGtisOldCustomer = OldGtis.IsGtisOldCustomer;
                            }
                            if (OldGtis.State != (int)EnumContractServiceState.OUT)
                            {
                                //如果续约使用的账号是当前未过期账号，要记录原账户审核的id，以便后续撤回
                                gtis.HistoryId = OldGtis.Id;
                            }
                            foreach (var oldUser in OldUsers)
                            {
                                var findIndex = paramsUsers.FindIndex(u => u.AccountNumber == oldUser.AccountNumber);
                                if (findIndex >= 0)
                                {
                                    //10.9 更新的用户 (变更的用户在数据库也要进行添加而不是更新 这样撤回更改时，账号的回退会更方便一点)
                                    UpdateGtisUser(paramsUsers[findIndex], oldUser, gtis, ref LeftSuperSubAccountTotalNum, true);
                                    paramsUsers.RemoveAt(findIndex);
                                }
                            }
                            foreach (var newUser in paramsUsers)
                            {
                                AddGtisUser(newUser, gtis, ref LeftSuperSubAccountTotalNum);
                            }
                            if (auditContractProductServiceInfoGtisAppl_IN.MainSysUserPhones != null && auditContractProductServiceInfoGtisAppl_IN.MainSysUserPhones.Count > 0)
                            {
                                if (auditContractProductServiceInfoGtisAppl_IN.MainSysUserPhones.Count > auditContractProductServiceInfoGtisAppl_IN.MaxCrmAccount)
                                {
                                    throw new ApiException("开通SalesWits权限的使用者数量不能超过SalesWits账户最大数量");
                                }
                                foreach (var phoneUser in auditContractProductServiceInfoGtisAppl_IN.MainSysUserPhones)
                                {
                                    AddGtisPhoneUser(applData.Id, gtis.Id, phoneUser);
                                }
                            }
                            /*//取gtis账号数和环球搜账号数的差
                            var gtisGlobalsearchAccDiff = gtis.GlobalSearchAccountCount.GetValueOrDefault(0) - gtis.PrimaryAccountsNum.GetValueOrDefault(0) - gtis.SubAccountsNum.GetValueOrDefault(0);
                            //如果差值 > 0,需要插入单环球搜的子账号
                            if (gtisGlobalsearchAccDiff > 0)
                            {
                                for (int i = 0; i < gtisGlobalsearchAccDiff; i++)
                                {
                                    AddGtisUser(null, gtis, ref LeftSuperSubAccountTotalNum, EnumGtisAccountType.Sub);
                                }
                            }*/
                        }


                        //判断当前合同是否存在审核状态中的环球搜
                        if (DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.CheckContractHasSubmitApply(OldGtis.ContractId))
                        {
                            RefineOldSubmitGlobalSearchData(OldGtis);
                            #region 集成到RefineOldSubmitGlobalSearchData中，注释
                            /*//同步Gtis系统数据
                            var gtisOpeUserList = BLL_GtisOpe.Instance.GetUserInfo(OldGtis.ContractNum).Result;
                            //当前合同对应的g5账号绑定的环球搜码集合
                            var g5GlobalSearchCodes = gtisOpeUserList.Where(e => !string.IsNullOrEmpty(e.GlobalSearchCode)).Select(e => e.GlobalSearchCode).Distinct().ToList();
                            //如果gtis账号列表中存在环球搜码，调用环球搜接口查看环球搜码信息
                            if (g5GlobalSearchCodes.Count > 0)
                            {
                                try
                                {
                                    //调用环球搜查询接口，查询每个环球搜码的账号类别及服务时间
                                    var queryStr = string.Join(';', g5GlobalSearchCodes);
                                    var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, queryStr);
                                    var globalSearchUserList = new List<Db_crm_contract_serviceinfo_globalsearch_user>();
                                    var servCycleStart = new DateTime();
                                    var ServCycleEnd = new DateTime();
                                    var servMonth = new int();
                                    var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                                    if ("200".Equals(ret["status"].ToString()))
                                    {
                                        var results = ret["results"].ToList();
                                        results.ForEach(result =>
                                        {
                                            var globalSearchUser = new Db_crm_contract_serviceinfo_globalsearch_user();
                                            globalSearchUser.Id = Guid.NewGuid().ToString();
                                            globalSearchUser.AccountNumber = result["idCst"].ToString();
                                            EnumContractServiceGlobalSearchUserState openingStatus = new EnumContractServiceGlobalSearchUserState();
                                            if ("active".Equals(result["status"].ToString()))
                                                //返回正常，账号状态标记正常
                                                globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                                            else if ("disabled".Equals(result["status"].ToString()))
                                                //返回过期，账号状态标记停用
                                                globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.Stop;
                                            else if ("noexisted".Equals(result["status"].ToString()))
                                                //返回不存在，账号状态标记异常
                                                globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                                            globalSearchUser.AccountType = result["acctType"].ToString() == "主账户" ? EnumContractServiceGlobalSearchAccountType.PrimaryAccount : EnumContractServiceGlobalSearchAccountType.SubAccount;
                                            globalSearchUser.StartDate = DateTime.Parse(result["begindate"].ToString());
                                            globalSearchUser.EndDate = DateTime.Parse(result["enddate"].ToString()); ;
                                            globalSearchUserList.Add(globalSearchUser);
                                            //获取服务开始/结束时间，计算服务月份
                                            if (globalSearchUser.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount)
                                            {
                                                servCycleStart = globalSearchUser.StartDate.Value;
                                                ServCycleEnd = globalSearchUser.EndDate.Value;
                                                //计算服务月份
                                                DateTime startDate = globalSearchUser.StartDate.Value; ;
                                                DateTime endDate = globalSearchUser.EndDate.Value;
                                                int monthsDifference = 0;
                                                while (startDate <= endDate)
                                                {
                                                    startDate = startDate.AddMonths(1);
                                                    monthsDifference++;
                                                }
                                                monthsDifference--;
                                                servMonth = monthsDifference;
                                            }
                                        });
                                    }

                                    //查询当前合同的审核中的环球搜申请数据
                                    var globalSearchApply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetSubmitApplyByContractId(OldGtis.ContractId);
                                    //查询当前合同的审核中的环球搜服务数据
                                    var globalSearchServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetSerivceDataByApplyId(globalSearchApply.Id);
                                    //如果不存在服务数据或当前服务是被驳回数据，需要补充服务数据
                                    if (globalSearchServe == null || globalSearchServe.State == EnumContractServiceState.RETURNED)
                                    {
                                        //被驳回数据需要置位历史后再补充数据
                                        if (globalSearchServe != null && globalSearchServe.State == EnumContractServiceState.RETURNED)
                                        {
                                            globalSearchServe.IsHistory = true;
                                            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(globalSearchServe);
                                        }
                                        globalSearchServe = new Db_crm_contract_serviceinfo_globalsearch();
                                        globalSearchServe.ContractNum = OldGtis.ContractNum;
                                        globalSearchServe.ContractId = OldGtis.ContractId;
                                        globalSearchServe.ProductId = globalSearchApply.ProductId;
                                        globalSearchServe.ContractProductInfoId = globalSearchApply.ContractProductInfoId;
                                        globalSearchServe.ProductServiceInfoGlobalSearchApplId = globalSearchApply.Id;
                                        globalSearchServe.AccountGenerationMethod = OldGtis.AccountGenerationMethod;
                                        globalSearchServe.ServiceCycleStart = servCycleStart;
                                        globalSearchServe.ServiceCycleEnd = ServCycleEnd;
                                        globalSearchServe.ServiceMonth = servMonth;
                                        globalSearchServe.State = EnumContractServiceState.VALID;
                                        globalSearchServe.PrimaryAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).Count();
                                        globalSearchServe.SubAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).Count();
                                        globalSearchServe.IsChanged = false;
                                        globalSearchServe.IsHistory = false;//如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
                                        globalSearchServe.RegisteredId = UserId;
                                        globalSearchServe.RegisteredTime = DateTime.Now;
                                        globalSearchServe.RegisteredRemark = "同步数据自动通过";
                                        globalSearchServe.ProcessingType = globalSearchApply.ProcessingType;
                                        globalSearchServe.ReviewerId = UserId;
                                        globalSearchServe.ReviewerTime = DateTime.Now;
                                        globalSearchServe.ReviewerRemark = "同步数据自动通过";
                                        globalSearchServe.UpdateDate = DateTime.Now;
                                        globalSearchServe.UpdateUser = UserId;
                                        globalSearchServe.GlobalSearchRemark = String.Empty;
                                        globalSearchServe.Remark = String.Empty;
                                        globalSearchServe.Id = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.InsertDataReturnId(globalSearchServe).ToString();
                                    }
                                    else
                                    {
                                        globalSearchServe.State = EnumContractServiceState.VALID;
                                        globalSearchServe.ServiceCycleStart = servCycleStart;
                                        globalSearchServe.ServiceCycleEnd = ServCycleEnd;
                                        globalSearchServe.ServiceMonth = servMonth;
                                        globalSearchServe.PrimaryAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).Count();
                                        globalSearchServe.SubAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).Count();
                                        globalSearchServe.ReviewerId = UserId;
                                        globalSearchServe.ReviewerTime = DateTime.Now;
                                        globalSearchServe.ReviewerRemark = "同步数据自动通过";
                                        globalSearchServe.GlobalSearchRemark = String.Empty;
                                        globalSearchServe.Remark = String.Empty;
                                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(globalSearchServe);
                                    }

                                    globalSearchApply.State = EnumProcessStatus.Pass.ToInt();
                                    DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.Update(globalSearchApply);

                                    globalSearchUserList.ForEach(globalSearchCode =>
                                    {
                                        globalSearchCode.ContractServiceInfoGlobalSearchId = globalSearchServe.Id;
                                        globalSearchCode.ProductServiceInfoGlobalSearchApplId = globalSearchApply.Id;
                                    });
                                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertListData(globalSearchUserList);
                                }
                                catch
                                {
                                    throw new ApiException("环球搜账号状态查询失败");
                                }
                            }*/
                            #endregion
                        }




                    }
                    else
                    {
                        //新增的，重新生成账号
                        RemakeGtisUsers(gtis, ref LeftSuperSubAccountTotalNum);
                    }
                }
                gtis.RegisteredId = currentUser;
                gtis.RegisteredDate = dt;
                //gtis.RegisteredRemark = auditContractProductServiceInfoGtisAppl_IN.Remark;

                //绑定到账信息并计算环球搜结算单位
                if (auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds != null && auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds.Count > 0)
                {
                    EnumProductType productType = EnumProductType.Gtis;
                    var productInfo = DbOpe_crm_product.Instance.QueryByPrimaryKey(gtis.ProductId);
                    if (productInfo != null && productInfo.ProductType != null)
                        productType = (EnumProductType)productInfo.ProductType;
                    DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptData(auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds, gtis.Id, productType);
                    gtis.GlobalSearchSettlementCount = DbOpe_crm_contract_receiptregister_service.Instance.GetGlobalSearchSettlementCountByReceiptRegisterId(auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds);
                }
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(gtis);
                //记录常驻国家和省市
                List<Db_crm_contract_serviceinfo_gtis_residentcountry> ResidentCountryList = new List<Db_crm_contract_serviceinfo_gtis_residentcountry>();
                foreach (int g in auditContractProductServiceInfoGtisAppl_IN.ResidentCountries)
                {
                    Db_crm_contract_serviceinfo_gtis_residentcountry Gtis_ResidentCountry = new Db_crm_contract_serviceinfo_gtis_residentcountry();
                    Gtis_ResidentCountry.ResidentCountry = g;
                    Gtis_ResidentCountry.ContractServiceInfoGtisId = gtis.Id;
                    ResidentCountryList.Add(Gtis_ResidentCountry);
                }
                DbOpe_crm_contract_serviceinfo_gtis_residentcountry.Instance.InsertListData(ResidentCountryList);

                List<Db_crm_contract_serviceinfo_gtis_residentcity> ResidentCityList = new List<Db_crm_contract_serviceinfo_gtis_residentcity>();
                foreach (int g in auditContractProductServiceInfoGtisAppl_IN.ResidentCitys)
                {
                    Db_crm_contract_serviceinfo_gtis_residentcity Gtis_ResidentCity = new Db_crm_contract_serviceinfo_gtis_residentcity();
                    Gtis_ResidentCity.ResidentCity = g;
                    Gtis_ResidentCity.ContractServiceInfoGtisId = gtis.Id;
                    ResidentCityList.Add(Gtis_ResidentCity);
                }
                DbOpe_crm_contract_serviceinfo_gtis_residentcity.Instance.InsertListData(ResidentCityList);



            }
            EnumContractServiceOpenState state = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceOpenState(applData.State.Value, applData.ProcessingType.Value, gtis.State, gtis.ServiceCycleEnd);

            var workFlowName = applData.ProcessingType == (int)EnumProcessingType.Change ? "GTIS服务变更审批流程" : "GTIS服务审批流程";
            if (auditContractProductServiceInfoGtisAppl_IN.Pass)
            {
                if (applData.IsFree == true)
                {
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_gtis_appl, ContractInfoAndReceipt_Out>(workFlowName, applData.Id, applData, info, applData.Feedback, state.GetEnumDescription(), "赠送");
                }
                else
                {
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_gtis, ContractInfoAndReceipt_Out>(workFlowName, applData.Id, gtis, info, gtis.RegisteredRemark, state.GetEnumDescription(), "初审");
                }
            }
            else
            {
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_gtis_appl, ContractInfoAndReceipt_Out>(workFlowName, applData.Id, applData, info, info.Issuer, applData.Feedback, state.GetEnumDescription(), "初审");
            }

        }

        private void NewAuditContractProductServiceInfoGtisAppl(AuditContractProductServiceInfoGtisAppl_IN auditContractProductServiceInfoGtisAppl_IN)
        {
            #region 前置验证和必要数据获取
            //获取当前时间
            DateTime now = DateTime.Now;
            //获取申请信息
            var curApplyData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(auditContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
            //获取产品信息
            var ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(curApplyData.ContractProductInfoId);
            //基础校验，申请信息存在且有效并在申请状态下才可以初审
            if (curApplyData == null || curApplyData.Deleted == true)
                throw new ApiException("未找到对应服务产品申请信息");
            if (curApplyData.State != (int)EnumProcessStatus.Submit)
                throw new ApiException("不能对服务产品申请信息重复审核");
            if (curApplyData.IsInvalid != (int)EnumIsInvalid.Effective)
                throw new ApiException("已失效的审核无法进行操作");
            //获取合同信息
            var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(curApplyData.ContractId);
            if (contractInfo == null)
                throw new ApiException("未找到合同信息");
            //查看是否有被驳回的服务信息
            var rejServeData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceByApplyId(auditContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
            //如果有上一条登记审核信息 将之前驳回的申请置为历史
            if (rejServeData != null)
            {
                if (rejServeData.State != (int)EnumContractServiceState.RETURNED && rejServeData.State != (int)EnumContractServiceState.TO_BE_OPENED)
                    throw new ApiException("未找到待开通/被驳回的服务产品");
                rejServeData.IsApplHistory = true;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(rejServeData);
                //被驳回服务重新初审通过时，删除被驳回服务的到账绑定关系
                DbOpe_crm_contract_receiptregister_service.Instance.DeleteReturnServiceLinkData(rejServeData.Id);
            }
            //由当前申请数据映射出当前服务数据对象
            Db_crm_contract_serviceinfo_gtis curServeData = curApplyData.MappingTo<Db_crm_contract_serviceinfo_gtis>();
            #endregion
            #region 初审拒绝的处理逻辑
            if (!auditContractProductServiceInfoGtisAppl_IN.Pass)
            {
                //拒绝的更改审核状态为拒绝  同时要加一条内容为空的登记信息
                curApplyData.State = (int)EnumProcessStatus.Refuse;
                curApplyData.ReviewerDate = now;
                curApplyData.ReviewerId = currentUser;
                curApplyData.Feedback = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                curApplyData.Remark4List = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                //服务变更数据拒绝后该数据状态为无效
                if (curApplyData.ProcessingType == (int)EnumProcessingType.Change)
                    curApplyData.IsInvalid = (int)EnumIsInvalid.Invalid;
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(curApplyData);
                //将当前生效服务(状态为正常、待生效、过期)对应的申请的状态重新置为有效
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.ReActiveCurrentGtisAppl(curApplyData.ContractProductInfoId);
                //增加内容为空的登记拒绝信息
                curServeData.Id = Guid.NewGuid().ToString();
                curServeData.ProductServiceInfoGtisApplId = curApplyData.Id;
                curServeData.ContractNum = contractInfo.ContractNum;
                curServeData.State = (int)EnumContractServiceState.REFUSE;
                curServeData.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
                curServeData.Deleted = false;
                curServeData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
                curServeData.RegisteredId = currentUser;
                curServeData.RegisteredDate = now;
                curServeData.RegisteredRemark = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                curServeData.Remark = null;
                curServeData.CouponIds = String.Empty;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(curServeData);
                //如果存在优惠券，将状态改为未使用
                if (!string.IsNullOrEmpty(curApplyData.CouponIds))
                {
                    if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(curApplyData.CouponIds.Split(',').ToList(), EnumCouponType.Grant).state != 1)
                        throw new ApiException("优惠券退回失败");
                }
                //如果使用了个人服务天数，修改状态为拒绝
                if (curApplyData.PerlongServiceDays.GetValueOrDefault(0) > 0)
                {
                    BLL_PrivateService.Instance.UsingPrivateServiceForContractService(curApplyData.ContractId, EnumPrivateServiceType.Refuse, curApplyData.Id, curApplyData.PrivateServiceDays.GetValueOrDefault(0), curApplyData.OverServiceDays.GetValueOrDefault(0));
                }
                //消息发送
                MessageMainInfo message = new MessageMainInfo();
                message.Issuer = curApplyData.CreateUser;
                message.MessageTypeToId = curApplyData.Id;
                message.MessagemMainAboutDes = contractInfo.ContractName;
                BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.GtisService, EnumMessageStateInfo.Refus, curApplyData.ContractId);

            }
            #endregion
            #region 服务变更的初审通过逻辑
            else if (curApplyData.ProcessingType == (int)EnumProcessingType.Change)
            {
                #region 初审通过的前置验证内容
                if (StringUtil.IsNullOrEmpty(contractInfo.ContractNum))
                    throw new ApiException("未到账合同不能开通服务产品");
                if (auditContractProductServiceInfoGtisAppl_IN.ServiceCycleStart == null || auditContractProductServiceInfoGtisAppl_IN.ServiceCycleEnd == null)
                    throw new ApiException("服务周期不能为空");

                if (auditContractProductServiceInfoGtisAppl_IN.GtisUser == null || auditContractProductServiceInfoGtisAppl_IN.GtisUser.Count == 0)
                    throw new ApiException("Gtis账号列表不可为空");
                var gtisAccountNum = auditContractProductServiceInfoGtisAppl_IN.PrimaryAccountsNum + auditContractProductServiceInfoGtisAppl_IN.SubAccountsNum;
                if (auditContractProductServiceInfoGtisAppl_IN.GlobalSearchAccountCount.GetValueOrDefault(0) > gtisAccountNum)
                    throw new ApiException("Gtis总账号数(" + gtisAccountNum + "个)不能小于环球搜账号数(" + auditContractProductServiceInfoGtisAppl_IN.GlobalSearchAccountCount.GetValueOrDefault(0) + "个)，请重新填写");
                //判断共享使用次数 每个账号共享使用人数之和 不能大于总的共享使用次数
                if (auditContractProductServiceInfoGtisAppl_IN.GtisUser.Where(e => e.OpeningStatus == 1).Sum(p => p.SharePeopleNum) > auditContractProductServiceInfoGtisAppl_IN.ShareUsageNum)
                    throw new ApiException("账号绑定人数之和不能大于绑定使用总数");
                #endregion
                #region 必要数据获取
                //变更前Gtis服务信息
                GtisInfo_OUT oldServeData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(contractInfo.ContractNum);
                //变更前Gtis账户信息
                var oldServeDataUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == oldServeData.Id);
                #endregion
                #region 申请数据的处理（服务通过只记录本次备注，其他内容没有修改）
                curApplyData.Remark4List = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(curApplyData);
                #endregion
                #region 补充服务数据内容
                //将传入参数映射进服务数据
                auditContractProductServiceInfoGtisAppl_IN.MappingTo(curServeData);
                curServeData.Id = Guid.NewGuid().ToString();
                curServeData.ProductServiceInfoGtisApplId = curApplyData.Id;
                curServeData.ContractNum = contractInfo.ContractNum;
                curServeData.State = (int)EnumContractServiceState.TO_BE_REVIEW;
                curServeData.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
                curServeData.Deleted = false;
                curServeData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
                curServeData.Remark = null;
                curServeData.RegisteredRemark = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                curServeData.IsGtisOldCustomer = false;//默认是新客户
                curServeData.RegisteredId = currentUser;
                curServeData.RegisteredDate = now;
                //服务记录的账号共享人数，取每个账号共享人数的最大值
                curServeData.SharePeopleNum = auditContractProductServiceInfoGtisAppl_IN.GtisUser.Max(u => u.SharePeopleNum);
                //服务记录的子账号授权国家次数，取主账号的数据
                curServeData.AuthorizationNum = auditContractProductServiceInfoGtisAppl_IN.GtisUser.Find(u => u.AccountType == (int)EnumGtisAccountType.Main).AuthorizationNum;
                //这里子账号数量需要重新Count计算
                curServeData.SubAccountsNum = auditContractProductServiceInfoGtisAppl_IN.GtisUser.FindAll(u => u.AccountType != (int)EnumGtisAccountType.Main).Count;
                //服务变更，默认前端是继承账号，读前端的账号信息(老的账号信息不动，复制一份老的账号信息，在老账号基础上读取前端账号信息并进行更新)
                curServeData.AccountGenerationMethod = (int)EnumGtisAccountGenerationMethod.Generate;
                curServeData.HistoryId = oldServeData.Id;
                //取上一次的新老客户标记
                curServeData.IsGtisOldCustomer = oldServeData.IsGtisOldCustomer;
                //补充服务数据中定制报告权限和次数
                if (curServeData.WordRptPermissions == true && curServeData.WordRptMaxTimes.GetValueOrDefault(0) > 0)
                    curServeData.WordRptMaxTimes = curServeData.WordRptMaxTimes;
                else
                {
                    curServeData.WordRptMaxTimes = 0;
                    curServeData.WordRptPermissions = false;
                }
                //绑定到账信息并计算环球搜结算单位
                if (auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds != null && auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds.Count > 0)
                {
                    EnumProductType productType = EnumProductType.Gtis;
                    if (ProductInfo != null && ProductInfo.ProductType != null)
                        productType = (EnumProductType)ProductInfo.ProductType;
                    DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptData(auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds, curServeData.Id, productType);
                    curServeData.GlobalSearchSettlementCount = DbOpe_crm_contract_receiptregister_service.Instance.GetGlobalSearchSettlementCountByReceiptRegisterId(auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds);
                }
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(curServeData);
                #endregion
                #region 处理零售国家信息
                if (auditContractProductServiceInfoGtisAppl_IN.RetailCountry == (int)EnumGtisRetailCountry.Add)
                {
                    auditContractProductServiceInfoGtisAppl_IN.GtisRetailCountry.ForEach(c =>
                    {
                        DbOpe_crm_contract_serviceinfo_gtis_country.Instance.Insert(new Db_crm_contract_serviceinfo_gtis_country
                        {
                            Id = Guid.NewGuid().ToString(),
                            ContractServiceInfoGtisId = curServeData.Id,
                            Sid = c.Sid
                        });
                    });
                }
                #endregion
                #region 超级子账号数汇总
                //获取另外购买的超级子账号信息
                var ProductInfo_SuperSubAccount = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(curApplyData.ContractId)?.FirstOrDefault();
                //超级子账号总数(套餐中的超级子账号数+另外购买的超级子账号数)
                var SuperSubAccountTotalNum = (ProductInfo_SuperSubAccount == null ? 0 : ProductInfo.SuperSubAccountNum.GetValueOrDefault(0)) + (ProductInfo_SuperSubAccount == null ? 0 : ProductInfo_SuperSubAccount.SuperSubAccountNum.GetValueOrDefault(0));
                //分配超级子账号，超级子账号数量不能大于子账号授权国家次数
                int LeftSuperSubAccountTotalNum = Math.Min(SuperSubAccountTotalNum, curServeData.AuthorizationNum.Value);
                #endregion
                #region 生成Gits账号
                //创建对象接收传入的Gtis账号，方便后面代码书写
                var paramsUsers = auditContractProductServiceInfoGtisAppl_IN.GtisUser.ToList();
                //10.9 变更的用户在数据库也要进行添加而不是更新 这样撤回更改时，账号的回退会更方便一点
                foreach (var oldUser in oldServeDataUsers)
                {
                    var findIndex = paramsUsers.FindIndex(u => u.AccountNumber == oldUser.AccountNumber);
                    if (findIndex >= 0)
                    {
                        //10.9 更新的用户 (变更的用户在数据库也要进行添加而不是更新 这样撤回更改时，账号的回退会更方便一点)
                        UpdateGtisUser(paramsUsers[findIndex], oldUser, curServeData, ref LeftSuperSubAccountTotalNum);
                        paramsUsers.RemoveAt(findIndex);
                    }
                }
                foreach (var newUser in paramsUsers)
                {
                    AddGtisUser(newUser, curServeData, ref LeftSuperSubAccountTotalNum);
                }
                #endregion
            }
            #endregion 续约合同服务申请的初审通过逻辑
            #region 续约合同服务申请的初审通过逻辑
            else if (contractInfo.ContractType == (int)EnumContractType.ReNew)
            {
                #region 初审通过的前置验证内容
                if (string.IsNullOrEmpty(curApplyData.RenewFirstParty))
                    throw new ApiException("申请时需要指定续约公司，请拒绝后让客户经理重新提交服务续约申请");
                if (StringUtil.IsNullOrEmpty(contractInfo.ContractNum))
                    throw new ApiException("未到账合同不能开通服务产品");
                if (auditContractProductServiceInfoGtisAppl_IN.ServiceCycleStart == null || auditContractProductServiceInfoGtisAppl_IN.ServiceCycleEnd == null)
                    throw new ApiException("服务周期不能为空");
                var gtisAccountNum = auditContractProductServiceInfoGtisAppl_IN.PrimaryAccountsNum + auditContractProductServiceInfoGtisAppl_IN.SubAccountsNum;
                if (auditContractProductServiceInfoGtisAppl_IN.GlobalSearchAccountCount.GetValueOrDefault(0) > gtisAccountNum)
                    throw new ApiException("Gtis总账号数(" + gtisAccountNum + "个)不能小于环球搜账号数(" + auditContractProductServiceInfoGtisAppl_IN.GlobalSearchAccountCount.GetValueOrDefault(0) + "个)，请重新填写");
                #endregion
                #region 必要数据获取
                //变更前Gtis服务信息
                GtisInfo_OUT oldServeData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(curApplyData.RenewContractNum);
                //变更前Gtis账户信息
                var oldServeDataUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == oldServeData.Id);
                #endregion
                #region 申请数据的处理（服务通过只记录本次备注，其他内容没有修改）
                curApplyData.Remark4List = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(curApplyData);
                #endregion
                #region 补充服务数据内容
                //将传入参数映射进服务数据
                auditContractProductServiceInfoGtisAppl_IN.MappingTo(curServeData);
                curServeData.Id = Guid.NewGuid().ToString();
                curServeData.ProductServiceInfoGtisApplId = curApplyData.Id;
                curServeData.ContractNum = contractInfo.ContractNum;
                curServeData.State = (int)EnumContractServiceState.TO_BE_REVIEW;
                curServeData.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
                curServeData.Deleted = false;
                curServeData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
                curServeData.Remark = null;
                curServeData.RegisteredRemark = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                curServeData.IsGtisOldCustomer = false;//默认是新客户
                curServeData.RegisteredId = currentUser;
                curServeData.RegisteredDate = now;
                //这里子账号数量需要重新Count计算
                curServeData.SubAccountsNum = auditContractProductServiceInfoGtisAppl_IN.GtisUser.FindAll(u => u.AccountType != (int)EnumGtisAccountType.Main).Count;
                //超过90天续约的境内客户，算新客户
                if (contractInfo.IsOverseasCustomer != null && !contractInfo.IsOverseasCustomer.Value && new TimeSpan(curServeData.ServiceCycleStart.Value.Ticks - oldServeData.ServiceCycleEnd.Value.Ticks).TotalDays > 90)
                    curServeData.IsGtisOldCustomer = false;
                //其他情况沿用续约之前的新老客户标记;
                else
                    curServeData.IsGtisOldCustomer = oldServeData.IsGtisOldCustomer;
                if (oldServeData.State != (int)EnumContractServiceState.OUT)
                    curServeData.HistoryId = oldServeData.Id;
                //补充服务数据中定制报告权限和次数
                if (curServeData.WordRptPermissions == true && curServeData.WordRptMaxTimes.GetValueOrDefault(0) > 0)
                    curServeData.WordRptMaxTimes = curServeData.WordRptMaxTimes;
                else
                {
                    curServeData.WordRptMaxTimes = 0;
                    curServeData.WordRptPermissions = false;
                }
                //处理零售国家信息
                if (auditContractProductServiceInfoGtisAppl_IN.RetailCountry == (int)EnumGtisRetailCountry.Add)
                {
                    auditContractProductServiceInfoGtisAppl_IN.GtisRetailCountry.ForEach(c =>
                    {
                        DbOpe_crm_contract_serviceinfo_gtis_country.Instance.Insert(new Db_crm_contract_serviceinfo_gtis_country
                        {
                            Id = Guid.NewGuid().ToString(),
                            ContractServiceInfoGtisId = curServeData.Id,
                            Sid = c.Sid
                        });
                    });
                }
                //绑定到账信息并计算环球搜结算单位
                if (auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds != null && auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds.Count > 0)
                {
                    EnumProductType productType = EnumProductType.Gtis;
                    if (ProductInfo != null && ProductInfo.ProductType != null)
                        productType = (EnumProductType)ProductInfo.ProductType;
                    DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptData(auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds, curServeData.Id, productType);
                    curServeData.GlobalSearchSettlementCount = DbOpe_crm_contract_receiptregister_service.Instance.GetGlobalSearchSettlementCountByReceiptRegisterId(auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds);
                }
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(curServeData);
                #endregion
                #region 超级子账号数汇总
                //获取另外购买的超级子账号信息
                var ProductInfo_SuperSubAccount = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(curApplyData.ContractId)?.FirstOrDefault();
                //超级子账号总数(套餐中的超级子账号数+另外购买的超级子账号数)
                var SuperSubAccountTotalNum = (ProductInfo_SuperSubAccount == null ? 0 : ProductInfo.SuperSubAccountNum.GetValueOrDefault(0)) + (ProductInfo_SuperSubAccount == null ? 0 : ProductInfo_SuperSubAccount.SuperSubAccountNum.GetValueOrDefault(0));
                //分配超级子账号，超级子账号数量不能大于子账号授权国家次数
                int LeftSuperSubAccountTotalNum = Math.Min(SuperSubAccountTotalNum, curServeData.AuthorizationNum.Value);
                #endregion
                #region 生成Gits账号
                if (auditContractProductServiceInfoGtisAppl_IN.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake)
                    RemakeGtisUsers(curServeData, ref LeftSuperSubAccountTotalNum);
                else
                {
                    //创建对象接收传入的Gtis账号，方便后面代码书写
                    var paramsUsers = auditContractProductServiceInfoGtisAppl_IN.GtisUser.ToList();
                    //10.9 变更的用户在数据库也要进行添加而不是更新 这样撤回更改时，账号的回退会更方便一点
                    foreach (var oldUser in oldServeDataUsers)
                    {
                        var findIndex = paramsUsers.FindIndex(u => u.AccountNumber == oldUser.AccountNumber);
                        if (findIndex >= 0)
                        {
                            //10.9 更新的用户 (变更的用户在数据库也要进行添加而不是更新 这样撤回更改时，账号的回退会更方便一点)
                            UpdateGtisUser(paramsUsers[findIndex], oldUser, curServeData, ref LeftSuperSubAccountTotalNum);
                            paramsUsers.RemoveAt(findIndex);
                        }
                    }
                    foreach (var newUser in paramsUsers)
                    {
                        AddGtisUser(newUser, curServeData, ref LeftSuperSubAccountTotalNum);
                    }
                }
                #endregion
            }
            #endregion
            #region 新增合同服务申请的初审通过逻辑
            else
            {
                #region 初审通过的前置验证内容
                if (StringUtil.IsNullOrEmpty(contractInfo.ContractNum))
                    throw new ApiException("未到账合同不能开通服务产品");
                if (auditContractProductServiceInfoGtisAppl_IN.ServiceCycleStart == null || auditContractProductServiceInfoGtisAppl_IN.ServiceCycleEnd == null)
                    throw new ApiException("服务周期不能为空");
                var gtisAccountNum = auditContractProductServiceInfoGtisAppl_IN.PrimaryAccountsNum + auditContractProductServiceInfoGtisAppl_IN.SubAccountsNum;
                if (auditContractProductServiceInfoGtisAppl_IN.GlobalSearchAccountCount.GetValueOrDefault(0) > gtisAccountNum)
                    throw new ApiException("Gtis总账号数(" + gtisAccountNum + "个)不能小于环球搜账号数(" + auditContractProductServiceInfoGtisAppl_IN.GlobalSearchAccountCount.GetValueOrDefault(0) + "个)，请重新填写");
                #endregion
                #region 申请数据的处理（服务通过只记录本次备注，其他内容没有修改）
                curApplyData.Remark4List = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(curApplyData);
                #endregion
                #region 补充服务数据内容
                //将传入参数映射进服务数据
                auditContractProductServiceInfoGtisAppl_IN.MappingTo(curServeData);
                curServeData.Id = Guid.NewGuid().ToString();
                curServeData.ProductServiceInfoGtisApplId = curApplyData.Id;
                curServeData.ContractNum = contractInfo.ContractNum;
                curServeData.State = (int)EnumContractServiceState.TO_BE_REVIEW;
                curServeData.IsChanged = (int)EnumContractServiceInfoIsChange.Not;
                curServeData.Deleted = false;
                curServeData.IsProcessed = (int)EnumGtisServiceIsProcess.Not;
                curServeData.Remark = null;
                curServeData.RegisteredRemark = auditContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                curServeData.IsGtisOldCustomer = false;//默认是新客户
                curServeData.RegisteredId = currentUser;
                curServeData.RegisteredDate = now;
                //补充服务数据中定制报告权限和次数
                if (curServeData.WordRptPermissions == true && curServeData.WordRptMaxTimes.GetValueOrDefault(0) > 0)
                    curServeData.WordRptMaxTimes = curServeData.WordRptMaxTimes;
                else
                {
                    curServeData.WordRptMaxTimes = 0;
                    curServeData.WordRptPermissions = false;
                }
                //处理零售国家信息
                if (auditContractProductServiceInfoGtisAppl_IN.RetailCountry == (int)EnumGtisRetailCountry.Add)
                {
                    auditContractProductServiceInfoGtisAppl_IN.GtisRetailCountry.ForEach(c =>
                    {
                        DbOpe_crm_contract_serviceinfo_gtis_country.Instance.Insert(new Db_crm_contract_serviceinfo_gtis_country
                        {
                            Id = Guid.NewGuid().ToString(),
                            ContractServiceInfoGtisId = curServeData.Id,
                            Sid = c.Sid
                        });
                    });
                }
                //绑定到账信息并计算环球搜结算单位
                if (auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds != null && auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds.Count > 0)
                {
                    EnumProductType productType = EnumProductType.Gtis;
                    if (ProductInfo != null && ProductInfo.ProductType != null)
                        productType = (EnumProductType)ProductInfo.ProductType;
                    DbOpe_crm_contract_receiptregister_service.Instance.LinkReceiptData(auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds, curServeData.Id, productType);
                    curServeData.GlobalSearchSettlementCount = DbOpe_crm_contract_receiptregister_service.Instance.GetGlobalSearchSettlementCountByReceiptRegisterId(auditContractProductServiceInfoGtisAppl_IN.ReceiptRegisterIds);
                }
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Insert(curServeData);
                #endregion
                #region 超级子账号数汇总
                //获取另外购买的超级子账号信息
                var ProductInfo_SuperSubAccount = DbOpe_crm_product_supersubaccount.Instance.GetProductSuperSubAccountByContractId(curApplyData.ContractId)?.FirstOrDefault();
                //超级子账号总数(套餐中的超级子账号数+另外购买的超级子账号数)
                var SuperSubAccountTotalNum = (ProductInfo_SuperSubAccount == null ? 0 : ProductInfo.SuperSubAccountNum.GetValueOrDefault(0)) + (ProductInfo_SuperSubAccount == null ? 0 : ProductInfo_SuperSubAccount.SuperSubAccountNum.GetValueOrDefault(0));
                //分配超级子账号，超级子账号数量不能大于子账号授权国家次数
                int LeftSuperSubAccountTotalNum = Math.Min(SuperSubAccountTotalNum, curServeData.AuthorizationNum.Value);
                #endregion
                #region 生成Gits账号
                RemakeGtisUsers(curServeData, ref LeftSuperSubAccountTotalNum);
                #endregion
            }
            #endregion
            #region 记录Workflow
            EnumContractServiceOpenState state = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceOpenState(curApplyData.State.Value, curApplyData.ProcessingType.Value, curServeData.State, curServeData.ServiceCycleEnd);

            var workFlowName = curApplyData.ProcessingType == (int)EnumProcessingType.Change ? "GTIS服务变更审批流程" : "GTIS服务审批流程";
            if (auditContractProductServiceInfoGtisAppl_IN.Pass)
            {
                if (curApplyData.IsFree == true)
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_gtis_appl, ContractInfoAndReceipt_Out>(workFlowName, curApplyData.Id, curApplyData, contractInfo, curApplyData.Feedback, state.GetEnumDescription(), "赠送");
                else
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_gtis, ContractInfoAndReceipt_Out>(workFlowName, curApplyData.Id, curServeData, contractInfo, curServeData.RegisteredRemark, state.GetEnumDescription(), "初审");
            }
            else
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_gtis_appl, ContractInfoAndReceipt_Out>(workFlowName, curApplyData.Id, curApplyData, contractInfo, contractInfo.Issuer, curApplyData.Feedback, state.GetEnumDescription(), "初审");
            #endregion
        }
        private void NewReviewContractProductServiceInfoGtisAppl(ReviewContractProductServiceInfoGtisAppl_IN reviewContractProductServiceInfoGtisAppl_IN)
        {
            #region 前置验证和必要数据获取
            //获取当前时间
            DateTime now = DateTime.Now;
            //获取申请信息
            //获取服务申请信息
            var curApplyData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(reviewContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
            //基础校验，申请信息存在且有效并在申请状态下才可以初审
            if (curApplyData == null || curApplyData.Deleted == true)
                throw new ApiException("未找到对应服务产品申请信息");
            if (curApplyData.State != (int)EnumProcessStatus.Submit)
                throw new ApiException("不能对服务产品申请信息重复审核");
            if (curApplyData.IsInvalid != (int)EnumIsInvalid.Effective)
                throw new ApiException("已失效的审核无法进行操作");
            //获取产品信息
            var ProductInfo = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoById(curApplyData.ContractProductInfoId);
            //获取合同信息
            var contractInfo = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(curApplyData.ContractId);
            if (contractInfo == null)
                throw new ApiException("未找到合同信息");
            //获取初审的审核信息
            var curServeData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceByApplyId(reviewContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
            if (curServeData == null)
                throw new ApiException("未找到对应服务产品开通信息");
            else if (curServeData.State != (int)EnumContractServiceState.TO_BE_REVIEW)
                throw new ApiException("未找到待复核的服务产品");
            #endregion
            #region 复核驳回的处理逻辑
            if (!reviewContractProductServiceInfoGtisAppl_IN.Pass)
            {
                curServeData.State = (int)EnumContractServiceState.RETURNED;
                curServeData.ReviewerId = currentUser;
                curServeData.ReviewerDate = now;
                curServeData.ReviewerRemark = reviewContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                curServeData.Remark = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(curServeData);
                curApplyData.Remark4List = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(curApplyData);
            }
            #endregion
            else
            {
                #region 服务变更的复核特殊处理内容
                if (curApplyData.ProcessingType == (int)EnumProcessingType.Change)
                {
                    //变更的话 获取变更前信息 置为无效
                    var oldServeData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(contractInfo.ContractNum);

                    //如果被变更服务使用服务天数，需要计算是否存在退还个人服务天数
                    if (oldServeData != null && oldServeData.PerlongServiceDays.GetValueOrDefault(0) > 0)
                        ReturnServicedaysProcessing(oldServeData, curServeData);
                    oldServeData.State = (int)EnumContractServiceState.INVALID;
                    oldServeData.IsChanged = (int)EnumContractServiceInfoIsChange.Change;
                    oldServeData.ChangedId = curServeData.Id;
                    DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(oldServeData);
                }
                #endregion
                #region 续约合同的服务申请特殊处理内容
                else if (contractInfo.ContractType == (int)EnumContractType.ReNew)
                {
                    if (string.IsNullOrEmpty(curApplyData.RenewFirstParty))
                        throw new ApiException("申请时需要指定续约公司，请先驳回，然后拒绝申请，让客户经理重新提交服务续约申请");
                    //续约如果使用原账户（未过期），也要把之前账户对应的审核作废  如果是上述情况，那么HistoryId在登记时记录了
                    var oldServeData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(contractInfo.RenewalContractNum);
                    if (oldServeData != null && curServeData.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate && !string.IsNullOrEmpty(curServeData.HistoryId))
                    {
                        if (oldServeData.State != (int)EnumContractServiceState.OUT)
                        {
                            oldServeData.State = (int)EnumContractServiceState.INVALID;
                            DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(oldServeData);
                        }
                        else
                        {
                            //如果复核时已经过期了，那就别记录这个值了，删了吧，过期就不用后面撤回了
                            curServeData.HistoryId = null;
                        }
                        //24.04.15 续约时如果老gtis存在未审核的赠送环球搜和慧思学院，也要更新成作废
                        var GetPresentedGlobalSearchService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetPresentedGlobalSearchServiceInfo(oldServeData.ContractId);
                        if (GetPresentedGlobalSearchService != null
                        && (
                        GetPresentedGlobalSearchService.OpenState == EnumContractServiceOpenState.ToBeOpened ||
                        GetPresentedGlobalSearchService.OpenState == EnumContractServiceOpenState.ToBeReview ||
                        GetPresentedGlobalSearchService.OpenState == EnumContractServiceOpenState.Returned
                        ))
                        {
                            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.VoidGlobalSearchService(oldServeData.ContractId, currentUser);
                        }
                        var PresentedCollegeService = DbOpe_crm_contract_serviceinfo_college.Instance.GetPresentedCollegeServiceInfo(oldServeData.ContractId);
                        if (PresentedCollegeService != null
                            && (
                            PresentedCollegeService.OpenState == EnumContractServiceOpenState.ToBeOpened ||
                            PresentedCollegeService.OpenState == EnumContractServiceOpenState.ToBeReview ||
                            PresentedCollegeService.OpenState == EnumContractServiceOpenState.Returned
                        ))
                        {
                            DbOpe_crm_contract_serviceinfo_college.Instance.VoidCollegeService(oldServeData.ContractId, currentUser);
                        }
                    }
                    #region 处理退回服务天数
                    //如果被变更服务使用服务天数，需要计算是否存在退还个人服务天数
                    if (oldServeData != null && oldServeData.PerlongServiceDays.GetValueOrDefault(0) > 0)
                        ReturnServicedaysProcessing(oldServeData, curServeData);
                    #endregion
                }
                #endregion

                curServeData.ReviewerId = currentUser;
                curServeData.ReviewerDate = now;
                curServeData.ReviewerRemark = reviewContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                curServeData.Remark = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                curServeData.GlobalSearchRemark = reviewContractProductServiceInfoGtisAppl_IN.GlobalSearchRemark;
                if (curServeData.ServiceCycleEnd != null && DateTime.Parse(now.ToShortDateString()) > curServeData.ServiceCycleEnd.Value)
                    curServeData.State = (int)EnumContractServiceState.OUT;
                else
                    curServeData.State = (int)EnumContractServiceState.VALID;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(curServeData);
                curApplyData.ReviewerDate = now;
                curApplyData.ReviewerId = currentUser;
                curApplyData.Feedback = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                curApplyData.Remark4List = reviewContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                curApplyData.State = (int)EnumProcessStatus.Pass;//开通后要把申请状态记为通过
                DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(curApplyData);
                //开通服务要刷新合同的保护截止日 
                //DbOpe_crm_contract.Instance.RefreshContractProtectDate(info.Id); 2024.1.12  取消
                //调用gtis进行实际的账号开通操作
                //NewExcuteGtisProcess(curServeData, contractInfo, curApplyData);

                //有可能开通的服务时间就是过期的，这里直接置为过期
                var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
                if (curServeData.ServiceCycleEnd != null && dtDay > curServeData.ServiceCycleEnd.Value)
                    DbOpe_crm_contract_serviceinfo_gtis.Instance.CheckGtisServiceOutOfTime(curApplyData.Id);
                //优惠券标记为已使用
                if (!string.IsNullOrEmpty(curServeData.CouponIds))
                    if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(curServeData.CouponIds.Split(',').ToList(), EnumCouponType.Used).state != 1)
                        throw new ApiException("优惠券核销失败");
                DbOpe_crm_customer_coupon.Instance.UpdateCoupanDeadline(curServeData.ContractId, curServeData.ServiceCycleEnd.Value);
                //个人服务天数标记使用
                if (curServeData.PerlongServiceDays.GetValueOrDefault(0) > 0)
                    BLL_PrivateService.Instance.UsingPrivateServiceForContractService(curServeData.ContractId, EnumPrivateServiceType.Used, curServeData.ProductServiceInfoGtisApplId, curServeData.PrivateServiceDays.Value, curServeData.OverServiceDays.Value);

                //如果合同包含环球搜产品，自动开通环球搜
                string globalProductId = string.Empty;
                string globalContractProductId = string.Empty;
                if (DbOpe_crm_contract_productinfo.Instance.CheckContractHasGlobalSearchProduct(curServeData.ContractId, ref globalProductId, ref globalContractProductId))
                    BLL_ContractServiceGlobalSearch.Instance.AutoOpenGlobalSearch(curServeData, curApplyData, globalProductId, globalContractProductId, new List<EnumGlobalSearchModifyType>());
                //自动开通慧思学院
                if (AppSettings.Env != Enum_SystemSettingEnv.Product)
                    BLL_ContractServiceCollege.Instance.AutoAuditAndReviewCollegeAppl(curApplyData.ContractId, curServeData.Id);
                else
                {
                    try
                    {
                        BLL_ContractServiceCollege.Instance.AutoAuditAndReviewCollegeAppl(curApplyData.ContractId, curServeData.Id);
                    }
                    catch (Exception ex)
                    {
                        LogUtil.AddErrorLog(ex.Message);
                    }
                }
            }

            EnumContractServiceOpenState state = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceOpenState(curApplyData.State.Value, curApplyData.ProcessingType.Value, curServeData.State, curServeData.ServiceCycleEnd);
            var workFlowName = curApplyData.ProcessingType == (int)EnumProcessingType.Change ? "GTIS服务变更审批流程" : "GTIS服务审批流程";
            if (reviewContractProductServiceInfoGtisAppl_IN.Pass)
            {
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_gtis_appl, ContractInfoAndReceipt_Out>(workFlowName, curApplyData.Id, curApplyData, contractInfo, contractInfo.Issuer, curServeData.ReviewerRemark, state.GetEnumDescription(), "复核");
            }
            else
            {
                BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_gtis, ContractInfoAndReceipt_Out>(workFlowName, curApplyData.Id, curServeData, contractInfo, curServeData.Remark, state.GetEnumDescription(), "复核");
            }
            var userDatas = new List<Db_crm_contract_serviceinfo_gtis_user>();
            var gtisContractNum = string.Empty;
            SynchroGtisUserData(curServeData.ContractNum, ref userDatas, ref gtisContractNum);
        }

        /// <summary>
        /// 复核/开通GTIS服务产品
        /// </summary>
        /// <param name="reviewContractProductServiceInfoGtisAppl_IN"></param>
        public void ReviewContractProductServiceInfoGtisAppl(ReviewContractProductServiceInfoGtisAppl_IN reviewContractProductServiceInfoGtisAppl_IN)
        {
            DateTime dt = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.TransDeal(() =>
            {
                //获取服务申请信息
                var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(reviewContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
                if (applData == null || applData.Deleted == true)
                {
                    throw new ApiException("未找到对应服务产品申请信息");
                }
                if (applData.State != (int)EnumProcessStatus.Submit)
                {
                    throw new ApiException("不能对服务产品申请信息重复审核");
                }
                if (applData.IsInvalid != (int)EnumIsInvalid.Effective)
                {
                    throw new ApiException("已失效的审核无法进行操作");
                }
                //获取合同信息
                var info = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(applData.ContractId);
                if (info == null)
                {
                    throw new ApiException("未找到合同信息");
                }
                //获取登记信息
                var registerInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetRegisterInfoByApplId(reviewContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
                if (registerInfo == null)
                {
                    throw new ApiException("未找到对应服务产品开通信息");
                }
                else if (registerInfo.State != (int)EnumContractServiceState.TO_BE_REVIEW)
                {
                    throw new ApiException("未找到待复核的服务产品");
                }
                //复核
                var lastGtisData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == registerInfo.Id);
                if (!reviewContractProductServiceInfoGtisAppl_IN.Pass)
                {
                    //退回
                    lastGtisData.State = (int)EnumContractServiceState.RETURNED;
                    lastGtisData.ReviewerId = currentUser;
                    lastGtisData.ReviewerDate = dt;
                    lastGtisData.ReviewerRemark = reviewContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                    lastGtisData.Remark = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                    DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(lastGtisData);
                    applData.Remark4List = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                    DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);
                }
                else
                { //开通
                    #region 存在环球搜产品时校验环球搜服务周期是否填写
                    string globalProductId = string.Empty;
                    string globalContractProductId = string.Empty;
                    var hasGlobalSearch = DbOpe_crm_contract_productinfo.Instance.CheckContractHasGlobalSearchProduct(lastGtisData.ContractId, ref globalProductId, ref globalContractProductId);
                    var autoOpenGlobalSearch = hasGlobalSearch;
                    var globalSearchChangeItem = new List<EnumGlobalSearchModifyType>();
                    if ((registerInfo.GlobalSearchExecuteServiceCycleStart == null || registerInfo.GlobalSearchExecuteServiceCycleEnd == null || registerInfo.GlobalSearchExecuteServiceMonth == null) && hasGlobalSearch)
                    {
                        throw new ApiException("未填写环球搜服务周期，请驳回后补充信息");
                    }
                    #endregion
                    if (applData.ProcessingType == (int)EnumProcessingType.Change)
                    {
                        //变更的话 获取变更前信息 置为无效
                        //var OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(applData.ContractProductInfoId);
                        //var updateOldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetDataById(OldGtis.Id);
                        //var OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(info.ContractNum).MappingTo<Db_crm_contract_serviceinfo_gtis>();
                        var updateOldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(info.ContractNum);
                        #region 如果变更使用优惠券，同步给环球搜 2025.2.28 因环球搜开通融入到gtis开通中,这段会导致重复添加Globalsearch_user表数据,注释
                        /*//如果变更使用优惠券，同步给环球搜
                        if (!string.IsNullOrEmpty(lastGtisData.CouponIds))
                        {
                            var globalService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(lastGtisData.ContractNum);
                            if (globalService != null)
                            {
                                var couponCount = lastGtisData.CouponIds.Split(',').ToList().Count();
                                globalService.ServiceMonth += couponCount;
                                globalService.ServiceCycleEnd.Value.AddMonths(couponCount);

                                var oldGtisPriUser = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetGtisUsersByServeId(lastGtisData.Id).Where(e => e.AccountType == (int)EnumGtisAccountType.Main).First();

                                BLL_ContractServiceGlobalSearch.Instance.UpdateGlobalSearchUserBecauseCoupon(globalService.Id, oldGtisPriUser.GlobalSearchCode, lastGtisData.ContractNum, oldGtisPriUser.AccountNumber, lastGtisData.ServiceCycleStart, lastGtisData.ServiceCycleEnd, globalService.SettlementLevel, globalService.SettlementMonth, "使用优惠券赠送" + couponCount.ToString() + "月时长");
                            }
                        }*/
                        #endregion

                        //如果被变更服务使用服务天数，需要计算是否存在退还个人服务天数
                        if (updateOldGtis != null && updateOldGtis.PerlongServiceDays.GetValueOrDefault(0) > 0)
                        {
                            ReturnServicedaysProcessing(updateOldGtis, lastGtisData);
                            #region 此段内容已经集成到上面的方法中，注释
                            /*//当前的服务绑定的最晚到账日期
                            var lastArriveDate = DbOpe_crm_contract_receiptregister_service.Instance.GetLastArriveDateByServiceId(lastGtisData.Id);
                            //被变更服务的复核通过时间
                            var oldGtisReviewDate = updateOldGtis.ReviewerDate;
                            *//* 需求727关于到账时间对返还服务天数影响的原描述： 触发返还的服务对应的到账时间，必须在使用个人服务天数生效时间的次月内*//*
                            //若最晚到账时间符合规则(最晚到账时间早于被变更服务开通时间次月最后一天24点) && 查看当前的服务时间和被变更的服务时间存在重叠  处理服务天数退还，优先退超额服务天数
                            if (lastArriveDate != null && oldGtisReviewDate != null
                                && lastArriveDate < oldGtisReviewDate.Value.AddMonths(2).AddDays(-oldGtisReviewDate.Value.Day + 1).Date
                                && updateOldGtis.ServiceCycleEnd.Value > lastGtisData.ServiceCycleStart.Value)
                            {
                                //防止int赋值报错，如果存才空值先赋0
                                updateOldGtis.OverServiceDays = updateOldGtis.OverServiceDays.GetValueOrDefault(0);
                                updateOldGtis.PrivateServiceDays = updateOldGtis.PrivateServiceDays.GetValueOrDefault(0);

                                //计算服务重叠天数
                                var tobeReturnedDays = (updateOldGtis.ServiceCycleEnd.Value - lastGtisData.ServiceCycleStart.Value).TotalDays.ToInt();
                                //定义待退回的超额服务天数和个人服务天数
                                int tobeReturned_OverServDays = 0, tobeReturned_PriServDays = 0;

                                *//*优先计算超额服务天数*//*
                                if (tobeReturnedDays > 0 && updateOldGtis.OverServiceDays > 0 && updateOldGtis.OverServiceDays >= tobeReturnedDays)
                                {//旧服务使用超额服务天数 && 使用数量大于或等于服务重叠天数
                                    tobeReturned_OverServDays = tobeReturnedDays;//待退回的超额服务天数为总重叠天数
                                    updateOldGtis.OverServiceDays -= tobeReturnedDays;//重新计算旧服务的超额服务天数
                                    tobeReturnedDays = 0;
                                }
                                else if (tobeReturnedDays > 0 && updateOldGtis.OverServiceDays > 0 && updateOldGtis.OverServiceDays < tobeReturnedDays)
                                {//旧服务使用超额服务天数 && 且使用数量小于服务重叠天数
                                    tobeReturned_OverServDays = updateOldGtis.OverServiceDays.Value;//待退回的超额服务天数为旧服务的超额服务天数
                                    updateOldGtis.OverServiceDays = 0;//旧服务的超额服务天数归零
                                    //重新计算总重叠天数，减去旧服务使用的超额服务天数。
                                    tobeReturnedDays -= tobeReturned_OverServDays;
                                }



                                *//*计算个人服务天数*//*
                                if (tobeReturnedDays > 0 && updateOldGtis.PrivateServiceDays > 0 && updateOldGtis.PrivateServiceDays >= tobeReturnedDays)
                                {//此时服务重叠天数大于0 && 旧服务使用个人服务天数 && 使用数量大于或等于服务重叠天数
                                    tobeReturned_PriServDays = tobeReturnedDays;//待退回的个人服务天数为总重叠天数
                                    updateOldGtis.PrivateServiceDays -= tobeReturnedDays;//重新计算旧服务的个人服务天数
                                }
                                else if (tobeReturnedDays > 0 && updateOldGtis.PrivateServiceDays > 0 && updateOldGtis.PrivateServiceDays < tobeReturnedDays)
                                {//此时服务重叠天数大于0 && 旧服务使用个人服务天数 && 使用数量小于服务重叠天数
                                    tobeReturned_PriServDays = updateOldGtis.PrivateServiceDays.Value;//待退回的个人服务天数为旧服务的超额服务天数
                                    updateOldGtis.PrivateServiceDays = 0;//旧服务的个人服务天数归零
                                }

                                //将超额天数和个人天数进行登记退还
                                BLL_PrivateService.Instance.UsingPrivateServiceForContractService(updateOldGtis.ContractId, EnumPrivateServiceType.GiveBacking, updateOldGtis.ProductServiceInfoGtisApplId, tobeReturned_PriServDays, tobeReturned_OverServDays);

                            }*/
                            #endregion
                        }

                        updateOldGtis.State = (int)EnumContractServiceState.INVALID;
                        updateOldGtis.IsChanged = (int)EnumContractServiceInfoIsChange.Change;
                        updateOldGtis.ChangedId = lastGtisData.Id;
                        DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(updateOldGtis);

                        if (hasGlobalSearch)
                        {
                            //判断旧服务的合同是否含有环球搜
                            var updateOldGtisHasGlobalSearch = DbOpe_crm_contract_productinfo.Instance.CheckContractHasGlobalSearchProduct(updateOldGtis.ContractId);
                            if (!updateOldGtisHasGlobalSearch)
                                globalSearchChangeItem.Add(EnumGlobalSearchModifyType.NewService);
                            else
                            {
                                //从旧Gtis服务中获取旧环球搜的账号数及服务开始结束时间
                                var oldAccountNum = updateOldGtis.GlobalSearchAccountCount;
                                var oldExecuteServiceStart = updateOldGtis.GlobalSearchExecuteServiceCycleStart;
                                var oldExecuteServiceEnd = updateOldGtis.GlobalSearchExecuteServiceCycleEnd;
                                //如果旧Gtis服务中没有环球搜相关参数，需要搜索到旧的环球搜服务后再查找账号数和服务开始结束时间
                                if (oldAccountNum == null || oldExecuteServiceStart == null || oldExecuteServiceEnd == null)
                                {
                                    var old_globalsearch_serv = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.CheckContractHasGlobalSearchServiceByContractId(updateOldGtis.ContractId);
                                    if (old_globalsearch_serv != null)
                                    {
                                        oldAccountNum = old_globalsearch_serv.PrimaryAccountsNum.GetValueOrDefault(0) + old_globalsearch_serv.SubAccountsNum.GetValueOrDefault(0);
                                        oldExecuteServiceStart = old_globalsearch_serv.ExecuteServiceCycleStart == null ? old_globalsearch_serv.ServiceCycleStart : old_globalsearch_serv.ExecuteServiceCycleStart;
                                        oldExecuteServiceEnd = old_globalsearch_serv.ExecuteServiceCycleEnd == null ? old_globalsearch_serv.ServiceCycleEnd : old_globalsearch_serv.ExecuteServiceCycleEnd;
                                    }
                                }
                                //如果不存在有效的服务内容，视为新增服务
                                if (oldAccountNum == null || oldExecuteServiceStart == null || oldExecuteServiceEnd == null)
                                {
                                    globalSearchChangeItem.Add(EnumGlobalSearchModifyType.NewService);
                                }
                                //判断账号数是否一致
                                else
                                {
                                    if (lastGtisData.GlobalSearchAccountCount != oldAccountNum)
                                    {
                                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeCode);
                                    }
                                    //判断服务时间是否一致
                                    if (lastGtisData.GlobalSearchExecuteServiceCycleStart != oldExecuteServiceStart || lastGtisData.GlobalSearchExecuteServiceCycleEnd != oldExecuteServiceEnd)
                                    {
                                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeTime);
                                    }
                                    //账号数和服务时间均一致，标记为未变更
                                    if (globalSearchChangeItem.Count == 0)
                                    {
                                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.Unchanged);
                                        autoOpenGlobalSearch = false;
                                    }
                                }

                            }

                        }


                    }
                    else if (info.ContractType == (int)EnumContractType.ReNew)
                    {
                        if (string.IsNullOrEmpty(applData.RenewFirstParty))
                        {
                            throw new ApiException("申请时需要指定续约公司，请先驳回，然后拒绝申请，让客户经理重新提交服务续约申请");
                        }
                        //续约如果使用原账户（未过期），也要把之前账户对应的审核作废  如果是上述情况，那么HistoryId在登记时记录了
                        //var OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == lastGtisData.HistoryId);
                        var OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(info.RenewalContractNum);
                        if (OldGtis != null && lastGtisData.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate && !string.IsNullOrEmpty(lastGtisData.HistoryId))
                        {
                            if (OldGtis.State != (int)EnumContractServiceState.OUT)
                            {
                                OldGtis.State = (int)EnumContractServiceState.INVALID;
                                DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(OldGtis);
                            }
                            else
                            {
                                //如果复核时已经过期了，那就别记录这个值了，删了吧，过期就不用后面撤回了
                                lastGtisData.HistoryId = null;
                            }
                            //24.04.15 续约时如果老gtis存在未审核的赠送环球搜和慧思学院，也要更新成作废
                            var GetPresentedGlobalSearchService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetPresentedGlobalSearchServiceInfo(OldGtis.ContractId);
                            if (GetPresentedGlobalSearchService != null
                            && (
                            GetPresentedGlobalSearchService.OpenState == EnumContractServiceOpenState.ToBeOpened ||
                            GetPresentedGlobalSearchService.OpenState == EnumContractServiceOpenState.ToBeReview ||
                            GetPresentedGlobalSearchService.OpenState == EnumContractServiceOpenState.Returned
                            ))
                            {
                                DbOpe_crm_contract_serviceinfo_globalsearch.Instance.VoidGlobalSearchService(OldGtis.ContractId, currentUser);
                            }
                            var PresentedCollegeService = DbOpe_crm_contract_serviceinfo_college.Instance.GetPresentedCollegeServiceInfo(OldGtis.ContractId);
                            if (PresentedCollegeService != null
                                && (
                                PresentedCollegeService.OpenState == EnumContractServiceOpenState.ToBeOpened ||
                                PresentedCollegeService.OpenState == EnumContractServiceOpenState.ToBeReview ||
                                PresentedCollegeService.OpenState == EnumContractServiceOpenState.Returned
                            ))
                            {
                                DbOpe_crm_contract_serviceinfo_college.Instance.VoidCollegeService(OldGtis.ContractId, currentUser);
                            }
                        }


                        if (hasGlobalSearch)
                        {
                            //判断旧服务的合同是否含有环球搜
                            var oldGtisHasGlobalSearch = DbOpe_crm_contract_productinfo.Instance.CheckContractHasGlobalSearchProduct(OldGtis.ContractId);
                            if (!oldGtisHasGlobalSearch)
                                globalSearchChangeItem.Add(EnumGlobalSearchModifyType.NewService);
                            else
                            {
                                //从旧Gtis服务中获取旧环球搜的账号数及服务开始结束时间
                                var oldAccountNum = OldGtis.GlobalSearchAccountCount;
                                var oldExecuteServiceStart = OldGtis.GlobalSearchExecuteServiceCycleStart;
                                var oldExecuteServiceEnd = OldGtis.GlobalSearchExecuteServiceCycleEnd;
                                //如果旧Gtis服务中没有环球搜相关参数，需要搜索到旧的环球搜服务后再查找账号数和服务开始结束时间
                                if (oldAccountNum == null || oldExecuteServiceStart == null || oldExecuteServiceEnd == null)
                                {
                                    var old_globalsearch_serv = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.CheckContractHasGlobalSearchServiceByContractId(OldGtis.ContractId);
                                    if (old_globalsearch_serv != null)
                                    {
                                        oldAccountNum = old_globalsearch_serv.PrimaryAccountsNum.GetValueOrDefault(0) + old_globalsearch_serv.SubAccountsNum.GetValueOrDefault(0);
                                        oldExecuteServiceStart = old_globalsearch_serv.ExecuteServiceCycleStart == null ? old_globalsearch_serv.ServiceCycleStart : old_globalsearch_serv.ExecuteServiceCycleStart;
                                        oldExecuteServiceEnd = old_globalsearch_serv.ExecuteServiceCycleEnd == null ? old_globalsearch_serv.ServiceCycleEnd : old_globalsearch_serv.ExecuteServiceCycleEnd;
                                    }
                                }
                                //如果不存在有效的服务内容，视为新增服务
                                if (oldAccountNum == null || oldExecuteServiceStart == null || oldExecuteServiceEnd == null)
                                {
                                    globalSearchChangeItem.Add(EnumGlobalSearchModifyType.NewService);
                                }
                                else
                                {
                                    //判断账号生成方式
                                    if (lastGtisData.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake)
                                    {
                                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.NewService);
                                    }
                                    //判断账号数是否一致
                                    else if (lastGtisData.GlobalSearchAccountCount != oldAccountNum)
                                    {
                                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeCode);
                                    }
                                    //判断服务时间是否一致
                                    if (lastGtisData.GlobalSearchExecuteServiceCycleStart != oldExecuteServiceStart || lastGtisData.GlobalSearchExecuteServiceCycleEnd != oldExecuteServiceEnd)
                                    {
                                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.ChangeTime);
                                    }
                                    //账号数和服务时间均一致，标记为未变更。但续约合同需要补充环球搜对应数据
                                    if (globalSearchChangeItem.Count == 0)
                                    {
                                        globalSearchChangeItem.Add(EnumGlobalSearchModifyType.Unchanged);
                                        autoOpenGlobalSearch = true;
                                    }
                                }
                            }

                        }

                        #region 处理退回服务天数
                        //如果被变更服务使用服务天数，需要计算是否存在退还个人服务天数
                        if (OldGtis != null && OldGtis.PerlongServiceDays.GetValueOrDefault(0) > 0)
                        {
                            ReturnServicedaysProcessing(OldGtis, lastGtisData);
                            #region 此段内容已归入到上面的方法中，注释
                            /*//当前的服务的最晚到账日期
                            var lastArriveDate = DbOpe_crm_contract_receiptregister_service.Instance.GetLastArriveDateByServiceId(lastGtisData.Id);//查找到账的最晚日期
                            //被变更服务的复核通过时间
                            var oldGtisReviewDate = OldGtis.ReviewerDate;
                            *//* 需求727关于到账时间对返还服务天数影响的原描述： 触发返还的服务对应的到账时间，必须在使用个人服务天数生效时间的次月内*//*
                            //若最晚到账时间符合规则(最晚到账时间早于被变更服务开通时间次月最后一天24点) && 查看当前的服务时间和被变更的服务时间存在重叠  处理服务天数退还，优先退超额服务天数
                            if (lastArriveDate != null && oldGtisReviewDate != null
                                && lastArriveDate < oldGtisReviewDate.Value.AddMonths(2).AddDays(-oldGtisReviewDate.Value.Day + 1).Date
                                && OldGtis.ServiceCycleEnd.Value > lastGtisData.ServiceCycleStart.Value)
                            {

                                //防止int赋值报错，如果存在空值先赋0
                                OldGtis.OverServiceDays = OldGtis.OverServiceDays.GetValueOrDefault(0);
                                OldGtis.PrivateServiceDays = OldGtis.PrivateServiceDays.GetValueOrDefault(0);

                                //计算服务重叠天数
                                var tobeReturnedDays = (OldGtis.ServiceCycleEnd.Value - lastGtisData.ServiceCycleStart.Value).TotalDays.ToInt();
                                //定义待退回的超额服务天数和个人服务天数
                                int tobeReturned_OverServDays = 0, tobeReturned_PriServDays = 0;

                                *//*优先计算超额服务天数*//*
                                if (tobeReturnedDays > 0 && OldGtis.OverServiceDays > 0 && OldGtis.OverServiceDays >= tobeReturnedDays)
                                {//旧服务使用超额服务天数 && 使用数量大于或等于服务重叠天数
                                    tobeReturned_OverServDays = tobeReturnedDays;//待退回的超额服务天数为总重叠天数
                                    OldGtis.OverServiceDays -= tobeReturnedDays;//重新计算旧服务的超额服务天数
                                    tobeReturnedDays = 0;
                                }
                                else if (tobeReturnedDays > 0 && OldGtis.OverServiceDays > 0 && OldGtis.OverServiceDays < tobeReturnedDays)
                                {//旧服务使用超额服务天数 && 且使用数量小于服务重叠天数
                                    tobeReturned_OverServDays = OldGtis.OverServiceDays.Value;//待退回的超额服务天数为旧服务的超额服务天数
                                    OldGtis.OverServiceDays = 0;//旧服务的超额服务天数归零
                                    tobeReturnedDays -= tobeReturned_OverServDays;
                                }

                                *//*计算个人服务天数*//*
                                if (tobeReturnedDays > 0 && OldGtis.PrivateServiceDays > 0 && OldGtis.PrivateServiceDays >= tobeReturnedDays)
                                {//此时服务重叠天数大于0 && 旧服务使用个人服务天数 && 使用数量大于或等于服务重叠天数
                                    tobeReturned_PriServDays = tobeReturnedDays;//待退回的个人服务天数为总重叠天数
                                    OldGtis.PrivateServiceDays -= tobeReturnedDays;//重新计算旧服务的个人服务天数
                                }
                                else if (tobeReturnedDays > 0 && OldGtis.PrivateServiceDays > 0 && OldGtis.PrivateServiceDays < tobeReturnedDays)
                                {//此时服务重叠天数大于0 && 旧服务使用个人服务天数 && 使用数量小于服务重叠天数
                                    tobeReturned_PriServDays = OldGtis.PrivateServiceDays.Value;//待退回的个人服务天数为旧服务的超额服务天数
                                    OldGtis.PrivateServiceDays = 0;//旧服务的个人服务天数归零
                                }

                                //将超额天数和个人天数进行登记退还
                                BLL_PrivateService.Instance.UsingPrivateServiceForContractService(OldGtis.ContractId, EnumPrivateServiceType.GiveBacking, OldGtis.ProductServiceInfoGtisApplId, tobeReturned_PriServDays, tobeReturned_OverServDays);

                                DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(OldGtis);
                            }*/
                            #endregion
                        }
                        #endregion
                    }

                    lastGtisData.State = (int)EnumContractServiceState.VALID;
                    lastGtisData.ReviewerId = currentUser;
                    lastGtisData.ReviewerDate = dt;
                    lastGtisData.ReviewerRemark = reviewContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                    lastGtisData.Remark = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                    lastGtisData.GlobalSearchRemark = reviewContractProductServiceInfoGtisAppl_IN.GlobalSearchRemark;
                    DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(lastGtisData);
                    //开通后要把申请状态记为通过
                    applData.State = (int)EnumProcessStatus.Pass;
                    applData.ReviewerDate = dt;
                    applData.ReviewerId = currentUser;
                    applData.Feedback = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                    applData.Remark4List = reviewContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                    DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);
                    //开通服务要刷新合同的保护截止日 
                    //DbOpe_crm_contract.Instance.RefreshContractProtectDate(info.Id); 2024.1.12  取消
                    //调用gtis进行实际的账号开通操作
                    ExcuteGtisProcess(lastGtisData);

                    //自动开通慧思学院
                    if (AppSettings.Env != Enum_SystemSettingEnv.Product)
                    {
                        BLL_ContractServiceCollege.Instance.AutoAuditAndReviewCollegeAppl(applData.ContractId, lastGtisData.Id);
                    }
                    else
                    {
                        try
                        {
                            BLL_ContractServiceCollege.Instance.AutoAuditAndReviewCollegeAppl(applData.ContractId, lastGtisData.Id);
                        }
                        catch (Exception ex)
                        {
                            LogUtil.AddErrorLog(ex.Message);
                        }
                    }
                    //有可能开通的服务时间就是过期的，这里直接置为过期
                    var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
                    if (lastGtisData.ServiceCycleEnd != null && dtDay > lastGtisData.ServiceCycleEnd.Value)
                    {
                        DbOpe_crm_contract_serviceinfo_gtis.Instance.CheckGtisServiceOutOfTime(applData.Id);
                    }
                    //优惠券标记为已使用
                    if (!string.IsNullOrEmpty(lastGtisData.CouponIds))
                        if (DbOpe_crm_customer_coupon.Instance.ChangeCouponType(lastGtisData.CouponIds.Split(',').ToList(), EnumCouponType.Used).state != 1)
                            throw new ApiException("优惠券核销失败");
                    DbOpe_crm_customer_coupon.Instance.UpdateCoupanDeadline(lastGtisData.ContractId, lastGtisData.ServiceCycleEnd.Value);
                    //个人服务天数标记使用
                    if (lastGtisData.PerlongServiceDays.GetValueOrDefault(0) > 0)
                    {
                        BLL_PrivateService.Instance.UsingPrivateServiceForContractService(lastGtisData.ContractId, EnumPrivateServiceType.Used, lastGtisData.ProductServiceInfoGtisApplId, lastGtisData.PrivateServiceDays.Value, lastGtisData.OverServiceDays.Value);
                    }

                    //判断是否需要自动开通环球搜
                    if (autoOpenGlobalSearch)
                    {
                        BLL_ContractServiceGlobalSearch.Instance.AutoOpenGlobalSearch(lastGtisData, applData, globalProductId, globalContractProductId, globalSearchChangeItem);
                    }

                    /* MessageMainInfo message = new MessageMainInfo();
                     message.Issuer = applData.CreateUser;
                     message.MessageTypeToId = applData.Id;
                     message.MessagemMainAboutDes = info.ContractName;
                     BLL_MessageCenter.Instance.InsertMessageCenterDetail(message, EnumMessageLocalType.ServiceAbout, EnumMessageStepInfo.GtisService, EnumMessageStateInfo.Pass, applData.ContractId);*/
                }


                EnumContractServiceOpenState state = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceOpenState(applData.State.Value, applData.ProcessingType.Value, lastGtisData.State, lastGtisData.ServiceCycleEnd);
                var workFlowName = applData.ProcessingType == (int)EnumProcessingType.Change ? "GTIS服务变更审批流程" : "GTIS服务审批流程";
                if (reviewContractProductServiceInfoGtisAppl_IN.Pass)
                {
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_gtis_appl, ContractInfoAndReceipt_Out>(workFlowName, applData.Id, applData, info, info.Issuer, lastGtisData.ReviewerRemark, state.GetEnumDescription(), "复核");
                }
                else
                {
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_gtis, ContractInfoAndReceipt_Out>(workFlowName, applData.Id, lastGtisData, info, lastGtisData.Remark, state.GetEnumDescription(), "复核");
                }
                var userDatas = new List<Db_crm_contract_serviceinfo_gtis_user>();
                var gtisContractNum = string.Empty;
                SynchroGtisUserData(lastGtisData.ContractNum, ref userDatas, ref gtisContractNum);
            });
        }

        /// <summary>
        /// 处理服务天数退还
        /// </summary>
        /// <param name="oldGtis"></param>
        /// <param name="lastGtis"></param>
        private void ReturnServicedaysProcessing(Db_crm_contract_serviceinfo_gtis oldGtis, Db_crm_contract_serviceinfo_gtis lastGtis)
        {
            //当前的服务的最晚到账日期
            var lastArriveDate = DbOpe_crm_contract_receiptregister_service.Instance.GetLastArriveDateByServiceId(lastGtis.Id);//查找到账的最晚日期
            var oldGtisReviewDate = oldGtis.ReviewerDate;//被变更服务的复核通过时间
            /* 需求727关于到账时间对返还服务天数影响的原描述： 触发返还的服务对应的到账时间，必须在使用个人服务天数生效时间的次月内*/
            //若最晚到账时间符合规则(最晚到账时间早于被变更服务开通时间次月最后一天24点) && 查看当前的服务时间和被变更的服务时间存在重叠  处理服务天数退还，优先退超额服务天数
            if (lastArriveDate != null && oldGtisReviewDate != null
                && lastArriveDate < oldGtisReviewDate.Value.AddMonths(2).AddDays(-oldGtisReviewDate.Value.Day + 1).Date
                && oldGtis.ServiceCycleEnd.Value > lastGtis.ServiceCycleStart.Value)
            {

                //防止int赋值报错，如果存在空值先赋0
                oldGtis.OverServiceDays = oldGtis.OverServiceDays.GetValueOrDefault(0);
                oldGtis.PrivateServiceDays = oldGtis.PrivateServiceDays.GetValueOrDefault(0);

                //计算服务重叠天数
                var tobeReturnedDays = (oldGtis.ServiceCycleEnd.Value - lastGtis.ServiceCycleStart.Value).TotalDays.ToInt();
                //定义待退回的超额服务天数和个人服务天数
                int tobeReturned_OverServDays = 0, tobeReturned_PriServDays = 0;

                /*优先计算超额服务天数*/
                if (tobeReturnedDays > 0 && oldGtis.OverServiceDays > 0 && oldGtis.OverServiceDays >= tobeReturnedDays)
                {//旧服务使用超额服务天数 && 使用数量大于或等于服务重叠天数
                    tobeReturned_OverServDays = tobeReturnedDays;//待退回的超额服务天数为总重叠天数
                    oldGtis.OverServiceDays -= tobeReturnedDays;//重新计算旧服务的超额服务天数
                    tobeReturnedDays = 0;
                }
                else if (tobeReturnedDays > 0 && oldGtis.OverServiceDays > 0 && oldGtis.OverServiceDays < tobeReturnedDays)
                {//旧服务使用超额服务天数 && 且使用数量小于服务重叠天数
                    tobeReturned_OverServDays = oldGtis.OverServiceDays.Value;//待退回的超额服务天数为旧服务的超额服务天数
                    oldGtis.OverServiceDays = 0;//旧服务的超额服务天数归零
                    tobeReturnedDays -= tobeReturned_OverServDays;
                }

                /*计算个人服务天数*/
                if (tobeReturnedDays > 0 && oldGtis.PrivateServiceDays > 0 && oldGtis.PrivateServiceDays >= tobeReturnedDays)
                {//此时服务重叠天数大于0 && 旧服务使用个人服务天数 && 使用数量大于或等于服务重叠天数
                    tobeReturned_PriServDays = tobeReturnedDays;//待退回的个人服务天数为总重叠天数
                    oldGtis.PrivateServiceDays -= tobeReturnedDays;//重新计算旧服务的个人服务天数
                }
                else if (tobeReturnedDays > 0 && oldGtis.PrivateServiceDays > 0 && oldGtis.PrivateServiceDays < tobeReturnedDays)
                {//此时服务重叠天数大于0 && 旧服务使用个人服务天数 && 使用数量小于服务重叠天数
                    tobeReturned_PriServDays = oldGtis.PrivateServiceDays.Value;//待退回的个人服务天数为旧服务的超额服务天数
                    oldGtis.PrivateServiceDays = 0;//旧服务的个人服务天数归零
                }

                //将超额天数和个人天数进行登记退还
                BLL_PrivateService.Instance.UsingPrivateServiceForContractService(oldGtis.ContractId, EnumPrivateServiceType.GiveBacking, oldGtis.ProductServiceInfoGtisApplId, tobeReturned_PriServDays, tobeReturned_OverServDays);

                DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(oldGtis);
            }
        }

        /// <summary>
        /// 复核/开通GTIS服务产品(24.6.6 延时生效)
        /// </summary>
        /// <param name="reviewContractProductServiceInfoGtisAppl_IN"></param>
        public void ReviewContractProductServiceInfoGtisAppl_TOBE(ReviewContractProductServiceInfoGtisAppl_IN reviewContractProductServiceInfoGtisAppl_IN)
        {
            DateTime dt = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.TransDeal(() =>
            {
                //获取服务申请信息
                var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(reviewContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
                if (applData == null || applData.Deleted == true)
                {
                    throw new ApiException("未找到对应服务产品申请信息");
                }
                if (applData.State != (int)EnumProcessStatus.Submit)
                {
                    throw new ApiException("不能对服务产品申请信息重复审核");
                }
                if (applData.IsInvalid != (int)EnumIsInvalid.Effective)
                {
                    throw new ApiException("已失效的审核无法进行操作");
                }
                //获取合同信息
                var info = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(applData.ContractId);
                if (info == null)
                {
                    throw new ApiException("未找到合同信息");
                }
                //获取登记信息
                var registerInfo = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetRegisterInfoByApplId(reviewContractProductServiceInfoGtisAppl_IN.ProductServiceInfoGtisApplId);
                if (registerInfo == null)
                {
                    throw new ApiException("未找到对应服务产品开通信息");
                }
                else if (registerInfo.State != (int)EnumContractServiceState.TO_BE_REVIEW || registerInfo.State != (int)EnumContractServiceState.TO_BE_EFFECTIVE)
                {
                    throw new ApiException("未找到待复核或待生效的服务产品");
                }
                //复核
                var lastGtisData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == registerInfo.Id);
                if (!reviewContractProductServiceInfoGtisAppl_IN.Pass)
                {
                    //退回
                    lastGtisData.State = (int)EnumContractServiceState.RETURNED;
                    lastGtisData.ReviewerId = currentUser;
                    lastGtisData.ReviewerDate = dt;
                    lastGtisData.ReviewerRemark = reviewContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                    lastGtisData.Remark = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                    DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(lastGtisData);
                }
                else
                {
                    //判断是不是需要立即生效
                    var tobeEffectiveFlag = lastGtisData.ServiceCycleStart <= DateTime.Now.GetDaysEnd();
                    if (tobeEffectiveFlag)
                    {
                        //延时生效，验证数据有效性，然后将当前的设置先保存为《待生效》状态，等待服务开始日期后生效
                        lastGtisData.State = (int)EnumContractServiceState.TO_BE_EFFECTIVE;
                    }
                    else
                    {
                        //立即执行开通操作 
                        ExcuteReviewGTIS(lastGtisData.Id);
                    }
                    //更新服务审核记录
                    lastGtisData.ReviewerId = currentUser;
                    lastGtisData.ReviewerDate = dt;
                    lastGtisData.ReviewerRemark = reviewContractProductServiceInfoGtisAppl_IN.CurrentRemark;
                    lastGtisData.Remark = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                    DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(lastGtisData);
                    //把申请状态记为通过
                    applData.State = (int)EnumProcessStatus.Pass;
                    applData.ReviewerDate = dt;
                    applData.ReviewerId = currentUser;
                    applData.Feedback = reviewContractProductServiceInfoGtisAppl_IN.Remark;
                    DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.Update(applData);
                }
                //处理工作流
                EnumContractServiceOpenState state = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetContractServiceOpenState(applData.State.Value, applData.ProcessingType.Value, lastGtisData.State, lastGtisData.ServiceCycleEnd);
                var workFlowName = applData.ProcessingType == (int)EnumProcessingType.Change ? "GTIS服务变更审批流程" : "GTIS服务审批流程";
                if (reviewContractProductServiceInfoGtisAppl_IN.Pass)
                {
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_productserviceinfo_gtis_appl, ContractInfoAndReceipt_Out>(workFlowName, applData.Id, applData, info, info.Issuer, lastGtisData.ReviewerRemark, state.GetEnumDescription(), "复核");
                }
                else
                {
                    BLL_WorkFlow.Instance.AddWorkFlow<Db_crm_contract_serviceinfo_gtis, ContractInfoAndReceipt_Out>(workFlowName, applData.Id, lastGtisData, info, lastGtisData.Remark, state.GetEnumDescription(), "复核");
                }
            });
        }

        public void ExcuteReviewGTIS(string gtisServiceId)
        {
            var lastGtisData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == gtisServiceId);
            var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(lastGtisData.ProductServiceInfoGtisApplId);
            var info = DbOpe_crm_contract.Instance.GetContractAndReceiptByContractId(applData.ContractId);
            if (applData.ProcessingType == (int)EnumProcessingType.Change)
            {
                //变更的话 获取变更前信息 置为无效
                var OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(applData.ContractProductInfoId);
                var updateOldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetDataById(OldGtis.Id);
                updateOldGtis.State = (int)EnumContractServiceState.INVALID;
                updateOldGtis.IsChanged = (int)EnumContractServiceInfoIsChange.Change;
                updateOldGtis.ChangedId = lastGtisData.Id;
                DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(updateOldGtis);
            }
            else if (info.ContractType == (int)EnumContractType.ReNew && lastGtisData.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Generate && !string.IsNullOrEmpty(lastGtisData.HistoryId))
            {
                if (string.IsNullOrEmpty(applData.RenewFirstParty))
                {
                    throw new ApiException("申请时需要指定续约公司，请先驳回，然后拒绝申请，让客户经理重新提交服务续约申请");
                }
                //续约如果使用原账户（未过期），也要把之前账户对应的审核作废  如果是上述情况，那么HistoryId在登记时记录了
                var OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == lastGtisData.HistoryId);
                if (OldGtis.State != (int)EnumContractServiceState.OUT)
                {
                    OldGtis.State = (int)EnumContractServiceState.INVALID;
                    DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(OldGtis);
                }
                else
                {
                    //如果复核时已经过期了，那就别记录这个值了，删了吧，过期就不用后面撤回了
                    lastGtisData.HistoryId = null;
                }
                if (OldGtis != null)
                {
                    //24.04.15 续约时如果老gtis存在未审核的赠送环球搜和慧思学院，也要更新成作废
                    var GetPresentedGlobalSearchService = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetPresentedGlobalSearchServiceInfo(OldGtis.ContractId);
                    if (GetPresentedGlobalSearchService != null
                    && (
                    GetPresentedGlobalSearchService.OpenState == EnumContractServiceOpenState.ToBeOpened ||
                    GetPresentedGlobalSearchService.OpenState == EnumContractServiceOpenState.ToBeReview ||
                    GetPresentedGlobalSearchService.OpenState == EnumContractServiceOpenState.Returned
                    ))
                    {
                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.VoidGlobalSearchService(OldGtis.ContractId, currentUser);
                    }
                    var PresentedCollegeService = DbOpe_crm_contract_serviceinfo_college.Instance.GetPresentedCollegeServiceInfo(OldGtis.ContractId);
                    if (PresentedCollegeService != null
                        && (
                        PresentedCollegeService.OpenState == EnumContractServiceOpenState.ToBeOpened ||
                        PresentedCollegeService.OpenState == EnumContractServiceOpenState.ToBeReview ||
                        PresentedCollegeService.OpenState == EnumContractServiceOpenState.Returned
                    ))
                    {
                        DbOpe_crm_contract_serviceinfo_college.Instance.VoidCollegeService(OldGtis.ContractId, currentUser);
                    }
                }
            }
            lastGtisData.State = (int)EnumContractServiceState.VALID;
            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(lastGtisData);

            //调用gtis进行实际的账号开通操作
            ExcuteGtisProcess(lastGtisData);
            if (AppSettings.Env != Enum_SystemSettingEnv.Product)
            {
                BLL_ContractServiceCollege.Instance.AutoAuditAndReviewCollegeAppl(applData.ContractId, "");
            }
            else
            {
                //尝试自动开通慧思学院
                try
                {
                    BLL_ContractServiceCollege.Instance.AutoAuditAndReviewCollegeAppl(applData.ContractId, "");
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog(ex.Message);
                }
            }
            //有可能开通的服务时间就是过期的，这里直接置为过期
            var dt = DateTime.Now;
            var dtDay = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            if (lastGtisData.ServiceCycleEnd != null && dtDay > lastGtisData.ServiceCycleEnd.Value)
            {
                DbOpe_crm_contract_serviceinfo_gtis.Instance.CheckGtisServiceOutOfTime(applData.Id);
            }
        }



        /// <summary>
        /// 批量开通gtis服务产品
        /// </summary>
        public int ExcuteDelayProcessAppls(bool waitServiceCycleStart = false)
        {
            var processData = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetNeedProcessData(waitServiceCycleStart);
            int r = 0;
            foreach (var gtis in processData)
            {
                try
                {
                    ExcuteGtisProcess(gtis);
                }
                catch (Exception ex)
                {
                    LogUtil.AddErrorLog(gtis.Id + "开通GTIS失败：" + ex.Message);
                }
                finally
                {
                    r++;
                }
            }
            return r;

        }

        /// <summary>
        /// 执行开通gtis服务产品（立即）
        /// </summary>
        /// <param name="gtis"></param>
        /// <exception cref="ApiException"></exception>
        public void ExcuteGtisProcess(Db_crm_contract_serviceinfo_gtis gtis)
        {
            var dt = DateTime.Now;
            var contractInfo = DbOpe_crm_contract.Instance.GetContractByContractProductInfoId(gtis.ContractProductInfoId);
            if (contractInfo == null)
            {
                throw new ApiException("未找到合同信息");
            }
            //DbOpe_crm_contract_serviceinfo_gtis_user.Instance.TransDeal(() =>
            //{
            var applData = DbOpe_crm_contract_productserviceinfo_gtis_appl.Instance.GetDataById(gtis.ProductServiceInfoGtisApplId);
            var users = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(c => c.ContractServiceInfoGtisId == gtis.Id);
            var countrys = DbOpe_crm_contract_serviceinfo_gtis_residentcountry.Instance.GetDataList(c => c.ContractServiceInfoGtisId == gtis.Id).Select(c => c.ResidentCountry.Value).ToList();
            var citys = DbOpe_crm_contract_serviceinfo_gtis_residentcity.Instance.GetDataList(c => c.ContractServiceInfoGtisId == gtis.Id).Select(c => c.ResidentCity.Value).ToList();
            int[] sids = null;
            if (gtis.RetailCountry == (int)EnumGtisRetailCountry.Add)
            {
                sids = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetDataList(c => c.ContractServiceInfoGtisId == gtis.Id).Select(d => d.Sid.Value).ToArray();
            }
            var addUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
            var updateUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
            var delUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
            bool renewNoDataContract = false;
            if (applData.ProcessingType == (int)EnumProcessingType.Change)
            {
                //变更的话，得把老的账号关了
                //获取变更前信息
                var OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == gtis.HistoryId);
                var OldUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == OldGtis.Id);
                foreach (var oldUser in OldUsers)
                {
                    var findIndex = users.FindIndex(u => u.UserId == oldUser.UserId && !string.IsNullOrEmpty(u.UserId));
                    if (findIndex >= 0)
                    {
                        //更新的账号    
                        var copyUser = oldUser.MappingTo<Db_crm_contract_serviceinfo_gtis_user>();
                        copyUser.Id = users[findIndex].Id;
                        copyUser.ContractServiceInfoGtisId = users[findIndex].ContractServiceInfoGtisId;
                        copyUser.SharePeopleNum = users[findIndex].SharePeopleNum;
                        copyUser.AllCountrySubUser = users[findIndex].AllCountrySubUser;
                        if (copyUser.AccountType == (int)EnumGtisAccountType.Main)
                        {
                            gtis.AuthorizationNum = users[findIndex].AuthorizationNum;
                            copyUser.AuthorizationNum = users[findIndex].AuthorizationNum;
                        }
                        updateUsers.Add(copyUser);
                        users.RemoveAt(findIndex);
                    }
                    else
                    {
                        //停用了的账号
                        delUsers.Add(oldUser);
                    }
                }
                //剩下就是新加的账号
                addUsers = users.MappingTo<List<Db_crm_contract_serviceinfo_gtis_user>>();
                BM_GtisOpe_RenewalContact bM_GtisOpe_RenewalContact = DbOpe_crm_product_gtis_relation.Instance.FormatGtisUserForUpdate(addUsers, gtis, countrys, citys, sids, delUsers, true, OldGtis.ContractNum, updateUsers);
                List<BM_AddGtisUserRetModel> bM_AddGtisUserRetModels = new List<BM_AddGtisUserRetModel>();
                try
                {
                    LogUtil.AddLog("BM_GtisOpe_RenewalContact(svCode = " + bM_GtisOpe_RenewalContact.SvCode + ")  :  " + JsonConvert.SerializeObject(bM_GtisOpe_RenewalContact));
                    bM_AddGtisUserRetModels = BLL_GtisOpe.Instance.RenewalContact(bM_GtisOpe_RenewalContact).Result;
                }
                catch (Exception e)
                {
                    throw new ApiException("开通GTIS账号失败(" + e.Message + ")");
                }
                foreach (var user in users)
                {
                    //新加的账号
                    var addUserRet = bM_AddGtisUserRetModels.Find(u => u.CrmId == user.Id);
                    if (addUserRet != null)
                    {
                        user.AccountNumber = addUserRet.Uid;
                        user.UserId = addUserRet.SysUserID;
                        user.PassWord = addUserRet.Pwd;
                        user.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        user.ProcessedTime = dt;
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                        user.StartDate = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(user);
                    }
                }
                foreach (var updateUser in updateUsers)
                {
                    //沿用的账号
                    updateUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                    updateUser.ProcessedTime = dt;
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(updateUser);
                }
                foreach (var delUser in delUsers)
                {
                    //停用的账号
                    delUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                    delUser.ProcessedTime = dt;
                    delUser.OpeningStatus = (int)EnumGtisUserOpeningStatus.UserManagerStop;
                    delUser.EndDate = dt;
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(delUser);
                }
            }
            else if (contractInfo.ContractType == (int)EnumContractType.ReNew)
            {
                if (gtis.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake)
                {
                    //这里重新生成账户的话，要走新建接口
                    renewNoDataContract = true;
                }
                else
                {
                    List<Db_crm_contract_serviceinfo_gtis_user> OldUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
                    Db_crm_contract_serviceinfo_gtis OldGtis = new Db_crm_contract_serviceinfo_gtis();
                    if (!string.IsNullOrEmpty(gtis.HistoryId))
                    {
                        OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == gtis.HistoryId);
                        OldUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == OldGtis.Id);
                    }
                    else
                    {
                        var companyOldContractProductInfoId = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetOldContractProductIdByCompanyId(applData.RenewFirstParty, contractInfo.Id);
                        if (string.IsNullOrEmpty(companyOldContractProductInfoId))
                        {
                            throw new ApiException("未找到上一份服务信息，无法使用原账户");
                        }
                        OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(applData.RenewContractNum).MappingTo<Db_crm_contract_serviceinfo_gtis>();//DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(companyOldContractProductInfoId, true).MappingTo<Db_crm_contract_serviceinfo_gtis>();
                        /*if (OldGtis.ContractNum != applData.RenewContractNum)
                        {
                            throw new ApiException("被续约合同信息已经变更，请拒绝后重新申请续约");
                        }*/
                        OldUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == OldGtis.Id);
                    }
                    //这里加个判断 如果登记的账户 与 现在获取的老账户 不相同了，说明这个审核登记的要沿用的老账户已经不对了，这个公司在这期间又开通了新的账户
                    var oldUserMain = OldUsers.Find(o => o.AccountType == (int)EnumGtisAccountType.Main);
                    if (oldUserMain != null)
                    {
                        if (users.Find(u => u.UserId == oldUserMain.UserId) == null)
                        {
                            throw new ApiException("此登记要使用的原有账号已经失效，请驳回后重新进行登记");
                        }
                    }
                    else
                    {
                        throw new ApiException("此登记要使用的原有账号已经失效，请驳回后重新进行登记");
                    }

                    gtis.OldContractNum = OldGtis.ContractNum;
                    //这里获取上一份合同的用户，只是为了筛选出哪些账号要停用
                    foreach (var oldUser in OldUsers)
                    {
                        var findIndex = users.FindIndex(u => u.UserId == oldUser.UserId && !string.IsNullOrEmpty(u.UserId));
                        if (findIndex >= 0)
                        {
                            //更新的账号
                            var copyUser = oldUser.MappingTo<Db_crm_contract_serviceinfo_gtis_user>();
                            copyUser.Id = users[findIndex].Id;
                            copyUser.ContractServiceInfoGtisId = users[findIndex].ContractServiceInfoGtisId;
                            copyUser.SharePeopleNum = users[findIndex].SharePeopleNum;
                            copyUser.AllCountrySubUser = users[findIndex].AllCountrySubUser;
                            copyUser.ContractServiceInfoGlobalSearchId = null;
                            updateUsers.Add(copyUser);
                            if (copyUser.AccountType == (int)EnumGtisAccountType.Main)
                            {
                                gtis.AuthorizationNum = users[findIndex].AuthorizationNum;
                                copyUser.AuthorizationNum = users[findIndex].AuthorizationNum;
                            }
                            users.RemoveAt(findIndex);
                        }
                        else
                        {
                            //停用了的账号
                            delUsers.Add(oldUser);
                        }
                    }
                    //剩下就是新加的账号
                    addUsers = users.MappingTo<List<Db_crm_contract_serviceinfo_gtis_user>>();
                    BM_GtisOpe_RenewalContact bM_GtisOpe_RenewalContact = DbOpe_crm_product_gtis_relation.Instance.FormatGtisUserForUpdate(addUsers, gtis, countrys, citys, sids, delUsers, false, OldGtis.ContractNum, updateUsers);
                    List<BM_AddGtisUserRetModel> bM_AddGtisUserRetModels = new List<BM_AddGtisUserRetModel>();
                    try
                    {
                        LogUtil.AddLog("BM_GtisOpe_RenewalContact(svCode = " + bM_GtisOpe_RenewalContact.SvCode + ")  :  " + JsonConvert.SerializeObject(bM_GtisOpe_RenewalContact));
                        bM_AddGtisUserRetModels = BLL_GtisOpe.Instance.RenewalContact(bM_GtisOpe_RenewalContact).Result;
                    }
                    catch (Exception e)
                    {
                        throw new ApiException("开通GTIS账号失败(" + e.Message + ")");
                    }
                    foreach (var user in users)
                    {
                        //新加的账号
                        var addUserRet = bM_AddGtisUserRetModels.Find(u => u.CrmId == user.Id);
                        if (addUserRet != null)
                        {
                            user.AccountNumber = addUserRet.Uid;
                            user.UserId = addUserRet.SysUserID;
                            user.PassWord = addUserRet.Pwd;
                            user.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                            user.ProcessedTime = dt;
                            user.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                            user.StartDate = dt;
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(user);
                        }
                    }
                    foreach (var updateUser in updateUsers)
                    {
                        //沿用的账号
                        updateUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        updateUser.ProcessedTime = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(updateUser);
                    }
                    foreach (var delUser in delUsers)
                    {
                        //停用的账号
                        delUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        delUser.ProcessedTime = dt;
                        delUser.OpeningStatus = (int)EnumGtisUserOpeningStatus.UserManagerStop;
                        delUser.EndDate = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(delUser);
                    }

                }
            }
            if ((contractInfo.ContractType != (int)EnumContractType.ReNew && applData.ProcessingType == (int)EnumProcessingType.Add) || renewNoDataContract)
            {
                addUsers = users.MappingTo<List<Db_crm_contract_serviceinfo_gtis_user>>();
                //新增
                BM_AddGtisUser bM_AddGtisUser = DbOpe_crm_product_gtis_relation.Instance.FormatGtisUser(addUsers, gtis, countrys, citys, sids);
                List<BM_AddGtisUserRetModel> bM_AddGtisUserRetModels = new List<BM_AddGtisUserRetModel>();
                try
                {
                    LogUtil.AddLog("BM_AddGtisUser(svCode = " + bM_AddGtisUser.SvCode + ")  :  " + JsonConvert.SerializeObject(bM_AddGtisUser));
                    bM_AddGtisUserRetModels = BLL_GtisOpe.Instance.AddUser(bM_AddGtisUser).Result;
                }
                catch (Exception e)
                {
                    throw new ApiException("开通GTIS账号失败(" + e.Message + ")");
                }
                foreach (var retUser in bM_AddGtisUserRetModels)
                {
                    var user = users.Find(u => u.Id == retUser.CrmId);
                    if (user != null)
                    {
                        user.AccountNumber = retUser.Uid;
                        user.UserId = retUser.SysUserID;
                        user.PassWord = retUser.Pwd;
                        user.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        user.ProcessedTime = dt;
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                        user.StartDate = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(user);
                    }
                }
            }

            gtis.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
            gtis.ProcessedTime = dt;
            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(gtis);
        }

        public void NewExcuteGtisProcess(Db_crm_contract_serviceinfo_gtis curServeData, Db_crm_contract contractInfo, Db_crm_contract_productserviceinfo_gtis_appl curApplyData)
        {
            var dt = DateTime.Now;
            var users = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(c => c.ContractServiceInfoGtisId == curServeData.Id);
            var countrys = DbOpe_crm_contract_serviceinfo_gtis_residentcountry.Instance.GetDataList(c => c.ContractServiceInfoGtisId == curServeData.Id).Select(c => c.ResidentCountry.Value).ToList();
            var citys = DbOpe_crm_contract_serviceinfo_gtis_residentcity.Instance.GetDataList(c => c.ContractServiceInfoGtisId == curServeData.Id).Select(c => c.ResidentCity.Value).ToList();
            int[] sids = null;
            if (curServeData.RetailCountry == (int)EnumGtisRetailCountry.Add)
            {
                sids = DbOpe_crm_contract_serviceinfo_gtis_country.Instance.GetDataList(c => c.ContractServiceInfoGtisId == curServeData.Id).Select(d => d.Sid.Value).ToArray();
            }
            var addUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
            var updateUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
            var delUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
            bool renewNoDataContract = false;
            if (curApplyData.ProcessingType == (int)EnumProcessingType.Change)
            {
                //变更的话，得把老的账号关了
                //获取变更前信息
                var OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == curServeData.HistoryId);
                var OldUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == OldGtis.Id);
                foreach (var oldUser in OldUsers)
                {
                    var findIndex = users.FindIndex(u => u.UserId == oldUser.UserId && !string.IsNullOrEmpty(u.UserId));
                    if (findIndex >= 0)
                    {
                        //更新的账号    
                        var copyUser = oldUser.MappingTo<Db_crm_contract_serviceinfo_gtis_user>();
                        copyUser.Id = users[findIndex].Id;
                        copyUser.ContractServiceInfoGtisId = users[findIndex].ContractServiceInfoGtisId;
                        copyUser.SharePeopleNum = users[findIndex].SharePeopleNum;
                        copyUser.AllCountrySubUser = users[findIndex].AllCountrySubUser;
                        if (copyUser.AccountType == (int)EnumGtisAccountType.Main)
                        {
                            curServeData.AuthorizationNum = users[findIndex].AuthorizationNum;
                            copyUser.AuthorizationNum = users[findIndex].AuthorizationNum;
                        }
                        updateUsers.Add(copyUser);
                        users.RemoveAt(findIndex);
                    }
                    else
                    {
                        //停用了的账号
                        delUsers.Add(oldUser);
                    }
                }
                //剩下就是新加的账号
                addUsers = users.MappingTo<List<Db_crm_contract_serviceinfo_gtis_user>>();
                BM_GtisOpe_RenewalContact bM_GtisOpe_RenewalContact = DbOpe_crm_product_gtis_relation.Instance.FormatGtisUserForUpdate(addUsers, curServeData, countrys, citys, sids, delUsers, true, OldGtis.ContractNum, updateUsers);
                List<BM_AddGtisUserRetModel> bM_AddGtisUserRetModels = new List<BM_AddGtisUserRetModel>();
                try
                {
                    LogUtil.AddLog("BM_GtisOpe_RenewalContact(svCode = " + bM_GtisOpe_RenewalContact.SvCode + ")  :  " + JsonConvert.SerializeObject(bM_GtisOpe_RenewalContact));
                    bM_AddGtisUserRetModels = BLL_GtisOpe.Instance.RenewalContact(bM_GtisOpe_RenewalContact).Result;
                }
                catch (Exception e)
                {
                    throw new ApiException("开通GTIS账号失败(" + e.Message + ")");
                }
                foreach (var user in users)
                {
                    //新加的账号
                    var addUserRet = bM_AddGtisUserRetModels.Find(u => u.CrmId == user.Id);
                    if (addUserRet != null)
                    {
                        user.AccountNumber = addUserRet.Uid;
                        user.UserId = addUserRet.SysUserID;
                        user.PassWord = addUserRet.Pwd;
                        user.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        user.ProcessedTime = dt;
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                        user.StartDate = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(user);
                    }
                }
                foreach (var updateUser in updateUsers)
                {
                    //沿用的账号
                    updateUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                    updateUser.ProcessedTime = dt;
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(updateUser);
                }
                foreach (var delUser in delUsers)
                {
                    //停用的账号
                    delUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                    delUser.ProcessedTime = dt;
                    delUser.OpeningStatus = (int)EnumGtisUserOpeningStatus.UserManagerStop;
                    delUser.EndDate = dt;
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(delUser);
                }
            }
            else if (contractInfo.ContractType == (int)EnumContractType.ReNew)
            {
                if (curServeData.AccountGenerationMethod == (int)EnumGtisAccountGenerationMethod.Remake)
                {
                    //这里重新生成账户的话，要走新建接口
                    renewNoDataContract = true;
                }
                else
                {
                    List<Db_crm_contract_serviceinfo_gtis_user> OldUsers = new List<Db_crm_contract_serviceinfo_gtis_user>();
                    Db_crm_contract_serviceinfo_gtis OldGtis = new Db_crm_contract_serviceinfo_gtis();
                    if (!string.IsNullOrEmpty(curServeData.HistoryId))
                    {
                        OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetData(g => g.Id == curServeData.HistoryId);
                        OldUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == OldGtis.Id);
                    }
                    else
                    {
                        var companyOldContractProductInfoId = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetOldContractProductIdByCompanyId(curApplyData.RenewFirstParty, contractInfo.Id);
                        if (string.IsNullOrEmpty(companyOldContractProductInfoId))
                        {
                            throw new ApiException("未找到上一份服务信息，无法使用原账户");
                        }
                        OldGtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisInfoByContractNum(curApplyData.RenewContractNum).MappingTo<Db_crm_contract_serviceinfo_gtis>();//DbOpe_crm_contract_serviceinfo_gtis.Instance.GetInfoByApplContractProductId(companyOldContractProductInfoId, true).MappingTo<Db_crm_contract_serviceinfo_gtis>();
                        /*if (OldGtis.ContractNum != applData.RenewContractNum)
                        {
                            throw new ApiException("被续约合同信息已经变更，请拒绝后重新申请续约");
                        }*/
                        OldUsers = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetDataList(u => u.ContractServiceInfoGtisId == OldGtis.Id);
                    }
                    //这里加个判断 如果登记的账户 与 现在获取的老账户 不相同了，说明这个审核登记的要沿用的老账户已经不对了，这个公司在这期间又开通了新的账户
                    var oldUserMain = OldUsers.Find(o => o.AccountType == (int)EnumGtisAccountType.Main);
                    if (oldUserMain != null)
                    {
                        if (users.Find(u => u.UserId == oldUserMain.UserId) == null)
                        {
                            throw new ApiException("此登记要使用的原有账号已经失效，请驳回后重新进行登记");
                        }
                    }
                    else
                    {
                        throw new ApiException("此登记要使用的原有账号已经失效，请驳回后重新进行登记");
                    }

                    curServeData.OldContractNum = OldGtis.ContractNum;
                    //这里获取上一份合同的用户，只是为了筛选出哪些账号要停用
                    foreach (var oldUser in OldUsers)
                    {
                        var findIndex = users.FindIndex(u => u.UserId == oldUser.UserId && !string.IsNullOrEmpty(u.UserId));
                        if (findIndex >= 0)
                        {
                            //更新的账号
                            var copyUser = oldUser.MappingTo<Db_crm_contract_serviceinfo_gtis_user>();
                            copyUser.Id = users[findIndex].Id;
                            copyUser.ContractServiceInfoGtisId = users[findIndex].ContractServiceInfoGtisId;
                            copyUser.SharePeopleNum = users[findIndex].SharePeopleNum;
                            copyUser.AllCountrySubUser = users[findIndex].AllCountrySubUser;
                            copyUser.ContractServiceInfoGlobalSearchId = null;
                            updateUsers.Add(copyUser);
                            if (copyUser.AccountType == (int)EnumGtisAccountType.Main)
                            {
                                curServeData.AuthorizationNum = users[findIndex].AuthorizationNum;
                                copyUser.AuthorizationNum = users[findIndex].AuthorizationNum;
                            }
                            users.RemoveAt(findIndex);
                        }
                        else
                        {
                            //停用了的账号
                            delUsers.Add(oldUser);
                        }
                    }
                    //剩下就是新加的账号
                    addUsers = users.MappingTo<List<Db_crm_contract_serviceinfo_gtis_user>>();
                    BM_GtisOpe_RenewalContact bM_GtisOpe_RenewalContact = DbOpe_crm_product_gtis_relation.Instance.FormatGtisUserForUpdate(addUsers, curServeData, countrys, citys, sids, delUsers, false, OldGtis.ContractNum, updateUsers);
                    List<BM_AddGtisUserRetModel> bM_AddGtisUserRetModels = new List<BM_AddGtisUserRetModel>();
                    try
                    {
                        LogUtil.AddLog("BM_GtisOpe_RenewalContact(svCode = " + bM_GtisOpe_RenewalContact.SvCode + ")  :  " + JsonConvert.SerializeObject(bM_GtisOpe_RenewalContact));
                        bM_AddGtisUserRetModels = BLL_GtisOpe.Instance.RenewalContact(bM_GtisOpe_RenewalContact).Result;
                    }
                    catch (Exception e)
                    {
                        throw new ApiException("开通GTIS账号失败(" + e.Message + ")");
                    }
                    foreach (var user in users)
                    {
                        //新加的账号
                        var addUserRet = bM_AddGtisUserRetModels.Find(u => u.CrmId == user.Id);
                        if (addUserRet != null)
                        {
                            user.AccountNumber = addUserRet.Uid;
                            user.UserId = addUserRet.SysUserID;
                            user.PassWord = addUserRet.Pwd;
                            user.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                            user.ProcessedTime = dt;
                            user.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                            user.StartDate = dt;
                            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(user);
                        }
                    }
                    foreach (var updateUser in updateUsers)
                    {
                        //沿用的账号
                        updateUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        updateUser.ProcessedTime = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(updateUser);
                    }
                    foreach (var delUser in delUsers)
                    {
                        //停用的账号
                        delUser.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        delUser.ProcessedTime = dt;
                        delUser.OpeningStatus = (int)EnumGtisUserOpeningStatus.UserManagerStop;
                        delUser.EndDate = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(delUser);
                    }

                }
            }
            if ((contractInfo.ContractType != (int)EnumContractType.ReNew && curApplyData.ProcessingType == (int)EnumProcessingType.Add) || renewNoDataContract)
            {
                addUsers = users.MappingTo<List<Db_crm_contract_serviceinfo_gtis_user>>();
                //新增
                BM_AddGtisUser bM_AddGtisUser = DbOpe_crm_product_gtis_relation.Instance.FormatGtisUser(addUsers, curServeData, countrys, citys, sids);
                List<BM_AddGtisUserRetModel> bM_AddGtisUserRetModels = new List<BM_AddGtisUserRetModel>();
                try
                {
                    LogUtil.AddLog("BM_AddGtisUser(svCode = " + bM_AddGtisUser.SvCode + ")  :  " + JsonConvert.SerializeObject(bM_AddGtisUser));
                    bM_AddGtisUserRetModels = BLL_GtisOpe.Instance.AddUser(bM_AddGtisUser).Result;
                }
                catch (Exception e)
                {
                    throw new ApiException("开通GTIS账号失败(" + e.Message + ")");
                }
                foreach (var retUser in bM_AddGtisUserRetModels)
                {
                    var user = users.Find(u => u.Id == retUser.CrmId);
                    if (user != null)
                    {
                        user.AccountNumber = retUser.Uid;
                        user.UserId = retUser.SysUserID;
                        user.PassWord = retUser.Pwd;
                        user.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
                        user.ProcessedTime = dt;
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.Ok;
                        user.StartDate = dt;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Update(user);
                    }
                }
            }

            curServeData.IsProcessed = (int)EnumGtisServiceIsProcess.Processed;
            curServeData.ProcessedTime = dt;
            DbOpe_crm_contract_serviceinfo_gtis.Instance.Update(curServeData);
        }

        public void RemakeGtisUsers(Db_crm_contract_serviceinfo_gtis gtis, ref int LeftSuperSubAccountTotalNum)
        {
            for (var i = 0; i < gtis.PrimaryAccountsNum; i++)
            {
                AddGtisUser(null, gtis, ref LeftSuperSubAccountTotalNum);
            }
            for (var j = 0; j < gtis.SubAccountsNum; j++)
            {
                AddGtisUser(null, gtis, ref LeftSuperSubAccountTotalNum, EnumGtisAccountType.Sub);
            }
            /*if (gtis.PrimaryAccountsNum + gtis.SubAccountsNum < gtis.GlobalSearchAccountCount)
            {
                var toAddCount = gtis.GlobalSearchAccountCount - gtis.PrimaryAccountsNum - gtis.SubAccountsNum;
                for (var k = 0; k < toAddCount; k++)
                {
                    AddGtisUser(null, gtis, ref LeftSuperSubAccountTotalNum, EnumGtisAccountType.Sub);
                }
            }*/
        }

        /// <summary>
        /// 新建用户
        /// </summary>
        /// <param name="userParam">如果传null表示自动生成一个新账号，否则则读取这个参数内的设置来新建用户</param>
        /// <param name="gtis"></param>
        /// <param name="isMain"></param>
        /// <param name="LeftSuperSubAccountTotalNum">剩余未分配的超级子账号数</param>
        public void AddGtisUser(GtisUser_IN userParam, Db_crm_contract_serviceinfo_gtis gtis, ref int LeftSuperSubAccountTotalNum, EnumGtisAccountType isMain = EnumGtisAccountType.Main)
        {
            DateTime dt = DateTime.Now;
            var user = new Db_crm_contract_serviceinfo_gtis_user();
            //新建的用户 AccountNumber UserId一定是null，真的AccountNumber UserId要gtis系统返回 这里只能定义每个新加账号的SharePeopleNum（AuthorizationNum这里gtis也没办法分别定义，每个账户是统一的值）
            if (userParam == null)
            {
                user = new Db_crm_contract_serviceinfo_gtis_user()
                {
                    AccountType = (int)isMain,
                    AccountNumber = "",
                    SharePeopleNum = gtis.SharePeopleNum,
                    AuthorizationNum = gtis.AuthorizationNum,
                };
            }
            else
            {
                userParam.MappingTo(user);
            }
            user.Id = Guid.NewGuid().ToString();
            user.ContractServiceInfoGtisId = gtis.Id;
            user.UserId = "";
            user.PassWord = "";
            user.OpeningStatus = (int)EnumGtisUserOpeningStatus.INPROCESS;
            user.AllCountrySubUser = false;
            //情况1：如果子账号数量为1并且gtis选了子账号自动分配全部国家，那么这个子账号自动变为超级子账号
            if (gtis.AllCountrySubUser == true && gtis.SubAccountsNum == 1 && (EnumGtisAccountType)user.AccountType == EnumGtisAccountType.Sub)
            {
                user.AllCountrySubUser = true;
            }
            //情况2 ：买了超级子账号且是子账号
            if (LeftSuperSubAccountTotalNum > 0 && (EnumGtisAccountType)user.AccountType == EnumGtisAccountType.Sub)
            {
                user.AllCountrySubUser = true;
                LeftSuperSubAccountTotalNum--;
            }
            user.StartDate = dt;
            user.EndDate = gtis.ServiceCycleEnd;
            //if (gtis.IsProcessed == (int)EnumGtisServiceIsProcess.Processed)
            //{
            //    //新增的用户 创建 GTIS账号信息（后补）
            //}
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Insert(user);
        }
        /// <summary>
        /// 更新GTIS用户
        /// </summary>
        /// <param name="userParams"></param>
        /// <param name="oldUser"></param>
        /// <param name="gtis"></param>
        /// <param name="LeftSuperSubAccountTotalNum"></param>
        /// <param name="renew"></param>
        public void UpdateGtisUser(GtisUser_IN userParams, Db_crm_contract_serviceinfo_gtis_user oldUser, Db_crm_contract_serviceinfo_gtis gtis, ref int LeftSuperSubAccountTotalNum, bool renew = false)
        {
            //老用户数据库信息不动（根据gtis主记录状态去判断）  更新用户也要insert
            var user = oldUser.MappingTo<Db_crm_contract_serviceinfo_gtis_user>();
            //oldUser.Deleted = true;
            userParams.MappingTo(user);
            user.ContractServiceInfoGtisId = gtis.Id;
            user.Id = Guid.NewGuid().ToString();
            user.AllCountrySubUser = false;
            ////情况1：如果子账号数量为1并且gtis选了子账号自动分配全部国家，那么这个子账号自动变为超级子账号   240716这个gtis接口变不了的，只有新增时才可以勾选
            //if (gtis.AllCountrySubUser == true && gtis.SubAccountsNum == 1 && (EnumGtisAccountType)user.AccountType == EnumGtisAccountType.Sub)
            //{
            //    user.AllCountrySubUser = true;
            //}
            ////情况2 ：买了超级子账号  240716这个gtis接口变不了的，只有新增时才可以勾选
            //if (LeftSuperSubAccountTotalNum > 0 && (EnumGtisAccountType)user.AccountType == EnumGtisAccountType.Sub)
            //{
            //    user.AllCountrySubUser = true;
            //    LeftSuperSubAccountTotalNum--;
            //}
            if (renew)
            {
                user.ContractServiceInfoGlobalSearchId = null;
            }
            //if (gtis.IsProcessed == (int)EnumGtisServiceIsProcess.Processed)
            //{
            //    //更改 GTIS账号信息（后补）
            //}
            DbOpe_crm_contract_serviceinfo_gtis_user.Instance.Insert(user);
        }


        public void AddGtisPhoneUser(string applyId, string serviceId, MainSysUserPhone userphone)
        {
            var gtisPhoneUser = new Db_crm_contract_serviceinfo_gtis_user_phone();
            gtisPhoneUser.ProductServiceInfoGtisApplId = applyId;
            gtisPhoneUser.SysUserPhoneId = userphone.ID;
            gtisPhoneUser.LinkMan = userphone.LinkMan;
            gtisPhoneUser.IsOpenCrm = userphone.IsOpenCrm;
            DbOpe_crm_contract_serviceinfo_gtis_user_phone.Instance.InsertData(gtisPhoneUser);
        }







        /// <summary>
        /// 同步Gtis用户信息数据
        /// </summary>
        /// <param name="ContractNum"></param>
        /// <param name="userDatas"></param>
        /// <param name="gtisContractNum"></param>
        public List<BM_GtisOpeUserInfo> SynchroGtisUserData(string ContractNum, ref List<Db_crm_contract_serviceinfo_gtis_user> userDatas, ref string gtisContractNum)
        {
            List<BM_GtisOpeUserInfo> gtisOpeUserList = new List<BM_GtisOpeUserInfo>();
            //获取要同步的真实客户编码
            gtisContractNum = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoContractNumByContractNum(ContractNum);
            //获取gtis服务信息
            var gtis = DbOpe_crm_contract_serviceinfo_gtis.Instance.GetGtisServiceInfoByContractNum(gtisContractNum);
            //如果不存在gtis服务，跳出方法
            if (gtis == null)
                return gtisOpeUserList;
            //开通、即将到期、过期状态需要同步校准
            if (gtis.State == (int)EnumContractServiceState.VALID || gtis.State == (int)EnumContractServiceState.OUT)
            {
                //查找当前合同的环球搜服务
                var global = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetGlobalSearchServiceByContractNum(gtisContractNum);
                //根据客户编码获取G5系统中账号信息
                gtisOpeUserList = BLL_GtisOpe.Instance.GetUserInfo(gtisContractNum).Result;
                //如果 gtisOpeUserList 没有结果，作废gtis服务
                if (gtisOpeUserList == null || gtisOpeUserList.Count == 0)
                {
                    //作废gtis服务
                    DbOpe_crm_contract_serviceinfo_gtis.Instance.VoidGtisServiceByServiceId(gtis.Id);
                    //如果存在环球搜服务，作废环球搜服务
                    if (global != null)
                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.VoidGlobalSearchService(global.ContractId, UserId);
                }
                //gtisOpeUserList有返回结果
                else
                {
                    #region GTIS同步
                    //待返回的Gtis用户列表
                    var retGtisUserList = new List<Db_crm_contract_serviceinfo_gtis_user>();
                    //获取当前服务中的gtis用户信息
                    var crmGtisUserList = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetGtisUsersByServeId(gtis.Id);
                    var crmGtisUserIds = crmGtisUserList.Select(e => e.UserId).ToList();
                    var sysUserIDs = gtisOpeUserList.Select(e => e.SysUserID).ToList();
                    //交集--需要刷新的用户信息
                    var intersectUserIds = crmGtisUserIds.Intersect(sysUserIDs).ToList();
                    intersectUserIds.ForEach(sysUserId =>
                    {
                        //g5返回的用户信息
                        var gtisOpeUser = gtisOpeUserList.Find(e => e.SysUserID == sysUserId);
                        //当前crm保存的用户信息
                        var crmGtisUser = crmGtisUserList.Find(e => e.UserId == sysUserId);
                        //修改crmGtisUser的属性
                        crmGtisUser.OpeningStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.TransGtisUserStates(gtisOpeUser.State);
                        crmGtisUser.StartDate = gtisOpeUser.StartServerDate;
                        crmGtisUser.EndDate = gtisOpeUser.EndServerDate;
                        crmGtisUser.LoginIP = gtisOpeUser.LastLoginPlace;
                        crmGtisUser.LastLoginTime = gtisOpeUser.LastLoginDate;
                        crmGtisUser.SharePeopleNum = gtisOpeUser.SharingTimesUse;
                        crmGtisUser.PhoneUser = (gtisOpeUser.AllPhoneUserInfo.Count > 0) ? gtisOpeUser.AllPhoneUserInfo.First().LinkMan : "";
                        crmGtisUser.PhoneUserNum = gtisOpeUser.AllPhoneUserInfo.Count;
                        crmGtisUser.AuthorizationNum = gtisOpeUser.AuthorizationNum;
                        crmGtisUser.ContractServiceInfoGlobalSearchId = global?.Id;
                        crmGtisUser.GlobalSearchCode = gtisOpeUser.GlobalSearchCode;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateDataQueue(crmGtisUser);
                        retGtisUserList.Add(crmGtisUser);
                    });
                    //sysUserIDs 对 crmGtisUserIds 的差集--需要添加的用户信息
                    var tobeAddUserIds = sysUserIDs.Except(crmGtisUserIds).ToList();
                    tobeAddUserIds.ForEach(sysUserId =>
                    {
                        //g5返回的用户信息
                        var gtisOpeUser = gtisOpeUserList.Find(e => e.SysUserID == sysUserId);
                        //当前crm保存的用户信息
                        var crmGtisUser = new Db_crm_contract_serviceinfo_gtis_user();
                        //填充crmGtisUser的属性
                        crmGtisUser.ContractServiceInfoGtisId = gtis.Id;
                        crmGtisUser.UserId = gtisOpeUser.SysUserID;
                        crmGtisUser.AccountNumber = gtisOpeUser.AccountNumber;
                        crmGtisUser.PassWord = gtisOpeUser.PassWord;
                        crmGtisUser.AccountType = (int)(gtisOpeUser.AccountType == 0 ? EnumGtisAccountType.Main : EnumGtisAccountType.Sub);
                        crmGtisUser.IsProcessed = 0;
                        crmGtisUser.OpeningStatus = (int)DbOpe_crm_contract_productserviceinfo_gtis_appl_user.Instance.TransGtisUserStates(gtisOpeUser.State);
                        crmGtisUser.StartDate = gtisOpeUser.StartServerDate;
                        crmGtisUser.EndDate = gtisOpeUser.EndServerDate;
                        crmGtisUser.LoginIP = gtisOpeUser.LastLoginPlace;
                        crmGtisUser.LastLoginTime = gtisOpeUser.LastLoginDate;
                        crmGtisUser.SharePeopleNum = gtisOpeUser.SharingTimesUse;
                        crmGtisUser.PhoneUser = (gtisOpeUser.AllPhoneUserInfo.Count > 0) ? gtisOpeUser.AllPhoneUserInfo.First().LinkMan : "";
                        crmGtisUser.PhoneUserNum = gtisOpeUser.AllPhoneUserInfo.Count;
                        crmGtisUser.AuthorizationNum = gtisOpeUser.AuthorizationNum;
                        crmGtisUser.ContractServiceInfoGlobalSearchId = global?.Id;
                        crmGtisUser.GlobalSearchCode = gtisOpeUser.GlobalSearchCode;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.InsertDataQueue(crmGtisUser);
                        retGtisUserList.Add(crmGtisUser);
                        if (gtisOpeUser.AccountType == 0)
                        {
                            gtis.ServiceCycleStart = gtisOpeUser.StartServerDate;
                            gtis.ServiceCycleEnd = gtisOpeUser.EndServerDate;
                        }

                        if (gtisOpeUser.AccountType == (int)EnumGtisAccountType.Main && gtisOpeUser.IsHistory != null)
                            gtis.IsGtisOldCustomer = gtisOpeUser.IsHistory.Value == 1;

                    });
                    //crmGtisUserIds 对 sysUserIDs 的差集--需要删除的用户信息
                    var tobeDelUserIds = crmGtisUserIds.Except(sysUserIDs).ToList();
                    var tobeDelUserList = crmGtisUserList.Where(e => tobeDelUserIds.Contains(e.UserId)).ToList();
                    tobeDelUserList.ForEach(user =>
                    {
                        user.OpeningStatus = (int)EnumGtisUserOpeningStatus.NotFound;
                        DbOpe_crm_contract_serviceinfo_gtis_user.Instance.UpdateDataQueue(user);
                        retGtisUserList.Add(user);
                    });
                    #endregion
                    #region 环球搜绑定同步 2025.2.23 gtis更换绑定会破坏现有逻辑，考虑注释掉环球搜的同步内容 需要确认是否会从gtis后台增加环球搜码
                    /*if (global != null)
                    {
                        //获取当前服务中的环球搜码信息
                        var globalUserList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(global.Id);
                        //当前合同对应的环球搜码集合
                        var crmGlobalSearchCodes = globalUserList.Select(e => e.AccountNumber).ToList();
                        //当前合同对应的g5账号绑定的环球搜码集合
                        var g5GlobalSearchCodes = gtisOpeUserList.Select(e => e.GlobalSearchCode).ToList();
                        //g5GlobalSearchCodes 对 crmGlobalSearchCodes 的差集--需要添加的环球搜码信息
                        var tobeAddGlobalSearchCodes = g5GlobalSearchCodes.Except(crmGlobalSearchCodes).ToList();
                        //添加环球搜码--?不判断g5绑定的环球搜码是否在别的合同的环球搜服务中使用过,不考虑主子账号，一律按子账号处理
                        tobeAddGlobalSearchCodes.ForEach(code =>
                        {
                            if (!string.IsNullOrEmpty(code))
                            {
                                //判断当前code是否绑定了gtis主账号
                                var openingStatus = gtisOpeUserList.Where(e => e.GlobalSearchCode == code && e.AccountType == (int)EnumGtisAccountType.Main).Any() ? EnumContractServiceGlobalSearchAccountType.PrimaryAccount : EnumContractServiceGlobalSearchAccountType.SubAccount;
                                //当前crm保存的用户信息
                                var globalSearchUser = new Db_crm_contract_serviceinfo_globalsearch_user();
                                //填充crmGtisUser的属性
                                globalSearchUser.ContractServiceInfoGlobalSearchId = global.Id;
                                globalSearchUser.ProductServiceInfoGlobalSearchApplId = global.ProductServiceInfoGlobalSearchApplId;
                                globalSearchUser.AccountNumber = code;
                                globalSearchUser.AccountType = openingStatus;
                                globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                                globalSearchUser.StartDate = global.ServiceCycleStart;
                                globalSearchUser.EndDate = global.ServiceCycleEnd;
                                DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertDataQueue(globalSearchUser);
                            }
                        });
                        //删除环球搜码
                        var tobeDelGlobalSearchCodes = crmGlobalSearchCodes.Except(g5GlobalSearchCodes).ToList();
                        DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.DelGlobalSearchUserByCode(tobeDelGlobalSearchCodes, global.Id);
                    }*/
                    #endregion

                    #region gtis服务数据更新
                    gtis.ServiceCycleStart = retGtisUserList.Where(user => user.AccountType == (int)EnumGtisAccountType.Main).First()?.StartDate;
                    gtis.ServiceCycleEnd = retGtisUserList.Where(user => user.AccountType == (int)EnumGtisAccountType.Main).First()?.EndDate;
                    gtis.SharePeopleNum = retGtisUserList.Where(user => user.OpeningStatus != (int)EnumGtisUserOpeningStatus.NotFound).Max(u => u.SharePeopleNum);
                    gtis.SubAccountsNum = retGtisUserList.Where(user => user.OpeningStatus != (int)EnumGtisUserOpeningStatus.NotFound).Where(u => u.AccountType == (int)EnumGtisAccountType.Sub).Count();
                    gtis.AuthorizationNum = retGtisUserList.Where(user => user.OpeningStatus != (int)EnumGtisUserOpeningStatus.NotFound).ToList().Find(u => u.AccountType == (int)EnumGtisAccountType.Main).AuthorizationNum;
                    if (retGtisUserList.Where(user => user.AccountType == (int)EnumGtisAccountType.Main).First()?.OpeningStatus == 0)
                        gtis.State = (int)EnumContractServiceState.VALID;
                    else if (retGtisUserList.Where(user => user.AccountType == (int)EnumGtisAccountType.Main).First()?.OpeningStatus == 6)
                        gtis.State = (int)EnumContractServiceState.OUT;
                    DbOpe_crm_contract_serviceinfo_gtis.Instance.UpdateData(gtis);
                    #endregion
                    userDatas = retGtisUserList;
                    //提交sql
                    DbOpe_crm_contract_serviceinfo_gtis_user.Instance.SaveQueues();
                    #region 环球搜服务数据更新  2025.2.23 gtis更换绑定会破坏现有逻辑，考虑注释掉环球搜的同步内容
                    /*if (global != null)
                    {
                        var retGlobalUserList = DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.GetGlobalSearchUserByServiceId(global.Id);
                        global.PrimaryAccountsNum = retGlobalUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount && e.Deleted == false).Count();
                        global.SubAccountsNum = retGlobalUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount && e.Deleted == false).Count();
                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(global);
                    }*/

                    #endregion
                }
            }
            return gtisOpeUserList;
        }

        /// <summary>
        /// 检查子账号授权国家次数是否减少
        /// </summary>
        /// <param name="checkAuthorizationNumReduce_In"></param>
        /// <returns></returns>
        public bool CheckAuthorizationNumReduce(CheckAuthorizationNumReduce_In checkAuthorizationNumReduce_In)
        {
            //根据客户编码获取GtisUser数据
            var gtisOpeUserList = BLL_GtisOpe.Instance.GetUserInfo(checkAuthorizationNumReduce_In.ContractNum).Result;
            if (gtisOpeUserList == null || gtisOpeUserList.Count == 0)
                return false;
            //当前子账号授权国家次数
            var oriAuthorizationNum = gtisOpeUserList.Where(e => e.AccountType == 0).First()?.AuthorizationNum.GetValueOrDefault(0);
            //如果次数减少返回true
            if (checkAuthorizationNumReduce_In.AuthorizationNum < oriAuthorizationNum)
                return true;
            else
                return false;
        }


        /// <summary>
        /// 补充完善被续约合同中存在的审核中的环球搜数据(针对旧数据)
        /// </summary>
        private void RefineOldSubmitGlobalSearchData(GtisInfo_OUT gtisInfo)
        {
            //同步Gtis系统数据
            var gtisOpeUserList = BLL_GtisOpe.Instance.GetUserInfo(gtisInfo.ContractNum).Result;
            //当前合同对应的g5账号绑定的环球搜码集合
            var g5GlobalSearchCodes = gtisOpeUserList.Where(e => !string.IsNullOrEmpty(e.GlobalSearchCode)).Select(e => e.GlobalSearchCode).Distinct().ToList();
            //如果gtis账号列表中存在环球搜码，调用环球搜接口查看环球搜码信息
            if (g5GlobalSearchCodes.Count > 0)
            {
                try
                {
                    //调用环球搜查询接口，查询每个环球搜码的账号类别及服务时间
                    var queryStr = string.Join(';', g5GlobalSearchCodes);
                    var url = string.Format(AppSettings.GlobalSearchAPI.CheckUserStatus, queryStr);
                    var globalSearchUserList = new List<Db_crm_contract_serviceinfo_globalsearch_user>();
                    var servCycleStart = new DateTime();
                    var ServCycleEnd = new DateTime();
                    var servMonth = new int();
                    var ret = NetUtil.Post_ReturnString(url).DeserializeNewtonJson<JObject>();
                    if ("200".Equals(ret["status"].ToString()))
                    {
                        var results = ret["results"].ToList();
                        results.ForEach(result =>
                        {
                            var globalSearchUser = new Db_crm_contract_serviceinfo_globalsearch_user();
                            globalSearchUser.Id = Guid.NewGuid().ToString();
                            globalSearchUser.AccountNumber = result["idCst"].ToString();
                            EnumContractServiceGlobalSearchUserState openingStatus = new EnumContractServiceGlobalSearchUserState();
                            if ("active".Equals(result["status"].ToString()))
                                //返回正常，账号状态标记正常
                                globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.Normal;
                            else if ("disabled".Equals(result["status"].ToString()))
                                //返回过期，账号状态标记停用
                                globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.Stop;
                            else if ("noexisted".Equals(result["status"].ToString()))
                                //返回不存在，账号状态标记异常
                                globalSearchUser.OpeningStatus = EnumContractServiceGlobalSearchUserState.AbNormal;
                            globalSearchUser.AccountType = result["acctType"].ToString() == "主账户" ? EnumContractServiceGlobalSearchAccountType.PrimaryAccount : EnumContractServiceGlobalSearchAccountType.SubAccount;
                            globalSearchUser.StartDate = DateTime.Parse(result["begindate"].ToString());
                            globalSearchUser.EndDate = DateTime.Parse(result["enddate"].ToString()); ;
                            globalSearchUserList.Add(globalSearchUser);
                            //获取服务开始/结束时间，计算服务月份
                            if (globalSearchUser.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount)
                            {
                                servCycleStart = globalSearchUser.StartDate.Value;
                                ServCycleEnd = globalSearchUser.EndDate.Value;
                                //计算服务月份
                                DateTime startDate = globalSearchUser.StartDate.Value; ;
                                DateTime endDate = globalSearchUser.EndDate.Value;
                                int monthsDifference = 0;
                                while (startDate <= endDate)
                                {
                                    startDate = startDate.AddMonths(1);
                                    monthsDifference++;
                                }
                                monthsDifference--;
                                servMonth = monthsDifference;
                            }
                        });
                    }

                    //查询当前合同的审核中的环球搜申请数据
                    var globalSearchApply = DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.GetSubmitApplyByContractId(gtisInfo.ContractId);
                    //查询当前合同的审核中的环球搜服务数据
                    var globalSearchServe = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.GetSerivceDataByApplyId(globalSearchApply.Id);
                    //如果不存在服务数据或当前服务是被驳回数据，需要补充服务数据
                    if (globalSearchServe == null || globalSearchServe.State == EnumContractServiceState.RETURNED)
                    {
                        //被驳回数据需要置位历史后再补充数据
                        if (globalSearchServe != null && globalSearchServe.State == EnumContractServiceState.RETURNED)
                        {
                            globalSearchServe.IsHistory = true;
                            DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(globalSearchServe);
                        }
                        globalSearchServe = new Db_crm_contract_serviceinfo_globalsearch();
                        globalSearchServe.ContractNum = gtisInfo.ContractNum;
                        globalSearchServe.ContractId = gtisInfo.ContractId;
                        globalSearchServe.ProductId = globalSearchApply.ProductId;
                        globalSearchServe.ContractProductInfoId = globalSearchApply.ContractProductInfoId;
                        globalSearchServe.ProductServiceInfoGlobalSearchApplId = globalSearchApply.Id;
                        globalSearchServe.AccountGenerationMethod = gtisInfo.AccountGenerationMethod;
                        globalSearchServe.ServiceCycleStart = servCycleStart;
                        globalSearchServe.ServiceCycleEnd = ServCycleEnd;
                        globalSearchServe.ServiceMonth = servMonth;
                        globalSearchServe.State = EnumContractServiceState.VALID;
                        globalSearchServe.PrimaryAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).Count();
                        globalSearchServe.SubAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).Count();
                        globalSearchServe.IsChanged = false;
                        globalSearchServe.IsHistory = false;//如果是被驳回状态的数据，新建service数据后，将原service数据置位历史状态
                        globalSearchServe.RegisteredId = UserId;
                        globalSearchServe.RegisteredTime = DateTime.Now;
                        globalSearchServe.RegisteredRemark = "同步数据自动通过";
                        globalSearchServe.ProcessingType = globalSearchApply.ProcessingType;
                        globalSearchServe.ReviewerId = UserId;
                        globalSearchServe.ReviewerTime = DateTime.Now;
                        globalSearchServe.ReviewerRemark = "同步数据自动通过";
                        globalSearchServe.UpdateDate = DateTime.Now;
                        globalSearchServe.UpdateUser = UserId;
                        globalSearchServe.GlobalSearchRemark = String.Empty;
                        globalSearchServe.Remark = String.Empty;
                        globalSearchServe.Id = DbOpe_crm_contract_serviceinfo_globalsearch.Instance.InsertDataReturnId(globalSearchServe).ToString();
                    }
                    else
                    {
                        globalSearchServe.State = EnumContractServiceState.VALID;
                        globalSearchServe.ServiceCycleStart = servCycleStart;
                        globalSearchServe.ServiceCycleEnd = ServCycleEnd;
                        globalSearchServe.ServiceMonth = servMonth;
                        globalSearchServe.PrimaryAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.PrimaryAccount).Count();
                        globalSearchServe.SubAccountsNum = globalSearchUserList.Where(e => e.AccountType == EnumContractServiceGlobalSearchAccountType.SubAccount).Count();
                        globalSearchServe.ReviewerId = UserId;
                        globalSearchServe.ReviewerTime = DateTime.Now;
                        globalSearchServe.ReviewerRemark = "同步数据自动通过";
                        globalSearchServe.GlobalSearchRemark = String.Empty;
                        globalSearchServe.Remark = String.Empty;
                        DbOpe_crm_contract_serviceinfo_globalsearch.Instance.UpdateData(globalSearchServe);
                    }

                    globalSearchApply.State = EnumProcessStatus.Pass.ToInt();
                    DbOpe_crm_contract_productserviceinfo_globalsearch_appl.Instance.UpdateData(globalSearchApply);

                    globalSearchUserList.ForEach(globalSearchCode =>
                    {
                        globalSearchCode.ContractServiceInfoGlobalSearchId = globalSearchServe.Id;
                        globalSearchCode.ProductServiceInfoGlobalSearchApplId = globalSearchApply.Id;
                    });

                    //
                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.DelGlobalSearchUserByContractNum(gtisInfo.ContractNum);
                    DbOpe_crm_contract_serviceinfo_globalsearch_user.Instance.InsertListData(globalSearchUserList);
                }
                catch
                {
                    throw new ApiException("环球搜账号状态查询失败");
                }
            }
        }
    }
}
