﻿using CRM2_API.Common.AppSetting;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;

namespace CRM2_API.Common.Cache
{
    public partial class LocalCache//最外层必须用partial包着
    {
        /// <summary>
        /// 缓存例子
        /// </summary>
        public class LC_MainProduct : ILocalCache
        {
            #region 设置缓存例子
            public static List<CustomerIndustry_OUT> CustomerIndustryCache { private set; get; } = new List<CustomerIndustry_OUT>(0);//需要先实例化，否则下面lock报错

            public void SetCache()
            {
                lock (CustomerIndustryCache)//一定要锁这个对象，否则出现使用脏数据的情况
                {
                    CustomerIndustryCache = DbOpe_sys_customerindustry.Instance.GetData();
                }
            }
            #endregion
        }
    }
}
