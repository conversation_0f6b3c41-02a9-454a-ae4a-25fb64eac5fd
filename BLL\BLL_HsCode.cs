﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using static CRM2_API.Model.ControllersViewModel.VM_HsCode;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.IO;
using CRM2_API.DAL.DbModel.Crm2;
using SqlSugar;
using System.Text.RegularExpressions;
using System.Collections.Generic;

namespace CRM2_API.BLL
{
    public class BLL_HsCode : BaseBLL<BLL_HsCode>
    {
        /// <summary>
        /// 根据查询条件获取海关编码列表。
        /// </summary>
        public ApiTableOut<SearchHsCodeList_Out> SearchHsCodeList(SearchHsCodeList_In searchHsCodeListIn)
        {
            int total = 0;
            List<SearchHsCodeList_Out> result = DbOpe_sys_hscode.Instance.SearchHsCodeList(searchHsCodeListIn, ref total);
            foreach (SearchHsCodeList_Out h in result)
            {
                h.ActualTimePeriod = SplitTimePeriod(h.ActualTimePeriod);
                h.PlannedTimePeriod = SplitTimePeriod(h.PlannedTimePeriod);
            }
            return new ApiTableOut<SearchHsCodeList_Out> { Data = result, Total = total };
        }


        private string SplitTimePeriod(string TimePeriodString)
        {
            List<string> ActualList = new List<string>();
            if (TimePeriodString.IsNotNullOrEmpty())
            {
                List<DateTime> ActualTimePeriods = TimePeriodString.Split(",").ToList().Select(r => DateTime.ParseExact(r, "yyyyMM", System.Globalization.CultureInfo.CurrentCulture)).OrderBy(r => r).ToList();
                int ActualTimePeriodsCount = ActualTimePeriods.Count;
                if (ActualTimePeriodsCount == 1)
                {
                    return TimePeriodString;
                }
                for (int i = 0; i < ActualTimePeriodsCount; i++)
                {
                    DateTime ActualTimePeriodStart = ActualTimePeriods[i];
                    DateTime CurrentTimePeriod = ActualTimePeriods[i];
                    for (int j = i + 1; j < ActualTimePeriodsCount; j++)
                    {
                        if (CurrentTimePeriod.AddMonths(1) == ActualTimePeriods[j])
                        {
                            CurrentTimePeriod = ActualTimePeriods[j];
                            if (ActualTimePeriodsCount == (j + 1))
                            {
                                if (ActualTimePeriodStart.AddMonths(1) == CurrentTimePeriod)
                                {
                                    ActualList.Add(ActualTimePeriodStart.ToString("yyyyMM"));
                                    ActualList.Add(CurrentTimePeriod.ToString("yyyyMM"));
                                }
                                else
                                {
                                    if (ActualTimePeriodStart == CurrentTimePeriod)
                                    {
                                        ActualList.Add(ActualTimePeriodStart.ToString("yyyyMM"));
                                        if (ActualTimePeriodsCount == (j + 1))
                                        {
                                            ActualList.Add(ActualTimePeriods[j].ToString("yyyyMM"));
                                        }
                                    }
                                    else
                                    {
                                        ActualList.Add(ActualTimePeriodStart.ToString("yyyyMM") + "-" + CurrentTimePeriod.ToString("yyyyMM"));
                                    }
                                }
                            }
                            i = j - 1;
                        }
                        else
                        {
                            if (ActualTimePeriodStart.AddMonths(1) == CurrentTimePeriod)
                            {
                                ActualList.Add(ActualTimePeriodStart.ToString("yyyyMM"));
                                ActualList.Add(CurrentTimePeriod.ToString("yyyyMM"));
                            }
                            else
                            {
                                if (ActualTimePeriodStart == CurrentTimePeriod)
                                {
                                    ActualList.Add(ActualTimePeriodStart.ToString("yyyyMM"));
                                    if (ActualTimePeriodsCount == (j + 1))
                                    {
                                        ActualList.Add(ActualTimePeriods[j].ToString("yyyyMM"));
                                    }
                                }
                                else
                                {
                                    ActualList.Add(ActualTimePeriodStart.ToString("yyyyMM") + "-" + CurrentTimePeriod.ToString("yyyyMM"));
                                    if (ActualTimePeriodsCount == (j + 1))
                                    {
                                        ActualList.Add(ActualTimePeriods[j].ToString("yyyyMM"));
                                    }
                                }
                            }
                            i = j - 1;
                            break;
                        }
                    }
                }
            }
            return ActualList.JoinToString(",");
        }

        /// <summary>
        /// 上传海关编码
        /// </summary>
        /// <param name="uploadHsCode_In"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public List<HsCode_Out> UploadHsCode(UploadHsCode_In uploadHsCode_In)
        {
            var subfix = uploadHsCode_In.HsCodeFile.FileName.Split('.').ToList().Last().ToString();
            if (!"xlsx".Equals(subfix) && !"xls".Equals(subfix))
            {
                throw new ApiException("上传文件格式不正确");
            }

            IWorkbook workbook = new XSSFWorkbook(uploadHsCode_In.HsCodeFile.OpenReadStream());
            ISheet worksheet = workbook.GetSheetAt(0);

            string CountryName = "";
            int Sid = 0;
            int HsCodeType = 0;
            List<HsCode_Out> list = new List<HsCode_Out>();
            // 读取每一行每一列的数据
            for (int row = 0; row <= worksheet.LastRowNum; row++)
            {
                IRow currentRow = worksheet.GetRow(row);
                if (row == 0)
                {
                    ICell CountryCell = currentRow.GetCell(1);
                    CountryName = CountryCell.ToString();
                    Sid = 0;
                    List<Db_sys_g4_dbnames> SidList = DbOpe_sys_g4_dbnames.Instance.GetDataList(r => r.CountryName == CountryName && r.Deleted == false);
                    if (SidList.Count == 0)
                    {
                        throw new ApiException("上传文件中缺少国家");
                    }
                    else
                    {
                        Sid = SidList.First().SID == null ? 0 : SidList.First().SID.Value;
                    }
                }
                else if (row == 1)
                {
                    ICell HsCodeTypeCell = currentRow.GetCell(1);
                    string HsCodeTypeString = HsCodeTypeCell.ToString();
                    HsCodeTypeString = Regex.Replace(HsCodeTypeString, @"[^0-9]+", "");
                    if (HsCodeTypeString == "8")
                    {
                        HsCodeType = 1;
                    }
                    else if (HsCodeTypeString == "10")
                    {
                        HsCodeType = 2;
                    }
                }
                else
                {
                    HsCode_Out HsCode = new HsCode_Out();

                    ICell IAETypeCell = currentRow.GetCell(0);
                    if (IAETypeCell != null)
                    {
                        string IAETypeString = IAETypeCell.ToString();
                        if (IAETypeString == "进口")
                        {
                            HsCode.IAEType = 1;
                        }
                        else if (IAETypeString == "出口")
                        {
                            HsCode.IAEType = 2;
                        }
                    }

                    ICell HsCodeCell = currentRow.GetCell(1);
                    if (HsCodeCell != null)
                    {
                        string HsCodeString = HsCodeCell.ToString();
                        HsCode.HsCode = HsCodeString;
                    }

                    ICell TimePeriodCell = currentRow.GetCell(2);
                    if (TimePeriodCell != null)
                    {
                        string TimePeriodString = TimePeriodCell.ToString();
                        HsCode.TimePeriod = TimePeriodString;
                    }
                    HsCode.SID = Sid;
                    HsCode.CountryName = CountryName;
                    HsCode.HsCodeType = HsCodeType;
                    list.Add(HsCode);
                }
            }

            List<Db_sys_hscode> HsCodeList = DbOpe_sys_hscode.Instance.GetDataList(r => r.SID == Sid && r.Deleted == false);
            foreach (HsCode_Out hs in list)
            {
                bool isHave = HsCodeList.Where(r => r.SID == hs.SID && r.HsCodeType == hs.HsCodeType && r.IAEType == hs.IAEType && r.HsCode == hs.HsCode && r.TimePeriod == hs.TimePeriod).Any();
                if (isHave)
                {
                    hs.State = "失败";
                }
                else
                {
                    hs.State = "成功";
                }
            }

            return list;
        }

        /// <summary>
        /// 添加海关编码
        /// </summary>
        /// <param name="hsCodeIn"></param>
        /// <returns></returns>
        public void AddHsCode(List<HsCode_In> hsCodeIn)
        {
            if (hsCodeIn.Count == 0 || hsCodeIn == null)
            {
                throw new ApiException("未导入海关编码信息");
            }

            if (!hsCodeIn.Any(e => e.State == "成功"))
            {
                throw new ApiException("请导入正确的海关编码信息");
            }

            List<HsCode_In> list = hsCodeIn.Where(e => e.State == "成功").ToList();

            DbOpe_sys_hscode.Instance.TransDeal(() =>
            {
                List<Db_sys_hscode> HsCodeList = DbOpe_sys_hscode.Instance.GetDataList(r => r.SID == hsCodeIn.First().SID && r.Deleted == false);
                List<HsCode_In> result = new List<HsCode_In>();
                foreach (HsCode_In hs in list)
                {
                    bool isHave = HsCodeList.Where(r => r.SID == hs.SID && r.HsCodeType == hs.HsCodeType && r.IAEType == hs.IAEType && r.HsCode == hs.HsCode && r.TimePeriod == hs.TimePeriod).Any();
                    if (!isHave)
                    {
                        result.Add(hs);
                    }
                }
                if (result.Count > 0)
                {
                    DbOpe_sys_hscode.Instance.InsertListData(result);
                }
            });
        }

        /// <summary>
        /// 上传海关编码计划
        /// </summary>
        /// <param name="uploadHsCodePlanned_In"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public List<HsCodePlanned_Out> UploadHsCodePlanned(UploadHsCodePlanned_In uploadHsCodePlanned_In)
        {
            var subfix = uploadHsCodePlanned_In.HsCodeFile.FileName.Split('.').ToList().Last().ToString();
            if (!"xlsx".Equals(subfix) && !"xls".Equals(subfix))
            {
                throw new ApiException("上传文件格式不正确");
            }

            IWorkbook workbook = new XSSFWorkbook(uploadHsCodePlanned_In.HsCodeFile.OpenReadStream());
            ISheet worksheet = workbook.GetSheetAt(0);

            string CountryName = "";
            int Sid = 0;
            int HsCodeType = 0;
            List<HsCodePlanned_Out> list = new List<HsCodePlanned_Out>();
            // 读取每一行每一列的数据
            for (int row = 0; row <= worksheet.LastRowNum; row++)
            {
                IRow currentRow = worksheet.GetRow(row);
                if (row == 0)
                {
                    ICell CountryCell = currentRow.GetCell(1);
                    CountryName = CountryCell.ToString();
                    Sid = 0;
                    List<Db_sys_g4_dbnames> SidList = DbOpe_sys_g4_dbnames.Instance.GetDataList(r => r.CountryName == CountryName && r.Deleted == false);
                    if (SidList.Count == 0)
                    {
                        throw new ApiException("上传文件中缺少国家");
                    }
                    else
                    {
                        Sid = SidList.First().SID == null ? 0 : SidList.First().SID.Value;
                    }
                }
                else if (row == 1)
                {
                    ICell HsCodeTypeCell = currentRow.GetCell(1);
                    string HsCodeTypeString = HsCodeTypeCell.ToString();
                    HsCodeTypeString = Regex.Replace(HsCodeTypeString, @"[^0-9]+", "");
                    if (HsCodeTypeString == "8")
                    {
                        HsCodeType = 1;
                    }
                    else if (HsCodeTypeString == "10")
                    {
                        HsCodeType = 2;
                    }
                    else if (HsCodeTypeString == "1")
                    {
                        HsCodeType = 3;
                    }
                    else if (HsCodeTypeString == "2")
                    {
                        HsCodeType = 4;
                    }
                }
                else
                {
                    HsCodePlanned_Out HsCode = new HsCodePlanned_Out();

                    ICell IAETypeCell = currentRow.GetCell(0);
                    if (IAETypeCell != null)
                    {
                        string IAETypeString = IAETypeCell.ToString();
                        if (IAETypeString == "进口")
                        {
                            HsCode.IAEType = 1;
                        }
                        else if (IAETypeString == "出口")
                        {
                            HsCode.IAEType = 2;
                        }
                    }

                    ICell HsCodeCell = currentRow.GetCell(1);
                    if (HsCodeCell != null)
                    {
                        string HsCodeString = HsCodeCell.ToString();
                        HsCode.HsCode = HsCodeString;
                    }

                    ICell TimePeriodCell = currentRow.GetCell(2);
                    if (TimePeriodCell != null)
                    {
                        string TimePeriodString = TimePeriodCell.ToString();
                        HsCode.TimePeriod = TimePeriodString;

                        List<string> TimePeriodList = new List<string>();
                        List<string> TimePeriods = TimePeriodString.Split(",").ToList();
                        foreach (string TimePeriod in TimePeriods)
                        {
                            if (TimePeriod.IndexOf("-")>0)
                            {
                                string TimePeriodStartString = TimePeriod.Split("-")[0].ToString();
                                string TimePeriodEndString = TimePeriod.Split("-")[1].ToString();
                                DateTime TimePeriodStart = DateTime.ParseExact(TimePeriodStartString, "yyyyMM", System.Globalization.CultureInfo.CurrentCulture);
                                DateTime TimePeriodEnd = DateTime.ParseExact(TimePeriodEndString, "yyyyMM", System.Globalization.CultureInfo.CurrentCulture);

                                while (TimePeriodStart <= TimePeriodEnd)
                                {
                                    TimePeriodList.Add(TimePeriodStart.ToString("yyyyMM"));
                                    TimePeriodStart = TimePeriodStart.AddMonths(1);
                                }
                            }
                            else
                            {
                                TimePeriodList.Add(TimePeriod);
                            }
                        }
                        HsCode.TimePeriods = TimePeriodList;
                    }
                    HsCode.SID = Sid;
                    HsCode.CountryName = CountryName;
                    HsCode.HsCodeType = HsCodeType;
                    list.Add(HsCode);
                }
            }

            List<Db_sys_hscode_planned> HsCodePlannedList = DbOpe_sys_hscode_planned.Instance.GetDataList(r => r.SID == Sid && r.Deleted == false);
            foreach (HsCodePlanned_Out hs in list)
            {
                bool isHave = HsCodePlannedList.Where(r => r.SID == hs.SID && r.HsCodeType == hs.HsCodeType && r.IAEType == hs.IAEType && r.HsCode == hs.HsCode && hs.TimePeriods.Contains(r.TimePeriod)).Any();
                if (isHave)
                {
                    hs.State = "失败";
                }
                else
                {
                    hs.State = "成功";
                }
            }

            return list;
        }

        /// <summary>
        /// 添加海关编码计划
        /// </summary>
        /// <param name="hsCodePlanned_In"></param>
        /// <returns></returns>
        public void AddHsCodePlanned(List<HsCodePlanned_In> hsCodePlanned_In)
        {
            if (hsCodePlanned_In.Count == 0 || hsCodePlanned_In == null)
            {
                throw new ApiException("未导入海关编码计划信息");
            }

            List<HsCodePlanned_In> list = new List<HsCodePlanned_In>();
            foreach (HsCodePlanned_In hs in hsCodePlanned_In)
            {
                foreach (string t in hs.TimePeriods)
                {
                    HsCodePlanned_In item = new HsCodePlanned_In();
                    item.SID = hs.SID;
                    item.CountryName = hs.CountryName;
                    item.IAEType = hs.IAEType;
                    item.HsCodeType = hs.HsCodeType;
                    item.HsCode = hs.HsCode;
                    item.TimePeriod = t;
                    list.Add(item);
                }
            }

            DbOpe_sys_hscode.Instance.TransDeal(() =>
            {
                List<Db_sys_hscode_planned> HsCodePlannedList = DbOpe_sys_hscode_planned.Instance.GetDataList(r => r.SID == hsCodePlanned_In.First().SID && r.Deleted == false);
                List<HsCodePlanned_In> result = new List<HsCodePlanned_In>();
                foreach (HsCodePlanned_In hs in list)
                {
                    bool isHave = HsCodePlannedList.Where(r => r.SID == hs.SID && r.HsCodeType == hs.HsCodeType && r.IAEType == hs.IAEType && r.HsCode == hs.HsCode && r.TimePeriod == hs.TimePeriod).Any();
                    if (!isHave)
                    {
                        result.Add(hs);
                    }
                }
                if (result.Count > 0)
                {
                    DbOpe_sys_hscode_planned.Instance.InsertListData(result);
                }
                else
                {
                    throw new ApiException("请导入正确的海关编码计划信息");
                }
            });
        }

        /// <summary>
        /// 获取海关编码国家
        /// </summary>
        /// <param name="ProductId"></param>
        /// <returns></returns>
        public List<HsCodeCountry_Out> GetHsCodeCountryListByProductId(string ProductId)
        {
            return DbOpe_sys_hscode.Instance.GetHsCodeCountryListByProductId(ProductId);
        }

        /// <summary>
        /// 获取海关编码国家
        /// </summary>
        /// <returns></returns>
        public List<HsCodeCountry_Out> GetHsCodeCountryList()
        {
            return DbOpe_sys_hscode.Instance.GetHsCodeCountryList();
        }
    }
}
