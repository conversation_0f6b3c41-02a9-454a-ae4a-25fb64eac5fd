using CRM2_API.DAL.DbModel.Crm2;

namespace CRM2_API.Model.BLLModel.ServiceOpening
{
    /// <summary>
    /// 历史服务类型枚举
    /// </summary>
    public enum HistoryServiceType
    {
        /// <summary>
        /// GTIS服务
        /// </summary>
        GTIS,

        /// <summary>
        /// 环球搜服务
        /// </summary>
        GlobalSearch,

        /// <summary>
        /// SalesWits服务
        /// </summary>
        SalesWits,

        /// <summary>
        /// 慧思学院服务
        /// </summary>
        College
    }

    /// <summary>
    /// 历史服务集合
    /// </summary>
    public class HistoryServicesCollection
    {
        /// <summary>
        /// 历史Wits主服务
        /// </summary>
        public Db_crm_contract_serviceinfo_wits WitsService { get; set; }

        /// <summary>
        /// 历史GTIS服务
        /// </summary>
        public Db_crm_contract_serviceinfo_gtis GtisService { get; set; }

        /// <summary>
        /// 历史环球搜服务
        /// </summary>
        public Db_crm_contract_serviceinfo_globalsearch GlobalSearchService { get; set; }

        /// <summary>
        /// 历史SaleWits服务
        /// </summary>
        public Db_crm_contract_serviceinfo_saleswits SaleWitsService { get; set; }

        /// <summary>
        /// 历史慧思学院服务
        /// </summary>
        public Db_crm_contract_serviceinfo_college CollegeService { get; set; }

        /// <summary>
        /// 检查是否有任何历史服务
        /// </summary>
        public bool HasAnyService => WitsService != null || GtisService != null || 
                                   GlobalSearchService != null || SaleWitsService != null || 
                                   CollegeService != null;

        /// <summary>
        /// 获取历史服务摘要信息
        /// </summary>
        public string GetSummary()
        {
            var services = new List<string>();
            
            if (WitsService != null) services.Add("Wits");
            if (GtisService != null) services.Add("GTIS");
            if (GlobalSearchService != null) services.Add("GlobalSearch");
            if (SaleWitsService != null) services.Add("SalesWits");
            if (CollegeService != null) services.Add("College");

            return services.Count > 0 ? $"找到历史服务: {string.Join(", ", services)}" : "未找到任何历史服务";
        }
    }

    /// <summary>
    /// 历史服务查找结果
    /// </summary>
    /// <typeparam name="T">服务实体类型</typeparam>
    public class HistoryServiceResult<T> where T : class
    {
        /// <summary>
        /// 是否成功找到历史服务
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 历史服务实体
        /// </summary>
        public T Service { get; set; }

        /// <summary>
        /// 历史Wits服务ID
        /// </summary>
        public string HistoryWitsServiceId { get; set; }

        /// <summary>
        /// 历史服务ID
        /// </summary>
        public string HistoryServiceId { get; set; }

        /// <summary>
        /// 查找消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 服务类型
        /// </summary>
        public HistoryServiceType ServiceType { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static HistoryServiceResult<T> CreateSuccess(T service, string historyWitsServiceId, string historyServiceId, HistoryServiceType serviceType)
        {
            return new HistoryServiceResult<T>
            {
                Success = true,
                Service = service,
                HistoryWitsServiceId = historyWitsServiceId,
                HistoryServiceId = historyServiceId,
                ServiceType = serviceType,
                Message = $"成功找到历史{serviceType}服务"
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static HistoryServiceResult<T> CreateFailure(string message, HistoryServiceType serviceType)
        {
            return new HistoryServiceResult<T>
            {
                Success = false,
                Service = null,
                ServiceType = serviceType,
                Message = message
            };
        }
    }
}
