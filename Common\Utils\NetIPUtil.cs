using Microsoft.AspNetCore.Http;
using System.Net;

namespace CRM2_API.Common.Utils
{
    /// <summary>
    /// 网络IP相关工具类
    /// </summary>
    public class NetIPUtil
    {
        /// <summary>
        /// 获取用户真实IP地址 - 增强版，处理多种代理情况
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>用户真实IP地址</returns>
        public static string GetRealIpAddress(HttpContext context)
        {
            string ip = null;
            
            // 检查X-Forwarded-For头（最常用）
            if (context.Request.Headers.TryGetValue("X-Forwarded-For", out var forwardedFor))
            {
                // X-Forwarded-For格式为: client, proxy1, proxy2, ...
                // 取第一个值即为客户端原始IP
                ip = forwardedFor.FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim();
            }
            
            // 检查X-Real-IP头（Nginx常用）
            if (string.IsNullOrEmpty(ip) && context.Request.Headers.TryGetValue("X-Real-IP", out var realIp))
            {
                ip = realIp.FirstOrDefault();
            }
            
            // 检查CF-Connecting-IP (Cloudflare)
            if (string.IsNullOrEmpty(ip) && context.Request.Headers.TryGetValue("CF-Connecting-IP", out var cfIp))
            {
                ip = cfIp.FirstOrDefault();
            }
            
            // 检查True-Client-IP (Akamai)
            if (string.IsNullOrEmpty(ip) && context.Request.Headers.TryGetValue("True-Client-IP", out var trueClientIp))
            {
                ip = trueClientIp.FirstOrDefault();
            }
            
            // 如果所有HTTP头都没有，就使用RemoteIpAddress
            if (string.IsNullOrEmpty(ip) && context.Connection?.RemoteIpAddress != null)
            {
                ip = context.Connection.RemoteIpAddress.ToString();
            }
            
            // 检查是否为本地IPv6回环地址
            if (ip == "::1")
            {
                ip = "127.0.0.1";
            }
            
            // 进行IP地址有效性验证
            if (!string.IsNullOrEmpty(ip) && IsValidIpAddress(ip))
            {
                return ip;
            }
            
            return "Unknown";
        }
        
        /// <summary>
        /// 验证IP地址有效性
        /// </summary>
        /// <param name="ip">待验证的IP地址</param>
        /// <returns>是否有效</returns>
        private static bool IsValidIpAddress(string ip)
        {
            // 简单验证IP地址格式
            if (IPAddress.TryParse(ip, out _))
            {
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 获取Nginx代理后的客户端IP (兼容原有方法)
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>客户端IP地址</returns>
        public static string GetIp_Nginx(HttpContext context)
        {
            // 调用新的方法，确保向后兼容
            return GetRealIpAddress(context);
        }
    }
} 