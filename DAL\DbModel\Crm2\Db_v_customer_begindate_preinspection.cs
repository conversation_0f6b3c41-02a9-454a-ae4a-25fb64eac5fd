using SqlSugar;
using System;

namespace CRM2_API.DAL.DbModel.Crm2
{
    /// <summary>
    /// 预验证专用客户视图
    /// </summary>
    [SugarTable("v_customer_begindate_preinspection")]
    public class Db_v_customer_begindate_preinspection
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public string CustomerId { get; set; }
        /// <summary>
        /// 客户编号
        /// </summary>
        public string CustomerNum { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }
        
        /// <summary>
        /// 是否在私有池中 1:在 0:不在
        /// </summary>
        public int privatePool { get; set; }
        
        /// <summary>
        /// 是否在公有池中 1:在 0:不在
        /// </summary>
        public int publicPool { get; set; }
        
        /// <summary>
        /// 进入池的日期（对于私有池是CreateDate，对于公有池是InvalidDate）
        /// </summary>
        public DateTime? BeginDate { get; set; }
    }
} 