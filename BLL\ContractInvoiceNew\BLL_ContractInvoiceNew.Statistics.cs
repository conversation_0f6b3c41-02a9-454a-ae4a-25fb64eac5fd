using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using CRM2_API.DAL.DbModel.Crm2;
using System.Collections.Generic;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Common.Utils;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;
using System;
using System.Linq;
using SqlSugar;
using CRM2_API.DAL.DbCommon;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 合同发票业务类 - 统计部分
    /// </summary>
    public partial class BLL_ContractInvoiceNew
    {
        /// <summary>
        /// 获取发票统计信息
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>发票统计信息</returns>
        public InvoiceStatisticsResponse GetInvoiceStatistics(QueryInvoiceApplicationListRequest query)
        {
            var response = new InvoiceStatisticsResponse
            {
                TotalApplications = 0,
                TotalInvoiceAmount = 0,
                TotalRefundAmount = 0
            };
            
            try
            {
                // 创建查询对象 - 使用数据库级别的查询
                var db = DbContext.Crm2Db;

                // 基础查询 - 发票申请与关联表
                var query1 = db.Queryable<Db_crm_invoice_application, Db_crm_contract, Db_crm_invoice, Db_crm_invoice_review, Db_crm_invoice_refund_application, Db_v_customer_subcompany_private_user, Db_crm_proforma_invoice, Db_crm_invoice_receipt_matching, Db_crm_contract_receiptregister, Db_crm_contract_receipt_details, Db_crm_collectioninfo, Db_crm_invoice>
                    (
                        (invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection, refundInvoice) =>
                        new JoinQueryInfos(
                            JoinType.Left, invoiceAppl.ContractId == contract.Id && contract.Deleted != true,
                            JoinType.Left, invoiceAppl.Id == invoice.InvoiceApplicationId && invoice.Deleted != true,
                            JoinType.Left, invoiceAppl.Id == invoiceAudit.InvoiceApplicationId && invoiceAudit.Deleted != true,
                            JoinType.Left, invoice.Id == invoiceRefundAppl.InvoiceId,
                            JoinType.Left, contract.FirstParty == cspu.Id,
                            JoinType.Left, invoiceAppl.Id == proInvoice.ApplicationId && proInvoice.Deleted != true,
                            JoinType.Left, invoiceAppl.Id == matching.InvoiceApplicationId && matching.Deleted != true,
                            JoinType.Left, matching.ReceiptId == receipt.Id && receipt.Deleted != true,
                            JoinType.Left, receiptRelation.ContractReceiptRegisterId == receipt.Id && receiptRelation.Deleted != true,
                            JoinType.Left, collection.Id == receiptRelation.CollectionInfoId && collection.Deleted != true,
                            JoinType.Left, invoice.RelatedInvoiceId == refundInvoice.Id && refundInvoice.Deleted != true
                        )
                    )
                    .Where(invoiceAppl => invoiceAppl.Deleted != true);
                // 应用数据权限控制 - 根据用户角色确定可查看的数据范围
                // 检查超级管理员权限 - 超级管理员可以查看所有数据
                bool superRole = BLL_Role.Instance.CheckSuperUser();
                
                // 非超级管理员需要应用权限过滤
                if (!superRole)
                {
                    // 获取当前用户信息，包括角色
                    var currentUser = DbOpe_v_userwithorg.Instance.GetUserByIdFromCache(UserId);
                    
                    // 获取用户表单权限
                    var userFormIds = DbOpe_sys_form.Instance.GetFormIdsByUserId(UserId);
                    
                    // 检查是否有开票权限（经办人权限）
                    bool hasInvoiceOperatorPermission = userFormIds.Any(f => 
                        f.ControllerName == "InvoiceSystem" && 
                        (f.MethodName == "AuditInvoiceApplication" || 
                         f.MethodName == "AutoMatching" ||
                         f.MethodName == "ManualMatching"));
                    
                    // 检查是否有复核权限
                    bool hasInvoiceReviewPermission = userFormIds.Any(f => 
                        f.ControllerName == "InvoiceSystem" && 
                        (f.MethodName == "ReviewInvoiceApplication" || 
                         f.MethodName == "ReviewMatching" ||
                         f.MethodName == "ConfirmInvoiceRefundReview"));
                    
                    // 判断用户是否为后台管理人员
                    bool isBackendManager = DbOpe_sys_user.Instance.CheckUserIsManager(UserId);
                    
                    // 判断是否有特定筛选条件
                    bool hasSpecificFilter = 
                                           !string.IsNullOrEmpty(query.ContractName) || 
                                           !string.IsNullOrEmpty(query.FirstPartyName) ||
                                           !string.IsNullOrEmpty(query.ApplicantId) ||
                                           !string.IsNullOrEmpty(query.InvoiceNumber) ;

                    // 客户经理只能看自己客户的发票
                    if (!isBackendManager)
                    {
                        query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                              matching, receipt, receiptRelation, collection) => 
                            cspu.CompanyCurrentUser == UserId // 客户当前所属人是自己
                        );
                    }
                    else
                    {
                        // 有特定筛选条件时，有权限的后台人员可以查看所有匹配的发票
                        if (hasSpecificFilter && (hasInvoiceOperatorPermission || hasInvoiceReviewPermission))
                        {
                            query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                                  matching, receipt, receiptRelation, collection) => 
                                invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceApplied ||
                                invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceReviewRejected ||
                                invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.RefundApplied ||
                                invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.RefundReviewRejected ||
                                (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted && 
                                 invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.NotReceived) ||
                                (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted && 
                                 invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.WaitingConfirm) ||
                                (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted && 
                                 invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.Rejected)
                            );
                        }
                        // 复核人可以看到：相关状态的发票
                        else if (!hasInvoiceOperatorPermission && hasInvoiceReviewPermission && !hasSpecificFilter)
                        {
                            query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation,collection) => 
                                invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceProcessed ||
                                invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.RefundProcessed ||
                                (invoiceAppl.DisplayStatus == (int)EnumInvoiceDisplayStatus.InvoiceCompleted && 
                                 invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.WaitingAudit)
                            );
                        }
                        // 同时具有经办和复核权限的用户可以看到所有待处理的发票
                        else if (hasInvoiceOperatorPermission && hasInvoiceReviewPermission && !hasSpecificFilter)
                        {
                            // 有全部权限，不需要额外限制
                        }
                        // 没有任何发票处理权限的后台人员
                        else
                        {
                            // 没有权限的后台人员什么也看不了
                            query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                                  matching, receipt, receiptRelation, collection) => false);
                        }
                    }
                }

                // 应用查询条件
                #region 合同相关参数
                if (!string.IsNullOrEmpty(query.ContractId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.ContractId == query.ContractId);
                }

                if (!string.IsNullOrEmpty(query.ContractName))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        contract.ContractName.Contains(query.ContractName));
                }

                if (!string.IsNullOrEmpty(query.FirstPartyName))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        cspu.CompanyName.Contains(query.FirstPartyName));
                }

                if (query.ContractStatus != null && query.ContractStatus.Count > 0)
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        contract.ContractStatus.HasValue && 
                        query.ContractStatus.Contains(contract.ContractStatus.Value));
                }

                if (query.StampReviewStatus != null && query.StampReviewStatus.Count > 0)
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        contract.StampReviewStatus.HasValue && 
                        query.StampReviewStatus.Contains(contract.StampReviewStatus.Value));
                }

                if (!string.IsNullOrEmpty(query.CustomerNum))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        contract.ContractNum.Contains(query.CustomerNum));
                }
                #endregion

                #region 发票申请相关参数
                if (query.DisplayStatus != null && query.DisplayStatus.Count > 0)
                {
                    var statusList = query.DisplayStatus.Select(status => (int)status).ToList();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        statusList.Contains(invoiceAppl.DisplayStatus ?? 0));
                }

                if (!string.IsNullOrEmpty(query.ApplicantId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.ApplicantId == query.ApplicantId);
                }

                if (query.ApplicationStartTime.HasValue)
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.ApplyTime >= query.ApplicationStartTime.Value);
                }

                if (query.ApplicationEndTime.HasValue)
                {
                    // 添加一天减一秒，使查询包含结束当天的所有记录
                    DateTime endTime = query.ApplicationEndTime.Value.AddDays(1).AddSeconds(-1);
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.ApplyTime <= endTime);
                }

                if (!string.IsNullOrEmpty(query.ApplicationInvoiceCompanyId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.BillingCompany == query.ApplicationInvoiceCompanyId);
                }

                if (!string.IsNullOrEmpty(query.ApplicationInvoiceTitle))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.BillingHeader.Contains(query.ApplicationInvoiceTitle));
                }

                if (!string.IsNullOrEmpty(query.ApplicationInvoiceDetails))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.InvoicingDetails.Contains(query.ApplicationInvoiceDetails));
                }
                #endregion

                #region 发票开具相关参数
                if (query.BillingType != null && query.BillingType.Count > 0)
                {
                    var typeList = query.BillingType.Select(type => (int)type).ToList();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        typeList.Contains(invoiceAppl.BillingType));
                }

                if (query.InvoiceType != null && query.InvoiceType.Count > 0)
                {
                    var typeList = query.InvoiceType.Select(type => (int)type).ToList();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        typeList.Contains(invoiceAppl.InvoiceType));
                }

                if (query.RefundStatus != null && query.RefundStatus.Count > 0)
                {
                    var statusList = query.RefundStatus.Select(status => (int)status).ToList();
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoice != null && statusList.Contains(invoice.RefundStatus));
                }

                if (!string.IsNullOrEmpty(query.InvoiceCompanyId))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.BillingCompany == query.InvoiceCompanyId);
                }

                if (!string.IsNullOrEmpty(query.InvoiceHearder))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.BillingHeader.Contains(query.InvoiceHearder));
                }

                if (!string.IsNullOrEmpty(query.InvoiceDetails))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        invoiceAppl.InvoicingDetails.Contains(query.InvoiceDetails));
                }

                if (!string.IsNullOrEmpty(query.InvoiceNumber))
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice != null && invoice.InvoiceNumber.Contains(query.InvoiceNumber)) || 
                        (proInvoice != null && proInvoice.InvoiceNumber.Contains(query.InvoiceNumber)));
                }

                if (query.InvoicingDateStart.HasValue)
                {
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice != null && invoice.InvoicingDate >= query.InvoicingDateStart.Value) || 
                        (proInvoice != null && proInvoice.InvoiceDate >= query.InvoicingDateStart.Value));
                }

                if (query.InvoicingDateEnd.HasValue)
                {
                    DateTime endDate = query.InvoicingDateEnd.Value.AddDays(1).AddSeconds(-1);
                    query1 = query1.Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, 
                                          matching, receipt, receiptRelation, collection) => 
                        (invoice != null && invoice.InvoicingDate <= endDate) || 
                        (proInvoice != null && proInvoice.InvoiceDate <= endDate));
                }
                #endregion

                // 1. 计算发票申请总数
                response.TotalApplications = query1.Count();
                
                // 2. 计算发票总金额 - 直接在数据库中计算
                // 如果没有记录，Sum 方法会返回 0
                response.TotalInvoiceAmount = query1.Sum((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection,refundInvoice) => 
                SqlFunc.IIF(invoice != null, invoice.TotalAmount, proInvoice != null ? proInvoice.Amount : 0)
                );
                
                // 3. 计算退款总金额 - 需要查询关联的退款记录
                // 获取已完成的发票ID列表
                var completedInvoiceIds = query1
                    .Where((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) => 
                        invoice.MatchingStatus == (int)EnumInvoiceMatchingStatus.MatchSuccess)
                    .Select((invoiceAppl, contract, invoice, invoiceAudit, invoiceRefundAppl, cspu, proInvoice, matching, receipt, receiptRelation, collection) => invoice.Id)
                    .ToList();
                
                if (completedInvoiceIds.Any())
                {
                    // 查询这些发票关联的所有已完成退款申请的金额总和
                    // 如果没有记录，Sum 方法会返回 0
                    response.TotalRefundAmount = db.Queryable<Db_crm_invoice_refund_application>()
                        .Where(r => completedInvoiceIds.Contains(r.InvoiceId) && 
                                r.AuditStatus == (int)EnumRefundApplicationStatus.Completed && 
                                r.Deleted != true)
                        .Sum(r => r.RefundAmount);
                }
                
            }
            catch (Exception ex)
            {
                LogUtil.AddErrorLog($"获取发票统计信息时发生错误: {ex.Message}");
                throw new ApiException("获取发票统计信息失败");
            }
            
            return response;
        }
    }
} 