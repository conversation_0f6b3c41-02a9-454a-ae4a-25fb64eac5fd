﻿using System.ComponentModel;
using System.Reflection;

namespace CRM2_API.Common.Cache
{
    /// <summary>
    /// 本地缓存
    /// </summary>
    public partial class LocalCache
    {
        #region 初始化及读取所有枚举的方法
        /// <summary>
        /// 当前类的反射对象
        /// </summary>
        static readonly Dictionary<Enum_LocalCahce, ILocalCache> dicAllCacheMethod = new();
        /// <summary>
        /// 初始化所有缓存
        /// </summary>
        public static void InitAllCache()
        {
            var thisType = typeof(LocalCache);
            //读取所有属性，找到对应的Set方法，然后执行
            var allCacheEnum = Enum.GetValues<Enum_LocalCahce>();
            var allClass = thisType.GetNestedTypes(BindingFlags.Public | BindingFlags.DeclaredOnly | BindingFlags.CreateInstance);
            //多线程加载缓存
            using(var tu= TaskUtil.GetTaskMaxUtil(5))
            {
                allCacheEnum.ForEach(e =>
                {
                    var cacheType = allClass.Where(m => m.Name == e.ToString() && m.GetInterface(nameof(ILocalCache)) != null).FirstOrDefault();
                    if (cacheType != null)
                    {
                        var instance = Activator.CreateInstance(cacheType) as ILocalCache;
                        dicAllCacheMethod.Add(e, instance);
                        tu.AddRunTask(() =>
                        {
                            instance.SetCache();
                        });
                    }
                });
                tu.WaitAll();
            }
            dicAllCacheMethod.TrimExcess();
            InitSelectCache(allCacheEnum);
        }
        /// <summary>
        /// 初始化选中的缓存
        /// </summary>
        /// <param name="cacheEnums"></param>
        public static void InitSelectCache(Enum_LocalCahce[] cacheEnums)
        {
            using (var taskHelp = TaskUtil.GetTaskMaxUtil(10))
            {
                cacheEnums.ForEach(e =>
                {
                    taskHelp.AddRunTask(() =>
                    {
                        dicAllCacheMethod[e].SetCache();
                    });
                });
                taskHelp.WaitAll();
            }
        }
        /// <summary>
        /// 获取所有缓存名称
        /// </summary>
        /// <returns>key：缓存枚举   value:缓存描述</returns>
        public static Dictionary<Enum_LocalCahce, string> GetAllCacheName()
        {
            var allEnum = Enum.GetValues<Enum_LocalCahce>();
            Dictionary<Enum_LocalCahce, string> dicRet = new(allEnum.Length);
            allEnum.ForEach(e =>
            {
                dicRet.Add(e, e.GetEnumDescription());
            });
            return dicRet;
        }
        #endregion

    }

    /// <summary>
    /// 本地缓存，需要继承的接口
    /// </summary>
    public interface ILocalCache
    {
        /// <summary>
        /// 设置缓存的方法
        /// 方法中，可以初始化多个对象
        /// </summary>
        void SetCache();
    }

    /// <summary>
    /// 本地缓存枚举，每个Set方法做为枚举
    /// </summary>
    public enum Enum_LocalCahce
    {
#if DEBUG
        /// <summary>
        /// 缓存例子
        /// </summary>
        [Description("缓存例子")]
        LC_Example,
#endif
        /// <summary>
        /// 系统字典
        /// </summary>
        [Description("系统字典")]
        LC_SysDictionary,
        /// <summary>
        /// 系统地址表
        /// </summary>
        [Description("系统地址表")]
        LC_Address,
        /// <summary>
        /// 客户行业产品表
        /// </summary>
        [Description("客户行业产品表")]
        LC_MainProduct,
        /// <summary>
        /// 合同信息变更列名表
        /// </summary>
        [Description("合同信息变更列名表")]
        LC_ContractChangeType


    }
}
