using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using static CRM2_API.Model.ControllersViewModel.VM_Collectioninfo;
using static CRM2_API.Model.ControllersViewModel.VM_ContractReceiptRegister;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 测试数据 BLL 类 - 银行到账和业绩登记相关测试数据
    /// </summary>
    public partial class BLL_TestData
    {
        /// <summary>
        /// 准备到账确认成功后的测试数据（包含创建客户、合同、银行到账和登记业绩）
        /// </summary>
        /// <param name="isManualMatching">是否使用手动匹配(true:手动匹配, false:自动匹配)</param>
        /// <returns>测试数据创建结果</returns>
        public CompleteTestDataResult PrepareReceiptTestData(bool isManualMatching = false)
        {
            try
            {
                // 步骤1：创建客户，添加随机后缀确保不会自动匹配到现有合同
                string randomSuffix = DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999);
                var customerResult = CreateTestCustomer(randomSuffix);
                if (customerResult == null)
                {
                    return null;
                }
                
                // 步骤2：创建合同
                var contractResult = CreateTestContract(customerResult.Id);
                if (contractResult == null)
                {
                    return null;
                }
                
                // 对于手动匹配，我们需要确保自动匹配不会发生
                // 获取合同的付款信息，确保我们创建的银行到账记录与合同付款信息不匹配
                var paymentInfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByContractId(contractResult.Id);
                if (paymentInfo == null)
                {
                    return null;
                }
                
                // 步骤3：创建银行到账记录
                string collectionInfoId;
                if (isManualMatching)
                {
                    // 手动匹配模式：使用与合同付款信息不同的金额，避免自动匹配
                    decimal amount = paymentInfo.PlannedArrivalAmount.HasValue ? 
                        paymentInfo.PlannedArrivalAmount.Value + 123.45m : 10123.45m;
                    
                    collectionInfoId = CreateBankReceipt(
                        contractResult.Id, 
                        customerResult.Name,
                        amount, // 使用不同的金额，避免自动匹配
                        null,  // 交易凭证保持默认
                        isManualMatching // 标记为手动匹配
                    );
                }
                else
                {
                    // 自动匹配模式：使用与合同付款信息相同的金额，促使自动匹配
                    decimal amount = paymentInfo.PlannedArrivalAmount.HasValue ? 
                        paymentInfo.PlannedArrivalAmount.Value : 10000m;
                    
                    collectionInfoId = CreateBankReceipt(
                        contractResult.Id, 
                        customerResult.Name,
                        amount, // 使用相同的金额，促使自动匹配
                        null,   // 交易凭证保持默认
                        isManualMatching // 标记为自动匹配
                    );
                }
                
                if (string.IsNullOrEmpty(collectionInfoId))
                {
                    return null;
                }
                
                // 如果是手动匹配，则调用手动匹配API
                if (isManualMatching)
                {
                    try
                    {
                        var manualMatchingData = new AddManualMatching_In
                        {
                            ContractId = contractResult.Id,
                            CollectionInfoId = collectionInfoId,
                            Remark = "手动匹配测试数据"
                        };
                        
                        BLL_Collectioninfo.Instance.AddManualMatching(manualMatchingData);
                    }
                    catch (Exception ex)
                    {
                        // 如果手动匹配失败，记录错误并返回null
                        Console.WriteLine($"手动匹配失败: {ex.Message}");
                        return null;
                    }
                }
                
                // 步骤4：登记业绩
                var receiptRegisterId = RegisterReceipt(contractResult.Id, collectionInfoId);
                if (string.IsNullOrEmpty(receiptRegisterId))
                {
                    return null;
                }
                
                // 步骤5：复核业绩
                var auditResult = AuditReceipt(receiptRegisterId);
                if (!auditResult)
                {
                    return null;
                }
                
                // 步骤6：确认业绩
                var confirmResult = ConfirmReceipt(receiptRegisterId);
                if (!confirmResult)
                {
                    return null;
                }
                
                // 返回成功结果
                var result = new CompleteTestDataResult
                {
                    CustomerId = customerResult.Id,
                    CustomerName = customerResult.Name,
                    ContractId = contractResult.Id,
                    ContractName = contractResult.Name,
                    CollectionInfoId = collectionInfoId,
                    ReceiptRegisterId = receiptRegisterId
                };
                
                // 设置成功消息
                result.SuccessMessage = isManualMatching ? 
                    "成功创建手动匹配测试数据" : 
                    "成功创建自动匹配测试数据";
                
                return result;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 创建银行到账记录
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="customerName">客户名称</param>
        /// <param name="amount">到账金额</param>
        /// <param name="transactionReceipt">交易凭证</param>
        /// <param name="isManualMatching">是否手动匹配</param>
        /// <returns>银行到账记录ID</returns>
        public string CreateBankReceipt(string contractId, string customerName = null, decimal? amount = null, 
            string transactionReceipt = null, bool isManualMatching = false)
        {
            try
            {
                // 获取合同信息
                var contract = DbOpe_crm_contract.Instance.GetContractById(contractId);
                if (contract == null)
                {
                    return null;
                }

                if (string.IsNullOrEmpty(customerName))
                {
                    // 获取客户信息
                    var company = DbOpe_crm_customer_subcompany.Instance.GetData(d => d.Id == contract.FirstParty);
                    if (company == null)
                    {
                        return null;
                    }
                    else
                    {
                        customerName = company.CompanyName;
                    }
                }

                // 设置默认金额
                decimal actualAmount = amount ?? 10000m;

                // 构建银行到账数据
                var collectionInfo = new AddCollectionInfo_In
                {
                    TransactionReceipt = transactionReceipt ?? (customerName + "资讯合同"),
                    CollectingCompanyName = "济南数据",
                    ArrivalDate = DateTime.Now,
                    PaymentCompanyName = customerName,
                    Currency = 1, // 人民币
                    ArrivalAmount = actualAmount, // 到账金额
                    PaymentMethod = 1, // 银行转账
                    Honour = 0,
                    BankPaymentAmount = actualAmount,
                    CashPaymentAmount = 0,
                    IdentifyStatus = 2, // 已识别
                    CashSourceRemarks = "",
                    CollectioninfoAttachFile = null,
                    CollectingBankName = "中国银行",
                    BankAccountNumber = "",
                    IsDeposit = false
                };
                
                // 调用BLL添加银行到账记录
                var result = BLL_Collectioninfo.Instance.AddCollectionInfo(new List<AddCollectionInfo_In> { collectionInfo });
                string collectionInfoId = result != null && result.Count > 0 ? result[0].Id : null;
                
                return collectionInfoId;
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// 登记业绩
        /// </summary>
        /// <param name="contractId">合同ID</param>
        /// <param name="collectionInfoId">银行到账记录ID</param>
        /// <returns>登记业绩ID</returns>
        public string RegisterReceipt(string contractId, string collectionInfoId)
        {
            try
            {
                // 获取合同信息
                var contract = DbOpe_crm_contract.Instance.GetContractById(contractId);
                if (contract == null)
                {
                    return null;
                }
                
                // 获取合同产品信息
                var contractProducts = DbOpe_crm_contract_productinfo.Instance.GetContractProductInfoByContractId(contractId);
                if (contractProducts == null || contractProducts.Count == 0)
                {
                    return null;
                }
                
                // 获取合同支付信息
                var paymentInfo = DbOpe_crm_contract_paymentinfo.Instance.GetPaymentInfoByContractId(contractId);
                if (paymentInfo == null)
                {
                    return null;
                }
                
                var matchingId = DbOpe_crm_contract_collectioninfo_automatching.Instance.GetData(d => 
                    d.CollectionInfoId == collectionInfoId && 
                    d.ContractId == contractId &&
                    d.State == (int)EnumAutoMatchingState.MatchedToBeConfirmed && 
                    d.IsAutoMatching == true)?.Id;
                    
                if (matchingId == null)
                {
                    return null;
                }

                // 构建登记业绩数据
                var registerData = new AddReceiptRegister_In
                {
                    ContractId = new Guid(contractId),
                    ContractPaymentInfoId = new Guid(paymentInfo.Id),
                    IsReceipt = 2, // 已收款
                    ProtectionDeadline = DateTime.Now.AddYears(1), // 一年后
                    IsSecret = 2, // 不保密
                    CustomerType = 0, // 默认客户类型
                    Remark = "",
                    IsSettlementExchange = 2, // 不结算
                    BelongingMonth = DateTime.Now, // 当前月份
                    GlobalSearchUnit = 0.5m, // 默认全球搜索单位
                    ReceiptRegisterCongratulations = new List<ContractReceiptRegisterCongratulations>(), // 恭喜确认为空
                    ReceiptDetails = new List<ContractReceiptDetails>
                    {
                        new ContractReceiptDetails
                        {
                            CollectionInfoId = new Guid(collectionInfoId),
                            PaymentType = 3 // 常规付款
                        }
                    },
                    ReceiptRegisterAchievement = new List<ContractReceiptRegisterAchievement>(),
                    CollectionInfoAutoMatchingId = matchingId, // 自动匹配ID
                    ReceiptRegisterCoupons = new List<ContractReceiptRegisterCoupon>
                    {
                        new ContractReceiptRegisterCoupon
                        {
                            YearNum = 0,
                            Deadline = null,
                            Remark = null,
                            CouponCount = 0
                        }
                    }
                };
                
                // 添加产品业绩
                foreach (var product in contractProducts)
                {
                    registerData.ReceiptRegisterAchievement.Add(new ContractReceiptRegisterAchievement
                    {
                        IsSuperSubAccount = false,
                        ContractProductInfoId = new Guid(product.Id),
                        Type = 1, // 常规业绩
                        ProductType = (int)product.ProductType,
                        PerformanceDeduction = 0
                    });
                }
                
                // 添加项目业绩
                registerData.ReceiptRegisterProjectInfoAchievement = new List<ContractReceiptRegisterProjectInfoAchievement>
                {
                    new ContractReceiptRegisterProjectInfoAchievement
                    {
                        ProjectInfoId = new Guid("6313f729-fec1-11ed-bc7b-30d042e24322"), // 项目ID 1
                        Type = 1, // 项目类型
                        Price = null,
                        ItemsNum = null,
                        OtherItemsDetails = new List<Guid>(),
                        PerformanceDeduction = 0
                    },
                    new ContractReceiptRegisterProjectInfoAchievement
                    {
                        ProjectInfoId = new Guid("63fb1a67-fec1-11ed-bc7b-30d042e24322"), // 项目ID 2
                        Type = 2, // 项目类型
                        Price = null,
                        ItemsNum = null,
                        OtherItemsDetails = new List<Guid>(),
                        PerformanceDeduction = 0
                    },
                    new ContractReceiptRegisterProjectInfoAchievement
                    {
                        ProjectInfoId = new Guid("6447f0f9-fec1-11ed-bc7b-30d042e24322"), // 项目ID 3
                        Type = 3, // 项目类型
                        Price = null,
                        ItemsNum = null,
                        OtherItemsDetails = new List<Guid>(),
                        PerformanceDeduction = 0
                    }
                };
                
                // 调用BLL添加业绩登记
                string receiptRegisterId = BLL_ContractReceiptRegister.Instance.AddReceiptRegister(registerData);
                return receiptRegisterId;
            }
            catch (Exception)
            {
                return null;
            }
        }
        
        /// <summary>
        /// 复核业绩
        /// </summary>
        /// <param name="receiptRegisterId">业绩登记ID</param>
        /// <returns>是否成功</returns>
        public bool AuditReceipt(string receiptRegisterId)
        {
            try
            {
                if (!string.IsNullOrEmpty(receiptRegisterId))
                {
                    // 获取记录信息
                    var receipt = BLL_ContractReceiptRegister.Instance.GetReceiptRegisterById(receiptRegisterId);
                    if (receipt == null)
                    {
                        return false;
                    }
                    
                    // 构建审核数据
                    var auditData = new AuditContractReceiptRegister_In
                    {
                        Id = receiptRegisterId,
                        ProtectionDeadline = receipt.ProtectionDeadline,
                        IsSecret = receipt.IsSecret,
                        CustomerType = receipt.CustomerType,
                        Remark = receipt.Remark,
                        AuditFeedback = "",
                        ReviewRemarks = "",
                        IsSettlementExchange = 2,
                        BelongingMonth = receipt.BelongingMonth,
                        GlobalSearchUnit = receipt.GlobalSearchUnit,
                        ReceiptRegisterCongratulations = new List<UpdateContractReceiptRegisterCongratulations>(),
                        State = 1 // 审核通过
                    };
                    
                    // 调用审核API
                    BLL_ContractReceiptRegister.Instance.AuditContractReceiptRegister(auditData);
                    return true;
                }
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        /// <summary>
        /// 确认业绩
        /// </summary>
        /// <param name="receiptRegisterId">业绩登记ID</param>
        /// <returns>是否成功</returns>
        public bool ConfirmReceipt(string receiptRegisterId)
        {
            try
            {
                // 获取UserTokenInfo，这需要在控制器层处理
                var userId = TokenModel.Instance.id;
                if (string.IsNullOrEmpty(userId))
                {
                    return false;
                }
                
                // 获取业绩审核ID
                var achievementId = DbOpe_crm_contract_receiptregister.Instance.GetAuditIdByRegisterId(userId, receiptRegisterId);
                if (achievementId == null)
                {
                    return false;
                }
                
                var achievementAuditData = new AuditContractReceiptRegisterAchievement_IN
                {
                    Id = achievementId, // 使用业绩ID而非登记ID
                    Remark = "",
                    AchievementState = (EnumAchievementState)2 // 审核通过
                };

                // 调用审核API
                BLL_ContractAchievement.Instance.AuditContractReceiptRegisterAchievement(achievementAuditData);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
} 