using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CRM2_API.Common.Cache
{
    public partial class RedisCache
    {
        /// <summary>
        /// 用户与组织机构信息表缓存
        /// </summary>
        public class UserWithOrg
        {
            const string USERWITHORG_ALL = "userwithorg_all";
            const string USERWITHORG_ID = "userwithorg_id_";
            const string USERWITHORG_ORG = "userwithorg_org_";

            /// <summary>
            /// 缓存的简化模型
            /// </summary>
            public class UserWithOrgSimple
            {
                /// <summary>
                /// 主键ID
                /// </summary>
                public string Id { get; set; }
                
                /// <summary>
                /// 账号
                /// </summary>
                public string UserName { get; set; }
                
                /// <summary>
                /// 员工编号
                /// </summary>
                public string UserNum { get; set; }
                
                /// <summary>
                /// 用户姓名
                /// </summary>
                public string Name { get; set; }
                
                /// <summary>
                /// 部门ID
                /// </summary>
                public string DepartmentId { get; set; }
                
                /// <summary>
                /// 所属组织ID
                /// </summary>
                public string OrganizationId { get; set; }
                
                /// <summary>
                /// 手机号
                /// </summary>
                public string Telephone { get; set; }
                
                /// <summary>
                /// 电子邮箱
                /// </summary>
                public string Email { get; set; }
                
                /// <summary>
                /// 用户类型：1-管理者;2-职工
                /// </summary>
                public int UserType { get; set; }
                
                /// <summary>
                /// 是否启用
                /// </summary>
                public bool UserStatus { get; set; }
                
                /// <summary>
                /// 完整组织名称
                /// </summary>
                public string OrgFullName { get; set; }
                
                /// <summary>
                /// 用户名称（完整组织名称）
                /// </summary>
                public string UserWithOrgFullName { get; set; }
                
                /// <summary>
                /// 组织机构类型
                /// </summary>
                public int? OrgType { get; set; }
            }

            /// <summary>
            /// 获取所有用户与组织机构信息
            /// </summary>
            /// <returns>用户列表，如果缓存不存在返回null</returns>
            public static List<UserWithOrgSimple> GetAllUsers()
            {
                if (RedisHelper.Exists(USERWITHORG_ALL))
                    return RedisHelper.Get<List<UserWithOrgSimple>>(USERWITHORG_ALL);
                return null;
            }

            /// <summary>
            /// 保存所有用户与组织机构信息到缓存
            /// </summary>
            /// <param name="users">用户列表</param>
            public static void SaveAllUsers(List<UserWithOrgSimple> users)
            {
                RedisHelper.Set(USERWITHORG_ALL, users, TimeSpan.FromHours(26));

                // 同时保存每个用户到单独的缓存中
                if (users != null && users.Count > 0)
                {
                    foreach (var user in users)
                    {
                        if (!string.IsNullOrEmpty(user.Id))
                        {
                            SaveUser(user);
                        }
                    }

                    // 按组织ID分组保存用户
                    var orgGroups = users.Where(u => u.UserStatus && !string.IsNullOrEmpty(u.OrganizationId))
                                        .GroupBy(u => u.OrganizationId);
                    foreach (var group in orgGroups)
                    {
                        SaveUsersByOrgId(group.Key, group.ToList());
                    }
                }
            }

            /// <summary>
            /// 根据用户ID获取用户信息
            /// </summary>
            /// <param name="id">用户ID</param>
            /// <returns>用户信息，如果缓存不存在返回null</returns>
            public static UserWithOrgSimple GetUserById(string id)
            {
                var key = USERWITHORG_ID + id;
                if (RedisHelper.Exists(key))
                    return RedisHelper.Get<UserWithOrgSimple>(key);
                
                // 当单独的ID缓存不存在时，尝试从全部缓存中获取
                var allUsers = GetAllUsers();
                if (allUsers != null && allUsers.Count > 0)
                {
                    var user = allUsers.FirstOrDefault(u => u.Id == id);
                    if (user != null)
                    {
                        // 找到后同时保存到单独缓存
                        SaveUser(user);
                        return user;
                    }
                }
                return null;
            }

            /// <summary>
            /// 保存单个用户与组织机构信息到缓存
            /// </summary>
            /// <param name="user">用户信息</param>
            public static void SaveUser(UserWithOrgSimple user)
            {
                var key = USERWITHORG_ID + user.Id;
                RedisHelper.Set(key, user, TimeSpan.FromHours(26));
            }

            /// <summary>
            /// 根据组织ID获取该组织下的所有用户
            /// </summary>
            /// <param name="orgId">组织ID</param>
            /// <returns>用户列表，如果缓存不存在返回null</returns>
            public static List<UserWithOrgSimple> GetUsersByOrgId(string orgId)
            {
                var key = USERWITHORG_ORG + orgId;
                if (RedisHelper.Exists(key))
                    return RedisHelper.Get<List<UserWithOrgSimple>>(key);
                
                // 如果没有针对特定组织的缓存，但有所有用户的缓存，则从所有用户中筛选
                var allUsers = GetAllUsers();
                if (allUsers != null)
                {
                    var users = allUsers.Where(u => u.OrganizationId == orgId && u.UserStatus).ToList();
                    SaveUsersByOrgId(orgId, users);
                    return users;
                }
                
                return null;
            }

            /// <summary>
            /// 保存特定组织下的用户列表到缓存
            /// </summary>
            /// <param name="orgId">组织ID</param>
            /// <param name="users">用户列表</param>
            public static void SaveUsersByOrgId(string orgId, List<UserWithOrgSimple> users)
            {
                var key = USERWITHORG_ORG + orgId;
                RedisHelper.Set(key, users, TimeSpan.FromHours(26));
            }

            /// <summary>
            /// 根据用户ID清除单个用户缓存
            /// </summary>
            /// <param name="id">用户ID</param>
            public static void ClearUserCache(string id)
            {
                var key = USERWITHORG_ID + id;
                RedisHelper.Del(key);
            }

            /// <summary>
            /// 根据组织ID清除该组织下所有用户的缓存
            /// </summary>
            /// <param name="orgId">组织ID</param>
            public static void ClearOrgUsersCache(string orgId)
            {
                var key = USERWITHORG_ORG + orgId;
                RedisHelper.Del(key);
            }

            /// <summary>
            /// 清除所有用户缓存
            /// </summary>
            public static void ClearAllCache()
            {
                RedisHelper.Del(USERWITHORG_ALL);
                var idKeys = RedisHelper.Keys(USERWITHORG_ID + "*");
                if (idKeys != null && idKeys.Length > 0)
                    RedisHelper.Del(idKeys);
                
                var orgKeys = RedisHelper.Keys(USERWITHORG_ORG + "*");
                if (orgKeys != null && orgKeys.Length > 0)
                    RedisHelper.Del(orgKeys);
            }
            /// <summary>
            /// 初始化用户与组织机构缓存
            /// </summary>
            public static void InitializeCache()
            {
                // 从数据库获取并更新缓存
                var dbUsers = DbOpe_v_userwithorg.Instance.GetDataList(r => r.Deleted == false);
                ClearAllCache();
                if (dbUsers != null && dbUsers.Count > 0)
                {
                    var users = dbUsers.Select(r => new UserWithOrg.UserWithOrgSimple
                    {
                        Id = r.Id,
                        UserName = r.UserName,
                        UserNum = r.UserNum,
                        Name = r.Name,
                        DepartmentId = r.DepartmentId,
                        OrganizationId = r.OrganizationId,
                        Telephone = r.Telephone,
                        Email = r.Email,
                        UserType = r.UserType,
                        UserStatus = r.UserStatus,
                        OrgFullName = r.OrgFullName,
                        UserWithOrgFullName = r.UserWithOrgFullName,
                        OrgType = r.OrgType
                    }).ToList();
                    // 保存到缓存
                    UserWithOrg.SaveAllUsers(users);
                }
            }

            /// <summary>
            /// 智能更新用户缓存（只更新受影响的用户和组织）
            /// </summary>
            /// <param name="userId">被修改的用户ID</param>
            public static void SmartUpdateUserCache(string userId)
            {
                try
                {
                    // 1. 获取被修改的用户信息
                    var updatedUser = DbOpe_v_userwithorg.Instance.GetData(r => r.Id == userId && r.Deleted == false);
                    if (updatedUser == null)
                    {
                        // 用户不存在，清除相关缓存
                        ClearUserCache(userId);
                        return;
                    }

                    // 2. 获取修改前的用户信息（从缓存中）
                    var oldUser = GetUserById(userId);
                    
                    // 3. 更新单个用户缓存
                    var userSimple = new UserWithOrgSimple
                    {
                        Id = updatedUser.Id,
                        UserName = updatedUser.UserName,
                        UserNum = updatedUser.UserNum,
                        Name = updatedUser.Name,
                        DepartmentId = updatedUser.DepartmentId,
                        OrganizationId = updatedUser.OrganizationId,
                        Telephone = updatedUser.Telephone,
                        Email = updatedUser.Email,
                        UserType = updatedUser.UserType,
                        UserStatus = updatedUser.UserStatus,
                        OrgFullName = updatedUser.OrgFullName,
                        UserWithOrgFullName = updatedUser.UserWithOrgFullName,
                        OrgType = updatedUser.OrgType
                    };
                    SaveUser(userSimple);

                    // 4. 更新所有用户缓存（如果存在）
                    var allUsers = GetAllUsers();
                    if (allUsers != null)
                    {
                        var existingUserIndex = allUsers.FindIndex(u => u.Id == userId);
                        if (existingUserIndex >= 0)
                        {
                            allUsers[existingUserIndex] = userSimple;
                        }
                        else
                        {
                            allUsers.Add(userSimple);
                        }
                        SaveAllUsers(allUsers);
                    }

                    // 5. 如果用户所属组织发生变化，更新相关组织缓存
                    if (oldUser != null && oldUser.OrganizationId != updatedUser.OrganizationId)
                    {
                        // 清除旧组织的缓存
                        if (!string.IsNullOrEmpty(oldUser.OrganizationId))
                        {
                            ClearOrgUsersCache(oldUser.OrganizationId);
                        }
                        
                        // 清除新组织的缓存
                        if (!string.IsNullOrEmpty(updatedUser.OrganizationId))
                        {
                            ClearOrgUsersCache(updatedUser.OrganizationId);
                        }
                    }
                    else if (!string.IsNullOrEmpty(updatedUser.OrganizationId))
                    {
                        // 组织没有变化，但需要更新当前组织的缓存
                        ClearOrgUsersCache(updatedUser.OrganizationId);
                    }
                }
                catch (Exception ex)
                {
                    // 如果智能更新失败，回退到全量更新
                    LogUtil.AddErrorLog($"智能更新用户缓存失败，回退到全量更新: {ex.Message}");
                    InitializeCache();
                }
            }

            /// <summary>
            /// 批量智能更新用户缓存
            /// </summary>
            /// <param name="userIds">被修改的用户ID列表</param>
            public static void SmartUpdateUserCacheBatch(List<string> userIds)
            {
                if (userIds == null || userIds.Count == 0)
                    return;

                try
                {
                    // 如果修改的用户数量超过总用户的10%，则使用全量更新
                    var allUsers = GetAllUsers();
                    if (allUsers != null && userIds.Count > allUsers.Count * 0.1)
                    {
                        InitializeCache();
                        return;
                    }

                    // 逐个更新用户缓存
                    foreach (var userId in userIds)
                    {
                        SmartUpdateUserCache(userId);
                    }
                }
                catch (Exception ex)
                {
                    // 如果批量更新失败，回退到全量更新
                    LogUtil.AddErrorLog($"批量智能更新用户缓存失败，回退到全量更新: {ex.Message}");
                    InitializeCache();
                }
            }
        }
    }
} 