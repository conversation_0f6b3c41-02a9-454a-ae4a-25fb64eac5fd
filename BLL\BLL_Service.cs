﻿using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BusinessModel.BM_GtisOpe;
using CRM2_API.Services;
using DocumentFormat.OpenXml.Wordprocessing;
using ServiceTaskScheduler;
using ServiceTaskScheduler.Core;
using ServiceTaskScheduler.Models;
using System.Threading.Tasks;
using static CRM2_API.Common.Cache.RedisCache;

namespace CRM2_API.BLL
{
    public class BLL_Service : BaseBLL<BLL_Service>, ITaskResultCallback
    {

        private TaskService _taskService;
        /// <summary>
        /// 懒加载方式获取TaskService
        /// </summary>
        protected TaskService TaskService => _taskService ??= GetService<TaskService>();
        private static bool _isCallbackRegistered = false;
        private static readonly object _lockObj = new object();

        public BLL_Service()
        {
            lock (_lockObj)
            {
                if (!_isCallbackRegistered)
                {
                    TaskService.RegisterCallback(this);
                    _isCallbackRegistered = true;

                }
            }
        }
        /// <summary>
        /// 创建账号开通任务
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="scheduledTime">计划执行时间</param>
        /// <param name="priority">优先级</param>
        /// <param name="otherParams">其他参数</param>
        /// <returns>任务ID</returns>
        public async Task<string> CreateAccountOpeningTaskAsync(
            string username,
            string password,
            DateTime? scheduledTime = null,
            int priority = 5,
            string otherParams = null)
        {
            // 创建任务步骤
            var steps = new List<TaskStep>
            {
                // 步骤1: 系统A开通账号
                TaskService.CreateStep<WebSystemAAccountHandler, WebSystemAParams>(
                    "开通系统A账号",
                    "调用系统A接口开通账号",
                    new WebSystemAParams
                    {
                        Username = username,
                        Password = password,
                        OtherParams = otherParams
                    }
                ),

                // 步骤2: 系统B开通账号
                TaskService.CreateStep<WebSystemBAccountHandler, WebSystemBParams>(
                    "开通系统B账号",
                    "调用系统B接口开通账号",
                    new WebSystemBParams
                    {
                        Username = username,
                        Password = password,
                        OtherParams = otherParams
                    }
                ),

                // 步骤3: 绑定账号关系
                TaskService.CreateStep<WebBindAccountHandler, WebBindAccountParams>(
                    "绑定账号关系",
                    "绑定系统A和系统B的账号关系",
                    new WebBindAccountParams
                    {
                        OtherParams = otherParams
                    }
                )
            };

            // 创建多步骤任务
            var taskId = await TaskService.CreateMultiStepTaskAsync(
                "账号开通任务",
                $"为用户 {username} 开通系统A和系统B账号并绑定关系",
                scheduledTime ?? DateTime.Now,
                steps,
                priority,
                new { Username = username, OtherParams = otherParams }
            );

            return taskId;
        }

        public async Task<string> CreateSingleGtisServiceTaskAsync(string ApplId,
            DateTime? dataForStart,
            BM_GtisOpe_RenewalContact bM_GtisOpe_RenewalContact,
            List<Db_crm_contract_serviceinfo_gtis_user> addUser,
            List<Db_crm_contract_serviceinfo_gtis_user> updateUser,
            List<Db_crm_contract_serviceinfo_gtis_user> delUser
            )
        {
            // 创建任务步骤
            var steps = new List<TaskStep>
            {
                // 步骤1: 调用GTIS接口开通账号
                TaskService.CreateStep<GtisRenewalHandler, BM_GtisOpe_RenewalContact>(
                    "调用GTIS接口开通账号",
                "调用GTIS接口开通账号",
                 bM_GtisOpe_RenewalContact
                ),

                // 步骤2: 更新新增GTIS账号
                TaskService.CreateStep<GtisAddUserHandler, List<Db_crm_contract_serviceinfo_gtis_user>>(
                    "更新新增GTIS账号",

                "更新新增GTIS账号",
                addUser
                ),

                // 步骤3: 更新当前GTIS账号
                TaskService.CreateStep<GtisUpdateUserHandler, List<Db_crm_contract_serviceinfo_gtis_user>>(
                    "更新当前GTIS账号",
                    "更新当前GTIS账号",
                    updateUser
                ),
                // 步骤4: 删除失效GTIS账号
                TaskService.CreateStep<GtisDelUserHandler, List<Db_crm_contract_serviceinfo_gtis_user>>(
                    "更新当前GTIS账号",
                    "更新当前GTIS账号",
                    delUser
                ),

            };

            // 创建多步骤任务
            var taskId = await TaskService.CreateMultiStepTaskAsync(
                "GTIS开通任务",
                $"GTIS开通任务",
            dataForStart ?? DateTime.Now,
            steps,
                5,
                new {  }
            );

            return taskId;
        }


        public async Task<string> DoGtisOpenMissionTaskAysnc(DateTime? dataForStart, List<TaskStep> steps)
        {
            // 创建多步骤任务
            var taskId = await TaskService.CreateMultiStepTaskAsync(
                "GTIS开通任务",
                $"自建GTIS开通顺次操作",
            dataForStart ?? DateTime.Now,
            steps,
                5,
                new { }
            );

            return taskId;
        }


        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务信息</returns>
        public async Task<TaskInfo> GetTaskAsync(string taskId)
        {
            return await TaskService.GetTaskAsync(taskId);
        }

        /// <summary>
        /// 获取所有任务
        /// </summary>
        /// <returns>任务列表</returns>
        public async Task<IEnumerable<TaskInfo>> GetAllTasksAsync()
        {
            return await TaskService.GetAllTasksAsync();
        }

        public void OnTaskCompleted(TaskInfo task)
        {
            throw new NotImplementedException();
        }

        public void OnTaskFailed(TaskInfo task, string errorMessage)
        {
            throw new NotImplementedException();
        }
    }
}