﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("sys_organization_params")]
    public class Db_sys_organization_params
    {
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string Id {get;set;}

           /// <summary>
           /// Desc:组织ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string OrgId {get;set;}
        /// <summary>
        /// Desc:组织ID
        /// Default:
        /// Nullable:False
        /// </summary>           
        public int OrgType { get; set; }

        /// <summary>
        /// Desc:参数类型
        /// Default:
        /// Nullable:False
        /// </summary>           
        public string ParamsType {get;set;}

           /// <summary>
           /// Desc:参数key
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ParamsKey {get;set;}

           /// <summary>
           /// Desc:参数值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string ParmasValue {get;set;}

           /// <summary>
           /// Desc:启用时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? StartDate {get;set;}

           /// <summary>
           /// Desc:停用时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? EndDate {get;set;}

           /// <summary>
           /// Desc:删除标识:0-未删除;1-已删除
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool Deleted {get;set;}

           /// <summary>
           /// Desc:创建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string CreateUser {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime CreateDate {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string UpdateUser {get;set;}

           /// <summary>
           /// Desc:修改时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? UpdateDate {get;set;}

    }
}
