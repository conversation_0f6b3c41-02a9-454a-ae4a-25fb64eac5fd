using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BLLModel.InvoiceSystem;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.Enum;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static CRM2_API.Model.BLLModel.InvoiceSystem.VM_InvoiceSystem;
using static CRM2_API.Model.ControllersViewModel.VM_InvoiceSystem;

namespace CRM2_API.BLL
{
    /// <summary>
    /// 测试数据 BLL 类 - 补充发票相关测试数据
    /// </summary>
    public partial class BLL_TestData
    {
        /// <summary>
        /// 测试补充发票识别和提交流程
        /// </summary>
        /// <returns>补充发票测试结果</returns>
        public CompleteTestDataResult TestSupplementInvoice()
        {
            string tempFilePath = null;
            
            try
            {
                // 1. 获取测试用的发票PDF文件字节数组
                byte[] invoicePdfBytes = GetTestInvoicePdfBytes();
                if (invoicePdfBytes == null || invoicePdfBytes.Length == 0)
                {
                    throw new ApiException("获取测试发票文件失败");
                }


                    // 4. 调用发票OCR识别接口
                    var recognizeResult = BLL_ContractInvoiceNew.Instance.UploadAndRecognizeSupplementInvoice(invoicePdfBytes).Result;
                    if (recognizeResult == null || string.IsNullOrEmpty(recognizeResult.RecognitionId))
                    {
                        throw new ApiException("补充发票OCR识别失败");
                    }

                    // 5. 准备提交补充发票请求
                    var submitRequest = new SubmitSupplementInvoice_In
                    {
                        RecognitionId = recognizeResult.RecognitionId,
                        InvoiceType = (int)EnumInvoiceType.SpecialTicket, // 使用增值税专用发票类型
                        BillingCompany = recognizeResult.SellerName,
                        BillingHeader = recognizeResult.BuyerName,
                        CreditCode = recognizeResult.BuyerTaxCode,
                        InvoiceDate = DateTime.Parse(recognizeResult.InvoiceDate),
                        InvoiceDetails = recognizeResult.InvoiceDetails,
                        Recipient = "张三",
                        Email = "<EMAIL>",
                        Remark = "补充发票测试"
                    };

                    // 6. 调用提交补充发票接口
                    var submitResult = BLL_ContractInvoiceNew.Instance.SubmitSupplementInvoice(submitRequest).Result;
                    if (submitResult == null || string.IsNullOrEmpty(submitResult.ApplicationId))
                    {
                        throw new ApiException("补充发票提交失败");
                    }

                    // 7. 返回成功结果
                    return new CompleteTestDataResult
                    {
                        InvoiceApplicationId = submitResult.ApplicationId,
                        SuccessMessage = "补充发票测试成功，完成了OCR识别和提交流程",
                        InvoiceAmount = recognizeResult.TotalAmount
                    };
            }
            catch (Exception ex)
            {
                // 记录异常信息，方便调试
                Console.WriteLine($"补充发票测试失败: {ex.Message}");
                return null;
            }
            finally
            {
                // 清理临时文件
                try
                {
                    if (!string.IsNullOrEmpty(tempFilePath) && File.Exists(tempFilePath))
                    {
                        File.Delete(tempFilePath);
                    }
                }
                catch { }
            }
        }
       
        /// <summary>
        /// 测试补充发票复核流程
        /// </summary>
        /// <param name="invoiceApplicationId">指定要复核的发票申请ID，如果为空则自动查找一个待复核的补充发票申请</param>
        /// <returns>补充发票复核测试结果</returns>
        public CompleteTestDataResult TestReviewSupplementInvoice(string invoiceApplicationId = null)
        {
            try
            {
                // 1. 获取待复核的补充发票申请
                Db_crm_invoice_application invoiceApplication = null;
                
                if (!string.IsNullOrEmpty(invoiceApplicationId))
                {
                    // 如果提供了指定的申请ID，则直接查询
                    invoiceApplication = DbOpe_crm_invoice_application.Instance.QueryByPrimaryKey(invoiceApplicationId);
                    
                    if (invoiceApplication == null)
                    {
                        throw new ApiException($"未找到指定的发票申请记录: {invoiceApplicationId}");
                    }
                    
                    // 验证是否为补充发票类型
                    if (invoiceApplication.BillingType != (int)EnumBillingType.SupplementInvoice)
                    {
                        throw new ApiException("指定的发票申请不是补充发票类型");
                    }
                    
                    // 验证状态是否为待复核
                    if (invoiceApplication.AuditStatus != (int)EnumInvoiceApplicationStatus.Processed)
                    {
                        throw new ApiException("指定的发票申请不是待复核状态");
                    }
                }
                else
                {
                    // 如果没有提供申请ID，则自动查找最新的待复核补充发票申请
                    // 修改查询条件，只获取通过测试接口创建的补充发票申请（通过Remark字段识别）
                    invoiceApplication = DbOpe_crm_invoice_application.Instance.GetDataList(
                        a => a.BillingType == (int)EnumBillingType.SupplementInvoice &&
                             a.AuditStatus == (int)EnumInvoiceApplicationStatus.Processed &&
                             a.Remark == "补充发票测试" && // 只获取通过测试接口创建的申请
                             a.Deleted != true)
                        .OrderByDescending(a => a.CreateDate)
                        .FirstOrDefault();
                    
                    if (invoiceApplication == null)
                    {
                        throw new ApiException("未找到通过测试接口创建的待复核补充发票申请记录");
                    }
                }
                
                // 2. 获取最新的审核记录
                var review = DbOpe_crm_invoice_review.Instance.GetDataList(
                    r => r.InvoiceApplicationId == invoiceApplication.Id && 
                         r.Deleted != true)
                    .OrderByDescending(r => r.CreateDate)
                    .FirstOrDefault();
                
                if (review == null)
                {
                    throw new ApiException($"未找到发票申请对应的审核记录: {invoiceApplication.Id}");
                }
                
                // 检查审核记录是否已设置InvoiceType，如果没有则更新
                if (review.InvoiceType == null || review.InvoiceType == 0)
                {
                    // 从申请记录中获取发票类型信息
                    int invoiceType = invoiceApplication.InvoiceType;
                    
                    // 更新审核记录中的发票类型
                    review.InvoiceType = invoiceType;
                    review.UpdateUser = invoiceApplication.CreateUser; // 使用申请人作为更新人
                    review.UpdateDate = DateTime.Now;
                    
                    // 保存更新
                    DbOpe_crm_invoice_review.Instance.Update(review);
                    
                    Console.WriteLine($"已更新审核记录发票类型: {invoiceApplication.Id}, 类型: {invoiceType}");
                }
                
                // 3. 准备复核请求参数
                var reviewRequest = new ReviewInvoiceApplicationRequest
                {
                    Id = invoiceApplication.Id,
                    ReviewResult = 1, // 默认通过
                    ReviewComments = "自动化测试复核通过"
                };
                
                // 4. 调用复核接口
                bool reviewResult = BLL_ContractInvoiceNew.Instance.ReviewInvoiceApplication(reviewRequest);
                if (!reviewResult)
                {
                    throw new ApiException("补充发票复核失败");
                }
                
                // 5. 再次查询发票申请，确认状态已更新
                var updatedApplication = DbOpe_crm_invoice_application.Instance.QueryByPrimaryKey(invoiceApplication.Id);
                if (updatedApplication == null || updatedApplication.AuditStatus != (int)EnumInvoiceApplicationStatus.Completed)
                {
                    throw new ApiException("补充发票复核状态更新失败");
                }
                
                // 查询创建的发票记录
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                    i.InvoiceApplicationId == invoiceApplication.Id && 
                    i.Deleted != true);
                
                // 6. 返回成功结果
                var result = new CompleteTestDataResult
                {
                    InvoiceApplicationId = invoiceApplication.Id,
                    SuccessMessage = "补充发票复核测试成功，发票已完成复核流程",
                    InvoiceAmount = invoiceApplication.AppliedAmount
                };
                
                // 设置发票ID
                if (invoice != null)
                {
                    result.InvoiceId = invoice.Id;
                }
                
                return result;
            }
            catch (Exception ex)
            {
                // 记录异常信息，方便调试
                Console.WriteLine($"补充发票复核测试失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 测试补充发票完整流程（从上传到复核）
        /// </summary>
        /// <returns>补充发票完整流程测试结果</returns>
        public CompleteTestDataResult TestSupplementInvoiceCompleteProcess()
        {
            try
            {
                // 1. 先创建补充发票申请
                var submitResult = TestSupplementInvoice();
                if (submitResult == null || string.IsNullOrEmpty(submitResult.InvoiceApplicationId))
                {
                    throw new ApiException("补充发票申请创建失败");
                }
                
                Console.WriteLine($"补充发票申请创建成功，ID: {submitResult.InvoiceApplicationId}");
                
                // 2. 使用创建的申请ID进行复核
                var reviewResult = TestReviewSupplementInvoice(submitResult.InvoiceApplicationId);
                if (reviewResult == null)
                {
                    throw new ApiException("补充发票复核失败");
                }
                
                // 3. 返回完整流程结果
                var result = new CompleteTestDataResult
                {
                    InvoiceApplicationId = submitResult.InvoiceApplicationId,
                    SuccessMessage = "补充发票完整流程测试成功（申请和复核）",
                    InvoiceAmount = submitResult.InvoiceAmount
                };
                
                // 设置发票ID
                if (reviewResult.InvoiceId != null)
                {
                    result.InvoiceId = reviewResult.InvoiceId;
                }
                
                return result;
            }
            catch (Exception ex)
            {
                // 记录异常信息，方便调试
                Console.WriteLine($"补充发票完整流程测试失败: {ex.Message}");
                return null;
            }
        }

        #region 补充发票退票测试方法
        
        /// <summary>
        /// 测试补充发票退票申请流程
        /// </summary>
        /// <param name="invoiceId">指定要退票的发票ID，如果为空则自动查找一个已复核完成的补充发票</param>
        /// <returns>补充发票退票测试结果</returns>
        public CompleteTestDataResult TestSupplementInvoiceRefund(string invoiceId = null)
        {
            try
            {
                // 1. 查找或创建一个已完成复核的补充发票
                Db_crm_invoice invoice = null;
                
                if (!string.IsNullOrEmpty(invoiceId))
                {
                    // 如果提供了指定的发票ID，则直接查询
                    invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                        i.Id == invoiceId && 
                        i.IsSupplementInvoice == true && 
                        i.Deleted != true);
                    
                    if (invoice == null)
                    {
                        throw new ApiException($"未找到指定的补充发票: {invoiceId}");
                    }
                    
                    // 验证发票状态
                    if (invoice.RefundStatus != (int)EnumInvoiceRefundStatus.Normal)
                    {
                        throw new ApiException("指定的补充发票已有退票状态，不能再次退票");
                    }
                }
                else
                {
                    // 如果没有提供发票ID，则自动查找或创建一个补充发票
                    // 先查找是否有通过测试创建的补充发票
                    invoice = DbOpe_crm_invoice.Instance.GetDataList(
                        i => i.IsSupplementInvoice == true && 
                             i.RefundStatus == (int)EnumInvoiceRefundStatus.Normal && 
                             i.Deleted != true)
                        .OrderByDescending(i => i.CreateDate)
                        .FirstOrDefault();
                    
                    if (invoice == null)
                    {
                        // 如果没有找到合适的补充发票，则先创建一个
                        var createInvoiceResult = TestSupplementInvoiceCompleteProcess();
                        if (createInvoiceResult == null || string.IsNullOrEmpty(createInvoiceResult.InvoiceApplicationId))
                        {
                            throw new ApiException("创建测试用补充发票失败");
                        }
                        
                        // 根据申请ID查询发票
                        var invoiceApplication = DbOpe_crm_invoice_application.Instance.QueryByPrimaryKey(createInvoiceResult.InvoiceApplicationId);
                        if (invoiceApplication == null)
                        {
                            throw new ApiException("未找到创建的补充发票申请记录");
                        }
                        
                        // 查找对应的发票记录
                        invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                            i.InvoiceApplicationId == createInvoiceResult.InvoiceApplicationId && 
                            i.Deleted != true);
                            
                        if (invoice == null)
                        {
                            throw new ApiException("未找到创建的补充发票记录");
                        }
                        
                        Console.WriteLine($"已创建测试用补充发票，ID: {invoice.Id}");
                    }
                }
                
                // 2. 获取测试用的发票退票PDF文件字节数组
                byte[] refundPdfBytes = GetTestInvoicePdfBytes(); // 使用相同方法获取测试PDF
                if (refundPdfBytes == null || refundPdfBytes.Length == 0)
                {
                    throw new ApiException("获取测试退票文件失败");
                }
                
                // 3. 调用补充发票退票OCR识别接口
                var recognizeResult = BLL_ContractInvoiceNew.Instance.UploadAndRecognizeSupplementInvoiceRefund(refundPdfBytes, invoice.Id).Result;
                if (recognizeResult == null || string.IsNullOrEmpty(recognizeResult.RecognitionId))
                {
                    throw new ApiException("补充发票退票OCR识别失败");
                }
                
                // 4. 准备提交补充发票退票请求
                var submitRequest = new SubmitSupplementInvoiceRefund_In
                {
                    RecognitionId = recognizeResult.RecognitionId,
                    OriginalInvoiceId = invoice.Id,
                    RefundReason = "补充发票退票测试"
                };
                
                // 5. 调用提交补充发票退票接口
                var submitResult = BLL_ContractInvoiceNew.Instance.SubmitSupplementInvoiceRefund(submitRequest);
                if (submitResult == null || string.IsNullOrEmpty(submitResult.RefundId))
                {
                    throw new ApiException("补充发票退票申请提交失败");
                }
                
                // 6. 返回成功结果
                var result = new CompleteTestDataResult
                {
                    SuccessMessage = "补充发票退票测试成功，完成了OCR识别和退票申请提交流程",
                    InvoiceAmount = invoice.InvoicedAmount
                };
                
                // 设置附加属性
                result.InvoiceId = invoice.Id;
                result.RefundApplicationId = submitResult.RefundId;
                
                return result;
            }
            catch (Exception ex)
            {
                // 记录异常信息，方便调试
                Console.WriteLine($"补充发票退票测试失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 测试补充发票退票复核流程
        /// </summary>
        /// <param name="refundId">指定要复核的退票申请ID，如果为空则自动查找一个待复核的退票申请</param>
        /// <returns>补充发票退票复核测试结果</returns>
        public CompleteTestDataResult TestSupplementInvoiceRefundReview(string refundId = null)
        {
            try
            {
                // 1. 查找待复核的退票申请
                Db_crm_invoice_refund_application refundApplication = null;
                
                if (!string.IsNullOrEmpty(refundId))
                {
                    // 如果提供了指定的退票申请ID，则直接查询
                    refundApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => 
                        r.Id == refundId && 
                        r.Deleted != true);
                        
                    if (refundApplication == null)
                    {
                        throw new ApiException($"未找到指定的退票申请: {refundId}");
                    }
                    
                    // 验证状态是否为待复核
                    if (refundApplication.AuditStatus != (int)EnumRefundApplicationStatus.Processed)
                    {
                        throw new ApiException("指定的退票申请不是待复核状态");
                    }
                    
                    // 验证是否为补充发票的退票申请
                    var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                        i.Id == refundApplication.InvoiceId && 
                        i.Deleted != true);
                        
                    if (invoice == null || !invoice.IsSupplementInvoice)
                    {
                        throw new ApiException("指定的退票申请不是补充发票的退票申请");
                    }
                }
                else
                {
                    // 如果没有提供申请ID，则自动查找最新的待复核退票申请
                    // 先获取所有补充发票
                    var supplementInvoiceIds = DbOpe_crm_invoice.Instance.GetDataList(
                        i => i.IsSupplementInvoice == true && 
                             i.Deleted != true)
                        .Select(i => i.Id)
                        .ToList();
                        
                    if (supplementInvoiceIds.Count == 0)
                    {
                        throw new ApiException("系统中没有补充发票记录");
                    }
                    
                    // 查找这些补充发票的待复核退票申请
                    refundApplication = DbOpe_crm_invoice_refund_application.Instance.GetDataList(
                        r => supplementInvoiceIds.Contains(r.InvoiceId) && 
                             r.AuditStatus == (int)EnumRefundApplicationStatus.Processed && 
                             r.RefundReason == "补充发票退票测试" && // 只获取通过测试接口创建的申请
                             r.Deleted != true)
                        .OrderByDescending(r => r.CreateDate)
                        .FirstOrDefault();
                        
                    if (refundApplication == null)
                    {
                        // 如果没有找到待复核的退票申请，则先创建一个
                        var createRefundResult = TestSupplementInvoiceRefund();
                        if (createRefundResult == null || string.IsNullOrEmpty(createRefundResult.RefundApplicationId))
                        {
                            throw new ApiException("创建测试用补充发票退票申请失败");
                        }
                        
                        // 查询创建的退票申请
                        refundApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => 
                            r.Id == createRefundResult.RefundApplicationId && 
                            r.Deleted != true);
                            
                        if (refundApplication == null)
                        {
                            throw new ApiException("未找到创建的退票申请记录");
                        }
                        
                        Console.WriteLine($"已创建测试用补充发票退票申请，ID: {refundApplication.Id}");
                    }
                }
                
                // 2. 获取退票审核记录
                var review = DbOpe_crm_invoice_refund_review.Instance.GetDataList(
                    r => r.RefundApplicationId == refundApplication.Id && 
                         r.Deleted != true)
                    .OrderByDescending(r => r.CreateDate)
                    .FirstOrDefault();
                    
                if (review == null)
                {
                    throw new ApiException($"未找到退票申请对应的审核记录: {refundApplication.Id}");
                }
                
                // 3. 准备复核请求参数
                var reviewRequest = new ReviewInvoiceRefundConfirmationRequest
                {
                    RefundApplicationId = refundApplication.Id, // 使用正确的属性名
                    ReviewResult = true, // 使用布尔类型
                    ReviewComment = "自动化测试退票复核通过" // 使用正确的属性名
                };
                
                // 4. 调用退票复核接口
                bool reviewResult = BLL_ContractInvoiceNew.Instance.ConfirmInvoiceRefundReview(reviewRequest);
                if (!reviewResult)
                {
                    throw new ApiException("补充发票退票复核失败");
                }
                
                // 5. 再次查询退票申请，确认状态已更新
                var updatedApplication = DbOpe_crm_invoice_refund_application.Instance.GetData(r => 
                    r.Id == refundApplication.Id && 
                    r.Deleted != true);
                    
                if (updatedApplication == null || updatedApplication.AuditStatus != (int)EnumRefundApplicationStatus.Completed)
                {
                    throw new ApiException("补充发票退票复核状态更新失败");
                }
                
                // 6. 返回成功结果
                var result = new CompleteTestDataResult
                {
                    SuccessMessage = "补充发票退票复核测试成功，退票已完成复核流程",
                    InvoiceAmount = refundApplication.RefundAmount
                };
                
                // 设置附加属性
                result.InvoiceId = refundApplication.InvoiceId;
                result.RefundApplicationId = refundApplication.Id;
                
                return result;
            }
            catch (Exception ex)
            {
                // 记录异常信息，方便调试
                Console.WriteLine($"补充发票退票复核测试失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 测试补充发票退票完整流程（从创建补充发票到退票复核）
        /// </summary>
        /// <returns>补充发票退票完整流程测试结果</returns>
        public CompleteTestDataResult TestSupplementInvoiceRefundCompleteProcess()
        {
            try
            {
                // 1. 先确保有一个已完成的补充发票
                var createResult = TestSupplementInvoiceCompleteProcess();
                if (createResult == null || string.IsNullOrEmpty(createResult.InvoiceApplicationId))
                {
                    throw new ApiException("创建测试用补充发票失败");
                }
                
                // 查询创建的补充发票记录
                var invoiceApplication = DbOpe_crm_invoice_application.Instance.GetData(a => 
                    a.Id == createResult.InvoiceApplicationId && 
                    a.Deleted != true);
                    
                if (invoiceApplication == null)
                {
                    throw new ApiException("未找到创建的补充发票申请记录");
                }
                
                var invoice = DbOpe_crm_invoice.Instance.GetData(i => 
                    i.InvoiceApplicationId == createResult.InvoiceApplicationId && 
                    i.Deleted != true);
                    
                if (invoice == null)
                {
                    throw new ApiException("未找到创建的补充发票记录");
                }
                
                Console.WriteLine($"已创建测试用补充发票，ID: {invoice.Id}");
                
                // 2. 提交退票申请
                var refundResult = TestSupplementInvoiceRefund(invoice.Id);
                if (refundResult == null || string.IsNullOrEmpty(refundResult.RefundApplicationId))
                {
                    throw new ApiException("补充发票退票申请创建失败");
                }
                
                Console.WriteLine($"补充发票退票申请创建成功，ID: {refundResult.RefundApplicationId}");
                
                // 3. 进行退票复核
                var reviewResult = TestSupplementInvoiceRefundReview(refundResult.RefundApplicationId);
                if (reviewResult == null)
                {
                    throw new ApiException("补充发票退票复核失败");
                }
                
                // 4. 返回完整流程结果
                var result = new CompleteTestDataResult
                {
                    SuccessMessage = "补充发票退票完整流程测试成功（从创建到退票复核）",
                    InvoiceAmount = invoice.InvoicedAmount
                };
                
                // 设置附加属性
                result.InvoiceId = invoice.Id;
                result.InvoiceApplicationId = createResult.InvoiceApplicationId;
                result.RefundApplicationId = refundResult.RefundApplicationId;
                
                return result;
            }
            catch (Exception ex)
            {
                // 记录异常信息，方便调试
                Console.WriteLine($"补充发票退票完整流程测试失败: {ex.Message}");
                return null;
            }
        }
        
        #endregion
    }
} 