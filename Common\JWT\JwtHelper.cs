﻿using CRM2_API.Common.AppSetting;
using CRM2_API.Model.System;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace CRM2_API.Common.JWT
{
    /// <summary>
    /// jwt帮助类
    /// </summary>
    public class JwtHelper
    {
        /// <summary>
        /// 创建jwt的token
        /// </summary>
        /// <param name="model">jwt中的模型</param>
        /// <returns></returns>
        public static string CreateToken<TokenModel>(TokenModel model)
        {
            //将传入的model加密
            var claims = new Claim[]
            {
                new Claim(JwtRegisteredClaimNames.Jti,EncryptUtil.EncryptDES(model.SerializeNewtonJson(),AppSettings.Jwt.SecretKey[..8]))//这个位置，直接使用秘钥的前8位，所以秘钥长度必须要大于8
            };
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(AppSettings.Jwt.SecretKey));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            //jwt使用的是utc时间
            var dateTime = DateTime.UtcNow;
            //过期时间
            DateTime expires = dateTime.AddHours(AppSettings.Jwt.ExpressionHours);
            //DateTime expires = dateTime.AddMinutes(AppSettings.Jwt.ExpressionHours);
            var jwt = new JwtSecurityToken(
            claims: claims, //声明集合
                notBefore: dateTime,
                expires: expires,
                signingCredentials: creds);

            var jwtHandler = new JwtSecurityTokenHandler();
            return jwtHandler.WriteToken(jwt);
        }
        /// <summary>
        /// 验证并解析token，返回token中的模型
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public static TokenModel GetTokenModel(string token)
        {
            var jwtHandler = new JwtSecurityTokenHandler();
            JwtSecurityToken jwtToken = jwtHandler.ReadJwtToken(token);
            //验证签名
            jwtHandler.ValidateToken(token,
                new TokenValidationParameters
                {
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(AppSettings.Jwt.SecretKey)),
                    RequireSignedTokens = true,
                    SaveSigninToken = false,
                    ValidateActor = false,
                    // 将下面两个参数设置为false，可以不验证Issuer和Audience，但是不建议这样做。
                    ValidateAudience = false,
                    ValidateIssuer = false,
                    ValidateIssuerSigningKey = true,
                    // 是否要求Token的Claims中必须包含 Expires
                    RequireExpirationTime = true,
                    // 允许的服务器时间偏移量
                    ClockSkew = TimeSpan.Zero,
                    // 是否验证Token有效期，使用当前时间与Token的Claims中的NotBefore和Expires对比 2023.12.23改为不验证token有效期
                    ValidateLifetime = false
                }, out var valToken);
            return EncryptUtil.DecryptDES(jwtToken.Id, AppSettings.Jwt.SecretKey[..8]).DeserializeNewtonJson<TokenModel>();
        }
    }
}
