﻿using CRM2_API.BLL.Common;
using CRM2_API.Common;
using CRM2_API.Common.Filter;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.InkML;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.IdentityModel.Tokens;
using System.Net;
using System.Net.Sockets;

namespace CRM2_API.Common.Filter
{
    public class AllowIPAttribute : IAuthorizationFilter
    {
        //private readonly string[] _allowedIPs;

        //public AllowIPAttribute(params string[] allowedIPs)
        //{
        //    _allowedIPs = allowedIPs;
        //}
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            if (context.Filters.Any(f => f is SkipIPCheckAttribute))
                return;
            var ip = "";
            if (!CheckIp(context, ref ip))
            {
                //context.Result = new ForbidResult();
                //context.HttpContext.Response.WriteAsync("当前IP禁止访问.");
                throw new ApiException("当前IP禁止访问:(" + ip + ")");
                return;
            }
        }


        /// <summary>
        /// 验证角色-ip-接口权限
        /// </summary>
        /// <param name="context"></param>
        /// <param name="ip"></param>
        /// <returns></returns>
        public bool CheckIp(AuthorizationFilterContext context, ref string ip)
        {
            //获取formid
            string formId = "";
            var isSkipAuthCheck = context.Filters.Any(f => f is SkipAuthCheckAttribute);
            var isSkipRightCheck = context.Filters.Any(f => f is SkipRightCheckAttribute);
            if (isSkipAuthCheck)
            {
                //获取接口的ControllerName
                var controllerName = context.ActionDescriptor.DisplayName.Split('.')[2].ToString().Replace("Controller", "");
                //获取接口的ActionName
                var methodName = context.ActionDescriptor.DisplayName.Split('.')[3].Replace(" (CRM2_API)", "");
                if (controllerName != "Example")
                {
                    formId = DbOpe_sys_form.Instance.GetSysForm(controllerName, methodName).Id;
                }
            }
            else if (isSkipRightCheck)
            {
                //这种找不到formid
            }
            else
            {
                //对应formId
                formId = Com_SysForm.Instance.FormId;
            }
            //if (string.IsNullOrEmpty(formId))
            //{
            //    return false;
            //}

            //这里formId为空也验证，是因为有通用接口规则，不需要formId（通用规则里formId是00000000-0000-0000-0000-000000000000，表示所有接口）
            var userId = TokenModel.Instance.id;
            //ip = IPOperator.GetIp(context);
            ip = NetUtil.GetIp_Nginx(context.HttpContext);

            return DbOpe_sys_form_id.Instance.CheckRoleIpAuth(ip, userId, formId);
        }

    }
    /// <summary>
    /// 跳过权限验证特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class SkipIPCheckAttribute : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {

        }
    }
}