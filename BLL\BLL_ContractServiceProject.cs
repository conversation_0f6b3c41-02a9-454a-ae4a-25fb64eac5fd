﻿using CRM2_API.BLL.Common;
using CRM2_API.Common.Cache;
using CRM2_API.Common.JWT;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.System;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using SqlSugar;
using System.Diagnostics.Contracts;
using System.IO;
using System.Security.Policy;
using System.Threading.Tasks;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_ContractInvoice;

namespace CRM2_API.BLL
{
    public class BLL_ContractServiceProject : BaseBLL<BLL_ContractServiceProject>
    {
        /// <summary>
        /// 作废合同服务信息期刊邮寄信息
        /// </summary>
        /// <param name="applyId"></param>
        /// <exception cref="ApiException"></exception>
        public void VoidContractProductServiceProjectInfo(string applyId)
        {
            //获取申请数据 
            var apply = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.QueryByPrimaryKey(applyId);
            //申请为拒绝状态的数据不可以作废
            if (apply.State == EnumProcessStatus.Void.ToInt())
                throw new ApiException("所选的申请已为作废状态");
            //申请的状态改为作废
            apply.State = EnumProcessStatus.Void.ToInt();
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_project_appl.Instance.UpdateQueue(apply);
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_project_appl.Instance.SaveQueues();
        }

        /// <summary>
        /// 删除合同服务信息期刊邮寄信息
        /// </summary>
        /// <param name="applyId"></param>
        public void DeleteContractProductServiceProjectInfo(string applyId)
        {
            //获取申请数据 
            var apply = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.QueryByPrimaryKey(applyId);
            //申请待开通状态的数据不可以删除
            if (apply.State != EnumProcessStatus.Void.ToInt())
                throw new ApiException("所选的申请不可删除");
            //删除申请
            apply.Deleted = true;
            apply.UpdateUser = UserId;
            apply.UpdateDate = DateTime.Now;
            DbOpe_crm_contract_productserviceinfo_project_appl.Instance.UpdateQueue(apply);
            //提交sql队列
            DbOpe_crm_contract_productserviceinfo_project_appl.Instance.SaveQueues();
        }

        /// <summary>
        /// 登记合同服务信息期刊邮寄的快递信息
        /// </summary>
        /// <param name="register_In"></param>
        public void RegisterContractProductServiceInfoMailing(RegisterContractProductServiceInfoMailing_In register_In)
        {
            //DbOpe_crm_contract_productserviceinfo_project_appl.Instance.RegisterContractProductServiceAppl(register_In.ProductServiceInfoProjectApplId, UserId);
            //获取已经寄出的期数
            var mailedPeriod = DbOpe_crm_contract_serviceinfo_mailing.Instance.GetMailedCountByApplyId(register_In.ProductServiceInfoProjectApplId);
            //本次邮寄的期数
            var curMailPeriod = register_In.MailedPeriodsNum.Split("/").ToList().First().ToInt();
            //获取本服务的应寄总期数
            var totalPeriods = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.GetTotalPeriodNum(register_In.ProductServiceInfoProjectApplId);
            if (curMailPeriod > totalPeriods)
                throw new ApiException("邮寄期数达到上限，不可邮寄");
            else if (curMailPeriod <= mailedPeriod)
                throw new ApiException("本期已经寄出，不可重复邮寄");


            var mailingInfo = register_In.MappingTo<Db_crm_contract_serviceinfo_mailing>();
            mailingInfo.MailedPeriod = mailingInfo.MailedPeriodsNum.Split("/").ToList().First().ToInt();
            var mailinginfoId = DbOpe_crm_contract_serviceinfo_mailing.Instance.InsertDataQueueReturnId(mailingInfo);
            //上传期刊邮寄快递附件
            if (register_In.ExpressVoucherAttachment != null && register_In.ExpressVoucherAttachment.Count > 0)
            {
                Util<DbOpe_crm_contract_serviceinfo_mailing_attachfile, BM_AttachFile> tempAttachFile = new Util<DbOpe_crm_contract_serviceinfo_mailing_attachfile, BM_AttachFile>(DbOpe_crm_contract_serviceinfo_mailing_attachfile.Instance);
                tempAttachFile.UploadFile(register_In.ExpressVoucherAttachment, EnumAttachFileType.MailingFiles.ToString(), UserId, mailinginfoId);
            }
            DbOpe_crm_contract_serviceinfo_mailing.Instance.SaveQueues();
        }

        /// <summary>
        /// 修改合同服务期刊邮寄申请信息
        /// </summary>
        /// <param name="update_In"></param>
        public void UpdateContractProductServiceProjectInfo(UpdateContractProductServiceProjectInfo_In update_In)
        {
            var cur_appl = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.QueryByPrimaryKey(update_In.Id);
            var upd_appl = update_In.MappingTo<Db_crm_contract_productserviceinfo_project_appl>();
            update_In.MappingTo(cur_appl);
            DbOpe_crm_contract_productserviceinfo_project_appl.Instance.UpdateData(cur_appl);
            var upd_appl_his = update_In.MappingTo<Db_crm_contract_productserviceinfo_project_appl_history>();
            upd_appl_his.ContractProductServiceInfoProjectApplId = update_In.Id;
            upd_appl_his.Id = Guid.NewGuid().ToString();
            DbOpe_crm_contract_productserviceinfo_project_appl_history.Instance.InsertData(upd_appl_his);

            DbOpe_crm_contract.Instance.RefreshContractProtectDate(cur_appl.ContractId);
        }

        /// <summary>
        /// 根据申请Id批量修改服务用户状态
        /// </summary>
        /// <param name="applyList_In"></param>
        public void SetContractServiceInfoGlobalSearchUserByApplyIds(SetContractServiceInfoCollegeContactsByApplyIds_In applyList_In)
        {
            var applyIdList = applyList_In.Ids.Split(',').ToList();
            var serviceIds = DbOpe_crm_contract_serviceinfo_college.Instance.GetServiceInfoIdByApplyIds(applyIdList);
            DbOpe_crm_contract_serviceinfo_college_contacts.Instance.SetContractServiceInfoCollegeContactsByApplyId(serviceIds, applyList_In.State, UserId);
        }

        /// <summary>
        /// 根据合同Id和审核日期获取发刊信息列表
        /// </summary>
        /// <param name="contractProductServiceinfoProjectListIn"></param>
        /// <returns></returns>
        public ApiTableOut<ContractProductServiceinfoProjectList_Out> GetContractProductServiceinfoProjectListByContractId(ContractProductServiceinfoProjectList_In contractProductServiceinfoProjectListIn)
        {
            //240912 合同、发票、到账权限更改
            Db_crm_contract result = DbOpe_crm_contract.Instance.GetContractById(contractProductServiceinfoProjectListIn.ContractId,true);
            if (result == null)
            {
                throw new ApiException("该用户没有数据权限");
            }
            int total = 0;
            return new ApiTableOut<ContractProductServiceinfoProjectList_Out> { Data = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.GetContractProductServiceinfoProjectListByContractId(contractProductServiceinfoProjectListIn, ref total), Total = total };
        }

        /// <summary>
        /// 根据客户Id和审核日期获取发刊信息列表
        /// </summary>
        /// <param name="contractProductServiceinfoProjectListCustomerIdIn"></param>
        /// <returns></returns>
        public ApiTableOut<ContractProductServiceinfoProjectList_Out> GetContractProductServiceinfoProjectListByCustomerId(ContractProductServiceinfoProjectList_CustomerId_In contractProductServiceinfoProjectListCustomerIdIn)
        {
            PrivateCustomerSimpleInfo privatepool = null;
            bool IsPrivate = DbOpe_crm_customer_privatepool.Instance.CheckInPool(contractProductServiceinfoProjectListCustomerIdIn.CustomerId, ref privatepool, UserId, false, false);
            bool IsPublic = DbOpe_crm_customer_publicpool.Instance.CheckInPool(contractProductServiceinfoProjectListCustomerIdIn.CustomerId,true, UserId, false, false);
            if (!IsPrivate && !IsPublic)
            {
                throw new ApiException("该用户没有数据权限");
            }
            int total = 0;
            return new ApiTableOut<ContractProductServiceinfoProjectList_Out> { Data = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.GetContractProductServiceinfoProjectListByCustomerId(contractProductServiceinfoProjectListCustomerIdIn, ref total), Total = total };
        }

        public IImporter Importer = new ExcelImporter();
        /// <summary>
        /// 下载期刊邮寄批量导入模板
        /// </summary>
        /// <returns></returns>
        public string DownloadMailingExcelTemplate()
        {
            var filePath = Path.Combine(Directory.GetCurrentDirectory(), "MailingTemplate.xlsx");
            if (File.Exists(filePath))
                File.Delete(filePath);
            Importer.GenerateTemplate<MailingExcelDto>(filePath);
            return filePath;
        }


        /// <summary>
        /// 上传期刊邮寄批量导入模板
        /// </summary>
        /// <param name="uploadMailingTemplate_In"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public List<AnalysisMailingTemplate_Out> UploadMailingTemplate(UploadMailingTemplate_In uploadMailingTemplate_In)
        {
            var subfix = uploadMailingTemplate_In.TemplateFile.FileName.Split('.').ToList().Last().ToString();
            if (!"xlsx".Equals(subfix) && !"xls".Equals(subfix))
                throw new ApiException("上传文件格式不正确");
            //解析excel内容
            var import = Importer.Import<MailingExcelDto>(uploadMailingTemplate_In.TemplateFile.OpenReadStream()).Result;
            //验证是否缺少数据，合同号、合同名称、快递名称、快递单号必填
            if (import.HasError)
                throw new ApiException("上传的excel文件中数据不全，请重新核对后上传");
            var mailingList = import.Data.ToList().MappingTo<List<AnalysisMailingTemplate_Out>>();
            /*
                //验证上传的内容是否存在相同合同号
                var sameContractNo = mailingList.GroupBy(e => e.ContractNo).Where(e => e.Count() > 1).Select(e => e.Key).ToList();
                if (sameContractNo.Count != 0 && !uploadMailingTemplate_In.isContinue)
                    throw new ApiException("上传的文件中存在相同的合同号，是否继续上传");
                //验证上传的快递单号是否重复
                if (mailingList.GroupBy(e => e.ExpressNumber).Any(e => e.Count() > 1))
                    throw new ApiException("上传的文件中存在相同的快递号，请重新核对后上传");
            */
            //获取重复的快递单号列表
            var expressNumList = mailingList.Select(e => e.ExpressNumber).ToList();
            expressNumList.AddRange(uploadMailingTemplate_In.UploadedInfo.Select(e => e.ExpressNumber).ToList());
            var sameExpressNumList = expressNumList.GroupBy(e => e).Where(e => e.Count() > 1).Select(e => e.Key).ToList();

            //记录已经写入的excel数据合同号
            //var insertContractNoList = new List<string>();
            var insertContractNoList = uploadMailingTemplate_In.UploadedInfo;
            var retList = new List<AnalysisMailingTemplate_Out>();
            mailingList.ForEach(mailingInfo =>
            {
                //根据合同号、合同名称获取期刊邮寄的申请信息
                var obj = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.GetMailedInfo(mailingInfo);
                //若申请信息不为空且快递单号不重复，填充期数数据
                if (obj != null && !sameExpressNumList.Contains(mailingInfo.ExpressNumber))
                {
                    //获取应该增加的期数
                    var addNum = insertContractNoList.Where(e => e.ContractNo.Equals(mailingInfo.ContractNo)).Sum(e => e.CurrentMailPeriodsNum) + mailingInfo.CurrentMailPeriodsNum;
                    //获取已经寄出的期数
                    var period = DbOpe_crm_contract_serviceinfo_mailing.Instance.GetMailedCountByApplyId(obj.Id);
                    //验证寄出期数总数不能超过服务的总期数
                    if ((period + addNum) <= obj.PeriodsNum)
                    {
                        //标记成功
                        mailingInfo.Existed = true;
                        //填充当前期数，当前期数/总期数
                        mailingInfo.MailedPeriodsNum = (period + addNum).ToString() + "/" + obj.PeriodsNum;
                        //填充基本信息Id
                        mailingInfo.ContractId = obj.ContractId;
                        mailingInfo.ProductId = obj.ProductId;
                        mailingInfo.ContractProductInfoId = obj.ContractProductInfoId;
                        mailingInfo.ProductServiceInfoProjectApplId = obj.Id;
                        //写入记录的合同号
                        insertContractNoList.Add(new UploadMailingTemplate_In_Sub { ContractNo = mailingInfo.ContractNo, CurrentMailPeriodsNum = mailingInfo.CurrentMailPeriodsNum });
                    }

                }
                retList.Add(mailingInfo);
            });
            return retList;
        }

        /// <summary>
        /// 批量登记合同服务信息期刊邮寄的快递信息
        /// </summary>
        /// <param name="registerList_In"></param>
        public void BatchRegisterContractProductServiceInfoMailing(List<BatchRegisterContractProductServiceInfoMailing_In> registerList_In)
        {

            if (registerList_In.Count == 0 || registerList_In == null)
                throw new ApiException("未导入批量邮寄信息");
            if(!registerList_In.Any(e => e.Existed))
                throw new ApiException("请导入正确的批量邮寄信息");
            var filterList = registerList_In.Where(e => e.Existed).ToList();
            //循环整理期刊邮寄信息
            registerList_In.Where(e => e.Existed).ToList().ForEach(regist =>
            {
                //获取已经寄出的期数
                var mailedPeriod = DbOpe_crm_contract_serviceinfo_mailing.Instance.GetMailedCountByApplyId(regist.ProductServiceInfoProjectApplId);
                //本次邮寄的最大期数记数
                var curLastMailPeriod = regist.MailedPeriodsNum.Split("/").ToList().First().ToInt();
                //获取本次寄出的数量
                var curMailCount = regist.CurrentMailPeriodsNum;
                //获取本服务的应寄总期数
                var totalPeriods = DbOpe_crm_contract_productserviceinfo_project_appl.Instance.GetTotalPeriodNum(regist.ProductServiceInfoProjectApplId);
                if ((curMailCount + mailedPeriod) > totalPeriods)
                    throw new ApiException("邮寄期数达到上限，不可邮寄");
                else if (curLastMailPeriod <= mailedPeriod)
                    throw new ApiException("本期已经寄出，不可重复邮寄");
                //根据当前寄出期数 循环插入数据
                for (var i = regist.CurrentMailPeriodsNum - 1; i >= 0; i--)
                {
                    var mailingInfo = regist.MappingTo<Db_crm_contract_serviceinfo_mailing>();
                    var periodNumElements = mailingInfo.MailedPeriodsNum.Split('/').ToList();
                    mailingInfo.MailedPeriod = periodNumElements[0].ToInt() - i;
                    mailingInfo.MailedPeriodsNum = mailingInfo.MailedPeriod.ToString() + "/" + periodNumElements[1].ToString();
                    mailingInfo.Id = Guid.NewGuid().ToString();
                    mailingInfo.CreateUser = UserId;
                    mailingInfo.CreateDate = DateTime.Now;
                    mailingInfo.Deleted = false;
                    DbOpe_crm_contract_serviceinfo_mailing.Instance.InsertQueue(mailingInfo);
                    //DbOpe_crm_contract_productserviceinfo_project_appl.Instance.RegisterContractProductServiceAppl(regist.ProductServiceInfoProjectApplId, UserId);
                }
            });
            //提交sql
            DbOpe_crm_contract_serviceinfo_mailing.Instance.SaveQueues();
        }

    }
}


