﻿
using Microsoft.AspNetCore.Mvc.Abstractions;

namespace CRM2_API.BLL.Common
{
    public class Com_RequestBody
    {
        private static readonly AsyncLocal<ActionDescriptor> _actDescriptor = new();

        public static ActionDescriptor GetActionDescriptor
        {
            get { return _actDescriptor.Value ?? new(); }
        }

        public static void SetActionDescriptor(ActionDescriptor actDescriptor)
        {
            _actDescriptor.Value = actDescriptor;
        }
    }
}
