﻿using System;
using System.Linq;
using System.Text;
using CRM2_API.Model.BLLModel.Enum;
using SqlSugar;

namespace CRM2_API.DAL.DbModel.Crm2
{
    ///<summary>
    ///晋升表(字典表方式)
    ///</summary>
    [SugarTable("sys_promotion")]
    public class Db_sys_promotion : IComparable<Db_sys_promotion>
    {
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>           
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:True
        /// </summary>           
        public EnumPromotionType? Type { get; set; }

        /// <summary>
        /// Desc:职级描述主键[开始]
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string PromotionStartKey { get; set; }

        /// <summary>
        /// Desc:职级描述主键[结束]
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string PromotionEndKey { get; set; }

        /// <summary>
        /// Desc:晋升条件类型1
        /// Default:
        /// Nullable:True
        /// </summary>           
        public EnumPromotionConditionType? PromotionConditionType1 { get; set; }

        /// <summary>
        /// Desc:晋升值1
        /// Default:
        /// Nullable:True
        /// </summary>           
        public decimal? PromotionConditionValue1 { get; set; }

        /// <summary>
        /// Desc:晋升条件类型2
        /// Default:
        /// Nullable:True
        /// </summary>           
        public EnumPromotionConditionType? PromotionConditionType2 { get; set; }

        /// <summary>
        /// Desc:晋升值2
        /// Default:
        /// Nullable:True
        /// </summary>           
        public decimal? PromotionConditionValue2 { get; set; }

        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Remark { get; set; }

        /// <summary>
        /// Desc:继承的id，当前记录生效会使父记录失效
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string Inherit { get; set; }

        /// <summary>
        /// Desc:等待生效,为1时则为等待生效.0或NULL则无意义
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? WaitEffec { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:True
        /// </summary>           
        public bool? Deleted { get; set; }

        /// <summary>
        /// Desc:排序码
        /// Default:
        /// Nullable:True
        /// </summary>           
        public int? SortNum { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string CreateUser { get; set; }

        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// Desc:修改人
        /// Default:
        /// Nullable:True
        /// </summary>           
        public string UpdateUser { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// Desc:生效时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? TakingEffectTime { get; set; }

        /// <summary>
        /// Desc:失效时间
        /// Default:
        /// Nullable:True
        /// </summary>           
        public DateTime? ExpirationTime { get; set; }

        public override bool Equals(object? obj)
        {
            return obj is Db_sys_promotion promotion &&
                   Id == promotion.Id;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Id);
        }

        public int CompareTo(Db_sys_promotion? other)
        {
            if (ReferenceEquals(this, other))
            {
                return 0;
            }

            if (ReferenceEquals(null, other))
            {
                return 1;
            }

            return Nullable.Compare(SortNum, other.SortNum);
        }
    }
}
