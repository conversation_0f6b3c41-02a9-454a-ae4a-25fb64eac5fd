﻿using CRM2_API.BLL.Common;
using Microsoft.AspNetCore.Mvc;
using Namotion.Reflection;
using NJsonSchema;
using NJsonSchema.CodeGeneration.TypeScript;
using System.ComponentModel;
using System.IO;
using System.IO.Compression;
using System.Reflection;
using System.Text.RegularExpressions;

namespace CRM2_API.BLL.BLL_Example
{
    /// <summary>
    /// 生成typescript模型
    /// </summary>
    public class BLL_GenerateModelToTS : BaseBLL<BLL_GenerateModelToTS>
    {
        /// <summary>
        /// 生成模型文件夹名称
        /// </summary>
        private readonly string BaseDir = AppDomain.CurrentDomain.BaseDirectory + "GenerateTs/";
        /// <summary>
        /// 本次生成的guid
        /// </summary>
        private string Id { get; set; }
        /// <summary>
        /// 本次文件放置的文件夹
        /// </summary>
        private string FileDir { get; set; }
        /// <summary>
        /// 根据Controller生成TS模型
        /// </summary>
        /// <param name="ControllerType"></param>
        /// <returns></returns>
        public Stream GenerateController(Type ControllerType)
        {
            var methods = ControllerType.GetMethods();
            var actions = methods.FindAll(m => m.GetCustomAttribute<HttpPostAttribute>() != null || m.GetCustomAttribute<HttpGetAttribute>() != null).ToList();
            Id = Guid.NewGuid().ToString();
            FileDir = BaseDir + Id + "/";
            //生成本次临时文件夹
            Directory.CreateDirectory(FileDir);
            //循环所有方法，挨个生成文件
            actions.ForEach(a =>
            {
                GenerateMethodToFile(a);
            });
            return GetFileStream();
        }
        /// <summary>
        /// 根据方法，生成TS模型
        /// </summary>
        /// <param name="methodInfo"></param>
        /// <returns></returns>
        public Stream GenerateMethod(MethodInfo methodInfo)
        {
            Id = Guid.NewGuid().ToString();
            FileDir = BaseDir + Id + "/";
            //生成本次临时文件夹
            Directory.CreateDirectory(FileDir);
            GenerateMethodToFile(methodInfo);
            return GetFileStream();
        }

        /// <summary>
        /// 将方法的模型，生成到文件夹中
        /// </summary>
        /// <param name="method"></param>
        private void GenerateMethodToFile(MethodInfo method)
        {
            var pIn = method.GetParameters();
            if (pIn.HaveContent())//方法参数
            {
                pIn.ForEach(p =>
                {
                    GenerateFile(p.ParameterType);
                });
            }
            var retType = method.ReturnType;//方法返回值
            GenerateFile(retType);
        }
        /// <summary>
        /// 所有运行类型
        /// </summary>
        private static List<Type> allType = Assembly.GetExecutingAssembly().ExportedTypes.Where(t => t.IsEnum).ToList();
        /// <summary>
        /// 生成类型的所有TS文件
        /// </summary>
        /// <param name="t"></param>
        private void GenerateFile(Type t)
        {
            if (!t.IsClass || t.Name == "String")
                return;

            //数组需要特殊解析，否则生成ts，会造成模型解析错误
            if (t.GetInterface(typeof(IEnumerable<>).FullName) != null)
            {
                if (t.IsArray)//普通数组 例如 T[]
                    GenerateFile(t.GetElementType());
                else//泛型数组  List<T>
                    GenerateFile(t.GenericTypeArguments[0]);
                return;
            }
            //生成jsonschema
            var schema = JsonSchema.FromType(t);
            var setting = new TypeScriptGeneratorSettings
            {
                TypeStyle = TypeScriptTypeStyle.Interface,
                MarkOptionalProperties = true//标记是否为空
            };
            //生成typescript
            TypeScriptGenerator g = new TypeScriptGenerator(schema, setting);
            string ts = g.GenerateFile();

            //将生成的ts分解为一个一个的模块，生成结果只有enum和interface
            //匹配 带注释 和 不带注释 的 enum interface
            //                                 /*.....*/\n export enum ...\n}    \n export enum ...\n}     /*.....*/\n export interface ...\n}   \n export interface ...\n}
            var matches = Regex.Matches(ts, @"(/\*.+?(\*/\n)(export enum|export interface) .+?\n})|((\n)(export enum|export interface) .+?\n})", RegexOptions.Singleline);
            if (matches.HaveContent())
            {
                var listAllDefName = schema.Definitions.Keys.ToList();
                listAllDefName.Add(schema.Title);
                foreach (Match match in matches)
                {
                    if (match.Index == 1051)
                    { 
                    }
                    //是否是枚举，后面会添加注释
                    bool isEnum = false;
                    //找到模型名称
                    string name = "";
                    if (match.Value.RegexIsMatch("export enum .+? "))
                    {
                        name = Regex.Match(match.Value, @"export enum .+? ").Value.Replace("export enum", "").Trim();
                        isEnum = true;
                    }
                    else
                        name = Regex.Match(match.Value, @"export interface .+? ").Value.Replace("export interface", "").Trim();
                    //文件名
                    string fileName = name + ".ts";
                    //不存在再添加
                    if (!File.Exists(FileDir + fileName))
                    {
                        StringBuilder sb = new StringBuilder();
                        //添加import
                        listAllDefName.ForEach(def =>
                        {
                            if (def == name)//非自己
                                return;
                            //匹配到继承或属性，增加import
                            if (match.Value.RegexIsMatch($"(extends {def} )|( {def};)|( {def}\\[];)|(: {def} )"))
                                sb.AppendLine($"import {{ {def} }} from './{def}'");
                        });
                        //有import，增加换行
                        if (sb.Length > 0)
                            sb.AppendLine("");
                        //将缩进由4个空格，改为2个空格
                        string modelValue = match.Value.Replace("    ", "  ");
                        //若是枚举，添加枚举的注释
                        if (isEnum)
                        {
                            List<string> modelRows = modelValue.Split("\n").ToList();
                            var enumType = allType.Find(t => t.Name == name);
                            var allEnumFields = enumType.GetFields();
                            foreach (var field in allEnumFields)
                            {
                                string fieldName = field.Name[0].ToString().ToUpper() + field.Name[1..field.Name.Length];
                                var index = modelRows.FindIndex(r => r.Contains(" "+fieldName+" ="));
                                if (index >= 0)
                                {
                                    string desc = GetEnumSummaryOrDesc(enumType, field);
                                    if (desc.IsNotNullOrEmpty())
                                        modelRows.Insert(index, $"  /** {desc} */");
                                }
                            }
                            modelValue = modelRows.JoinToString("\n");
                        }
                        //最后添加当前模型
                        sb.AppendLine(modelValue);
                        File.WriteAllText(FileDir + fileName, sb.ToString());
                    }
                }
            }
        }
        private string GetEnumSummaryOrDesc(Type t, FieldInfo fInfo)
        {
            string desc = fInfo.GetXmlDocsSummary();
            if (desc.IsNullOrEmpty())
            {
                var attrDesc = fInfo.GetCustomAttribute<DescriptionAttribute>();
                if (attrDesc != null)
                    desc = attrDesc.Description;
            }
            return desc;
        }
        /// <summary>
        /// 获取生成的模型，压缩成zip，并删除生成的所有内容
        /// </summary>
        /// <returns></returns>
        private Stream GetFileStream()
        {
            string zipPath = BaseDir + Id + ".zip";
            ZipFile.CreateFromDirectory(FileDir, zipPath);
            //读取压缩文件
            MemoryStream mStream = new MemoryStream(File.ReadAllBytes(zipPath));
            //删除已生成的文件
            Directory.Delete(FileDir, true);
            File.Delete(zipPath);
            return mStream;
        }
    }
}
