﻿using CRM2_API.BLL;
using CRM2_API.BLL.RemindInfo;
using CRM2_API.Common.DingTalk;
using CRM2_API.Common.Filter;
using CRM2_API.Common.JWT;
using CRM2_API.Controllers.Base;
using CRM2_API.DAL.DbModelOpe.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;
using CRM2_API.Model.ControllersViewModel.Common;
using CRM2_API.Model.ControllersViewModel.Notice;
using CRM2_API.Model.System;
using DocumentFormat.OpenXml.Spreadsheet;
using JiebaNet.Segmenter.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NPOI.HPSF;
using System.ComponentModel;
using System.Diagnostics.Contracts;
using System.IO;
using System.Web;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.BLLModel.Enum.MessageCenterEnumOption;
using static CRM2_API.Model.ControllersViewModel.VM_BusinessMessage;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using static CRM2_API.Model.ControllersViewModel.VM_Home;
using static CRM2_API.Model.ControllersViewModel.VM_MessageCenter;
using static CRM2_API.Model.ControllersViewModel.VM_Messages;
using static CRM2_API.Model.ControllersViewModel.VM_WorkflowPending;
using CRM2_API.Common.Cache;
using CRM2_API.Services.PreInspection;

namespace CRM2_API.Controllers
{
    [Description("首页管理")]
    public class HomeController : MyControllerBase
    {
        public HomeController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }

        /// <summary>
        /// 健康检查API，用于监控服务是否正常运行
        /// </summary>
        /// <returns>服务状态信息</returns>
        [HttpGet]
        [SkipRightCheck]
        [SkipRecordLog]
        [SkipIPCheck]
        [SkipAuthCheck]
        [SkipRSAKey]
        public IActionResult HealthCheck()
        {
            try
            {
                // 检查数据库连接（可选）
                // bool dbConnectionOk = CheckDatabaseConnection();

                // 检查Redis连接（可选）
                // bool redisConnectionOk = CheckRedisConnection();

                // 返回健康状态
                return new JsonResult(new
                {
                    status = "healthy",
                    timestamp = DateTime.Now,
                    version = "3.0", // 可以从程序集获取版本
                    // database = dbConnectionOk ? "connected" : "disconnected",
                    // redis = redisConnectionOk ? "connected" : "disconnected"
                });
            }
            catch (Exception ex)
            {
                // 返回不健康状态
                Response.StatusCode = 500;
                return new JsonResult(new
                {
                    status = "unhealthy",
                    timestamp = DateTime.Now,
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 手动初始化所有Redis缓存
        /// </summary>
        /// <returns>初始化结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        [SkipIPCheck]
        public JsonResult RefreshRedisCache()
        {
            try
            {
                // 执行缓存初始化
                RedisCache.CacheInit.InitAllCache();
                // 返回成功结果
                return new JsonResult(new { success = true, message = "Redis缓存初始化成功" });
            }
            catch (Exception ex)
            {
                // 记录错误日志
                LogUtil.AddLog("手动刷新Redis缓存失败", ex);

                // 返回失败结果
                return new JsonResult(new { success = false, message = $"Redis缓存初始化失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 手动触发缓存监控
        /// </summary>
        /// <returns>监控结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        [SkipIPCheck]
        public JsonResult TriggerCacheMonitor()
        {
            try
            {
                // 执行缓存监控
                RedisCache.CacheMonitor.MonitorAllCaches();
                // 返回成功结果
                return new JsonResult(new { success = true, message = "缓存监控执行成功" });
            }
            catch (Exception ex)
            {
                // 记录错误日志
                LogUtil.AddLog("手动触发缓存监控失败", ex);

                // 返回失败结果
                return new JsonResult(new { success = false, message = $"缓存监控执行失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 获取缓存监控详细结果
        /// </summary>
        /// <returns>详细的监控结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        [SkipIPCheck]
        public JsonResult GetCacheMonitorDetails()
        {
            try
            {
                var monitorResults = new List<object>();
                
                // 检查用户与组织缓存
                var userWithOrgResult = CheckCacheStatus("UserWithOrg", "userwithorg_all", () => 
                {
                    var users = RedisCache.UserWithOrg.GetAllUsers();
                    return users?.Count ?? 0;
                });
                monitorResults.Add(userWithOrgResult);
                
                // 检查收款公司缓存
                var collectingCompanyResult = CheckCacheStatus("CollectingCompany", "collectingcompany_all", () => 
                {
                    var companies = RedisCache.CollectingCompany.GetAllCollectingCompanies();
                    return companies?.Count ?? 0;
                });
                monitorResults.Add(collectingCompanyResult);
                
                // 检查付款公司缓存
                var payingCompanyResult = CheckCacheStatus("PayingCompany", "payingcompany_all", () => 
                {
                    var companies = RedisCache.PayingCompany.GetAllPayingCompanies();
                    return companies?.Count ?? 0;
                });
                monitorResults.Add(payingCompanyResult);
                
                // 检查排除词缓存
                var exceptWordsResult = CheckCacheStatus("ExceptWords", "exceptwords_all", () => 
                {
                    var words = RedisCache.ExceptWords.GetAllExceptWords();
                    return words?.Count ?? 0;
                });
                monitorResults.Add(exceptWordsResult);
                
                // 检查黑名单用户缓存
                var blacklistUsersResult = CheckCacheStatus("BlacklistUsers", "blacklist_users_all", () => 
                {
                    var users = RedisCache.BlacklistUsers.GetAllBlacklistUsers();
                    return users?.Count ?? 0;
                });
                monitorResults.Add(blacklistUsersResult);
                
                return new JsonResult(new { 
                    success = true, 
                    message = "缓存监控详情获取成功",
                    results = monitorResults
                });
            }
            catch (Exception ex)
            {
                LogUtil.AddLog("获取缓存监控详情失败", ex);
                return new JsonResult(new { success = false, message = $"获取缓存监控详情失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 手动查看Redis缓存具体情况
        /// </summary>
        /// <param name="cacheType">缓存类型（可选）：UserWithOrg, CollectingCompany, PayingCompany, ExceptWords, BlacklistUsers</param>
        /// <returns>Redis缓存的详细情况</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        [SkipIPCheck]
        public JsonResult GetRedisCacheDetails(string cacheType = "")
        {
            try
            {
                var cacheDetails = new List<object>();
                
                // 定义所有缓存类型及其对应的键
                var cacheTypes = new Dictionary<string, string>
                {
                    { "UserWithOrg", "userwithorg_all" },
                    { "CollectingCompany", "collectingcompany_all" },
                    { "PayingCompany", "payingcompany_all" },
                    { "ExceptWords", "exceptwords_all" },
                    { "BlacklistUsers", "blacklist_users_all" }
                };
                
                // 如果指定了缓存类型，只检查该类型
                if (!string.IsNullOrEmpty(cacheType) && cacheTypes.ContainsKey(cacheType))
                {
                    var detail = GetSingleCacheDetail(cacheType, cacheTypes[cacheType]);
                    cacheDetails.Add(detail);
                }
                else
                {
                    // 检查所有缓存类型
                    foreach (var kvp in cacheTypes)
                    {
                        var detail = GetSingleCacheDetail(kvp.Key, kvp.Value);
                        cacheDetails.Add(detail);
                    }
                }
                
                return new JsonResult(new { 
                    success = true, 
                    message = "Redis缓存详情获取成功",
                    cacheDetails = cacheDetails
                });
            }
            catch (Exception ex)
            {
                LogUtil.AddLog("获取Redis缓存详情失败", ex);
                return new JsonResult(new { success = false, message = $"获取Redis缓存详情失败：{ex.Message}" });
            }
        }
        
        /// <summary>
        /// 获取单个缓存的详细信息
        /// </summary>
        /// <param name="cacheType">缓存类型</param>
        /// <param name="cacheKey">缓存键</param>
        /// <returns>缓存详细信息</returns>
        private object GetSingleCacheDetail(string cacheType, string cacheKey)
        {
            try
            {
                bool keyExists = RedisHelper.Exists(cacheKey);
                object cacheData = null;
                string dataType = "Unknown";
                int dataLength = 0;
                string ttl = "Unknown";
                string status = "Unknown";
                string message = "";
                
                if (!keyExists)
                {
                    status = "Missing";
                    message = "缓存键不存在";
                }
                else
                {
                    try
                    {
                        // 获取TTL（剩余生存时间）
                        var ttlSeconds = RedisHelper.Ttl(cacheKey);
                        ttl = ttlSeconds == -1 ? "永不过期" : $"{ttlSeconds}秒";
                        
                        // 获取缓存数据类型
                        var dataTypeResult = RedisHelper.Type(cacheKey);
                        dataType = dataTypeResult.ToString() ?? "Unknown";
                        
                        // 根据缓存类型获取具体数据
                        switch (cacheType)
                        {
                            case "UserWithOrg":
                                var users = RedisCache.UserWithOrg.GetAllUsers();
                                cacheData = users?.Take(5).ToList(); // 只取前5条作为示例
                                dataLength = users?.Count ?? 0;
                                break;
                                
                            case "CollectingCompany":
                                var collectingCompanies = RedisCache.CollectingCompany.GetAllCollectingCompanies();
                                cacheData = collectingCompanies?.Take(5).ToList();
                                dataLength = collectingCompanies?.Count ?? 0;
                                break;
                                
                            case "PayingCompany":
                                var payingCompanies = RedisCache.PayingCompany.GetAllPayingCompanies();
                                cacheData = payingCompanies?.Take(5).ToList();
                                dataLength = payingCompanies?.Count ?? 0;
                                break;
                                
                            case "ExceptWords":
                                var exceptWords = RedisCache.ExceptWords.GetAllExceptWords();
                                cacheData = exceptWords?.Take(10).ToList(); // 排除词可能很多，取前10条
                                dataLength = exceptWords?.Count ?? 0;
                                break;
                                
                            case "BlacklistUsers":
                                var blacklistUsers = RedisCache.BlacklistUsers.GetAllBlacklistUsers();
                                cacheData = blacklistUsers?.Take(5).ToList();
                                dataLength = blacklistUsers?.Count ?? 0;
                                break;
                        }
                        
                        if (dataLength == 0)
                        {
                            status = "Empty";
                            message = "缓存数据为空";
                        }
                        else
                        {
                            status = "Healthy";
                            message = $"缓存正常，共 {dataLength} 条记录";
                        }
                    }
                    catch (Exception ex)
                    {
                        status = "Error";
                        message = $"获取缓存数据时发生错误: {ex.Message}";
                        dataType = "Error";
                    }
                }
                
                return new
                {
                    CacheType = cacheType,
                    CacheKey = cacheKey,
                    Status = status,
                    Message = message,
                    KeyExists = keyExists,
                    DataType = dataType,
                    DataLength = dataLength,
                    TTL = ttl,
                    SampleData = cacheData, // 示例数据（前几条记录）
                    CheckTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    CacheType = cacheType,
                    CacheKey = cacheKey,
                    Status = "Error",
                    Message = $"检查缓存详情时发生错误: {ex.Message}",
                    KeyExists = false,
                    DataType = "Error",
                    DataLength = 0,
                    TTL = "Unknown",
                    SampleData = (object)null,
                    CheckTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
        }

        /// <summary>
        /// 测试时间计算逻辑
        /// </summary>
        /// <param name="reportDate">报告日期</param>
        /// <param name="reportType">报告类型：1-日报，2-周报，3-月报</param>
        /// <returns>时间计算结果</returns>
        [HttpPost]
        [SkipRightCheck]
        [SkipRecordLog]
        [SkipIPCheck]
        public JsonResult TestTimeCalculation(string reportDate, int reportType = 1)
        {
            try
            {
                if (string.IsNullOrEmpty(reportDate))
                {
                    return new JsonResult(new { success = false, message = "报告日期不能为空" });
                }

                if (!DateTime.TryParse(reportDate, out DateTime parsedDate))
                {
                    return new JsonResult(new { success = false, message = "报告日期格式不正确" });
                }

                var bll = new BLL.WorkReportNew.BLL_WorkReportDatabase();
                var enumReportType = (Model.Enum.EnumReportType)reportType;
                
                var result = bll.CalculateReportDeadline(new Model.ControllersViewModel.Report.VM_ReportDeadlineCalc_In
                {
                    ReportType = enumReportType,
                    ReportDate = parsedDate
                });

                return new JsonResult(new { 
                    success = true, 
                    message = "时间计算成功",
                    data = new
                    {
                        ReportDate = parsedDate.ToString("yyyy-MM-dd"),
                        ReportType = enumReportType.ToString(),
                        RegularTime = result.RegularTime,
                        LateTime = result.LateTime,
                        FinalTime = result.FinalTime,
                        CurrentStatus = result.CurrentStatus.ToString(),
                        CalculateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    }
                });
            }
            catch (Exception ex)
            {
                LogUtil.AddLog("测试时间计算失败", ex);
                return new JsonResult(new { success = false, message = $"测试时间计算失败：{ex.Message}" });
            }
        }
        
        /// <summary>
        /// 检查单个缓存状态
        /// </summary>
        /// <param name="cacheType">缓存类型</param>
        /// <param name="cacheKey">缓存键</param>
        /// <param name="getCountFunc">获取数量的函数</param>
        /// <returns>缓存状态信息</returns>
        private object CheckCacheStatus(string cacheType, string cacheKey, Func<int> getCountFunc)
        {
            try
            {
                bool keyExists = RedisHelper.Exists(cacheKey);
                int recordCount = 0;
                string status = "Unknown";
                string message = "";
                
                if (!keyExists)
                {
                    status = "Missing";
                    message = "缓存键不存在";
                }
                else
                {
                    try
                    {
                        recordCount = getCountFunc();
                        if (recordCount == 0)
                        {
                            status = "Empty";
                            message = "缓存数据为空";
                        }
                        else
                        {
                            status = "Healthy";
                            message = $"缓存正常，共 {recordCount} 条记录";
                        }
                    }
                    catch (Exception ex)
                    {
                        status = "Error";
                        message = $"获取缓存数据时发生错误: {ex.Message}";
                    }
                }
                
                return new
                {
                    CacheType = cacheType,
                    CacheKey = cacheKey,
                    Status = status,
                    Message = message,
                    RecordCount = recordCount,
                    KeyExists = keyExists,
                    CheckTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    CacheType = cacheType,
                    CacheKey = cacheKey,
                    Status = "Error",
                    Message = $"检查缓存状态时发生错误: {ex.Message}",
                    RecordCount = 0,
                    KeyExists = false,
                    CheckTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
        }

        /// <summary>
        /// 设置后台接口权限白名单规则是否启用
        /// </summary>
        [HttpPost]
        public void ChangeTypeValidState(bool toValid)
        {
            BLL_Home.Instance.ChangeTypeValidState(toValid);
        }

        /// <summary>
        /// 获取用户待办信息
        /// </summary>
        [HttpPost]
        public List<WorkflowPending_Out> GetWorkflowPending()
        {
            return BLL_WorkFlow.Instance.GetWorkflowPending();
        }

        /// <summary>
        /// 变更用户待办信息未查看为已查看
        /// </summary>
        [HttpPost]
        public void UpdateWorkflowPendingStateToViewed(string id)
        {
            BLL_WorkFlow.Instance.UpdateWorkflowPendingStateToViewed(id);
        }

        /// <summary>
        /// 获取用户已处理信息
        /// </summary>
        [HttpPost]
        public List<WorkflowInfo_Out> GetWorkflowHandleInfo(string dataId, string workFlowName)
        {
            return BLL_WorkFlow.Instance.GetWorkflowHandleInfo(dataId, workFlowName);
        }

        /// <summary>
        /// 获取用户已处理信息以及业务信息
        /// </summary>
        [HttpPost]
        public List<WorkflowBusinessInfo_Out> GetWorkflowBusinessInfo(string dataId, string workFlowName)
        {
            return BLL_WorkFlow.Instance.GetWorkflowBusinessInfo(dataId, workFlowName);
        }

        /// <summary>
        /// 查询合同已处理信息
        /// </summary>
        [HttpPost]
        public ApiTableOut<WorkflowInfo_Out> SearchContractWorkflowHandleInfo(SearchWorkflowHandleInfo_In searchWorkflowHandleInfoIn)
        {
            return BLL_WorkFlow.Instance.SearchContractWorkflowHandleInfo(searchWorkflowHandleInfoIn);
        }

        /// <summary>
        /// 获取业务消息种类表
        /// </summary>
        [HttpPost]
        public List<BusinessMessageType> GetBusinessMessageType()
        {
            return BLL_Home.Instance.GetBusinessMessageType();
        }

        /// <summary>
        /// 添加用户业务消息种类
        /// </summary>
        [HttpPost]
        public void AddUserBusinessMessagetype(List<string> businessMessagetype)
        {
            BLL_Home.Instance.AddUserBusinessMessagetype(businessMessagetype);
        }

        /// <summary>
        /// 修改用户业务消息种类
        /// </summary>
        [HttpPost]
        public void UpdateUserBusinessMessagetype(List<string> businessMessagetypeIds)
        {
            BLL_Home.Instance.UpdateUserBusinessMessagetype(businessMessagetypeIds);
        }

        /// <summary>
        /// 获取用户业务消息种类
        /// </summary>
        [HttpPost]
        public List<BusinessMessageTypeAndInfo> GetUserBusinessMessageType()
        {
            return BLL_Home.Instance.GetUserBusinessMessageType();
        }

        /// <summary>
        /// 设置用户业务消息种类为已处理
        /// </summary>
        [HttpPost]
        public void UpdateUserBusinessMessageToHandle(string businessMessagetypeId)
        {
            BLL_Home.Instance.UpdateUserBusinessMessageToHandle(businessMessagetypeId);
        }

        /// <summary>
        /// 获取用户业务消息信息
        /// </summary>
        [HttpPost]
        public List<BusinessMessageTypeAndInfo> GetBusinessMessageAndInfoType()
        {
            return BLL_Home.Instance.GetBusinessMessageAndInfoType();
        }

        /// <summary>
        /// 获取用户消息信息
        /// </summary>
        [HttpPost]
        [Obsolete]
        public List<MessagesList_Out> GetMessagesList()
        {
            return BLL_Home.Instance.GetMessagesList();
        }

        /// <summary>
        /// 根据查询条件获取用户消息信息列表
        /// </summary>
        /// <param name="searchMessagesListIn"></param>
        [HttpPost]
        public ApiTableOut<SearchMessagesList_Out> SearchMessagesList(SearchMessagesList_In searchMessagesListIn)
        {
            return BLL_Home.Instance.SearchMessagesList(searchMessagesListIn);
        }
        /// <summary>
        /// 首页消息中心最新数据及未读状态获取
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public MessageCenter_Out GetMessageCenter()
        {
            MessageCenter_Out result = new MessageCenter_Out();

            SearchMessagesList_In forlist = new SearchMessagesList_In { PageNumber = 1, PageSize = 99 };
            int total = -1;
            List<SearchMessagesList_Out> messageList = DbOpe_sys_messages.Instance.SearchMessagesList(forlist, TokenModel.Instance.id, ref total);

            result.LastMessage = messageList.Take(6).ToList();
            result.UnReadMessageCount = messageList.Where(e => e.State == 1).Count();

            return result;
        }
        /// <summary>
        /// 获取用户消息提醒信息
        /// </summary>
        [HttpPost, SkipSilence]
        public List<MessageReminder_Out> GetMessageReminderList()
        {
            return BLL_Home.Instance.GetMessageReminderList();
        }

        /// <summary>
        /// 根据用户Id获取用户的晋升历史记录
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public List<GetUserPromotionListByUserId_Out> GetPromotionList()
        {
            return BLL_Home.Instance.GetPromotionList();
        }

        /// <summary>
        /// 修改用户登录设置
        /// </summary>
        [HttpPost]
        public void UpdateLoginTypeUser(List<string> loginTypeIds)
        {
            BLL_Home.Instance.UpdateLoginTypeUser(loginTypeIds);
        }

        /// <summary>
        /// 获取用户登录设置
        /// </summary>
        [HttpPost]
        public List<LoginTypeUser_Out> GetLoginTypeUser()
        {
            return BLL_Home.Instance.GetLoginTypeUser();
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        [HttpPost]
        public GetUserInfo_Out GetUserInfo()
        {
            return BLL_Home.Instance.GetUserInfo();
        }

        /// <summary>
        /// 上传用户头像
        /// </summary>
        [HttpPost]
        public void UploadAvatarImage([FromForm] UploadAvatarImage_In uploadAvatarImageIn)
        {
            BLL_Home.Instance.UploadAvatarImage(uploadAvatarImageIn);
        }

        /// <summary>
        /// 下载头像
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult LoadAvatarImage()
        {
            return BLL_Home.Instance.LoadAvatarImage(Response);
        }

        /// <summary>
        /// 获取用户业务消息信息和通知
        /// </summary>
        [HttpPost]
        public List<BusinessMessageTypeAndInfoAndMessagesList> GetBusinessMessageAndInfoTypeAndMessagesList()
        {
            return BLL_Home.Instance.GetBusinessMessageAndInfoTypeAndMessagesList();
        }

        /// <summary>
        /// 删除用户业务消息信息和通知
        /// </summary>
        [HttpPost]
        public void DeleteBusinessMessageAndInfoTypeAndMessagesList(string id)
        {
            BLL_Home.Instance.DeleteBusinessMessageAndInfoTypeAndMessagesList(id);
        }

        /// <summary>
        /// 根据查询条件获取用户业务消息信息和通知列表
        /// </summary>
        /// <param name="searchBusinessMessageAndInfoTypeAndMessagesIn"></param>
        [HttpPost]
        public ApiTableOut<SearchBusinessMessageAndInfoTypeAndMessages_Out> SearchBusinessMessageAndInfoTypeAndMessages(SearchBusinessMessageAndInfoTypeAndMessages_In searchBusinessMessageAndInfoTypeAndMessagesIn)
        {
            return BLL_Home.Instance.SearchBusinessMessageAndInfoTypeAndMessages(searchBusinessMessageAndInfoTypeAndMessagesIn);
        }

        /// <summary>
        /// 变更系统消息已读状态
        /// </summary>
        /// <param name="id"></param>
        [HttpPost]
        public void UpdateMessagesState(string id)
        {
            BLL_Home.Instance.UpdateMessagesState(id);
        }

        /// <summary>
        /// 隐藏系统消息
        /// </summary>
        /// <param name="id"></param>
        [HttpPost]
        public void HiddenMessages(string id)
        {
            BLL_Home.Instance.HiddenMessages(id);
        }

        /// <summary>
        /// 消息中心设置已读
        /// </summary>
        /// <param name="setMessageIsReadIn"></param>
        [HttpPost]
        public NoticeBase_Out SetIsReadInMessageCenter(SetMessageIsRead_In setMessageIsReadIn)
        {
            return BLL_Home.Instance.SetIsReadInMessageCenter(setMessageIsReadIn);
        }


        /// <summary>
        /// 消息标签 点赞/回复/评论 数量获取 移动端 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<ArticleAboutCountForM> GetArticleMessageCount()
        {
            List<ArticleAboutCountForM> result = DbOpe_crm_article.Instance.ArticleAboutCount();

            return result;
        }

        /// <summary>
        /// 消息标签 点赞/回复/评论 内容获取 移动端 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ApiTableOut<ArticleAboutMessageForM> GetArticleMessage(SearchArticleMessage searchArticleMessage)
        {
            int total = 0;
            List<ArticleAboutMessageForM> result = DbOpe_crm_article.Instance.ArticleAboutMessage(searchArticleMessage, ref total);
            return GetApiTableOut(result, total);
        }

        /// <summary>
        /// 消息标签 点赞/回复/评论 设置已读 移动端 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public NoticeBase_Out SetArticleMessageIsRead(SetMobileMessageIsRead_In setMobileMessageIsRead_In)
        {
            //return null;
            return DbOpe_crm_article.Instance.SetArticleMessageIsRead(setMobileMessageIsRead_In);
        }

        /// <summary>
        /// 消息标签 点赞/回复/评论 设置隐藏 移动端 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public NoticeBase_Out SetArticleMessageIsHidden(SetMobileMessageIsRead_In setMobileMessageIsRead_In)
        {
            //return null;
            return DbOpe_crm_article.Instance.SetArticleMessageIsHidden(setMobileMessageIsRead_In);
        }

        /// <summary>
        /// 消息标签 点赞/回复/评论 设置全部已读 移动端 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public NoticeBase_Out SetArticleMessageAllRead()
        {
            //return null;
            return DbOpe_crm_article.Instance.SetArticleMessageAllRead();
        }
        /// <summary>
        /// 获取下一个代办事项
        /// </summary>
        /// <param name="getNextWorkflowPending_IN"></param>
        /// <returns></returns>
        [HttpPost]
        public GetNextWorkflowPending_OUT GetNextWorkflowPending(GetNextWorkflowPending_IN getNextWorkflowPending_IN)
        {
            return DbOpe_sys_workflow_pending.Instance.GetNextWorkflowPending(getNextWorkflowPending_IN);
        }
        /// <summary>
        /// 20240812 系统飘红提醒
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        //[SkipRSAKey]
        //[SkipAuthCheck]
        //[SkipRecordLog]
        public ApiRemindInfoOut GetRemindInfo()
        {
            ApiRemindInfoOut apiRemindInfoOut = new ApiRemindInfoOut();
            apiRemindInfoOut.RemindInfoOuts = BLL_Customer.Instance.GetRemindInfos();
            apiRemindInfoOut.RemindInfoOuts = apiRemindInfoOut.RemindInfoOuts.Concat(BLL_ContractAchievement.Instance.GetRemindInfos()).ToList();
            apiRemindInfoOut.RemindInfoOuts = apiRemindInfoOut.RemindInfoOuts.Concat(BLL_MessageCenter.Instance.GetRemindInfosNoServiceAppl()).ToList();
            apiRemindInfoOut.RemindInfoOuts = apiRemindInfoOut.RemindInfoOuts.Concat(BLL_ServiceExceptionRemind.Instance.GetRemindInfos()).ToList();
            return apiRemindInfoOut;
        }


        //[HttpPost]
        //[SkipRightCheck]
        //public void GetRemindInfoNew()
        //{
        //    //DbOpe_sys_message_ignore.Instance.GetIgnoreList();
        //    var GetSendData = DbOpe_sys_messagecenterdetail.Instance.FindData("9c1411dc-4765-4a44-889e-beed1ef9ce06");
        //    if (GetSendData != null)
        //    {

        //        //BLL_MessageCenter.Instance.sendGZHMessage(GetSendData.MessageVXPath).Wait();

        //        DbOpe_sys_messagecenterdetail.Instance.SendMessage(GetSendData, EnumMessageLocalType.ServiceAbout);

        //    }
        //    //BLL_MessageCenter.Instance.AddMessageCenter();
        //    //BLL_MessageCenter.Instance.ScheduleSendGZHMessage();
        //    //DbOpe_sys_message_ignore.Instance.SetIgnoreAntiEffective();
        //    //string s = "oyn-HjtgIkpOamHHch7EyScvHcZg,SaveCustomerNearlyRelease,TRITECH LUBRICANTS & SALES PTY LTD.保留客户即将到期,待办,请及时处理";
        //    //BLL_MessageCenter.Instance.sendMessageTest(s);
        //}

        /// <summary>
        /// 消息中心
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public List<MessageCenter> GetMessageCenterList()
        {
            return DbOpe_sys_messagecenter.Instance.GetMessageCenterList();
        }

        /// <summary>
        /// 消息中心详细
        /// </summary>
        /// <param name="messageDetail_In"></param>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public ApiTableOut<MessageDetailInfo> GetMessageCenterDetailList(MessageDetail_In messageDetail_In)
        {
            int total = 0;
            var list = DbOpe_sys_messagecenterdetail.Instance.GetMessageDetailInfo(messageDetail_In, ref total);
            return GetApiTableOut(list, total);
        }

        /// <summary>
        /// 手动设置消息已读
        /// </summary>
        /// <param name="isRead"></param>
        [HttpPost]
        [SkipRightCheck]
        public void SetIsReadByCenterDetailId(SetIsRead isRead)
        {
            DbOpe_sys_messagecenterdetail.Instance.SetMessageIsRead(isRead);
        }

        /// <summary>
        /// 设置消息忽略，同类型同一相关消息忽略15天
        /// </summary>
        /// <param name="isIgnore"></param>
        [HttpPost]
        [SkipRightCheck]
        public MessageResult SetMessageIgnore(SetIgnore isIgnore)
        {
            return DbOpe_sys_message_ignore.Instance.SetMessageIgnore(isIgnore);
        }

        /// <summary>
        /// 首页内容
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public UserMainPageInH5 GetUserMainPage()
        {
            return DbOpe_sys_messagecenterdetail.Instance.GetUserMainPageH5();
        }

        /// <summary>
        /// 忽略信息列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SkipRightCheck]
        public List<MessageIgnoreInfo> GetIgnoreList()
        {
            return DbOpe_sys_message_ignore.Instance.GetIgnoreList();
        }

        [HttpGet]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public IActionResult DownloadLog(string fileName)
        {
            var list = LogUtil.GetAllLogFileName();

            var filePath = list.Find(p => p.Contains(fileName));

            if (filePath == null)
            {
                return new JsonResult(new
                {
                    Msg = "文件不存在!" + fileName
                });
            }
            else
            {
                var stream = File.OpenRead(filePath);
                string encodeFilename = HttpUtility.UrlEncode(fileName, Encoding.GetEncoding("UTF-8"));
                Response.Headers.Add("Content-Disposition", "inline; filename=" + encodeFilename);
                string ContentType = "application/octet-stream";
                ContentType = ContentTypeHelper.GetContentType(".txt");
                //var contentDisposition = new ContentDispositionHeaderValue("inline");
                //contentDisposition.SetHttpFileName(contentDisposition.FileName);
                //response.Headers[HeaderNames.ContentDisposition] = contentDisposition.ToString();


                //return new FileStreamResult(stream, "application/pdf");
                return new FileStreamResult(stream, ContentType);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        [SkipRSAKey]
        [SkipAuthCheck, SkipIPCheck]
        [SkipRecordLog]
        public JsonResult Test(string p)
        {
            var msg = "";
            var msg1 = "";
            var keyWords = new List<string>();
            var userId = "00000000-0000-0000-0000-000000000000";
            //计时 单位要毫秒
            var startTime = DateTime.Now;   
            var results1 = DbOpe_crm_customer_privatepool.Instance.PreInspectionCustomerList(p, userId);
            var endTime = DateTime.Now;
            var time1 = (endTime - startTime).TotalMilliseconds;
            startTime = DateTime.Now;
            var results2 = new PreInspectionOptimizer().PreInspectionCustomerList(p, userId);
            endTime = DateTime.Now;
            var time2 = (endTime - startTime).TotalMilliseconds;
            return new JsonResult(new {
                results1 = results1,
                results2 = results2,
                msg = msg,
                msg1 = msg1,
                time1 = time1,
                time2 = time2
            });
        }

    }
}

