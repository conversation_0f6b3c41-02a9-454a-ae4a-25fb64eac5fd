﻿using System.ComponentModel;
using CRM2_API.BLL.PerformanceObjectives;
using CRM2_API.Common.Filter;
using CRM2_API.Controllers.Base;
using CRM2_API.Model.ControllersViewModel.PerformanceObjectives;
using CRM2_API.Model.System;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CRM2_API.Controllers
{
    
    /// <summary>
    /// 业绩目标控制器
    /// </summary>
    [Description("业绩目标控制器")]
    public class PerformanceObjectivesController : MyControllerBase
    {
        public PerformanceObjectivesController(IHttpContextAccessor _httpContextAccessor) : base(_httpContextAccessor)
        {
        }
        
        /// <summary>
        /// 修改业绩目标信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// </summary>
        /// <param name="updatePerformanceObjectivesIn"></param>
        /// <returns></returns>
        [HttpPost]
        public UpdatePerformanceObjectives_Out UpdatePerformanceObjectives(UpdatePerformanceObjectives_In updatePerformanceObjectivesIn)
        {
            return BLL_PerformanceObjectives.Instance.UpdatePerformanceObjectives(updatePerformanceObjectivesIn,true);
        }

        /// <summary>
        /// 修改业绩目标信息，主键为空，新建；主键不存在，删除；主键存在，修改；
        /// 和上面方法不同的是：该方法会返回每行的信息及最后执行的状态值
        /// 而上面的方法仅仅返回成功与否
        /// </summary>
        [HttpPost]
        [SkipRightCheck]
        public UpdatePerformanceObjectivesPresentDetails_Out UpdatePerformanceObjectivesPresentDetails(UpdatePerformanceObjectivesPresentDetails_In updatePerformanceObjectivesPresentDetailsIn)
        {
            return BLL_PerformanceObjectives.Instance.UpdatePerformanceObjectivesPresentDetails(updatePerformanceObjectivesPresentDetailsIn,true);
        }

        /// <summary>
        /// 根据年份获取业绩目标信息
        /// </summary>
        /// <param name="getPerformanceObjectivesListIn"></param>
        /// <returns></returns>
        [HttpPost]
        public List<GetPerformanceObjectivesList_Out> GetPerformanceObjectivesList(GetPerformanceObjectivesList_In getPerformanceObjectivesListIn)
        {
            return BLL_PerformanceObjectives.Instance.GetPerformanceObjectivesList(getPerformanceObjectivesListIn);
        }

        /// <summary>
        /// 统一配置业绩目标信息
        /// </summary>
        /// <param name="unifiedConfigPerformance"></param>
        /// <returns></returns>
        [HttpPost]
        [Obsolete]
        public UnifiedConfigPerformanceObjectives_Out UnifiedConfigPerformanceObjectives(
            List<UnifiedConfigPerformanceObjectives_In> unifiedConfigPerformance)
        {
            return BLL_PerformanceObjectives.Instance.UnifiedConfigPerformanceObjectives(unifiedConfigPerformance,true);
        }

        /// <summary>
        /// 下载业绩目标的导入模版
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult DownloadPerformanceObjectivesImportTemplate(
            DownloadPerformanceObjectivesImportTemplate_In? downloadPerformanceObjectivesImportTemplateIn)
        {
            if (downloadPerformanceObjectivesImportTemplateIn is null)
            {
                downloadPerformanceObjectivesImportTemplateIn = new();
            }
            return BLL_PerformanceObjectives.Instance.DownloadPerformanceObjectivesImportTemplate(
                downloadPerformanceObjectivesImportTemplateIn,Response);
        }
        
        /// <summary>
        /// 导入业绩目标的模版数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ImportPerformanceObjectivesTemplateData([FromForm] ImportPerformanceObjectivesTemplateData_In importPerformanceObjectivesTemplateDataIn)
        {
            if (importPerformanceObjectivesTemplateDataIn == null ||
                importPerformanceObjectivesTemplateDataIn.File == null)
            {
                throw new ApiException("模版文件不存在!");
            }
            return BLL_PerformanceObjectives.Instance.ImportPerformanceObjectivesTemplateData(
                importPerformanceObjectivesTemplateDataIn);
        }
    }
}